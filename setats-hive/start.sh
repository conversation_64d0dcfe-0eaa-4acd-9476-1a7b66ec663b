#!/bin/bash

set -e
CONFIG_FILE="$HIVE_HOME/conf/hive-site.xml"  # 替换为实际配置文件路径

# 提取数据库主机
ip=$(grep -A2 "javax.jdo.option.ConnectionURL" "$CONFIG_FILE" | grep value | sed 's/.*mysql:\/\///; s/:.*//')

# 提取数据库端口
port=$(grep -A2 "javax.jdo.option.ConnectionURL" "$CONFIG_FILE" | grep value | sed 's/.*://; s/\/.*//')

# 提取数据库用户名
user=$(grep -A2 "javax.jdo.option.ConnectionUserName" "$CONFIG_FILE" | grep value | sed 's/<value>//; s/<\/value>//')

# 提取数据库密码
password=$(grep -A2 "javax.jdo.option.ConnectionPassword" "$CONFIG_FILE" | \
           grep value | \
           sed 's/<value>//; s/<\/value>//' | \
           xargs)

# 输出结果
echo "ip: $ip"
echo "port: $port"
echo "user: $user"
echo "password: xxxxx"

sed -e "s/__hiveMetastoreUser/$hiveMetastoreUser/g" \
    -e "s/__hiveMetastorePass/$hiveMetastorePass/g" \
    "$HIVE_HOME/user.sql.template" > "$HIVE_HOME/user.sql"

username=wshmeta
# 创建 MySQL 用户的函数（幂等操作）
create_mysql_user() {
    # 检查用户是否存在
    if ! mysql -h $ip -u $user -p"${password}" -P $port -e "SELECT 1 FROM mysql.user WHERE User = '$username';" | grep -q "1"; then
        # 创建用户
        mysql -h $ip -u $user -p"${password}" -P $port < $HIVE_HOME/user.sql
        echo "用户 '$username' 创建成功"
    else
        echo "用户 '$username' 已存在"
    fi
}

# 创建应用用户
create_mysql_user

schematool -dbType mysql -initSchema || true
hive --service metastore \
    -hiveconf hive.root.logger=INFO,console \
    -hiveconf hive.log.level=INFO \
    -hiveconf hive.log.dir=/tmp/log/hive \
    2>&1 | tee -a /tmp/log/hive/hive-metastore.log
