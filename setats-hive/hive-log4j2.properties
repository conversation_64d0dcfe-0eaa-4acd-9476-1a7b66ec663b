status = INFO
name = HiveLog4j2Config

property.hive.log.dir = ${sys:hive.log.dir:-/tmp/log/hive}
property.hive.log.file = ${sys:hive.log.file:-hive-metastore.log}

appender.console.type = Console
appender.console.name = CONSOLE
appender.console.layout.type = PatternLayout
appender.console.layout.pattern = %d{yyyy-MM-dd HH:mm:ss} %-5p %c{1}:%L - %m%n

appender.rolling.type = RollingFile
appender.rolling.name = ROLLINGFILE
appender.rolling.fileName = ${hive.log.dir}/${hive.log.file}
appender.rolling.filePattern = ${hive.log.dir}/${hive.log.file}.%d{yyyy-MM-dd}
appender.rolling.layout.type = PatternLayout
appender.rolling.layout.pattern = %d{yyyy-MM-dd HH:mm:ss} %-5p %c{1}:%L - %m%n
appender.rolling.policies.type = Policies
appender.rolling.policies.time.type = TimeBasedTriggeringPolicy
appender.rolling.policies.time.interval = 1
appender.rolling.policies.time.modulate = true
appender.rolling.strategy.type = DefaultRolloverStrategy
appender.rolling.strategy.max = 10

rootLogger.level = INFO
rootLogger.appenderRef.console.ref = CONSOLE
rootLogger.appenderRef.rolling.ref = ROLLINGFILE

logger.hive.name = org.apache.hadoop.hive
logger.hive.level = INFO

logger.metastore.name = org.apache.hadoop.hive.metastore
logger.metastore.level = INFO