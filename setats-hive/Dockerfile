FROM openjdk:8-jre-slim

ARG jdk=TencentKona-8.0.0-232

ADD https://oceanus-public-1257058918.cos.ap-guangzhou.myqcloud.com/${jdk}.tar.gz /tmp
RUN tar -zxf /tmp/${jdk}.tar.gz -C /opt/ && chown -R root:root /opt/${jdk}
RUN rm -f /tmp/${jdk}.tar.gz

# 安装必要的依赖
RUN set -ex && \
    apt-get update && \
    apt install -y wget mariadb-client-10.5 bash libc6 procps net-tools tcpdump telnet iputils-ping curl lrzsz dos2unix vim-tiny netcat unzip && \
    rm /bin/sh && \
    ln -sv /bin/bash /bin/sh && \
    apt-get clean && apt-get autoclean \

ENV JAVA_HOME /opt/${jdk}
# 设置环境变量
ENV HIVE_VERSION=3.1.3
ENV HADOOP_VERSION=3.3.1
ENV HIVE_HOME=/opt/hive
ENV HADOOP_HOME=/opt/hadoop
ENV PATH=$PATH:$HIVE_HOME/bin:$HADOOP_HOME/bin:$JAVA_HOME/bin
ENV HIVE_CONF_DIR=$HIVE_HOME/conf
# 下载并安装Hadoop
RUN wget https://archive.apache.org/dist/hadoop/common/hadoop-$HADOOP_VERSION/hadoop-$HADOOP_VERSION.tar.gz -O /tmp/hadoop.tar.gz && \
    tar -xzf /tmp/hadoop.tar.gz -C /opt/ && \
    ln -s /opt/hadoop-$HADOOP_VERSION $HADOOP_HOME && \
    rm /tmp/hadoop.tar.gz

# 下载并安装Hive
RUN wget https://archive.apache.org/dist/hive/hive-$HIVE_VERSION/apache-hive-$HIVE_VERSION-bin.tar.gz -O /tmp/hive.tar.gz && \
    tar -xzf /tmp/hive.tar.gz -C /opt/ && \
    ln -s /opt/apache-hive-$HIVE_VERSION-bin $HIVE_HOME && \
    rm /tmp/hive.tar.gz

# 下载MySQL JDBC驱动
RUN wget https://repo1.maven.org/maven2/mysql/mysql-connector-java/8.0.26/mysql-connector-java-8.0.26.jar -O $HIVE_HOME/lib/mysql-connector-java.jar

# 修复SLF4J依赖冲突
RUN rm -f $HADOOP_HOME/share/hadoop/common/lib/slf4j-log4j12-*.jar && \
    rm -f $HIVE_HOME/lib/log4j-slf4j-impl-*.jar

# 创建必要的目录
RUN mkdir -p $HIVE_HOME/logs


COPY download_jar.sh $HIVE_HOME/
ADD https://oceanus-public-1257058918.cos.ap-guangzhou.myqcloud.com/coscli/coscli-v1.0.1-linux-amd64 $HIVE_HOME/coscli
RUN chmod +x $HIVE_HOME/coscli
RUN chmod +x $HIVE_HOME/download_jar.sh
RUN apt install -y jq


# 创建必要的目录
RUN mkdir -p $HIVE_HOME/logs

COPY hive-log4j2.properties $HIVE_CONF_DIR/hive-log4j2.properties

ENV TZ=Asia/Shanghai \
    DEBIAN_FRONTEND=noninteractive

RUN ln -fs /usr/share/zoneinfo/${TZ} /etc/localtime \
    && echo ${TZ} > /etc/timezone
# 暴露Hive Metastore端口
EXPOSE 9083

# 设置工作目录
WORKDIR $HIVE_HOME
COPY start.sh $HIVE_HOME/
COPY user.sql.template $HIVE_HOME/
RUN chmod 744 $HIVE_HOME/start.sh
ENTRYPOINT ["sh", "-c", "$HIVE_HOME/start.sh"]


