#!/bin/bash
set -e

if [[ "$setatsJar" == "" ]]; then
    echo "setatsJar is empty"
    exit 0
else
   echo "setatsJar is not empty"
fi

echo "download jar from cos"
CredentialSvcRsp=$(curl -s -X GET "http://credentials-provider-service.$CREDENTIAL_NAMESPACE.svc.cluster.local:7856")
TmpSecretId=$(echo "$CredentialSvcRsp" | jq -r .TmpSecretId)
TmpSecretKey=$(echo "$CredentialSvcRsp" | jq -r .TmpSecretKey)
SessionToken=$(echo "$CredentialSvcRsp" | jq -r .<PERSON>)

export cos_config_file="/tmp/.cos.yaml"
cat <<EOF >$cos_config_file
cos:
  base:
    protocol: https
    mode: ""
    cvmrolename: ""
    closeautoswitchhost: ""
  buckets:
  - name: $bucket
    alias: cosBucket
    region: $region
    endpoint: cos.${region}.myqcloud.com
    ofs: false
EOF


# [{"path":"setats/gz-emr.jar","fileName":"gz-emr.jar","bucket":"autotest","region":"ap-guangzhou"}]
txt=`echo "$setatsJar" | base64 -d`
echo "jars is $txt"
echo "$txt" | jq -c '.[]' | while IFS= read -r item; do
    targetDir=/etc
    path=$(echo "$item" | jq -r '.path')
    fileName=$(echo "$item" | jq -r '.fileName')
    bucket=$(echo "$item" | jq -r '.bucket')
    region=$(echo "$item" | jq -r '.region')
    # 处理每个元素（示例：打印或执行其他操作）
    echo "  fileName: $fileName"
    echo "  path: $path"
    echo "  bucket: $bucket"
    echo "  region: $region"
    echo "  -----"
    if [[ $fileName == *.jar ]]; then
        targetDir="$HIVE_HOME/lib"
    fi
    echo "  targetDir: $targetDir"
    # 在这里可以添加其他处理逻辑，例如下载文件：
    echo "download jar from cos"
    # 工具coscli使用之前, 必须要使用这个命令重新设置以下配置
    $HIVE_HOME/coscli config -c $cos_config_file set --session_token "$SessionToken" --secret_id "$TmpSecretId" --secret_key "$TmpSecretKey"
    $HIVE_HOME/coscli -c $cos_config_file cp cos://cosBucket/$path $targetDir/$fileName
done