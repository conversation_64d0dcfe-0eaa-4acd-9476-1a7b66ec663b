#port
shark.port=5021

#DataBaseurl
dburl=root:root@tcp(127.0.0.1:3306)/online_galileo?charset=utf8

#TaskCenter callback
taskcentercallback=http://127.0.0.1:5021/interface
completeTaskInterface=qcloud.galileo.taskcenter.completeTask
alarmTaskInterface=qcloud.galileo.taskcenter.alarmTask

#Cluster-Admin
clusterAdmin=http://127.0.0.1:9002/

#CDP-Admin
cdpAdmin=http://127.0.0.1:9003/

#platform
platformUrl=http://account.tencentyun.com:50001

#netcenter
netcenter=http://127.0.0.1:9004

#regionIds
supportRegionIds=1,4,8,16

#aes-encrypt key
aesEncryptKey=Tencent2018$%^&*

scsDevEnv=true
#oceanus dev account
#scsDevEnvTestAppId=*********
#scsDevEnvTestUin=************
#scsDevEnvTestSubAccountUin=************
scsDevEnvAppId=**********
scsDevEnvUin=************
#scsDevEnvSubAccountUin=************
scsDevEnvSubAccountUin=************


scsDevEnvCamUin=************
scsDevEnvCamOperateUin=************
scsDevEnvCamAppId=*********


scsDevEnvNewCamUin=************
scsDevEnvNewCamOperateUin=************
scsDevEnvNewCamAppId=**********
