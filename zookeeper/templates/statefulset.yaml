{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

apiVersion: {{ include "common.capabilities.statefulset.apiVersion" . }}
kind: StatefulSet
metadata:
  name: {{ template "common.names.fullname" . }}
  namespace: {{ template "zookeeper.namespace" . }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: zookeeper
    role: zookeeper
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  replicas: {{ .Values.replicaCount }}
  revisionHistoryLimit: {{ .Values.revisionHistoryLimit }}
  podManagementPolicy: {{ .Values.podManagementPolicy }}
  {{- $podLabels := include "common.tplvalues.merge" ( dict "values" ( list .Values.podLabels .Values.commonLabels ) "context" . ) }}
  selector:
    matchLabels: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 6 }}
      app.kubernetes.io/component: zookeeper
  serviceName: {{ printf "%s-%s" (include "common.names.fullname" .) (default "headless" .Values.service.headless.servicenameOverride) | trunc 63 | trimSuffix "-" }}
  {{- if .Values.updateStrategy }}
  updateStrategy: {{- toYaml .Values.updateStrategy | nindent 4 }}
  {{- end }}
  template:
    metadata:
      annotations:
        {{- if .Values.podAnnotations }}
        {{- include "common.tplvalues.render" (dict "value" .Values.podAnnotations "context" $) | nindent 8 }}
        {{- end }}
        {{- if (include "zookeeper.createConfigmap" .) }}
        checksum/configuration: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
        {{- end }}
        {{- if or (include "zookeeper.quorum.createSecret" .) (include "zookeeper.client.createSecret" .) (include "zookeeper.client.createTlsPasswordsSecret" .) (include "zookeeper.quorum.createTlsPasswordsSecret" .) }}
        checksum/secrets: {{ include (print $.Template.BasePath "/secrets.yaml") . | sha256sum }}
        {{- end }}
        {{- if or (include "zookeeper.client.createTlsSecret" .) (include "zookeeper.quorum.createTlsSecret" .) }}
        checksum/tls-secrets: {{ include (print $.Template.BasePath "/tls-secrets.yaml") . | sha256sum }}
        {{- end }}
      labels: {{- include "common.labels.standard" ( dict "customLabels" $podLabels "context" $ ) | nindent 8 }}
        app.kubernetes.io/component: zookeeper
    spec:
      enableServiceLinks: {{ .Values.enableServiceLinks }}
      serviceAccountName: {{ template "zookeeper.serviceAccountName" . }}
      {{- include "zookeeper.imagePullSecrets" . | nindent 6 }}
      automountServiceAccountToken: {{ .Values.automountServiceAccountToken }}
      {{- if .Values.hostAliases }}
      hostAliases: {{- include "common.tplvalues.render" (dict "value" .Values.hostAliases "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.affinity }}
      affinity: {{- include "common.tplvalues.render" (dict "value" .Values.affinity "context" $) | nindent 8 }}
      {{- else }}
      affinity:
        podAffinity: {{- include "common.affinities.pods" (dict "type" .Values.podAffinityPreset "component" "zookeeper" "customLabels" $podLabels "context" $) | nindent 10 }}
        podAntiAffinity: {{- include "common.affinities.pods" (dict "type" .Values.podAntiAffinityPreset "component" "zookeeper" "customLabels" $podLabels "context" $) | nindent 10 }}
        nodeAffinity: {{- include "common.affinities.nodes" (dict "type" .Values.nodeAffinityPreset.type "key" .Values.nodeAffinityPreset.key "values" .Values.nodeAffinityPreset.values) | nindent 10 }}
      {{- end }}
      {{- if .Values.nodeSelector }}
      nodeSelector: {{- include "common.tplvalues.render" (dict "value" .Values.nodeSelector "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.tolerations }}
      tolerations: {{- include "common.tplvalues.render" (dict "value" .Values.tolerations "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.topologySpreadConstraints }}
      topologySpreadConstraints: {{- include "common.tplvalues.render" (dict "value" .Values.topologySpreadConstraints "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.priorityClassName }}
      priorityClassName: {{ .Values.priorityClassName }}
      {{- end }}
      {{- if .Values.schedulerName }}
      schedulerName: {{ .Values.schedulerName }}
      {{- end }}
      {{- if .Values.podSecurityContext.enabled }}
      securityContext: {{- include "common.compatibility.renderSecurityContext" (dict "secContext" .Values.podSecurityContext "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.dnsPolicy  }}
      dnsPolicy: {{ .Values.dnsPolicy }}
      {{- end }}
      {{- if .Values.dnsConfig }}
      dnsConfig: {{- include "common.tplvalues.render" (dict "value" .Values.dnsConfig "context" $) | nindent 8 }}
      {{- end }}
      initContainers:
        {{- if and .Values.volumePermissions.enabled .Values.persistence.enabled }}
        - name: volume-permissions
          image: {{ template "zookeeper.volumePermissions.image" . }}
          imagePullPolicy: {{ default "" .Values.volumePermissions.image.pullPolicy | quote }}
          command:
            - /bin/bash
          args:
            - -ec
            - |
              mkdir -p /bitnami/zookeeper
              chown -R {{ .Values.containerSecurityContext.runAsUser }}:{{ .Values.podSecurityContext.fsGroup }} /bitnami/zookeeper
              find /bitnami/zookeeper -mindepth 1 -maxdepth 1 -not -name ".snapshot" -not -name "lost+found" | xargs -r chown -R {{ .Values.containerSecurityContext.runAsUser }}:{{ .Values.podSecurityContext.fsGroup }}
              {{- if .Values.dataLogDir }}
              mkdir -p {{ .Values.dataLogDir }}
              chown -R {{ .Values.containerSecurityContext.runAsUser }}:{{ .Values.podSecurityContext.fsGroup }} {{ .Values.dataLogDir }}
              find {{ .Values.dataLogDir }} -mindepth 1 -maxdepth 1 -not -name ".snapshot" -not -name "lost+found" | xargs -r chown -R {{ .Values.containerSecurityContext.runAsUser }}:{{ .Values.podSecurityContext.fsGroup }}
              {{- end }}
          {{- if .Values.volumePermissions.containerSecurityContext.enabled }}
          securityContext: {{- include "common.compatibility.renderSecurityContext" (dict "secContext" .Values.volumePermissions.containerSecurityContext "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.volumePermissions.resources }}
          resources: {{- toYaml .Values.volumePermissions.resources | nindent 12 }}
          {{- else if ne .Values.volumePermissions.resourcesPreset "none" }}
          resources: {{- include "common.resources.preset" (dict "type" .Values.volumePermissions.resourcesPreset) | nindent 12 }}
          {{- end }}
          volumeMounts:
            - name: empty-dir
              mountPath: /tmp
              subPath: tmp-dir
            - name: data
              mountPath: /bitnami/zookeeper
            {{- if .Values.dataLogDir }}
            - name: data-log
              mountPath: {{ .Values.dataLogDir }}
            {{- end }}
        {{- end }}
        {{- if or .Values.tls.client.enabled .Values.tls.quorum.enabled }}
        - name: init-certs
          image: {{ include "zookeeper.image" . }}
          imagePullPolicy: {{ .Values.image.pullPolicy | quote }}
          {{- if .Values.containerSecurityContext.enabled }}
          securityContext: {{- include "common.compatibility.renderSecurityContext" (dict "secContext" .Values.containerSecurityContext "context" $) | nindent 12 }}
          {{- end }}
          command:
            - /scripts/init-certs.sh
          env:
            - name: MY_POD_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.name
            {{- if or .Values.tls.client.passwordsSecretName (include "zookeeper.client.createTlsPasswordsSecret" .) }}
            - name: ZOO_TLS_CLIENT_KEYSTORE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "zookeeper.client.tlsPasswordsSecret" . }}
                  key: {{ include "zookeeper.client.tlsPasswordKeystoreKey" . }}
            - name: ZOO_TLS_CLIENT_TRUSTSTORE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "zookeeper.client.tlsPasswordsSecret" . }}
                  key: {{ include "zookeeper.client.tlsPasswordTruststoreKey" . }}
            {{- end }}
            {{- if or .Values.tls.quorum.passwordsSecretName (include "zookeeper.quorum.createTlsPasswordsSecret" .) }}
            - name: ZOO_TLS_QUORUM_KEYSTORE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "zookeeper.quorum.tlsPasswordsSecret" . }}
                  key: {{ include "zookeeper.quorum.tlsPasswordKeystoreKey" . }}
            - name: ZOO_TLS_QUORUM_TRUSTSTORE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "zookeeper.quorum.tlsPasswordsSecret" . }}
                  key: {{ include "zookeeper.quorum.tlsPasswordTruststoreKey" . }}
            {{- end }}
          {{- if .Values.tls.resources }}
          resources: {{- toYaml .Values.tls.resources | nindent 12 }}
          {{- else if ne .Values.tls.resourcesPreset "none" }}
          resources: {{- include "common.resources.preset" (dict "type" .Values.tls.resourcesPreset) | nindent 12 }}
          {{- end }}
          volumeMounts:
            - name: empty-dir
              mountPath: /tmp
              subPath: tmp-dir
            - name: scripts
              mountPath: /scripts/init-certs.sh
              subPath: init-certs.sh
            {{- if or .Values.tls.client.enabled }}
            - name: client-certificates
              mountPath: /certs/client
            - name: client-shared-certs
              mountPath: /opt/bitnami/zookeeper/config/certs/client
            {{- end }}
            {{- if or .Values.tls.quorum.enabled }}
            - name: quorum-certificates
              mountPath: /certs/quorum
            - name: quorum-shared-certs
              mountPath: /opt/bitnami/zookeeper/config/certs/quorum
            {{- end }}
        {{- end }}
        {{- if .Values.initContainers }}
        {{- include "common.tplvalues.render" (dict "value" .Values.initContainers "context" $) | trim | nindent 8 }}
        {{- end }}
      containers:
        - name: zookeeper
          image: {{ template "zookeeper.image" . }}
          imagePullPolicy: {{ .Values.image.pullPolicy | quote }}
          {{- if .Values.containerSecurityContext.enabled }}
          securityContext: {{- include "common.compatibility.renderSecurityContext" (dict "secContext" .Values.containerSecurityContext "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.diagnosticMode.enabled }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.diagnosticMode.command "context" $) | nindent 12 }}
          {{- else if .Values.command }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.command "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.diagnosticMode.enabled }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.diagnosticMode.args "context" $) | nindent 12 }}
          {{- else if .Values.args }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.args "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.resources }}
          resources: {{- toYaml .Values.resources | nindent 12 }}
          {{- else if ne .Values.resourcesPreset "none" }}
          resources: {{- include "common.resources.preset" (dict "type" .Values.resourcesPreset) | nindent 12 }}
          {{- end }}
          env:
            - name: BITNAMI_DEBUG
              value: {{ ternary "true" "false" (or .Values.image.debug .Values.diagnosticMode.enabled) | quote }}
            - name: ZOO_DATA_LOG_DIR
              value: {{ .Values.dataLogDir | quote }}
            - name: ZOO_PORT_NUMBER
              value: {{ .Values.containerPorts.client | quote }}
            - name: ZOO_TICK_TIME
              value: {{ .Values.tickTime | quote }}
            - name: ZOO_INIT_LIMIT
              value: {{ .Values.initLimit | quote }}
            - name: ZOO_SYNC_LIMIT
              value: {{ .Values.syncLimit | quote }}
            - name: ZOO_PRE_ALLOC_SIZE
              value: {{ .Values.preAllocSize | quote }}
            - name: ZOO_SNAPCOUNT
              value: {{ .Values.snapCount | quote }}
            - name: ZOO_MAX_CLIENT_CNXNS
              value: {{ .Values.maxClientCnxns | quote }}
            - name: ZOO_4LW_COMMANDS_WHITELIST
              value: {{ .Values.fourlwCommandsWhitelist | quote }}
            - name: ZOO_LISTEN_ALLIPS_ENABLED
              value: {{ ternary "yes" "no" .Values.listenOnAllIPs | quote }}
            - name: ZOO_AUTOPURGE_INTERVAL
              value: {{ .Values.autopurge.purgeInterval | quote }}
            - name: ZOO_AUTOPURGE_RETAIN_COUNT
              value: {{ .Values.autopurge.snapRetainCount | quote }}
            - name: ZOO_MAX_SESSION_TIMEOUT
              value: {{ .Values.maxSessionTimeout | quote }}
            - name: ZOO_SERVERS
              {{- $replicaCount := int .Values.replicaCount }}
              {{- $minServerId := int .Values.minServerId }}
              {{- $followerPort := int .Values.containerPorts.follower }}
              {{- $electionPort := int .Values.containerPorts.election }}
              {{- $releaseNamespace := include "zookeeper.namespace" . }}
              {{- $zookeeperFullname := include "common.names.fullname" . }}
              {{- $zookeeperHeadlessServiceName := printf "%s-%s" $zookeeperFullname "headless" | trunc 63  }}
              {{- $clusterDomain := .Values.clusterDomain }}
              {{- if .Values.zooServers }}
              value: {{ include "common.tplvalues.render" (dict "value" .Values.zooServers "context" $) | quote }}
              {{- else }}
              value: {{ range $i, $e := until $replicaCount }}{{ $zookeeperFullname }}-{{ $e }}.{{ $zookeeperHeadlessServiceName }}.{{ $releaseNamespace }}.svc.{{ $clusterDomain }}:{{ $followerPort }}:{{ $electionPort }}::{{ add $e $minServerId }} {{ end }}
              {{- end }}
            - name: ZOO_ENABLE_AUTH
              value: {{ ternary "yes" "no" .Values.auth.client.enabled | quote }}
            {{- if .Values.auth.client.enabled }}
            - name: ZOO_CLIENT_USER
              value: {{ .Values.auth.client.clientUser | quote }}
            - name: ZOO_CLIENT_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "zookeeper.client.secretName" . }}
                  key: client-password
            - name: ZOO_SERVER_USERS
              value: {{ .Values.auth.client.serverUsers | quote }}
            - name: ZOO_SERVER_PASSWORDS
              valueFrom:
                secretKeyRef:
                  name: {{ include "zookeeper.client.secretName" . }}
                  key: server-password
            {{- end }}
            - name: ZOO_ENABLE_QUORUM_AUTH
              value: {{ ternary "yes" "no" .Values.auth.quorum.enabled | quote }}
            {{- if .Values.auth.quorum.enabled }}
            - name: ZOO_QUORUM_LEARNER_USER
              value: {{ .Values.auth.quorum.learnerUser | quote }}
            - name: ZOO_QUORUM_LEARNER_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "zookeeper.quorum.secretName" . }}
                  key: quorum-learner-password
            - name: ZOO_QUORUM_SERVER_USERS
              value: {{ .Values.auth.quorum.serverUsers | quote }}
            - name: ZOO_QUORUM_SERVER_PASSWORDS
              valueFrom:
                secretKeyRef:
                  name: {{ include "zookeeper.quorum.secretName" . }}
                  key: quorum-server-password
            {{- end }}
            - name: ZOO_HEAP_SIZE
              value: {{ .Values.heapSize | quote }}
            - name: ZOO_LOG_LEVEL
              value: {{ .Values.logLevel | quote }}
            - name: ALLOW_ANONYMOUS_LOGIN
              value: {{ ternary "no" "yes" .Values.auth.client.enabled | quote }}
            {{- if .Values.jvmFlags }}
            - name: JVMFLAGS
              value: {{ .Values.jvmFlags | quote }}
            {{- end }}
            {{- if .Values.metrics.enabled }}
            - name: ZOO_ENABLE_PROMETHEUS_METRICS
              value: "yes"
            - name: ZOO_PROMETHEUS_METRICS_PORT_NUMBER
              value: {{ coalesce .Values.metrics.containerPort .Values.containerPorts.metrics | quote }}
            {{- end }}
            {{- if .Values.tls.client.enabled }}
            - name: ZOO_TLS_PORT_NUMBER
              value: {{ .Values.containerPorts.tls | quote }}
            - name: ZOO_TLS_CLIENT_ENABLE
              value: {{ .Values.tls.client.enabled | quote }}
            - name: ZOO_TLS_CLIENT_AUTH
              value: {{ .Values.tls.client.auth | quote }}
            - name: ZOO_TLS_CLIENT_KEYSTORE_FILE
              value: {{ .Values.tls.client.keystorePath | quote }}
            - name: ZOO_TLS_CLIENT_TRUSTSTORE_FILE
              value: {{ .Values.tls.client.truststorePath | quote }}
            {{- if or .Values.tls.client.keystorePassword .Values.tls.client.passwordsSecretName .Values.tls.client.autoGenerated }}
            - name: ZOO_TLS_CLIENT_KEYSTORE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "zookeeper.client.tlsPasswordsSecret" . }}
                  key: {{ include "zookeeper.client.tlsPasswordKeystoreKey" . }}
            {{- end }}
            {{- if or .Values.tls.client.truststorePassword .Values.tls.client.passwordsSecretName .Values.tls.client.autoGenerated }}
            - name: ZOO_TLS_CLIENT_TRUSTSTORE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "zookeeper.client.tlsPasswordsSecret" . }}
                  key: {{ include "zookeeper.client.tlsPasswordTruststoreKey" . }}
            {{- end }}
            {{- end }}
            {{- if .Values.tls.quorum.enabled }}
            - name: ZOO_TLS_QUORUM_ENABLE
              value: {{ .Values.tls.quorum.enabled | quote }}
            - name: ZOO_TLS_QUORUM_CLIENT_AUTH
              value: {{ .Values.tls.quorum.auth | quote }}
            - name: ZOO_TLS_QUORUM_KEYSTORE_FILE
              value: {{ .Values.tls.quorum.keystorePath | quote }}
            - name: ZOO_TLS_QUORUM_TRUSTSTORE_FILE
              value: {{ .Values.tls.quorum.truststorePath | quote }}
            {{- if or .Values.tls.quorum.keystorePassword .Values.tls.quorum.passwordsSecretName .Values.tls.quorum.autoGenerated }}
            - name: ZOO_TLS_QUORUM_KEYSTORE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "zookeeper.quorum.tlsPasswordsSecret" . }}
                  key: {{ include "zookeeper.quorum.tlsPasswordKeystoreKey" . }}
            {{- end }}
            {{- if or .Values.tls.quorum.truststorePassword .Values.tls.quorum.passwordsSecretName .Values.tls.quorum.autoGenerated }}
            - name: ZOO_TLS_QUORUM_TRUSTSTORE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "zookeeper.quorum.tlsPasswordsSecret" . }}
                  key: {{ include "zookeeper.quorum.tlsPasswordTruststoreKey" . }}
            {{- end }}
            {{- end }}
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.name
            - name: ZOO_ADMIN_SERVER_PORT_NUMBER
              value: {{ .Values.containerPorts.adminServer | quote }}
            {{- if .Values.extraEnvVars }}
            {{- include "common.tplvalues.render" (dict "value" .Values.extraEnvVars "context" $) | nindent 12 }}
            {{- end }}
          {{- if or .Values.extraEnvVarsCM .Values.extraEnvVarsSecret }}
          envFrom:
            {{- if .Values.extraEnvVarsCM }}
            - configMapRef:
                name: {{ include "common.tplvalues.render" (dict "value" .Values.extraEnvVarsCM "context" $) }}
            {{- end }}
            {{- if .Values.extraEnvVarsSecret }}
            - secretRef:
                name: {{ include "common.tplvalues.render" (dict "value" .Values.extraEnvVarsSecret "context" $) }}
            {{- end }}
          {{- end }}
          ports:
            {{- if not .Values.service.disableBaseClientPort }}
            - name: client
              containerPort: {{ .Values.containerPorts.client }}
            {{- end }}
            {{- if .Values.tls.client.enabled }}
            - name: client-tls
              containerPort: {{ .Values.containerPorts.tls }}
            {{- end }}
            {{- if gt (int .Values.replicaCount) 1 }}
            {{- /* These ports are only open when there are more than 1 replica */}}
            - name: follower
              containerPort: {{ .Values.containerPorts.follower }}
            - name: election
              containerPort: {{ .Values.containerPorts.election }}
            {{- end }}
            {{- if .Values.metrics.enabled }}
            - name: metrics
              containerPort: {{ coalesce .Values.metrics.containerPort .Values.containerPorts.metrics }}
            {{- end }}
            - name: http-admin
              containerPort: {{ .Values.containerPorts.adminServer }}
          {{- if not .Values.diagnosticMode.enabled }}
          {{- if .Values.customLivenessProbe }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.customLivenessProbe "context" $) | nindent 12 }}
          {{- else if .Values.livenessProbe.enabled }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.livenessProbe "enabled" "probeCommandTimeout") "context" $) | nindent 12 }}
            exec:
              command:
                - /bin/bash
                - -ec
                - ZOO_HC_TIMEOUT={{ .Values.livenessProbe.probeCommandTimeout }} /opt/bitnami/scripts/zookeeper/healthcheck.sh
          {{- end }}
          {{- if .Values.customReadinessProbe }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.customReadinessProbe "context" $) | nindent 12 }}
          {{- else if .Values.readinessProbe.enabled }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.readinessProbe "enabled" "probeCommandTimeout") "context" $) | nindent 12 }}
            exec:
              command:
                - /bin/bash
                - -ec
                - ZOO_HC_TIMEOUT={{ .Values.readinessProbe.probeCommandTimeout }} /opt/bitnami/scripts/zookeeper/healthcheck.sh
          {{- end }}
          {{- if .Values.customStartupProbe }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" .Values.customStartupProbe "context" $) | nindent 12 }}
          {{- else if .Values.startupProbe.enabled }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.startupProbe "enabled") "context" $) | nindent 12 }}
            tcpSocket:
              {{- if not .Values.service.disableBaseClientPort }}
              port: client
              {{- else }}
              port: follower
              {{- end }}
          {{- end }}
          {{- end }}
          {{- if .Values.lifecycleHooks }}
          lifecycle: {{- include "common.tplvalues.render" (dict "value" .Values.lifecycleHooks "context" $) | nindent 12 }}
          {{- end }}
          volumeMounts:
            - name: empty-dir
              mountPath: /tmp
              subPath: tmp-dir
            - name: empty-dir
              mountPath: /opt/bitnami/zookeeper/conf
              subPath: app-conf-dir
            - name: empty-dir
              mountPath: /opt/bitnami/zookeeper/logs
              subPath: app-logs-dir
            - name: scripts
              mountPath: /scripts/setup.sh
              subPath: setup.sh
            - name: data
              mountPath: /bitnami/zookeeper
            {{- if .Values.dataLogDir }}
            - name: data-log
              mountPath: {{ .Values.dataLogDir }}
            {{- end }}
            {{- if or .Values.configuration .Values.existingConfigmap }}
            - name: config
              mountPath: /opt/bitnami/zookeeper/conf/zoo.cfg
              subPath: zoo.cfg
            {{- end }}
            {{- if .Values.tls.client.enabled }}
            - name: client-shared-certs
              mountPath: /opt/bitnami/zookeeper/config/certs/client
              readOnly: true
            {{- end }}
            {{- if .Values.tls.quorum.enabled }}
            - name: quorum-shared-certs
              mountPath: /opt/bitnami/zookeeper/config/certs/quorum
              readOnly: true
            {{- end }}
            {{- if .Values.extraVolumeMounts }}
            {{- include "common.tplvalues.render" ( dict "value" .Values.extraVolumeMounts "context" $ ) | nindent 12 }}
            {{- end }}
        {{- if .Values.sidecars }}
        {{- include "common.tplvalues.render" ( dict "value" .Values.sidecars "context" $ ) | nindent 8 }}
        {{- end }}
      volumes:
        - name: empty-dir
          emptyDir: {}
        - name: scripts
          configMap:
            name: {{ printf "%s-scripts" (include "common.names.fullname" .) }}
            defaultMode: 493
        {{- if or .Values.configuration .Values.existingConfigmap }}
        - name: config
          configMap:
            name: {{ include "zookeeper.configmapName" . }}
        {{- end }}
        {{- if and .Values.persistence.enabled .Values.persistence.existingClaim }}
        - name: data
          persistentVolumeClaim:
            claimName: {{ printf "%s" (tpl .Values.persistence.existingClaim .) }}
        {{- else if not .Values.persistence.enabled }}
        - name: data
          emptyDir: {}
        {{- end }}
        {{- if and .Values.persistence.enabled .Values.persistence.dataLogDir.existingClaim }}
        - name: data-log
          persistentVolumeClaim:
            claimName: {{ printf "%s" (tpl .Values.persistence.dataLogDir.existingClaim .) }}
        {{- else if and ( not .Values.persistence.enabled ) .Values.dataLogDir }}
        - name: data-log
          emptyDir: {}
        {{- end }}
        {{- if .Values.tls.client.enabled }}
        - name: client-certificates
          secret:
            secretName: {{ include "zookeeper.client.tlsSecretName" . }}
            defaultMode: 256
        - name: client-shared-certs
          emptyDir: {}
        {{- end }}
        {{- if .Values.tls.quorum.enabled }}
        - name: quorum-certificates
          secret:
            secretName: {{ include "zookeeper.quorum.tlsSecretName" . }}
            defaultMode: 256
        - name: quorum-shared-certs
          emptyDir: {}
        {{- end }}
        {{- if .Values.extraVolumes }}
        {{- include "common.tplvalues.render" (dict "value" .Values.extraVolumes "context" $) | nindent 8 }}
        {{- end }}
  {{- if and .Values.persistence.enabled (not (and .Values.persistence.existingClaim .Values.persistence.dataLogDir.existingClaim) ) }}
  volumeClaimTemplates:
    {{- if not .Values.persistence.existingClaim }}
    - apiVersion: v1
      kind: PersistentVolumeClaim
      metadata:
        name: data
        {{- if .Values.persistence.annotations }}
        annotations: {{- include "common.tplvalues.render" (dict "value" .Values.persistence.annotations "context" $) | nindent 10 }}
        {{- end }}
        {{- if .Values.persistence.labels }}
        labels: {{- include "common.tplvalues.render" (dict "value" .Values.persistence.labels "context" $) | nindent 10 }}
        {{- end }}
      spec:
        accessModes:
        {{- range .Values.persistence.accessModes }}
          - {{ . | quote }}
        {{- end }}
        resources:
          requests:
            storage: {{ .Values.persistence.size | quote }}
        {{- include "common.storage.class" (dict "persistence" .Values.persistence "global" .Values.global) | nindent 8 }}
        {{- if .Values.persistence.selector }}
        selector: {{- include "common.tplvalues.render" (dict "value" .Values.persistence.selector "context" $) | nindent 10 }}
        {{- end }}
    {{- end }}
    {{- if and (not .Values.persistence.dataLogDir.existingClaim) .Values.dataLogDir }}
    - apiVersion: v1
      kind: PersistentVolumeClaim
      metadata:
        name: data-log
        {{- if .Values.persistence.annotations }}
        annotations: {{- include "common.tplvalues.render" (dict "value" .Values.persistence.annotations "context" $) | nindent 10 }}
        {{- end }}
        {{- if .Values.persistence.labels }}
        labels: {{- include "common.tplvalues.render" (dict "value" .Values.persistence.labels "context" $) | nindent 10 }}
        {{- end }}
      spec:
        accessModes:
        {{- range .Values.persistence.accessModes }}
          - {{ . | quote }}
        {{- end }}
        resources:
          requests:
            storage: {{ .Values.persistence.dataLogDir.size | quote }}
        {{- include "common.storage.class" (dict "persistence" .Values.persistence "global" .Values.global) | nindent 8 }}
        {{- if .Values.persistence.dataLogDir.selector }}
        selector: {{- include "common.tplvalues.render" (dict "value" .Values.persistence.dataLogDir.selector "context" $) | nindent 10 }}
        {{- end }}
    {{- end }}
  {{- end }}
