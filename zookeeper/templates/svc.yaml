{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

apiVersion: v1
kind: Service
metadata:
  name: {{ template "common.names.fullname" . }}
  namespace: {{ template "zookeeper.namespace" . }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: zookeeper
  {{- if or .Values.commonAnnotations .Values.service.annotations }}
  {{- $annotations := include "common.tplvalues.merge" ( dict "values" ( list .Values.service.annotations .Values.commonAnnotations ) "context" . ) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $) | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.service.type }}
  {{- if and .Values.service.clusterIP (eq .Values.service.type "ClusterIP") }}
  clusterIP: {{ .Values.service.clusterIP }}
  {{- end }}
  {{- if or (eq .Values.service.type "LoadBalancer") (eq .Values.service.type "NodePort") }}
  externalTrafficPolicy: {{ .Values.service.externalTrafficPolicy | quote }}
  {{- end }}
  {{- if and (eq .Values.service.type "LoadBalancer") (not (empty .Values.service.loadBalancerSourceRanges)) }}
  loadBalancerSourceRanges: {{ .Values.service.loadBalancerSourceRanges }}
  {{- end }}
  {{- if and (eq .Values.service.type "LoadBalancer") (not (empty .Values.service.loadBalancerIP)) }}
  loadBalancerIP: {{ .Values.service.loadBalancerIP }}
  {{- end }}
  {{- if .Values.service.sessionAffinity }}
  sessionAffinity: {{ .Values.service.sessionAffinity }}
  {{- end }}
  {{- if .Values.service.sessionAffinityConfig }}
  sessionAffinityConfig: {{- include "common.tplvalues.render" (dict "value" .Values.service.sessionAffinityConfig "context" $) | nindent 4 }}
  {{- end }}
  ports:
    {{- if not .Values.service.disableBaseClientPort }}
    - name: tcp-client
      port: {{ .Values.service.ports.client }}
      targetPort: client
      {{- if and (or (eq .Values.service.type "NodePort") (eq .Values.service.type "LoadBalancer")) (not (empty .Values.service.nodePorts.client)) }}
      nodePort: {{ .Values.service.nodePorts.client }}
      {{- else if eq .Values.service.type "ClusterIP" }}
      nodePort: null
      {{- end }}
    {{- end }}
    {{- if .Values.tls.client.enabled }}
    - name: tcp-client-tls
      port: {{ .Values.service.ports.tls }}
      targetPort: client-tls
      {{- if and (or (eq .Values.service.type "NodePort") (eq .Values.service.type "LoadBalancer")) (not (empty .Values.service.nodePorts.tls)) }}
      nodePort: {{ .Values.service.nodePorts.tls }}
      {{- else if eq .Values.service.type "ClusterIP" }}
      nodePort: null
      {{- end }}
    {{- end }}
    {{- if gt (int .Values.replicaCount) 1 }}
    {{- /* These ports are only open when there are more than 1 replica */}}
    - name: tcp-follower
      port: {{ .Values.service.ports.follower }}
      targetPort: follower
    - name: tcp-election
      port: {{ .Values.service.ports.election }}
      targetPort: election
    {{- end }}
    {{- if .Values.service.extraPorts }}
    {{- include "common.tplvalues.render" (dict "value" .Values.service.extraPorts "context" $) | nindent 4 }}
    {{- end }}
  {{- $podLabels := include "common.tplvalues.merge" ( dict "values" ( list .Values.podLabels .Values.commonLabels ) "context" . ) }}
  selector: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: zookeeper
