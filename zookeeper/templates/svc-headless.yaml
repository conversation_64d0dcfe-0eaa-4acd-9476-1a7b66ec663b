{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

apiVersion: v1
kind: Service
metadata:
  name: {{ printf "%s-%s" (include "common.names.fullname" .) (default "headless" .Values.service.headless.servicenameOverride) | trunc 63 | trimSuffix "-" }}
  namespace: {{ template "zookeeper.namespace" . }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: zookeeper
  {{- if or .Values.commonAnnotations .Values.service.headless.annotations }}
  {{- $annotations := include "common.tplvalues.merge" ( dict "values" ( list .Values.service.headless.annotations .Values.commonAnnotations ) "context" . ) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $) | nindent 4 }}
  {{- end }}
spec:
  type: ClusterIP
  clusterIP: None
  publishNotReadyAddresses: {{ .Values.service.headless.publishNotReadyAddresses }}
  ports:
    {{- if not .Values.service.disableBaseClientPort }}
    - name: tcp-client
      port: {{ .Values.service.ports.client }}
      targetPort: client
    {{- end }}
    {{- if .Values.tls.client.enabled }}
    - name: tcp-client-tls
      port: {{ .Values.service.ports.tls }}
      targetPort: client-tls
    {{- end }}
    - name: tcp-follower
      port: {{ .Values.service.ports.follower }}
      targetPort: follower
    - name: tcp-election
      port: {{ .Values.service.ports.election }}
      targetPort: election
  {{- $podLabels := include "common.tplvalues.merge" ( dict "values" ( list .Values.podLabels .Values.commonLabels ) "context" . ) }}
  selector: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: zookeeper
