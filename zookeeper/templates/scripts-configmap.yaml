{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ printf "%s-scripts" (include "common.names.fullname" .) }}
  namespace: {{ template "zookeeper.namespace" . }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: zookeeper
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
data:
  init-certs.sh: |-
    #!/bin/bash

    {{- if .Values.tls.client.enabled }}
    if [[ -f "/certs/client/tls.key" ]] && [[ -f "/certs/client/tls.crt" ]] && [[ -f "/certs/client/ca.crt" ]]; then
        if [[ -f "/opt/bitnami/zookeeper/config/certs/client/.initialized" ]]; then
            exit 0
        fi
        openssl pkcs12 -export -in "/certs/client/tls.crt" \
          -passout pass:"${ZOO_TLS_CLIENT_KEYSTORE_PASSWORD}" \
          -inkey "/certs/client/tls.key" \
          -out "/tmp/keystore.p12"
        keytool -importkeystore -srckeystore "/tmp/keystore.p12" \
          -srcstoretype PKCS12 \
          -srcstorepass "${ZOO_TLS_CLIENT_KEYSTORE_PASSWORD}" \
          -deststorepass "${ZOO_TLS_CLIENT_KEYSTORE_PASSWORD}" \
          -destkeystore "/opt/bitnami/zookeeper/config/certs/client/zookeeper.keystore.jks"
        rm "/tmp/keystore.p12"
        keytool -import -file "/certs/client/ca.crt" \
              -keystore "/opt/bitnami/zookeeper/config/certs/client/zookeeper.truststore.jks" \
              -storepass "${ZOO_TLS_CLIENT_TRUSTSTORE_PASSWORD}" \
              -noprompt
        touch /opt/bitnami/zookeeper/config/certs/client/.initialized
    {{- if .Values.tls.client.autoGenerated }}
    else
        echo "Couldn't find the expected PEM certificates! They are mandatory when Client encryption via TLS is enabled."
        exit 1
    fi
    {{- else }}
    elif [[ -f {{ printf "/certs/client/%s" (include "zookeeper.client.tlsTruststoreKey" .) | quote }} ]] && [[ -f {{ printf "/certs/client/%s" (include "zookeeper.client.tlsKeystoreKey" .) | quote }} ]]; then
        cp {{ printf "/certs/client/%s" (include "zookeeper.client.tlsTruststoreKey" .) | quote }} "/opt/bitnami/zookeeper/config/certs/client/zookeeper.truststore.jks"
        cp {{ printf "/certs/client/%s" (include "zookeeper.client.tlsKeystoreKey" .) | quote }} "/opt/bitnami/zookeeper/config/certs/client/zookeeper.keystore.jks"
    else
        echo "Couldn't find the expected Java Key Stores (JKS) files! They are mandatory when Client encryption via TLS is enabled."
        exit 1
    fi
    {{- end }}
    {{- end }}
    {{- if .Values.tls.quorum.enabled }}
    if [[ -f "/certs/quorum/tls.key" ]] && [[ -f "/certs/quorum/tls.crt" ]] && [[ -f "/certs/quorum/ca.crt" ]]; then
        openssl pkcs12 -export -in "/certs/quorum/tls.crt" \
          -passout pass:"${ZOO_TLS_QUORUM_KEYSTORE_PASSWORD}" \
          -inkey "/certs/quorum/tls.key" \
          -out "/tmp/keystore.p12"
        keytool -importkeystore -srckeystore "/tmp/keystore.p12" \
          -srcstoretype PKCS12 \
          -srcstorepass "${ZOO_TLS_QUORUM_KEYSTORE_PASSWORD}" \
          -deststorepass "${ZOO_TLS_QUORUM_KEYSTORE_PASSWORD}" \
          -destkeystore "/opt/bitnami/zookeeper/config/certs/quorum/zookeeper.keystore.jks"
        rm "/tmp/keystore.p12"
        keytool -import -file "/certs/quorum/ca.crt" \
              -keystore "/opt/bitnami/zookeeper/config/certs/quorum/zookeeper.truststore.jks" \
              -storepass "${ZOO_TLS_QUORUM_TRUSTSTORE_PASSWORD}" \
              -noprompt
    {{- if .Values.tls.quorum.autoGenerated }}
    else
        echo "Couldn't find the expected PEM certificates! They are mandatory when encryption Quorum via TLS is enabled."
        exit 1
    fi
    {{- else }}
    elif [[ -f {{ printf "/certs/quorum/%s" (include "zookeeper.quorum.tlsTruststoreKey" .) | quote }} ]] && [[ -f {{ printf "/certs/quorum/%s" (include "zookeeper.quorum.tlsKeystoreKey" .) | quote }} ]]; then
        cp {{ printf "/certs/quorum/%s" (include "zookeeper.quorum.tlsTruststoreKey" .) | quote }} "/opt/bitnami/zookeeper/config/certs/quorum/zookeeper.truststore.jks"
        cp {{ printf "/certs/quorum/%s" (include "zookeeper.quorum.tlsKeystoreKey" .) | quote }} "/opt/bitnami/zookeeper/config/certs/quorum/zookeeper.keystore.jks"
    else
        echo "Couldn't find the expected Java Key Stores (JKS) files! They are mandatory when Quorum encryption via TLS is enabled."
        exit 1
    fi
    {{- end }}
    {{- end }}
  setup.sh: |-
    #!/bin/bash

    # Execute entrypoint as usual after obtaining ZOO_SERVER_ID
    # check ZOO_SERVER_ID in persistent volume via myid
    # if not present, set based on POD hostname
    if [[ -f "/bitnami/zookeeper/data/myid" ]]; then
        export ZOO_SERVER_ID="$(cat /bitnami/zookeeper/data/myid)"
    else
        HOSTNAME="$(hostname -s)"
        if [[ $HOSTNAME =~ (.*)-([0-9]+)$ ]]; then
            ORD=${BASH_REMATCH[2]}
            export ZOO_SERVER_ID="$((ORD + {{ .Values.minServerId }} ))"
        else
            echo "Failed to get index from hostname $HOSTNAME"
            exit 1
        fi
    fi
    exec /entrypoint.sh /run.sh
