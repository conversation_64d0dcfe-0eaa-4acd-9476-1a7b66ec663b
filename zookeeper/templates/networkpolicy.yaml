{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if .Values.networkPolicy.enabled }}
kind: NetworkPolicy
apiVersion: {{ include "common.capabilities.networkPolicy.apiVersion" . }}
metadata:
  name: {{ include "common.names.fullname" . }}
  namespace: {{ template "zookeeper.namespace" . }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  {{- $podLabels := include "common.tplvalues.merge" ( dict "values" ( list .Values.podLabels .Values.commonLabels ) "context" . ) }}
  podSelector:
    matchLabels: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 6 }}
  policyTypes:
    - Ingress
    - Egress
  {{- if .Values.networkPolicy.allowExternalEgress }}
  egress:
    - {}
  {{- else }}
  egress:
    # Allow dns resolution
    - ports:
        - port: 53
          protocol: UDP
        - port: 53
          protocol: TCP
    # Allow internal communications between nodes
    - ports:
        - port: {{ .Values.containerPorts.follower }}
        - port: {{ .Values.containerPorts.election }}
      to:
        - podSelector:
            matchLabels: {{- include "common.labels.matchLabels" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 14 }}
    {{- if .Values.networkPolicy.extraEgress }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.rts.networkPolicy.extraEgress "context" $ ) | nindent 4 }}
    {{- end }}
  {{- end }}
  ingress:
    # Allow inbound connections to ZooKeeper
    - ports:
        - port: {{ .Values.containerPorts.client }}
        {{- if .Values.metrics.enabled }}
        - port: {{ coalesce .Values.metrics.containerPort .Values.containerPorts.metrics }}
        {{- end }}
      {{- if not .Values.networkPolicy.allowExternal }}
      from:
        - podSelector:
            matchLabels: {{- include "common.labels.matchLabels" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 14 }}
        - podSelector:
            matchLabels:
              {{ include "common.names.fullname" . }}-client: "true"
        - podSelector:
            matchLabels: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 14 }}
        {{- if .Values.networkPolicy.ingressNSMatchLabels }}
        - namespaceSelector:
            matchLabels:
              {{- range $key, $value := .Values.networkPolicy.ingressNSMatchLabels }}
              {{ $key | quote }}: {{ $value | quote }}
              {{- end }}
          {{- if .Values.networkPolicy.ingressNSPodMatchLabels }}
          podSelector:
            matchLabels:
              {{- range $key, $value := .Values.networkPolicy.ingressNSPodMatchLabels }}
              {{ $key | quote }}: {{ $value | quote }}
              {{- end }}
          {{- end }}
        {{- end }}
      {{- end }}
    # Allow internal communications between nodes
    - ports:
        - port: {{ .Values.containerPorts.follower }}
        - port: {{ .Values.containerPorts.election }}
      from:
        - podSelector:
            matchLabels: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 14 }}
    {{- if .Values.networkPolicy.extraIngress }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.networkPolicy.extraIngress "context" $ ) | nindent 4 }}
    {{- end }}
{{- end }}
