# Copyright Broadcom, Inc. All Rights Reserved.
# SPDX-License-Identifier: APACHE-2.0

## @section Global parameters
## Global Docker image parameters
## Please, note that this will override the image parameters, including dependencies, configured to use the global value
## Current available global Docker image parameters: imageRegistry, imagePullSecrets and storageClass
##

## @param global.imageRegistry Global Docker image registry
## @param global.imagePullSecrets Global Docker registry secret names as an array
## @param global.defaultStorageClass Global default StorageClass for Persistent Volume(s)
## @param global.storageClass DEPRECATED: use global.defaultStorageClass instead
##
global:
  imageRegistry: ""
  ## E.g.
  ## imagePullSecrets:
  ##   - myRegistryKeySecretName
  ##
  imagePullSecrets: []
  defaultStorageClass: ""
  storageClass: ""
  ## Security parameters
  ##
  security:
    ## @param global.security.allowInsecureImages Allows skipping image verification
    allowInsecureImages: false
  ## Compatibility adaptations for Kubernetes platforms
  ##
  compatibility:
    ## Compatibility adaptations for Openshift
    ##
    openshift:
      ## @param global.compatibility.openshift.adaptSecurityContext Adapt the securityContext sections of the deployment to make them compatible with Openshift restricted-v2 SCC: remove runAsUser, runAsGroup and fsGroup and let the platform use their allowed default IDs. Possible values: auto (apply if the detected running cluster is Openshift), force (perform the adaptation always), disabled (do not perform adaptation)
      ##
      adaptSecurityContext: auto
## @section Common parameters
##

## @param kubeVersion Override Kubernetes version
##
kubeVersion: ""
## @param nameOverride String to partially override common.names.fullname template (will maintain the release name)
##
nameOverride: ""
## @param fullnameOverride String to fully override common.names.fullname template
##
fullnameOverride: ""
## @param clusterDomain Kubernetes Cluster Domain
##
clusterDomain: cluster.local
## @param extraDeploy Extra objects to deploy (evaluated as a template)
##
extraDeploy: []
## @param commonLabels Add labels to all the deployed resources
##
commonLabels: {}
## @param commonAnnotations Add annotations to all the deployed resources
##
commonAnnotations: {}
## @param namespaceOverride Override namespace for ZooKeeper resources
## Useful when including ZooKeeper as a chart dependency, so it can be released into a different namespace than the parent
##
namespaceOverride: ""
## Enable diagnostic mode in the statefulset
##
diagnosticMode:
  ## @param diagnosticMode.enabled Enable diagnostic mode (all probes will be disabled and the command will be overridden)
  ##
  enabled: false
  ## @param diagnosticMode.command Command to override all containers in the statefulset
  ##
  command:
    - sleep
  ## @param diagnosticMode.args Args to override all containers in the statefulset
  ##
  args:
    - infinity
## @section ZooKeeper chart parameters

## Bitnami ZooKeeper image version
## ref: https://hub.docker.com/r/bitnami/zookeeper/tags/
## @param image.registry [default: REGISTRY_NAME] ZooKeeper image registry
## @param image.repository [default: REPOSITORY_NAME/zookeeper] ZooKeeper image repository
## @skip image.tag ZooKeeper image tag (immutable tags are recommended)
## @param image.digest ZooKeeper image digest in the way sha256:aa.... Please note this parameter, if set, will override the tag
## @param image.pullPolicy ZooKeeper image pull policy
## @param image.pullSecrets Specify docker-registry secret names as an array
## @param image.debug Specify if debug values should be set
##
image:
  registry: docker.io
  repository: bitnami/zookeeper
  tag: 3.9.3-debian-12-r8
  digest: ""
  ## Specify a imagePullPolicy
  ## ref: https://kubernetes.io/docs/concepts/containers/images/#pre-pulled-images
  ##
  pullPolicy: IfNotPresent
  ## Optionally specify an array of imagePullSecrets.
  ## Secrets must be manually created in the namespace.
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/
  ## Example:
  ## pullSecrets:
  ##   - myRegistryKeySecretName
  ##
  pullSecrets: []
  ## Set to true if you would like to see extra information on logs
  ##
  debug: false
## Authentication parameters
##
auth:
  client:
    ## @param auth.client.enabled Enable ZooKeeper client-server authentication. It uses SASL/Digest-MD5
    ##
    enabled: false
    ## @param auth.client.clientUser User that will use ZooKeeper clients to auth
    ##
    clientUser: ""
    ## @param auth.client.clientPassword Password that will use ZooKeeper clients to auth
    ##
    clientPassword: ""
    ## @param auth.client.serverUsers Comma, semicolon or whitespace separated list of user to be created
    ## Specify them as a string, for example: "user1,user2,admin"
    ##
    serverUsers: ""
    ## @param auth.client.serverPasswords Comma, semicolon or whitespace separated list of passwords to assign to users when created
    ## Specify them as a string, for example: "pass4user1, pass4user2, pass4admin"
    ##
    serverPasswords: ""
    ## @param auth.client.existingSecret Use existing secret (ignores previous passwords)
    ##
    existingSecret: ""
  quorum:
    ## @param auth.quorum.enabled Enable ZooKeeper server-server authentication. It uses SASL/Digest-MD5
    ##
    enabled: false
    ## @param auth.quorum.learnerUser User that the ZooKeeper quorumLearner will use to authenticate to quorumServers.
    ## Note: Make sure the user is included in auth.quorum.serverUsers
    ##
    learnerUser: ""
    ## @param auth.quorum.learnerPassword Password that the ZooKeeper quorumLearner will use to authenticate to quorumServers.
    ##
    learnerPassword: ""
    ## @param auth.quorum.serverUsers Comma, semicolon or whitespace separated list of users for the quorumServers.
    ## Specify them as a string, for example: "user1,user2,admin"
    ##
    serverUsers: ""
    ## @param auth.quorum.serverPasswords Comma, semicolon or whitespace separated list of passwords to assign to users when created
    ## Specify them as a string, for example: "pass4user1, pass4user2, pass4admin"
    ##
    serverPasswords: ""
    ## @param auth.quorum.existingSecret Use existing secret (ignores previous passwords)
    ##
    existingSecret: ""
## @param tickTime Basic time unit (in milliseconds) used by ZooKeeper for heartbeats
##
tickTime: 2000
## @param initLimit ZooKeeper uses to limit the length of time the ZooKeeper servers in quorum have to connect to a leader
##
initLimit: 10
## @param syncLimit How far out of date a server can be from a leader
##
syncLimit: 5
## @param preAllocSize Block size for transaction log file
##
preAllocSize: 65536
## @param snapCount The number of transactions recorded in the transaction log before a snapshot can be taken (and the transaction log rolled)
##
snapCount: 100000
## @param maxClientCnxns Limits the number of concurrent connections that a single client may make to a single member of the ZooKeeper ensemble
##
maxClientCnxns: 60
## @param maxSessionTimeout Maximum session timeout (in milliseconds) that the server will allow the client to negotiate
## Defaults to 20 times the tickTime
##
maxSessionTimeout: 40000
## @param heapSize Size (in MB) for the Java Heap options (Xmx and Xms)
## This env var is ignored if Xmx an Xms are configured via `jvmFlags`
##
heapSize: 1024
## @param fourlwCommandsWhitelist A list of comma separated Four Letter Words commands that can be executed
##
fourlwCommandsWhitelist: srvr, mntr, ruok
## @param minServerId Minimal SERVER_ID value, nodes increment their IDs respectively
## Servers increment their ID starting at this minimal value.
## E.g., with `minServerId=10` and 3 replicas, server IDs will be 10, 11, 12 for z-0, z-1 and z-2 respectively.
##
minServerId: 1
## @param listenOnAllIPs Allow ZooKeeper to listen for connections from its peers on all available IP addresses
##
listenOnAllIPs: false
## @param zooServers ZooKeeper space separated servers list. Leave empty to use the default ZooKeeper server names.
##
zooServers: ""
## Ongoing data directory cleanup configuration
##
autopurge:
  ## @param autopurge.snapRetainCount The most recent snapshots amount (and corresponding transaction logs) to retain
  ##
  snapRetainCount: 10
  ## @param autopurge.purgeInterval The time interval (in hours) for which the purge task has to be triggered
  ## Set to a positive integer to enable the auto purging. Set to 0 to disable auto purging.
  ##
  purgeInterval: 1
## @param logLevel Log level for the ZooKeeper server. ERROR by default
## Have in mind if you set it to INFO or WARN the ReadinessProve will produce a lot of logs
##
logLevel: ERROR
## @param jvmFlags Default JVM flags for the ZooKeeper process
##
jvmFlags: ""
## @param dataLogDir Dedicated data log directory
## This allows a dedicated log device to be used, and helps avoid competition between logging and snapshots.
## E.g.
## dataLogDir: /bitnami/zookeeper/dataLog
##
dataLogDir: ""
## @param configuration Configure ZooKeeper with a custom zoo.cfg file
## e.g:
## configuration: |-
##   deploy-working-dir=/bitnami/geode/data
##   log-level=info
##   ...
##
configuration: ""
## @param existingConfigmap The name of an existing ConfigMap with your custom configuration for ZooKeeper
## NOTE: When it's set the `configuration` parameter is ignored
##
existingConfigmap: ""
## @param extraEnvVars Array with extra environment variables to add to ZooKeeper nodes
## e.g:
## extraEnvVars:
##   - name: FOO
##     value: "bar"
##
extraEnvVars: []
## @param extraEnvVarsCM Name of existing ConfigMap containing extra env vars for ZooKeeper nodes
##
extraEnvVarsCM: ""
## @param extraEnvVarsSecret Name of existing Secret containing extra env vars for ZooKeeper nodes
##
extraEnvVarsSecret: ""
## @param command Override default container command (useful when using custom images)
##
command:
  - /scripts/setup.sh
## @param args Override default container args (useful when using custom images)
##
args: []
## @section Statefulset parameters

## @param replicaCount Number of ZooKeeper nodes
##
replicaCount: 1
## @param revisionHistoryLimit The number of old history to retain to allow rollback
##
revisionHistoryLimit: 10
## @param containerPorts.client ZooKeeper client container port
## @param containerPorts.tls ZooKeeper TLS container port
## @param containerPorts.follower ZooKeeper follower container port
## @param containerPorts.election ZooKeeper election container port
## @param containerPorts.adminServer ZooKeeper admin server container port
## @param containerPorts.metrics ZooKeeper Prometheus Exporter container port
##
containerPorts:
  client: 2181
  tls: 3181
  follower: 2888
  election: 3888
  adminServer: 8080
  metrics: 9141
## Configure extra options for ZooKeeper containers' liveness, readiness and startup probes
## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-startup-probes/#configure-probes
## @param livenessProbe.enabled Enable livenessProbe on ZooKeeper containers
## @param livenessProbe.initialDelaySeconds Initial delay seconds for livenessProbe
## @param livenessProbe.periodSeconds Period seconds for livenessProbe
## @param livenessProbe.timeoutSeconds Timeout seconds for livenessProbe
## @param livenessProbe.failureThreshold Failure threshold for livenessProbe
## @param livenessProbe.successThreshold Success threshold for livenessProbe
## @param livenessProbe.probeCommandTimeout Probe command timeout for livenessProbe
##
livenessProbe:
  enabled: true
  initialDelaySeconds: 30
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 6
  successThreshold: 1
  probeCommandTimeout: 3
## @param readinessProbe.enabled Enable readinessProbe on ZooKeeper containers
## @param readinessProbe.initialDelaySeconds Initial delay seconds for readinessProbe
## @param readinessProbe.periodSeconds Period seconds for readinessProbe
## @param readinessProbe.timeoutSeconds Timeout seconds for readinessProbe
## @param readinessProbe.failureThreshold Failure threshold for readinessProbe
## @param readinessProbe.successThreshold Success threshold for readinessProbe
## @param readinessProbe.probeCommandTimeout Probe command timeout for readinessProbe
##
readinessProbe:
  enabled: true
  initialDelaySeconds: 5
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 6
  successThreshold: 1
  probeCommandTimeout: 2
## @param startupProbe.enabled Enable startupProbe on ZooKeeper containers
## @param startupProbe.initialDelaySeconds Initial delay seconds for startupProbe
## @param startupProbe.periodSeconds Period seconds for startupProbe
## @param startupProbe.timeoutSeconds Timeout seconds for startupProbe
## @param startupProbe.failureThreshold Failure threshold for startupProbe
## @param startupProbe.successThreshold Success threshold for startupProbe
##
startupProbe:
  enabled: false
  initialDelaySeconds: 30
  periodSeconds: 10
  timeoutSeconds: 1
  failureThreshold: 15
  successThreshold: 1
## @param customLivenessProbe Custom livenessProbe that overrides the default one
##
customLivenessProbe: {}
## @param customReadinessProbe Custom readinessProbe that overrides the default one
##
customReadinessProbe: {}
## @param customStartupProbe Custom startupProbe that overrides the default one
##
customStartupProbe: {}
## @param lifecycleHooks for the ZooKeeper container(s) to automate configuration before or after startup
##
lifecycleHooks: {}
## ZooKeeper resource requests and limits
## ref: https://kubernetes.io/docs/concepts/configuration/manage-compute-resources-container/
## @param resourcesPreset Set container resources according to one common preset (allowed values: none, nano, micro, small, medium, large, xlarge, 2xlarge). This is ignored if resources is set (resources is recommended for production).
## More information: https://github.com/bitnami/charts/blob/main/bitnami/common/templates/_resources.tpl#L15
##
resourcesPreset: "micro"
## @param resources Set container requests and limits for different resources like CPU or memory (essential for production workloads)
## Example:
## resources:
##   requests:
##     cpu: 2
##     memory: 512Mi
##   limits:
##     cpu: 3
##     memory: 1024Mi
##
resources: {}
## Configure Pods Security Context
## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod
## @param podSecurityContext.enabled Enabled ZooKeeper pods' Security Context
## @param podSecurityContext.fsGroupChangePolicy Set filesystem group change policy
## @param podSecurityContext.sysctls Set kernel settings using the sysctl interface
## @param podSecurityContext.supplementalGroups Set filesystem extra groups
## @param podSecurityContext.fsGroup Set ZooKeeper pod's Security Context fsGroup
##
podSecurityContext:
  enabled: true
  fsGroupChangePolicy: Always
  sysctls: []
  supplementalGroups: []
  fsGroup: 1001
## Configure Container Security Context
## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-container
## @param containerSecurityContext.enabled Enabled containers' Security Context
## @param containerSecurityContext.seLinuxOptions [object,nullable] Set SELinux options in container
## @param containerSecurityContext.runAsUser Set containers' Security Context runAsUser
## @param containerSecurityContext.runAsGroup Set containers' Security Context runAsGroup
## @param containerSecurityContext.runAsNonRoot Set container's Security Context runAsNonRoot
## @param containerSecurityContext.privileged Set container's Security Context privileged
## @param containerSecurityContext.readOnlyRootFilesystem Set container's Security Context readOnlyRootFilesystem
## @param containerSecurityContext.allowPrivilegeEscalation Set container's Security Context allowPrivilegeEscalation
## @param containerSecurityContext.capabilities.drop List of capabilities to be dropped
## @param containerSecurityContext.seccompProfile.type Set container's Security Context seccomp profile
##
containerSecurityContext:
  enabled: true
  seLinuxOptions: {}
  runAsUser: 1001
  runAsGroup: 1001
  runAsNonRoot: true
  privileged: false
  readOnlyRootFilesystem: true
  allowPrivilegeEscalation: false
  capabilities:
    drop: ["ALL"]
  seccompProfile:
    type: "RuntimeDefault"
## @param automountServiceAccountToken Mount Service Account token in pod
##
automountServiceAccountToken: false
## @param hostAliases ZooKeeper pods host aliases
## https://kubernetes.io/docs/concepts/services-networking/add-entries-to-pod-etc-hosts-with-host-aliases/
##
hostAliases: []
## @param podLabels Extra labels for ZooKeeper pods
## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/
##
podLabels: {}
## @param podAnnotations Annotations for ZooKeeper pods
## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations/
##
podAnnotations: {}
## @param podAffinityPreset Pod affinity preset. Ignored if `affinity` is set. Allowed values: `soft` or `hard`
## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
##
podAffinityPreset: ""
## @param podAntiAffinityPreset Pod anti-affinity preset. Ignored if `affinity` is set. Allowed values: `soft` or `hard`
## Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
##
podAntiAffinityPreset: soft
## Node affinity preset
## Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#node-affinity
##
nodeAffinityPreset:
  ## @param nodeAffinityPreset.type Node affinity preset type. Ignored if `affinity` is set. Allowed values: `soft` or `hard`
  ##
  type: ""
  ## @param nodeAffinityPreset.key Node label key to match Ignored if `affinity` is set.
  ## E.g.
  ## key: "kubernetes.io/e2e-az-name"
  ##
  key: ""
  ## @param nodeAffinityPreset.values Node label values to match. Ignored if `affinity` is set.
  ## E.g.
  ## values:
  ##   - e2e-az1
  ##   - e2e-az2
  ##
  values: []
## @param affinity Affinity for pod assignment
## Ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity
## Note: podAffinityPreset, podAntiAffinityPreset, and nodeAffinityPreset will be ignored when it's set
##
affinity: {}
## @param nodeSelector Node labels for pod assignment
## Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/
##
nodeSelector: {}
## @param tolerations Tolerations for pod assignment
## Ref: https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/
##
tolerations: []
## @param topologySpreadConstraints Topology Spread Constraints for pod assignment spread across your cluster among failure-domains. Evaluated as a template
## Ref: https://kubernetes.io/docs/concepts/workloads/pods/pod-topology-spread-constraints/#spread-constraints-for-pods
##
topologySpreadConstraints: []
## @param podManagementPolicy StatefulSet controller supports relax its ordering guarantees while preserving its uniqueness and identity guarantees. There are two valid pod management policies: `OrderedReady` and `Parallel`
## ref: https://kubernetes.io/docs/tutorials/stateful-application/basic-stateful-set/#pod-management-policy
##
podManagementPolicy: Parallel
## @param priorityClassName Name of the existing priority class to be used by ZooKeeper pods, priority class needs to be created beforehand
## Ref: https://kubernetes.io/docs/concepts/configuration/pod-priority-preemption/
##
priorityClassName: ""
## @param schedulerName Kubernetes pod scheduler registry
## https://kubernetes.io/docs/tasks/administer-cluster/configure-multiple-schedulers/
##
schedulerName: ""
## @param updateStrategy.type ZooKeeper statefulset strategy type
## @param updateStrategy.rollingUpdate ZooKeeper statefulset rolling update configuration parameters
## ref: https://kubernetes.io/docs/concepts/workloads/controllers/statefulset/#update-strategies
##
updateStrategy:
  type: RollingUpdate
  rollingUpdate: {}
## @param extraVolumes Optionally specify extra list of additional volumes for the ZooKeeper pod(s)
## Example Use Case: mount certificates to enable TLS
## e.g:
## extraVolumes:
## - name: zookeeper-keystore
##   secret:
##     defaultMode: 288
##     secretName: zookeeper-keystore
## - name: zookeeper-truststore
##   secret:
##     defaultMode: 288
##     secretName: zookeeper-truststore
##
extraVolumes: []
## @param extraVolumeMounts Optionally specify extra list of additional volumeMounts for the ZooKeeper container(s)
## Example Use Case: mount certificates to enable TLS
## e.g:
## extraVolumeMounts:
## - name: zookeeper-keystore
##   mountPath: /certs/keystore
##   readOnly: true
## - name: zookeeper-truststore
##   mountPath: /certs/truststore
##   readOnly: true
##
extraVolumeMounts: []
## @param sidecars Add additional sidecar containers to the ZooKeeper pod(s)
## e.g:
## sidecars:
##   - name: your-image-name
##     image: your-image
##     imagePullPolicy: Always
##     ports:
##       - name: portname
##         containerPort: 1234
##
sidecars: []
## @param initContainers Add additional init containers to the ZooKeeper pod(s)
## Example:
## initContainers:
##   - name: your-image-name
##     image: your-image
##     imagePullPolicy: Always
##     ports:
##       - name: portname
##         containerPort: 1234
##
initContainers: []
## ZooKeeper Pod Disruption Budget
## ref: https://kubernetes.io/docs/concepts/workloads/pods/disruptions/
## @param pdb.create Deploy a pdb object for the ZooKeeper pod
## @param pdb.minAvailable Minimum available ZooKeeper replicas
## @param pdb.maxUnavailable Maximum unavailable ZooKeeper replicas. Defaults to `1` if both `pdb.minAvailable` and `pdb.maxUnavailable` are empty.
##
pdb:
  create: true
  minAvailable: ""
  maxUnavailable: ""
## @param enableServiceLinks Whether information about services should be injected into pod's environment variable
## The environment variables injected by service links are not used, but can lead to slow boot times or slow running of the scripts when there are many services in the current namespace.
## If you experience slow pod startups or slow running of the scripts you probably want to set this to `false`.
##
enableServiceLinks: true
## DNS-Pod services
## Ref: https://kubernetes.io/docs/concepts/services-networking/dns-pod-service/
## @param dnsPolicy Specifies the DNS policy for the zookeeper pods
## DNS policies can be set on a per-Pod basis. Currently Kubernetes supports the following Pod-specific DNS policies.
## Available options: Default, ClusterFirst, ClusterFirstWithHostNet, None
## Ref: https://kubernetes.io/docs/concepts/services-networking/dns-pod-service/#pod-s-dns-policy
dnsPolicy: ""
## @param dnsConfig  allows users more control on the DNS settings for a Pod. Required if `dnsPolicy` is set to `None`
## The dnsConfig field is optional and it can work with any dnsPolicy settings.
## Ref: https://kubernetes.io/docs/concepts/services-networking/dns-pod-service/#pod-dns-config
## E.g.
## dnsConfig:
##   nameservers:
##     - ********* # this is an example
##   searches:
##     - ns1.svc.cluster-domain.example
##     - my.dns.search.suffix
##   options:
##     - name: ndots
##       value: "2"
##     - name: edns0
dnsConfig: {}
## @section Traffic Exposure parameters
service:
  ## @param service.type Kubernetes Service type
  ##
  type: ClusterIP
  ## @param service.ports.client ZooKeeper client service port
  ## @param service.ports.tls ZooKeeper TLS service port
  ## @param service.ports.follower ZooKeeper follower service port
  ## @param service.ports.election ZooKeeper election service port
  ##
  ports:
    client: 2181
    tls: 3181
    follower: 2888
    election: 3888
  ## Node ports to expose
  ## NOTE: choose port between <30000-32767>
  ## @param service.nodePorts.client Node port for clients
  ## @param service.nodePorts.tls Node port for TLS
  ##
  nodePorts:
    client: ""
    tls: ""
  ## @param service.disableBaseClientPort Remove client port from service definitions.
  ##
  disableBaseClientPort: false
  ## @param service.sessionAffinity Control where client requests go, to the same pod or round-robin
  ## Values: ClientIP or None
  ## ref: https://kubernetes.io/docs/concepts/services-networking/service/
  ##
  sessionAffinity: None
  ## @param service.sessionAffinityConfig Additional settings for the sessionAffinity
  ## sessionAffinityConfig:
  ##   clientIP:
  ##     timeoutSeconds: 300
  ##
  sessionAffinityConfig: {}
  ## @param service.clusterIP ZooKeeper service Cluster IP
  ## e.g.:
  ## clusterIP: None
  ##
  clusterIP: ""
  ## @param service.loadBalancerIP ZooKeeper service Load Balancer IP
  ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#type-loadbalancer
  ##
  loadBalancerIP: ""
  ## @param service.loadBalancerSourceRanges ZooKeeper service Load Balancer sources
  ## ref: https://kubernetes.io/docs/tasks/access-application-cluster/configure-cloud-provider-firewall/#restrict-access-for-loadbalancer-service
  ## e.g:
  ## loadBalancerSourceRanges:
  ##   - **********/24
  ##
  loadBalancerSourceRanges: []
  ## @param service.externalTrafficPolicy ZooKeeper service external traffic policy
  ## ref https://kubernetes.io/docs/tasks/access-application-cluster/create-external-load-balancer/#preserving-the-client-source-ip
  ##
  externalTrafficPolicy: Cluster
  ## @param service.annotations Additional custom annotations for ZooKeeper service
  ##
  annotations: {}
  ## @param service.extraPorts Extra ports to expose in the ZooKeeper service (normally used with the `sidecar` value)
  ##
  extraPorts: []
  ## @param service.headless.annotations Annotations for the Headless Service
  ## @param service.headless.publishNotReadyAddresses If the ZooKeeper headless service should publish DNS records for not ready pods
  ## @param service.headless.servicenameOverride String to partially override headless service name
  ##
  headless:
    publishNotReadyAddresses: true
    annotations: {}
    servicenameOverride: ""
## Network policies
## Ref: https://kubernetes.io/docs/concepts/services-networking/network-policies/
##
networkPolicy:
  ## @param networkPolicy.enabled Specifies whether a NetworkPolicy should be created
  ##
  enabled: true
  ## @param networkPolicy.allowExternal Don't require client label for connections
  ## When set to false, only pods with the correct client label will have network access to the port Redis&reg; is
  ## listening on. When true, zookeeper accept connections from any source (with the correct destination port).
  ##
  allowExternal: true
  ## @param networkPolicy.allowExternalEgress Allow the pod to access any range of port and all destinations.
  ##
  allowExternalEgress: true
  ## @param networkPolicy.extraIngress [array] Add extra ingress rules to the NetworkPolicy
  ## e.g:
  ## extraIngress:
  ##   - ports:
  ##       - port: 1234
  ##     from:
  ##       - podSelector:
  ##           - matchLabels:
  ##               - role: frontend
  ##       - podSelector:
  ##           - matchExpressions:
  ##               - key: role
  ##                 operator: In
  ##                 values:
  ##                   - frontend
  extraIngress: []
  ## @param networkPolicy.extraEgress [array] Add extra ingress rules to the NetworkPolicy
  ## e.g:
  ## extraEgress:
  ##   - ports:
  ##       - port: 1234
  ##     to:
  ##       - podSelector:
  ##           - matchLabels:
  ##               - role: frontend
  ##       - podSelector:
  ##           - matchExpressions:
  ##               - key: role
  ##                 operator: In
  ##                 values:
  ##                   - frontend
  ##
  extraEgress: []
  ## @param networkPolicy.ingressNSMatchLabels [object] Labels to match to allow traffic from other namespaces
  ## @param networkPolicy.ingressNSPodMatchLabels [object] Pod labels to match to allow traffic from other namespaces
  ##
  ingressNSMatchLabels: {}
  ingressNSPodMatchLabels: {}
## @section Other Parameters

## Service account for ZooKeeper to use.
## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-service-account/
##
serviceAccount:
  ## @param serviceAccount.create Enable creation of ServiceAccount for ZooKeeper pod
  ##
  create: true
  ## @param serviceAccount.name The name of the ServiceAccount to use.
  ## If not set and create is true, a name is generated using the common.names.fullname template
  ##
  name: ""
  ## @param serviceAccount.automountServiceAccountToken Allows auto mount of ServiceAccountToken on the serviceAccount created
  ## Can be set to false if pods using this serviceAccount do not need to use K8s API
  ##
  automountServiceAccountToken: false
  ## @param serviceAccount.annotations Additional custom annotations for the ServiceAccount
  ##
  annotations: {}
## @section Persistence parameters

## Enable persistence using Persistent Volume Claims
## ref: https://kubernetes.io/docs/concepts/storage/persistent-volumes/
##
persistence:
  ## @param persistence.enabled Enable ZooKeeper data persistence using PVC. If false, use emptyDir
  ##
  enabled: true
  ## @param persistence.existingClaim Name of an existing PVC to use (only when deploying a single replica)
  ##
  existingClaim: ""
  ## @param persistence.storageClass PVC Storage Class for ZooKeeper data volume
  ## If defined, storageClassName: <storageClass>
  ## If set to "-", storageClassName: "", which disables dynamic provisioning
  ## If undefined (the default) or set to null, no storageClassName spec is
  ##   set, choosing the default provisioner.  (gp2 on AWS, standard on
  ##   GKE, AWS & OpenStack)
  ##
  storageClass: ""
  ## @param persistence.accessModes PVC Access modes
  ##
  accessModes:
    - ReadWriteOnce
  ## @param persistence.size PVC Storage Request for ZooKeeper data volume
  ##
  size: 8Gi
  ## @param persistence.annotations Annotations for the PVC
  ##
  annotations: {}
  ## @param persistence.labels Labels for the PVC
  ##
  labels: {}
  ## @param persistence.selector Selector to match an existing Persistent Volume for ZooKeeper's data PVC
  ## If set, the PVC can't have a PV dynamically provisioned for it
  ## E.g.
  ## selector:
  ##   matchLabels:
  ##     app: my-app
  ##
  selector: {}
  ## Persistence for a dedicated data log directory
  ##
  dataLogDir:
    ## @param persistence.dataLogDir.size PVC Storage Request for ZooKeeper's dedicated data log directory
    ##
    size: 8Gi
    ## @param persistence.dataLogDir.existingClaim Provide an existing `PersistentVolumeClaim` for ZooKeeper's data log directory
    ## If defined, PVC must be created manually before volume will be bound
    ## The value is evaluated as a template
    ##
    existingClaim: ""
    ## @param persistence.dataLogDir.selector Selector to match an existing Persistent Volume for ZooKeeper's data log PVC
    ## If set, the PVC can't have a PV dynamically provisioned for it
    ## E.g.
    ## selector:
    ##   matchLabels:
    ##     app: my-app
    ##
    selector: {}
## @section Volume Permissions parameters
##

## Init containers parameters:
## volumePermissions: Change the owner and group of the persistent volume(s) mountpoint(s) to 'runAsUser:fsGroup' on each node
##
volumePermissions:
  ## @param volumePermissions.enabled Enable init container that changes the owner and group of the persistent volume
  ##
  enabled: false
  ## @param volumePermissions.image.registry [default: REGISTRY_NAME] Init container volume-permissions image registry
  ## @param volumePermissions.image.repository [default: REPOSITORY_NAME/os-shell] Init container volume-permissions image repository
  ## @skip volumePermissions.image.tag Init container volume-permissions image tag (immutable tags are recommended)
  ## @param volumePermissions.image.digest Init container volume-permissions image digest in the way sha256:aa.... Please note this parameter, if set, will override the tag
  ## @param volumePermissions.image.pullPolicy Init container volume-permissions image pull policy
  ## @param volumePermissions.image.pullSecrets Init container volume-permissions image pull secrets
  ##
  image:
    registry: docker.io
    repository: bitnami/os-shell
    tag: 12-debian-12-r39
    digest: ""
    pullPolicy: IfNotPresent
    ## Optionally specify an array of imagePullSecrets.
    ## Secrets must be manually created in the namespace.
    ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/
    ## Example:
    ## pullSecrets:
    ##   - myRegistryKeySecretName
    ##
    pullSecrets: []
  ## Init container resource requests and limits
  ## ref: https://kubernetes.io/docs/concepts/configuration/manage-compute-resources-container/
  ## @param volumePermissions.resourcesPreset Set container resources according to one common preset (allowed values: none, nano, micro, small, medium, large, xlarge, 2xlarge). This is ignored if volumePermissions.resources is set (volumePermissions.resources is recommended for production).
  ## More information: https://github.com/bitnami/charts/blob/main/bitnami/common/templates/_resources.tpl#L15
  ##
  resourcesPreset: "nano"
  ## @param volumePermissions.resources Set container requests and limits for different resources like CPU or memory (essential for production workloads)
  ## Example:
  ## resources:
  ##   requests:
  ##     cpu: 2
  ##     memory: 512Mi
  ##   limits:
  ##     cpu: 3
  ##     memory: 1024Mi
  ##
  resources: {}
  ## Init container' Security Context
  ## Note: the chown of the data folder is done to containerSecurityContext.runAsUser
  ## and not the below volumePermissions.containerSecurityContext.runAsUser
  ## @param volumePermissions.containerSecurityContext.enabled Enabled init container Security Context
  ## @param volumePermissions.containerSecurityContext.seLinuxOptions [object,nullable] Set SELinux options in container
  ## @param volumePermissions.containerSecurityContext.runAsUser User ID for the init container
  ##
  containerSecurityContext:
    enabled: true
    seLinuxOptions: {}
    runAsUser: 0
## @section Metrics parameters
##

## ZooKeeper Prometheus Exporter configuration
##
metrics:
  ## @param metrics.enabled Enable Prometheus to access ZooKeeper metrics endpoint
  ##
  enabled: false
  ## Service configuration
  ##
  service:
    ## @param metrics.service.type ZooKeeper Prometheus Exporter service type
    ##
    type: ClusterIP
    ## @param metrics.service.port ZooKeeper Prometheus Exporter service port
    ##
    port: 9141
    ## @param metrics.service.annotations [object] Annotations for Prometheus to auto-discover the metrics endpoint
    ##
    annotations:
      prometheus.io/scrape: "true"
      prometheus.io/port: "{{ .Values.metrics.service.port }}"
      prometheus.io/path: "/metrics"
  ## Prometheus Operator ServiceMonitor configuration
  ##
  serviceMonitor:
    ## @param metrics.serviceMonitor.enabled Create ServiceMonitor Resource for scraping metrics using Prometheus Operator
    ##
    enabled: false
    ## @param metrics.serviceMonitor.namespace Namespace for the ServiceMonitor Resource (defaults to the Release Namespace)
    ##
    namespace: ""
    ## @param metrics.serviceMonitor.interval Interval at which metrics should be scraped.
    ## ref: https://github.com/coreos/prometheus-operator/blob/master/Documentation/api.md#endpoint
    ##
    interval: ""
    ## @param metrics.serviceMonitor.scrapeTimeout Timeout after which the scrape is ended
    ## ref: https://github.com/coreos/prometheus-operator/blob/master/Documentation/api.md#endpoint
    ##
    scrapeTimeout: ""
    ## @param metrics.serviceMonitor.additionalLabels Additional labels that can be used so ServiceMonitor will be discovered by Prometheus
    ##
    additionalLabels: {}
    ## @param metrics.serviceMonitor.selector Prometheus instance selector labels
    ## ref: https://github.com/bitnami/charts/tree/main/bitnami/prometheus-operator#prometheus-configuration
    ##
    selector: {}
    ## @param metrics.serviceMonitor.relabelings RelabelConfigs to apply to samples before scraping
    ##
    relabelings: []
    ## @param metrics.serviceMonitor.metricRelabelings MetricRelabelConfigs to apply to samples before ingestion
    ##
    metricRelabelings: []
    ## @param metrics.serviceMonitor.honorLabels Specify honorLabels parameter to add the scrape endpoint
    ##
    honorLabels: false
    ## @param metrics.serviceMonitor.jobLabel The name of the label on the target service to use as the job name in prometheus.
    ##
    jobLabel: ""
    ## @param metrics.serviceMonitor.scheme The explicit scheme for metrics scraping.
    ##
    scheme: ""
    ## @param metrics.serviceMonitor.tlsConfig [object] TLS configuration used for scrape endpoints used by Prometheus
    ## ref: https://github.com/prometheus-operator/prometheus-operator/blob/master/Documentation/api.md#tlsconfig
    ## e.g:
    ## tlsConfig:
    ##   ca:
    ##     secret:
    ##       name: existingSecretName
    ##
    tlsConfig: {}
  ## Prometheus Operator PrometheusRule configuration
  ##
  prometheusRule:
    ## @param metrics.prometheusRule.enabled Create a PrometheusRule for Prometheus Operator
    ##
    enabled: false
    ## @param metrics.prometheusRule.namespace Namespace for the PrometheusRule Resource (defaults to the Release Namespace)
    ##
    namespace: ""
    ## @param metrics.prometheusRule.additionalLabels Additional labels that can be used so PrometheusRule will be discovered by Prometheus
    ##
    additionalLabels: {}
    ## @param metrics.prometheusRule.rules PrometheusRule definitions
    ##  - alert: ZooKeeperSyncedFollowers
    ##    annotations:
    ##      message: The number of synced followers for the leader node in ZooKeeper deployment my-release is less than 2. This usually means that some of the ZooKeeper nodes aren't communicating properly. If it doesn't resolve itself you can try killing the pods (one by one).
    ##    expr: max(synced_followers{service="my-release-metrics"}) < 2
    ##    for: 5m
    ##    labels:
    ##      severity: critical
    ##  - alert: ZooKeeperOutstandingRequests
    ##    annotations:
    ##      message: The number of outstanding requests for ZooKeeper pod {{ $labels.pod }} is greater than 10. This can indicate a performance issue with the Pod or cluster a whole.
    ##    expr: outstanding_requests{service="my-release-metrics"} > 10
    ##    for: 5m
    ##    labels:
    ##      severity: critical
    ##
    rules: []
## @section TLS/SSL parameters
##

## Enable SSL/TLS encryption
##
tls:
  client:
    ## @param tls.client.enabled Enable TLS for client connections
    ##
    enabled: false
    ## @param tls.client.auth SSL Client auth. Can be "none", "want" or "need".
    ##
    auth: "none"
    ## @param tls.client.autoGenerated Generate automatically self-signed TLS certificates for ZooKeeper client communications
    ## Currently only supports PEM certificates
    ##
    autoGenerated: false
    ## @param tls.client.existingSecret Name of the existing secret containing the TLS certificates for ZooKeeper client communications
    ##
    existingSecret: ""
    ## @param tls.client.existingSecretKeystoreKey The secret key from the tls.client.existingSecret containing the Keystore.
    ##
    existingSecretKeystoreKey: ""
    ## @param tls.client.existingSecretTruststoreKey The secret key from the tls.client.existingSecret containing the Truststore.
    ##
    existingSecretTruststoreKey: ""
    ## @param tls.client.keystorePath Location of the KeyStore file used for Client connections
    ##
    keystorePath: /opt/bitnami/zookeeper/config/certs/client/zookeeper.keystore.jks
    ## @param tls.client.truststorePath Location of the TrustStore file used for Client connections
    ##
    truststorePath: /opt/bitnami/zookeeper/config/certs/client/zookeeper.truststore.jks
    ## @param tls.client.passwordsSecretName Existing secret containing Keystore and truststore passwords
    ##
    passwordsSecretName: ""
    ## @param tls.client.passwordsSecretKeystoreKey The secret key from the tls.client.passwordsSecretName containing the password for the Keystore.
    ##
    passwordsSecretKeystoreKey: ""
    ## @param tls.client.passwordsSecretTruststoreKey The secret key from the tls.client.passwordsSecretName containing the password for the Truststore.
    ##
    passwordsSecretTruststoreKey: ""
    ## @param tls.client.keystorePassword Password to access KeyStore if needed
    ##
    keystorePassword: ""
    ## @param tls.client.truststorePassword Password to access TrustStore if needed
    ##
    truststorePassword: ""
  quorum:
    ## @param tls.quorum.enabled Enable TLS for quorum protocol
    ##
    enabled: false
    ## @param tls.quorum.auth SSL Quorum Client auth. Can be "none", "want" or "need".
    ##
    auth: "none"
    ## @param tls.quorum.autoGenerated Create self-signed TLS certificates. Currently only supports PEM certificates.
    ##
    autoGenerated: false
    ## @param tls.quorum.existingSecret Name of the existing secret containing the TLS certificates for ZooKeeper quorum protocol
    ##
    existingSecret: ""
    ## @param tls.quorum.existingSecretKeystoreKey The secret key from the tls.quorum.existingSecret containing the Keystore.
    ##
    existingSecretKeystoreKey: ""
    ## @param tls.quorum.existingSecretTruststoreKey The secret key from the tls.quorum.existingSecret containing the Truststore.
    ##
    existingSecretTruststoreKey: ""
    ## @param tls.quorum.keystorePath Location of the KeyStore file used for Quorum protocol
    ##
    keystorePath: /opt/bitnami/zookeeper/config/certs/quorum/zookeeper.keystore.jks
    ## @param tls.quorum.truststorePath Location of the TrustStore file used for Quorum protocol
    ##
    truststorePath: /opt/bitnami/zookeeper/config/certs/quorum/zookeeper.truststore.jks
    ## @param tls.quorum.passwordsSecretName Existing secret containing Keystore and truststore passwords
    ##
    passwordsSecretName: ""
    ## @param tls.quorum.passwordsSecretKeystoreKey The secret key from the tls.quorum.passwordsSecretName containing the password for the Keystore.
    ##
    passwordsSecretKeystoreKey: ""
    ## @param tls.quorum.passwordsSecretTruststoreKey The secret key from the tls.quorum.passwordsSecretName containing the password for the Truststore.
    ##
    passwordsSecretTruststoreKey: ""
    ## @param tls.quorum.keystorePassword Password to access KeyStore if needed
    ##
    keystorePassword: ""
    ## @param tls.quorum.truststorePassword Password to access TrustStore if needed
    ##
    truststorePassword: ""
  ## Init container resource requests and limits
  ## ref: https://kubernetes.io/docs/concepts/configuration/manage-compute-resources-container/
  ## @param tls.resourcesPreset Set container resources according to one common preset (allowed values: none, nano, micro, small, medium, large, xlarge, 2xlarge). This is ignored if tls.resources is set (tls.resources is recommended for production).
  ## More information: https://github.com/bitnami/charts/blob/main/bitnami/common/templates/_resources.tpl#L15
  ##
  resourcesPreset: "nano"
  ## @param tls.resources Set container requests and limits for different resources like CPU or memory (essential for production workloads)
  ## Example:
  ## resources:
  ##   requests:
  ##     cpu: 2
  ##     memory: 512Mi
  ##   limits:
  ##     cpu: 3
  ##     memory: 1024Mi
  ##
  resources: {}
