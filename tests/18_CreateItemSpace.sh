#!/bin/bash

REQESTID=`uuidgen`
PORT=5021 
ItemSpaceName=myspace
Description=mydescription
REGION=ap-beijing
ZONE=ap-beijing-5
REQ=`cat - <<EOF
{
    "Action": "CreateItemSpace",
    "ApiModule": "oceanus",
    "AppId": **********,
    "ClientIp": "127.0.0.1",
    "Language": "",
    "Region": "${REGION}",
    "RequestId": "${REQESTID}",
    "RequestSource": "API",
    "SubAccountUin": "************",
    "Timestamp": "**********",
    "Uin": "************",
    "Version": "2019-04-22",
    "ItemSpaceName": "${ItemSpaceName}",
     "Region": "${REGION}",
    "Description": "${Description}"
}
EOF
`
curl -H "X-Tc-Requestid:${REQESTID}" -d"$REQ" http://127.0.0.1:${PORT}/interface

