#!/bin/bash

REQESTID=`uuidgen`
PORT=9021 #设置为自行部署的galileo端口
JOB_NAME=TestJob
CLUSTER_ID=cluster-oqgprrz0
REGION=ap-guangzhou
ZONE=ap-guangzhou-3
REQ=`cat - <<EOF
{
    "Action": "CreateMetaTable",
    "ApiModule": "oceanus",
    "AppId": 1257058945,
    "ClientIp": "127.0.0.1",
    "CatalogId": 0,
    "DatabaseId": 0,
    "SqlCode": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "Language": "",
    "ClusterId": "${CLUSTER_ID}",
    "Region": "${REGION}",
    "Remark": "独享集群 TKE 测试作业",
    "RequestId": "${REQESTID}",
    "RequestSource": "API",
    "SubAccountUin": "************",
    "Timestamp": "**********",
    "Token": "",
    "Uin": "************",
    "Version": "2019-04-22",
    "Zone": "${ZONE}"
}
EOF
`
curl -H "X-Tc-Requestid:${REQESTID}" -d"$REQ" http://127.0.0.1:${PORT}/interface
