#!/bin/bash

REQESTID=`uuidgen`
PORT=5021
REGION=ap-beijing
ZONE=ap-beijing-5
JOBID=cql-2k4bknlv # 根据 CreateJob 的结果
REQ=`cat - <<EOF
{
    "Action": "DescribeTreeJobs",
    "ApiModule": "oceanus",
    "AppId": **********,
    "ClientIp": "127.0.0.1",
    "Version": "2019-04-22",
    "Region": "${REGION}",
    "Zone": "${ZONE}",
    "RequestId": "${REQESTID}",
    "RequestSource": "API",
    "SubAccountUin": "************",
    "Timestamp": "**********",
    "ItemSpaceId": 1327,
    "Token": "",
    "Uin": "************"
}
EOF
`
curl -H "X-Tc-Requestid:${REQESTID}" -d"$REQ" http://localhost:${PORT}/interface
