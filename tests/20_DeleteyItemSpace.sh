#!/bin/bash

REQESTID=`uuidgen`
PORT=5021 
ItemSpaceId=1372
MItemSpaceName=myspace
MDescription=mydescription4
REGION=ap-beijing
ZONE=ap-beijing-5
REQ=`cat - <<EOF
{
    "Action": "DeleteItemSpace",
    "ApiModule": "oceanus",
    "AppId": **********,
    "ClientIp": "127.0.0.1",
    "Language": "",
    "Region": "${REGION}",
    "RequestId": "${REQESTID}",
    "RequestSource": "API",
    "SubAccountUin": "************",
    "Timestamp": "**********",
    "Token": "",
    "Uin": "************",
    "Version": "2019-04-22",
    "ItemSpaceId":1327,
    "MItemSpaceName": "${MItemSpaceName}",
    "MDescription": "${MDescription}"
}
EOF
`
curl -H "X-Tc-Requestid:${REQESTID}" -d"$REQ" http://127.0.0.1:${PORT}/interface

