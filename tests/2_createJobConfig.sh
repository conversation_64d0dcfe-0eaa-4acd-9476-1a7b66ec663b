#!/bin/bash

REQESTID=`uuidgen`
PORT=8027 #设置为自行部署的galileo端口
REGION=ap-guangzhou
ZONE=ap-guangzhou-3
JOBID=cql-94uz3tm1 # 根据 CreateJob 的结果
REQ=`cat - <<EOF
{
    "Action": "CreateJobConfig",
    "ApiModule": "oceanus",
    "AppId": **********,
    "ClientIp": "127.0.0.1",
    "Language": "",
    "RequestId": "${REQESTID}",
    "RequestSource": "API",
    "SubAccountUin": "************",
    "Timestamp": "**********",
    "Token": "",
    "Uin": "************",
    "Version": "2019-04-22",
    "Region": "${REGION}",
    "Zone": "${ZONE}",
    "JobId": "${JOBID}",
    "ProgramArgs": "{\"CheckpointInterval\":600, \"SqlCode\":\"************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\"}",
    "Remark": "测试 SQL 作业",
    "ResourceRefs": [{"Type":0,"Version":1,"ResourceId":"resource-jdbc"},{"Type":0,"Version":1,"ResourceId":"resource-kafka"}]
}
EOF
`
curl -H "X-Tc-Requestid:${REQESTID}" -d"$REQ" http://9.71.144.243:${PORT}/interface
