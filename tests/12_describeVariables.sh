#!/bin/bash

REQESTID=`uuidgen`
PORT=9021 #设置为自行部署的galileo端口
JOB_NAME=TestJob
CLUSTER_ID=cluster-oqgprrz0
REGION=ap-guangzhou
ZONE=ap-guangzhou-3
REQ=`cat - <<EOF
{
    "Action": "DescribeMetaReferences",
    "ApiModule": "oceanus",
    "AppId": **********,
    "ClientIp": "127.0.0.1",
    "SqlCode": "IElOU0VSVCBJTlRPIF9kYy5fZGIuYE15UmVzdWx0MTBgICBTRUxFQ1QgQ09VTlQob3JkZXJJZCkgYXMgX2NvdW50LCAxMDAgLCBUVU1CTEVfU1RBUlQocm93dGltZSwgSU5URVJWQUwgJzUnIHNlY29uZCkgYXMgdF9zdGFydCwgVFVNQkxFX0VORChyb3d0aW1lLCBJTlRFUlZBTCAnNScgc2Vjb25kKSBhcyB0X2VuZCBGUk9NIF9kYy5fZGIuYE9yZGVyVGFibGVgIEdST1VQIEJZICBUVU1CTEUocm93dGltZSwgSU5URVJWQUwgJzUnIHNlY29uZCkg",
    "Language": "",
    "ClusterId": "${CLUSTER_ID}",
    "Region": "${REGION}",
    "Remark": "独享集群 TKE 测试作业",
    "RequestId": "${REQESTID}",
    "RequestSource": "API",
    "SubAccountUin": "************",
    "Timestamp": "**********",
    "Token": "",
    "Uin": "************",
    "Version": "2019-04-22",
    "Zone": "${ZONE}",
    "ResourceRefs":[
     {
            "ResourceId":"resource-9da5a6d9",
            "Version":2,
            "Type":0
        }
     ]
}
EOF
`
curl -H "X-Tc-Requestid:${REQESTID}" -d"$REQ" http://127.0.0.1:${PORT}/interface
