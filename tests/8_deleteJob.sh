#!/bin/bash

REQESTID=`uuidgen`
PORT=8027 #设置为自行部署的galileo端口
REGION=ap-guangzhou
ZONE=ap-guangzhou-3
JOBID=cql-94uz3tm1 # 根据 CreateJob 的结果
REQ=`cat - <<EOF
{
    "Action": "DeleteJobs",
    "ApiModule": "oceanus",
    "AppId": **********,
    "ClientIp": "127.0.0.1",
    "Version": "2019-04-22",
    "Region": "${REGION}",
    "Zone": "${ZONE}",
    "RequestId": "${REQESTID}",
    "RequestSource": "API",
    "SubAccountUin": "************",
    "Timestamp": "**********",
    "Token": "",
    "Uin": "************",
    "JobIds": [
        "${JOBID}"
    ]
}
EOF
`
curl -H "X-Tc-Requestid:${REQESTID}" -d"$REQ" http://************:${PORT}/interface
