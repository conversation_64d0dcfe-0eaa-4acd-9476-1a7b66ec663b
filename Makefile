.PHONY: all clean docker

export LC_ALL := en_US.UTF-8

TEST_DIR := src/tstream_cc/service/log
TEST_DIR += src/common/flow src/tstream_cc/service/cdb
TEST_DIR += src/tstream_cc/service/cls
TEST_DIR += src/tstream_cc/service/cvm
TEST_DIR += src/tstream_cc/service/vpc
TEST_DIR += src/tstream_cc/service/emr
TEST_DIR += src/tstream_cc/service/tke
TEST_DIR += src/tstream_cc/service/white_list
TEST_DIR += src/tstream_cc/service/cluster
TEST_DIR += src/tstream_cc/service/table
TEST_DIR += src/tstream_cc/service/helm
TEST_DIR += src/tstream_cc/service/k8s
TEST_DIR += src/tstream_cc/service/config
TEST_DIR += src/tstream_cc/service/password
TEST_DIR += src/tstream_cc/service/cbs
TEST_DIR += src/tstream_cc/taskhandler
TEST_DIR += src/component
TEST_DIR += src/tstream_cc/service/timer
TEST_DIR += src/common/util
TEST_DIR += src/tstream_cc/service/resource_auth
TEST_DIR += src/tstream_cc/service/cos
TEST_DIR += src/tstream_cc/service/job_config
TEST_DIR += src/tstream_cc/service/config
TEST_DIR += src/tstream_cc/service/config/configure_center
TEST_DIR += src/tstream_cc/service/coredns
TEST_DIR += src/tstream_cc/service/metadata

TEST_DIR := $(dir $(shell find ${TEST_DIR} -name '*_test.go'))
TEST_DIR := $(patsubst %/,%,$(shell echo ${TEST_DIR} | tr -s ' ' '\n' | sort | uniq | tr -s "\n" " "))

target_test = $(addprefix bin/,$(addsuffix _test,$(notdir $(1))))

CREDENTIALS_PROVIDER_IMAGE = ccr.ccs.tencentyun.com/oceanus/credentials_provider:v0.1.5

TARGET := ./bin/galileo_cc
TARGET_TEST := $(call target_test,${TEST_DIR})

GOENV := CGO_ENABLED=0 GOOS=linux GOARCH=amd64
BUILD_FLAGS := -v
DEBUG_BUILD_FLAGS := -gcflags='all=-N -l' -v

TEST_FLAGS := -v -c

.PHONY: ${TARGET} ${TEST_DIR} clean debug

all: ${TARGET}
	#./bin/log_test -test.v -test.run TestStsAssumeRole

test: ${TEST_DIR}

${TARGET}: %:
	${GOENV} go build ${BUILD_FLAGS} -o ${TARGET} ./src/main/galileo_cc

${TEST_DIR}: %:
	${GOENV} go test ${TEST_FLAGS} -o $(call target_test,$@) $(addprefix ./,$@)

docker-build-release:
	go mod vendor
	docker build --network=host -t ${CREDENTIALS_PROVIDER_IMAGE} -f Dockerfile.credentials_provider .

eni_upgrade:
	${GOENV} go build ${BUILD_FLAGS} -o bin/eni_upgrade ./src/main/tools/eni_upgrade

clean:
	${RM} ${TARGET} ${TARGET_TEST}

debug: ${TEST_DIR}
	${GOENV} go build ${DEBUG_BUILD_FLAGS} -o ${TARGET} ./src/main/galileo_cc
