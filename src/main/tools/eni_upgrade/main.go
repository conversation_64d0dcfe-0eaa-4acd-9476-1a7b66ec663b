package main

import (
	"encoding/json"
	"fmt"
	"github.com/spf13/pflag"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/dlocker"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/tke"
	taskHandler "tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler"
	"time"
)

var dbUrl string
var txManager *dao.DataSourceTransactionManager

func init() {
	pflag.StringVar(&dbUrl, "db.url", "", "DB URL")
}

func initDB() (err error) {
	txManager, err = dao.NewDataSourceTransactionManager(dbUrl)
	if err != nil {
		return err
	}
	service.SetTxManager(txManager)
	taskHandler.SetTxManager(txManager)
	dlocker.SetTxManager(txManager)
	return nil
}

func getRunningClusters() (clusters []string, err error) {
	clusters = make([]string, 0)
	// 不考虑 初始化中， 和扩容中，应该不会有这种场景
	clusterGroup, err := service2.ListClusterGroups(&service2.ListClusterGroupsParam{
		AppId:        0,
		Regions:      nil,
		Zone:         "",
		ClusterType:  []int8{constants.CLUSTER_GROUP_TYPE_PRIVATE},
		ClusterNames: nil,
		IsVagueNames: false,
		SerialIds:    nil,
		Offset:       0,
		Limit:        0,
		OrderType:    0,
		StatusList:   []int{constants.CLUSTER_GROUP_STATUS_RUNNING},
	})
	if err != nil {
		return
	}
	for _, c := range clusterGroup {
		clusters = append(clusters, c.SerialId)
	}
	return
}

func needUpgrade(s *service3.ClusterGroupService) bool {
	cluster, _ := s.GetActiveCluster()
	if cluster.SchedulerType != constants.CLUSTER_SCHEDULER_TYPE_TKE {
		return false
	}
	// 内网VPC，无需初始化eni
	if s.GetClusterGroup().NetEnvironmentType == constants.NETWORK_ENV_INNER_VPC {
		return false
	}

	if len(cluster.ClusterConfig) == 0 {
		return false
	}

	clusterConfig := make(map[string]string)
	err := json.Unmarshal([]byte(cluster.ClusterConfig), &clusterConfig)
	if err != nil {
		return false
	}

	for k, v := range clusterConfig {
		// 采用了eni的， 肯定会包含 kubernetes.jobmanager.annotations
		// 旧eni 方案， anno 包含了 qcloud.CNI_CONFIG_INFO 这个字段
		if k == "kubernetes.jobmanager.annotations" && strings.Contains(v, "qcloud.CNI_CONFIG_INFO") {
			return true
		}
	}
	return false
}

func initEni(s *service3.ClusterGroupService) (err error) {
	cluster, _ := s.GetActiveCluster()
	req := &flow.TaskExecRequest{
		Retrycount: 0,
		Processkey: "",
		Taskcode:   "",
		DocId:      "",
		FlowId:     "",
		TaskId:     "",
		Params: map[string]string{
			constants.FLOW_PARAM_REQUEST_ID:       s.GetClusterGroup().SerialId,
			constants.FLOW_PARAM_CLUSTER_GROUP_ID: fmt.Sprintf("%d", s.GetClusterGroup().Id),
			constants.FLOW_PARAM_CLUSTER_ID:       fmt.Sprintf("%d", cluster.Id),
		},
	}

	handler := taskHandler.NewCreateClusterBaseHandler(&taskHandler.InitENIHandler{})
	rsp := handler.CompleteTask(req)
	b, _ := json.MarshalIndent(rsp, "", "")
	fmt.Printf("first init: %s\n", string(b))
	if rsp.RetCode != flow.TASK_IN_PROCESS {
		return fmt.Errorf("rsp code %d", rsp.RetCode)
	}

	for {
		time.Sleep(time.Second)
		req.Retrycount++
		req.Params = rsp.Params

		rsp = handler.CompleteTask(req)

		b, _ = json.MarshalIndent(rsp, "", "")
		fmt.Printf("running: %s", string(b))
		if rsp.RetCode == flow.TASK_SUCCESS {
			break
		}
	}
	return
}

func updateClusterConfig(s *service3.ClusterGroupService) (err error) {
	cluster, _ := s.GetActiveCluster()
	clusterConfig := make(map[string]string, 0)
	if len(cluster.ClusterConfig) > 0 {
		if err := json.Unmarshal([]byte(cluster.ClusterConfig), &clusterConfig); err != nil {
			return errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
	}

	tkeService := tke.GetTkeService()
	eniAnno := tkeService.GenerateTkeEniAnnotation()

	// see flink KubernetesConfigOptions.JOB_MANAGER_ANNOTATIONS
	// The user-specified annotations that are set to the JobManager pod. The value could be " +
	//			"in the form of a1:v1,a2:v2"
	anno := make([]string, 0)
	for k, v := range eniAnno {
		anno = append(anno, fmt.Sprintf("'%s:%s'", k, v))
	}
	annotations := strings.Join(anno, ",")

	jobManagerAnnotations := "kubernetes.jobmanager.annotations"
	taskManagerAnnotations := "kubernetes.taskmanager.annotations"
	clusterConfig[jobManagerAnnotations] = annotations
	clusterConfig[taskManagerAnnotations] = annotations

	strConf, err := json.Marshal(clusterConfig)
	if err != nil {
		return errorcode.InternalErrorCode.NewWithErr(err)
	}

	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.ExecuteSqlWithArgs("update Cluster set ClusterConfig=? where Id=?", string(strConf), cluster.Id)
		return nil
	}).Close()
	return nil
}

func upgradeCluster(s *service3.ClusterGroupService) (err error) {
	if err = initEni(s); err != nil {
		return err
	}

	if err = updateClusterConfig(s); err != nil {
		return err
	}
	return
}

func processCluster(clusterName string, noNeedUpgrade, upgrade, upgradeFailed map[string]struct{}) {
	fmt.Printf("process cluster %s ....\n", clusterName)
	defer fmt.Printf("process cluster %s done\n", clusterName)
	s, err := service3.NewClusterGroupServiceBySerialId(clusterName)
	if err != nil {
		fmt.Printf("%s %v\n", clusterName, err)
		upgradeFailed[clusterName] = struct{}{}
		return
	}
	_, err = s.GetActiveCluster()
	if err != nil {
		fmt.Printf("%s %v\n", clusterName, err)
		upgradeFailed[clusterName] = struct{}{}
		return
	}
	if !needUpgrade(s) {
		noNeedUpgrade[clusterName] = struct{}{}
		return
	}

	err = upgradeCluster(s)
	if err != nil {
		upgradeFailed[clusterName] = struct{}{}
	} else {
		upgrade[clusterName] = struct{}{}
	}
}

func process() {
	if err := initDB(); err != nil {
		fmt.Println("create db err", err)
		return
	}

	clusters, err := getRunningClusters()
	if err != nil {
		fmt.Println(err)
		return
	}

	noNeedUpgrade := make(map[string]struct{})
	upgrade := make(map[string]struct{})
	upgradeFailed := make(map[string]struct{})

	for _, c := range clusters {
		processCluster(c, noNeedUpgrade, upgrade, upgradeFailed)
	}

	fmt.Println("cluster upgrade success")
	for clusterName, _ := range upgrade {
		fmt.Println(clusterName)
	}

	if len(upgradeFailed) > 0 {
		fmt.Println("cluster upgrade failed")
		for clusterName, _ := range upgradeFailed {
			fmt.Println(clusterName)
		}
	}
}

func main() {
	pflag.Parse()
	process()
}
