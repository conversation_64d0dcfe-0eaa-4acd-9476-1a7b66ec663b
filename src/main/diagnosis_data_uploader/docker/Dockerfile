FROM debian:stable-slim

ENV TZ=Asia/Shanghai \
    DEBIAN_FRONTEND=noninteractive \
    GOLANG_PROTOBUF_REGISTRATION_CONFLICT=warn

RUN sed -i 's/deb.debian.org/mirrors.tencentyun.com/g' /etc/apt/sources.list.d/debian.sources
RUN sed -i 's/security.debian.org/mirrors.tencentyun.com/g' /etc/apt/sources.list.d/debian.sources

RUN apt update
RUN apt-get -qq install procps
RUN apt-get -qq install htop

ADD diagnosis_data_uploader /diagnosis_data_uploader