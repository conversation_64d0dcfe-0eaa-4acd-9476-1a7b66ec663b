package main

import (
	"net/http"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/main/diagnosis_data_uploader/log"
	"tencentcloud.com/tstream_galileo/src/main/diagnosis_data_uploader/service/comserv"
	"tencentcloud.com/tstream_galileo/src/main/diagnosis_data_uploader/service/logup"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
)

func init() {
	comserv.EnvInit()
	// 启动日志文件监控
	go logup.Watch(comserv.GetWatchFolder())

	// 启动日志文件周期上传
	(&logup.LogUploadTimer{}).Start(1)
}

func main() {

	defer log.FlushLogger()

	// 启动一个空的服务，让 Flink 作业检测是否当前开启了诊断采集
	log.Infof("Starting to listen on port %d", constants.ComponentDiagnosisDataUploaderPort)
	server := &http.Server{}
	server.Addr = ":" + strconv.Itoa(constants.ComponentDiagnosisDataUploaderPort)
	server.Handler = &comserv.DiagnosisHttpServer{}
	err := server.ListenAndServe()
	if err != nil {
		log.Errorf("Failed to start HTTP server because %+v", err)
	}

}
