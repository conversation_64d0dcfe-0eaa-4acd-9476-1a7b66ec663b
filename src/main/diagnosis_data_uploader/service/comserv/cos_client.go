package comserv

import (
	"fmt"
	"github.com/tencentyun/cos-go-sdk-v5"
	"net/http"
	"net/url"
	"tencentcloud.com/tstream_galileo/src/main/diagnosis_data_uploader/log"
)

func NewCosClient(region string, bucketName string, secretId string, secretKey string, token string) *cos.Client {
	if region == "" {
		panic("Region field of COSClient is empty")
	}
	serviceUrl, err := url.Parse(fmt.Sprintf("http://cos.%s.myqcloud.com", region))
	if err != nil {
		log.Errorf("Failed to parse URL: %+v", err)
		panic(err)
	}

	newBucketURL, err := cos.NewBucketURL(bucketName, region, false)
	if err != nil {
		log.Errorf("Failed to check savepoint! NewBucketURL err:%v", err)
		panic(err)
	}

	return cos.NewClient(
		&cos.BaseURL{
			BucketURL:  newBucketURL,
			ServiceURL: serviceUrl,
		},
		&http.Client{
			Transport: &cos.AuthorizationTransport{
				SecretID:     secretId,
				SecretKey:    secretKey,
				SessionToken: token,
			},
		})
}
