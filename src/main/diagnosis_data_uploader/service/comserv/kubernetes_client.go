package comserv

import (
	"context"
	"path/filepath"
	"strings"

	v12 "k8s.io/api/apps/v1"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"tencentcloud.com/tstream_galileo/src/main/diagnosis_data_uploader/log"
	//
	// Uncomment to load all auth plugins
	// _ "k8s.io/client-go/plugin/pkg/client/auth"
	//
	// Or uncomment to load specific auth plugins
	// _ "k8s.io/client-go/plugin/pkg/client/auth/azure"
	// _ "k8s.io/client-go/plugin/pkg/client/auth/gcp"
	// _ "k8s.io/client-go/plugin/pkg/client/auth/oidc"
	// _ "k8s.io/client-go/plugin/pkg/client/auth/openstack"
	//
)

func NewClientset() *kubernetes.Clientset {
	// creates the in-cluster config
	config, err := rest.InClusterConfig()
	if err != nil {
		panic(err.Error())
	}
	// creates the clientset
	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		panic(err.Error())
	}
	return clientset
}

func GetPod(clientset *kubernetes.Clientset, podName string) (*v1.Pod, error) {
	return clientset.CoreV1().Pods("default").Get(context.TODO(), podName, metav1.GetOptions{})
}

func ListPod(clientset *kubernetes.Clientset) (*v1.PodList, error) {
	return clientset.CoreV1().Pods("default").List(context.TODO(), metav1.ListOptions{
		ResourceVersion: "0",
	})
}

func GetDeployment(clientset *kubernetes.Clientset, deploymentName string) (*v12.Deployment, error) {
	return clientset.AppsV1().Deployments("default").Get(context.TODO(), deploymentName, metav1.GetOptions{})
}

func ResourceNotExists(filePath string) bool {
	dir, _ := filepath.Split(filePath)
	_, lastDir := filepath.Split(filepath.Dir(dir))
	// 去掉目录名的 oceanus-prefix 前缀, 剩下 cql-0003ua2n-27-jobmanager-1624948606722$31 或 cql-0003ua2n-27-taskmanager-1-1$31
	directoryNameWithoutPrefix := lastDir[len(DiagnosisFolderPrefix):]
	if !strings.HasPrefix(directoryNameWithoutPrefix, DiagnosisTarballFilePrefix) {
		log.Errorf("Mal-formed directoryNameWithoutPrefix: %s, skip", directoryNameWithoutPrefix)
		return false
	}
	// 对于 JobManager, resource 是 Deployment; 对于 TaskManager, resource 是 Pod
	var resourceName string

	// 调用 TKE 接口搜索 Deployment / Pod 是否存在
	var err error
	if strings.Contains(directoryNameWithoutPrefix, JobManager) {
		parts := strings.Split(directoryNameWithoutPrefix, "-"+JobManager+"-")
		if len(parts) != 2 {
			log.Errorf("Failed to parse diagnosis folder %s, skip", lastDir)
			return false
		}
		resourceName = parts[0] // 对于 JobManager, Deployment 名字是 cql-0003ua2n-27 部分
		log.Infof("Try to check whether deployment for JM exists: %s", resourceName)
		_, err = GetDeployment(GetClientSet(), resourceName)
	} else if strings.Contains(directoryNameWithoutPrefix, TaskManager) {
		parts := strings.Split(directoryNameWithoutPrefix, "$")
		resourceName = parts[0] // 对于 TaskManager，Pod 名字是 cql-0003ua2n-27-taskmanager-1-1 部分
		log.Infof("Try to check whether pod for TM exists: %s", resourceName)
		_, err = GetPod(GetClientSet(), resourceName)
	}
	if err != nil {
		if errors.IsNotFound(err) {
			log.Infof("Kubernetes resource `%s` has terminated and should be collected", resourceName)
			return true
		} else {
			log.Errorf("Unknown error happened during TKE call for %s, skip: %+v", resourceName, err)
		}
	}
	log.Infof("Kubernetes resource %s is still running, skip", resourceName)
	return false
}
