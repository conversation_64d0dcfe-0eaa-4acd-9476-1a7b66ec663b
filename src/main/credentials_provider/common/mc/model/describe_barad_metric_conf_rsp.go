package model

import "fmt"

type DescribeBaradMetricConfRsp struct {
	Response   *DescribeBaradMetricConfData `json:"data"`
	ReturnCode int64                        `json:"returnCode"`
	ReturnMsg  string                       `json:"returnMsg"`
	Timestamp  int64                        `json:"timestamp"`
}

type DescribeBaradMetricConfData struct {
	RequestId       string                `json:"RequestId"`
	MetricWhiteList []string              `json:"MetricWhiteList"`
	MetricRegex     []*BaradMetricRegexKV `json:"MetricRegex"`
	ExpiredTime     int64
}

func (d *DescribeBaradMetricConfData) String() string {
	return fmt.Sprintf("RequestId: %s, MetricWhiteList: %v, MetricRegex: %v,ExpiredTime: %d",
		d.RequestId, d.Met<PERSON>eList, d.<PERSON>ric<PERSON>x, d.ExpiredTime)
}

type BaradMetricRegexKV struct {
	RegexName  string `json:"RegexName"`
	RegexValue string `json:"RegexValue"`
}

func (b *BaradMetricRegexKV) String() string {
	return fmt.Sprintf("RegexName: %s,RegexValue: %s ;", b.RegexName, b.RegexValue)
}
