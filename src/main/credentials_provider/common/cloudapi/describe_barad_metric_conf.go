package cloudapi

type DescribeBaradMetricConfRsp struct {
	Response *OceanusResponse `json:"Response"`
}

type OceanusResponse struct {
	Error           *OceanusError         `json:"Error"`
	RequestId       string                `json:"RequestId"`
	MetricWhiteList []string              `json:"MetricWhiteList"`
	MetricRegex     []*BaradMetricRegexKV `json:"MetricRegex"`
}

type BaradMetricRegexKV struct {
	RegexName  string `json:"RegexName"`
	RegexValue string `json:"RegexValue"`
}

type OceanusError struct {
	Code    string `json:"Code"`
	Message string `json:"Message"`
}
