package env

import (
	"github.com/spf13/pflag"
	"os"
)

var (
	oceanusAuthHost string
	listenPort      string
	galileoUrl      string
	nginxUserName   string
	nginxPassword   string
	region          string
	clusterId       string
	clusterVersion  string
)

var ClusterId string
var ClsHost string
var PodName string
var PodIP string
var NodeName string

const (
	DefaultOceanusAuthHost = "oceanus.internal.tencentcloudapi.com"
	DefaultListenPort      = "2021"
)

func Init() {
	CheckEnv()
	pflag.StringVar(&oceanusAuthHost, "oceanus-auth-host", DefaultOceanusAuthHost, "")
	pflag.StringVar(&listenPort, "listen-port", DefaultListenPort, "")
	galileoUrl = os.Getenv("GALILEO_URL")
	nginxUserName = os.Getenv("NGINX_USERNAME")
	nginxPassword = os.Getenv("NGINX_PASSWORD")
	region = os.Getenv("REGION")
	clusterVersion = os.Getenv("ARCH_GENERATION")
	if IsEks() {
		clusterId = os.Getenv("EKS_CLUSTER_ID")
	} else {
		clusterId = os.Getenv("TKE_CLUSTER_ID")
	}
	pflag.Parse()
}

func GetListenPort() string {
	return listenPort
}

func GetOceanusAuthHost() string {
	return oceanusAuthHost
}

func GetGalileoUrl() string {
	return galileoUrl
}

func GetNginxUserName() string {
	return nginxUserName
}

func GetNginxUserPassword() string {
	return nginxPassword
}

func GetRegion() string {
	return region
}

func IsEks() bool {
	ClusterId := os.Getenv("EKS_CLUSTER_ID")
	if ClusterId == "" {
		return false
	}
	return true
}

func GetClusterId() string {
	return clusterId
}

func GetClusterVersion() string {
	return clusterVersion
}

func CheckEnv() {
	ClusterId = os.Getenv("TKE_CLUSTER_ID")
	if ClusterId == "" {
		panic("TKE_CLUSTER_ID not set")
	}
	ClsHost = os.Getenv("CLS_HOST")
	if ClsHost == "" {
		panic("CLS_HOST not set")
	}
	PodName = os.Getenv("POD_NAME")
	PodIP = os.Getenv("POD_IP")
	if PodIP == "" {
		panic("POD_IP not set")
	}
	NodeName = os.Getenv("NODE_NAME")
	if NodeName == "" {
		panic("NODE_NAME not set")
	}
}
