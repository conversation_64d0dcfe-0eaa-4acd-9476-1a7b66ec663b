package access_key

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha1"
	"crypto/tls"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"io/ioutil"
	"math/rand"
	"net/http"
	"os"
	"sort"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/main/credentials_provider/common/log"
	"tencentcloud.com/tstream_galileo/src/main/credentials_provider/credential"
	"time"
)

type EksService struct {
	domain string
}

/*
*

	{
	  "TmpSecretId": "xxx",
	  "TmpSecretKey": "xxxxx",
	  "ExpiredTime": **********,
	  "Expiration": "2022-03-18T19:11:49Z",
	  "Token": "xxxxx",
	  "Code": "Success"
	}
*/
type eksTmpCredentialResponse struct {
	TmpSecretId  string `json:"tmpSecretId"`
	TmpSecretKey string `json:"tmpSecretKey"`
	Token        string `json:"Token"`
	Code         string `json:"Code"`
	ExpiredTime  int64  `json:"expiredTime"`
	Expiration   string `json:"expiration"`
}

/*
*
credentials-provider 在eks 下面 获取 集群密钥需要通过设置anno，
[eks.tke.cloud.tencent.com/role-name]=$RoleName，
就可以在pod中通过 curl http://metadata.tencentyun.com/meta-data/cam/security-credentials/$RoleName来获取普通角色的临时秘钥，
这个RoleName的 角色载体 需要是  cvm ,目前使用 Roc_role
*/
func GetRoleName() (string, error) {
	log.Info("开始 GetRoleName")
	req, _ := http.NewRequest("GET", "http://metadata.tencentyun.com/meta-data/cam/security-credentials", nil)
	client := http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		msg := fmt.Sprintf("根据 http://metadata.tencentyun.com/meta-data/cam/security-credentials 获取 角色失败，错误是 %v", err)
		log.Error(msg)
		return "", fmt.Errorf(msg)
	}
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("%d %s", resp.StatusCode, resp.Status)
	}
	data, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}
	log.Info("Rolename is ", zap.String("Rolename", string(data)))
	return string(data), nil
}

func GetEksTmpCredential() (*credential.TmpCredential, error) {
	log.Info("开始 GetEksTmpCredential")
	roleName, err := GetRoleName()
	if err != nil {
		return nil, err
	}
	req, _ := http.NewRequest("GET", "http://metadata.tencentyun.com/meta-data/cam/security-credentials/"+roleName, nil)
	log.Info("url is http://metadata.tencentyun.com/meta-data/cam/security-credentials/", zap.String("roleName", roleName))
	client := http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("%d %s", resp.StatusCode, resp.Status)
	}
	data, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	r := eksTmpCredentialResponse{}
	err = json.Unmarshal(data, &r)
	if err != nil {
		msg := fmt.Sprintf("根据 http://metadata.tencentyun.com/meta-data/cam/security-credentials/%s 获取 临时密钥失败， 返回数据是 %s, 错误是 %v", roleName, string(data), err)
		log.Error(msg)
		return nil, fmt.Errorf(msg)
	}
	if r.Code != "Success" {
		return nil, fmt.Errorf("GetEksTmpCredential 失败")
	}

	return &credential.TmpCredential{
		SecretId:    r.TmpSecretId,
		SecretKey:   r.TmpSecretKey,
		Token:       r.Token,
		ExpiredTime: r.ExpiredTime,
	}, nil
}

func (e *EksService) Get() (*credential.TmpCredential, error) {
	log.Info("开始获取eks 密钥")
	tc, err := GetEksTmpCredential()
	if err != nil {
		return nil, err
	}

	region := os.Getenv("REGION")
	if region == "" {
		region = "ap-guangzhou"
	}

	ClusterId := os.Getenv("EKS_CLUSTER_ID")
	if ClusterId == "" {
		panic("EKS_CLUSTER_ID not set")
	}
	log.Info("EKS_CLUSTER_ID is", zap.String("EKS_CLUSTER_ID", ClusterId))

	params := map[string]string{}
	params["ClusterId"] = ClusterId
	params["Version"] = "2019-04-22"
	params["Nonce"] = strconv.Itoa(rand.Int())
	params["Timestamp"] = fmt.Sprintf("%d", time.Now().Unix())
	params["Action"] = "DescribeClsAuthInfo"
	params["SecretId"] = tc.SecretId
	params["Region"] = region
	params["Token"] = tc.Token
	params["Signature"] = sign(tc.SecretKey, e.domain, params)

	req, _ := http.NewRequest("GET", "https://"+e.domain, nil)

	q := req.URL.Query()
	for k, v := range params {
		q.Add(k, v)
	}
	req.URL.RawQuery = q.Encode()

	client := http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
	}

	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("%s", resp.Status)
	}
	data, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	rb := oceanusResponseBody{}
	err = json.Unmarshal(data, &rb)
	if err != nil {
		return nil, err
	}
	if rb.Response == nil {
		return nil, fmt.Errorf("empty response")
	}
	if rb.Response.Error != nil {
		return nil, fmt.Errorf("%s: %s", rb.Response.Error.Code, rb.Response.Error.Message)
	}

	return &credential.TmpCredential{
		SecretId:    rb.Response.SecretId,
		SecretKey:   rb.Response.SecretKey,
		Token:       rb.Response.Token,
		ExpiredTime: rb.Response.ExpiredTime,
	}, nil
}

type oceanusResponseBody struct {
	Response *oceanusResponse `json:"Response"`
}

type oceanusResponse struct {
	Error       *oceanusError `json:"Error"`
	SecretId    string        `json:"SecretId"`
	SecretKey   string        `json:"SecretKey"`
	Token       string        `json:"Token"`
	ExpiredTime int64         `json:"ExpiredTime"`
}

type oceanusError struct {
	Code    string `json:"Code"`
	Message string `json:"Message"`
}

func sign(secretKey, domain string, params map[string]string) string {
	keys := make([]string, len(params))
	i := 0
	for k, _ := range params {
		keys[i] = k
		i++
	}
	sort.Strings(keys)
	var buf bytes.Buffer
	buf.WriteString("GET")
	buf.WriteString(domain)
	buf.WriteString("?")
	for i, k := range keys {
		buf.WriteString(k)
		buf.WriteString("=")
		buf.WriteString(params[k])
		if i < len(keys)-1 {
			buf.WriteString("&")
		}
	}
	mac := hmac.New(sha1.New, []byte(secretKey))
	mac.Write(buf.Bytes())
	signByte := mac.Sum(nil)
	signature := base64.StdEncoding.EncodeToString(signByte)
	return signature
}
