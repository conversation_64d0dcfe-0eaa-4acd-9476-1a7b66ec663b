package access_key

import (
	"go.uber.org/zap"
	"os"
	"strconv"
	"sync"
	"tencentcloud.com/tstream_galileo/src/main/credentials_provider/common/env"
	"tencentcloud.com/tstream_galileo/src/main/credentials_provider/common/log"
	"tencentcloud.com/tstream_galileo/src/main/credentials_provider/common/mc/model"
	"tencentcloud.com/tstream_galileo/src/main/credentials_provider/credential"
	"tencentcloud.com/tstream_galileo/src/main/credentials_provider/util"
	"time"
)

type AccessKeyService struct {
	stopCh <-chan struct{}
	src    credential.Source

	prevErr error
	cred    *credential.TmpCredential
	eks     *EksService
	zkCred  *model.ReceiveZkCredentialParam
}

var accessKeyService *AccessKeyService
var once sync.Once

func GetInstance() *AccessKeyService {
	once.Do(func() {
		accessKeyService = newAccessKeyService(env.GetOceanusAuthHost(), util.SetupSignalHandler())
	})
	return accessKeyService
}

func newAccessKeyService(host string, stopCh <-chan struct{}) *AccessKeyService {
	s := &AccessKeyService{
		stopCh: stopCh,
		src:    credential.NewOceanusSource(host),
		eks: &EksService{
			domain: host + "/",
		},
	}
	clusterVersion := env.GetClusterVersion()
	if &clusterVersion != nil && len(clusterVersion) > 0 {
		version, _ := strconv.ParseInt(env.GetClusterVersion(), 10, 64)
		log.Infof("current cluster version %d", version)
		if version >= 3 {
			return s
		}
	}
	go s.run()
	return s
}

func (s *AccessKeyService) Get() *credential.TmpCredential {
	return s.cred
}

func isEks() bool {
	ClusterId := os.Getenv("EKS_CLUSTER_ID")
	if ClusterId == "" {
		return false
	}
	return true
}

func (s *AccessKeyService) getTmpCredential() (*credential.TmpCredential, error) {
	if isEks() {
		return s.eks.Get()
	} else {
		return s.src.Get()
	}
}

func (s *AccessKeyService) run() {
	log.Info("Starting AccessKeyService ...")
	var timer *time.Timer
	ticker := time.NewTicker(time.Minute * 10)
	var tmpCred *credential.TmpCredential
	for {
		if s.cred == nil {
			timer = time.NewTimer(time.Second)
		} else if s.prevErr != nil {
			timer = time.NewTimer(time.Second * 10)
		} else {
			currTime := time.Now().Unix()
			diff := time.Duration(s.cred.ExpiredTime - currTime - 60)
			if diff <= 0 {
				diff = 1
			}
			timer = time.NewTimer(time.Second * diff)
		}
		select {
		case <-s.stopCh:
			return
		case <-timer.C:
			tmpCred, s.prevErr = s.getTmpCredential()
		case <-ticker.C:
			tmpCred, s.prevErr = s.getTmpCredential()
		}

		if s.prevErr == nil {
			log.Info("get temp credential from oceanus")
			if !equals(tmpCred, s.cred) {
				s.cred = tmpCred
			}
		} else {
			log.Warn("", zap.String("err", s.prevErr.Error()))
		}
	}
}

func equals(c1, c2 *credential.TmpCredential) bool {
	if c1 == c2 {
		return true
	}
	if c1 == nil || c2 == nil {
		return false
	}
	return c1.SecretId == c2.SecretId && c1.SecretKey == c2.SecretKey && c1.Token == c2.Token
}
