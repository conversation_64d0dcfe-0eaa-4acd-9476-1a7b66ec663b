package access_key

import (
	"encoding/json"
	"io/ioutil"
	"net/http"
	"tencentcloud.com/tstream_galileo/src/main/credentials_provider/common/log"
	"tencentcloud.com/tstream_galileo/src/main/credentials_provider/common/mc/model"
	"tencentcloud.com/tstream_galileo/src/main/credentials_provider/common/router"
	"tencentcloud.com/tstream_galileo/src/main/credentials_provider/credential"
)

type AccessKey struct {
	service *AccessKeyService
}

type AccessKeyRsp struct {
	TmpSecretId  string
	TmpSecretKey string
	ExpiredTime  int64
	Expiration   string
	Token        string
	Code         string
}

type ZkAccessKeyRsp struct {
	ZkUser string
	ZkPass string
	Code   string
}

func NewController() *AccessKey {
	controller := &AccessKey{}
	controller.service = GetInstance()
	return controller
}

func (a *AccessKey) Register() {
	router.GetHandler().GET("", a.Process)
	router.GetHandler().GET("/", a.Process)
	router.GetHandler().GET("/AccessKey", a.Process)
	router.GetHandler().GET("/access_key", a.Process)
	router.GetHandler().POST("/ReceiveKey", a.Reveive)
	router.GetHandler().POST("/ReceiveZkKey", a.ReceiveZkKey)
	router.GetHandler().GET("/zk_Key", a.ProcessZkKey)
}

func (a *AccessKey) ProcessZkKey(c *router.Context) {
	tmpCredential := a.service.zkCred
	log.Infof("ProcessZkKey tmpCredential %+v", tmpCredential)
	if tmpCredential == nil {
		log.Errorf("ProcessZkKey tmpCredential is null")
		c.FailWithCustomCode(http.StatusNotFound, nil)
		return
	}
	rsp := &ZkAccessKeyRsp{
		ZkUser: tmpCredential.ZkUser,
		ZkPass: tmpCredential.ZkPass,
		Code:   "Success",
	}
	c.JSON(http.StatusOK, rsp)

}

func (a *AccessKey) Process(c *router.Context) {
	tmpCredential := a.service.Get()
	if tmpCredential == nil {
		c.FailWithCustomCode(http.StatusNotFound, nil)
		return
	}
	rsp := &AccessKeyRsp{
		TmpSecretId:  tmpCredential.SecretId,
		TmpSecretKey: tmpCredential.SecretKey,
		Token:        tmpCredential.Token,
		ExpiredTime:  tmpCredential.ExpiredTime,
		Code:         "Success",
	}
	c.JSON(http.StatusOK, rsp)

}

func (a *AccessKey) ReceiveZkKey(c *router.Context) {
	body, err := ioutil.ReadAll(c.Req.Body)
	if err != nil {
		log.Errorf("failed to receive zk credentials because %+v", err)
		return
	}
	log.Infof("receZkCredParam:body  %s", string(body))
	receZkCredParam := model.ReceiveZkCredentialParam{}
	err = json.Unmarshal(body, &receZkCredParam)
	log.Infof("receZkCredParam: %+v", receZkCredParam)
	if err != nil {
		log.Error("fail to parse request credential param")
		return
	}
	log.Infof("receive zk credential from %s, requestId %s", c.Req.Header.Get("User-Agent"), receZkCredParam.RequestId)
	a.service.zkCred = &receZkCredParam
	c.JSON(http.StatusOK, "OK")
}

func (a *AccessKey) Reveive(c *router.Context) {
	body, err := ioutil.ReadAll(c.Req.Body)
	if err != nil {
		log.Errorf("failed to receive credentials because %+v", err)
		return
	}
	receCredParam := model.ReceiveCredentialParam{}
	err = json.Unmarshal(body, &receCredParam)
	if err != nil {
		log.Error("fail to parse request credential param")
		return
	}
	log.Infof("receive credential from %s, requestId %s", c.Req.Header.Get("User-Agent"), receCredParam.RequestId)
	cred := &credential.TmpCredential{
		SecretId:    receCredParam.Credential.SecretId,
		SecretKey:   receCredParam.Credential.SecretKey,
		Token:       receCredParam.Credential.Token,
		ExpiredTime: receCredParam.Credential.ExpiredTime,
	}
	a.service.cred = cred
	c.JSON(http.StatusOK, "OK")
}
