package main

import (
	"fmt"
	"github.com/juju/errors"
	"os"
	"os/signal"
	"syscall"
	"tencentcloud.com/tstream_galileo/src/common/commands"
	"tencentcloud.com/tstream_galileo/src/common/configure"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/dlocker"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	_ "tencentcloud.com/tstream_galileo/src/common/httpserver/impl"
	"tencentcloud.com/tstream_galileo/src/common/international"
	logging "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/scheduler"
	_ "tencentcloud.com/tstream_galileo/src/galileo_cc/controller"
	_ "tencentcloud.com/tstream_galileo/src/galileo_cc/controller/mc"
	_ "tencentcloud.com/tstream_galileo/src/galileo_cc/controller/mc/cdb_admin"
	_ "tencentcloud.com/tstream_galileo/src/galileo_cc/controller/mc/cdp_admin"
	_ "tencentcloud.com/tstream_galileo/src/galileo_cc/controller/mc/ckafka"
	_ "tencentcloud.com/tstream_galileo/src/galileo_cc/controller/mc/cluster_admin"
	_ "tencentcloud.com/tstream_galileo/src/galileo_cc/controller/mc/debug"
	_ "tencentcloud.com/tstream_galileo/src/galileo_cc/controller/mc/region"
	_ "tencentcloud.com/tstream_galileo/src/galileo_cc/controller/mc/tablesource"
	_ "tencentcloud.com/tstream_galileo/src/galileo_cc/controller/taskcenter"
	service2 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/barad"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/alarm"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/billing"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/chdfs"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/clean"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/cluster"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/cluster_scheduler"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/cos"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/cost"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/debug"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/draft"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/etl"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/etl_precheck"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/folder"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/grant"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/item_space"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/job"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/job_autoscale"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/job_config"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/log"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/login_settings"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/metadata"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/metric"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/overview"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/quota"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/region"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/resource"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/resource_config"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/role_auth"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/role_info"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/role_permission"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/runtime"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/savepoint"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/setats"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/sql"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/sql_gateway"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/subnet"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/test"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/tke"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/variable"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/vpc"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/watchdog"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/white_list"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/yunti"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cluster_master"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/mc/alarm"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/mc/api"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/mc/billing"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/mc/bucket"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/mc/cluster"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/mc/debug"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/mc/draft"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/mc/job"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/mc/metadata"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/mc/metric"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/mc/net"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/mc/region"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/mc/resource"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/mc/upgrade"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/mc/user"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/metric_provider"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/operation/cluster_mng"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/operation/job_mng"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/watchdog"
	billingService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/billing"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config"
	cos "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cos/handler"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/daemon"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job"
	describeMetricKeyService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/metric_provider"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/timer"
	taskHandler "tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler"
)

var confFile = "galileo_cc.properties"

var txManager *dao.DataSourceTransactionManager

func init() {
	dbUrl := configure.GetConfStringValue(confFile, "dburl")
	if dbUrl == "" {
		fmt.Println("darwin Process init error, dburl not set....")
		os.Exit(-1)
	}
	taskCallBackInfo := &flow.TaskCenterCallBackInfo{
		CallbackAddr:          configure.GetConfStringValue(confFile, "taskcentercallback"),
		CompleteTaskInterface: configure.GetConfStringValue(confFile, "completeTaskInterface"),
		AlarmTaskInterface:    configure.GetConfStringValue(confFile, "alarmTaskInterface"),
	}

	tx, err := dao.NewDataSourceTransactionManager(dbUrl)
	if err != nil {
		fmt.Println("darwin Process create DataSourceTransactionManager err", err)
		os.Exit(-1)
	}
	txManager = tx
	service2.SetTxManager(txManager)
	taskHandler.SetTxManager(txManager)
	dlocker.SetTxManager(txManager)
	flow.SetTaskCenterCallbackInfo(taskCallBackInfo)
	flow.SetTxManager(txManager)
	flow.SetCanSubmitCallBack(nil)
	// 设置sql server 回调galileo的地址，复用了taskcenter回调galileo的配置
	//metadata.SetGalileoCallbackUrl(configure.GetConfStringValue(confFile, "taskcentercallback"))
	// Timer 定时任务
	taskHandler.CleanMetricDataWithFixedDelay()      // 定时清空 Metrics 数据
	timer.DetectCommandTimeoutWithFixedDelay()       // 定时对超时命令做告警
	timer.DetectClusterStatusTimeoutWithFixedDelay() // 定时对集群未初始化做告警
	timer.DetectCommandAckTimeoutWithFixedDelay()
	timer.DetectPodErrorOrInitWithFixedDelay()       // 定时对 Pod 初始化超时、Pod 状态异常的进行告警
	timer.DetectJobStatusWithFixedEventAlterStatus() // 定时对作业真实状态与EventAlert告警状态不同步的情况进行处理
	timer.ResourceConfigSync()
	timer.LoadFlow()
	timer.LoadCommands()
	commands.SetTxManager(txManager)
	timer.DeleteVpcHandler()
	timer.TerminateCbs()
	timer.AutoScale()                    //定时判断作业是否要进行自动扩缩容
	timer.ClusterAllRegionCountHandler() //定时采集集群数据
	timer.RegionSynWithFixedDelay()
	timer.AsyncJobInfoSync()       // 定时删除 AsyncJobInfo 表 type==4 的数据
	timer.DeleteExpiredEventData() // 定时删除超过 90 天（可配置）的事件数据（默认24小时执行一次，）
	timer.DetectJobDispatchFailWithFixedDelay()
	// 后付费 推量  https://tcb.woa.com/magical-brush/docs/754674275
	timer.SendResource()
	timer.SendResourceAlarm()
	timer.SyncProjectUsersHandler()
	timer.CalcUniformClusterCvmHandler()
	timer.AutoScaleTimeBased()     // 定时获取时间规则
	timer.SendCredentialInfo()     // 定时发送密钥到credentials-provider
	timer.SyncWebUIPrefixHandler() // 定时同步内网WebUIPrefix
	// todo 对 auto scale action 进行清理

	timer.DeleteExpiredWatchdogEventData()    // 定时删除超过 30 天的Watchdog事件数据（默认6小时执行一次，）
	timer.DeleteExpiredJobInsMetricMetaData() // 定时清理作业指标元数据

	timer.ScanUnExpectedStatusJobs() // 定期扫描预期状态和实际状态不相符的作业

	timer.ProcessOrderedWatchdogEvent()
	timer.ProcessOutofOrderWatchdogEvent()

	timer.InterfaceClear()
	// daemon 进程
	runningLogDaemon := daemon.GetEnableRunningLogDaemon()
	runningLogDaemon.Start()

	service.InitMetricVariables()
	billingService.InitBillingVariables()
	describeMetricKeyService.InitMetricVariables()
	billingService.InitBillingSendResourceVariables()
	billingService.InitBillingModifyResourceStatusVariables()
	config.InitRainbowService(nil, nil, nil)
	cos.GetHandler().Start()
	go scheduler.Start()
	international.InitInternationalTranslation()

}

func main() {
	// 设置 core dump
	os.Setenv("GOTRACEBACK", "crash")

	// 信号处理
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt)
	signal.Notify(c, syscall.SIGTERM)
	signal.Notify(c, syscall.SIGPIPE)
	go func() {
		s := <-c
		if s == syscall.SIGPIPE {
			logging.Warning("Process accept sig pip,....")
			return
		}
		logging.Error("Ctrl-c or SIGTERM found, exit.....")
		os.Exit(0)
	}()

	server := httpserver.NewSharkServer()
	sharkPort := configure.GetConfStringValueWithDefault(confFile, "shark.port", "2021")
	logging.Info("galileo_cc is going to listen at port", sharkPort)
	params := map[string]string{"http.server.addr": ":" + sharkPort}
	// 启动 http
	if err := server.InitServer(params); err != nil {
		logging.Error("galileo_cc create http server err: ", errors.Trace(err))
		os.Exit(-1)
	}
	if err := server.Start(); err != nil {
		logging.Error("galileo_cc start http server err: ", errors.Trace(err))
		os.Exit(-1)
	}
}
