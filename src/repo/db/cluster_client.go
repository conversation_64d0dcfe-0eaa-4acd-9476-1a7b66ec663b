//go:generate mockgen -source=cluster_client.go -destination=./mockdb/mock_cluster_client.go -package=mockdb
package db

import (
	"context"

	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
)

// ClusterClient 集群信息客户端代理
type ClusterClient interface {
	// SearchSpaceClusterGroupList 支持使用id或者name搜索
	SearchSpaceClusterGroupList(ctx context.Context, spaceId int64,
		clusterGroupId string, clusterGroupName string) ([]*table.ClusterGroup, error)

	// BatchQueryClusterGroupByIds 根据id批量拉取cluster group
	BatchQueryClusterGroupByIds(ctx context.Context, idList []int64) (map[int64]*table.ClusterGroup, error)

	// BatchQueryClusterGroupBySerialIds 根据serialIds批量拉取cluster group
	BatchQueryClusterGroupBySerialIds(ctx context.Context, serialIdList []string) (map[string]*table.ClusterGroup, error)
}
