//go:generate mockgen -source=job_client.go -destination=./mockdb/mock_job_client.go -package=mockdb
package db

import (
	"context"

	"tencentcloud.com/tstream_galileo/src/common/dao"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job"
	jobconfigtable "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job_config"
	treetable "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/tree"
)

// JobClient 作业信息客户端代理
type JobClient interface {
	// CountJobGroupByDay 按天计数, 没有联表查询
	// CountName格式是2006-01-02
	CountJobGroupByDay(ctx context.Context, condition *dao.Condition) ([]*CountResult, error)

	// CountJobGroupByFolderId 根据目录id分组计数
	CountJobGroupByFolderId(ctx context.Context, condition *dao.Condition) ([]*CountResult, error)

	// CountJobGroupByClusterGroupId 根据集群id(自增id)分组计数
	// CountName是集群自增id
	CountJobGroupByClusterGroupId(ctx context.Context, condition *dao.Condition) ([]*CountResult, error)
	// CountJob 根据条件计数
	CountJob(ctx context.Context, condition *dao.Condition) (int64, error)

	// SearchJobBySqlKeyword 根据sql作业关键字查询作业id
	SearchJobBySqlKeyword(ctx context.Context, appId int64, spaceId int64, sqlKeyword string) ([]*table.JobInfoForSqlCode, error)

	SearchJobFolder(ctx context.Context, appId int64, spaceId int64, folderName string) ([]*treetable.Tree, error)

	ListJobTree(ctx context.Context, appId int64, spaceId int64) ([]*treetable.Tree, error)

	PageJobSortById(ctx context.Context, lastId int64, pageSize int, condition *dao.Condition) ([]*table.Job, error)

	BatchQueryJobScaleRuleByJobIds(ctx context.Context,
		jobIdList []string) (map[string]*jobconfigtable.JobScaleRule, error)
}
