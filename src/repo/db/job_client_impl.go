package db

import (
	"context"
	"fmt"

	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logging "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job"
	jobconfigtable "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job_config"
	treetable "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/tree"
)

type jobClient struct {
	proxy *dao.DataSourceTransactionManager
}

// NewJobClient 创建一个作业存储客户端
var NewJobClient = func() JobClient {
	return &jobClient{
		proxy: service.GetTxManager(),
	}
}

// CountJobGroupByDay 按天计数
func (c *jobClient) CountJobGroupByDay(ctx context.Context, condition *dao.Condition) ([]*CountResult, error) {
	condition.Ne("Status", constants.JOB_STATUS_DELETE)
	where, args := condition.GetWhere()
	stmt := fmt.Sprintf("SELECT DATE(CreateTime) AS CountName, COUNT(1) AS Count FROM Job %s GROUP BY CountName ORDER BY NULL", where)
	logging.Infof("start to count group by day, sql:%s, args:%+v", stmt, args)
	_, data, err := c.proxy.GetQueryTemplate().DoQueryWithArgs(stmt, args...)
	if err != nil {
		logging.Errorf("count group by day failed, %+v", err)
		return nil, err
	}
	return decodeToCountResult(data, err)
}

func (c *jobClient) CountJobGroupByFolderId(ctx context.Context, condition *dao.Condition) ([]*CountResult, error) {
	condition.Ne("Status", constants.JOB_STATUS_DELETE)
	where, args := condition.GetWhere()
	stmt := fmt.Sprintf("SELECT FolderId AS CountName, COUNT(1) AS Count FROM Job %s GROUP BY FolderId ORDER BY NULL",
		where)
	logging.Infof("start to count group folder id, sql:%s, args:%+v", stmt, args)
	_, data, err := c.proxy.GetQueryTemplate().DoQueryWithArgs(stmt, args...)
	if err != nil {
		logging.Errorf("count group by folder id failed, %+v", err)
		return nil, err
	}
	return decodeToCountResult(data, err)
}

func (c *jobClient) CountJobGroupByClusterGroupId(ctx context.Context, condition *dao.Condition) ([]*CountResult, error) {
	condition.Ne("Status", constants.JOB_STATUS_DELETE)
	where, args := condition.GetWhere()
	stmt := fmt.Sprintf("SELECT ClusterGroupId AS CountName, COUNT(1) AS Count FROM Job %s GROUP BY ClusterGroupId ORDER BY NULL",
		where)
	logging.Infof("start to count group by clustergroup id, sql:%s, args:%+v", stmt, args)
	_, data, err := c.proxy.GetQueryTemplate().DoQueryWithArgs(stmt, args...)
	if err != nil {
		logging.Errorf("count group by clustergroup id failed, %+v", err)
		return nil, err
	}
	return decodeToCountResult(data, err)
}

func (c *jobClient) CountJob(ctx context.Context, condition *dao.Condition) (int64, error) {
	condition.Ne("Status", constants.JOB_STATUS_DELETE)
	where, args := condition.GetWhere()
	stmt := fmt.Sprintf("SELECT COUNT(1) AS Count FROM Job %s",
		where)
	logging.Infof("start to count group, sql:%s, args:%+v", stmt, args)
	_, data, err := c.proxy.GetQueryTemplate().DoQueryWithArgs(stmt, args...)
	if err != nil {
		logging.Errorf("count group failed, %+v", err)
		return -1, err
	}
	if len(data) == 0 {
		logging.Infof("not found result")
		return 0, nil
	}
	cr := &CountResult{}
	if err := util.ScanMapIntoStruct(cr, data[0]); err != nil {
		logging.Errorf("decode to count result failed, %+v", err)
		return -1, err
	}
	return cr.Count, nil
}

func (c *jobClient) SearchJobBySqlKeyword(ctx context.Context, appId int64,
	spaceId int64, sqlKeyword string) ([]*table.JobInfoForSqlCode, error) {
	condition := dao.NewCondition()
	condition.Eq("j.AppId", appId)
	condition.Eq("j.ItemSpaceId", spaceId)
	condition.Ne("j.Status", constants.JOB_STATUS_DELETE)
	condition.Like("jcs.DecodeSqlCode", "%"+sqlKeyword+"%")
	where, args := condition.GetWhere()
	stmt := fmt.Sprintf("SELECT j.*, jcs.DecodeSqlCode FROM Job j join JobConfigSqlCode jcs on j.Id = jcs.JobId %s", where)
	logging.Infof("start to search job by sql, sql:%s, args:%+v", stmt, args)
	_, data, err := c.proxy.GetQueryTemplate().DoQueryWithArgs(stmt, args...)
	if err != nil {
		logging.Errorf("list job tree failed, %+v", err)
		return nil, err
	}
	jobs := make([]*table.JobInfoForSqlCode, 0)
	for i := 0; i < len(data); i++ {
		job := &table.JobInfoForSqlCode{}
		err = util.ScanMapIntoStruct(job, data[i])
		if err != nil {
			return nil, errorcode.FailedOperationCode.NewWithErr(err)
		}
		jobs = append(jobs, job)
	}
	return jobs, nil
}

func (c *jobClient) SearchJobFolder(ctx context.Context, appId int64,
	spaceId int64, folderName string) ([]*treetable.Tree, error) {
	condition := dao.NewCondition()
	condition.Eq("AppId", appId)
	condition.Eq("ItemSpaceId", spaceId)
	condition.Like("FolderName", "%"+folderName+"%")
	where, args := condition.GetWhere()
	stmt := fmt.Sprintf("SELECT * FROM Tree %s", where)
	logging.Infof("start to search job tree, sql:%s, args:%+v", stmt, args)
	_, data, err := c.proxy.GetQueryTemplate().DoQueryWithArgs(stmt, args...)
	if err != nil {
		logging.Errorf("search job tree failed, %+v", err)
		return nil, err
	}
	resultList := make([]*treetable.Tree, 0, len(data))
	for i := 0; i < len(data); i++ {
		tree := &treetable.Tree{}
		err = util.ScanMapIntoStruct(tree, data[i])
		if err != nil {
			logging.Errorf("decode to tree failed, %+v", err)
			return nil, err
		}
		resultList = append(resultList, tree)
	}
	return resultList, nil
}

func (c *jobClient) ListJobTree(ctx context.Context, appId int64, spaceId int64) ([]*treetable.Tree, error) {
	condition := dao.NewCondition()
	condition.Eq("AppId", appId)
	condition.Eq("ItemSpaceId", spaceId)
	where, args := condition.GetWhere()
	stmt := fmt.Sprintf("SELECT * FROM Tree %s ORDER BY CreateTime DESC",
		where)
	logging.Infof("start to list job tree, sql:%s, args:%+v", stmt, args)
	_, data, err := c.proxy.GetQueryTemplate().DoQueryWithArgs(stmt, args...)
	if err != nil {
		logging.Errorf("list job tree failed, %+v", err)
		return nil, err
	}
	resultList := make([]*treetable.Tree, 0, len(data))
	for i := 0; i < len(data); i++ {
		tree := &treetable.Tree{}
		err = util.ScanMapIntoStruct(tree, data[i])
		if err != nil {
			logging.Errorf("decode to tree failed, %+v", err)
			return nil, err
		}
		resultList = append(resultList, tree)
	}
	return resultList, nil
}

func (c *jobClient) PageJobSortById(ctx context.Context, lastId int64,
	pageSize int, condition *dao.Condition) ([]*table.Job, error) {
	if lastId > 0 {
		condition.Lt("Id", lastId)
	}
	condition.Ne("Status", constants.JOB_STATUS_DELETE)
	where, args := condition.GetWhere()
	stmt := fmt.Sprintf("SELECT * FROM Job %s ORDER BY Id DESC LIMIT %d", where, pageSize)
	logging.Infof("start to list job sort by id, sql:%s, args:%+v", stmt, args)
	_, data, err := c.proxy.GetQueryTemplate().DoQueryWithArgs(stmt, args...)
	if err != nil {
		logging.Errorf("list job sort by id failed, %+v", err)
		return nil, err
	}
	resultList := make([]*table.Job, 0, len(data))
	for i := 0; i < len(data); i++ {
		job := &table.Job{}
		err = util.ScanMapIntoStruct(job, data[i])
		if err != nil {
			logging.Errorf("decode to job sort by id failed, %+v", err)
			return nil, err
		}
		resultList = append(resultList, job)
	}
	return resultList, nil
}

func (c *jobClient) BatchQueryJobScaleRuleByJobIds(ctx context.Context,
	jobIdList []string) (map[string]*jobconfigtable.JobScaleRule, error) {
	condition := dao.NewCondition()
	condition.In("JobId", jobIdList)
	condition.Eq("Status", 1)

	where, args := condition.GetWhere()
	stmt := fmt.Sprintf("SELECT * FROM JobScaleRule %s", where)
	logging.Infof("start to batch query job scale rule, sql:%s, args:%+v", stmt, args)
	_, data, err := c.proxy.GetQueryTemplate().DoQueryWithArgs(stmt, args...)
	if err != nil {
		logging.Errorf("batch query job scale rule failed, %+v", err)
		return nil, err
	}
	resultMap := make(map[string]*jobconfigtable.JobScaleRule)
	for i := 0; i < len(data); i++ {
		rule := &jobconfigtable.JobScaleRule{}
		err = util.ScanMapIntoStruct(rule, data[i])
		if err != nil {
			logging.Errorf("decode to job scale rule failed, %+v", err)
			return nil, err
		}
		resultMap[rule.JobId] = rule
	}
	return resultMap, nil
}
