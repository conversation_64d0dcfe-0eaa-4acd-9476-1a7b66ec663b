package service

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"os"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"testing"
)

const DBURL = "root:root@tcp(127.0.0.1:3306)/online_galileo?charset=utf8"

func init() {
	testing.Init()
	tx, err := dao.NewDataSourceTransactionManager(DBURL)
	if err != nil {
		fmt.Println("darwin Process create DataSourceTransactionManager err", err)
		os.Exit(-1)
	}
	SetTxManager(tx)
}

func TestUpdateJobConfigJobGraphHaveSameJobGraph(t *testing.T) {
	s := "{\"jid\":\"ffffffff837247990000000000000000\",\"name\":\"WordCount\",\"type\":\"STREAMING\",\"nodes\":[{\"id\":\"6d2677a0ecc3fd8df0b72ec675edf8f4\",\"parallelism\":2,\"operator\":\"\",\"operator_strategy\":\"\",\"description\":\"Sink: Logging Sink<br/>\",\"inputs\":[{\"num\":0,\"id\":\"ea632d67b7d595e5b851708ae9ad79d6\",\"ship_strategy\":\"REBALANCE\",\"exchange\":\"pipelined_bounded\"}],\"optimizer_properties\":{}},{\"id\":\"ea632d67b7d595e5b851708ae9ad79d6\",\"parallelism\":1,\"operator\":\"\",\"operator_strategy\":\"\",\"description\":\"Keyed Reduce<br/>\",\"inputs\":[{\"num\":0,\"id\":\"0a448493b4782967b150582570326227\",\"ship_strategy\":\"HASH\",\"exchange\":\"pipelined_bounded\"}],\"optimizer_properties\":{}},{\"id\":\"0a448493b4782967b150582570326227\",\"parallelism\":2,\"operator\":\"\",\"operator_strategy\":\"\",\"description\":\"Flat Map<br/>\",\"inputs\":[{\"num\":0,\"id\":\"bc764cd8ddf7a0cff126f51c16239658\",\"ship_strategy\":\"REBALANCE\",\"exchange\":\"pipelined_bounded\"}],\"optimizer_properties\":{}},{\"id\":\"bc764cd8ddf7a0cff126f51c16239658\",\"parallelism\":1,\"operator\":\"\",\"operator_strategy\":\"\",\"description\":\"Source: Count down for infinite seconds<br/>\",\"optimizer_properties\":{}}]}"
	res1 := base64.StdEncoding.EncodeToString([]byte(s))
	fmt.Println(res1)
}

func TestUpdateJobConfigJobGraph(t *testing.T) {
	s := "{\"jid\":\"00000000000000000000000000000000\",\"name\":\"WordCount\",\"isStoppable\":false,\"state\":\"RUNNING\",\"start-time\":1741946244361,\"end-time\":-1,\"duration\":13214705,\"maxParallelism\":2048,\"now\":1741959459066,\"timestamps\":{\"FAILED\":0,\"RESTARTING\":0,\"SUSPENDED\":0,\"CREATED\":1741946244505,\"RECONCILING\":0,\"RUNNING\":1741946245381,\"FAILING\":0,\"FINISHED\":0,\"CANCELLING\":0,\"INITIALIZING\":1741946244361,\"CANCELED\":0},\"vertices\":[{\"id\":\"bc764cd8ddf7a0cff126f51c16239658\",\"name\":\"Source: Count down for infinite seconds\",\"maxParallelism\":2048,\"parallelism\":1,\"status\":\"RUNNING\",\"start-time\":1741946271516,\"end-time\":-1,\"duration\":13187550,\"tasks\":{\"SCHEDULED\":0,\"CREATED\":0,\"INITIALIZING\":0,\"CANCELED\":0,\"DEPLOYING\":0,\"FINISHED\":0,\"RECONCILING\":0,\"RUNNING\":1,\"CANCELING\":0,\"FAILED\":0},\"metrics\":{\"read-bytes\":0,\"read-bytes-complete\":true,\"write-bytes\":1145989,\"write-bytes-complete\":true,\"read-records\":0,\"read-records-complete\":true,\"write-records\":13167,\"write-records-complete\":true}},{\"id\":\"0a448493b4782967b150582570326227\",\"name\":\"Flat Map\",\"maxParallelism\":2048,\"parallelism\":2,\"status\":\"RUNNING\",\"start-time\":1741946271520,\"end-time\":-1,\"duration\":13187546,\"tasks\":{\"SCHEDULED\":0,\"CREATED\":0,\"INITIALIZING\":0,\"CANCELED\":0,\"DEPLOYING\":0,\"FINISHED\":0,\"RECONCILING\":0,\"RUNNING\":2,\"CANCELING\":0,\"FAILED\":0},\"metrics\":{\"read-bytes\":1147805,\"read-bytes-complete\":true,\"write-bytes\":3711604,\"write-bytes-complete\":true,\"read-records\":13167,\"read-records-complete\":true,\"write-records\":223839,\"write-records-complete\":true}},{\"id\":\"ea632d67b7d595e5b851708ae9ad79d6\",\"name\":\"Keyed Reduce\",\"maxParallelism\":2048,\"parallelism\":2,\"status\":\"RUNNING\",\"start-time\":1741946271524,\"end-time\":-1,\"duration\":13187542,\"tasks\":{\"SCHEDULED\":0,\"CREATED\":0,\"INITIALIZING\":0,\"CANCELED\":0,\"DEPLOYING\":0,\"FINISHED\":0,\"RECONCILING\":0,\"RUNNING\":2,\"CANCELING\":0,\"FAILED\":0},\"metrics\":{\"read-bytes\":3716326,\"read-bytes-complete\":true,\"write-bytes\":3711604,\"write-bytes-complete\":true,\"read-records\":223839,\"read-records-complete\":true,\"write-records\":223839,\"write-records-complete\":true}},{\"id\":\"6d2677a0ecc3fd8df0b72ec675edf8f4\",\"name\":\"Sink: Logging Sink\",\"maxParallelism\":2048,\"parallelism\":2,\"status\":\"RUNNING\",\"start-time\":1741946271525,\"end-time\":-1,\"duration\":13187541,\"tasks\":{\"SCHEDULED\":0,\"CREATED\":0,\"INITIALIZING\":0,\"CANCELED\":0,\"DEPLOYING\":0,\"FINISHED\":0,\"RECONCILING\":0,\"RUNNING\":2,\"CANCELING\":0,\"FAILED\":0},\"metrics\":{\"read-bytes\":3715370,\"read-bytes-complete\":true,\"write-bytes\":0,\"write-bytes-complete\":true,\"read-records\":223839,\"read-records-complete\":true,\"write-records\":0,\"write-records-complete\":true}}],\"status-counts\":{\"SCHEDULED\":0,\"CREATED\":0,\"INITIALIZING\":0,\"CANCELED\":0,\"DEPLOYING\":0,\"FINISHED\":0,\"RECONCILING\":0,\"RUNNING\":4,\"CANCELING\":0,\"FAILED\":0},\"plan\":{\"jid\":\"00000000000000000000000000000000\",\"name\":\"WordCount\",\"nodes\":[{\"id\":\"6d2677a0ecc3fd8df0b72ec675edf8f4\",\"parallelism\":2,\"operator\":\"\",\"operator_strategy\":\"\",\"description\":\"Sink: Logging Sink\",\"inputs\":[{\"num\":0,\"id\":\"ea632d67b7d595e5b851708ae9ad79d6\",\"ship_strategy\":\"FORWARD\",\"exchange\":\"pipelined_bounded\"}],\"optimizer_properties\":{}},{\"id\":\"ea632d67b7d595e5b851708ae9ad79d6\",\"parallelism\":2,\"operator\":\"\",\"operator_strategy\":\"\",\"description\":\"Keyed Reduce\",\"inputs\":[{\"num\":0,\"id\":\"0a448493b4782967b150582570326227\",\"ship_strategy\":\"HASH\",\"exchange\":\"pipelined_bounded\"}],\"optimizer_properties\":{}},{\"id\":\"0a448493b4782967b150582570326227\",\"parallelism\":2,\"operator\":\"\",\"operator_strategy\":\"\",\"description\":\"Flat Map\",\"inputs\":[{\"num\":0,\"id\":\"bc764cd8ddf7a0cff126f51c16239658\",\"ship_strategy\":\"REBALANCE\",\"exchange\":\"pipelined_bounded\"}],\"optimizer_properties\":{}},{\"id\":\"bc764cd8ddf7a0cff126f51c16239658\",\"parallelism\":1,\"operator\":\"\",\"operator_strategy\":\"\",\"description\":\"Source: Count down for infinite seconds\",\"optimizer_properties\":{}}]}}"
	graph := &FlinkJobVertice{}
	err := json.Unmarshal([]byte(s), graph)
	if err != nil {
		fmt.Println(err.Error())

	}
	//fmt.Println(graph)
	res, _ := json.Marshal(graph)
	res1 := base64.StdEncoding.EncodeToString(res)
	fmt.Println(res1)
	s1, err := base64.StdEncoding.DecodeString(res1)
	if err != nil {
		return
	}
	fmt.Println(s1)
	type args struct {
		jobConfigId int64
		jobGraph    string
		oldJobGraph string

		jobId int64
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{name: "sss", args: args{
			jobConfigId: 1,
			jobId:       1,
			jobGraph:    res1,
			oldJobGraph: "",
		}},
		// TODO: Add test cases.
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := UpdateJobConfigJobGraph(tt.args.jobConfigId, tt.args.jobGraph, tt.args.oldJobGraph, tt.args.jobId); (err != nil) != tt.wantErr {
				t.Errorf("UpdateJobConfigJobGraph() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

type FlinkJobVertice struct {
	Vertices []Vertice `json:"vertices"`
}
type Vertice struct {
	Id          string `json:"id"`
	Name        string `json:"name"`
	Parallelism int    `json:"parallelism"`
}
