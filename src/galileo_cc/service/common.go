package service

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"strconv"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/configure"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/model/ckafka"
	"time"
)

var ConfFile = "galileo_cc.properties"
var ComponentFile = "component.properties"

var txManager *dao.DataSourceTransactionManager
var httpClient = &http.Client{Timeout: 30 * time.Second}

func SetTxManager(txm *dao.DataSourceTransactionManager) {
	txManager = txm
}

func GetTxManager() (txm *dao.DataSourceTransactionManager) {
	return txManager
}

func GetClusterAdminUrl() string {
	var ClusterAdminUrl = configure.GetConfStringValue(ConfFile, "clusterAdmin")
	return ClusterAdminUrl
}

func GetCdpAdminUrl() string {
	var CdpAdminUrl = configure.GetConfStringValue(ConfFile, "cdpAdmin")
	return CdpAdminUrl
}

func GetPlatformUrl() string {
	var url = configure.GetConfStringValue(ConfFile, "platformUrl")
	return url
}

func GetConfStringValue(key string) string {
	var val = configure.GetConfStringValue(ConfFile, key)
	return val
}

func GetComponentUrl(componentName string, regionId int) string {
	urlkey := fmt.Sprintf("component.url.%s.%d", componentName, regionId)
	return configure.GetConfStringValue(ComponentFile, urlkey)
}

func GetJsonFromUrlByGet(url string, target interface{}) error {
	response, err := httpClient.Get(url)
	if err != nil {
		return err
	}
	defer response.Body.Close()

	return json.NewDecoder(response.Body).Decode(target)
}

// 返回码200，body才是json，否则就是错误信息，把错误信息抛出去更友好，否则都是json解析的错误
func GetResponseFromUrlByGetWithCheck(url string, target interface{}) error {
	response, err := httpClient.Get(url)
	if err != nil {
		return err
	}
	defer response.Body.Close()
	if response.StatusCode != 200 {
		var body []byte
		body, _ = ioutil.ReadAll(response.Body)
		err = fmt.Errorf(string(body))
		return err
	}

	return json.NewDecoder(response.Body).Decode(target)
}

func GetResponseStrFromUrlByGetWithCheck(url string) (string, error) {
	response, err := httpClient.Get(url)
	if err != nil {
		return "", err
	}
	defer response.Body.Close()
	var body []byte
	body, _ = ioutil.ReadAll(response.Body)
	if response.StatusCode != 200 {
		err = fmt.Errorf(string(body))
		return "", err
	}

	return string(body), nil
}

func GetJsonFromUrlByPost(url string, body string, target interface{}) error {
	response, err := httpClient.Post(url, "application/json", strings.NewReader(body))
	if err != nil {
		return err
	}
	defer response.Body.Close()

	return json.NewDecoder(response.Body).Decode(target)
}

// ckafka查询出错时json里data字段是[]，而正确时是{}，故无法定义struct，需要先判断一层returnCode是否为0，若为0代表成功则继续decode结果
func GetJsonFromCkafkaUrlByPost(url string, body string, target interface{}) error {
	response, err := httpClient.Post(url, "application/json", strings.NewReader(body))
	if err != nil {
		return err
	}
	defer response.Body.Close()

	commonResponse := &ckafka.CkafkaCommonResponse{}
	var bytes []byte
	bytes, err = ioutil.ReadAll(response.Body)
	if err != nil {
		return err
	}
	json.Unmarshal(bytes, commonResponse)
	if commonResponse.ReturnCode != 0 {
		err = fmt.Errorf(commonResponse.ReturnMessage)
		return err
	}
	return json.Unmarshal(bytes, target)
}

func GetStrFromUrlByPost(url string, body string) (string, error) {
	response, err := httpClient.Post(url, "application/json", strings.NewReader(body))
	if err != nil {
		return "", err
	}
	defer response.Body.Close()
	var responseBody []byte
	responseBody, _ = ioutil.ReadAll(response.Body)
	if response.StatusCode != 200 {
		err = fmt.Errorf(string(responseBody))
		return "", err
	}

	return string(responseBody), nil
}

func Itoa(num int64) string {
	return strconv.FormatInt(num, 10)
}
