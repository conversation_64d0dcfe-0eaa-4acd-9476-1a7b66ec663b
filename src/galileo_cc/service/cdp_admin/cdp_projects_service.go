package cdp_admin

import (
	"tencentcloud.com/tstream_galileo/src/galileo_cc/model/cdp_admin"
	"tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"fmt"
)

func GetCdpProjects(appId int64,regionId int) (*cdp_admin.CdpAdminProjectsIntermediate, error){
	cdpAdminIntermediate := &cdp_admin.CdpAdminProjectsIntermediate{}
	cdpAdminGetProjectUrl := fmt.Sprintf("%s/projects/%s/?regionId=%d&page=0&size=100000",service.GetCdpAdminUrl(), service.Itoa(appId), regionId)
	err := service.GetJsonFromUrlByGet(cdpAdminGetProjectUrl, &cdpAdminIntermediate)
	if err != nil {
		logging.Errorf("Error getting projects using CDP appId: %d, errors: %+v" , appId, err)
		return cdpAdminIntermediate, err
	}

	if len(cdpAdminIntermediate.Content) > 0 {
		return cdpAdminIntermediate,nil
	} else {
		return nil , nil
	}
}