package service

import (
	"errors"
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	jobConfigModel "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job_config"
)

func GetJobConfig(serialId string, jobConfigVersion int64) (t *jobConfigModel.JobConfig, err error) {

	defer func() {
		if errs := recover(); errs != nil {
			logging.Errorf("GetJobConfig panic ,errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()

	sql := "SELECT a.* FROM JobConfig as a, Job as b where a.jobId = b.id and b.serialId=? and a.versionId=?"
	args := make([]interface{}, 0)
	args = append(args, serialId)
	args = append(args, jobConfigVersion)

	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logging.Error("Failed to query with sql:", sql, ", errors:", err.Error())
		return &jobConfigModel.JobConfig{}, err
	}

	if len(data) == 0 {
		logging.Errorf("not find job config, with job serialId:%s, with job config version:%d", serialId, jobConfigVersion)
		return &jobConfigModel.JobConfig{}, errors.New("not find Job Config")
	}

	if len(data) > 1 {
		logging.Error("logic error, not only one jobconfig query sql:", sql)
		return &jobConfigModel.JobConfig{}, errors.New("logic error, not only one JobConfig")
	}

	result := &jobConfigModel.JobConfig{}
	err = util.ScanMapIntoStruct(result, data[0])
	if err != nil {
		logging.Error("GetJobConfig ScanMapIntoStruct err:", err)
		return nil, err
	}
	return result, nil
}

func GetJobConfigByJobInstanceId(jobInstanceId int64) (t *jobConfigModel.JobConfig, err error) {

	defer func() {
		if errs := recover(); errs != nil {
			logging.Errorf("GetJobConfig panic ,errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()

	sql := "select jc.* from JobConfig jc inner join JobInstance ji on ji.JobConfigId = jc.Id where ji.Id = ?"
	args := make([]interface{}, 0)
	args = append(args, jobInstanceId)

	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logging.Error("Failed to query with sql:", sql, ", errors:", err.Error())
		return &jobConfigModel.JobConfig{}, err
	}

	if len(data) == 0 {
		logging.Errorf("not find job config, with JobInstanceId:%s", jobInstanceId)
		return &jobConfigModel.JobConfig{}, errors.New("not find Job Config")
	}

	if len(data) > 1 {
		logging.Error("logic error, not only one jobconfig query sql:", sql)
		return &jobConfigModel.JobConfig{}, errors.New("logic error, not only one JobConfig")
	}

	result := &jobConfigModel.JobConfig{}
	err = util.ScanMapIntoStruct(result, data[0])
	if err != nil {
		logging.Error("GetJobConfigByJobInstanceId ScanMapIntoStruct err:", err)
		return nil, err
	}
	return result, nil
}

func GetJobConfigById(jobConfigId int64) (t *jobConfigModel.JobConfig, err error) {

	defer func() {
		if errs := recover(); errs != nil {
			logging.Errorf("GetJobConfig panic ,errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()

	sql := "SELECT * FROM JobConfig where id=?"
	args := make([]interface{}, 0)
	args = append(args, jobConfigId)

	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logging.Error("Failed to query with sql:", sql, ", errors:", err.Error())
		return &jobConfigModel.JobConfig{}, err
	}

	if len(data) == 0 {
		logging.Errorf("not find job config, with job serialId:%s, with job config id:%d", jobConfigId)
		return &jobConfigModel.JobConfig{}, errors.New("not find Job Config")
	}

	if len(data) > 1 {
		logging.Error("logic error, not only one jobconfig query sql:", sql)
		return &jobConfigModel.JobConfig{}, errors.New("logic error, not only one JobConfig")
	}

	result := &jobConfigModel.JobConfig{}
	err = util.ScanMapIntoStruct(result, data[0])
	if err != nil {
		logging.Error("GetJobConfigById ScanMapIntoStruct err:", err)
		return nil, err
	}
	return result, nil
}

func GetJobConfigsByStatus(jobId int64, statusArray []int) (t []*jobConfigModel.JobConfig, err error) {

	defer func() {
		if errs := recover(); errs != nil {
			logging.Errorf("GetJobConfig panic, for jobId %d, errors: %+v", jobId, errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	sql := "select * from JobConfig where jobId = ? and status in ("
	args := make([]interface{}, 0)
	args = append(args, jobId)
	for i := 0; i < len(statusArray); i++ {
		status := statusArray[i]
		sql += " ? "
		if i != len(statusArray)-1 {
			sql += ","
		}
		args = append(args, status)
	}
	sql += ")"

	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logging.Errorf("Failed to query with sql: %s, errors: %+v", sql, err)
		return []*jobConfigModel.JobConfig{}, err
	}
	if len(data) == 0 {
		logging.Errorf("Failed to get job config, with job id:%d, job config status: %+v", jobId, statusArray)
		return []*jobConfigModel.JobConfig{}, nil
	}

	result := make([]*jobConfigModel.JobConfig, 0)
	for i := 0; i < len(data); i++ {
		jc := &jobConfigModel.JobConfig{}
		err := util.ScanMapIntoStruct(jc, data[i])
		if err != nil {
			logging.Errorf("Failed to covert map into struct, with errors:%+v", err)
			return []*jobConfigModel.JobConfig{}, err
		}
		result = append(result, jc)
	}
	t = result
	return t, nil
}
