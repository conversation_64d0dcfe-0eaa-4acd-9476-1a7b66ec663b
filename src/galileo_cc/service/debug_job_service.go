package service

import (
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/model"
	"tencentcloud.com/tstream_galileo/src/common/logger"
	"errors"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/constants"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"runtime/debug"
)

func AddDebugJob(debugJob *model.DebugJob) (addDebugJobResponse *model.AddDebugJobResp, err error) {

	defer func() {
		if errs := recover(); errs != nil {
			debug.PrintStack()
			logging.Errorf("Fail to add debug job:%+v, errors:%+v",  debugJob, errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()

	addDebugJobResponse = &model.AddDebugJobResp{}
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		addDebugJobResponse.Id = tx.SaveObject(debugJob, constants.TABLE_NAME_OF_DEBUG_JOB)
		return nil
	}).Close()

	return addDebugJobResponse, nil
}

func QueryDebugJob(id int64) (queryDebugJobResp *model.QueryDebugJobResp, err error) {
	sql := "select status, returnCode, returnMsg from DebugJob where id=?"
	cnt, rowsData, err := txManager.GetQueryTemplate().DoQueryWithArgs(sql, id)
	if err != nil {
		return &model.QueryDebugJobResp{}, err
	}
	if cnt == 0 {
		return &model.QueryDebugJobResp{}, errors.New(fmt.Sprintf("id[%d] not found in db", id))
	}

	queryResult := &model.QueryDebugJobResp{}
	util.ScanMapIntoStruct(queryResult, rowsData[0])

	return queryResult, nil
}

func SaveDebugConfig(dc *model.DebugConfig, dss []*model.DebugSource) (id int64, err error) {

	defer func() {
		if errs := recover(); errs != nil {
			debug.PrintStack()
			logging.Errorf("Failed to save DebugConfig:%+v and DebugSources:%+v, errors:%+v",  dc, dss, errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()

	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		id = tx.SaveObject(dc, constants.TABLE_NAME_OF_DEBUG_CONFIG)
		for _, ds := range dss {
			ds.ConfigId = id
			tx.SaveObject(ds, constants.TABLE_NAME_OF_DEBUG_SOURCE)
		}
		return nil
	}).Close()

	return id, nil
}

func UpdateDebugConfig(debugConfigId int64, dc *model.DebugConfig, dss []*model.DebugSource) (err error) {

	defer func() {
		if errs := recover(); errs != nil {
			debug.PrintStack()
			logging.Errorf("Failed to update DebugConfig:%+v and DebugSources:%+v, errors:%+v",  dc, dss, errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()

	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		rowsAffected := tx.UpdateObjectWithLock(dc, debugConfigId, constants.TABLE_NAME_OF_DEBUG_CONFIG, true)
		if rowsAffected != 1 {
			msg := fmt.Sprintf("Failed to update DebugConfigId:%d, DebugConfig:%+v, rows affected: %d", debugConfigId, dc, rowsAffected)
			logging.Error(msg)
			return errors.New(msg)
		}
		for _, ds := range dss {
			args := make([]interface{}, 0)
			args = append(args, debugConfigId)
			args = append(args, ds.TableName)
			cnt, data, err := tx.Query("SELECT * FROM DebugSource WHERE configId=? AND tableName=?", args)
			if err != nil {
				msg := fmt.Sprintf("Failed to query DebugSource for configId:%d, tableName:%s", debugConfigId, ds.TableName)
				logging.Error(msg)
				return err
			}
			if cnt > 1 {
				msg := fmt.Sprintf("More than one DebugSource for configId: %d, tableName:%s", debugConfigId, ds.TableName)
				logging.Error(msg)
				return errors.New(msg)
			} else if cnt == 1{
				prevDS := &model.DebugSource{}
				err := util.ScanMapIntoStruct(prevDS, data[0])
				if err != nil {
					logging.Error("Failed to scan map into struct DebugSource", err.Error())
					return err
				}
				prevDS.FieldDelimiter = ds.FieldDelimiter
				prevDS.Content = ds.Content
				prevDS.InputType = ds.InputType
				prevDS.FileName = ds.FileName
				prevDS.UpdateTime = util.GetCurrentTime()
				tx.UpdateObjectWithLock(prevDS, prevDS.Id, constants.TABLE_NAME_OF_DEBUG_SOURCE, true)
			} else if cnt == 0 {
				tx.SaveObject(ds, constants.TABLE_NAME_OF_DEBUG_SOURCE)
			}
		}

		return nil
	}).Close()

	return nil
}

func GetDebugConfig(id int64) (t *model.DebugConfig, err error) {
	sql := "SELECT * FROM DebugConfig WHERE id=?"

	args := make([]interface{}, 0)
	args = append(args, id)

	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logging.Error("Failed to query with sql:", sql, ", errors:", err.Error())
		return &model.DebugConfig{}, err
	}

	if len(data) == 0 {
		logging.Errorf("not find DebugConfig:%d", id)
		return &model.DebugConfig{}, errors.New("not find DebugConfig")
	}

	if len(data) > 1 {
		logging.Errorf("logic error, not only one DebugConfig with id:%d found", id)
		return &model.DebugConfig{}, errors.New("logic error, not only one DebugConfig")
	}

	t = &model.DebugConfig{}
	util.ScanMapIntoStruct(t, data[0])

	return t, nil
}

func SearchDebugConfig(serialId string, sqlCode string) (t []*model.DebugConfig, err error) {
	sql := "SELECT * FROM DebugConfig WHERE jobSerialId=? AND sqlStatement=? ORDER BY createTime desc limit 1"

	args := make([]interface{}, 0)
	args = append(args, serialId)
	args = append(args, sqlCode)

	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logging.Error("Failed to query with sql:", sql, ", errors:", err.Error())
		return []*model.DebugConfig{}, err
	}

	t = make([]*model.DebugConfig, len(data))
	for i := 0; i < len(data); i++ {
		temp := &model.DebugConfig{}
		util.ScanMapIntoStruct(temp, data[i])
		t[i] = temp
	}

	return t, nil
}

func ListDebugSource(configId int64) (t []*model.DebugSource, err error) {
	sql := "SELECT * FROM DebugSource where configId=?"

	_, data, err := txManager.GetQueryTemplate().DoQueryWithArgs(sql, configId)
	if err != nil {
		logging.Error("Failed to query with sql:", sql, ", errors:", err.Error())
		return []*model.DebugSource{}, err
	}

	t = make([]*model.DebugSource, len(data))
	for i:= 0; i < len(data); i++ {
		temp := &model.DebugSource{}
		util.ScanMapIntoStruct(temp, data[i])
		t[i] = temp
	}

	return t, nil
}

func DeleteDebugSource(debugConfigId int64, table string) (err error) {
	defer func() {
		if errs := recover(); errs != nil {
			debug.PrintStack()
			logging.Errorf("Failed to delete DebugConfigId:%+d and TableName:%+s, errors:%+v",  debugConfigId, table, errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()

	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		sql := "DELETE FROM DebugSource where configId=? AND tableName=?"
		tx.ExecuteSqlWithArgs(sql, debugConfigId, table)
		return nil
	}).Close()

	return nil
}

/*
 * Clone DebugConfig and its DebugSources
 */
func CloneDebugConfig(dc *model.DebugConfig) (configId int64, err error) {

	defer func() {
		if errs := recover(); errs != nil {
			logging.Errorf("CloneDebugConfig panic ,for debugConfig:%+v, errors:%+v",  configId, errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()

	dc.CreateTime = util.GetCurrentTime()
	dc.UpdateTime = util.GetCurrentTime()

	var newConfigId int64
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		newConfigId = tx.SaveObject(dc, constants.TABLE_NAME_OF_DEBUG_CONFIG)
		sql := "SELECT * FROM DebugSource WHERE configId=?"
		_, data, err := tx.QueryWithArgs(sql, dc.Id)
		if err != nil {
			logging.Error("Failed to query with sql:", sql, ", errors:", err.Error())
			return err
		}

		for i := 0; i < len(data); i++ {
			debugSource := &model.DebugSource{}
			util.ScanMapIntoStruct(debugSource, data[i])
			debugSource.ConfigId = newConfigId
			debugSource.UpdateTime = util.GetCurrentTime()
			tx.SaveObject(debugSource, constants.TABLE_NAME_OF_DEBUG_SOURCE)
		}

		return nil
	}).Close()

	return newConfigId, nil
}

func ListDebugSink(debugJobId int64, table string) (t []*model.DebugSink, err error) {
	sql := "select * from DebugSink where debugJobId=?"
	args := make([]interface{}, 0)
	args = append(args, debugJobId)
	if len(table) > 0 {
		sql += " AND tableName=?"
		args = append(args, table)
	}
	_, rowsData, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return []*model.DebugSink{}, err
	}

	t = make([]*model.DebugSink, len(rowsData))
	for i:= 0; i < len(rowsData); i++ {
		temp := &model.DebugSink{}
		util.ScanMapIntoStruct(temp, rowsData[i])
		t[i] = temp
	}

	return t, nil
}