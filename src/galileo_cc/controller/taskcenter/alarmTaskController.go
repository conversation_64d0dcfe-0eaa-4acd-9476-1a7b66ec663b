package taskcenter

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	"tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/notify"
	"tencentcloud.com/tstream_galileo/src/common/notify/tof"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/controller"
	regionService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/region"
	"time"
)

func init() {
	const controllerName = "qcloud.galileo.taskcenter.alarmTask"
	if code, msg := httpserver.RegisterController(controllerName, &alarmTaskController{}); code != 0 {
		logging.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ", msg: " + msg)
	}
}

type alarmTaskController struct {
}

func (this *alarmTaskController) CreateRequestObj() interface{} {
	return &taskInfoReq{}
}

func (this *alarmTaskController) Process(req interface{}, eventId int64) (int64, string, interface{}) {
	reqData := req.(*taskInfoReq)
	region := fetchRegion(reqData.FlowId)

	n := notify.NewNotify()

	eventMsg := map[string]interface{}{
		"processKey": reqData.Processkey,
		"taskcode":   reqData.Taskcode,
		"retrycount": reqData.Retrycount,
		"flowId":     reqData.FlowId,
		"params":     reqData.Params,
	}
	tofMsg := tof.TOFMessage{
		AppId:        reqData.AppId,
		Region:       region,
		EventName:    "TStream 流程系统告警",
		Time:         time.Now().Format("2006-01-02 15:04:05"),
		EventMessage: eventMsg,
	}

	n.SyncSendMessageKafka(tofMsg)
	return controller.SUCCESS, "ok", map[string]string{}
}

func fetchRegion(flowIdStr string) (region string) {
	region = ""
	flowId, err := strconv.ParseInt(flowIdStr, 10, 64)
	if err != nil {
		logging.Errorf("strconv.ParseInt error: %+v", err)
		return
	}

	flowObj, err := flow.GetFlowById(flowId)
	if err != nil {
		logging.Errorf("GetFlowById error: %+v", err)
		return
	}
	regionId := flowObj.RegionId
	regionObj, err := regionService.GetRegionById(regionId)
	if err != nil {
		logging.Errorf("GetRegionById error: %+v", err)
		return
	}
	region = regionObj.Region

	return
}
