package cdp_admin

import (
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	"tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/controller"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service/cdp_admin"
	"tencentcloud.com/tstream_galileo/src/common/simplejson"
	"strconv"
	"strings"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job_config"
)

func init() {
	const controllerName = "qcloud.galileo.mc.getRealTimeRecords"
	if code,msg := httpserver.RegisterController(controllerName, &getRealtimeRecordsController{}); code != 0 {
		logging.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ", msg: " + msg)
	}
}

type getRealtimeRecordsReq struct {
	AppId     int64    `check:"nullable:false|range:(0,INF)"`
	SerialId  string   `check:"nullable:false|strlen:(0,50)"`
	Total     int      `check:"nullable:false|range:(0,1000)"`
	Times     int      `check:"nullable:false|range:(0,INF)"`
	TableName string   `check:"nullable:false|strlen:(0,50)"`
	Offset 	  int64    `check:"nullable:true|range:[-2,INF)"`
}

type getRealtimeRecordsResp struct {
	Data      []simplejson.OrderedMap    `json:"data"`
	Count     int                        `json:"count"`
	Offset    int64                      `json:"offset"`
	OriginOffset int64                   `json:"originOffset"`
}

type getRealtimeRecordsController struct {
}

func (this* getRealtimeRecordsController) CreateRequestObj() interface{} {
	return &getRealtimeRecordsReq{}
}

func (this* getRealtimeRecordsController) Process(req interface{}, eventId int64)(int64, string, interface{}){
	reqData := req.(*getRealtimeRecordsReq)
	jobInfoList, err := service.ListProjectJobBySerialId(reqData.AppId, reqData.SerialId)
	if err != nil {
		logging.Errorf("Failed to get job, with job serialId : %s, error: %+v", reqData.SerialId, err)
		return controller.ERROR_CODE_JOB_QUERY_FAILURE, err.Error(), nil
	}
	if len(jobInfoList) == 0 {
		logging.Errorf("Failed to find job, with job serialId: %s, appId: %d", reqData.SerialId, reqData.AppId)
		return controller.ERROR_CODE_JOB_NO_QUERY_RESULT, "Job not found", nil
	}
	jobInfo := jobInfoList[0]
	if jobInfo.Status != constants.JOB_STATUS_RUNNING {
		logging.Errorf("The job is not running, with job id: %d", jobInfo.Id)
		return controller.ERROR_CODE_JOB_NOT_RUNNING, "Job not running" , nil
	}
	jobConfig, err := service.GetJobConfigById(jobInfo.PublishedJobConfigId)
	if err != nil {
		logging.Errorf("Failed to get progress jobconfig, with job id :%d, error: %+v", jobInfo.Id, err)
		return controller.ERROR_CODE_JOB_CONFIG_QUERY_FAILURE, err.Error(), nil
	}
	proRegionId, err := service.GetRegionIdByName(jobInfo.Region)
	if err != nil {
		logging.Errorf("Failed to get region id, with region name:%s, with errors:%+v", jobInfo.Region, err)
		return controller.ERROR_CODE_REGION_QUERY_FAILURE, err.Error(), nil
	}
	sqlCode, err := service2.DeEncodeAndDeEncryptSqlCode(jobConfig.SqlCode)
	if err != nil {
		logging.Errorf("Failed to DeEncodeAndDeEncryptSqlCode, sqlCode:%s, error: %+v", jobConfig.SqlCode ,err)
		return controller.SYSERR, err.Error(), nil
	}
	sourceAndSinkTables, err := service.GetTableDDL(jobConfig.Id, sqlCode, proRegionId)
	if err != nil {
		logging.Errorf("Failed to get table ddl by jobconfig id: %d, errors: %+v", jobConfig.Id, err)
		return controller.ERROR_CODE_DDL_PARSE_FAILED, "table ddl parse failed", nil
	}
	var topicName string
	var projectName string
	var dataSourceOrSinkType string
	var ckafkaInstanceId string
	var ckafkaEncoding string
	var ckafkaFieldDelimiter string

	tableFields := []string{}
	for i:=0; i < len(sourceAndSinkTables);i ++ {
		tableTmp := sourceAndSinkTables[i]
		if strings.ToLower(tableTmp.Name) == strings.ToLower(reqData.TableName) {
			topicName = tableTmp.WithParams["topic"]
			projectName  = tableTmp.WithParams["project"]
			dataSourceOrSinkType = tableTmp.WithParams["type"]
			ckafkaEncoding = tableTmp.WithParams["encoding"]
			ckafkaFieldDelimiter = tableTmp.WithParams["fielddelimiter"]
			ckafkaInstanceId = tableTmp.WithParams["instanceid"]
			for _, key:= range tableTmp.Fields.Keys() {
				tableFields = append(tableFields, key)
			}
			break
		} else {
			continue
		}
	}
	cdpRecordsResp := &getRealtimeRecordsResp{}
	defaultConsumeTimeout := 3000
	defaultPartitionNum := 0  // FIXME: 这里有问题
	if returnEmptyResult(dataSourceOrSinkType) {
		cdpRecordsResp.Count = 0
		cdpRecordsResp.Data = []simplejson.OrderedMap{}
		cdpRecordsResp.Offset = -1
		cdpRecordsResp.OriginOffset = reqData.Offset
		return controller.SUCCESS,  "ok", cdpRecordsResp
	}  else if "ckafka" == strings.ToLower(dataSourceOrSinkType) {
		if topicName == "" {
			logging.Errorf("Failed to get topicName from ddl, with is empty")
			return  controller.ERROR_CODE_CKAFKA_QUERY_FAILURE, "No topic name",nil
		}
		records,newOffset, err := service.GetCkafkaRecordsByOffset(proRegionId, ckafkaInstanceId, topicName, defaultConsumeTimeout, defaultPartitionNum, reqData.Offset,
			reqData.Total,ckafkaEncoding, ckafkaFieldDelimiter,tableFields,reqData.Times, reqData.AppId)
		if err != nil {
			logging.Errorf("Failed to get msg from ckafka, with topicName:%s, offsetPos: %d, default consume timeout:%d", topicName, reqData.Offset,
				defaultConsumeTimeout)
			return controller.ERROR_CODE_CKAFKA_QUERY_FAILURE, "ckafka msg consume failed",nil
		}
		cdpRecordsResp.Count = len(records)
		cdpRecordsResp.Data = records
		cdpRecordsResp.Offset = newOffset
		cdpRecordsResp.OriginOffset = reqData.Offset
		return controller.SUCCESS, "ok", cdpRecordsResp

	} else {
		if topicName == "" || projectName == "" {
			logging.Errorf("Failed to topicName or projectName from ddl, which is empty,table name %s, project name %s", topicName, projectName)
			return controller.ERROR_CODE_CDP_TOPIC_PROJECT_NOT_FOUND, "topic name or project name is empty", nil
		}

		if reqData.Offset < -1 {
			logging.Errorf("Failed to get msg from cdp, as the offset: %d is below -1", reqData.Offset)
			return controller.ERROR_CODE_CDP_MSG_CONSUME_FAILED, "cdp msg consume failed", nil
		}
		records, newOffset, err := cdp_admin.GetRecordsFromCdp(reqData.AppId, proRegionId, projectName, topicName, defaultPartitionNum, reqData.Offset, reqData.Total,
			defaultConsumeTimeout, reqData.Times, tableFields)
		if err != nil {
			logging.Errorf("Failed to get msg from cdp, with topicName:%s, offsetPos: %d, default consume timeout: %d", topicName, reqData.Offset, defaultConsumeTimeout)
			return controller.ERROR_CODE_CDP_MSG_CONSUME_FAILED, "cdp msg consume failed", nil
		}
		cdpRecordsResp.Count = len(records)
		cdpRecordsResp.Data = records
		cdpRecordsResp.Offset = newOffset
		cdpRecordsResp.OriginOffset = reqData.Offset
		return controller.SUCCESS, "ok", cdpRecordsResp
	}
}

func returnEmptyResult(resultType string) bool {
	switch strings.ToLower(resultType) {
		case "cdb", "mysql":
			return true
		default:
			return false
	}
}