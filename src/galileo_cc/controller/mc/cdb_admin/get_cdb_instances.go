package cdb_admin

import (
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	"tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/controller"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/model/cdb_admin"
	cdbService "tencentcloud.com/tstream_galileo/src/galileo_cc/service/cdb_admin"
	//cdpService "tencentcloud.com/tstream_galileo/src/galileo_cc/service/cdp_admin"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
)

func init() {
	const controllerName = "qcloud.galileo.mc.getCdbInstances"
	if code, msg := httpserver.RegisterController(controllerName, &getCdbInstancesController{}); code != 0 {
		logging.Error("Failed to register controller " + controllerName + ", code: " + service.Itoa(int64(code)) + ", msg: " + msg)
	}
}

type getCdbInstancesController struct {
}

func (this *getCdbInstancesController) CreateRequestObj() interface{} {
	return &cdb_admin.CdbAdminInstancesRequest{}
}

func (this *getCdbInstancesController) Process(req interface{}, eventId int64) (int64, string, interface{}) {
	reqData := req.(*cdb_admin.CdbAdminInstancesRequest)
	result,err := cdbService.GetcdbInstances(reqData.AppID, reqData.RegionId)
	if err != nil {
		logging.Errorf("Failed to get cdb projects ,with appId:%d, regionId:%d, errors:%+v", reqData.AppID, reqData.RegionId, err)
		return controller.ERROR_CODE_CDB_QUERY_FAILURE, err.Error(), nil
	}

	return controller.SUCCESS, "ok", result
}
