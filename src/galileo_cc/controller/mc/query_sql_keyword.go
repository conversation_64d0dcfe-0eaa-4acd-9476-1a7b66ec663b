package mc

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/controller"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/interface_auth"
)

func init() {
	const controllerName = "qcloud.galileo.mc.querySqlKeyword"
	if code, msg := httpserver.RegisterController(controllerName, &querySqlKeywordController{}); code != 0 {
		logger.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
	interface_auth.RegisterControllerNameMap(controllerName, "DescribeSqlKeyword")
}

type querySqlKeywordReq struct {
	Keyword string `check:"nullable:true|strlen:[0, 50)"`

	AppId         int64  `json:"AppId"`
	Uin           string `json:"Uin"`
	SubAccountUin string `json:"SubAccountUin"`
}

type querySqlKeywordController struct {
}

func (this *querySqlKeywordController) CreateRequestObj() interface{} {
	return &querySqlKeywordReq{}
}

func (this *querySqlKeywordController) Process(req interface{}, eventId int64) (int64, string, interface{}) {

	reqData := req.(*querySqlKeywordReq)

	list, err := service.QuerySqlKeyword(reqData.Keyword)

	if err != nil {
		logger.Error("Failed to querySqlKeyword, Keyword:", reqData.Keyword, ",errors: ", err.Error())
		return controller.SYSERR, err.Error(), nil
	}

	return controller.SUCCESS, "ok", list
}
