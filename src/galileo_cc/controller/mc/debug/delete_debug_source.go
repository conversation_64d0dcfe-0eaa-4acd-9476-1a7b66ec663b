package debug

import (
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/controller"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
)

func init() {
	const controllerName = "qcloud.galileo.mc.deleteDebugSource"
	if code, msg := httpserver.RegisterController(controllerName, &deleteDebugSourceController{}); code != 0 {
		logging.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type deleteDebugSourceReq struct {
	AppId         	int64  	`check:"nullable:false|range:(0, INF)"`
	JobSerialId		string	`check:"nullable:false|strlen:[12, 12]"`
	DebugConfigId   int64 	`check:"nullable:false|range:(0, INF)"`
	Table   		string	`check:"nullable:false|strlen:(0, 256)"`
}

type deleteDebugSourceController struct {
}

func (this *deleteDebugSourceController) CreateRequestObj() interface{} {
	return &deleteDebugSourceReq{}
}

func (this *deleteDebugSourceController) Process(req interface{}, eventId int64) (int64, string, interface{}) {
	logging.Info("enter deleteDebugSourceController.Process")

	reqData := req.(*deleteDebugSourceReq)
	data, err := service.ListProjectJobBySerialId(reqData.AppId, reqData.JobSerialId)
	if err != nil {
		logging.Errorf("Failed to get project job, with appId:%d, serialId:%s, errors:%+v", reqData.AppId, reqData.JobSerialId, err)
		return controller.ERROR_CODE_JOB_QUERY_FAILURE, err.Error(),nil
	}
	if len(data) == 0 {
		logging.Errorf("No job found for AppId: %d, serialId:%s , perhaps no permission", reqData.AppId, reqData.JobSerialId)
		return controller.ERROR_CODE_NO_PERMISSION_TO_OPERATE, "No Permission", nil
	}
	err = service.DeleteDebugSource(reqData.DebugConfigId, reqData.Table)
	if err != nil {
		logging.Errorf("Failed to delete DebugConfig with id: %+d, TableName: %+s, errors: %+s", reqData.DebugConfigId, reqData.Table, err.Error())
		return controller.ERROR_CODE_DEBUG_SOURCE_DELETE_FAILURE, err.Error(), nil
	}

	return controller.SUCCESS, "ok", nil
}