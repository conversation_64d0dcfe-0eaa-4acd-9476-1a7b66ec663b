package debug

import (
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/controller"
	log "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/model"
	"tencentcloud.com/tstream_galileo/src/common/util"
)

func init() {
	const controllerName = "qcloud.galileo.mc.upsertDebugConfig"
	if code, msg := httpserver.RegisterController(controllerName, &upsertDebugConfigController{}); code != 0 {
		logging.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type upsertDebugConfigReq struct {
	AppId            	int64      		`check:"nullable:false|range:(0, INF)"`
	DebugConfigId 		int64
	JobSerialId			string			`check:"nullable:false|strlen:[12, 12]"`
	SqlCode				string			`check:"nullable:true|strlen:(0, 5120)"`
	SourceTableParams 	[]*SourceTable
}

type SourceTable struct {
	TableName 		string		`check:"nullable:false|strlen:(0, 100)"`
	FieldDelimiter 	string		`check:"nullable:false|strlen:(0, 12)"`
	InputType 		string		`check:"nullable:false|enum: manual,file"`
	Content			string		`check:"nullable:false|strlen:(0, 5120)"`
	FileName		string 		`check:"nullable:true|strlen:(0, 200)"`
}

type upsertDebugConfigController struct {
}

func (this *upsertDebugConfigController) CreateRequestObj() interface{} {
	return &upsertDebugConfigReq{}
}

func (this *upsertDebugConfigController) Process(req interface{}, eventId int64) (int64, string, interface{}) {
	logging.Info("enter upsertDebugConfigController.Process")

	reqData := req.(*upsertDebugConfigReq)

	// check if job exists
	job, err := service.GetJobBySerialId(reqData.AppId, reqData.JobSerialId)
	if err != nil {
		log.Error("Failed to get job with JobSerialId:", reqData.JobSerialId, ",errors: ", err.Error())
		return controller.ERROR_CODE_JOB_QUERY_FAILURE, err.Error(), nil
	}
	if job.Id == 0 {
		log.Errorf("Not find the job, with appid:%d, with serialId:%d", reqData.AppId,reqData.JobSerialId)
		return controller.ERROR_CODE_NO_PERMISSION_TO_OPERATE, "No Permission",nil
	}

	debugConfig := &model.DebugConfig{}
	debugConfig.JobSerialId = reqData.JobSerialId
	debugConfig.CreateTime = util.GetCurrentTime()
	debugConfig.UpdateTime = util.GetCurrentTime()
	if reqData.DebugConfigId > 0 {
		debugConfig, err = service.GetDebugConfig(reqData.DebugConfigId)
		if err != nil {
			logging.Error("Failed to get DebugConfig with DebugConfigId:", reqData.DebugConfigId, ",errors: ", err.Error())
			return controller.ERROR_CODE_DEBUG_CONFIG_QUERY_FAILURE, err.Error(), nil
		}
	}
	if len(reqData.SqlCode) > 0 {
		debugConfig.SqlStatement = reqData.SqlCode
	}

	debugSources := make([]*model.DebugSource, 0, len(reqData.SourceTableParams))
	for i := 0; i < len(reqData.SourceTableParams); i++ {
		st := reqData.SourceTableParams[i]
		tempDS := &model.DebugSource{}
		tempDS.ConfigId = reqData.DebugConfigId
		tempDS.TableName = st.TableName
		tempDS.FieldDelimiter = st.FieldDelimiter
		tempDS.InputType = st.InputType
		tempDS.Content = st.Content
		tempDS.FileName = st.FileName
		tempDS.UpdateTime = util.GetCurrentTime()
		debugSources = append(debugSources, tempDS)
	}

	rsp := &model.UpsertDebugConfigRsp{}
	if reqData.DebugConfigId == 0 {
		id, err := service.SaveDebugConfig(debugConfig, debugSources)
		if err != nil {
			log.Error("Failed to save DebugConfig and DebugSources, errors: ", err.Error())
			return controller.ERROR_CODE_DEBUG_JOB_CONFIG_SAVE_FAILURE, err.Error(), nil
		}
		rsp.Id = id
	} else {
		debugConfig.UpdateTime = util.GetCurrentTime()
		err = service.UpdateDebugConfig(reqData.DebugConfigId, debugConfig, debugSources)
		if err != nil {
			log.Error("Failed to update DebugConfig and DebugSources, errors: ", err.Error())
			return controller.ERROR_CODE_DEBUG_JOB_CONFIG_UPDATE_FAILURE, err.Error(), nil
		}
		rsp.Id = reqData.DebugConfigId
	}

	return controller.SUCCESS, "ok", rsp
}
