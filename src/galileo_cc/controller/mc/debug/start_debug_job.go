package debug

import (
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	"tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/constants"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/controller"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/model"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"strconv"
)

func init() {
	const controllerName = "qcloud.galileo.mc.startDebugJob"
	if code, msg := httpserver.RegisterController(controllerName, &addDebugJobController{}); code != 0 {
		logging.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type addDebugJobReq struct {
	AppId         int64  `check:"nullable:false|range:(0, INF)"`
	JobSerialId   string `check:"nullable:false|strlen:[12, 12]"`
	DebugConfigId int64  `check:"nullable:false|range:(0, INF)"`
}

type addDebugJobController struct {
}

func (this *addDebugJobController) CreateRequestObj() interface{} {
	return &addDebugJobReq{}
}

func (this *addDebugJobController) Process(req interface{}, eventId int64) (int64, string, interface{}) {
	logging.Info("enter addDebugJobController.Process")

	reqData := req.(*addDebugJobReq)

	job, err := service.GetJobBySerialId(reqData.AppId, reqData.JobSerialId)
	if err != nil {
		logging.Error("Failed to get Job with JobId:", reqData.JobSerialId, ",errors: ", err.Error())
		return controller.ERROR_CODE_JOB_QUERY_FAILURE, err.Error(), nil
	}
	if job.Id == 0{
		logging.Errorf("Not find the job, with appId:%d, serialId:%s", reqData.AppId, reqData.JobSerialId)
		return controller.ERROR_CODE_NO_PERMISSION_TO_OPERATE, "No Permission",nil
	}

	_, err = service.GetDebugConfig(reqData.DebugConfigId)
	if err != nil {
		logging.Error("Failed to get DebugConfig with DebugConfigId:", reqData.DebugConfigId, ",errors: ", err.Error())
		return controller.ERROR_CODE_DEBUG_CONFIG_QUERY_FAILURE, err.Error(), nil
	}

	debugJob := &model.DebugJob{}
	debugJob.ConfigId = reqData.DebugConfigId
	debugJob.CreateTime = util.GetCurrentTime()
	debugJob.Status = constants.DEBUG_JOB_STATUS_SUBMIT
	debugJob.UpdateTime = util.GetCurrentTime()
	debugJob.AttemptId = 0
	debugJob.ReturnMsg = ""

	addDebugJobResponse, err := service.AddDebugJob(debugJob)
	if err != nil {
		logging.Errorf("Failed to add debug job: %+v", reqData, ", errors: %+v", err)
		return controller.ERROR_CODE_DEBUG_JOB_SUBMIT_FAILURE, err.Error(), nil
	}

	return controller.SUCCESS, "ok", addDebugJobResponse
}