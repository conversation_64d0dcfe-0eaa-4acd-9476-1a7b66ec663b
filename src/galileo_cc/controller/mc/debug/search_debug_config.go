package debug

import (
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/controller"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
)

func init() {
	const controllerName = "qcloud.galileo.mc.searchDebugConfig"
	if code, msg := httpserver.RegisterController(controllerName, &searchDebugConfigController{}); code != 0 {
		logging.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type searchDebugConfigReq struct {
	AppId            	        int64      		`check:"nullable:false|range:(0, INF)"`
	JobSerialId			string			`check:"nullable:false|strlen:[12, 12]"`
	SqlCode				string			`check:"nullable:false|strlen:(0, 5120)"`
}

type searchDebugConfigRsp struct {
	Id            	int64
}

type searchDebugConfigController struct {
}

func (this *searchDebugConfigController) CreateRequestObj() interface{} {
	return &searchDebugConfigReq{}
}

func (this *searchDebugConfigController) Process(req interface{}, eventId int64) (int64, string, interface{}) {
	logging.Info("enter searchDebugConfigController.Process")

	reqData := req.(*searchDebugConfigReq)
	job, err :=service.GetJobBySerialId(reqData.AppId,reqData.JobSerialId)
	if err != nil {
		logging.Errorf("Failed to query job, with appId:%d, with serialId:%s, with errors:%+v", reqData.AppId, reqData.JobSerialId, err)
		return controller.ERROR_CODE_JOB_QUERY_FAILURE, err.Error(),nil
	}
	if job.Id == 0 {
		logging.Errorf("Not find the job ,with appid:%d, with serialId:%s", reqData.AppId, reqData.JobSerialId)
		return controller.ERROR_CODE_JOB_NO_QUERY_RESULT, "Not find the job",nil
	}

	list, err := service.SearchDebugConfig(reqData.JobSerialId, reqData.SqlCode)
	if err != nil {
		logging.Errorf("Failed to query debug job: %+v", reqData, ", errors: %+v", err)
		return controller.ERROR_CODE_DEBUG_CONFIG_QUERY_FAILURE, err.Error(), nil
	}

	rsp := &searchDebugConfigRsp{}
	if len(list) == 0 {
		rsp.Id = 0
	} else {
		rsp.Id = list[0].Id
	}

	return controller.SUCCESS, "ok", rsp
}

