package ckafka

import (
	"tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/controller"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/model/ckafka"
)

func init() {
	const controllerName = "qcloud.galileo.mc.getCkafkaInstances"
	if code, msg := httpserver.RegisterController(controllerName, &getCkafkaInstancesController{}); code != 0 {
		logging.Error("Failed to register controller " + controllerName + ", code: " + service.Itoa(int64(code)) + ", msg: " + msg)
	}
}

type getCkafkaInstancesController struct {
}

func (this *getCkafkaInstancesController) CreateRequestObj() interface{} {
	return &ckafka.CkafkaInstancesRequest{}
}

func (this *getCkafkaInstancesController) Process(req interface{}, eventId int64) (int64, string, interface{}) {
	reqData := req.(*ckafka.CkafkaInstancesRequest)
	instances,err := service.GetCkafkaInstances(reqData.RegionId, reqData.AppID)
	if err != nil {
		logging.Errorf("Failed to get ckafka instances with appId:%d regionId:%d, errors:%+v", reqData.AppID, reqData.RegionId, err)
		return controller.ERROR_CODE_CKAFKA_QUERY_FAILURE, err.Error(), nil
	}
	totalCount := len(instances)
	filterResult := &ckafka.CkafkaInstancesResponse{}
	filterResult.Data.InstanceList = make([]struct{
		InstanceId string `json:"instanceId"`
		InstanceName string `json:"instanceName"`
		Status int `json:"status"`
		IfCommunity bool `json:"ifCommunity"`
	}, 0)
	filterResult.Data.TotalCount = totalCount
	for i:=0; i< totalCount; i++ {
		m := instances[i]
		t := struct {
			InstanceId string `json:"instanceId"`
			InstanceName string `json:"instanceName"`
			Status int `json:"status"`
			IfCommunity bool `json:"ifCommunity"`
		}{InstanceId:m["instanceId"].(string), InstanceName:m["instanceName"].(string), Status:int(m["status"].(float64)), IfCommunity:m["ifCommunity"].(bool)}
		filterResult.Data.InstanceList = append(filterResult.Data.InstanceList, t)
	}
	return controller.SUCCESS, "ok", filterResult.Data
}