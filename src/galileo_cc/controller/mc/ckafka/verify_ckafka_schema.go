package ckafka

import (
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	"tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/model/ckafka"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/controller"
	"strconv"
	"strings"
	"github.com/juju/errors"
	"tencentcloud.com/tstream_galileo/src/component"
	"encoding/json"
)

func init() {
	const controllerName = "qcloud.galileo.mc.verifyCkafkaSchema"
	if code, msg := httpserver.RegisterController(controllerName, &verifyCkafkaSchemaController{}); code != 0 {
		logging.Error("Failed to register controller " + controllerName + ", code: " + service.Itoa(int64(code)) + ", msg: " + msg)
	}
}

type verifyCkafkaSchemaController struct {}

func (this *verifyCkafkaSchemaController) CreateRequestObj() interface{} {
	return &ckafka.CkafkaSchemaRequest{}
}

func (this *verifyCkafkaSchemaController) Process(req interface{}, eventId int64) (int64, string, interface{}) {
	reqData := req.(*ckafka.CkafkaSchemaRequest)
	// cluster-admin部署到vpc环境，需要给支撑环境的ckafka bootstrapServer申请一个vpc可访问的vip
	var err = errors.New("")
	cgw := component.NewComponentAccount(reqData.RegionId)
	ownerUin, err := cgw.GetOwnerUinByAppId(reqData.AppID)
	//server, err = service.GetCkafkaVpcVip(reqData.RegionId, reqData.InstanceId, reqData.AppID, ownerUin)
	if err != nil {
		logging.Errorf("Failed to get ckafka bootstrapServer's vip with appId:%d regionId:%d instanceId: %s topicName :%s, errors:%+v",
			reqData.AppID, reqData.RegionId, reqData.InstanceId, reqData.TopicName, err)
		return controller.ERROR_CODE_CKAFKA_RECORD_QUERY_FAILURE, err.Error(), nil
	}

	// consume one record
	record, err := service.GetCkafkaRecord(reqData.RegionId, reqData.InstanceId, reqData.TopicName, ownerUin, reqData.AppID)
	if err != nil {
		logging.Errorf("Failed to get ckafka record with appId:%d regionId:%d instanceId: %s topicName :%s, errors:%+v",
			reqData.AppID, reqData.RegionId, reqData.InstanceId, reqData.TopicName, err)
		return controller.ERROR_CODE_CKAFKA_RECORD_QUERY_FAILURE, err.Error(), nil
	}
	retMap := map[string]interface{}{}
	json.Unmarshal([]byte(record), &retMap)
	recordData := retMap["data"]
	if recordData == "" || recordData == nil {
		msg := "no record in ckafka " + reqData.InstanceId + ":" + reqData.TopicName
		return controller.ERROR_CODE_CKAFKA_SCHEMA_VERIFY_NO_DATA_FAILURE, msg, nil
	}
	logging.Infof("ckafka record data:%s", recordData)
	// verify schema
	var cols []string
	if reqData.Delimiter == "" {
		cols = append(cols, recordData.(string))
	} else {
		cols = strings.Split(recordData.(string), reqData.Delimiter)
	}
	if len(cols) != len(reqData.Schema) {
		msg := "schema's col num " + strconv.Itoa(len(reqData.Schema)) + " not equals to ckafka record's col num " + strconv.Itoa(len(cols))
		return controller.ERROR_CODE_CKAFKA_SCHEMA_VERIFY_COL_NUM_FAILURE, msg, nil
	}
	for i := 0; i < len(reqData.Schema); i++ {
		if reqData.Schema[i] == "DOUBLE" {
			_, err := strconv.ParseFloat(cols[i], 64)
			if err != nil {
				msg := cols[i] + " is not double"
				return controller.ERROR_CODE_CKAFKA_SCHEMA_VERIFY_NOT_DOUBLE_FAILURE, msg, nil
			}
		} else if reqData.Schema[i] == "TIMESTAMP" {
			_, err := strconv.ParseInt(cols[i], 10, 64)
			if err != nil {
				msg := cols[i] + " is not timestamp"
				return controller.ERROR_CODE_CKAFKA_SCHEMA_VERIFY_NOT_TIMESTAMP_FAILURE, msg, nil
			}
		} else if reqData.Schema[i] == "BOOLEAN" {
			_, err := strconv.ParseBool(cols[i])
			if err != nil {
				msg := cols[i] + " is not boolean"
				return controller.ERROR_CODE_CKAFKA_SCHEMA_VERIFY_NOT_BOOLEAN_FAILURE, msg, nil
			}
		}
	}

	return controller.SUCCESS, "ok", "succ"
}