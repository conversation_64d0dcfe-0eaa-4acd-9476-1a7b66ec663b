package tca

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/controller"
	mtca "tencentcloud.com/tstream_galileo/src/galileo_cc/model/tca"
)

func init() {
	const controllerName = "qcloud.galileo.tca.pullCommands"
	if code, msg := httpserver.RegisterController(controllerName, &getCommandsController{}); code != 0 {
		logger.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type getCommandsReq struct {
	ClusterGroupId int64 `check:"nullable:false|range:(0, INF)"`
	BatchSize      int   `check:"nullable:false|range:(0, INF)"`
}

type getCommandsRsp struct {
	Commands []*mtca.Command
}

type getCommandsController struct {
}

func (this *getCommandsController) CreateRequestObj() interface{} {
	return &getCommandsReq{}
}

func (this *getCommandsController) Process(req interface{}, eventId int64) (int64, string, interface{}) {
	//reqData := req.(*getCommandsReq)
	//commands, err := stca.GetCommands(reqData.ClusterGroupId, reqData.BatchSize, tca.TCA_COMMAND_CREATE)
	//if err != nil {
	//	logger.Errorf("Failed to get commands, with req:%+v, with errors:%+v", reqData, err)
	//	return controller.SYSERR, err.Error(), nil
	//}
	resp := getCommandsRsp{}
	//resp.Commands = commands

	return controller.SUCCESS, "ok", resp
}

func (this *getCommandsController) Process2(req interface{}, eventId int64) (string, string, interface{}) {
	return controller.OK, controller.NULL, nil
}
