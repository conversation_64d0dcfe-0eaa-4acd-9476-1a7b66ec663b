package ckafka

type CkafkaTopicsRequest struct {
	AppID        int64 `json:"appId"`
	RegionId	 int   `json:"regionId"`
	InstanceId string `json:"instanceId"`
}

type ExtendedCkafkaTopicsRequest struct {
	Interface struct{
		InterfaceName string `json:"interfaceName"`
		Para struct{
			AppId int64 `json:"appId"`
			InstanceId string `json:"instanceId"`
			Limit int `json:"limit"`
			Offset int `json:"offset"`
		} `json:"para"`
	} `json:"interface"`
}