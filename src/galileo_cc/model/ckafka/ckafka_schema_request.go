package ckafka

type CkafkaSchemaRequest struct {
	AppID        int64 `json:"appId"`
	RegionId	 int   `json:"regionId"`
	InstanceId string `json:"instanceId"`
	TopicName string `json:"topicName"`
	Delimiter string `json:"delimiter"`
	Schema []string `json:"schema"`
}

type ExtendedCkafkaSchemaRequest struct {
	Interface struct{
		InterfaceName string `json:"interfaceName"`
		Para struct{
			AppId int64 `json:"appId"`
			InstanceId string `json:"instanceId"`
		} `json:"para"`
	} `json:"interface"`
}

type AccountRequest struct {
	Interface struct{
		InterfaceName string `json:"interfaceName"`
		Para struct{
			AppId int64 `json:"appid"`
		} `json:"para"`
	} `json:"interface"`
}

type CkafkaRouteRequest struct {
	Interface struct{
		InterfaceName string `json:"interfaceName"`
		Para struct{
			OwnerUin string `json:"ownerUin"`
			Uin string `json:"uin"`
			AppId int64 `json:"appId"`
			InstanceId string `json:"instanceId"`
			VipType int `json:"vipType"`
		} `json:"para"`
	} `json:"interface"`
}

type CkafkaConsumeRequest struct {
	Version   string   `json:"version"`
	Caller    string   `json:"caller"`
	Username  string   `json:"username"`
	Password  string   `json:"password"`
	Callee    string   `json:"callee"`
	EventID   int64    `json:"eventId"`
	Timestamp int64    `json:"timestamp"`
	Interface struct {
		InterfaceName string `json:"interfaceName"`
		Para          struct {
			Topic        string `json:"topic"`
			Server string `json:"server"`
		} `json:"para"`
	} `json:"interface"`
}