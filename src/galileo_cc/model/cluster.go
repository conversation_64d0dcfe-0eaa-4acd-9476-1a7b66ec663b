package model

type ClusterGroup struct {
	Id 				int64
	AppId			int64
	Name 			string
	RegionId 		int64
	ZoneId			int64
	ShareMode 		string
	Status			string
	Remark			string
	CreateTime		string
	UpdateTime		string
}

type Cluster struct {
	Id        		int64
	ClusterGroupId 	int64
	VpcId			string
	SubnetId		string
	ClusterType		string
	Status			string
	Remark 			string
	CreateTime		string
	UpdateTime		string
}

type ClusterMetric struct {
	Id 				int64
	ClusterId 		int64
	VcoreUsed 		int64
	VcoreTotal 		int64
	MemoryUsed 		int64
	MemoryTotal 	int64
	DiskCapacity 	int64
	DiskUsed 		int64
	CreateTime 		string
}