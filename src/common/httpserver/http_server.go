package httpserver

import (
	"net/http"
	log "tencentcloud.com/tstream_galileo/src/common/logger"
)

type sharkHttpServer struct {
	serverName string
	addr       string
	filters    []ChainFilter
	httpServer *http.Server
	shutdown   bool
}

type requestHandler struct {
	server *sharkHttpServer
}

func (this requestHandler) ServeHTTP(write http.ResponseWriter, request *http.Request) {
	for _, filter := range this.server.filters {
		if filter == nil {
			log.Info("execute httpFilter error filter is nil")
			continue
		}
		//log.Debug("execute httpFilter ,name:" + filter.GetName())
		result, err := filter.DoFilter(write, request) // 这里是云 API 等 HTTP 请求的入口
		if err != nil {
			log.Error("execute httpFilter, name:" + filter.GetName() + ", return err exit ServeHTTP, err:" + err.Error())
		}
		if !result {
			log.Debug("execute httpFilter, name:" + filter.GetName() + ", return false exit ServeHTTP")
			return
		}
	}
}

func (this *sharkHttpServer) InitServer(params map[string]string) error {

	defaultAddr := ":9898"
	if params != nil {
		configAddr, ok := params["http.server.addr"]
		if ok {
			defaultAddr = configAddr
		}
	}
	this.addr = defaultAddr
	this.serverName = "sharkHttpServer"
	this.filters = make([]ChainFilter, 0, 0)
	this.filters = append(this.filters, NewRouterChainFilter())

	this.httpServer = &http.Server{}
	this.httpServer.Addr = this.addr
	this.httpServer.Handler = &requestHandler{server: this}
	this.shutdown = true
	return nil
}

func (this *sharkHttpServer) Start() error {
	this.shutdown = false
	err := this.httpServer.ListenAndServe()
	return err
}

func (this *sharkHttpServer) ShutDown() error {
	this.shutdown = true
	return nil
}

func NewSharkServer() *sharkHttpServer {
	server := &sharkHttpServer{}
	return server
}
