/**
* 业务处理器管理器， 新建controller时， 调用RegController注册函数， 框架通过GetController获取controller的创建器
* 这里管理的是创建Controller的回调函数， 每个请求到达时，创建一个controller对象，处理后续的请求， 这样controller可以有自己的成员变量
 */
package httpserver

import (
	"context"
	"fmt"
	model2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job"

	log "tencentcloud.com/tstream_galileo/src/common/logger"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/metadata"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/sql"
)

var cloudControllerMap = map[string]CloudController{}

var cloudControllerV2Map = map[string]CloudControllerV2{}

// cloudControllerWrapper 同时兼容v1/v2
type cloudControllerWrapper struct {
	controller   CloudController
	controllerV2 CloudControllerV2
}

// NewCloudControllerWrapper 根据接口名创建wrapper, 如果没有匹配的实现返回error,
func NewCloudControllerWrapper(name string) (CloudControllerV2, error) {
	c1 := getCloudController(name)
	c2 := getCloudControllerV2(name)
	if c1 == nil && c2 == nil {
		return nil, fmt.Errorf("not found cloud controller with name %s", name)
	}
	return &cloudControllerWrapper{
		controller:   c1,
		controllerV2: c2,
	}, nil
}

func (w *cloudControllerWrapper) CreateRequestObj() interface{} {
	if w.controllerV2 != nil {
		return w.controllerV2.CreateRequestObj()
	}
	return w.controller.CreateRequestObj()
}

func (w *cloudControllerWrapper) Process(ctx context.Context, req interface{},
	eventId int64) (cloudApiErrorCode string, msg string, rsp interface{}) {
	if w.controllerV2 != nil {
		return w.controllerV2.Process(ctx, req, eventId)
	}
	return w.controller.Process(req, eventId)
}

func RegisterCloudController(ifName string, cloudController CloudController) (errno int8, msg string) {

	if len(ifName) == 0 {
		return -1, "interface name is empty"
	}

	if cloudController == nil {
		return -2, "No cloudController create functor exists"
	}

	if _, exists := controllerMap[ifName]; exists {
		return -3, "CloudController create functor exists for interface: " + ifName
	}
	log.Info("Register CloudController:" + ifName)
	cloudControllerMap[ifName] = cloudController
	return 0, "OK"
}

func getCloudController(name string) CloudController {
	return cloudControllerMap[name]
}

// RegisterCloudControllerV2 register cloud controller v2 (with context)
func RegisterCloudControllerV2(name string, cloudController CloudControllerV2) error {
	if name == "" {
		return nil
	}
	if _, ok := cloudControllerV2Map[name]; ok {
		return fmt.Errorf("duplicate cloudController name:%s", name)
	}
	log.Info("Register CloudControllerV2:" + name)
	cloudControllerV2Map[name] = cloudController
	return nil
}

// GetCloudControllerV2 get cloud controller v2 (with context)
func getCloudControllerV2(name string) CloudControllerV2 {
	return cloudControllerV2Map[name]
}

func GetCloudResponse(action string) (errno int8, msg string, c interface{}) {
	if action == "CreateMetaCatalog" {
		return 0, "OK", &model.CreateMetaCatalogRsp{}
	}
	if action == "CreateMetaTable" {
		return 0, "OK", &model.CreateMetaTableRsp{}
	}
	if action == "DescribeExternalMetaDatabases" {
		return 0, "OK", &model.DescribeExternalMetaDatabasesRsp{}
	}
	if action == "DescribeExternalMetaTables" {
		return 0, "OK", &model.DescribeExternalMetaTablesRsp{}
	}
	if action == "ModifyMetaTable" {
		return 0, "OK", &model.ModifyMetaTableRsp{}
	}
	if action == "CheckSqlDeepGrammar" {
		return 0, "OK", &sql.CheckSqlDeepGrammarRsp{}
	}
	if action == "ModifyMetaTableFlinkVersions" {
		return 0, "OK", &model.ModifyMetaTableFlinkVersionsRsp{}
	}
	if action == "DescribeJobsException" {
		return 0, "OK", &model2.DescribeJobsExceptionRsp{}
	}
	return -1, "No cloudResponse found for:" + action, nil
}
