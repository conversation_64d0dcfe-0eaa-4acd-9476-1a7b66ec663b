/**
* 业务处理器的公共结构， 具体的业务处理器实现Controller接口，接收Request结构的请求， 返回Response序列化后的包
 */

package httpserver

import (
	"context"
	"reflect"
	"strings"

	"tencentcloud.com/tstream_galileo/src/common/util"
)

type CloudController interface {
	CreateRequestObj() interface{}
	Process(req interface{}, eventId int64) (cloudApiErrorCode string, msg string, rsp interface{})
}

type CloudControllerV2 interface {
	CreateRequestObj() interface{}
	Process(ctx context.Context, req interface{},
		eventId int64) (cloudApiErrorCode string, msg string, rsp interface{})
}

type CloudApiRequest struct {
	Version   string
	Timestamp string
	RequestId string
	Action    string
}

type CloudApiResponse struct {
	Response interface{} `json:"Response"`
}

type ErrorResponse struct {
	RequestId string      `json:"RequestId"`
	RspData   interface{} `json:"Error"`
}

type ErrorPart struct {
	Code    string
	Message string
}

func GetReturnCodeFromCloudApiResponse(resp interface{}) int {
	rspType := reflect.TypeOf(resp)
	if rspType == reflect.TypeOf(CloudApiResponse{}) {
		resp := resp.(CloudApiResponse)
		rspData := resp.Response
		if reflect.TypeOf(rspData) == reflect.TypeOf(ErrorResponse{}) {
			return 1
		} else {
			return 0
		}
	} else {
		respObj := resp.(*Response)
		return int(respObj.ReturnCode)
	}
}

func SetCloudApiResponseContents(asyncStatus int, processCloudApiErrorCode string, processMsg string, rspData interface{}, RequestId string) *CloudApiResponse {
	cloudApiResponse := &CloudApiResponse{}
	result := map[string]interface{}{}
	if processCloudApiErrorCode == "OK" && rspData != nil {
		m, err := util.ScanStructIntoMap0(rspData)
		if err != nil {
			processMsg = err.Error()
		}
		result = m
	}
	if asyncStatus != 0 {
		result["AsyncStatus"] = asyncStatus
	}
	result["RequestId"] = RequestId
	if processCloudApiErrorCode != "OK" {
		error := &ErrorPart{}
		error.Code = processCloudApiErrorCode
		if processCloudApiErrorCode == "InternalError" || strings.HasPrefix(processCloudApiErrorCode, "InternalError.") {
			error.Code = "FailedOperation"
		}
		error.Message = processMsg
		result["Error"] = error
		cloudApiResponse.Response = result
	} else {
		cloudApiResponse.Response = result
	}
	return cloudApiResponse
}
