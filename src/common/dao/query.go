package dao

import (
	"fmt"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/util"
)

//select column from table join table on
//where condition
//group by column having condition
//order by column
//limit m, n

type QueryBuilder struct {
	baseQuery    string
	whereClauses []string
	isOrWhere    []bool
	queryArgs    []interface{}
}

type AfterWhere struct {
	qb *QueryBuilder
}

type AfterGroupByHaving struct {
	qb           *QueryBuilder
	groupBy      string
	havingClause string
}

type AfterOrderBy struct {
	qb           *QueryBuilder
	groupBy      string
	havingClause string
	orderBy      []string
}

type FinalQuery struct {
	qb           *QueryBuilder
	groupBy      string
	havingClause string
	orderBy      []string
	limitClause  string
}

func NewQueryBuilder(baseQuery string) *QueryBuilder {
	return &QueryBuilder{
		baseQuery: baseQuery,
	}
}

func (qb *QueryBuilder) GroupBy(groupBy string) *AfterGroupByHaving {
	return &AfterGroupByHaving{qb, groupBy, ""}
}

func (qb *QueryBuilder) GroupByHaving(groupBy, having string, args ...interface{}) *AfterGroupByHaving {
	if having != "" {
		qb.queryArgs = append(qb.queryArgs, args...)
	}
	return &AfterGroupByHaving{qb, groupBy, having}
}

// Conditions must be complete, eg: column = ?
func (qb *QueryBuilder) Where(exp string, args ...interface{}) *AfterWhere {
	qb.isOrWhere = append(qb.isOrWhere, false)
	qb.whereClauses = append(qb.whereClauses, fmt.Sprintf("(%s)", exp))
	qb.queryArgs = append(qb.queryArgs, args...)
	return &AfterWhere{qb}
}

func (qb *QueryBuilder) WhereEq(column string, arg interface{}) *AfterWhere {
	qb.isOrWhere = append(qb.isOrWhere, false)
	qb.whereClauses = append(qb.whereClauses, fmt.Sprintf("%s = ?", column))
	qb.queryArgs = append(qb.queryArgs, arg)
	return &AfterWhere{qb}
}
func (qb *QueryBuilder) WhereNe(column string, arg interface{}) *AfterWhere {
	qb.isOrWhere = append(qb.isOrWhere, false)
	qb.whereClauses = append(qb.whereClauses, fmt.Sprintf("%s != ?", column))
	qb.queryArgs = append(qb.queryArgs, arg)
	return &AfterWhere{qb}
}
func (qb *QueryBuilder) WhereLt(column string, arg interface{}) *AfterWhere {
	qb.isOrWhere = append(qb.isOrWhere, false)
	qb.whereClauses = append(qb.whereClauses, fmt.Sprintf("%s < ?", column))
	qb.queryArgs = append(qb.queryArgs, arg)
	return &AfterWhere{qb}
}
func (qb *QueryBuilder) WhereLte(column string, arg interface{}) *AfterWhere {
	qb.isOrWhere = append(qb.isOrWhere, false)
	qb.whereClauses = append(qb.whereClauses, fmt.Sprintf("%s <= ?", column))
	qb.queryArgs = append(qb.queryArgs, arg)
	return &AfterWhere{qb}
}
func (qb *QueryBuilder) WhereGt(column string, arg interface{}) *AfterWhere {
	qb.isOrWhere = append(qb.isOrWhere, false)
	qb.whereClauses = append(qb.whereClauses, fmt.Sprintf("%s > ?", column))
	qb.queryArgs = append(qb.queryArgs, arg)
	return &AfterWhere{qb}
}
func (qb *QueryBuilder) WhereGte(column string, arg interface{}) *AfterWhere {
	qb.isOrWhere = append(qb.isOrWhere, false)
	qb.whereClauses = append(qb.whereClauses, fmt.Sprintf("%s >= ?", column))
	qb.queryArgs = append(qb.queryArgs, arg)
	return &AfterWhere{qb}
}
func (qb *QueryBuilder) WhereLike(column string, arg interface{}) *AfterWhere {
	qb.isOrWhere = append(qb.isOrWhere, false)
	qb.whereClauses = append(qb.whereClauses, fmt.Sprintf("%s LIKE ?", column))
	qb.queryArgs = append(qb.queryArgs, arg)
	return &AfterWhere{qb}
}

func (qb *QueryBuilder) WhereIn(column string, arg interface{}) *AfterWhere {
	qb.isOrWhere = append(qb.isOrWhere, false)
	instr := util.ParseInCond(&qb.queryArgs, arg)
	qb.whereClauses = append(qb.whereClauses, fmt.Sprintf("%s in %s", column, instr))
	return &AfterWhere{qb}
}

func (qb *QueryBuilder) WhereNIn(column string, arg interface{}) *AfterWhere {
	qb.isOrWhere = append(qb.isOrWhere, false)
	instr := util.ParseInCond(&qb.queryArgs, arg)
	qb.whereClauses = append(qb.whereClauses, fmt.Sprintf("%s not in %s", column, instr))
	return &AfterWhere{qb}
}

// Conditions must be complete, eg: column = ?
func (qb *QueryBuilder) CWhere(condition bool, exp string, args ...interface{}) *AfterWhere {
	if !condition {
		return &AfterWhere{qb}
	}
	qb.isOrWhere = append(qb.isOrWhere, false)
	qb.whereClauses = append(qb.whereClauses, fmt.Sprintf("(%s)", exp))
	qb.queryArgs = append(qb.queryArgs, args...)
	return &AfterWhere{qb}
}

func (qb *QueryBuilder) CWhereEq(condition bool, column string, arg interface{}) *AfterWhere {
	if !condition {
		return &AfterWhere{qb}
	}
	qb.isOrWhere = append(qb.isOrWhere, false)
	qb.whereClauses = append(qb.whereClauses, fmt.Sprintf("%s = ?", column))
	qb.queryArgs = append(qb.queryArgs, arg)
	return &AfterWhere{qb}
}
func (qb *QueryBuilder) CWhereNe(condition bool, column string, arg interface{}) *AfterWhere {
	if !condition {
		return &AfterWhere{qb}
	}
	qb.isOrWhere = append(qb.isOrWhere, false)
	qb.whereClauses = append(qb.whereClauses, fmt.Sprintf("%s != ?", column))
	qb.queryArgs = append(qb.queryArgs, arg)
	return &AfterWhere{qb}
}
func (qb *QueryBuilder) CWhereLt(condition bool, column string, arg interface{}) *AfterWhere {
	if !condition {
		return &AfterWhere{qb}
	}
	qb.isOrWhere = append(qb.isOrWhere, false)
	qb.whereClauses = append(qb.whereClauses, fmt.Sprintf("%s < ?", column))
	qb.queryArgs = append(qb.queryArgs, arg)
	return &AfterWhere{qb}
}
func (qb *QueryBuilder) CWhereLte(condition bool, column string, arg interface{}) *AfterWhere {
	if !condition {
		return &AfterWhere{qb}
	}
	qb.isOrWhere = append(qb.isOrWhere, false)
	qb.whereClauses = append(qb.whereClauses, fmt.Sprintf("%s <= ?", column))
	qb.queryArgs = append(qb.queryArgs, arg)
	return &AfterWhere{qb}
}
func (qb *QueryBuilder) CWhereGt(condition bool, column string, arg interface{}) *AfterWhere {
	if !condition {
		return &AfterWhere{qb}
	}
	qb.isOrWhere = append(qb.isOrWhere, false)
	qb.whereClauses = append(qb.whereClauses, fmt.Sprintf("%s > ?", column))
	qb.queryArgs = append(qb.queryArgs, arg)
	return &AfterWhere{qb}
}
func (qb *QueryBuilder) CWhereGte(condition bool, column string, arg interface{}) *AfterWhere {
	if !condition {
		return &AfterWhere{qb}
	}
	qb.isOrWhere = append(qb.isOrWhere, false)
	qb.whereClauses = append(qb.whereClauses, fmt.Sprintf("%s >= ?", column))
	qb.queryArgs = append(qb.queryArgs, arg)
	return &AfterWhere{qb}
}
func (qb *QueryBuilder) CWhereLike(condition bool, column string, arg interface{}) *AfterWhere {
	if !condition {
		return &AfterWhere{qb}
	}
	qb.isOrWhere = append(qb.isOrWhere, false)
	qb.whereClauses = append(qb.whereClauses, fmt.Sprintf("%s LIKE ?", column))
	qb.queryArgs = append(qb.queryArgs, arg)
	return &AfterWhere{qb}
}

func (qb *QueryBuilder) CWhereIn(condition bool, column string, arg interface{}) *AfterWhere {
	if !condition {
		return &AfterWhere{qb}
	}
	qb.isOrWhere = append(qb.isOrWhere, false)
	instr := util.ParseInCond(&qb.queryArgs, arg)
	qb.whereClauses = append(qb.whereClauses, fmt.Sprintf("%s in %s", column, instr))
	return &AfterWhere{qb}
}

func (qb *QueryBuilder) CWhereNIn(condition bool, column string, arg interface{}) *AfterWhere {
	if !condition {
		return &AfterWhere{qb}
	}
	qb.isOrWhere = append(qb.isOrWhere, false)
	instr := util.ParseInCond(&qb.queryArgs, arg)
	qb.whereClauses = append(qb.whereClauses, fmt.Sprintf("%s not in %s", column, instr))
	return &AfterWhere{qb}
}

func (qb *QueryBuilder) OrderBy(orderBy string) *AfterOrderBy {
	return &AfterOrderBy{qb, "", "", []string{orderBy}}
}

func (qb *QueryBuilder) OrderByDesc(orderBy string) *AfterOrderBy {
	return &AfterOrderBy{qb, "", "", []string{fmt.Sprintf("%s DESC", orderBy)}}
}

func (qb *QueryBuilder) Limit(count int) *FinalQuery {
	limitClause := fmt.Sprintf("%d", count)
	return &FinalQuery{qb, "", "", nil, limitClause}
}

func (qb *QueryBuilder) LimitFrom(offset, count int) *FinalQuery {
	limitClause := fmt.Sprintf("%d, %d", offset, count)
	return &FinalQuery{qb, "", "", nil, limitClause}
}

// Conditions must be complete, eg: column = ?
func (aw *AfterWhere) Where(exp string, args ...interface{}) *AfterWhere {
	aw.qb.isOrWhere = append(aw.qb.isOrWhere, false)
	aw.qb.whereClauses = append(aw.qb.whereClauses, fmt.Sprintf("(%s)", exp))
	aw.qb.queryArgs = append(aw.qb.queryArgs, args...)
	return aw
}
func (aw *AfterWhere) WhereEq(column string, arg interface{}) *AfterWhere {
	aw.qb.isOrWhere = append(aw.qb.isOrWhere, false)
	aw.qb.whereClauses = append(aw.qb.whereClauses, fmt.Sprintf("%s = ?", column))
	aw.qb.queryArgs = append(aw.qb.queryArgs, arg)
	return aw
}
func (aw *AfterWhere) WhereNe(column string, arg interface{}) *AfterWhere {
	aw.qb.isOrWhere = append(aw.qb.isOrWhere, false)
	aw.qb.whereClauses = append(aw.qb.whereClauses, fmt.Sprintf("%s != ?", column))
	aw.qb.queryArgs = append(aw.qb.queryArgs, arg)
	return aw
}
func (aw *AfterWhere) WhereLt(column string, arg interface{}) *AfterWhere {
	aw.qb.isOrWhere = append(aw.qb.isOrWhere, false)
	aw.qb.whereClauses = append(aw.qb.whereClauses, fmt.Sprintf("%s < ?", column))
	aw.qb.queryArgs = append(aw.qb.queryArgs, arg)
	return aw
}
func (aw *AfterWhere) WhereLte(column string, arg interface{}) *AfterWhere {
	aw.qb.isOrWhere = append(aw.qb.isOrWhere, false)
	aw.qb.whereClauses = append(aw.qb.whereClauses, fmt.Sprintf("%s <= ?", column))
	aw.qb.queryArgs = append(aw.qb.queryArgs, arg)
	return aw
}
func (aw *AfterWhere) WhereGt(column string, arg interface{}) *AfterWhere {
	aw.qb.isOrWhere = append(aw.qb.isOrWhere, false)
	aw.qb.whereClauses = append(aw.qb.whereClauses, fmt.Sprintf("%s > ?", column))
	aw.qb.queryArgs = append(aw.qb.queryArgs, arg)
	return aw
}
func (aw *AfterWhere) WhereGte(column string, arg interface{}) *AfterWhere {
	aw.qb.isOrWhere = append(aw.qb.isOrWhere, false)
	aw.qb.whereClauses = append(aw.qb.whereClauses, fmt.Sprintf("%s >= ?", column))
	aw.qb.queryArgs = append(aw.qb.queryArgs, arg)
	return aw
}
func (aw *AfterWhere) WhereLike(column string, arg interface{}) *AfterWhere {
	aw.qb.isOrWhere = append(aw.qb.isOrWhere, false)
	aw.qb.whereClauses = append(aw.qb.whereClauses, fmt.Sprintf("%s LIKE ?", column))
	aw.qb.queryArgs = append(aw.qb.queryArgs, arg)
	return aw
}
func (aw *AfterWhere) WhereIn(column string, arg interface{}) *AfterWhere {
	aw.qb.isOrWhere = append(aw.qb.isOrWhere, false)
	instr := util.ParseInCond(&aw.qb.queryArgs, arg)
	aw.qb.whereClauses = append(aw.qb.whereClauses, fmt.Sprintf("%s in %s", column, instr))
	return aw
}
func (aw *AfterWhere) WhereNIn(column string, arg interface{}) *AfterWhere {
	aw.qb.isOrWhere = append(aw.qb.isOrWhere, false)
	instr := util.ParseInCond(&aw.qb.queryArgs, arg)
	aw.qb.whereClauses = append(aw.qb.whereClauses, fmt.Sprintf("%s not in %s", column, instr))
	return aw
}

// Conditions must be complete, eg: column = ?
func (aw *AfterWhere) CWhere(condition bool, exp string, args ...interface{}) *AfterWhere {
	if !condition {
		return aw
	}
	aw.qb.isOrWhere = append(aw.qb.isOrWhere, false)
	aw.qb.whereClauses = append(aw.qb.whereClauses, fmt.Sprintf("(%s)", exp))
	aw.qb.queryArgs = append(aw.qb.queryArgs, args...)
	return aw
}
func (aw *AfterWhere) CWhereEq(condition bool, column string, arg interface{}) *AfterWhere {
	if !condition {
		return aw
	}
	aw.qb.isOrWhere = append(aw.qb.isOrWhere, false)
	aw.qb.whereClauses = append(aw.qb.whereClauses, fmt.Sprintf("%s = ?", column))
	aw.qb.queryArgs = append(aw.qb.queryArgs, arg)
	return aw
}
func (aw *AfterWhere) CWhereNe(condition bool, column string, arg interface{}) *AfterWhere {
	if !condition {
		return aw
	}
	aw.qb.isOrWhere = append(aw.qb.isOrWhere, false)
	aw.qb.whereClauses = append(aw.qb.whereClauses, fmt.Sprintf("%s != ?", column))
	aw.qb.queryArgs = append(aw.qb.queryArgs, arg)
	return aw
}
func (aw *AfterWhere) CWhereLt(condition bool, column string, arg interface{}) *AfterWhere {
	if !condition {
		return aw
	}
	aw.qb.isOrWhere = append(aw.qb.isOrWhere, false)
	aw.qb.whereClauses = append(aw.qb.whereClauses, fmt.Sprintf("%s < ?", column))
	aw.qb.queryArgs = append(aw.qb.queryArgs, arg)
	return aw
}
func (aw *AfterWhere) CWhereLte(condition bool, column string, arg interface{}) *AfterWhere {
	if !condition {
		return aw
	}
	aw.qb.isOrWhere = append(aw.qb.isOrWhere, false)
	aw.qb.whereClauses = append(aw.qb.whereClauses, fmt.Sprintf("%s <= ?", column))
	aw.qb.queryArgs = append(aw.qb.queryArgs, arg)
	return aw
}
func (aw *AfterWhere) CWhereGt(condition bool, column string, arg interface{}) *AfterWhere {
	if !condition {
		return aw
	}
	aw.qb.isOrWhere = append(aw.qb.isOrWhere, false)
	aw.qb.whereClauses = append(aw.qb.whereClauses, fmt.Sprintf("%s > ?", column))
	aw.qb.queryArgs = append(aw.qb.queryArgs, arg)
	return aw
}
func (aw *AfterWhere) CWhereGte(condition bool, column string, arg interface{}) *AfterWhere {
	if !condition {
		return aw
	}
	aw.qb.isOrWhere = append(aw.qb.isOrWhere, false)
	aw.qb.whereClauses = append(aw.qb.whereClauses, fmt.Sprintf("%s >= ?", column))
	aw.qb.queryArgs = append(aw.qb.queryArgs, arg)
	return aw
}
func (aw *AfterWhere) CWhereLike(condition bool, column string, arg interface{}) *AfterWhere {
	if !condition {
		return aw
	}
	aw.qb.isOrWhere = append(aw.qb.isOrWhere, false)
	aw.qb.whereClauses = append(aw.qb.whereClauses, fmt.Sprintf("%s LIKE ?", column))
	aw.qb.queryArgs = append(aw.qb.queryArgs, arg)
	return aw
}
func (aw *AfterWhere) CWhereIn(condition bool, column string, arg interface{}) *AfterWhere {
	if !condition {
		return aw
	}
	aw.qb.isOrWhere = append(aw.qb.isOrWhere, false)
	instr := util.ParseInCond(&aw.qb.queryArgs, arg)
	aw.qb.whereClauses = append(aw.qb.whereClauses, fmt.Sprintf("%s in %s", column, instr))
	return aw
}
func (aw *AfterWhere) CWhereNIn(condition bool, column string, arg interface{}) *AfterWhere {
	if !condition {
		return aw
	}
	aw.qb.isOrWhere = append(aw.qb.isOrWhere, false)
	instr := util.ParseInCond(&aw.qb.queryArgs, arg)
	aw.qb.whereClauses = append(aw.qb.whereClauses, fmt.Sprintf("%s not in %s", column, instr))
	return aw
}

// Conditions must be complete, eg: column = ?
func (aw *AfterWhere) OrWhere(exp string, args ...interface{}) *AfterWhere {
	aw.qb.isOrWhere = append(aw.qb.isOrWhere, true)
	aw.qb.whereClauses = append(aw.qb.whereClauses, fmt.Sprintf("(%s)", exp))
	aw.qb.queryArgs = append(aw.qb.queryArgs, args...)
	return aw
}
func (aw *AfterWhere) OrWhereEq(column string, arg interface{}) *AfterWhere {
	aw.qb.isOrWhere = append(aw.qb.isOrWhere, true)
	aw.qb.whereClauses = append(aw.qb.whereClauses, fmt.Sprintf("%s = ?", column))
	aw.qb.queryArgs = append(aw.qb.queryArgs, arg)
	return aw
}
func (aw *AfterWhere) OrWhereNe(column string, arg interface{}) *AfterWhere {
	aw.qb.isOrWhere = append(aw.qb.isOrWhere, true)
	aw.qb.whereClauses = append(aw.qb.whereClauses, fmt.Sprintf("%s != ?", column))
	aw.qb.queryArgs = append(aw.qb.queryArgs, arg)
	return aw
}
func (aw *AfterWhere) OrWhereLt(column string, arg interface{}) *AfterWhere {
	aw.qb.isOrWhere = append(aw.qb.isOrWhere, true)
	aw.qb.whereClauses = append(aw.qb.whereClauses, fmt.Sprintf("%s < ?", column))
	aw.qb.queryArgs = append(aw.qb.queryArgs, arg)
	return aw
}
func (aw *AfterWhere) OrWhereLte(column string, arg interface{}) *AfterWhere {
	aw.qb.isOrWhere = append(aw.qb.isOrWhere, true)
	aw.qb.whereClauses = append(aw.qb.whereClauses, fmt.Sprintf("%s <= ?", column))
	aw.qb.queryArgs = append(aw.qb.queryArgs, arg)
	return aw
}
func (aw *AfterWhere) OrWhereGt(column string, arg interface{}) *AfterWhere {
	aw.qb.isOrWhere = append(aw.qb.isOrWhere, true)
	aw.qb.whereClauses = append(aw.qb.whereClauses, fmt.Sprintf("%s > ?", column))
	aw.qb.queryArgs = append(aw.qb.queryArgs, arg)
	return aw
}
func (aw *AfterWhere) OrWhereGte(column string, arg interface{}) *AfterWhere {
	aw.qb.isOrWhere = append(aw.qb.isOrWhere, true)
	aw.qb.whereClauses = append(aw.qb.whereClauses, fmt.Sprintf("%s >= ?", column))
	aw.qb.queryArgs = append(aw.qb.queryArgs, arg)
	return aw
}
func (aw *AfterWhere) OrWhereLike(column string, arg interface{}) *AfterWhere {
	aw.qb.isOrWhere = append(aw.qb.isOrWhere, true)
	aw.qb.whereClauses = append(aw.qb.whereClauses, fmt.Sprintf("%s LIKE ?", column))
	aw.qb.queryArgs = append(aw.qb.queryArgs, arg)
	return aw
}
func (aw *AfterWhere) OrWhereIn(column string, arg interface{}) *AfterWhere {
	aw.qb.isOrWhere = append(aw.qb.isOrWhere, true)
	instr := util.ParseInCond(&aw.qb.queryArgs, arg)
	aw.qb.whereClauses = append(aw.qb.whereClauses, fmt.Sprintf("%s in %s", column, instr))
	return aw
}
func (aw *AfterWhere) OrWhereNIn(column string, arg interface{}) *AfterWhere {
	aw.qb.isOrWhere = append(aw.qb.isOrWhere, true)
	instr := util.ParseInCond(&aw.qb.queryArgs, arg)
	aw.qb.whereClauses = append(aw.qb.whereClauses, fmt.Sprintf("%s not in %s", column, instr))
	return aw
}

// Conditions must be complete, eg: column = ?
func (aw *AfterWhere) COrWhere(condition bool, exp string, args ...interface{}) *AfterWhere {
	if !condition {
		return aw
	}
	aw.qb.isOrWhere = append(aw.qb.isOrWhere, true)
	aw.qb.whereClauses = append(aw.qb.whereClauses, fmt.Sprintf("(%s)", exp))
	aw.qb.queryArgs = append(aw.qb.queryArgs, args...)
	return aw
}
func (aw *AfterWhere) COrWhereEq(condition bool, column string, arg interface{}) *AfterWhere {
	if !condition {
		return aw
	}
	aw.qb.isOrWhere = append(aw.qb.isOrWhere, true)
	aw.qb.whereClauses = append(aw.qb.whereClauses, fmt.Sprintf("%s = ?", column))
	aw.qb.queryArgs = append(aw.qb.queryArgs, arg)
	return aw
}
func (aw *AfterWhere) COrWhereNe(condition bool, column string, arg interface{}) *AfterWhere {
	if !condition {
		return aw
	}
	aw.qb.isOrWhere = append(aw.qb.isOrWhere, true)
	aw.qb.whereClauses = append(aw.qb.whereClauses, fmt.Sprintf("%s != ?", column))
	aw.qb.queryArgs = append(aw.qb.queryArgs, arg)
	return aw
}
func (aw *AfterWhere) COrWhereLt(condition bool, column string, arg interface{}) *AfterWhere {
	if !condition {
		return aw
	}
	aw.qb.isOrWhere = append(aw.qb.isOrWhere, true)
	aw.qb.whereClauses = append(aw.qb.whereClauses, fmt.Sprintf("%s < ?", column))
	aw.qb.queryArgs = append(aw.qb.queryArgs, arg)
	return aw
}
func (aw *AfterWhere) COrWhereLte(condition bool, column string, arg interface{}) *AfterWhere {
	if !condition {
		return aw
	}
	aw.qb.isOrWhere = append(aw.qb.isOrWhere, true)
	aw.qb.whereClauses = append(aw.qb.whereClauses, fmt.Sprintf("%s <= ?", column))
	aw.qb.queryArgs = append(aw.qb.queryArgs, arg)
	return aw
}
func (aw *AfterWhere) COrWhereGt(condition bool, column string, arg interface{}) *AfterWhere {
	if !condition {
		return aw
	}
	aw.qb.isOrWhere = append(aw.qb.isOrWhere, true)
	aw.qb.whereClauses = append(aw.qb.whereClauses, fmt.Sprintf("%s > ?", column))
	aw.qb.queryArgs = append(aw.qb.queryArgs, arg)
	return aw
}
func (aw *AfterWhere) COrWhereGte(condition bool, column string, arg interface{}) *AfterWhere {
	if !condition {
		return aw
	}
	aw.qb.isOrWhere = append(aw.qb.isOrWhere, true)
	aw.qb.whereClauses = append(aw.qb.whereClauses, fmt.Sprintf("%s >= ?", column))
	aw.qb.queryArgs = append(aw.qb.queryArgs, arg)
	return aw
}
func (aw *AfterWhere) COrWhereLike(condition bool, column string, arg interface{}) *AfterWhere {
	if !condition {
		return aw
	}
	aw.qb.isOrWhere = append(aw.qb.isOrWhere, true)
	aw.qb.whereClauses = append(aw.qb.whereClauses, fmt.Sprintf("%s LIKE ?", column))
	aw.qb.queryArgs = append(aw.qb.queryArgs, arg)
	return aw
}
func (aw *AfterWhere) COrWhereIn(condition bool, column string, arg interface{}) *AfterWhere {
	if !condition {
		return aw
	}
	aw.qb.isOrWhere = append(aw.qb.isOrWhere, true)
	instr := util.ParseInCond(&aw.qb.queryArgs, arg)
	aw.qb.whereClauses = append(aw.qb.whereClauses, fmt.Sprintf("%s in %s", column, instr))
	return aw
}
func (aw *AfterWhere) COrWhereNIn(condition bool, column string, arg interface{}) *AfterWhere {
	if !condition {
		return aw
	}
	aw.qb.isOrWhere = append(aw.qb.isOrWhere, true)
	instr := util.ParseInCond(&aw.qb.queryArgs, arg)
	aw.qb.whereClauses = append(aw.qb.whereClauses, fmt.Sprintf("%s not in %s", column, instr))
	return aw
}

func (aw *AfterWhere) GroupBy(groupBy string) *AfterGroupByHaving {
	return &AfterGroupByHaving{aw.qb, groupBy, ""}
}

func (aw *AfterWhere) GroupByHaving(groupBy, having string, args ...interface{}) *AfterGroupByHaving {
	if having != "" {
		aw.qb.queryArgs = append(aw.qb.queryArgs, args...)
	}
	return &AfterGroupByHaving{aw.qb, groupBy, having}
}

func (aw *AfterWhere) OrderBy(orderBy string) *AfterOrderBy {
	return &AfterOrderBy{aw.qb, "", "", []string{orderBy}}
}

func (aw *AfterWhere) OrderByDesc(orderBy string) *AfterOrderBy {
	return &AfterOrderBy{aw.qb, "", "", []string{fmt.Sprintf("%s DESC", orderBy)}}
}

func (aw *AfterWhere) Limit(count int) *FinalQuery {
	limitClause := fmt.Sprintf("%d", count)
	return &FinalQuery{aw.qb, "", "", nil, limitClause}
}

func (aw *AfterWhere) LimitFrom(offset, count int) *FinalQuery {
	limitClause := fmt.Sprintf("%d, %d", offset, count)
	return &FinalQuery{aw.qb, "", "", nil, limitClause}
}

func (gb *AfterGroupByHaving) OrderBy(orderBy string) *AfterOrderBy {
	return &AfterOrderBy{gb.qb, gb.groupBy, gb.havingClause, []string{orderBy}}
}

func (gb *AfterGroupByHaving) OrderByDesc(orderBy string) *AfterOrderBy {
	return &AfterOrderBy{gb.qb, gb.groupBy, gb.havingClause, []string{fmt.Sprintf("%s DESC", orderBy)}}
}

func (ob *AfterOrderBy) OrderBy(orderBy string) *AfterOrderBy {
	ob.orderBy = append(ob.orderBy, orderBy)
	return ob
}

func (ob *AfterOrderBy) OrderByDesc(orderBy string) *AfterOrderBy {
	ob.orderBy = append(ob.orderBy, fmt.Sprintf("%s DESC", orderBy))
	return ob
}

func (gb *AfterGroupByHaving) Limit(count int) *FinalQuery {
	limitClause := fmt.Sprintf("%d", count)
	return &FinalQuery{gb.qb, gb.groupBy, gb.havingClause, nil, limitClause}
}

func (gb *AfterGroupByHaving) LimitFrom(offset, count int) *FinalQuery {
	limitClause := fmt.Sprintf("%d, %d", offset, count)
	return &FinalQuery{gb.qb, gb.groupBy, gb.havingClause, nil, limitClause}
}

func (ob *AfterOrderBy) Limit(count int) *FinalQuery {
	limitClause := fmt.Sprintf("%d", count)
	return &FinalQuery{ob.qb, ob.groupBy, ob.havingClause, ob.orderBy, limitClause}
}

func (ob *AfterOrderBy) LimitFrom(offset, count int) *FinalQuery {
	limitClause := fmt.Sprintf("%d, %d", offset, count)
	return &FinalQuery{ob.qb, ob.groupBy, ob.havingClause, ob.orderBy, limitClause}
}

func (fq *FinalQuery) Build() (string, []interface{}) {
	var queryParts []string
	queryParts = append(queryParts, fq.qb.baseQuery)

	if len(fq.qb.whereClauses) > 0 {
		whereClause := where(fq.qb.whereClauses, fq.qb.isOrWhere)
		queryParts = append(queryParts, whereClause)
	}

	if fq.groupBy != "" {
		queryParts = append(queryParts, "GROUP BY "+fq.groupBy)
		if fq.havingClause != "" {
			queryParts = append(queryParts, "HAVING "+fq.havingClause)
		}
	}

	if len(fq.orderBy) > 0 {
		queryParts = append(queryParts, "ORDER BY "+strings.Join(fq.orderBy, ", "))
	}

	if fq.limitClause != "" {
		queryParts = append(queryParts, "LIMIT "+fq.limitClause)
	}

	return strings.Join(queryParts, " "), fq.qb.queryArgs
}

func (qb *QueryBuilder) Build() (string, []interface{}) {
	var queryParts []string
	queryParts = append(queryParts, qb.baseQuery)

	if len(qb.whereClauses) > 0 {
		whereClause := where(qb.whereClauses, qb.isOrWhere)
		queryParts = append(queryParts, whereClause)
	}
	return strings.Join(queryParts, " "), qb.queryArgs
}

func (aw *AfterWhere) Build() (string, []interface{}) {
	var queryParts []string
	queryParts = append(queryParts, aw.qb.baseQuery)

	if len(aw.qb.whereClauses) > 0 {
		whereClause := where(aw.qb.whereClauses, aw.qb.isOrWhere)
		queryParts = append(queryParts, whereClause)
	}
	return strings.Join(queryParts, " "), aw.qb.queryArgs
}

func (gb *AfterGroupByHaving) Build() (string, []interface{}) {
	var queryParts []string
	queryParts = append(queryParts, gb.qb.baseQuery)

	if len(gb.qb.whereClauses) > 0 {
		whereClause := where(gb.qb.whereClauses, gb.qb.isOrWhere)
		queryParts = append(queryParts, whereClause)
	}

	if gb.groupBy != "" {
		queryParts = append(queryParts, "GROUP BY "+gb.groupBy)
		if gb.havingClause != "" {
			queryParts = append(queryParts, "HAVING "+gb.havingClause)
		}
	}

	return strings.Join(queryParts, " "), gb.qb.queryArgs
}

func (ob *AfterOrderBy) Build() (string, []interface{}) {
	var queryParts []string
	queryParts = append(queryParts, ob.qb.baseQuery)

	if len(ob.qb.whereClauses) > 0 {
		whereClause := where(ob.qb.whereClauses, ob.qb.isOrWhere)
		queryParts = append(queryParts, whereClause)
	}

	if ob.groupBy != "" {
		queryParts = append(queryParts, "GROUP BY "+ob.groupBy)
		if ob.havingClause != "" {
			queryParts = append(queryParts, "HAVING "+ob.havingClause)
		}
	}

	if len(ob.orderBy) > 0 {
		queryParts = append(queryParts, "ORDER BY "+strings.Join(ob.orderBy, ", "))
	}
	return strings.Join(queryParts, " "), ob.qb.queryArgs
}
