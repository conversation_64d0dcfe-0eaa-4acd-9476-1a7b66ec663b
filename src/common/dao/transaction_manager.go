/**
* 事物管理器
 */
package dao

import (
	"database/sql"
	"fmt"
	"runtime"
	_ "tencentcloud.com/tstream_galileo/src/common/dbdriver/mysql"
	log "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"

	"github.com/juju/errors"
)

type DataSourceTransactionManager struct {
	maxstack        int
	transactionPool *syncMap
	datasource      *sql.DB
	querytemplate   *queryTemplate
}

func (this *DataSourceTransactionManager) Close() {
	defer func() {
		if errs := recover(); errs != nil {
			fmt.Println("DataSourceTransactionManager Close err,", errs)
		}
	}()
	if this.datasource != nil {
		err := this.datasource.Close()
		if err != nil {
			fmt.Println("DataSourceTransactionManager Close err,", err)
		}
	}
}

func (this *DataSourceTransactionManager) Ping() {
	defer func() {
		if errs := recover(); errs != nil {
			fmt.Println("DataSourceTransactionManager Ping err,", errs)
		}
	}()
	if this.datasource != nil {
		rows, err := this.datasource.Query("select 1 from DUAL")
		if err != nil {
			fmt.Println("DataSourceTransactionManager Ping err,", err)
			panic(err)
		}
		rows.Close()
	}
}

func (this *DataSourceTransactionManager) getTransactionTemplateId(ptr uintptr) string {
	goid := util.GoroutineId()
	if len(goid) <= 0 {
		panic(errors.BadRequestf("get goid empty"))
	}
	return fmt.Sprintf("%d", ptr) + "_" + goid
}

func (this *DataSourceTransactionManager) LockRow(tableName string, id int64) bool {
	sql := fmt.Sprintf("update %s set locked=1 where id=?", tableName)
	tx, err := this.datasource.Begin()
	if err != nil {
		log.Error(fmt.Sprintf("lock table:%s for id:%d Begin err:", tableName, id), err)
		return false
	}
	ret, err := tx.Exec(sql, id)
	if err != nil {
		log.Error(fmt.Sprintf("lock table:%s for id:%d Exec err:", tableName, id), err)
		return false
	}
	defer tx.Commit()
	rows, err := ret.RowsAffected()
	if err != nil {
		log.Error(fmt.Sprintf("lock table:%s for id:%d  RowsAffected err:", tableName, id), err)
		return false
	}
	return rows == 1
}

func (this *DataSourceTransactionManager) UnLockRow(tableName string, id int64) bool {
	sql := fmt.Sprintf("update %s set locked=0 where id=?", tableName)
	tx, err := this.datasource.Begin()
	if err != nil {
		log.Error(fmt.Sprintf("UnLockRow table:%s for id:%d Begin err:", tableName, id), err)
		return false
	}
	ret, err := tx.Exec(sql, id)
	if err != nil {
		log.Error(fmt.Sprintf("UnLockRow table:%s for id:%d Exec err:", tableName, id), err)
		return false
	}
	defer tx.Commit()
	rows, err := ret.RowsAffected()
	if err != nil {
		log.Error(fmt.Sprintf("UnLockRow table:%s for id:%d  RowsAffected err:", tableName, id), err)
		return false
	}
	return rows == 1
}

func (this *DataSourceTransactionManager) GetTransactionTemplate() *transactionTemplate {
	var template *transactionTemplate

	for i := 1; i < this.maxstack; i++ {
		pc, _, _, ok := runtime.Caller(i)
		if !ok {
			break
		}
		fn := runtime.FuncForPC(pc)
		entry := fn.Entry()
		tpId := this.getTransactionTemplateId(entry)

		template = this.transactionPool.get(tpId)

		if template != nil {
			if i == 1 {
				log.Error("recursion detect for:", fn.Name())
				panic(errors.New("recursion detect for:" + fn.Name()))
			}
			return template
		}
	}
	template = &transactionTemplate{}
	pc, _, _, ok := runtime.Caller(1)
	if !ok {
		panic(errors.New("GetTransaction error"))
	}
	fn := runtime.FuncForPC(pc)
	tpId := this.getTransactionTemplateId(fn.Entry())
	template.transactionEntry = tpId
	template.txManager = this

	this.transactionPool.put(template.transactionEntry, template)

	return template
}

func (this *DataSourceTransactionManager) GetQueryTemplate() *queryTemplate {
	return this.querytemplate
}

func NewDataSourceTransactionManager(dburl string) (txManager *DataSourceTransactionManager, err error) {
	txManager = &DataSourceTransactionManager{}
	txManager.maxstack = 64
	txManager.transactionPool = gloabSyncMap
	txManager.datasource, err = sql.Open("mysql", dburl)
	if err != nil {
		return nil, err
	}
	txManager.datasource.SetMaxOpenConns(2000)
	txManager.datasource.SetMaxIdleConns(1000)
	err = txManager.datasource.Ping()
	if err != nil {
		return nil, err
	}
	qt := &queryTemplate{}
	qt.datasource = txManager.datasource
	txManager.querytemplate = qt
	return txManager, err
}
