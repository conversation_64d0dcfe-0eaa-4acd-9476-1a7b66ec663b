package dao

import (
	"fmt"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/util"
)

//delete from table join table on
//where condition
//order by column desc
//limit n

type DeleteBuilder struct {
	baseDelete   string
	whereClauses []string
	isOrWhere    []bool
	deleteArgs   []interface{}
}

type DeleteWhere struct {
	db *DeleteBuilder
}

type DeleteOrderBy struct {
	db      *DeleteBuilder
	orderBy []string
}

type FinalDelete struct {
	db          *DeleteBuilder
	orderBy     []string
	limitClause string
}

func NewDeleteBuilder(baseDelete string) *DeleteBuilder {
	return &DeleteBuilder{
		baseDelete: baseDelete,
	}
}

// Conditions must be complete, eg: column = ?
func (db *DeleteBuilder) Where(exp string, args ...interface{}) *DeleteWhere {
	db.isOrWhere = append(db.isOrWhere, false)
	db.whereClauses = append(db.whereClauses, fmt.Sprintf("(%s)", exp))
	db.deleteArgs = append(db.deleteArgs, args...)
	return &DeleteWhere{db}
}

func (db *DeleteBuilder) WhereEq(column string, arg interface{}) *DeleteWhere {
	db.isOrWhere = append(db.isOrWhere, false)
	db.whereClauses = append(db.whereClauses, fmt.Sprintf("%s = ?", column))
	db.deleteArgs = append(db.deleteArgs, arg)
	return &DeleteWhere{db}
}
func (db *DeleteBuilder) WhereNe(column string, arg interface{}) *DeleteWhere {
	db.isOrWhere = append(db.isOrWhere, false)
	db.whereClauses = append(db.whereClauses, fmt.Sprintf("%s != ?", column))
	db.deleteArgs = append(db.deleteArgs, arg)
	return &DeleteWhere{db}
}
func (db *DeleteBuilder) WhereLt(column string, arg interface{}) *DeleteWhere {
	db.isOrWhere = append(db.isOrWhere, false)
	db.whereClauses = append(db.whereClauses, fmt.Sprintf("%s < ?", column))
	db.deleteArgs = append(db.deleteArgs, arg)
	return &DeleteWhere{db}
}
func (db *DeleteBuilder) WhereLte(column string, arg interface{}) *DeleteWhere {
	db.isOrWhere = append(db.isOrWhere, false)
	db.whereClauses = append(db.whereClauses, fmt.Sprintf("%s <= ?", column))
	db.deleteArgs = append(db.deleteArgs, arg)
	return &DeleteWhere{db}
}
func (db *DeleteBuilder) WhereGt(column string, arg interface{}) *DeleteWhere {
	db.isOrWhere = append(db.isOrWhere, false)
	db.whereClauses = append(db.whereClauses, fmt.Sprintf("%s > ?", column))
	db.deleteArgs = append(db.deleteArgs, arg)
	return &DeleteWhere{db}
}
func (db *DeleteBuilder) WhereGte(column string, arg interface{}) *DeleteWhere {
	db.isOrWhere = append(db.isOrWhere, false)
	db.whereClauses = append(db.whereClauses, fmt.Sprintf("%s >= ?", column))
	db.deleteArgs = append(db.deleteArgs, arg)
	return &DeleteWhere{db}
}
func (db *DeleteBuilder) WhereLike(column string, arg interface{}) *DeleteWhere {
	db.isOrWhere = append(db.isOrWhere, false)
	db.whereClauses = append(db.whereClauses, fmt.Sprintf("%s LIKE ?", column))
	db.deleteArgs = append(db.deleteArgs, arg)
	return &DeleteWhere{db}
}

func (db *DeleteBuilder) WhereIn(column string, arg interface{}) *DeleteWhere {
	db.isOrWhere = append(db.isOrWhere, false)
	instr := util.ParseInCond(&db.deleteArgs, arg)
	db.whereClauses = append(db.whereClauses, fmt.Sprintf("%s in %s", column, instr))
	return &DeleteWhere{db}
}

func (db *DeleteBuilder) WhereNIn(column string, arg interface{}) *DeleteWhere {
	db.isOrWhere = append(db.isOrWhere, false)
	instr := util.ParseInCond(&db.deleteArgs, arg)
	db.whereClauses = append(db.whereClauses, fmt.Sprintf("%s not in %s", column, instr))
	return &DeleteWhere{db}
}

// Conditions must be complete, eg: column = ?
func (db *DeleteBuilder) CWhere(condition bool, exp string, args ...interface{}) *DeleteWhere {
	if !condition {
		return &DeleteWhere{db}
	}
	db.isOrWhere = append(db.isOrWhere, false)
	db.whereClauses = append(db.whereClauses, fmt.Sprintf("(%s)", exp))
	db.deleteArgs = append(db.deleteArgs, args...)
	return &DeleteWhere{db}
}

func (db *DeleteBuilder) CWhereEq(condition bool, column string, arg interface{}) *DeleteWhere {
	if !condition {
		return &DeleteWhere{db}
	}
	db.isOrWhere = append(db.isOrWhere, false)
	db.whereClauses = append(db.whereClauses, fmt.Sprintf("%s = ?", column))
	db.deleteArgs = append(db.deleteArgs, arg)
	return &DeleteWhere{db}
}
func (db *DeleteBuilder) CWhereNe(condition bool, column string, arg interface{}) *DeleteWhere {
	if !condition {
		return &DeleteWhere{db}
	}
	db.isOrWhere = append(db.isOrWhere, false)
	db.whereClauses = append(db.whereClauses, fmt.Sprintf("%s != ?", column))
	db.deleteArgs = append(db.deleteArgs, arg)
	return &DeleteWhere{db}
}
func (db *DeleteBuilder) CWhereLt(condition bool, column string, arg interface{}) *DeleteWhere {
	if !condition {
		return &DeleteWhere{db}
	}
	db.isOrWhere = append(db.isOrWhere, false)
	db.whereClauses = append(db.whereClauses, fmt.Sprintf("%s < ?", column))
	db.deleteArgs = append(db.deleteArgs, arg)
	return &DeleteWhere{db}
}
func (db *DeleteBuilder) CWhereLte(condition bool, column string, arg interface{}) *DeleteWhere {
	if !condition {
		return &DeleteWhere{db}
	}
	db.isOrWhere = append(db.isOrWhere, false)
	db.whereClauses = append(db.whereClauses, fmt.Sprintf("%s <= ?", column))
	db.deleteArgs = append(db.deleteArgs, arg)
	return &DeleteWhere{db}
}
func (db *DeleteBuilder) CWhereGt(condition bool, column string, arg interface{}) *DeleteWhere {
	if !condition {
		return &DeleteWhere{db}
	}
	db.isOrWhere = append(db.isOrWhere, false)
	db.whereClauses = append(db.whereClauses, fmt.Sprintf("%s > ?", column))
	db.deleteArgs = append(db.deleteArgs, arg)
	return &DeleteWhere{db}
}
func (db *DeleteBuilder) CWhereGte(condition bool, column string, arg interface{}) *DeleteWhere {
	if !condition {
		return &DeleteWhere{db}
	}
	db.isOrWhere = append(db.isOrWhere, false)
	db.whereClauses = append(db.whereClauses, fmt.Sprintf("%s >= ?", column))
	db.deleteArgs = append(db.deleteArgs, arg)
	return &DeleteWhere{db}
}
func (db *DeleteBuilder) CWhereLike(condition bool, column string, arg interface{}) *DeleteWhere {
	if !condition {
		return &DeleteWhere{db}
	}
	db.isOrWhere = append(db.isOrWhere, false)
	db.whereClauses = append(db.whereClauses, fmt.Sprintf("%s LIKE ?", column))
	db.deleteArgs = append(db.deleteArgs, arg)
	return &DeleteWhere{db}
}

func (db *DeleteBuilder) CWhereIn(condition bool, column string, arg interface{}) *DeleteWhere {
	if !condition {
		return &DeleteWhere{db}
	}
	db.isOrWhere = append(db.isOrWhere, false)
	instr := util.ParseInCond(&db.deleteArgs, arg)
	db.whereClauses = append(db.whereClauses, fmt.Sprintf("%s in %s", column, instr))
	return &DeleteWhere{db}
}

func (db *DeleteBuilder) CWhereNIn(condition bool, column string, arg interface{}) *DeleteWhere {
	if !condition {
		return &DeleteWhere{db}
	}
	db.isOrWhere = append(db.isOrWhere, false)
	instr := util.ParseInCond(&db.deleteArgs, arg)
	db.whereClauses = append(db.whereClauses, fmt.Sprintf("%s not in %s", column, instr))
	return &DeleteWhere{db}
}

// Conditions must be complete, eg: column = ?
func (dw *DeleteWhere) Where(exp string, args ...interface{}) *DeleteWhere {
	dw.db.isOrWhere = append(dw.db.isOrWhere, false)
	dw.db.whereClauses = append(dw.db.whereClauses, fmt.Sprintf("(%s)", exp))
	dw.db.deleteArgs = append(dw.db.deleteArgs, args...)
	return dw
}

func (dw *DeleteWhere) WhereEq(column string, arg interface{}) *DeleteWhere {
	dw.db.isOrWhere = append(dw.db.isOrWhere, false)
	dw.db.whereClauses = append(dw.db.whereClauses, fmt.Sprintf("%s = ?", column))
	dw.db.deleteArgs = append(dw.db.deleteArgs, arg)
	return dw
}

func (dw *DeleteWhere) WhereNe(column string, arg interface{}) *DeleteWhere {
	dw.db.isOrWhere = append(dw.db.isOrWhere, false)
	dw.db.whereClauses = append(dw.db.whereClauses, fmt.Sprintf("%s != ?", column))
	dw.db.deleteArgs = append(dw.db.deleteArgs, arg)
	return dw
}
func (dw *DeleteWhere) WhereLt(column string, arg interface{}) *DeleteWhere {
	dw.db.isOrWhere = append(dw.db.isOrWhere, false)
	dw.db.whereClauses = append(dw.db.whereClauses, fmt.Sprintf("%s < ?", column))
	dw.db.deleteArgs = append(dw.db.deleteArgs, arg)
	return dw
}
func (dw *DeleteWhere) WhereLte(column string, arg interface{}) *DeleteWhere {
	dw.db.isOrWhere = append(dw.db.isOrWhere, false)
	dw.db.whereClauses = append(dw.db.whereClauses, fmt.Sprintf("%s <= ?", column))
	dw.db.deleteArgs = append(dw.db.deleteArgs, arg)
	return dw
}
func (dw *DeleteWhere) WhereGt(column string, arg interface{}) *DeleteWhere {
	dw.db.isOrWhere = append(dw.db.isOrWhere, false)
	dw.db.whereClauses = append(dw.db.whereClauses, fmt.Sprintf("%s > ?", column))
	dw.db.deleteArgs = append(dw.db.deleteArgs, arg)
	return dw
}
func (dw *DeleteWhere) WhereGte(column string, arg interface{}) *DeleteWhere {
	dw.db.isOrWhere = append(dw.db.isOrWhere, false)
	dw.db.whereClauses = append(dw.db.whereClauses, fmt.Sprintf("%s >= ?", column))
	dw.db.deleteArgs = append(dw.db.deleteArgs, arg)
	return dw
}
func (dw *DeleteWhere) WhereLike(column string, arg interface{}) *DeleteWhere {
	dw.db.isOrWhere = append(dw.db.isOrWhere, false)
	dw.db.whereClauses = append(dw.db.whereClauses, fmt.Sprintf("%s LIKE ?", column))
	dw.db.deleteArgs = append(dw.db.deleteArgs, arg)
	return dw
}
func (dw *DeleteWhere) WhereIn(column string, arg interface{}) *DeleteWhere {
	dw.db.isOrWhere = append(dw.db.isOrWhere, false)
	instr := util.ParseInCond(&dw.db.deleteArgs, arg)
	dw.db.whereClauses = append(dw.db.whereClauses, fmt.Sprintf("%s in %s", column, instr))
	return dw
}
func (dw *DeleteWhere) WhereNIn(column string, arg interface{}) *DeleteWhere {
	dw.db.isOrWhere = append(dw.db.isOrWhere, false)
	instr := util.ParseInCond(&dw.db.deleteArgs, arg)
	dw.db.whereClauses = append(dw.db.whereClauses, fmt.Sprintf("%s not in %s", column, instr))
	return dw
}

// Conditions must be complete, eg: column = ?
func (dw *DeleteWhere) CWhere(condition bool, exp string, args ...interface{}) *DeleteWhere {
	if !condition {
		return dw
	}
	dw.db.isOrWhere = append(dw.db.isOrWhere, false)
	dw.db.whereClauses = append(dw.db.whereClauses, fmt.Sprintf("(%s)", exp))
	dw.db.deleteArgs = append(dw.db.deleteArgs, args...)
	return dw
}

func (dw *DeleteWhere) CWhereEq(condition bool, column string, arg interface{}) *DeleteWhere {
	if !condition {
		return dw
	}
	dw.db.isOrWhere = append(dw.db.isOrWhere, false)
	dw.db.whereClauses = append(dw.db.whereClauses, fmt.Sprintf("%s = ?", column))
	dw.db.deleteArgs = append(dw.db.deleteArgs, arg)
	return dw
}

func (dw *DeleteWhere) CWhereNe(condition bool, column string, arg interface{}) *DeleteWhere {
	if !condition {
		return dw
	}
	dw.db.isOrWhere = append(dw.db.isOrWhere, false)
	dw.db.whereClauses = append(dw.db.whereClauses, fmt.Sprintf("%s != ?", column))
	dw.db.deleteArgs = append(dw.db.deleteArgs, arg)
	return dw
}
func (dw *DeleteWhere) CWhereLt(condition bool, column string, arg interface{}) *DeleteWhere {
	if !condition {
		return dw
	}
	dw.db.isOrWhere = append(dw.db.isOrWhere, false)
	dw.db.whereClauses = append(dw.db.whereClauses, fmt.Sprintf("%s < ?", column))
	dw.db.deleteArgs = append(dw.db.deleteArgs, arg)
	return dw
}
func (dw *DeleteWhere) CWhereLte(condition bool, column string, arg interface{}) *DeleteWhere {
	if !condition {
		return dw
	}
	dw.db.isOrWhere = append(dw.db.isOrWhere, false)
	dw.db.whereClauses = append(dw.db.whereClauses, fmt.Sprintf("%s <= ?", column))
	dw.db.deleteArgs = append(dw.db.deleteArgs, arg)
	return dw
}
func (dw *DeleteWhere) CWhereGt(condition bool, column string, arg interface{}) *DeleteWhere {
	if !condition {
		return dw
	}
	dw.db.isOrWhere = append(dw.db.isOrWhere, false)
	dw.db.whereClauses = append(dw.db.whereClauses, fmt.Sprintf("%s > ?", column))
	dw.db.deleteArgs = append(dw.db.deleteArgs, arg)
	return dw
}
func (dw *DeleteWhere) CWhereGte(condition bool, column string, arg interface{}) *DeleteWhere {
	if !condition {
		return dw
	}
	dw.db.isOrWhere = append(dw.db.isOrWhere, false)
	dw.db.whereClauses = append(dw.db.whereClauses, fmt.Sprintf("%s >= ?", column))
	dw.db.deleteArgs = append(dw.db.deleteArgs, arg)
	return dw
}
func (dw *DeleteWhere) CWhereLike(condition bool, column string, arg interface{}) *DeleteWhere {
	if !condition {
		return dw
	}
	dw.db.isOrWhere = append(dw.db.isOrWhere, false)
	dw.db.whereClauses = append(dw.db.whereClauses, fmt.Sprintf("%s LIKE ?", column))
	dw.db.deleteArgs = append(dw.db.deleteArgs, arg)
	return dw
}
func (dw *DeleteWhere) CWhereIn(condition bool, column string, arg interface{}) *DeleteWhere {
	if !condition {
		return dw
	}
	dw.db.isOrWhere = append(dw.db.isOrWhere, false)
	instr := util.ParseInCond(&dw.db.deleteArgs, arg)
	dw.db.whereClauses = append(dw.db.whereClauses, fmt.Sprintf("%s in %s", column, instr))
	return dw
}
func (dw *DeleteWhere) CWhereNIn(condition bool, column string, arg interface{}) *DeleteWhere {
	if !condition {
		return dw
	}
	dw.db.isOrWhere = append(dw.db.isOrWhere, false)
	instr := util.ParseInCond(&dw.db.deleteArgs, arg)
	dw.db.whereClauses = append(dw.db.whereClauses, fmt.Sprintf("%s not in %s", column, instr))
	return dw
}

// Conditions must be complete, eg: column = ?
func (dw *DeleteWhere) OrWhere(exp string, args ...interface{}) *DeleteWhere {
	dw.db.isOrWhere = append(dw.db.isOrWhere, true)
	dw.db.whereClauses = append(dw.db.whereClauses, fmt.Sprintf("(%s)", exp))
	dw.db.deleteArgs = append(dw.db.deleteArgs, args...)
	return dw
}
func (dw *DeleteWhere) OrWhereEq(column string, arg interface{}) *DeleteWhere {
	dw.db.isOrWhere = append(dw.db.isOrWhere, true)
	dw.db.whereClauses = append(dw.db.whereClauses, fmt.Sprintf("%s = ?", column))
	dw.db.deleteArgs = append(dw.db.deleteArgs, arg)
	return dw
}
func (dw *DeleteWhere) OrWhereNe(column string, arg interface{}) *DeleteWhere {
	dw.db.isOrWhere = append(dw.db.isOrWhere, true)
	dw.db.whereClauses = append(dw.db.whereClauses, fmt.Sprintf("%s != ?", column))
	dw.db.deleteArgs = append(dw.db.deleteArgs, arg)
	return dw
}
func (dw *DeleteWhere) OrWhereLt(column string, arg interface{}) *DeleteWhere {
	dw.db.isOrWhere = append(dw.db.isOrWhere, true)
	dw.db.whereClauses = append(dw.db.whereClauses, fmt.Sprintf("%s < ?", column))
	dw.db.deleteArgs = append(dw.db.deleteArgs, arg)
	return dw
}
func (dw *DeleteWhere) OrWhereLte(column string, arg interface{}) *DeleteWhere {
	dw.db.isOrWhere = append(dw.db.isOrWhere, true)
	dw.db.whereClauses = append(dw.db.whereClauses, fmt.Sprintf("%s <= ?", column))
	dw.db.deleteArgs = append(dw.db.deleteArgs, arg)
	return dw
}
func (dw *DeleteWhere) OrWhereGt(column string, arg interface{}) *DeleteWhere {
	dw.db.isOrWhere = append(dw.db.isOrWhere, true)
	dw.db.whereClauses = append(dw.db.whereClauses, fmt.Sprintf("%s > ?", column))
	dw.db.deleteArgs = append(dw.db.deleteArgs, arg)
	return dw
}
func (dw *DeleteWhere) OrWhereGte(column string, arg interface{}) *DeleteWhere {
	dw.db.isOrWhere = append(dw.db.isOrWhere, true)
	dw.db.whereClauses = append(dw.db.whereClauses, fmt.Sprintf("%s >= ?", column))
	dw.db.deleteArgs = append(dw.db.deleteArgs, arg)
	return dw
}
func (dw *DeleteWhere) OrWhereLike(column string, arg interface{}) *DeleteWhere {
	dw.db.isOrWhere = append(dw.db.isOrWhere, true)
	dw.db.whereClauses = append(dw.db.whereClauses, fmt.Sprintf("%s LIKE ?", column))
	dw.db.deleteArgs = append(dw.db.deleteArgs, arg)
	return dw
}
func (dw *DeleteWhere) OrWhereIn(column string, arg interface{}) *DeleteWhere {
	dw.db.isOrWhere = append(dw.db.isOrWhere, true)
	instr := util.ParseInCond(&dw.db.deleteArgs, arg)
	dw.db.whereClauses = append(dw.db.whereClauses, fmt.Sprintf("%s in %s", column, instr))
	return dw
}
func (dw *DeleteWhere) OrWhereNIn(column string, arg interface{}) *DeleteWhere {
	dw.db.isOrWhere = append(dw.db.isOrWhere, true)
	instr := util.ParseInCond(&dw.db.deleteArgs, arg)
	dw.db.whereClauses = append(dw.db.whereClauses, fmt.Sprintf("%s not in %s", column, instr))
	return dw
}

// Conditions must be complete, eg: column = ?
func (dw *DeleteWhere) COrWhere(condition bool, exp string, args ...interface{}) *DeleteWhere {
	if !condition {
		return dw
	}
	dw.db.isOrWhere = append(dw.db.isOrWhere, true)
	dw.db.whereClauses = append(dw.db.whereClauses, fmt.Sprintf("(%s)", exp))
	dw.db.deleteArgs = append(dw.db.deleteArgs, args...)
	return dw
}
func (dw *DeleteWhere) COrWhereEq(condition bool, column string, arg interface{}) *DeleteWhere {
	if !condition {
		return dw
	}
	dw.db.isOrWhere = append(dw.db.isOrWhere, true)
	dw.db.whereClauses = append(dw.db.whereClauses, fmt.Sprintf("%s = ?", column))
	dw.db.deleteArgs = append(dw.db.deleteArgs, arg)
	return dw
}
func (dw *DeleteWhere) COrWhereNe(condition bool, column string, arg interface{}) *DeleteWhere {
	if !condition {
		return dw
	}
	dw.db.isOrWhere = append(dw.db.isOrWhere, true)
	dw.db.whereClauses = append(dw.db.whereClauses, fmt.Sprintf("%s != ?", column))
	dw.db.deleteArgs = append(dw.db.deleteArgs, arg)
	return dw
}
func (dw *DeleteWhere) COrWhereLt(condition bool, column string, arg interface{}) *DeleteWhere {
	if !condition {
		return dw
	}
	dw.db.isOrWhere = append(dw.db.isOrWhere, true)
	dw.db.whereClauses = append(dw.db.whereClauses, fmt.Sprintf("%s < ?", column))
	dw.db.deleteArgs = append(dw.db.deleteArgs, arg)
	return dw
}
func (dw *DeleteWhere) COrWhereLte(condition bool, column string, arg interface{}) *DeleteWhere {
	if !condition {
		return dw
	}
	dw.db.isOrWhere = append(dw.db.isOrWhere, true)
	dw.db.whereClauses = append(dw.db.whereClauses, fmt.Sprintf("%s <= ?", column))
	dw.db.deleteArgs = append(dw.db.deleteArgs, arg)
	return dw
}
func (dw *DeleteWhere) COrWhereGt(condition bool, column string, arg interface{}) *DeleteWhere {
	if !condition {
		return dw
	}
	dw.db.isOrWhere = append(dw.db.isOrWhere, true)
	dw.db.whereClauses = append(dw.db.whereClauses, fmt.Sprintf("%s > ?", column))
	dw.db.deleteArgs = append(dw.db.deleteArgs, arg)
	return dw
}
func (dw *DeleteWhere) COrWhereGte(condition bool, column string, arg interface{}) *DeleteWhere {
	if !condition {
		return dw
	}
	dw.db.isOrWhere = append(dw.db.isOrWhere, true)
	dw.db.whereClauses = append(dw.db.whereClauses, fmt.Sprintf("%s >= ?", column))
	dw.db.deleteArgs = append(dw.db.deleteArgs, arg)
	return dw
}
func (dw *DeleteWhere) COrWhereLike(condition bool, column string, arg interface{}) *DeleteWhere {
	if !condition {
		return dw
	}
	dw.db.isOrWhere = append(dw.db.isOrWhere, true)
	dw.db.whereClauses = append(dw.db.whereClauses, fmt.Sprintf("%s LIKE ?", column))
	dw.db.deleteArgs = append(dw.db.deleteArgs, arg)
	return dw
}
func (dw *DeleteWhere) COrWhereIn(condition bool, column string, arg interface{}) *DeleteWhere {
	if !condition {
		return dw
	}
	dw.db.isOrWhere = append(dw.db.isOrWhere, true)
	instr := util.ParseInCond(&dw.db.deleteArgs, arg)
	dw.db.whereClauses = append(dw.db.whereClauses, fmt.Sprintf("%s in %s", column, instr))
	return dw
}
func (dw *DeleteWhere) COrWhereNIn(condition bool, column string, arg interface{}) *DeleteWhere {
	if !condition {
		return dw
	}
	dw.db.isOrWhere = append(dw.db.isOrWhere, true)
	instr := util.ParseInCond(&dw.db.deleteArgs, arg)
	dw.db.whereClauses = append(dw.db.whereClauses, fmt.Sprintf("%s not in %s", column, instr))
	return dw
}

func (dw *DeleteWhere) OrderBy(orderBy string) *DeleteOrderBy {
	return &DeleteOrderBy{dw.db, []string{orderBy}}
}
func (dw *DeleteWhere) OrderByDesc(orderBy string) *DeleteOrderBy {
	return &DeleteOrderBy{dw.db, []string{fmt.Sprintf("%s DESC", orderBy)}}
}

func (ob *DeleteOrderBy) Limit(count int) *FinalDelete {
	limitClause := fmt.Sprintf("%d", count)
	return &FinalDelete{ob.db, ob.orderBy, limitClause}
}

func (ob *DeleteOrderBy) OrderBy(orderBy string) *DeleteOrderBy {
	ob.orderBy = append(ob.orderBy, orderBy)
	return ob
}
func (ob *DeleteOrderBy) OrderByDesc(orderBy string) *DeleteOrderBy {
	ob.orderBy = append(ob.orderBy, fmt.Sprintf("%s DESC", orderBy))
	return ob
}

func (dw *DeleteWhere) Limit(count int) *FinalDelete {
	limitClause := fmt.Sprintf("%d", count)
	return &FinalDelete{dw.db, nil, limitClause}
}

func (dw *DeleteWhere) Build() (string, []interface{}) {
	var queryParts []string
	queryParts = append(queryParts, dw.db.baseDelete)

	if len(dw.db.whereClauses) > 0 {
		whereClause := where(dw.db.whereClauses, dw.db.isOrWhere)
		queryParts = append(queryParts, whereClause)
	} else {
		panic(fmt.Errorf("no where condition found, can not delete, %s", dw.db.baseDelete))
	}

	return strings.Join(queryParts, " "), dw.db.deleteArgs
}

func (fu *FinalDelete) Build() (string, []interface{}) {
	var queryParts []string
	queryParts = append(queryParts, fu.db.baseDelete)

	if len(fu.db.whereClauses) > 0 {
		whereClause := where(fu.db.whereClauses, fu.db.isOrWhere)
		queryParts = append(queryParts, whereClause)
	} else {
		panic(fmt.Errorf("no where condition found, can not delete, %s", fu.db.baseDelete))
	}

	if len(fu.orderBy) > 0 {
		queryParts = append(queryParts, "ORDER BY "+strings.Join(fu.orderBy, ", "))
	}

	if fu.limitClause != "" {
		queryParts = append(queryParts, "LIMIT "+fu.limitClause)
	}

	return strings.Join(queryParts, " "), fu.db.deleteArgs
}
