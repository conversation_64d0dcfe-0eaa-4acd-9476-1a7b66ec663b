package dao

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/juju/errors"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	log "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/monitor"
	"tencentcloud.com/tstream_galileo/src/common/rpcctx"
	"tencentcloud.com/tstream_galileo/src/common/util"
)

type queryTemplate struct {
	datasource *sql.DB
}

func (this *queryTemplate) DoQuery(sql string, args []interface{}) (count int, data map[int]map[string][]byte, err error) {
	return this.DoQueryWithContext(context.Background(), sql, args)
}

func (this *queryTemplate) DoQueryWithContext(ctx context.Context,
	sql string, args []interface{}) (count int, data map[int]map[string][]byte, err error) {
	msg, ctx := rpcctx.CreateClientMessage(ctx,
		rpcctx.WithCallerServerName("galileo"),
		rpcctx.WithCalleeServerName("mysql"),
		rpcctx.WithCalleeRPCName("DoQuery"))
	defer rpcctx.PutBackMessage(msg)
	ctx, holder := monitor.StartClientNoInject(ctx)
	defer func() {
		// 注意: 不能简化成defer holder.Report(err)
		holder.Report(ctx, err)
	}()
	sql = encodeSql(sql)
	log.Debugf("%s, %+v", sql, args)
	rows, err := this.datasource.Query(sql, args...)
	if err != nil {
		msg := fmt.Sprintf("queryTemplate->DoQuery sql: %s, err: %+v ", util.LogSql(sql, args), err)
		return 0, nil, errorcode.InternalErrorCode_DoSqlFailed.NewWithInfo(msg, err)
	}
	defer rows.Close()
	count, data, err = util.MakeMapByteResult(rows)
	return count, data, errorcode.InternalErrorCode_DoSqlFailed.NewWithErr(err)
}

func (this *queryTemplate) DoQueryWithArgs(sql string, args ...interface{}) (count int, data map[int]map[string][]byte, err error) {
	return this.DoQueryWithArgsAndContext(context.Background(), sql, args...)
}

func (this *queryTemplate) DoQueryWithArgsAndContext(ctx context.Context,
	sql string, args ...interface{}) (count int, data map[int]map[string][]byte, err error) {
	msg, ctx := rpcctx.CreateClientMessage(ctx,
		rpcctx.WithCallerServerName("galileo"),
		rpcctx.WithCalleeServerName("mysql"),
		rpcctx.WithCalleeRPCName("DoQueryWithArgs"))
	defer rpcctx.PutBackMessage(msg)
	ctx, holder := monitor.StartClientNoInject(ctx)
	defer func() {
		// 注意: 不能简化成defer holder.Report(err)
		holder.Report(ctx, err)
	}()
	sql = encodeSql(sql)
	log.Debugf("%s, %+v", sql, args)
	rows, err := this.datasource.Query(sql, args...)
	if err != nil {
		msg := fmt.Sprintf("queryTemplate->DoQueryWithArgs sql: %s, err: %+v ", util.LogSql(sql, args), err)
		return 0, nil, errorcode.InternalErrorCode_DoSqlFailed.NewWithInfo(msg, err)
	}
	defer rows.Close()
	count, data, err = util.MakeMapByteResult(rows)
	return count, data, errorcode.InternalErrorCode_DoSqlFailed.NewWithErr(err)
}

// 获取对象
func (this *queryTemplate) GetObjectBySql(sql string, obj interface{}, args ...interface{}) {
	this.GetObjectBySqlWithContext(context.Background(), sql, obj, args...)
}

func (this *queryTemplate) GetObjectBySqlWithContext(ctx context.Context,
	sqlContent string, obj interface{}, args ...interface{}) {
	var err error
	msg, ctx := rpcctx.CreateClientMessage(ctx,
		rpcctx.WithCallerServerName("galileo"),
		rpcctx.WithCalleeServerName("mysql"),
		rpcctx.WithCalleeRPCName("GetObjectBySql"))
	defer rpcctx.PutBackMessage(msg)
	ctx, holder := monitor.StartClientNoInject(ctx)
	defer func() {
		// 注意: 不能简化成defer holder.Report(err)
		holder.Report(ctx, err)
	}()
	sqlContent = encodeSql(sqlContent)
	var rows *sql.Rows
	rows, err = this.datasource.Query(sqlContent, args...)
	if err != nil {
		log.Error("queryTemplate->GetObjectBySql->Query err:", err, " for: sql ", util.LogSql(sqlContent, args))
		panic(errors.Trace(err))
	}
	defer func() {
		if rows != nil {
			rows.Close()
		}
	}()
	count, bytemap, err := util.MakeMapByteResult(rows)
	if err != nil {
		log.Error("queryTemplate->GetObjectBySql->MakeMapByteResult err:", err, " for: sql ", util.LogSql(sqlContent, args))
		panic(errors.Trace(err))
	}
	if count == 0 {
		return
	}
	datamap, _ := bytemap[0]

	err = util.ScanMapIntoStruct(obj, datamap)
	if err != nil {
		log.Error("queryTemplate->GetObjectBySql->ScanMapIntoStruct err:", err, " for: sql ", util.LogSql(sqlContent, args))
		panic(errors.Trace(err))
	}
}
