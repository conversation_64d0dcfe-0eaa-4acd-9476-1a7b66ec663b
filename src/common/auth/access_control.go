package auth

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"git.code.oa.com/rainbow/golang-sdk/log"
	"io"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/audit"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/role_auth"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	auditService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/audit"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/role_auth"
	rolePermissionService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/role_permission"
	"unicode"
)

/**
 * Created by Andy on 2021/12/1 5:20 下午.
 * At tencent
 */
const (
	RETRY_COUNT                  = 10
	AUTH_GROUP_KEY               = "Authentication"
	AUTH_APIS_KEY                = "AuthApis"
	ROLE_AUTH_CHS_KEY            = "RoleAuthCH"
	ROLE_AUTH_KEY                = "RoleAuth"
	ROLE_AUTH_CLASSIFICATION     = "AuthMisclassification"
	ROLE_AUTH_CLASSIFICATION_I18 = "AuthMisclassification_i18"
	QSROLE_AUTH                  = "AuthQCSRole"
	AuditFilterActionsKey        = "AuditFilterActions"
	AuditIncludeActionsKey       = "AuditIncludeActions"
	AuditIncludeEventActionsKey  = "AuditIncludeEventActions"
	SQL_CODE_KEY                 = "SqlCode"
	PROGRAM_ARGS_KEY             = "ProgramArgs"
)

var Roles map[string]map[string]bool
var checkAuthAPis []string
var RoleDevelopers map[string]bool
var RoleViewer map[string]bool
var RoleItemOwner map[string]bool
var RoleAdministor map[string]bool

var AuthQCSRole []string
var RolesCHS map[string]map[string][]string
var RoleDevelopersCHS map[string][]string
var RoleViewerCHS map[string][]string
var RoleItemOwnerCHS map[string][]string
var RoleAdministorCHS map[string][]string

var AuthClassification map[string]string
var AuthClassification_i18 map[string]string

var AuditFilterActions []string
var AuditIncludeActions []string

var AuditIncludeEventActions []string

const (
	ROLE_ADMINISTOR = "Administer"
	ITEMOWNER       = "ItemOwner"
	DEVELOPERS      = "Developers"
	VIEWER          = "Viewer"
)

func InitAuthQCSRole() {
	if err := config.DecodeK8sObjectFromRainbowConfig(
		AUTH_GROUP_KEY, QSROLE_AUTH, &AuthQCSRole); err != nil {
		panic(err)
		return
	}
}

func InitAuditFilterActions() {
	if err := config.DecodeK8sObjectFromRainbowConfig(
		AUTH_GROUP_KEY, AuditFilterActionsKey, &AuditFilterActions); err != nil {
		panic(err)
		return
	}
}

func InitAuditIncludeActions() {
	if err := config.DecodeK8sObjectFromRainbowConfig(
		AUTH_GROUP_KEY, AuditIncludeActionsKey, &AuditIncludeActions); err != nil {
		panic(err)
		return
	}
}

func InitAuditIncludeEventActions() {
	if err := config.DecodeK8sObjectFromRainbowConfig(
		AUTH_GROUP_KEY, AuditIncludeEventActionsKey, &AuditIncludeEventActions); err != nil {
		panic(err)
		return
	}
}

func InitClassification(language string) {
	if language == constants.CHINESE {
		if len(AuthClassification) == 0 {
			apis := config.MustGetRainbowConfigurationWithRetry(
				AUTH_GROUP_KEY, ROLE_AUTH_CLASSIFICATION, RETRY_COUNT)
			if err := json.Unmarshal([]byte(apis), &AuthClassification); err != nil {
				panic(errorcode.NewStackError(errorcode.InternalErrorCode, "cant deserialize "+AUTH_GROUP_KEY, err))
			}
		}
	} else {
		if len(AuthClassification_i18) == 0 {
			apis := config.MustGetRainbowConfigurationWithRetry(
				AUTH_GROUP_KEY, ROLE_AUTH_CLASSIFICATION_I18, RETRY_COUNT)
			if err := json.Unmarshal([]byte(apis), &AuthClassification_i18); err != nil {
				panic(errorcode.NewStackError(errorcode.InternalErrorCode, "cant deserialize "+AUTH_GROUP_KEY, err))
			}
		}
	}
}

func InitRoleCHS() {
	if len(RoleAdministorCHS) == 0 {
		DevelopersRule := config.MustGetRainbowConfigurationWithRetry(
			AUTH_GROUP_KEY, ROLE_AUTH_CHS_KEY, RETRY_COUNT)
		if err := json.Unmarshal([]byte(DevelopersRule), &RolesCHS); err != nil {
			panic(errorcode.NewStackError(errorcode.InternalErrorCode, "cant deserialize "+ROLE_AUTH_KEY, err))
		}
		RoleDevelopersCHS = RolesCHS[DEVELOPERS]
		RoleViewerCHS = RolesCHS[VIEWER]
		RoleItemOwnerCHS = RolesCHS[ITEMOWNER]
		RoleAdministorCHS = RolesCHS[ROLE_ADMINISTOR]
	}
}

func GetAuthApiArray() []string {
	return rolePermissionService.ListControllerNames(-1)
}

func GetAuthQCSRoleArray() []string {
	if len(AuthQCSRole) == 0 {
		InitAuthQCSRole()
	}
	return AuthQCSRole
}

func GetAuditFilterActions() []string {
	if len(AuditFilterActions) == 0 {
		InitAuditFilterActions()
	}
	return AuditFilterActions
}

func GetAuditIncludeActions() []string {
	if len(AuditIncludeActions) == 0 {
		InitAuditIncludeActions()
	}
	return AuditIncludeActions
}

func GetAuditIncludeEventActions() []string {
	if len(AuditIncludeEventActions) == 0 {
		InitAuditIncludeEventActions()
	}
	return AuditIncludeEventActions
}

func GetAuthClassification(language string) map[string]string {
	if language == constants.CHINESE {
		if len(AuthClassification) == 0 {
			InitClassification(language)
		}
		return AuthClassification
	} else {
		if len(AuthClassification_i18) == 0 {
			InitClassification(language)
		}
		return AuthClassification_i18
	}
}

func CheckIsSelfAuth(action string, reqData map[string]interface{}, requestId string) bool {
	var IsOwner int64
	if isOwner, ok := reqData["IsSupOwner"]; !ok {
		//log.Errorf("%s: The interface has no input parameter %s", "IsSupOwner", requestId)
	} else {
		IsOwner = int64(isOwner.(int))
	}

	if IsOwner > 0 {
		return false
	}

	if ok, _ := service.Contain(action, GetAuthApiArray()); ok {
		return ok
	}
	return false
}

// CheckIsOuterItemSpaceAutoApi 全局管理员权限为true,空间管理员为 false 的权限
func CheckIsOuterItemSpaceAutoApi(action string) bool {
	pass1, _ := CheckAuth(action, constants.ROLE_AUTHO_PERMISSION_SUPER_OWNER)
	pass2, _ := CheckAuth(action, constants.ROLE_AUTHO_PERMISSION_OWNER)
	if pass1 && !pass2 {
		return true
	}
	return false
}

func DecompressData(data string) (string, error) {
	rawData, err := base64.StdEncoding.DecodeString(data)
	if err != nil {
		log.Errorf("base64.StdEncoding.DecodeString: %s panic ,errors:%+v", data, err)
		return "", err
	}

	gzipReader, err := gzip.NewReader(bytes.NewReader(rawData))
	if err != nil {
		log.Errorf("parse gzip data: %+v panic ,errors:%+v", rawData, err)
		return "", err
	}

	var buf bytes.Buffer
	if _, err := io.Copy(&buf, gzipReader); err != nil {
		_ = gzipReader.Close() // 尝试关闭
		return "", err
	}

	// 显式关闭并处理错误
	if err := gzipReader.Close(); err != nil {
		log.Errorf("Gzip reader close error: %+v", err)
	}
	return buf.String(), nil
}

func CheckIsOwner(oriData map[string]interface{}, reqStruct map[string]interface{}) (newData []byte, err error) {

	if reqStruct == nil {
		return nil, nil
	}

	var SubUin string
	var Appid int64
	var Uin string
	var Action string

	if uin, ok := oriData["SubAccountUin"]; !ok {
		log.Errorf("The interface has no input parameter SubAccountUin")
	} else {
		SubUin = uin.(string)
	}

	if appid, ok := oriData["AppId"]; !ok {
		log.Errorf("The interface has no input parameter Appid")
	} else {
		Appid = int64(appid.(float64))
	}

	if uin, ok := oriData["Uin"]; !ok {
		log.Errorf("The interface has no input parameter %s", "SubAccountUin")
	} else {
		Uin = uin.(string)
	}

	if action, ok := oriData["Action"]; !ok {
		log.Errorf("The interface has no input parameter %s", "Action")
	} else {
		Action = action.(string)
	}

	if sqlCodeAfterGzip, ok := oriData["SqlCodeAfterGzip"]; ok {
		sqlCodeAfterGzipStr := sqlCodeAfterGzip.(string)
		if len(sqlCodeAfterGzipStr) > 0 {
			log.Debugf("The interface has input parameter %s", "SqlCodeAfterGzip")
			sqlCode, err := DecompressData(sqlCodeAfterGzipStr)
			if err != nil {
				log.Errorf("BeforeProcessReq DecompressData  SqlCodeAfterGzip error: %+v", err)
				return nil, err
			}
			oriData[SQL_CODE_KEY] = sqlCode
			if reqStruct != nil {
				reqStruct[SQL_CODE_KEY] = sqlCode
			}
		}
	}

	if programArgsAfterGzip, ok := oriData["ProgramArgsAfterGzip"]; ok {
		programArgsAfterGzipStr := programArgsAfterGzip.(string)
		if len(programArgsAfterGzipStr) > 0 {
			log.Debugf("The interface has input parameter %s", "ProgramArgsAfterGzip")
			programArgs, err := DecompressData(programArgsAfterGzipStr)
			if err != nil {
				log.Errorf("BeforeProcessReq DecompressData programArgsAfterGzip  error: %+v", err)
				return nil, err
			}
			oriData[PROGRAM_ARGS_KEY] = programArgs
			if reqStruct != nil {
				reqStruct[PROGRAM_ARGS_KEY] = programArgs
			}
		}
	}

	if SubUin != "" && Uin != "" {
		if SubUin == Uin {
			oriData["IsSupOwner"] = 1
			if reqStruct != nil {
				reqStruct["IsSupOwner"] = 1
			}

			newData, err = json.Marshal(oriData)
			if err != nil {
				logger.Errorf("BeforeProcessReq Marshal error: %+v", err)
				return nil, err
			} else {
				return newData, err
			}
		}
	}

	if Action != "" {
		interfaceName := reqStruct["Action"].(string)
		if ok, _ := service.Contain(interfaceName, GetAuthApiArray()); !ok {
			return nil, nil
		}
	}

	if SubUin != "" && Appid != 0 {
		// 针对托管账号的处理
		admin, err := service2.RoleAuthoAdministorCheck(Appid, SubUin)
		if err != nil {
			logger.Errorf("BeforeProcessReq Query Administer error: %+v", err)
			err = errorcode.UnsupportedOperation_NoPermissionAccess.ReplaceDesc("Query Administor error")
			return nil, err
		}
		administrator, _, _, err := processEscrowAccount(oriData, Appid, SubUin, Uin, admin)
		if err != nil {
			msg := fmt.Sprintf("processEscrowAccount  SubAccountUin is %v error: %+v", SubUin, err)
			logger.Error(msg)
			err = errorcode.InternalErrorCode.ReplaceDesc(msg)
			return nil, err
		}
		if administrator != nil {
			admin = administrator
		}
		if admin != nil && admin.Status != constants.ROLE_AUTHO_STATUS_DISEABLE {
			oriData["IsSupOwner"] = 1
			if reqStruct != nil {
				reqStruct["IsSupOwner"] = 1
			}
			newData, err = json.Marshal(oriData)
			if err != nil {
				log.Errorf("BeforeProcessReq Marshal error: %+v", err)
				return nil, err
			} else {
				return newData, err
			}
		}
	}

	return nil, nil
}

func Audit(reqData map[string]interface{}, requestId string, action string, cloudApiErrorCode string, msg string, rspData interface{}) {
	// 只记录配置的action
	ok, _ := service.Contain(action, GetAuditIncludeActions())
	if !ok {
		return
	}

	var SubUin string
	var AppId int64
	var OwnerUin string

	if uin, ok := reqData["SubAccountUin"]; !ok {
		log.Errorf("%s: The interface has no input parameter %s", "SubAccountUin", requestId)
	} else {
		SubUin = uin.(string)
	}

	if ownerUin, ok := reqData["Uin"]; !ok {
		log.Errorf("%s: The interface has no input parameter %s", "Uin", requestId)
	} else {
		OwnerUin = ownerUin.(string)
	}

	if appid, ok := reqData["AppId"]; !ok {
		log.Errorf("%s: The interface has no input parameter %s", "Appid", requestId)
	} else {
		AppId = int64(appid.(float64))
	}
	// 过滤掉不需要的信息
	reqData["CamContext"] = ""
	reqData["Token"] = ""

	var truncatedReqString = ""
	var truncatedRspString = ""

	reqBytes, _ := json.Marshal(reqData)
	reqStr := string(reqBytes)
	truncatedReqString = service.Substr(reqStr, 0, 1000)
	// 失败记录 输出
	if cloudApiErrorCode != "" && cloudApiErrorCode != controller.OK {
		rspBytes, _ := json.Marshal(rspData)
		rspStr := string(rspBytes)
		truncatedRspString = service.Substr(rspStr, 0, 1000)
	}

	operation := &audit.Operation{
		AppId:      AppId,
		OwnerUin:   OwnerUin,
		OperateUin: SubUin,
		Action:     action,
		RequestId:  requestId,
		ResultCode: cloudApiErrorCode,
		Request:    truncatedReqString,
		Response:   truncatedRspString,
		ErrorMsg:   msg,
		CreateTime: util.GetCurrentTime(),
	}
	auditService.CreateOperation(operation)
}

func AuditEvent(reqData interface{}, requestId string, action string, cloudApiErrorCode string, msg string, rspData interface{}) {
	// 只记录配置的action
	ok, _ := service.Contain(action, GetAuditIncludeEventActions())
	if !ok {
		return
	}

	var truncatedReqString = ""
	var truncatedRspString = ""

	reqBytes, _ := json.Marshal(reqData)
	reqStr := string(reqBytes)
	truncatedReqString = service.Substr(reqStr, 0, 1000)
	// 失败记录 输出
	if cloudApiErrorCode != "" && cloudApiErrorCode != controller.OK {
		rspBytes, _ := json.Marshal(rspData)
		rspStr := string(rspBytes)
		truncatedRspString = service.Substr(rspStr, 0, 1000)
	}

	operation := &audit.Operation{
		AppId:      0,
		OwnerUin:   "",
		OperateUin: "",
		Action:     action,
		RequestId:  requestId,
		ResultCode: cloudApiErrorCode,
		Request:    truncatedReqString,
		Response:   truncatedRspString,
		ErrorMsg:   msg,
		CreateTime: util.GetCurrentTime(),
	}
	auditService.CreateOperation(operation)
}

// CheckAuthorization 权限控制
func CheckAuthorization(reqData map[string]interface{}, requestId string, action string) (bool, error) {

	var ItemSpaceId string
	var SubUin string
	var Appid int64
	var IsOwner int64

	if id, ok := reqData["WorkSpaceId"]; ok {
		ItemSpaceId = id.(string)
	}

	if uin, ok := reqData["SubAccountUin"]; !ok {
		log.Errorf("%s: The interface has no input parameter %s", "SubAccountUin", requestId)
	} else {
		SubUin = uin.(string)
	}

	if appid, ok := reqData["AppId"]; !ok {
		log.Errorf("%s: The interface has no input parameter %s", "Appid", requestId)
	} else {
		Appid = int64(appid.(float64))
	}

	if isOwner, ok := reqData["IsSupOwner"]; !ok {
		log.Errorf("%s: The interface has no input parameter %s", "IsSupOwner", requestId)
	} else {
		IsOwner = int64(isOwner.(int))
	}

	if SubUin != "" && Appid != 0 {
		if ItemSpaceId == "" {
			if CheckIsOuterItemSpaceAutoApi(action) {
				logger.Infof(" Administer Operation %s", action)
				if IsOwner == 0 {
					err := errorcode.UnsupportedOperation_NoPermissionAccess.ReplaceDesc("No permission to operate ItemSpace Or Cluster")
					logger.Errorf(" %s: No permission to operate ItemSpace Or Cluster: %+v", requestId, err)
					return false, err
				}
			}
			// TODO 不是超管授权的情况
			return true, nil
		} else {
			if IsOwner > 0 {
				return true, nil
			}
		}
	} else {
		log.Errorf("%s: The interface has no input parameter %s Or %s", requestId, "Appid", "SubAccountUin")
		err := errorcode.InvalidParameterCode.ReplaceDesc(" The interface has no input parameter Appid or SubAccountUin")
		return false, err
	}

	if ItemSpaceId == "" {
		return true, nil
	}
	// 根据ItemSpaceId,SubAccountUin 查找对应的权限
	table, err := service2.RoleAuthoCheck(Appid, ItemSpaceId, SubUin)
	if err != nil {
		logger.Errorf(" Query RoleAuth error: %+v", err)
		err = errorcode.UnsupportedOperation_NoPermissionAccess.ReplaceDesc("Query RoleAuth error")
		return false, err
	}
	if table == nil || table.Status == constants.ROLE_AUTHO_STATUS_DISEABLE {
		err = errors.New("No access to ItemSpace ")
		logger.Errorf(" %s: No access to ItemSpace: %+v", err, requestId)
		err = errorcode.UnsupportedOperation_NoPermissionAccess.ReplaceDesc(" No access to ItemSpace")
		return false, err
	}

	// 空间内部权限控制
	return CheckAuth(action, table.Permission)
}

// CheckAuth 检测开发者权限
func CheckAuth(action string, roleId int64) (pase bool, err error) {
	controllerNames := rolePermissionService.ListControllerNames(roleId)
	ok, _ := service.Contain(action, controllerNames)
	if ok {
		return true, nil
	}
	err = errorcode.UnsupportedOperation_NoPermissionAccess.ReplaceDesc("The user role space permission is limited," +
		" Please ask the space administrator for permission ")
	return false, err
}

func processEscrowAccount(reqData map[string]interface{}, appid int64, subUin string, uin string, admin *table.Administrator) (administrator *table.Administrator, isEscrowA bool, pase bool, err error) {
	// 检测是否是托管账号
	ok, err := checkEscrowAccount(reqData)
	if err != nil || !ok {
		return nil, ok, false, err
	}
	// 是否在白名单中(不在白名单中自行添加)
	if !IsInWhiteList(appid, constants.WHITE_LIST_ALLOCATING_DEFAULT_SPACE) {
		err := AddWhiteList(appid, constants.WHITE_LIST_ALLOCATING_DEFAULT_SPACE)
		if err != nil || !ok {
			return nil, ok, false, err
		}
	}
	//是否为超级管理员
	if admin == nil {
		administrator, err = service2.BuildAndInsertAdminister(appid, subUin, uin)
		if err != nil || !ok {
			return nil, ok, false, err
		}
	} else {
		if admin.Status == constants.ROLE_AUTHO_STATUS_DISEABLE {
			err = service2.BuildAndUpdateAdminister(admin, uin)
			if err != nil || !ok {
				return nil, ok, false, err
			}
			admin.Status = constants.ROLE_AUTHO_STATUS_USEABLE
		}
	}

	// 否有默认空间
	var Region string
	if region, ok := reqData["Region"]; !ok {
		log.Errorf("The interface has no input parameter Region")
	} else {
		Region = fmt.Sprint(region)
	}
	itemSpace, err := CheckDefaultItemSpaceAll(appid, Region, defaultItemSpace+fmt.Sprint(appid)+Region)
	if err != nil {
		return nil, ok, false, err
	}
	if itemSpace == nil {
		err := BuildAndInsertItemSpace(appid, subUin, uin, Region, defaultItemSpace+fmt.Sprint(appid)+Region)
		if err != nil {
			return nil, ok, false, err
		}
	} else {
		if itemSpace.Status == constants.ITEM_SPACE_STATUS_DELETED {
			msg := fmt.Sprintf(" Invalid operation, default space has been deleted. which WorkSpaceId is %v And WorkSpaceName is %v ", itemSpace.SerialId, itemSpace.ItemSpaceName)
			logger.Error(msg)
			err = errorcode.InternalErrorCode.ReplaceDesc(msg)
			return nil, ok, false, err
		}
	}

	// 绑定集群到默认空间
	return administrator, ok, true, nil

}

func checkEscrowAccount(reqData map[string]interface{}) (ok bool, err error) {
	ok = false
	// 检测 SubAccountUin  以 46* 开头
	if uin, ok := reqData["SubAccountUin"]; !ok {
		log.Errorf("The interface has no input parameter SubAccountUin")
	} else {
		if !strings.HasPrefix(fmt.Sprint(uin), "46") {
			return false, nil
		}
	}

	// 检测 TOKEN 的长度
	if token, ok := reqData["Token"]; !ok {
		log.Errorf("The interface has no input parameter Token")
		return false, errors.New("The interface has no input parameter Token ")
	} else {
		if len(fmt.Sprint(token)) <= 50 {
			return false, nil
		}
	}

	// CamContext 包含白名单中 包含云服务的服务角色策略
	if camContext, ok := reqData["CamContext"]; !ok {
		log.Errorf("The interface has no input parameter SubAccountUin")
	} else {
		for _, value := range GetAuthQCSRoleArray() {
			if !strings.Contains(fmt.Sprint(camContext), value) {
				return false, nil
			}
		}
	}
	return true, nil
}

// 检查bucket和region的合法性
func IsLegitimate(str string) bool {
	for _, x := range []rune(str) {
		if !unicode.IsDigit(x) && !unicode.IsLetter(x) && x != '-' {
			return false
		}
	}
	return true
}
