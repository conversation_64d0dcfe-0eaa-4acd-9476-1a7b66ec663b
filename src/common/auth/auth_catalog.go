package auth

import (
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/metadata"
)

/**
 * Created by <PERSON> on 2021/12/26 10:07 下午.
 * At tencent
 */

func InnerAuthCatalog(ItemSpaceSerialId string, isSupOwner int64, tableId int64, appId int64, subUin string, reion string, action string) (id int64, err error) {
	if tableId == 0 {
		logger.Errorf("catalog is  null can not authentication ")
		return 0, nil
	}

	metaSotreDatabase, err := checkCatalog(tableId)
	if err != nil {
		logger.Errorf(" InnerAuth query  ItemSpaces error: %+v", err)
		return 0, err
	}

	return metaSotreDatabase.ItemSpaceId, InnerAuthById(ItemSpaceSerialId, isSupOwner, metaSotreDatabase.ItemSpaceId, appId, subUin, action, false)

}

func checkCatalog(tableId int64) (resources *table.MetastoreDatabase, err error) {

	sql := "select * from MetastoreDatabase  where Id = ( select DatabaseId from MetastoreTable where Id = ? and `Status` = ?)"
	args := make([]interface{}, 0, 0)
	args = append(args, tableId,constants.METADATA_CATALOG_STATUS_ACTIVE)
	cnt, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)

	if cnt == 0 {
		return nil, nil
	}
	resource := &table.MetastoreDatabase{}
	err = util.ScanMapIntoStruct(resource, data[0])
	if err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	} else if len(data) > 1 {
		return nil, errorcode.InternalErrorCode.NewWithInfo(fmt.Sprint(tableId), nil)
	} else if len(data) == 0 {
		msg := fmt.Sprintf("ResourceNotFound: Resource with MetastoreTable: %d", tableId)
		return nil, errorcode.ResourceNotFoundCode.ReplaceDesc(msg)
	}
	return resource, nil
}
