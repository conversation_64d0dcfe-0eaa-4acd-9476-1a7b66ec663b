package auth

import (
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/metadata"
)

/**
 * Created by <PERSON> on 2021/12/26 11:29 下午.
 * At tencent
 */

func InnerAuthMetaDb(ItemSpaceSerialId string, isSupOwner int64, databaseId int64, appId int64, subUin string, region string, action string) (id int64, err error) {
	if databaseId == 0 {
		logger.Errorf("resourceId is Null Can not Auth ")
		return 0, nil
	}
	meta, err := checkMeta(databaseId, appId, region)
	if err != nil {
		logger.Errorf(" InnerAuth query  ItemSpaces error: %+v", err)
		return 0, err
	}
	return meta.ItemSpaceId, InnerAuthById(ItemSpaceSerialId, isSupOwner, meta.ItemSpaceId, appId, subUin, action, false)
}

func InnerAuthMetaTb(ItemSpaceSerialId string, isSupOwner int64, tableId int64, appId int64, subUin string, reion string, action string) (id int64, err error) {
	if tableId == 0 {
		logger.Errorf("resourceId is Null Can not Auth ")
		return 0, nil
	}
	meta, err := checkMetaFormTable(tableId, appId, reion)
	if err != nil {
		logger.Errorf(" InnerAuth query  ItemSpaces error: %+v", err)
		return 0, err
	}
	return meta.ItemSpaceId, InnerAuthById(ItemSpaceSerialId, isSupOwner, meta.ItemSpaceId, appId, subUin, action, false)
}

func InnerAuthMetaCatalog(ItemSpaceSerialId string, isSupOwner int64, serialId string, appId int64, subUin string, region string, action string) (id int64, err error) {
	if len(serialId) == 0 {
		logger.Errorf("resourceId is Null Can not Auth ")
		return 0, nil
	}
	meta, err := CheckMetaCatalogFormSerialId(serialId, appId, region)
	if err != nil {
		logger.Errorf(" InnerAuth query  ItemSpaces error: %+v", err)
		return 0, err
	}
	return meta.ItemSpaceId, InnerAuthById(ItemSpaceSerialId, isSupOwner, meta.ItemSpaceId, appId, subUin, action, false)
}

func checkMeta(databaseId int64, appId int64, region string) (metadbs *table.MetastoreDatabase, err error) {

	args := make([]interface{}, 0, 0)
	args = append(args, databaseId)
	args = append(args, appId)
	args = append(args, region)
	args = append(args, constants.METADATA_CATALOG_STATUS_ACTIVE)

	sql := "SELECT * from MetastoreDatabase WHERE Id =? AND AppId = ? AND Region =? AND `Status` = ?"
	cnt, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)

	if cnt == 0 {
		return nil, nil
	}
	metadb := &table.MetastoreDatabase{}
	err = util.ScanMapIntoStruct(metadb, data[0])
	if err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	} else if len(data) > 1 {
		return nil, errorcode.InternalErrorCode.NewWithInfo(fmt.Sprint(databaseId), nil)
	} else if len(data) == 0 {
		msg := fmt.Sprintf("DatabaseNotFound: Databases with appId: %d, regionId: %s, databaseId: %d", appId, region, databaseId)
		return nil, errorcode.ResourceNotFoundCode.ReplaceDesc(msg)
	}
	return metadb, nil
}

func checkMetaFormTable(tableId int64, appId int64, region string) (metadbs *table.MetastoreDatabase, err error) {

	args := make([]interface{}, 0, 0)
	args = append(args, tableId)
	args = append(args, constants.METADATA_CATALOG_STATUS_ACTIVE)
	args = append(args, appId)
	args = append(args, region)
	args = append(args, constants.METADATA_CATALOG_STATUS_ACTIVE)

	sql := "SELECT * from MetastoreDatabase WHERE Id = ( SELECT DatabaseId from MetastoreTable WHERE Id = ? AND `Status`=?) AND AppId = ? AND Region = ? AND `Status`=?"
	cnt, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)

	if cnt == 0 {
		return nil, nil
	}

	metadb := &table.MetastoreDatabase{}
	err = util.ScanMapIntoStruct(metadb, data[0])
	if err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}
	return metadb, nil
}

func CheckMetaCatalogFormSerialId(serialId string, appId int64, region string) (metadbs *table.MetastoreCatalog, err error) {

	args := make([]interface{}, 0, 0)
	args = append(args, serialId)
	args = append(args, appId)
	args = append(args, region)

	sql := "SELECT * from MetastoreCatalog WHERE SerialId = ? and AppId = ? and Region = ? "
	cnt, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)

	if cnt == 0 {
		return nil, nil
	}

	metadb := &table.MetastoreCatalog{}
	err = util.ScanMapIntoStruct(metadb, data[0])
	if err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}
	return metadb, nil
}
