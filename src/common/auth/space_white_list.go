package auth

import (
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service3 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/white_list"
)

/**
 * Created by <PERSON> on 2021/12/26 5:26 下午.
 * At tencent
 */

// IsInWhiteList 角色权限白名单
func IsInWhiteList(appId int64, types int64) bool {
	return findWhiteList(appId, types)
}

func IsSupportMultipleDeploymentMode(appId int64) bool {
	// 创建共享集群
	if !findWhiteList(appId, constants.WHITE_LIST_CREATE_IN_AGENT) {
		logger.Warningf("AppId: %d, is not support multiple deployment mode, because WHITE_LIST_CREATE_IN_AGENT", appId)
		return false
	}
	// 不是创建独享集群，则不支持多可用区部署
	if findWhiteList(appId, constants.WHITE_LIST_CREATE_PRIVATE_CLUSTER) {
		logger.Warningf("AppId: %d, is not support multiple deployment mode, because WHITE_LIST_CREATE_PRIVATE_CLUSTER", appId)
		return false
	}
	// 共享集群 没有资源共享
	if findWhiteList(appId, constants.WHITE_LIST_RESOURCE_TYPE_SHARE) {
		logger.Warningf("AppId: %d, is not support multiple deployment mode, because WHITE_LIST_RESOURCE_TYPE_SHARE", appId)
		return false
	}
	// 节点弹性网卡
	if findWhiteList(appId, constants.WHITE_LIST_NODE_NETWORK) {
		logger.Warningf("AppId: %d, is not support multiple deployment mode, because WHITE_LIST_NODE_NETWORK", appId)
		return false
	}
	// 共享集群 默认资源独享
	return true
}

func WhiteListValue(appId int64, types int64) (bool, *service.WhiteList) {
	sql := "SELECT * from WhiteList WHERE AppId = ? AND Type = ? "
	args := make([]interface{}, 0)
	args = append(args, appId)
	args = append(args, types)

	txManager := service3.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to execute the sql:%s, with args:%+v, with errors:%+v", sql, args, err)
		return false, nil
	}
	if len(data) == 0 {
		return false, nil
	}

	whiteList := &service.WhiteList{}
	err = util.ScanMapIntoStruct(whiteList, data[0])
	if err != nil {
		logger.Errorf("Failed to convert variable data bytes into struct, with errors:%+v", err)
		return false, nil
	}

	return true, whiteList
}

func findWhiteList(appId int64, types int64) bool {
	sql := "SELECT AppId from WhiteList WHERE AppId = ? AND Type = ? "
	args := make([]interface{}, 0)
	args = append(args, appId)
	args = append(args, types)

	txManager := service3.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to execute the sql:%s, with args:%+v, with errors:%+v", sql, args, err)
		return false
	}
	if len(data) == 0 {
		return false
	}
	if len(data) == 1 {
		return true
	}

	return false
}

func AddWhiteList(appId int64, types int64) error {
	whiteList := &service.WhiteList{}
	whiteList.Type = int8(types)
	whiteList.AppId = appId
	whiteList.CreateTime = util.GetCurrentTime()
	whiteList.UpdateTime = whiteList.CreateTime
	err := SaveTableAdminister(whiteList)
	if err != nil {
		logger.Errorf("Save WhiteList error: %+v", err)
		err = errorcode.InternalErrorCode.ReplaceDesc("Can Not Save WhiteList")
		return err
	}
	return nil
}

func SaveTableAdminister(whiteList *service.WhiteList) (err error) {
	defer errorcode.DefaultDeferHandler(&err)
	txManager := service3.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.SaveObject(whiteList, "WhiteList")
		return nil
	}).Close()
	return err
}
