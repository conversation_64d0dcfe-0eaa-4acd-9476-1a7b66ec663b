package auth

import (
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service2 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/tree"
)

/**
 * Created by <PERSON> on 2021/12/27 7:55 下午.
 * At tencent
 */

func InnerAuthFold(ItemSpaceSerialId string, isSupOwner int64, FolderId string, appId int64, subUin string, region string, action string, tableName string) (id int64, err error) {
	if FolderId == "" {
		logger.Errorf("FolderId is Null Can not Auth ")
		return 0, nil
	}
	folder, err := DescribeFolders(appId, region, FolderId, tableName)
	if err != nil {
		logger.Errorf(" InnerAuth query  ItemSpaces error: %+v", err)
		return 0, err
	}
	return folder.ItemSpaceId, InnerAuthById(ItemSpaceSerialId, isSupOwner, folder.ItemSpaceId, appId, subUin, action, false)
}

func DescribeFolders(appId int64, region string, folderId string, tableName string) (trees *table.Tree, err error) {
	txManager := service2.GetTxManager()
	cond := dao.NewCondition()
	if appId > 0 {
		cond.Eq("AppId", appId)
	}
	if len(region) > 0 && IsLegitimate(region) {
		cond.Eq("Region", region)
	}
	if folderId != "" {
		cond.Eq("FolderId", folderId)
	}

	sql := "SELECT * from " + tableName + " "
	where, args := cond.GetWhere()
	sql += where
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)

	if err != nil {
		logger.Errorf(" Failed to describe_tree when Building,error: %+v", err)
		return nil, err
	}

	tree := &table.Tree{}
	err = util.ScanMapIntoStruct(tree, data[0])
	if err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	} else if len(data) > 1 {
		return nil, errorcode.InternalErrorCode.NewWithInfo(folderId, nil)
	} else if len(data) == 0 {
		msg := fmt.Sprintf("FoldNotFound: Fold with appId: %d, regionId: %s, serialId: %s", appId, region, folderId)
		return nil, errorcode.ResourceNotFoundCode.ReplaceDesc(msg)
	}
	return tree, err
}
