package syslog

import (
	"tencentcloud.com/tstream_galileo/src/common/logger/model"
)

type SysLogConf struct {
	LoggerFile   string
	Level        model.LogLevel
	Highlighting bool
	DailyRolling bool
	HourRolling  bool
	_confMap     map[string]string
}

func (me *SysLogConf) FromFile(fileName string) {
	util := model.NewConfFileUtil()
	util.SetFile(model.LoggerFileNamme)

	me.LoggerFile = util.GetConfStringValueWithDefault(model.LoggerFileNamme, "syslog.file", "/tmp/galileo_cc.log")
	levelConf := util.GetConfStringValueWithDefault(model.LoggerFileNamme, "syslog.level", "debug")
	me.Level = model.StringToLogLevel(levelConf)
	me.Highlighting = util.GetConfBoolValue(model.LoggerFileNamme, "syslog.high_lighting", false)
	me.DailyRolling = util.GetConfBoolValue(model.LoggerFileNamme, "syslog.daily_rolling", true)
	me.HourRolling = util.GetConfBoolValue(model.LoggerFileNamme, "syslog.hour_rolling", false)
}
