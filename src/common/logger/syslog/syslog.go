// high level log wrapper, so it can output different log based on level
package syslog

import (
	"fmt"
	"io"
	"log"
	"os"
	"sync"
	"tencentcloud.com/tstream_galileo/src/common/logger/grm"
	"tencentcloud.com/tstream_galileo/src/common/logger/model"
	"time"
)

const (
	Ldate         = log.Ldate
	Llongfile     = log.Llongfile
	Lmicroseconds = log.Lmicroseconds
	Lshortfile    = log.Lshortfile
	LstdFlags     = log.LstdFlags
	Ltime         = log.Ltime
)

const FORMAT_TIME_DAY string = "20060102"
const FORMAT_TIME_HOUR string = "2006010215"

type sysLogger struct {
	_log         *log.Logger
	level        model.LogLevel
	highlighting bool

	dailyRolling bool
	hourRolling  bool

	fileName  string
	logSuffix string
	fd        *os.File

	lock sync.Mutex
}

func (l *sysLogger) Logger() *log.Logger {
	return l._log
}

func (l *sysLogger) SetHighlighting(highlighting bool) {
	l.highlighting = highlighting
}

func (l *sysLogger) SetLevel(level model.LogLevel) {
	l.level = level
}

func (l *sysLogger) GetLogLevel() model.LogLevel {
	return l.level
}

func (l *sysLogger) SetFlags(flags int) {
	l._log.SetFlags(flags)
}

func (l *sysLogger) SetLevelByString(level string) {
	l.level = model.StringToLogLevel(level)
}

func (l *sysLogger) SetRotateByDay() {
	l.dailyRolling = true
	l.logSuffix = genDayTime(time.Now())
}

func (l *sysLogger) SetRotateByHour() {
	l.hourRolling = true
	l.logSuffix = genHourTime(time.Now())
}

func (l *sysLogger) rotate() error {
	l.lock.Lock()
	defer l.lock.Unlock()

	var suffix string
	if l.dailyRolling {
		suffix = genDayTime(time.Now())
	} else if l.hourRolling {
		suffix = genHourTime(time.Now())
	} else {
		return nil
	}

	// Notice: if suffix is not equal to l.LogSuffix, then rotate
	if suffix != l.logSuffix {
		err := l.doRotate(suffix)
		if err != nil {
			return err
		}
	}

	return nil
}

func (l *sysLogger) doRotate(suffix string) error {
	lastFileName := l.fileName + "." + l.logSuffix
	if l.fileName == "" {
		return nil
	}
	err := os.Rename(l.fileName, lastFileName)
	if err != nil {
		return err
	}

	// Notice: Not check error, is this ok?
	l.fd.Close()

	err = l.SetOutputByName(l.fileName)
	if err != nil {
		return err
	}

	l.logSuffix = suffix

	return nil
}

func (l *sysLogger) SetOutput(out io.Writer) {
	l._log = log.New(out, l._log.Prefix(), l._log.Flags())
}

func (l *sysLogger) SetOutputByName(path string) error {
	f, err := os.OpenFile(path, os.O_CREATE|os.O_APPEND|os.O_RDWR, 0600)
	if err != nil {
		log.Fatal(err)
	}

	l.SetOutput(f)

	l.fileName = path
	l.fd = f

	return err
}

func (l *sysLogger) log(t model.LogType, v ...interface{}) {
	if l.level|model.LogLevel(t) != l.level {
		return
	}

	err := l.rotate()
	if err != nil {
		_, _ = fmt.Fprintf(os.Stderr, "%s\n", err.Error())
		return
	}

	v1 := make([]interface{}, len(v)+2)
	logStr, logColor := model.LogTypeToString(t)
	if l.highlighting {
		v1[0] = "\033" + logColor + "m[" + logStr + "]"
		copy(v1[1:], v)
		v1[len(v)+1] = "\033[0m"
	} else {
		v1[0] = "[" + logStr + "]"
		copy(v1[1:], v)
		v1[len(v)+1] = ""
	}

	s := fmt.Sprintln(v1...)
	l._log.Output(4, s)
}

func (l *sysLogger) logf(t model.LogType, format string, v ...interface{}) {
	if l.level|model.LogLevel(t) != l.level {
		return
	}

	err := l.rotate()
	if err != nil {
		fmt.Fprintf(os.Stderr, "%s\n", err.Error())
		return
	}

	logStr, logColor := model.LogTypeToString(t)
	var s string

	//当前协程存在请求参数，日志输出增加Action，requestId等参数，便于后续日志搜索
	currRoutCxt := grm.GetCurrentGoRountineContext()
	if l.highlighting {
		if len(currRoutCxt) == 0 {
			s = "\033" + logColor + "m[" + logStr + "] " + fmt.Sprintf(format, v...) + "\033[0m"
		} else {
			s = "\033" + logColor + "m[" + logStr + "] " + currRoutCxt["Action"].(string) + currRoutCxt["Region"].(string) + currRoutCxt["RequestId"].(string) + currRoutCxt["RemoteAddr"].(string) + fmt.Sprintf(format, v...) + "\033[0m"
		}
	} else {
		if len(currRoutCxt) == 0 {
			s = "[" + logStr + "] " + fmt.Sprintf(format, v...)
		} else {
			s = "[" + logStr + "] " + currRoutCxt["Action"].(string) + currRoutCxt["Region"].(string) + currRoutCxt["RequestId"].(string) + currRoutCxt["RemoteAddr"].(string) + fmt.Sprintf(format, v...)
		}
	}

	l._log.Output(4, s)
}

func (l *sysLogger) Fatal(v ...interface{}) {
	l.log(model.LOG_FATAL, v...)
	os.Exit(-1)
}

func (l *sysLogger) Fatalf(format string, v ...interface{}) {
	l.logf(model.LOG_FATAL, format, v...)
	os.Exit(-1)
}

func (l *sysLogger) Error(v ...interface{}) {
	l.log(model.LOG_ERROR, v...)
}

func (l *sysLogger) Errorf(format string, v ...interface{}) {
	l.logf(model.LOG_ERROR, format, v...)
}

func (l *sysLogger) Warning(v ...interface{}) {
	l.log(model.LOG_WARNING, v...)
}

func (l *sysLogger) Warningf(format string, v ...interface{}) {
	l.logf(model.LOG_WARNING, format, v...)
}

func (l *sysLogger) Debug(v ...interface{}) {
	l.log(model.LOG_DEBUG, v...)
}

func (l *sysLogger) Debugf(format string, v ...interface{}) {
	l.logf(model.LOG_DEBUG, format, v...)
}

func (l *sysLogger) Info(v ...interface{}) {
	l.log(model.LOG_INFO, v...)
}

func (l *sysLogger) Infof(format string, v ...interface{}) {
	l.logf(model.LOG_INFO, format, v...)
}

func (l *sysLogger) PrepareParam(uin uint64, serverIp string, cmd, subCmd uint32, errorId int32) {
	// nothing doing
}

func genDayTime(t time.Time) string {
	return t.Format(FORMAT_TIME_DAY)
}

func genHourTime(t time.Time) string {
	return t.Format(FORMAT_TIME_HOUR)
}

var singleton *sysLogger

var lock sync.Mutex

func NewSysLogger(conf *SysLogConf) *sysLogger {
	lock.Lock()
	defer lock.Unlock()

	if singleton == nil {
		singleton = &sysLogger{}
		singleton._log = log.New(os.Stdout, "", LstdFlags)
		singleton.SetOutputByName(conf.LoggerFile) // 先初始化Log 对象
		singleton.SetFlags(LstdFlags | Lshortfile)
		singleton.SetLevel(model.LogLevel(conf.Level))
		singleton.SetHighlighting(conf.Highlighting)
		if conf.DailyRolling {
			singleton.SetRotateByDay()
		}
		if conf.HourRolling {
			singleton.SetRotateByHour()
		}
	}

	return singleton
}
