package commands

import (
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	commandc "tencentcloud.com/tstream_galileo/src/common/command"
	"tencentcloud.com/tstream_galileo/src/common/configure"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/dlocker"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/notify"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/common/uuid"
	service2 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cluster_admin_protocol"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster_master"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config"
)

var txManager *dao.DataSourceTransactionManager

func SetTxManager(txm *dao.DataSourceTransactionManager) {
	txManager = txm
}

func SubmitCommand(batchSize int) (err error) {
	logger.Info("call submitCommand")
	locker := dlocker.NewDlocker("SubmitCommand", fmt.Sprintf("submitCommand"), 60)
	err = locker.Lock()
	if err != nil {
		return fmt.Errorf("sync lock for submitCommand failed![ %s ]", err.Error())
	}
	defer func() {
		locker.UnLock()
		if errs := recover(); errs != nil {
			logger.Error("submit error msg:", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
			return
		}
	}()
	sql := "SELECT * FROM Command WHERE status=? ORDER BY createTime DESC LIMIT ?"
	args := make([]interface{}, 0)
	args = append(args, constants.CLUSTER_MASTER_COMMAND_CREATE)
	args = append(args, batchSize)
	_, rowsdata, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to get commands, with errors:%+v", err)
		return err
	}
	if len(rowsdata) < 1 {
		logger.Infof("no commands, skip")
		return nil
	}

	// 获取clustergroupserialId， 共享资源池的namespace需要
	clusterGroupIds := make([]int64, 0, 0)
	commandClusterIdMap := make(map[int64][]*cluster_master.CommandNew, 0)
	for i := 0; i < len(rowsdata); i++ {
		rowdata := rowsdata[i]
		command := &cluster_master.CommandNew{}
		err = util.ScanMapIntoStruct(command, rowdata)
		if err != nil {
			logger.Warningf("###@@@Failed to util.ScanMapIntoStruct(command, rowdata), with errors:%+v", err)
			continue
		}

		clusterGroupIds = append(clusterGroupIds, command.ClusterGroupId)
		if commandClusterIdMap[command.ClusterId] == nil || len(commandClusterIdMap[command.ClusterId]) < 1 {
			rs := make([]*cluster_master.CommandNew, 0)
			rs = append(rs, command)
			commandClusterIdMap[command.ClusterId] = rs
		} else {
			commandClusterIdMap[command.ClusterId] = append(commandClusterIdMap[command.ClusterId], command)
		}
	}

	clusterGroups, err := ListClusterGroupsByIds(clusterGroupIds)
	if err != nil {
		logger.Errorf("Failed to get ListClusterGroupsByIds, with errors:%+v", err)
		return err
	}
	clusterGroupIdMap := make(map[int64]*table.ClusterGroup, 0)
	for _, clusterGroup := range clusterGroups {
		clusterGroupIdMap[clusterGroup.Id] = clusterGroup
	}
	region := ""
	for clusterId, commands := range commandClusterIdMap {
		if len(commands) < 1 {
			continue
		}

		clusterGroup, exists := clusterGroupIdMap[commands[0].ClusterGroupId]
		if !exists {
			logger.Warningf("***ClusterGroupId %d not exists, skip", commands[0].ClusterGroupId)
			continue
		}
		if clusterGroup.AgentSerialId == "" {
			logger.Warningf("***ClusterId %d is not Agent, skip", clusterId)
			continue
		}
		if clusterGroup.Status != constants.CLUSTER_GROUP_STATUS_RUNNING {
			logger.Warningf("***ClusterId %d is not running, skip", clusterId)
			continue
		}
		region = clusterGroup.Region
		for _, command := range commands {
			if exists {
				command.ClusterSerialId = clusterGroup.SerialId
			} else {
				logger.Errorf("### Failed to get clustergroup by id %d", command.ClusterGroupId)
			}
		}

		err = SendCommands(clusterId, commands, region)
		if err != nil {
			logger.Errorf("Failed to SendCommands, with errors:%+v", err)
			return err
		}
	}

	return nil
}

func ListClusterGroupsByIds(clusterGroupIds []int64) (clusterGroups []*table.ClusterGroup, err error) {
	clusterGroups = make([]*table.ClusterGroup, 0)
	if len(clusterGroupIds) < 1 {
		return clusterGroups, nil
	}
	var sql = "SELECT * FROM ClusterGroup"
	cond := dao.NewCondition()
	if len(clusterGroupIds) > 0 {
		cond.In("Id", clusterGroupIds)
	}

	where, args := cond.GetWhere()
	sql += where

	txManager := service2.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return clusterGroups, err
	}

	for i := 0; i < len(data); i++ {
		clusterGroup := &table.ClusterGroup{}
		err = util.ScanMapIntoStruct(clusterGroup, data[i])
		if err != nil {
			return clusterGroups, errorcode.InternalErrorCode_UnMarshalFailed.NewWithErr(err)
		}
		clusterGroups = append(clusterGroups, clusterGroup)
	}

	return clusterGroups, nil
}

type CommandCSRsp struct {
	cluster_admin_protocol.QaeResponse
	Data struct {
		cluster_admin_protocol.ClusterAdminResponse
	} `json:"data"`
}

type CommandCSPara struct {
	Params          Params `json:"params"`
	ClusterId       int64  `json:"clusterId"`
	ClusterSerialId string `json:"clusterSerialId"`
}
type Params struct {
	Commands  string `json:"commands"`
	RequestId string `json:"requestId"`
}

const CommandTimeOut = "command.timeout"

func getTimeout() int64 {
	var timeout = configure.GetConfInt64Value(constants.GALILEO_CONF_FILE, CommandTimeOut, 15)
	return timeout
}

func SendCommands(clusterId int64, commands []*cluster_master.CommandNew, region string) error {

	err := Interactive(clusterId, commands, region)
	if err != nil {
		return err
	}
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		udpateSql := "UPDATE Command SET Status=?, AckTime=? WHERE Status=? AND Id in ("
		args := make([]interface{}, 0)
		args = append(args, constants.CLUSTER_MASTER_COMMAND_ACK)
		args = append(args, util.GetCurrentTime())
		args = append(args, constants.CLUSTER_MASTER_COMMAND_CREATE)
		for i, command := range commands {
			if i != len(commands)-1 {
				udpateSql += "?, "
				args = append(args, command.Id)
				continue
			}
			udpateSql += "?)"
			args = append(args, command.Id)
			break
		}
		tx.ExecuteSql(udpateSql, args)
		return nil
	}).Close()
	return nil

}

func Interactive(clusterId int64, commands []*cluster_master.CommandNew, region string) error {
	newUUID := uuid.New()
	requestId := fmt.Sprintf("sendcommands-clusterid-%d-%s", clusterId, newUUID)
	logger.Infof("####sendcommands requestId is %s", requestId)
	// 2. 封装请求CA的参数
	strCommands, err := json.Marshal(commands)
	para := &CommandCSPara{
		Params: Params{
			Commands:  string(strCommands),
			RequestId: requestId,
		},
		ClusterId: clusterId,
	}
	csReq := cluster_admin_protocol.NewClusterAdminReq(requestId, "qcloud.cs.pushCommand", para)
	sendData, err := json.Marshal(csReq)
	// 4. 请求CS获取测试结果
	url, err := commandc.GetCAOrCSUrl(&table.Cluster{
		Id: clusterId,
	})
	if err != nil {
		msg := fmt.Sprintf("Error commandService.GetCAOrCSUrl, error: %v", err)
		logger.Error(msg)
		return errorcode.FailedOperationCode.NewWithMsg(msg)
	}
	// 3. 创建CRD
	commandService, err := commandc.NewCommandService(clusterId)
	if err != nil || commandService == nil {
		msg := fmt.Sprintf("Error SendCommands can't get the commandService from ClusterId %d, error: %v", clusterId, err)
		logger.Error(msg)
		if strings.Contains(msg, "timeout") {
			// 只发送给必要的人
			receiverList, _ := config.GetRainbowConfiguration("Notification", "tof_notification_eks_send_resource_receivers.csv")
			n := notify.NewNotify()
			n.SendByCustomParamTof4(fmt.Sprintf("!!! region: %s 连接共享集群clusterId %d 超时", region, clusterId), msg, receiverList, "")
		}
		return errorcode.FailedOperationCode.NewWithMsg(msg)
	}

	rsp, err := commandService.DoRequest(&commandc.CommandRequest{
		ReqId:    requestId,
		Url:      url,
		SendData: string(sendData),
		Uin:      "uin",
		Apikey:   "triggerClusterNetworkTest",
		Timeout:  getTimeout(),
		Callback: "",
	})
	if err != nil {
		msg := fmt.Sprintf("%s Error commandService.DoRequest, error: %v", requestId, err)
		logger.Error(msg)
		return err
	}
	logger.Debugf("#### sendcommands rsp is %s", rsp)
	// 5. 解析返回数据
	commandCSRsp := &CommandCSRsp{}
	err = json.Unmarshal([]byte(rsp), commandCSRsp)
	if err != nil {
		msg := fmt.Sprintf("%s Error parsing response: ret: %v, error: %v", requestId, rsp, err)
		logger.Errorf(msg)
		return err
	}
	// 6. 返回结果
	// 6.1 CA端异常返回
	if commandCSRsp.Data.ReturnCode != 0 {
		logger.Errorf("%s call command failed, errMsg is %s", requestId, commandCSRsp.Data.ReturnMsg)
		return errorcode.FailedOperationCode.ReplaceDesc(commandCSRsp.Data.ReturnMsg)
	}
	return nil
}
