/**
 * 取值范围校验器，
 * 配置格式： range：[1,1) 支持 [()] INF表示无穷大
 * 数据类型：
 * int, float
 * 对应的复合结构: struct, map(value域）, slice, array
 */
package checker

import (
	"errors"
	"reflect"
	"strconv"
	"strings"
)

func init() {
	checkers["range"] = func() paramChecker { return &rangeChecker{} }
}

type rangeChecker struct {
	start, end               float64
	includeStart, includeEnd bool // [ = true, ( = false
}

func (this *rangeChecker) checkRange(value float64) bool {

	// check start
	if this.start == INF {
	} else if value > this.start {
	} else if value == this.start && this.includeStart {
	} else {
		return false
	}

	// check end
	if this.end == INF {
		return true
	} else if value < this.end {
		return true
	} else if value == this.end && this.includeEnd {
		return true
	} else {
		return false
	}
}

type state interface {
	dataPicker(start int, input []rune) (e error, stop int)
	dataProcessor(*range<PERSON>hecker) error
	getNextState() state
	getName() string
}

type flagState struct {
	name    string
	data    rune
	flag    map[rune]bool
	setFunc func(checker *rangeChecker, include bool)
	next    state
}

func (this *flagState) dataPicker(start int, input []rune) (e error, stop int) {
	for ; start < len(input); start++ {
		ch := input[start]

		if ch == ' ' {
			continue
		}

		if _, exists := this.flag[ch]; exists {
			this.data = ch
			return nil, start + 1
		}

		return errors.New("invalidate char found:" + string(ch)), 0
	}
	return errors.New("No conent found"), 0
}

func (this *flagState) dataProcessor(checker *rangeChecker) error {

	include, exists := this.flag[this.data]
	if !exists {
		return errors.New("Unsupported flag")
	}
	this.setFunc(checker, include)
	return nil
}

func (this *flagState) getNextState() state {
	return this.next
}

func (this *flagState) getName() string {
	return this.name
}

type numState struct {
	name    string
	data    string
	endChar string
	setFunc func(checker *rangeChecker, num float64)
	next    state
}

func (this *numState) dataPicker(start int, input []rune) (e error, stop int) {

	num := []rune{}

	numEnd := false

	for ; start < len(input); start++ {
		ch := input[start]

		if ch == ' ' {
			if len(num) > 0 {
				numEnd = true
			}
			continue
		}

		// parse INF
		if len(input) > start+3 && string(input[start:start+3]) == "INF" {
			start += 3
			this.data = "INF"
			for ; start < len(input); start++ {
				if input[start] == ' ' {
					continue
				}
				if strings.Contains(this.endChar, string(input[start])) {
					break
				} else {
					return errors.New("Unexpected char after INFO" + string(input[start])), 0
				}
			}
			return nil, start
		}

		if strings.Contains(this.endChar, string(ch)) {
			break
		}

		if numEnd {
			return errors.New("Number string cant contains space"), 0
		}

		if ch >= '0' && ch <= '9' || ch == '.' || ch == '-' { // float number & negtive number
			num = append(num, ch)
		} else {
			return errors.New("Config not number: " + string(ch)), 0
		}
	}

	length := len(num)
	if length == 0 {
		return errors.New("no number found"), 0
	}

	if length > 19 { // int64 最大长度
		return errors.New("exceed int max value"), 0
	}

	this.data = string(num)

	return nil, start
}

func (this *numState) dataProcessor(checker *rangeChecker) error {
	if this.data == "INF" {
		this.setFunc(checker, INF)
		return nil
	}

	firstNum, e := strconv.ParseFloat(this.data, 64)
	if e != nil {
		return e
	}
	this.setFunc(checker, firstNum)
	return nil
}

func (this *numState) getNextState() state {
	return this.next
}

func (this *numState) getName() string {
	return this.name
}

var startState flagState = flagState{
	name: "start_state",
	flag: map[rune]bool{rune('['): true, rune('('): false},
	setFunc: func(checker *rangeChecker, include bool) {
		checker.includeStart = include
	},
	next: &startNumState,
}

var startNumState numState = numState{
	name:    "start_number_state",
	endChar: ",",
	setFunc: func(checker *rangeChecker, num float64) { checker.start = num },
	next:    &seperatorState,
}

var seperatorState flagState = flagState{
	name:    "seperator_state",
	flag:    map[rune]bool{rune(','): true},
	setFunc: func(checker *rangeChecker, include bool) {},
	next:    &endNumState,
}

var endNumState numState = numState{
	name:    "end_number_state",
	endChar: ")]",
	setFunc: func(checker *rangeChecker, num float64) { checker.end = num },
	next:    &endState,
}

var endState flagState = flagState{
	name: "end_state",
	flag: map[rune]bool{rune(']'): true, rune(')'): false},
	setFunc: func(checker *rangeChecker, include bool) {
		checker.includeEnd = include
	},
	next: nil,
}

//// [(start, end)]
func (this *rangeChecker) parseConf(conf string) error {
	var curState state = &startState
	index := 0
	var e error
	confRune := []rune(conf)
	for curState != nil && index < len(confRune) {
		if e, index = curState.dataPicker(index, confRune); e != nil {
			return errors.New("state: " + curState.getName() + ",error: " + e.Error())
		}

		if e = curState.dataProcessor(this); e != nil {
			return errors.New("state: " + curState.getName() + ", error: " + e.Error())
		}

		curState = curState.getNextState()
	}

	if curState != nil {
		return errors.New("uncompelete config find: " + conf)
	}

	if index < len(conf) {
		for _, ch := range conf[index:] {
			if ch != ' ' {
				return errors.New("extral string found in conf: " + conf)
			}
		}
	}

	return nil
}

//// [(start, end)]
//func (this *RangeChecker) ParseConf(conf string) error {
//	valueStart := false
//	firstNumEnd := false
//	numStr := []rune{}

//	for _, char := range []rune(conf) {
//		switch char {
//		case '[':
//			this.includeStart = true
//			valueStart = true

//		case '(':
//			this.includeStart = false
//			valueStart = true

//		case ')':
//			if !valueStart {
//				return errors.New("unexpected ), no num exist")
//			}

//			secondeNum, e := strconv.Atoi(string(numStr))
//			if e != nil {
//				return e
//			}
//			this.end = int64(secondeNum)
//			this.includeEnd = false

//		case ']':
//			if !valueStart {
//				return errors.New("unexpected ], no num exist")
//			}

//			secondeNum, e := strconv.Atoi(string(numStr))
//			if e != nil {
//				return e
//			}
//			this.end = int64(secondeNum)
//			this.includeEnd = true
//			valueStart = false

//		case ' ': // skip space
//		case ',':
//			if !valueStart {
//				return errors.New("unexpected ,")
//			}

//			if firstNumEnd {
//				return errors.New("multi , found")
//			}

//			firstNum, e := strconv.ParseInt(string(numStr), 0, 64)
//			if e != nil {
//				return e
//			}
//			numStr = nil
//			this.start = int64(firstNum)
//			firstNumEnd = true
//			valueStart = true

//		default:
//			if valueStart {
//				numStr = append(numStr, char)
//				//fmt.Printf("num str: %s\n", string(numStr))
//			}
//		}
//	}
//	return nil
//}

func (this *rangeChecker) doCheck(value reflect.Value) (error, bool) {

	if this.start == INF && this.end == INF {
		return nil, true
	}

	if !value.IsValid() { // invalidate
		return errors.New("Invalidate value"), false
	}

	switch value.Type().Kind() {
	case reflect.Ptr, reflect.Interface:
		if value.IsNil() { // invalidate
			return errors.New("Nil value"), false
		}
		return this.doCheck(value.Elem())

	case reflect.Struct:
		for i := 0; i < value.NumField(); i++ {
			if e, result := this.doCheck(value.Field(i)); e != nil || !result {
				return e, false
			}
		}
		return nil, true

	case reflect.Slice:
		if value.IsNil() || value.Len() == 0 {
			return nil, true
		}

		for i := 0; i < value.Len(); i++ {
			if e, result := this.doCheck(value.Index(i)); e != nil || !result {
				return e, false
			}
		}
		return nil, true

	case reflect.Array:
		if value.Len() == 0 { // empty array is validate
			return nil, true
		}

		for i := 0; i < value.Len(); i++ {
			if e, result := this.doCheck(value.Index(i)); e != nil || !result {
				return e, false
			}
		}
		return nil, true

	// check value range
	case reflect.Map:
		if value.IsNil() || value.Len() == 0 {
			return nil, true
		}
		for _, key := range value.MapKeys() {

			if e, result := this.doCheck(value.MapIndex(key)); e != nil || !result {
				return e, false
			}
		}
		return nil, true

	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return nil, this.checkRange(float64(value.Int()))

	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		return nil, this.checkRange(float64(value.Uint()))

	case reflect.Float32, reflect.Float64:
		return nil, this.checkRange(value.Float())

	default:
		return errors.New("Unsupported type:" + value.Type().Kind().String()), false
	}

	return errors.New("Can't reach here"), false
}
