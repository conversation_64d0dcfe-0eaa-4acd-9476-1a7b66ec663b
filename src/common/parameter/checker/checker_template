/**
 * 正则表达式校验器，
 * 配置格式： regular:* 
 * 数据类型：
 * string
 * 对应的复合结构: struct, map(value域）, slice, array
 */

package checker

import (
	"errors"
	"reflect"
	"strconv"
	"strings"
)

func init() {
	checkers["regular"] = func() paramChecker { return &regularChecker{} }
}

type regularChecker struct {
	regex string
}

func (this* regularChecker) parseConf(conf string)  error {
	
}

func (this* regularChecker) doCheck(value reflect.Value) (error, bool) {
	
}