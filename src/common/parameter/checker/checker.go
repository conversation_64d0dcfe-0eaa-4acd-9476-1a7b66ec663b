package checker

import (
	"errors"
	"reflect"
	"strings"
)

const INF = 0x7FFFFFFFFFFFFFFF

/*
* 检查object的struct tag，读取tag的校验类型， 校验对应个的value是否符合规则
 */
func Check(obj interface{}) (pass bool, e error) {

	vp := reflect.ValueOf(obj)

	if vp.Kind() == reflect.Ptr {
		vp = reflect.Indirect(vp)
	}

	if vp.Kind() != reflect.Struct {
		return false, errors.New("Only struct can be checked")
	}

	tp := vp.Type()

	for index := 0; index < tp.NumField(); index++ {
		field := tp.Field(index)
		fieldName := field.Name
		fieldValue := vp.FieldByName(fieldName)

		// tag
		tag := field.Tag.Get("check")

		// step 1: parse config
		confErr, checkConfs := parseConf(tag)
		if confErr != nil {
			return false, errors.New(" Field Name:" + fieldName + ", Error:" + confErr.Error())
		}

		for checkerName, checkerConf := range checkConfs {

			//			if checkerC<PERSON>, exists := checkers[checkerName]; exists {
			//				var e error

			//				checker := checkerCreator()

			//				// step 2.1: parse checker conf
			//				e = checker.parseConf(checkerConf)

			//				if e != nil {
			//					return false, errors.New("Failed:" + fieldName + " rule: " + checkerName + "config error")
			//				}

			//				// step 2.2: do checker
			//				e, pass = checker.doCheck(fieldValue)

			//				if e != nil {
			//					return false, errors.New("Failed:" + fieldName + " rule: " +
			//						checkerName + " checker error, errors: " + e.Error())
			//				}

			//				if !pass {
			//					return false, errors.New("Failed:" + fieldName + " Not Pass rule: " + checkerName)
			//				}

			//			} else {
			//				return false, errors.New("checker: " + checkerName + " Not Found")
			//			}
			pass, e = checkOnField(checkerName, checkerConf, fieldName, fieldValue)

			if !pass {
				// 当前规则校验失败，检查下是否为空， 如果为空， 继续校验其他规则， 空值的由nullable规则来判断
				if checkerName != "nullable" {
					passNullCheck, _ := checkOnField("nullable", "false", fieldName, fieldValue)
					if !passNullCheck {
//						return true, nil   // nolint
						continue
					}
				}
				return
			}
		}

		if field.Type.Kind() == reflect.Struct {
			if !fieldValue.CanInterface() {
				return false, errors.New(fieldName + " cant interface")
			}
			if pass, e = Check(fieldValue.Interface()); !pass {
				return pass, e
			}
		}
	}

	// 遍历tag，

	return true, nil
}

func checkOnField(checkerName, checkerConf, fieldName string, fieldValue reflect.Value) (pass bool, e error) {
	if checkerCreator, exists := checkers[checkerName]; exists {
		checker := checkerCreator()

		// step 2.1: parse checker conf
		e = checker.parseConf(checkerConf)

		if e != nil {
			return false, errors.New("Because " + fieldName + " rule: " + checkerName + "config error:" + e.Error())
		}

		// step 2.2: do checker
		e, pass = checker.doCheck(fieldValue)

		if e != nil {
			return false, errors.New("Because " + fieldName + " rule: " +
				checkerName + " checker error, errors: " + e.Error())
		}

		if !pass {
			return false, errors.New("Because " + fieldName + " Not Pass rule: " + checkerName)
		}

		return true, nil

	} else {
		return false, errors.New("checker: " + checkerName + " Not Found")
	}
}

/**
* 解析一个字段的校验配置， 可以包括多种类型的校验规则
* 配置格式： 规则名1：规则内容|规则名2：规则内容
* 返回值： error： 配置错误
* 	map[string]string：key: 规则名称， value 规则内容
 */
func parseConf(conf string) (error, map[string]string) {

	if len(conf) == 0 {
		return nil, nil // no config is allowed
	}

	rules := strings.Split(conf, "|")

	confMap := make(map[string]string, len(rules))

	for _, rule := range rules {

		ruleParts := strings.Split(rule, ":")
		if len(ruleParts) != 2 {
			return errors.New("Multi: found in one rule: " + conf) , nil
		}

		if _, exists := confMap[ruleParts[0]]; exists {
			return errors.New("Multi rule exists for one field, rule name:" + ruleParts[0]), nil
		}

		confMap[ruleParts[0]] = ruleParts[1]
	}

	return nil, confMap
}

var checkers map[string]func() paramChecker = make(map[string]func() paramChecker, 10)

type paramChecker interface {
	// parse config for one config type, eg: [1,10) for range checker
	// return nil if parse success,
	parseConf(conf string) error

	// check value by rules defined in conf
	// return error != nil if exception found, pass indicate whether pass check
	doCheck(value reflect.Value) (e error, pass bool)
}
