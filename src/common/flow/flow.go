package flow

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/dlocker"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/component"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"time"
)

const (
	UNSUBMIT      = 0
	SUBMITED      = 1
	ENDED         = 2
	CANCELED      = -1
	SUSPENDED     = -2
	FLOW_LOCKED   = 1
	FLOW_UNLOCKED = 0

	TASK_SUCCESS    = 0
	TASK_IN_PROCESS = 1
	TASK_FAIL       = -1

	TASK_STATUS_SUCCESS = 2
	TASK_STATUS_RUNNING = 1
	TASK_STATUS_FAILED  = -1
	TASK_STATUS_INIT    = 0

	//以task为单元的任务重试时间间隔，单位为毫秒,不传在taskcenter中有默认值。
	//可以在创建流程时通过params设定，也可以在执行流程时在CompleteTask通过参数params更改。
	PARAM_DEFAULT_FAILED_TASK_DISPATCHER_TIME  = "flow.task.failed.dispatch.time"
	PARAM_DEFAULT_RUNNING_TASK_DISPATCHER_TIME = "flow.task.running.dispatch.time"

	OCEANUS_CREATE_CLUSTER = "oceanus_create_cluster"
)

type FlowInfo struct {
	Id          int64
	RegionId    int
	FlowId      string
	Processname string
	DocId       string
	Status      int
	Locked      int
	Progress    float32
	Addtime     string
	Starttime   int64
	Endtime     int64
	Flowparam   string
	IsGroup     int
}

type FlowRelation struct {
	Id          int64
	ParentId    int64
	ChildId     int64
	Processname string
	ResourceId  string
	AppId       int64
	Region      int
}

type FlowStatus struct {
	Id         int64
	DocId      string
	FlowId     string
	Status     int
	ProcessKey string
	TaskCode   string
	Starttime  string
	Endtime    string
}

type TaskExecRequest struct {
	Retrycount int
	Processkey string
	Taskcode   string
	DocId      string
	FlowId     string
	TaskId     string
	Params     map[string]string
}

func (this *TaskExecRequest) String() string {
	if b, err := json.Marshal(this); err != nil {
		return ""
	} else {
		return string(b)
	}
}

type TaskExecResponse struct {
	RetCode  int
	Progress float32
	Err      error
	Params   map[string]string
}

func (this *TaskExecResponse) String() string {
	if b, err := json.Marshal(this); err != nil {
		return ""
	} else {
		return string(b)
	}
}

type TaskHandler interface {
	CompleteTask(request *TaskExecRequest) (resp *TaskExecResponse)
}

type TaskCenterCallBackInfo struct {
	CallbackAddr          string
	CompleteTaskInterface string
	AlarmTaskInterface    string
}

type BaseFlowParam interface {
	ToFlowDesc() map[string]string
}

func NewTaskExecResponse(retcode int, p float32, errmsg string) *TaskExecResponse {
	return &TaskExecResponse{
		RetCode:  retcode,
		Progress: p,
		Err:      errors.New(errmsg),
		Params:   map[string]string{},
	}
}

func NewTaskExecResponseWithParams(retcode int, p float32, errmsg string, params map[string]string) *TaskExecResponse {
	return &TaskExecResponse{
		RetCode:  retcode,
		Progress: p,
		Err:      errors.New(errmsg),
		Params:   params,
	}
}

var txManager *dao.DataSourceTransactionManager

var taskHandlerMap map[string]TaskHandler

var taskCenterCallBackInfo *TaskCenterCallBackInfo

func SetTxManager(txm *dao.DataSourceTransactionManager) {
	txManager = txm
}

func SetTaskCenterCallbackInfo(tc *TaskCenterCallBackInfo) {
	taskCenterCallBackInfo = tc
}

func init() {
	taskHandlerMap = map[string]TaskHandler{}
}

var callback func(docId, processName string) bool

func SetCanSubmitCallBack(fn func(docId, processName string) bool) {
	callback = fn
}

func CompleteTask(request *TaskExecRequest) (retcode int, errmsg string, params map[string]string) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Error(fmt.Sprintf("CompleteTask err,request:%+v,msg:%+v,stack:%s", request, errs, util.Gostack()))
			errmsg = fmt.Sprintf("%+v", errs)
			retcode = TASK_FAIL
			params = request.Params
		}
	}()
	locker := dlocker.NewDlocker("oceanus-flow-completeTask", fmt.Sprintf("optId-%s", request.TaskId), 6000)
	err := locker.Lock()
	if err != nil {
		return TASK_FAIL, fmt.Sprintf("lock task failed![ %s ]", err.Error()), request.Params
	}
	defer locker.UnLock()

	handler, ok := taskHandlerMap[makeHandlerKey(request.Processkey, request.Taskcode)]
	if !ok {
		logger.Error("task handler not found for:" + makeHandlerKey(request.Processkey, request.Taskcode))
		return TASK_SUCCESS, "task handler not found", request.Params
	}
	logger.Infof("handler: %s", makeHandlerKey(request.Processkey, request.Taskcode))
	resp := handler.CompleteTask(request)
	logger.Infof("handler: %s finish", makeHandlerKey(request.Processkey, request.Taskcode))
	if resp == nil {
		logger.Error("task handler return nil for:"+makeHandlerKey(request.Processkey, request.Taskcode)+" for request:%+v", request)
		return TASK_FAIL, "task handler return nil", request.Params
	}

	if request.Retrycount == 0 {
		txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
			fstatus := &FlowStatus{
				DocId:      request.DocId,
				FlowId:     request.FlowId,
				Status:     TASK_STATUS_RUNNING,
				ProcessKey: request.Processkey,
				TaskCode:   request.Taskcode,
				Starttime:  util.GetCurrentTime(),
				Endtime:    "0000-00-00 00:00:00",
			}
			_, err := tx.SaveObjectDoNotPanic(fstatus, "taskflow_status")
			if err != nil && !strings.Contains(strings.ToLower(err.Error()), "duplicate entry") {
				return err
			}
			return nil
		}).Close()
	}
	if resp.RetCode == TASK_IN_PROCESS {
		return TASK_IN_PROCESS, "ok", resp.Params
	} else if resp.RetCode == TASK_FAIL {
		if resp.Err == nil {
			return TASK_FAIL, "system error", resp.Params
		} else {
			errmsg = resp.Err.Error()
			return TASK_FAIL, errmsg, resp.Params
		}
	} else if resp.RetCode == TASK_SUCCESS {
		txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
			sql := "update taskflow_status set status=?, endtime=? where flowId=? and processKey=? and taskCode=?"
			result := tx.ExecuteSqlWithArgs(sql, TASK_STATUS_SUCCESS, util.GetCurrentTime(), request.FlowId, request.Processkey, request.Taskcode)
			if result != nil {
				rows, err := result.RowsAffected()
				logger.Infof("RowsAffected : %d", rows)
				if err != nil {
					logger.Errorf("RowsAffected Err : %s", err.Error())
				}
			}
			if strings.ToLower(request.Taskcode) == "endpoint" {
				sql := "update taskflow set progress=?,status=?,endtime=? where flowId=?"
				tx.ExecuteSqlWithArgs(sql, resp.Progress, ENDED, time.Now().Unix(), request.FlowId)
				/**
				 * 组合taskflow判断所有子flow是否完成，如果完成，更新父flow的状态
				 */
				err := updateParentFlowByChildId(request.FlowId, tx)
				if err != nil {
					logger.Errorf("updateParentFlowByChildId Err : %s", err.Error())
					return err
				}
			} else {
				sql := "update taskflow set progress=?,endtime=? where flowId=?"
				tx.ExecuteSqlWithArgs(sql, resp.Progress, time.Now().Unix(), request.FlowId)
			}
			return nil
		}).Close()
		return TASK_SUCCESS, "OK", resp.Params
	} else {
		return TASK_FAIL, "unknown status", resp.Params
	}

}

func makeHandlerKey(processKey, taskCode string) string {
	return fmt.Sprintf("%s-%s", processKey, taskCode)
}
func RegistTaskHandler(processKey, taskCode string, handler TaskHandler) {
	hkey := makeHandlerKey(processKey, taskCode)
	_, ok := taskHandlerMap[hkey]
	if !ok {
		taskHandlerMap[hkey] = handler
	}
}

func GetTaskHandler(processKey, taskCode string) (h TaskHandler) {
	hk := makeHandlerKey(processKey, taskCode)
	if handler, ok := taskHandlerMap[hk]; ok {
		h = handler
	}
	return h
}

func switchFlowLock(id int64, lock, status int) (ok bool) {
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		sql := "update taskflow set locked=? where id=? and status=?"
		args := make([]interface{}, 0, 0)
		args = append(args, lock)
		args = append(args, id)
		args = append(args, status)
		result := tx.ExecuteSql(sql, args)
		rows, err := result.RowsAffected()
		if err != nil {
			logger.Error(fmt.Sprintf("switchFlowLock err, flowId:%d,lock:%d,status:%d", id, lock, status))
			ok = false
		}
		ok = (rows == 1)
		return nil
	}).Close()
	return ok
}

func switchFlowLockInStatus(id int64, lock int, status []int) (ok bool) {
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		sql := "update taskflow set locked=?"

		cond := &dao.Condition{}
		cond.Eq("id", id).In("status", status)

		args := make([]interface{}, 0, 0)
		args = append(args, lock)
		where, wargs := cond.GetWhere()

		sql += where
		args = append(args, wargs...)

		result := tx.ExecuteSql(sql, args)
		rows, err := result.RowsAffected()
		if err != nil {
			logger.Error(fmt.Sprintf("switchFlowLockInStatus err, flowId:%d,lock:%d,status:%d", id, lock, status))
			ok = false
		}
		ok = (rows == 1)
		return nil
	}).Close()
	return ok
}

func getFlowParas(flowId int64) (params map[string]string, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Error("getFlowParas getFlowContextMap error ,flowId:", flowId, ",msg:", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
			params = nil
		}
	}()
	sql := "SELECT paramkey,paramvalue FROM taskflowparams where flowid=?"
	args := make([]interface{}, 0, 0)
	args = append(args, flowId)
	count, bytedatamap, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return nil, err
	}
	if count <= 0 {
		return map[string]string{}, nil
	}
	params = map[string]string{}
	for _, row := range bytedatamap {
		key := bytes.NewBuffer(row["paramkey"]).String()
		value := bytes.NewBuffer(row["paramvalue"]).String()
		params[key] = value
	}
	return params, err
}

func submitFlow(batchSize int) (err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Error("submit error msg:", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
			return
		}
	}()
	if batchSize == 0 {
		batchSize = 100
	}
	/**
	EKS迭代 加入isGroup字段，因为后付费接入，欠费需要隔离所有资源，只能返回一个flowid，所以要组合一个flowid，正常情况下
	isGroup 默认是0 ，如果是 欠费 同时隔离， 恢复 用户的多个资源，需要一个一个生成flowid（isGroup=0），然后组合一个总的flowid返回（isGroup=1）
	*/
	sql := "select * from taskflow where status=? and isGroup=0 limit ?"
	cnt, rowsdata, err := txManager.GetQueryTemplate().DoQueryWithArgs(sql, UNSUBMIT, batchSize)
	if err != nil {
		return err
	}
	if cnt == 0 {
		return nil
	}

	for i := 0; i < cnt; i++ {
		rowdata := rowsdata[i]
		info := &FlowInfo{}
		util.ScanMapIntoStruct(info, rowdata)
		if info.Id == 0 {
			logger.Warning("submit Flowtaskflow FlowInfo id = 0")
			continue
		}

		fInfo, _ := json.Marshal(info)
		logger.Info("begin to deal with FlowInfo: ", string(fInfo))
		txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
			if callback != nil {
				if !callback(info.DocId, info.Processname) {
					logger.Warning(fmt.Sprintf("submitFlow !callback(info.DocId) info: %+v", info))
					return nil
				}
			}
			if switchFlowLock(info.Id, FLOW_LOCKED, UNSUBMIT) {
				params, err := getFlowParas(info.Id)
				if err != nil {
					logger.Error("getFlowParas err:", err)
					switchFlowLock(info.Id, FLOW_UNLOCKED, UNSUBMIT)
					return err
				}
				taskcenter := component.NewComponentTaskCenter(info.RegionId)
				flowId, err := taskcenter.CreateProcessInstance(info.Processname, info.DocId, params)
				if err != nil {
					logger.Error(fmt.Sprintf("taskCenter.CreateProcessInstance err:%s", err.Error()))
					switchFlowLock(info.Id, FLOW_UNLOCKED, UNSUBMIT)
					return err
				}
				sql := "update taskflow set flowId=?,status=? where id=?"
				tx.ExecuteSqlWithArgs(sql, flowId, SUBMITED, info.Id)
				switchFlowLock(info.Id, FLOW_UNLOCKED, SUBMITED)
			}
			return nil
		}).Close()
	}

	return nil
}

func GetFlowsByDocId(docId int64) (flows []*FlowInfo, err error) {
	return GetFlowByStrDocId(fmt.Sprintf("%d", docId))
}

func GetFlowByProcessName(name string) (flows []*FlowInfo, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("GetFlowByProcessName error"))
	flows = make([]*FlowInfo, 0, 0)

	sql := "SELECT * FROM taskflow where processname=? and isGroup=0 and status in (?,?) order by id"
	count, bytedatamap, err := txManager.GetQueryTemplate().DoQueryWithArgs(sql, name, UNSUBMIT, SUBMITED)
	if err != nil {
		logger.Errorf("Failed to GetFlowByProcessName:%+v", err)
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	for i := 0; i < count; i++ {
		info := &FlowInfo{}
		util.ScanMapIntoStruct(info, bytedatamap[i])
		flows = append(flows, info)
	}

	return flows, nil
}

func GetFlowRelationByAppId(appId int64, processname string, region int) (flowRelations []*FlowRelation, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("GetFlowRelationByAppId error"))
	flowRelations = make([]*FlowRelation, 0, 0)

	sql := "SELECT * FROM taskflow_relation where Region=? and processname=? and AppId=? and isFinished = 0 order by id"
	count, bytedatamap, err := txManager.GetQueryTemplate().DoQueryWithArgs(sql, region, processname, appId)
	if err != nil {
		logger.Errorf("Failed to GetFlowRelationByAppId:%+v", err)
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	for i := 0; i < count; i++ {
		info := &FlowRelation{}
		util.ScanMapIntoStruct(info, bytedatamap[i])
		flowRelations = append(flowRelations, info)
	}

	return flowRelations, nil
}

/**
 * 一个child 唯一对应一条记录
 */
func GetFlowRelationByChildId(childFlowId int64) (flowRelation *FlowRelation, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("GetFlowRelationByChildId error"))
	sql := "SELECT * FROM taskflow_relation where childId=? and isFinished = 0 order by id"
	count, bytedatamap, err := txManager.GetQueryTemplate().DoQueryWithArgs(sql, childFlowId)
	if err != nil {
		logger.Errorf("Failed to updateParentFlowByChildId:%+v", err)
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	if count <= 0 {
		return nil, nil
	}
	flowRelation = &FlowRelation{}
	util.ScanMapIntoStruct(flowRelation, bytedatamap[0])
	return flowRelation, nil
}

func updateParentFlowByChildId(taskflowFlowId string, tx *dao.Transaction) (err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("updateParentFlowByChildId error"))

	id, _ := strconv.ParseInt(taskflowFlowId, 10, 64)
	flowObj, err := GetFlowById(id)
	if err != nil {
		logger.Errorf("Failed to updateParentFlowByChildId:%+v", err)
		return errorcode.NewStackError(errorcode.InternalErrorCode_QueryFlowFailed, "", err)
	}
	// 没有关联，直接返回
	if flowObj == nil {
		return nil
	}
	childFlowId := flowObj.Id

	flowRelation, err := GetFlowRelationByChildId(childFlowId)
	if err != nil {
		logger.Errorf("Failed to updateParentFlowByChildId:%+v", err)
		return errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	// 没有关联，直接返回
	if flowRelation == nil {
		return nil
	}
	parentFlowId := flowRelation.ParentId
	flowRelations, err := GetFlowRelationByParentId(parentFlowId)
	if err != nil {
		logger.Errorf("Failed to updateParentFlowByChildId:%+v", err)
		return errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	isFinished := true
	for _, relation := range flowRelations {
		flowInfo, err := GetFlowByUniqId(relation.ChildId)
		/**
		 *  查询和更新不在同一个事务里，所以这里加个额外的判断。如果是传进来的，直接通过
		 */
		if flowInfo.FlowId == taskflowFlowId {
			continue
		}
		if err != nil {
			err = errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
			return err
		}
		if flowInfo.Status != ENDED {
			isFinished = false
			break
		}
	}
	if isFinished {
		// 更新 taskflow
		sql := "update taskflow set progress=?,status=?,endtime=? where Id=?"
		tx.ExecuteSqlWithArgs(sql, 1, ENDED, time.Now().Unix(), parentFlowId)

		// 后付费欠费 销毁所有隔离资源以后，需要设置 flowRelation 的 状态 为完成
		if flowRelation.Processname == constants.FLOW_OCEANUS_DELETE_CLUSTER {
			// 更新 taskflow_relation isfinished
			sql = "update taskflow_relation set isFinished=1 where processname=? and parentId = ?"
			args := make([]interface{}, 0)
			args = append(args, constants.FLOW_OCEANUS_DELETE_CLUSTER)
			args = append(args, parentFlowId)
			tx.ExecuteSql(sql, args)
		}
	}
	return nil
}

func GetFlowRelationByParentId(parentId int64) (flowRelations []*FlowRelation, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("GetFlowRelationByParentId error"))
	flowRelations = make([]*FlowRelation, 0, 0)

	sql := "SELECT * FROM taskflow_relation where parentId=? and isFinished = 0 order by id"
	count, bytedatamap, err := txManager.GetQueryTemplate().DoQueryWithArgs(sql, parentId)
	if err != nil {
		logger.Errorf("Failed to GetFlowRelationByParentId:%+v", err)
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	for i := 0; i < count; i++ {
		info := &FlowRelation{}
		util.ScanMapIntoStruct(info, bytedatamap[i])
		flowRelations = append(flowRelations, info)
	}

	return flowRelations, nil
}

func GetFlowByStrDocId(docId string) (flows []*FlowInfo, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("GetFlowByStrDocId error"))
	flows = make([]*FlowInfo, 0, 0)

	sql := "SELECT * FROM taskflow where docId=? and isGroup=0 and status in (?,?) order by id"
	count, bytedatamap, err := txManager.GetQueryTemplate().DoQueryWithArgs(sql, docId, UNSUBMIT, SUBMITED)
	if err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	for i := 0; i < count; i++ {
		info := &FlowInfo{}
		util.ScanMapIntoStruct(info, bytedatamap[i])
		flows = append(flows, info)
	}

	return flows, nil
}

func GetFlowById(flowId int64) (flow *FlowInfo, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Error("GetFlowById error ,flowId:", flowId, ",msg:", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
			flow = nil
		}
	}()
	sql := "SELECT * FROM taskflow where flowId=?"
	count, bytedatamap, err := txManager.GetQueryTemplate().DoQueryWithArgs(sql, fmt.Sprintf("%d", flowId)) //flowId是varchar类型，已加索引
	if err != nil {
		return nil, err
	}
	if count <= 0 {
		return nil, nil
	}
	flow = &FlowInfo{}
	util.ScanMapIntoStruct(flow, bytedatamap[0])
	return flow, err
}

func GetFlowByUniqId(id int64) (flow *FlowInfo, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Error("GetFlowById error ,flowId:", id, ",msg:", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
			flow = nil
		}
	}()
	sql := "SELECT * FROM taskflow where id = ?"
	count, bytedatamap, err := txManager.GetQueryTemplate().DoQueryWithArgs(sql, id)
	if err != nil {
		return nil, err
	}
	if count <= 0 {
		return nil, nil
	}
	flow = &FlowInfo{}
	util.ScanMapIntoStruct(flow, bytedatamap[0])
	return flow, err
}

func CreateFlowRelation(flowName, docId string, regionId int, flowRelations []*FlowRelation) (flowId int64, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			errMsg := fmt.Sprintf("CreateFlowRelation error, flowName %s, docId %s, regionId %d, %+v",
				flowName, docId, regionId, errs)
			err = errors.New(errMsg)
		}
	}()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		parentFlow := &FlowInfo{
			Processname: flowName,
			DocId:       docId,
			Status:      UNSUBMIT,
			RegionId:    regionId,
			Locked:      0,
			Addtime:     util.GetCurrentTime(),
			Starttime:   time.Now().Unix(),
			Endtime:     int64(0),
			IsGroup:     1,
		}
		flowid := tx.SaveObject(parentFlow, "taskflow")
		flowId = flowid
		for _, flowRelation := range flowRelations {
			flowRelation.ParentId = flowid
			tx.SaveObject(flowRelation, "taskflow_relation")
		}
		return nil
	}).Close()
	return flowId, nil
}

func CreateFlow(flowName, docId string, regionId int, params map[string]string, flowParam BaseFlowParam) (flowId int64, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			bytesParams, _ := json.Marshal(params)
			bytesFlowParam, _ := json.Marshal(flowParam)
			errMsg := fmt.Sprintf("CreateFlow error, flowName %s, docId %s, regionId %d, params %s, flowParsm %s, %+v",
				flowName, docId, regionId, bytesParams, bytesFlowParam, errs)
			err = errors.New(errMsg)
		}
	}()
	if taskCenterCallBackInfo == nil || len(taskCenterCallBackInfo.CallbackAddr) == 0 || len(taskCenterCallBackInfo.CompleteTaskInterface) == 0 || len(taskCenterCallBackInfo.AlarmTaskInterface) == 0 {
		errMsg := fmt.Sprintf("taskCenterCallBackInfo does not initialized")
		logger.Error(errMsg)
		return 0, errors.New(errMsg)
	}
	flowrequest, _ := json.Marshal(flowParam)
	info := &FlowInfo{
		Processname: flowName,
		DocId:       docId,
		Status:      UNSUBMIT,
		RegionId:    regionId,
		Locked:      0,
		Addtime:     util.GetCurrentTime(),
		Starttime:   time.Now().Unix(),
		Endtime:     int64(0),
		Flowparam:   string(flowrequest),
	}
	if params == nil {
		params = map[string]string{}
	}
	params["serviceurl"] = taskCenterCallBackInfo.CallbackAddr
	params["service.interface.completetask"] = taskCenterCallBackInfo.CompleteTaskInterface
	params["service.interface.alarm"] = taskCenterCallBackInfo.AlarmTaskInterface
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		flowid := tx.SaveObject(info, "taskflow")
		sql := "insert into taskflowparams(flowid,paramkey,paramvalue) values(?,?,?)"
		if params != nil {
			for key, value := range params {
				args := make([]interface{}, 0, 0)
				args = append(args, flowid)
				args = append(args, key)
				args = append(args, value)
				tx.ExecuteSql(sql, args)
			}
		}
		flowId = flowid
		return nil
	}).Close()
	return flowId, nil
}

func SuspendFlow(regionId int, flowId int64) error {
	sql := "select * from taskflow where regionId=? and flowId=? and status=?"
	cnt, rowsdata, err := txManager.GetQueryTemplate().DoQueryWithArgs(sql, regionId, fmt.Sprintf("%d", flowId), SUBMITED)
	if err != nil {
		logger.Errorf("SuspendFlow err %v", err)
		return err
	}
	if cnt == 0 {
		err := errors.New(fmt.Sprintf("flow %d not found, should be in state submitted", flowId))
		logger.Errorf("SuspendFlow err %v", err)
		return err
	}
	info := &FlowInfo{}
	if err := util.ScanMapIntoStruct(info, rowsdata[0]); err != nil {
		logger.Errorf("SuspendFlow err %v", err)
		return err
	}

	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		if switchFlowLock(info.Id, FLOW_LOCKED, SUBMITED) {
			taskcenter := component.NewComponentTaskCenter(regionId)
			err := taskcenter.SuspendProcessInstance(regionId, flowId)
			if err != nil {
				logger.Error(fmt.Sprintf("taskCenter.SuspendProcessInstance err:%s", err.Error()))
				switchFlowLock(info.Id, FLOW_UNLOCKED, SUBMITED)
				return err
			}
			sql := "update taskflow set flowId=?,status=? where id=?"
			tx.ExecuteSqlWithArgs(sql, flowId, SUSPENDED, info.Id)
			switchFlowLock(info.Id, FLOW_UNLOCKED, SUSPENDED)
		}
		return nil
	}).Close()

	return nil
}

func CancelFlow(regionId int, flowId int64) error {
	sql := "select * from taskflow where regionId=? and flowId=? and status in (?, ?)"
	cnt, rowsdata, err := txManager.GetQueryTemplate().DoQueryWithArgs(sql, regionId, fmt.Sprintf("%d", flowId), SUBMITED, SUSPENDED)
	if err != nil {
		logger.Errorf("CancelFlow err %v", err)
		return err
	}
	if cnt == 0 {
		err := errors.New(fmt.Sprintf("flow %d not found, should be in state submitted/suspended", flowId))
		logger.Errorf("CancelFlow err %v", err)
		return err
	}
	info := &FlowInfo{}
	if err := util.ScanMapIntoStruct(info, rowsdata[0]); err != nil {
		logger.Errorf("CancelFlow err %v", err)
		return err
	}

	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		if switchFlowLockInStatus(info.Id, FLOW_LOCKED, []int{SUBMITED, SUSPENDED}) {
			taskcenter := component.NewComponentTaskCenter(regionId)
			err := taskcenter.CancelProcessInstance(regionId, flowId)
			if err != nil {
				logger.Error(fmt.Sprintf("taskCenter.CancelProcessInstance err:%s", err.Error()))
				switchFlowLockInStatus(info.Id, FLOW_UNLOCKED, []int{SUBMITED, SUSPENDED})
				return err
			}
			sql := "update taskflow set flowId=?,status=? where id=?"
			tx.ExecuteSqlWithArgs(sql, flowId, CANCELED, info.Id)
			switchFlowLock(info.Id, FLOW_UNLOCKED, CANCELED)
		}
		return nil
	}).Close()

	return nil
}

func GetFlowStatusByFlowId(flowId int64) (flowStatus []*FlowStatus, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Error("GetFlowStatusByFlowId error ,flowId:", flowId, ",msg:", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
			flowStatus = nil
		}
	}()
	flowStatus = make([]*FlowStatus, 0)
	if flowId <= 0 {
		return flowStatus, nil
	}
	sql := "SELECT * FROM taskflow_status where flowId=? order by starttime"
	args := make([]interface{}, 0, 0)
	args = append(args, flowId)
	count, rows, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return nil, err
	}
	if count <= 0 {
		return flowStatus, nil
	}
	for i := 0; i < count; i++ {
		fs := &FlowStatus{}
		err = util.ScanMapIntoStruct(fs, rows[i])
		if err != nil {
			panic(err)
		}
		flowStatus = append(flowStatus, fs)
	}
	return flowStatus, err
}

type StageInfoDetail struct {
	StageInfo
	Status    int     `json:"Status"` //0:未开始 1:进行中 2：已完成 -1：失败
	Desc      string  `json:"Desc"`
	Progress  float64 `json:"Progress"`
	Starttime string  `json:"Starttime"`
	Endtime   string  `json:"Endtime"`
	WoodJobId int64   `json:"WoodJobId"`
}

type StageInfo struct {
	Stage         string `json:"Stage"`
	Name          string `json:"Name"`
	LanguageKey   string `json:"LanguageKey"` //有了Name基础上还需要LanguageKey的原因是Name在很多流程里同一个名字(比如waitWood)，但展示给用户却是不同的含义
	IsShow        bool   `json:"IsShow"`
	IsSubFlow     bool   `json:"IsSubFlow"`
	SubFlowFlag   string `json:"SubFlowFlag"`
	HadWoodDetail bool   `json:"HadWoodDetail"`
}

type FlowDefine struct {
	Name   string       `json:"Name"`
	Stages []*StageInfo `json:"Stages"`
}

type FlowConf struct {
	Flows map[string]*FlowDefine `json:"Flows"`
}

func GetFlowIdByClusterId(clusterId string) (flowId int64, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Error("GetFlowIdByClusterId error ,ClusterId:", clusterId, ",msg:", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
			flowId = 0
		}
	}()
	sql := "SELECT * FROM taskflow WHERE processname = ? and isGroup=0 AND docId LIKE ? ORDER BY starttime desc LIMIT 1"
	count, bytedatamap, err := txManager.GetQueryTemplate().DoQueryWithArgs(sql, OCEANUS_CREATE_CLUSTER, "%"+clusterId+"%")
	if err != nil {
		return 0, err
	}
	if count <= 0 {
		logger.Info("Cann't find the flowId")
		return 0, nil
	}
	flow := &FlowInfo{}
	util.ScanMapIntoStruct(flow, bytedatamap[0])
	logger.Info("flow", flow)
	flowId, err = strconv.ParseInt(flow.FlowId, 10, 64)
	logger.Info("flowId", flowId)
	return flowId, err
}
