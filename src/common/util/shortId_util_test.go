package util

import (
	"fmt"
	"testing"
)

func TestCidUtil_EncodeId(t *testing.T) {
	type args struct {
		obj        int64
		abbreviate string
		password   string
		zone       int64
		id_len     int64
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
		{
			name: "test1",
			args: args{
				obj:        1234567890,
				abbreviate: "resource",
				password:   "resource",
				zone:       1,
				id_len:     8,
			},
			want: "abcdefghijklmnopqrstuvwxyz",
		},
		{
			name: "test2",
			args: args{
				obj:        1234567890,
				abbreviate: "resource",
				password:   "resource",
				zone:       2,
				id_len:     8,
			},
			want: "zyxwvutsrqponmlkjihgfedcba",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			this := &CidUtil{}
			if got := this.EncodeId(tt.args.obj, tt.args.abbreviate, tt.args.password, tt.args.zone, tt.args.id_len); got != tt.want {
				fmt.Println(got)
			}
		})
	}
}
