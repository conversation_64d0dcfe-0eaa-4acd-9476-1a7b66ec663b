package util

import (
	"bytes"
	"database/sql"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"reflect"
	"runtime"
	"strconv"
	"strings"
	"time"
	"unsafe"

	"crypto/aes"
	"encoding/base64"
	"github.com/juju/errors"
	"math/rand"
	"tencentcloud.com/tstream_galileo/src/common/cipher_galileo"
)

func ParseInCond(args *[]interface{}, params interface{}) (tpl string) {

	switch obj := params.(type) {

	case []int, []int8, []int16, []int32, []string, []int64, []uint, []uint8, []uint16, []uint32, []uint64, []float32, []float64, []interface{}:
		ary := reflect.ValueOf(obj)
		size := ary.Len()
		if size > 0 {
			tpl = "("
			for i := 0; i < size; i++ {
				*args = append(*args, ary.Index(i).Interface())
				if i == 0 {
					tpl = tpl + "?"
				} else {
					tpl = tpl + ", ?"
				}
				if i == (size - 1) {
					tpl = tpl + ")"
				}
			}
			return tpl
		}
	default:
		panic("ParseInCond params unknow type")
	}
	return "()"
}

func InStrArray(strToFind string, array []string) bool {
	for _, element := range array {
		if strToFind == element {
			return true
		}
	}
	return false
}

func GetCurrentTime() string {
	return time.Now().Format("2006-01-02 15:04:05")
}

func GetOneYearAgoTime() string {
	return time.Now().AddDate(-1, 0, 0).Format("2006-01-02 15:04:05")
}

func GetAfterOneDayTime() string {
	return time.Now().AddDate(0, 0, 1).Format("2006-01-02 15:04:05")
}

func GetCurrentDate() string {
	return time.Now().Format("2006-01-02")
}

func GetMapValue(m map[interface{}]interface{}) interface{} {
	for key := range m {
		return m[key]
	}
	return nil
}

func ConvertToPtrSlice(strings []string) []*string {
	ptrSlice := make([]*string, len(strings))
	for i, str := range strings {
		ptrSlice[i] = &str
	}
	return ptrSlice
}

func TimestampToUTC(ts int64) string {
	return time.Unix(ts, 0).UTC().Format("2006-01-02 15:04:05")
}

func TimestampToLocalTime(ts int64) string {
	return time.Unix(ts, 0).Format("2006-01-02 15:04:05")
}

func NanoTimestampToLocalTime(ts int64) string {
	return time.Unix(ts/1000, 0).Format("2006-01-02 15:04:05")
}

func LocalTimeToTimestamp(ts string) int64 {
	timeTemplate1 := "2006-01-02 15:04:05"                          //常规类型
	stamp, _ := time.ParseInLocation(timeTemplate1, ts, time.Local) //使用parseInLocation将字符串格式化返回本地时区时间
	return stamp.Unix()
}

func LocalTimeToUnixMilli(ts string) int64 {
	timeTemplate1 := "2006-01-02 15:04:05"                            //常规类型
	stamp, err := time.ParseInLocation(timeTemplate1, ts, time.Local) //使用parseInLocation将字符串格式化返回本地时区时间
	if err != nil {
		return GetNowTimestamp()
	}
	return stamp.UnixMilli()
}

func RFCTimeToTimestamp(ts string) int64 { //常规类型
	stamp, _ := time.Parse(time.RFC3339, ts) //使用parseInLocation将字符串格式化返回本地时区时间
	return stamp.Unix() * 1000
}

func SubDate(begin, end string) int64 {
	t1, err := time.Parse("2006-01-02 15:04:05", begin)
	if err != nil {
		return 0
	}
	t2, err := time.Parse("2006-01-02 15:04:05", end)
	if err != nil {
		return 0
	}
	value := t2.Unix() - t1.Unix()
	return value
}

func SubDateNano(begin, end string) int64 {
	t1, err := time.Parse("2006-01-02 15:04:05", begin)
	if err != nil {
		return 0
	}
	t2, err := time.Parse("2006-01-02 15:04:05", end)
	if err != nil {
		return 0
	}
	value := t2.UnixNano() - t1.UnixNano()
	return value
}

/**
* 获取毫秒级的timestamp
 */
func GetNowTimestamp() int64 {
	return time.Now().UnixNano() / int64(time.Millisecond)
}

func lowerFirstString(name string) string {
	return strings.ToLower(name[0:1]) + name[1:]
}

func upperFirstString(name string) string {
	return strings.ToUpper(name[0:1]) + name[1:]
}

/*
*
获取GoroutineId
*/
func GoroutineId() string {
	buf := make([]byte, 30) // goid 的类型为int64， buf 的格式为： goroutine 9223372036854775807 [running]:
	buf = buf[:runtime.Stack(buf, false)]
	return string(bytes.Split(buf, []byte(" "))[1])
}

func Gostack() string {
	buf := make([]byte, 10240)
	runtime.Stack(buf, false)
	return string(buf)
}

/*
*
 */
func setValuetoStruct(t reflect.Type, baseaddr uintptr, objMap map[string][]byte) (err error) {

	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		bytesdata, ok := objMap[lowerFirstString(field.Name)]
		if !ok {
			bytesdata, ok = objMap[upperFirstString(field.Name)]
		}
		if field.Type.Kind() == reflect.Struct {
			//如果属性是个结构体
			addr := uintptr(unsafe.Pointer(baseaddr)) + field.Offset
			err := setValuetoStruct(field.Type, addr, objMap)
			if err != nil {
				return err
			}
			continue
		}
		if !ok {
			continue
		}
		switch field.Type.Kind() {
		case reflect.String: //字符串
			var ptr *string
			ptr = (*string)(unsafe.Pointer(uintptr(unsafe.Pointer(baseaddr)) + field.Offset))
			*ptr = string(bytesdata)
		case reflect.Int64: //
			var ptr *int64
			ptr = (*int64)(unsafe.Pointer(uintptr(unsafe.Pointer(baseaddr)) + field.Offset))
			v, err := strconv.ParseInt(string(bytesdata), 10, 64)
			if err != nil {
				return err
			}
			*ptr = v
		case reflect.Bool:
			var ptr *bool
			ptr = (*bool)(unsafe.Pointer(uintptr(unsafe.Pointer(baseaddr)) + field.Offset))
			*ptr = string(bytesdata) == "1"
		case reflect.Int:
			var ptr *int
			ptr = (*int)(unsafe.Pointer(uintptr(unsafe.Pointer(baseaddr)) + field.Offset))
			v, err := strconv.ParseInt(string(bytesdata), 10, 0)
			if err != nil {
				return err
			}
			*ptr = (int)(v)
		case reflect.Int8:
			var ptr *int8
			ptr = (*int8)(unsafe.Pointer(uintptr(unsafe.Pointer(baseaddr)) + field.Offset))
			v, err := strconv.ParseInt(string(bytesdata), 10, 8)
			if err != nil {
				return err
			}
			*ptr = (int8)(v)
		case reflect.Int16:
			var ptr *int16
			ptr = (*int16)(unsafe.Pointer(uintptr(unsafe.Pointer(baseaddr)) + field.Offset))
			v, err := strconv.ParseInt(string(bytesdata), 10, 16)
			if err != nil {
				return err
			}
			*ptr = (int16)(v)
		case reflect.Int32:
			var ptr *int32
			ptr = (*int32)(unsafe.Pointer(uintptr(unsafe.Pointer(baseaddr)) + field.Offset))
			v, err := strconv.ParseInt(string(bytesdata), 10, 32)
			if err != nil {
				return err
			}
			*ptr = (int32)(v)
		case reflect.Float32:
			var ptr *float32
			ptr = (*float32)(unsafe.Pointer(uintptr(unsafe.Pointer(baseaddr)) + field.Offset))
			if len(string(bytesdata)) == 0 {
				*ptr = float32(0)
			} else {
				v, err := strconv.ParseFloat(string(bytesdata), 32)
				if err != nil {
					return err
				}
				*ptr = float32(v)
			}
		case reflect.Float64:
			var ptr *float64
			ptr = (*float64)(unsafe.Pointer(uintptr(unsafe.Pointer(baseaddr)) + field.Offset))
			if len(string(bytesdata)) == 0 {
				*ptr = float64(0)
			} else {
				v, err := strconv.ParseFloat(string(bytesdata), 64)
				if err != nil {
					return err
				}
				*ptr = v
			}
		default:
			return errors.BadRequestf("unsupported type in Scan: " + t.String())

		}
	}
	return nil
}

/*
*
map里的值放进结构体
*/
func ScanMapIntoStruct(obj interface{}, objMap map[string][]byte) error {
	//判断传入的值是否为指针
	var typestruct reflect.Type
	var addr uintptr
	if reflect.ValueOf(obj).Kind() != reflect.Ptr {
		return errors.New("ScanMapIntoStruct need a ptr")
	}
	addr = reflect.ValueOf(obj).Pointer()
	dataStruct := reflect.Indirect(reflect.ValueOf(obj))
	if dataStruct.Kind() != reflect.Struct {
		return errors.New("expected a pointer to a struct")
	}
	typestruct = dataStruct.Type()
	return setValuetoStruct(typestruct, addr, objMap)
}

func ScanStructIntoMap(obj interface{}) (map[string]interface{}, error) {
	dataStruct := reflect.Indirect(reflect.ValueOf(obj))
	if dataStruct.Kind() != reflect.Struct {
		return nil, errors.New("expected a pointer to a struct")
	}
	dataStructType := dataStruct.Type()
	mapped := make(map[string]interface{})

	for i := 0; i < dataStructType.NumField(); i++ {
		field := dataStructType.Field(i)
		fieldName := field.Name
		mapKey := lowerFirstString(fieldName)
		value := dataStruct.FieldByName(fieldName).Interface()
		mapped[mapKey] = value
	}

	return mapped, nil
}

func ScanStructIntoMap0(obj interface{}) (map[string]interface{}, error) {
	dataStruct := reflect.Indirect(reflect.ValueOf(obj))
	if dataStruct.Kind() != reflect.Struct {
		return nil, errors.New("expected a pointer to a struct")
	}
	dataStructType := dataStruct.Type()
	mapped := make(map[string]interface{})

	for i := 0; i < dataStructType.NumField(); i++ {
		field := dataStructType.Field(i)
		fieldName := field.Name
		mapKey := fieldName
		value := dataStruct.FieldByName(fieldName).Interface()
		mapped[mapKey] = value
	}

	return mapped, nil
}

func GetInsertSql(obj interface{}, tableName string) (sql string, err error) {
	results, err := ScanStructIntoMap(obj)
	if err != nil {
		return "", err
	}
	var keys []string
	var placeholders []string
	delete(results, "id")
	for key, _ := range results {
		keys = append(keys, key)
		placeholders = append(placeholders, "?")
	}
	statement := fmt.Sprintf("insert into %v (%v) values (%v)",
		tableName,
		strings.Join(keys, ", "),
		strings.Join(placeholders, ", "))
	return statement, nil
}

func GetInsertParams(obj interface{}, tableName string) (sql string, args []interface{}, err error) {
	results, err := ScanStructIntoMap(obj)
	if err != nil {
		return "", nil, err
	}
	var keys []string
	var placeholders []string
	delete(results, "id")
	for key, val := range results {
		keys = append(keys, key)
		placeholders = append(placeholders, "?")
		args = append(args, val)
	}
	statement := fmt.Sprintf("insert into %v (%v) values (%v)",
		tableName,
		"`"+strings.Join(keys, "`, `")+"`",
		strings.Join(placeholders, ", "))
	return statement, args, nil
}

func GetUpdateParams(obj interface{}, id int64, tableName string) (sql string, args []interface{}, err error) {
	results, err := ScanStructIntoMap(obj)
	if err != nil {
		return "", nil, err
	}
	var keys []string
	delete(results, "id")
	for key, val := range results {
		keys = append(keys, "`"+key+"`"+"=?")
		args = append(args, val)
	}
	statement := fmt.Sprintf("update %v set %v where id=%d",
		tableName,
		strings.Join(keys, ", "),
		id)
	return statement, args, nil
}

func GetValueFromEle(value reflect.Value) (v string, err error) {
	switch value.Type().Kind() {
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return strconv.FormatInt(value.Int(), 10), nil
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		return strconv.FormatUint(value.Uint(), 10), nil
	case reflect.Float32, reflect.Float64:
		return strconv.FormatFloat(value.Float(), 'f', 3, 64), nil
	case reflect.String:
		return value.String(), nil
	case reflect.Bool:
		return strconv.FormatBool(value.Bool()), nil
	default:
		return "", errors.New(fmt.Sprintf("unmatched %v", value.Type().Kind()))
	}
	return "", errors.New("unmatched")
}

func GetInt64ueFromEle(value reflect.Value) (i int64, err error) {
	v, err := GetValueFromEle(value)
	if err != nil {
		return 0, err
	}
	// json转为Map时候， 数值型会被转换为最宽的浮点型保存数据， 需要特殊处理下
	index := strings.Index(v, ".")
	if index > 0 {
		v = v[0:index]
	}
	i, err = strconv.ParseInt(v, 10, 64)
	return i, err
}

func ParseStringArrayFromRetData(data map[string]interface{}, key string) (error, []string) {

	dataNoType, exists := data[key]

	if !exists {
		return errors.New("data has no field: " + key), nil
	}

	//  type is []interface{}
	dataInterfaceSlice, ok := dataNoType.([]interface{})

	if !ok {
		return errors.New(key + " is not slice"), nil
	}

	strSlice := make([]string, 0)

	for _, elem := range dataInterfaceSlice {

		str, ok := elem.(string)

		if !ok {
			return errors.New("field:" + key + " is not slice of string array"), nil
		}

		strSlice = append(strSlice, str)
	}

	return nil, strSlice

}

func GetIntValueFromEle(value reflect.Value) (i int, err error) {
	v, err := GetValueFromEle(value)
	if err != nil {
		return 0, err
	}
	// json转为Map时候， 数值型会被转换为最宽的浮点型保存数据， 需要特殊处理下
	index := strings.Index(v, ".")
	if index > 0 {
		v = v[0:index]
	}
	i, err = strconv.Atoi(v)
	return i, err
}

/**
*
* 查询结果集转换成map byte类型
 */
func MakeMapByteResult(datarows *sql.Rows) (rows int, data map[int]map[string][]byte, err error) {
	if datarows.Err() != nil {
		return 0, nil, datarows.Err()
	}
	count := 0
	cols, err := datarows.Columns()
	if err != nil {
		return 0, nil, err
	}
	values := make([]sql.RawBytes, len(cols))
	scans := make([]interface{}, len(cols))
	for i := range values {
		scans[i] = &values[i]
	}
	results := make(map[int]map[string][]byte)
	for datarows.Next() {
		if err := datarows.Scan(scans...); err != nil {
			return 0, nil, err
		}
		row := map[string][]byte{}
		for j, v := range values {
			key := cols[j]
			row[key] = func(sql.RawBytes) []byte {
				data := []byte(v)
				value := make([]byte, len(data))
				copy(value, data)
				return value
			}(v)
		}
		results[count] = row
		count++

	}
	return count, results, nil
}

func isInPath(dir string, dirs []string) bool {
	if dirs == nil {
		return false
	}
	for _, dirname := range dirs {
		if dir == dirname {
			return true
		}
	}
	return false
}

//func UnTar(srcTar string, dstDir string, excludes []string) (err error) {
//	// 清理路径字符串
//	dstDir = path.Clean(dstDir) + string(os.PathSeparator)
//	args := make([]string, 0, 0)
//	args = append(args, "-xf")
//	args = append(args, srcTar)
//	args = append(args, "-C")
//	args = append(args, dstDir)
//	args = append(args, "--strip-components")
//	args = append(args, "1")
//	if excludes != nil && len(excludes) > 0 {
//		for _, dir := range excludes {
//			args = append(args, "--exclude")
//			args = append(args, dir)
//		}
//	}
//	cmd := exec.Command("tar", args...)
//	cmd.Env = os.Environ()
//
//	stdout, err := cmd.StdoutPipe()
//	if err != nil {
//		return err
//	}
//	err = cmd.Start()
//	if err != nil {
//		return err
//	}
//
//	bytedata, err := ioutil.ReadAll(stdout)
//	if err != nil {
//		return err
//	}
//
//	err = cmd.Wait()
//	if err != nil {
//		return errors.Trace(err)
//	}
//	fmt.Println("UnTar data:", string(bytedata))
//	return nil
//}

func GetRootDir() string {
	dir, err := filepath.Abs(filepath.Dir(os.Args[0]))
	if err != nil {
		fmt.Println("util.GetRootDir err", err)
		os.Exit(-1)
	}
	dir = filepath.Join(dir, "../") + "/"
	return dir
}

func CheckFileExist(file string) bool {
	_, err := os.Stat(file)
	if err != nil {
		return false
	}
	return true
}

func DelDir(path string) error {
	return os.RemoveAll(path)
}

func DelFile(path string) error {
	return os.Remove(path)
}

func RenameFile(oldpath, newpath string) error {
	return os.Rename(oldpath, newpath)
}

func CreateDireByFilepath(path string) error {
	dir := filepath.Dir(path)
	return Mkdir(dir)
}

func Mkdir(path string) error {
	_, err := os.Stat(path)
	if err != nil {
		err = os.MkdirAll(path, os.ModePerm)
		return err
	}
	return nil
}

func CopyFile(src, dst string) (err error) {
	_, err = os.Stat(src)
	if err != nil {
		return err
	}
	err = copyFileContents(src, dst)
	return err
}

func copyFileContents(src, dst string) (err error) {
	in, err := os.Open(src)
	if err != nil {
		return
	}
	defer in.Close()
	out, err := os.OpenFile(dst, os.O_WRONLY|os.O_TRUNC|os.O_CREATE, 0660)
	if err != nil {
		return err
	}
	defer func() {
		cerr := out.Close()
		if err == nil {
			err = cerr
		}
	}()
	if _, err = io.Copy(out, in); err != nil {
		return err
	}
	err = out.Sync()
	return err
}

func LogSqlWithArgs(sql string, args ...interface{}) string {
	return LogSql(sql, args)
}

func LogSql(sql string, args []interface{}) string {

	sqlItems := strings.Split(sql, "?")

	var expanded string
	for i := 0; i < len(sqlItems); i++ {

		if len(strings.TrimSpace(sqlItems[i])) == 0 {
			continue
		}

		if i < len(args) {
			v := reflect.ValueOf(args[i])
			switch v.Type().Kind() {
			case reflect.String:
				expanded = expanded + sqlItems[i] + fmt.Sprintf("'%s'", v.String())

			case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
				expanded = expanded + sqlItems[i] + strconv.FormatInt(v.Int(), 10)

			case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
				expanded = expanded + sqlItems[i] + strconv.FormatUint(v.Uint(), 10)

			case reflect.Uintptr:
				expanded = expanded + sqlItems[i] + strconv.FormatUint(reflect.Indirect(v).Uint(), 10)

			case reflect.Float32, reflect.Float64:
				expanded = expanded + sqlItems[i] + strconv.FormatFloat(v.Float(), 'f', 2, 64)

			default:
				expanded = expanded + sqlItems[i] + fmt.Sprintf(" %s ", args[i])
			}

		} else if i == len(args) {
			// REPLACE INTO main_time_line(timeline) VALUES(?)  ? 后面还有一部分的情况
			expanded = expanded + sqlItems[i]
		} else {
			expanded = expanded + sqlItems[i] + "?"
		}
	}

	return expanded
}

/**
 * 模拟三元表达式
 */
func IfElse(condition bool, trueReturn, falseReturn interface{}) interface{} {
	if condition {
		return trueReturn
	} else {
		return falseReturn
	}
}

func GetErrMsg(err interface{}) string {
	return fmt.Sprintf("errmsg is:%v", err)
}

func GetRandomString(l int) string {
	str := "0123456789abcdefghijklmnopqrstuvwxyz"
	bytes := []byte(str)
	result := []byte{}
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	for i := 0; i < l; i++ {
		result = append(result, bytes[r.Intn(len(bytes))])
	}
	return string(result)
}

func AesEncrypt(content []byte, key string) (string, error) {
	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		return "", err
	}
	blockSize := block.BlockSize()
	content = pKCS5Padding(content, blockSize)
	//blockMode := cipher.NewCBCDecrypter(block,"")
	blockMode := cipher_galileo.NewECBEncrypter(block)
	crypted := make([]byte, len(content))
	blockMode.CryptBlocks(crypted, content)
	return base64.StdEncoding.EncodeToString(crypted), nil
}

func AesDecrypt(content string, key string) (string, error) {
	decodeData, err := base64.StdEncoding.DecodeString(content)
	if err != nil {
		return "", err
	}
	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		return "", err
	}
	blockMode := cipher_galileo.NewECBDecrypter(block)
	origData := make([]byte, len(decodeData))
	blockMode.CryptBlocks(origData, decodeData)
	origData = pKCS5UnPadding(origData)
	return string(origData), nil
}

func PropertiesToMap(propertiesString string) map[string]string {
	cofMap := make(map[string]string)
	arr := strings.Split(propertiesString, "\n")
	for _, v := range arr {
		index := strings.Index(v, "=")
		if index < 0 {
			continue
		}
		key := strings.TrimSpace(v[:index])
		if len(key) == 0 {
			continue
		}
		value := strings.TrimSpace(v[index+1:])
		if len(value) == 0 {
			continue
		}
		cofMap[key] = value
	}
	return cofMap
}

func ObjectToString(obj interface{}) (string, error) {
	bf := bytes.NewBuffer([]byte{})
	jsonEncoder := json.NewEncoder(bf)
	jsonEncoder.SetEscapeHTML(false)
	err := jsonEncoder.Encode(obj)
	if err != nil {
		return "", err
	}
	return bf.String(), nil
}
