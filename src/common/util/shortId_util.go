package util

import (
	"math"
	"strconv"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/uuid"
)

var POST_PRE_LEN_MAP map[int64][]int64
var PRIME_MAP map[int64]int64

// 转换进制
const ID_BASE = 36

// ID分隔符
const ID_SEPARATOR = "-"

type CidUtil struct {
}

func init() {
	// 前置后置长度信息映射
	POST_PRE_LEN_MAP = map[int64][]int64{
		8:  []int64{32, 9},
		9:  []int64{36, 10},
		10: []int64{40, 11},
	}

	// 素数映射
	PRIME_MAP = map[int64]int64{
		8:  4294967029,
		9:  68719476503,
		10: 1000000005721,
	}
}

/*
# 将加密字符串转换成整型
# 输入
#   v 加密字符串
#   ret_len 保留多少位
# 输出
#   返回整型
*/
func (this *CidUtil) encode_password(v string, retLen int64) int64 {

	var ret int64
	ret = 0
	vLen := uint(len(v))

	var i uint
	for i = 0; i < vLen; i++ {
		ret |= (int64(byte(v[vLen-i-1])) << (8 * i))
	}

	if retLen > 0 {
		ret &= ((1 << uint64(retLen)) - 1)
	}

	return ret
}

/*
# 根据整型得到加密字符串

	# 输入
	#   v 整型
	# 输出
	#   返回加密字符串
*/
func (this *CidUtil) decode_password(v int64) string {
	ret := ""
	for v > 0 {
		ret = ret + strconv.FormatInt(int64(v&255), 10)
		v = v >> 8
	}
	return ret
}

/*
# 返回postfix插入prefix的平均步长

	# 输入
	#   prefix_len 前缀长度
	#   postfix_len 后缀长度
	# 输出
	#   返回postfix插入prefix的平均步长
*/
func (this *CidUtil) step_len(prefix_len, postfix_len int64) int64 {
	return int64(math.Floor(float64(prefix_len / postfix_len)))
}

/*
# 获取val从pos位置长度为len的bit
# 输入
#   val 整型
#   pos 起始位置
#   len 长度
# 输出
#   返回截取的bit
*/
func (this *CidUtil) split_bit(val, pos, len int64) int64 {
	return (val >> uint64(pos)) & ((1 << uint64(len)) - 1)
}

/*
# 返回postfix入prefix的步长
# 输入
#   step_idx 步骤游标
#   step_len 平均步长
#   prefix_len 前缀长度
#   postfix_len 后缀长度
# 输出
#   返回postfix插入prefix的步长
*/
func (this *CidUtil) step_postfix_len(step_idx, step_len, prefix_len, postfix_len int64) int64 {
	if step_idx == postfix_len-1 {
		return prefix_len - step_idx*step_len
	} else {
		return step_len
	}
}

/*
# 将postfix打散到prefix中
# 输入
#   prefix 前缀
#   prefix_len 前缀长度
#   postfix 后缀
#   postfix_len 后缀长度
# 输出
#   返回打散的整型
*/
func (this *CidUtil) shuffle_prefix_postfix(prefix, prefix_len, postfix, postfix_len int64) int64 {
	var ret int64 = 0
	step := this.step_len(prefix_len, postfix_len)
	var i int64
	for i = 0; i < postfix_len; i++ {
		ret |= (((this.split_bit(prefix, i*step, this.step_postfix_len(i, step, prefix_len, postfix_len)) << 1) | this.split_bit(postfix, i, 1)) << uint64(((step + 1) * i)))
	}
	return ret
}

/*
# 参考算法导论 欧几里德算法的推广形式
# d = ax + by
# 根据欧几里德算法推广形式计算出d,x,y
# 输入
#   a, b
# 输出
#   d,x,y
*/
func (this *CidUtil) extended_euclid(a, b int64) []int64 {
	ret := make([]int64, 0)
	if b <= 0 {
		ret = append(ret, a)
		ret = append(ret, 1)
		ret = append(ret, 0)
		return ret
	}

	tmp := this.extended_euclid(b, a%b)

	ret = append(ret, tmp[0])
	ret = append(ret, tmp[2])
	ret = append(ret, (tmp[1] - int64(math.Floor(float64(a/b)))*tmp[2]))

	return ret
}

/*
# 参考算法导论 推论31.26
# 当gcd(a, b) = 1, 则方程ax + ny = 1有唯一解,这个唯一解就是
# extended_euclid(a, b)算法得到的x
# 输入
#   a, b
# 输出
#   x
*/
func (this *CidUtil) inverse(a, b int64) int64 {
	tmp := this.extended_euclid(a, b)
	if tmp[1] < 0 {
		tmp[1] = tmp[1]%b + b
	}

	return tmp[1]
}

/*
# 将整型转换成字符串
# 输入
#   v 整型
#   b 转换的进制
#   ret_len 返回的字符串长度
# 输出
#   返回字符串
*/
func (this *CidUtil) int_2_str(v int64, b int, ret_len int64) string {
	ret := strconv.FormatInt(v, b)
	if int64(len(ret)) < ret_len {
		var i int64
		//		for i = 0; i < ret_len - int64(len(ret)); i++ {
		//			ret = "0" + ret;
		//		}
		for i = int64(len(ret)); i < ret_len; i++ {
			ret = "0" + ret
		}
	}

	return ret
}

/*
# 加密得到ID
# 输入
#   prefix 前缀
#   prefix_len 前缀长度
#   postfix 后缀
#   postfix_len 后缀长度
#   password 加密字符串
#   base 转换进制
#   prime 素数
#   ret_len 返回的字符串长度
# 输出
#   返回字符串ID
*/
func (this *CidUtil) encode(prefix, prefix_len, postfix, postfix_len int64, password string, base int, prime, ret_len int64) string {
	newPassword := this.encode_password(password, prefix_len+postfix_len)
	ret := this.shuffle_prefix_postfix(this.inverse(prefix, prime), prefix_len, postfix, postfix_len)
	ret ^= newPassword
	return this.int_2_str(ret, base, ret_len)
}

/*
# 将整型唯一ID转换成字符串ID
# 输入
#   obj_id 对象分地域唯一性ID
#   abbreviate 对象缩写
#   password 对象加密字符串 长度5-8位
#   zone 地域信息
#   id_len 返回的ID长度(不包括缩写) 8-10
# 输出
#   返回对象字符串ID
*/
func (this *CidUtil) EncodeId(obj int64, abbreviate, password string, zone, id_len int64) string {
	tmp, _ := POST_PRE_LEN_MAP[id_len]
	prime, _ := PRIME_MAP[id_len]
	uuidObj := uuid.New()
	password = password + uuidObj
	return abbreviate + ID_SEPARATOR + this.encode(obj, tmp[0], zone, tmp[1], password, ID_BASE, prime, id_len)
}

/*
# 取出postfix以及prefix
# 输入
#   val 打散后的整型
#   prefix_len 前缀长度
#   postfix_len 后缀长度
# 输出
#   返回前缀、后缀
*/
func (this *CidUtil) split_prefix_postfix(val, prefix_len, postfix_len int64) []int64 {
	var prefix int64
	prefix = 0
	var postfix int64
	postfix = 0

	step := this.step_len(prefix_len, postfix_len)
	var i int64
	for i = 0; i < postfix_len; i++ {
		postfix |= (this.split_bit(val, (step+1)*i, 1) << uint64(i))
		prefix |= (this.split_bit(val, ((step+1)*i)+1, this.step_postfix_len(i, step, prefix_len, postfix_len)) << uint64(step*i))
	}
	return []int64{prefix, postfix}
}

/*
# 从字符串ID得到前缀、后缀
# 输入
#   v 字符串ID
#   prefix_len 前缀长度
#   postfix_len 后缀长度
#   password 加密字符串
#   base 转换进制
#   prime 素数
# 输出
#   返回前缀、后缀整型
*/
func (this *CidUtil) decode(v string, prefix_len, postfix_len int64, password string, base int, prime int64) ([]int64, error) {
	newPassword := this.encode_password(password, prefix_len+postfix_len)

	iV, err := strconv.ParseInt(v, base, 64)
	if err != nil {
		return nil, err
	}

	iV ^= newPassword
	tmp := this.split_prefix_postfix(iV, prefix_len, postfix_len)
	tmp[0] = this.inverse(tmp[0], prime)
	return tmp, nil
}

/*
# 从字符串ID分解出整型ID以及地域信息
# 输入
#   v 对象字符串ID
#   password 对象加密字符串
# 输出
#   返回整型ID以及地域
*/
func (this *CidUtil) DecodeId(v, password string) ([]int64, error) {
	items := strings.Split(v, ID_SEPARATOR)
	id := items[1]
	id_len := int64(len(id))
	tmp := POST_PRE_LEN_MAP[id_len]
	return this.decode(id, tmp[0], tmp[1], password, ID_BASE, PRIME_MAP[id_len])
}
