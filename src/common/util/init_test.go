package util

import (
	"flag"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
)

var (
	fTestPassword  = flag.String("test.password", "admin321", "")
	fTestKey       = flag.String("test.key", constants.AES_ENCRYPT_KEY, "")
	fTestEncrypted = flag.String("test.encrypted", "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", "")
)
