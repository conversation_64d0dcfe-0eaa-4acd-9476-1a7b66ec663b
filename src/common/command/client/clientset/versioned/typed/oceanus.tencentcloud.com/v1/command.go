/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package v1

import (
	"context"
	scheme "tencentcloud.com/tstream_galileo/src/common/command/client/clientset/versioned/scheme"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	rest "k8s.io/client-go/rest"
	v1 "tencentcloud.com/tstream_galileo/src/common/command/apis/oceanus.tencentcloud.com/v1"
)

// CommandsGetter has a method to return a CommandInterface.
// A group's client should implement this interface.
type CommandsGetter interface {
	Commands(namespace string) CommandInterface
}

// CommandInterface has methods to work with Command resources.
type CommandInterface interface {
	Create(*v1.Command) (*v1.Command, error)
	Update(*v1.Command) (*v1.Command, error)
	Delete(name string, options *metav1.DeleteOptions) error
	DeleteCollection(options *metav1.DeleteOptions, listOptions metav1.ListOptions) error
	Get(name string, options metav1.GetOptions) (*v1.Command, error)
	List(opts metav1.ListOptions) (*v1.CommandList, error)
	Watch(opts metav1.ListOptions) (watch.Interface, error)
	Patch(name string, pt types.PatchType, data []byte, subresources ...string) (result *v1.Command, err error)
	CommandExpansion
}

// commands implements CommandInterface
type commands struct {
	client rest.Interface
	ns     string
}

// newCommands returns a Commands
func newCommands(c *OceanusV1Client, namespace string) *commands {
	return &commands{
		client: c.RESTClient(),
		ns:     namespace,
	}
}

// Get takes name of the command, and returns the corresponding command object, and an error if there is any.
func (c *commands) Get(name string, options metav1.GetOptions) (result *v1.Command, err error) {
	result = &v1.Command{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("commands").
		Name(name).
		VersionedParams(&options, scheme.ParameterCodec).
		Do(context.TODO()).
		Into(result)
	return
}

// List takes label and field selectors, and returns the list of Commands that match those selectors.
func (c *commands) List(opts metav1.ListOptions) (result *v1.CommandList, err error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	result = &v1.CommandList{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("commands").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(context.TODO()).
		Into(result)
	return
}

// Watch returns a watch.Interface that watches the requested commands.
func (c *commands) Watch(opts metav1.ListOptions) (watch.Interface, error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	opts.Watch = true
	return c.client.Get().
		Namespace(c.ns).
		Resource("commands").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Watch(context.TODO())
}

// Create takes the representation of a command and creates it.  Returns the server's representation of the command, and an error, if there is any.
func (c *commands) Create(command *v1.Command) (result *v1.Command, err error) {
	result = &v1.Command{}
	err = c.client.Post().
		Namespace(c.ns).
		Resource("commands").
		Body(command).
		Do(context.TODO()).
		Into(result)
	return
}

// Update takes the representation of a command and updates it. Returns the server's representation of the command, and an error, if there is any.
func (c *commands) Update(command *v1.Command) (result *v1.Command, err error) {
	result = &v1.Command{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("commands").
		Name(command.Name).
		Body(command).
		Do(context.TODO()).
		Into(result)
	return
}

// Delete takes name of the command and deletes it. Returns an error if one occurs.
func (c *commands) Delete(name string, options *metav1.DeleteOptions) error {
	return c.client.Delete().
		Namespace(c.ns).
		Resource("commands").
		Name(name).
		Body(options).
		Do(context.TODO()).
		Error()
}

// DeleteCollection deletes a collection of objects.
func (c *commands) DeleteCollection(options *metav1.DeleteOptions, listOptions metav1.ListOptions) error {
	var timeout time.Duration
	if listOptions.TimeoutSeconds != nil {
		timeout = time.Duration(*listOptions.TimeoutSeconds) * time.Second
	}
	return c.client.Delete().
		Namespace(c.ns).
		Resource("commands").
		VersionedParams(&listOptions, scheme.ParameterCodec).
		Timeout(timeout).
		Body(options).
		Do(context.TODO()).
		Error()
}

// Patch applies the patch and returns the patched command.
func (c *commands) Patch(name string, pt types.PatchType, data []byte, subresources ...string) (result *v1.Command, err error) {
	result = &v1.Command{}
	err = c.client.Patch(pt).
		Namespace(c.ns).
		Resource("commands").
		SubResource(subresources...).
		Name(name).
		Body(data).
		Do(context.TODO()).
		Into(result)
	return
}
