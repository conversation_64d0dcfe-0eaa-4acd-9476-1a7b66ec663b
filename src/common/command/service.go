package command

import (
	"fmt"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"strings"
	v2 "tencentcloud.com/tstream_galileo/src/common/command/apis/oceanus.tencentcloud.com/v1"
	command "tencentcloud.com/tstream_galileo/src/common/command/client/clientset/versioned"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
	tableService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/tke"
	"unicode/utf8"
)

type CommandService struct {
	Cluster         *table.Cluster
	k8sService      *k8s.K8sService
	client          *command.Clientset
	imagePullSecret *v1.Secret
}

type CommandRequest struct {
	ReqId    string
	Url      string
	SendData string
	Uin      string
	Apikey   string
	Timeout  int64
	Callback string
}

func NewCommandService(clusterId int64) (*CommandService, error) {
	cluster, k8sService, client, imagePullSecret, err := InitCommandRecource(clusterId)
	if err != nil {
		return nil, err
	}
	return &CommandService{
		Cluster:         cluster,
		k8sService:      k8sService,
		client:          client,
		imagePullSecret: imagePullSecret,
	}, nil
}

// 合并 CA CM TS 服务以后， 获取 cluster admin（历史集群） 或者 cluster scheduler（新集群）的地址
// 默认是 CS的地址，如果历史集群有 CA 服务，就用CA的...
func GetCAOrCSUrl(cluster *table.Cluster) (string, error) {
	archGeneration, err := tableService.GetTableService().GetTkeArchGeneration(cluster.Id)
	if err != nil {
		return "", err
	}

	serviceUrl := fmt.Sprintf("http://%s:%d/interface", constants.ClusterAdminServiceName, constants.ClusterAdminPort)
	if archGeneration >= constants.TKE_ARCH_GENERATION_V2 {
		serviceUrl = fmt.Sprintf("http://%s:%d/interface", constants.ClusterSchedulerServiceName, constants.ClusterSchedulerPort)
	}

	logger.Debugf("### cluster id %d ClusterAdminServiceUrl is %s", cluster.Id, serviceUrl)
	return serviceUrl, nil
}

func (this *CommandService) DoRequest(req *CommandRequest) (string, error) {
	cmd := NewCommandCR(req.ReqId, req.Url, req.SendData, req.Uin, req.Apikey, this.imagePullSecret, req.Timeout, req.Callback)

	r, err := this.k8sService.CommandService.ExecuteCommandSync(this.client, cmd, req.Timeout)
	if err != nil {
		logger.Errorf("%s, watch cmd error,msg:%s", req.ReqId, err.Error())
		if strings.Contains(err.Error(), "timeout") {
			return "", errorcode.FailedOperationCode.ReplaceDesc(err.Error())
		} else {
			return "", errorcode.InternalErrorCode_DoCommandRequestFailed.NewWithErr(err)
		}
	}
	rsp := r.Spec.Rsp
	if rsp == "" || strings.HasPrefix(rsp, "error:") {
		msg := fmt.Sprintf("%s Failed to request to the %s,reason:%s", req.ReqId, req.Url, rsp)
		logger.Errorf(msg)
		return "", errorcode.InvalidParameterCode.ReplaceDesc(msg)
	}
	logger.Debugf("%s get response:%s", req.ReqId, rsp)
	return rsp, nil
}

func (this *CommandService) HandlerException(returnCode int, reqId string, returnMsg string) error {
	if returnCode != 0 {
		msg := fmt.Sprintf("%s request done but return exception : %s", reqId, returnMsg)
		logger.Error(msg)
		switch returnCode {
		case 2:
			return errorcode.ResourceNotFoundCode_TableNotExist.ReplaceDesc(returnMsg)
		case 3:
			return errorcode.FailedOperationCode_DataSourceConnectionFailed.ReplaceDesc(returnMsg)
		default:
			return errorcode.InvalidParameterCode.ReplaceDesc(returnMsg)
		}
	}
	return nil
}

func subString(input string) string {
	if utf8.RuneCountInString(input) > 10 {
		for i := range input {
			if utf8.RuneCountInString(input[:i]) == 10 {
				input = input[:i]
				break
			}
		}
	}
	return input
}

func NewCommandCR(reqId string, url string, sendData string, uin string, apikey string, imagePullSecret *v1.Secret, timeout int64, callback string) *v2.Command {
	suffix := subString(strings.ToLower(apikey))
	cmd := &v2.Command{
		ObjectMeta: metav1.ObjectMeta{
			Name:      reqId + "-command" + suffix,
			Namespace: constants.OCEANUS_NAMESPACE,
		},
		Spec: v2.CommandSpec{
			Url:        url,
			Callback:   callback,
			SendData:   sendData,
			ApiKey:     apikey,
			RequestId:  reqId,
			CreateTime: util.GetCurrentTime(),
			Uin:        uin,
			Timeout:    timeout,
		},
	}
	if imagePullSecret != nil {
		cmd.ObjectMeta.OwnerReferences = tke.GetTkeService().SecretAsOwnerReference(imagePullSecret)
	}
	return cmd
}

func InitCommandRecource(clusterId int64) (cluster *table.Cluster, k8sService *k8s.K8sService, client *command.Clientset, imagePullSecret *v1.Secret, err error) {

	cluster, err = tableService.GetTableService().ListClusterById(clusterId)
	if err != nil {
		return nil, nil, nil, nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	clusterGroup, err := tableService.GetTableService().ListClusterGroupById(cluster.ClusterGroupId)
	if err != nil {
		return nil, nil, nil, nil, err
	}
	k8sService = k8s.GetK8sService()

	client, err = k8sService.NewCommandClient([]byte(cluster.KubeConfig))
	if err != nil {
		return nil, nil, nil, nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	clientset, err := k8sService.NewClient([]byte(cluster.KubeConfig))
	if err != nil {
		return nil, nil, nil, nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	_, imagePullSecret, err = tke.GetTkeService().GetOrCreateImagePullSecrets(clusterGroup.Region, clientset)
	if err != nil {
		return nil, nil, nil, nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	return
}
