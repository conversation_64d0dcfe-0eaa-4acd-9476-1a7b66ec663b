package errorcode

var (
	// 预定义所有错误码,请统一在这里增加错误码
	InternalErrorCode         = NewCode("InternalError", 11000, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	InvalidParameterCode      = NewCode("InvalidParameter", 13000, "InvalidParameter")
	InvalidParameterValueCode = NewCode("InvalidParameterValue", 13000, "InvalidParameterValue")
	MissingParameterCode      = NewCode("MissingParameter", 15000, "MissingParameter")
	UnknownParameterCode      = NewCode("UnknownParameter", 17000, "UnknownParameter")
	UnauthorizedOperationCode = NewCode("UnauthorizedOperation", 18000, "UnauthorizedOperation")
	UnsupportedOperationCode  = NewCode("UnsupportedOperation", 16000, "UnsupportedOperation")
	ResourceNotFoundCode      = NewCode("ResourceNotFound", 12000, "ResourceNotFound")
	LimitExceededCode         = NewCode("LimitExceeded", 19000, "LimitExceeded")
	ResourceUnavailableCode   = NewCode("ResourceUnavailable", 20000, "ResourceUnavailable")
	ResourceInsufficientCode  = NewCode("ResourceInsufficient", 21000, "ResourceInsufficient")
	FailedOperationCode       = NewCode("FailedOperation", 22000, "FailedOperation")
	ResourceInUseCode         = NewCode("ResourceInUse", 14000, "ResourceInUse")
	DryRunOperationCode       = NewCode("DryRunOperation", 23000, "DryRunOperation")
	ResourceSoldOutCode       = NewCode("ResourcesSoldOut", 24000, "ResourcesSoldOut")
	AuthFailureCode           = NewCode("AuthFailure", 25000, "AuthFailure")
	NoPrivilege               = NewCode("NoPrivilege", 26000, "NoPrivilege")

	// 二级错误码定义
	// 以InvalidParameter_RegionErr为例，需要在APIv3平台上的一级错误码InvalidParameter下面添加二级错误码InvalidRegion，最终返回到云API平台时，错误字符串为InvalidParameterValue.InvalidRegion

	InternalErrorCode_NET                        = InternalErrorCode.MakeSecondCode("Net", 11001, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	InternalErrorCode_Component                  = InternalErrorCode.MakeSecondCode("InternalErrorCode_Component", 11001, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	InternalErrorCode_GetLockFailed              = InternalErrorCode.MakeSecondCode("GetLockFailed", 11002, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	InternalErrorCode_CreateFlowFailed           = InternalErrorCode.MakeSecondCode("CreateFlowFailed", 11003, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	InternalErrorCode_QueryFlowFailed            = InternalErrorCode.MakeSecondCode("QueryFlowFailed", 11004, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	InternalErrorCode_MarshalFailed              = InternalErrorCode.MakeSecondCode("MarshalFailed", 11005, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	InternalErrorCode_UnMarshalFailed            = InternalErrorCode.MakeSecondCode("UnMarshalFailed", 11006, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	InternalErrorCode_GetStsAssumeRoleFailed     = InternalErrorCode.MakeSecondCode("GetStsAssumeRoleFailed", 11007, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	InternalErrorCode_NoContainerFound           = InternalErrorCode.MakeSecondCode("NoContainerFound", 11008, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	InternalErrorCode_UnknownNetType             = InternalErrorCode.MakeSecondCode("UnknownNetType", 11009, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	InternalErrorCode_TypeIncompatible           = InternalErrorCode.MakeSecondCode("TypeIncompatible", 11010, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	InternalErrorCode_UnexpectedRecordNums       = InternalErrorCode.MakeSecondCode("UnexpectedRecordNums", 11011, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	InternalErrorCode_NoRsp                      = InternalErrorCode.MakeSecondCode("NoRsp", 11012, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	InternalErrorCode_DoCommandRequestFailed     = InternalErrorCode.MakeSecondCode("DoCommandRequestFailed", 11013, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	InternalErrorCode_DoSqlFailed                = InternalErrorCode.MakeSecondCode("DoSqlFailed", 11014, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	InternalErrorCode_ParseTimeFailed            = InternalErrorCode.MakeSecondCode("ParseTimeFailed", 11015, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	InternalErrorCode_CreateCommandServiceFailed = InternalErrorCode.MakeSecondCode("CreateCommandServiceFailed", 11016, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	InternalErrorCode_GetCAOrCSRspFailed         = InternalErrorCode.MakeSecondCode("GetCAOrCSRspFailed", 11017, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	InternalErrorCode_GetBucketFailed            = InternalErrorCode.MakeSecondCode("GetBucketFailed", 11018, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	InternalErrorCode_StsAssumeRoleNoAccess      = InternalErrorCode.MakeSecondCode("StsAssumeRoleNoAccess", 11019, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	InternalErrorCode_DecodeK8sObjectFailed      = InternalErrorCode.MakeSecondCode("DecodeK8sObjectFailed", 11020, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	InternalErrorCode_EncodeFailed               = InternalErrorCode.MakeSecondCode("EncodeFailed", 11021, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	InternalErrorCode_UnSupportUnit              = InternalErrorCode.MakeSecondCode("UnSupportUnit", 11022, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	// 单独指依赖的资源
	InternalErrorCode_ResourceNotFound     = InternalErrorCode.MakeSecondCode("ResourceNotFound", 11023, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	InternalErrorCode_CreateResourceFailed = InternalErrorCode.MakeSecondCode("CreateResourceFailed", 11024, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	InternalErrorCode_AssignBucketFailed   = InternalErrorCode.MakeSecondCode("AssignBucketFailed", 11025, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	// JobConfig
	InternalErrorCode_JobConfigNotFound     = InternalErrorCode.MakeSecondCode("JobConfigNotFound", 11026, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	InternalErrorCode_JobConfigCreateFailed = InternalErrorCode.MakeSecondCode("JobConfigCreateFailed", 11027, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	InternalErrorCode_JobConfigUpdateFailed = InternalErrorCode.MakeSecondCode("JobConfigUpdateFailed", 11028, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	InternalErrorCode_JobConfigDeleteFailed = InternalErrorCode.MakeSecondCode("JobConfigDeleteFailed", 11029, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	// K8sClient
	InternalErrorCode_K8sClientCreateFailed = InternalErrorCode.MakeSecondCode("K8sClientCreateFailed", 11030, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	// ResourceRef
	InternalErrorCode_ResourceRefCreateFailed = InternalErrorCode.MakeSecondCode("ResourceRefCreateFailed", 11031, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	InternalErrorCode_ResourceRefDeleteFailed = InternalErrorCode.MakeSecondCode("ResourceRefCreateFailed", 11031, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	// Subnet
	InternalErrorCode_SubnetNotFound = InternalErrorCode.MakeSecondCode("SubnetNotFound", 11032, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	// Job
	InternalErrorCode_JobCreateFailed = InternalErrorCode.MakeSecondCode("JobCreateFailed", 11033, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	InternalErrorCode_JobUpdateFailed = InternalErrorCode.MakeSecondCode("JobUpdateFailed", 11034, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	InternalErrorCode_JobDeleteFailed = InternalErrorCode.MakeSecondCode("JobDeleteFailed", 11035, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	// MetaRef
	InternalErrorCode_MetaRefCreateFailed = InternalErrorCode.MakeSecondCode("MetaRefCreateFailed", 11036, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	InternalErrorCode_MetaRefDeleteFailed = InternalErrorCode.MakeSecondCode("MetaRefDeleteFailed", 11037, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	// VariableRef
	InternalErrorCode_VariableRefCreateFailed = InternalErrorCode.MakeSecondCode("VariableRefCreateFailed", 11038, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	InternalErrorCode_VariableRefDeleteFailed = InternalErrorCode.MakeSecondCode("VariableRefDeleteFailed", 11039, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	// Variable
	InternalErrorCode_VariableCreateFailed = InternalErrorCode.MakeSecondCode("VariableCreateFailed", 11040, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	InternalErrorCode_VariableUpdateFailed = InternalErrorCode.MakeSecondCode("VariableUpdateFailed", 11041, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	InternalErrorCode_VariableDeleteFailed = InternalErrorCode.MakeSecondCode("VariableDeleteFailed", 11042, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	// MetaLineage
	InternalErrorCode_MetaLineageCreateFailed = InternalErrorCode.MakeSecondCode("MetaLineageCreateFailed", 11043, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	InternalErrorCode_MetaLineageDeleteFailed = InternalErrorCode.MakeSecondCode("MetaLineageDeleteFailed", 11044, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	// Tag
	InternalErrorCode_TagBindFailed   = InternalErrorCode.MakeSecondCode("TagBindFailed", 11045, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")
	InternalErrorCode_TagUnBindFailed = InternalErrorCode.MakeSecondCode("TagUnBindFailed", 11046, "An internal error has occurred. Retry your request, but if the problem persists, contact us.")

	InvalidParameter_InvalidSoftWareName = InvalidParameterCode.MakeSecondCode("InvalidSoftWareName", 30507, "invalid parameter  of software name")

	InvalidParameter_InvaildTaskCount = InvalidParameterCode.MakeSecondCode("InvalidTaskCount", 30525, "invalid parameter, the number of task nodes must be less than or equal to 20")
	InvalidParameter_InvaildCoreCount = InvalidParameterCode.MakeSecondCode("InvalidCoreCount", 30526, "invalid parameter, the number of core nodes must be less than or equal to 20")

	InvalidParameterValue_RegionErr       = InvalidParameterCode.MakeSecondCode("InvalidRegion", 30528, "invalid parameter value of `Region`")
	InvalidParameterValue_JobIdValueError = InvalidParameterValueCode.MakeSecondCode("JobIdValueError", 30577, "invalid job id")
	InvalidParameterValue_Timestamp       = InvalidParameterValueCode.MakeSecondCode("Timestamp", 30578, "invalid timestamp value. Range should be less than 7 days, and not more than 90 days ahead of current time.")
	InvalidParameterValue_Type            = InvalidParameterValueCode.MakeSecondCode("Type", 30579, "invalid type")
	InvalidParameterValue_Legitimate      = InvalidParameterValueCode.MakeSecondCode("Legitimate", 30580, "Legitimate value")

	InvalidParameter_InvalidCosFileURI  = InvalidParameterCode.MakeSecondCode("InvalidCosFileURI", 30527, "COS file access failed, please check it")
	InvalidParameter_InvalidFilePath    = InvalidParameterCode.MakeSecondCode("InvalidFilePath", 30529, "COS file access failed, please check it")
	InvalidParameter_InvalidDownloadObj = InvalidParameterCode.MakeSecondCode("InvalidDownloadObj", 30530, "COS file access failed, please check it")
	InvalidParameter_InvalidWriteFile   = InvalidParameterCode.MakeSecondCode("InvalidWriteFile", 30531, "COS file access failed, please check it")
	InvalidParameter_InvalidExtendField = InvalidParameterCode.MakeSecondCode("InvalidExtendField", 30601, "Extend fields check failed, please check it")
	InvalidParameter_InvalidCosClient   = InvalidParameterCode.MakeSecondCode("InvalidCosClient", 30602, "Failed to create cos client, please check it")

	InvalidParameter_InvalidServiceName       = InvalidParameterCode.MakeSecondCode("InvalidServiceName", 30533, "invalid parameter  of service name")
	InvalidParameter_InvalidClassification    = InvalidParameterCode.MakeSecondCode("InvalidClassification", 30534, "invalid parameter  of classification")
	InvalidParameter_InvalidSoftWareVersion   = InvalidParameterCode.MakeSecondCode("InvalidSoftWareVersion", 30535, "invalid parameter  of software version")
	InvalidParameter_InvalidExtendNameService = InvalidParameterCode.MakeSecondCode("InvalidExtendNameService", 30603, "invalid parameter  of extend name service")
	InvalidParameter_InvaildPaymod            = InvalidParameterCode.MakeSecondCode("InvalidPaymode", 30512, "invalid parameter of PayMode")

	InvalidParameter_InvaildUnNecessaryNodeList = InvalidParameterCode.MakeSecondCode("InvalidUnNecessaryNodeList", 30528, "invalid parameter, some node info not exist")
	InvalidParameter_InvaildModifySpec          = InvalidParameterCode.MakeSecondCode("InvalidModifySpec", 13001, "NewCpu and NewMem must great than old spec")
	InvalidParameter_InvaildResourceId          = InvalidParameterCode.MakeSecondCode("InvalidResourceId", 13002, "the resource disk is LOCAL_BASIC or LOCAL_SSD")
	InvalidParameter_InvaildClientToken         = InvalidParameterCode.MakeSecondCode("InvalidClientToken", 13003, "ClientToken length must greater than 0 and less than 64 ")
	InvalidParameter_InvalidAppId               = InvalidParameterCode.MakeSecondCode("InvalidAppId", 30604, "invalid AppId")
	InvalidParameter_InvalidPassword            = InvalidParameterCode.MakeSecondCode("InvalidPassword", 30605, "invalid password")
	InvalidParameter_InvaildTimeUnit            = InvalidParameterCode.MakeSecondCode("InvalidTimeUnit", 13004, "invalid TimeUnit ")
	InvalidParameter_InvaildTimeSpan            = InvalidParameterCode.MakeSecondCode("InvalidTimeSpan", 13005, "invailid TimeSpan ")
	InvalidParameter_InvaildCount               = InvalidParameterCode.MakeSecondCode("InvalidCount", 13006, "CoreCount or TaskCount should be more than 0")
	InvalidParameter_InvaildCountNum            = InvalidParameterCode.MakeSecondCode("InvalidCountNum", 13007, "CoreCount and TaskCount should not more than 1 at same time")
	InvalidParameter_InvaildResType             = InvalidParameterCode.MakeSecondCode("InvalidResType", 13008, "invalid ResType")
	InvalidParameter_InvaildNodeType            = InvalidParameterCode.MakeSecondCode("InvalidNodeType", 13008, "invalid NodeType")
	InvalidParameter_InvalidClusterId           = InvalidParameterCode.MakeSecondCode("InvalidClusterId", 30511, "invalid parameter of clusterId")
	InvalidParameter_InvalidConfigType          = InvalidParameterCode.MakeSecondCode("InvalidConfigType", 13009, "invalid ConfigType")
	InvalidParamter_InvalidExportConfContexts   = InvalidParameterCode.MakeSecondCode("InvalidExportConfContexts", 13010, "invalid parameter of ExportConfContexts")
	InvalidParamter_InvalidRouterCount          = InvalidParameterCode.MakeSecondCode("InvalidRouterCount", 13011, "invalid parameter, the number of router nodes "+
		"must be less than or equal to 20 and great than 0")
	InvalidParameter_InvalidCommonDiskType        = InvalidParameterCode.MakeSecondCode("InvalidCommonDiskType", 13012, "invalid parameter, the disk type of common node")
	InvalidParameter_InvalidMasterDiskType        = InvalidParameterCode.MakeSecondCode("InvalidMasterDiskType", 13013, "invalid parameter, the disk type of master node")
	InvalidParameter_InvalidTaskDiskType          = InvalidParameterCode.MakeSecondCode("InvalidTaskDiskType", 13014, "invalid parameter, the disk type of task node")
	InvalidParameter_InvalidCoreDiskType          = InvalidParameterCode.MakeSecondCode("InvalidCoreDiskType", 13015, "invalid parameter, the disk type of core node")
	InvalidParameter_SoftwareNotInProduct         = InvalidParameterCode.MakeSecondCode("SoftwareNotInProduct", 13016, "invalid parameter, selected software not in product")
	InvalidParameter_NotContainMustSelectSoftware = InvalidParameterCode.MakeSecondCode("NotContainMustSelectSoftware", 13017, "invalid parameter, selected software not in product")
	InvalidParameter_InvalidCOSInfo               = InvalidParameterCode.MakeSecondCode("InvalidCOSInfo", 13018, "invalid COS relative parameter")
	InvalidParameter_HALessMasterCount            = InvalidParameterCode.MakeSecondCode("HALessMasterCount", 13019, "count of master is less, must be 2 In HA Instance")
	InvalidParameter_HALessCoreCount              = InvalidParameterCode.MakeSecondCode("HALessCoreCount", 13020, "count of core is less, must be equal to or greater than 3 In HA Instance")
	InvalidParameter_InvalidCommonCount           = InvalidParameterCode.MakeSecondCode("IncorrectCommonCount", 13021, "count of common must be odd and granter than 2 in HA Instance, and must be 0 in normal Instance")
	InvalidParameter_InvalidMasterCount           = InvalidParameterCode.MakeSecondCode("IncorrectMasterCount", 13022, "count of master  must be 1 In normal Instance, and be 2 In HA Instance")
	InvalidParameter_InvalidCoreCount             = InvalidParameterCode.MakeSecondCode("InvalidCoreCount", 13023, "count of core must be greater than 2 in HA Instance , and greater than 1 in normal Instance")
	InvalidParameter_InvalidResourceSpec          = InvalidParameterCode.MakeSecondCode("InvalidResourceSpec", 13024, "invalid Resource.Spec")
	InvalidParameter_InvalidDiskSize              = InvalidParameterCode.MakeSecondCode("InvalidDiskSize", 13025, "invalid DiskSize")
	InvalidParameter_LessTaskCount                = InvalidParameterCode.MakeSecondCode("LessTaskCount", 13026, "count of Task is less ")
	InvalidParameter_LessCommonCount              = InvalidParameterCode.MakeSecondCode("LessCommonCount", 13027, "count of Common is less")
	InvalidParameter_LessCvmInstanceId            = InvalidParameterCode.MakeSecondCode("LessCvmInstanceId", 13028, "count of CvmInstanceId is less")
	InvalidParameter_IllegalSearchKeyword         = InvalidParameterCode.MakeSecondCode("IllegalSearchKeyword", 15029, "Search syntax error. For details, visit https://cloud.tencent.com/document/product/614/47044")
	InvalidParameter_IllegalMaxParallelism        = InvalidParameterCode.MakeSecondCode("IllegalMaxParallelism", 15030, "Invalid value of \"pipeline.max-parallelism\". Its value must be in the range of [maximum parallelism of the job, 16384].")
	InvalidParameter_MaxParallelismTooSmall       = InvalidParameterCode.MakeSecondCode("MaxParallelismTooSmall", 15031, "\"pipeline.max-parallelism\" cannot be smaller than the default parallelism of the operator. Please reduce the default parallelism of the operator, or increase \"pipeline.max-parallelism\" (the running status cannot be retained).")
	InvalidParameter_MaxParallelismTooLarge       = InvalidParameterCode.MakeSecondCode("MaxParallelismTooLarge", 15032, "The value of \"pipeline.max-parallelism\" is too large. The recommended value is no more than 2048, and the maximum value allowed is 16384.")
	InvalidParameter_CLSNotEnabled                = InvalidParameterCode.MakeSecondCode("CLSNotEnabled", 15033, "Cloud Log Service is not activated. Please access https://console.cloud.tencent.com/cls to activate it.")
	InvalidParameter_MetaNetworkError             = InvalidParameterCode.MakeSecondCode("MetaNetworkError", 15036, "The external metadata network is unavailable")
	InvalidParameter_DatabaseNotExist             = InvalidParameterCode.MakeSecondCode("DatabaseNotExist ", 15037, "The external database does not exist")

	InvalidParameter_CreateClsLogsetError = InvalidParameterCode.MakeSecondCode("CreateClsLogsetError", 15034, "Create Cls logset failed.")
	InvalidParameter_CreateClsTopicError  = InvalidParameterCode.MakeSecondCode("CreateClsTopicError", 15035, "Create Cls topic failed.")

	InvalidParameter_ModifyClusterClsError      = InvalidParameterCode.MakeSecondCode("ModifyClusterClsError", 15036, "Modify cluster Cls failed.")
	InvalidParameter_ModifyClusterClsTopicError = InvalidParameterCode.MakeSecondCode("ModifyClusterClsError", 15037, "Modify cluster Cls failed. CLS Topic already bundled.")

	InvalidParameter_ModifyClusterDefaultLogCollectConfError = InvalidParameterCode.MakeSecondCode("ModifyClusterDefaultLogCollectConfError", 15038, "Modify cluster default log collect failed. COS bucket not valid.")

	InvalidParameter_JobConfigLogCollectParamError = InvalidParameterCode.MakeSecondCode("JobConfigLogCollectParamError", 15039, "Create job config error, LogCollectType not valid.")
	InvalidParameter_JobConfigCosBucketParamError  = InvalidParameterCode.MakeSecondCode("JobConfigLogCollectParamError", 15040, "Create job config error, COSBucket not valid.")

	InvalidParameter_JobConfigPropertiesParamError = InvalidParameterCode.MakeSecondCode("JobConfigPropertiesParamError", 15041, "Modify draft config error, properties value not valid.")
	InvalidParameter_JobLogLevelParamError         = InvalidParameterCode.MakeSecondCode("ModifyJobLogLevelError", 15042, "Modify job log level error, invalid level")
	InvalidParameter_JobStatusError                = InvalidParameterCode.MakeSecondCode("ModifyJobLogLevelError", 15043, "Modify job log level error, job is not running")
	InvalidParameter_FlinkVersionError             = InvalidParameterCode.MakeSecondCode("ModifyJobLogLevelError", 15044, "Modify job log level error, flink version not support")
	InvalidParameter_ClusterNotSupportError        = InvalidParameterCode.MakeSecondCode("ModifyJobLogLevelError", 15045, "Modify job log level error, cluster not support")

	InvalidParameter_ModifyClusterEsServerlessError = InvalidParameterCode.MakeSecondCode("ModifyClusterEsServerlessError", 15046, "Modify cluster EsServerless failed.")

	// InvalidParamter_InvalidIpList       = InvalidParameterCode.MakeSecondCode("InvalidIpList", 13013, "invalid parameter of IpList")
	// InvalidParameter_InvaildResourceIds = InvalidParameterCode.MakeSecondCode("InvaildResourceIds ", 13014, "invalid parameter of ResourceIds")
	// 空间
	InvalidParameter_InvalidItemSpaceName = InvalidParameterCode.MakeSecondCode("InvalidItemSpaceName", 15037, "Operate ItemSpace error because ItemSpaceName is illegal.")
	InvalidParameter_DuplicatedSpaceName  = InvalidParameterCode.MakeSecondCode("DuplicatedSpaceName", 15037, "Operate ItemSpace error because DuplicatedSpaceName.")

	InvalidParameter_ModifyClsTopicError = InvalidParameterCode.MakeSecondCode("ModifyClsTopicError", 15037, "Modify Cls topic failed.")

	InvalidParamter_InvalidSoftInfo             = InvalidParameterCode.MakeSecondCode("InvalidSoftInfo", 13028, "invalid parameter of SoftInfo")
	InvalidParamter_InvalidComponent            = InvalidParameterCode.MakeSecondCode("InvalidComponent", 13029, "invalid component impala/kylin/alluxio/zeppelin/livy in security cluster")
	InvalidParamter_InvalidIpList               = InvalidParameterCode.MakeSecondCode("InvalidIpList", 13030, "invalid parameter of IpList")
	InvalidParameter_InvaildResourceIds         = InvalidParameterCode.MakeSecondCode("InvalidResourceIds", 13031, "invalid parameter of ResourceIds")
	InvalidParameter_InvalidZone                = InvalidParameterCode.MakeSecondCode("InvalidZone", 13032, "invalid parameter value of Zone")
	InvalidParameter_AppIdResourceNotMatch      = InvalidParameterCode.MakeSecondCode("AppIdResourceNotMatch", 13033, "appId and resource not match")
	InvalidParameter_ProjectResourceNotMatch    = InvalidParameterCode.MakeSecondCode("ProjectResourceNotMatch", 13033, "project and resource not match")
	InvalidParameter_ZoneResourceNotMatch       = InvalidParameterCode.MakeSecondCode("ZoneResourceNotMatch", 13033, "zone and resource not match")
	InvalidParameter_PayModeResourceNotMatch    = InvalidParameterCode.MakeSecondCode("PayModeResourceNotMatch", 13033, "paymode and resource not match")
	InvalidParameter_DisplayStrategyNotMatch    = InvalidParameterCode.MakeSecondCode("DisplayStrategyNotMatch", 13034, "display Strategy not match")
	InvalidParameter_OrderFieldNotMatch         = InvalidParameterCode.MakeSecondCode("OrderFieldNotMatch", 13035, "order field not match")
	InvalidParameter_InvalidProductId           = InvalidParameterCode.MakeSecondCode("InvalidProductId", 13036, "value of param `ProductId` is invalid, product version is invalid")
	InvalidParameter_InvalidVpcId               = InvalidParameterCode.MakeSecondCode("InvalidVpcId", 13037, "value of param `VPCSettings.VpcId` is invalid")
	InvalidParameter_InvalidSubnetId            = InvalidParameterCode.MakeSecondCode("InvalidSubnetId", 13038, "value of param `VPCSettings.SubnetId` is invalid")
	InvalidParameter_InvalidSupportHA           = InvalidParameterCode.MakeSecondCode("InvalidSupportHA", 13039, "value of param `SupportHA` is invalid")
	InvalidParameter_InvalidInstanceName        = InvalidParameterCode.MakeSecondCode("InvalidInstanceName", 13040, "value of param `InstanceName` is invalid")
	InvalidParameter_InvalidPreExecutedFile     = InvalidParameterCode.MakeSecondCode("InvalidPreExecutedFile", 13041, "value of param PreExecutedFile is invalid")
	InvalidParameter_InvalidAutoRenew           = InvalidParameterCode.MakeSecondCode("InvalidAutoRenew", 13042, "value of param AutoRenew is invalid")
	InvalidParameter_InvalidLoginSetting        = InvalidParameterCode.MakeSecondCode("InvalidLoginSetting", 13043, "value of param LoginSetting is invalid")
	InvalidParameter_InvalidSecurityGroupId     = InvalidParameterCode.MakeSecondCode("InvalidSercurityGrpupId", 13044, "value of param SgId is invalid")
	InvalidParameter_InvalidSecretId            = InvalidParameterCode.MakeSecondCode("InvalidSecretId", 13045, "value of param SecretId is invalid")
	InvalidParameter_InvalidSecretKey           = InvalidParameterCode.MakeSecondCode("InvalidSecretKey", 13046, "value of param SecretKey is invalid")
	InvalidParameter_InvalidTagsGroup           = InvalidParameterCode.MakeSecondCode("InvalidTagsGroup", 13047, "value of param TagsGroup is invalid")
	InvalidParameter_InvalidServiceType         = InvalidParameterCode.MakeSecondCode("InvalidServiceType", 13048, "value of param ServiceType is invalid")
	InvalidParameter_InvalidProjectId           = InvalidParameterCode.MakeSecondCode("InvalidProjectId", 13449, "value of param ProjectId is invalid")
	InvalidParameter_InvalidSoftDeployInfo      = InvalidParameterCode.MakeSecondCode("InvalidSoftDeployInfo", 13450, "value of param SoftDeployInfo is invalid")
	InvalidParameter_InvalidServiceNodeInfo     = InvalidParameterCode.MakeSecondCode("InvalidServiceNodeInfo", 13451, "value of param ServiceNodeInfo is invalid")
	InvalidParameter_InvalidGroupName           = InvalidParameterCode.MakeSecondCode("InvaildGroupName", 13452, "value of param GroupName is invalid")
	InvalidParameter_InvalidConfGroupName       = InvalidParameterCode.MakeSecondCode("InvalidConfGroupName", 13452, "value of param ConfGroupName is invalid")
	InvalidParameter_InvalidCustomConf          = InvalidParameterCode.MakeSecondCode("InvalidCustomConf", 13453, "value of param Custom Conf is invalid")
	InvalidParameter_InvalidIpList              = InvalidParameterCode.MakeSecondCode("InvalidIpList", 13454, "value of param IpList is invalid")
	InvalidParameter_InvalidConfLogId           = InvalidParameterCode.MakeSecondCode("InvalidConfLogId", 13415, "the conf log can't support rollback")
	InvalidParameter_ConfGroupNameAlreadyExist  = InvalidParameterCode.MakeSecondCode("ConfGroupNameAlreadyExist", 13456, "Config Group already exist")
	InvalidParameter_InvalidStrategyType        = InvalidParameterCode.MakeSecondCode("InvalidStrategyType", 13457, "strategy must be 1 when use load based strategy,and be 2 when use time based strategy")
	InvalidParameter_InvalidStrategy            = InvalidParameterCode.MakeSecondCode("InvalidStrategy", 13458, "only set one of  load based and time based strategy at the same time")
	InvalidParameter_RepeatedStrategyName       = InvalidParameterCode.MakeSecondCode("RepeatedStrategyName", 13459, "name of strategy is repeated")
	InvalidParameter_InvalidScaleAction         = InvalidParameterCode.MakeSecondCode("InvalidScaleAction", 13460, "invalid scale action. must be scaleout or scalein")
	InvalidParameter_InvalidActionStatus        = InvalidParameterCode.MakeSecondCode("InvalidActionStatus", 13461, "invalid action status.")
	InvalidParameter_InvalidCompareMethod       = InvalidParameterCode.MakeSecondCode("InvalidCompareMethod", 13462, "invalid compare method. ")
	InvalidParameter_InvalidProcessMethod       = InvalidParameterCode.MakeSecondCode("InvalidProcessMethod", 13463, "invalid process method. must be max,min or avg ")
	InvalidParameter_InvalidStrategySpec        = InvalidParameterCode.MakeSecondCode("InvalidStrategySpec", 13464, "invalid spec bind to strategy ")
	InvalidParameter_InvalidStrategyModify      = InvalidParameterCode.MakeSecondCode("InvalidStrategy", 13465, "invalid strategy modify action")
	InvalidParameter_InvalidFilterKey           = InvalidParameterCode.MakeSecondCode("InvalidFilterKey", 13466, "invalid filter key")
	InvalidParameter_InvalidSecuritySupport     = InvalidParameterCode.MakeSecondCode("InvalidSecuritySupport", 13467, "this product not support Security Instance")
	InvalidParameter_InvalidConditionNum        = InvalidParameterCode.MakeSecondCode("InvalidConditionNum", 13468, "the num of condition must be 1 at lease")
	InvalidParameter_InvalidUpperLowerBound     = InvalidParameterCode.MakeSecondCode("InvalidUpperLowerBound", 13469, "upper bound must be greater than lower bound")
	InvalidParameter_ExistSameSpec              = InvalidParameterCode.MakeSecondCode("ExistSameSpec", 13470, "have a same spec now")
	InvalidParameter_InvalidSpecPriority        = InvalidParameterCode.MakeSecondCode("InvalidSpecPriority", 13471, "invalid priority")
	InvalidParameter_InvalidStrategyPriority    = InvalidParameterCode.MakeSecondCode("InvalidStrategyPriority", 13472, "invalid priority")
	InvalidParameter_InvalidEventType           = InvalidParameterCode.MakeSecondCode("InvalidEventType", 13473, "invalid event type")
	InvalidParameter_InvalidStrategyStatus      = InvalidParameterCode.MakeSecondCode("InvalidStrategyStatus", 13473, "invalid strategy status")
	InvalidParameter_InvalidMetaInstanceId      = InvalidParameterCode.MakeSecondCode("InvalidMetaInstanceId", 13474, "invalid MetaInstanceId")
	InvalidParameter_InvalidResourceType        = InvalidParameterCode.MakeSecondCode("InvalidResourceType", 13475, "invalid resource type")
	InvalidParameter_InvalidUnifyMeta           = InvalidParameterCode.MakeSecondCode("InvalidUnifyMeta", 13476, "value of UnifyMeta is invalid")
	InvalidParameter_InvalidMetaType            = InvalidParameterCode.MakeSecondCode("InvalidMetaType", 13477, "value of metaType is invalid")
	InvalidParameter_InvalidSoftWare            = InvalidParameterCode.MakeSecondCode("InvalidSoftWare", 13478, "value of software is invalid, software don't hava hive")
	InvalidParameter_InvalidBatchSize           = InvalidParameterCode.MakeSecondCode("InvalidBatchSize", 13479, "value of BatchSize is invalid")
	InvalidParameter_InvalidRestartPolicy       = InvalidParameterCode.MakeSecondCode("InvalidRestartPolicy", 13480, "value of RestartPolicy is invalid")
	InvalidParameter_InvalidDealOnFail          = InvalidParameterCode.MakeSecondCode("InvalidDealOnFail", 13481, "value of DealOnFail is invalid")
	InvalidParameter_InvalidRollingRestart      = InvalidParameterCode.MakeSecondCode("InvalidRollingRestart", 13482, "value of RollingRestart is invalid")
	InvalidParameter_InvalidRestartReasonLength = InvalidParameterCode.MakeSecondCode("InvalidRestartReasonLength", 13483, "Restart Reason too long")

	InvalidParameter_WoodZeroRestartNodes         = InvalidParameterCode.MakeSecondCode("WoodZeroRestartNodes", 13504, "Zero Restart Nodes, invalid")
	InvalidParameter_HttpMethodNotSupported       = InvalidParameterCode.MakeSecondCode("HttpMethodNotSupported", 13505, "http method not supported")
	InvalidParameter_CatalogNameAlreadyExist      = InvalidParameterCode.MakeSecondCode("CatalogNameAlreadyExist", 13506, "Catalog name already exist")
	InvalidParameter_InvalidCatalogName           = InvalidParameterCode.MakeSecondCode("InvalidCatalogName", 13507, "Catalog name already exist")
	InvalidParameter_InvalidCalcDate              = InvalidParameterCode.MakeSecondCode("InvalidCalcDate", 13508, "CalcDate invalid")
	InvalidParameter_CHDFSAccessGroupAlreadyExist = InvalidParameterCode.MakeSecondCode("CHDFSAccessGroupAlreadyExist", 13509, "Access Group already exist")
	InvalidParameter_InvalidTime                  = InvalidParameterCode.MakeSecondCode("InvalidTime", 13509, "time format invalid")
	InvalidParameter_AccessGroupAlreadyExist      = InvalidParameterCode.MakeSecondCode("AccessGroupAlreadyExist", 13509, "Access Group already exist")

	// ResourceNotFounCode从12xxx开始编码
	ResourceNotFoundCode_SpecNotFound                         = ResourceNotFoundCode.MakeSecondCode("SpecNotFound", 12001, "cvm spec not found")
	ResourceNotFoundCode_ClusterNotFound                      = ResourceNotFoundCode.MakeSecondCode("ClusterNotFound", 12002, "Instance not found")
	ResourceNotFoundCode_ServiceGroupNotFound                 = ResourceNotFoundCode.MakeSecondCode("ServiceGroupNotFound", 12003, "Service Group not found")
	ResourceNotFoundCode_OptionalSpecNotFound                 = ResourceNotFoundCode.MakeSecondCode("OptionalSpecFound", 12004, "the node have not optional spec")
	ResourceNotFoundCode_ServiceConfNotFound                  = ResourceNotFoundCode.MakeSecondCode("ServiceConfNotFound", 12005, "the service conf have not found")
	ResourceNotFoundCode_ServiceNodeNotFound                  = ResourceNotFoundCode.MakeSecondCode("ServiceNodeNotFound", 12006, "the service node have not found")
	ResourceNotFoundCode_ResourceNotFound                     = ResourceNotFoundCode.MakeSecondCode("ResourceNotFound", 12007, "the resource have not found")
	ResourceNotFoundCode_MetricsMetaNotFound                  = ResourceNotFoundCode.MakeSecondCode("ResourceNotFound", 12008, "the metrics meta have not found")
	ResourceNotFoundCode_SubnetNotFound                       = ResourceNotFoundCode.MakeSecondCode("SubnetNotFound", 12009, "the subnet or vpc not found")
	ResourceNotFoundCode_CvmInstanceNotFound                  = ResourceNotFoundCode.MakeSecondCode("CvmInstanceNotFound", 12010, "the Cvm instance not found")
	ResourceNotFoundCode_DiskNotFound                         = ResourceNotFoundCode.MakeSecondCode("DiskNotFound", 12011, "the disk not found")
	ResourceNotFoundCode_HardwareInfoNotFound                 = ResourceNotFoundCode.MakeSecondCode("HardwareInfoNotFound", 12012, "the hardware information not found")
	ResourceNotFoundCode_InstanceNotFound                     = ResourceNotFoundCode.MakeSecondCode("InstanceNotFound", 12013, "Instance not found")
	ResourceNotFoundCode_TagsNotFound                         = ResourceNotFoundCode.MakeSecondCode("TagsNotFound", 12014, "Tags not found")
	ResourceNotFoundCode_ConfGroupNotFound                    = ResourceNotFoundCode.MakeSecondCode("ConfGroupNotFound", 12015, "ConfGroup not found")
	ResourceNotFoundCode_ConfGroupNodeNotFound                = ResourceNotFoundCode.MakeSecondCode("ConfGroupNodeNotFound", 12016, "ConfGroup node not found")
	LimitExceededCode_ConfGroupNumLimitExceeded               = LimitExceededCode.MakeSecondCode("ConfGroupNumLimitExceeded", 19001, "ConfGroup number exceed the limit")
	LimitExceededCode_WorkSpaceLimitExceeded                  = LimitExceededCode.MakeSecondCode("WorkSpaceLimitExceeded", 19002, "ItemSpace number exceed the limit")
	LimitExceededCode_ClusterAssociatedWorkSpaceLimitExceeded = LimitExceededCode.MakeSecondCode("ClusterAssociatedWorkSpaceLimitExceeded", 19003, "ClusterAssociatedItemSpaceLimitExceeded number exceed the limit")
	ResourceNotFoundCode_FlowNotFound                         = ResourceNotFoundCode.MakeSecondCode("FlowNotFound", 12015, "flow not found")
	ResourceNotFoundCode_StrategyNotFound                     = ResourceNotFoundCode.MakeSecondCode("StrategyNotFound", 12014, "Strategy not found")
	ResourceNotFoundCode_AutoScaleSpecNotFound                = ResourceNotFoundCode.MakeSecondCode("AutoScaleSpecNotFound", 12015, "auto scale scale not found")
	ResourceNotFoundCode_AutoScaleNodesNotFound               = ResourceNotFoundCode.MakeSecondCode("AutoScaleNodesNotFound", 12016, "the num of autoscale nodes is 0")
	ResourceNotFoundCode_UserNotExist                         = ResourceNotFoundCode.MakeSecondCode("UserNotExist", 12016, "user not exist")
	ResourceNotFoundCode_CDBInfoNotFound                      = ResourceNotFoundCode.MakeSecondCode("CDBInfoNotFound", 12017, "the cdb info not found")
	ResourceNotFoundCode_TableNotExist                        = ResourceNotFoundCode.MakeSecondCode("TableNotExist", 12018, "table not exist")
	ResourceNotFoundCode_ConfigZeroLenth                      = ResourceNotFoundCode.MakeSecondCode("TagsNotFound", 12018, "Resource config array has zero length")
	ResourceNotFoundCode_JobserialIdNotFound                  = ResourceNotFoundCode.MakeSecondCode("TagsNotFound", 12019, "Job's actual serialId is NOT same with the request parameter")
	ResourceNotFoundCode_JobNotFound                          = ResourceNotFoundCode.MakeSecondCode("JobNotFound", 12020, "Job not found")
	ResourceNotFoundCode_VariableNotFound                     = ResourceNotFoundCode.MakeSecondCode("VariableNotFound ", 12021, "Variable not found")
	ResourceNotFoundCode_JobConfigNotFound                    = ResourceNotFoundCode.MakeSecondCode("JobConfigNotFound ", 12022, "JobConfig not found")
	ResourceNotFoundCode_ConnectorNotFound                    = ResourceNotFoundCode.MakeSecondCode("ConnectorNotFound ", 12022, "Connector not found")
	ResourceNotFoundCode_SessionCluster                       = ResourceNotFoundCode.MakeSecondCode("SessionClusterNotFound", 12023, "Session Cluster not found")
	ResourceNotFoundCode_ParseConnectorCluster                = ResourceNotFoundCode.MakeSecondCode("ParseConnectorClusterNotFound", 12024, "Parse Connector Cluster not found")

	ResourceInUseCode_InstanceInProcess              = ResourceInUseCode.MakeSecondCode("InstanceInProcess", 14001, "instance is in process")
	ResourceNotFoundCode_ExternalCatalogDataNotFound = ResourceNotFoundCode.MakeSecondCode("ExternalCatalogDataNotFound ", 12020, "External CatalogData Not Found ")
	ResourceInUseCode_ResourceConnectorDuplicateName = ResourceInUseCode.MakeSecondCode("ResourceConnectorDuplicateName", 14003, "resource connector duplicate name")

	UnsupportedOperationCode_RestartServiceUnsupported = UnsupportedOperationCode.MakeSecondCode("RestartServiceUnsupported", 16001, "service is not support restart")
	UnsupportedOperation_ScaleOutUnsupported           = UnauthorizedOperationCode.MakeSecondCode("ScaleOutUnSupported", 16002, "scale out is unsupported")
	UnsupportedOperation_UnauthenticatedUnsupport      = UnauthorizedOperationCode.MakeSecondCode("unautheticatedUnsupport", 16003, "your account is not authenticated, operation is unsupported")
	UnsupportedOperation_OnlyRollingRestartSupport     = UnsupportedOperationCode.MakeSecondCode("OnlyRollingRestartSupport", 16004, "only rolling restart is allowd for the component you selected")
	UnsupportedOperation_FlinkVersionCheckSqlGrammar   = UnauthorizedOperationCode.MakeSecondCode(
		"FlinkVersionCheckSqlGrammar", 16005, "Job flinkVersion not support")
	UnsupportedOperation_ExternalCatalogUnsupported      = UnsupportedOperationCode.MakeSecondCode("ExternalCatalogUnsupported", 16006, "Cluster do not support external catalog. ")
	UnsupportedOperation_MultiExternalCatalogUnsupported = UnsupportedOperationCode.MakeSecondCode("MultiExternalCatalogUnsupported", 16007, "Only one external catalog supported in a job.")
	UnsupportedOperation_DropOperationUnsupported        = UnsupportedOperationCode.MakeSecondCode("DropOperationUnsupported", 16008, "Not support drop operation in external catalog.")

	// 角色权限
	UnsupportedOperation_ModifyAdministerUnsupported  = UnauthorizedOperationCode.MakeSecondCode("ModifyAdministerUnsupported", 16006, "Administer account and cannot be modified")
	UnsupportedOperation_NoPermissionAccess           = UnsupportedOperationCode.MakeSecondCode("NoPermissionAccess", 16007, " No operation permission ")
	UnsupportedOperation_CanNotDelete                 = UnauthorizedOperationCode.MakeSecondCode("CanNotDelete", 16008, " Resources not cleaned up ")
	UnsupportedOperation_SqlGetStreamGraphUnsupported = UnsupportedOperationCode.MakeSecondCode(
		"SqlGetStreamGraphUnsupported", 16009, "Cluster do not support sql job get stream graph")
	UnsupportedOperation_SqlCheckCompatibilityUnsupported = UnsupportedOperationCode.MakeSecondCode(
		"SqlCheckCompatibilityUnsupported", 16010, "Cluster do not support sql job to check state compatibility")

	MissingParameter_MissingMasterResource = MissingParameterCode.MakeSecondCode("MissingMasterResource", 15001, "MasterResource is null or Missing MasterResource")
	MissingParameter_MissingCoreResource   = MissingParameterCode.MakeSecondCode("MissingCoreResource", 15002, "CoreResource is null or Missing CoreResource")

	ResourceSoldOut_CvmSoldOut = ResourceSoldOutCode.MakeSecondCode("CvmSoldOut", 24001, "Cvm is Sold out")
	ResourceSoldOut_CbsSoldOut = ResourceSoldOutCode.MakeSecondCode("CbsSoldOut", 24002, "Cbs is Sold out")

	ResourceInsufficient_InstanceInsufficient = ResourceInsufficientCode.MakeSecondCode("InstanceInsufficient", 21001, "Spec is Not Sufficient")
	ResourceInsufficient_DiskInsufficient     = ResourceInsufficientCode.MakeSecondCode("DiskInsufficient", 21002, "Disk is Not Sufficient")

	ResourceUnavailable_NodeUnavailable       = ResourceUnavailableCode.MakeSecondCode("NodeUnavailable", 20001, "node is unavailable")
	ResourceUnavailable_NotSupportResourcType = ResourceUnavailableCode.MakeSecondCode("NotSupportResourcType", 20002, "ResourceType not Support")

	// 规格管理
	ResourceSpec_NotExist                        = ResourceUnavailableCode.MakeSecondCode("ResourceSpec_NotExist", 20003, "node ResourceSpec is unavailable")
	ResourceSpec_NotDefaultSpec                  = ResourceUnavailableCode.MakeSecondCode("ResourceSpec_NotDefaultSpec", 20003, "The Resource not Exist Default ResourceSpec")
	ResourceSpec_NotSupportResourceType          = ResourceUnavailableCode.MakeSecondCode("NotSupportResourceType", 20004, "The ResourceType not Support")
	ResourceSpec_RepeatSpec                      = ResourceUnavailableCode.MakeSecondCode("RepeatSpec", 20005, "InstanceType(spec+cpu+mem) is repeat")
	ResourceUnavailable_NotSupportHardwareStatus = ResourceUnavailableCode.MakeSecondCode("NotSupportHardwareStatus", 20007, "hardware info statu not right.")

	OrderNumber_ExceedsMax = ResourceUnavailableCode.MakeSecondCode("OrderNum_ExceedsMax", 20006, "The Number of GoodDetail Orders Exceeds The Maximum(50)")

	// FailedOperationCode
	FailedOperationCode_SpecDeleteDenyForAutoScaleStrategies = FailedOperationCode.MakeSecondCode("SpecDeleteDenyForAutoScaleStrategies", 22001, "autoscale strategies bind to this node spec, please delete strategies first")
	FailedOperationCode_MoreSpecNotAllowed                   = FailedOperationCode.MakeSecondCode("MoreSpecNotAllowed", 22002, "the num of autoscale spec is max, please delete one before add new one")
	FailedOperationCode_MoreStrategyNotAllowed               = FailedOperationCode.MakeSecondCode("MoreStrategyNotAllowed", 22003, "the num of autoscale strategy is max, please delete one before add new one")
	FailedOperationCode_RefundCdbFailed                      = FailedOperationCode.MakeSecondCode("RefundCdbFailed", 22004, "Refund CDB failed")
	FailedOperationCode_RefundCvmFailed                      = FailedOperationCode.MakeSecondCode("RefundCvmFailed", 22005, "Refund CVM failed")
	FailedOperationCode_GetMonitorInfoFailed                 = FailedOperationCode.MakeSecondCode("GetMonitorInfoFailed", 22006, "Get monitor info failed")
	FailedOperationCode_DuplicateOrderNotAllowed             = FailedOperationCode.MakeSecondCode("DuplicateOrderNotAllowed", 22007, "you submitted a duplicate order, please check your console")
	FailedOperationCode_DataSourceConnectionFailed           = FailedOperationCode.MakeSecondCode("DataSourceConnectionFailed", 22008, "Failed to connect to data source")
	FailedOperationCode_ExecuteCommandTimeout                = FailedOperationCode.MakeSecondCode("ExecuteCommandTimeout", 22009, "Execution command timed out")
	FailedOperationCode_ParseSql                             = FailedOperationCode.MakeSecondCode("ParseSql", 22010, " Parse sql failed")
	FailedOperationCode_GrammarCheckFailure                  = FailedOperationCode.MakeSecondCode("GrammarCheckFailure", 22011, "Check sql failed.")
	FailedOperationCode_ModifySubnetFailure                  = FailedOperationCode.MakeSecondCode("ModifySubnetFailure", 22012, "ModifySubnet failed.")
	ConnectionErrorCode_DeleteFailed                         = FailedOperationCode.MakeSecondCode("DeleteFailed", 22013, "Delete connection failed.")
	ConnectionErrorCode_CreateFailed                         = FailedOperationCode.MakeSecondCode("CreateFailed", 22014, "Create connection failed ")
	ConnectionErrorCode_ModifyFailed                         = FailedOperationCode.MakeSecondCode("ModifyFailed", 22015, "Modify connection failed")
	ConnectionErrorCode_DescribeFailed                       = FailedOperationCode.MakeSecondCode("DescribeFailed", 22016, "Describe connection failed")
	FailedOperationCode_DeleteExternalCatalog                = FailedOperationCode.MakeSecondCode("DeleteExternalCatalog ", 22013, "Delete External Catalog failed.")
	FailedOperationCode_GetExternalCatalogService            = FailedOperationCode.MakeSecondCode("GetExternalCatalogService  ", 22014, "Get External Catalog Service failed.")
	FailedOperationCode_ModifyExternalCatalog                = FailedOperationCode.MakeSecondCode("ModifyExternalCatalog ", 22015, "Modify External Catalog failed.")
	FailedOperationCode_ConvertMetaDataToStruct              = FailedOperationCode.MakeSecondCode("ConvertMetaDataToStruct  ", 22016, "Convert Meta Data Bytes To Struct failed.")
	FailedOperationCode_ModifyMetaConnector                  = FailedOperationCode.MakeSecondCode("ModifyMetaConnector", 22017, " Can't modidy Connector")
	FailedOperationCode_ConnectExternalCatalog               = FailedOperationCode.MakeSecondCode("ConnectExternalCatalog", 22018, " Connect external catalog failed.")
	FailedOperationCode_RunSqlPreview                        = FailedOperationCode.MakeSecondCode("RunSqlPreview", 220198, "Run Sql Preview failed.")
	FailedOperationCode_FetchSqlPreview                      = FailedOperationCode.MakeSecondCode("FetchSqlPreview", 220199, "Fetch Sql Preview failed.")
	FailedOperationCode_StopSqlPreview                       = FailedOperationCode.MakeSecondCode("StopSqlPreview", 220200, "Stop Sql Preview failed.")
	FailedOperationCode_CheckSqlCompatibility                = FailedOperationCode.MakeSecondCode("CheckSqlCompatibility", 220203, "Check Sql Compatibility failed.")
	FailedOperationCode_RunSqlGateway                        = FailedOperationCode.MakeSecondCode("RunSqlGateway", 220201, "Run Sql Gateway failed.")
	FailedOperationCode_FetchSqlGatewayStatement             = FailedOperationCode.MakeSecondCode("FetchSqlGatewayStatement", 220202, "Fetch Sql Gateway Statement failed.")

	FlinkSqlErrorCode_DescribeFailed = FailedOperationCode.MakeSecondCode("DescribeFailed", 22017, "Generate flink sql failed")

	ModifyTableFailedOperation     = FailedOperationCode.MakeSecondCode("ModifyTableFailedOperation", 22019, "The tableName cannot be modified")
	ModifyConnectorFailedOperation = FailedOperationCode.MakeSecondCode("ModifyConnectorFailedOperation", 22020, "The connector type cannot be modified")
)
