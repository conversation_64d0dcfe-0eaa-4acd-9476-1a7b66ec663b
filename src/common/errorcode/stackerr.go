package errorcode

import (
	"bytes"
	"fmt"
	"github.com/juju/errors"
	"runtime"
)

const (
	max_depth_stack = 32
	user_main_func  = "main.main"
)

type Stacker interface {
	Stack() string
}

type Frame uintptr
type StackCodedError struct {
	CodedError
	frames []uintptr
}
type ErrLog struct {
	ErrMsg string
	Stack  string
}

func (scf StackCodedError) Error() string {
	codeErrMessage := scf.CodedError.Error()
	stackMessage := scf.Stack()
	errLog := &ErrLog{
		ErrMsg: codeErrMessage,
		Stack:  stackMessage,
	}
	return fmt.Sprintf("%+v", *errLog)
}

func NewStackError(code Code, message string, err error) StackCodedError {
	if err != nil {
		if stackCodedErr, ok := err.(StackCodedError); ok {
			return stackCodedErr
		}
		message = message + " err info:" + err.Error()
	}

	return StackCodedError{
		CodedError: CodedError{
			err:  errors.New(message),
			code: code,
		},
		frames: getFrame(),
	}
}

func getFrame() []uintptr {
	//runtime.Callers只填充len(pcs)个frame
	pcs := make([]uintptr, max_depth_stack)
	length := runtime.Callers(3, pcs)
	return pcs[0:length]
}

func (scf StackCodedError) Stack() string {
	var infoBuffer bytes.Buffer
	fmt.Fprint(&infoBuffer, "err stack:\n")
	for depth, frame := range scf.frames {
		f := runtime.FuncForPC(frame)
		file, line := f.FileLine(frame)
		fname := f.Name()
		fmt.Fprint(&infoBuffer, fmt.Sprintf("%s\n\t%s:%d\n ", fname, file, line))
		if fname == user_main_func {
			break
		}

		if depth > max_depth_stack { // 避免无限循环
			break
		}
	}
	return infoBuffer.String()
}
