package model

import (
	"strconv"
	"strings"
)

type Field struct {
	Name string `check:"nullable:false|strlen:(0,100)"`
	Type string `check:"nullable:false|enum: bigint, double, boolean, timestamp, string"`
}

type CdpJobProperties struct {
	Connector    string
	Parallelism  int
	Checkpoint   int
	TimeType     string
	SrcTableName string
	Sql          string
	CdpAppId     string
	CdpSecretId  string
	CdpSecretKey string
	CdpUrl       string

	CdpSourceSchema  []*Field
	CdpProjName      string
	CdpSrcTopicName  string
	CdpLimit         int
	CpdStartMode     string
	CdpDestTopicName string

	CpdWaterMarkType string
	CdpTimeFieldName string
	CdpFlagFieldName string
	CdpMaxTimeLag    int
}

const (
	CONNECOR_TYPE_KEY = "component.connector"
	PARALLELISM_KEY   = "component.parallelism"
	CHECK_POINT_KEY   = "component.checkpoint"
	TIME_TYPE_KEY     = "component.timeType"
	SRC_TABLE_KEY     = "component.cdp.srcTableName"
	SQL_KEY           = "component.sql"

	CDP_APPID_KEY     = "component.cdp.appid"
	CDP_SECRETID_KEY  = "component.cdp.secretId" // nolint
	CDP_SECRETKEY_KEY = "component.cdp.secretKey"
	CDP_URL_KEY       = "component.cdp.url"

	SRC_TABLE_SCHEMAL_KEY   = "component.cdp.sourceSchema"
	CDP_PRJ_NAME_KEY        = "component.cdp.projectName"
	CDP_SRC_TOPIC_KEY       = "component.cdp.srcTopicName"
	CDP_LIMIT_KEY           = "component.cdp.limit"
	CDP_START_MODE_KEY      = "component.cdp.startMode"
	CDP_DESC_TOPIC_NAME_KEY = "component.cdp.destTopicName"

	CDP_WATERMARK_TYPE_KEY  = "component.cdp.watermarkType"
	CDP_TIME_FIELD_NAME_KEY = "component.cdp.timeFieldName"
	CDP_FLAG_FIELD_NAME_KEY = "component.cdp.flagFieldName"
	CDP_MAX_TIME_LAG_KEY    = "component.cdp.maxTimeLag"

	CRLF = "\r\n"
)

func (me *CdpJobProperties) encodeSchema(schema []*Field) string {
	temp := make([]string, len(schema))
	index := 0
	for _, field := range schema {
		temp[index] = field.Name + ":" + field.Type
		index++
	}
	return strings.Join(temp, ",")
}

func (me *CdpJobProperties) ToString() string {

	ret := ""

	ret += CONNECOR_TYPE_KEY + "=" + me.Connector + CRLF
	ret += PARALLELISM_KEY + "=" + strconv.FormatInt(int64(me.Parallelism), 10) + CRLF
	ret += CHECK_POINT_KEY + "=" + strconv.FormatInt(int64(me.Checkpoint), 10) + CRLF
	ret += TIME_TYPE_KEY + "=" + me.TimeType + CRLF
	ret += SRC_TABLE_KEY + "=" + me.SrcTableName + CRLF
	ret += SQL_KEY + "=" + me.Sql + CRLF

	ret += CDP_APPID_KEY + "=" + me.CdpAppId + CRLF
	ret += CDP_SECRETID_KEY + "=" + me.CdpSecretId + CRLF
	ret += CDP_SECRETKEY_KEY + "=" + me.CdpSecretKey + CRLF
	ret += CDP_URL_KEY + "=" + me.CdpUrl + CRLF

	ret += SRC_TABLE_SCHEMAL_KEY + "=" + me.encodeSchema(me.CdpSourceSchema) + CRLF
	ret += CDP_PRJ_NAME_KEY + "=" + me.CdpProjName + CRLF
	ret += CDP_SRC_TOPIC_KEY + "=" + me.CdpSrcTopicName + CRLF
	ret += CDP_LIMIT_KEY + "=" + strconv.FormatInt(int64(me.CdpLimit), 10) + CRLF
	ret += CDP_START_MODE_KEY + "=" + me.CpdStartMode + CRLF
	ret += CDP_DESC_TOPIC_NAME_KEY + "=" + me.CdpDestTopicName + CRLF

	ret += CDP_WATERMARK_TYPE_KEY + "=" + me.CpdWaterMarkType + CRLF
	ret += CDP_TIME_FIELD_NAME_KEY + "=" + me.CdpTimeFieldName + CRLF
	ret += CDP_FLAG_FIELD_NAME_KEY + "=" + me.CdpFlagFieldName + CRLF
	ret += CDP_MAX_TIME_LAG_KEY + "=" + strconv.FormatInt(int64(me.CdpMaxTimeLag), 10) + CRLF

	return ret
}
