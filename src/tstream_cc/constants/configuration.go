package constants

const (
	CONF_KEY_PREPAID_PERIOD                 = "oceanus.cluster.prepaid.period"
	CONF_KEY_VPC_CIDR                       = "oceanus.cluster.vpc.cidr"
	CONF_KEY_SUBNET_CIDR                    = "oceanus.cluster.subnet.cidr"
	CONF_KEY_CDB_MEMORY_MB                  = "oceanus.cluster.cdb.memory.mb"
	CONF_KEY_CDB_VOLUME_GB                  = "oceanus.cluster.cdb.volume.gb"
	CONF_KEY_TKE_CIDR                       = "oceanus.cluster.tke.cidr"
	CONF_KEY_TKE_MAX_NODE_POD_NUM           = "oceanus.cluster.tke.max.node.pod.num"
	CONF_KEY_TKE_MAX_SERVICE_NUM            = "oceanus.cluster.tke.max.service.num"
	CONF_KEY_TKE_WORKER_NODE_DISKSIZE_GB    = "oceanus.cluster.tke.worker.node.disksize.gb"
	CONF_KEY_TKE_CLUSTER_OS                 = "oceanus.cluster.tke.cluster.os"
	CONF_KEY_TKE_CLUSTER_VERSION            = "oceanus.cluster.tke.cluster.version"
	CONF_KEY_TKE_CONTROLLER_SPEC            = "oceanus.cluster.tke.controller.spec"
	CONF_KEY_TKE_WORKER_SPEC                = "oceanus.cluster.tke.worker.spec"
	CONF_KEY_TKE_NGINX_INGRESS_CHART_PATH   = "oceanus.cluster.tke.nginx.ingress.chart.path"
	CONF_KEY_RESERVED_CU_PER_WORKER_NODE    = "oceanus.cluster.reserved.cu.per.worker.node"
	CONF_KEY_CONTROLLER_LOG_TOPIC_ID        = "oceanus.cluster.controller.log.topic.id"
	CONF_KEY_SECURITY_GROUP                 = "oceanus.cluster.security.group"
	CONF_KEY_TKE_REGISTRY_DOMAIN            = "tke.registry.domain"
	CONF_KEY_CLUSTER_AUTO_CREATE_BLACK_LIST = "oceanus.cluster.auto.create.region.black.list"
	CONF_KEY_CVM_QUOTA_MIN_LIMIT            = "oceanus.cvm.quota.min.limit"
	CONF_KEY_CHECK_TIME_CONFLICT_DEAD_LINE  = "deadLine"
	CLOSE_AUTO_SCALE_TIME_BASED             = "close_auto_scale_time_based"
	CONF_KEY_POOLED_CVM_LIMIT               = "oceanus.pooled.cvm.limit"

	ConfKeyEnableSimpleDefaultParallelism = "oceanus.simple-default-parallelism.enable"
)

const (
	CONF_RAINBOW_GROUP_TKE                                    = "Tke"
	CONF_RAINBOW_KEY_TKE_TILLER_SERVICE_ACCOUNT               = "sa-tiller.yaml"
	CONF_RAINBOW_KEY_TKE_TILLER_CLUSTER_ROLE_BINDING          = "clusterrolebindings-tiller-cluster-rule.yaml"
	CONF_RAINBOW_KEY_TKE_TILLER_DEPLOY                        = "deploy-tiller-deploy.yaml"
	CONF_RAINBOW_KEY_TKE_LOG_CONFIGS_CRD                      = "crd-logconfigs.cls.cloud.tencent.com.yaml"
	CONF_RAINBOW_KEY_TKE_CLS_PROVISIONER_SERVICE_ACCOUNT      = "sa-cls-provisioner.yaml"
	CONF_RAINBOW_KEY_TKE_CLS_PROVISIONER_CLUSTER_ROLE         = "clusterroles-cls-provisioner.yaml"
	CONF_RAINBOW_KEY_TKE_CLS_PROVISIONER_CLUSTER_ROLE_BINDING = "clusterrolebindings-cls-provisioner.yaml"
	CONF_RAINBOW_KEY_TKE_CLS_PROVISIONER_DEPLOY               = "deploy-cls-provisioner.template.yaml"
	CONF_RAINBOW_KEY_TKE_LOG_AGENT_SERVICE_ACCOUNT            = "sa-tke-log-agent.yaml"
	CONF_RAINBOW_KEY_TKE_LOG_AGENT_CLUSTER_ROLE               = "clusterroles-tke-log-agent.yaml"
	CONF_RAINBOW_KEY_TKE_LOG_AGENT_CLUSTER_ROLE_BINDING       = "clusterrolebindings-tke-log-agent.yaml"
	CONF_RAINBOW_KEY_TKE_LOG_AGENT_DAEMON_SET                 = "ds-tke-log-agent.template.yaml"
	CONF_RAINBOW_KEY_TKE_OCEANUS_NAMESPACE                    = "ns-oceanus.yaml"
	CONF_RAINBOW_KEY_TKE_OCEANUS_SERVICE_ACCOUNT              = "sa-oceanus.yaml"
	CONF_RAINBOW_KEY_TKE_OCEANUS_CLUSTER_ROLE_BINDING         = "clusterrolebindings-oceanus.yaml"
	CONF_RAINBOW_KEY_TKE_FLINK_SERVICE_ACCOUNT                = "sa-flink.yaml"
	CONF_RAINBOW_KEY_TKE_FLINK_ROLE_BINDING_DEFAULT           = "clusterrolebindings-flink-role-binding-default.yaml"
	CONF_RAINBOW_KEY_TKE_FLINK_ROLE_BINDING_FLINK             = "clusterrolebindings-flink-role-binding-flink.yaml"
	CONF_RAINBOW_KEY_TKE_INIT_USER_SCRIPT                     = "init-user.script.sh"
	CONF_RAINBOW_KEY_TKE_INIT_USER_SCRIPT_V2                  = "init-user.script.v2.sh"
	CONF_RAINBOW_KEY_CLUSTER_CLUSTERCONFIG                    = "cluster.clusterconfig.json"
	CONF_RAINBOW_KEY_NGINX_INGRESS_SECRETS                    = "secrets-basic-auth.template.yaml"
	CONF_RAINBOW_KEY_OCEANUS_FLINK_WEBUI_ING                  = "ing-oceanus-flink-webui.yaml"
	CONF_RAINBOW_KEY_OCEANUS_CONTROLLER_LC                    = "lc-oceanus-controller.template.yaml"
	CONF_RAINBOW_KEY_OCEANUS_RUNNINGLOG_LC                    = "lc-oceanus-runninglog.template.yaml"
	CONF_RAINBOW_KEY_OCEANUS_RUNNINGLOG_LC_V2                 = "lc-oceanus-runninglog.template.v2.yaml"
	CONF_RAINBOW_KEY_KUBECONFIG                               = "kubeconfig.template.yaml"
	CONF_RAINBOW_KEY_ZOOKEEPER                                = "deploy_zookeeper.yaml"
	CONF_RAINBOW_KEY_DIAGNOSIS_DATA_UPLOADER                  = "deploy-diagnosis-data-uploader.yaml"
)

const (
	CONF_RAINBOW_GROUP_ENI                        = "Eni"
	CONF_RAINBOW_KEY_TKE_CNI_AGENT_DAEMON_SET     = "ds-tke-cni-agent.template-v2.yaml"
	CONF_RAINBOW_KEY_TKE_CNI_AGENT_CONFIG_MAP     = "cm-tke-cni-agent-conf.template-v2.yaml"
	CONF_RAINBOW_KEY_IP_MASQ_AGENT_CONFIG_MAP     = "cm-ip-masq-agent-config.yaml"
	CONF_RAINBOW_KEY_TKE_CNI_SERVICE_ACCOUNT      = "sa-tke-cni.yaml"
	CONF_RAINBOW_KEY_TKE_CNI_CLUSTER_ROLE         = "clusterroles-tke-cni-clusterrole.yaml"
	CONF_RAINBOW_KEY_TKE_CNI_CLUSTER_ROLE_BINDING = "clusterrolebindings-tke-cni-clusterrole-binding.yaml"
	CONF_RAINBOW_KEY_RESTART_PODS                 = "restart-pods.json"
)

// 管控服务key
const (
	CONF_RAINBOW_KEY_STS_CLUSTER_ADMIN        = "sts-cluster-admin.template.yaml"
	CONF_RAINBOW_KEY_STS_SQL_SERVER           = "sts-sql-server.template.yaml"
	CONF_RAINBOW_KEY_STS_CLUSTER_MASTER       = "sts-cluster-master.template.yaml"
	CONF_RAINBOW_KEY_STS_TASKCENTER           = "sts-taskcenter.template.yaml"
	CONF_RAINBOW_KEY_STS_WATCHDOG             = "sts-watchdog.template.yaml"
	CONF_RAINBOW_KEY_STS_CREDENTIALS_PROVIDER = "sts-credentials-provider.template.yaml"
	CONF_RAINBOW_KEY_STS_METRIC_PROVIDER      = "sts-metric-provider.template.yaml"
	CONF_RAINBOW_KEY_STS_COMMAND_CONTROLLER   = "sts-command-controller.template.yaml"
	CONF_RAINBOW_KEY_TKE_COMMAND_CONFIG_CRD   = "crd-commands.oceanus.tencentcloud.com.yaml"
)

// 指标上报到监控中台，telegraf 的一些配置项
const (
	CONF_RAINBOW_KEY_TELEGRAF_TEMPLATE                      = "telegraf-conf-template"
	CONF_RAINBOW_KEY_TELEGRAF_INTERVAL                      = "telegraf_interval"
	CONF_RAINBOW_KEY_TELEGRAF_FLUSH_INTERVAL                = "telegraf_flush_interval"
	CONF_RAINBOW_KEY_TELEGRAF_LOGFILE_ROTATION_INTERVAL     = "telegraf_logfile_rotation_interval"
	CONF_RAINBOW_KEY_TELEGRAF_LOGFILE_ROTATION_MAX_SIZE     = "telegraf_logfile_rotation_max_size"
	CONF_RAINBOW_KEY_TELEGRAF_LOGFILE_ROTATION_MAX_ARCHIVES = "telegraf_logfile_rotation_max_archives"
	CONF_RAINBOW_KEY_TELEGRAF_NAMESPACE                     = "telegraf_namespace"
	CONF_RAINBOW_KEY_TELEGRAF_OUTPUT_TASK_NUM               = "telegraf_output_task_num"
	CONF_RAINBOW_KEY_TELEGRAF_OUTPUT_MAX_RPS                = "telegraf_output_max_rps"
	CONF_RAINBOW_KEY_TELEGRAF_LOG_SAMPLE_RATE               = "telegraf_log_sample_rate"
	CONF_RAINBOW_KEY_TELEGRAF_REGION_MAPPING                = "telegraf_region_mappings"
)

// metricProvider HPA 的配置项
const (
	CONF_RAINBOW_KEY_METRICPROVIDER_HPA_METRIC_NAMES = "hpa_metric_names"
	CONF_RAINBOW_KEY_METRICPROVIDER_HPA_MIN_REPLICAS = "hpa_min_replicas"
	CONF_RAINBOW_KEY_METRICPROVIDER_HPA_MAX_REPLICAS = "hpa_max_replicas"
)

// fine grained resource pod specs
const (
	ConfRainbowGroupCommon                    = "Common"
	ConfRainbowKeyFineGrainedResourcePodSpecs = "fine-grained-resource.pod.specs"
	ConfRainbowKeyNonStandardPodSpecs         = "non.standard.pod.specs"
	ConfRainbowKeyOceanusFullFeatures         = "oceanus.full-features"
)

const (
	ConfRainbowFlinkConfOverride = "flink-conf-override.v2.yaml"
)

const (
	ConfRainbowEksNormalCpuSpecifications = "eks_normal_cpu_specifications"
)
