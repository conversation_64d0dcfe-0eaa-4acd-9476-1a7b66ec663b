package constants

/**
 *
 * <AUTHOR>
 * @time 2019/4/26
 * @version 0.4.0
 * @since 0.4.0
 * @see
 */

const AP_SHANGHAI_ADC = "ap-shanghai-adc"
const (
	MonitorApiDomainInternal = "monitor.internal.tencentcloudapi.com"
	ClbApiDomainInternal     = "clb.internal.tencentcloudapi.com"
	EsApiDomainInternal      = "es.internal.tencentcloudapi.com"
	BillingApiDomainInternal = "billing.internal.tencentcloudapi.com"
)
const PublicType = 0  // cluster WebUIType 公网
const PrivateType = 1 // cluster WebUIType 内网
const (
	JOB_CONFIG_DRAFT_VERSION         = -1
	JOB_CONFIG_STATUS_UN_PUBLISHED   = 0
	JOB_CONFIG_STATUS_ON_PUBLISHED   = 1
	JOB_CONFIG_STATUS_PUBLISHED_SUCC = 2
	JOB_CONFIG_STATUS_PUBLISHED_FAIL = -1
	JOB_CONFIG_STATUS_DELETE         = -2
	JOB_CONFIG_LIMIT_PER_JOB         = 100
	AES_ENCRYPT_KEY                  = "Tencent2018$%^&*"

	MAX_PARALLELISM_KEY                        = "pipeline.max-parallelism"
	RESOURCE_ALLOCATION_STRATEGY               = "slotmanager.resource-allocation.strategy"
	MAX_PARALLELISM_DEFAULT_VALUE              = "2048"
	UPPER_BOUND_MAX_PARALLELISM                = 16384 // Flink 里的 32768 最大范围超出了 int64 的范围, 这里取第二大的值, 通常不可能达到
	DEFAULT_MAX_PARALLELISM_FOR_SHARED_CLUSTER = 128

	JOB_START_JOB_GRAPH = "job.start-job.graph" // 共享集群的 MaxParallelism

	CLUSTER_TYPE_EXCLUSIVE = "exclusive"
	CLUSTER_TYPE_SHARED    = "shared"

	// checkpoint 保存数量配置关键字
	CHECKPOINTS_NUM_RETAINED_KEY = "state.checkpoints.num-retained"
	// checkpoint 默认保存数量
	CHECKPOINTS_NUM_RETAINED_VALUE = 5

	// savepoint 保存数量配置关键字
	SAVEPOINTS_NUM_RETAINED_KEY = "state.savepoints.num-retained"
	// savepoint 默认保存数量
	SAVEPOINTS_NUM_RETAINED_VALUE = 30

	META_DATA_PATH = "/_metadata"

	JOB_INSTANCE_STATUS_CREATE    = 1
	JOB_INSTANCE_STATUS_RUNNING   = 2
	JOB_INSTANCE_STATUS_DEPLOYING = 3
	JOB_INSTANCE_STATUS_HISTORY   = -1

	INDEX_WITHOUT_JOB_CONFIGS_FOR_JOB    = -1
	VERSION_ID_WITHOUT_CONFIGS_FOR_JOB   = 0
	OK_OP_RESULT_FOR_JOB                 = ""
	RUN_JOB_PROGRESS_DESC                = "启动中/starting"
	RUN_WITH_SAVEPOINT_JOB_PROGRESS_DESC = "使用快照启动中/starting with savepoint"
	STOP_JOB_PROGRESS_DESC               = "停止中/stopping"
	PAUSE_JOB_PROGRESS_DESC              = "停止触发快照中/stopping with savepoint"
	RECOVER_JOB_PROGRESS_DESC            = "恢复中/recovering"

	DEFAULT_JM_RUNNING_CU = 1

	JOB_NAME_MAX_LENGTH = 100

	SERVICE_ACCOUNT_OCEANUS                        = "oceanus"
	SERVICE_ACCOUNT_SQLSERVER                      = "sqlserver"
	CONTAINERIZED_MASTER_ENV_HADOOP_USER_NAME      = "containerized.master.env.HADOOP_USER_NAME"
	CONTAINERIZED_TASKMANAGER_ENV_HADOOP_USER_NAME = "containerized.taskmanager.env.HADOOP_USER_NAME"
	MANAGE_TYPE_EXTERNAL                           = "external"
	MANAGE_TYPE_INTERNAL                           = "internal"
	RUN_MODE_WITH_START_JOB                        = 1
	RUN_MODE_NOT_START_JOB                         = 0
	CREATE_JOB_SCALE_RULE_KEY                      = "CreateJobScaleRule"
	BUILT_IN_CONNECTOR_KEY                         = "BuiltInConnector"
	JOB_MANAGER_CPU_KEY                            = "JobManagerCpu"
	JOB_MANAGER_MEM_KEY                            = "JobManagerMem"
	TASK_MANAGER_CPU_KEY                           = "TaskManagerCpu"
	TASK_MANAGER_MEM_KEY                           = "TaskManagerMem"
	WORK_SPACE_ID_KEY                              = "WorkSpaceId"
	PUBLIC_DEPENDENCY_KEY                          = "PublicDependency"
	SEMICOLON                                      = ";"
	COMMA                                          = ","
	SQL_FILE_PATH_KEY                              = "sqlFilePath"
	SQL_SUFFIX                                     = "sql"
	EXPERT_MODE_SUFFIX                             = "expert_mode_conf"
)

const (
	NETWORKALLOCATION      = "NetworkAllocation"
	RESOURCEALLOCATION     = "ResourceAllocation"
	SERVICEALLOCATION      = "ServiceAllocation"
	DEPLOYMENTVERIFICATION = "DeploymentVerification"

	CREATEVPC                   = "createVPC"
	APPLYCDB                    = "applyCDB"
	APPLYTKE                    = "applyTKE"
	INITENI                     = "initENI"
	INITCDB                     = "initCDB"
	INITTKECLUSTER              = "initTKECluster"
	DEPLOYCREDENTIALSPROVIDER   = "deployCredentialsProvider"
	DEPLOYWATCHDOG              = "deployWatchdog"
	DEPLOYTASKCENTER            = "deployTaskCenter"
	DEPLOYSQLSERVER             = "deploySqlServer"
	DEPLOYCOMMANDCONTROLLER     = "deployCommandController"
	DEPLOYDIAGNOSISDATAUPLOADER = "deployDiagnosisDataUploader"
	DEPLOYZOOKEEPER             = "deployZooKeeper"
	DEPLOYCLUSTERMASTER         = "deployClusterMaster"
	DEPLOYCLUSTERADMIN          = "deployClusterAdmin"
	DEPLOYMETRICPODANDSERVICE   = "deployMetricPodAndService"
	RUNTESTS                    = "runTests"
	ENDPOINT                    = "endPoint"
)

const (
	JOB_TYPE_SQL     = 1
	JOB_TYPE_JAR     = 2
	JOB_TYPE_ETL     = 3
	JOB_TYPE_PYFLINK = 4

	CLUSTER_GROUP_TYPE_UNDEFINED = 0
	CLUSTER_GROUP_TYPE_SHARED    = 1
	CLUSTER_GROUP_TYPE_PRIVATE   = 2
	CLUSTER_GROUP_TYPE_UNIFORM   = 3
	CLUSTER_GROUP_TYPE_SUB_EKS   = 4

	DOUBLE_BAR = "--"
)

const (
	JOB_STOP_TYPE_STOP      = 1
	JOB_STOP_TYPE_PAUSE     = 2
	JOB_STOP_TYPE_SUCCEEDED = 3 // Short-lived / batch jobs

	JOB_RUN_TYPE_RUN                        = 1
	JOB_RUN_TYPE_RESUME                     = 2
	JOB_RUN_TYPE_RESTART                    = 3
	JOB_RUN_TYPE_RESTART_WITH_SAVEPOINT     = 4
	JOB_RUN_TYPE_RUN_WITH_DEFAULT_SAVEPOINT = 5

	JOB_SCALE_RESTART_DIRECT         = 1
	JOB_SCALE_RESTART_WITH_SAVEPOINT = 2
)

const (
	CU_MEM_2GB  = 2
	CU_MEM_4GB  = 4
	CU_MEM_8GB  = 8
	CU_MEM_16GB = 16
)

const (
	CLUSTER_INFO_ORDERBY_CTREATETIME      = 0
	CLUSTER_INFO_ORDERBY_CTREATETIME_DESC = 1
	CLUSTER_INFO_ORDERBY_CTREATETIME_ASC  = 2
	CLUSTER_INFO_ORDERBY_STATUS           = 3
)

const (
	JOB_STATUS_CREATE      = 1  // 未初始化
	JOB_STATUS_INITIALIZED = 2  // 未发布
	JOB_STATUS_PROGRESS    = 3  // 操作中
	JOB_STATUS_RUNNING     = 4  // 运行中
	JOB_STATUS_STOPPED     = 5  // 停止
	JOB_STATUS_PAUSED      = 6  // 暂停
	JOB_STATUS_CONCERNING  = -1 // 故障
	JOB_STATUS_DELETE      = -2 // 删除		// FIXME 考虑计费的几种状态，调整值
	JOB_STATUS_FINISHED    = 7  // 完成 对应flink 的FINISHED 的状态， 批作业， 不包含 停止触发快照.

	//用于自动扩缩容的特殊状态，不会存储到数据库

)

// Session集群的状态
const (
	ClusterSessionStopped  = 1  // 停止
	ClusterSessionCreating = 2  //开启中
	ClusterSessionRunning  = 3  // 开启
	ClusterSessionFailed   = 4  //开启失败
	ClusterSessionStopping = 5  //停止中
	ClusterSessionDeleted  = -2 // 已删除(集群被销毁时更新为此状态)
)

// Emr集群的状态
const (
	ClusterHadoopYarnStopped  = 1  // 停止
	ClusterHadoopYarnCreating = 2  //开启中
	ClusterHadoopYarnRunning  = 3  // 开启
	ClusterHadoopYarnFailed   = 4  //开启失败
	ClusterHadoopYarnStopping = 5  //停止中
	ClusterHadoopYarnDeleted  = -2 // 已删除(集群被销毁时更新为此状态)
)

const (
	CLUSTER_GROUP_STATUS_CREATING           = 1  // 未初始化
	CLUSTER_GROUP_STATUS_RUNNING            = 2  // 运行中
	CLUSTER_GROUP_STATUS_INIT_PROGRESS      = 3  // 初始化中
	CLUSTER_GROUP_STATUS_SCALE_PROGRESS     = 4  // 扩容中
	CLUSTER_GROUP_STATUS_ISOLATED           = 5  // 隔离
	CLUSTER_GROUP_STATUS_SCALEDOWN_PROGRESS = 6  // 缩容中
	CLUSTER_GROUP_STATUS_UPGRADE            = 8  // 升级中
	CLUSTER_GROUP_STATUS_DELETED            = -2 // 已删除 or 已销毁
	CLUSTER_GROUP_STATUS_DELETING           = -1 // 删除中
	CLUSTER_GROUP_STATUS_RECOVERING         = 7  // 恢复中。 7.0迭代 隔离集群会回收cvm，续费的时候恢复cvm需要一点时间，需要一个中间状态。
)

const (
	SETATS_STOPPED                = 1  // 停止
	SETATS_RUNNING                = 2  // 运行中
	SETATS_INIT_PROGRESS          = 3  // 初始化中
	SETATS_SCALE_PROGRESS         = 4  // 扩容中
	SETATS_WAREHOOUSE_NOT_INIT    = 5  // Warehoouse未配置
	SETATS_WAREHOOUSE_NOT_INITING = 6  // Warehoouse配置中
	SETATS_RESTARTING             = 7  // 重启中
	SETATS_DELETED                = -2 // 已删除(集群被销毁时更新为此状态)
	SETATS_DELETING               = -1 // 删除中
	SETATS_ISOLATED               = 8  // 隔离
	SETATS_RECOVERING             = 9  // 恢复中
)

const (
	SetatsManager          = "setats-manager"
	SetatsWorker           = "setats-worker"
	SetatsManagerPort      = 6072
	SetatsEngineJobIdSplit = "__"
	EngineJobStateKilled   = -3 // kill结果：-3：已终止；  -4： kill中；
	EngineJobStateKilling  = -4 // kill结果：-3：已终止；  -4： kill中；
	BatchTaskScheduleType  = 1
	BatchTaskRunType       = 2
	IgnoreError            = "Expecting token to be 2 or 1, but found 0"
	EngineTypeSetats       = "setats"
	// engine.type: setats
	Engine_Type_Key     = "engine.type"
	Engine_Id_Key       = "engine.id"
	BuildInHiveCatalog  = 1
	SETATS_LOCATION_COS = "cos"
)

const (
	BILLINGRESOURCE_STATUS_ACTIVE   = 1
	BILLINGRESOURCE_STATUS_INACTIVE = 0

	BILLINGRESOURCE_TYPE_CLUSTER = 1
	BILLINGRESOURCE_TYPE_SETATS  = 2
)

const (
	ZK_TYPE_NO_AUTH = 0 // 0 no auth, 1 Digest
	ZK_TYPE_DIGEST  = 1 // 0 no auth, 1 Digest
)

const (
	CVM_POOL_STATUS_VALID   = 1
	CVM_POOL_STATUS_PENDING = 2
	CVM_POOL_STATUS_HISTORY = -1
)

/**
 * 定义返回给前端的带状态(可恢复的)事件类型
 */
//goland:noinspection GoSnakeCaseUsage
const (
	BARAD_EVENT_ALERT_NAME_JOB_FAIL        = "oceanus_job_fail"
	BARAD_EVENT_ALERT_NAME_CHECKPOINT_FAIL = "oceanus_checkpoint_fail"
	BARAD_EVENT_ALERT_NAME_JOB_STOPPED     = "oceanus_job_stopped"
	BARAD_EVENT_ALERT_NAME_JOB_STARTED     = "oceanus_job_started"

	BARAD_EVENT_ALERT_JOB_SCHEDULE_FAIL = "oceanus_job_schedule_fail"
	BARAD_EVENT_ALERT_JOB_SCALE_FAIL    = "oceanus_job_scale_fail"
	BARAD_EVENT_ALERT_JOB_SCALE_TIMEOUT = "oceanus_job_scale_timeout"

	// 数据库 EventAlert 表中的 Type 定义
	EVENT_ENTITY_TYPE_JOB_START_OR_STOP          = "1"
	EVENT_ENTITY_TYPE_JOB_FAIL_OR_RECOVER        = "2"
	EVENT_ENTITY_TYPE_CHECKPOINT_FAIL_OR_RECOVER = "3"
	EVENT_ENTITY_TYPE_JOB_EXCEPTIONS             = "4"
	// type为5的事件目前作为自定义事件提供给inlong，oceanus暂时不做任何处理，此处只保留这个类型
	EVENT_ENTITY_TYPE_CUSTOM_EVENT = "5"

	// // job schedul failed
	// EVENT_ENTITY_TYPE_JOB_SCHEDUL_FAILED = "5"

	// 向前端 API 返回的 Type 定义
	EVENT_TYPE_JOB_STOP                = "10"
	EVENT_TYPE_JOB_START               = "11"
	EVENT_TYPE_JOB_FAIL_RECOVER        = "20"
	EVENT_TYPE_JOB_FAIL_TRIGGER        = "21"
	EVENT_TYPE_CHECKPOINT_FAIL_RECOVER = "30"
	EVENT_TYPE_CHECKPOINT_FAIL_TRIGGER = "31"
)

const (
	PEER_VPC_STATUS_OK     = 1
	PEER_VPC_STATUS_DELETE = -1
)

const (
	RESOURCE_STORAGETYPE_COS   = "COS"
	RESOURCE_STORAGETYPE_LOCAL = "LOCAL"
)

const (
	BATCH_EXECUTION_MODE     = "BATCH"
	STREAMING_EXECUTION_MODE = "STREAMING"
)

const (
	RESOURCE_STATUS_DELETE          = -2
	RESOURCE_STATUS_ACTIVE          = 1
	RESOURCE_TYPE_JAR               = 1 //jar包
	RESOURCE_TYPE_DEPENDENCY        = 2 //配置
	RESOURCE_TYPE_PYTHON            = 3
	RESOURCE_CONF_STATUS_INITIALIZE = 0
	RESOURCE_CONF_STATUS_ACTIVE     = 1
	RESOURCE_CONF_STATUS_DELETE     = -2
	RESOURCE_REF_STATUS_ACTIVE      = 1

	RESOURCE_REF_USAGE_TYPE_MAIN             = 1
	RESOURCE_REF_USAGE_TYPE_DEPENDENCY       = 2
	RESOURCE_REF_USAGE_TYPE_PYTHON_FILE      = 3
	RESOURCE_REF_USAGE_TYPE_PYTHON_DATA_FILE = 4
	RESOURCE_REF_USAGE_TYPE_DEFALUT          = -1
	RESOURCE_REF_USAGE_TYPE_DEPENDENCY_JAR   = 0
	RESOURCE_REF_USAGE_TYPE_DEPENDENCY_SQL   = 5

	RESOURCE_REF_STATUS_DELETE         = -2
	RESOURCE_REF_VERSION_ID_USE_LATEST = -1

	RESOURCE_DEPENDENCY_PATH_CONFIG_GROUP = "Common"
	RESOURCE_DEPENDENCY_PATH_CONFIG_KEY   = "DispatchPath"
	RESOURCE_DEPENDENCY_DEFAULT_PATH      = "/var/flink-data/user-dependency"
)

var ResourceRefUsageTypeSet = map[int]struct{}{
	RESOURCE_REF_USAGE_TYPE_MAIN:             {},
	RESOURCE_REF_USAGE_TYPE_DEPENDENCY:       {},
	RESOURCE_REF_USAGE_TYPE_PYTHON_FILE:      {},
	RESOURCE_REF_USAGE_TYPE_PYTHON_DATA_FILE: {},
}

const (
	DEBUG_JOB_STATUS_CREATE          = 1  // 未初始化
	DEBUG_JOB_STATUS_INITIALIZED     = 2  // 初始化
	DEBUG_JOB_STATUS_SUCCESS         = 3  // 成功
	DEBUG_JOB_STATUS_FAILED          = -1 // 故障
	DEBUG_JOB_STATUS_DELETE          = -2 // 删除
	BUKCET_TYPE_STATUSDATA_EXCLUSIVE = 2  // 独占集群Flink存储状态
	BUKCET_TYPE_STATUSDATA_SHARE     = 1  // 共享集群Flink存储状态
	BUKCET_TYPE_RESOURCEDATA         = 3  // 资源数据状态存储
	BUKCET_TYPE_CLUSTERDATA          = 4  // 集群日志状态存储
	BUCKET_STATUS_DELETE             = -2
	BUCKET_WEIGTH_DEFAULT            = 1
	BUCKET_USE_HDFS_DEFAULT          = -1 // -1是不使用 1是使用
	BUCKET_USE_HDFS                  = 1  // -1是不使用 1是使用
	BUCKET_STATUS_ACTIVE             = 1
	BUCKET_REF_STATUS_ACTIVE         = 1
	BUCKET_REF_STATUS_DELETE         = -2

	BUCKET_DEFAULT = "ap-guangzhou"
)

const (
	USER_UIN_MAP_ACTIVE = 1
	USER_UIN_MAP_DELETE = -2
)

const (
	STORAGE_TYPE_COS = 1
)

const (
	CLUSTER_ROLE_TYPE_ACTIVE          = 1  // 活跃Cluster
	CLUSTER_ROLE_TYPE_STANDBY         = 2  // 待命Cluster
	CLUSTER_ROLE_TYPE_DECOMMISSIONING = -1 // 准备销毁Cluster
	CLUSTER_ROLE_TYPE_DECOMMISSIONED  = -2 // 删除Cluster
)

const (
	CLUSTER_SCHEDULER_TYPE_EMR = 1
	CLUSTER_SCHEDULER_TYPE_TKE = 2 // TKE
)

// 用于CA通过这个字段获取不同的 ClusterClient，代码在  common 的 ClusterClientFactory
const (
	SCHEDULER_TYPE_YARN = 1
	SCHEDULER_TYPE_K8S  = 2
)

const (
	FLINK_JOB_TYPE_YARN = 1
)

const (
	CLUSTER_NET_CONNECTION_TYPE_ENI   = 0 // 弹性网卡方案
	CLUSTER_NET_CONNECTION_TYPE_INNER = 2 // 内网非弹性网卡方案
)

const (
	CLUSTER_NET_ENI_POD  = 0 // 弹性网卡方案，POD绑定弹性网卡
	CLUSTER_NET_ENI_NODE = 1 // 弹性网卡方案，NODE绑定弹性网卡
)

const (
	CLUSTER_NODE_ENI_ETH1 = 0 // Oceanus自动创建管控,默认走ETH1方案
	CLUSTER_NODE_ENI_ETH0 = 1 // Oceanus自动创建管控,默认走ETH0方案
	CLUSTER_NODE_ENI_TKE  = 2 // TKE组件管理(旧方案，需过滤掉)
)

const (
	CLUSTER_MASTER_COMMAND_CREATE = 1 // "create"
	CLUSTER_MASTER_COMMAND_FETCH  = 2 // "fetch"
	CLUSTER_MASTER_COMMAND_ACK    = 3 // "ack"
	CLUSTER_MASTER_COMMAND_FINISH = 4 // "finish"
)

const (
	CLUSTER_MASTER_COMMAND_ACTION_RUN_JOB                     = 1 // "RunJob"
	CLUSTER_MASTER_COMMAND_ACTION_CANCEL_JOB                  = 2 // "CancelJob"
	CLUSTER_MASTER_COMMAND_ACTION_RESTART_JOB                 = 3 // "RestartJob"
	CLUSTER_MASTER_COMMAND_ACTION_CANCEL_JOB_WITH_SAVEPOINT   = 4 // "Cancel Job with Savepoint"
	CLUSTER_MASTER_COMMAND_ACTION_QUERY_AND_UPDATE_JOB_STATUS = 5 // "Query and update real job status after RunJob / CancelJob, etc. fails"
	WATCHDOG_COMMAND_ACTION_QUERY_JOB                         = 6 // "QueryJob"
)

const (
	SAVEPOINT_STATUS_HISTORY     = -1
	SAVEPOINT_STATUS_ACTIVE      = 1
	SAVEPOINT_STATUS_EXPIRED     = 2
	SAVEPOINT_STATUS_IN_PROGRESS = 3
	SAVEPOINT_STATUS_FAILED      = 4
	SAVEPOINT_STATUS_TIMEOUT     = 5

	RECORD_TYPE_SAVEPOINT             = 1
	RECORD_TYPE_CHECKPOINT            = 2
	RECORD_TYPE_CANCEL_WITH_SAVEPOINT = 3

	SAVEPOINT_DEFAULT_TIMEOUT = 1200

	SAVEPOINT_PATH_STATUS_AVAILABLE   = 1
	SAVEPOINT_PATH_STATUS_UNAVAILABLE = 2
)

const (
	START_MODE_LATEST = "LATEST"
)

const (
	SUCCESS   int64 = 0
	PARAMSERR int64 = -1
	SYSERR    int64 = -2

	OK   = "OK"
	NULL = ""
)

const (
	InnerUserTransition_STATUS_ACTIVE   = 1
	InnerUserTransition_STATUS_INACTIVE = -1
)

const (
	REGION_STATUS_ACTIVE   = 1
	REGION_STATUS_INACTIVE = 0
)

const (
	ZoneStateActive   = 1
	ZoneStateInactive = 0
)

const (
	CvmSaleConfActive   = 1
	CvmSaleConfInactive = 0
)

const (
	CvmGenerationKvm10  = 0
	CvmGenerationKvm30  = 3
	CvmGenerationKvm110 = 1
)

const (
	DESCRIBE_BATCH_SIZE_UPPER_LIMIT = 20

	OPERATION_BATCH_SIZE_UPPER_LIMIT = 100
)

const (
	EXECUTION_CHECKPOINTING_INTERVAL = "execution.checkpointing.interval"

	CHECKPOINT_INTERVAL_LOWER_LIMIT = 10

	CHECKPOINT_RETENTION_KEY        = "execution.checkpointing.externalized-checkpoint-retention"
	CHECKPOINT_RETENTION_ON_SUCCESS = "RETAIN_ON_SUCCESS"

	CHECKPOINT_RETENTION_NUM = "state.checkpoints.num-retained"

	WHITE_LIST_CHECKPOINT_INTERVAL_LOWER_LIMIT = 1

	CHECKPOINT_TIMEOUT = "execution.checkpointing.timeout"

	JOB_TOTAL_COUNT_UPPER_LIMIT = 1000

	RESOURCE_CONFIG_LIMIT_PER_RESOURCE = 200
	RESOURCE_TOTAL_COUNT_UPPER_LIMIT   = 200
)

const (
	NETWORK_ENV_CLOUD_VPC = 1
	NETWORK_ENV_INNER_VPC = 3

	RESOURCE_TYPE_PRIVATE = 0
	RESOURCE_TYPE_SHARE   = 1
)

const (
	DEFAULT_YUNTI_API_URL    = "http://api.yunti.oa.com"
	YUNTI_API_URL            = "YuntiApiUrl"
	YUNTI_API_KEY_NAME       = "YuntiApiKeyName"
	YUNTI_API_KEY_SECRET     = "YuntiApiKeySecret"
	HTTP_REQUEST_TIMEOUT     = 20
	HTTP_PROFILE_REQ_TIMEOUT = 20
)

const (
	XING_YUN_API_URL_INIT_CVM_Password = "XingYunInitCvmPasswordApiUrl"
	XING_YUN_API_URL_GET_CVM_Passowrd  = "XingYunGetCvmPasswordApiUrl"
)

const (
	BARAD_EVENT_ALERT_CALLER              = "SCS"
	BARAD_EVENT_ALERT_CALLEE              = "Barad"
	BARAD_EVENT_ALERT_VERSION             = 1
	BARAD_EVENT_ALERT_PRODUCT_NAME        = "oceanus"
	BARAD_EVENT_ALERT_ON                  = 1
	BARAD_EVENT_ALERT_OFF                 = 0
	FLINK_CHECKPOINT_DIR_V2               = "state.checkpoints.dir"
	FLINK_CHECKPOINT_DIR_V1               = "state.backend.fs.checkpointdir"
	FLINK_SAVEPOINT_DIR                   = "state.savepoints.dir"
	FLINK_HIGH_AVAILABILITY_STORAGE_DIR   = "high-availability.storageDir"
	FLINK_K8S_CLUSTER_ID                  = "kubernetes.cluster-id"
	FLINK_K8S_JM_LABELS                   = "kubernetes.jobmanager.labels"
	FLINK_K8S_TM_LABELS                   = "kubernetes.taskmanager.labels"
	FLINK_K8S_REST_SERVICE_ANNOTATIONS    = "kubernetes.rest-service.annotations"
	FLINK_K8S_CONTAINER_IMAGE             = "kubernetes.container.image"
	FLINK_K8S_CONTAINER_IMAGE_PULL_POLICY = "kubernetes.container.image.pull-policy"
	DEBUG_SERVICE_K8S_CONTAINER_IMAGE     = "oceanus.debug.kubernetes.container.image"
	FLINK_K8S_NAMESPACE                   = "kubernetes.namespace"
	FLINK_K8S_INIT_CORE_ENABLE            = "kubernetes.internal.enable.init.container.bind.core"
	FLINK_K8S_INIT_RESOURCE_ENABLE        = "kubernetes.internal.enable.init.container.resource"

	FLINK_K8S_DNS_POLICY_NAMESERVERS = "kubernetes.dnsPolicy.nameservers" // "None" 共享集群每个集群走自己的coredns
	FLINK_K8S_ZOOKEEPER_QUORUM       = "high-availability.zookeeper.quorum"
)

const (
	CONFIGURATION_TYPE_BASIC        = 0
	CONFIGURATION_TYPE_FLINK_COMMON = 1
	CONFIGURATION_TYPE_FLINK_YARN   = 2
	CONFIGURATION_TYPE_FLINK_TKE    = 3
)

const (
	SKIP_ROLE_CHECKLIST        = "skip.role.checklist"
	APPID_WHITELIST_REGION_NEV = "appId.whiteList.regionEnv"
	YEHE_API_URL               = "yehe.api.url"
	DEFAULT_YEHE_API_URL       = "http://*************:50032" // 如有问题可以联系 vedasun
	YEHE_WHITELIST_INTERFACE   = "qcloud.whiteList.getWhiteList"

	OCEANUS_REGION_WHITELIST_EC     = "oceanus_region_whitelist_ec"
	OCEANUS_REGION_SHITELIST_INLAND = "oceanus_region_whitelist_inland"
	OCEANUS_REGION_WHITELIST_ABROAD = "oceanus_region_whitelist_abroad"
)

const (
	GUANGZHOU = "ap-guangzhou"
	BEIJING   = "ap-beijing"
	SHANGHAI  = "ap-shanghai"
	CHENGDU   = "ap-chengdu"
	CHONGQING = "ap-chongqing"

	WUHAN        = "ap-wuhan-ec"
	CHANGSHA     = "ap-changsha-ec"
	FUZHOU       = "ap-fuzhou-ec"
	SHENZHEN     = "ap-shenzhen"
	SHIJIAZHUANG = "ap-shijiazhuang-ec"
	TAIPEI       = "ap-taipei"
	HONGKONG     = "ap-hongkong"
)

const (
	GRANT_OK              = 1
	GRANT_NO_OCEANUS_ROLE = 2
	GRANT_NO_PASSROLE     = 3
)

const (
	SQL_TEMPLATE_ACTIVE = 1
	SQL_TEMPLATE_DELETD = -2
)

const (
	SYSTEM_PROVIDED = 1
	USER_PROVIDED   = 0
	USER_CONNECTOR  = 2
	CONNECTOR       = 3 //不做保存，仅用于查询传递判断，代表in(1,2)
)

const (
	RESOURCE_GROUP_ENABLED = 1
)

const (
	DEBUG_JOB_IS_SHARE_CLUSTER  = 1
	DEBUG_JOB_NOT_SHARE_CLUSTER = 0
)

const (
	K8S_CLUSTER_TYPE_TKE = 0
	K8S_CLUSTER_TYPE_EKS = 1
)

const (
	K8S_CLUSTER_WITHOUT_WORKER = 0 // 未添加
	K8S_CLUSTER_WITH_WORKER    = 1 // 已添加
)

// JobConfig表中LogCollect字段 可能对应：0（不采集）、1（采集到CLS）、4（采集到COS）
// 其他情况为兼容历史集群，作业配置返回参数
const (
	JobLogCollectDisabled        = 0
	JobLogCollectEnabled         = 1
	JobLogCollectHistoryDisabled = 2
	JobLogCollectHistoryEnabled  = 3
	JobLogCollectEnabledOnCos    = 4 // 日志采集到COS，
	JobLogCollectEnabledOnES     = 5

	JobLogCollectOnClsKey = "cls"
	JobLogCollectOnCosKey = "cos"
	JobLogCollectOnEsKey  = "es"
)

const (
	JobLogCollectTypeCLS = 2 // 作业、草稿配置请求参数 采集日志方式：采集到CLS
	JobLogCollectTypeCOS = 3 // 作业、草稿配置请求参数 采集日志方式：采集到COS
	JobLogCollectTypeES  = 4 // 作业、草稿配置请求参数 采集日志方式：采集到ES
)

const (
	ORDER_ORIGIN_CONSOLE = 0
	ORDER_ORIGIN_DIANSHI = 1
)

const (
	BuiltInMetricsReporters                    = "barad,oceanus"
	BuiltInMetricsReportersWithoutDetailMetric = "barad"
)

const (
	ETL_JOB_SCHEME_COLUMN_COLUMNTYPE_CONSTANT = "constant"
	ETL_JOB_SCHEME_COLUMN_COLUMNTYPE_VARIABLE = "variable"
)

const (
	ComponentMetricProvider          = "metric-provider"
	MetricProviderPort               = 8421
	MetricProviderTelegrafConfigName = "telegraf-config"
	RainbowGroupMetricProvider       = "MetricProvider"
)

const (
	DESCRIBE_FEATURE_SIZE_UPPER_LIMIT = 100
)

const (
	GALILEO_CONF_FILE = "galileo_cc.properties"
)

const (
	ETL_COMMAND_ACTION_DESCRIBE_TABLE_SCHEMA        = 1
	ETL_COMMAND_ACTION_DESCRIBE_TABLE_LIST          = 2
	ETL_COMMAND_ACTION_VALIDATE_CONNECTOR           = 3
	ETL_COMMAND_ACTION_DESCRIBE_CONNECTION_SUBITEMS = 4
	ETL_COMMAND_ACTION_CHECK_CONNECTIVITY           = 5
	/** 新增etl 作业默认值   ts210712 by andylhuang**/
	ETL_METRICS_LATENCY_INTERVAL_NAME           = "metrics.latency.interval"
	ETL_METRICS_LATENCY_INTERVAL_VALUE          = "20000"
	ETL_PIPELINE_OPERATOR_CHAINING_NAME         = "pipeline.operator-chaining"
	ETL_PIPELINE_OPERATOR_CHAINING_VALUE        = "false"
	ETL_PIPELINE_OPERATOR_CHAINING_ENABLE_VALUE = "true"
	ETL_SYNC_TYPE_TABLE                         = 1
	ETL_SYNC_TYPE_DATABASE                      = 2
	ETL_SYNC_TYPE_MULTI_TABLES                  = 3
)

const (
	SYSTEM_VARIABLE_JOB_SERIAL_ID     = "SYSTEM_VARIABLE_JOB_SERIAL_ID"
	SYSTEM_VARIABLE_CLUSTER_SERIAL_ID = "SYSTEM_VARIABLE_CLUSTER_SERIAL_ID"

	METADATA_TABLE_RESOURCE_REF_NOT_REFERENCE_RESOURCE        = -1 // AUTO REF
	METADATA_TABLE_RESOURCE_REF_NO_NEED_REFERENCE_VERSION     = -2 // datagen
	METADATA_TABLE_RESOURCE_REF_NO_NEED_REFERENCE_RESOURCE_ID = -2
	METADATA_TABLE_RESOURCE_REFERENCE_LATEST_VERSION          = -1
	METADATA_TABLE_RESOURCE_TYPE_SYSTEM_RESOURCE              = 1
	METADATA_TABLE_RESOURCE_TYPE_USER_RESOURCE                = 2

	FLINK_VERSION_1_10                                               = "Flink-1.10"
	FLINK_VERSION_1_11                                               = "Flink-1.11"
	FLINK_VERSION_1_13                                               = "Flink-1.13"
	FLINK_VERSION_1_14                                               = "Flink-1.14"
	FLINK_VERSION_1_16                                               = "Flink-1.16"
	FLINK_VERSION_1_17                                               = "Flink-1.17"
	FLINK_VERSION_1_18                                               = "Flink-1.18"
	FLINK_VERSION_1_20                                               = "Flink-1.20"
	METADATA_TABLE_META_CATALOG_RESOURCE_REF_RESOURCE_TYPE_COMMON    = 1
	METADATA_TABLE_META_CATALOG_RESOURCE_REF_RESOURCE_TYPE_EXCLUSIVE = 2
	METADATA_TABLE_META_CATALOG_RESOURCE_REF_RESOURCE_STATUS_ACTIVE  = 1
	METADATA_TABLE_META_CATALOG_RESOURCE_REF_RESOURCE_STATUS_DELETE  = -2
	METADATA_TABLE_META_CATALOG_JOBCONFIG_REF_NOT                    = -1
	METADATA_CATALOG_TYPE_OCEANUS                                    = 0
	METADATA_CATALOG_TYPE_OCEANUS_NAME                               = "oceanus"
	METADATA_CATALOG_TYPE_HIVE                                       = 1
	METADATA_CATALOG_TYPE_MYSQL                                      = 2
	METADATA_CATALOG_TYPE_HIVE_NAME                                  = "hive"
	METADATA_CATALOG_STATUS_ACTIVE                                   = 1
	METADATA_CATALOG_STATUS_DELETE                                   = -2
	JOB_META_TABLE_STATUS_ACTIVE                                     = 0
	METADATA_TABLE_JOB_REF_DELETE                                    = -1
	METADATA_TABLE_RESOURCE_REF_STATUS_ACTIVE                        = 1
	METADATA_TABLE_VARIABLE_TYPE_META_TABLE                          = 1
	METADATA_TABLE_VARIABLE_TYPE_TEMPORAL_TABLE                      = 2
	METADATA_TABLE_VARIABLE_TYPE_CDAS                                = 3
	META_TABLE_VERSION_LASTEST                                       = -1
	META_TABLE_VERSION_DEFAULT                                       = 1
)

var SYSTEM_GLOBAL_VARIABLE_ARRAY = []string{SYSTEM_VARIABLE_JOB_SERIAL_ID, SYSTEM_VARIABLE_CLUSTER_SERIAL_ID}

var CurrentSupportFlinkVersion = map[string]struct{}{
	FLINK_VERSION_1_10: {},
	FLINK_VERSION_1_11: {},
	FLINK_VERSION_1_13: {},
	FLINK_VERSION_1_16: {},
	FLINK_VERSION_1_18: {},
	FLINK_VERSION_1_20: {},
}

const (
	FOLDER_ROOT_ID                string = "root"
	FOLDER_ROOT_NAME              string = "作业列表"
	FOLDER_ROOT_NAME_RESOURCE     string = "依赖目录"
	FOLDER_TYPE_DEPENDENCY_FOLDER int8   = 1
	FOLDER_MAX_SIZE               int    = -1 //不限制查询条数
)

const (
	DEFAULT_JOB_ERROR_INFO_FROM_CA   = "Job could not be started"
	DEFAULT_JOB_ERROR_INFO_CN        = "Failed to start the job"
	DEFAULT_JOB_ERROR_INFO_CN_DETAIL = "Failed to start the job. Possible causes:"
)

const (
	JM_MEM_LIMIT_FACTOR = "kubernetes.jobmanager.memory.limit-factor"
	JM_CPU_LIMIT_FACTOR = "kubernetes.jobmanager.cpu.limit-factor"
	TM_MEM_LIMIT_FACTOR = "kubernetes.taskmanager.memory.limit-factor"
	TM_CPU_LIMIT_FACTOR = "kubernetes.taskmanager.cpu.limit-factor"
)

// whit_list
const (
	WHITE_LIST_ALLOCATING_DEFAULT_SPACE    = 1    // 允许不传ItemSpace
	WHITE_LIST_OPEN_FINE_GRAINED           = 2    // 开启0.25 CU
	FINEGRAINEDRESOURCE_025                = 0.25 // 0.25 CU
	FINEGRAINEDRESOURCE_05                 = 0.5  // 0.5 CU
	WHITE_LIST_EKS                         = 3    // EKS白名单
	WHITE_LIST_CREATE_INDIRECT_VPC_CLUSTER = 4    // 开启间接vpc集群
	WHITE_LIST_CHECK_SYS_CONNECTOR         = 5    // 检查系统内置connector
	WHITE_LIST_OPEN_RESOURCE_LIMIT_FACTOR  = 6    // V3集群开启内存/CPU限制
	WHITE_FORCE_STOP_JOB                   = 8    // 强制停止作业
	WHITE_LIST_OPEN_NON_STANDARD_POD_SPEC  = 9    // 开启非标准规格POD
	WHITE_LIST_OPEN_FLINK_CONFIGURATION    = 10   // 开启非标准作业高级参数
	WHITE_LIST_START_JOB_NO_CHECK          = 11   // 启动作业不检查

	WHITE_LIST_CREATE_CHDFS_ACCESS_GROUP = 12 // 创建 CHDFS 权限组,这个接口只有DLC使用，其他用户用不到

	WHITE_LIST_DOWNLOAD_RESOURCE = 13 // 下载资源依赖类型
	WHITE_LIST_SQL_GATEWAY       = 14 // 开启SqlGateway
	WHITE_LIST_NODE_NETWORK      = 15 // 创建node eni
	WHITE_LIST_TRACE             = 17 // 创建 CHDFS 权限组,这个接口只有DLC使用，其他用户用不到
	WHITE_LIST_CREATE_DUMP_FILE  = 18 // 创建 dump file

	WHITE_LIST_CPU_MANAGER_POLICY = 20 // 云监控集群支持自动绑定核心

	WHITE_LIST_MEMRATIO_2 = 21 // 1:2机型
	WHITE_LIST_MEMRATIO_8 = 22 // 1:8机型
	WHITE_LIST_CORES_16   = 23 // 16C机型1:4
	WHITE_LIST_CORES_32   = 24 // 32C机型1:4
	WHITE_LIST_CORES_64   = 25 // 64C机型1:4

	// 创建 dump file
	WHITE_LIST_RESERVED_CU_PER_NODE   = 26 // 节点预留CU数量
	WHITE_LIST_CORE_SITE              = 27 // 集群CoreSite配置
	WHITE_LIST_CREATE_IN_AGENT        = 28 // 创建共享集群，指定共享集群母集群
	WHITE_LIST_CHANGE_CONTROLLER_TYPE = 29 // 更改Controller节点类型
	WHITE_LIST_RESOURCE_TYPE_SHARE    = 30 // 共享资源池，资源共享

	WHITE_LIST_NODE_EXPANSION = 31 //

	WHITE_DEPLOY_METRIC_PROVIDER = 33 // 部署metric provider 白名单
	WHITE_LIST_SUB_EKS           = 32 // 包年包月下eks集群

	WHITE_LIST_SUB_EKS_CU_NUM         = 34 // 包年包月下eks集群默认资源是1/4，可以支持开白修改弹性资源的配额0.5 0.25
	WHITE_LIST_CREATE_UPGRADE_CLUSTER = 35

	WHITE_LIST_INC_CPULIMIT_FOR_SMALLCU        = 36 // 小CU作业,提升 CPU limit, 加快启动速度
	WHITE_LIST_EXCLUSIVE_COMPUTEUNDERWRITECU14 = 40 // 包销模式开白
	WHITE_LIST_EKS_POD_CBS_SIZE                = 41 // 弹性集群作业支持设置pod 磁盘大小
	WHITE_LIST_REPLACE_SYSTEM_CONNECTOR        = 42 // 替换内置connector
	WHITE_LIST_CLUSTER_CONFIG                  = 43 // 集群配置白名单配置
	WHITE_LIST_CREATE_PRIVATE_CLUSTER          = 44 // 确认创建独享集群

	WHITE_LIST_REMOVE_CLUSTER_FEATURE = 47 // 创建集群时，移除cluster feature

	WHITE_LIST_AlLOW_ETL_JOB = 50 // 允许使用ETL作业

	WHITE_LIST_DISABLE_JOB_FINISHED_STATUS = 51 // 9.19迭代作业增加了 完成状态， 要过滤掉inlong设置，inlong不需要展示完成，设置停止就行
	WHITE_LIST_EXCLUSIVE_SUPPORT_SUB_EKS   = 60 // 独享集群支持子eks

	WHITE_LIST_SCALE_VERTEX_PARALLELISM = 70

	WHITE_LIST_SUPPORT_LOW_INTERVAL_CHECK_POINT = 61 // 支持低频率checkpoint

	WHITE_LIST_SCALE_DOWN_PROTECTED                  = 62 // 缩容不下架节点
	WHITE_LIST_SCALE_UP_PROTECTED                    = 63 // 扩容不新增节点
	WHITE_LIST_SUPPORT_SETATS                        = 65 // 支持setats
	WHITE_LIST_TREE_PAGE_NEW_MODE                    = 66 // 给前端使用, 用于控制是否走作业管理作业树新页面逻辑
	WHITE_LSIT_MULTIPLE_DEPLOYMENT_MODE              = 67 // 用户是否支持跨可用区部署， 共享集群 资源独享
	WHITE_LIST_MULTIPLE_DEPLOYMENT_MODE_SLAVE_CU_NUM = 68 // 多可用区备集群默认资源是1/2，可以支持开白修改弹性资源的配额0.5 0.25
	WHITE_LIST_SETATS_BUILD_IN_HIVE                  = 69 // setats 内置hive
)

// ItemSpace
const (
	CLUSTER_DEFAULT_LOG_COLLECT_DISABLE       = 1 // 集群默认日志采集方式：不采集
	CLUSTER_DEFAULT_LOG_COLLECT_CLS           = 2 // 集群默认日志采集方式：采集到CLS
	CLUSTER_DEFAULT_LOG_COLLECT_COS           = 3 // 集群默认日志采集方式：采集到COS
	CLUSTER_DEFAULT_LOG_COLLECT_ES_SERVERLESS = 4 // 集群默认日志采集方式：采集到 es serverless
)

// 作业失败自动恢复
const (
	// 作业自动恢复开启
	AutoRecoverEnable = 1
	// 作业自动恢复关闭
	AutoRecoverDisable = -1
)

const (
	// 作业停止是否继续告警
	ContinueAlarmEnable  = 1
	ContinueAlarmDisable = 2
)

const DefaultCheckpoingRetainNum = 3

const (
	DIAGNOSIS_DATA_VOLUME_MOUNTED_EKY   = "flink.kubernetes.diagnosis-volume-mounted"
	DIAGNOSIS_DATA_VOLUME_MOUNTED_VALUE = "true"

	DIAGNOSIS_DATA_COLLECT_ENABLED_KEY   = "flink.kubernetes.diagnosis-collection-enabled"
	DIAGNOSIS_DATA_COLLECT_ENABLED_VALUE = "true"
)

const (
	VARIABLE_STATUS_ACTIVE      = 1
	VARIABLE_STATUS_HISTORY     = 2
	VARIABLE_STATUS_DELETE      = -2
	VARIABLE_COMPONENT_TYPE_JOB = 1
	VARIABLE_TYPE_VISIBLE       = 1
	VARIABLE_TYPE_HIDDEN        = 2
	VARIABLE_COMPONENTTYPE_JOB  = 1
	VARIABLE_TYPE_SYSTEM        = 3
)

const (
	RunMode            = "RunMode"
	WorkSpaceId        = "WorkSpaceId"
	EngineTaskId       = "EngineTaskId"
	JobManagerSpec     = "JobManagerSpec"
	TaskManagerSpec    = "TaskManagerSpec"
	DefaultParallelism = "DefaultParallelism"
	FlinkVersionKey    = "FlinkVersion"
	Properties         = "Properties"
	SchedulingTime     = "SchedulingTime"
)

const (
	ITEM_SPACE_STATUS_INIT    = 1  // 未初始化
	ITEM_SPACE_STATUS_USEABLE = 2  // 可用
	ITEM_SPACE_STATUS_DELETED = -1 // 删除

	ITEM_SPACE_ORDERBY_CTREATETIME_DESC = 1
	ITEM_SPACE_ORDERBY_CTREATETIME_ASC  = 2
	ITEM_SPACE_ORDERBY_STATUS_DESC      = 3
	ITEM_SPACE_ORDERBY_STATUS_ASC       = 4
)

// RoalAutho
const (
	ROLE_AUTHO_PERMISSION_SUPER_OWNER      = 0       // 超级管理员
	ROLE_AUTHO_PERMISSION_OWNER            = 1       // 空间管理员
	ROLE_AUTHO_PERMISSION_DEVELOPER        = 2       // 开发者
	ROLE_AUTHO_PERMISSION_VIEW             = 3       // 操作
	ROLE_AUTHO_PERMISSION_SUPER_OWNER_NAME = "超级管理员" // 超级管理员

	ROLE_AUTHO_STATUS_USEABLE  = 2 // 可用
	ROLE_AUTHO_STATUS_DISEABLE = 1 // 不可用
)

// ItemSpaceCLusterGroups
const (
	ITEM_SPACE_CLUSTERGROUPS_STATUS_USEABLE = 2 // 可用
	ITEM_SPACE_CLUSTERGROUPS__DELETED       = 1 // 删除
	CLUSTERBOUNDSPACE                       = "ClusterBoundSpace"
	CLUSTERBOUNDSPACENUM                    = 10
)

const (
	DEFAULT_ITEM_SPACE_NAME = "default0" // 默认空间名称
)

const (
	SCALE_RULES_STATUS_ACTIVE   int8 = 1
	SCALE_RULES_STATUS_DELETE   int8 = -1
	SCALE_RULES_STATUS_INACTIVE int8 = 2

	SCALE_PLAN_STATUS_ACTIVE   int8 = 1
	SCALE_PLAN_STATUS_DELETE   int8 = -1
	SCALE_PLAN_STATUS_INACTIVE int8 = 2

	SCALE_RULES_AUTO_SCALE_BASIC      string = "auto_scale_basic"
	SCALE_RULES_AUTO_SCALE_TIME_BASED string = "auto_scale_time_based"

	SCALE_RULES_AUTO_SCALE_BASIC_TYPE      int = 2
	SCALE_RULES_AUTO_SCALE_TIME_BASED_TYPE int = 1

	SCALE_RULES_TIMEUNIT_ONCE  = "once"
	SCALE_RULES_TIMEUNIT_DAY   = "day"
	SCALE_RULES_TIMEUNIT_WEEK  = "week"
	SCALE_RULES_TIMEUNIT_MONTH = "month"

	SCALE_SUGGESTION_STATUS_OK     int8 = 1
	SCALE_SUGGESTION_STATUS_DELETE int8 = -1

	SCALE_EVENT_STATUS_SUCCESS int8 = 1
	SCALE_EVENT_STATUS_FAILED  int8 = 2

	SCALE_ACTION_STATUS_INITIALIZE  int8 = 0
	SCALE_ACTION_STATUS_INPROGRESS  int8 = 1
	SCALE_ACTION_STATUS_SUCCESS     int8 = 2
	SCALE_ACTION_STATUS_FAILED      int8 = 3
	SCALE_ACTION_STATUS_IGNORED     int8 = 4
	SCALE_ACTION_STATUS_UNSATISFIED int8 = 6

	SCALE_DEALY_SECONDS           int = 300
	SCALE_BACKOFF_SECONDS         int = 1800
	SCALE_RESTART_TIMEOUT_SECONDS int = 180
	SCALE_CONDITIONRATIO          int = 100

	CPU_INCREASE_RULE     string = "cpu_increase"
	CPU_DECREASE_RULE     string = "cpu_decrease"
	INCREASE_RULE_KEYWORD        = "increase"
	DECREASE_RULE_KEYWORD        = "decrease"
	SCALE_RULE_NAME              = "cpu"

	SCALE_ACTION_SEPERATOR                    = "."
	SCALE_ACTION_TYPE_TASK_PARALLELISM        = "task-parallelism"
	SCALE_ACTION_TYPE_TASK_RESOURCE           = "task-resource"
	SCALE_ACTION_COMPONENT_JOB_MANAGER        = "jm"
	SCALE_ACTION_COMPONENT_TASK_MANAGER       = "tm"
	SCALE_ACTION_SCHEDULE_TYPE_SCALE_DOWN     = "scale-down"
	SCALE_ACTION_SCHEDULE_TYPE_SCALE_UP       = "scale-up"
	SCALE_ACTION_SCHEDULE_TYPE_SCALE_FLEXIBLE = "task-flexible"

	SCALE_ACTION_TYPE_TASK_PARALLELISM_INT = 1
	SCALE_ACTION_TYPE_TASK_RESOURCE_INT    = 2
	SCALE_ACTION_TYPE_TASK_FLEXIBLE        = 3

	SCALE_ACTION_TYPE_SCALE_UP       = 1
	SCALE_ACTION_TYPE_SCALE_DOWN     = 2
	SCALE_ACTION_TYPE_SCALE_FLEXIBLE = 3

	SCALE_ACTION_TYPE_TASK_PARALLELISM_SCALE_UP   = "task-parallelism.scale-up"
	SCALE_ACTION_TYPE_TASK_PARALLELISM_SCALE_DOWN = "task-parallelism.scale-down"
	SCALE_ACTION_TYPE_TASK_RESOURCE_SCALE_UP      = "task-resource.scale-up"
	SCALE_ACTION_TYPE_TASK_RESOURCE_SCALE_DOWN    = "task-resource.scale-down"
	SCALE_ACTION_TYPE_TASK_FLEXIBLE_TYPE          = "task-flexible.scale-flexible"

	SCALE_RULES_AUTO_SCALE_TIME_BASED_SCALE_UP_PREFIX   = "AutoScaleJob_TimeBased_start_"
	SCALE_RULES_AUTO_SCALE_TIME_BASED_SCALE_DOWN_PREFIX = "AutoScaleJob_TimeBased_end_"

	//初始状态，表示经过检查可以进入扩缩容流程
	SCALE_JOB_STATUS_READY = "READY"
	//成功启动SAVEPOINT
	SCALE_JOB_STATUS_START_SAVEPOINT = "START_SAVEPOINT"
	//成功完成SAVE_POINT
	SCALE_JOB_STATUS_FINISH_SAVEPOINT = "FINISH_SAVEPOINT"
	//成功提交启动任务流程
	SCALE_JOB_STATUS_SUBMIT_RESTART_COMMAND = "SUBMIT_RESTART_COMMAND"
	//成功启动任务
	SCALE_JOB_STATUS_FINISH_RESTART_COMMAND = "FINISH_RESTART_COMMAND"
	//成功提交启动任务流程
	SCALE_JOB_STATUS_SUBMIT_INPLACE_COMMAND = "SUBMIT_INPLACE_COMMAND"
	//成功启动任务
	SCALE_JOB_STATUS_FINISH_INPLACE_COMMAND = "FINISH_INPLACE_COMMAND"

	//调优记录类型
	SCALE_FLEXIBLE_EVENT = 1
	SCALE_AUTO_EVENT     = 2

	//作业正在调优的类型
	SCALING_TYPE_CLOSE      = 0
	SCALING_TYPE_BASIC      = 1
	SCALING_TYPE_TIME_BASED = 2
)

const (
	SCALE_PARAM_KEY_JOBS   = "SCALE_PARAM_KEY_JOBS"
	SCALE_PARAM_START_TIME = "SCALE_PARAM_START_TIME"
	SCALE_FLOW_DOC_ID      = "AutoScaleJob"

	RULE_NAME_SPLIT = "_"

	SCALING_MODE_NONE   = 0
	SCALING_MODE_MANUAL = 1
	SCALING_MODE_AUTO   = 2

	//未通过扩缩容预检查
	SCALE_EVENT_TYPE_PRE_CHECK_NOT_PASS = "pre_check_not_pass"
	//CU资源不够
	SCALE_EVENT_TYPE_CU_INSUFFICIENT = "cu_insufficient"
	//CU并行资源不够，需要回退到下一次
	SCALE_EVENT_TYPE_PARALLEL_CU_INSUFFICIENT = "parallel_cu_insufficient"
	//超过 JM/TM cu数 配置的LIMIT 上线
	SCALE_EVENT_TYPE_EXCEED_TM_CU_UPPER_LIMIT = "exceed_tm_cu_upper_limit"
	//超过 JM/TM cu数 配置的LIMIT 下限
	SCALE_EVENT_TYPE_EXCEED_TM_CU_LOWER_LIMIT  = "exceed_tm_cu_lower_limit"
	SCALE_EVENT_TYPE_EXCEED_TM_CPU_LOWER_LIMIT = "exceed_tm_cpu_lower_limit"
	SCALE_EVENT_TYPE_EXCEED_JM_CPU_LOWER_LIMIT = "exceed_jm_cpu_lower_limit"
	SCALE_EVENT_TYPE_EXCEED_TM_MEM_LOWER_LIMIT = "exceed_tm_mem_lower_limit"
	SCALE_EVENT_TYPE_EXCEED_JM_MEM_LOWER_LIMIT = "exceed_jm_mem_lower_limit"
	//超过扩容配置的LIMIT
	SCALE_EVENT_TYPE_EXCEED_UPPER_LIMIT = "exceed_upper_limit"
	//超过缩容配置的LIMIT
	SCALE_EVENT_TYPE_EXCEED_LOWER_LIMIT = "exceed_lower_limit"
	//不满足冷却时间
	SCALE_EVENT_TYPE_COLD_DOWN_TIME_UNMATCHED = "cool_down_time_unmatched"
	//数据不完整
	SCALE_EVENT_TYPE_METRIC_DATA_INCOMPLETE = "metric_data_incomplete"
	//提交Savepoint失败
	SCALE_EVENT_TYPE_TRIGGER_SAVEPOINT_FAILED = "trigger_savepoint_failed"
	//Savepoint执行失败
	SCALE_EVENT_TYPE_EXEC_SAVEPOINT_FAILED = "savepoint_failed"
	//Savepoint执行超时
	SCALE_EVENT_TYPE_EXEC_SAVEPOINT_TIMEOUT = "savepoint_timeout"
	//提交任务重启失败
	SCALE_EVENT_TYPE_SUBMIT_RUN_JOB_FAILED = "submit_run_job_failed"
	//算子资源修改失败
	SCALE_EVENT_TYPE_MODIFY_RESOURCES_FAILED = "modify_resources_failed"
	//重启作业未知原因失败
	SCALE_EVENT_TYPE_RESTART_JOB_FAILED = "restart_job_failed"
	//原地扩缩容作业未知原因失败
	SCALE_EVENT_TYPE_INPLACE_JOB_FAILED = "inPlace_job_failed"
	//重启作业超时
	SCALE_EVENT_TYPE_RESTART_JOB_TIMEOUT = "restart_job_timeout"
	//原地扩缩容作业超时
	SCALE_EVENT_TYPE_INPLACE_JOB_TIMEOUT = "inPlace_job_timeout"
	// 已设置其他级别的并行度，parallelism.default 不生效
	SCALE_EVENT_TYPE_OTHER_LEVELS_OF_PARALLELISM_HAVE_BEEN_SET = "other_levels_of_parallelism_have_been_set"
	SQL_KEYWORD                                                = "SqlKeyword"
	// 资源没变，跳过
	SCALE_RESOURCE_SAME                           = "scale_resource_same"
	SCALE_EVENT_TYPE_INPLACE_MODIFY_CONFIG_FAILED = "inPlace_job_modify_config_failed"
	SCALE_EVENT_TYPE_INPLACE_RESART_WD            = "inPlace_restart_wd_failed"
	SCALE_FLOW_RUNNING                            = "scale_flow_running"
	SCALE_VERTEX_SAME_GRAPH                       = "scale_vertex_same_before_graph"

	RESTART_SAVEPOINT_DESC = "由自动扩缩容程序创建的快照"
)

const (
	ETL_CANVAS_NODE_TYPE_SOURCE   = "source"
	ETL_CANVAS_NODE_TYPE_SINK     = "sink"
	ETL_CANVAS_NODE_TYPE_OPERATOR = "operator"
	SKIP_PREFIX                   = "_"
	JDBC_URL_PREFIX               = "jdbc.url."
	ASYNC_JOB_TYPE_PRECHECK       = 1
	PreCheckHandlerName           = "PreCheckHandler"
)

const (
	EMPTY = ""
)

const (
	ASYNC_JOB_INFO_STATUS_READY       = 1
	ASYNC_JOB_INFO_STATUS_RUNNING     = 2
	ASYNC_JOB_INFO_STATUS_SUCCESS     = 3
	ASYNC_JOB_INFO_STATUS_FAIL        = 4
	ASYNC_JOB_INFO_TYPE_ETL_PRE_CHECK = 1
)

const (
	ETL_OP_TYPE_DEFAULT = "default"
	ETL_OP_TYPE_ALTER   = "alter"
	ETL_OP_TYPE_CREATE  = "create"
	/**
	参考七彩石 ConfigureCenter.Flow.Common.eksRoleName
	*/
	EKS_ROLE_NAME                                  = "eks.tke.cloud.tencent.com/role-name"
	EKS_CPU_TYPE_KEY                               = "eks.tke.cloud.tencent.com/cpu-type"
	EKS_SECURITY_GROUP_ID_KEY                      = "eks.tke.cloud.tencent.com/security-group-id"
	EKS_FINEGRAINEDRESOURCE_CPU_TYPE               = "intel,amd"
	KUBERNETES_JOBMANAGER_ANNOTATIONS              = "kubernetes.jobmanager.annotations"
	KUBERNETES_TASKMANAGER_ANNOTATIONS             = "kubernetes.taskmanager.annotations"
	KUBERNETES_HOSTNETWORK_ENABLED                 = "kubernetes.hostnetwork.enabled"
	KUBERNETES_INITCONTAINER_MB_KEY                = "kubernetes.internal.init.container.bind.core.memory.mb"
	KUBERNETES_INITCONTAINER_MB_1024               = "1024"
	REST_FLAMEGRAPH_ENABLED                        = "rest.flamegraph.enabled"
	EKS_POD_CBS_SIZE                               = "eks.tke.cloud.tencent.com/root-cbs-size"
	TASKMANAGER_NETWORK_BINDADDRESS_SKIP_LOCALHOST = "taskmanager.network.bind-address.skip-localhost"
)

const (
	// 使用 metric_provider 组件获取 Key 时的授权信息
	USER_NAME = "metricProvider"

	// 七彩石配置信息
	METRIC_GROUP_KEY         = "Metric"
	METRIC_ACCESS_KEY_SECRET = "ACCESS_KEY_SECRET"
	METRIC_ACCESS_KEY_ID     = "ACCESS_KEY_ID"
)

/**
 * 前端区分 集群是否需要 增加2CU 收费逻辑 因为历史集群 变配不需要
 */
const (
	ROLE_TYPE_INTERNAL       = 0
	ROLE_TYPE_OUTER          = 1
	ROLE_INVALID_STATUS      = -2
	ROLE_VALID_STATUS        = 1
	ROLE_TABLE               = "Role"
	ROLE_PERMISSION_TABLE    = "RolePermission"
	PERMISSION_TABLE         = "Permission"
	ADMINISTRATOR_TABLE      = "Administrator"
	PERMISSION_SELECTABLE    = 1
	PERMISSION_NO_SELECTABLE = 0
)

const (
	// 作业日志级别
	LOG_LEVEL_DEBUG = "DEBUG"
	LOG_LEVEL_INFO  = "INFO"
	LOG_LEVEL_WARN  = "WARN"
	LOG_LEVEL_ERROR = "ERROR"
	LOG_LEVEL_TRACE = "TRACE"

	// 作业日志级别集群特性常量
	CLUSTER_LOG_LEVEL_FEATURE = "LogLevel"

	CLASS_LOG_LEVEL_DEFALUT_VALUE = "[]"
)
const (
	NOT_NEED_MANAGE_NODE = 0
	NEED_MANAGE_NODE     = 1
)

// 计费相关
const (
	BILLING_GROUP_KEY             = "Billing"
	BILLING_RETRY_COUNT           = 10
	BILLING_API_GATEWAY           = "BILLING_API_GATEWAY"
	BILLING_API_GATEWAY_APPID     = "BILLING_API_GATEWAY_APPID"
	BILLING_API_GATEWAY_APPSECRET = "BILLING_API_GATEWAY_APPSECRET"
	SEND_RESOURCE_URL             = "SEND_RESOURCE_URL"
	Cid                           = "Cid"
)

const (
	SETTLE_INIT  = 0
	SETTLE_DOING = 1
	SETTLE_DONE  = 2
	BATCH_SIZE   = 5
)

const (
	UseOldSysConnector_ENABLE   = 0 // 使用旧版内置connector
	UseOldSysConnector_UNENABLE = 1 // 不使用旧版内置connector
)

const (
	AUTO_RENEW_FLAG_DEFAULT = 0 // 表示默认状态(用户未设置，即初始状态：若用户有预付费不停服特权，也会对该值进行自动续费
	AUTO_RENEW_FLAG_RENEW   = 1 // 表示自动续费
	AUTO_RENEW_FLAG_UNRENEW = 2 // 表示明确不自动续费(用户设置)
)

// SqlGateway状态
const (
	SqlGatewayStopped  = 1  // 已停止
	SqlGatewayCreating = 2  //开启中
	SqlGatewayRunning  = 3  // 已开启
	SqlGatewayFailed   = 4  //开启失败
	SqlGatewayStopping = 5  //停止中
	SqlGatewayDeleted  = -2 // 已删除(集群被销毁时更新为此状态)
)
const (
	CVM_DEFAULT_MEMRATIO         = 4
	CVM_DEFAULT_CORES            = 8
	SUB_EKS_CU_RATIO     float64 = 4

	CVM_MEMRATIO_2 = 2
	CVM_MEMRATIO_8 = 8
)

const LOG2ES = "Log2Es"
const Log2cosByCli = "Log2cosByCli"
const OceanusFeatureParamKey = "oceanus.cluster.support.features"
const (
	METADATA_DESCRIBEHIVETBS_ACTION  = 1
	METADATA_CHECKDDLGRAMMAR_ACTION  = 2
	METADATA_ANALYZEUSERJAR_ACTION   = 3
	SQL_CHECK_ACTION                 = 4
	SQL_PREVIEW_FETCH_ACTION         = 5
	SQL_PREVIEW_RUN_ACTION           = 6
	SQL_PREVIEW_STOP_ACTION          = 7
	METADATA_DESCRIBEHIVEDBS_ACTION  = 8
	SQL_CHECK_COMPATIBILITY_ACTION   = 9
	METADATA_DESCRIBEMYSQLDBS_ACTION = 10
	METADATA_DESCRIBEMYSQLTBS_ACTION = 11
	METADATA_PARSECONNECTOR_ACTION   = 12

	METADATA_DESCRIBEHIVETBS_PATH  = "/metadata/describeHiveTbs"
	METADATA_CHECKDDLGRAMMAR_PATH  = "/metadata/checkDdlGrammar"
	METADATA_ANALYZEUSERJAR_PATH   = "/metadata/analyzeUserJar"
	METADATA_PARSECONNECTOR_PATH   = "/metadata/parseConnector"
	SQL_CHECK_PATH                 = "/sql/check"
	SQL_PREVIEW_FETCH_PATH         = "/sql/preview/fetch"
	SQL_PREVIEW_RUN_PATH           = "/sql/preview/run"
	SQL_PREVIEW_STOP_PATH          = "/sql/preview/stop"
	METADATA_DESCRIBEHIVEDBS_PATH  = "/metadata/describeHiveDbs"
	METADATA_DESCRIBEMYSQLDBS_PATH = "/metadata/describeMysqlDbs"
	METADATA_DESCRIBEMYSQLTBS_PATH = "/metadata/describeMysqlTbs"
	SQL_CHECK_COMPATIBILITY_PATH   = "/sql/checkCompatibility"

	CHECK_SQL_COMPATIBILITY = "CheckSqlCompatibility"
)

var SQL_SERVER_PATH_MAP = map[int]string{
	METADATA_DESCRIBEHIVETBS_ACTION:  METADATA_DESCRIBEHIVETBS_PATH,
	METADATA_CHECKDDLGRAMMAR_ACTION:  METADATA_CHECKDDLGRAMMAR_PATH,
	METADATA_ANALYZEUSERJAR_ACTION:   METADATA_ANALYZEUSERJAR_PATH,
	METADATA_PARSECONNECTOR_ACTION:   METADATA_PARSECONNECTOR_PATH,
	SQL_CHECK_ACTION:                 SQL_CHECK_PATH,
	SQL_PREVIEW_FETCH_ACTION:         SQL_PREVIEW_FETCH_PATH,
	SQL_PREVIEW_RUN_ACTION:           SQL_PREVIEW_RUN_PATH,
	SQL_PREVIEW_STOP_ACTION:          SQL_PREVIEW_STOP_PATH,
	METADATA_DESCRIBEHIVEDBS_ACTION:  METADATA_DESCRIBEHIVEDBS_PATH,
	SQL_CHECK_COMPATIBILITY_ACTION:   SQL_CHECK_COMPATIBILITY_PATH,
	METADATA_DESCRIBEMYSQLDBS_ACTION: METADATA_DESCRIBEMYSQLDBS_PATH,
	METADATA_DESCRIBEMYSQLTBS_ACTION: METADATA_DESCRIBEMYSQLTBS_PATH,
}

const (
	JOB_EVENT_INFO_KEY = "JobEventInfo"
)

const (
	// RunningCu desc, CurrentRunMillis asc
	JOB_ORDER_BY_RUNNINGCU        = "RunningCu"
	JOB_ORDER_BY_CURRENTRUNMILLIS = "CurrentRunMillis"
	JOB_ORDER_BY_RUNNINGCPU       = "RunningCpu"
	JOB_ORDER_BY_RUNNINGMEM       = "RunningMem"
	ASC                           = "asc"
	DESC                          = "desc"
)

const CALLTYPE_DEVOPS = 1

const (
	ZkAuthInterfaceName                  = "qcloud.clustermanager.zk"
	ZKAUTH_SETROOTACLFORADMIN_ACTION     = 1
	ZKAUTH_SETACL_ACTION                 = 2
	ZooKeeperComponentReplicaNumber  int = 3   // 注意七彩石下 Tke / deploy_zookeeper.yaml 中的 replicaNumber 也要保持一致
	INGRESS_CPU_LIMIT                    = 100 //100m
	INGRESS_MEMORY_LIMIT                 = 350 // 350Mi
)

const (
	OCEANUS_COS_PARAM_FS_COSN_CROSS_ACCOUNT_ACCESS_KEY     = "oceanus.fs.cosn.cross.account.access"
	OCEANUS_COS_PARAM_FS_COSN_CROSS_ACCOUNT_ACCESS_ENABLE  = "true"
	OCEANUS_COS_PARAM_FS_COSN_CROSS_ACCOUNT_ACCESS_DISABLE = "false"

	SQL_KEYWORD_TRIM_CHARACTER_LENGTH = "sql.keyword.trim.character.length"
)

var CosParamFlinkVersion = map[string]struct{}{
	FLINK_VERSION_1_16: {},
	FLINK_VERSION_1_18: {},
	FLINK_VERSION_1_20: {},
}

const (
	MIDNIGHT                                 = "00:00"
	AUTO_SCALE_TIME_BASED_INTERVAL           = 30
	AUTO_SCALE_TIME_BASED_MIDNIGHT_BACK_TIME = 5
)

const TIME_SAMPLE = "2006-01-02 15:04"
const TIME_SAMPLE_MINUTE = "2006-01-02 15:04:05"
const TIME_ZONE = "Asia/Shanghai"

// TODO 9.21改成configuration里的
const COOL_DOWN_TIME = "10"
const DELAY_TIME = "30"
const DELAY_TIME_EX = "40"
const FUTURE_TIME = "15"
const ONE_DAY_SECOND = 24 * 60 * 60
const REACHLIMIT = 64

const FLOAT_TOLERATE = 0.0001

const (
	SOURCE    = "1" //源表
	DIMENSION = "2" //维表
	SINK      = "3" //结果表
)

const (
	DisableStaticMode = 0
	EnableStaticMode  = 1
)

const (
	ANALYZE_USER_JAR_EVENTID               = 8
	CHECK_DDL_GRAMMAR_EVENTID              = 9
	CHECK_SQL_GRAMMAR_EVENTID              = 10
	RUN_SQL_PREVIEW_EVENTID                = 11
	FETCH_SQL_PREVIEW_EVENTID              = 12
	STOP_SQL_PREVIEW_EVENTID               = 13
	ATTACH_VARIABLES_EVENTID               = 14
	CHECK_SQL_COMPATIBILITY_EVENTID        = 15
	QUERY_SQL_COMPATIBILITY_RESULT_EVENTID = 16
	PARSE_CONNECTOR_EVENTID                = 17
	PARSE_AND_VALIDATE_EVENTID             = 18
)

const (
	UnExpectJobStatusRunFailed  = "UnExpectJobStatusRunFailed"
	UnExpectJobStatusStopFailed = "UnExpectJobStatusStopFailed"
)

// 国际化标准语言参数格式
const (
	CHINESE = "zh-CN"
	ENGLISH = "en-US"
)

// 数据库国际化，新增表需要使用的后缀
const (
	I18_DATABASE_CHINESE = "_zh"
	I18_DATEBASE_ENGLISH = "_en"
)

const (
	RestartScale        = "restart"
	InPlaceScale        = "in-place"
	InPlaceScaleTimeout = 660
	ADMIN               = "admin"
	SCALEVERTEX         = "scale-vertex"
	START_USE_JOB_GRAPH = "start-job.graph"
)

const (
	YARN_WEB_PORT                   = 8088
	YARN_WEB_RPC_PORT               = 8032
	NAMENODE_RPC_PORT               = 9000
	HADOOP_APP_NAME                 = "hadoop"
	NAMENODE_COMPONENT_NAME         = "hdfs-nn"
	RESOURCE_MANAGER_COMPONENT_NAME = "yarn-rm"
	NODE_MANAGER_COMPONENT_NAME     = "yarn-nm"
	DATANODE_COMPONENT_NAME         = "hdfs-dn"
	CORE_SITE_FILE_NAME             = "core-site.xml"
	MAPRED_SITE_FILE_NAME           = "mapred-site.xml"
	YARN_SITE_FILE_NAME             = "yarn-site.xml"
	HDFS_SITE_FILE_NAME             = "hdfs-site.xml"
	STREAMPARK_CONFIG_FILE_NAME     = "streampark-config.yaml"
	DINKY_CONFIG_FILE_NAME          = "dinky-config.yaml"
	NGINX_DEFAULT_CONF              = "default.conf"
	NGINX_CONF                      = "nginx.conf"
	HOSTS_CONF                      = "hosts"
)

const (
	EVENTALERT_SOLUTION_SUFFIX = "_solution_link"
)

// 请求链路透传信息
const (
	AttachKeyRequestID     = "X-oceanus-request-id"
	AttachKeyServerName    = "X-oceanus-server-name"
	AttachKeyServerRpcName = "X-oceanus-server-rpc-name"
)

// ServerName 本服务服务名
const (
	ServerName = "galileo"
)

const (
	FLINK_SESSION_DEFAULT_HADOOP_USER = "flink"
)

const (
	MasterRole                     = 0
	SlaveRole                      = 1
	DeploymentModeDefault          = 0
	DeploymentModeMultiple         = 1
	SLAVE_ZONE_CU_RATIO    float64 = 2
	DefaultCuPerCvm                = 7
)
