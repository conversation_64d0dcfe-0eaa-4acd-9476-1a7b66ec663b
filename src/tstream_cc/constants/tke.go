package constants

// TKE 独享集群自动化所需的常量
const (
	K8S_KIND_STATEFULSET = "StatefulSet"
	K8S_KIND_DEPLOYMENT  = "Deployment"
	K8S_KIND_DAEMONSET   = "DaemonSet"

	OceanusSecretName = "qcloud" // ImagePullSecrets 所用的鉴权 Secret 名称

	DEFAULT_NAMESPACE              = "default"
	OCEANUS_NAMESPACE              = "oceanus"     // Kubernetes 集群部署的管控服务 Pod 命名空间
	KUBE_SYSTEM_NAMESPACE          = "kube-system" // TKE集群中kube-system 命名空间
	OCEANUS_NAMESPACE_OCEANUS_ROLE = "oceanus-ns-oceanus-role.yaml"

	DEFAULT_DNS_POLICY = "ClusterFirst"
	NONE_DNS_POLICY    = "\"None\""
)

const (
	TKE_STATUS_CREATING = 0
	TKE_STATUS_RUNNING  = 1
	TKE_STATUS_DELETED  = -2

	TKE_STATUS_RUNNING_STR  = "Running"
	TKE_STATUS_ABNORMAL_STR = "Abnormal"
	EKS_STATUS_IDLING_STR   = "Idling"

	TKE_INSTANCE_STATE_RUNNING  = "running"
	TKE_INSTANCE_STATE_FAILED   = "failed"
	TKE_INSTANCE_STATE_SHUTDOWN = "shutdown"

	TKE_API_DOMAIN_INTERNAL = "tke.internal.tencentcloudapi.com"

	TKE_CLUSTER_TYPE             = "MANAGED_CLUSTER"
	TKE_NODE_ROLE                = "WORKER"
	TKE_MAX_CLUSTER_SERVICE_NUM  = 10240
	TKE_SERVICE_CIDR             = ""
	TKE_CVM_CHARGE_TYPE          = "PREPAID"
	TKE_CVM_CHARGE_POSTPAID      = "POSTPAID_BY_HOUR"
	TKE_CVM_PREPAID_PERIOD       = 48
	TKE_CVM_RENEW_FLAG           = "NOTIFY_AND_AUTO_RENEW"
	TKE_CVM_DISK_TYPE            = "CLOUD_PREMIUM"
	TKE_CVM_LABEL_KEY            = "oceanus-node-type"
	EXPANSION_LABEL_KEY          = "expansion"
	EXPANSION_CPU_LABEL_KEY      = "expansion/cpu"
	EXPANSION_MEMORY_LABEL_KEY   = "expansion/memory"
	EXPANSION_POD_LABEL_KEY      = "expansion/pods"
	TKE_CVM_ZK_LABEL_KEY         = "oceanus-node-zk"
	TKE_CVM_WORKER_LABEL_KEY     = "oceanus-node-worker"
	TKE_CVM_WORKER_MANAGE_KEY    = "oceanus-node-manage-by"
	TKE_CVM_WORKER_NUM_LABEL_KEY = "oceanus-node-worker-num"
	TKE_CVM_NODE_ZONE            = "oceanus-node-zone"
	TKE_HEALTH_CHECK_POLICY_NAME = "auto-devops"
	TKE_CVM_NODE_CLUSTER         = "oceanus-node-cluster"
	TKE_CVM_NODE_APPID           = "oceanus-node-appid"
	TKE_CHART_FROM_MARKET        = "tke-market"
	TKE_CLUSTER_TYPE_SMALL_FORM  = "tke"
	TKE_CHART_NAMESPACE          = "opensource-stable"
	TKE_PRIORITY_CLASS_NAME      = "high-priority"
	TKE_CVM_NODE_RESOURCE_TYPE   = "oceanus-resource-type"
	//没有打这两个标签的Worker节点是中间状态，用于进行转换的
	TKE_CVM_NODE_RESOURCE_TYPE_PRIVATE = "private"
	TKE_CVM_NODE_RESOURCE_TYPE_SHARE   = "share"

	TKE_CVM_INTERNET_CHARGE_TYPE = "TRAFFIC_POSTPAID_BY_HOUR"
	TKE_CVM_PUBLIC_IP_ASSIGNED   = false
	TKE_PROJECT_ID               = 0

	TKE_DATA_DISK_PARTITION                   = "/dev/vdb"
	TKE_DATA_DISK_MOUNT_TARGET                = "/var/lib/docker"
	TKE_DATA_DISK_MOUNT_TARGET_FOR_CONTAINERD = "/var/lib/containerd"
	TKE_KUBELET_DIR                           = "root-dir"
	TKE_EVICTION_HARD                         = "eviction-hard"
	TKE_EVICTION_HARD_VALUE                   = "nodefs.available<5%,nodefs.inodesFree<5%,imagefs.available<5%,memory.available<100Mi"
	TKE_DATA_DISK_FILE_SYSTEM                 = "ext4"
	TKE_DATA_DISK_AUTO_FORMAT_AND_MOUNT       = true

	TKE_NODE_SYSTEM_DISK_SIZE       = 20
	TKE_NODE_SETATSSYSTEM_DISK_SIZE = 50

	// 管控节点
	TKE_CONTROL_NODE_INSTANCE_TYPE     = "SA2.MEDIUM8"
	TKE_CONTROL_NODE_DISK_SIZE         = 80
	TKE_CONTROL_NODE_DISK_SIZE_UNIFORM = 1024
	TKE_CONTROL_NODE_NUM               = 3
	TKE_CONTROL_NODE_LABEL_VAL         = "controller"

	TKE_ZK_NODE_LABEL_VAL = "zookeeper"

	// TKE_ARCH_GENERATION_V2 V0架构：没有更改资源大小，比如：controller 是4核8G、磁盘是200G
	// V1架构：改变了资源大小， 比如：controller 改成2核8G、磁盘改成80G，worker节点磁盘改成150G。 其实底层和V0架构完全一样的
	// V2架构：就是最新的
	TKE_ARCH_GENERATION_V1 = 1
	TKE_ARCH_GENERATION_V2 = 2
	TKE_ARCH_GENERATION_V3 = 3 //去掉公共服务白名单版本
	TKE_ARCH_GENERATION_V4 = 4 //统一资源池版本
	TKE_ARCH_GENERATION_V5 = 5 //统一worker和controller节点版本
	TKE_ARCH_GENERATION_V6 = 6 //统一公有和私有化版本

	TKE_CONTROL_NODE_NUM_V3      = 1
	TKE_CONTROL_NODE_NUM_UNIFORM = 3

	// 计算节点
	TKE_WORKER_NODE_INSTANCE_TYPE = "SA2.2XLARGE32"
	TKE_WORKER_NODE_DISK_SIZE     = 150
	TKE_WORKER_NODE_NUM           = 2
	TKE_WORKER_NODE_LABEL_VAL     = "worker"
	TKE_WORKER_ZK_DISK_SIZE       = 10

	TKE_NGINX_INGRESS_RELEASE_NAME = "ingress"

	TKE_HADOOP_YARN_RELEASE_NAME = "hadoop"

	TKE_DELETE_CLUSTER_MODEL                    = "terminate"
	TKE_DELETE_CLUSTER_RESOURCE_CSB             = "CBS"
	TKE_DELETE_CLUSTER_RESOURCE_CDB_DELETE_MODE = "terminate"

	TkeMetaFeatureTypeCrossTenant = "crossTenant"
	EksMetaFeatureBusiness        = "oceanus"

	TkeDeleteClusterInstanceMode = "retain"

	TkeDirectEniResourceName = "tke.cloud.tencent.com/direct-eni"

	TkeSvcIngressName    = "ingress-nginx-ingress-controller"
	SETATS_MANAGER_LABEL = "setats-manager"
	SETATS_WORKER_LABEL  = "setats-worker"

	CORE_SITE_KEY            = "core-site.xml"
	FLINK_CONF_KEY           = "flink-conf.yaml"
	LOG4J_CONSOLE_PROPERTIES = "log4j-console.properties"
	APP_SELECTOR_KEY         = "app"
)

// cluster admin
const (
	ComponentClusterAdmin                  = "cluster-admin"
	HadoopConfigmapName                    = "cluster-admin-hadoop-config"
	LoglistenerConfigmapNameWithStartupLog = "loglistener-config-startuplog"
	LoglistenerConfigmapName               = "loglistener-config"
	ClusterAdminConfigmapName              = "cluster-admin-config"
	ClusterAdminServiceName                = "cluster-admin-service"
	ClusterAdminPort                       = 9003
	ComponentClusterScheduler              = "cluster-scheduler"
	ClusterSchedulerPort                   = 9020
	ClusterSchedulerDatabaseName           = "cluster_scheduler"
	ClusterSchedulerConfigmapName          = "cluster-scheduler-config"
	ClusterSchedulerServiceName            = "cluster-scheduler-service"
)

// user agent
const (
	ComponentUserAgent     = "user-agent"
	UserAgentPort          = 9006
	UserAgentConfigmapName = "user-agent-config"
	UserAgentServiceName   = "user-agent-service"
)

// sql server
const (
	ComponentSqlServer     = "sql-server"
	SqlServerServiceName   = "sql-server-service"
	SqlServerConfigmapName = "sql-server-config"
	SqlServerPort          = 8081
	SqlGatewayServiceName  = "sql-gateway-flink-1-17-rest.default"
	SqlGatewayPort         = 8083
)
const (
	FlinkPort = 8081
)

const (
	ComponentNodeExpansion = "node-expansion"
	NodeExpansionPort      = 443
)

const (
	ComponentSetatsOperator = "setats-operator"
)

const (
	ComponentSetatsHiveOperator = "setats-hive-metastore"
)

// command controller
const (
	ComponentCommandController = "command-controller"
)

const (
	ClsComponents               = "ClsComponents"
	ComponentEnableEniIpReserve = "enableEniIpReserve"
)

// cluster master
const (
	ComponentClusterMaster     = "cluster-master"
	ClusterMasterConfigmapName = "cluster-master-config"
	ClusterMasterServiceName   = "cluster-master-service"
	ClusterMasterPort          = 9005
)

// credentials-provider
const (
	ComponentCredentialsProvider = "credentials-provider"
	CredentialsProviderPort      = 7856
)

// diagnosis data uploader
const (
	ComponentDiagnosisDataUploader     = "diagnosis-data-uploader"
	ComponentDiagnosisDataUploaderPort = 19958
)

// filebeat
const (
	FilebeatConfigMapName = "filebeat-config"
	ComponentFilebeat     = "filebeat"
	ComponentFilebeatPort = 5066

	ComponentFilebeatForSetats     = "filebeat-setats"
	FilebeatConfigMapNameForSetats = "filebeat-config-setats"
)

// task center
const (
	ComponentTaskcenter     = "taskcenter"
	TaskcenterDatabaseName  = "taskcenter"
	TaskcenterConfigmapName = "taskcenter-config"
	TaskcenterServiceName   = "taskcenter-service"
	TaskcenterPort          = 2025
)

// watchdog
const (
	ComponentWatchdog     = "watchdog"
	WatchdogDatabaseName  = "watchdog"
	WatchdogConfigmapName = "watchdog-config"
	WatchdogServiceName   = "watchdog-service"
	WatchdogPort          = 8600
)

// nginx-reverse-proxy
const (
	ComponentNginxReverse = "nginx-reverse-proxy"
	// default.conf
	NginxReverseDefaultConfigmapName = "nginx-config"
	// nginx-conf
	NginxReverseConfigName  = "nginx-conf"
	NginxReverseServiceName = "nginx-reverse-proxy-service"
	NginxReversePort        = 80
	NginxReverseDefaultConf = "default.conf"
	NginxReverseNginxConf   = "nginx.conf"
)

// zookeeper
const (
	ComponentZooKeeper    = "zookeeper"
	ZooKeeperChartVersion = "5.15.1"
	ZooKeeperPort         = 2181
)

const (
	AppContainer         = "app-container"
	LoglistenerContainer = "loglistener"
)

const (
	ComponentLogAgent    = "tke-log-agent" // tke-log-agent DaemonSet 名称
	ContainerLogAgent    = "log-agent"     // tke-log-agent 下的log-agent container
	ContainerLogListener = "loglistener"   // tke-log-agent 下的 loglistener container
)

const (
	TkeImagesNameSpaces = "tkeimages" // TKE 容器镜像命名空间
)

const (
	FlinkVersion    = "Flink-1.11"
	Flinkversion118 = "Flink-1.18"
)

const (
	ConfigMap   = 0
	StatefulSet = 1
	Deployment  = 2
	Service     = 3
	CRD         = 4
	DaemonSet   = 5
)

type Resource struct {
	ResourceName string
	// 0: configmap|1: stateful set|2: deployment
	ResourceType int
	// 集群版本ArchGeneration对应的ResourceType，如果没有找到，默认为ResourceType
	ArchResourceType map[int]int
	// stateful set and deployment has container name
	Containers []string

	ContainerImageNameSpaces []string
}

var (
	// cluster admin
	ClusterAdmin = Resource{
		ResourceName: ComponentClusterAdmin,
		ResourceType: StatefulSet,
		Containers:   []string{AppContainer, LoglistenerContainer},
	}
	ClusterAdminConfigMap = Resource{
		ResourceName: ClusterAdminConfigmapName,
		ResourceType: ConfigMap,
	}
	ClusterAdminHadoopConfigMap = Resource{
		ResourceName: HadoopConfigmapName,
		ResourceType: ConfigMap,
	}
	ClusterAdminStartupLogConfigMap = Resource{
		ResourceName: LoglistenerConfigmapNameWithStartupLog,
		ResourceType: ConfigMap,
	}
	ClusterAdminService = Resource{
		ResourceName: ClusterAdminServiceName,
		ResourceType: Service,
	}
	// cluster master
	ClusterMaster = Resource{
		ResourceName: ComponentClusterMaster,
		ResourceType: StatefulSet,
		Containers:   []string{AppContainer, LoglistenerContainer},
	}
	ClusterMasterConfigMap = Resource{
		ResourceName: ClusterMasterConfigmapName,
		ResourceType: ConfigMap,
	}
	ClusterMasterLogListenerConfigMap = Resource{
		ResourceName: LoglistenerConfigmapName,
		ResourceType: ConfigMap,
	}
	ClusterMasterService = Resource{
		ResourceName: ClusterMasterServiceName,
		ResourceType: Service,
	}
	// task center, has loglistener-config
	TaskCenter = Resource{
		ResourceName: ComponentTaskcenter,
		ResourceType: StatefulSet,
		Containers:   []string{AppContainer, LoglistenerContainer},
	}
	TaskCenterConfigMap = Resource{
		ResourceName: TaskcenterConfigmapName,
		ResourceType: ConfigMap,
	}
	TaskCenterService = Resource{
		ResourceName: TaskcenterServiceName,
		ResourceType: Service,
	}
	// watchdog, has cluster-admin-hadoop-config, loglistener-config
	Watchdog = Resource{
		ResourceName: ComponentWatchdog,
		ResourceType: StatefulSet,
		ArchResourceType: map[int]int{
			TKE_ARCH_GENERATION_V3: Deployment,
		},
		Containers: []string{AppContainer, LoglistenerContainer},
	}
	WatchdogConfigMap = Resource{
		ResourceName: WatchdogConfigmapName,
		ResourceType: ConfigMap,
	}
	WatchdogService = Resource{
		ResourceName: WatchdogServiceName,
		ResourceType: Service,
	}
	// credentials-provider
	CredentialsProvider = Resource{
		ResourceName: ComponentCredentialsProvider,
		ResourceType: StatefulSet,
		ArchResourceType: map[int]int{
			TKE_ARCH_GENERATION_V3: Deployment,
		},
		Containers: []string{ComponentCredentialsProvider},
	}
	// diagnosis-data-uploader
	DiagnosisDataUploader = Resource{
		ResourceName: ComponentDiagnosisDataUploader,
		ResourceType: DaemonSet,
		Containers:   []string{ComponentDiagnosisDataUploader},
	}
	// command-controller
	CommandController = Resource{
		ResourceName: ComponentCommandController,
		ResourceType: StatefulSet,
		ArchResourceType: map[int]int{
			TKE_ARCH_GENERATION_V3: Deployment,
		},
		Containers: []string{AppContainer},
	}
	// metric-provider
	MetricProvider = Resource{
		ResourceName: ComponentMetricProvider,
		ResourceType: StatefulSet,
		Containers:   []string{ComponentMetricProvider},
	}
	// cluster-scheduler
	ClusterScheduler = Resource{
		ResourceName: ComponentClusterScheduler,
		ResourceType: StatefulSet,
		ArchResourceType: map[int]int{
			TKE_ARCH_GENERATION_V3: Deployment,
		},
		Containers: []string{AppContainer, LoglistenerContainer},
	}
	// sql server
	SqlServer = Resource{
		ResourceName: ComponentSqlServer,
		ResourceType: StatefulSet,
		ArchResourceType: map[int]int{
			TKE_ARCH_GENERATION_V3: Deployment,
		},
		Containers: []string{AppContainer, LoglistenerContainer},
	}
	// zookeeper
	Zookeeper = Resource{
		ResourceName: ComponentZooKeeper,
		ResourceType: StatefulSet,
	}
	// TKELogAgent daemonSet tke-log-agent
	TKELogAgent = Resource{
		ResourceName:             ComponentLogAgent,
		ResourceType:             DaemonSet,
		Containers:               []string{ContainerLogAgent, ContainerLogListener},
		ContainerImageNameSpaces: []string{TkeImagesNameSpaces, TkeImagesNameSpaces},
	}

	// user-agent
	UserAgent = Resource{
		ResourceName: ComponentUserAgent,
		ResourceType: StatefulSet,
		Containers:   []string{AppContainer, LoglistenerContainer},
	}

	ResourceMap = map[string]Resource{
		ClusterAdmin.ResourceName:                      ClusterAdmin,
		ClusterAdminConfigMap.ResourceName:             ClusterAdminConfigMap,
		ClusterAdminHadoopConfigMap.ResourceName:       ClusterAdminHadoopConfigMap,
		ClusterAdminStartupLogConfigMap.ResourceName:   ClusterAdminStartupLogConfigMap,
		ClusterAdminService.ResourceName:               ClusterAdminService,
		ClusterMaster.ResourceName:                     ClusterMaster,
		ClusterMasterConfigMap.ResourceName:            ClusterMasterConfigMap,
		ClusterMasterLogListenerConfigMap.ResourceName: ClusterMasterLogListenerConfigMap,
		ClusterMasterService.ResourceName:              ClusterMasterService,
		TaskCenter.ResourceName:                        TaskCenter,
		TaskCenterConfigMap.ResourceName:               TaskCenterConfigMap,
		TaskCenterService.ResourceName:                 TaskCenterService,
		Watchdog.ResourceName:                          Watchdog,
		WatchdogConfigMap.ResourceName:                 WatchdogConfigMap,
		WatchdogService.ResourceName:                   WatchdogService,
		CommandController.ResourceName:                 CommandController,
		SqlServer.ResourceName:                         SqlServer,
		TKELogAgent.ResourceName:                       TKELogAgent,
		MetricProvider.ResourceName:                    MetricProvider,
		ClusterScheduler.ResourceName:                  ClusterScheduler,
		CredentialsProvider.ResourceName:               CredentialsProvider,
		Zookeeper.ResourceName:                         Zookeeper,
		DiagnosisDataUploader.ResourceName:             DiagnosisDataUploader,
		UserAgent.ResourceName:                         UserAgent,
	}
)

// V3 Cluster controller count is 1 or 3
func DefaultControllerNum(ag int, cgType int8) int64 {
	if cgType == CLUSTER_GROUP_TYPE_UNIFORM {
		return TKE_CONTROL_NODE_NUM_UNIFORM
	}
	if ag >= TKE_ARCH_GENERATION_V2 {
		return TKE_CONTROL_NODE_NUM_V3
	}
	return TKE_CONTROL_NODE_NUM
}
