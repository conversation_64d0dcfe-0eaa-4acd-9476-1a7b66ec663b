package constants

const (
	FLOW_OCEANUS_CREATE_CLUSTER         = "oceanus_create_cluster"
	FLOW_OCEANUS_DELETE_CLUSTER         = "oceanus_delete_cluster"
	FLOW_OCEANUS_SCALE_CLUSTER          = "oceanus_scale_cluster"
	FLOW_OCEANUS_ISOLATE_CLUSTER        = "oceanus_isolate_cluster"
	FLOW_OCEANUS_AUTO_SCALE_JOB         = "oceanus_auto_scale"
	FLOW_OCEANUS_UPGRADE_CDB            = "oceanus_upgrade_cdb"
	FLOW_OCEANUS_UPGRADE_CONTROLLER     = "oceanus_upgrade_controller"
	FLOW_OCEANUS_UPGRADE_WORKER         = "oceanus_upgrade_worker"
	FLOW_OCEANUS_RENEW_CLUSTER          = "oceanus_renew_cluster"
	FLOW_OCEANUS_CREATE_SESSION_CLUSTER = "oceanus_create_session_cluster"
	FLOW_OCEANUS_STOP_SESSION_CLUSTER   = "oceanus_stop_session_cluster"

	FLOW_OCEANUS_CREATE_SQL_GATEWAY  = "oceanus_create_sql_gateway"
	FLOW_OCEANUS_STOP_SQL_GATEWAY    = "oceanus_stop_sql_gateway"
	FLOW_OCEANUS_APPLY_CLS_COMPONENT = "oceanus_apply_cls_component"
	FLOW_OCEANUS_IN_PLACE_SCALE_JOB  = "oceanus_in_place_scale"

	FLOW_OCEANUS_APPLY_ES_SERVERLESS_COMPONENT = "oceanus_apply_es_serverless_component"

	FLOW_OCEANUS_CREATE_SETATS          = "oceanus_create_setats"
	FLOW_OCEANUS_START_SETATS_WAREHOUSE = "oceanus_start_setats_warehouse"
	FLOW_OCEANUS_STOP_SETATS_WAREHOUSE  = "oceanus_stop_setats_warehouse"
	FLOW_OCEANUS_RESTART_SETATS         = "oceanus_restart_setats"
	FLOW_OCEANUS_SCALE_SETATS           = "oceanus_scale_setats"
	FLOW_OCEANUS_DELETE_SETATS          = "oceanus_delete_setats"
)

const (
	FLOW_PARAM_STATUS                        = "Status"
	FLOW_PARAM_ERR_MSG                       = "Error"
	FLOW_PARAM_REQUEST_ID                    = "RequestId"
	FLOW_PARAM_CLUSTER_GROUP_ID              = "ClusterGroupId"
	FLOW_PARAM_CLUSTER_ID                    = "ClusterId"
	FLOW_PARAM_ZONE                          = "Zone"
	FLOW_PARAM_TAGS                          = "Tags"
	FLOW_PARAM_KEY_ID                        = "KeyId"
	FLOW_PARAM_VPC_ID                        = "VpcId"
	FLOW_PARAM_SUBNET_ID                     = "SubnetId"
	FLOW_PARAM_INSTANCE_ID                   = "InstanceId"
	FLOW_PARAM_TKE_INSTANCE_ID               = "TkeInstanceId"
	FLOW_PARAM_CDB_INSTANCE_ID               = "CdbInstanceId"
	FLOW_PARAM_INSTANCE_NAME                 = "InstanceName"
	FLOW_PARAM_WORKER_STATUS                 = "WorkerStatus"
	FLOW_PARAM_WORKER_FAILED_TYPE            = "WorkerFailedType"
	FLOW_PARAM_KUBCONFIG_STATUS              = "KubeConfigStatus"
	FLOW_PARAM_TILLER_INSTANCE               = "Tiller"
	FLOW_PARAM_INGRESS_INSTANCE              = "Ingress"
	FLOW_PARAM_LOGCONFIGS_INSTANCE           = "LogConfigs"
	FLOW_PARAM_FLINK_INSTANCE                = "Flink"
	FLOW_PARAM_IN_IT_TOPIC_INDEX             = "InitTopicIndex"
	FLOW_PARAM_ENI_INSTANCE                  = "Eni"
	FLOW_PARAM_ENI_CONTROLLER_WATCH          = "EniControllerWatch"
	FLOW_PARAM_WEBUI_PREFIX                  = "WebUIPrefix"
	FLOW_PARAM_APPEND_CLUSTER_CONF           = "ClusterConfig"
	FLOW_PARAM_NGINX_INGRESS                 = "NginxIngress"
	FLOW_PARAM_NET_CONNECTION_TYPE           = "NetConnectionType"
	FLOW_PARAM_CLUSTER_TYPE                  = "ClusterType"
	FLOW_PARAM_INVOKE_SOURCE                 = "FlowInvokeSource"
	FLOW_INVOKE_FROM_CREATE_COMPONETS        = "CreateComponents"
	FLOW_PARAM_ARCH_GNERATION                = "ArchGeneration"
	FLOW_PARAM_CLUSTERADMIN_DB               = "ClusterAdminDb"
	FLOW_PARAM_WATCHDOG_DB                   = "WatchdogDb"
	FLOW_PARAM_CLUSTERADMIN_IMAGE            = "ClusterAdminImage"
	FLOW_PARAM_SCALE_CLUSTER_TARGET_CU_NUM   = "ScaleTargetCuNum"
	FLOW_PARAM_UPGRADE_CDB_TARGET_MEMORY     = "UpgradeCdbMemory"
	FLOW_PARAM_UPGRADE_CDB_TARGET_VOLUME     = "UpgradeCdbVolume"
	FLOW_PARAM_UPGRADE_CONTROLLER_FINISHED   = "UpgradeControllerFinished"
	FLOW_PARAM_UPGRADE_CONTROLLER_ADDED      = "UpgradeControllerAdded"
	FLOW_PARAM_UPGRADE_CONTROLLER_INSTANCE   = "UpgradeControllerInstance"
	FLOW_PARAM_UPGRADE_CONTROLLER_WORK_NODE  = "UpgradeControllerWorkNode"
	FLOW_PARAM_UPGRADE_CONTROLLER_DELETE     = "UpgradeControllerDelete"
	FLOW_PARAM_UPGRADE_CONTROLLER_DELETE_POD = "UpgradeControllerDeletePod"
	FLOW_PARAM_UPGRADE_CONTROLLER_ZK_POD     = "UpgradeControllerZKPod"
	FLOW_PARAM_UPGRADE_CONTROLLER_DELETE_PVC = "UpgradeControllerDeletePVC"
	FLOW_PARAM_UPGRADE_CONTROLLER_DELETE_PV  = "UpgradeControllerDeletePV"
	FLOW_PARAM_UPGRADE_CONTROLLER_DELETE_ZK  = "UpgradeControllerDeleteZK"
	FLOW_PARAM_UPGRADE_CONTROLLER_ZK_CHECK   = "UpgradeControllerZKCheck"
	FLOW_PARAM_UPGRADE_WORKER_FINISHED       = "UpgradeWorkerFinished"
	FLOW_PARAM_UPGRADE_WORKER_ADDED          = "UpgradeWorkerAdded"
	FLOW_PARAM_UPGRADE_WORKER_INSTANCE       = "UpgradeWorkerInstance"
	FLOW_PARAM_UPGRADE_WORKER_ALL_INSTANCE   = "UpgradeWorkerALLInstance"
	FLOW_PARAM_UPGRADE_WORKER_NEW_INSTANCE   = "UpgradeWorkerNewInstance"
	FLOW_PARAM_UPGRADE_WORKER_DELETE         = "UpgradeWorkerDelete"
	FLOW_PARAM_CLUSTERSCHEDULER_IMAGE        = "ClusterSchedulerImage"
	FLOW_PARAM_WATCHDOG_IMAGE                = "WatchdogImage"
	FLOW_PARAM_CLUSTERSCHEDULER_DB           = "ClusterSchedulerDb"
	FLOW_PARAM_USERAGENT_IMAGE               = "UserAgentImage"
	FLOW_PARAM_ISOLATE_JOB_IDS               = "IsolateJobIds"
	FLOW_PARAM_ISOLATE_JOB_TIME              = "IsolateJobTime"

	FlowParamCommandControllerImage = "CommandControllerImageName"

	FlowParamDeleteTkeInstanceSucc     = "DeleteTkeInstanceSucc"
	FlowParamDeleteTkeInstanceFailed   = "DeleteTkeInstanceFailed"
	FlowParamDeleteTkeInstanceNotFound = "DeleteTkeInstanceNotFound"
	FlowParamDiskFromCVM               = "DiskFromCVM"

	FlowParamPoolTkeInstanceSucc   = "PoolTkeInstanceSucc"
	FlowParamPoolTkeInstanceFailed = "PoolTkeInstanceFailed"

	FlowParamNeedToDeleteCvm = "NeedToDeleteCvm"
	FlowParamUnSchedulerCvm  = "UnSchedulerCvm"
	FlowParamDriveJobCvm     = "DriveJobCvm"
	FlowParamAddLabelCvm     = "AddLabelCvm"
	FlowParamSchedulerCvm    = "SchedulerCvm"

	FlowParamCanTryCvm       = "CanTryCvm"
	FlowParamNoJobCvm        = "NoJobCvm"
	FlowParamConfirmNoJobCvm = "ConfirmNoJobCvm"

	FlowParamTryCvm               = "TryCvm"
	FlowParamModifyVpcMap         = "ModifyVpcMap"
	FlowParamAddInstanceMap       = "AddInstanceMap"
	FlowParamCompletedInstanceMap = "CompletedInstanceMap"

	FLOW_PARAM_FLOW_ACTION            = "FlowAction"
	FLOW_PARAM_FLOW_ACTION_CREATE_CVM = "FlowActionCreateCvm"

	FlowParamNeedCvmCount = "NeedCvmCount"

	FLOW_PARAM_SETATS_MASTER_DISK_TYPE                 = "MasterDiskType"
	FLOW_PARAM_SETATS_MASTER_DISK_SIZE                 = "MasterDiskSize"
	FLOW_PARAM_SETATS_WORKER_DISK_TYPE                 = "WorkerDiskType"
	FLOW_PARAM_SETATS_WORKER_DISK_SIZE                 = "WorkerDiskSize"
	FLOW_PARAM_SETATS_WORKERDEFAULTPARALLELISM         = "WorkerDefaultParallelism"
	FLOW_PARAM_SETATS_MASTERCPU                        = "MasterCpu"
	FLOW_PARAM_SETATS_WORKERCPU                        = "WorkerCpu"
	FLOW_PARAM_SETATS_SERIALID                         = "SetatsSerialId"
	FLOW_RETURN_PARAM_SETATS_SCALE_DISK_INVOCATION_IDS = "SetatsScaleDiskInvocationIds"
	FLOW_PARAM_SETATS_TARGET_MASTER_DISK_SIZE          = "TargetMasterDiskSize"
	FLOW_PARAM_SETATS_TARGET_WORKER_DISK_SIZE          = "TargetWorkerDiskSize"
	FLOW_RETURN_PARAM_SETATS_SCALE_DISK_INSTANCE_IDS   = "SetatsScaleDiskInstanceIds"
	FLOW_PARAM_SETATS_SCALE_DISK                       = "isScaleDisk"
	FLOW_PARAM_SETATS_SCALE_WORKER                     = "isScaleWorker"
	FLOW_PARAM_SETATS_IS_CLUSTER                       = "isCluster"
	FLOW_PARAM_SETATS_REFS                             = "SetatsRefs"
)

const (
	TKE_HELM_STATUS_DEPLOYED          = "deployed"
	TKE_HELM_STATUS_UNINSTALLED       = "uninstalled"
	TKE_HELM_STATUS_SUPERSEDED        = "superseded"
	TKE_HELM_STATUS_FAILED            = "failed"
	TKE_HELM_STATUS_UNINSTALLING      = "uninstalling"
	TKE_HELM_STATUS_PENDING_INSTALL   = "pending-install"
	TKE_HELM_STATUS_PENDING_UPGRADE   = "pending-upgrade"
	TKE_HELM_STATUS_PENDING_ROLLBACK  = "pending-rollback"
	TKE_HELM_STATUS_PENDING_UNINSTALL = "pending-uninstall"
	TKE_HELM_STATUS_UNKNOWN           = "unknown"
	TKE_HELM_STATUS_PARAMS_ERROR      = "params-error"
)

const (
	NGINX_INGRESS_CONTROLLER_LB_ID = "NginxIngressControllerLbId"
)

// session集群常量
const (
	FLOW_PARAM_JMCPU                  = "JobManagerCpu"
	FLOW_PARAM_JMMEM                  = "JobManagerMem"
	FLOW_PARAM_TMCPU                  = "TaskManagerCpu"
	FLOW_PARAM_TMMEM                  = "TaskManagerMem"
	FLOW_PARAM_FLINK_VERSION          = "FlinkVersion"
	FLOW_PARAM_CLUSTER_GROUP_SERIALID = "ClusterGroupSerialId"
	FLOW_PARAM_JMCUSPEC               = "JmCuSpec"
	FLOW_PARAM_DEFAULT_JMCUSPEC       = "1.000000"
	FLOW_PARAM_JM_LABELS              = "JmLabels"
	FLOW_PARAM_TMCUSPEC               = "TmCuSpec"
	FLOW_PARAM_TMNUM                  = "TmNum"
	FLOW_PARAM_DEFAULT_TMNUM          = "1"
	FLOW_PARAM_DEFAULT_TMCUSPEC       = "1.000000"
	FLOW_PARAM_TM_LABELS              = "TmLabels"
	FLOW_PARAM_APPID                  = "AppId"
	FLOW_PARAM_OWNERUIN               = "OwnerUin"
	FLOW_PARAM_CREATORUIN             = "CreatorUin"
	FLOW_PARAM_TYPE                   = "type"
	FLOW_PARAM_APP                    = "app"
	FLOW_PARAM_REGION                 = "Region"
	FLOW_PARAM_PROPERTIES             = "Properties"
	FLOW_PARAM_IMAGE_PULL_POLICY      = "IfNotPresent"
	// 新架构的1cu BaseMemory
	OneCUMemory1 = 3900
	// 老架构的1cu BaseMemory
	OneCUMemory2                       = 4096
	ClusterLabelFormat1                = "%s:%d"
	ClusterLabelFormat2                = "%s:%s"
	ClusterLabelComponent              = "component"
	ClusterJobmanager                  = "jobmanager"
	ClusterTaskManager                 = "taskmanager"
	ClusterSessionAPPTemplate          = "%s-session-%s"
	ClusterSessionFlinkConfigTemplate  = "%s-session-flinkconfig-%s"
	ClusterSessionHadoopConfigTemplate = "%s-session-hadoopconfig-%s"
	ClusterSessionJMTemplate           = "%s-session-%s-jobmanager"
	ClusterSessionTMTemplate           = "%s-session-%s-taskmanager"
	ClusterSessionType                 = "flink-standalone-kubernetes"
)

const (
	CONTROLLER_CVM_ORDER_ID = "ControllerOrderId"
	WORKER_CVM_ORDER_ID     = "WorkerOrderId"
	CDB_ORDER_ID            = "CDBOrderId"
)

// sql-gateway
const (
	FLOW_PARAM_GATEWAY_FLINK_VERSION = "FlinkVersion"
	FLOW_PARAM_GATEWAY_SERIAL_ID     = "GatewaySerialId"
	FLOW_PARAM_CU_SPEC               = "CuSpec"
	FLOW_PARAM_REFS                  = "Refs"
	FLOW_PARAM_REFS_PATH             = "RefsPath"
	FLOW_PARAM_CPU_SPEC              = "Cpu"
	FLOW_PARAM_MEM_SPEC              = "Mem"
)
const (
	FLOW_PARAM_ES_SERVERLESS_DIID               = "EsServerlessDiId"
	FLOW_PARAM_ES_SERVERLESS_INDEXID            = "EsServerlessIndexId"
	FLOW_PARAM_ES_SERVERLESS_WORKSPACE_ID       = "EsServerlessWorkspaceId"
	FLOW_PARAM_ES_SERVERLESS_FILEBEAT_NAME      = "EsServerlessFilebeatNames"
	FLOW_PARAM_ES_SERVERLESS_FILEBEAT_NAMESPACE = "default"

	ES_SERVERLESS_INNER_PRODUCT_PUBLIC_CLOUD uint64 = 12203
	ES_SERVERLESS_INNER_PRODUCT_YUNTI        uint64 = 12201
)
