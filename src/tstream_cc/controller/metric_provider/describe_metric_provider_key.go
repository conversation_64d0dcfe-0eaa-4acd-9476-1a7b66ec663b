package metric_provider

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/metric_provider"
)

func init() {
	const controllerName = "qcloud.galileo.tmetricprovider.describeMetricKey"
	if code, msg := httpserver.RegisterController(controllerName, &describeMetricProviderKeyController{}); code != 0 {
		logger.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type describeMetricProviderKeyReq struct {
	UserName string `json:"userName"`
	Password string `json:"password"`
}

type describeMetricKeyRsp struct {
	KeyId     string
	KeySecret string
}

type describeMetricProviderKeyController struct {
}

func (d describeMetricProviderKeyController) CreateRequestObj() interface{} {
	return &describeMetricProviderKeyReq{}
}

func (d describeMetricProviderKeyController) Process(req interface{}, eventId int64) (mcErrorCode int64, msg string, rsp interface{}) {
	reqData := req.(*describeMetricProviderKeyReq)
	retCode, retMsg, err, keyId, keySecret := metric_provider.DescribeKey(reqData.UserName, reqData.Password)
	if len(retMsg) > 0 {
		return retCode, retMsg, nil
	}

	if err != nil {
		logger.Errorf("Failed to describe key, with req:%+v, with errors:%+v", reqData, err)
		return constants.SYSERR, retMsg, nil
	}

	resp := &describeMetricKeyRsp{keyId, keySecret}

	return constants.SUCCESS, constants.OK, resp
}
