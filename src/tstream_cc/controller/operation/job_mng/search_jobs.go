package job_mng

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/component"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"
	jobModel "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job"
	job "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/region"
)

func init() {
	const controllerName = "qcloud.galileo.operation.searchJobs"
	if code, msg := httpserver.RegisterController(controllerName, &searchJobController{}); code != 0 {
		logger.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type searchJobReq struct {
	AppId     int32  `check:"nullable:false|range:(0,INF)"`
	UserAppId int32  `check:"nullable:true|range:(0,INF)"`
	RegionId  int    `check:"nullable:false|range:(0,INF)"`
	Status    string `check:"nullable:true|strlen:(0,50)"`
	PageNo    int    `check:"nullable:false|range:(0,INF)"`
	PageSize  int    `check:"nullable:false|range:(0,INF)"`
	KeyWord   string `check:"nullable:true|strlen:(0,50)"`
	Type      string `check:"nullable:true|strlen:(0,50)"`
	ClientIP  string `check:"nullable:false|strlen:(0,50)"`
}

type JobPart struct {
	JobSerialId             string `json:"serialId"`
	JobConfigId             int64  `json:"jobConfigId"`
	JobConfigVersionId      int16  `json:"versionId"`
	JobName                 string `json:"name"`
	Status                  string `json:"status"`
	Cu                      int16  `json:"cu"`
	DefaultParallelism      int16  `json:"defaultParallelism"`
	InstanceRunningDuration int64  `json:"instanceRunningDuration"`
	RunningTotalDuration    int64  `json:"runningTotalDuration"`
	Type                    string `json:"type"`
	UserAppId               int32  `json:"appId"`
	JobStartTime            int64  `json:"startTime"`
}

type searchJobResp struct {
	Total  int        `json:"total"`
	JobSet []*JobPart `json:"jobSet"`
}

type searchJobController struct{}

func (this *searchJobController) CreateRequestObj() interface{} {
	return &searchJobReq{}
}

func (this *searchJobController) Process(req interface{}, eventId int64) (int64, string, interface{}) {
	reqData := req.(*searchJobReq)
	account := component.NewComponentAccount(reqData.RegionId)
	OwnerUin, err := account.GetOwnerUinByAppId(int64(reqData.AppId))
	if err != nil {
		logger.Errorf("Failed to get owner uin by appId: %d, with errors:%+v", reqData.AppId, err)
		return controller.ERROR_CODE_ACCOUNT_QUERY_FAILURE, err.Error(), nil
	}
	regionName, err := region.GetRegionNameById(reqData.RegionId)
	if err != nil {
		logger.Errorf("Failed to get region name by id, with region id:%d", reqData.RegionId)
		return controller.ERROR_CODE_REGION_QUERY_FAILURE, err.Error(), nil
	}

	offset := (reqData.PageNo - 1) * reqData.PageSize
	var despJobsReq *jobModel.DescribeJobsReq
	if len(reqData.KeyWord) != 0 {
		filters := []struct {
			Name   string
			Values []string
		}{
			{"Name", []string{reqData.KeyWord}},
		}
		despJobsReq = &jobModel.DescribeJobsReq{
			RequestBase: apiv3.RequestBase{
				AppId: int64(reqData.UserAppId), Uin: OwnerUin, SubAccountUin: "Operation System Administrator", RequestId: "", Region: regionName, Version: "1.0",
			},
			Offset: offset, Limit: reqData.PageSize, Filters: filters}
	} else if len(reqData.Status) != 0 {
		jobStatus := job.GetStatusByStatusDesc(reqData.Status)
		jobStatusStr := strconv.Itoa(int(jobStatus))
		filters := []struct {
			Name   string
			Values []string
		}{
			{"Status", []string{jobStatusStr}},
		}
		despJobsReq = &jobModel.DescribeJobsReq{
			RequestBase: apiv3.RequestBase{
				AppId: int64(reqData.UserAppId), Uin: OwnerUin, SubAccountUin: "Operation System Administrator", RequestId: "", Region: regionName, Version: "1.0",
			},
			Offset: offset, Limit: reqData.PageSize, Filters: filters}
	} else {
		despJobsReq = &jobModel.DescribeJobsReq{
			RequestBase: apiv3.RequestBase{
				AppId: int64(reqData.UserAppId), Uin: OwnerUin, SubAccountUin: "Operation System Administrator", RequestId: "", Region: regionName, Version: "1.0",
			},
			Offset: offset, Limit: reqData.PageSize}
	}

	errCode, msg, jobs := job.DoDescribeJobs(despJobsReq)
	if errCode != controller.OK {
		logger.Errorf("Failed to DescribeJobs, with appId:%d, with error code %s, msg: %s", reqData.AppId, errCode, msg)
		return controller.ERROR_CODE_JOB_QUERY_FAILURE, msg, nil
	}
	if len(jobs.JobSet) == 0 {
		logger.Warningf("No job found, with appId:%d, with req:%+v", reqData.AppId, req)
		return controller.SUCCESS, "ok", jobs
	}
	jobSet := make([]*JobPart, 0)
	jobIds := make([]string, 0)
	for i := 0; i < len(jobs.JobSet); i++ {
		jobInfo := jobs.JobSet[i]
		jobIds = append(jobIds, jobInfo.JobId)
	}
	resultMap, err := job.GetJobInstanceRunningDuration(jobIds)
	if err != nil {
		logger.Errorf("Failed to get job instance running durations, with errors:%+v", err)
		return controller.ERROR_CODE_JOB_INSTANCE_QUERY_FAILURE, err.Error(), nil
	}
	for i := 0; i < len(jobs.JobSet); i++ {
		jobItem := jobs.JobSet[i]
		status := "running"
		if jobItem.Status == constants.JOB_STATUS_CREATE {
			status = "create"
		} else if jobItem.Status == constants.JOB_STATUS_STOPPED {
			status = "stopped"
		} else if jobItem.Status == constants.JOB_STATUS_FINISHED {
			status = "finished"
		} else if jobItem.Status == constants.JOB_STATUS_CONCERNING {
			status = "concerning"
		} else if jobItem.Status == constants.JOB_STATUS_INITIALIZED {
			status = "initialized"
		} else if jobItem.Status == constants.JOB_STATUS_PAUSED {
			status = "paused"
		} else if jobItem.Status == constants.JOB_STATUS_PROGRESS {
			status = "progress"
		}
		jobType := "sql"
		if jobItem.JobType == constants.JOB_TYPE_JAR {
			jobType = "jar"
		}
		if _, ok := resultMap[jobItem.JobId]; ok {
			runningDurationInfo := resultMap[jobItem.JobId]
			instanceRunningDuration := runningDurationInfo[0]
			totalRunningDuration := runningDurationInfo[1]
			instanceStartTime := runningDurationInfo[2]
			jobPart := &JobPart{JobSerialId: jobItem.JobId, JobConfigId: 0, JobConfigVersionId: 0, JobName: jobItem.Name,
				Status: status, Cu: jobItem.RunningCuNum, InstanceRunningDuration: int64(instanceRunningDuration), RunningTotalDuration: totalRunningDuration, Type: jobType,
				UserAppId: reqData.UserAppId, JobStartTime: instanceStartTime}
			jobSet = append(jobSet, jobPart)
		} else {
			jobPart := &JobPart{JobSerialId: jobItem.JobId, JobConfigId: 0, JobConfigVersionId: 0, JobName: jobItem.Name,
				Status: status, Cu: jobItem.RunningCuNum, InstanceRunningDuration: 0, RunningTotalDuration: 0, Type: jobType,
				UserAppId: reqData.UserAppId, JobStartTime: 0}
			jobSet = append(jobSet, jobPart)
		}
	}

	resp := &searchJobResp{}
	resp.Total = jobs.TotalCount
	resp.JobSet = jobSet
	return controller.SUCCESS, "ok", resp
}
