package job_mng

import (
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/region"
)

func init() {
	const controllerName = "qcloud.galileo.operation.listRegion"
	if code, msg := httpserver.RegisterController(controllerName, &listRegionController{}); code != 0 {
		logger.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type listRegionReq struct {
	AppId 			int64 					`check:"nullable:false|range:(0,INF)"`
	ClientIP 		string 					`check:"nullable:false|strlen:(0,50)"`
}

type RegionInfo struct {
	Id  		   int64 				`json:"regionId"`
	En_name		   string 				`json:"en_name"`
	Zh_name 	   string 				`json:"zh_name"`
	State 		   int					`json:"state"`
}

type listRegionResp struct {
	Total 			int 				`json:"total"`
	Regions 		[]*RegionInfo 		`json:"regions"`
}

type listRegionController struct {}

func (this * listRegionController) CreateRequestObj() interface{}{
	return &listRegionReq{}
}

func (this * listRegionController) Process(req interface{},eventId int64) (int64,string,interface{}) {

	regionList, err := region.ListVPCRegion()
	if err != nil {
		logger.Errorf("Failed to query region infos, with errors:%+v", err)
		return controller.ERROR_CODE_REGION_QUERY_FAILURE, err.Error(),nil
	}
	regions := make([]*RegionInfo,0)
	for i:=0;i<len(regionList);i++ {
		region := regionList[i]
		regionInfo := &RegionInfo{Id:int64(region.RegionId),En_name:region.Region,Zh_name:region.RegionName, State:region.State}
		regions = append(regions, regionInfo)
	}
	resp := &listRegionResp{Total:len(regionList),Regions:regions}
	return controller.SUCCESS,"ok",resp
}