package job_mng

import (
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/component"
	"strconv"
	"strings"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	jobModel "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job"
	common "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	job "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/region"
)

func init() {
	const controllerName = "qcloud.galileo.operation.startJob"
	if code, msg := httpserver.RegisterController(controllerName, &startJobController{}); code != 0 {
		logger.Error("Failed to register controller " + controller<PERSON>ame + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}
type startJobReq struct {
	AppId 			int32 				`check:"nullable:false|range:(0,INF)"`
	SerialId		string				`check:"nullable:false|strlen:(0,50)"`
	IsResume		bool
	StartMode 		string				`check:"nullable:false|strlen:(0,50)"`
	ClientIP 		string				`check:"nullable:false|strlen:(0,50)"`
}
type startJobResp struct {
	SerialId 		string 				`json:"serialId"`
	IsSucc			string				`json:"isSucc"`
}

type startJobController struct {}

func (this * startJobController) CreateRequestObj() interface{} {
	return &startJobReq{}
}

func (this * startJobController) Process(req interface{},eventId int64)(int64, string, interface{}) {
	reqData := req.(*startJobReq)
	startMode := reqData.StartMode
	if startMode != "Normal" && startMode != "Resume" && startMode != "LATEST" && !strings.HasPrefix(startMode,"T") {
		logger.Errorf("Such job startMode:%s is not supported ", startMode)
		return controller.PARAMSERR, "StartMode not support", nil
	}
	listJobQuery := jobModel.ListJobQuery{
		AppId:             0,
		SerialIds:         []string{reqData.SerialId},
		IsVagueNames:      false,
		Offset:            0,
		Limit:             20,
	}
	jobs, err := common.ListJobs(&listJobQuery)
	if err != nil {
		logger.Errorf("Failed to job with serialId, with serialId:%s, with appId:%d, with errors:%+v", reqData.SerialId, reqData.AppId,err)
		return controller.ERROR_CODE_JOB_QUERY_FAILURE, err.Error(),nil
	}
	if len(jobs) == 0 {
		logger.Errorf("Failed to get job with serialId: %s, as query size is 0", reqData.SerialId)
		return controller.ERROR_CODE_JOB_QUERY_FAILURE, err.Error(), nil
	}
	jobData := jobs[0]
	regionId, err := region.GetRegionIdByName(jobData.Region)
	if err != nil {
		logger.Errorf("Failed to get Region id by Name: %s, with errors:%+v", jobData.Region, err)
	}
	account := component.NewComponentAccount(regionId)
	ownerUin,err := account.GetOwnerUinByAppId(int64(reqData.AppId))
	if err != nil {
		logger.Errorf("Failed to get ownerUin by appId, with appId:%d, with errors:%+v", reqData.AppId, err)
		return controller.ERROR_CODE_ACCOUNT_QUERY_FAILURE, err.Error(), nil
	}
	var runJobsDesp jobModel.RunJobDescription
	if reqData.IsResume {
		runJobsDesp = jobModel.RunJobDescription{JobId: reqData.SerialId, RunType: constants.JOB_RUN_TYPE_RESUME, StartMode: reqData.StartMode}
	} else {
		runJobsDesp = jobModel.RunJobDescription{JobId: reqData.SerialId, RunType: constants.JOB_RUN_TYPE_RUN, StartMode: reqData.StartMode}
	}
	runJobsDespArray := make([]jobModel.RunJobDescription, 0)
	runJobsDespArray = append(runJobsDespArray, runJobsDesp)
	jobRunReq := &jobModel.RunJobsReq{AppId:jobData.AppId, Uin: ownerUin, SubAccountUin: "Operation Platform Administrator", RequestId:"", Region: jobData.Region, Version: "1.0",
		RunJobDescriptions: runJobsDespArray}
	errCode, msg, _ := job.DoRunJobs(jobRunReq, false)
	if errCode != controller.OK {
		logger.Errorf("Failed to start job, with serialId:%d, with appId:%d, with error msg :%s", reqData.SerialId, reqData.AppId, msg)
		return controller.ERROR_CODE_JOB_START_FAILURE, "Failed to start job", nil
	}

	resp := &startJobResp{SerialId: reqData.SerialId, IsSucc:"Yes"}
	return controller.SUCCESS,"ok",resp
}
