package job_mng

import (
	"encoding/json"
	"strconv"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job_config"
	regionService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/region"
	tableService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
)

func init() {
	const controllerName = "qcloud.galileo.operation.queryJobSourceType"
	if code, msg := httpserver.RegisterController(controllerName, &queryJobSourceTypeController{}); code != 0 {
		logger.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type queryJobSourceTypeReq struct {
	AppId    int64  `check:"nullable:false|range:(0,INF)"`
	ClientIP string `check:"nullable:false|strlen:(0,50)"`
	SerialId string `check:"nullable:false|strlen:(0,50)"`
}

type queryJobSourceTypeResp struct {
	HasOldCkafka bool   `json:"hasOldCkafka"`
	SerialId     string `json:"serialId"`
}

type queryJobSourceTypeController struct {
}

func (this *queryJobSourceTypeController) CreateRequestObj() interface{} {
	return &queryJobSourceTypeReq{}
}

func (this *queryJobSourceTypeController) Process(req interface{}, eventId int64) (int64, string, interface{}) {
	reqData := req.(*queryJobSourceTypeReq)
	job, err := service.FindJobBySerialID(reqData.SerialId)
	if err != nil {
		logger.Errorf("Failed to get job by serialId, with serialId:%s, with appId:%d,with errors:%+v", reqData.SerialId, reqData.AppId, err)
		return controller.ERROR_CODE_JOB_QUERY_FAILURE, err.Error(), nil
	}
	if job.Type == constants.JOB_TYPE_JAR {
		return controller.SUCCESS, "ok", &queryJobSourceTypeResp{HasOldCkafka: false, SerialId: reqData.SerialId}
	}
	jobConfigIds := make([]int64, 0)
	jobConfigIds = append(jobConfigIds, job.PublishedJobConfigId)
	jobConfigs, err := service3.ListJobConfigs(job.Id, jobConfigIds, []int64{}, nil)
	if err != nil {
		logger.Errorf("Failed to get job configs, with appId:%d, with serialId:%s, with errors:%+v", reqData.AppId, reqData.SerialId, err)
		return controller.ERROR_CODE_JOB_CONFIG_QUERY_FAILURE, err.Error(), nil
	}
	if len(jobConfigs) == 0 {
		logger.Errorf("Failed to get job configs, with query size is 0, with serialId:%d, with appId:%d", reqData.SerialId, reqData.AppId)
		return controller.ERROR_CODE_JOB_CONFIG_QUERY_NO_RESULT, "Query no result", nil
	}
	jobConfig := jobConfigs[0]
	regionID, err := regionService.GetRegionIdByName(job.Region)
	if err != nil {
		logger.Errorf("Failed to get the regionID from region service region:%s, with errors:%+v", job.Region, err)
		return controller.SYSERR, "Get Region service Failed", nil
	}
	sqlCode, err := service3.DeEncodeAndDeEncryptSqlCode(jobConfig.SqlCode)
	if err != nil {
		logger.Errorf("Failed to decode and decrypt SqlCode: %s", jobConfig.SqlCode)
		return controller.SYSERR, "Failed to decode and decrypt SqlCode", nil
	}
	result, err := tableService.CheckTableSQLGrammar(reqData.SerialId, regionID, job.AppId, sqlCode, constants.SQL_CHECK_PATH)
	if err != nil {
		logger.Errorf("Failed to check table grammar, with errors:%+v", err)
		return controller.ERROR_CODE_GRAMMAR_CHECK_FAILURE, "Grammar check error", nil
	}
	result = strings.Replace(result, "\\", "", -1)
	result = result[1 : len(result)-1]
	type GrammarCheckResult struct {
		PassCheck    bool        `json:"passCheck"`
		Errors       interface{} `json:"errors"`
		Warnings     interface{} `json:"warnings"`
		HasOldCkafka bool        `json:"hasOldCkafka"`
	}
	var checkResult GrammarCheckResult
	err = json.Unmarshal([]byte(result), &checkResult)
	if err != nil {
		logger.Errorf("Failed to unmarshal json data, with appId:%d, with json string:%s, with errors;%+v", reqData.AppId, result, err)
		return controller.SYSERR, err.Error(), nil
	}
	if !checkResult.PassCheck {
		logger.Errorf("The job sql has syntax errors, please notify the users to modify the job sql.")
		return controller.ERROR_CODE_GRAMMAR_CHECK_FAILURE, "job sql syntax errors", nil
	} else {
		resp := &queryJobSourceTypeResp{HasOldCkafka: checkResult.HasOldCkafka, SerialId: reqData.SerialId}
		return controller.SUCCESS, "ok", resp
	}
}
