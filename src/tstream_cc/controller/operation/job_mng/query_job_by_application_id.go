package job_mng

import (
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
)

func init() {
	const controllerName = "qcloud.galileo.operation.queryJobByApplicationId"
	if code, msg := httpserver.RegisterController(controllerName, &queryJobByApplicationIdController{}); code != 0 {
		logger.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type queryJobByApplicationIdReq struct {
	ApplicationId		string 			`check:"nullable:false|strlen:(0,50)"`
}


type queryJobByApplicationIdController struct {}


func (this * queryJobByApplicationIdController) CreateRequestObj() interface{} {
	return &queryJobByApplicationIdReq{}
}

func (this * queryJobByApplicationIdController) Process(req interface{}, eventId int64) (int64, string,interface{}){
	reqData := req.(*queryJobByApplicationIdReq)

	job, err := service.FindJobByApplicationID(reqData.ApplicationId)
	if err != nil {
		logger.Errorf("Failed to get job info, with applicationId:%s, with errors:%+v", reqData.ApplicationId, err)
		return controller.ERROR_CODE_JOB_QUERY_FAILURE , err.Error(), nil
	}
	return controller.SUCCESS, "ok", job
}



