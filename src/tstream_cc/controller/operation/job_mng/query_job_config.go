package job_mng

import (
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"encoding/json"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job_config"
)

func init() {
	const controllerName = "qcloud.galileo.operation.queryJobConfig"
	if code, msg := httpserver.RegisterController(controllerName, &queryJobConfigController{}); code != 0 {
		logger.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type queryJobConfigReq struct {
	AppId 			int32 				`check:"nullable:false|range:(0,INF)"`
	SerialId		string 				`check:"nullable:false|strlen:(0,50)"`
	ClientIP		string 				`check:"nullable:false|strlen:(0,50)"`
}

type queryJobConfigResp struct {
	Status    	 	string 				`json:"status"`
	JobConfigId 	int64 				`json:"jobConfigId"`
	JobConfigInfo 	string 				`json:"jobConfigInfo"`
}

type queryJobConfigController struct {}


func (this * queryJobConfigController) CreateRequestObj() interface{} {
	return &queryJobConfigReq{}
}

func (this * queryJobConfigController) Process(req interface{}, eventId int64) (int64, string,interface{}){
	reqData := req.(*queryJobConfigReq)
	job, err := service.FindJobBySerialID(reqData.SerialId)
	if err != nil {
		logger.Errorf("Failed to get job info, with serialId:%s, with appId:%d, with errors:%+v", reqData.SerialId, reqData.AppId, err)
		return controller.ERROR_CODE_JOB_QUERY_FAILURE , err.Error(), nil
	}

	jcs, err := service3.ListJobConfigs(job.Id, nil, []int64{}, nil)
	if err != nil {
		logger.Errorf("Failed to get job config ,with appId:%d, with errors;%+v", reqData.AppId, err)
		return controller.ERROR_CODE_JOB_CONFIG_QUERY_FAILURE, err.Error(), nil
	}
	if len(jcs) == 0 {
		logger.Errorf("Failed to get job config, as query job config size is 0, with appId:%d", reqData.AppId)
		return controller.ERROR_CODE_JOB_CONFIG_QUERY_NO_RESULT, err.Error(), nil
	}
	jc := jcs[0]
	if job.PublishedJobConfigId != 0 {
		for i := 0; i < len(jcs); i ++ {
			jc = jcs[i]
			if jc.Id == job.PublishedJobConfigId {
				break
			} else {
				continue
			}
		}
	}
	jc.SqlCode = ""
	dataBytes, err := json.Marshal(jc)
	if err != nil {
		logger.Errorf("Failed to marshal job config into string bytes, with jobconfig:%+v, with errors:%+v", jc, err)
		return controller.SYSERR, err.Error(),nil
	}
	resp := &queryJobConfigResp{Status:"progress",JobConfigId:jc.Id,JobConfigInfo: string(dataBytes)}
	return controller.SUCCESS, "ok", resp
}



