package job_mng

import (
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	. "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job"
	common "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
)

func init() {
	const controllerName = "qcloud.galileo.operation.listJob"
	if code, msg := httpserver.RegisterController(controllerName, &ListJobController{}); code != 0 {
		logger.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type listJobReq struct {
	AppId 			int32 					`check:"nullable:false|range:(0,INF)"`
	SerialId		string					`check:"nullable:false|strlen:(0,50)"`
	Name 			string 					`check:"nullable:false|strlen:(0,50)"`
	ClientIP 		string 					`check:"nullable:false|strlen:(0,50)"`
}


type ListJobController struct {}

func (this * ListJobController) CreateRequestObj() interface{} {
	return &listJobReq{}
}

func (this * ListJobController) Process(req interface{},eventId int64) (int64, string, interface{}) {
	reqData := req.(*listJobReq)
	listJobQuery := ListJobQuery{
		AppId:             reqData.AppId,
		SerialIds:         []string{reqData.SerialId},
		IsVagueNames:      false,
		Offset:            -1,
		Limit:             -1,
	}
	jobs,err := common.ListJobs(&listJobQuery)
	if err != nil {
		logger.Error("Failed to ListJob, AppId:", reqData.AppId, ",errors: ", err.Error())
		return controller.ERROR_CODE_JOB_QUERY_FAILURE, err.Error(), nil
	}
	return controller.SUCCESS, "ok", jobs
}




