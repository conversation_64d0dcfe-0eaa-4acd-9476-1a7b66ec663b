package job_mng


import (
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job_config"

)

func init() {
	const controllerName = "qcloud.galileo.operation.queryJobSQL"
	if code, msg := httpserver.RegisterController(controllerName, &queryJobSQLController{}); code != 0 {
		logger.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type queryJobSQLReq struct{
	AppId   		int64 			`check:"nullable:false|range:(0,INF)"`
	SerialId 		string			`check:"nullable:false|strlen:(0,50)"`
	ClientIP		string 			`check:"nullable:false|strlen:(0,50)"`
}

type queryJobSQLResp struct {
	JobConfigId 	int64 			`json:"jobConfigId"`
	Status  		string			`json:"status"`
	JobSQL       	string			`json:"jobSQL"`
}

type queryJobSQLController struct {}

func (this * queryJobSQLController) CreateRequestObj() interface{} {
	return &queryJobSQLReq{}
}

func (this * queryJobSQLController) Process(req interface{}, eventId int64) (int64, string ,interface{}) {
	reqData := req.(*queryJobSQLReq)
	job, err := service.FindJobBySerialID(reqData.SerialId)
	if err != nil {
		logger.Errorf("Failed to get job, with serialId:%s, with appId:%d, with errors:%+v", reqData.SerialId, reqData.AppId, err)
		return controller.ERROR_CODE_JOB_QUERY_FAILURE, err.Error(),nil
	}
	jobId := job.Id
	jobConfigs, err := service3.ListJobConfigs(jobId, nil, []int64{}, nil)
	if err != nil {
		logger.Errorf("Failed to get job config ,with appId:%d, with errors:%+v", reqData.AppId, err)
		return controller.ERROR_CODE_JOB_CONFIG_QUERY_FAILURE, err.Error(),nil
	}
	if len(jobConfigs) == 0 {
		logger.Errorf("The job: %s do not has state job config", reqData.SerialId)
		return controller.ERROR_CODE_JOB_CONFIG_QUERY_NO_RESULT,"no job config found", nil
	}
	jc := jobConfigs[0]
	if job.PublishedJobConfigId != 0 {
		for i := 0;i < len(jobConfigs); i ++ {
			jc = jobConfigs[i]
			if jc.Id == job.PublishedJobConfigId {
				break
			} else {
				continue
			}
		}
	}
	sqlCode, err := service3.DeEncodeAndDeEncryptSqlCode(jc.SqlCode)
	if err != nil {
		logger.Errorf("Failed to decode and decrypt SqlCode: %s", jc.SqlCode)
		return controller.SYSERR, "Failed to decode and decrypt SqlCode", nil
	}
	jobSQLResp := &queryJobSQLResp{JobConfigId:jc.Id, Status:"progress", JobSQL:sqlCode}
	return controller.SUCCESS,"ok", jobSQLResp
}