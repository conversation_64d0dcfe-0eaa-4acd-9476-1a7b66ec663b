package job_mng

import (
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/component"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	jobModel "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job"
	common "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	job "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job"
	jobConfig "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job_config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/region"
)

func init() {
	const controllerName = "qcloud.galileo.operation.stopJob"
	if code, msg := httpserver.RegisterController(controllerName, &stopJobController{}); code != 0 {
		logger.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type stopJobReq struct {
	AppId 			int32 				`check:"nullable:false|range:(0,INF)"`
	SerialId		string				`check:"nullable:false|strlen:(0,50)"`
	IsPause 		bool
	ClientIP 		string 				`check:"nullable:false|strlen:(0,50)"`
}

type stopJobResp struct {
	SerialId      string 				`json:"serialId"`
	IsSucc 		  string 				`json:"isSucc"`
}

type stopJobController struct {

}

func (this * stopJobController) CreateRequestObj () interface{} {
	return &stopJobReq{}
}

func (this * stopJobController) Process(req interface{}, eventId int64) (int64, string, interface{}) {
	reqData := req.(*stopJobReq)
	listJobQuery := jobModel.ListJobQuery{
		AppId:             0,
		SerialIds:         []string{reqData.SerialId},
		IsVagueNames:      false,
		Offset:            0,
		Limit:             20,
	}
	jobs, err := common.ListJobs(&listJobQuery)
	if err != nil {
		logger.Errorf("Failed to job with serialId, with serialId:%s, with appId:%d, with errors:%+v", reqData.SerialId, reqData.AppId,err)
		return controller.ERROR_CODE_JOB_QUERY_FAILURE, err.Error(),nil
	}
	if len(jobs) == 0 {
		logger.Errorf("Failed to get job with serialId: %s, as query size is 0", reqData.SerialId)
		return controller.ERROR_CODE_JOB_QUERY_FAILURE, err.Error(), nil
	}
	jobData := jobs[0]
	regionId, err := region.GetRegionIdByName(jobData.Region)
	if err != nil {
		logger.Errorf("Failed to get region id by name: %s, with errors:%+v", jobData.Region, err)
		return controller.ERROR_CODE_REGION_QUERY_FAILURE, err.Error(), nil
	}
	account := component.NewComponentAccount(regionId)
	ownerUin, err := account.GetOwnerUinByAppId(int64(reqData.AppId))
	if err != nil {
		logger.Errorf("Failed to get owner uin by appId: %d, with errors: %+v", reqData.AppId, err)
		return controller.ERROR_CODE_ACCOUNT_QUERY_FAILURE, err.Error(), nil
	}
	jcs, err := jobConfig.GetJobConfigsByJobSerialId(reqData.AppId, reqData.SerialId, -1, 0)
	if err != nil {
		logger.Errorf("Failed to get progress state job config ,with serialId:%s, with appId:%d, with errors:%+v", reqData.SerialId, reqData.AppId, err)
		return controller.ERROR_CODE_JOB_CONFIG_QUERY_NO_RESULT, err.Error(),nil
	}
	if len(jcs) == 0 {
		logger.Errorf("Failed to get progress job config, as query result size is 0, with serialId:%s, with appId:%d", reqData.SerialId, reqData.AppId)
		return controller.ERROR_CODE_JOB_CONFIG_QUERY_NO_RESULT,"job config query no result",nil
	}
	stopType := constants.JOB_STOP_TYPE_PAUSE
	if !reqData.IsPause {
		stopType = constants.JOB_STOP_TYPE_STOP
	}
	stopJobDesp := jobModel.StopJobDescription{JobId: jobData.SerialId, StopType: int8(stopType)}
	stopJobDesps := make([]jobModel.StopJobDescription, 0)
	stopJobDesps = append(stopJobDesps, stopJobDesp)
	stopJobReq := &jobModel.StopJobsReq{AppId: jobData.AppId, Uin:ownerUin, SubAccountUin: "Operation System Administrator", RequestId:"", Region:jobData.Region, Version:"1.0", StopJobDescriptions:stopJobDesps}
	errCode, msg, _ := job.DoStopJobs(stopJobReq)
	if errCode != constants.OK {
		logger.Errorf("Failed to stop job, with serialId:%s, with appId:%d, with error msg:%s", reqData.SerialId, reqData.AppId, msg)
		return controller.ERROR_CODE_JOB_STOP_FAILURE, err.Error(), nil
	}
	resp := &stopJobResp{SerialId:reqData.SerialId,IsSucc:"Yes"}
	return controller.SUCCESS, "ok", resp
}
