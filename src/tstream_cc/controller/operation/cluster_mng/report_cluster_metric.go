package cluster_mng

import (
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	cluster "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
)

func init() {
	const controllerName = "qcloud.galileo.internal.reportClusterMetric"
	if code, msg := httpserver.RegisterController(controllerName, &reportClusterMetricController{}); code != 0 {
		logger.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ", msg: " + msg)
	}
}

type reportClusterMetricController struct {
}

type reportClusterMetricReq struct {
	ClusterId    int64  `json:"clusterId"`
	RegionId     int    `json:"regionId"`
	VcoreUsed    int64  `json:"usedVcores"`
	VcoreTotal   int64  `json:"totalVcores"`
	MemoryUsed   int64  `json:"usedMemoryMB"`
	MemoryTotal  int64  `json:"totalMemoryMB"`
	DiskCapacity int64  `json:"totalDiskMB"`
	DiskUsed     int64  `json:"usedDiskMB"`
	AppId        int64  `json:"appId"`
	ClientIP     string `json:"clientIp"`
}

type reportClusterMetricResp struct {
	IsSucc string `json:"isSucc"`
	AppId  int64  `json:"appId"`
}

func (this *reportClusterMetricController) CreateRequestObj() interface{} {
	return &reportClusterMetricReq{}
}

func (this *reportClusterMetricController) Process(req interface{}, eventId int64) (int64, string, interface{}) {
	reqData := req.(*reportClusterMetricReq)
	var clusterId int64
	if reqData.ClusterId == -1 {
		clusterIdGet, err := cluster.GetClusterId(reqData.RegionId, constants.CLUSTER_GROUP_TYPE_SHARED)
		if err != nil {
			logger.Errorf("Failed to get cluster id, with reqData:%+v, with errors:%+v", reqData, err)
			return controller.ERROR_CODE_CLUSTER_GROUP_NOT_FOUND, err.Error(), nil
		}
		clusterId = int64(clusterIdGet)
	} else {
		clusterId = reqData.ClusterId
	}
	err := cluster.AddClusterMetric(clusterId, reqData.VcoreUsed, reqData.VcoreTotal, int64(reqData.MemoryUsed), int64(reqData.MemoryTotal), reqData.DiskCapacity,
		reqData.DiskUsed)
	if err != nil {
		logger.Errorf("Failed to add cluster metric, with reqData:%+v, with errors:%+v", reqData, err)
		return controller.SYSERR, err.Error(), nil
	}
	resp := &reportClusterMetricResp{IsSucc: "OK", AppId: reqData.AppId}
	return controller.SUCCESS, "ok", resp
}
