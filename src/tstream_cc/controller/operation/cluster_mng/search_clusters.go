package cluster_mng

import (
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"fmt"
	"strconv"
	"strings"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	cluster "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/region"
)

func init() {
	const controllerName = "qcloud.galileo.operation.searchClusters"
	if code, msg := httpserver.RegisterController(controllerName, &searchClustersController{}); code != 0 {
		logger.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type searchClustersController struct {
}

type searchClustersReq struct {
	RegionId  int    `check:"nullable:false|range:(0,INF)"`
	ClusterId int64  `check:"nullable:true|range:(0,INF)"`
	Status    string `check:"nullable:true|strlen:(0,50)"`
	PageNo    int    `check:"nullable:false|range:(0,INF)"`
	PageSize  int    `check:"nullable:false|range:(0,INF)"`
	Keyword   string `check:"nullable:true|strlen:(0,50)"`
	ClientIP  string `check:"nullable:false|strlen:(0,50)"`
}

type Cluster struct {
	ClusterId    int64  `json:"id"`
	ClusterName  string `json:"clusterName"`
	MemoryTotal  int64  `json:"memoryTotal"`
	MemoryUsed   int64  `json:"memoryUsed"`
	VCoreTotal   int64  `json:"vcoreTotal"`
	VCoreUsed    int64  `json:"vcoreUsed"`
	DiskCapacity int64  `json:"diskCapacity"`
	DiskUsed     int64  `json:"diskUsed"`
	ShareMode    string `json:"shareMode"`
	Status       string `json:"status"`
	RegionName   string `json:"region"`
	UserAppId    int64  `json:"userAppId"`
}

type searchClustersResp struct {
	Total    int        `json:"total"`
	Clusters []*Cluster `json:"clusters"`
}

func (this *searchClustersController) CreateRequestObj() interface{} {
	return &searchClustersReq{}
}

func (this *searchClustersController) Process(req interface{}, eventId int64) (int64, string, interface{}) {
	reqData := req.(*searchClustersReq)
	regionName,err := region.GetRegionNameById(reqData.RegionId)
	if err != nil {
		logger.Errorf("Failed to get region name by id, with region Id:%d", reqData.RegionId)
		return controller.SYSERR, err.Error(), nil
	}
	status := -1
	if strings.ToUpper(reqData.Status) == "RUNNING" {
		status = constants.CLUSTER_GROUP_STATUS_RUNNING
	} else if strings.ToUpper(reqData.Status) == "CREATING" {
		status = constants.CLUSTER_GROUP_STATUS_CREATING
	}
	clusterInfos, err := cluster.SearchClusterInfos(regionName, reqData.ClusterId, status, reqData.PageNo, reqData.PageSize, reqData.Keyword)
	if err != nil {
		logger.Errorf("Failed to get cluster infos, with reqData:%+v, with errors:%+v", reqData, err)
		return controller.ERROR_CODE_CLUSTER_GROUP_NO_CLUSTER_AVAILABLE, err.Error(), nil
	}
	clusters := make([]*Cluster, 0)
	for i := 0; i < len(clusterInfos); i++ {
		clusterInfo := clusterInfos[i]
		clusterGroup, err0 := cluster.GetClusterGroupByClusterId(clusterInfo.Id)
		if err0 != nil {
			logger.Errorf("Failed to get cluster group, with clusterId:%d, with errors:%+v", clusterInfo.Id, err)
			return controller.ERROR_CODE_CLUSTER_GROUP_NOT_FOUND, err.Error(), nil
		}
		clusterMetric, err := cluster.GetClusterMetric(clusterInfo.Id)
		if err != nil {
			logger.Errorf("Failed to get Cluster metric, with clusterId;%d, with errors:%+v", clusterInfo.Id, err)
			return controller.SYSERR, err.Error(), nil
		}
		region := clusterGroup.Region
		shareMode := "noShare"
		clusterStatus := "running"
		if clusterGroup.Type == constants.CLUSTER_GROUP_TYPE_SHARED {
			shareMode = "share"
		}
		if clusterGroup.Status == constants.CLUSTER_GROUP_STATUS_CREATING {
			clusterStatus = "creating"
		}
		clusterName := fmt.Sprintf("%s_%d", clusterGroup.Name, clusterInfo.Id)
		cluster := &Cluster{ClusterId: clusterInfo.Id, ClusterName: clusterName, MemoryTotal: clusterMetric.MemoryTotal, MemoryUsed: clusterMetric.MemoryUsed,
			VCoreTotal: clusterMetric.VcoreTotal, VCoreUsed: clusterMetric.VcoreUsed, DiskCapacity: clusterMetric.DiskCapacity, DiskUsed: clusterMetric.DiskUsed,
			ShareMode: shareMode, Status: clusterStatus, RegionName: region, UserAppId: int64(clusterGroup.AppId)}

		clusters = append(clusters, cluster)
	}

	count, err := cluster.GetClusterCountByCondition(regionName, reqData.ClusterId, status, reqData.Keyword)
	resp := &searchClustersResp{Total: count, Clusters: clusters}
	return controller.SUCCESS, "ok", resp
}
