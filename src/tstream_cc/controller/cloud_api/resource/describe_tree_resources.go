package resource

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/resource"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource"
)

func init() {
	const controllerName = "DescribeTreeResources"
	if code, msg := httpserver.RegisterCloudController(controllerName, &describeTreeResourcesController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type describeTreeResourcesController struct {
}

func (this *describeTreeResourcesController) CreateRequestObj() interface{} {
	return &model.DescribeTreeResourcesReq{}
}

func (this *describeTreeResourcesController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*model.DescribeTreeResourcesReq)
	if reqData.Limit == 0 {
		reqData.Limit = -1
	}
	rsp, err := service.DoDescribeTreeResources(reqData)
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}
