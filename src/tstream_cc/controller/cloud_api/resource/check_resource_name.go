package resource

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/cluster"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource"
)

/**
 * <AUTHOR>
 * @time 2021/1/26
 */
func init() {
	const controllerName = "CheckResourceName"
	if code, msg := httpserver.RegisterCloudController(controllerName, &checkResourceNameController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type checkResourceNameController struct {
}

func (this *checkResourceNameController) CreateRequestObj() interface{} {
	return &model.CheckResourceNameReq{}
}

func (this *checkResourceNameController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*model.CheckResourceNameReq)
	rsp, err := service.DoCheckResourceName(reqData)
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}
