package resource

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/resource"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/sql"
)

func init() {
	const controllerName = "ParseConnector"
	if code, msg := httpserver.RegisterCloudController(controllerName, &ParseConnectorController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type ParseConnectorController struct {
}

func (this *ParseConnectorController) CreateRequestObj() interface{} {
	return &model.ParseConnectorReq{}
}

func (this *ParseConnectorController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*model.ParseConnectorReq)
	rsp, err := service.DoParseConnector(reqData)
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}
