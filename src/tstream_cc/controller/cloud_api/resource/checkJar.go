package resource

import (
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	"tencentcloud.com/tstream_galileo/src/common/logger"

	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/interface_auth"
)

func init() {
	const controllerName = "DescribeJar"
	if code, msg := httpserver.RegisterCloudController(controllerName, &checkJarNewController{}); code != 0 {
		logging.Errorf("Failed to register controller: %s , with err msg: %s", controllerName, msg)
	}
	interface_auth.RegisterControllerNameMap(controllerName, "DescribeJar")
}

type checkJarReqNew struct {
	AppId              int64  `check:"nullable:false"`
	JobID              string `check:"nullable:true"`
	EntrypointClass    string `check:"nullable:true"`
	ProgramArgs        string `check:"nullable:true"`
	ResourceId         string `check:"nullable:true"`
	ResourceRefVersion int64  `check:"nullable:true"`
	Type               int    `check:"nullable:true"`
	UserType           int    `check:"nullable:true"`
	JobConfigVersion   int64  `check:"nullable:true"`

	Uin           string `json:"Uin"`
	SubAccountUin string `json:"SubAccountUin"`
}

type checkJarResp struct {
	Retcode int64  `json:"retcode"`
	Errmsg  string `json:"errmsg"`
	Data    string `json:"data"`
}

type checkJarNewController struct {
}

func (this *checkJarNewController) CreateRequestObj() interface{} {
	return &checkJarReqNew{}
}

// 跳过 Jar check
func (this *checkJarNewController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	resp := &checkJarResp{}
	resp.Retcode = 0
	resp.Errmsg = "succ"
	resp.Data = ""
	return controller.OK, controller.NULL, resp
}

/**
SELECT a.id,a.jobid,a.versionid,b.serialId FROM JobConfig as a, Job as b where a.jobId = b.id and b.status!=-2 and a.versionid>0 ;
SELECT a.id,a.jobid,a.versionid,b.serialId FROM JobConfig as a, Job as b where a.jobId = b.id and b.status!=-2 and a.versionid>0 and a.id=4368;
SELECT a.id as jobconfig_id,a.jobid,a.versionid,b.serialId,c.id as resourcerefid,c.resourceid,c.jobconfigid,c.status,c.usagetype FROM JobConfig as a, Job as b,ResourceRef as c where a.jobId = b.id and a.id=c.jobconfigid and b.status!=-2 and a.versionid>0 and c.Status<>-2 and c.usagetype=1
*/
