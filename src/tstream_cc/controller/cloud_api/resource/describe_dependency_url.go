package resource

import (
	"strconv"

	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller/cloud_api/cos"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/resource"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource"
)

func init() {
	const controllerName = "DescribeDependencyUrl"
	if code, msg := httpserver.RegisterCloudController(controllerName, &describeDependencyUrlController{}); code != 0 {
		logger.Errorf("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type describeDependencyUrlController struct {
}

func (this *describeDependencyUrlController) CreateRequestObj() interface{} {
	return &model.DescribeDependencyUrlReq{}
}

func (this *describeDependencyUrlController) Process(request interface{}, eventId int64) (string, string, interface{}) {
	req := request.(*model.DescribeDependencyUrlReq)

	if !cos.IsLegitimate(req.Region) {
		logger.Errorf("Region is not legitimate")
		code := errorcode.InvalidParameterValue_Legitimate
		return code.GetCodeStr(), code.GetCodeDesc(), nil
	}

	if req.AppId == 0 {
		logger.Errorf("AppId cannot be null")
		code := errorcode.InvalidParameter_InvalidAppId
		return code.GetCodeStr(), code.GetCodeDesc(), nil
	}

	resp, err := service.DoDescribeDependencyUrl(req)
	if err != nil {
		logger.Errorf("DescribeDependencyUrl: failed with error:", err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), nil
	}

	return controller.OK, "ok", resp
}
