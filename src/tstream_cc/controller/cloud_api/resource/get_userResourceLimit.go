package resource

import (
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logging "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/interface_auth"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource"
)

func init() {
	const controllerName = "DescribeUserResourceLimit"
	if code, msg := httpserver.RegisterCloudController(controllerName, &getUserResourceLimitNewController{}); code != 0 {
		logging.Errorf("Failed to register controller: %s , with err msg: %s", controllerName, msg)
	}
	interface_auth.RegisterControllerNameMap(controllerName, "DescribeUserResourceLimit")
}

type UserResourceLimitReq struct {
	AppId         int32
	Uin           string `json:"Uin"`
	SubAccountUin string `json:"SubAccountUin"`
}

type UserResourceLimitRsq struct {
	MaxResourceSizeLimit   int64
	MaxDependencySizeLimit int64
}

type getUserResourceLimitNewController struct {
}

func (this *getUserResourceLimitNewController) CreateRequestObj() interface{} {
	return &UserResourceLimitReq{}
}

func (this *getUserResourceLimitNewController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*UserResourceLimitReq)
	limit, dependencyLimit, err := service.GetResourceSizeLimit(reqData.AppId)
	if err != nil {
		logging.Errorf("GetResourceSizeLimit error: %+v", err)
		//return controller.ERROR_CODE_GET_RESOURCE_SIZE_LIMIT_FAILED, err.Error(), nil
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), nil
	}
	//return controller.SUCCESS, "ok", &UserResourceLimitRsq{MaxResourceSizeLimit: limit}
	return controller.OK, controller.NULL, &UserResourceLimitRsq{MaxResourceSizeLimit: limit, MaxDependencySizeLimit: dependencyLimit}
}
