package log

import (
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/log"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/log"
)

func init() {
	const controllerName = "DescribeProperESServerlessSpace"
	if code, msg := httpserver.RegisterCloudController(controllerName, &DescribeProperESServerlessSpaceController{}); code != 0 {
		logger.Errorf("Fail to register controller %s, code %d, err %s", controllerName, code, msg)
	}
}

type DescribeProperESServerlessSpaceController struct {
}

func (c *DescribeProperESServerlessSpaceController) CreateRequestObj() interface{} {
	return &log.DescribeProperESServerlessSpaceReq{}
}

func (c *DescribeProperESServerlessSpaceController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*log.DescribeProperESServerlessSpaceReq)
	rsp, err := (&service.DescribeProperESServerlessSpaceService{}).DescribeProperESServerlessSpace(reqData)
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}
