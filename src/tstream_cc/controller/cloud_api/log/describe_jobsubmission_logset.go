package log

import (
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/log"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/log"
)

func init() {
	const controllerName = "DescribeJobSubmissionLogSet"
	if code, msg := httpserver.RegisterCloudController(controllerName, &describeJobSubmissionLogSetController{}); code != 0 {
		logger.Errorf("Fail to register controller %s, code %d, err %s", controllerName, code, msg)
	}
}

type describeJobSubmissionLogSetController struct {
}

func (this *describeJobSubmissionLogSetController) CreateRequestObj() interface{} {
	return &log.DescribeJobSubmissionLogSetReq{}
}

func (this *describeJobSubmissionLogSetController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	return (&service.DescribeJobSubmissionLogSetService{}).DescribeJobSubmissionLogSet(req.(*log.DescribeJobSubmissionLogSetReq))
}
