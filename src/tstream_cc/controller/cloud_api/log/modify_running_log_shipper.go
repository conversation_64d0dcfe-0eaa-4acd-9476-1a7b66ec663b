package log

import (
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/log"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/log"
)

func init() {
	const controllerName = "ModifyRunningLogShipper"
	if code, msg := httpserver.RegisterCloudController(controllerName, &modifyRunningLogShipperController{}); code != 0 {
		logger.Errorf("Fail to register controller %s, code %d, err %s", controllerName, code, msg)
	}
}

type modifyRunningLogShipperController struct {
}

func (this *modifyRunningLogShipperController) CreateRequestObj() interface{} {
	return &log.ModifyRunningLogShipperReq{}
}

func (this *modifyRunningLogShipperController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	return (&service.ModifyRunningLogShipperService{}).ModifyRunningLogShipper(req.(*log.ModifyRunningLogShipperReq))
}
