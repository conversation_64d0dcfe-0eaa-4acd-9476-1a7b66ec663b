package log

import (
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/log"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/log"
)

// author: Archie<PERSON>ao
// date: 2021/10/19 9:51 下午
// description: 创建Oceanus专属日志集

func init() {
	const controllerName = "CreateProperClsLogset"
	if code, msg := httpserver.RegisterCloudController(controllerName, &createProperClsLogsetController{}); code != 0 {
		logger.Errorf("Fail to register controller %s, code %d, err %s", controllerName, code, msg)
	}
}

type createProperClsLogsetController struct {
}

func (d *createProperClsLogsetController) CreateRequestObj() interface{} {
	return &log.CreateProperClsLogsetReq{}
}

func (d *createProperClsLogsetController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*log.CreateProperClsLogsetReq)
	rsp, err := (&service.CreateProperClsLogsetService{}).CreateProperClsLogset(reqData)
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}
