package log

import (
	"tencentcloud.com/tstream_galileo/src/common/httpserver"

	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/log"
	logService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/log"
)

func init() {
	const controllerName = "CreateProperESServerlessIndex"
	if code, msg := httpserver.RegisterCloudController(controllerName, &createProperESServerlessIndexController{}); code != 0 {
		logger.Errorf("Fail to register controller %s, code %d, err %s", controllerName, code, msg)
	}
}

type createProperESServerlessIndexController struct {
}

func (c *createProperESServerlessIndexController) CreateRequestObj() interface{} {
	return &log.CreateProperESServerlessIndexReq{}
}

func (c *createProperESServerlessIndexController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	service := &logService.CreateProperESServerlessIndexService{}
	reqData := req.(*log.CreateProperESServerlessIndexReq)

	rsp, err := service.CreateProperEsServerlessIndex(reqData)
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}

	return controller.OK, controller.NULL, rsp
}
