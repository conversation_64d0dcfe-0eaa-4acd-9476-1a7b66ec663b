package log

import (
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/log"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/log"
)

// author: Archie<PERSON>ao
// date: 2021/10/11 4:12 下午
// description: 获取关联的CLS的作业

func init() {
	const controllerName = "DescribeClsRelatedJob"
	if code, msg := httpserver.RegisterCloudController(controllerName, &describeClsRelatedJobController{}); code != 0 {
		logger.Errorf("Fail to register controller %s, code %d, err %s", controllerName, code, msg)
	}
}

type describeClsRelatedJobController struct {
}

func (d *describeClsRelatedJobController) CreateRequestObj() interface{} {
	return &log.DescribeClsRelatedJobReq{}
}

func (d *describeClsRelatedJobController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*log.DescribeClsRelatedJobReq)
	rsp, err := (&service.DescribeClsRelatedJobService{}).GetJobByClsInfo(reqData)
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}