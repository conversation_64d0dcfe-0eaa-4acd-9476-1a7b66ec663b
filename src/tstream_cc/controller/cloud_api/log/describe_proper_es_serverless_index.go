package log

import (
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/log"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/log"
)

func init() {
	const controllerName = "DescribeProperESServerlessIndex"
	if code, msg := httpserver.RegisterCloudController(controllerName, &DescribeProperESServerlessIndexController{}); code != 0 {
		logger.Errorf("Fail to register controller %s, code %d, err %s", controllerName, code, msg)
	}
}

type DescribeProperESServerlessIndexController struct {
}

func (c *DescribeProperESServerlessIndexController) CreateRequestObj() interface{} {
	return &log.DescribeProperESServerlessIndexReq{}
}

func (c *DescribeProperESServerlessIndexController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*log.DescribeProperESServerlessIndexReq)
	rsp, err := (&service.DescribeProperESServerlessIndexService{}).DescribeProperESServerlessIndex(reqData)
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}
