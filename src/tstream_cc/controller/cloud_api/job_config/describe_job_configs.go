package job_config

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/log"
)

/**
 *
 * <AUTHOR>
 * @time 2019/4/29
 * @version 0.4.0
 * @since 0.4.0
 * @see
 */
func init() {
	const controllerName = "DescribeJobConfigs"
	if code, msg := httpserver.RegisterCloudController(controllerName, &describeJobConfigsController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type describeJobConfigsController struct {
}

func (this *describeJobConfigsController) CreateRequestObj() interface{} {
	return &model.DescribeJobConfigsReq{}
}

func (this *describeJobConfigsController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*model.DescribeJobConfigsReq)
	rsp, err := service.DoDescribeJobConfigs(reqData)
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}
