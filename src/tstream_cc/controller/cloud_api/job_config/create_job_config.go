package job_config

import (
	"context"

	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_config"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job_config"
)

/**
 *
 * <AUTHOR>
 * @time 2019/4/29
 * @version 0.4.0
 * @since 0.4.0
 * @see
 */
func init() {
	const controllerName = "CreateJobConfig"
	if err := httpserver.RegisterCloudControllerV2(controllerName, &createJobConfigController{}); err != nil {
		logger.Errorf("Failed to register cloud controller:%+v", err)
	}
}

type createJobConfigController struct {
}

func (this *createJobConfigController) CreateRequestObj() interface{} {
	return &model.CreateJobConfigReq{}
}

func (this *createJobConfigController) Process(ctx context.Context,
	req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*model.CreateJobConfigReq)
	reqData.ProgramArgs = service.GetRealProgramArgs(reqData.ProgramArgs, reqData.ProgramArgsAfterGzip)
	rsp, err := service.DoCreateJobConfig(ctx, reqData)
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		if code == errorcode.InternalErrorCode {
			code = errorcode.InternalErrorCode_JobConfigCreateFailed
		}
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}
