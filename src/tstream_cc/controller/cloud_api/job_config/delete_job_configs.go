package job_config

import (
	"strconv"

	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_config"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job_config"
)

func init() {
	const controllerName = "DeleteJobConfigs"
	if code, msg := httpserver.RegisterCloudController(controllerName, &deleteJobConfigsController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type deleteJobConfigsController struct {
}

func (this *deleteJobConfigsController) CreateRequestObj() interface{} {
	return &model.DeleteJobConfigsReq{}
}

func (this *deleteJobConfigsController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*model.DeleteJobConfigsReq)
	return service.DoDeleteJobConfigs(reqData)
}
