package job_config

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_config"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job_config"
)

func init() {
	const controllerName = "DescribeFlinkConf"
	if code, msg := httpserver.RegisterCloudController(controllerName, &describeFlinkConfController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type describeFlinkConfController struct {
}

func (this *describeFlinkConfController) CreateRequestObj() interface{} {
	return &model.DescribeFlinkConfReq{}
}

func (this *describeFlinkConfController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*model.DescribeFlinkConfReq)

	rsp, err := service.NewDescribeFlinkConfService(reqData).DescribeFlinkConf()
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}
