package sql_gateway

import (
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/sql_gateway"
	sql_gateway2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/sql_gateway"
)

func init() {
	const controllerName = "FetchSqlGatewayStatementResult"
	if code, msg := httpserver.RegisterCloudController(controllerName, &fetchSqlGatewayStatementResultController{}); code != 0 {
		logger.Errorf("Fail to register controller %s, code %d, err %s", controllerName, code, msg)
	}
}

type fetchSqlGatewayStatementResultController struct {
}

func (c *fetchSqlGatewayStatementResultController) CreateRequestObj() interface{} {
	return &sql_gateway.FetchSqlGatewayResultReq{}
}

func (c *fetchSqlGatewayStatementResultController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*sql_gateway.FetchSqlGatewayResultReq)
	s := sql_gateway2.NewSqlGatewayService(reqData.ClusterId, reqData.SessionId)
	rsp, err := s.FetchResult(reqData)
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}
