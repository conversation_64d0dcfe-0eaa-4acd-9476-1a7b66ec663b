package sql_gateway

import (
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/sql_gateway"
	sqlgateway2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/sql_gateway"
)

func init() {
	const controllerName = "RunSqlGatewayStatement"
	if code, msg := httpserver.RegisterCloudController(controllerName, &runSqlGatewayStatementController{}); code != 0 {
		logger.Errorf("Fail to register controller %s, code %d, err %s", controllerName, code, msg)
	}
}

type runSqlGatewayStatementController struct {
}

func (c *runSqlGatewayStatementController) CreateRequestObj() interface{} {
	return &sql_gateway.RunSqlGatewayStatementReq{}
}

func (c *runSqlGatewayStatementController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*sql_gateway.RunSqlGatewayStatementReq)
	s := sqlgateway2.NewSqlGatewayService(reqData.ClusterId, reqData.SessionId)
	rsp, err := s.RunStatement(reqData)
	if err != nil {
		logger.Errorf("%s %s %s %v", reqData.ClusterId, reqData.SessionId, reqData.Sql, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}