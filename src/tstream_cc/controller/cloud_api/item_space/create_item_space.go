package item_space

/**
 * Created by <PERSON> on 2021/10/14 5:56 下午.
 * At tencent
 */

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/item_space"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/item_space"
)

func init() {
	const controllerName = "CreateWorkSpace"
	if code, msg := httpserver.RegisterCloudController(controllerName, &createItemSpaceController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type createItemSpaceController struct{}

func (this *createItemSpaceController) CreateRequestObj() interface{} {
	return &model.CreateItemSpaceReq{}
}

func (this *createItemSpaceController) Process(req interface{}, eventId int64) (string, string, interface{}) {

	reqData := req.(*model.CreateItemSpaceReq)

	return service.DoCreateItemSpace(reqData)
}
