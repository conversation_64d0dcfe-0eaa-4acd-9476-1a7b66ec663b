package item_space

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/item_space"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/item_space"
)

/**
 * Created by <PERSON> on 2021/11/24 5:31 下午.
 * At tencent
 */

func init() {
	const controllerName = "DeleteWorkSpace"
	if code, msg := httpserver.RegisterCloudController(controllerName, &deleteItemSpaceController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type deleteItemSpaceController struct{}

func (this *deleteItemSpaceController) CreateRequestObj() interface{} {
	return &model.DeleteItemSpaceReq{}
}

func (this *deleteItemSpaceController) Process(req interface{}, eventId int64) (string, string, interface{}) {

	reqData := req.(*model.DeleteItemSpaceReq)

	return service.DoDeleteItemSpace(reqData)
}
