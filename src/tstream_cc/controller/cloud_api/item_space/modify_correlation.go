package item_space

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/item_space"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/item_space"
)

/**
 * Created by <PERSON> on 2021/11/15 1:52 下午.
 * At tencent
 */

func init() {
	const controllerName = "ModifyCorrelation"
	if code, msg := httpserver.RegisterCloudController(controllerName, &modifyCorrelationController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type modifyCorrelationController struct{}

func (this *modifyCorrelationController) CreateRequestObj() interface{} {
	return &model.ModifyCorrelationReq{}
}

func (this *modifyCorrelationController) Process(req interface{}, eventId int64) (string, string, interface{}) {

	reqData := req.(*model.ModifyCorrelationReq)

	return service.DoModifyCorrelation(reqData)
}
