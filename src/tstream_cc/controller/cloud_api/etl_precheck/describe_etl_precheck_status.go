package etl_precheck

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/etl_precheck"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/etl_precheck"
)

func init() {
	const controllerName = "DescribeEtlPreCheckStatus"
	if code, msg := httpserver.RegisterCloudController(controllerName, &DescribeEtlPreCheckStatusController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type DescribeEtlPreCheckStatusController struct {
}

func (this *DescribeEtlPreCheckStatusController) CreateRequestObj() interface{} {
	return &model.DescribeEtlPreCheckStatusReq{}
}

func (this *DescribeEtlPreCheckStatusController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*model.DescribeEtlPreCheckStatusReq)
	rsp, err := service.DescribeEtlPreCheckStatus(reqData)
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}
