package etl_precheck

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/etl_precheck"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/etl_precheck"
)

func init() {
	const controllerName = "CreateEtlPreCheck"
	if code, msg := httpserver.RegisterCloudController(controllerName, &CreateEtlPreCheckController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type CreateEtlPreCheckController struct {
}

func (this *CreateEtlPreCheckController) CreateRequestObj() interface{} {
	return &model.CreateEtlPreCheckReq{}
}

func (this *CreateEtlPreCheckController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*model.CreateEtlPreCheckReq)
	rsp, err := service.CreateEtlPreCheck(reqData)
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}
