package runtime

import (
	"strconv"

	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/runtime"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/runtime"
)

func init() {
	const controllerName = "CreateDumpFile"
	if code, msg := httpserver.RegisterCloudController(controllerName, &createDumpFileController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type createDumpFileController struct {
}

func (d *createDumpFileController) CreateRequestObj() interface{} {
	return &model.CreateDumpFileReq{}
}

func (d *createDumpFileController) Process(req interface{}, eventID int64) (string, string, interface{}) {
	reqData := req.(*model.CreateDumpFileReq)
	rsp, err := (&service.CreateDumpFileService{}).DoCreateDumpFile(reqData)
	if err != nil {
		logger.Errorf("%s CreateDumpFile error has returned %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}
