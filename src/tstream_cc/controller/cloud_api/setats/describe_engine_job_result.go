package cluster

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/setats"
)

func init() {
	const controllerName = "DescribeEngineJobResult"
	if code, msg := httpserver.RegisterCloudController(controllerName, &describeEngineJobResultController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type describeEngineJobResultController struct {
}

func (this *describeEngineJobResultController) CreateRequestObj() interface{} {
	return &setats.DescribeEngineJobResultReq{}
}

// https://write.woa.com/document/130992657738792960
func (this *describeEngineJobResultController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*setats.DescribeEngineJobResultReq)

	rsp, err := DescribeEngineJobResult(reqData)
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}

func DescribeEngineJobResult(req *setats.DescribeEngineJobResultReq) (*setats.DescribeEngineJobResultRsp, error) {

	return &setats.DescribeEngineJobResultRsp{
		EngineJobResultType: 0,
		EngineJobResultMeta: &setats.EngineJobResultMeta{
			SQL:          "",
			CostTime:     0,
			CreateTime:   "",
			ResultSchema: nil,
			TotalCount:   0,
		},
		EngineJobResult: &setats.EngineJobResult{
			ResultSet: "",
			Charset:   "",
		},
		JumpInfos: []*setats.EngineJobResultJumpInfo{},
	}, nil
}
