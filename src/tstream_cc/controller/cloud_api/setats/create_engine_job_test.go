package cluster

import (
	"encoding/json"
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/dlocker"
	service2 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/setats"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource"
	"testing"
)

func init() {
	service.InitTestDB(service.WALLYDB)
	dlocker.SetTxManager(service2.GetTxManager())
}

func TestCalcStandardPodSpec(t *testing.T) {
	errss := make([]string, 0)
	errss = append(errss, "1")
	errss = append(errss, "2")
	errorMessage, _ := json.Marshal(errss)
	sss := string(errorMessage)
	fmt.Println(sss)

	paramMap := make(map[string]string)
	paramMap["a"] = "a"
	paramMap["b"] = "b"
	aa := paramMap["ab"]
	fmt.Println(aa)

	//fmt.Println(calcStandardPodSpec(3.3, 16, 4))
	parameters := make([]*setats.ExtParameter, 0)
	parameters = append(parameters, &setats.ExtParameter{
		Name:  "RunMode",
		Value: "1",
	})
	parameters = append(parameters, &setats.ExtParameter{
		Name:  "WorkSpaceId",
		Value: "space-xxxx",
	})
	parameters = append(parameters, &setats.ExtParameter{
		Name:  "EngineTaskId",
		Value: "20250303202310079",
	})
	parameters = append(parameters, &setats.ExtParameter{
		Name:  "JobManagerSpec",
		Value: "1",
	})
	parameters = append(parameters, &setats.ExtParameter{
		Name:  "TaskManagerSpec",
		Value: "1",
	})
	parameters = append(parameters, &setats.ExtParameter{
		Name:  "FlinkVersion",
		Value: "Flink-1.16",
	})
	parameters = append(parameters, &setats.ExtParameter{
		Name:  "DefaultParallelism",
		Value: "1",
	})
	parameters = append(parameters, &setats.ExtParameter{
		Name:  "Properties",
		Value: "[]",
	})
	_, err := CreateEngineJob(&setats.CreateEngineJobReq{
		RequestBase: apiv3.RequestBase{
			Region:        "ap-chengdu",
			RequestId:     "xx11111ddd11",
			AppId:         **********,
			Uin:           "************",
			SubAccountUin: "************",
		},
		InstanceId:      "setats-xxx",
		JobType:         "",
		JobContent:      "LS0gRGF0YWdlbiBDb25uZWN0b3Ig5Y+v5Lul6ZqP5py655Sf5oiQ5LiA5Lqb5pWw5o2u55So5LqO5rWL6K+VCi0tIOWPguingSBodHRwczovL2Nsb3VkLnRlbmNlbnQuY29tL2RvY3VtZW50L3Byb2R1Y3QvODQ5LzU4NzEzCkNSRUFURSBUQUJMRSBkYXRhZ2VuX3NvdXJjZV90YWJsZSAoIAogICAgaWQgSU5ULCAKICAgIG5hbWUgU1RSSU5HIAopIFdJVEggKCAKICAgICdjb25uZWN0b3InID0gJ2RhdGFnZW4nLAogICAgJ3Jvd3MtcGVyLXNlY29uZCc9JzEnLCAgLS0g5q+P56eS5Lqn55Sf55qE5pWw5o2u5p2h5pWwCiAgICAnZmllbGRzLmlkLmtpbmQnPSdzZXF1ZW5jZScsCiAgICAnZmllbGRzLmlkLnN0YXJ0Jz0nMScsCiAgICAnZmllbGRzLmlkLmVuZCc9JzIwJwogICAgCik7CgotLSDovpPlhaXliLAgQmxhY2tob2xlIFNpbmsg55qE5pWw5o2uLCDkvJrooqvlhajpg6jkuKLlvIPjgILov5nkuKogU2luayDpgILlkIjlgZrmgKfog73mtYvor5XjgIIKLS0g5Y+C6KeBIGh0dHBzOi8vY2kuYXBhY2hlLm9yZy9wcm9qZWN0cy9mbGluay9mbGluay1kb2NzLXJlbGVhc2UtMS4xMS96aC9kZXYvdGFibGUvY29ubmVjdG9ycy9ibGFja2hvbGUuaHRtbApDUkVBVEUgVEFCTEUgYmxhY2tob2xlX3NpbmsgKCAKICAgIGlkIElOVCwgCiAgICBuYW1lIFNUUklORyAKKSBXSVRIICggCiAgICAnY29ubmVjdG9yJyA9ICdibGFja2hvbGUnCik7CgppbnNlcnQgaW50byBibGFja2hvbGVfc2luayBzZWxlY3QgKiBmcm9tIGRhdGFnZW5fc291cmNlX3RhYmxlOwo=",
		JobUUID:         "xxx2222",
		ExecuteUser:     "",
		ExecutePassword: "",
		ResourcePath:    "",
		Parameters:      parameters,
		ExtParameters:   nil,
	})
	fmt.Println(err)
}
