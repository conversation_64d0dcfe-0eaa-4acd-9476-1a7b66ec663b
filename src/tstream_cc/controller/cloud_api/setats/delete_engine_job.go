package cluster

import (
	"errors"
	"fmt"
	"strconv"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	service2 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/setats"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/sql_gateway"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	service4 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job"
	sqlgateway2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/sql_gateway"
)

func init() {
	const controllerName = "DeleteEngineJob"
	if code, msg := httpserver.RegisterCloudController(controllerName, &deleteEngineJobController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type deleteEngineJobController struct {
}

func (this *deleteEngineJobController) CreateRequestObj() interface{} {
	return &setats.DeleteEngineJobReq{}
}

// https://write.woa.com/document/130992657738792960
func (this *deleteEngineJobController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*setats.DeleteEngineJobReq)

	rsp, err := DeleteEngineJob(reqData)
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}

func BuildEngineJobReq(job *table.Job) *model2.StopJobsReq {
	stopJobData := &model2.StopJobsReq{
		AppId:         job.AppId,
		Uin:           job.OwnerUin,
		SubAccountUin: job.CreatorUin,
		Region:        job.Region,
		IsSupOwner:    1,
		Version:       "1.0",
		StopJobDescriptions: []model2.StopJobDescription{
			{
				JobId:    job.SerialId,
				StopType: int8(constants.JOB_STOP_TYPE_STOP),
			},
		},
	}
	return stopJobData
}

func DeleteEngineJob(req *setats.DeleteEngineJobReq) (rst *setats.DeleteEngineJobRsp, err error) {

	rst = &setats.DeleteEngineJobRsp{}
	defer func() {
		if errs := recover(); errs != nil {
			err = errors.New(fmt.Sprintf("DeleteEngineJob to panic, with errors:%+v", errs))
			logger.Errorf("%s DeleteEngineJob to panic, with errors:%+v", req.RequestId, errs)
		}
	}()

	engineJobId := req.EngineJobId
	if engineJobId == "" {
		logger.Warningf("%s DeleteEngineJob engineJobId is empty", req.RequestId)
		return &setats.DeleteEngineJobRsp{
			State: constants.EngineJobStateKilled,
		}, nil
	}

	batchTask, err := service.GetBatchTask(&setats.BatchTaskReq{
		AppId:       req.AppId,
		EngineJobId: engineJobId,
	})
	if err != nil {
		msg := fmt.Sprintf("%s DeleteEngineJob GetBatchTask error %+v with engineJobId %s", req.RequestId, err, engineJobId)
		logger.Errorf(msg)
		return rst, errors.New(msg)
	}
	if batchTask == nil {
		msg := fmt.Sprintf("%s DeleteEngineJob batchTask is nil with engineJobId %s", req.RequestId, engineJobId)
		logger.Warningf(msg)
		return &setats.DeleteEngineJobRsp{
			State: constants.EngineJobStateKilled,
		}, nil
	}

	parts := strings.Split(engineJobId, constants.SetatsEngineJobIdSplit)
	if len(parts) != 3 {
		msg := fmt.Sprintf("%s DeleteEngineJob engine job id:%s is not valid, valid pattern is cql-abc__1__123", req.RequestId, engineJobId)
		logger.Errorf(msg)
		return rst, errors.New(msg)

	}
	if batchTask.Type == constants.BatchTaskRunType {
		if batchTask.Status == constants.EngineJobStateKilled {
			return &setats.DeleteEngineJobRsp{
				State: constants.EngineJobStateKilled,
			}, nil
		}
		clusterGroupSerialId := ""
		instanceId := req.InstanceId
		if instanceId == "" {
			logger.Errorf("%s DescribeEngineJobState Failed to get setats InstanceId:%s", req.RequestId, req.InstanceId)
			return rst, errors.New("InstanceId is blank")
		}
		if strings.Contains(instanceId, "cluster-") {
			clusterGroupSerialId = instanceId
		} else {
			_setats, err := service.GetSetatsBySerialId(instanceId)
			if err != nil {
				logger.Errorf("%s DescribeEngineJobState Failed to get setats by serial id:%s, with errors:%+v", req.RequestId, instanceId, err)
				return rst, err
			}
			if _setats.Status == constants.SETATS_DELETED || _setats.Status == constants.SETATS_ISOLATED {
				logger.Errorf("%s setats status is deleted or isolated", req.RequestId)
				return rst, errors.New("setats status is deleted or isolated")
			}
			clusterGroupSerialId = _setats.ClusterGroupSerialId
		}
		sessionId := parts[0]
		operationHandleId := parts[1]
		reqData := &sql_gateway.CancelSqlGatewayStatementReq{
			RequestBase:       req.RequestBase,
			ClusterId:         clusterGroupSerialId,
			SessionId:         sessionId,
			OperationHandleId: operationHandleId,
			FlinkVersion:      constants.Flinkversion118,
			JobID:             batchTask.JobID,
		}
		s := sqlgateway2.NewSqlGatewayService(reqData.ClusterId, reqData.SessionId)
		err = s.CancelSqlGatewayStatement(reqData)
		if err != nil {
			logger.Warningf("%s Failed CancelSqlGatewayStatement, with errors:%+v", req.RequestId, err)
		}
		service2.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
			tx.ExecuteSqlWithArgs("update BatchTask set Status=? where Id=?",
				constants.EngineJobStateKilled, batchTask.Id)
			return nil
		}).Close()
		// kill结果：-3：已终止；  -4： kill中；
		return &setats.DeleteEngineJobRsp{
			State: constants.EngineJobStateKilled,
		}, nil
	}

	jobId := parts[0]
	job, err := service2.GetJobBySerialId(req.AppId, jobId)
	if err != nil {
		logger.Errorf("%s: DeleteEngineJob GetJobBySerialId  %s error: %+v", req.RequestId, jobId, err)
		return rst, err
	}
	// kill结果：-3：已终止；  -4： kill中；
	if job.Status == constants.JOB_STATUS_STOPPED || job.Status == constants.JOB_STATUS_FINISHED {
		return &setats.DeleteEngineJobRsp{
			State: constants.EngineJobStateKilled,
		}, nil
	} else if job.Status == constants.JOB_STATUS_PROGRESS &&
		(job.ProgressDesc == constants.STOP_JOB_PROGRESS_DESC || job.ProgressDesc == constants.PAUSE_JOB_PROGRESS_DESC) {
		return &setats.DeleteEngineJobRsp{
			State: constants.EngineJobStateKilling,
		}, nil
	} else if job.Status == constants.JOB_STATUS_RUNNING || job.Status == constants.JOB_STATUS_PROGRESS {
		// 运行或者启动中
		stopJobReq := BuildEngineJobReq(job)
		spaceId, err := auth.ConverItemSpaceSerialId(job.ItemSpaceId)
		if err != nil {
			logger.Errorf("%s DeleteEngineJob - Fail to converItemSpaceSerialId by ID:%s, err:%+v", req.RequestId, job.ItemSpaceId, err)
			return rst, err
		}
		stopJobReq.WorkSpaceId = spaceId
		// 判断强制停止
		canForceStopJob := auth.IsInWhiteList(req.AppId, constants.WHITE_FORCE_STOP_JOB)
		if !canForceStopJob {
			err = auth.AddWhiteList(req.AppId, constants.WHITE_FORCE_STOP_JOB)
			if err != nil {
				logger.Warningf("%s Failed to AddWhiteList WHITE_FORCE_STOP_JOB for engineJobId %s  error:%+v", req.RequestId, engineJobId, err)
			}
		}
		errCode, msg, _ := service4.DoStopJobs(stopJobReq)
		if errCode != constants.OK {
			logger.Errorf("%s DeleteEngineJob Failed to stop job, with serialId:%s, with appId:%s, with error msg:%s", req.RequestId, job.SerialId, job.AppId, msg)
			return rst, errors.New(msg)
		}
		//更新 status 标识为 手动终止
		service2.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
			tx.ExecuteSqlWithArgs("update BatchTask set Status=? where Id=?",
				constants.EngineJobStateKilled, batchTask.Id)
			return nil
		}).Close()
		// kill结果：-3：已终止；  -4： kill中；
		return &setats.DeleteEngineJobRsp{
			State: constants.EngineJobStateKilling,
		}, nil
	}
	// kill结果：-3：已终止；  -4： kill中；
	return &setats.DeleteEngineJobRsp{
		State: constants.EngineJobStateKilled,
	}, nil
}
