package cluster

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/setats"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
)

func init() {
	const controllerName = "ModifySetatsConfiguration"
	if code, msg := httpserver.RegisterCloudController(controllerName, &modifySetatsConfigurationController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type modifySetatsConfigurationController struct {
}

func (this *modifySetatsConfigurationController) CreateRequestObj() interface{} {
	return &setats.ModifySetatsConfigurationReq{}
}

func (this *modifySetatsConfigurationController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*setats.ModifySetatsConfigurationReq)

	rsp, err := service.ModifySetatsConfiguration(reqData)
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}
