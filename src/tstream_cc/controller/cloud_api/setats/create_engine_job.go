package cluster

import (
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/dlocker"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/common/uuid"
	service1 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model1 "tencentcloud.com/tstream_galileo/src/tstream_cc/model"
	jobModel "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job"
	model5 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/setats"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/sql_gateway"
	table4 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/item_space"
	setats2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/setats"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	service7 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job_config"
	sqlgateway2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/sql_gateway"
	"time"
)

func init() {
	const controllerName = "CreateEngineJob"
	if code, msg := httpserver.RegisterCloudController(controllerName, &createEngineJobController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type createEngineJobController struct {
}

func (this *createEngineJobController) CreateRequestObj() interface{} {
	return &setats.CreateEngineJobReq{}
}

// https://write.woa.com/document/130992657738792960
func (this *createEngineJobController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*setats.CreateEngineJobReq)

	rsp, err := CreateEngineJob(reqData)
	if err != nil {
		logger.Errorf("@@@@@ %s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}

/**
 * https://doc.weixin.qq.com/doc/w3_ACcAhAZ1AD4CNW61gOE1ZQYavi5PY?scode=AJEAIQdfAAoSJFGO0BACcAhAZ1AD4
 * https://write.woa.com/document/130992657738792960
 */

func CreateEngineJob(req *setats.CreateEngineJobReq) (rst *setats.CreateEngineJobRsp, err error) {

	rst = &setats.CreateEngineJobRsp{}
	defer func() {
		if errs := recover(); errs != nil {
			err = errors.New(fmt.Sprintf("CreateEngineJob panic %v", errs))
			logger.Errorf("%s CreateEngineJob to panic, with errors:%+v", req.RequestId, errs)
		}
	}()

	JobUUID := req.JobUUID
	locker := dlocker.NewDlocker("CreateEngineJob", fmt.Sprintf("optId-%s", JobUUID), 1200)
	err = locker.Lock()
	if err != nil {
		if strings.Contains(err.Error(), "Locked by others") {
			logger.Debugf("%s could not fetch the lock. %+v", req.RequestId, err)
		} else {
			logger.Errorf("%s could not fetch the lock. %+v", req.RequestId, err)
		}
		return rst, err
	}
	defer func() {
		err1 := locker.UnLock()
		if err1 != nil {
			logger.Errorf("%s could not fetch the lock. %+v", req.RequestId, err1)
		}
	}()

	batchTask, err := service.GetBatchTask(&setats.BatchTaskReq{
		AppId:   req.AppId,
		JobUUID: JobUUID,
	})
	if batchTask != nil {
		return &setats.CreateEngineJobRsp{
			EngineJobId:     batchTask.EngineJobId,
			EngineJobLogUrl: batchTask.EngineJobLogUrl,
		}, nil
	}
	if err != nil {
		logger.Errorf("%s Failed to get batchTask, with errors:%+v", req.RequestId, err)
		return rst, err
	}

	clusterGroupSerialId := ""
	instanceId := req.InstanceId
	if instanceId == "" {
		logger.Errorf("%s CreateEngineJob Failed to get setats InstanceId:%s", req.RequestId, req.InstanceId)
		return rst, errors.New("InstanceId is blank")
	}
	if strings.Contains(instanceId, "cluster-") {
		clusterGroupSerialId = instanceId
	} else {
		_setats, err := service.GetSetatsBySerialId(instanceId)
		if err != nil {
			logger.Errorf("%s CreateEngineJob Failed to get setats by serial id:%s, with errors:%+v", req.RequestId, instanceId, err)
			return rst, err
		}
		if _setats.Status == constants.SETATS_DELETED || _setats.Status == constants.SETATS_ISOLATED {
			logger.Errorf("%s setats status is deleted or isolated", req.RequestId)
			return rst, errors.New("setats status is deleted or isolated")
		}
		clusterGroupSerialId = _setats.ClusterGroupSerialId
	}

	clusterGroup, err := service.GetClusterGroupBySerialId(clusterGroupSerialId)
	if err != nil {
		logger.Errorf("%s Failed to get cluster group by serial id:%s, with errors:%+v", req.RequestId, clusterGroupSerialId, err)
		return rst, err
	}
	cluster, err := service.GetActiveClusterByClusterGroupId(clusterGroup.Id)
	if err != nil {
		logger.Errorf("%s Failed to get cluster by clusterGroup id:%d, with errors:%+v", req.RequestId, clusterGroup.Id, err)
		return rst, err
	}

	engineJobObj, err := GetEngineJobObj(clusterGroup, req, err)
	if err != nil {
		logger.Errorf("%s Failed to get engineJobObj, with errors:%+v", req.RequestId, err)
		return rst, err
	}
	// 试运行
	schedulingTime := engineJobObj.SchedulingTime
	if schedulingTime == "" {
		result := &setats.CreateEngineJobRsp{}
		result, err = RunEngineJob(req, clusterGroupSerialId)
		if err != nil {
			logger.Errorf("%s Failed to run engineJob, with errors:%+v", req.RequestId, err)
			return rst, err
		}
		// 保存 BatchTask
		service1.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
			batchTask = &setats2.BatchTask{
				JobSerialId:      req.InstanceId,
				JobConfigVersion: -1,
				AppId:            req.AppId,
				OwnerUin:         req.Uin,
				CreatorUin:       req.SubAccountUin,
				Region:           req.Region,
				Zone:             clusterGroup.Zone,
				EngineJobId:      result.EngineJobId,
				JobUUID:          req.JobUUID,
				EngineJobLogUrl:  result.EngineJobLogUrl,
				Type:             constants.BatchTaskRunType,
				CreateTime:       util.GetCurrentTime(),
				UpdatedTime:      util.GetCurrentTime(),
			}
			_, err = tx.SaveObjectDoNotPanic(batchTask, "BatchTask")
			if err != nil && !strings.Contains(strings.ToLower(err.Error()), "duplicate entry") {
				logger.Errorf("%s Failed to save batchTask, with errors:%+v", req.RequestId, err)
				return err
			}
			return nil
		}).Close()
		return result, nil
	}

	jobName := fmt.Sprintf("WS-%s-%s-%d", engineJobObj.EngineTaskId, ConvertTime(engineJobObj.SchedulingTime), time.Now().UnixNano()/int64(time.Millisecond))
	createJobReq := buildCreateJobReq(req, engineJobObj, clusterGroupSerialId, jobName)
	createJobReq.CamContext = ""
	isOk, msg, rspJob := service7.DoCreateJob(createJobReq)
	if isOk != controller.OK {
		logger.Errorf("%s CreateEngineJob Failed to create job, with errors:%s", req.RequestId, msg)
		return rst, errors.New(msg)
	}

	createJobConfigReq := BuildJobConfigReq(cluster, clusterGroup, engineJobObj, rspJob.JobId, req)
	createJobConfigReq.RequestId = req.RequestId
	rsp, err := service3.DoCreateJobConfig(createJobConfigReq)
	if err != nil {
		logger.Errorf("%s Failed to create job config, with errors:%+v", req.RequestId, err)
		return rst, err
	}

	err = RunJobWithRetry(createJobConfigReq, int64(rsp.Version), "", 10)
	if err != nil {
		logger.Errorf("%s Failed to run job with CreateJobConfigReq: %+v, versionId: %d,  because %+v", req.RequestId, createJobConfigReq, rsp.Version, err)
		return rst, err
	}

	engineJobId := fmt.Sprintf("%s%s%d%s%d", rspJob.JobId, constants.SetatsEngineJobIdSplit, rsp.Version, constants.SetatsEngineJobIdSplit, time.Now().UnixNano()/int64(time.Millisecond))

	engineJobLogUrl :=
		fmt.Sprintf("https://console.cloud.tencent.com/oceanus/job/detail/sqlJob/%s/%s/%s?tab=log",
			clusterGroup.Region, engineJobObj.WorkSpaceId, rspJob.JobId)

	// 保存 BatchTask
	service1.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		batchTask = &setats2.BatchTask{
			JobSerialId:      rspJob.JobId,
			JobConfigVersion: rsp.Version,
			AppId:            req.AppId,
			OwnerUin:         req.Uin,
			CreatorUin:       req.SubAccountUin,
			Region:           req.Region,
			Zone:             clusterGroup.Zone,
			EngineJobId:      engineJobId,
			JobUUID:          req.JobUUID,
			EngineJobLogUrl:  engineJobLogUrl,
			Type:             constants.BatchTaskScheduleType,
			CreateTime:       util.GetCurrentTime(),
			UpdatedTime:      util.GetCurrentTime(),
		}
		_, err1 := tx.SaveObjectDoNotPanic(batchTask, "BatchTask")
		if err1 != nil && !strings.Contains(strings.ToLower(err.Error()), "duplicate entry") {
			logger.Errorf("%s Failed to save batchTask, with errors:%+v", req.RequestId, err)
			return err1
		}
		return nil
	}).Close()

	return &setats.CreateEngineJobRsp{
		EngineJobId:     engineJobId,
		EngineJobLogUrl: engineJobLogUrl,
	}, nil
}

// sql gateway session 公用
var expireTime int64 = 600 // 10分钟超时

func RunEngineJob(req *setats.CreateEngineJobReq, clusterGroupSerialId string) (*setats.CreateEngineJobRsp, error) {
	createEngineJobRsp := &setats.CreateEngineJobRsp{}
	logger.Infof("%s RunEngineJobwithSql %s", req.RequestId, req.JobContent)
	sql, errBase64 := base64.StdEncoding.DecodeString(req.JobContent)
	if errBase64 != nil {
		logger.Errorf("%s RunEngineJobwithSql base64.StdEncoding.DecodeString err:%v", req.RequestId, errBase64)
		return createEngineJobRsp, errBase64
	}
	strSql := string(sql)
	logger.Infof("%s after RunEngineJobwithSql %s", req.RequestId, strSql)
	// 暂时不允许 select 不带 limit 操作
	if strings.Contains(strSql, "select ") && strings.Contains(strSql, " from ") {
		if !strings.Contains(strSql, " limit ") {
			logger.Errorf("%s RunEngineJobwithSql select sql %s must contain limit", req.RequestId, strSql)
			return createEngineJobRsp, errors.New("select sql must contain limit")
		}
	}

	sessionId := ""
	/**
	 * 先查询上一次
	 */
	batchTask, err := service.GetLatestSessionId(&setats.BatchTaskReq{
		AppId:      req.AppId,
		InstanceId: req.InstanceId,
	})
	if err != nil {
		logger.Warningf("%s Failed CreateEngineJob_RunEngineJob to GetLatestSessionId, with errors:%+v", req.RequestId, err)
	}
	if batchTask != nil && batchTask.EngineJobId != "" {
		parts := strings.Split(batchTask.EngineJobId, constants.SetatsEngineJobIdSplit)
		if len(parts) != 3 {
			msg := fmt.Sprintf("%s CreateEngineJob_RunEngineJob engine job id:%s is not valid, valid pattern is cql-abc__1__123", req.RequestId, batchTask.EngineJobId)
			logger.Warningf(msg)
		}
		interval := time.Now().Unix() - util.LocalTimeToTimestamp(batchTask.CreateTime)
		if interval > expireTime {
			logger.Infof("%s the cache session with %s expired", req.RequestId, batchTask.EngineJobId)
		} else {
			sessionId = parts[0]
		}
	}

	reqData := &sql_gateway.RunSqlGatewayStatementReq{
		RequestBase:  req.RequestBase,
		ClusterId:    clusterGroupSerialId,
		SessionId:    sessionId,
		Sql:          req.JobContent,
		FlinkVersion: constants.Flinkversion118,
	}
	s := sqlgateway2.NewSqlGatewayService(reqData.ClusterId, reqData.SessionId)
	rsp, err := s.RunStatement(reqData)
	if err != nil {
		logger.Errorf("%s Failed CreateEngineJob to run statement, with errors:%+v", req.RequestId, err)
		return createEngineJobRsp, err
	}
	sessionId = rsp.SessionId

	operationHandleId := rsp.OperationHandleId
	errorMessageList := rsp.ErrorMessage
	if len(errorMessageList) > 0 {
		errorMessage, err := json.Marshal(errorMessageList)
		if err != nil {
			logger.Errorf("%s Failed to marshal errorMessageList, with errors:%+v", req.RequestId, err)
			return createEngineJobRsp, err
		}
		logger.Errorf("%s Failed CreateEngineJob to run statement, with errors:%+v", req.RequestId, string(errorMessage))
		return createEngineJobRsp, errors.New(string(errorMessage))
	}
	engineJobId := fmt.Sprintf("%s%s%s%s%d", sessionId, constants.SetatsEngineJobIdSplit, operationHandleId, constants.SetatsEngineJobIdSplit, time.Now().UnixNano()/int64(time.Millisecond))
	createEngineJobRsp.EngineJobId = engineJobId
	createEngineJobRsp.EngineJobLogUrl = ""
	return createEngineJobRsp, nil
}

func ConvertTime(inputTime string) string {

	// 定义输入时间格式
	inputFormat := "2006-01-02 15:04:05"

	// 解析输入时间字符串
	parsedTime, err := time.Parse(inputFormat, inputTime)
	if err != nil {
		logger.Errorf("time.Parse err:%v", err)
		return inputTime
	}

	// 定义输出时间格式
	outputFormat := "20060102150405"

	// 格式化时间为输出格式
	return parsedTime.Format(outputFormat)

}

func RunJobWithRetry(req *model5.CreateJobConfigReq, versionId int64, savepointPath string, retryCnt int) (err error) {
	cnt := 0
	for cnt < retryCnt {
		err = RunJob(req, versionId, savepointPath)

		if err == nil {
			return nil
		}
		logger.Errorf("%s CreateEngineJob Failed to run job with CreateJobConfigReq: %+v, versionId: %d,  because %+v", req.RequestId, req, versionId, err)

		cnt = cnt + 1
		time.Sleep(1 * time.Second)
	}
	return err
}

func RunJob(req *model5.CreateJobConfigReq, versionId int64, savepointPath string) error {
	runJobsDesp := jobModel.RunJobDescription{JobId: req.JobId, RunType: constants.JOB_RUN_TYPE_RUN, JobConfigVersion: versionId,
		StartMode: constants.START_MODE_LATEST, SavepointPath: savepointPath}

	runJobsDespArray := make([]jobModel.RunJobDescription, 0)
	runJobsDespArray = append(runJobsDespArray, runJobsDesp)
	jobRunReq := &jobModel.RunJobsReq{AppId: req.AppId, Uin: req.Uin, SubAccountUin: req.SubAccountUin,
		RequestId: uuid.NewRandom().String(), Region: req.Region, Version: "1.0", WorkSpaceId: req.WorkSpaceId, IsSupOwner: 1,
		RunJobDescriptions: runJobsDespArray}
	errCode, msg, _ := service7.DoRunJobs(jobRunReq, true)
	if errCode != controller.OK {
		logger.Errorf("%s Failed to start job, with serialId:%s, with appId:%d, with error msg :%s", req.RequestId, req.JobId, req.AppId, msg)
		return errors.New("failed to start job")
	}
	return nil
}

func BuildJobConfigReq(cluster *table4.Cluster, clusterGroup *table4.ClusterGroup, engineJobObj *EngineJobObj, jobId string, req *setats.CreateEngineJobReq) *model5.CreateJobConfigReq {
	reqJobConfigData := &model5.CreateJobConfigReq{}
	reqJobConfigData.AppId = clusterGroup.AppId
	reqJobConfigData.Uin = clusterGroup.OwnerUin
	reqJobConfigData.Action = "CreateJobConfig"
	reqJobConfigData.SubAccountUin = clusterGroup.CreatorUin
	reqJobConfigData.Region = clusterGroup.Region
	reqJobConfigData.WorkSpaceId = engineJobObj.WorkSpaceId
	reqJobConfigData.JobId = jobId
	reqJobConfigData.ProgramArgs = engineJobObj.ProgramArgs
	reqJobConfigData.DefaultParallelism = engineJobObj.DefaultParallelism
	reqJobConfigData.JobManagerSpec = engineJobObj.JobManagerSpec
	reqJobConfigData.TaskManagerSpec = engineJobObj.TaskManagerSpec
	reqJobConfigData.IsSupOwner = 1
	reqJobConfigData.FlinkVersion = engineJobObj.FlinkVersion

	reqJobConfigData.Properties = engineJobObj.Properties
	// 外部作业不开启自动恢复
	reqJobConfigData.AutoRecover = constants.AutoRecoverDisable

	reqJobConfigData.LogLevel = constants.LOG_LEVEL_INFO
	reqJobConfigData.LogCollect = true
	reqJobConfigData.LogCollectType = constants.JobLogCollectTypeCOS
	reqJobConfigData.COSBucket = cluster.DefaultCOSBucket
	reqJobConfigData.RequestId = engineJobObj.RequestId
	// 增加 setats依赖到作业里
	// setats refs
	setatsResourceInfoList, err := service.ListSetatsRefBySetatsSerialId(req.InstanceId)
	if err != nil {
		logger.Warningf("%s Failed to ListSetatsRefBySetatsSerialId for %s because %+v", req.RequestId, req.InstanceId, err)
	} else {
		if len(setatsResourceInfoList) > 0 {
			resourceRefs := make([]*model5.ResourceRefItem, 0)
			for _, setatsResourceInfo := range setatsResourceInfoList {
				resourceRef := &model5.ResourceRefItem{
					ResourceId: setatsResourceInfo.ResourceSerialId,
					Version:    setatsResourceInfo.Version,
					Type:       constants.RESOURCE_REF_USAGE_TYPE_DEFALUT,
				}
				resourceRefs = append(resourceRefs, resourceRef)
			}
			reqJobConfigData.ResourceRefs = resourceRefs
		}
	}
	logger.Infof("%s Create_Engine_Job_BuildJobConfigReq reqJobConfigData: %+v", req.RequestId, reqJobConfigData)
	return reqJobConfigData
}

func GetEngineJobObj(clusterGroup *table4.ClusterGroup, req *setats.CreateEngineJobReq, err error) (*EngineJobObj, error) {
	engineJobObj := &EngineJobObj{}

	sql := req.JobContent
	if sql == "" {
		msg := fmt.Sprintf("%s Failed to get sql %s", req.RequestId, sql)
		logger.Errorf(msg)
		return nil, errors.New(msg)
	}
	parameters := req.Parameters
	paramMap := make(map[string]string)
	for _, parameter := range parameters {
		paramMap[parameter.Name] = parameter.Value
	}
	defaultParallelism := int64(1)
	jobManagerSpec := float64(1)
	taskManagerSpec := float64(1)
	properties := make([]*model1.Property, 0)

	workSpaceId := paramMap[constants.WorkSpaceId]
	if workSpaceId == "" {
		msg := fmt.Sprintf("%s Failed to get workSpaceId %s", req.RequestId, workSpaceId)
		logger.Errorf(msg)
		return nil, errors.New(msg)
	}
	// 检测空间和集群是否绑定
	correlations, err := QueryCorrelation([]string{workSpaceId}, []string{clusterGroup.SerialId}, true, req.AppId)
	if err != nil {
		logger.Errorf("%s: QueryCorrelation error: %+v", req.RequestId, err)
		return nil, err
	}
	if len(correlations) < 1 {
		msg := fmt.Sprintf("%s Failed to get correlation for workSpaceId:%s, setatsId:%s", req.RequestId, workSpaceId, req.InstanceId)
		logger.Errorf(msg)
		return nil, errors.New(msg)
	}
	engineJobObj.WorkSpaceId = workSpaceId

	engineTaskId := paramMap[constants.EngineTaskId]
	if engineTaskId == "" {
		msg := fmt.Sprintf("%s Failed to get engineTaskId %s", req.RequestId, engineTaskId)
		logger.Errorf(msg)
		return nil, errors.New(msg)
	}
	flinkVersion := paramMap[constants.FlinkVersionKey]
	if flinkVersion == "" {
		msg := fmt.Sprintf("%s Failed to get flinkVersion %s", req.RequestId, flinkVersion)
		logger.Errorf(msg)
		return nil, errors.New(msg)
	}

	strProperties := paramMap[constants.Properties]
	if strProperties != "" {
		err = json.Unmarshal([]byte(strProperties), &properties)
		if err != nil {
			msg := fmt.Sprintf("%s Failed to unmarshal strProperties %s into json, with errors:%+v", req.RequestId, strProperties, err)
			logger.Errorf(msg)
			return nil, errors.New(msg)
		}
	}
	// 设置空间
	properties = append(properties, &model1.Property{
		Key:   constants.WORK_SPACE_ID_KEY,
		Value: engineJobObj.WorkSpaceId,
	})
	engineJobObj.Properties = properties

	strDefaultParallelism := paramMap[constants.DefaultParallelism]
	if strDefaultParallelism != "" {
		defaultParallelism, err = strconv.ParseInt(strDefaultParallelism, 10, 64)
		if err != nil {
			msg := fmt.Sprintf("%s Failed to parse strDefaultParallelism %s into int, with errors:%+v", req.RequestId, strDefaultParallelism, err)
			logger.Errorf(msg)
			return nil, errors.New(msg)
		}
	}
	engineJobObj.DefaultParallelism = int16(defaultParallelism)
	strJobManagerSpec := paramMap[constants.JobManagerSpec]
	if strJobManagerSpec != "" {
		jobManagerSpec, err = strconv.ParseFloat(strJobManagerSpec, 32)
		if err != nil {
			msg := fmt.Sprintf("%s Failed to parse strJobManagerSpec %s into float, with errors:%+v", req.RequestId, strJobManagerSpec, err)
			logger.Errorf(msg)
			return nil, errors.New(msg)
		}
	}
	engineJobObj.JobManagerSpec = float32(jobManagerSpec)
	strTaskManagerSpec := paramMap[constants.TaskManagerSpec]
	if strTaskManagerSpec != "" {
		taskManagerSpec, err = strconv.ParseFloat(strTaskManagerSpec, 32)
		if err != nil {
			msg := fmt.Sprintf("%s Failed to parse strTaskManagerSpec %s into float, with errors:%+v", req.RequestId, strTaskManagerSpec, err)
			logger.Errorf(msg)
			return nil, errors.New(msg)
		}
	}
	engineJobObj.TaskManagerSpec = float32(taskManagerSpec)
	strSchedulingTime := paramMap[constants.SchedulingTime]
	engineJobObj.SchedulingTime = strSchedulingTime

	programArgsMap := make(map[string]interface{})
	programArgsMap["SqlCode"] = sql
	programArgsMap["CheckpointInterval"] = 60

	programArgs, err := json.Marshal(programArgsMap)
	if err != nil {
		msg := fmt.Sprintf("%s Failed to marshal programArgsMap %+v into json, with errors:%+v", req.RequestId, programArgsMap, err)
		return nil, errors.New(msg)
	}
	engineJobObj.ProgramArgs = string(programArgs)
	engineJobObj.FlinkVersion = flinkVersion
	engineJobObj.EngineTaskId = engineTaskId
	engineJobObj.RequestId = req.RequestId
	return engineJobObj, nil
}

func QueryCorrelation(itemSpaceIds []string, clusterGroupSerialId []string, isUseable bool, appId int64) (tables []*table.ItemSpaceClusterItem, err error) {
	errorcode.DefaultDeferHandler(&err)
	sql := " SELECT a.`Status`,a.ClusterGroupId, b.SerialId as ClusterGroupSerialId,b.Name as ClusterName," +
		"c.SerialId as  WorkItemSpaceId ,c.ItemSpaceName as WorkSpaceName  FROM ItemSpacesClusters a INNER JOIN ClusterGroup b" +
		" ON a.ClusterGroupId = b.Id INNER JOIN ItemSpace c on c.Id = a.ItemSpaceId "

	cond := dao.NewCondition()
	if len(itemSpaceIds) > 0 {
		cond.In("c.SerialId", itemSpaceIds)
	}

	if len(clusterGroupSerialId) > 0 {
		cond.In("b.SerialId", clusterGroupSerialId)
	}

	if isUseable {
		cond.Ne("a.Status", constants.ITEM_SPACE_CLUSTERGROUPS__DELETED)
	}

	if appId > 0 {
		cond.Eq("a.AppId", appId)
	}

	cond.Ne("c.Status", constants.ITEM_SPACE_STATUS_DELETED)

	where, args := cond.GetWhere()
	sql += where

	txManager := service1.GetTxManager()
	_, datas, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to execute the sql:%s, with args:%+v, with errors:%+v", sql, args, err)
		return tables, err
	}
	if len(datas) == 0 {
		return tables, nil
	}

	t := make([]*table.ItemSpaceClusterItem, 0)
	for _, data := range datas {
		table := &table.ItemSpaceClusterItem{}
		err = util.ScanMapIntoStruct(table, data)
		if err != nil {
			return nil, errorcode.InternalErrorCode.NewWithErr(err)
		}
		t = append(t, table)
	}
	return t, nil
}

type EngineJobObj struct {
	Properties         []*model1.Property
	DefaultParallelism int16
	JobManagerSpec     float32
	TaskManagerSpec    float32
	ProgramArgs        string
	SchedulingTime     string
	EngineTaskId       string
	FlinkVersion       string
	WorkSpaceId        string
	RequestId          string
}

func buildCreateJobReq(req *setats.CreateEngineJobReq, engineJobObj *EngineJobObj, clusterGroupSerialId string, jobName string) *jobModel.CreateJobReq {
	return &jobModel.CreateJobReq{
		RequestBase:   req.RequestBase,
		AppId:         int32(req.AppId),
		Uin:           req.Uin,
		SubAccountUin: req.SubAccountUin,
		RequestId:     req.RequestId,
		Region:        req.Region,
		Version:       req.Version,
		Name:          jobName,
		JobType:       constants.JOB_TYPE_SQL,
		ClusterType:   constants.CLUSTER_GROUP_TYPE_PRIVATE,
		ClusterId:     clusterGroupSerialId,
		Remark:        fmt.Sprintf("wedata scheduler job %s", engineJobObj.SchedulingTime),
		FolderId:      constants.FOLDER_ROOT_ID,
		Description:   fmt.Sprintf("wedata scheduler job SchedulingTime %s, EngineTaskId %s", engineJobObj.SchedulingTime, engineJobObj.EngineTaskId),
		FlinkVersion:  engineJobObj.FlinkVersion,
		WorkSpaceId:   engineJobObj.WorkSpaceId,
	}
}
