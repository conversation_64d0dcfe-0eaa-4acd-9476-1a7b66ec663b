package cluster

import (
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service1 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/setats"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/sql_gateway"
	setats2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/setats"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	sqlgateway2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/sql_gateway"
)

func init() {
	const controllerName = "DescribeEngineJobLog"
	if code, msg := httpserver.RegisterCloudController(controllerName, &describeEngineJobLogController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type describeEngineJobLogController struct {
}

func (this *describeEngineJobLogController) CreateRequestObj() interface{} {
	return &setats.DescribeEngineJobLogReq{}
}

// https://write.woa.com/document/130992657738792960
func (this *describeEngineJobLogController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*setats.DescribeEngineJobLogReq)

	rsp, err := DescribeEngineJobLog(reqData)
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}

func DescribeEngineJobLog(req *setats.DescribeEngineJobLogReq) (rst *setats.DescribeEngineJobLogRsp, err error) {

	rst = &setats.DescribeEngineJobLogRsp{}
	defer func() {
		if errs := recover(); errs != nil {
			err = errors.New(fmt.Sprintf("DescribeEngineJobLog to panic, with errors:%+v", errs))
			logger.Errorf("%s DescribeEngineJobLog to panic, with errors:%+v", req.RequestId, errs)
		}
	}()

	engineJobId := req.EngineJobId
	if engineJobId == "" {
		logger.Errorf("%s DescribeEngineJobLog engineJobId is empty", req.RequestId)
		return rst, errors.New("engineJobId is empty")
	}

	clusterGroupSerialId := ""
	instanceId := req.InstanceId
	if instanceId == "" {
		logger.Errorf("%s DescribeEngineJobLog Failed to get setats InstanceId:%s", req.RequestId, req.InstanceId)
		return rst, errors.New("InstanceId is blank")
	}
	if strings.Contains(instanceId, "cluster-") {
		clusterGroupSerialId = instanceId
	} else {
		_setats, err := service.GetSetatsBySerialId(instanceId)
		if err != nil {
			logger.Errorf("%s DescribeEngineJobLog Failed to get setats by serial id:%s, with errors:%+v", req.RequestId, instanceId, err)
			return rst, err
		}
		if _setats.Status == constants.SETATS_DELETED || _setats.Status == constants.SETATS_ISOLATED {
			logger.Errorf("setats status is deleted or isolated")
			return rst, errors.New("setats status is deleted or isolated")
		}
		clusterGroupSerialId = _setats.ClusterGroupSerialId
	}

	batchTask, err := service.GetBatchTask(&setats.BatchTaskReq{
		AppId:       req.AppId,
		EngineJobId: engineJobId,
	})
	if err != nil {
		msg := fmt.Sprintf("%s DescribeEngineJobLog GetBatchTask error %+v with engineJobId %s", req.RequestId, err, engineJobId)
		logger.Errorf(msg)
		return rst, errors.New(msg)
	}
	if batchTask == nil {
		msg := fmt.Sprintf("%s DescribeEngineJobLog batchTask is nil with engineJobId %s", req.RequestId, engineJobId)
		logger.Errorf(msg)
		return rst, errors.New(msg)
	}

	parts := strings.Split(engineJobId, constants.SetatsEngineJobIdSplit)
	if len(parts) != 3 {
		msg := fmt.Sprintf("%s DescribeEngineJobLog engine job id:%s is not valid, valid pattern is cql-abc__1__123", req.RequestId, engineJobId)
		logger.Errorf(msg)
		return rst, errors.New(msg)

	}
	// 调度不查日志
	if batchTask.Type == constants.BatchTaskScheduleType {
		return &setats.DescribeEngineJobLogRsp{
			Logs:   []string{"ok"},
			Offset: req.Offset,
			Limit:  req.Limit,
		}, nil
	}

	batchTaskLogList, err := ListBatchTaskLog(batchTask.Id)
	if err != nil {
		msg := fmt.Sprintf("%s DescribeEngineJobLog ListBatchTaskLog error %+v with engineJobId %s", req.RequestId, err, engineJobId)
		logger.Errorf(msg)
		return rst, errors.New(msg)
	}
	rstLogs := make([]string, 0)
	if len(batchTaskLogList) > 0 {
		for _, log := range batchTaskLogList {
			rstLogs = append(rstLogs, log.Content)
		}
		return &setats.DescribeEngineJobLogRsp{
			Logs:   rstLogs,
			Offset: req.Offset,
			Limit:  req.Limit,
		}, nil
	}

	sessionId := parts[0]
	operationHandleId := parts[1]
	reqData := &sql_gateway.FetchSqlGatewayResultReq{
		RequestBase:       req.RequestBase,
		ClusterId:         clusterGroupSerialId,
		SessionId:         sessionId,
		OperationHandleId: operationHandleId,
		FlinkVersion:      constants.Flinkversion118,
	}

	s := sqlgateway2.NewSqlGatewayService(reqData.ClusterId, reqData.SessionId)
	rsp, err := s.FetchResult(reqData)
	if err != nil {
		logger.Errorf("%s Failed DescribeEngineJobLog to FetchResult, with errors:%+v", req.RequestId, err)
		return rst, err
	}
	errorMessageList := rsp.ErrorMessage
	if len(errorMessageList) > 0 {
		errorMessage := []byte("")
		errorMessage, err = json.Marshal(errorMessageList)
		if err != nil {
			logger.Errorf("%s Failed to marshal errorMessageList, with errors:%+v", req.RequestId, err)
			return rst, err
		}
		logger.Errorf("%s Failed DescribeEngineJobLog to run statement, with errors:%+v", req.RequestId, string(errorMessage))
		return &setats.DescribeEngineJobLogRsp{
			Logs:   errorMessageList,
			Offset: req.Offset,
			Limit:  req.Limit,
		}, nil
	}
	strResults, err := json.Marshal(rsp.Results)
	if err != nil {
		logger.Errorf("%s Failed to marshal rsp.Results, with errors:%+v", req.RequestId, err)
		return rst, err
	}

	rstLogs = append(rstLogs, string(strResults))

	return &setats.DescribeEngineJobLogRsp{
		Logs:   rstLogs,
		Offset: req.Offset,
		Limit:  req.Limit,
	}, nil
}

func ListBatchTaskLog(batchTaskId int64) ([]*setats2.BatchTaskLog, error) {
	rst := make([]*setats2.BatchTaskLog, 0)
	sql := "select * from BatchTaskLog where BatchTaskId=? order by OrderNumber asc"
	args := make([]interface{}, 0)
	args = append(args, batchTaskId)

	txManager := service1.GetTxManager()
	count, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to execute the sql:%s, with args:%+v, with errors:%+v", sql, args, err)
		return nil, err
	}
	if count == 0 {
		return rst, nil
	}
	for i := 0; i < count; i++ {
		batchTaskLog := &setats2.BatchTaskLog{}
		err = util.ScanMapIntoStruct(batchTaskLog, data[i])
		if err != nil {
			logger.Errorf("Faild to convert bytes into batchTaskLog, with errors:%+v")
			return rst, err
		}
		rst = append(rst, batchTaskLog)
	}
	return rst, nil
}
