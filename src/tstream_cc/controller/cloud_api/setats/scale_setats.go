package cluster

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/setats"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
)

func init() {
	const controllerName = "ScaleSetats"
	if code, msg := httpserver.RegisterCloudController(controllerName, &scaleSetatsController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type scaleSetatsController struct {
}

func (this *scaleSetatsController) CreateRequestObj() interface{} {
	return &setats.ScaleSetatsReq{}
}

func (this *scaleSetatsController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*setats.ScaleSetatsReq)

	rsp, err := service.ScaleSetats(reqData)
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}
