package cluster

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/setats"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
)

func init() {
	const controllerName = "RestartSetats"
	if code, msg := httpserver.RegisterCloudController(controllerName, &restartSetatsController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type restartSetatsController struct {
}

func (this *restartSetatsController) CreateRequestObj() interface{} {
	return &setats.RestartSetatsReq{}
}

func (this *restartSetatsController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*setats.RestartSetatsReq)

	rsp, err := service.RestartSetats(reqData)
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}
