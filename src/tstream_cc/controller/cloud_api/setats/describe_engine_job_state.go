package cluster

import (
	"encoding/json"
	"errors"
	"fmt"
	"path"
	"strconv"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service1 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/setats"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/sql_gateway"
	setats2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/setats"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	sqlgateway2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/sql_gateway"
)

func init() {
	const controllerName = "DescribeEngineJobState"
	if code, msg := httpserver.RegisterCloudController(controllerName, &describeEngineJobStateController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type describeEngineJobStateController struct {
}

func (this *describeEngineJobStateController) CreateRequestObj() interface{} {
	return &setats.DescribeEngineJobStateReq{}
}

// https://write.woa.com/document/130992657738792960
func (this *describeEngineJobStateController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*setats.DescribeEngineJobStateReq)

	rsp, err := DescribeEngineJobState(reqData)
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}

func DescribeEngineJobState(req *setats.DescribeEngineJobStateReq) (rst *setats.DescribeEngineJobStateRsp, err error) {

	rst = &setats.DescribeEngineJobStateRsp{}
	defer func() {
		if errs := recover(); errs != nil {
			err = errors.New(fmt.Sprintf("%v", errs))
			logger.Errorf("%s DescribeEngineJobState to panic, with errors:%+v", req.RequestId, errs)
		}
	}()

	engineJobId := req.EngineJobId
	if engineJobId == "" {
		logger.Errorf("%s DescribeEngineJobState engineJobId is empty", req.RequestId)
		return rst, errors.New("engineJobId is empty")
	}

	batchTask, err := service.GetBatchTask(&setats.BatchTaskReq{
		AppId:       req.AppId,
		EngineJobId: engineJobId,
	})
	if err != nil {
		msg := fmt.Sprintf("%s DescribeEngineJobState GetBatchTask error %+v with engineJobId %s", req.RequestId, err, engineJobId)
		logger.Errorf(msg)
		return rst, errors.New(msg)
	}
	if batchTask == nil {
		msg := fmt.Sprintf("%s DescribeEngineJobState batchTask is nil with engineJobId %s", req.RequestId, engineJobId)
		logger.Errorf(msg)
		return rst, errors.New(msg)
	}

	parts := strings.Split(engineJobId, constants.SetatsEngineJobIdSplit)
	if len(parts) != 3 {
		msg := fmt.Sprintf("%s DescribeEngineJobState engine job id:%s is not valid, valid pattern is cql-abc__1__123", req.RequestId, engineJobId)
		logger.Errorf(msg)
		return rst, errors.New(msg)

	}
	if batchTask.Type == constants.BatchTaskRunType {
		if batchTask.Status == constants.EngineJobStateKilled {
			return &setats.DescribeEngineJobStateRsp{
				State:      constants.EngineJobStateKilled,
				Percentage: 0,
				CostTime:   0,
				CreateTime: fmt.Sprintf("%d", util.LocalTimeToUnixMilli(batchTask.CreateTime)),
			}, nil
		}
		clusterGroupSerialId := ""
		instanceId := req.InstanceId
		if instanceId == "" {
			logger.Errorf("%s DescribeEngineJobState Failed to get setats InstanceId:%s", req.RequestId, req.InstanceId)
			return rst, errors.New("InstanceId is blank")
		}
		if strings.Contains(instanceId, "cluster-") {
			clusterGroupSerialId = instanceId
		} else {
			_setats, err := service.GetSetatsBySerialId(instanceId)
			if err != nil {
				logger.Errorf("%s DescribeEngineJobState Failed to get setats by serial id:%s, with errors:%+v", req.RequestId, instanceId, err)
				return rst, err
			}
			if _setats.Status == constants.SETATS_DELETED || _setats.Status == constants.SETATS_ISOLATED {
				logger.Errorf("%s setats status is deleted or isolated", req.RequestId)
				return rst, errors.New("setats status is deleted or isolated")
			}
			clusterGroupSerialId = _setats.ClusterGroupSerialId
		}

		sessionId := parts[0]
		operationHandleId := parts[1]
		nextResultUri := ""
		if batchTask.NextResultUri != "" {
			nextResultUri = batchTask.NextResultUri
		}
		reqData := &sql_gateway.FetchSqlGatewayResultReq{
			RequestBase:       req.RequestBase,
			ClusterId:         clusterGroupSerialId,
			SessionId:         sessionId,
			OperationHandleId: operationHandleId,
			ResultUri:         nextResultUri,
			FlinkVersion:      constants.Flinkversion118,
		}
		s := sqlgateway2.NewSqlGatewayService(reqData.ClusterId, reqData.SessionId)
		rsp := &sql_gateway.FetchSqlGatewayResultRsp{}
		rsp, err = s.FetchResult(reqData)
		if err != nil {
			logger.Errorf("%s Failed DescribeEngineJobStateRsp to run statement, with errors:%+v", req.RequestId, err)
			return rst, err
		}
		logger.Debugf("%s DescribeEngineJobState_FetchResult rsp:%+v", req.RequestId, rsp)
		errorMessageList := rsp.ErrorMessage
		if len(errorMessageList) > 0 {
			errorMessage := []byte("")
			errorMessage, err = json.Marshal(errorMessageList)
			if err != nil {
				logger.Errorf("%s Failed to marshal errorMessageList, with errors:%+v", req.RequestId, err)
				return rst, err
			}
			logger.Errorf("%s Failed DescribeEngineJobResult to run statement, with errors:%+v", req.RequestId, string(errorMessage))
			return &setats.DescribeEngineJobStateRsp{
				State:      -1,
				Percentage: 0,
				CostTime:   10,
				CreateTime: fmt.Sprintf("%d", util.LocalTimeToUnixMilli(batchTask.CreateTime)),
			}, nil
		}

		// 保存 BatchTaskLog
		strResults, err := json.Marshal(rsp.Results)
		if err != nil {
			logger.Errorf("%s Failed to marshal rsp.Results, with errors:%+v", req.RequestId, err)
			return &setats.DescribeEngineJobStateRsp{
				State:      -1,
				Percentage: 0,
				CostTime:   10,
				CreateTime: fmt.Sprintf("%d", util.LocalTimeToUnixMilli(batchTask.CreateTime)),
			}, nil
		}
		orderNumber := 0
		if nextResultUri != "" {
			cleanPath := path.Clean(nextResultUri)
			base := path.Base(cleanPath)
			if num, err := strconv.Atoi(base); err == nil {
				logger.Infof("%s last number is: %d", req.RequestId, num)
				orderNumber = num
			} else {
				logger.Warningf("can not convert %s to number", base)
			}

		}
		// 只保留 50 条日志， 防止异常情况把数据库撑爆
		if orderNumber < 50 {
			service1.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
				batchTaskLog := &setats2.BatchTaskLog{
					BatchTaskId: batchTask.Id,
					Content:     string(strResults),
					OrderNumber: orderNumber,
					CreateTime:  util.GetCurrentTime(),
					UpdatedTime: util.GetCurrentTime(),
				}
				_, err1 := tx.SaveObjectDoNotPanic(batchTaskLog, "BatchTaskLog")
				if err1 != nil && !strings.Contains(strings.ToLower(err.Error()), "duplicate entry") {
					logger.Errorf("%s Failed to save batchTask, with errors:%+v", req.RequestId, err)
					return err1
				}
				return nil
			}).Close()
		}
		// 任务当前的状态，0：初始化 1：任务运行中 2：任务执行成功 -1：任务执行失败 -3：用户手动终止
		if rsp.NextResultUri == "" {
			return &setats.DescribeEngineJobStateRsp{
				State:      2,
				Percentage: 0,
				CostTime:   10,
				CreateTime: fmt.Sprintf("%d", util.LocalTimeToUnixMilli(batchTask.CreateTime)),
			}, nil
		} else {
			//更新 NextResultUri
			service1.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
				tx.ExecuteSqlWithArgs("update BatchTask set NextResultUri=? where Id=?",
					rsp.NextResultUri, batchTask.Id)
				return nil
			}).Close()
			return &setats.DescribeEngineJobStateRsp{
				State:      1,
				Percentage: 0,
				CostTime:   10,
				CreateTime: fmt.Sprintf("%d", util.LocalTimeToUnixMilli(batchTask.CreateTime)),
			}, nil
		}
	}
	jobId := parts[0]
	job, err := service1.GetJobBySerialId(req.AppId, jobId)
	if err != nil {
		logger.Errorf("%s: DescribeEngineJobState GetJobBySerialId  %s error: %+v", req.RequestId, jobId, err)
		return rst, err
	}
	// 任务当前的状态，0：初始化 1：任务运行中 2：任务执行成功 -1：任务执行失败 -3：用户手动终止
	jobStatus := job.Status
	var state int64 = 0
	// 手动终止
	if (job.Status == constants.JOB_STATUS_STOPPED || job.Status == constants.JOB_STATUS_FINISHED) &&
		batchTask.Status == constants.EngineJobStateKilled {
		state = constants.EngineJobStateKilled
	} else if jobStatus == constants.JOB_STATUS_RUNNING {
		state = 1
	} else if jobStatus == constants.JOB_STATUS_STOPPED && job.LastOpResult != "" {
		state = -1
	} else if jobStatus == constants.JOB_STATUS_STOPPED && job.LastOpResult == "" {
		state = 2
	} else if job.Status == constants.JOB_STATUS_PROGRESS &&
		(job.ProgressDesc == constants.STOP_JOB_PROGRESS_DESC || job.ProgressDesc == constants.PAUSE_JOB_PROGRESS_DESC) {
		// kill结果：-3：已终止；  -4： kill中；
		state = constants.EngineJobStateKilling
	}
	startTime := job.CreateTime
	if job.StartTime != "" && job.StartTime != "0000-00-00 00:00:00" {
		startTime = job.StartTime
	}
	costTime, err := service2.GetCurrentRunMillis(startTime)
	if err != nil {
		logger.Warningf("%s DescribeEngineJobState GetCurrentRunMillis error:%+v", req.RequestId, err)
		costTime = 0
	}
	if job.TotalRunMillis != 0 {
		costTime = job.TotalRunMillis
	}
	createTime := util.LocalTimeToUnixMilli(job.CreateTime)
	return &setats.DescribeEngineJobStateRsp{
		State:      state,
		Percentage: 0,
		CostTime:   costTime,
		CreateTime: fmt.Sprintf("%d", createTime),
	}, nil
}
