package chdfs

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	chdfs2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/chdfs"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/chdfs"
)

func init() {
	const controllerName = "CreateCHDFSAccessGroup"
	if code, msg := httpserver.RegisterCloudController(controllerName, &createCHDFSAccessGroupController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type createCHDFSAccessGroupController struct {
}

func (this *createCHDFSAccessGroupController) CreateRequestObj() interface{} {
	return &chdfs2.CreateCHDFSAccessGroupReq{}
}

func (this *createCHDFSAccessGroupController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*chdfs2.CreateCHDFSAccessGroupReq)

	svr := chdfs.NewCreateCHDFSAccessGroupService(reqData)
	rsp, err := svr.CreateCHDFSAccessGroup()
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}
