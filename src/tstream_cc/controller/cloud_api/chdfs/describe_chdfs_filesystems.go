package chdfs

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	chdfs2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/chdfs"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/chdfs"
)

func init() {
	const controllerName = "DescribeCHdfsFileSystems"
	if code, msg := httpserver.RegisterCloudController(controllerName, &describeCHdfsFileSystemsController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type describeCHdfsFileSystemsController struct {
}

func (this *describeCHdfsFileSystemsController) CreateRequestObj() interface{} {
	return &chdfs2.DescribeCHdfsFileSystemsReq{}
}

func (this *describeCHdfsFileSystemsController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*chdfs2.DescribeCHdfsFileSystemsReq)

	svr := chdfs.NewDescribeCHdfsFileSystemsService(reqData)
	rsp, err := svr.DescribeCHdfsFileSystems()
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}
