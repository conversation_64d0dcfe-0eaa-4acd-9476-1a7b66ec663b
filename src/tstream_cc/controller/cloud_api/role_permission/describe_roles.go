package role_permission

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/role_permission"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/role_permission"
)

/**
 * <AUTHOR>
 * @Description //TODO $
 * @Date $ $
 **/

func init()  {
	const controllerName = "DescribeRoles"
	if code, msg := httpserver.RegisterCloudController(controllerName, &DescribeRolesController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type DescribeRolesController struct {

}

func (d *DescribeRolesController) CreateRequestObj() interface{} {
	return &model.DescribeRolesReq{};
}

func (d *DescribeRolesController) Process(req interface{}, eventId int64) (cloudApiErrorCode string, msg string, rsp interface{}) {
	reqData := req.(*model.DescribeRolesReq)

	return service.DescribeRoles(reqData)
}
