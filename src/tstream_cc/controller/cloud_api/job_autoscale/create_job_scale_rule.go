package job_autoscale

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_autoscale"
	job_autoscale2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job_autoscale"
)

func init() {
	const controllerName = "CreateJobScaleRule"
	if code, msg := httpserver.RegisterCloudController(controllerName, &CreateJobScaleRuleController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type CreateJobScaleRuleController struct {
}

func (this *CreateJobScaleRuleController) CreateRequestObj() interface{} {
	return &job_autoscale.CreateJobScaleRuleReq{}
}

func (this *CreateJobScaleRuleController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*job_autoscale.CreateJobScaleRuleReq)
	rsp, err := job_autoscale2.DoCreateJobScaleRule(reqData)
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}
