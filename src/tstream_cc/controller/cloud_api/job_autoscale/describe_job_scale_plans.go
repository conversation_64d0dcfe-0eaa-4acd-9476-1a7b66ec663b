package job_autoscale

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_autoscale"
	job_autoscale2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job_autoscale"
)

func init() {
	const controllerName = "DescribeJobScalePlans"
	if code, msg := httpserver.RegisterCloudController(controllerName, &DescribeJobScalePlansController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type DescribeJobScalePlansController struct {
}

func (this *DescribeJobScalePlansController) CreateRequestObj() interface{} {
	return &job_autoscale.DescribeJobScalePlansReq{}
}

func (this *DescribeJobScalePlansController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*job_autoscale.DescribeJobScalePlansReq)
	rsp, err := job_autoscale2.DoDescribeJobScalePlan(reqData)
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}
