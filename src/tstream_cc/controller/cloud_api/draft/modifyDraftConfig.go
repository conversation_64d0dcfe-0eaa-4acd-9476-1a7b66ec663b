package draft

import (
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/draft"
	draft3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/draft"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job_config"
)

func init() {
	const controllerName = "ModifyDraftConfig"
	if code, msg := httpserver.RegisterCloudController(controllerName, &modifyDraftController{}); code != 0 {
		logger.Errorf("Fail to register controller %s, code %d, err %s", controllerName, code, msg)
	}
}

type modifyDraftController struct {
}

func (c *modifyDraftController) CreateRequestObj() interface{} {
	return &draft.ModifyDraftReq{}
}

func (c *modifyDraftController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*draft.ModifyDraftReq)
	reqData.ProgramArgs = service.GetRealProgramArgs(reqData.ProgramArgs, reqData.ProgramArgsAfterGzip)
	rsp, err := draft3.DoCloudApiModifyDraft(reqData)
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		if code == errorcode.InternalErrorCode {
			code = errorcode.InternalErrorCode_JobConfigUpdateFailed
		}
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}
