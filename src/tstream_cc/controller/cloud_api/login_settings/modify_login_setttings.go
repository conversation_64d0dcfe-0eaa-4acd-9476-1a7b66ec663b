package login_settings

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	login_settings2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/login_settings"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/login_settings"
)

import (
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
)

func init() {
	const controllerName = "ModifyLoginSettings"
	if code, msg := httpserver.RegisterCloudController(controllerName, &modifyLoginSettingsController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type modifyLoginSettingsController struct {
}

func (this *modifyLoginSettingsController) CreateRequestObj() interface{} {
	return &login_settings2.ModifyLoginSettingsRequest{}
}

func (this *modifyLoginSettingsController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*login_settings2.ModifyLoginSettingsRequest)

	rsp, err := (&login_settings.ModifyLoginSettingsService{}).ModifyLoginSettings(reqData)
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}
