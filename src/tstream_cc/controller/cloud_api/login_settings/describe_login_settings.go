package login_settings

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	login_settings2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/login_settings"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/login_settings"
)

import (
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
)

func init() {
	const controllerName = "DescribeLoginSettings"
	if code, msg := httpserver.RegisterCloudController(controllerName, &describeLoginSettingsController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type describeLoginSettingsController struct {
}

func (this *describeLoginSettingsController) CreateRequestObj() interface{} {
	return &login_settings2.DescribeLoginSettingsRequest{}
}

func (this *describeLoginSettingsController) Process(req interface{}, eventId int64) (string, string, interface{}) {

	reqData := req.(*login_settings2.DescribeLoginSettingsRequest)

	rsp, err := (&login_settings.DescribeLoginSettingsService{}).DescribeLoginSettings(reqData)
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}
