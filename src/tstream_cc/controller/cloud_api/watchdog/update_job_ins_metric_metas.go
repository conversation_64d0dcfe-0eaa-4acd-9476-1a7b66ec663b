package watchdog

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/watchdog"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/job"
)

/**
 * 用于watchdog更新运行作业实例指标的元数据信息
 *
 */
func init() {
	const controllerName = "UpdateJobInsMetricMetas"
	if code, msg := httpserver.RegisterCloudController(controllerName, &updateJobInsMetricMetasController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ", msg: " + msg)
	}
}

type updateJobInsMetricMetasController struct{}

func (this *updateJobInsMetricMetasController) CreateRequestObj() interface{} {
	return &watchdog.UpdateJobInsMetricMetasReq{}
}

func (this *updateJobInsMetricMetasController) Process(request interface{}, eventId int64) (string, string, interface{}) {
	reqData := request.(*watchdog.UpdateJobInsMetricMetasReq)
	_, msg, resp, err := service.UpdateJobInsMetricMetas(reqData)
	if err != nil {
		logger.Errorf("[%s] %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), resp
	}
	return controller.OK, msg, resp
}
