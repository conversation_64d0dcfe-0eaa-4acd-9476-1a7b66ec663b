package watchdog

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/watchdog"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/job"
)

/**
 * 用于watchdog更新运行作业的实际的tm运行并行度。更新JobInstance表的 TmRunningCuNum
 * 详情查看：https://tapd.woa.com/20358692/prong/stories/view/1020358692885380813
 */
func init() {
	const controllerName = "UpdateJobResource"
	if code, msg := httpserver.RegisterCloudController(controllerName, &updateJobResourceController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ", msg: " + msg)
	}
}

type updateJobResourceController struct{}

func (this *updateJobResourceController) CreateRequestObj() interface{} {
	return &watchdog.UpdateJobResourceReq{}
}

func (this *updateJobResourceController) Process(request interface{}, eventId int64) (string, string, interface{}) {
	reqData := request.(*watchdog.UpdateJobResourceReq)
	_, msg, resp, err := service.UpdateJobResource(reqData)
	if err != nil {
		logger.Errorf("[%s] %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), resp
	}
	return controller.OK, msg, resp
}
