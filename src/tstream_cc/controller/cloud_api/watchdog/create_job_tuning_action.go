package watchdog

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_autoscale"
	job_autoscale2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job_autoscale"
)

func init() {
	const controllerName = "CreateJobTuningAction"
	if code, msg := httpserver.RegisterCloudController(controllerName, &CreateJobTuningActionController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ", msg: " + msg)
	}
}

type CreateJobTuningActionController struct {
}

func (this *CreateJobTuningActionController) CreateRequestObj() interface{} {
	return &job_autoscale.CreateJobTuningActionReq{}
}

func (this *CreateJobTuningActionController) Process(request interface{}, eventId int64) (string, string, interface{}) {
	reqData := request.(*job_autoscale.CreateJobTuningActionReq)
	rsp, err := job_autoscale2.DoCreateJobTuningAction(reqData)
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), &job_autoscale.CreateJobTuningActionResp{IsSucc: controller.FALSE}
	}
	return controller.OK, controller.NULL, rsp
}
