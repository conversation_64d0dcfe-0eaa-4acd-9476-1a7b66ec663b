package watchdog

import (
	"encoding/base64"
	"flag"
	"fmt"
	"os"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/barad"
	"testing"
)

func TestJobFailed(t *testing.T) {
	byteArray := []byte("test_alert")
	base64Data := base64.StdEncoding.EncodeToString(byteArray)
	req := &barad.JobAlertReq{
		JobId:     "cql-0004i5j2",
		Type:      "4",
		Message:   string(base64Data),
		EventName: "test_job_alert",
		Important: true,
		Status:    0,
	}
	controller := &JobAlertController{}
	controller.Process(req, 0)

}

var (
	fTestTime = flag.Int64("test.time", 60*60*24*14, "")
	dbUrl     = flag.String("test.db.url", "root:asdfQWER123@tcp(9.134.75.242:3306)/galileo-shanghai?charset=utf8", "")
)

func init() {
	testing.Init()
	flag.Parse()
	tx, err := dao.NewDataSourceTransactionManager(*dbUrl)
	if err != nil {
		fmt.Println("darwin Process create DataSourceTransactionManager err", err)
		os.Exit(-1)
	}
	service.SetTxManager(tx)
}
