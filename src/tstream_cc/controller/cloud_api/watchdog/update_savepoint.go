package watchdog

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/watchdog"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/job"
)

func init() {
	const controllerName = "UpdateSavepoint"
	if code, msg := httpserver.RegisterCloudController(controllerName, &updateSavepointController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ", msg: " + msg)
	}
}

type updateSavepointController struct{}

func (this *updateSavepointController) CreateRequestObj() interface{} {
	return &watchdog.UpdateSavepointReq{}
}

func (this *updateSavepointController) Process(request interface{}, eventId int64) (string, string, interface{}) {
	reqData := request.(*watchdog.UpdateSavepointReq)
	_, msg, rsp, err := service.UpdateSavepoint(reqData)
	if err != nil {
		logger.Errorf("[%s] %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, msg, rsp
}
