package watchdog

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/watchdog"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/event"
)

func init() {
	const controllerName = "CreateEvent"
	if code, msg := httpserver.RegisterCloudController(controllerName, &createEventController{}); code != 0 {
		logger.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ", msg: " + msg)
	}
}

type createEventController struct{}

func (*createEventController) CreateRequestObj() interface{} {
	return &watchdog.CreateEventReq{}
}

type eventRsp struct {
	Events []*watchdog.EventResp
}

func (*createEventController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*watchdog.CreateEventReq)
	list, err := event.DoCreateEvent(reqData)
	rsp := &eventRsp{
		Events: list,
	}
	if err != nil {
		logger.Errorf("%s CreateEvent error: %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}

	return controller.OK, controller.NULL, rsp
}
