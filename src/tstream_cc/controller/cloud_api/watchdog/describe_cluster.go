package watchdog

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/watchdog"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
)

func init() {
	const controllerName = "DescribeCluster"
	if code, msg := httpserver.RegisterCloudController(controllerName, &describeClusterController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ", msg: " + msg)
	}
}

type describeClusterController struct{}

func (this *describeClusterController) CreateRequestObj() interface{} {
	return &watchdog.DescribeClusterReq{}
}

func (this *describeClusterController) Process(request interface{}, eventId int64) (string, string, interface{}) {
	reqData := request.(*watchdog.DescribeClusterReq)
	rsp, err := (&service2.DescribeClusterService{}).DescribeCluster(reqData, eventId)
	if err != nil {
		logger.Errorf("[%s] %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}
