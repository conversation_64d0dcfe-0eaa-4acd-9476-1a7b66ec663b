package watchdog

import (
	"encoding/base64"
	"errors"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/barad"
	service5 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/barad"
)

func init() {
	const controllerName = "JobAlert"
	if code, msg := httpserver.RegisterCloudController(controllerName, &JobAlertController{}); code != 0 {
		logger.Errorf("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type JobAlertController struct {
}

func (this *JobAlertController) CreateRequestObj() interface{} {
	return &barad.JobAlertReq{}
}

func (this *JobAlertController) Process(request interface{}, eventId int64) (string, string, interface{}) {
	reqData := request.(*barad.JobAlertReq)

	// 兼容历史集群, 如果没有传 EventName 字段, 则默认是 oceanus_job_fail 事件
	if reqData.EventName == "" {
		logger.Debugf("No EventName field in JobAlertReq, treat it at `oceanus_job_fail` for backward-compatibility")
		reqData.EventName = constants.BARAD_EVENT_ALERT_NAME_JOB_FAIL
	}

	// 如果用户没有传入 Type, 如果是已知类型（2、3）, 则自动填充 Type 字段
	if reqData.Type == "" {
		if eventType, ok := constants.EventNameToTypeMapping[reqData.EventName]; ok {
			logger.Debugf("No event type is provided, will convert %s to %s", reqData.EventName, eventType)
			reqData.Type = eventType
		} else {
			logger.Warningf("Unknown event name %s, but no type is provided", reqData.EventName)
			return controller.InvalidRequestParam, "Event type is needed for unknown event name", nil
		}
	}

	// 处理 Message 字段中文丢失问题 (尝试用 Base64 解码, 如果失败则原样输出, 成功则替换原有 Message)
	decodedMessage, err := base64.StdEncoding.DecodeString(reqData.Message)
	if err != nil {
		logger.Debugf("Message is not in base64 format, skip")
	} else {
		reqData.Message = string(decodedMessage)
	}

	var retCode int64
	var retMsg string
	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		retCode, retMsg, err = service5.ProcessEventAlert(reqData)
		if retCode != 0 {
			logger.Warningf("Failed to send alert to barad, retCode %d, retMsg %s, with error: %+v", retCode, retMsg, err)
			return errors.New(retMsg)
		}
		return nil
	}).Close()

	rsp := &barad.JobAlertRsp{
		RetCode: retCode,
		RetMsg:  retMsg,
	}

	return controller.OK, retMsg, rsp
}
