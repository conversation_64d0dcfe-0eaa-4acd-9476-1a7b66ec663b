package watchdog

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/watchdog"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/job"
)

/*
*
Watchdog 专用, 用于恢复集群（例如 YARN 的 Application / Container)
*/
func init() {
	const controllerName = "RecoverJob"
	if code, msg := httpserver.RegisterCloudController(controllerName, &recoverJobController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ", msg: " + msg)
	}
}

type recoverJobController struct{}

func (this *recoverJobController) CreateRequestObj() interface{} {
	return &watchdog.RecoverJobReq{}
}

func (this *recoverJobController) Process(request interface{}, eventId int64) (string, string, interface{}) {
	reqData := request.(*watchdog.RecoverJobReq)
	_, msg, resp, err := service.DoRecoverJob(reqData)
	if err != nil {
		logger.Errorf("[%s] %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), resp
	}
	return controller.OK, msg, resp
}
