package watchdog

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/watchdog"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/job"
)

/*
*
Watchdog 专用
*/
func init() {
	const controllerName = "NotifyJobSucceeded"
	if code, msg := httpserver.RegisterCloudController(controllerName, &notifyJobSucceededController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ", msg: " + msg)
	}
}

type notifyJobSucceededController struct{}

func (this *notifyJobSucceededController) CreateRequestObj() interface{} {
	return &watchdog.NotifyJobReq{}
}

func (this *notifyJobSucceededController) Process(request interface{}, eventId int64) (string, string, interface{}) {
	reqData := request.(*watchdog.NotifyJobReq)
	_, msg, resp, err := service.DoNotifyJobSucceeded(reqData)
	if err != nil {
		logger.Errorf("[%s] %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), resp
	}
	return controller.OK, msg, resp
}
