package watchdog

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_config"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job_config"
)

func init() {
	const controllerName = "CreateExternalJobConfig"
	if code, msg := httpserver.RegisterCloudController(controllerName, &createExternalJobConfigController{}); code != 0 {
		logger.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ", msg: " + msg)
	}
}

type createExternalJobConfigController struct{}

func (*createExternalJobConfigController) CreateRequestObj() interface{} {
	return &model.CreateJobConfigReq{}
}

func (*createExternalJobConfigController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*model.CreateJobConfigReq)
	// 外部作业不开启自动恢复
	reqData.AutoRecover = constants.AutoRecoverDisable
	rsp, err := service.DoCreateExternakJobConfig(reqData)
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}
