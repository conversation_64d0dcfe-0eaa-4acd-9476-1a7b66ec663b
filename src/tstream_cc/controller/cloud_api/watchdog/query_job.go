package watchdog

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"

	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job"
)

/*
*
Watchdog 专用 (新版, 替代 listJobByCluster)
*/
func init() {
	const controllerName = "QueryJob"
	if code, msg := httpserver.RegisterCloudController(controllerName, &queryJobController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ", msg: " + msg)
	}
}

type queryJobReq struct {
	apiv3.RequestBase
	ClusterId         int64  `check:"nullable:true|range:(0,INF)"`
	JobStatus         int32  `check:"nullable:true|range:[-2,6]"`
	JobInstanceStatus int32  `check:"nullable:true|range:[-2,6]"`
	SerialId          string `check:"nullable:true"`
	JobName           string
	ManageType        string
	FlinkJobType      int8
	ClusterSerialId   string
}

type queryJobController struct{}

func (this *queryJobController) CreateRequestObj() interface{} {
	return &queryJobReq{}
}

type queryJonRsp struct {
	Jobs []*service.QueryJobInstanceWithAutoTuning
}

func (this *queryJobController) Process(request interface{}, eventId int64) (string, string, interface{}) {
	reqData := request.(*queryJobReq)

	// serialId 和 clusterId 必须传一个，jobStatus 和 jobInstanceStatus 必须传一个, 避免大量无效返回结果
	if reqData.ClusterId <= 0 && reqData.SerialId == "" && reqData.JobName == "" {
		return controller.FailedOperation_JobQueryFailure, "You must provide either ClusterId or SerialId", nil
	}
	logger.Infof("QueryJob1 %s", util.GetCurrentTime())
	list, err := service.QueryJobWithAutoTuning(reqData.ClusterId, reqData.JobStatus, reqData.JobInstanceStatus,
		reqData.SerialId, reqData.JobName, reqData.ManageType, reqData.ClusterSerialId, reqData.FlinkJobType)
	if err != nil {
		logger.Error("Failed to QueryJobBy, ClusterId:", reqData.ClusterId, reqData.JobStatus, reqData.JobInstanceStatus, reqData.SerialId, ",errors: ", err.Error())
		return controller.FailedOperation_JobQueryFailure, err.Error(), nil
	}
	rsp := &queryJonRsp{
		Jobs: list,
	}
	logger.Infof("QueryJob2 %s", util.GetCurrentTime())
	if len(list) < 1 {
		return controller.OK, controller.NULL, rsp
	}
	err2 := service.SetJobConfigVal(list)
	logger.Infof("QueryJob3 %s", util.GetCurrentTime())
	if err2 != nil {
		logger.Error(err2.Error())
		return controller.FailedOperation_JobQueryFailure, err2.Error(), nil
	}
	return controller.OK, controller.NULL, rsp
}
