package watchdog

import (
	"encoding/base64"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/notify"
	"tencentcloud.com/tstream_galileo/src/common/notify/tof"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/alarm"
	"time"
)

func init() {
	const controllerName = "SendWarningInfo"
	if code, msg := httpserver.RegisterCloudController(controllerName, &sendWarningInfoController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type sendWarningInfoController struct {
}

func (this *sendWarningInfoController) CreateRequestObj() interface{} {
	return &alarm.SendWarningInfoReq{}
}
func (this *sendWarningInfoController) Process(request interface{}, eventId int64) (retCode string, errMsg string, response interface{}) {
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf("Failed to call sendWarningInfo because %+v", err)
			retCode = controller.InternalError
			errMsg = "Fatal system error"
		}
	}()

	reqData := request.(*alarm.SendWarningInfoReq)

	// 参数校验
	var code int64
	code, errMsg = this.checkReqParam(reqData)
	if code != controller.SUCCESS {
		return
	}

	// 打印 Event ID 和请求信息
	logger.Infof("[%d] Request data %+v", eventId, reqData)

	resp := alarm.SendWarningInfoResp{Statuses: make([]alarm.SendStatus, 0)}
	response, retCode, errMsg = resp, controller.OK, "ok"

	notifyAlarm := notify.NewNotify()

	message := reqData.Message
	title := reqData.Title

	// TODO 由于 WatchDog 调用 Galileo 发送告警时，如果有中文会有乱码问题。在 Galileo 侧抓包，收到的字符乱码；在 WatchDog 侧输出
	// message 的 hex 编码，在进行解码中文正常；nginx 已添加 UTF-8 字符集；任然出现中文乱码。怀疑是镜像字符集问题。临时解决方案通过
	// Base64 编码传递 message
	msgDecodeStrBytes, err := base64.StdEncoding.DecodeString(message)
	if err == nil {
		message = string(msgDecodeStrBytes)
	}

	titleDecodeStrBytes, err := base64.StdEncoding.DecodeString(title)
	if err == nil {
		title = string(titleDecodeStrBytes)
	}

	err = notifyAlarm.SendByCustomParamTof4(title, message, reqData.Receiver, reqData.Sender)

	tofMsg := tof.TOFMessage{
		EventName: "未知(供外部系统发送告警)",
		Time:      time.Now().Format("2006-01-02 15:04:05"),
		EventMessage: map[string]interface{}{
			"eventId": eventId,
			"title":   title,
			"message": message,
		},
	}
	notifyAlarm.SyncSendMessageKafka(tofMsg)

	return
}

func (this *sendWarningInfoController) checkReqParam(reqData *alarm.SendWarningInfoReq) (errCode int64, errMsg string) {
	if len(reqData.Sender) < 0 {
		errCode, errMsg = controller.PARAMSERR, controller.InvalidRequestParam+" [Sender is not allowed to be empty]"
		return
	}

	if len(reqData.Message) < 0 {
		errCode, errMsg = controller.PARAMSERR, controller.InvalidRequestParam+" [Message is not allowed to be empty]"
		return
	}

	if len(reqData.Title) < 0 {
		errCode, errMsg = controller.PARAMSERR, controller.InvalidRequestParam+" [Title is not allowed to be empty]"
		return
	}

	return controller.SUCCESS, "ok"
}
