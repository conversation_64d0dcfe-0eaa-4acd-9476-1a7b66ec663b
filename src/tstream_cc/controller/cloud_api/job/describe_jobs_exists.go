package job

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job"
)

func init() {
	const controllerName = "DescribeJobsExists"
	if code, msg := httpserver.RegisterCloudController(controllerName, &describejobsexistsController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type describejobsexistsController struct {
}

func (this *describejobsexistsController) CreateRequestObj() interface{} {
	return &model.DescribeJobsExistsReq{}
}

func (this *describejobsexistsController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*model.DescribeJobsExistsReq)
	return service.DoDescribeJobsExists(reqData)
}
