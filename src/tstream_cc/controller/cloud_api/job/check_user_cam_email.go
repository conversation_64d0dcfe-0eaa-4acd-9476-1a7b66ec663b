package job

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cam"
)

func init() {
	const controllerName = "CheckUserCamEmail"
	if code, msg := httpserver.RegisterCloudController(controllerName, &checkUserCamEmailController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type checkUserCamEmailController struct {
}

func (this *checkUserCamEmailController) CreateRequestObj() interface{} {
	return &model.CheckUserCamEmailReq{}
}

func (this *checkUserCamEmailController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*model.CheckUserCamEmailReq)

	return cam.CheckUserCamEmail(reqData.Uin, reqData.SubAccountUin, reqData.Region)
}
