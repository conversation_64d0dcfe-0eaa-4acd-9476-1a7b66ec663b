package billing

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/billing"
	billing2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/billing"
)

func init() {
	const controllerName = "DeleteClusterInstance"
	if code, msg := httpserver.RegisterCloudController(controllerName, &DeleteClusterInstance{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type DeleteClusterInstance struct {
}

func (this *DeleteClusterInstance) CreateRequestObj() interface{} {
	return &billing.PurchaseReq{}
}

func (this *DeleteClusterInstance) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*billing.PurchaseReq)
	return billing2.DoBillingRefundPurchase(reqData)
}
