package billing

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/billing"
	billing2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/billing"
)

func init() {
	const controllerName = "QueryPricePurchase"
	if code, msg := httpserver.RegisterCloudController(controllerName, &QueryPricePurchase{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type QueryPricePurchase struct {
}

func (this *QueryPricePurchase) CreateRequestObj() interface{} {
	return &billing.QueryPricePurchaseReq{}
}

func (this *QueryPricePurchase) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*billing.QueryPricePurchaseReq)
	return billing2.DoQueryPricePurchase(reqData)
}
