package subnet

import (
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	subnetModel "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/subnet"
	subnetService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/subnet"
)

func init() {
	const controllerName = "ModifySubnet"
	if code, msg := httpserver.RegisterCloudController(controllerName, &modifySubnetController{}); code != 0 {
		logger.Errorf("Fail to register controller %s, code %d, err %s", controllerName, code, msg)
	}
}

type modifySubnetController struct {
}

func (c *modifySubnetController) CreateRequestObj() interface{} {
	return &subnetModel.ModifySubnetReq{}
}

func (c *modifySubnetController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*subnetModel.ModifySubnetReq)
	rsp, err := (&subnetService.ModifySubnetService{Req: reqData}).ModifySubnet()
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}