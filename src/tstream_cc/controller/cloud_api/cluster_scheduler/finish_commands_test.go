package cluster_master

import (
	"encoding/base64"
	"encoding/json"
	"flag"
	"fmt"
	"os"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cluster_master_protocol"
	"testing"
)

func TestFinishCommands(t *testing.T) {
	mp := make(map[string]string)
	mp["jobId"] = "1"
	mp["jobRuntimeId"] = "1"
	mp["action"] = "1"
	mp["succeed"] = "false"
	mp["status"] = "1"
	//slice := make([]map[string]string, 0)
	//slice = append(slice, mp)
	// 将map转换为JSON字符串
	jsonData, err := json.Marshal(mp)
	if err != nil {
		fmt.Println("转换为JSON时发生错误:", err)
		return
	}
	// 对JSON字符串进行Base64编码
	base64Data := base64.StdEncoding.EncodeToString(jsonData)

	fmt.Println("Base64编码结果:", base64Data)
	controller := &finishCommandsController{}

	// 创建 CommandResultCloudApi 对象
	cmdResult1 := &cluster_master_protocol.CommandResultCloudApi{
		RequestId: "12345678910",
		CommandId: 1,
		Result:    base64Data,
	}

	// 创建 finishCommandsReq 对象
	req := &finishCommandsReq{
		RequestBase: apiv3.RequestBase{
			AppId: 1257058945,
		},
		CmdExeResults: []*cluster_master_protocol.CommandResultCloudApi{cmdResult1},
	}
	controller.Process(req, 0)

}

var (
	fTestTime = flag.Int64("test.time", 60*60*24*14, "")
	dbUrl     = flag.String("test.db.url", "root:asdfQWER123@tcp(9.134.75.242:3306)/galileo-shanghai?charset=utf8", "")
)

func init() {
	testing.Init()
	flag.Parse()
	tx, err := dao.NewDataSourceTransactionManager(*dbUrl)
	if err != nil {
		fmt.Println("darwin Process create DataSourceTransactionManager err", err)
		os.Exit(-1)
	}
	service.SetTxManager(tx)
}
