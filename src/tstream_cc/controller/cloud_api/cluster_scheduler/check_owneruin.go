package cluster_master

import (
	"fmt"
	"strconv"
	"strings"
	logging "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
)

func CheckAppId(AppId int64, CommandIds []int64) bool {
	placeholders := make([]string, len(CommandIds))
	for i := range CommandIds {
		placeholders[i] = "?"
	}
	placeholderStr := strings.Join(placeholders, ",")

	args := make([]interface{}, len(CommandIds))
	for i, id := range CommandIds {
		args[i] = id
	}
	sql := fmt.Sprintf("select distinct AppId from Command where Id in (%s)", placeholderStr)
	cnt, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	logging.Info("cnt", cnt, "err", err, "appiddd", string(data[0]["AppId"]))
	if err != nil || cnt != 1 {
		return false
	}
	appid, _ := strconv.ParseInt(string(data[0]["AppId"]), 10, 64)
	if appid != AppId {
		return false
	}
	return true
}

func CheckAppIdWithClusterGroupId(AppId int64, ClusterGroupId int64) bool {
	sql := "select AppId from ClusterGroup where Id = ?"
	args := make([]interface{}, 0)
	args = append(args, ClusterGroupId)
	cnt, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil || cnt != 1 {
		return false
	}
	appid, _ := strconv.ParseInt(string(data[0]["AppId"]), 10, 64)
	if appid != AppId {
		return false
	}
	return true
}
