package cluster_master

import (
	"fmt"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster_master"
)

/**
 *
 * <AUTHOR>
 * @time 2018/11/22
 * @version 0.3.6
 * @since 0.3.6
 * @see
 */
func init() {
	const controllerName = "RespondCommands"
	if code, msg := httpserver.RegisterCloudController(controllerName, &ackCommandsController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type ackCommandsReq struct {
	apiv3.RequestBase
	CommandIds []int64
}

type ackCommandsController struct {
}

func (this *ackCommandsController) CreateRequestObj() interface{} {
	return &ackCommandsReq{}
}

func (this *ackCommandsController) Process(request interface{}, eventId int64) (string, string, interface{}) {
	reqData := request.(*ackCommandsReq)
	if CheckAppId(reqData.AppId, reqData.CommandIds) {
		err := cluster_master.AckCommands(reqData.CommandIds)
		if err != nil {
			logger.Error(fmt.Sprintf("Failed to save ack status to local db, errors: %v", err))
			code := errorcode.GetCode(err)
			return code.GetCodeStr(), code.GetCodeDesc(), nil
		}
		return controller.OK, controller.NULL, nil
	}
	return controller.FailedOperation_CommandOperateFailure, controller.NULL, nil
}
