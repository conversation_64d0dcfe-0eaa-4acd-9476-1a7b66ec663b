package cluster_master

import (
	"encoding/base64"
	"encoding/json"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cluster_master_protocol"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster_master"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/configuration_center"
)

/**
 *
 * <AUTHOR>
 * @time 2018/11/22
 * @version 0.3.6
 * @since 0.3.6
 * @see
 */
func init() {
	const controllerName = "FinishCommands"
	if code, msg := httpserver.RegisterCloudController(controllerName, &finishCommandsController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type finishCommandsReq struct {
	apiv3.RequestBase
	CmdExeResults []*cluster_master_protocol.CommandResultCloudApi
}

type finishCommandsController struct {
}

func (this *finishCommandsController) CreateRequestObj() interface{} {
	return &finishCommandsReq{}
}

func (this *finishCommandsController) Process(request interface{}, eventId int64) (string, string, interface{}) {
	reqData := request.(*finishCommandsReq)
	var commandIds []int64
	for _, result := range reqData.CmdExeResults {
		commandIds = append(commandIds, result.CommandId)
	}

	appid, _ := service.GetScsUserAppId()

	if (appid != controller.NULL && appid == strconv.FormatInt(reqData.AppId, 10)) || CheckAppId(reqData.AppId, commandIds) {
		commandResult, err := convertCommandResultsCloudApi(reqData.CmdExeResults)
		if err != nil {
			code := errorcode.GetCode(err)
			return code.GetCodeStr(), code.GetCodeDesc(), nil
		}
		err = cluster_master.FinishCommands(commandResult)
		if err != nil {
			logger.Errorf("Failed to update command execution result, errors: %+v", err)
			code := errorcode.GetCode(err)
			return code.GetCodeStr(), code.GetCodeDesc(), nil
		}
		return controller.OK, controller.NULL, nil
	}
	return controller.FailedOperation_CommandOperateFailure, controller.NULL, nil
}

func convertCommandResultsCloudApi(commandResultsCloudApi []*cluster_master_protocol.CommandResultCloudApi) ([]*cluster_master_protocol.CommandResult, error) {
	convertedResults := make([]*cluster_master_protocol.CommandResult, len(commandResultsCloudApi))
	for i, result := range commandResultsCloudApi {
		decodedResult, err := base64.StdEncoding.DecodeString(result.Result)
		if err != nil {
			logger.Errorf("Failed to decode base64 string commandResult, errors: %+v", err)
			return nil, err
		}
		decodedMap := make(map[string]string)
		err = json.Unmarshal(decodedResult, &decodedMap)
		if err != nil {
			logger.Errorf("Failed to unmarshal byte[] commandResult, errors: %+v", err)
			return nil, err
		}
		convertedResult := &cluster_master_protocol.CommandResult{
			RequestId: result.RequestId,
			CommandId: result.CommandId,
			Result:    decodedMap,
		}
		convertedResults[i] = convertedResult
	}
	return convertedResults, nil
}
