package cluster_master

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster_master"
	clustermaster2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster_master"
)

/**
 *
 * <AUTHOR>
 * @time 2018/11/22
 * @version 0.3.6
 * @since 0.3.6
 * @see
 */
func init() {
	const controllerName = "PullCommands"
	if code, msg := httpserver.RegisterCloudController(controllerName, &pullCommandsController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type getCommandsReq struct {
	apiv3.RequestBase
	ClusterGroupId int64 `check:"nullable:false|range:(0, INF)"`
	BatchSize      int8  `check:"nullable:false|range:(0, INF)"`
}

type getCommandsRsp struct {
	Commands []*cluster_master.Command
}

type pullCommandsController struct {
}

func (this *pullCommandsController) CreateRequestObj() interface{} {
	return &getCommandsReq{}
}

func (this *pullCommandsController) Process(request interface{}, eventId int64) (string, string, interface{}) {
	reqData := request.(*getCommandsReq)
	if CheckAppIdWithClusterGroupId(reqData.AppId, reqData.ClusterGroupId) {
		commands, err := clustermaster2.ListCommands(reqData.ClusterGroupId, reqData.BatchSize, constants.CLUSTER_MASTER_COMMAND_CREATE)
		if err != nil {
			logger.Errorf("Failed to List Commands, with req:%+v, with errors:%+v", reqData, err)
			code := errorcode.GetCode(err)
			return code.GetCodeStr(), code.GetCodeDesc(), nil
		}
		rsp := getCommandsRsp{
			Commands: commands,
		}
		return controller.OK, constants.NULL, rsp
	}
	return controller.FailedOperation_CommandOperateFailure, controller.NULL, nil
}
