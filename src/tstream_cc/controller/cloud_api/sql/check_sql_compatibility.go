package sql

import (
	"context"

	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/sql"
	sql2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/sql"
)

func init() {
	const controllerName = "CheckSqlJobStateCompatibility"
	if err := httpserver.RegisterCloudControllerV2(controllerName, &checkSqlCompatibilityController{}); err != nil {
		logger.Errorf("Fail to register controller:%+v", err)
	}
}

type checkSqlCompatibilityController struct {
}

func (c *checkSqlCompatibilityController) CreateRequestObj() interface{} {
	return &sql.CheckSqlCompatibilityReq{}
}

func (c *checkSqlCompatibilityController) Process(ctx context.Context,
	req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*sql.CheckSqlCompatibilityReq)
	rsp, err := (&sql2.SqlCompatibilityService{Req: reqData}).CheckCompatibility(ctx)
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}
