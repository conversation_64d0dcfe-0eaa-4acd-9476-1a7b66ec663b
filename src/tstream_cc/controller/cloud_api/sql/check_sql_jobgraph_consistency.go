package sql

import (
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/sql"
	sql2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/sql"
)

func init() {
	const controllerName = "CheckSqlJobGraphConsistency"
	if code, msg := httpserver.RegisterCloudController(controllerName, &checkSqlJobGraphConsistencyController{}); code != 0 {
		logger.Errorf("Fail to register controller %s, code %d, err %s", controllerName, code, msg)
	}
}

type checkSqlJobGraphConsistencyController struct {
}

func (c *checkSqlJobGraphConsistencyController) CreateRequestObj() interface{} {
	return &sql.CheckSqlJobGraphConsistencyReq{}
}

func (c *checkSqlJobGraphConsistencyController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*sql.CheckSqlJobGraphConsistencyReq)
	rsp, err := sql2.NewJobGraphConsistencyService(reqData).CheckConsistency()
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}
