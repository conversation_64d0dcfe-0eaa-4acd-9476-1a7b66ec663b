package role_info

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/role_info"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/role_info"
)

/**
 * Created by <PERSON> on 2021/11/23 3:16 下午.
 * At tencent
 */
func init() {
	const controllerName = "DescribeSynchronousVersion"
	if code, msg := httpserver.RegisterCloudController(controllerName, &describeSynchronousVersionController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type describeSynchronousVersionController struct{}

func (this *describeSynchronousVersionController) CreateRequestObj() interface{} {
	return &model.DescribeSynchronousVersionReq{}
}

func (this *describeSynchronousVersionController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*model.DescribeSynchronousVersionReq)
	return service.DoDescribeSynchronousVersion(reqData)
}
