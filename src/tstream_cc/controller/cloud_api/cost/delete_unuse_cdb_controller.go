package cost

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/cost"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cost"
)

func init() {
	const controllerName = "DeleteUnuseCdb"
	if code, msg := httpserver.RegisterCloudController(controllerName, &DeleteUnuseCdb{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type DeleteUnuseCdb struct {
}

func (this *DeleteUnuseCdb) CreateRequestObj() interface{} {
	return &model.DeleteUnuseCdbReq{}
}

func (this *DeleteUnuseCdb) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*model.DeleteUnuseCdbReq)

	return cost.DeleteUnuseCdb(reqData)
}
