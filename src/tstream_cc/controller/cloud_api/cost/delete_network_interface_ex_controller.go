package cost

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/cost"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/vpc"
)

func init() {
	const controllerName = "DeleteNetworkInterfaceEx"
	if code, msg := httpserver.RegisterCloudController(controllerName, &deleteNetworkInterfaceExController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type deleteNetworkInterfaceExController struct {
}

func (this *deleteNetworkInterfaceExController) CreateRequestObj() interface{} {
	return &model.DeleteNetworkInterfaceExReq{}
}

func (this *deleteNetworkInterfaceExController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	r := req.(*model.DeleteNetworkInterfaceExReq)
	vpcEx := vpc.GetVpcServiceEx()
	b := vpcEx.NewDefaultDeleteNetworkInterfaceExBuilder()
	b.WithVpcAppId(r.VpcAppId)
	b.WithVpcUin(r.VpcUin)
	b.WithVpcSubAccountUin(r.VpcSubAccountUin)

	b.WithVpcId(r.VpcId)
	b.WithNetworkInterfaceId(r.NetworkInterfaceId)
	resp, err := vpcEx.DeleteNetworkInterfaceExWithScsAccount(r.Region, b.Build())
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}
	return controller.OK, "", model.DeleteNetworkInterfaceExResp{Response: resp.ToJsonString()}
}
