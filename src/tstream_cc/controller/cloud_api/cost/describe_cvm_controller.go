package cost

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/cost"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cost"
)

func init() {
	const controllerName = "DescribeCvm"
	if code, msg := httpserver.RegisterCloudController(controllerName, &describeCvmController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type describeCvmController struct {
}

func (this *describeCvmController) CreateRequestObj() interface{} {
	return &model.DescribeCvmReq{}
}

func (this *describeCvmController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*model.DescribeCvmReq)

	return cost.DescribeCvmSet(reqData)
}
