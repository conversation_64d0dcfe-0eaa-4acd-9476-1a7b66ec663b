package cost

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/cost"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cost"
)

func init() {
	const controllerName = "ModifyClusterOpenPolicy"
	if code, msg := httpserver.RegisterCloudController(controllerName, &modifyClusterOpenPolicyController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type modifyClusterOpenPolicyController struct {
}

func (this *modifyClusterOpenPolicyController) CreateRequestObj() interface{} {
	return &model.ModifyClusterOpenPolicyReq{}
}

func (this *modifyClusterOpenPolicyController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*model.ModifyClusterOpenPolicyReq)
	return cost.ModifyClusterOpenPolicy(reqData)
}
