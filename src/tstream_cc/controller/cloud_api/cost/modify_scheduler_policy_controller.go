package cost

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/cost"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cost"
)

func init() {
	const controllerName = "ModifySchedulerPolicy"
	if code, msg := httpserver.RegisterCloudController(controllerName, &modifySchedulerPolicyController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type modifySchedulerPolicyController struct {
}

func (this *modifySchedulerPolicyController) CreateRequestObj() interface{} {
	return &model.ModifySchedulerPolicyReq{}
}

func (this *modifySchedulerPolicyController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*model.ModifySchedulerPolicyReq)

	return cost.ModifySchedulerPolicy(reqData)
}
