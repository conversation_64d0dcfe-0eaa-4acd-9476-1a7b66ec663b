package metadata

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/metadata"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/metadata"
)

func init() {
	const controllerName = "DescribeExternalMetaCatalog"
	if code, msg := httpserver.RegisterCloudController(controllerName, &DescribeExternalMetaCatalogsController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type DescribeExternalMetaCatalogsController struct {
}

func (this *DescribeExternalMetaCatalogsController) CreateRequestObj() interface{} {
	return &model.DescribeExternalMetaCatalogReq{}
}

func (this *DescribeExternalMetaCatalogsController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*model.DescribeExternalMetaCatalogReq)
	return (&service.DoDescribeMetaExternalCatalogService{}).DoDescribeExternalMetaCatalogs(reqData, eventId)
}
