package metadata

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/metadata"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/metadata"
)

func init() {
	const controllerName = "DeleteExternalMetaCatalogs"
	if code, msg := httpserver.RegisterCloudController(controllerName, &DeoleteMetaCatalogController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type DeoleteMetaCatalogController struct {
}

func (this *DeoleteMetaCatalogController) CreateRequestObj() interface{} {
	return &model.DeleteExternalMetaCatalogsReq{}
}

func (this *DeoleteMetaCatalogController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*model.DeleteExternalMetaCatalogsReq)
	return (&service.DoDeleteMetaCatalogService{}).DoDeleteMetaCatalogs(reqData, eventId)
}
