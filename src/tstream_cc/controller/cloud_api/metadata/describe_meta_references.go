package metadata

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/metadata"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/sql"
)

func init() {
	const controllerName = "DescribeMetaReferences"
	if code, msg := httpserver.RegisterCloudController(controllerName, &DescribeMetaReferencesController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type DescribeMetaReferencesController struct {
}

func (this *DescribeMetaReferencesController) CreateRequestObj() interface{} {
	return &model.DescribeMetaReferencesReq{}
}

func (this *DescribeMetaReferencesController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*model.DescribeMetaReferencesReq)
	reqData.SqlCode = sql.GetRealSqlCode(reqData.SqlCode, reqData.SqlCodeAfterGzip)
	return (&sql.DoDescribeMetaReferencesService{}).DoDescribeMetaReferences(reqData, eventId)
}
