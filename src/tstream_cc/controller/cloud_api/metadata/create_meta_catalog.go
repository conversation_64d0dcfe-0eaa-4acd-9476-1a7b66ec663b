package metadata

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/metadata"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/metadata"
)

func init() {
	const controllerName = "CreateMetaCatalog"
	if code, msg := httpserver.RegisterCloudController(controllerName, &CreateMetaCatalogController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
	httpserver.RegisterCloudResponse(controllerName, &model.CreateMetaCatalogRsp{})
}

type CreateMetaCatalogController struct {
}

func (this *CreateMetaCatalogController) CreateRequestObj() interface{} {
	return &model.CreateMetaCatalogReq{}
}

func (this *CreateMetaCatalogController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*model.CreateMetaCatalogReq)
	rsp, err := (&service.DoCreateMetaCatalogService{}).DoCreateMetaCatalog(reqData, eventId)
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}
