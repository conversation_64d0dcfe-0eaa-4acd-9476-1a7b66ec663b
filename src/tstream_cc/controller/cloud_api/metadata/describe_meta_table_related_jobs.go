package metadata

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/metadata"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/metadata"
)

func init() {
	const controllerName = "DescribeMetaTableRelatedJobs"
	if code, msg := httpserver.RegisterCloudController(controllerName, &DescribeMetaTableRelatedJobsController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type DescribeMetaTableRelatedJobsController struct {
}

func (this *DescribeMetaTableRelatedJobsController) CreateRequestObj() interface{} {
	return &model.DescribeMetaTableRelatedJobsReq{}
}

func (this *DescribeMetaTableRelatedJobsController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*model.DescribeMetaTableRelatedJobsReq)
	rsp, err :=  (&service.DescribeMetaTableRelatedJobsService{}).DoDescribeMetaTableRelatedJobs(reqData, eventId)
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}
