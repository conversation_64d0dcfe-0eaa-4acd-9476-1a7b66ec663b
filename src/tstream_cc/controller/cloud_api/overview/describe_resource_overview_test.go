package overview

import (
	"encoding/json"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/overview"
	"testing"
)

func TestDescribeResourceOverviewController_Process(t *testing.T) {
	c := &describeResourceOverviewController{}

	req := &overview.DescribeResourceOverviewReq{}
	req.AppId = *fTestAppId
	req.Region = *fTestRegion

	code, msg, rsp := c.Process(req, 1)
	t.Log(code)
	t.Log(msg)
	b, _ := json.MarshalIndent(rsp, "", "\t")
	t.Log(string(b))
}
