package overview

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/overview"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/overview"
)

func init() {
	const controllerName = "DescribeDocuments"
	if code, msg := httpserver.RegisterCloudController(controllerName, &describeDocumentsController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type describeDocumentsController struct {
}

func (c *describeDocumentsController) CreateRequestObj() interface{} {
	return &model.DescribeDocumentsReq{}
}

func (c *describeDocumentsController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*model.DescribeDocumentsReq)
	rsp, err := overview.NewDocumentsService(reqData).List()
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}
