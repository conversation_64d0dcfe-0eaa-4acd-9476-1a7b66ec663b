package overview

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	overview2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/overview"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/overview"
)

func init() {
	const controllerName = "DescribeResourceOverview"
	if code, msg := httpserver.RegisterCloudController(controllerName, &describeResourceOverviewController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type describeResourceOverviewController struct {
}

func (c *describeResourceOverviewController) CreateRequestObj() interface{} {
	return &overview2.DescribeResourceOverviewReq{}
}

func (c *describeResourceOverviewController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*overview2.DescribeResourceOverviewReq)
	rsp, err := overview.NewOverviewService(reqData).Overview()
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}
