package overview

import (
	"flag"
	"fmt"
	"os"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"testing"
)

var (
	fTestAppId  = flag.Int64("test.AppId", 1257058945, "")
	fTestRegion = flag.String("test.region", "ap-guangzhou",
		"e.g. ap-guangzhou ap-beijing ap-shanghai ap-chengdu")

	fTestDbUrl = flag.String("test.db.url", "root:root@tcp(127.0.0.1:3306)/online_galileo?charset=utf8", "")
)

func init() {
	testing.Init()
	flag.Parse()
	tx, err := dao.NewDataSourceTransactionManager(*fTestDbUrl)
	if err != nil {
		fmt.Println("darwin Process create DataSourceTransactionManager err", err)
		os.Exit(1)
	}
	service.SetTxManager(tx)
}
