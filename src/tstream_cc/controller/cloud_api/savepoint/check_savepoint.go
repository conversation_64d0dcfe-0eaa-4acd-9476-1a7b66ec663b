package savepoint

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/savepoint"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/savepoint"
)

func init() {
	const controllerName = "CheckSavepoint"
	if code, msg := httpserver.RegisterCloudController(controllerName, &checkSavepointController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type checkSavepointController struct {
}

func (this *checkSavepointController) CreateRequestObj() interface{} {
	return &model.CheckSavepointReq{}
}

func (this *checkSavepointController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*model.CheckSavepointReq)
	rsp, err := service.DoCheckSavepoint(reqData)
	if err != nil {
		logger.Errorf("[%s] %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, "ok", rsp
}
