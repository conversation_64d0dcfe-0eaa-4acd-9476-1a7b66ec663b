package clean

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/clean"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/clean"
)

func init() {
	const controllerName = "CheckUnusefulCvm"
	if code, msg := httpserver.RegisterCloudController(controllerName, &checkUnusefulCvmController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type checkUnusefulCvmController struct {
}

func (this *checkUnusefulCvmController) CreateRequestObj() interface{} {
	return &model.CheckUnusefulCvmReq{}
}

func (this *checkUnusefulCvmController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*model.CheckUnusefulCvmReq)

	return clean.CheckUnusefulCvm(reqData)
}
