package clean

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/clean"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/clean"
)

func init() {
	const controllerName = "ListAllCvm"
	if code, msg := httpserver.RegisterCloudController(controllerName, &listAllCvmController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type listAllCvmController struct {
}

func (this *listAllCvmController) CreateRequestObj() interface{} {
	return &model.ListAllCvmReq{}
}

func (this *listAllCvmController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*model.ListAllCvmReq)

	return clean.ListAllCvm(reqData)
}
