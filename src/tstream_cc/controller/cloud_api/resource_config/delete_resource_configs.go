package resource_config

import (
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/resource_config"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource_config"
)

func init() {
	const controllerName = "DeleteResourceConfigs"
	if code, msg := httpserver.RegisterCloudController(controllerName, &DeleteResourceConfigsController{}); code != 0 {
		logger.Errorf("Fail to register controller %s, code %d, err %s", controllerName, code, msg)
	}
}

type DeleteResourceConfigsController struct {
}

func (d *DeleteResourceConfigsController) CreateRequestObj() interface{} {
	return &model.DeleteResourceConfigReq{}
}

func (d *DeleteResourceConfigsController) Process(req interface{}, eventId int64) (msgErr string, msg string,
	rsp interface{}) {
	reqData := req.(*model.DeleteResourceConfigReq)
	s := &service.DeleteResourceConfigService{
		RequestId: reqData.RequestId,
	}

	rsp, err := s.DeleteResourceConfigs(reqData)
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp

}
