package resource_config

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/resource_config"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource_config"
)

func init() {
	const controllerName = "CreateResourceConfig"
	if code, msg := httpserver.RegisterCloudController(controllerName, &CreateResourceConfigController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type CreateResourceConfigController struct {
}

func (this *CreateResourceConfigController) CreateRequestObj() interface{} {
	return &model.CreateResourceConfigReq{}
}

func (this *CreateResourceConfigController) Process(req interface{}, eventId int64) (string, string, interface{}) {

	reqData := req.(*model.CreateResourceConfigReq)
	rsp, err := service.DoCreateResourceConfig(reqData)
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}
