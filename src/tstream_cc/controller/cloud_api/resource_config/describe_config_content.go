package resource_config

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"

	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/resource_config"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource_config"
)

func init() {
	const controllerName = "DescribeResourceConfigContent"
	if code, msg := httpserver.RegisterCloudController(controllerName, &describeResourceConfigContentController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type describeResourceConfigContentController struct {
}

func (this *describeResourceConfigContentController) CreateRequestObj() interface{} {
	return &model.DescribeResourceConfigContentReq{}
}

func (this *describeResourceConfigContentController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*model.DescribeResourceConfigContentReq)
	rsp, err := service.DoDescribeResourceConfigContent(reqData)
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}
