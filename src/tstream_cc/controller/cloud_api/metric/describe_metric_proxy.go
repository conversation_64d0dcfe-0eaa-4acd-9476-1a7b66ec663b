package metric

import (
	"strconv"

	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/metric"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/metric"
)

func init() {
	const controllerName = "DescribeMetricProxy"
	if code, msg := httpserver.RegisterCloudController(controllerName, &describeMetricProxyController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type describeMetricProxyController struct {
}

func (d *describeMetricProxyController) CreateRequestObj() interface{} {
	return &model.DescribeMetricProxyReq{}
}

func (d *describeMetricProxyController) Process(req interface{}, eventID int64) (string, string, interface{}) {
	reqData := req.(*model.DescribeMetricProxyReq)
	rsp, err := (&service.DescribeMetricProxyService{}).DoDescribeMetric(reqData)
	if err != nil {
		logger.Errorf("%s DescribeMetricProxy error has returned %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}
