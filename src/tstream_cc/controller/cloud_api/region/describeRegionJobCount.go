package region

import (
	"strconv"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/region"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/region"
)

/*
*
用以替换掉describe_region_job_count.go
*/
func init() {
	const controllerName = "DescribeRegionJobCount"
	if code, msg := httpserver.RegisterCloudController(controllerName, &describeRegionJobCountNewController{}); code != 0 {
		logger.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type RegionCountArray struct {
	Regions []*RegionCountNew
}
type RegionCountNew struct {
	Region         string
	ClusterCount   int16
	JobCount       int32
	ItemSpaceCount int64
	ResourceCount  int
}

type describeRegionJobCountNewController struct {
}

func (this *describeRegionJobCountNewController) CreateRequestObj() interface{} {
	return &region.DescribeRegionJobCountReq{}
}
func toMap(arr []string) map[string]int {
	set := make(map[string]int)
	// 将list内容传递进map,只根据key判断，所以不需要关心value的值，用struct{}{}表示
	for _, value := range arr {
		set[value] = 0
	}
	return set
}
func (this *describeRegionJobCountNewController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*region.DescribeRegionJobCountReq)
	var regionFilter []string
	if reqData.Filters == nil || len(reqData.Filters) == 0 {
		regionFilter = []string{}
	} else {
		filterName := reqData.Filters[0].Name
		if filterName != "region" {
			logger.Errorf("Parameter error: Filter name is not region")
			//return controller.PARAMSERR, "Parameter error", nil
			code := errorcode.InvalidParameterCode
			return code.GetCodeStr(), code.GetCodeDesc(), nil
		}
		filterValues := reqData.Filters[0].Values
		regionFilter = filterValues
	}
	regionZones, err := service.GetRegionZones(reqData.Region, reqData.AppId, reqData.Uin, regionFilter, []int{constants.K8S_CLUSTER_TYPE_TKE, constants.K8S_CLUSTER_TYPE_EKS})
	if err != nil {
		logger.Errorf("Failed to get region zones, with request:%+v, with errors:%+v", reqData, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), nil
		//return controller.ERROR_CODE_REGION_QUERY_FAILURE, err.Error(), nil
	}
	regionArray := strings.Split(reqData.Regions, "|")
	regionMap := toMap(regionArray)
	rsp := make([]*RegionCountNew, 0)
	for _, v := range regionZones {
		regionZoneCount := len(v)
		for i := 0; i < regionZoneCount; i++ {
			regionZone := v[i]
			_, is_input := regionMap[regionZone.Region]
			if is_input && regionMap[regionZone.Region] == 0 {
				regionMap[regionZone.Region] = 1
				regionAp := regionZone.Region
				tmpFilter := make([]string, 0)
				tmpFilter = append(tmpFilter, regionAp)

				clusterCountDatas, err := service.GetRegionClusterCount(reqData.AppId, tmpFilter, nil)
				if err != nil {
					logger.Errorf("Failed to get cluster count of region:%s, with errors:%+v", regionAp, err)
					code := errorcode.GetCode(err)
					return code.GetCodeStr(), code.GetCodeDesc(), nil
					//return controller.ERROR_CODE_CLUSTER_QUERY_FAILURE, err.Error(), nil
				}
				if len(clusterCountDatas) == 0 {
					logger.Errorf("Failed to get cluster count of region:%s, with result is empty", regionAp)
					code := errorcode.GetCode(err)
					return code.GetCodeStr(), code.GetCodeDesc(), nil
					//return controller.ERROR_CODE_CLUSTER_QUERY_EMPTY, err.Error(), nil
				}
				clusterCountData := clusterCountDatas[0]
				totalClusterCount := clusterCountData.TotalClusterCount
				jobCountDatas, err := service.GetRegionJobCount(reqData.AppId, tmpFilter, nil)
				if err != nil {
					logger.Errorf("Failed to get job count of region:%s, with errors:%+v", regionAp, err)
					code := errorcode.GetCode(err)
					return code.GetCodeStr(), code.GetCodeDesc(), nil
					//return controller.ERROR_CODE_JOB_COUNT_QUERY_FAILURE, err.Error(), nil
				}
				if len(jobCountDatas) == 0 {
					logger.Errorf("Failed to get job count of region:%s, with result is empty", regionAp)
					code := errorcode.GetCode(err)
					return code.GetCodeStr(), code.GetCodeDesc(), nil
					//return controller.ERROR_CODE_JOB_COUNT_QUERY_NO_RESULT, err.Error(), nil
				}
				jobCountData := jobCountDatas[0]
				totalJobCount := jobCountData.TotalJobCount
				resourceCount, err := service.GetRegionResourceCount(reqData.AppId, tmpFilter)
				if err != nil {
					logger.Errorf("Failed to get resource count of region:%s, with errors:%+v", regionAp, err)
					code := errorcode.GetCode(err)
					return code.GetCodeStr(), code.GetCodeDesc(), nil
					//return controller.ERROR_CODE_JOB_COUNT_QUERY_FAILURE, err.Error(), nil
				}
				totalResourceCount, _ := resourceCount[regionAp]

				// 获取地域有权限的空间数量
				itemSpaces, err := service.GetRegionItemSpaceCount(reqData.AppId, tmpFilter, reqData.Uin, reqData.SubAccountUin)
				if err != nil {
					logger.Errorf("Failed to get ItemSpace count of region:%s, with errors:%+v", regionAp, err)
					code := errorcode.GetCode(err)
					return code.GetCodeStr(), code.GetCodeDesc(), nil
					//return controller.ERROR_CODE_JOB_COUNT_QUERY_FAILURE, err.Error(), nil
				}
				totalItemSpaceCount := len(itemSpaces[regionAp])
				regionDetail := &RegionCountNew{
					Region:         regionAp,
					ClusterCount:   totalClusterCount,
					JobCount:       totalJobCount,
					ItemSpaceCount: int64(totalItemSpaceCount),
					ResourceCount:  totalResourceCount,
				}
				rsp = append(rsp, regionDetail)
			}
		}
	}
	result := &RegionCountArray{rsp}
	return controller.OK, controller.NULL, result
}
