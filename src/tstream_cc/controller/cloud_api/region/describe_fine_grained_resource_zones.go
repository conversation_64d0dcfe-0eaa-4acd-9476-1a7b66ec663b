package region

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/region"
	region22 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/region2"
)

func init() {
	const controllerName = "DescribeFineGrainedResourceZones"
	if code, msg := httpserver.RegisterCloudController(controllerName, &describeFineGrainedResourceZonesController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type describeFineGrainedResourceZonesController struct {
}

func (this *describeFineGrainedResourceZonesController) CreateRequestObj() interface{} {
	return &region.DescribeFineGrainedResourceZonesReq{}
}

func (this *describeFineGrainedResourceZonesController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*region.DescribeFineGrainedResourceZonesReq)

	rsp, err := region22.NewDescribeFineGrainedResourceZonesService(reqData).DescribeFineGrainedResourceZones()
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}
