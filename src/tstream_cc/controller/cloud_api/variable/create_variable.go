package variable

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/variable"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/variable"
)

func init() {
	const controllerName = "CreateVariable"
	if code, msg := httpserver.RegisterCloudController(controllerName, &CreateVariableController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type CreateVariableController struct {

}

func (this *CreateVariableController) CreateRequestObj() interface{} {
	return &model.CreateVariableReq{}
}

func (this *CreateVariableController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*model.CreateVariableReq)
	return (&variable.CreateVariableService{}).DoCreateVariable(reqData, eventId)
}
