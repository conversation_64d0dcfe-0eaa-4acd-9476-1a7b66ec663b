package variable

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	"tencentcloud.com/tstream_galileo/src/common/logger"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/variable"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/variable"
)

func init() {
	const controllerName = "DescribeVariableRelatedJobs"

	if code, msg := httpserver.RegisterCloudController(controllerName, &describeVariableRelatedJobsController{}); code != 0 {
		logging.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type describeVariableRelatedJobsController struct {
}

func (this *describeVariableRelatedJobsController) CreateRequestObj() interface{} {
	return &model.DescribeVariableRelatedJobsReq{}
}

func (this *describeVariableRelatedJobsController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*model.DescribeVariableRelatedJobsReq)
	return service.DoDescribeVariableRelatedJobs(reqData)
}
