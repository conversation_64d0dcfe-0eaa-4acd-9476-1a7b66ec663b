package variable

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/variable"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/variable"
)

func init() {
	const controllerName = "DescribeVariables"

	if code, msg := httpserver.RegisterCloudController(controllerName, &describeVariablesController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controller<PERSON>ame + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type describeVariablesController struct {
}

func (this *describeVariablesController) CreateRequestObj() interface{} {
	return &model.DescribeVariablesReq{}
}

func (this *describeVariablesController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*model.DescribeVariablesReq)
	return service.DoDescribeVariables(reqData)

}
