package variable

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/variable"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/variable"
)

func init() {
	const controllerName = "ModifyVariable"
	if code, msg := httpserver.RegisterCloudController(controllerName, &ModifyVariableController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type ModifyVariableController struct {
}

func (this *ModifyVariableController) CreateRequestObj() interface{} {
	return &model.ModifyVariableReq{}
}

func (this *ModifyVariableController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*model.ModifyVariableReq)
	return (&variable.ModifyVariableService{}).DoModifyVariable(reqData, eventId)
}
