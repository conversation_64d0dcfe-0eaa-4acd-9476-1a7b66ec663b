package variable

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/variable"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/variable"
)

func init() {
	const controllerName = "DescribeVariablesHistory"

	if code, msg := httpserver.RegisterCloudController(controllerName, &describeVariablesHistoryController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type describeVariablesHistoryController struct {
}

func (this *describeVariablesHistoryController) CreateRequestObj() interface{} {
	return &model.DescribeVariablesHistoryReq{}
}

func (this *describeVariablesHistoryController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*model.DescribeVariablesHistoryReq)
	return service.DoDescribeVariablesHistory(reqData)

}
