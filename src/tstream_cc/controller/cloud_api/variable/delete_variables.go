package variable

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/variable"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/variable"
)

func init() {
	const controllerName = "DeleteVariables"

	if code, msg := httpserver.RegisterCloudController(controllerName, &deleteVariablesController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controller<PERSON>ame + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type deleteVariablesController struct {
}

func (this *deleteVariablesController) CreateRequestObj() interface{} {
	return &variable.DeleteVariableReq{}
}

func (this *deleteVariablesController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*variable.DeleteVariableReq)
	return service.DoDeleteVariables(reqData)
}
