package role_auth

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/role_auth"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/role_auth"
)

/**
 * Created by <PERSON> on 2021/11/23 3:16 下午.
 * At tencent
 */
func init() {
	const controllerName = "CreateAdministrator"
	if code, msg := httpserver.RegisterCloudController(controllerName, &createAdministratorUserController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type createAdministratorUserController struct{}

func (this *createAdministratorUserController) CreateRequestObj() interface{} {
	return &model.CreateAdministorUserReq{}
}

func (this *createAdministratorUserController) Process(req interface{}, eventId int64) (string, string, interface{}) {

	reqData := req.(*model.CreateAdministorUserReq)

	return service.DoCreateAdministrator(reqData)
}
