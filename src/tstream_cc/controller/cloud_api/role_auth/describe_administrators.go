package role_auth

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/role_auth"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/role_auth"
)

/**
 * Created by <PERSON> on 2021/11/23 3:16 下午.
 * At tencent
 */
func init() {
	const controllerName = "DescribeAdministrators"
	if code, msg := httpserver.RegisterCloudController(controllerName, &describeAdministratorsController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type describeAdministratorsController struct{}

func (this *describeAdministratorsController) CreateRequestObj() interface{} {
	return &model.DescribeAdministorsReq{}
}

func (this *describeAdministratorsController) Process(req interface{}, eventId int64) (string, string, interface{}) {

	reqData := req.(*model.DescribeAdministorsReq)

	return service.DoDescribeAdministrators(reqData)
}
