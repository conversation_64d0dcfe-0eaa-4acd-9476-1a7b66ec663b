package white_list

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/white_list"
)

func init() {
	const controllerName = "CheckCommonWhitelist"
	if code, msg := httpserver.RegisterCloudController(controllerName, &CheckCommonWhitelistController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type CheckCommonWhitelistController struct {
}

func (this *CheckCommonWhitelistController) CreateRequestObj() interface{} {
	return &model.CheckCommonWhitelistReq{}
}

func (this *CheckCommonWhitelistController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*model.CheckCommonWhitelistReq)
	inWhiteList := false
	// 跨可用区部署是否支持
	if reqData.Type == constants.WHITE_LSIT_MULTIPLE_DEPLOYMENT_MODE {
		inWhiteList = auth.IsSupportMultipleDeploymentMode(reqData.AppId)
	} else {
		inWhiteList = auth.IsInWhiteList(reqData.AppId, reqData.Type)
	}
	rsp := &model.CheckWhitelistRsp{
		RequestId:   reqData.RequestId,
		InWhiteList: inWhiteList,
	}
	return controller.OK, controller.NULL, rsp
}
