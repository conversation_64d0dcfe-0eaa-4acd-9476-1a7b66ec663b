package white_list

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/white_list"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/white_list"
)

func init() {
	const controllerName = "CheckWhitelist"
	if code, msg := httpserver.RegisterCloudController(controllerName, &CheckWhitelistController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type CheckWhitelistController struct {
}

func (this *CheckWhitelistController) CreateRequestObj() interface{} {
	return &model.CheckWhitelistReq{}
}

func (this *CheckWhitelistController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*model.CheckWhitelistReq)
	rsp, err := service.DoCheckWhitelist(reqData)
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}
