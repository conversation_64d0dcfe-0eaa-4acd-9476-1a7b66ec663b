package tke

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/tke"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/update"
)

func init() {
	const controllerName = "UpgradeCluster"
	if code, msg := httpserver.RegisterCloudController(controllerName, &upgradeClusterController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controller<PERSON>ame + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type upgradeClusterController struct {
}

func (this *upgradeClusterController) CreateRequestObj() interface{} {
	return &model.UpgradeClusterReq{}
}

func (this *upgradeClusterController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*model.UpgradeClusterReq)
	return update.DoUpgradeCluster(reqData)
}
