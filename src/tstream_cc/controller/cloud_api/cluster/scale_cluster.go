package cluster

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/cluster"
)

func init() {
	const controllerName = "ScaleCluster"
	if code, msg := httpserver.RegisterCloudController(controllerName, &scaleClusterController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type scaleClusterController struct {
}

func (this *scaleClusterController) CreateRequestObj() interface{} {
	return &model.ScaleClusterReq{}
}

func (this *scaleClusterController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	// 计费上线后, 该接口已不再需要
	logger.E<PERSON><PERSON>("ScaleCluster interface is no longer used, invalid request %+v", req)

	return controller.UnsupportedOperation_Cluster, "", nil
}
