package cluster

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/cluster"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
)

/**
 *
 * <AUTHOR>
 * @time 2019/4/29
 * @version 0.4.0
 * @since 0.4.0
 * @see
 */
func init() {
	const controllerName = "DescribeClusters"
	if code, msg := httpserver.RegisterCloudController(controllerName, &describeClustersController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type describeClustersController struct {
}

func (this *describeClustersController) CreateRequestObj() interface{} {
	return &model.DescribeClustersReq{}
}

func (this *describeClustersController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*model.DescribeClustersReq)
	rsp, err := service.DoDescribeClusters(reqData)
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		if code == errorcode.InternalErrorCode {
			code = errorcode.FailedOperationCode
		}
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}
