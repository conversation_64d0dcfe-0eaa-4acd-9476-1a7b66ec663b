package cluster

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/cluster"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
)

func init() {
	const controllerName = "DescribeClusterDNS"
	if code, msg := httpserver.RegisterCloudController(controllerName, &describeClusterDNSController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type describeClusterDNSController struct {
}

func (this *describeClusterDNSController) CreateRequestObj() interface{} {
	return &model.DescribeClusterDNSReq{}
}

func (this *describeClusterDNSController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*model.DescribeClusterDNSReq)

	svr := service.NewDescribeClusterDNSService(reqData)
	rsp, err := svr.DescribeClusterDNS()
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}
