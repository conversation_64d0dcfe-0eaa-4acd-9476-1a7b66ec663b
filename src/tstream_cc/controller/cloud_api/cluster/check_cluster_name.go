package cluster

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/cluster"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
)

// author: ArchieYao
// date: 2021/8/11 2:50 下午
// description: 集群名称重名校验

func init() {
	const controllerName = "CheckClusterName"
	if code, msg := httpserver.RegisterCloudController(controllerName, &checkClusterNameController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type checkClusterNameController struct {

}

func (this *checkClusterNameController) CreateRequestObj() interface{} {
	return &model.CheckClusterNameReq{}
}

func (this *checkClusterNameController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*model.CheckClusterNameReq)
	rsp, err := service.DoCheckClusterName(reqData)
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}