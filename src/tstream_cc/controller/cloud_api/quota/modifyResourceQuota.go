package quota

import (
	"strconv"

	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/quota"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/quota"
)

func init() {
	const controllerName = "ModifyResourceQuota"
	if code, msg := httpserver.RegisterCloudController(controllerName, &modifyResourceQuotaController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type modifyResourceQuotaController struct {
}

func (c *modifyResourceQuotaController) CreateRequestObj() interface{} {
	return &model.ModifyResourceQuotaReq{}
}

func (c *modifyResourceQuotaController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*model.ModifyResourceQuotaReq)
	rsp, err := service.GetDefaultQuota().ModifyResourceQuota(reqData)
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}
