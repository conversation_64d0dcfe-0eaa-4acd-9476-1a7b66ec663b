package etl

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/etl"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/etl"
)

func init() {
	const controllerName = "CheckConnectionName"
	if code, msg := httpserver.RegisterCloudController(controllerName, &CheckConnectionNameController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type CheckConnectionNameController struct {
}

func (this *CheckConnectionNameController) CreateRequestObj() interface{} {
	return &model.CheckConnectionRequest{}
}

func (this *CheckConnectionNameController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	rsp := &model.ConnectionCheckRsp{}
	reqData := req.(*model.CheckConnectionRequest)
	// 鉴权
	itemSpcId, err := auth.InnerCreateAuth(reqData.WorkSpaceId, reqData.AppId, reqData.Region)
	if err != nil {
		logger.Errorf("%s: User  uin %s is NOT authenticated, refuse to serve", reqData.RequestId, reqData.Uin)
		return controller.OK, controller.NULL , rsp
	}
	exists := service.CheckConnectionNameExists(reqData.AppId, reqData.Region, reqData.Name, itemSpcId)
	rsp.Exists = exists
	return controller.OK, controller.NULL , rsp
}
