package etl

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/etl"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/etl"
)

func init() {
	const controllerName = "DescribeTableList"
	if code, msg := httpserver.RegisterCloudController(controllerName, &DescribeTableListController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type DescribeTableListController struct {
}

func (this *DescribeTableListController) CreateRequestObj() interface{} {
	return &model.DescribeTableListReq{}
}

func (this *DescribeTableListController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*model.DescribeTableListReq)
	rsp, err := service.DoDescribeTableList(reqData, eventId)
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}
	return controller.OK, controller.NULL, rsp
}
