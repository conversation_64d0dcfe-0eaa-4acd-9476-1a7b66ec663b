package controller

const (
	SUCCE<PERSON>          int64 = 0
	PARAMSERR        int64 = -1
	SYSERR           int64 = -2
	NO_IN_WHITE_LIST int64 = -3

	OK   string = "OK"
	NULL string = ""
)

// noinspection GoSnakeCaseUsage
const (
	ERROR_CODE_JOB_CANNOT_BE_STARTED int64 = 30001
	ERROR_CODE_JOB_CANNOT_BE_STOPPED int64 = 30002
	ERROR_CODE_JOB_NO_QUERY_RESULT   int64 = 30003
	ERROR_CODE_JOB_NOT_RUNNING       int64 = 30004
	ERROR_CODE_JOB_ALREADY_DELETED   int64 = 30005
	ERROR_CODE_JOB_CANNOT_BE_DELETED int64 = 30006
	ERROR_CODE_JOB_NAME_FORMAT_WRONG int64 = 30007

	ERROR_CODE_JOB_QUERY_FAILURE                  int64 = 30009
	ERROR_CODE_JOB_ALREADY_EXIST                  int64 = 30010
	ERROR_CODE_JOB_ADD_FAILURE                    int64 = 30011
	ERROR_CODE_JOB_DELETE_FAILURE                 int64 = 30012
	ERROR_CODE_JOB_RESTART_FAILURE                int64 = 30013
	ERROR_CODE_JOB_START_FAILURE                  int64 = 30014
	ERROR_CODE_JOB_STOP_FAILURE                   int64 = 30015
	ERROR_CODE_JOB_CU_OUT_OF_RANGE                int64 = 30016
	ERROR_CODE_JOB_CANNOT_BE_RESUMED              int64 = 30017
	ERROR_CODE_JOB_RECOVER_FAILURE                int64 = 30018
	ERROR_CODE_JOB_TYPE_WRONG                     int64 = 30019
	ERROR_CODE_JOB_SERIAL_ID_WRONG                int64 = 30020
	ERROR_CODE_JOB_COUNT_QUERY_NO_RESULT          int64 = 30022
	ERROR_CODE_JOB_COUNT_QUERY_FAILURE            int64 = 30021
	ERROR_CODE_NOTIFY_JOB_FAILURE                 int64 = 30099
	ERROR_CODE_UPDATE_JOB_EXEC_NODE_GRAPH_FAILURE int64 = 30100

	ERROR_CODE_JOB_CONFIG_VERSION_OBSOLETE        int64 = 31001
	ERROR_CODE_JOB_CONFIG_CHECKPOINT_MODE_ILLEGAL int64 = 31002
	ERROR_CODE_JOB_CONFIG_QUERY_NO_RESULT         int64 = 31003
	ERROR_CODE_JOB_CONFIG_UPDATE_FAIL             int64 = 31004
	ERROR_CODE_JOB_CONFIG_VERSION_WRONG           int64 = 31005
	ERROR_CODE_JOB_CONFIG_VERIFY_FAILURE          int64 = 31006
	ERROR_CODE_JOB_CONFIG_NOT_PROGRESS            int64 = 31007
	ERROR_CODE_JOB_CONFIG_QUERY_FAILURE           int64 = 31008
	ERROR_CODE_JOB_CONFIG_EXCEED_LIMIT            int64 = 31009

	ERROR_CODE_RUNNING_JOB_INSTANCE_NOT_FOUND                   int64 = 32001
	ERROR_CODE_JOB_INSTANCE_QUERY_FAILURE                       int64 = 32002
	ERROR_CODE_JOB_INSTANCE_BIGGER_THAN_MAX                     int64 = 32003
	ERROR_CODE_JOB_INSTANCE_QUERY_NO_RESULT                     int64 = 32004
	ERROR_CODE_JOB_INSTANCE_RUNNING_CU_NUM_BIGGER_THAN_MAX      int64 = 32005
	ERROR_CODE_JOB_INSTANCE_DEFAULT_PARALLELISM_BIGGER_THAN_MAX int64 = 32006
	ERROR_CODE_JOB_NOTIFY_SUCCEEDED_FAILURE                     int64 = 32007
	ERROR_CODE_STOP_YARN_JOB_FAILURE                            int64 = 32008

	ERROR_CODE_DDL_PARSE_FAILED           int64 = 33001
	ERROR_CODE_TABLE_NAME_FORMAT_WRONG    int64 = 33002
	ERROR_CODE_TABLE_ALREADY_EXIST        int64 = 33003
	ERROR_CODE_TABLE_SOURCE_NOT_CDP       int64 = 33004
	ERROR_CODE_TABLE_CDP_CONFIG_WRONG     int64 = 33005
	ERROR_CODE_TABLE_SCHEMA_NOT_SUPPORT   int64 = 33006
	ERROR_CODE_TABLE_QUERY_NO_RESULT      int64 = 33007
	ERROR_CODE_TABLE_QUERY_NUMBER_GREATER int64 = 33008
	ERROR_CODE_TABLE_QUERY_FAILURE        int64 = 33009
	ERROR_CODE_TABLE_ADD_FAILURE          int64 = 33010
	ERROR_CODE_TABLE_DELETE_FAILURE       int64 = 33011
	ERROR_CODE_TABLE_UPDATE_FAILURE       int64 = 33012

	ERROR_CODE_CDP_MSG_CONSUME_FAILED      int64 = 34001
	ERROR_CODE_CDP_TOPIC_PROJECT_NOT_FOUND int64 = 34002
	ERROR_CODE_CDP_REGION_VERIFY_FAILURE   int64 = 34003
	ERROR_CODE_CDP_ADDRESS_QUERY_FAILURE   int64 = 34004
	ERROR_CODE_CDP_PROJECTS_QUERY_FAILURE  int64 = 34005
	ERROR_CODE_CDP_TOPICS_QUERY_FAILURE    int64 = 34006

	ERROR_CODE_CDB_QUERY_FAILURE         int64 = 34101
	ERROR_CODE_CDB_USER_PASSWORD_FAILURE int64 = 34102

	ERROR_CODE_CKAFKA_MARSHAL_REQUEST_FAILURE             int64 = 34201
	ERROR_CODE_CKAFKA_QUERY_FAILURE                       int64 = 34202
	ERROR_CODE_CKAFKA_RECORD_QUERY_FAILURE                int64 = 34203
	ERROR_CODE_CKAFKA_SCHEMA_VERIFY_NO_DATA_FAILURE       int64 = 34204
	ERROR_CODE_CKAFKA_SCHEMA_VERIFY_COL_NUM_FAILURE       int64 = 34205
	ERROR_CODE_CKAFKA_SCHEMA_VERIFY_NOT_DOUBLE_FAILURE    int64 = 34206
	ERROR_CODE_CKAFKA_SCHEMA_VERIFY_NOT_TIMESTAMP_FAILURE int64 = 34207
	ERROR_CODE_CKAFKA_SCHEMA_VERIFY_NOT_BOOLEAN_FAILURE   int64 = 34208

	ERROR_CODE_ACCOUNT_QUERY_FAILURE int64 = 34301

	ERROR_CODE_PROJECT_NO_QUERY_RESULT   int64 = 35001
	ERROR_CODE_PROJECT_NAME_FORMAT_WRONG int64 = 35002
	ERROR_CODE_PROJECT_ALREADY_EXIST     int64 = 35003
	ERROR_CODE_PROJECT_CANNOT_BE_DELETED int64 = 35004
	ERROR_CODE_PROJECT_NOT_EXIST         int64 = 35005
	ERROR_CODE_PROJECT_QUERY_FAILURE     int64 = 35006
	ERROR_CODE_PROJECT_ADD_FAILURE       int64 = 35007
	ERROR_CODE_PROJECT_DELETE_FAILURE    int64 = 35008
	ERROR_CODE_PROJECT_UPDATE_FAILURE    int64 = 35009

	ERROR_CODE_SAVEPOINT_CANNOT_ADD     int64 = 36001
	ERROR_CODE_SAVEPOINT_ADD_FAILURE    int64 = 36002
	ERROR_CODE_SAVEPOINT_CREATE_FAILURE int64 = 36003
	ERROR_CODE_SAVEPOINT_QUERY_FAILURE  int64 = 36004
	ERROR_CODE_SAVEPOINT_DELETE_FAILURE int64 = 36005

	ERROR_CODE_DEBUG_CONFIG_QUERY_FAILURE      int64 = 37001
	ERROR_CODE_DEBUG_CONFIG_CLONE_FAILURE      int64 = 37002
	ERROR_CODE_DEBUG_SOURCE_DELETE_FAILURE     int64 = 37003
	ERROR_CODE_DEBUG_SINK_QUERY_FAILURE        int64 = 37004
	ERROR_CODE_DEBUG_SOURCE_QUERY_FAILURE      int64 = 37005
	ERROR_CODE_DEBUG_JOB_QUERY_FAILURE         int64 = 37006
	ERROR_CODE_DEBUG_JOB_SUBMIT_FAILURE        int64 = 37007
	ERROR_CODE_DEBUG_JOB_CONFIG_SAVE_FAILURE   int64 = 37008
	ERROR_CODE_DEBUG_JOB_CONFIG_UPDATE_FAILURE int64 = 37009

	ERROR_CODE_REGION_QUERY_FAILURE int64 = 38001
	ERROR_CODE_REGION_INIT_FAILURE  int64 = 38005

	ERROR_CODE_NO_PERMISSION_TO_OPERATE int64 = 39001

	ERROR_CODE_GRAMMAR_CHECK_FAILURE int64 = 40001
	ERROR_CODE_JAR_CHECK_FAILURE     int64 = 40002

	ERROR_CODE_CLUSTER_GROUP_NOT_FOUND            int64 = 41001
	ERROR_CODE_CLUSTER_GROUP_NO_CLUSTER_AVAILABLE int64 = 41002
	ERROR_CODE_CLUSTER_QUERY_FAILURE              int64 = 41003
	ERROR_CODE_CLUSTER_QUERY_EMPTY                int64 = 41004

	ERROR_CODE_COMMAND_ACK_FAILURE       int64 = 42001
	ERROR_CODE_UPDATE_JOB_STATUS_FAILURE int64 = 42002

	ERROR_CODE_RESOURCES_GET_FAILURE                 int64 = 43001
	ERROR_CODE_RESOURCES_REGION_INCONSISTENT_FAILURE int64 = 43002
	ERROR_CODE_RESOURCE_REFS_GET_FAILURE             int64 = 43010
	ERROR_CODE_RESOURCE_CONFIG_GET_FAILURE           int64 = 43011
	ERROR_CODE_RESOURCE_CONFIG_LOC_CONVERSION_ERROR  int64 = 43012

	ERROR_CODE_CREATE_BUCKET_ERROR   int64 = 440001
	ERROR_CODE_CREATE_BUCKET_FAILED  int64 = 440002
	ERROR_CODE_DISABLE_BUCKET_ERROR  int64 = 440003
	ERROR_CODE_DISABLE_BUCKET_FAILED int64 = 440004
	ERROR_CODE_CREATE_CLUSTER_ERROR  int64 = 440004
	ERROR_CODE_DELETE_CLUSTER_ERROR  int64 = 440008

	ERROR_CODE_MODIFY_BUCKET_ERROR  int64 = 440005
	ERROR_CODE_MODIFY_BUCKET_FAILED int64 = 440006

	ERROR_CODE_GET_RESOURCE_SIZE_LIMIT_FAILED int64 = 440007
)

const (
	InternalError                             string = "InternalError"
	LimitExceeded                             string = "LimitExceeded"
	InvalidParameterValue_ClusterId           string = "InvalidParameterValue.ClusterId"
	InvalidParameterValue_ListTooLong         string = "InvalidParameterValue.ListTooLong"
	ResourceNotFound_ClusterId                string = "ResourceNotFound.ClusterId"
	InvalidParameterValue_JobName             string = "InvalidParameterValue.JobName"
	InvalidParameterValue_VariableName        string = "InvalidParameterValue.VariableName"
	InvalidParameterValue_ClusterType_JobType string = "InvalidParameterValue.JobTypeCombineWithClusterType"
	FailedOperation_DuplicatedJobName         string = "FailedOperation.DuplicatedJobName"
	CAMNotAuthorized                          string = "CAMNotAuthorized"

	ResourceNotFound_JobId                        string = "ResourceNotFound.JobId"
	InvalidParameterValue_JobType_EntrypointClass string = "InvalidParameterValue.JobTypeCombineWithEntrypointClass"

	ResourceInsufficient        string = "ResourceInsufficient"
	InvalidParameterValue_CuMem string = "InvalidParameterValue.CuMem"

	InvalidParameterValue_Password string = "InvalidParameterValue.Password"

	FailedOperation_DuplicatedClusterName string = "FailedOperation.DuplicatedClusterName"

	FailedOperation_JobConfigOnPublish      string = "FailedOperation.JobConfigOnPublish"
	FailedOperation_DraftConfigCanNotDelete string = "FailedOperation.DraftConfigCanNotDelete"

	FailedOperation_DuplicatedResourceName     string = "FailedOperation.DuplicatedResourceName"
	FailedOperation_InvalidClusterName         string = "FailedOperation.InvalidClusterName"
	FailedOperation_InvalidRegionZoneParameter string = "FailedOperation.InvalidRegionZoneParameter"
	FailedOperation_InvalidCPUParameter        string = "FailedOperation.InvalidCPUParameter"
	FailedOperation_InvalidVPCParameter        string = "FailedOperation.InvalidVPCParameter"
	FailedOperation_UserNotAuthenticated       string = "FailedOperation.UserNotAuthenticated"
	FailedOperation_ClusterNotFound            string = "FailedOperation.ClusterNotFound"

	FailedOperation_CreateK8sCommandClient    string = "FailedOperation.CreateK8sCommandClient"
	FailedOperation_CreateK8sClient           string = "FailedOperation.CreateK8sClient"
	FailedOperation_GetImageSecrets           string = "FailedOperation.GetImageSecrets"
	FailedOperation_InitMetaDatabaseOrCatalog string = "FailedOperation.InitMetaDatabaseOrCatalog"
	ResourceUnavailable                       string = "ResourceUnavailable.Cluster"

	// 未录入(PublishJob)
	ResourceInUse                string = "ResourceInUse"
	InvalidParameterValue        string = "InvalidParameterValue"
	UnsupportedOperation         string = "UnsupportedOperation"
	UnsupportedOperation_Cluster string = "UnsupportedOperation.Cluster"

	ResourceNameAlreadyExists         string = "ResourceNameAlreadyExists"
	ResourceNotFound                  string = "ResourceNotFound"
	InternalError_AppIdNotInWhiteList string = "InternalError.AppIdNotInWhiteList"
	ResourceNotFound_ResourceLoc      string = "ResourceNotFound.ResourceLoc"

	InternalError_ResourceConfigCanNotDelete string = "InternalError.ResourceConfigCanNotDelete"

	UnsupportedFlinkVersion_CheckSqlGrammar string = "UnsupportedOperation.FlinkVersionCheckSqlGrammar"

	FailedOperation_CreateCluster string = "FailedOperation.CreateCluster"

	InvalidParameterValue_VpcParameterEmpty          = "InvalidParameterValue.VPCParameterEmpty"
	InvalidParameterValue_CosParameterEmpty   string = "InvalidParameterValue.COSParameterEmpty"
	InvalidParameterValue_MetaTableExisted    string = "InvalidParameterValue.MetaTableExisted"
	InvalidParameterValue_MetaDatabaseExisted string = "InvalidParameterValue.MetaDatabaseExisted"
	InvalidParameterValue_MetaTableName       string = "InvalidParameterValue.MetaTableName"
	InvalidParameterValue_MetaDatabaseName    string = "InvalidParameterValue.MetaDatabaseName"
	FailedOperation_CheckDdlGrammar           string = "FailedOperation.CheckDdlGrammar"
	InvalidParameter_CreateVariableExists     string = "InvalidParameter.CreateVariableExists"

	LimitExceeded_MetaTable    string = "LimitExceeded.MetaTable"
	LimitExceeded_MetaDatabase string = "LimitExceeded.MetaDatabase"
	LimitExceeded_Variables    string = "LimitExceeded.Variables"

	InvalidClusterState      string = "InternalError.InvalidClusterState"
	InvalidClusterGroupState string = "InternalError.InvalidClusterGroupState"
	InvalidTkeState          string = "InternalError.InvalidTkeState"
	InvalidRequestParam      string = "InvalidParameter"

	FailedCheckEtlFeature string = "InternalError.FailedCheckEtlFeature"
	FailedSendAlarm       string = "InternalError.FailedSendAlarm"

	FailedOperation_ConnectorNotFound   string = "FailedOperation.ConnectorNotFound"
	FailedOperation_CheckSqlGrammar     string = "FailedOperation.CheckSqlGrammar"
	FailedOperation_GrammarCheckFailure string = "FailedOperation.GrammarCheckFailure"
	FailedOperation_ParseSql            string = "FailedOperation.ParseSql"

	// ITEM_SPACE
	InvalidParameterValue_ItemSpaceName string = "InvalidParameterValue.ItemSpaceName"
	FailedOperation_DuplicatedSpaceName string = "FailedOperation.DuplicatedSpaceName"
	NoAuthorization_ItemSpaceAccess     string = "NoAuthorization.ItemSpaceAccess"

	FailedOperation_JobQueryFailure       string = "FailedOperation.JobQueryFailure"
	FailedOperation_CommandOperateFailure string = "FailedOperation.CommandOperateFailure"
	FailedOperation_DeleteWedataItemspace string = "FailedOperation.DeleteWedataItemspace"
)
