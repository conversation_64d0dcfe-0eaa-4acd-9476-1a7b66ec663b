package cluster_master

import (
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cluster_master_protocol"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster_master"
)

/**
 *
 * <AUTHOR>
 * @time 2018/11/22
 * @version 0.3.6
 * @since 0.3.6
 * @see
 */
func init() {
	const controllerName = "qcloud.galileo.tca.finishCommands"
	if code, msg := httpserver.RegisterController(controllerName, &finishCommandsController{}); code != 0 {
		logger.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type finishCommandsReq struct {
	CmdExeResults 		[]*cluster_master_protocol.CommandResult
}

type finishCommandsController struct {
}

func (this *finishCommandsController) CreateRequestObj() interface{} {
	return &finishCommandsReq{}
}

func (this *finishCommandsController) Process(req interface{}, eventId int64) (int64, string, interface{}) {
	reqData := req.(*finishCommandsReq)
	err := cluster_master.FinishCommands(reqData.CmdExeResults)
	if err != nil {
		logger.Errorf("Failed to update command execution result, errors: %+v", err)
		return constants.SYSERR, err.Error(), nil
	}

	return constants.SUCCESS, "ok", nil
}
