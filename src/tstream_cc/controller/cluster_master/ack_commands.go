package cluster_master

import (
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"fmt"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster_master"
)

/**
 *
 * <AUTHOR>
 * @time 2018/11/22
 * @version 0.3.6
 * @since 0.3.6
 * @see
 */
func init() {
	const controllerName = "qcloud.galileo.tca.ackCommands"
	if code, msg := httpserver.RegisterController(controllerName, &ackCommandsController{}); code != 0 {
		logger.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type ackCommandsReq struct {
	CommandIds 		[]int64
}

type ackCommandsController struct {
}

func (this *ackCommandsController) CreateRequestObj() interface{} {
	return &ackCommandsReq{}
}

func (this *ackCommandsController) Process(req interface{}, eventId int64) (int64, string, interface{}) {
	reqData := req.(*ackCommandsReq)
	err := cluster_master.AckCommands(reqData.CommandIds)
	if err != nil {
		logger.Error(fmt.Sprintf("Failed to save ack status to local db, errors: %v", err))
		return constants.SYSERR, err.Error(), nil
	}

	return constants.SUCCESS, "ok", nil
}

