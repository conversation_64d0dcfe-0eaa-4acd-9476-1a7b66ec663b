package cluster_master

import (
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster_master"
	clustermaster2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster_master"
)

/**
 *
 * <AUTHOR>
 * @time 2018/11/22
 * @version 0.3.6
 * @since 0.3.6
 * @see
 */
func init() {
	const controllerName = "qcloud.galileo.tca.pullCommands"
	if code, msg := httpserver.RegisterController(controllerName, &pullCommandsController{}); code != 0 {
		logger.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type getCommandsReq struct {
	ClusterGroupId		int64    	`check:"nullable:false|range:(0, INF)"`
	BatchSize			int8    		`check:"nullable:false|range:(0, INF)"`
}

type getCommandsRsp struct {
	Commands		[]*cluster_master.Command
}

type pullCommandsController struct {
}

func (this *pullCommandsController) CreateRequestObj() interface{} {
	return &getCommandsReq{}
}

func (this *pullCommandsController) Process(req interface{}, eventId int64) (int64, string, interface{}) {
	reqData := req.(*getCommandsReq)
	commands, err := clustermaster2.ListCommands(reqData.ClusterGroupId, reqData.BatchSize, constants.CLUSTER_MASTER_COMMAND_CREATE)
	if err != nil {
		logger.Errorf("Failed to List Commands, with req:%+v, with errors:%+v", reqData, err)
		return constants.SYSERR, err.Error(), nil
	}
	resp := getCommandsRsp{}
	resp.Commands = commands
	return constants.SUCCESS, constants.OK, resp
}
