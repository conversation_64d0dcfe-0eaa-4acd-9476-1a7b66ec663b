package watchdog

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/watchdog"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/job"
)

func init() {
	const controllerName = "qcloud.galileo.watchdog.updateSavepoint"
	if code, msg := httpserver.RegisterController(controllerName, &updateSavepointController{}); code != 0 {
		logger.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ", msg: " + msg)
	}
}

type updateSavepointController struct{}

func (*updateSavepointController) CreateRequestObj() interface{} {
	return &watchdog.UpdateSavepointReq{}
}

func (*updateSavepointController) Process(req interface{}, eventId int64) (mcErrorCode int64, msg string, rsp interface{}) {
	reqData := req.(*watchdog.UpdateSavepointReq)
	status, msg, rsp, err := service.UpdateSavepoint(reqData)
	if err != nil {
		return controller.ERROR_CODE_JOB_NOTIFY_SUCCEEDED_FAILURE, err.Error(), rsp
	}
	return status, msg, rsp
}
