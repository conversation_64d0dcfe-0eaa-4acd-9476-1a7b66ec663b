package watchdog

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/watchdog"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/event"
)

func init() {
	const controllerName = "qcloud.galileo.watchdog.createEvent"
	if code, msg := httpserver.RegisterController(controllerName, &createEventController{}); code != 0 {
		logger.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ", msg: " + msg)
	}
}

type createEventController struct{}

func (*createEventController) CreateRequestObj() interface{} {
	return &watchdog.CreateEventReq{}
}

func (*createEventController) Process(req interface{}, eventId int64) (int64, string, interface{}) {
	reqData := req.(*watchdog.CreateEventReq)
	rsp, err := event.DoCreateEvent(reqData)
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		code := errorcode.GetCode(err)
		return controller.ERROR_CODE_JOB_RECOVER_FAILURE, code.GetCodeDesc(), rsp
	}
	return controller.SUCCESS, controller.NULL, rsp
}
