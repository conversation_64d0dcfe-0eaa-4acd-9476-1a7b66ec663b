package bucket

import (
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"strconv"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/bucket"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/bucket"
)

func init() {
	const controllerName = "qcloud.galileo.mc.disableBucket"
	if code, msg := httpserver.RegisterController(controllerName, &DisableBucketController{}); code != 0 {
		logger.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type DisableBucketController struct {
}

func (this *DisableBucketController) CreateRequestObj() interface{} {
	return &model.DisableBucketReq{}
}

func (this *DisableBucketController) Process(req interface{}, eventId int64) (int64, string, interface{}) {
	reqData := req.(*model.DisableBucketReq)
	return service.DoDisableBucket(reqData)
}
