package billing

import (
	"encoding/json"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	service2 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/billing"
	billing2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/billing"
)

/**
 * 计费模块：生命周期策略
 *
 * 拉取拥有资源的所有用户appId
 *
 * http://tapd.oa.com/pt_jifei/markdown_wikis/show/#1210140771000569649
 */
func init() {
	// 提供给国际站使用
	// 因为这个接口只有appid， 没有办法区分是国际站还是国内站
	/*
		h_QCloudBilling_Register_Helper 8-2 下午 2:23
		你们国际站和国内站现网也是一样的域名吗

		sarenlin 8-2 下午 2:25
		恩， 是的

		h_QCloudBilling_Register_Helper 8-2 下午 2:38
		那有个接口无法区分是国内还是国际站的，会导致有问题，现在不是国内国际需要隔离开吗

		h_QCloudBilling_Register_Helper 8-2 下午 2:39
		https://tcb.woa.com/magical-brush/docs/754664841

		sarenlin 8-2 下午 2:40
		这个文档上， 好像没有说哪个接口要区分 国内站和国际站？

		h_QCloudBilling_Register_Helper 8-2 下午 2:41
		别的接口都有uin，可以通过uin区分，这个接口没有uin，无法区分是国内还是国际的

		h_QCloudBilling_Register_Helper 8-2 下午 2:44
		之前公司要求国际和国内的服务数据要隔离，你们还没有隔离吗，国际站的数据需要部署在海外
	*/
	const controllerName = "qcloud.oceanus.getAllIntlAppids"
	if code, msg := httpserver.RegisterController(controllerName, &getAllIntlAppIdsController{}); code != 0 {
		logger.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type getAllIntlAppIdsController struct {
}

func (g *getAllIntlAppIdsController) CreateRequestObj() interface{} {
	return &billing.GetAllAppIdsReq{}
}

func (g *getAllIntlAppIdsController) Process(req interface{}, eventId int64) (int64, string, interface{}) {
	reqData := req.(*billing.GetAllAppIdsReq)

	jsonReq, _ := json.Marshal(reqData)
	logger.Infof("[%d] Request data %+v", eventId, string(jsonReq))

	/**
	之前是预付费模式，现在接入后付费， 新的接口会传 PayMode 和 subProductCode，但老的不会， 所以要给默认值
	*/
	defaultPayMode := billing.PayModePrepaid
	if reqData.PayMode == nil {
		reqData.PayMode = &defaultPayMode
	}
	defaultSubProductCode := billing.OceanusExclusiveSubProductCode
	if reqData.SubProductCode == nil {
		reqData.SubProductCode = &defaultSubProductCode
	}

	// 1. 如果资源区分地域，则需要支持按地域查询
	// 2. 若多个业务使用同一接口，则需区分各业务的type（子产品标签，商品码）
	// 3. 接口须支持分页，接口必须是同步， 需保证数据准备，涉及到发通知
	// 4. 处于隔离中的资源，也要返回其用户appId
	// 5. 生命周期逻辑：http://tapd.oa.com/pt_jifei/markdown_wikis/#1010140771010429089

	rsp, err := g.process(reqData)
	if err != nil {
		logger.Errorf("[%d] err:%s", eventId, err.Error())
		code := errorcode.GetCode(err)
		return constants.SYSERR, code.GetCodeDesc(), rsp
	}

	jsonRsp, _ := json.Marshal(rsp)
	logger.Infof("[%d] Rsp %s", eventId, string(jsonRsp))

	return constants.SUCCESS, "ok", rsp
}

func (g *getAllIntlAppIdsController) process(req *billing.GetAllAppIdsReq) (rsp *billing.GetAllAppIdsResp, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, "getAllAppIdsController")
	rsp = &billing.GetAllAppIdsResp{}
	s := billing2.NewBillingResourceService()
	rsp.Total, err = s.GetPayAppIdsTotalCount(*req.SubProductCode, req.Region, billing.BillingResourceInternational, *req.PayMode)
	if err != nil {
		return
	}
	rsp.AppIds, err = s.GetPayAppIds(*req.SubProductCode, req.Region, billing.BillingResourceInternational, &req.PageNo, &req.PageSize,  *req.PayMode)
	if err != nil {
		return rsp, err
	}

	// 如果是 测试环境，则添加测试号码到结果里面
	// 因为 测试环境，在所有api的入口， 会把appid/uin 替换成 配置里面的appid/uin, 所以db里面记录的会没有原始请求的appid
	// 而测试环境，我们只会使用到 251196547 这个appid
	isDev := service2.GetConfStringValue("scsDevEnv")
	if isDev == "true" && len(rsp.AppIds) != 0 {
		rsp.AppIds = append(rsp.AppIds, 251196547)
		// 国际站测试号码
		rsp.AppIds = append(rsp.AppIds, 251009759)
	}
	return rsp, err
}
