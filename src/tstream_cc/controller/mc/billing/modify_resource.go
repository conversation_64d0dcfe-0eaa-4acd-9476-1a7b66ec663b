package billing

import (
	"encoding/json"
	"fmt"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/dlocker"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service2 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/billing"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/cluster"
	billing3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/billing"
	billing2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/billing"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/region"
)

/**
 * 计费模块：配置变更策略 feature-860782587
 *
 * 变配资源（发货）接口
 *
 * http://tapd.oa.com/pt_jifei/markdown_wikis/show/#1210140771000569591
 */
func init() {
	const controllerName = "qcloud.oceanus.modifyResource"
	if code, msg := httpserver.RegisterController(controllerName, &modifyResourceController{}); code != 0 {
		logger.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type modifyResourceController struct {
}

func (m *modifyResourceController) CreateRequestObj() interface{} {
	return &billing.ModifyResourceReq{}
}

func (m *modifyResourceController) Process(req interface{}, eventId int64) (retCode int64, errMsg string, response interface{}) {
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf("Failed to call modifyResource because %+v", err)
			retCode = controller.SYSERR
			errMsg = "Fatal system error"
		}
	}()

	reqData := req.(*billing.ModifyResourceReq)

	// 打印 Event ID 和请求信息
	logger.Infof("[%d] Request data %+v", eventId, reqData)

	resp := billing.ModifyResourceResp{}

	billingResource, err := billingResourceService.GetResourceById(reqData.ResourceId)
	if err != nil {
		return constants.SYSERR, "cannot get cluster by resource id", resp
	}

	var clusterGroupSerialId string
	if billingResource.Type == constants.BILLINGRESOURCE_TYPE_CLUSTER {
		clusterGroupSerialId = reqData.ResourceId
	} else {
		_setats, err := service.GetSetatsBySerialId(reqData.ResourceId)
		if err != nil {
			logger.Errorf("Failed to get setats by resource id %s because %+v", reqData.ResourceId, err)
			panic(err)
		}
		clusterGroupSerialId = _setats.ClusterGroupSerialId
	}

	// 获取资源信息
	clusterGroup, err := service.ListClusterGroupBySerialId(clusterGroupSerialId)
	if err != nil {
		logger.Errorf("Failed to get cluster group by serial id %s because %+v", reqData.ResourceId, err)
		panic(err)
	}
	cluster, err := service.GetActiveClusterByClusterGroupId(clusterGroup.Id)
	if err != nil {
		logger.Errorf("Failed to get cluster by cluster group id %d because %+v", clusterGroup.Id, err)
		panic(err)
	}

	if billingResource.Type == constants.BILLINGRESOURCE_TYPE_CLUSTER {

		// 多可用区部署
		if clusterGroup.DeploymentMode == constants.DeploymentModeMultiple {
			if reqData.GoodsDetail.NewConfig.OceanusExclusiveComputeMultipleCu14 < 1 {
				logger.Errorf("Modify_OceanusExclusiveComputeMultipleCu14 invalid.")
				return constants.SUCCESS, "Modify_OceanusExclusiveComputeMultipleCu14 invalid.", resp
			}
			if reqData.GoodsDetail.OldConfig.OceanusExclusiveComputeMultipleCu14 < 1 {
				logger.Errorf("Modify_OceanusExclusiveComputeMultipleCu14 invalid.")
				return constants.SUCCESS, "Modify_OceanusExclusiveComputeMultipleCu14 invalid.", resp
			}
			// 虽然计费项不同，会统一赋值到这里，方便走统一校验逻辑
			reqData.GoodsDetail.NewConfig.OceanusExclusiveComputeCu = reqData.GoodsDetail.NewConfig.OceanusExclusiveComputeMultipleCu14
			reqData.GoodsDetail.OldConfig.OceanusExclusiveComputeCu = reqData.GoodsDetail.OldConfig.OceanusExclusiveComputeMultipleCu14
		}

		// 1:2 1:8
		if cluster.MemRatio == constants.CVM_MEMRATIO_2 {
			if reqData.GoodsDetail.NewConfig.OceanusExclusiveComputeCu12 < 1 {
				logger.Errorf("Modify_sv_oceanus_compute_exclusive_cu1c2g invalid.")
				return constants.SYSERR, "Modify_sv_oceanus_compute_exclusive_cu1c2g invalid.", resp
			}
			if reqData.GoodsDetail.OldConfig.OceanusExclusiveComputeCu12 < 1 {
				logger.Errorf("Modify_sv_oceanus_compute_exclusive_cu1c2g invalid.")
				return constants.SYSERR, "Modify_sv_oceanus_compute_exclusive_cu1c2g invalid.", resp
			}
			// 虽然计费项不同，会统一赋值到这里，方便走统一校验逻辑
			reqData.GoodsDetail.NewConfig.OceanusExclusiveComputeCu = reqData.GoodsDetail.NewConfig.OceanusExclusiveComputeCu12
			reqData.GoodsDetail.OldConfig.OceanusExclusiveComputeCu = reqData.GoodsDetail.OldConfig.OceanusExclusiveComputeCu12
		}
		if cluster.MemRatio == constants.CVM_MEMRATIO_8 {
			if reqData.GoodsDetail.NewConfig.OceanusExclusiveComputeCu18 < 1 {
				logger.Errorf("Modify_sv_oceanus_compute_exclusive_cu1c8g invalid.")
				return constants.SYSERR, "Modify_sv_oceanus_compute_exclusive_cu1c8g invalid.", resp
			}
			if reqData.GoodsDetail.OldConfig.OceanusExclusiveComputeCu18 < 1 {
				logger.Errorf("checkModify_sv_oceanus_compute_exclusive_cu1c8g invalid.")
				return constants.SYSERR, "Modify_sv_oceanus_compute_exclusive_cu1c8g invalid.", resp
			}
			// 虽然计费项不同，会统一赋值到这里，方便走统一校验逻辑
			reqData.GoodsDetail.NewConfig.OceanusExclusiveComputeCu = reqData.GoodsDetail.NewConfig.OceanusExclusiveComputeCu18
			reqData.GoodsDetail.OldConfig.OceanusExclusiveComputeCu = reqData.GoodsDetail.OldConfig.OceanusExclusiveComputeCu18
		}

		/**
		 * 最前面校验  OceanusExclusiveComputeCu 和 ComputeCu， 包年包月
		 * 逻辑需要兼容不传 ComputeCu 的 情况
		 * 需要同时校验 ===新配置 和 老配置===
		 */
		if billingResource.IsNeedManageNode == constants.NEED_MANAGE_NODE {
			newOceanusExclusiveComputeCu := reqData.GoodsDetail.NewConfig.OceanusExclusiveComputeCu
			newComputeCu := reqData.GoodsDetail.NewConfig.ComputeCu
			oldOceanusExclusiveComputeCu := reqData.GoodsDetail.OldConfig.OceanusExclusiveComputeCu
			oldComputeCu := reqData.GoodsDetail.OldConfig.ComputeCu
			/**
			 * 校验是不是新老都传递了 ComputeCu
			 */
			if (newComputeCu == 0 && oldComputeCu != 0) || (newComputeCu != 0 && oldComputeCu == 0) {
				errMsg := fmt.Sprintf("new ComputeCu(%d)'s rule not same as old ComputeCu(%d)",
					newComputeCu, oldComputeCu)
				logger.Errorf(errMsg)
				return controller.SYSERR, errMsg, resp
			}

			checkManageCuRst, errMsg, _ := billing2.CheckManageCu(newOceanusExclusiveComputeCu, newComputeCu)
			if checkManageCuRst != controller.SUCCESS {
				// 校验失败， returnCode 也应该为0， 计费侧通过 resp data里面的status 判断是否可以购买
				return controller.SYSERR, errMsg, resp
			}
			reqData.GoodsDetail.NewConfig.OceanusExclusiveComputeCu = newComputeCu

			checkManageCuRst, errMsg, _ = billing2.CheckManageCu(oldOceanusExclusiveComputeCu, oldComputeCu)
			if checkManageCuRst != controller.SUCCESS {
				// 校验失败， returnCode 也应该为0， 计费侧通过 resp data里面的status 判断是否可以购买
				return controller.SYSERR, errMsg, resp
			}
			reqData.GoodsDetail.OldConfig.OceanusExclusiveComputeCu = oldComputeCu
		}
	}

	// Trivial case: 首先查询这个 DealName 的 Pending 状态的 FlowId, 如果有记录且存在，则直接返回 OK
	errMsg, resp = m.getPendingBillingOrder(reqData, resp)
	if errMsg != "" { // 查到了发货中的记录
		return controller.SUCCESS, errMsg, resp
	}

	// 如果记录不存在, 尝试加锁
	locker := dlocker.NewDlocker("oceanus-billing-modifyResource", reqData.DealName, 8000)
	err = locker.Lock()
	if err != nil { // 加锁失败, 且没有 flowId, 就返回 flowId = 0 让计费重试
		logger.Infof("Another modifyResource process for %s has lock but not finished yet", reqData.DealName)
		return controller.SUCCESS, "Another modifyResource instance is still going, not ready yet", resp
	}
	defer locker.UnLock()

	// 加锁后再次检查（Double-Checked Locking 模式), 防止之前查到的是旧数据
	errMsg, resp = m.getPendingBillingOrder(reqData, resp)
	if errMsg != "" { // 查到了发货中的记录
		return controller.SUCCESS, errMsg, resp
	}

	// 转换地域信息
	regionName, err := region.GetRegionNameById(reqData.Region)
	if err != nil {
		logger.Errorf("Unsupported region %d %+v", reqData.Region, err)
		return controller.SYSERR, "Unsupported region", resp
	}
	zone, err := region.GetZoneById(reqData.ZoneId)
	if err != nil {
		logger.Errorf("Unsupported zone id %d %+v", reqData.ZoneId, err)
		return controller.SYSERR, "Unsupported zone", resp
	}

	// 初始化 Order 记录
	newBillingOrder := m.createNewBillingOrder(reqData)
	txManager := service2.GetTxManager()

	var orderId int64
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		orderId = tx.SaveObject(newBillingOrder, "BillingOrder")

		flowId := int64(0)
		if billingResource.Type == constants.BILLINGRESOURCE_TYPE_CLUSTER {
			// 创建集群 Flow
			scaleClusterRequest := m.generateModifyClusterRequest(reqData, eventId, regionName, zone)
			flowId, err = service.ScaleCluster(scaleClusterRequest)
			if err != nil {
				logger.Errorf("Failed to modify cluster because %+v", err)
				retCode, errMsg, response = controller.SYSERR, "Failed to modify cluster", resp
				return nil
			}
		} else if billingResource.Type == constants.BILLINGRESOURCE_TYPE_SETATS {

			tx.ExecuteSqlWithArgs("update Setats set Status=? where ClusterGroupSerialId=? and Status != -2", constants.SETATS_SCALE_PROGRESS, clusterGroup.SerialId)

			_setats, err := service.GetSetatsBySerialId(reqData.ResourceId)
			if err != nil {
				logger.Errorf("Failed to get setats by resource id %s because %+v", reqData.ResourceId, err)
				retCode, errMsg, response = controller.SYSERR, "Failed to modify cluster", resp
				return nil
			}
			requestId := service2.Itoa(eventId) + " " + reqData.DealName
			newWorkerDiskSize := reqData.GoodsDetail.NewConfig.WorkerInfo.Disk.DiskSize
			newMasterDiskSize := reqData.GoodsDetail.NewConfig.MasterInfo.Disk.DiskSize
			newWorkerDefaultParallelism := reqData.GoodsDetail.NewConfig.WorkerInfo.DefaultParallelism

			targetWorkerDiskSize := 0
			targetMasterDiskSize := 0
			targetDeployWorkerParallelism := 0
			isScaleDisk := 0
			isScaleWorker := 0
			if newWorkerDiskSize > _setats.WorkerDiskSize {
				targetWorkerDiskSize = newWorkerDiskSize
				isScaleDisk = 1
			}
			if newMasterDiskSize > _setats.MasterDiskSize {
				targetMasterDiskSize = newMasterDiskSize
				isScaleDisk = 1
			}
			if newWorkerDefaultParallelism > _setats.WorkerDefaultParallelism {
				targetDeployWorkerParallelism = newWorkerDefaultParallelism
				isScaleWorker = 1
			}

			params := map[string]string{
				constants.FLOW_PARAM_CLUSTER_GROUP_ID:                fmt.Sprintf("%d", clusterGroup.Id),
				constants.FLOW_PARAM_CLUSTER_ID:                      fmt.Sprintf("%d", cluster.Id),
				constants.FLOW_PARAM_REQUEST_ID:                      requestId,
				constants.FLOW_PARAM_REGION:                          regionName,
				constants.FLOW_PARAM_APPID:                           fmt.Sprintf("%d", reqData.AppId),
				constants.FLOW_PARAM_SETATS_MASTER_DISK_SIZE:         fmt.Sprintf("%d", targetMasterDiskSize),
				constants.FLOW_PARAM_SETATS_WORKER_DISK_SIZE:         fmt.Sprintf("%d", targetWorkerDiskSize),
				constants.FLOW_PARAM_SETATS_WORKERDEFAULTPARALLELISM: fmt.Sprintf("%d", targetDeployWorkerParallelism),
			}
			if 1 == isScaleWorker {
				params[constants.FLOW_PARAM_SETATS_SCALE_WORKER] = "1"
			}
			if 1 == isScaleDisk {
				params[constants.FLOW_PARAM_SETATS_SCALE_DISK] = "1"
			}

			// 创建集群组 Flow
			flowId, err = flow.CreateFlow(constants.FLOW_OCEANUS_SCALE_SETATS,
				fmt.Sprintf("%s@%s@%s", clusterGroup.SerialId, reqData.ResourceId, requestId), reqData.Region, params, nil)
			if err != nil {
				logger.Errorf("Failed to modify cluster because %+v", err)
				retCode, errMsg, response = controller.SYSERR, "Failed to modify cluster", resp
				return nil
			}
		}

		resp.FlowId = int(flowId) // FlowId 只是发货期间临时查询, 位数截断不会造成风险. flowId 为 0 则计费会重试

		// 设置 BillingOrder 的各个字段
		newBillingOrder.FlowId = flowId
		newBillingOrder.ClusterId = cluster.Id
		newBillingOrder.ClusterGroupId = clusterGroup.Id
		newBillingOrder.Status = billing.OrderStatusPending
		newBillingOrder.ResourceId = reqData.ResourceId

		// 更新 Order 表 (status = 2 & flowId 以及下面字段)
		tx.UpdateObject(newBillingOrder, orderId, "BillingOrder")

		// 更新 BillingResource 表
		billingResource, err := billingResourceService.GetResourceById(reqData.ResourceId)
		if err != nil {
			logger.Errorf("Failed to get billing resource by id because %+v", err)
			panic(err)
		}
		billingResource.ComputeCu = reqData.GoodsDetail.NewConfig.OceanusExclusiveComputeCu
		billingResource.FlowId = flowId
		goodsDetailStr, err := json.Marshal(reqData.GoodsDetail.NewConfig)
		if err != nil {
			logger.Errorf("Failed to marshal reqDat.GoodsDetail because %+v", reqData.GoodsDetail)
		}
		billingResource.GoodsDetail = string(goodsDetailStr)
		tx.UpdateObject(billingResource, billingResource.Id, "BillingResource")

		logger.Infof("Successfully scaled cluster %s from %d to %d CU with cluster group id %d, cluster id %d. Flow id %d",
			clusterGroup.SerialId,
			reqData.GoodsDetail.OldConfig.OceanusExclusiveComputeCu,
			reqData.GoodsDetail.NewConfig.OceanusExclusiveComputeCu,
			clusterGroup.Id, cluster.Id, flowId)

		retCode, errMsg, response = constants.SUCCESS, "ok", resp
		return nil
	}).Close()

	return
}

// 查询状态为 Pending（发货中）的 BillingOrder 对象, 找不到则返回空字符串
func (m *modifyResourceController) getPendingBillingOrder(reqData *billing.ModifyResourceReq, resp billing.ModifyResourceResp) (string, billing.ModifyResourceResp) {
	billingOrder, err := billing2.GetBillingOrderByDealName(reqData.DealName)
	if err != nil {
		logger.Errorf("Failed to GetBillingOrderByDealName because %+v", err)
		panic(err)
	}

	if billingOrder != nil { // 如果 flowId 不为 0, 会重试调用 queryFlow 接口
		logger.Infof("On-going billing order for %s (%s) exists: %+v", reqData.DealName, billingOrder.ResourceId, billingOrder)
		resp.FlowId = int(billingOrder.FlowId)
		return "Cluster modification in progress", resp
	}
	return "", resp
}

// 创建订单记录
func (m *modifyResourceController) createNewBillingOrder(reqData *billing.ModifyResourceReq) billing3.BillingOrder {
	return billing3.BillingOrder{
		AppId:          reqData.AppId,
		DealName:       reqData.DealName,
		Uin:            reqData.Uin,
		OperateUin:     reqData.OperateUin,
		Region:         reqData.Region,
		ZoneId:         reqData.ZoneId,
		PayMode:        reqData.PayMode,
		TimeUnit:       reqData.GoodsDetail.NewConfig.TimeUnit,
		TimeSpan:       0,  // 变配不涉及续费
		AutoRenewFlag:  -1, // 无意义
		SubProductCode: reqData.GoodsDetail.NewConfig.SubProductCode,
		ComputeCu:      reqData.GoodsDetail.NewConfig.OceanusExclusiveComputeCu,
		Status:         billing.OrderStatusInitialized,
		CategoryId:     billing.OceanusExclusiveCategoryIdModify,
		CreateTime:     util.GetCurrentTime(),
		UpdateTime:     util.GetCurrentTime(),
	}
}

func (m *modifyResourceController) generateModifyClusterRequest(reqData *billing.ModifyResourceReq, eventId int64, region string, zone string) *model.ScaleClusterReq {
	return &model.ScaleClusterReq{
		RequestBase: apiv3.RequestBase{
			Action:        "ScaleCluster",
			Region:        region,
			RequestId:     service2.Itoa(eventId) + " " + reqData.DealName,
			AppId:         reqData.AppId,
			Uin:           reqData.Uin,
			SubAccountUin: reqData.OperateUin,
		},
		ComputeUnitNum: int16(reqData.GoodsDetail.NewConfig.OceanusExclusiveComputeCu),
		ClusterId:      reqData.ResourceId,
	}
}
