package billing

import (
	"fmt"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/billing"
	billing2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/billing"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"time"
)

/**
 * 计费模块：续费策略
 *
 * 续费参数校验接口
 *
 * http://tapd.oa.com/pt_jifei/markdown_wikis/show/#1210140771000569611
 */
func init() {
	const controllerName = "qcloud.oceanus.checkRenew"
	if code, msg := httpserver.RegisterController(controllerName, &checkRenewController{}); code != 0 {
		logger.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

var billingResourceService = &billing2.BillingResourceService{}

type checkRenewController struct {
}

func (c *checkRenewController) CreateRequestObj() interface{} {
	return &billing.CheckRenewReq{}
}

func (c *checkRenewController) Process(req interface{}, eventId int64) (retCode int64, errMsg string, response interface{}) {
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf("Failed to call checkRenew because %+v", err)
			retCode = controller.SYSERR
			errMsg = "Fatal system error"
		}
	}()

	reqData := req.(*billing.CheckRenewReq)

	// 1. 业务后台需要检查续费资源请求包的每个参数是否准确
	// 2. 校验项目包括
	//    a.资源ID是否属于该用户
	//    b.资源当前到期时间是否与后台实际值一致
	//    c.产品相关的各类限制策略，比如续费时长、机型是否准备下线、pid与商品配置是否匹配等，否则可能会导致逻辑漏洞
	// 4. 接口必须是同步的

	// 打印 Event ID 和请求信息
	logger.Infof("[%d] Request data %+v", eventId, reqData)

	// 初始化参数
	resp := &billing.CheckRenewResp{}
	resp.Status = billing.StatusSystemError

	isCluster := true
	billingResource, err := billingResourceService.GetResourceById(reqData.ResourceId)
	if err != nil {
		resp.Status = billing.StatusSystemError
		return constants.SYSERR, "cannot get cluster by resource id", resp
	}
	isCluster = (billingResource.Type == constants.BILLINGRESOURCE_TYPE_CLUSTER)

	var clusterGroupSerialId string
	if billingResource.Type == constants.BILLINGRESOURCE_TYPE_CLUSTER {
		clusterGroupSerialId = reqData.ResourceId
		// 校验包销参数
		if reqData.GoodsDetail.BillingResourceMode != "" {
			if reqData.GoodsDetail.BillingResourceMode != billing.ExclusiveSale {
				return controller.SUCCESS, fmt.Sprintf("billingResourceMode must be exclusiveSale"), resp
			}
			if reqData.GoodsDetail.OceanusExclusiveComputeUnderwriteCu14 < 1 {
				return controller.SUCCESS, fmt.Sprintf("exclusiveSale mode sv_oceanus_compute_underwrite_cu1c4g invalid. "), resp
			}
			found := false
			for _, duration := range billing.Durations {
				if duration == reqData.GoodsDetail.Duration {
					logger.Infof("find Duration %s", duration)
					found = true
					break
				}
			}
			if !found {
				logger.Errorf("cannot find Duration %s", reqData.GoodsDetail.Duration)
				return controller.SUCCESS, fmt.Sprintf("duration must be 1y or 2y or 3y or 4y or 5y"), resp
			}
			allow := auth.IsInWhiteList(int64(reqData.AppId), constants.WHITE_LIST_EXCLUSIVE_COMPUTEUNDERWRITECU14)
			if !allow {
				return controller.SUCCESS, fmt.Sprintf("no permission to use exclusive mode"), resp
			}
			// 虽然计费项不同，会统一赋值到这里，方便走统一校验逻辑
			reqData.GoodsDetail.OceanusExclusiveComputeCu = reqData.GoodsDetail.OceanusExclusiveComputeUnderwriteCu14
		}
	} else {
		_setats, err := service.GetSetatsBySerialId(reqData.ResourceId)
		if err != nil {
			logger.Errorf("Failed to get setats by resource id %s because %+v", reqData.ResourceId, err)
			return constants.SUCCESS, "cannot get setats by resource id", resp
		}
		clusterGroupSerialId = _setats.ClusterGroupSerialId
	}

	// 获取资源信息
	clusterGroup, err := service.ListClusterGroupBySerialId(clusterGroupSerialId)
	if err != nil {
		logger.Errorf("Failed to get cluster group by serial id %s because %+v", reqData.ResourceId, err)
		return constants.SUCCESS, "cannot get cluster group by resource id", resp
	}
	cluster, err := service.GetActiveClusterByClusterGroupId(clusterGroup.Id)
	if err != nil {
		logger.Errorf("Failed to get cluster by cluster group id %d because %+v", clusterGroup.Id, err)
		return constants.SUCCESS, "cannot get cluster by resource id", resp
	}

	if billingResource.Type == constants.BILLINGRESOURCE_TYPE_CLUSTER {
		// 1:2 1:8
		if cluster.MemRatio == constants.CVM_MEMRATIO_2 {
			if reqData.GoodsDetail.OceanusExclusiveComputeCu12 < 1 {
				logger.Errorf("checkRenew_sv_oceanus_compute_exclusive_cu1c2g invalid.")
				return constants.SUCCESS, "checkRenew_sv_oceanus_compute_exclusive_cu1c2g invalid.", resp
			}

			// 虽然计费项不同，会统一赋值到这里，方便走统一校验逻辑
			reqData.GoodsDetail.OceanusExclusiveComputeCu = reqData.GoodsDetail.OceanusExclusiveComputeCu12
		}
		if cluster.MemRatio == constants.CVM_MEMRATIO_8 {
			if reqData.GoodsDetail.OceanusExclusiveComputeCu18 < 1 {
				logger.Errorf("checkRenew_sv_oceanus_compute_exclusive_cu1c8g invalid.")
				return constants.SUCCESS, "checkRenew_sv_oceanus_compute_exclusive_cu1c8g invalid.", resp
			}
			// 虽然计费项不同，会统一赋值到这里，方便走统一校验逻辑
			reqData.GoodsDetail.OceanusExclusiveComputeCu = reqData.GoodsDetail.OceanusExclusiveComputeCu18
		}

		/**
		 * 最前面校验  OceanusExclusiveComputeCu 和 ComputeCu， 包年包月
		 * 逻辑需要兼容不传 ComputeCu 的 情况
		 */

		if billingResource.IsNeedManageNode == constants.NEED_MANAGE_NODE {
			oceanusExclusiveComputeCu := reqData.GoodsDetail.OceanusExclusiveComputeCu
			computeCu := reqData.GoodsDetail.ComputeCu
			checkManageCuRst, errMsg, status := billing2.CheckManageCu(oceanusExclusiveComputeCu, computeCu)
			if checkManageCuRst != controller.SUCCESS {
				resp.Status = status
				// 校验失败， returnCode 也应该为0， 计费侧通过 resp data里面的status 判断是否可以购买
				return controller.SUCCESS, errMsg, resp
			}
			reqData.GoodsDetail.OceanusExclusiveComputeCu = computeCu
		}
	}

	// ==== 基准检查（检查基本参数）
	returnCode, msg, resp := c.checkBasicArguments(reqData, resp, isCluster)
	if returnCode != controller.SUCCESS {
		return controller.SUCCESS, msg, resp
	}

	// ==== 商品信息（GoodsDetail）检查 ====
	returnCode, msg, resp = c.checkGoodsDetail(reqData, resp, isCluster)
	if returnCode != controller.SUCCESS {
		return controller.SUCCESS, msg, resp
	}

	if !isCluster {
		//TODO
	}

	// 检查成功
	resp.Status = billing.StatusSuccess

	return constants.SUCCESS, "ok", resp
}

func (c *checkRenewController) checkBasicArguments(reqData *billing.CheckRenewReq, resp *billing.CheckRenewResp, isCluster bool) (int64, string, *billing.CheckRenewResp) {
	retCode, errMsg, status := billing2.CheckNecessaryArguments(
		int(reqData.Region),
		int(reqData.ZoneId),
		reqData.AppId,
		reqData.PayMode,
		reqData.OperateUin,
		reqData.Uin,
		0,
	)
	resp.Status = status
	if retCode != controller.SUCCESS {
		return retCode, errMsg, resp
	}

	if isCluster {
		// 然后做一些该接口独有的检查
		// 资源 ID 是否属于该用户（查询 ClusterGroup 表）
		clusterGroup, err := service.ListClusterGroupBySerialId(reqData.ResourceId)
		if err != nil {
			logger.Errorf("Failed to get cluster group by serial id because %+v", err)
			resp.Status = billing.StatusSystemError
			return constants.SYSERR, "Failed to get cluster group", resp
		}
		if clusterGroup.OwnerUin != reqData.Uin || clusterGroup.AppId != reqData.AppId {
			resp.Status = billing.StatusResourceNotBelongToThisUin
			return constants.PARAMSERR, "Resource not exist or not belong to this account", resp
		}

		// 检查续费传入的 CU 数和集群当前 CU 数是否一致
		if clusterGroup.CuNum != int16(reqData.GoodsDetail.OceanusExclusiveComputeCu) {
			resp.Status = billing.StatusInvalidCuNum
			return constants.PARAMSERR, "CU number for renewal is not consistent with existing one", resp
		}
	}

	return constants.SUCCESS, "", resp
}

func (c *checkRenewController) checkGoodsDetail(reqData *billing.CheckRenewReq, resp *billing.CheckRenewResp, isCluster bool) (int64, string, *billing.CheckRenewResp) {

	// 做一些 GoodsDetail 的公共检查
	var autoRenewFlag = 0 // 可选字段, 默认不传时当作 0
	if reqData.GoodsDetail.AutoRenewFlag != nil {
		autoRenewFlag = *reqData.GoodsDetail.AutoRenewFlag
	}
	retCode, errMsg, status := billing2.CheckNecessaryGoodsDetail(
		reqData.AppId,
		reqData.GoodsDetail.TimeUnit,
		reqData.GoodsDetail.TimeSpan,
		reqData.GoodsDetail.GoodsNum,
		reqData.GoodsDetail.Pid,
		autoRenewFlag,
		reqData.GoodsDetail.ProductCode,
		reqData.GoodsDetail.SubProductCode,
		billing.MinimalAllowedComputeUnit, // 占位符, 续费时不检查现有 CU, 避免历史集群无法续费
		reqData.PayMode,
		isCluster,
	)
	resp.Status = status
	if retCode != controller.SUCCESS {
		return retCode, errMsg, resp
	}

	// 然后做一些该接口独有的检查
	// 1 资源当前到期时间是否与后台实际值一致
	billingResource, err := billingResourceService.GetResourceById(reqData.ResourceId)
	if err != nil {
		resp.Status = billing.StatusSystemError
		return constants.SYSERR, "cannot get cluster by resource id", resp
	}
	if billingResource.ExpireTime != reqData.GoodsDetail.CurDeadline {
		resp.Status = billing.StatusNotConsistentExpireTime
		return constants.PARAMSERR, "current deadline not consistent with the database", resp
	}

	// 2 续费后的到期时间不超过当前 + 3年
	currentExpireTime, err := time.ParseInLocation(billing.StandardTimestampFormatString, billingResource.ExpireTime, time.Local)
	if err != nil {
		logger.Errorf("Cannot parse billingResource.ExpireTime because of invalid format: %+v", billingResource.ExpireTime)
		resp.Status = billing.StatusSystemError
		return constants.SYSERR, "cannot parse current expire time", resp
	}
	newExpireTime := billing2.CalculateExpireTime(currentExpireTime, reqData.GoodsDetail.TimeUnit, reqData.GoodsDetail.TimeSpan)
	maximumAllowedExpireTime := time.Now().AddDate(0, billing.MaximumAllowedTimeSpanMonth, 0) // 最长的续费时间（当前 + 3 年）

	if newExpireTime.After(maximumAllowedExpireTime) {
		resp.Status = billing.StatusRenewSpanExceedsLimit
		return constants.PARAMSERR, "renew TimeSpan exceeds limit", resp
	}

	return controller.SUCCESS, "", resp
}
