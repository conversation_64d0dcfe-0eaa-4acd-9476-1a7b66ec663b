package billing

import (
	"encoding/json"
	"fmt"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/dlocker"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/billing"
	billing2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/billing"
	roleAuthService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/role_auth"
)

/**
 * 计费模块：生命周期策略
 *
 * 资源释放（销毁）接口
 *
 * http://tapd.oa.com/pt_jifei/markdown_wikis/show/#1210140771000569633
 * https://tcb.woa.com/magical-brush/docs/754671434
 */
func init() {
	const controllerName = "qcloud.oceanus.destroyResource"
	if code, msg := httpserver.RegisterController(controllerName, &DestroyResourceController{}); code != 0 {
		logger.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type DestroyResourceController struct {
}

func (d *DestroyResourceController) CreateRequestObj() interface{} {
	return &billing.DestroyResourceReq{}
}

func (d *DestroyResourceController) Process(req interface{}, eventId int64) (int64, string, interface{}) {
	reqData := req.(*billing.DestroyResourceReq)

	jsonReq, _ := json.Marshal(reqData)
	logger.Infof("[%d] Request data %+v", eventId, string(jsonReq))

	defaultSubProductCode := billing.OceanusExclusiveSubProductCode
	if reqData.SubProductCode == nil {
		reqData.SubProductCode = &defaultSubProductCode
	}

	/**
	 * 只有管理员才能销毁集群.
	 */

	if reqData.Uin != reqData.OperateUin {
		admin, err := roleAuthService.RoleAuthoAdministorCheck(reqData.AppId, reqData.OperateUin)
		if err != nil || admin == nil || admin.Status != constants.ROLE_AUTHO_STATUS_USEABLE {
			logger.Errorf("### only admin can operate，RoleAuthoAdministorCheck fail with AppId(%d), OperateUin(%s) because %+v", reqData.AppId, reqData.OperateUin, err)
			return constants.SYSERR, "only admin can operate.", nil
		}
	}
	reqData.EventId = eventId
	rsp, err := d.process(reqData)
	if err != nil {
		logger.Errorf("[%d] err:%s", eventId, err.Error())
		code := errorcode.GetCode(err)
		return constants.SYSERR, code.GetCodeDesc(), rsp
	}
	if rsp.FlowId == billing.NoResourceFlag {
		return constants.SUCCESS, "ok", &struct{}{}
	}

	jsonRsp, _ := json.Marshal(rsp)
	logger.Infof("[%d] Rsp %s", eventId, string(jsonRsp))

	return constants.SUCCESS, "ok", rsp
}

func (d *DestroyResourceController) process(req *billing.DestroyResourceReq) (rsp *billing.DestroyResourceResp,
	err error) {

	defer errorcode.DefaultDeferHandlerWithMess(&err, "destroyResourceController")

	// 后付费生命周期欠费销毁
	isQN := req.ResourceId == ""

	var locker *dlocker.DLocker
	// 后付费生命周期欠费销毁, 按照地域
	if isQN {
		logger.Infof("## 用户appid %d 由于欠费，现在需要销毁隔离的所有资源", req.AppId)
		locker = dlocker.NewDlocker("destroyResource",
			fmt.Sprintf("%s-%d-%d", billing.PayModePost_Source_QN, req.AppId, req.Region), 3*60)
	} else {
		locker = dlocker.NewDlocker("destroyResource", req.ResourceId, 3*60)
	}

	err = locker.Lock()
	if err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "lock fail", err)
	}
	defer locker.UnLock()

	rsp = &billing.DestroyResourceResp{}
	s := billing2.NewDestroyBillingResourceService(req)
	if isQN {
		rsp.FlowId, err = s.DestroyAll()
	} else {
		rsp.FlowId, err = s.Destroy()
	}
	if err != nil {
		return
	}

	return rsp, nil
}
