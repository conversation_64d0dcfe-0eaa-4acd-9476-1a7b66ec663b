package billing

import (
	"encoding/json"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/billing"
	billingService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/billing"
)

/**
 *
 *
 * 后付费 删除集群
 *
 * https://tcb.woa.com/magical-brush/docs/823197437
 */
func init() {
	const controllerName = "qcloud.oceanus.modifyResourceStatus"
	if code, msg := httpserver.RegisterController(controllerName, &modifyResourceStatusController{}); code != 0 {
		logger.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type modifyResourceStatusController struct {
}

func (i *modifyResourceStatusController) CreateRequestObj() interface{} {
	return &billing.ModifyResourceStatusRequest{}
}

func (i *modifyResourceStatusController) Process(req interface{}, eventId int64) (retCode int64, errMsg string, response interface{}) {
	reqData := req.(*billing.ModifyResourceStatusRequest)

	jsonReq, _ := json.Marshal(reqData)
	logger.Infof("qcloud.oceanus.sendResource [%d] Request data %+v", eventId, string(jsonReq))

	s := billingService.NewModifyResourceStatusService(reqData, eventId)
	err := s.ModifyResourceStatus()
	if err == nil {
		return constants.SUCCESS, "ok", &struct {}{}
	} else {
		return constants.SYSERR, err.Error(), &struct {}{}
	}

}
