package billing

import (
	"fmt"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/billing"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/cluster"
	"testing"
)

func TestCheckCreate(t *testing.T) {
	eventId := int64(123123123123123123)
	c1 := &checkCreateController{}
	vpcs := make([]model.VpcDescription, 0)
	vpcs = append(vpcs, model.VpcDescription{
		VpcId:    "vpc-s0he2zcq",
		SubnetId: "subnet-41kr55hn",
		Zone:     "ap-guangzhou-1",
	})
	req := &billing.CheckCreateReq{
		Uin:        "100006386216",
		AppId:      1257058945,
		OperateUin: "100006386216",
		Type:       "",
		Region:     0,
		ZoneId:     0,
		PayMode:    0,
		ProjectId:  0,
		GoodsDetail: &billing.CreateGoodsDetail{
			TimeUnit:                              "",
			TimeSpan:                              0,
			GoodsNum:                              0,
			Pid:                                   0,
			AutoRenewFlag:                         0,
			ProductCode:                           "",
			SubProductCode:                        "",
			OceanusExclusiveComputeCu:             0,
			ProductInfo:                           nil,
			ClusterName:                           "",
			VpcDescriptions:                       vpcs,
			LoginPassword:                         "",
			Remark:                                "",
			Tags:                                  nil,
			DefaultCOSBucket:                      "",
			CLSLogSet:                             "",
			CLSTopicId:                            "",
			OrderOrigin:                           0,
			AcceptNonFineGrainedResource:          false,
			International:                         0,
			ComputeCu:                             0,
			ArchGeneration:                        0,
			ClusterGroupType:                      0,
			AgentSerialId:                         "",
			ParentSerialId:                        "",
			BillingResourceMode:                   "",
			Duration:                              "",
			OceanusExclusiveComputeUnderwriteCu14: 0,
			OceanusExclusiveComputeCu12:           0,
			OceanusExclusiveComputeCu18:           0,
			Extparam:                              billing.Extparam{},
			OceanusCloudSsd:                       0,
			OceanusCloudHssd:                      0,
			SlaveVpcDescriptions:                  vpcs,
			OceanusExclusiveComputeMultipleCu14:   12,
		},
	}
	r, r1, _ := c1.Process(req, eventId)
	fmt.Println(r, r1)
}

func TestCheckDisk(t *testing.T) {
	ss := CheckDisk(&billing.CheckCreateReq{
		Uin:        "100006386216",
		AppId:      1257058945,
		OperateUin: "100006386216",
		Type:       "",
		Region:     0,
		ZoneId:     0,
		PayMode:    0,
		ProjectId:  0,
		GoodsDetail: &billing.CreateGoodsDetail{
			TimeUnit:                              "",
			TimeSpan:                              0,
			GoodsNum:                              0,
			Pid:                                   0,
			AutoRenewFlag:                         0,
			ProductCode:                           "",
			SubProductCode:                        "",
			OceanusExclusiveComputeCu:             0,
			ProductInfo:                           nil,
			ClusterName:                           "",
			VpcDescriptions:                       nil,
			LoginPassword:                         "",
			Remark:                                "",
			Tags:                                  nil,
			DefaultCOSBucket:                      "",
			CLSLogSet:                             "",
			CLSTopicId:                            "",
			OrderOrigin:                           0,
			AcceptNonFineGrainedResource:          false,
			International:                         0,
			ComputeCu:                             0,
			ArchGeneration:                        0,
			ClusterGroupType:                      0,
			AgentSerialId:                         "",
			ParentSerialId:                        "",
			BillingResourceMode:                   "",
			Duration:                              "",
			OceanusExclusiveComputeUnderwriteCu14: 0,
			OceanusExclusiveComputeCu12:           0,
			OceanusExclusiveComputeCu18:           0,
			Extparam:                              billing.Extparam{},
			OceanusCloudPremium:                   32,
			OceanusCloudBssd:                      222,
			OceanusCloudSsd:                       0,
			OceanusCloudHssd:                      0,
			MasterInfo: &billing.SetatsCvm{
				Cpu: 8,
				Mem: 32,
				Disk: &billing.SetatsDisk{
					DiskType: "CLOUD_PREMIUM",
					DiskSize: 50,
				},
				DefaultParallelism: 0,
			},
			WorkerInfo: &billing.SetatsCvm{
				Cpu: 8,
				Mem: 32,
				Disk: &billing.SetatsDisk{
					DiskType: "CLOUD_PREMIUM",
					DiskSize: 60,
				},
				DefaultParallelism: 2,
			},
			ClusterGroupSerialId: "cluster-xxxx",
		},
	})
	fmt.Println(ss)
}
