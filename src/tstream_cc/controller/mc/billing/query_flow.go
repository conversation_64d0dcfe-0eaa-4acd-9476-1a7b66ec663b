package billing

import (
	"encoding/json"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/billing"
	billing3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/billing"
)

/**
 * 计费模块：异步发货
 *
 * 查询异步流程执行结果（发货状态检查）接口
 *
 * http://tapd.oa.com/pt_jifei/markdown_wikis/show/#1210140771000564909
 */
func init() {
	const controllerName = "qcloud.oceanus.queryFlow"
	if code, msg := httpserver.RegisterController(controllerName, &queryFlowController{}); code != 0 {
		logger.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type queryFlowController struct {
}

func (q *queryFlowController) CreateRequestObj() interface{} {
	return &billing.QueryFlowReq{}
}

func (q *queryFlowController) Process(req interface{}, eventId int64) (int64, string, interface{}) {
	reqData := req.(*billing.QueryFlowReq)

	jsonReq, _ := json.Marshal(reqData)
	logger.Infof("[%d] Request data %+v", eventId, string(jsonReq))

	// 1. 用于上述异步接口的执行结果查询，状态仅包含四种(成功/失败/进行中/发不出货直接退订单)，接口必须是同步的
	// 2. 重试机制：1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h 4h 8h 12h（会一直重试，没有最大次数，返回0,3,4不会重试）

	rsp, err := q.process(reqData)
	if err != nil {
		logger.Errorf("[%d] err: %s", eventId, err.Error())
		rsp = &billing.QueryFlowResp{Status: billing.FlowStatusProcessing}
	}

	jsonRsp, _ := json.Marshal(rsp)
	logger.Infof("[%d] Rsp %s", eventId, string(jsonRsp))
	return constants.SUCCESS, "ok", rsp
}

func (q *queryFlowController) process(req *billing.QueryFlowReq) (rsp *billing.QueryFlowResp, err error) {
	rsp = &billing.QueryFlowResp{}
	defer errorcode.DefaultDeferHandlerWithMess(&err, "queryFlowController")
	flowInfo, err := flow.GetFlowByUniqId(req.FlowId)
	if err != nil {
		err = errorcode.NewStackError(errorcode.InternalErrorCode_QueryFlowFailed, "", err)
		return
	}

	if flowInfo.Status != flow.ENDED {
		rsp.Status = billing.FlowStatusProcessing
		return
	}

	rsp.Resources, err = q.getBillingResourceInfo(flowInfo)
	rsp.Status = billing.FlowStatusSuccess
	return
}

func (q *queryFlowController) getBillingResourceInfo(flowInfo *flow.FlowInfo) (resources []*billing.Resources,
	err error) {
	resources = make([]*billing.Resources, 0)

	billingResource, err := billing3.NewBillingResourceService().GetResourceByFlowId(flowInfo.Id)
	if err != nil {
		return
	}

	for _, r := range billingResource {
		resource := &billing.Resources{
			ResourceId:           r.ResourceId,
			ExecutionSuccess:     billing.ExecutionSuccess,
			ExecutionStartTime:   util.TimestampToLocalTime(flowInfo.Starttime),
			ExecutionEndTime:     util.TimestampToLocalTime(flowInfo.Endtime),
			ResourceNewStartTime: r.CreateTime,
			ResourceNewEndTime:   r.ExpireTime,
		}
		resources = append(resources, resource)
	}

	return resources, nil
}
