package billing

import (
	"fmt"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/billing"
	billing2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/billing"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
)

/**
 * 计费模块：配置变更策略 feature-860782583
 *
 * 变配参数校验接口
 *
 * http://tapd.oa.com/pt_jifei/markdown_wikis/show/#1210140771000569585
 */
func init() {
	const controllerName = "qcloud.oceanus.checkModify"
	if code, msg := httpserver.RegisterController(controllerName, &checkModifyController{}); code != 0 {
		logger.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type checkModifyController struct {
}

func (c *checkModifyController) CreateRequestObj() interface{} {
	return &billing.CheckModifyReq{}
}

func (c *checkModifyController) Process(req interface{}, eventId int64) (retCode int64, errMsg string, response interface{}) {
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf("Failed to call checkModify because %+v", err)
			retCode = controller.SYSERR
			errMsg = fmt.Sprintf("Failed to call checkModify because %+v", err)
		}
	}()

	reqData := req.(*billing.CheckModifyReq)

	// 【注意事项】
	// 	1. 业务后台需要检查变更资源配置请求包的每个参数是否准确
	// 	2. 校验包括 a.资源ID是否属于该用户，b.资源当前到期时间是否与后台实际值一致 c.资源旧配置是否与后台实际值一致 d.资源新配置是否合法 e.新旧配置的pid是否与商品配置匹配等，否则可能会导致逻辑漏洞
	// 	3. 0 元订单事故案例（未校验询价参数,timeSpan,goodsNum） ：
	// http://tapd.oa.com/pt_jifei/markdown_wikis/?#1010140771011094213
	// 	4. 接口必须是同步的

	// 打印 Event ID 和请求信息
	logger.Infof("checkModify [%d] Request data %+v", eventId, reqData)

	// 初始化参数
	resp := &billing.CheckModifyResp{}
	resp.Status = billing.StatusSystemError

	billingResource, err := billingResourceService.GetResourceById(reqData.ResourceId)
	if err != nil {
		resp.Status = billing.StatusSystemError
		return constants.SUCCESS, "cannot get billingResource by resource id", resp
	}

	// setats
	if billingResource.Type == constants.BILLINGRESOURCE_TYPE_SETATS {
		_, err := service.GetSetatsBySerialId(reqData.ResourceId)
		if err != nil {
			resp.Status = billing.StatusSystemError
			return constants.SUCCESS, "cannot get setats by resource id", resp
		}
		// 检查成功
		resp.Status = billing.StatusSuccess
		return constants.SUCCESS, "ok", resp
	}

	// 获取资源信息
	clusterGroup, err := service.ListClusterGroupBySerialId(reqData.ResourceId)
	if err != nil {
		logger.Errorf("Failed to get cluster group by serial id %s because %+v", reqData.ResourceId, err)
		return constants.SUCCESS, "cannot get cluster group by resource id", resp
	}
	cluster, err := service.GetActiveClusterByClusterGroupId(clusterGroup.Id)
	if err != nil {
		logger.Errorf("Failed to get cluster by cluster group id %d because %+v", clusterGroup.Id, err)
		return constants.SUCCESS, "cannot get cluster by resource id", resp
	}
	// 多可用区部署
	if clusterGroup.DeploymentMode == constants.DeploymentModeMultiple {
		if reqData.GoodsDetail.NewConfig.OceanusExclusiveComputeMultipleCu14 < 1 {
			logger.Errorf("checkModify_OceanusExclusiveComputeMultipleCu14 invalid.")
			return constants.SUCCESS, "checkModify_OceanusExclusiveComputeMultipleCu14 invalid.", resp
		}
		if reqData.GoodsDetail.OldConfig.OceanusExclusiveComputeMultipleCu14 < 1 {
			logger.Errorf("checkModify_OceanusExclusiveComputeMultipleCu14 invalid.")
			return constants.SUCCESS, "checkModify_OceanusExclusiveComputeMultipleCu14 invalid.", resp
		}
		// 虽然计费项不同，会统一赋值到这里，方便走统一校验逻辑
		reqData.GoodsDetail.NewConfig.OceanusExclusiveComputeCu = reqData.GoodsDetail.NewConfig.OceanusExclusiveComputeMultipleCu14
		reqData.GoodsDetail.OldConfig.OceanusExclusiveComputeCu = reqData.GoodsDetail.OldConfig.OceanusExclusiveComputeMultipleCu14
	}
	// 1:2 1:8
	if cluster.MemRatio == constants.CVM_MEMRATIO_2 {
		if reqData.GoodsDetail.NewConfig.OceanusExclusiveComputeCu12 < 1 {
			logger.Errorf("checkModify_sv_oceanus_compute_exclusive_cu1c2g invalid.")
			return constants.SUCCESS, "checkModify_sv_oceanus_compute_exclusive_cu1c2g invalid.", resp
		}
		if reqData.GoodsDetail.OldConfig.OceanusExclusiveComputeCu12 < 1 {
			logger.Errorf("checkModify_sv_oceanus_compute_exclusive_cu1c2g invalid.")
			return constants.SUCCESS, "checkModify_sv_oceanus_compute_exclusive_cu1c2g invalid.", resp
		}
		// 虽然计费项不同，会统一赋值到这里，方便走统一校验逻辑
		reqData.GoodsDetail.NewConfig.OceanusExclusiveComputeCu = reqData.GoodsDetail.NewConfig.OceanusExclusiveComputeCu12
		reqData.GoodsDetail.OldConfig.OceanusExclusiveComputeCu = reqData.GoodsDetail.OldConfig.OceanusExclusiveComputeCu12
	}
	if cluster.MemRatio == constants.CVM_MEMRATIO_8 {
		if reqData.GoodsDetail.NewConfig.OceanusExclusiveComputeCu18 < 1 {
			logger.Errorf("checkModify_sv_oceanus_compute_exclusive_cu1c8g invalid.")
			return constants.SUCCESS, "checkModify_sv_oceanus_compute_exclusive_cu1c8g invalid.", resp
		}
		if reqData.GoodsDetail.OldConfig.OceanusExclusiveComputeCu18 < 1 {
			logger.Errorf("checkModify_sv_oceanus_compute_exclusive_cu1c8g invalid.")
			return constants.SUCCESS, "checkModify_sv_oceanus_compute_exclusive_cu1c8g invalid.", resp
		}
		// 虽然计费项不同，会统一赋值到这里，方便走统一校验逻辑
		reqData.GoodsDetail.NewConfig.OceanusExclusiveComputeCu = reqData.GoodsDetail.NewConfig.OceanusExclusiveComputeCu18
		reqData.GoodsDetail.OldConfig.OceanusExclusiveComputeCu = reqData.GoodsDetail.OldConfig.OceanusExclusiveComputeCu18
	}
	/**
	 * 最前面校验  OceanusExclusiveComputeCu 和 ComputeCu， 包年包月
	 * 逻辑需要兼容不传 ComputeCu 的 情况
	 * 需要同时校验 ===新配置 和 老配置===
	 */

	if billingResource.IsNeedManageNode == constants.NEED_MANAGE_NODE {
		newOceanusExclusiveComputeCu := reqData.GoodsDetail.NewConfig.OceanusExclusiveComputeCu
		newComputeCu := reqData.GoodsDetail.NewConfig.ComputeCu
		oldOceanusExclusiveComputeCu := reqData.GoodsDetail.OldConfig.OceanusExclusiveComputeCu
		oldComputeCu := reqData.GoodsDetail.OldConfig.ComputeCu
		/**
		 * 校验是不是新老都传递了 ComputeCu
		 */
		if (newComputeCu == 0 && oldComputeCu != 0) || (newComputeCu != 0 && oldComputeCu == 0) {
			errMsg := fmt.Sprintf("new ComputeCu(%d)'s rule not same as old ComputeCu(%d)",
				newComputeCu, oldComputeCu)
			logger.Errorf(errMsg)
			resp.Status = billing.StatusInvalidCuNum
			return controller.SUCCESS, errMsg, resp
		}

		checkManageCuRst, errMsg, status := billing2.CheckManageCu(newOceanusExclusiveComputeCu, newComputeCu)
		if checkManageCuRst != controller.SUCCESS {
			resp.Status = status
			// 校验失败， returnCode 也应该为0， 计费侧通过 resp data里面的status 判断是否可以购买
			return controller.SUCCESS, errMsg, resp
		}
		reqData.GoodsDetail.NewConfig.OceanusExclusiveComputeCu = newComputeCu

		checkManageCuRst, errMsg, status = billing2.CheckManageCu(oldOceanusExclusiveComputeCu, oldComputeCu)
		if checkManageCuRst != controller.SUCCESS {
			resp.Status = status
			// 校验失败， returnCode 也应该为0， 计费侧通过 resp data里面的status 判断是否可以购买
			return controller.SUCCESS, errMsg, resp
		}
		reqData.GoodsDetail.OldConfig.OceanusExclusiveComputeCu = oldComputeCu

		/**
		 * 处理升降配参数，因为 有些临界值，比如 48 -> 47  传给计费的是 48 / 49 是升配，但是对于后端， 48 /47 是降配。 所以 后段
		 * 需要根据计算CU重新设置
		 */
		if newComputeCu < oldComputeCu {
			reqData.GoodsDetail.NewConfig.ModifyMode = billing.ModifyModeScaleDown
		}
		if newComputeCu > oldComputeCu {
			reqData.GoodsDetail.NewConfig.ModifyMode = billing.ModifyModeScaleUp
		}
	}

	// ==== 基准检查（检查基本参数）
	returnCode, msg, resp := c.checkBasicArguments(reqData, resp)
	if returnCode != controller.SUCCESS {
		return controller.SUCCESS, msg, resp
	}

	// ==== 商品信息（GoodsDetail）检查 ====
	returnCode, msg, resp = c.checkGoodsDetail(reqData, resp, billingResource.Type == constants.BILLINGRESOURCE_TYPE_CLUSTER)
	if returnCode != controller.SUCCESS {
		return controller.SUCCESS, msg, resp
	}

	// 检查成功
	resp.Status = billing.StatusSuccess

	return constants.SUCCESS, "ok", resp
}

func (c *checkModifyController) checkBasicArguments(reqData *billing.CheckModifyReq, resp *billing.CheckModifyResp) (int64, string, *billing.CheckModifyResp) {

	// 做一些最基础参数的检查
	retCode, errMsg, status := billing2.CheckNecessaryArguments(
		int(reqData.Region),
		int(reqData.ZoneId),
		reqData.AppId,
		reqData.PayMode,
		reqData.OperateUin,
		reqData.Uin,
		0,
	)
	resp.Status = status
	if retCode != controller.SUCCESS {
		return retCode, errMsg, resp
	}

	// 然后做一些该接口独有的检查
	// 资源 ID 是否属于该用户（查询 ClusterGroup 表）
	clusterGroup, err := service.ListClusterGroupBySerialId(reqData.ResourceId)
	if err != nil {
		logger.Errorf("Failed to get cluster group by serial id because %+v", err)
		resp.Status = billing.StatusSystemError
		return constants.SYSERR, "Failed to get cluster group", resp
	}
	if clusterGroup.OwnerUin != reqData.Uin || clusterGroup.AppId != reqData.AppId {
		resp.Status = billing.StatusResourceNotBelongToThisUin
		return constants.PARAMSERR, "Resource not exist or not belong to this account", resp
	}

	// 检查变配传入的 CU 数和集群当前 CU 数是否一致（旧配置是否和数据库一致）
	if clusterGroup.CuNum != int16(reqData.GoodsDetail.OldConfig.OceanusExclusiveComputeCu) {
		resp.Status = billing.StatusInvalidCuNum
		return constants.PARAMSERR, "CU number for modification is not consistent with existing one", resp
	}

	// 检查 ClusterGroup 是否可以变配
	clusterGroupService, err := service.NewClusterGroupService(clusterGroup.Id)
	if err != nil {
		logger.Errorf("Failed to call NewClusterGroupService because %+v", err)
		resp.Status = billing.StatusSystemError
		return constants.SYSERR, "Cannot get ClusterGroup service", resp
	}
	canScale, err := clusterGroupService.CanScale()
	if err != nil {
		logger.Errorf("Failed to call clusterGroupService#CanScale because %+v", err)
		resp.Status = billing.StatusSystemError
		return constants.SYSERR, "Cannot detect whether ClusterGroup can scale or not", resp
	}
	if !canScale {
		resp.Status = billing.StatusClusterCannotBeScaled
		return controller.PARAMSERR, "Cluster cannot be scaled", resp
	}

	return constants.SUCCESS, "", resp
}

func (c *checkModifyController) checkGoodsDetail(reqData *billing.CheckModifyReq, resp *billing.CheckModifyResp, isCluster bool) (int64, string, *billing.CheckModifyResp) {
	// 做一些 Old GoodsDetail 的公共检查
	retCode, errMsg, status := billing2.CheckNecessaryGoodsDetail(
		reqData.AppId,
		reqData.GoodsDetail.OldConfig.TimeUnit,
		1, // 变配不需要传此参数, 这里只是占位符
		reqData.GoodsDetail.GoodsNum,
		reqData.GoodsDetail.OldConfig.Pid,
		0, // 变配不需要传此参数, 这里只是占位符
		reqData.GoodsDetail.OldConfig.ProductCode,
		reqData.GoodsDetail.OldConfig.SubProductCode,
		billing.MinimalAllowedComputeUnit, // 占位符, 旧配置不需要检查 CU 合法性, 避免历史集群无法升级
		reqData.PayMode,
		isCluster,
	)
	resp.Status = status
	if retCode != controller.SUCCESS {
		return retCode, errMsg, resp
	}

	// 做一些 New GoodsDetail 的公共检查
	retCode, errMsg, status = billing2.CheckNecessaryGoodsDetail(
		reqData.AppId,
		reqData.GoodsDetail.NewConfig.TimeUnit,
		1, // 变配不需要传此参数, 这里只是占位符
		reqData.GoodsDetail.GoodsNum,
		reqData.GoodsDetail.NewConfig.Pid,
		0, // 变配不需要传此参数, 这里只是占位符
		reqData.GoodsDetail.NewConfig.ProductCode,
		reqData.GoodsDetail.NewConfig.SubProductCode,
		reqData.GoodsDetail.NewConfig.OceanusExclusiveComputeCu,
		reqData.PayMode,
		isCluster,
	)
	resp.Status = status
	if retCode != controller.SUCCESS {
		return retCode, errMsg, resp
	}

	if isCluster {
		if reqData.GoodsDetail.NewConfig.ModifyMode == billing.ModifyModeScaleDown {
			// 将配
			if reqData.GoodsDetail.OldConfig.OceanusExclusiveComputeCu <= reqData.GoodsDetail.NewConfig.
				OceanusExclusiveComputeCu {
				resp.Status = billing.StatusInvalidCuNum
				return controller.PARAMSERR, "OceanusExclusiveComputeCu must be smaller than current in modify mode down",
					resp
			}
		} else {
			// 升配
			if reqData.GoodsDetail.OldConfig.OceanusExclusiveComputeCu >= reqData.GoodsDetail.NewConfig.
				OceanusExclusiveComputeCu {
				resp.Status = billing.StatusInvalidCuNum
				return controller.PARAMSERR, "OceanusExclusiveComputeCu must be greater than current in modify mode up",
					resp
			}
		}
	}

	// 检查 CurDeadline
	billingResource, err := billingResourceService.GetResourceById(reqData.ResourceId)
	if err != nil {
		resp.Status = billing.StatusSystemError
		return constants.SYSERR, "cannot get cluster by resource id", resp
	}
	if billingResource.ExpireTime != reqData.GoodsDetail.CurDeadline {
		resp.Status = billing.StatusNotConsistentExpireTime
		return constants.PARAMSERR, "current deadline not consistent with the database", resp
	}

	return constants.SUCCESS, "", resp
}
