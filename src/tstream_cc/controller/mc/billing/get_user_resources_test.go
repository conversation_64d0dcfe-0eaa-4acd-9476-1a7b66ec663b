package billing

import (
	"encoding/json"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/billing"
	"testing"
)

func TestQueryPrePayAllResourceController_Process(t *testing.T) {
	c := &getUserResourcesController{}

	req := &billing.QueryPayAllResourceReq{
		AppId:    *fTestAppId,
		Uin:      "",
		Type:     *fTestSubProductCode,
		PayMode:  fPayMode,
		Region:   *fTestRegion,
		PageNo:   *fPageNo,
		PageSize: *fPageSize,
	}

	code, msg, rsp := c.Process(req, 1)
	t.Log(code)
	t.Log(msg)

	b, _ := json.MarshalIndent(rsp, "", "\t")
	t.Log(string(b))
}
