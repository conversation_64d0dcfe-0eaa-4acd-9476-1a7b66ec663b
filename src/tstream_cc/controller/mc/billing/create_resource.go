package billing

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/dlocker"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service2 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/billing"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/cluster"
	setats2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/setats"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/k8s"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/login_settings"
	billing3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/billing"
	billing2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/billing"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/region"
	"time"
)

/**
 * 计费模块：新购策略
 *
 * 创建资源（发货）接口
 *
 * http://tapd.oa.com/pt_jifei/markdown_wikis/0#1010140771011373913
 * https://tcb.woa.com/magical-brush/docs/754666717
 */
func init() {
	const controllerName = "qcloud.oceanus.createResource"
	if code, msg := httpserver.RegisterController(controllerName, &CreateResourceController{}); code != 0 {
		logger.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type CreateResourceController struct {
}

func (c *CreateResourceController) CreateRequestObj() interface{} {
	return &billing.CreateResourceReq{}
}

func (c *CreateResourceController) Process(req interface{}, eventId int64) (retCode int64, errMsg string, response interface{}) {
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf("Failed to call createResource because %+v", err)
			retCode = controller.SYSERR
			errMsg = "Fatal system error"
		}
	}()

	reqData := req.(*billing.CreateResourceReq)

	// 打印 Event ID 和请求信息
	logger.Infof("createResource [%d] Request data %+v", eventId, reqData)

	resp := billing.CreateResourceResp{}

	// 校验包销参数
	if reqData.GoodsDetail.BillingResourceMode != "" {
		if reqData.GoodsDetail.BillingResourceMode != billing.ExclusiveSale {
			return controller.SYSERR, fmt.Sprintf("billingResourceMode must be exclusiveSale"), resp
		}
		if reqData.GoodsDetail.OceanusExclusiveComputeUnderwriteCu14 < 1 {
			return controller.SYSERR, fmt.Sprintf("exclusiveSale mode sv_oceanus_compute_underwrite_cu1c4g invalid. "), resp
		}
		found := false
		for _, duration := range billing.Durations {
			if duration == reqData.GoodsDetail.Duration {
				logger.Infof("find Duration %s", duration)
				found = true
				break
			}
		}
		if !found {
			logger.Errorf("cannot find Duration %s", reqData.GoodsDetail.Duration)
			return controller.SYSERR, fmt.Sprintf("duration must be 1y or 2y or 3y or 4y or 5y"), resp
		}

		allow := auth.IsInWhiteList(reqData.AppId, constants.WHITE_LIST_EXCLUSIVE_COMPUTEUNDERWRITECU14)
		if !allow {
			return controller.SYSERR, fmt.Sprintf("no permission to use exclusive mode"), resp
		}
		// 虽然计费项不同，会统一赋值到这里，方便走统一校验逻辑
		reqData.GoodsDetail.OceanusExclusiveComputeCu = reqData.GoodsDetail.OceanusExclusiveComputeUnderwriteCu14
	}

	cores := GetWhiteListCores(int32(reqData.AppId))
	// 1:2 1:8 校验
	if reqData.GoodsDetail.OceanusExclusiveComputeCu12 != 0 {
		allow := auth.IsInWhiteList(reqData.AppId, constants.WHITE_LIST_MEMRATIO_2)
		if !allow {
			return controller.SYSERR, fmt.Sprintf("no permission to use 1:2 cu"), resp
		}
		if cores != constants.CVM_DEFAULT_CORES {
			return controller.SYSERR, fmt.Sprintf("1:2 cu must use standard cores"), resp
		}
		// 虽然计费项不同，会统一赋值到这里，方便走统一校验逻辑
		reqData.GoodsDetail.OceanusExclusiveComputeCu = reqData.GoodsDetail.OceanusExclusiveComputeCu12
	}
	if reqData.GoodsDetail.OceanusExclusiveComputeCu18 != 0 {
		allow := auth.IsInWhiteList(reqData.AppId, constants.WHITE_LIST_MEMRATIO_8)
		if !allow {
			return controller.SYSERR, fmt.Sprintf("no permission to use 1:8 cu"), resp
		}
		if cores != constants.CVM_DEFAULT_CORES {
			return controller.SYSERR, fmt.Sprintf("1:8 cu must use standard cores"), resp
		}
		// 虽然计费项不同，会统一赋值到这里，方便走统一校验逻辑
		reqData.GoodsDetail.OceanusExclusiveComputeCu = reqData.GoodsDetail.OceanusExclusiveComputeCu18
	}

	/**
	 * 最前面校验  OceanusExclusiveComputeCu 和 ComputeCu， 包年包月
	 * 逻辑需要兼容不传 ComputeCu 的 情况
	 */
	oceanusExclusiveComputeCu := reqData.GoodsDetail.OceanusExclusiveComputeCu
	computeCu := reqData.GoodsDetail.ComputeCu
	if reqData.PayMode == billing.PayModePrepaid && computeCu != 0 {
		checkManageCuRst, errMsg, _ := billing2.CheckManageCu(oceanusExclusiveComputeCu, computeCu)
		if checkManageCuRst != controller.SUCCESS {
			return controller.SYSERR, errMsg, resp
		}
		reqData.GoodsDetail.OceanusExclusiveComputeCu = computeCu
	}

	// Trivial case: 首先查询这个 DealName 的 Pending 状态的 FlowId, 如果有记录且存在，则直接返回 OK
	errMsg, resp = c.getPendingBillingOrder(reqData, resp)
	if errMsg != "" { // 查到了发货中的记录
		return controller.SUCCESS, errMsg, resp
	}

	// 如果记录不存在, 尝试加锁
	locker := dlocker.NewDlocker("oceanus-billing-createResource", reqData.DealName, 8000)
	err := locker.Lock()
	if err != nil { // 加锁失败, 且没有 flowId, 就返回 flowId = 0 让计费重试
		logger.Infof("Another createResource process for %s has lock but not finished yet", reqData.DealName)
		return controller.SUCCESS, "Another createResource instance is still going, not ready yet", resp
	}
	defer locker.UnLock()

	// 加锁后再次检查（Double-Checked Locking 模式), 防止之前查到的是旧数据
	errMsg, resp = c.getPendingBillingOrder(reqData, resp)
	if errMsg != "" { // 查到了发货中的记录
		return controller.SUCCESS, errMsg, resp
	}

	// 初始化 Order 记录
	newBillingOrder := c.createNewBillingOrder(reqData)
	txManager := service2.GetTxManager()

	// 转换地域信息
	regionName, err := region.GetRegionNameById(reqData.Region)
	if err != nil {
		logger.Errorf("Unsupported region %d %+v", reqData.Region, err)
		return controller.SYSERR, "Unsupported region", resp
	}
	zone, err := region.GetZoneById(reqData.ZoneId)
	if err != nil {
		logger.Errorf("Unsupported zone id %d %+v", reqData.ZoneId, err)
		return controller.SYSERR, "Unsupported zone", resp
	}

	var orderId int64
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		orderId = tx.SaveObject(newBillingOrder, "BillingOrder")
		//共享集群必须支持细腻度资源
		reqData.GoodsDetail.AcceptNonFineGrainedResource = false

		isSetats := IsSetats(reqData)
		logger.Infof("isSetats %v", isSetats)
		clusterGroupId := int64(0)
		clusterId := int64(0)
		_flowId := int64(0)
		resourceId := ""
		if !isSetats {
			// 创建集群 Flow
			createClusterReq := c.generateCreateClusterRequest(reqData, eventId, regionName, zone)

			if ok, w := auth.WhiteListValue(reqData.AppId, constants.WHITE_LIST_CREATE_IN_AGENT); ok && w.GetStrVal() != "" &&
				createClusterReq.AgentSerialId == "" &&
				createClusterReq.ClusterGroupType == constants.CLUSTER_GROUP_TYPE_PRIVATE &&
				createClusterReq.ClusterType == constants.K8S_CLUSTER_TYPE_TKE {
				createClusterReq.AgentSerialId = w.GetStrVal()
			}

			code, msg, clusterGroup, cluster, flowId := service.DoCreateClusterInternal(createClusterReq)
			resp.FlowId = int(flowId) // FlowId 只是发货期间临时查询, 位数截断不会造成风险. flowId 为 0 则计费会重试
			_flowId = flowId
			if code == controller.OK && clusterGroup != nil && cluster != nil {
				resp.Resources = []*billing.Resources{{ResourceId: clusterGroup.SerialId}}
				resp.ResourceIds = []string{clusterGroup.SerialId}
			} else {
				logger.Errorf("Failed to create cluster because %s with resp %+v", msg, resp)
				retCode, errMsg, response = controller.SYSERR, fmt.Sprintf("Failed to create cluster (%s)", msg), resp
				return nil
			}
			// 创建sub eks 集群，更新 包年包月为扩容中
			if reqData.GoodsDetail.ParentSerialId != "" {
				tx.ExecuteSqlWithArgs("update ClusterGroup set Status=? where SerialId=?", constants.CLUSTER_GROUP_STATUS_SCALE_PROGRESS, reqData.GoodsDetail.ParentSerialId)
			}
			clusterId = cluster.Id
			resourceId = clusterGroup.SerialId
			clusterGroupId = clusterGroup.Id
		} else {
			createSetatsReq := c.generateCreateSetatsRequest(reqData, eventId, regionName, zone)
			createSetatsRsp, err := service.CreateSetats(createSetatsReq)
			if err != nil {
				logger.Errorf("Failed to create setats because %+v", err)
				retCode, errMsg, response = controller.SYSERR, fmt.Sprintf("Failed to create setats, please submit a ticket"), resp
				return nil
			}
			clusterId = createSetatsRsp.ClusterId
			clusterGroupId = createSetatsRsp.ClusterGroupId
			_flowId = createSetatsRsp.FlowId
			resp.FlowId = int(_flowId)
			resourceId = createSetatsRsp.SetatsSerialId
		}
		logger.Infof("create resource resourceId:  %s", resourceId)

		newBillingOrder.ClusterId = clusterId
		newBillingOrder.ClusterGroupId = clusterGroupId
		newBillingOrder.ResourceId = resourceId
		newBillingOrder.Status = billing.OrderStatusPending
		newBillingOrder.FlowId = _flowId

		// 更新 Order 表 (status = 2 & flowId 以及下面字段)
		tx.UpdateObject(newBillingOrder, orderId, "BillingOrder")

		// 保存信息到 BillingResource 表
		resource := c.createNewBillingResource(reqData, resourceId, _flowId, isSetats)
		if reqData.GoodsDetail.ClusterGroupType == constants.CLUSTER_GROUP_TYPE_UNIFORM && reqData.GoodsDetail.AgentSerialId != "" {
			//统一资源池母集群开新区,不需要再次记录BillingResource
		} else {
			tx.SaveObject(resource, "BillingResource")
		}

		logger.Infof("Successfully created resourceId %s with cluster group id %d, cluster id %d. Flow id %d",
			resourceId, clusterGroupId, clusterId, _flowId)

		retCode, errMsg, response = constants.SUCCESS, "ok", resp
		return nil
	}).Close()

	return
}

func IsSetats(reqData *billing.CreateResourceReq) bool {
	isSetats := false
	// setats
	if reqData.GoodsDetail.OceanusCloudBssd > 0 ||
		reqData.GoodsDetail.OceanusCloudPremium > 0 ||
		reqData.GoodsDetail.OceanusCloudHssd > 0 ||
		reqData.GoodsDetail.OceanusCloudSsd > 0 {
		isSetats = true
	}
	return isSetats
}

// 查询状态为 Pending（发货中）的 BillingOrder 对象, 找不到则返回空字符串
func (c *CreateResourceController) getPendingBillingOrder(reqData *billing.CreateResourceReq, resp billing.CreateResourceResp) (string, billing.CreateResourceResp) {
	billingOrder, err := billing2.GetBillingOrderByDealName(reqData.DealName)
	if err != nil {
		logger.Errorf("Failed to GetBillingOrderByDealName because %+v", err)
		panic(err)
	}

	if billingOrder != nil { // 如果 flowId 不为 0, 会重试调用 queryFlow 接口
		logger.Infof("On-going billing order for %s (%s) exists: %+v", reqData.DealName, billingOrder.ResourceId, billingOrder)

		resp.FlowId = int(billingOrder.FlowId)
		resp.Resources = []*billing.Resources{{ResourceId: billingOrder.ResourceId}}
		resp.ResourceIds = []string{billingOrder.ResourceId}

		return "Cluster creation in progress", resp
	}
	return "", resp
}

func (o *CreateResourceController) getClusterType(payMode int) int8 {
	// 包年包月
	if payMode == billing.PayModePrepaid {
		return 0
	} else if payMode == billing.PayModePost { // 按量付费
		return 1
	}
	return 0
}

func (o *CreateResourceController) generateSupportedFeatures(goodsDetail *billing.CreateGoodsDetail, clusterType int8) []string {

	group := constants.ConfRainbowGroupCommon

	features := make([]string, 0)
	if err := config.DecodeK8sObjectFromRainbowConfig(
		group, constants.ConfRainbowKeyOceanusFullFeatures, &features); err != nil {
		panic(errorcode.InternalErrorCode.NewWithErr(err))
	}
	ret := make([]string, 0, len(features))
	for _, f := range features {
		// disable find-grained-resource feature if non-fine grained resource cluster
		if f == constants.FeatureFineGrainedResource && goodsDetail.AcceptNonFineGrainedResource {
			continue
		}
		if f == constants.FeatureLog2Cos && clusterType == constants.K8S_CLUSTER_TYPE_EKS {
			// EKS 集群暂时不支持这个特性
			continue
		}
		ret = append(ret, f)
	}

	return ret
}

func getCuMum(reqData *billing.CreateResourceReq) int {
	// 包销模式，1/2 1/8 也使用这个，虽然计费项不同，会统一赋值到这里，方便走统一校验逻辑
	cuNum := reqData.GoodsDetail.OceanusExclusiveComputeCu
	// 后付费 计算cu默认为0
	if reqData.PayMode == billing.PayModePost {
		cuNum = 0
	}
	if IsSetats(reqData) {
		cuNum = 0
	}
	// 包年包月子eks集群使用 使用ComputeCu
	if reqData.GoodsDetail.ParentSerialId != "" {
		cuNum = reqData.GoodsDetail.ComputeCu
	}
	return cuNum
}

func GetWhiteListCores(appId int32) (cores int8) {
	cores = constants.CVM_DEFAULT_CORES
	inWhite := auth.IsInWhiteList(int64(appId), constants.WHITE_LIST_CORES_16)
	if inWhite {
		cores = 16
	}
	inWhite = auth.IsInWhiteList(int64(appId), constants.WHITE_LIST_CORES_32)
	if inWhite {
		cores = 32
	}
	inWhite = auth.IsInWhiteList(int64(appId), constants.WHITE_LIST_CORES_64)
	if inWhite {
		cores = 64
	}
	logger.Debugf("appId: %d, cores: %d", appId, cores)
	return cores
}

func (c *CreateResourceController) generateCreateSetatsRequest(reqData *billing.CreateResourceReq, eventId int64, regionName string, zone string) *setats2.CreateSetatsReq {

	return &setats2.CreateSetatsReq{
		RequestBase: apiv3.RequestBase{
			Region:        regionName,
			Version:       "",
			RequestId:     service2.Itoa(eventId) + " " + reqData.DealName,
			AppId:         reqData.AppId,
			Uin:           reqData.Uin,
			SubAccountUin: reqData.OperateUin,
			IsSupOwner:    1,
		},
		ClusterGroupSerialId:     reqData.GoodsDetail.ClusterGroupSerialId,
		MasterCpu:                reqData.GoodsDetail.MasterInfo.Cpu,
		MasterMem:                reqData.GoodsDetail.MasterInfo.Mem,
		MasterDiskType:           reqData.GoodsDetail.MasterInfo.Disk.DiskType,
		MasterDiskSize:           reqData.GoodsDetail.MasterInfo.Disk.DiskSize,
		WorkerCpu:                reqData.GoodsDetail.WorkerInfo.Cpu,
		WorkerMem:                reqData.GoodsDetail.WorkerInfo.Mem,
		WorkerDiskType:           reqData.GoodsDetail.WorkerInfo.Disk.DiskType,
		WorkerDiskSize:           reqData.GoodsDetail.WorkerInfo.Disk.DiskSize,
		WorkerDefaultParallelism: reqData.GoodsDetail.WorkerInfo.DefaultParallelism,
		Region:                   regionName,
		Zone:                     zone,
		Status:                   constants.SETATS_INIT_PROGRESS,
		Tags:                     reqData.GoodsDetail.Tags,
	}
}

// 为内部的 CreateCluster 接口生成请求
func (c *CreateResourceController) generateCreateClusterRequest(reqData *billing.CreateResourceReq, eventId int64, regionName string, zone string) *model.CreateClusterReq {
	clusterType := c.getClusterType(reqData.PayMode)

	cuNum := getCuMum(reqData)

	memRatio := int8(constants.CVM_DEFAULT_MEMRATIO)
	if reqData.GoodsDetail.OceanusExclusiveComputeCu12 != 0 {
		memRatio = constants.CVM_MEMRATIO_2
	}
	if reqData.GoodsDetail.OceanusExclusiveComputeCu18 != 0 {
		memRatio = constants.CVM_MEMRATIO_8
	}

	cores := GetWhiteListCores(int32(reqData.AppId))

	return &model.CreateClusterReq{
		AppId:              reqData.AppId,
		Uin:                reqData.Uin,
		SubAccountUin:      reqData.OperateUin,
		RequestId:          service2.Itoa(eventId) + " " + reqData.DealName,
		Region:             regionName,
		Version:            "", // 当前未用到该字段
		Name:               reqData.GoodsDetail.ClusterName,
		Zone:               zone,
		CuNum:              int16(cuNum),
		CuMem:              constants.CU_MEM_4GB, // 创建集群时默认为 1 核 4G 规格
		Remark:             reqData.GoodsDetail.Remark,
		DefaultCOSBucket:   reqData.GoodsDetail.DefaultCOSBucket,
		VPCDescriptions:    reqData.GoodsDetail.VpcDescriptions,
		NetEnvironmentType: constants.NETWORK_ENV_CLOUD_VPC, // 只允许 VPC 资源
		LoginSettings:      &login_settings.LoginSettings{Password: c.getFlinkWebUIPasswordOrDefault(reqData)},
		Tags:               reqData.GoodsDetail.Tags,
		CLSLogSet:          reqData.GoodsDetail.CLSLogSet,
		CLSTopicId:         reqData.GoodsDetail.CLSTopicId,
		OrderOrigin:        reqData.GoodsDetail.OrderOrigin,
		ClusterType:        clusterType,
		SupportedFeatures:  c.generateSupportedFeatures(reqData.GoodsDetail, clusterType),
		ArchGeneration:     reqData.GoodsDetail.ArchGeneration,
		ClusterGroupType:   reqData.GoodsDetail.ClusterGroupType,
		AgentSerialId:      reqData.GoodsDetail.AgentSerialId,
		ParentSerialId:     reqData.GoodsDetail.ParentSerialId,
		UniformOwnerUin:    reqData.GoodsDetail.UniformOwnerUin,
		MemRatio:           memRatio,
		Cores:              cores,
	}
}

// Flink UI 默认密码如果未设置, 则提供初始值
func (c *CreateResourceController) getFlinkWebUIPasswordOrDefault(reqData *billing.CreateResourceReq) string {
	loginPassword := reqData.GoodsDetail.LoginPassword
	if loginPassword == "" {
		loginPassword = base64.StdEncoding.EncodeToString([]byte(k8s.K8S_FLINK_WEBUI_PREFIX_PASSWD))
	}
	return loginPassword
}

// 创建订单记录

func (c *CreateResourceController) createNewBillingOrder(reqData *billing.CreateResourceReq) billing3.BillingOrder {
	cuNum := getCuMum(reqData)

	return billing3.BillingOrder{
		AppId:          reqData.AppId,
		DealName:       reqData.DealName,
		Uin:            reqData.Uin,
		OperateUin:     reqData.OperateUin,
		Region:         reqData.Region,
		ZoneId:         reqData.ZoneId,
		PayMode:        reqData.PayMode,
		TimeUnit:       reqData.GoodsDetail.TimeUnit,
		TimeSpan:       reqData.GoodsDetail.TimeSpan,
		AutoRenewFlag:  reqData.GoodsDetail.AutoRenewFlag,
		SubProductCode: reqData.GoodsDetail.SubProductCode,
		ComputeCu:      cuNum,
		Status:         billing.OrderStatusInitialized,
		CategoryId:     billing.OceanusExclusiveCategoryIdCreate,
		CreateTime:     util.GetCurrentTime(),
		UpdateTime:     util.GetCurrentTime(),
	}
}

// 保存资源记录
func (c *CreateResourceController) createNewBillingResource(
	reqData *billing.CreateResourceReq,
	resourceId string,
	flowId int64,
	isSetas bool,
) interface{} {

	exclusiveSaleStartTime := billing.NotIsolatedTimestamp
	if reqData.GoodsDetail.BillingResourceMode == billing.ExclusiveSale {
		exclusiveSaleStartTime = time.Now().Format(billing.StandardTimestampFormatString)
		//包销强制自动续费
		reqData.GoodsDetail.AutoRenewFlag = 1
	}

	goodsDetail, _ := json.Marshal(reqData.GoodsDetail)

	expireTime := billing.NotIsolatedTimestamp
	isNeedManageNode := constants.NOT_NEED_MANAGE_NODE
	if reqData.PayMode == billing.PayModePrepaid {
		expireTime = billing2.
			CalculateExpireTime(time.Now(), reqData.GoodsDetail.TimeUnit, reqData.GoodsDetail.TimeSpan).
			Format(billing.StandardTimestampFormatString)

		/**
		 * 包年包月，而且是 传递了计算CU的新规则集群，才设置
		 */
		if reqData.GoodsDetail.ComputeCu != 0 {
			isNeedManageNode = constants.NEED_MANAGE_NODE
		}
	}
	cuNum := getCuMum(reqData)

	resourceType := billing.ClusterResourceType
	if isSetas {
		resourceType = billing.SetatsResourceType
	}

	return billing3.BillingResource{
		ResourceId:             resourceId,
		AppId:                  reqData.AppId,
		Uin:                    reqData.Uin,
		OperateUin:             reqData.OperateUin,
		Region:                 reqData.Region,
		ZoneId:                 reqData.ZoneId,
		PayMode:                reqData.PayMode,
		ComputeCu:              cuNum,
		SubProductCode:         reqData.GoodsDetail.SubProductCode,
		TimeUnit:               reqData.GoodsDetail.TimeUnit,
		TimeSpan:               reqData.GoodsDetail.TimeSpan,
		AutoRenewFlag:          reqData.GoodsDetail.AutoRenewFlag,
		International:          reqData.GoodsDetail.International,
		Status:                 billing.ResourceStatusNormal,
		IsolatedTimestamp:      billing.NotIsolatedTimestamp,
		CreateTime:             time.Now().Format(billing.StandardTimestampFormatString),
		ExpireTime:             expireTime,
		GoodsDetail:            string(goodsDetail),
		FlowId:                 flowId,
		IsNeedManageNode:       isNeedManageNode,
		BillingResourceMode:    reqData.GoodsDetail.BillingResourceMode,
		Duration:               reqData.GoodsDetail.Duration,
		ExclusiveSaleStartTime: exclusiveSaleStartTime,
		Type:                   resourceType,
	}
}
