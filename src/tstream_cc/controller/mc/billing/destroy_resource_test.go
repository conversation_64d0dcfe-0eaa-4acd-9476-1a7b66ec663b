package billing

import (
	"encoding/json"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/billing"
	"testing"
)

func TestDestroyResourceController_Process(t *testing.T) {
	c := &DestroyResourceController{}

	req := &billing.DestroyResourceReq{
		AppId:      *fTestAppId,
		Uin:        "",
		OperateUin: "",
		Region:     *fTestRegion,
		ResourceId: *fTestResourceId,
	}

	code, msg, rsp := c.Process(req, 1)
	t.Log(code)
	t.Log(msg)

	b, _ := json.MarshalIndent(rsp, "", "\t")
	t.Log(string(b))
}
