package billing

import (
	"encoding/json"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/billing"
	billing2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/billing"
)

/**
 * 计费模块：生命周期策略
 *
 * 获取某用户所有资源接口（也称为 getUserResources）
 *
 * https://tcb.woa.com/magical-brush/docs/754665938
 */
func init() {
	const controllerName = "qcloud.oceanus.getUserResources"
	if code, msg := httpserver.RegisterController(controllerName, &getUserResourcesController{}); code != 0 {
		logger.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type getUserResourcesController struct {
}

func (q *getUserResourcesController) CreateRequestObj() interface{} {
	return &billing.QueryPayAllResourceReq{}
}

func (q *getUserResourcesController) Process(req interface{}, eventId int64) (int64, string, interface{}) {
	reqData := req.(*billing.QueryPayAllResourceReq)

	jsonReq, _ := json.Marshal(reqData)
	logger.Infof("[%d] Request data %+v", eventId, string(jsonReq))

	defaultPayMode := billing.PayModePrepaid
	if reqData.PayMode == nil {
		reqData.PayMode = &defaultPayMode
	}
	defaultSubProductCode := billing.OceanusExclusiveSubProductCode
	if reqData.SubProductCode == nil {
		reqData.SubProductCode = &defaultSubProductCode
	}

	rsp, err := q.process(reqData)
	if err != nil {
		logger.Errorf("[%d] err:%s", eventId, err.Error())
		code := errorcode.GetCode(err)
		return constants.SYSERR, code.GetCodeDesc(), rsp
	}

	jsonRsp, _ := json.Marshal(rsp)
	logger.Infof("[%d] Rsp %s", eventId, string(jsonRsp))

	// 1. 需要支持按地域，计费模式查询
	// 2. 若多个业务使用同一接口，则需区分各业务的type（子产品标签）
	// 3. 需支持分页
	// 4. 接口必须是同步的
	// 5. 需返回隔离中的资源，已销毁资源无需返回
	// 6. 原来预付费的查询所有资源接口，现在需要支持查询后付费资源

	return constants.SUCCESS, "ok", rsp
}

func (q *getUserResourcesController) process(req *billing.QueryPayAllResourceReq) (rsp *billing.
QueryPayAllResourceResp, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, "getUserResourcesController")
	rsp = &billing.QueryPayAllResourceResp{}
	s := billing2.NewBillingResourceService()
	rsp.Total, err = s.GetUserResourcesTotalCount(*req.SubProductCode, req.Region, req.AppId, *req.PayMode)
	if err != nil {
		return
	}
	resourceSet, err := s.GetUserResources(*req.SubProductCode, req.Region, req.AppId, *req.PayMode, &req.PageNo, &req.PageSize)
	rsp.Resources, err = s.ToResourceDetailSet(resourceSet)
	return rsp, err
}
