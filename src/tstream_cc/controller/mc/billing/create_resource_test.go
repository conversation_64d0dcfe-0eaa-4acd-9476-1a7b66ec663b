package billing

import (
	"fmt"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/billing"
	"testing"
)

func TestCreateResource(t *testing.T) {
	eventId := int64(123123123123123123)
	c1 := &CreateResourceController{}
	r, r1, _ := c1.Process(&billing.CreateResourceReq{
		AppId:      1257058945,
		TranId:     "t111",
		DealName:   "d1111",
		BigDealId:  "b1111",
		Uin:        "100006386216",
		OperateUin: "100006386216",
		Region:     4,
		ZoneId:     4,
		PayMode:    billing.PayModePrepaid,
		ProjectId:  0,
		GoodsDetail: &billing.CreateGoodsDetail{
			TimeUnit:                              "m",
			TimeSpan:                              1,
			GoodsNum:                              1,
			Pid:                                   1000510,
			AutoRenewFlag:                         1,
			ProductCode:                           "p_oceanus",
			SubProductCode:                        "sp_oceanus_exclusive",
			OceanusExclusiveComputeCu:             0,
			ProductInfo:                           nil,
			ClusterName:                           "",
			VpcDescriptions:                       nil,
			LoginPassword:                         "",
			Remark:                                "",
			Tags:                                  nil,
			DefaultCOSBucket:                      "",
			CLSLogSet:                             "",
			CLSTopicId:                            "",
			OrderOrigin:                           0,
			AcceptNonFineGrainedResource:          false,
			International:                         0,
			ComputeCu:                             0,
			ArchGeneration:                        0,
			ClusterGroupType:                      0,
			AgentSerialId:                         "",
			ParentSerialId:                        "",
			BillingResourceMode:                   "",
			Duration:                              "",
			OceanusExclusiveComputeUnderwriteCu14: 0,
			OceanusExclusiveComputeCu12:           0,
			OceanusExclusiveComputeCu18:           0,
			Extparam:                              billing.Extparam{},
			OceanusCloudPremium:                   32,
			OceanusCloudBssd:                      222,
			OceanusCloudSsd:                       0,
			OceanusCloudHssd:                      0,
			MasterInfo: &billing.SetatsCvm{
				Cpu: 8,
				Mem: 32,
				Disk: &billing.SetatsDisk{
					DiskType: "CLOUD_PREMIUM",
					DiskSize: 50,
				},
				DefaultParallelism: 0,
			},
			WorkerInfo: &billing.SetatsCvm{
				Cpu: 8,
				Mem: 32,
				Disk: &billing.SetatsDisk{
					DiskType: "CLOUD_PREMIUM",
					DiskSize: 60,
				},
				DefaultParallelism: 2,
			},
			ClusterGroupSerialId: "cluster-xxxx",
		},
	}, eventId)
	fmt.Println(r, r1)
}
