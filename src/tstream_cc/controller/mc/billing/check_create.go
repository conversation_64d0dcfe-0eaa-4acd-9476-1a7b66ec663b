package billing

import (
	"encoding/base64"
	"fmt"
	"strconv"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/component"
	service1 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	user2 "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/mc/user"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/billing"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/user"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/yunti"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	billing2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/billing"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cls"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cos"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/region"
	tag2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/tag"
	yuntiService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/yunti"
	"unicode"
	"unicode/utf8"
)

/**
 * 计费模块：新购策略
 *
 * 新购参数校验接口 feature-860782507
 *
 * https://tcb.woa.com/magical-brush/docs/754666638
 *
 * 效果图：http://tapd.oa.com/tfl/captures/2020-04/tapd_10140771_base64_1587871867_35.png
 */
func init() {
	const controllerName = "qcloud.oceanus.checkCreate"
	if code, msg := httpserver.RegisterController(controllerName, &checkCreateController{}); code != 0 {
		logger.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type checkCreateController struct {
}

func (c *checkCreateController) CreateRequestObj() interface{} {
	return &billing.CheckCreateReq{}
}

func CheckDisk(req *billing.CheckCreateReq) string {
	clusterGroupSerialId := req.GoodsDetail.ClusterGroupSerialId
	if clusterGroupSerialId == "" {
		return "clusterGroupSerialId is empty"
	}
	clusterGroup, err := service2.ListClusterGroupBySerialId(clusterGroupSerialId)
	if err != nil {
		logger.Errorf("CheckDisk Failed to get cluster group by serial id %s because %+v", clusterGroupSerialId, err)
		return fmt.Sprintf("CheckDisk Failed to get cluster group by serial id %s", clusterGroupSerialId)
	}
	if clusterGroup.NetEniType == constants.CLUSTER_NET_ENI_NODE {
		return fmt.Sprintf("cluster group %s is node eni, do not support", clusterGroupSerialId)
	}
	if clusterGroup.Status != constants.CLUSTER_GROUP_STATUS_RUNNING {
		return fmt.Sprintf("cluster group %s is not running", clusterGroupSerialId)
	}
	if clusterGroup.Type != constants.CLUSTER_GROUP_TYPE_PRIVATE {
		return fmt.Sprintf("cluster group %s is not private", clusterGroupSerialId)
	}
	// 是否已经有了 setats集群
	count, _, err := service.GetSetatsByClusterGroupSerialId(clusterGroup.SerialId)
	if err != nil {
		logger.Errorf("CheckDisk Failed to get setats by ClusterGroupSerialId id:%s, with errors:%+v", clusterGroup.SerialId, err)
		return fmt.Sprintf("CheckDisk Failed to get setats group by serial id %s", clusterGroupSerialId)
	}
	if count != 0 {
		logger.Errorf("CheckDisk cluster group %s has setats", clusterGroupSerialId)
		return fmt.Sprintf("cluster group %s has setats", clusterGroupSerialId)
	}

	if clusterGroup.AgentSerialId != "" {
		return fmt.Sprintf("cluster group %s has agent", clusterGroupSerialId)
	}
	if clusterGroup.AppId != req.AppId {
		return fmt.Sprintf("cluster group %s has different appid %d", clusterGroupSerialId, req.AppId)
	}
	if clusterGroup.OwnerUin != req.Uin {
		return fmt.Sprintf("cluster group %s has different uin %s", clusterGroupSerialId, req.Uin)
	}
	billingDiskTypeSizeMap := make(map[string]int)
	oceanusCloudPremium := req.GoodsDetail.OceanusCloudPremium
	if oceanusCloudPremium < 0 {
		return "oceanusCloudPremium must be equal or greater than 0"
	}
	billingDiskTypeSizeMap[billing.CLOUD_PREMIUM] = oceanusCloudPremium

	oceanusCloudBssd := req.GoodsDetail.OceanusCloudBssd
	if oceanusCloudBssd < 0 {
		return "oceanusCloudBssd must be equal or greater than 0"
	}
	billingDiskTypeSizeMap[billing.CLOUD_BSSD] = oceanusCloudBssd

	oceanusCloudSsd := req.GoodsDetail.OceanusCloudSsd
	if oceanusCloudSsd < 0 {
		return "oceanusCloudSsd must be equal or greater than 0"
	}
	billingDiskTypeSizeMap[billing.CLOUD_SSD] = oceanusCloudSsd

	oceanusCloudHssd := req.GoodsDetail.OceanusCloudHssd
	if oceanusCloudHssd < 0 {
		return "oceanusCloudHssd must be equal or greater than 0"
	}
	billingDiskTypeSizeMap[billing.CLOUD_HSSD] = oceanusCloudHssd

	infoDiskTypeSizeMap := make(map[string]int)
	masterInfo := req.GoodsDetail.MasterInfo
	if masterInfo == nil {
		return "masterInfo is empty"
	}
	masterDisk := masterInfo.Disk
	if masterDisk == nil {
		return "master disk is empty"
	}
	masterDiskSize := masterDisk.DiskSize
	if masterDiskSize < 1 {
		return "master disk size must be equal or greater than 0"
	}
	masterDiskType := masterDisk.DiskType
	exists, _ := service.Contain(masterDiskType, billing.DiskTypeList)
	if !exists {
		return fmt.Sprintf("master disk type %s is invalid", masterDiskType)
	}
	infoDiskTypeSizeMap[masterDiskType] = masterDiskSize

	workerInfo := req.GoodsDetail.WorkerInfo
	if workerInfo == nil {
		return "workerInfo is empty"
	}
	workerDisk := workerInfo.Disk
	if workerDisk == nil {
		return "worker disk is empty"
	}
	workerDiskSize := workerDisk.DiskSize
	if workerDiskSize < 1 {
		return "worker disk size must be equal or greater than 0"
	}
	workerDiskType := workerDisk.DiskType
	exists, _ = service.Contain(workerDiskType, billing.DiskTypeList)
	if !exists {
		return fmt.Sprintf("worker disk type %s is invalid", workerDiskType)
	}
	workerDefaultParallelism := workerInfo.DefaultParallelism
	if workerDefaultParallelism < 1 {
		return "worker default parallelism must be equal or greater than 0"
	}
	if _, exists = infoDiskTypeSizeMap[workerDiskType]; exists {
		infoDiskTypeSizeMap[workerDiskType] = infoDiskTypeSizeMap[workerDiskType] + workerDiskSize*workerDefaultParallelism
	} else {
		infoDiskTypeSizeMap[workerDiskType] = workerDiskSize * workerDefaultParallelism
	}
	for _, diskType := range billing.DiskTypeList {
		if _, exists = infoDiskTypeSizeMap[diskType]; !exists {
			infoDiskTypeSizeMap[diskType] = 0
		}
	}
	// 比较磁盘类型和大小
	logger.Infof("billingDiskTypeSizeMap %+v", billingDiskTypeSizeMap)
	logger.Infof("infoDiskTypeSizeMap %+v", infoDiskTypeSizeMap)
	for _, diskType := range billing.DiskTypeList {
		if billingDiskTypeSizeMap[diskType] != infoDiskTypeSizeMap[diskType] {
			return fmt.Sprintf("billing disk type %s size %d is not equal to info disk type %s size %d",
				diskType, billingDiskTypeSizeMap[diskType], diskType, infoDiskTypeSizeMap[diskType])
		}
	}
	return ""
}

func (c *checkCreateController) Process(req interface{}, eventId int64) (retCode int64, errMsg string, response interface{}) {
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf("Failed to call checkCreate because %+v", err)
			retCode = controller.SYSERR
			errMsg = "Fatal system error"
		}
	}()

	reqData := req.(*billing.CheckCreateReq)

	// 【注意事项】
	// 	1. 业务后台需校验创建资源请求的各参数是否准确。
	// 	2. 校验项需包含产品相关的各类限制策略
	// 		如：配额检查、用户资源限制、购买时长、支付金额（pid,询价参数,timeSpan,timeUnit,goodsNum）与商品配置是否匹配等
	// 		否则可能会导致逻辑漏洞 (检查时请求参数不包含tranId或者dealName，因为订单可能尚未生成)。
	// 	3. 0 元订单事故案例（未校验询价参数,timeSpan,goodsNum）：http://tapd.oa.com/pt_jifei/markdown_wikis/?#1010140771011094213
	// 	4. 接口必须是同步的（10s 完成）
	// 	5. 需要业务先调用计费的创建F订单接口 qcloud.Deal.generateDeals，计费才会回调 checkCreate

	// 打印 Event ID 和请求信息
	logger.Infof("checkCreate [%d] Request data %+v", eventId, reqData)

	// 兼容计费新的逻辑，以后不使用type了 切了订购关系或新接入(2021.06.17以后)的业务用productCode、subProductCode，type后续将会下线
	if reqData.Type == "" {
		reqData.Type = billing.OceanusExclusiveSubProductCode
	}

	// 初始化参数
	resp := &billing.CheckCreateResp{}
	resp.Status = billing.StatusSystemError

	isCluster := true
	// setats
	if reqData.GoodsDetail.OceanusCloudBssd > 0 ||
		reqData.GoodsDetail.OceanusCloudPremium > 0 ||
		reqData.GoodsDetail.OceanusCloudHssd > 0 ||
		reqData.GoodsDetail.OceanusCloudSsd > 0 {
		isCluster = false
		allow := auth.IsInWhiteList(int64(reqData.AppId), constants.WHITE_LIST_SUPPORT_SETATS)
		if !allow {
			return controller.SUCCESS, fmt.Sprintf("no permission to use setats"), resp
		}
	}

	// CAM鉴权 资源级
	//permission, msg, err := c.checkAuth(reqData, eventId)
	//if err != nil {
	//	return controller.SUCCESS, fmt.Sprintf("CAM authentication failed"), resp
	//}
	//if !permission {
	//	resp.Status = billing.StatusCAMNotAuthorized
	//	return controller.SUCCESS, msg, resp
	//}

	if isCluster {
		// 校验多可用区
		if reqData.GoodsDetail.OceanusExclusiveComputeMultipleCu14 != 0 {
			inWhiteList := auth.IsSupportMultipleDeploymentMode(int64(reqData.AppId))
			if !inWhiteList {
				logger.Errorf("no permission to use multiple mode")
				return controller.SUCCESS, fmt.Sprintf("no permission to use multiple mode"), resp
			}
			slaveVpcDescriptions := reqData.GoodsDetail.SlaveVpcDescriptions
			if len(slaveVpcDescriptions) < 1 {
				logger.Errorf("Multiple mode slaveVpcDescriptions is empty")
				return controller.SUCCESS, fmt.Sprintf("Multiple mode slaveVpcDescriptions is empty. "), resp
			}
			// 添加到 VpcDescriptions，后续进行校验
			if len(reqData.GoodsDetail.VpcDescriptions) > 0 {
				for _, slaveVpcDescription := range slaveVpcDescriptions {
					reqData.GoodsDetail.VpcDescriptions = append(reqData.GoodsDetail.VpcDescriptions, slaveVpcDescription)
				}
			}
			// 虽然计费项不同，会统一赋值到这里，方便走统一校验逻辑
			reqData.GoodsDetail.OceanusExclusiveComputeCu = reqData.GoodsDetail.OceanusExclusiveComputeMultipleCu14
		}
		// 校验包销参数
		if reqData.GoodsDetail.BillingResourceMode != "" {
			if reqData.GoodsDetail.BillingResourceMode != billing.ExclusiveSale {
				return controller.SUCCESS, fmt.Sprintf("billingResourceMode must be exclusiveSale"), resp
			}
			if reqData.GoodsDetail.OceanusExclusiveComputeUnderwriteCu14 < 1 {
				return controller.SUCCESS, fmt.Sprintf("exclusiveSale mode sv_oceanus_compute_underwrite_cu1c4g invalid. "), resp
			}
			found := false
			for _, duration := range billing.Durations {
				if duration == reqData.GoodsDetail.Duration {
					logger.Infof("find Duration %s", duration)
					found = true
					break
				}
			}
			if !found {
				logger.Errorf("cannot find Duration %s", reqData.GoodsDetail.Duration)
				return controller.SUCCESS, fmt.Sprintf("duration must be 1y or 2y or 3y or 4y or 5y"), resp
			}
			allow := auth.IsInWhiteList(int64(reqData.AppId), constants.WHITE_LIST_EXCLUSIVE_COMPUTEUNDERWRITECU14)
			if !allow {
				return controller.SUCCESS, fmt.Sprintf("no permission to use exclusive mode"), resp
			}
			// 虽然计费项不同，会统一赋值到这里，方便走统一校验逻辑
			reqData.GoodsDetail.OceanusExclusiveComputeCu = reqData.GoodsDetail.OceanusExclusiveComputeUnderwriteCu14
		}

		cores := GetWhiteListCores(reqData.AppId)
		// 1:2 1:8 校验
		if reqData.GoodsDetail.OceanusExclusiveComputeCu12 != 0 {
			allow := auth.IsInWhiteList(int64(reqData.AppId), constants.WHITE_LIST_MEMRATIO_2)
			if !allow {
				return controller.SUCCESS, fmt.Sprintf("no permission to use 1:2 cu"), resp
			}
			if cores != constants.CVM_DEFAULT_CORES {
				return controller.SUCCESS, fmt.Sprintf("1:2 cu must use standard cores"), resp
			}
			// 虽然计费项不同，会统一赋值到这里，方便走统一校验逻辑
			reqData.GoodsDetail.OceanusExclusiveComputeCu = reqData.GoodsDetail.OceanusExclusiveComputeCu12
		}

		if reqData.GoodsDetail.OceanusExclusiveComputeCu18 != 0 {
			allow := auth.IsInWhiteList(int64(reqData.AppId), constants.WHITE_LIST_MEMRATIO_8)
			if !allow {
				return controller.SUCCESS, fmt.Sprintf("no permission to use 1:8 cu"), resp
			}
			if cores != constants.CVM_DEFAULT_CORES {
				return controller.SUCCESS, fmt.Sprintf("1:8 cu must use standard cores"), resp
			}
			// 虽然计费项不同，会统一赋值到这里，方便走统一校验逻辑
			reqData.GoodsDetail.OceanusExclusiveComputeCu = reqData.GoodsDetail.OceanusExclusiveComputeCu18
		}

		if reqData.GoodsDetail.ClusterGroupType == constants.CLUSTER_GROUP_TYPE_SUB_EKS {
			if reqData.GoodsDetail.ParentSerialId == "" {
				return controller.SUCCESS, fmt.Sprintf("sub eks cluster type must be 4, parent cluster id not be null"), resp
			}
			// 校验权限
			InWhiteList := auth.IsInWhiteList(int64(reqData.AppId), constants.WHITE_LIST_SUB_EKS)
			if !InWhiteList {
				msg := fmt.Sprintf("AppID %d does not have permission to create an Sub EKS cluster", reqData.AppId)
				return controller.SUCCESS, msg, resp
			}
		}

		if reqData.GoodsDetail.ParentSerialId != "" {
			if reqData.GoodsDetail.ClusterGroupType != constants.CLUSTER_GROUP_TYPE_SUB_EKS {
				return controller.SUCCESS, fmt.Sprintf("sub eks cluster type must be 4, parent cluster id not be null"), resp
			}
			count, _, err := service2.ListClusterGroupByParentSerialId(reqData.GoodsDetail.ParentSerialId)
			if err != nil {
				return controller.SUCCESS, fmt.Sprintf("fail to get sub eks cluster with %s", reqData.GoodsDetail.ParentSerialId), resp
			}
			if count > 0 {
				return controller.SUCCESS, fmt.Sprintf("exist sub eks cluster with %s", reqData.GoodsDetail.ParentSerialId), resp
			}
			cg, err := service2.ListClusterGroupBySerialId(reqData.GoodsDetail.ParentSerialId)
			if err != nil {
				return controller.SUCCESS, fmt.Sprintf("fail to get parent cluster with %s", reqData.GoodsDetail.ParentSerialId), resp
			}
			if cg.AppId != reqData.AppId || cg.OwnerUin != reqData.Uin {
				return controller.SUCCESS, fmt.Sprintf("sub eks cluster user info is different with parent cluster"), resp
			}
		}

		/**
		 * 最前面校验  OceanusExclusiveComputeCu 和 ComputeCu， 包年包月
		 * 逻辑需要兼容不传 ComputeCu 的 情况
		 */
		oceanusExclusiveComputeCu := reqData.GoodsDetail.OceanusExclusiveComputeCu
		computeCu := reqData.GoodsDetail.ComputeCu
		if reqData.PayMode == billing.PayModePrepaid && computeCu != 0 {
			checkManageCuRst, errMsg, status := billing2.CheckManageCu(oceanusExclusiveComputeCu, computeCu)
			if checkManageCuRst != controller.SUCCESS {
				resp.Status = status
				// 校验失败， returnCode 也应该为0， 计费侧通过 resp data里面的status 判断是否可以购买
				return controller.SUCCESS, errMsg, resp
			}
			reqData.GoodsDetail.OceanusExclusiveComputeCu = computeCu
		}
	}

	// ==== 根据账号检查是不是内网账号信息
	NetEnviroment, returnCode, msg, resp := c.CheckAccount(reqData, resp)
	if returnCode != controller.SUCCESS {
		// 校验失败， returnCode 也应该为0， 计费侧通过 resp data里面的status 判断是否可以购买
		return controller.SUCCESS, msg, resp
	}

	// ==== 基准检查（检查基本参数）
	returnCode, msg, resp = c.checkBasicArguments(NetEnviroment, reqData, resp, isCluster)
	if returnCode != controller.SUCCESS {
		// 校验失败， returnCode 也应该为0， 计费侧通过 resp data里面的status 判断是否可以购买
		return controller.SUCCESS, msg, resp
	}

	// ==== 商品信息（GoodsDetail）检查 ====
	returnCode, msg, resp = c.checkGoodsDetail(NetEnviroment, reqData, resp, isCluster)
	if returnCode != controller.SUCCESS {
		return controller.SUCCESS, msg, resp
	}

	if !isCluster {
		errMsg = CheckDisk(reqData)
		if errMsg != "" {
			resp.Status = billing.StatusSystemError
			return controller.SUCCESS, errMsg, resp
		} else {
			// 检查成功
			resp.Status = billing.StatusSuccess
			return constants.SUCCESS, "ok", resp
		}
	}
	// 检查成功
	resp.Status = billing.StatusSuccess

	return constants.SUCCESS, "ok", resp
}

// CAM鉴权 资源级

func (c *checkCreateController) checkAuth(reqData *billing.CheckCreateReq, eventId int64) (bool, string, error) {

	authComponent := component.NewAuthComponent()
	regionParam, err := region.GetRegionNameById(int(reqData.Region))
	if err != nil {
		logger.Errorf("Failed to get region name by id from database because %+v", err)
		return false, "", err
	}
	isDev := service1.GetConfStringValue("scsDevEnv")

	// 测试环境跳过
	if isDev == "true" {
		return true, "", nil
	}
	var uin, operateUin, appid string
	uin = reqData.Uin
	appid = strconv.Itoa(int(reqData.AppId))
	operateUin = reqData.OperateUin

	resource := make([]string, 0)
	for _, v := range reqData.GoodsDetail.VpcDescriptions {
		vpc := fmt.Sprintf("qcs::vpc:%s:uin/%s:vpc/%s", regionParam, uin, v.VpcId)
		subnet := fmt.Sprintf("qcs::vpc:%s:uin/%s:subnet/%s", regionParam, uin, v.SubnetId)
		resource = append(resource, vpc, subnet)
	}
	oceanus := fmt.Sprintf("qcs::oceanus:%s:uin/%s:cluster/*", regionParam, uin)
	resource = append(resource, oceanus)
	qcsCos := fmt.Sprintf("qcs::cos:%s:uid/%s:prefix//%s/%s", regionParam, appid, appid, strings.TrimSuffix(reqData.GoodsDetail.DefaultCOSBucket, "-"+appid))
	resource = append(resource, qcsCos)

	condition := make(map[string]interface{})
	if len(reqData.GoodsDetail.Tags) > 0 {
		var tagStrings []string
		for _, t := range reqData.GoodsDetail.Tags {
			tagString := t.TagKey + "&" + t.TagValue
			tagStrings = append(tagStrings, tagString)
		}
		condition["qcs:request_tag"] = tagStrings
	}
	if reqData.GoodsDetail.Extparam.Token == "" {
		return true, "", nil
	}
	permission, msg, err := authComponent.SigAndAuth(uin, operateUin, appid,
		"oceanus:CreateClusterInstance", strconv.FormatInt(eventId, 10), reqData.GoodsDetail.Extparam.Token, resource, condition)
	if err != nil {
		logger.Errorf("%d: Failed to SigAndAuth, with error message %s", eventId, msg)
		return false, msg, err
	}
	if !permission {
		return false, msg, nil
	}

	return true, msg, nil
}

func (c *checkCreateController) CheckAccount(reqData *billing.CheckCreateReq, resp *billing.CheckCreateResp) (int, int64, string, *billing.CheckCreateResp) {
	NetEnviroment := constants.NETWORK_ENV_CLOUD_VPC
	region, err := region.GetRegionNameById(int(reqData.Region))
	if err != nil {
		logger.Errorf("Failed to get region name by id from database because %+v", err)
		resp.Status = billing.StatusSystemError
		return NetEnviroment, constants.SYSERR, "Failed to get region name by id", resp
	}
	requestBase := &apiv3.RequestBase{
		RequestId: util.GetCurrentTime(),
		Region:    region,
		Uin:       reqData.Uin,
	}
	checkAccountReq := &yunti.CheckAccountReq{
		RequestBase: *requestBase,
	}
	checkAccountRsp, err := yuntiService.CheckOwnUin(checkAccountReq)
	if err != nil {
		logger.Errorf("Failed to CheckOwnUin: %s  because %+v", reqData.Uin, err)
		resp.Status = billing.StatusSystemError
		return NetEnviroment, constants.SYSERR, "Failed to get region name by id", resp
	}

	if checkAccountRsp.Result.Total > 0 {
		NetEnviroment = constants.NETWORK_ENV_INNER_VPC
	}
	return NetEnviroment, constants.SUCCESS, constants.EMPTY, resp
}

func (c *checkCreateController) checkBasicArguments(NetEnviroment int, reqData *billing.CheckCreateReq, resp *billing.CheckCreateResp, isCluster bool) (int64, string, *billing.CheckCreateResp) {
	// 做一些最基础参数的检查
	retCode, errMsg, status := billing2.CheckNecessaryArguments(
		int(reqData.Region),
		int(reqData.ZoneId),
		reqData.AppId,
		reqData.PayMode,
		reqData.OperateUin,
		reqData.Uin,
		reqData.GoodsDetail.ClusterGroupType,
	)
	resp.Status = status
	if retCode != controller.SUCCESS {
		return retCode, errMsg, resp
	}

	// 然后做一些该接口独有的检查

	// 1. 检查用户是否已实名认证
	userAuthReq := &user.IsUserAuthenticatedReq{
		OwnerUin: reqData.Uin, // 主账号 Uin
	}
	retCode, retInfo, userAuthResp := (&user2.IsUserAuthenticatedController{}).Process(userAuthReq, 0)
	if retCode != controller.SUCCESS {
		logger.Errorf("Failed to check if user is authenticated or not, with error message %s", retInfo)
		resp.Status = billing.StatusSystemError
		return constants.SYSERR, "Unknown error while checking user is authenticated or not", resp
	}
	if userAuthResp.(*user.IsUserAuthenticatedResp).IsAuthenticated == 0 {
		logger.Errorf("User  uin %s is NOT authenticated, refuse to serve", reqData.Uin)
		resp.Status = billing.StatusUserNotAuthenticated
		return constants.PARAMSERR, "User not authenticated", resp
	}
	if isCluster {
		// 2. 确认该地域下集群名字是否可用
		regionName, err := region.GetRegionNameById(int(reqData.Region))
		if err != nil {
			logger.Errorf("Failed to get region name by id from database because %+v", err)
			resp.Status = billing.StatusSystemError
			return constants.SYSERR, "Failed to get region name by id", resp
		}
		// 3. 检查 ClusterName 是否合法
		if reqData.GoodsDetail.OrderOrigin == constants.ORDER_ORIGIN_CONSOLE {
			err := service.CheckNameValidity(reqData.GoodsDetail.ClusterName)
			if err != nil {
				logger.Errorf("Failed to invoke CheckNameValidity, with error: %+v", err)
				resp.Status = billing.StatusInvalidClusterName
				return constants.PARAMSERR, "Invalid cluster name", resp
			}

			clusterGroups, err := service.ListClusterGroups(&service.ListClusterGroupsParam{
				AppId:        reqData.AppId,
				Regions:      []string{regionName},
				Zone:         "",
				ClusterType:  []int8{constants.CLUSTER_GROUP_TYPE_PRIVATE},
				ClusterNames: []string{reqData.GoodsDetail.ClusterName},
				IsVagueNames: false,
				SerialIds:    nil,
				Offset:       0,
				Limit:        0,
				OrderType:    0,
				StatusList:   nil,
			})
			if err != nil {
				logger.Errorf("Failed to list ClusterGroups, with error: %+v", err)
				resp.Status = billing.StatusSystemError
				return constants.SYSERR, "Failed to list cluster groups", resp
			}
			if len(clusterGroups) > 0 {
				logger.Errorf("Failed to create Cluster, Duplicated ClusterName: %s", reqData.GoodsDetail.ClusterName)
				resp.Status = billing.StatusDuplicateClusterName
				return constants.PARAMSERR, "Cluster name already exists", resp
			}
			// 4. 检查 VPC 和子网是否合法
			err = service2.ValidateVpcSubnet(
				NetEnviroment, // 现网和内网(云梯)都支持了
				reqData.GoodsDetail.VpcDescriptions,
				reqData.Uin,
				reqData.OperateUin,
				regionName)
			if err != nil {
				logger.Errorf("Failed to check vpc subnet, with error: %+v", err)
				resp.Status = billing.StatusInvalidVpcSubnet
				return constants.PARAMSERR, "Invalid VPC or subnet", resp
			}
		}
		// 5. 检查 Flink UI 密码是否合法
		if reqData.GoodsDetail.LoginPassword != "" {
			if passwd, err := base64.StdEncoding.DecodeString(reqData.GoodsDetail.LoginPassword); err != nil {
				logger.Errorf("Failed to decode Flink UI password, with error: %+v", err)
				resp.Status = billing.StatusSystemError
				return controller.SYSERR, "Cannot decode password", resp
			} else if valid := util.IsValidPassword(string(passwd)); valid != true {
				logger.Errorf("Flink UI password is not valid")
				resp.Status = billing.StatusInvalidPassword
				return controller.PARAMSERR, "Invalid password", resp
			}
		} else {
			if reqData.GoodsDetail.OrderOrigin == constants.ORDER_ORIGIN_CONSOLE { // 只对来自控制台的请求校验密码非空
				logger.Errorf("Flink UI password is empty")
				resp.Status = billing.StatusInvalidPassword
				return controller.PARAMSERR, "Empty password", resp
			}
		}
	}

	// 6. 检查标签是否合法
	tags, err := tag2.GetTagService().ValidateTags(reqData.Uin, reqData.GoodsDetail.Tags)
	if err != nil {
		logger.Errorf("validateTags, with error: %+v", err)
		code := errorcode.GetCode(err)
		resp.Status = billing.StatusInvalidTag
		return code.GetCodeNum(), code.GetCodeDesc(), resp
	}
	reqData.GoodsDetail.Tags = tags

	return constants.SUCCESS, "", resp
}

func (c *checkCreateController) checkGoodsDetail(NetEnviroment int, reqData *billing.CheckCreateReq, resp *billing.CheckCreateResp, isCluster bool) (int64, string, *billing.CheckCreateResp) {

	// 做一些 GoodsDetail 的公共检查
	retCode, errMsg, status := billing2.CheckNecessaryGoodsDetail(
		reqData.AppId,
		reqData.GoodsDetail.TimeUnit,
		reqData.GoodsDetail.TimeSpan,
		reqData.GoodsDetail.GoodsNum,
		reqData.GoodsDetail.Pid,
		reqData.GoodsDetail.AutoRenewFlag,
		reqData.GoodsDetail.ProductCode,
		reqData.GoodsDetail.SubProductCode,
		reqData.GoodsDetail.OceanusExclusiveComputeCu,
		reqData.PayMode,
		isCluster,
	)
	resp.Status = status
	if retCode != controller.SUCCESS {
		return retCode, errMsg, resp
	}

	regionName, err := region.GetRegionNameById(int(reqData.Region))
	if err != nil {
		logger.Errorf("Invalid region id %d: %+v", reqData.Region, err)
		resp.Status = billing.StatusSystemError
		return controller.SYSERR, "Invalid region id", resp
	}
	if isCluster {
		if reqData.GoodsDetail.OrderOrigin == constants.ORDER_ORIGIN_CONSOLE || reqData.GoodsDetail.OrderOrigin == constants.ORDER_ORIGIN_DIANSHI {
			// 点石也需要校验COS
			if code, msg, resp := c.checkCosBucket(NetEnviroment, reqData, resp, regionName); code != constants.SUCCESS {
				return code, msg, resp
			}
			//if code, msg, resp := c.checkClsLogSetId(reqData, resp, regionName); code != constants.SUCCESS {
			//	return code, msg, resp
			//}
		}
		// 其他附加检查 检查备注长度
		if utf8.RuneCountInString(reqData.GoodsDetail.Remark) >= 100 {
			logger.Errorf("Remark is too long. should be less than 100 characters")
			resp.Status = billing.StatusInvalidOtherParams
			return constants.PARAMSERR, "Remark is too long", resp
		}
	}

	return constants.SUCCESS, "", resp
}

func (c *checkCreateController) checkCosBucket(NetEnviroment int, reqData *billing.CheckCreateReq, resp *billing.CheckCreateResp,
	regionName string) (int64, string, *billing.CheckCreateResp) {
	// 检查 COS Bucket 是否存在或有权限访问
	if !IsLegitimate(reqData.GoodsDetail.DefaultCOSBucket) {
		logger.Errorf("Bucket is legitimate")
		resp.Status = billing.StatusInvalidOtherParams
		return controller.PARAMSERR, "Bucket is not legitimate", resp
	}
	if reqData.GoodsDetail.DefaultCOSBucket == "" {
		logger.Errorf("No DefaultCOSBucket is provided")
		resp.Status = billing.StatusInvalidOtherParams
		return controller.PARAMSERR, "No DefaultCOSBucket provided", resp
	}
	// 只有运营账号才有访问用户cos的权限
	err := cos.CheckWhetherCOSBucketExists(reqData.Uin, reqData.OperateUin, regionName,
		reqData.GoodsDetail.DefaultCOSBucket)
	if err != nil {
		logger.Errorf("Failed to check whether COS bucket exists or not because %+v", err)
		resp.Status = billing.StatusInvalidOtherParams
		return controller.PARAMSERR, "Invalid DefaultCOSBucket", resp
	}
	return constants.SUCCESS, "", resp
}

func (c *checkCreateController) checkClsLogSetId(req *billing.CheckCreateReq, resp *billing.CheckCreateResp,
	region string) (int64, string, *billing.CheckCreateResp) {
	if req.GoodsDetail.CLSTopicId == "" {
		logger.Errorf("No CLSTopicId is provided")
		resp.Status = billing.StatusInvalidOtherParams
		return controller.PARAMSERR, "No ClsLogSetId provided", resp
	}
	err := cls.TopicCanBeUse(req.Uin, req.OperateUin, region, req.GoodsDetail.CLSLogSet, req.GoodsDetail.CLSTopicId)
	if err != nil {
		logger.Errorf("%v", err)
		errCode := errorcode.GetCode(err)
		return controller.PARAMSERR, errCode.GetCodeDesc(), resp
	}

	return constants.SUCCESS, "", resp
}

// 检查bucket和region的合法性
func IsLegitimate(str string) bool {
	for _, x := range []rune(str) {
		if !unicode.IsDigit(x) && !unicode.IsLetter(x) && x != '-' {
			return false
		}
	}
	return true
}
