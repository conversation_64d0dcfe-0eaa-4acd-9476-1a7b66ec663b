package billing

import (
	"encoding/json"
	"fmt"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/dlocker"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/billing"
	billing2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/billing"
	roleAuthService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/role_auth"
)

/**
 * 计费模块：退费策略、生命周期策略
 *
 * 停服资源接口
 *
 * http://tapd.oa.com/pt_jifei/markdown_wikis/show/#1210140771000569627
 */
func init() {
	const controllerName = "qcloud.oceanus.isolateResource"
	if code, msg := httpserver.RegisterController(controllerName, &isolateResourceController{}); code != 0 {
		logger.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type isolateResourceController struct {
}

func (i *isolateResourceController) CreateRequestObj() interface{} {
	return &billing.IsolateResourceReq{}
}

func (i *isolateResourceController) Process(req interface{}, eventId int64) (int64, string, interface{}) {
	reqData := req.(*billing.IsolateResourceReq)

	jsonReq, _ := json.Marshal(reqData)
	logger.Infof("[%d] Request data %+v", eventId, string(jsonReq))

	// 1. 接入平台生命周期管理或退款才需提供
	// 2. 业务后台隔离资源，需更新isolatedTimestamp为隔离时间
	// 3. 支持同时设置自动续费标记
	// 4. 支持同时设置新的到期时间
	// 5. 接口可以同步也可以异步，需要支持 可重入 （例如dealName或者（resourceId，uin，type））
	// 6. 生命周期逻辑：http://tapd.oa.com/pt_jifei/markdown_wikis/#1010140771010429089

	defaultSubProductCode := billing.OceanusExclusiveSubProductCode
	if reqData.SubProductCode == nil {
		reqData.SubProductCode = &defaultSubProductCode
	}

	/**
	 * 只有管理员才能隔离集群.
	 */

	if reqData.Uin != reqData.OperateUin {
		admin, err := roleAuthService.RoleAuthoAdministorCheck(reqData.AppId, reqData.OperateUin)
		if err != nil || admin == nil || admin.Status != constants.ROLE_AUTHO_STATUS_USEABLE {
			logger.Errorf("### only admin can operate，RoleAuthoAdministorCheck fail with AppId(%d), OperateUin(%s) because %+v", reqData.AppId, reqData.OperateUin, err)
			return constants.SYSERR, "only admin can operate.", nil
		}
	}

	rsp, err := i.process(reqData, eventId)
	if rsp.FlowId == billing.NoResourceFlag {
		return constants.SUCCESS, "ok", &struct{}{}
	}
	if err != nil {
		logger.Errorf("[%d] err:%s", eventId, err.Error())
		code := errorcode.GetCode(err)
		return constants.SYSERR, code.GetCodeDesc(), rsp
	}

	jsonRsp, _ := json.Marshal(rsp)
	logger.Infof("[%d] Rsp %s", eventId, string(jsonRsp))

	return constants.SUCCESS, "ok", rsp
}

func (i *isolateResourceController) process(req *billing.IsolateResourceReq, eventId int64) (rsp *billing.IsolateResourceResp,
	err error) {

	defer errorcode.DefaultDeferHandlerWithMess(&err, "isolateResourceController")

	// 这里不记录dealName或者（resourceId，uin，type）
	// 因为隔离接口本身就是可重入的， 当资源处于隔离状态，会返回 BillingResource 的flowId
	/**
	 * 后付费（https://tcb.woa.com/magical-brush/docs/754671391）
	 * 隔离分2种情况，请求来源，biz--业务手动发起，qn--计费生命周期自动发起，
	 *	如果不传ResourceId，就是欠费，需要隔离用户所有资源，这个时候加锁要用appid
	 * 如果传了ResourceId，控制台操作单个资源，保持原有逻辑
	 */

	var locker *dlocker.DLocker
	// 欠费, 按照地域处理
	if req.ResourceId == "" {
		logger.Infof("## 用户appid %d 由于欠费，现在需要隔离所有资源", req.AppId)
		locker = dlocker.NewDlocker("isolateResource",
			fmt.Sprintf("%s-%d-%d", billing.PayModePost_Source_QN, req.AppId, req.Region), 3*60)
	} else {
		locker = dlocker.NewDlocker("isolateResource", req.ResourceId, 3*60)
	}
	err = locker.Lock()
	if err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode_GetLockFailed, "lock fail", err)
	}
	defer locker.UnLock()
	rsp = &billing.IsolateResourceResp{}
	s := billing2.NewIsolateBillingResourceService(req, eventId)
	// 后付费
	if req.ResourceId == "" {
		rsp.FlowId, err = s.IsolateAllResources()
	} else {
		rsp.FlowId, err = s.Isolate()
	}
	return rsp, err
}
