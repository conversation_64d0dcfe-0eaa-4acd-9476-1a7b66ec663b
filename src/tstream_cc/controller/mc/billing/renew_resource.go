package billing

import (
	"encoding/json"
	"fmt"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/dlocker"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service2 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/billing"
	billing3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/billing"
	billing2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/billing"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"time"
)

/**
 * 计费模块：续费策略 feature-860782531
 *
 * 续费资源（发货）接口
 *
 * http://tapd.oa.com/pt_jifei/markdown_wikis/show/#1210140771000564911
 */
func init() {
	const controllerName = "qcloud.oceanus.renewResource"
	if code, msg := httpserver.RegisterController(controllerName, &renewResourceController{}); code != 0 {
		logger.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type renewResourceController struct {
}

func (r *renewResourceController) CreateRequestObj() interface{} {
	return &billing.RenewResourceReq{}
}

func (r *renewResourceController) Process(req interface{}, eventId int64) (retCode int64, errMsg string, response interface{}) {
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf("Failed to call renewResource because %+v", err)
			retCode = controller.SYSERR
			errMsg = "Fatal system error"
		}
	}()

	reqData := req.(*billing.RenewResourceReq)

	// 打印 Event ID 和请求信息
	logger.Infof("[%d] Request data %+v", eventId, reqData)
	resp := billing.RenewResourceResp{}

	billingResource, err := billingResourceService.GetResourceById(reqData.ResourceId)
	if err != nil {
		return constants.SYSERR, "cannot get cluster by resource id", resp
	}
	isCluster := 1
	if billingResource.Type == constants.BILLINGRESOURCE_TYPE_SETATS {
		isCluster = 0
	}

	if isCluster == 1 {
		// 校验包销参数
		if reqData.GoodsDetail.BillingResourceMode != "" {
			if reqData.GoodsDetail.BillingResourceMode != billing.ExclusiveSale {
				return controller.SYSERR, fmt.Sprintf("billingResourceMode must be exclusiveSale"), resp
			}
			if reqData.GoodsDetail.OceanusExclusiveComputeUnderwriteCu14 < 1 {
				return controller.SYSERR, fmt.Sprintf("exclusiveSale mode sv_oceanus_compute_underwrite_cu1c4g invalid. "), resp
			}
			found := false
			for _, duration := range billing.Durations {
				if duration == reqData.GoodsDetail.Duration {
					logger.Infof("find Duration %s", duration)
					found = true
					break
				}
			}
			if !found {
				logger.Errorf("cannot find Duration %s", reqData.GoodsDetail.Duration)
				return controller.SYSERR, fmt.Sprintf("duration must be 1y or 2y or 3y or 4y or 5y"), resp
			}
			allow := auth.IsInWhiteList(int64(reqData.AppId), constants.WHITE_LIST_EXCLUSIVE_COMPUTEUNDERWRITECU14)
			if !allow {
				return controller.SYSERR, fmt.Sprintf("no permission to use exclusive mode"), resp
			}
			// 虽然计费项不同，会统一赋值到这里，方便走统一校验逻辑
			reqData.GoodsDetail.OceanusExclusiveComputeCu = reqData.GoodsDetail.OceanusExclusiveComputeUnderwriteCu14
		}
	}

	var clusterGroupSerialId string
	if isCluster == 1 {
		clusterGroupSerialId = reqData.ResourceId
	} else {
		_setats, err := service.GetSetatsBySerialId(reqData.ResourceId)
		if err != nil {
			logger.Errorf("Failed to get setats by resource id %s because %+v", reqData.ResourceId, err)
			panic(err)
		}
		clusterGroupSerialId = _setats.ClusterGroupSerialId
	}
	// 获取资源信息
	clusterGroup, err := service.ListClusterGroupBySerialId(clusterGroupSerialId)
	if err != nil {
		logger.Errorf("Failed to get cluster group by serial id %s because %+v", reqData.ResourceId, err)
		panic(err)
	}
	cluster, err := service.GetActiveClusterByClusterGroupId(clusterGroup.Id)
	if err != nil {
		logger.Errorf("Failed to get cluster by cluster group id %d because %+v", clusterGroup.Id, err)
		panic(err)
	}

	if isCluster == 1 {

		// 多可用区部署
		if clusterGroup.DeploymentMode == constants.DeploymentModeMultiple {
			if reqData.GoodsDetail.OceanusExclusiveComputeMultipleCu14 < 1 {
				logger.Errorf("Renew_OceanusExclusiveComputeMultipleCu14 invalid.")
				return constants.SUCCESS, "Renew_OceanusExclusiveComputeMultipleCu14 invalid.", resp
			}
			// 虽然计费项不同，会统一赋值到这里，方便走统一校验逻辑
			reqData.GoodsDetail.OceanusExclusiveComputeCu = reqData.GoodsDetail.OceanusExclusiveComputeMultipleCu14
		}

		// 1:2 1:8
		if cluster.MemRatio == constants.CVM_MEMRATIO_2 {
			if reqData.GoodsDetail.OceanusExclusiveComputeCu12 < 1 {
				logger.Errorf("Renew_sv_oceanus_compute_exclusive_cu1c2g invalid.")
				return constants.SUCCESS, "Renew_sv_oceanus_compute_exclusive_cu1c2g invalid.", resp
			}

			// 虽然计费项不同，会统一赋值到这里，方便走统一校验逻辑
			reqData.GoodsDetail.OceanusExclusiveComputeCu = reqData.GoodsDetail.OceanusExclusiveComputeCu12
		}
		if cluster.MemRatio == constants.CVM_MEMRATIO_8 {
			if reqData.GoodsDetail.OceanusExclusiveComputeCu18 < 1 {
				logger.Errorf("Renew_sv_oceanus_compute_exclusive_cu1c8g invalid.")
				return constants.SUCCESS, "Renew_sv_oceanus_compute_exclusive_cu1c8g invalid.", resp
			}
			// 虽然计费项不同，会统一赋值到这里，方便走统一校验逻辑
			reqData.GoodsDetail.OceanusExclusiveComputeCu = reqData.GoodsDetail.OceanusExclusiveComputeCu18
		}
		/**
		 * 最前面校验  OceanusExclusiveComputeCu 和 ComputeCu， 包年包月
		 * 逻辑需要兼容不传 ComputeCu 的 情况
		 */

		if billingResource.IsNeedManageNode == constants.NEED_MANAGE_NODE {
			oceanusExclusiveComputeCu := reqData.GoodsDetail.OceanusExclusiveComputeCu
			computeCu := reqData.GoodsDetail.ComputeCu
			checkManageCuRst, errMsg, _ := billing2.CheckManageCu(oceanusExclusiveComputeCu, computeCu)

			if checkManageCuRst != controller.SUCCESS {
				return controller.SYSERR, errMsg, resp
			}
			reqData.GoodsDetail.OceanusExclusiveComputeCu = computeCu
		}
	}

	resp.ExecutionStartTime = time.Now().Format(billing.StandardTimestampFormatString)

	// 查询续费记录, 如果已经存在直接返回
	errMsg, resp = r.getRenewedBillingOrder(reqData, resp)
	if errMsg != "" { // 查到已续费的订单
		return controller.SUCCESS, errMsg, resp
	}

	// 如果记录不存在, 尝试加锁
	locker := dlocker.NewDlocker("oceanus-billing-renewResource", reqData.DealName, 8000)
	err = locker.Lock()
	if err != nil { // 加锁失败
		logger.Infof("Another renewResource process for %s has lock but not finished yet", reqData.DealName)
		return controller.SYSERR, "Another renewResource instance is still going, not ready yet", response // 续费不应该太久, 这里如果加锁失败返回 SYSERR 以引起重视
	}
	defer locker.UnLock()

	// 加锁后再次检查（Double-Checked Locking 模式), 防止之前查到的是旧数据
	errMsg, resp = r.getRenewedBillingOrder(reqData, resp)
	if errMsg != "" { // 查到已续费的订单
		return controller.SUCCESS, errMsg, resp
	}

	// 同步接口需要放入一个事务, 避免中间状态
	service2.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		// 写入新订单
		billingResource, err := billingResourceService.GetResourceById(reqData.ResourceId)
		if err != nil {
			logger.Errorf("Failed to get billing resource by id because %+v", err)
			panic(err)
		}

		billingOrder := r.createNewBillingOrder(reqData)
		billingOrder.ClusterId = cluster.Id
		billingOrder.ClusterGroupId = clusterGroup.Id
		billingOrder.ResourceId = reqData.ResourceId

		tx.SaveObject(billingOrder, "BillingOrder")

		// 更新 BillingResource 的各项字段
		// 1. AutoRenewFlag
		if reqData.GoodsDetail.AutoRenewFlag != nil { // 可选字段
			billingResource.AutoRenewFlag = *reqData.GoodsDetail.AutoRenewFlag
			logger.Infof("New AutoRenewFlag is provided as %d, will update BillingResource", billingResource.AutoRenewFlag)
		}
		// 2. IsolatedTimestamp   解除已隔离的资源
		billingResource.IsolatedTimestamp = billing.NotIsolatedTimestamp
		// 3. ExpireTime
		currentExpireTime, err := time.ParseInLocation(billing.StandardTimestampFormatString, billingResource.ExpireTime, time.Local)
		if err != nil {
			logger.Errorf("Failed to parse current expire time %s because %+v", billingResource.ExpireTime, err)
			panic(err)
		}
		expireTime := billing2.
			CalculateExpireTime(currentExpireTime, reqData.GoodsDetail.TimeUnit, reqData.GoodsDetail.TimeSpan).
			Format(billing.StandardTimestampFormatString)

		// 包年包月转包销模式，以及续费
		if reqData.GoodsDetail.BillingResourceMode == billing.ExclusiveSale {
			logger.Infof("exclusiveSale_duration is %s", reqData.GoodsDetail.Duration)
			billingResource.BillingResourceMode = billing.ExclusiveSale
			// 包销强制自动续费
			billingResource.AutoRenewFlag = 1
			if billingResource.Duration == "" {
				billingResource.Duration = reqData.GoodsDetail.Duration
			}
			if billingResource.ExclusiveSaleStartTime == billing.NotIsolatedTimestamp {
				billingResource.ExclusiveSaleStartTime = billingResource.ExpireTime
			}
		}
		billingResource.ExpireTime = expireTime

		// 4. GoodsDetail
		goodsDetailString, err := json.Marshal(reqData.GoodsDetail)
		if err != nil {
			logger.Errorf("Failed to marshal goods detail because %+v", err)
			panic(err)
		}
		billingResource.GoodsDetail = string(goodsDetailString)
		//billingResource.CreateTime = billingOrder.CreateTime // 续费后更新 v_vgjzhong 反馈这里只需要返回当前最新时间即可
		billingResource.Status = billing.ResourceStatusNormal

		/**
		 * 隔离增加cvm回收逻辑以后， 续费的时候，需要走流程，恢复cvm.
		 */
		docId := fmt.Sprintf("%s@%d", reqData.ResourceId, eventId)
		flowId, err := flow.CreateFlow(constants.FLOW_OCEANUS_RENEW_CLUSTER, docId,
			0, map[string]string{
				constants.FLOW_PARAM_REQUEST_ID:        fmt.Sprintf("%d", eventId),
				constants.FLOW_PARAM_CLUSTER_GROUP_ID:  fmt.Sprintf("%d", clusterGroup.Id),
				constants.FLOW_PARAM_CLUSTER_ID:        fmt.Sprintf("%d", cluster.Id),
				constants.FLOW_PARAM_SETATS_IS_CLUSTER: fmt.Sprintf("%d", isCluster),
			}, nil)
		resp.FlowId = flowId
		billingResource.FlowId = flowId // 变成异步发货 设置 FlowId

		tx.UpdateObject(billingResource, billingResource.Id, "BillingResource")

		resp.ResourceNewStartTime = billingOrder.CreateTime
		resp.ResourceNewEndTime = billingResource.ExpireTime
		resp.ExecutionEndTime = time.Now().Format(billing.StandardTimestampFormatString)

		if isCluster == 1 {
			// 如果集群在隔离中，设置集群状态为 恢复中， 成功以后，会设置为 运行中.
			if clusterGroup.Status == constants.CLUSTER_GROUP_STATUS_ISOLATED {
				sql := "UPDATE ClusterGroup SET Status = ? WHERE Id = ?"
				logger.Infof("Switched cluster group %s (%d) status to recovering", clusterGroup.SerialId, clusterGroup.Id)
				tx.ExecuteSqlWithArgs(sql, constants.CLUSTER_GROUP_STATUS_RECOVERING, clusterGroup.Id)
			}
		} else {
			service.SwitchSetatasStatusTo(clusterGroup.SerialId, constants.SETATS_RECOVERING)
		}
		return nil
	}).Close()

	return constants.SUCCESS, "ok", resp
}

// 查询状态已续费的 BillingOrder 对象, 找不到则返回空字符串
func (r *renewResourceController) getRenewedBillingOrder(reqData *billing.RenewResourceReq, resp billing.RenewResourceResp) (string, billing.RenewResourceResp) {
	billingOrder, err := billing2.GetBillingOrderByDealName(reqData.DealName)
	if err != nil {
		logger.Errorf("Failed to GetBillingOrderByDealName because %+v", err)
		panic(err)
	}
	if billingOrder != nil { // 如果查到已发货记录, 则查询资源
		billingResource, err := billingResourceService.GetResourceById(billingOrder.ResourceId)
		if err != nil {
			logger.Errorf("Failed to get billing resource by id because %+v", err)
			panic(err)
		}
		resp.ResourceNewStartTime = billingResource.CreateTime
		resp.ResourceNewEndTime = billingResource.ExpireTime
		resp.ExecutionEndTime = time.Now().Format(billing.StandardTimestampFormatString)
		return "Cluster has already been renewed", resp
	}

	return "", resp
}

// 创建订单记录
func (r *renewResourceController) createNewBillingOrder(reqData *billing.RenewResourceReq) billing3.BillingOrder {
	// AutoRenewFlag 是可选字段, 如果没有的话记录为 -1，表示不变
	var autoRenewFlag = -1
	if reqData.GoodsDetail.AutoRenewFlag != nil {
		autoRenewFlag = *reqData.GoodsDetail.AutoRenewFlag
	}

	return billing3.BillingOrder{
		AppId:          reqData.AppId,
		DealName:       reqData.DealName,
		Uin:            reqData.Uin,
		OperateUin:     reqData.OperateUin,
		Region:         reqData.Region,
		ZoneId:         reqData.ZoneId,
		PayMode:        reqData.PayMode,
		TimeUnit:       reqData.GoodsDetail.TimeUnit,
		TimeSpan:       reqData.GoodsDetail.TimeSpan,
		AutoRenewFlag:  autoRenewFlag,
		SubProductCode: reqData.GoodsDetail.SubProductCode,
		ComputeCu:      reqData.GoodsDetail.OceanusExclusiveComputeCu,
		Status:         billing.OrderStatusFinished, // 同步接口, 写入时直接完成
		CategoryId:     billing.OceanusExclusiveCategoryIdRenew,
		CreateTime:     util.GetCurrentTime(),
		UpdateTime:     util.GetCurrentTime(),
	}
}
