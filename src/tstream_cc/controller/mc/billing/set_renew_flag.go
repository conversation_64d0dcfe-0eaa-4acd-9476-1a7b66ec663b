package billing

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	service2 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/billing"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/region"
)

/**
 * 计费模块：续费策略
 *
 * 设置自动续费标记接口
 *
 * http://tapd.oa.com/pt_jifei/markdown_wikis/show/#1210140771000569639
 */
func init() {
	const controllerName = "qcloud.oceanus.setRenewFlag"
	if code, msg := httpserver.RegisterController(controllerName, &setRenewFlagController{}); code != 0 {
		logger.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type setRenewFlagController struct {
}

func (s *setRenewFlagController) CreateRequestObj() interface{} {
	return &billing.SetRenewFlagReq{}
}

func (s *setRenewFlagController) Process(req interface{}, eventId int64) (retCode int64, errMsg string, response interface{}) {
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf("Failed to call setRenewFlag because %+v", err)
			retCode = controller.SYSERR
			errMsg = "Fatal system error"
		}
	}()

	// 初始化参数
	reqData := req.(*billing.SetRenewFlagReq)
	resp := &billing.SetRenewFlagResp{}
	resp.Data = []interface{}{}

	// 打印 Event ID 和请求信息
	logger.Infof("[%d] Request data %+v", eventId, reqData)

	// 参数检查
	if reqData.Type != billing.OceanusExclusiveBillingType {
		logger.Errorf("Illegal billing type encountered: %+v", reqData.Type)
		return constants.PARAMSERR, "illegal billing type", resp
	}

	// 接入续费管理页的业务才需要提供，业务后台更新资源自动续费标记，接口必须是同步的
	// 用户在续费管理页通过此接口设置该资源是否进行自动续费，计费执行生命周期逻辑时查询资源的信息，判断资源是否需要自动续费

	txManager := service2.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {

		var resources []string
		if len(reqData.ResourceIds) == 0 && len(reqData.UUIDs) != 0 {
			resources = reqData.UUIDs
		} else {
			resources = reqData.ResourceIds
		}

		for _, resourceId := range resources {

			// 更新 BillingResource
			billingResource, err := billingResourceService.GetResourceById(resourceId)
			if err != nil {
				logger.Errorf("Failed to get billing resource by id because %+v", err)
				panic(err)
			}
			isCluster := (billingResource.Type == constants.BILLINGRESOURCE_TYPE_CLUSTER)
			var clusterGroupSerialId string
			if isCluster {
				clusterGroupSerialId = resourceId
			} else {
				_setats, err := service.GetSetatsBySerialId(resourceId)
				if err != nil {
					logger.Errorf("Failed to get setats by resource id %s because %+v", resourceId, err)
					panic(err)
				}
				clusterGroupSerialId = _setats.ClusterGroupSerialId
			}

			// 检查集群归属
			clusterGroup, err := service.ListClusterGroupBySerialId(clusterGroupSerialId)
			if err != nil {
				logger.Errorf("Failed to GetClusterGroupBySerialId because %+v", err)
				panic(err)
			}

			if clusterGroup.AppId != reqData.AppId || clusterGroup.OwnerUin != reqData.Uin {
				logger.Errorf("Resource %s does not belong to %v", resourceId, reqData.AppId)
				retCode, errMsg, response = constants.PARAMSERR, "resource does not belong to this account", resp
				return nil
			}

			// 检查地区
			regionName, err := region.GetRegionNameById(int(reqData.Region))
			if err != nil {
				logger.Errorf("Failed to get region name by id from database because %+v", err)
				panic(err)
			}
			if clusterGroup.Region != regionName {
				logger.Errorf("Region is incorrect for resource %s", resourceId)
				retCode, errMsg, response = constants.PARAMSERR, "region is incorrect", resp
				return nil
			}

			billingResource.AutoRenewFlag = reqData.AutoRenewFlag
			tx.UpdateObject(billingResource, billingResource.Id, "BillingResource")

			logger.Infof("Set AutoRenewFlag of %s to %d", resourceId, reqData.AutoRenewFlag)
		}

		retCode, errMsg, response = constants.SUCCESS, "ok", resp
		return nil
	}).Close()

	return
}
