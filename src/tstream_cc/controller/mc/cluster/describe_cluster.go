package cluster

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/cluster"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cluster"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
)

// 获取集群的信息, 返回内容等同于云 API 的 DescribeClusters
// 本接口仅被 Diagnosis-Data-Uploader DaemonSet 服务使用
// http://tapd.oa.com/TStream/prong/stories/view/1020358692866159403

func init() {
	const controllerName = "qcloud.galileo.mc.describeCluster"
	if code, msg := httpserver.RegisterController(controllerName, &DescribeClusterController{}); code != 0 {
		logger.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type DescribeClusterController struct {
}

func (this *DescribeClusterController) CreateRequestObj() interface{} {
	return &model.DescribeClusterReq{}
}

func (this *DescribeClusterController) Process(req interface{}, eventId int64) (int64, string, interface{}) {
	reqData := req.(*model.DescribeClusterReq)

	clusterGroup, err := service.GetClusterGroupBySerialId(reqData.ClusterSerialId)
	if err != nil {
		logger.Errorf("Failed to GetClusterGroupBySerialId for %s because %+v", reqData.ClusterSerialId, err)
		return controller.SYSERR, "Failed to GetClusterGroupBySerialId", nil
	}
	cluster, err := service.GetActiveClusterByClusterGroupId(clusterGroup.Id)
	if err != nil || cluster == nil {
		logger.Errorf("Failed to GetActiveClusterByClusterGroupId for %d because %+v", clusterGroup.Id, err)
		return controller.SYSERR, "Failed to GetActiveClusterByClusterGroupId", nil
	}

	cosBucket := cluster.DefaultCOSBucket
	if cluster.LogCOSBucket != "" {
		cosBucket = cluster.LogCOSBucket
	}

	return controller.SUCCESS, controller.NULL, &model.DescribeClusterRsp{
		ClusterSetItem: &model2.ClusterSetItem{
			// 目前只需要这个字段
			DefaultCOSBucket: cosBucket,
		},
	}
}
