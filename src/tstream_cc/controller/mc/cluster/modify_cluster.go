package cluster

import (
"tencentcloud.com/tstream_galileo/src/common/httpserver"
logger "tencentcloud.com/tstream_galileo/src/common/logger"
"strconv"
model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cluster"
service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
)

func init() {
	const controllerName = "qcloud.galileo.mc.modifyCluster"
	if code, msg := httpserver.RegisterController(controllerName, &ModifyClusterController{}); code != 0 {
		logger.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type ModifyClusterController struct {
}

func (this *ModifyClusterController) CreateRequestObj() interface{} {
	return &model.ModifyClusterReq{}
}

func (this *ModifyClusterController) Process(req interface{}, eventId int64) (int64, string, interface{}) {
	reqData := req.(*model.ModifyClusterReq)
	return service.DoModifyClusterRoleType(reqData)
}
