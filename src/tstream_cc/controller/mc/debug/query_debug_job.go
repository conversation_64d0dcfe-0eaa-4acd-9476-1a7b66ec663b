package debug

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/debug"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/debug"
)

func init() {
	const controllerName = "qcloud.galileo.mc.fetchDebugJob"
	if code, msg := httpserver.RegisterController(controllerName, &QueryDebugJobController{}); code != 0 {
		logger.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type QueryDebugJobController struct {
}

func (this *QueryDebugJobController) CreateRequestObj() interface{} {
	return &model.QueryDebugJobReq{}
}

func (this *QueryDebugJobController) Process(req interface{}, eventId int64) (int64, string, interface{}) {
	reqData := req.(*model.QueryDebugJobReq)
	return service.DoQueryDebugJob(reqData)
}
