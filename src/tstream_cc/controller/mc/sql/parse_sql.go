package metadata

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	sql "tencentcloud.com/tstream_galileo/src/tstream_cc/model/sql"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/sql"
)

func init() {
	const controllerName = "ParseSql"
	if code, msg := httpserver.RegisterCloudController(controllerName, &ParseSqlController{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type ParseSqlController struct {

}

func (this *ParseSqlController) CreateRequestObj() interface{} {
	return &sql.ParseSqlReq{}
}

func (this *ParseSqlController) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*sql.ParseSqlReq)
	return (&service.ParseSqlService{FlinkVersion: reqData.FlinkClientVersion}).DoParseSql(reqData, eventId)
}

