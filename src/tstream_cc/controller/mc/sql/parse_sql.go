package metadata

import (
	"context"

	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	sql "tencentcloud.com/tstream_galileo/src/tstream_cc/model/sql"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/sql"
)

func init() {
	const controllerName = "ParseSql"
	if err := httpserver.RegisterCloudControllerV2(controllerName, &ParseSqlController{}); err != nil {
		logger.Errorf("Failed to register cloud controller:%+v", err)
	}
}

type ParseSqlController struct {
}

func (this *ParseSqlController) CreateRequestObj() interface{} {
	return &sql.ParseSqlReq{}
}

func (this *ParseSqlController) Process(ctx context.Context,
	req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*sql.ParseSqlReq)
	return (&service.ParseSqlService{FlinkVersion: reqData.FlinkClientVersion}).DoParseSql(ctx, reqData, eventId)
}
