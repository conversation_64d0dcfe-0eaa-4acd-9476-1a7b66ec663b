package user

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/user"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/interface_auth"
)

func init() {
	const controllerName = "qcloud.galileo.mc.isUserAuthenticated"
	if code, msg := httpserver.RegisterController(controllerName, &IsUserAuthenticatedController{}); code != 0 {
		logger.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
	interface_auth.RegisterControllerNameMap(controllerName, "DescribeUserAuthentication")
}

type IsUserAuthenticatedController struct {
}

func (this *IsUserAuthenticatedController) CreateRequestObj() interface{} {
	return &user.IsUserAuthenticatedReq{}
}

func (this *IsUserAuthenticatedController) Process(req interface{}, eventId int64) (int64, string, interface{}) {
	reqData := req.(*user.IsUserAuthenticatedReq)

	platformUrl := service.GetConfStringValue("platformUrl")
	if platformUrl == "" {
		platformUrl = "http://account.tencentyun.com:50001"
	}

	requestJsonStruct := makeRequestStruct(reqData)
	requestBytes, err := json.Marshal(requestJsonStruct)
	if err != nil {
		logger.Errorf("Failed to unmarshal json request: %+v", err)
		return constants.SYSERR, "Failed to unmarshal json request", nil
	}
	httpRequest, err := http.NewRequest("POST", platformUrl, bytes.NewBuffer(requestBytes))
	if err != nil {
		logger.Errorf("Failed to initialize new request: %+v", err)
		return constants.SYSERR, "Failed to initialize new request", nil
	}

	httpClient := &http.Client{}
	httpResponse, err := httpClient.Do(httpRequest)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to make POST request: %+v", err)
		logger.Error(errMsg)
		return constants.SYSERR, errMsg, nil
	}
	defer httpResponse.Body.Close()

	if httpResponse.StatusCode != 200 {
		logger.Warningf("Status code of POST request is NOT 200 but %d", httpResponse.StatusCode)
		return constants.SYSERR, "Illegal status code", nil
	}

	body, _ := ioutil.ReadAll(httpResponse.Body)
	responseJsonStruct := &user.ResponseJson{}
	err = json.Unmarshal(body, responseJsonStruct)
	if err != nil {
		logger.Warningf("Failed to unmarshal response data because %+v", err)
		return constants.SYSERR, "Failed to unmarshal response data", nil
	}

	// 如果接口返回的 isAuthenticated 为 null (空字符串), 则说明账号状态异常, 直接忽略
	if responseJsonStruct.Data.IsAuthenticated == "" {
		rsp := &user.IsUserAuthenticatedResp{
			IsAuthenticated: 0,
		}
		return constants.SUCCESS, "ok", rsp
	}

	isAuthenticated, err := strconv.Atoi(responseJsonStruct.Data.IsAuthenticated)
	if err != nil {
		logger.Warningf("Illegal isAuthenticated value %+v", err)
		return constants.SYSERR, "Illegal isAuthenticated value", nil
	}
	rsp := &user.IsUserAuthenticatedResp{
		IsAuthenticated: isAuthenticated,
	}
	return constants.SUCCESS, "ok", rsp
}

func makeRequestStruct(reqData *user.IsUserAuthenticatedReq) *user.RequestJson {
	return &user.RequestJson{
		Version:       1,
		ComponentName: "MC",
		EventID:       1103941038,
		Interface: user.Interface{
			InterfaceName: "qcloud.cauth.getUserAuthInfoMd5",
			Para: user.Para{
				OwnerUin: reqData.OwnerUin,
			},
		},
	}
}
