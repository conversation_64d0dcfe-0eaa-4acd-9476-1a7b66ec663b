package upgrade

import (
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/upgrade"
	"testing"
)

func TestProcess_commandController(t *testing.T) {
	controller := &CreateComponents{}
	req := &upgrade.CreateComponentsReq{
		ResourceName: constants.ComponentCommandController,
	}
	req.Clusters = []upgrade.Cluster{{
		ImageName:  "",
		ResourceId: "cluster-e8ovfofm",
	}}

	controller.Process(req, 1)

}

func TestProcess_sql_server(t *testing.T) {
	controller := &CreateComponents{}
	req := &upgrade.CreateComponentsReq{
		ResourceName: constants.ComponentSqlServer,
	}
	req.Clusters = []upgrade.Cluster{{
		ResourceId: "cluster-e8ovfofm",
	}}

	controller.Process(req, 1)

}

func TestDeployRoleBinding(t *testing.T) {
	controller := &CreateComponentsNx{}
	req := &upgrade.CreateComponentsNxReq{
		ComponentName: "FlinkRoleBindingKey",
	}
	req.Clusters = []upgrade.Cluster{{
		ResourceId: "cluster-e8ovfofm",
	}}

	controller.Process(req, 1)

}
