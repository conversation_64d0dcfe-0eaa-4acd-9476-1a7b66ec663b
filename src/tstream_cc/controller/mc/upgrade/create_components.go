package upgrade

import (
	"encoding/json"
	"fmt"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/tke"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/upgrade"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config/configure_center"
	version "tencentcloud.com/tstream_galileo/src/tstream_cc/service/version"
	taskHandler "tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler"
)

func init() {
	// NX：SET if Not eXists
	const controllerName = "qcloud.oceanus.createComponents"
	if code, msg := httpserver.RegisterController(controllerName, &CreateComponents{}); code != 0 {
		logger.Error("Failed to register controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type CreateComponents struct {
}

func (c *CreateComponents) CreateRequestObj() interface{} {
	return &upgrade.CreateComponentsReq{}
}

func (c *CreateComponents) Process(req interface{}, eventId int64) (int64, string, interface{}) {
	reqData := req.(*upgrade.CreateComponentsReq)

	if reqData.Clusters == nil || len(reqData.Clusters) == 0 || reqData.ResourceName == "" {
		err := fmt.Errorf("CreateCommandControllerNx error, resourceIds is empty")
		stackError := errorcode.NewStackError(errorcode.InvalidParameterCode, "", err)
		return controller.SUCCESS, stackError.Error(), nil
	}

	rsp := &upgrade.CreateComponentsRsp{
		Clusters:        make([]upgrade.Cluster, 0, len(reqData.Clusters)),
		ErrorList:       make([]string, 0, len(reqData.Clusters)),
		SuccessClusters: make([]string, 0, len(reqData.Clusters)),
		FailClusters:    make([]string, 0, len(reqData.Clusters)),
		ResourceName:    reqData.ResourceName,
	}

	for _, item := range reqData.Clusters {
		clusterGroupService, err := service.NewClusterGroupServiceBySerialId(item.ResourceId)
		var clusterGroup *table.ClusterGroup
		var createParam = make(map[string]string)
		var cluster *table.Cluster
		var tkeList []*model.Tke
		var archGeneration int
		var params = make(map[string]string)
		var clusterList []*table.Cluster
		if err == nil {
			clusterGroup = clusterGroupService.GetClusterGroup()
			clusterList, err = clusterGroupService.GetClusterList()
		}
		if err == nil && len(clusterGroup.CreateParam) > 0 {
			err = json.Unmarshal([]byte(clusterGroup.CreateParam), &createParam)
		}
		if err == nil {
			cluster = clusterList[0]
			tkeList, err = clusterGroupService.GetTkeList()
		}

		if err == nil {
			if len(tkeList) > 1 {
				err = fmt.Errorf("%s tke is length is not 1, but %d", item.ResourceId, len(tkeList))
			} else if len(tkeList) == 1 {
				archGeneration = tkeList[0].ArchGeneration
				params[constants.FLOW_PARAM_REQUEST_ID] = fmt.Sprintf("%s_%s", item.ResourceId, reqData.ResourceName)
				params[constants.FLOW_PARAM_INVOKE_SOURCE] = constants.FLOW_INVOKE_FROM_CREATE_COMPONETS
				params[constants.FLOW_PARAM_CLUSTER_GROUP_ID] = strconv.FormatInt(cluster.ClusterGroupId, 10)
				params[constants.FLOW_PARAM_CLUSTER_ID] = strconv.FormatInt(cluster.Id, 10)

				if item.NewZone != "" {
					if clusterGroup.Type != constants.CLUSTER_GROUP_TYPE_UNIFORM {
						err = fmt.Errorf("%s is not a uniform cluster", item.ResourceId)
					} else {
						params[constants.FLOW_PARAM_ZONE] = item.NewZone
						//var support bool
						//if support, err = clusterGroup.IsSupportZone(item.NewZone); support {
						//	err = fmt.Errorf("%s is support zone: %s", item.ResourceId, item.NewZone)
						//} else if cNewZone, ok := createParam[constants.FLOW_PARAM_ZONE]; ok {
						//	if cNewZone != item.NewZone {
						//		err = fmt.Errorf("%s zone is creating, please not open %s zone", cNewZone, item.NewZone)
						//	}
						//} else {
						//	err = fmt.Errorf("please call flow qcloud.oceanus.createResource open new zone first, %s", item.ResourceId)
						//}
					}
				}
			} else { //createVPC(tags)、createTKE
				archGeneration, err = strconv.Atoi(createParam[constants.FLOW_PARAM_ARCH_GNERATION])
				params = createParam
				params[constants.FLOW_PARAM_REQUEST_ID] = fmt.Sprintf("%s_%s", item.ResourceId, reqData.ResourceName)
				params[constants.FLOW_PARAM_INVOKE_SOURCE] = constants.FLOW_INVOKE_FROM_CREATE_COMPONETS
			}
		}

		if err != nil || cluster == nil {
			tmp := map[string]string{
				"errorCode": fmt.Sprintf("%+v", err),
				"errorMsg":  fmt.Sprintf("%+v", err),
				"clusterId": item.ResourceId,
			}
			marshal, _ := json.Marshal(tmp)
			rsp.ErrorList = append(rsp.ErrorList, string(marshal))
			rsp.FailClusters = append(rsp.FailClusters, item.ResourceId)
			continue
		}

		req := &flow.TaskExecRequest{
			Params: params,
		}
		if reqData.Retrycount > 0 {
			req.Retrycount = reqData.Retrycount
		}
		if item.EniReserveMaxNum >= 0 {
			req.Params[taskHandler.EniReserveMaxNumParamName] = strconv.Itoa(item.EniReserveMaxNum)
		}
		if item.EniReserveMinNum >= 0 {
			req.Params[taskHandler.EniReserveMinNumParamName] = strconv.Itoa(item.EniReserveMinNum)
		}
		var errMessage = ""
		var failFlag int8 = 0
		handler, err := GetDeployHandler(reqData.ProcessKey, reqData.ResourceName)
		if err != nil {
			stackError := errorcode.NewStackError(errorcode.InvalidParameterCode, err.Error(), err)
			return controller.SUCCESS, stackError.Error(), nil
		}
		for {
			b, _ := json.Marshal(req)
			logger.Infof("request: %s", string(b))
			taskRsp := handler.CompleteTask(req)
			rsp.Clusters = append(rsp.Clusters, upgrade.Cluster{
				Err:        taskRsp.Err,
				ResourceId: item.ResourceId,
				ImageName:  item.ImageName,
			})
			req.Params = taskRsp.Params
			if taskRsp.RetCode == flow.TASK_SUCCESS {
				logger.Infof("successfully deploy cluster: %s ResourceName: %s", item.ResourceId, reqData.ResourceName)
				rsp.SuccessClusters = append(rsp.FailClusters, item.ResourceId)
				break
			} else if taskRsp.RetCode == flow.TASK_FAIL {
				failFlag = 1
				errMessage = fmt.Sprintf("failed to deploy cluster: %s ResourceName: %s, error is %s ", item.ResourceId, reqData.ResourceName, taskRsp.Err.Error())
				break
			} else if taskRsp.RetCode == flow.TASK_IN_PROCESS && req.Retrycount > 120 {
				failFlag = 1
				errMessage = fmt.Sprintf("deploy cluster: %s ResourceName: %s time out, err:%v", item.ResourceId, reqData.ResourceName, taskRsp.Err)
				break
			} else {
				logger.Infof("cluster: %s ResourceName: %s task in process, Retrycount:%d", item.ResourceId, reqData.ResourceName, req.Retrycount)
			}
			req.Retrycount++
		}
		if failFlag == 1 {
			logger.Errorf(errMessage)
			tmp := map[string]string{
				"errorMsg":  errMessage,
				"clusterId": item.ResourceId,
			}
			marshal, _ := json.Marshal(tmp)
			rsp.ErrorList = append(rsp.ErrorList, string(marshal))
			rsp.FailClusters = append(rsp.FailClusters, item.ResourceId)
		} else {
			/**
			主动更新一下TKeVersion表，因为走创建集群创建服务的通用逻辑，如果发现tkeversion表里有数据了，就不会更新了
			*/
			resource, ok := constants.ResourceMap[reqData.ResourceName]
			if ok {
				errorCode, _, imageVersion := version.GetTkeImageVersion(resource.ResourceName, resource.Containers[0], cluster, archGeneration)
				iVersion, err := service2.ImageToVersion(imageVersion)
				if errorCode == "" && err == nil {
					tv, err := version.GetLastTkeVersion(cluster.Id, resource.ResourceName, resource.Containers[0])
					if err == nil {
						if tv != nil && iVersion != tv.ContainerVersion {
							if err == nil {
								cc := configure_center.CC(clusterGroup.Region)
								tkeDomain, err := cc.ImageRegistry().TkeRegistry()
								if err != nil {
									panic(err)
								}
								tkeImageNamespace, err := cc.ImageRegistry().Namespace()
								if err != nil {
									panic(err)
								}
								oldImage := fmt.Sprintf("%s/%s/%s:%s",
									tkeDomain,
									tkeImageNamespace,
									resource.ResourceName,
									tv.ContainerVersion,
								)
								version.UpdateTkeVersion(cluster.Id, resource.ResourceName, resource.Containers[0], iVersion, oldImage)
							}

						}
					}

				}
			}
		}
	}

	return controller.SUCCESS, controller.OK, rsp
}
