package metadata

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/httpserver"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/metadata"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/metadata"
)
// Deprecated
func init() {
	const controllerName = "DescribeMetaTableV1"
	if code, msg := httpserver.RegisterCloudController(controllerName, &DescribeMetaTableControllerV1{}); code != 0 {
		logger.Error("Failed to register cloud controller " + controllerName + ", code: " + strconv.FormatInt(int64(code), 10) + ",msg: " + msg)
	}
}

type DescribeMetaTableControllerV1 struct {

}

func (this *DescribeMetaTableControllerV1) CreateRequestObj() interface{} {
	return &model.DescribeMetaTableReqV1{}
}

func (this *DescribeMetaTableControllerV1) Process(req interface{}, eventId int64) (string, string, interface{}) {
	reqData := req.(*model.DescribeMetaTableReqV1)
	return (&service.DoDescribeMetaTableServiceV1{}).DoDescribeMetaTableV1(reqData, eventId)
}