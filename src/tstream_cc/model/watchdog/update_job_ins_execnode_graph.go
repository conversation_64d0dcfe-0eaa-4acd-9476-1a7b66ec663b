package watchdog

import (
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"
)

type UpdateJobInsExecNodeGraphReq struct {
	apiv3.RequestBase
	RequestId         string `json:"requestId"`
	JobSerialId       string `check:"nullable:false" json:"jobSerialId"`
	JobInstanceId     int64  `check:"nullable:false" json:"jobInstanceId"`
	ExecNodeGraphJson string `json:"execNodeGraphJson"`
}

type UpdateJobInsExecNodeGraphResp struct {
	Result string `json:"Result"`
}
