package watchdog

import "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"

type UpdateSavepointReq struct {
	apiv3.RequestBase
	Id           int64  `json:"id"`
	JobId        int64  `json:"jobId"`
	JobRuntimeId int64  `json:"jobRuntimeId"`
	Status       int8   `json:"status"`
	RecordType   int8   `json:"recordType"`
	Size         int64  `json:"size"`
	Path         string `json:"path"`
	TriggerTime  int64  `json:"triggerTime"`
	LastAckTime  int64  `json:"lastAckTime"`
	ClusterId    int64  `json:"clusterId"`
}

type UpdateSavepointRsp struct {
	IsSucc string `json:"isSucc"`
}

type SavepointEntity struct {
	Id           int64  `json:"id"`
	JobId        int64  `json:"jobId"`
	SerialId     string `json:"serialId"`
	JobRuntimeId int64  `json:"jobRuntimeId"`
	Status       int8   `json:"status"`
	RecordType   int8   `json:"recordType"`
	Size         int64  `json:"size"`
	Timeout      int64  `json:"timeout"`
	Path         string `json:"path"`
	CreateTime   string `json:"createTime"`
	UpdateTime   string `json:"updateTime"`
	Description  string `json:"description"`
	ClusterId    int64  `json:"clusterId"`
	ItemSpaceId  int64  `json:"itemSpaceId"`
}

type SavepointParam struct {
	Bucket string
	Path   string
	Region string
}

type JobClusterEntity struct {
	JobId            int64
	ClusterId        int64
	Region           string
	DefaultCOSBucket string
	StateCOSBucket   string
	OwnerUin         string
	CreatorUin       string
}
