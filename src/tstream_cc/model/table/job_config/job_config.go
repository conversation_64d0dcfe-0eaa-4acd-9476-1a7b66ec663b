package table

/**
 *
 * <AUTHOR>
 * @time 2019/4/26
 * @version 0.4.0
 * @since 0.4.0
 * @see
 */
type JobConfig struct {
	Id                      int64
	CreatorUin              string
	JobId                   int64
	VersionId               int16
	EntrypointClass         string
	ProgramArgs             string
	CheckpointInterval      int64
	COSBucket               string
	SqlCode                 string
	Status                  int8 // 1: 使用中，-1：已删除
	Remark                  string
	CreateTime              string
	MaxParallelism          int16
	DefaultParallelism      int16
	Properties              string
	UpdateTime              string
	LogCollect              int
	JmCuSpec                float32
	TmCuSpec                float32
	ClsLogsetId             string
	ClsTopicId              string
	PythonVersion           string
	AutoRecover             int    // Oceanus 平台恢复作业开关 1:开启 -1: 关闭
	LogLevel                string // DEBUG INFO WARN ERROR
	UseOldSysConnector      int    // 使用历史的内置connector  0：不使用  1：使用  默认为0
	ExpertModeConfiguration string
	ClazzLevels             string // json.marshal([]*log.ClazzLevel) default [] // 修改类日志级别

	EsServerlessIndex       string // es索引
	EsServerlessSpace       string
	TraceModeConfiguration  string
	CheckpointRetainedNum   int8
	CheckpointTimeoutSecond int64
	JobGraph                string
	FlinkVersion            string

	JobManagerCpu  float32
	JobManagerMem  float32
	TaskManagerCpu float32
	TaskManagerMem float32
	LibConfig      string

	AutoScaleJobGraph                   string
	OldAutoScaleJobGraph                string
	ExpertModeConfigurationResourceName string

	LogCOSBucket        string // 日志COSBucket
	StateCOSBucket      string // saveppoint/checkpoint COSBucket
}
type JobRunningInfo struct {
	JobConfig

	JobInstanceId  int64
	TmRunningCuNum int16
	JmRunningCuNum int16
	// flink上报的tm资源
	TmTotalCpu float32
	TmTotalMem float32
}
