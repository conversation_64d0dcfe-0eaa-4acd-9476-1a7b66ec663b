package table

type Region struct {
	Id                   int64  `json:"id"`
	RegionId             int    `json:"regionId"`
	Region               string `json:"region"`
	RegionName           string `json:"regionName"`
	State                int    `json:"state"`
	NetEnvType           int    `json:"netEnvType"`
	ShortName            string `json:"shortName"`
	SupportSharedCluster int    `json:"supportSharedCluster"`
}

type Zone struct {
	Id         int64  `json:"id"`
	Region     string `json:"regionId"`
	ZoneId     int    `json:"zoneId"`
	Zone       string `json:"zone"`
	ZoneName   string `json:"zoneName"`
	State      int    `json:"state"`
	NetEnvType int    `json:"netEnvType"`
}

const (
	REGION_ID_GUANGZHOU = 1
	REGION_ID_SHANGHAI  = 4
	REGION_ID_CHENGDU   = 16
	REGION_ID_BEIJING   = 8
)

type RegionJobCount struct {
	Region              string
	TotalJobCount       int32
	RunningJobCount     int32
	StoppedJobCount     int32
	CreatedJobCount     int32
	InitializedJobCount int32
	PausedJobCount      int32
	ProgressJobCount    int32
	ConcerningJobCount  int32
}

type RegionClusterCount struct {
	Region                 string
	TotalClusterCount      int16
	RunningClusterCount    int16
	CreatingClusterCount   int16
	InProgressClusterCount int16
}

type RegionItemSpaceCount struct {
	Region              string
	TotalItemSpaceCount int32
}
