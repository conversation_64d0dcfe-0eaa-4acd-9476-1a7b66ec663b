package setats

// Setats 表对应的模型结构体
type Setats struct {
	Id                       int64
	SerialId                 string
	ClusterGroupSerialId     string
	AppId                    int64
	OwnerUin                 string
	CreatorUin               string
	MasterCpu                float32
	MasterMem                float32
	MasterDiskType           string
	MasterDiskSize           int
	WorkerCpu                float32
	WorkerMem                float32
	WorkerDiskType           string
	WorkerDiskSize           int
	WorkerDefaultParallelism int
	Region                   string
	Zone                     string
	Status                   int8 // 1 创建中 2：运行中，3：停止， 4 配置开启中 5 重启中 6 开启失败
	CreateTime               string
	UpdateTime               string
	ManagerUrl               string
	HiveMetastoreUser        string
	HiveMetastorePass        string
}

// SetatsWarehouse 表对应的模型结构体
type SetatsWarehouse struct {
	Id             int64
	SetatsSerialId string
	CatalogType    string
	Location       string
	Uri            string
	WarehouseUrl   string
	Authentication string
	Status         int8 // 2 开启, 0 未开启 1 开启中
	CreateTime     string
	UpdateTime     string
	HiveUri        string
	Properties     string
}

// SetatsRef 表对应的模型结构体
type SetatsRef struct {
	Id             int64
	SetatsSerialId string
	ResourceId     int64
	Version        int64 // 资源版本, 默认-1代表引用最新的资源
	Status         int8  // 配置资源的状态, 1:正常, -2:删除
	Type           int
	CreateTime     string
	UpdateTime     string
}

type SetatsResourceInfo struct {
	Id               int64
	SetatsSerialId   string
	ResourceSerialId string
	ResourceId       int64
	Version          int64 // 资源版本, 默认-1代表引用最新的资源
	Status           int8  // 配置资源的状态, 1:正常, -2:删除
	Type             int
	CreateTime       string
	UpdateTime       string
}

// SetatsParam 表对应的模型结构体
type SetatsParam struct {
	Id                int64
	SetatsSerialId    string
	ParamName         string
	ParamValue        string
	ReferenceValue    string
	ModificationGuide string
	IsRestart         int
	Status            int // -1 删除, 1 生效
	CreateTime        string
	UpdatedTime       string
}

type BatchTask struct {
	Id               int64
	JobSerialId      string
	JobConfigVersion int16
	AppId            int64
	OwnerUin         string
	CreatorUin       string
	Region           string
	Zone             string
	Status           int8
	EngineJobId      string
	EngineJobLogUrl  string
	JobUUID          string
	CreateTime       string
	UpdatedTime      string
	Type             int // 1 调度 2 运行
	NextResultUri    string
	Result0          string
	JobID            string
}

type BatchTaskLog struct {
	Id          int64
	BatchTaskId int64
	Content     string
	OrderNumber int
	CreateTime  string
	UpdatedTime string
}
