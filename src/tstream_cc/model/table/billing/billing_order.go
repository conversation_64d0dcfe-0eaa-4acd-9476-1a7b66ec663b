package billing

/**
 * 用于追踪用户下单信息
 */
//goland:noinspection GoNameStartsWithPackageName
type BillingOrder struct {
	Id             int64  `json:"id"`
	AppId          int64  `json:"appId"`
	CategoryId     int    `json:"categoryId"`
	DealName       string `json:"dealName"`
	Uin            string `json:"Uin"`
	OperateUin     string `json:"operateUin"`
	Region         int    `json:"region"`
	ZoneId         int    `json:"zoneId"`
	ClusterId      int64  `json:"clusterId"`
	ClusterGroupId int64  `json:"clusterGroupId"`
	ResourceId     string `json:"resourceId"`
	PayMode        int    `json:"payMode"`
	TimeUnit       string `json:"timeUnit"`
	TimeSpan       int    `json:"timeSpan"`
	AutoRenewFlag  int    `json:"autoRenewFlag"`
	SubProductCode string `json:"subProductCode"`
	ComputeCu      int    `json:"computeCu"`
	FlowId         int64  `json:"flowId"`
	Status         int    `json:"status"` // 1 初始 2 发货中 3 发货完成 -1 异常 -2 退货
	CreateTime     string `json:"createTime"`
	UpdateTime     string `json:"updateTime"`
}
