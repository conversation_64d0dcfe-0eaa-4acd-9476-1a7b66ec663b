package billing

/*
CREATE TABLE BillingResource (
 `Id` int(11) NOT NULL AUTO_INCREMENT,
 `ResourceId` varchar(255) NULL DEFAULT NULL,
`FlowId` bigint(20) NOT NULL,
  `AppId` bigint(20) NOT NULL,
  `Uin` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `OperateUin` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `Region` int(11) NOT NULL,
  `ZoneId` int(11) NOT NULL,
  `PayMode` tinyint(4) NULL DEFAULT NULL,
`ComputeCu` int(11) NOT NULL,
`SubProductCode` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `TimeUnit` varchar(5) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `TimeSpan` tinyint(4) NOT NULL,
  `AutoRenewFlag` tinyint(4) NOT NULL,
  `Status` tinyint(4) NOT NULL DEFAULT 1,
  `CreateTime` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `IsolatedTimestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    `ExpireTime` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    `GoodsDetail` text COLLATE utf8mb4_bin,
  PRIMARY KEY (`Id`) USING BTREE,
  INDEX (ResourceId)
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Compact;
*/

//goland:noinspection GoNameStartsWithPackageName
type BillingResource struct {
	Id                     int64
	ResourceId             string
	AppId                  int64
	Uin                    string
	OperateUin             string
	Region                 int
	ZoneId                 int
	PayMode                int
	ComputeCu              int
	SubProductCode         string
	TimeUnit               string
	TimeSpan               int
	AutoRenewFlag          int
	Status                 int
	FlowId                 int64
	CreateTime             string
	IsolatedTimestamp      string
	ExpireTime             string
	GoodsDetail            string
	International          int
	IsNeedManageNode       int    // 前端区分 集群是否需要2CU逻辑 因为历史集群 变配不需要
	BillingResourceMode    string // 包销模式 "exclusiveSale"
	Duration               string
	ExclusiveSaleStartTime string // 包销开始时间
	Type                   int    // 1 集群 2 Setats
}
