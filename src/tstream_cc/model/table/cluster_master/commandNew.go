package cluster_master

/**
 *
 * <AUTHOR>
 * @time 2019/4/26
 * @version 0.4.0
 * @since 0.4.0
 * @see
 */
type CommandNew struct {
	Id               int64
	ClusterGroupId   int64
	ClusterId        int64
	AppId            int32
	OwnerUin         string // 新增
	CreatorUin       string // 新增
	Region           string // 新增
	Zone             string // 新增
	JobId            int64
	JobName          string // 新增
	JobSerialId      string // 新增
	JobConfigId      int64  // 新增
	RunningOrderId   int64  `json:"-"` // 4.8 迭代新增（只在 Galileo 内部使用）
	Action           int8   // 由 string 调整为 数字
	JobType          int8   // 由 string 调整为 数字
	JobRegressStatus int8   // 由 string 调整为 数字
	JobNextStatus    int8   // 由 string 调整为 数字
	Params           string // 转义后的 JSON 字符串
	Status           int8   // 由 string 调整为 数字
	CreateTime       string
	FetchTime        string // 命令被ClusterMaster命令的时间
	AckTime          string // ClusterMaster取走命令后回复确认的时间
	FinishTime       string // ClusterMaster回复Galileo，命令执行完成的时间
	UpdateTime       string
	ClusterSerialId  string
}
