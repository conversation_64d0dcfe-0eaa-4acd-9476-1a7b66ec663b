package table

import (
	"encoding/json"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/white_list"
)

/**
 *
 * <AUTHOR>
 * @time 2019/4/26
 * @version 0.4.0
 * @since 0.4.0
 * @see
 */
type ClusterGroup struct {
	Id                 int64
	SerialId           string
	Name               string
	AppId              int32
	OwnerUin           string
	CreatorUin         string
	Region             string
	Zone               string
	SupportedZones     string
	Type               int8   // 1表示共享集群，2表示独享集群，3表示统一资源池的母集群
	AgentSerialId      string //统一资源池的母集群
	ResourceType       int8   //共享集群，资源类型 0:独立使用资源 1:共享使用资源
	NetEnvironmentType int8   //网络环境，1：公网环境，3：内网环境
	// NetConnectionType int8 //网络链接类型，0：弹性网卡方案，2:内网非弹性网卡方案 （这个字段原先没有记录，共用NetEnvironmentType）
	NetEniType         int8 // 弹性网卡方案，0：POD弹性网卡，1：Node弹性网卡
	NodeEniType        int8 // 节点弹性网卡方案 0:自动创建(默认) 1:Oceanus手动管理(需添加eth1和路由) 2:TKE组件管理(旧方案，需过滤掉)
	Status             int8 // 集群状态，1：创建中，2：运行中
	CuNum              int16
	UsedCuNum          int16
	CuMem              int8 // CU内存规格，单位GB，当前仅支持2,4,8,16
	Remark             string
	CreateTime         string
	UpdateTime         string
	StopTime           string
	StartTime          string
	CreateParam        string
	SendCredentialTime string
	ZkType             int8   // 集群zk是否开启了认证. 0 no auth, 1 Digest
	ParentSerialId     string // 共享集群，包年包月支持按量付费模式，需要传递 包年包月集群Id
	ZkRootPath         string
	ZkUser             string
	ZkPass             string
	UniformConfig      string
	UniformCalcInfo    string
	UniformCalcTime    string
	UniformOwnerUin    string
	DeploymentMode     int
}

type ZoneInfo struct {
	HasSA2 int64
	HasS5  int64

	LimitCPU float64

	ReqCPU float64
	ReqMem float64

	FreeCU float64
	NeedCU float64

	NumOfCuMap map[string]int64

	NeedCvm     int64
	BuyCvmCount int64
}

type CalcInfo struct {
	CuNeed           float64
	CuElastic        float64
	CuRunning        float64
	MaxFreeCU        float64
	CuFree           float64
	FreeCURate       float64
	ReserveCu        float64
	MaxAddWorkNum    int
	ErrorInstanceSet []string
}

type UniformCalcInfo struct {
	ZoneInfoMap map[string]*ZoneInfo
	CalcInfo    *CalcInfo
	CalcTime    string
}

type UniformConfig struct {
	BuySwitch                   bool    //是否定时计算购买CVM
	DisableRunBuySwitch         bool    //关闭运行时计算购买CVM
	PrintInfo                   bool    //打印计算信息
	DisableChooseZone           bool    //关闭选择地域功能
	DelayMinute                 int64   //计算结果有效的延迟时间
	MaxFreeCU                   int64   //最大空闲CU
	FreeCURate                  float64 //空闲CU的比例
	MinNeedCu                   float64 //最小空闲CU
	MaxBuyCount                 int     //最大购买CVM数量
	BlockedZones                []string
	DisableAutoCleanBlockedZone bool    //关闭自动清理Blocked地域
	MaxCURate                   float64 //最大自动添加CU的比例
	SuppressException           bool    //是否忽略异常
}

func (o *UniformConfig) IsBlockedZone(zone string) (blocked bool) {
	for _, blockedZone := range o.BlockedZones {
		if blockedZone == zone {
			return true
		}
	}
	return false
}

func (o *UniformConfig) CleanBlockedZone() {
	o.BlockedZones = make([]string, 0)
	return
}

func (o *UniformConfig) AddBlockedZone(zone string) {
	if len(o.BlockedZones) == 0 {
		o.BlockedZones = make([]string, 0)
	}

	for _, blockedZone := range o.BlockedZones {
		if blockedZone == zone {
			return
		}
	}
	o.BlockedZones = append(o.BlockedZones, zone)
	return
}

func (o *ClusterGroup) GetUniformConfig() (config *UniformConfig, err error) {
	config = &UniformConfig{}
	if len(o.UniformConfig) > 0 {
		err = json.Unmarshal([]byte(o.UniformConfig), config)
	}
	return
}

func (o *ClusterGroup) GetUniformCalcInfo() (calcInfo *UniformCalcInfo, err error) {
	calcInfo = &UniformCalcInfo{}
	if len(o.UniformCalcInfo) != 0 {
		err = json.Unmarshal([]byte(o.UniformCalcInfo), calcInfo)
	}
	return
}

func (o *ClusterGroup) GetSupportedZones() (supportedZones []string, err error) {
	supportedZones = make([]string, 0)
	supportedZones = append(supportedZones, o.Zone)

	tmpSupportedZones := make([]string, 0)
	if len(o.SupportedZones) != 0 {
		err = json.Unmarshal([]byte(o.SupportedZones), &tmpSupportedZones)
	}
	supportedZones = append(supportedZones, tmpSupportedZones...)
	return
}

func (o *ClusterGroup) IsSupportZone(zone string) (support bool, err error) {
	supportedZones, err := o.GetSupportedZones()
	if err != nil {
		return false, err
	}
	for _, supportedZone := range supportedZones {
		if supportedZone == zone {
			return true, nil
		}
	}
	return false, nil
}

func (o *ClusterGroup) AddSupportedZone(zone string) (err error) {
	supportedZones := make([]string, 0)
	if len(o.SupportedZones) != 0 {
		err = json.Unmarshal([]byte(o.SupportedZones), &supportedZones)
	}
	for _, supportedZone := range supportedZones {
		if supportedZone == zone {
			return
		}
	}
	supportedZones = append(supportedZones, zone)
	sz, err := json.Marshal(supportedZones)
	if err != nil {
		return err
	}
	o.SupportedZones = string(sz)
	return
}

func (o *ClusterGroup) GetDiskInfo() (diskType string, diskSize int64, err error) {
	diskSize = int64(constants.TKE_WORKER_NODE_DISK_SIZE)
	diskType = constants.TKE_CVM_DISK_TYPE
	ok, w := service.WhiteListValue(int64(o.AppId), constants.WHITE_LIST_CLUSTER_CONFIG)
	if !ok {
		return
	}

	cw, err := w.GetClusterWhiteList(o.SerialId)
	if err != nil {
		return
	}
	if cw.DiskType != "" {
		diskType = cw.DiskType
	}
	if cw.DiskSize > 0 {
		diskSize = cw.DiskSize
	}
	return
}

func (o *ClusterGroup) GetWorkSpec() (spec string, err error) {
	ok, w := service.WhiteListValue(int64(o.AppId), constants.WHITE_LIST_CLUSTER_CONFIG)
	if !ok {
		return
	}

	cw, err := w.GetClusterWhiteList(o.SerialId)
	if err != nil {
		return
	}
	return cw.WorkSpec, nil
}
