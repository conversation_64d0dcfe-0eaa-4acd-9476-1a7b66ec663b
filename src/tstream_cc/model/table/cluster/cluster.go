package table

import (
	"encoding/json"
)

/**
 *
 * <AUTHOR>
 * @time 2019/4/26
 * @version 0.4.0
 * @since 0.4.0
 * @see
 */
type Cluster struct {
	Id                    int64
	UniqClusterId         string
	ClusterGroupId        int64
	CreatorUin            string
	Zone                  string
	VpcId                 string
	SubnetId              string
	SupportedZoneSubnets  string
	VpcCIDR               string
	RoleType              int8 // 集群角色，1：Active，2：Standby, -1：Decommissioning，-2：Decommissioned
	SchedulerType         int8 // 调度器类型，1：EMR，2：K8S
	CuNum                 int16
	UsedCuNum             int16
	DefaultCOSBucket      string
	CreateTime            string
	UpdateTime            string
	StopTime              string
	Remark                string
	KubeConfig            string
	WebUIPrefix           string
	ClusterConfig         string
	FlinkConfig           string // flink.conf配置
	FlinkVersion          string // 集群默认的Flink 版本
	ClsLogSet             string
	ClsTopicId            string
	LogConfig             string
	DefaultLogCollectConf string // 集群默认日志采集方式配置
	SupportedFeatures     string
	SupportedFlinkVersion string // 集群支持的Flink 版本格式为 json 的 []string
	LogMode               int8   // 集群使用的日志模式：0：旧版logListener； 1：新版logListener； 2：COS
	ClusterExtendConfig   string // json format.
	LoadBalanceId         string
	WebUIType             int // 默认是 0 公网访问 1 内网访问

	MemRatio           int8
	Cores              int8
	CrossTenantEniMode int8 // 跨租户ENI模式，0：老集群，1：新集群 "StaticMode":true
	RouteConfig        string
}

func (o *Cluster) GetClusterConfig() (clusterConfig map[string]interface{}, err error) {
	clusterConfig = make(map[string]interface{})
	if len(o.ClusterConfig) == 0 {
		return
	}
	err = json.Unmarshal([]byte(o.ClusterConfig), &clusterConfig)
	return
}

func (o *Cluster) GetSupportedFeatures() (supportedFeatures []string, err error) {
	supportedFeatures = make([]string, 0)
	if len(o.SupportedFeatures) == 0 {
		return
	}
	err = json.Unmarshal([]byte(o.SupportedFeatures), &supportedFeatures)
	return
}

func (o *Cluster) ContainsFeature(feature string) bool {
	features, err := o.GetSupportedFeatures()
	if err != nil {
		return false
	}
	if len(features) > 0 {
		for _, item := range features {
			if item == feature {
				return true
			}
		}
	}
	return false
}

func (o *Cluster) GetSupportedFlinkVersion() (vers []string) {
	vers = make([]string, 0)
	if len(o.SupportedFlinkVersion) == 0 {
		return []string{o.FlinkVersion}
	}
	err := json.Unmarshal([]byte(o.SupportedFlinkVersion), &vers)
	if err != nil {
		return []string{o.FlinkVersion}
	}
	return vers
}

func (o *Cluster) GetClusterExtendConfig() (clusterExtendConfig map[string]interface{}, err error) {
	clusterExtendConfig = map[string]interface{}{}
	if len(o.ClusterExtendConfig) == 0 {
		return
	}
	err = json.Unmarshal([]byte(o.ClusterExtendConfig), &clusterExtendConfig)
	return
}

func (o *Cluster) GetSupportedZoneSubnets() (supportedZoneSubnets map[string]string, err error) {
	supportedZoneSubnets = make(map[string]string)
	if len(o.SupportedZoneSubnets) != 0 {
		err = json.Unmarshal([]byte(o.SupportedZoneSubnets), &supportedZoneSubnets)
	}
	supportedZoneSubnets[o.Zone] = o.SubnetId
	return
}

func (o *Cluster) AddSupportedZoneSubnets(zone string, subnet string) (err error) {
	supportedZoneSubnets := make(map[string]string)
	if len(o.SupportedZoneSubnets) != 0 {
		err = json.Unmarshal([]byte(o.SupportedZoneSubnets), &supportedZoneSubnets)
	}
	supportedZoneSubnets[zone] = subnet
	szs, err := json.Marshal(supportedZoneSubnets)
	if err != nil {
		return err
	}
	o.SupportedZoneSubnets = string(szs)
	return
}

func (o *Cluster) GetRouteConfig() (routeConfig []string, err error) {
	routeConfig = make([]string, 0)
	if len(o.RouteConfig) == 0 {
		return
	}
	err = json.Unmarshal([]byte(o.RouteConfig), &routeConfig)
	return
}
