package table

/**
 *
 * <AUTHOR>
 * @time 2019/4/26
 * @version 0.4.0
 * @since 0.4.0
 * @see
 */
type JobInstance struct {
	Id          int64
	JobId       int64
	JobConfigId int64
	// TODO 增加字段JmRunningCuNum int8,和数据库保持一致
	TmRunningCuNum     int16
	JmRunningCuNum     int16
	DefaultParallelism int16
	CuMem              int8
	FlinkJobId         string
	ApplicationId      string
	FlinkJobPlan       string
	Status             int8
	CreateTime         string
	UpdateTime         string
	StartTime          string
	StopTime           string
	RunningOrderId     int64
	Zone               string
	ExecNodeGraph      string

	TmTotalCpu float32
	TmTotalMem float32
}
