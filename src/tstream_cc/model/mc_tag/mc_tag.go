package mc_tag

//  {"page":"2","rows":[{"canDelete":0,"tagKey":"a","tagValue":"1"}],"rp":"2","total":3}
type GetTagsResponse struct {
	Page  int              `json:"page,string"`
	Rp    int              `json:"rp,string"`
	Total uint64           `json:"total"`
	Rows  []*TagWithDelete `json:"rows"`
}

type TagWithDelete struct {
	TagKey    string `json:"tagKey"`
	TagValue  string `json:"tagValue"`
	CanDelete int    `json:"canDelete"`
}

// {"page":"1","rows":[{"tagKey":"c","tagValue":"3"},{"tagKey":"b","tagValue":"2"}],"rp":"2","total":3}
type GetTagValuesResponse struct {
	Page  int    `json:"page,string"`
	Rp    int    `json:"rp,string"`
	Total uint64 `json:"total"`
	Rows  []*Tag `json:"rows"`
}

type Tag struct {
	TagKey   string `json:"tagKey"`
	TagValue string `json:"tagValue"`
}

// {"page":1,"rows":[{"resourceId":"cluster-3tzryrow","tagKey":"b","tagKeyMd5":"92eb5ffee6ae2fec3ad71c777531578f","tagValue":"2","tagValueMd5":"c81e728d9d4c2f636f067f89cc14862c"}],"rp":15,"total":1}
type GetResourceTagsByResourceIdsResponse struct {
	Page  int            `json:"page"`
	Rp    int            `json:"rp"`
	Total uint64         `json:"total"`
	Rows  []*TagResource `json:"rows"`
}

type TagResource struct {
	TagKey      string `json:"tagKey"`
	TagValue    string `json:"tagValue"`
	ResourceId  string `json:"resourceId"`
	TagKeyMd5   string `json:"tagKeyMd5"`
	TagValueMd5 string `json:"tagValueMd5"`
}

// {"page":"1","rows":[{"region":"ap-guangzhou","resourceId":"cluster-3tzryrow","resourcePrefix":"cluster","serviceType":"oceanus","tags":[{"tagKey":"b","tagValue":"2"}]}],"rp":"20","total":1}
type GetResourcesByTagsUnionResponse struct {
	Page  int            `json:"page,string"`
	Rp    int            `json:"rp,string"`
	Total uint64         `json:"total"`
	Rows  []*ResourceTag `json:"rows"`
}

type ResourceTag struct {
	ResourceRegion string `json:"region"`
	ResourceId     string `json:"resourceId"`
	ResourcePrefix string `json:"resourcePrefix"`
	ServiceType    string `json:"serviceType"`
	Tags           []*Tag `json:"tags"`
}
