package model

import (
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cluster_admin_protocol"
)

type CheckClusterNetworkReq struct {
	apiv3.RequestBase
	ClusterId string `check:"nullable:false|strlen:[16, 16]"`
	Host      string `check:"nullable:false|strlen:(0, 10000]"`
	Ports     string `check:"nullable:true"`
}

type CheckClusterNetworkRsp struct {
	Ping   bool      `json:"Ping"`
	Telnet []*Telnet `json:"Telnet"`
}
type Telnet struct {
	Port string `json:"Port"`
	Pass bool   `json:"Pass"`
}

type TriggerNetworkTestCARsp struct {
	cluster_admin_protocol.QaeResponse
	Data struct {
		cluster_admin_protocol.ClusterAdminResponse
		Params struct {
			CheckClusterNetworkRsp
		} `json:"params"`
	} `json:"data"`
}

type CheckClusterNetworkCAPara struct {
	Action          int    `json:"action"`
	ClusterId       int64  `json:"clusterId"`
	Params          Params `json:"params"`
	ClusterSerialId string `json:"clusterSerialId"`
}
type Params struct {
	Host  string `json:"Host"`
	Ports string `json:"Ports"`
}
