package sql_gateway

type SqlGatewaySessionReq struct {
	SessionName string            `json:"sessionName"`
	Properties  map[string]string `json:"properties"`
}

type CancelSqlGatewayReq struct {
}

type SqlGatewaySessionRsp struct {
	ErrorMessage string `json:"errorMessage"`
	SessionId    string `json:"sessionHandle"`
}

type SqlGatewayRunStatementRsp struct {
	ErrorMessage      []string `json:"errors"`
	OperationHandleId string   `json:"operationHandle"`
}

type SqlGatewayFetchResultRsp struct {
	JobID         string   `json:"jobID"`
	ErrorMessage  []string `json:"errors"`
	ResultType    string   `json:"resultType"`
	IsQueryResult bool     `json:"isQueryResult"`
	ResultKind    string   `json:"resultKind"`
	Results       struct {
		Columns []struct {
			Name        string `json:"name"`
			LogicalType struct {
				Type     string `json:"type"`
				Nullable bool   `json:"nullable"`
				Length   int64  `json:"length"`
			} `json:"logicalType"`
			Comment string `json:"comment"`
		} `json:"columns"`
		RowFormat string `json:"rowFormat"`
		Data      []struct {
			Kind   string        `json:"kind"`
			Fields []interface{} `json:"fields"`
		} `json:"data"`
	} `json:"results"`
	NextResultUri string `json:"nextResultUri"`
}
