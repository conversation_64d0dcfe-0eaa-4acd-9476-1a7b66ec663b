package sql

import (
	model3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/resource_config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/sql"
)

type SqlServerBaseRsp struct {
	Caller     string `json:"caller"`
	Callee     string `json:"callee"`
	EventID    int64  `json:"eventId"`
	Timestamp  int64  `json:"timestamp"`
	ReturnCode int64  `json:"returnCode"`
	ReturnMsg  string `json:"returnMsg"`
	Data       struct {
		ReturnCode int64  `json:"returnCode"`
		ReturnMsg  string `json:"returnMsg"`
		Params     struct {
			Pass            bool           `json:"pass"`
			ErrorMessage    string         `json:"errorMessage"`
			ErrorCoordinate sql.Coordinate `json:"coordinate"`
			MetaRefs        MetaRefs       `json:"metaRefs"`
		} `json:"params"`
	} `json:"data"`
}

type SqlServerCheckSqlGrammarRsp struct {
	*SqlServerBaseRsp
	Data struct {
		Pass          bool   `json:"pass"`
		Message       string `json:"errorMessage"`
		ErrorCause    string `json:"errorCause"`
		ErrorSolution string `json:"errorSolution"`
		StartRow      uint64 `json:"errStartRow"`
		StartColumn   uint64 `json:"errStartColumn"`
		EndRow        uint64 `json:"errEndRow"`
		EndColumn     uint64 `json:"errEndColumn"`
		Done          bool   `json:"done"`
		JobGraph      struct {
			Edges []struct {
				Source int `json:"source"`
				Target int `json:"target"`
			} `json:"edges"`
			Nodes []struct {
				Description string `json:"description"`
				Id          int    `json:"id"`
				Name        string `json:"name"`
				Parallelism int    `json:"parallelism"`
			} `json:"nodes"`
		} `json:"jobGraph"`
	} `json:"data"`
}

type SqlServerCheckDdlGrammarRsp struct {
	*SqlServerBaseRsp
	Data struct {
		Pass         bool   `json:"pass"`
		ErrorMessage string `json:"errorMessage"`
		StartRow     uint64 `json:"errStartRow"`
		StartColumn  uint64 `json:"errStartColumn"`
		EndRow       uint64 `json:"errEndRow"`
		EndColumn    uint64 `json:"errEndColumn"`
		Done         bool   `json:"done"`
	} `json:"data"`
}

type ResourceLocationForSqlServer struct {
	SerialId string
	model3.ResourceLocationV2
}

type ResourceLocationIntegral struct {
	ResourceSerialId string
	ResourceId       int64
	VersionId        int64
	model3.ResourceLocationV2
}

type SqlServerAnalyzeUserJarRsp struct {
	*SqlServerBaseRsp
	Data struct {
		AnalyzeSuccess        bool     `json:"analyzeSuccess"`
		UserDefinedConnectors []string `json:"userDefinedConnectors"`
		ErrorMessage          string   `json:"errorMessage"`
	} `json:"data"`
}

type SqlServerParseConnectorRsp struct {
	*SqlServerBaseRsp
	Data struct {
		ParseSuccess       bool   `json:"parseSuccess"`
		UserConnectorInfos string `json:"userConnectorInfos"`
		ErrorMessage       string `json:"errorMessage"`
	} `json:"data"`
}

type ConnectorRsp struct {
	Identifier    string `json:"identifier"`
	SupportSource bool   `json:"supportSource"`
	SupportSink   bool   `json:"supportSink"`
	ParseSuccess  bool   `json:"parseSuccess"`
}

type SqlServerRunSqlPreviewRsp struct {
	*SqlServerBaseRsp
	Data struct {
		ErrorMessage  string `json:"errorMessage"`
		ErrorCause    string `json:"errorCause"`
		ErrorSolution string `json:"errorSolution"`
		TaskId        string `json:"taskId"`
		Tables        []struct {
			Name   string `json:"name"`
			Schema struct {
				Columns []struct {
					Name string `json:"name"`
					Type string `json:"type"`
				} `json:"columns"`
			} `json:"schema"`
			Data [][]string `json:"data"`
		} `json:"tables"`
	} `json:"data"`
}

type SqlServerCheckSqlCompatibilityRsp struct {
	*SqlServerBaseRsp
	Data struct {
		Message       string `json:"message"`
		Done          bool   `json:"done"`
		Compatibility string `json:"compatibility"`
	} `json:"data"`
}

type SqlServerFetchSqlPreviewRsp struct {
	*SqlServerBaseRsp
	Data struct {
		ErrorMessage  string `json:"errorMessage"`
		ErrorCause    string `json:"errorCause"`
		ErrorSolution string `json:"errorSolution"`
		TaskId        string `json:"taskId"`
		Tables        []struct {
			Name   string `json:"name"`
			Schema struct {
				Columns []struct {
					Name string `json:"name"`
					Type string `json:"type"`
				} `json:"columns"`
			} `json:"schema"`
			Data [][]string `json:"data"`
		} `json:"tables"`
		End bool `json:"end"`
	} `json:"data"`
}

type SqlStopFetchSqlPreviewRsp struct {
	*SqlServerBaseRsp
	Data struct {
		ErrorMessage string `json:"errorMessage"`
	} `json:"data"`
}
