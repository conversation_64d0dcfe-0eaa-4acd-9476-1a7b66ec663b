package draft

import (
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model"
	model2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/log"
)

type ModifyDraftReq struct {
	AppId  int32  `check:"nullable:false|range:(0,INF)"`
	Region string `check:"nullable:true|strlen:(0,100)"`

	JobId              string                    `check:"nullable:false|strlen:[12,12]"`
	EntrypointClass    string                    `check:"nullable:true|strlen:[0,200]"`
	ProgramArgs        string                    `check:"nullable:true|strlen:[0,1000000]"`
	Remark             string                    `check:"nullable:true|strlen:[0,1000]"`
	Properties         []*model.Property         `check:"nullable:true"`
	DefaultParallelism int16                     `check:"nullable:true|range:[0, 2048]"`
	ResourceRefs       []*model2.ResourceRefItem `check:"nullable:true"`
	UserType           int8                      `check:"nullable:true"`

	Uin           string `json:"Uin"`
	SubAccountUin string `json:"SubAccountUin"`

	COSBucket      string `check:"nullable:true|strlen:[0, 100]"`
	LogCollect     bool
	LogCollectType int8   // 日志采集类型 2：CLS；3：COS
	LogLevel       string // 日志级别

	JobManagerSpec  float32
	TaskManagerSpec float32

	ClsLogsetId   string // 作业配置中的日志集Id
	ClsTopicId    string // 作业配置中的日志主题Id
	WorkSpaceId   string
	IsSupOwner    int64 // 是否是超管
	Action        string
	PythonVersion string

	EsServerlessIndex       string // es索引
	EsServerlessSpace       string
	ExpertModeOn            bool
	ExpertModeConfiguration *model2.ExpertModeConfiguration `json:"ExpertModeConfiguration,omitempty"`

	AutoRecover int               // Oceanus 平台恢复作业开关 1:开启 -1: 关闭
	ClazzLevels []*log.ClazzLevel // 修改类日志级别

	TraceModeConfiguration *model2.TraceModeConfiguration
	TraceModeOn            bool // Trace链路功能
	CheckpointRetainedNum  int8

	JobGraph     *model2.JobGraph `json:"JobGraph,omitempty"`
	FlinkVersion string
	Version      int16

	JobManagerCpu  float32
	JobManagerMem  float32
	TaskManagerCpu float32
	TaskManagerMem float32
	LibConfig      string
}
