package draft

import (
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model"
	model2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_config"
)

type DescribeJobConfigsRsp struct {
	RequestId    string
	TotalCount   int
	JobConfigSet []*JobConfigSetItem
}

type JobConfigSetItem struct {
	JobId              string
	EntrypointClass    string
	ProgramArgs        string
	Version            int16
	CreateTime         string
	Remark             string
	DefaultParallelism int16
	Properties         []*model.Property
	ResourceRefDetails []*model2.ResourceRefDetail
	COSBucket          string `check:"nullable:true|strlen:[0, 100]"`
	LogCollect         int
	JobManagerSpec     float32
	TaskManagerSpec    float32
}
