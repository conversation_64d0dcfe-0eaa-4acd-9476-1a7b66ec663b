package model

import "tencentcloud.com/tstream_galileo/src/tstream_cc/model"

const (
	CDB_API_DOMAIN_INTERNAL = "cdb.internal.tencentcloudapi.com"

	CDB_STATUS_CREATING  = 0
	CDB_STATUS_RUNNING   = 1
	CDB_STATUS_ISOLATING = 4
	CDB_STATUS_ISOLATED  = 5
	CDB_STATUS_DELETED   = -2

	CDB_QCLOUD_API_URL     = "https://cdb.api.qcloud.com/v2/index.php"                //测试域名
	CDB_TENCENTYUN_API_URL = "https://cdb.api.tencentyun.com/v2/index.php"            //内网域名
	CDB_API_URL_V3_INNER   = "https://cdb.ap-guangzhou-internal.tencentcloudapi.com/" //v3内网域名
	CDB_API_URL_V3_TEST    = "https://cdb.ap-guangzhou.api.tencentyun.com/"

	CDB_API_GET_CDB_INSTANCE_ACTION            = "DescribeCdbInstances"
	CDB_API_GET_CDB_INSTANCE_ACTION_V3         = "DescribeDBInstances"
	CDB_API_GEN_CDB_INSTANCE_ACTION            = "CreateCdbHour"        //V2
	CDB_API_GEN_CDB_INSTANCE_PACKAGE_ACTION    = "CreateCdb"            //包年包月 V2
	CDB_API_Inquiry_Price_ACTION               = "InquiryCdbPrice"      //包年包月V2
	CDB_API_Inquiry_Price_Hour_ACTION          = "InquiryCdbPriceHour"  //包年包月V2
	CDB_API_Inquiry_Price_V3                   = "DescribeDBPrice"      //V3询价
	CDB_API_GEN_CDB_INSTANCE_ACTION_V3         = "CreateDBInstanceHour" //V3 购买按量
	CDB_API_GEN_CDB_INSTANCE_PACKAGE_ACTION_V3 = "CreateDBInstance"
	CDB_API_ISOLATE_CDB_INSTANCE_V3            = "IsolateDBInstance"
	CDB_API_INITIATE_CDB_INSTANCE_V3           = "InitDBInstances"

	CDB_API_INIT_CDB_INSTANCE_ACTION             = "CdbMysqlInit"
	CDB_API_DESTROY_CDB_INSTANCE_ACTION          = "CloseCdbHour" //V2
	CDB_API_DESTROY_CDB_INSTANCE_ACTION_V3       = "IsolateDBInstance"
	CDB_API_MODIFY_CDB_INSTANCE_NAME_ACTION      = "ModifyCdbInstanceName"
	CDB_API_MODIFY_CDB_INSTANCE_NAME_ACTION_V3   = "ModifyDBInstanceName"
	CDB_API_MODIFY_CDB_INSTANCE_PARAMS_ACTION    = "ModifyCdbParams" //v2
	CDB_API_MODIFY_CDB_INSTANCE_PARAMS_ACTION_V3 = "ModifyInstanceParam"
	CDB_API_AUTO_RENEW_ACTION                    = "SetCdbAutoRenew" //v2
	CDB_API_AUTO_RENEW_ACTION_V3                 = "ModifyAutoRenewFlag"
	CDB_API_MODIFY_PROJECT                       = "ModifyCdbInstanceProject" //V2
	CDB_API_MODIFY_PROJECT_V3                    = "ModifyDBInstanceProject"

	CDB_API_NORMAL_ERROR = -1 //系统内部错误，可以透传给前端

	CDB_API_TYPE           = "custom"
	CDB_API_ENGINE_VERSION = "8.0"
	CDB_API_INSTANCE_RO    = "master"

	CDB_INSTANCE_MEM    = int64(4000) //4GB内存
	CDB_INSTANCE_VOLUME = int64(100)  //100GB磁盘

	/**实例状态:0 机器未发起申请,1机器申请中,2 机器已经生产成功，-1 机器下线,-2 机器已退换',*/
	CDB_STATUS_NOT_APPLY   = 0
	CDB_STATUS_IN_APPLY    = 1
	CDB_STATUS_WATING_INIT = 2
	CDB_STATUS_NORMAL      = 3
	CDB_STATUS_OFFLINE     = -1
	CDB_STATUS_REPLACE     = -2
	CDB_STATUS_ISOLATION   = -3
)

type GenCdbInstanceRsp struct {
	Code     int
	Message  string
	CodeDesc string
	DealIds  []string
	Data     GenCdbInstanceRspData
}
type GenCdbInstanceRspData struct {
	DealIds        []string
	CdbInstanceIds []string
}
type InitCdbInstanceRsp struct {
	Code     int
	Message  string
	CodeDesc string
	DealIds  []string
	JobId    int64
}

// ModifyCdbInstanceName V2返回值
type ModifyCdbInstanceNameRsp struct {
	Code     int
	Message  string
	CodeDesc string
}

// ModifyDBInstanceName V3 返回值
type ModifyCdbInstanceNameRspV3 struct {
	Error     *model.ApiError
	RequestId string
}

type ModifyInstanceParamRspV3 struct {
	Error          *model.ApiError
	RequestId      string
	AsyncRequestId string
}

type SetCdbAutoReneweRsp struct {
	Code     int
	Message  string
	CodeDesc string
}

type ModifyAutoRenewRsp struct {
	Error     *model.ApiError
	RequestId string
}

type DestroyCdbInstanceRsp struct {
	Code     int
	Message  string
	CodeDesc string
	DealIds  []string
	Data     DestroyCdbInstanceRspData
}
type DestroyCdbInstanceRspData struct {
	CdbInstanceIds []DestroyCdbInstanceRspDataUnit
	InstanceRole   string
}
type DestroyCdbInstanceRspDataUnit struct {
	Code          int
	Message       string
	CdbInstanceId string
}

type GetCdbInstanceInfoRsp struct {
	Code           int
	Message        string
	CodeDesc       string
	TotalCount     int
	CdbInstanceSet []*CdbInstanceInfo
}

type CdbInstanceInfo struct {
	UInstanceId string
	//废弃,CdbInstanceId		string
	InitFlag                int
	CdbInstanceType         int //1-主示例，2灾备
	StorageSize             int //磁盘大小
	MaxQueryCount           int
	CdbInstanceName         string
	CdbInstanceVip          string
	CdbInstanceVport        int
	CdbWanStatus            int    //外网访问状态
	CdbWanDomain            string //外网访问域名
	CdbWanPort              int
	Status                  int    //0-创建中1-运行中4-删除中5-隔离中101-锁定中
	TaskStatus              int    //0-没有任务1-升级中2-数据导入中3-开放Slave中4-外网访问开通中5-批量操作执行中6-回档中7-外网访问关闭中8-密码修改中9-实例名修改中10-重启中12-自建迁移中13-删除库表中14-灾备实例创建同步中
	EngineVersion           string //5.6,5.5
	CdbInstanceCreateTime   string
	CdbInstanceDeadlineTime string
	CdbTypeSet              string
	CdbType                 string //custom
	Memory                  int    //MB
	Volume                  int    //GB
	AutoRenew               int
	ZoneId                  int64
	VpcId                   int64
	SubnetId                int64
	ProjectId               int64
	PayType                 int
	//masterInfo				int
	//roInfo					//只读实例信息
	//drInfo					//灾备实例信息
}

// 对应表cluster_cdb_info
type ClusterCdbInfo struct {
	Id               int64  `json:"id"`
	AppId            int64  `json:"appId"`
	ClusterId        int64  `json:"clusterId"`
	Flag             int    `json:"flag"`
	InstallPath      string `json:"installPath"`
	Password         string `json:"password"`
	OrderNo          string `json:"orderNo"`
	InstanceId       string `json:"instanceId"`
	InstanceName     string `json:"instanceName"`
	InitFlag         int    `json:"initFlag"`
	Ip               string `json:"ip"`
	Port             int    `json:"port"`
	WanStatus        int    `json:"wanStatus"`
	WanDomain        string `json:"wanDomain"`
	WanPort          int    `json:"wanPort"`
	Memsize          int64  `json:"memsize"`
	Volume           int64  `json:"volume"`
	RegionId         int64  `json:"regionId"`
	MaxQueryCount    int64  `json:"maxQueryCount"`
	ZoneId           int64  `json:"zoneId"`
	Status           int    `json:"cdbStatus"` //0-创建中1-运行中4-删除中5-隔离中101-锁定中
	EngineVersion    string `json:"engineVersion"`
	PayType          int    `json:"payType"`
	Applytime        string `json:"applytime"`
	Freetime         string `json:"freetime"`
	Addtime          string `json:"addtime"`
	ExpireTime       string `json:"expireTime"`
	UserName         string `json:"userName"`
	SyncToWoodpecker int    `json:"syncToWoodpecker"`
	MetaType         int    `json:"metaType"`
}

type CdbAccessInfo struct {
	Ip         string
	Port       int
	UserName   string
	Password   string
	InstanceId string
}

type QueryCdbPriceApiResult struct {
	Code     int
	Message  string
	CodeDesc string
	Price    int64
}

type CdbWebViewInfo struct {
	InstanceName string `json:"instanceName"`
	InstanceId   string `json:"instanceId"`
	Ip           string `json:"ip"`
	Port         int    `json:"port"`
	Memsize      int64  `json:"memsize"`
	Volume       int64  `json:"volume"`
	Service      string `json:"service"`
	ExpireTime   string `json:"expireTime"`
	ApplyTime    string `json:"applyTime"`
	PayType      int    `json:"payType"`
	ExpireFlag   bool   `json:"expireFlag"`
	Status       int    `json:"status"`
	IsAutoRenew  int    `json:"isAutoRenew"`
	SerialNo     string `json:"serialNo"`
	ZoneId       int64  `json:"zoneId"`
	RegionId     int64  `json:"regionId"`
}

// DescribeDBPrice V3返回
type DescribeDBPriceApiResult struct {
	Error         *model.ApiError
	RequestId     string
	OriginalPrice int64
	Price         int64
}

// CreateDBInstanceHour/CreateDBInstance V3返回值
type CreateHourCDBRspV3 struct {
	Error       *model.ApiError
	DealIds     []string
	InstanceIds []string
	RequestId   string
}

type GetCdbInstanceInfoRspV3 struct {
	Error      *model.ApiError
	TotalCount int
	Items      []*CdbInstanceInfoV3
	RequestId  string
}

type BaseCdbResp struct {
	Error     *model.ApiError
	RequestId string
}

// DescribeDBInstances V3返回值 https://cloud.tencent.com/document/api/236/15878#InstanceInfo
type CdbInstanceInfoV3 struct {
	AutoRenew     int
	CdbError      int
	CreateTime    string
	DeadlineTime  string
	DeployMode    int
	EngineVersion string
	InitFlag      int
	InstanceId    string
	InstanceName  string
	InstanceType  int
	Cpu           int
	ResourceId    string
	RegionId      int
	RegionName    string
	Qps           int
	Region        string
	Vip           string
	Vport         int
	WanStatus     int
	WanDomain     string
	WanPort       int
	Status        int // 0-创建中1-运行中4-删除中5-隔离中
	TaskStatus    int
	IsolateTime   string
	DeviceType    string
	Memory        int
	Volume        int
	ZoneId        int64
	Zone          string
	ZoneName      string
	VpcId         int64
	SubnetId      int64
	UniqVpcId     string
	UniqSubnetId  string
	ProjectId     int64
	PayType       int
	ProtectMode   int
	BackupZoneId  int
	ExClusterId   string
	OfflineTime   string
	HourFeeStatus int
	PhysicalId    string
}

type IsolateCDBInstanceRspV3 struct {
	Error          *model.ApiError
	AsyncRequestId string
	RequestId      string
}
