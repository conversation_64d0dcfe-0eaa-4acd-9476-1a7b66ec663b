package cluster_master_protocol

/**
 *
 * <AUTHOR>
 * @time 2019/4/26
 * @version 0.4.0
 * @since 0.4.0
 * @see
 */
type RunJobParams struct {
	RequestId                  string
	OldJobRuntimeId            int64
	JobRuntimeId               int64
	JobRuntimeZone             string
	EntrypointClass            string
	JarFilePath                string
	MaxParallelism             int16
	DefaultParallelism         int16
	COSBucket                  string
	ProgramArgs                string
	ShipFiles                  string
	DynamicProperties          string
	CuMem                      int8
	IsRestart                  bool
	IsRestartWithSavepoint     bool
	IsResume                   bool
	ExternalizedCheckpointPath string // Watchdog 恢复作业专用
	LocalizationResources      string
	SavepointDirectory         string // Restart With Savepoint 专用
	SchedulerType              int    // 1 yarn 2 tke
	JobRunningOrderId          int64  // 作业是第几次run, 与JobInstance的RunningOrderId一致
	FlinkVersion               string
	UserDependencyFiles        string // 依赖资源分发专用
	SavepointPath              string //Savepoint管理启动
	SavepointId                int64  //Savepoint管理启动, 目前已经废弃
	LogCOSBucket               string
	StateCOSBucket               string
}

/**
IsRestart == true
	IsRestartWithSavepoint = false
	IsRestartWithSavepoint = true
IsRestart == false
	IsResume = false
	IsResume = true
*/
