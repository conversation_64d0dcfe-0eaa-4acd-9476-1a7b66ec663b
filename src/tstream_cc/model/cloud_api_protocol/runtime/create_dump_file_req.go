package runtime

import "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"

type CreateDumpFileReq struct {
	apiv3.RequestBase
	JobId       string
	WorkSpaceId string
	PodNames    []string
}

const FlinkPodMainContainer = "flink-main-container"

// flink 1.11 的main container
const Flink1_11PodMainContainer = "flink-task-manager"

const ThreadDumpCmd = "rm -rf /opt/flink/log/thread-dump-*.txt && jstack -l  $(jps -lv |grep flink | awk '{print $1}') > /opt/flink/log/thread-dump-%d.txt"
const HeapDumpCmd = "rm -rf /opt/flink/log/heap-dump-*.hprof && jmap -dump:format=b,file=/opt/flink/log/heap-dump-%d.hprof  $(jps -lv |grep flink | awk '{print $1}')"
const Upload2CosCmd = "[ -f /upload-cos.sh ] && /upload-cos.sh -t dump2cos -e '--include .*.hprof|.*.txt'"
