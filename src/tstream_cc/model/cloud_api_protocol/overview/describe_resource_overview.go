package overview

import "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"

type DescribeResourceOverviewReq struct {
	apiv3.RequestBase
}

type DescribeResourceOverviewRsp struct {
	CUOverview      *CUOverview
	ClusterOverview *ClusterOverview
	JobOverview     *JobOverview
}

type CUOverview struct {
	TotalCount     int
	FreeCount      int
	FreeCountFloat float32
}

type ClusterOverview struct {
	RunningCount          int
	IsolatedInstanceCount int
	DueInstanceCount      int
}

type JobOverview struct {
	RunningJobCount int
	StopJobCount    int
	PauseJobCount   int
}
