package debug

import "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"

type DescribeDebugJobReq struct {
	apiv3.RequestBase
	JobId                   string           `check:"nullable:false|strlen:[12, 12]"`
	DebugId			        int64			 `check:"nullable:false"`
}

type DescribeDebugJobResp struct {
	Status 		int    `json:"status"`
	ReturnCode 	int    `json:"returnCode"`
	ReturnMsg   string `json:"returnMsg"`
}
