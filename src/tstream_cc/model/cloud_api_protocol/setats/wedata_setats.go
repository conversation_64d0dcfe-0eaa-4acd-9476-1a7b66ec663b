package setats

import "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"

// https://write.woa.com/document/130992657738792960
type CreateEngineJobReq struct {
	apiv3.RequestBase
	InstanceId      string
	JobType         string
	JobContent      string
	JobUUID         string
	ExecuteUser     string
	ExecutePassword string
	ResourcePath    string
	Parameters      []*ExtParameter
	ExtParameters   []*ExtParameter
}

type BatchTaskReq struct {
	AppId       int64
	JobUUID     string
	EngineJobId string
	InstanceId  string
}

type ExtParameter struct {
	Name  string
	Value string
}

type CreateEngineJobRsp struct {
	EngineJobId     string
	EngineJobLogUrl string
}

type DescribeEngineJobStateReq struct {
	apiv3.RequestBase
	InstanceId  string
	EngineJobId string
}

type DescribeEngineJobStateRsp struct {
	State      int64  // 任务当前的状态，0：初始化 1：任务运行中 2：任务执行成功 -1：任务执行失败 -3：用户手动终止
	Percentage int64  // 任务执行进度num/100(%)示例值：100
	CostTime   int64  // 计算耗时，单位： ms示例值：42
	CreateTime string // 任务创建时间，时间戳示例值：1632991895728
}

type DescribeEngineJobLogReq struct {
	apiv3.RequestBase
	InstanceId  string
	EngineJobId string
	Offset      int64
	Limit       int64
}

type DescribeEngineJobLogRsp struct {
	Logs   []string
	Offset int64
	Limit  int64
}

type DescribeEngineJobResultReq struct {
	apiv3.RequestBase
	InstanceId  string
	EngineJobId string
	Offset      int64
	Limit       int64
}

type DescribeEngineJobResultRsp struct {
	EngineJobResultType  int64                      // 0:同步返回；1：异步文件形式返回
	EngineJobResultMeta  *EngineJobResultMeta       // 结果集通用元数据信息
	EngineJobResult      *EngineJobResult           // 查询的任务执行数据结果，同步结果返回
	EngineJobAsyncResult *EngineJobAsyncResult      // 查询的任务执行数据结果，异步结果返回
	JumpInfos            []*EngineJobResultJumpInfo // 引擎任务跳转信息列表
}

type EngineJobResultJumpInfo struct {
	JumpUrl string // 跳转链接
	JumpTag string //跳转的标签信息，标识类型
	Name    string // 跳转UI的中文名称
	EnName  string // 跳转UI的英文名称
}

type EngineJobResultMeta struct {
	SQL          string    // 当前执行的SQL示例值：select * from test limit 10;
	CostTime     int64     // 计算耗时，单位： ms示例值：42
	CreateTime   string    // 任务创建时间，时间戳示例值：1632991895728
	ResultSchema []*Column // 结果的schema信息 注意：此字段可能返回 null，表示取不到有效值。
	TotalCount   int64     // 数据总条数
}

type Column struct {
	Name string // 列名
	Type string // 列类型
}

type EngineJobAsyncResult struct {
	EngineJobResultPath       string                     // 大数据量时数据存储COS，工具层直接从COS下载，默认格式CSV
	ResultSetCosBucket        string                     // COS存储bucket name
	ResultSetCosObjectKey     string                     // COS存储object key，对象键（Key）是对象在存储桶中的唯一标识。
	Charset                   string                     // 结果集编码
	EngineJobResultAccessAuth *EngineJobResultAccessAuth // 异步结果集文件访问认证信息，一般为临时密钥

}

type EngineJobResultAccessAuth struct {
	EngineJobResultAccessId           string // 访问共享存储id
	EngineJobResultAccessKey          string // 访问共享存储秘钥
	EngineJobResultAccessSessionToken string // 访问临时token
	EngineJobResultAccessExpiryTime   int64  // 访问临时密钥过期时间，单位秒，例如60*60*2=7200代表两小时
}

type EngineJobResult struct {
	ResultSet string //结果信息，反转义后，外层数组的每个元素为一行数据 示例值：[["3","kk"],["3","kk"]]
	Charset   string // 结果集编码
}

type DeleteEngineJobReq struct {
	apiv3.RequestBase
	InstanceId  string
	EngineJobId string
}

type DeleteEngineJobRsp struct {
	State int64 // kill结果：-3：已终止；  -4： kill中；
}
