package setats

import (
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/tag"
)

// Param 定义参数结构体
type Param struct {
	ParamName         string // 参数名称
	ParamValue        string // 参数值
	ReferenceValue    string // 参考值
	ModificationGuide string // 修改指南
	IsRestart         int    // 是否需要重启 0, 1
}

// SetatsParams 定义包含参数列表的结构体
type ModifySetatsConfigurationReq struct {
	apiv3.RequestBase
	ClusterGroupSerialId string   // 集群组序列ID
	SetatsSerialId       string   // Setats序列ID
	ParamList            []*Param // 参数列表
}

type RestartSetatsReq struct {
	apiv3.RequestBase
	ClusterGroupSerialId string // 集群组序列ID
	SetatsSerialId       string // Setats序列ID
}

type RestartSetatsRsp struct {
}

type CreateSetatsReq struct {
	apiv3.RequestBase
	ClusterGroupSerialId     string // 集群组序列ID
	MasterCpu                float32
	MasterMem                float32
	MasterDiskType           string
	MasterDiskSize           int
	WorkerCpu                float32
	WorkerMem                float32
	WorkerDiskType           string
	WorkerDiskSize           int
	WorkerDefaultParallelism int
	Region                   string
	Zone                     string
	Status                   int8 // 1 创建中 2：运行中，3：停止， 4 配置开启中 5 重启中 6 开启失败
	Tags                     []*tag.Tag
}

type CreateSetatsRsp struct {
	FlowId         int64
	ClusterGroupId int64
	ClusterId      int64
	SetatsSerialId string
}

type ScaleSetatsReq struct {
	apiv3.RequestBase
	ClusterGroupSerialId     string // 集群组序列ID
	WorkerDefaultParallelism int
	MasterDiskSize           int
	WorkerDiskSize           int
}

type ScaleSetatsRsp struct {
	FlowId         int64
	ClusterGroupId int64
	ClusterId      int64
	SetatsSerialId string
}

type ModifySetatsConfigurationRsp struct {
}

type DescribeSetatsConfigurationReq struct {
	apiv3.RequestBase
	ClusterGroupSerialId string // 集群组序列ID
	SetatsSerialId       string // Setats序列ID
}

type DescribeSetatsConfigurationRsp struct {
	ParamList []*Param // 参数列表
}

// ResourceRef 表示资源引用的结构体
type ResourceRef struct {
	ResourceId  string
	Version     int64
	Type        int
	Status      int // 1 -1
	WorkspaceId string
	Name        string
}

// SetatsLocation 表示 Setats 的位置及相关信息
type ModifySetatsWarehouseReq struct {
	apiv3.RequestBase
	ClusterGroupSerialId string         // 例如 cluster-xxxxx
	SetatsSerialId       string         // 例如 setats-xxx
	Location             string         // 例如 cos
	CatalogType          string         // 例如 hadoop
	Uri                  string         // COS uri, 例如 cosn://test-1257058945 HDFS namenode：例如 HDFS1018500
	WarehouseUrl         string         //
	Authentication       string         // 例如 kerberos
	ResourceRefs         []*ResourceRef // 资源引用列表
	Properties           []*model.Property
	HiveUri              string // thrift://172.28.22.142:7004,thrift://172.28.22.207:7004
}

type ModifySetatsWarehouseRsp struct {
}

type DeleteSetatsReq struct {
	apiv3.RequestBase
	ClusterGroupSerialId string // 集群组序列ID
}

type DeleteSetatsRsp struct {
	apiv3.RequestBase
	ClusterGroupSerialId string // 集群组序列ID
}
