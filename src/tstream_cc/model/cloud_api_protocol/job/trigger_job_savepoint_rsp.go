package model

import "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cluster_admin_protocol"

type TriggerJobSavepointRsp struct {
	SavepointTrigger   bool   `json:"savepointTrigger"`
	ErrorMsg           string `json:"errorMsg"`
	FinalSavepointPath string `json:"finalSavepointPath"`
	SavepointId        string `json:"savepointId"`
}

type TriggerJobSavepointCARsp struct {
	cluster_admin_protocol.QaeResponse
	Data struct {
		cluster_admin_protocol.ClusterAdminResponse
		Params struct {
			FinalSavepointPath string `json:"finalSavepointPath"`
			ErrorMsg           string `json:"errorMsg"`
		} `json:"params"`
	} `json:"data"`
}
