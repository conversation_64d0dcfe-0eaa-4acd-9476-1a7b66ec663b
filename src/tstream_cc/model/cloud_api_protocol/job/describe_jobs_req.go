package model

import (
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"
)

/**
 *
 * <AUTHOR>
 * @time 2019/4/26
 * @version 0.4.0
 * @since 0.4.0
 * @see
 */
type DescribeJobsReq struct {
	apiv3.RequestBase

	JobIds []string
	/**
	Name        作业名称 模糊
	Status      作业状态
	ClusterId   集群id
	JobId       作业id
	ClusterName 集群名称 模糊
	*/
	Filters []struct {
		Name   string
		Values []string
	}
	Offset           int    `check:"nullable:true|range:(0, INF)"`
	Limit            int    `check:"nullable:true|range:(0, 100]"`
	WorkSpaceId      string //授权的命名空间Id
	ExtraResult      []string
	OrderByList      []OrderBy
	CallType         int    // 查询场景，默认是 0 通用场景， 1 是 运维管理场景，有些信息不需要渲染，提高接口速度
	ConnectorOptions string // 查询使用内置connector的作业

}

type OrderBy struct {
	Field string
	Type  string // asc, desc
}
