package model

import "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"

// DescribeTreeJobsReq 查询作业树请求
type DescribeTreeJobsReq struct {
	apiv3.RequestBase
	// 检索， 筛选
	Filters []struct {
		Name   string
		Values []string
	}
	WorkSpaceId string
	NewMode     int64  // 模式 0:走老的逻辑不分页 1:走新的分页逻辑
	Category    string // 分类类型: TreeCategory, 默认folderName
	ParentId    string // 父节点id
	PageAttach  string // 分页滚动透传参数
	PageSize    int64  // 页大小
}

// TreeCategory 作业数分类
type TreeCategory string

// 作业数分类枚举
const (
	// TreeCategoryFolderName 目录
	TreeCategoryFolderName TreeCategory = "folderName"
	// TreeCategoryClusterName 集群名字
	TreeCategoryClusterName TreeCategory = "clusterName"
	// TreeCategoryCreateTime 创建时间(天)
	TreeCategoryCreateTime TreeCategory = "createTime"

	// TreeCategoryNoop 默认兜底的category
	TreeCategoryNoop TreeCategory = "noop"
)

var treeCategoryMap = map[string]TreeCategory{
	"folderName":  TreeCategoryFolderName,
	"clusterName": TreeCategoryClusterName,
	"createTime":  TreeCategoryCreateTime,
	"noop":        TreeCategoryNoop,
}

// ParseTreeCategory 字符串转换
func ParseTreeCategory(s string) (TreeCategory, bool) {
	c, ok := treeCategoryMap[s]
	return c, ok
}

// TreeFilterKey 过滤类型
type TreeFilterKey string

// 过滤key枚举
const (
	TreeFilterKeyJobName       TreeFilterKey = "JobName"
	TreeFilterKeyJobId         TreeFilterKey = "JobId"
	TreeFilterKeyClusterName   TreeFilterKey = "ClusterName"
	TreeFilterKeyClusterId     TreeFilterKey = "ClusterId"
	TreeFilterKeyFolderName    TreeFilterKey = "FolderName"
	TreeFilterKeySqlKeyword    TreeFilterKey = "SqlKeyword"
	TreeFilterKeyJobType       TreeFilterKey = "JobType"
	TreeFilterKeyJobStatus     TreeFilterKey = "JobStatus"
	TreeFilterKeyZone          TreeFilterKey = "Zone"
	TreeFilterKeyExceptionType TreeFilterKey = "ExceptionType"
)

var treeFilterMap = map[string]TreeFilterKey{
	"JobName":       TreeFilterKeyJobName,
	"JobId":         TreeFilterKeyJobId,
	"ClusterName":   TreeFilterKeyClusterName,
	"ClusterId":     TreeFilterKeyClusterId,
	"FolderName":    TreeFilterKeyFolderName,
	"SqlKeyword":    TreeFilterKeySqlKeyword,
	"JobType":       TreeFilterKeyJobType,
	"JobStatus":     TreeFilterKeyJobStatus,
	"Zone":          TreeFilterKeyZone,
	"ExceptionType": TreeFilterKeyExceptionType,
}

// ParseTreeFilterKey 字符串转换
func ParseTreeFilterKey(s string) (TreeFilterKey, bool) {
	k, ok := treeFilterMap[s]
	return k, ok
}

// 异常类型枚举
const (
	ExceptionTypeAlarm  = "alarm"
	ExceptionTypeEvent  = "event"
	ExceptionTypeNormal = "normal"
)
