package model

import "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"

type DescribeJobsExceptionReq struct {
	apiv3.RequestBase

	// 检索， 筛选
	Filters []struct {
		Name   string
		Values []string
	}

	WorkSpaceId string

	// 异常的筛选范围
	StartTimestamp uint64 // 筛选条件：起始 Unix 时间戳（秒）
	EndTimestamp   uint64 // 筛选条件：结束 Unix 时间戳（秒）
	IsAsync        int    `json:"IsAsync"` // 0 同步 1 异步
	AsyncTaskId    string `json:"AsyncTaskId"`
}
