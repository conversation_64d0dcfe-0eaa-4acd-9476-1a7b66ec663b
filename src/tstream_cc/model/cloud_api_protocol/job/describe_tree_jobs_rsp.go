package model

// DescribeTreeJobsRsp 查询作业树回包
// 只有新的分页模式, 才会返回分页相关参数
type DescribeTreeJobsRsp struct {
	ParentId   string                 // 父节点id
	Id         string                 // 文件夹ID
	Name       string                 // 文件夹名
	JobSet     []*TreeJobSets         // 作业列表
	Children   []*DescribeTreeJobsRsp // 子目录列表
	RequestId  string
	PageAttach string // 分页滚动参数
	HasMore    bool   // 是否还有更多数据
	JobNum     int64  // 当前目录的作业数量 (当前目录作业数量 + 子目录的作业数量)
}

// TreeJobSets 作业详情
type TreeJobSets struct {
	JobId string
	Name  string

	JobType        int8
	RunningCu      float32
	Status         int8
	ScalingType    int8
	RunningCpu     float32
	RunningMem     float32
	DecodeSqlCode  string
	ClusterId      string
	ClusterName    string
	AppId          int64
	OwnerUin       string
	CreatorUin     string
	CreateTime     string
	StartTime      string
	StopTime       string
	UpdateTime     string
	TotalRunMillis int64
	IsAlarm        bool   // 近一天是否有云监控报警
	AlarmPolicyId  string // 云监控的告警策略Id
	IsEvent        bool   // 近6个小时是否有事件异常
}
