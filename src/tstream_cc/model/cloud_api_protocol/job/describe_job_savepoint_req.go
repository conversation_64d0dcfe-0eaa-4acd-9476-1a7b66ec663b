package model

import (
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"
)

type DescribeJobSavepointReq struct {
	apiv3.RequestBase
	JobId       string `json:"jobId"`
	Limit       int64  `json:"limit"`
	Offset      int64  `json:"offset"`
	WorkSpaceId string
	RecordTypes []int8 // 2 checkpoint 1 触发savepoint 3 停止触发的savepoint
}

type DescribeJobSavepointCAPara struct {
	Action       int64                      `json:"action"`
	JobType      int8                       `json:"jobType"`
	AppId        int64                      `json:"appId"`
	ClusterId    int64                      `json:"clusterId"`
	JobId        int64                      `json:"jobId"`
	JobRuntimeId int64                      `json:"jobRuntimeId"`
	Params       DescribeJobSavepointParams `json:"params"`
}

type DescribeJobSavepointParams struct {
	Limit  int64 `json:"limit"`
	Offset int64 `json:"offset"`
}
