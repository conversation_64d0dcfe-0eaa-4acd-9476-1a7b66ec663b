package model

import (
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/setats"
	tableItemSpace "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/item_space"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/tag"
)

/**
 *
 * <AUTHOR>
 * @time 2019/4/26
 * @version 0.4.0
 * @since 0.4.0
 * @see
 */
type DescribeClustersRsp struct {
	TotalCount int
	ClusterSet []*ClusterSetItem
}

type ClusterSetItem struct {
	ClusterId  string
	Name       string
	Region     string
	Zone       string
	AppId      int32
	OwnerUin   string
	CreatorUin string

	Type               int8
	CuNum              int16
	CuMem              int8
	Status             int8
	StatusDesc         string
	CreateTime         string
	UpdateTime         string
	Remark             string
	CCNs               []*CCN
	NetEnvironmentType int8
	DefaultCOSBucket   string
	LogCOSBucket       string
	FreeCuNum          int16
	FreeCu             float32 // for fine grained resource
	RunningCu          float32 // for eks
	PayMode            int     // 0 后付费,1 预付费

	ClusterSessions []*ClusterSessionRsp

	Tags []*tag.Tag

	//SchedulerType 	int8	// 调度器类型，1：EMR，2：TKE

	IsolatedTime       string
	ExpireTime         string
	SecondsUntilExpiry string
	AutoRenewFlag      int
	CLSLogSet          string
	CLSLogName         string
	CLSTopicId         string
	CLSTopicName       string
	Version            *Version // 版本信息

	DefaultLogCollectConf string // 集群默认日志采集配置
	Correlations          []*tableItemSpace.ItemSpaceClusterItem
	CustomizedDNSEnabled  int // 0 : not set, 1 : has set, 2 : not supported
	IsNeedManageNode      int //  前端区分 集群是否需要2CU逻辑 因为历史集群 变配不需要, default 1  新集群都需要
	Orders                []*Order
	ClusterType           int // 0:TKE, 1:EKS
	ArchGeneration        int // V3版本 = 2
	WebUIType             int // WebUIType
	SqlGateways           []*SqlGatewayItem
	SubEks                *SubEks

	AgentSerialId       string //统一资源池的母集群
	ResourceType        int    //资源类型
	BillingResourceMode string // 默认 "" 包销模式 "exclusiveSale"
	MemRatio            int8   // 默认是 4   其他 2（1/2） 8（1/8）
	CrossTenantEniMode  int8
	RunningMem          float32
	RunningCpu          float32
	TotalCpu            float32
	TotalMem            float32
	Yarns               []*HadoopYarnItem
	Setats              *SetatsInfo
	DeploymentMode      int // 0 单可用区 1多可用区
	SlaveZones          []*SlaveZone
}

type SlaveZone struct {
	VpcId    string
	SubnetId string
	Zone     string
}

type SetatsInfo struct {
	SetatsSerialId     string
	Status             int8
	Warehouse          *SetatsWarehouse
	MasterInfo         *SetatsCvmInfo
	WorkerInfo         *SetatsCvmInfo
	Tags               []*tag.Tag
	ExpireTime         string
	SecondsUntilExpiry string
	AutoRenewFlag      int
	CreateTime         string
	ManagerUrl         string
	IsolatedTime       string
	MetaUrl            string // setats hive metastore mysql ip:port
	MetaUser           string // 用户名
	MetaPass           string // 密码
	VpcId              string
	SubnetId           string
}

type SetatsCvmInfo struct {
	Cpu                float32
	Mem                float32
	Disk               *SetatsDisk
	DefaultParallelism int
}

type SetatsDisk struct {
	DiskType string
	DiskSize int
}

type SetatsWarehouse struct {
	Status          int8
	Location        string                // 例如 cos
	CatalogType     string                // 例如 hadoop
	Uri             string                //
	WarehouseUrl    string                //
	Authentication  string                // 例如 kerberos
	ResourceRefs    []*setats.ResourceRef // 资源引用列表
	HiveUri         string
	Properties      []model.Property
	HiveCatalogType int // 0 hive 1 内置hive
}

type SubEks struct {
	SerialId   string
	CuNum      int16
	Status     int8
	StatusDesc string
	RunningCu  float32
	RunningMem float32
	RunningCpu float32
	TotalCpu   float32
	TotalMem   float32
}

type CCN struct {
	VpcId    string
	SubnetId string
	CcnId    string
	//AppId    int32
	//OwnerUin string
}

type Version struct {
	Flink          string
	SupportedFlink []string
}

type Order struct {
	Type          int    //创建、续费、扩缩容 1 2 3 //CategoryId
	AutoRenewFlag int    //是否自动续费
	OperateUin    string //操作的人
	ComputeCu     int    //CU
	OrderTime     string //操作的时间 //CreateTime
}

type DescribeSupportZonesRsp struct {
	SupportZones []SupportZone
}

type SupportZone struct {
	Zone string
	Role int // 0 主 1 备
}
