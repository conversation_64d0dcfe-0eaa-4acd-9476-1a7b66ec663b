package model

import "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"

type DescribeClusterDNSReq struct {
	apiv3.RequestBase

	ClusterId string
}

type DescribeClusterDNSRsp struct {
	Hosts  []*CoreFileHost
	Blocks []*CoreFileBlock
}

func (o *DescribeClusterDNSRsp) Emtpy() bool {
	return len(o.Hosts) == 0 && len(o.Blocks) == 0
}

func (o *DescribeClusterDNSRsp) FixNilSlice() {
	if o.Hosts == nil {
		o.Hosts = make([]*CoreFileHost, 0, 0)
	}
	if o.Blocks == nil {
		o.Blocks = make([]*CoreFileBlock, 0, 0)
	}
}

// ----------------------------------------------------------------------------

type CoreFileHost struct {
	IP   string
	Host string
}

type CoreFileBlock struct {
	Zone      string
	ForwardTo []string
}
