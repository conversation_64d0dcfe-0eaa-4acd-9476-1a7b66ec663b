package model

import "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"

type CheckClusterScaleDownReq struct {
	apiv3.RequestBase
	ClusterId   string `check:"nullable:false|strlen:[16, 16]"`
	TargetCuNum int16
	Offset      int `check:"nullable:true|range:(0, INF)"`
	Limit       int `check:"nullable:true|range:(0, 100]"`
}

type CheckClusterScaleDownResp struct {
	TotalCount     int
	InfluencedJobs []*InfluencedJob
}

type InfluencedJob struct {
	JobId       string
	JobType     int8
	JobName     string
	WorkSpaceId string
}
