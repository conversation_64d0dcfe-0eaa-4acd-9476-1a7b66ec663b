package model

import (
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/login_settings"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/tag"
)

/**
 *
 * <AUTHOR>
 * @time 2019/4/26
 * @version 0.4.0
 * @since 0.4.0
 * @see
 */
type CreateClusterReq struct {
	AppId         int64
	Uin           string
	SubAccountUin string
	RequestId     string
	Region        string
	Version       string

	Name             string `check:"nullable:false|strlen:(0, 50]"`
	Zone             string `check:"nullable:false|strlen:(0, 50)"`
	CuNum            int16  `check:"nullable:false|range:(0, INF)"`
	CuMem            int8   `check:"nullable:true|enum:2,4,8,16"`
	Remark           string `check:"nullable:true|strlen:[0, 100]"`
	DefaultCOSBucket string `check:"nullable:false|strlen:(0, 100)"`
	VPCDescriptions  []VpcDescription

	NetEnvironmentType int8                          `check:"nullable:true|enum:1,2,3"`
	LoginSettings      *login_settings.LoginSettings `json:"LoginSettings,omitempty"`
	Tags               []*tag.Tag                    `json:"Tags,omitempty"`
	CLSLogSet          string                        `check:"nullable:false|strlen:(0, 100)"`
	CLSTopicId         string                        `check:"nullable:false|strlen:(0, 100)"`
	OrderOrigin        int                           `check:"nullable:true:enum:0,1"` // 订单来源：0（默认）控制台; 1 点石
	ClusterType        int8

	SupportedFeatures []string
	ArchGeneration    int
	NetEniType        int

	// 集群类型 3：表示统一资源池的母集群, 其他：表示创建独享集群
	// 如果创建独享集群，可以指定母集群, 如果没有找到母集群，随机选择一个母集群
	// 如果创建统一资源池母集群，并且指定AgentSerialId，表示开启一个新的地域
	ClusterGroupType int8
	AgentSerialId    string
	ResourceType     int8   //共享集群，资源类型 0:独立使用资源 1:共享使用资源
	UniformOwnerUin  string // 共享集群所属用户
	// 共享集群，包年包月支持按量付费模式，需要传递 包年包月集群Id
	ParentSerialId       string
	MemRatio             int8             // 默认4 CU（1核4G） 2 CU（1核2G）8 CU（1核8G）
	Cores                int8             // 16C机型1:4 32C机型1:4 64C机型1:4
	SlaveVpcDescriptions []VpcDescription // 多可用区
}

type VpcDescription struct {
	VpcId    string
	SubnetId string
	AppId    int32
	OwnerUin string
	Zone     string
}
