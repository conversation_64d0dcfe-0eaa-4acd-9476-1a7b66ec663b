package model

import (
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"
)

/**
 *
 * <AUTHOR>
 * @time 2019/4/26
 * @version 0.4.0
 * @since 0.4.0
 * @see
 */
type DescribeClustersReq struct {
	apiv3.RequestBase

	ClusterIds []string
	Filters    []struct {
		Name   string
		Values []string
	}
	Offset      int    `check:"nullable:true|range:(0, INF)"`
	Limit       int    `check:"nullable:true|range:(0, 100]"`
	OrderType   int    //0按照时间排序，1按照状态排序
	WorkSpaceId string // 授权的命名空间ID
}

type DescribeSupportZonesReq struct {
	apiv3.RequestBase
}
