package subnet

import (
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"
)

type ModifySubnetReq struct {
	apiv3.RequestBase
	ClusterId   string `check:"nullable:false|strlen:[16, 16]"`
	VpcId       string `check:"nullable:false"`
	OldSubnetId string `check:"nullable:false"`
	NewSubnetId string `check:"nullable:false"`
	Type        int    // 默认0 正常修改， 1 是轮训查询
	Zone        string
}

type Subnet struct {
	AppId     int64  `json:"AppId"`
	Uin       string `json:"Uin"`
	UniqVpcId string `json:"UniqVpcId"`
	SubnetId  string `json:"SubnetId"`
	ENILimit  int32  `json:"ENILimit"`
	Business  string `json:"Business"`
}

type ModifySubnetRsp struct {
	Result bool
}
