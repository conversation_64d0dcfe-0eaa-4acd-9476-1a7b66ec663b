package model

import (
	"encoding/json"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/resource_config"
)

type ParamType struct {
	Bucket string
	Path   string
	Region string
}

type ResourceLocation struct {
	StorageType int
	Param       *ParamType
}

type ResourceLocationV2 struct {
	StorageType      int
	ResourceType     int //1: jar , 2: config file
	Param            *ParamType
	LocalizationPath string //配置文件下载目录
}

// 兼容旧数据
func NewResourceLocationFromReq(config *table.ResourceConfig) (*ResourceLocation, error) {
	loc := config.ReqResourceLoc
	resourceLoc := &ResourceLocation{}
	if len(config.ReqResourceLoc) == 0 {
		loc = config.ResourceLoc
	}
	err := json.Unmarshal([]byte(loc), resourceLoc)
	return resourceLoc, errorcode.InternalErrorCode_MarshalFailed.NewWithErr(err)
}

// 只获取oceanus的ResourceLoc
func NewResourceLocation(config *table.ResourceConfig) (*ResourceLocation, error) {
	loc := config.ResourceLoc
	resourceLoc := &ResourceLocation{}
	err := json.Unmarshal([]byte(loc), resourceLoc)
	return resourceLoc, errorcode.InternalErrorCode_UnMarshalFailed.NewWithErr(err)
}
