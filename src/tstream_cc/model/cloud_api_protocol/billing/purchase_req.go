package billing

import "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"

type PurchaseReq struct {
	apiv3.RequestBase

	TimeSpan    int32  /*时间长度*/
	TimeUnit    string /*表示时间单位*/
	ZoneId      int32  //购买可用区ID，没有可用区概念则传对应区域的一区，例如广州一区100001
	RegionId    int32  //购买区域ID，见地域表
	CU          int    //
	ClusterName string

	LoginPassword    string             `json:"loginPassword"` // 密码 (Base64)
	Remark           string             `json:"remark"`        // 集群描述
	DefaultCOSBucket string             `json:"defaultCOSBucket"`
	CLSLogSet        string             // cls日志集id
	CLSTopicId       string             // cls日志id
	VpcDescriptions  []*VpcDescriptions `json:"vpcDescriptions"`

	AcceptNonFineGrainedResource bool `json:"acceptNonFineGrainedResource"` //是否支持细粒度

	GoodsNum int32

	ResourceId  string //续费专用，待续费的资源id
	CurDeadline string //续费专用，原资源到期时间

	ModifyMode string //变配专用，down为降配
	NewCU      int    //变配专用
	OldCU      int    //变配专用

	ResourceIdList []string //退费专用,要退费的资源列表
	DealName       string   //退费专用，订单号

	EventId int32 `json:"eventId"`

	Environment   int8  `json:"environment"` //1为测试，2为现网
	AutoRenewFlag int32 /*续费方式*/
	IsSetats      int
}
