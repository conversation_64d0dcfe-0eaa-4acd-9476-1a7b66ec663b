package metric

type DescribeMetricProxyRsp struct {
	Period     *uint64           `json:"Period,omitempty" name:"Period"`
	MetricName *string           `json:"MetricName,omitempty" name:"<PERSON>ricName"`
	StartTime  *string           `json:"StartTime,omitempty" name:"StartTime"`
	EndTime    *string           `json:"EndTime,omitempty" name:"EndTime"`
	DataPoints []*DataPointProxy `json:"DataPoints,omitempty" name:"DataPoints"`
	// Msg        *string      `json:"Msg,omitempty" name:"Msg"`
	RequestId      *string `json:"RequestId,omitempty" name:"RequestId"`
	AdditionalInfo *string `json:"AdditionalInfo,omitempty" name:"AdditionalInfo"`
}

func EmptyMetricProxyRsp() *DescribeMetricProxyRsp {
	return &DescribeMetricProxyRsp{
		DataPoints: make([]*DataPointProxy, 0),
	}
}

type DataPointProxy struct {

	// 实例对象维度组合
	Dimensions []*Dimension `json:"Dimensions,omitempty" name:"Dimensions"`

	// 时间戳
	Timestamps []*float64 `json:"Timestamps,omitempty" name:"Timestamps"`

	// 数据点列表
	Values []*float64 `json:"Values,omitempty" name:"Values"`
}

type AdditionalInfo struct {
	InstanceMap interface{} `json:"InstanceMap,omitempty" name:"InstanceMap"`
}
