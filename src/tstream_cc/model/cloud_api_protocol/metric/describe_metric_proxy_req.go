package metric

import "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"

type DescribeMetricProxyReq struct {
	apiv3.RequestBase
	ClusterId         string
	MetricName        string
	Namespace         string
	Period            int64
	Instances         []*Instance
	StartTime         string
	EndTime           string
	SpecifyStatistics int64
	MetricType        string
}

type Instance struct {
	Dimensions []*Dimension
}

const (
	SetatsMetric = "setats"
)
