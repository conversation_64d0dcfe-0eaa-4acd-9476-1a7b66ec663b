package cost

import (
	v20180724 "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/monitor/v20180724"
	tat "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/tat/v20201028"
	tke "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/tke/v20180525"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"
)

type UpgradeCdbReq struct {
	apiv3.RequestBase
	SerialId string
	Memory   int64
	Volume   int64

	CanDown    bool
	DryUpgrade bool
}

type UpgradeCdbResp struct {
	OldMemory int64
	OldVolume int64
}

type DescribeCanUpgradeMysqlReq struct {
}

type DescribeCanUpgradeMysqlResp struct {
}

type DescribeRegionSetReq struct {
}

type DescribeRegionSetResp struct {
	RegionSet []*string
}

type ModifySchedulerPolicyReq struct {
	apiv3.RequestBase
	SerialId   string
	Priorities []*Priority
}

type EnableClusterAuditReq struct {
	apiv3.RequestBase
	SerialId string
}

type ModifyClusterOpenPolicyReq struct {
	apiv3.RequestBase
	SerialId string
}

type EnableClusterEventPersistenceReq struct {
	apiv3.RequestBase
	SerialId string
}

type EnableClusterEventPersistenceResp struct {
}

type Priority struct {
	Name   string
	Weight int64
}

type PriorityErr struct {
	Name string
	Err  string
}

type ModifySchedulerPolicyResp struct {
}

type DescribeResourceOwnerReq struct {
	apiv3.RequestBase
	//Tke、Cvm（ins-xxx/np-xxx）、Oceanus集群Id、Oceanus作业Id
	ResourceId string
}

type DescribeResourceOwnerResp struct {
	ResourceId string
	ClusterId  string
	TkeId      string
	OwnerUin   string
	OwnerAppId int32
	ErrorMsg   string
}

type BuyControllerReq struct {
	apiv3.RequestBase
	SerialId string
	Count    int64
	Spec     string
	Force    bool
	Zone     string //共享集群母集群需要指定区，不然在默认区增加
}

type BuyControllerResp struct {
	Count int64
}

type UpgradeControllerReq struct {
	apiv3.RequestBase
	SerialId string
}

type UpgradeControllerResp struct {
	FlowId int64
}

type BuyWorkerReq struct {
	apiv3.RequestBase
	SerialId     string
	Count        int64  //购买的数量，当数量<=0，自动计算需要的数量
	DiskSize     int64  //磁盘大小
	DiskType     string ////磁盘类型，CLOUD_BSSD
	ForceVersion int    //当自动计算节点数量时候，强制计算对应集群版本需要的worker数量
	Spec         string //指定规格，默认自动计算规格
	Unschedule   bool
	Force        bool   //强制购买
	DryBuy       bool   //不购买，只是计算需要的数量
	Zone         string //共享集群母集群需要指定区，不然在默认区增加
	Labels       map[string]string
	Taints       []*tke.Taint
}

type BuyWorkerResp struct {
	Count   int64
	Message string
}

type RunCommandReq struct {
	apiv3.RequestBase
	SerialId      string
	InstanceIdSet []*string
	LabelSet      []*string
	Command       string // base64 command
	DryRun        bool
}

type RunCommandResp struct {
	Count         int
	RespSet       []*tat.RunCommandResponseParams
	InstanceIdSet []*string
	LabelSet      []*string
}

type ModifyDiskTypeReq struct {
	apiv3.RequestBase
	SerialId      string
	InstanceIdSet []*string
	LabelSet      []*string
	DiskType      string // base64 command
	DryRun        bool
}

type ModifyDiskTypeResp struct {
	Count         int
	ErrorSet      []*string
	InstanceIdSet []*string
	LabelSet      []*string
}

type ResizeDiskReq struct {
	apiv3.RequestBase
	SerialId      string
	InstanceIdSet []*string
	LabelSet      []*string
	DiskSize      uint64 // base64 command
	DryRun        bool
}

type ResizeDiskResp struct {
	Count         int
	ErrorSet      []*string
	InstanceIdSet []*string
	LabelSet      []*string
}

type DescribeInvocationTasksReq struct {
	apiv3.RequestBase
	SerialId          string
	InvocationTaskIds []*string
}

type DescribeInvocationTasksResp struct {
	Count   uint64
	RespSet []*tat.InvocationTask
}

type DescribeInvocationsReq struct {
	apiv3.RequestBase
	SerialId      string
	InvocationIds []*string
}

type DescribeInvocationsResp struct {
	Count   uint64
	RespSet []*tat.Invocation
}

type RenewCvmReq struct {
	apiv3.RequestBase
	SerialId string
}

type RenewCvmResp struct {
	Count   int64
	Message string
}

type SchedulerWorkerReq struct {
	apiv3.RequestBase
	SerialId      string
	Scheduler     bool
	InstanceIdSet []*string
	ForceVersion  int  //当自动计算节点时候，强制使用对用的版本
	DryScheduler  bool //不scheduler，只计算不标准的集群
}

type SchedulerWorkerResp struct {
	Count            int64
	InstanceIdSet    []*string
	ErrInstanceIdSet []*string
}

type DeleteUnschedulerWorkerReq struct {
	apiv3.RequestBase
	SerialId      string
	InstanceIdSet []*string
	PoolCvm       bool
	DryScheduler  bool
}

type DeleteUnschedulerWorkerResp struct {
	Count         int64
	InstanceIdSet []*string
	PooledIdSet   []*string
	DeleteIdSet   []*string
	ErrorMsg      string
}

type DeleteUnuseCdbReq struct {
	apiv3.RequestBase
	SerialId     string
	CdbIdSet     []*string
	DryScheduler bool
}

type DeleteUnuseCdbResp struct {
	Count    int64
	CdbIdSet []*string
	ErrorMsg string
}

type UpgradeWorkerReq struct {
	apiv3.RequestBase
	SerialId string
}

type UpgradeWorkerResp struct {
	FlowId int64
}

type DescribeCvmReq struct {
	apiv3.RequestBase
	SerialId string
	DPod     bool // describe pods, jobs and controller pod
}

type DescribeCvmResp struct {
	TotalCount      uint64
	ControllerCount int
	WorkerCount     int
	ControllerSet   []*CvmItem
	WorkerSet       []*CvmItem
	OtherSet        []*CvmItem
	Message         string
}

type CvmItem struct {
	InstanceId    string
	Uuid          string
	InstanceState string
	CvmType       string
	InstanceName  string

	InstanceType string
	SystemDisk   int64
	DataDisk     []*int64

	IP      string
	Labels  []*Label
	Jobs    map[string]struct{}
	Oceanus map[string]struct{}
}

type Label struct {
	Name  string
	Value string
}

type DescribeTkeStatisticDataReq struct {
	apiv3.RequestBase
	SerialId  string
	StartTime string
	EndTime   string
}

type GetJobMonitorDataReq struct {
	apiv3.RequestBase
	JobSet     []*string
	MetricName string
	Period     uint64
	StartTime  string
	EndTime    string
}

type GetJobMonitorDataResp struct {
	Period     uint64
	MetricName string

	// 数据点数组
	DataPoints []*v20180724.DataPoint

	StartTime string

	EndTime string
}

type CreateNetworkInterfaceExReq struct {
	apiv3.RequestBase
	VpcAppId                    uint64
	VpcUin                      string
	VpcSubAccountUin            string
	VpcId                       string
	SubnetId                    string
	NetworkInterfaceName        string
	NetworkInterfaceDescription string
	InstanceId                  string
}

type CreateNetworkInterfaceExResp struct {
	Response string
}

type AttachNetworkInterfaceExReq struct {
	apiv3.RequestBase
	VpcAppId           int64
	VpcUin             string
	VpcSubAccountUin   string
	NetworkInterfaceId string
	InstanceId         string
}

type AttachNetworkInterfaceExResp struct {
	Response string
}

type DescribeVpcTaskResultReq struct {
	apiv3.RequestBase
	VpcAppId         uint64
	VpcUin           string
	VpcSubAccountUin string
	TaskId           string
}

type DescribeVpcTaskResultResp struct {
	Response string
}

type DescribeNetworkInterfacesExReq struct {
	apiv3.RequestBase
	VpcAppId         int64
	VpcUin           string
	VpcSubAccountUin string

	VpcId                string
	SubnetId             string
	AttachmentInstanceId string
	NetworkInterfaceId   string
	NetworkInterfaceName string
}

type DescribeNetworkInterfacesExResp struct {
	Response string
}

type DeleteNetworkInterfaceExReq struct {
	apiv3.RequestBase
	VpcAppId           uint64
	VpcUin             string
	VpcSubAccountUin   string
	VpcId              string
	NetworkInterfaceId string
}

type DeleteNetworkInterfaceExResp struct {
	Response string
}
