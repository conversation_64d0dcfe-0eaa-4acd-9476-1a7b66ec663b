package draft

import (
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"
	model2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/log"
)

type ModifyDraftReq struct {
	apiv3.RequestBase

	JobId              string                    `check:"nullable:false|strlen:[12,12]"`
	EntrypointClass    string                    `check:"nullable:true|strlen:[0,200]"`
	ProgramArgs        string                    `check:"nullable:true"`
	ProgramArgsAfterGzip string
	Remark             string                    `check:"nullable:true|strlen:[0,1000]"`
	Properties         []*model.Property         `check:"nullable:true"`
	DefaultParallelism int16                     `check:"nullable:true|range:[0, 2048]"`
	ResourceRefs       []*model2.ResourceRefItem `check:"nullable:true"`
	UserType           int8                      `check:"nullable:true"`
	COSBucket          string                    `check:"nullable:true|strlen:[0, 100]"`
	LogCollect         bool
	LogCollectType     int8   // 日志采集类型 2：CLS；3：COS
	LogLevel           string // 日志级别
	JobManagerSpec     float32
	TaskManagerSpec    float32
	ClsLogsetId        string // 作业配置中的日志集Id
	ClsTopicId         string // 作业配置中的日志主题Id
	WorkSpaceId        string
	PythonVersion      string
	AutoRecover        int               // Oceanus 平台恢复作业开关 1:开启 -1: 关闭
	ClazzLevels        []*log.ClazzLevel // 修改类日志级别

	EsServerlessIndex       string // es索引
	EsServerlessSpace       string // es空间id
	ExpertModeOn            bool
	ExpertModeConfiguration *model2.ExpertModeConfiguration `json:"ExpertModeConfiguration,omitempty"`

	TraceModeOn            bool // Trace链路功能
	TraceModeConfiguration *model2.TraceModeConfiguration

	CheckpointIntervalSecond int64 // checkpoint 间隔时间配置
	CheckpointRetainedNum    int8  //checkpoint保存策略
	CheckpointTimeoutSecond  int64

	JobGraph       *model2.JobGraph `json:"JobGraph,omitempty"`
	FlinkVersion   string           // flink 版本
	JobVersion     int16            // 作业版本，草稿默认-1
	JobManagerCpu  float32
	JobManagerMem  float32
	TaskManagerCpu float32
	TaskManagerMem float32
}

type ModifyDraftResp struct {
}
