package model

/**
 *
 * <AUTHOR>
 * @time 2019/4/26
 * @version 0.4.0
 * @since 0.4.0
 * @see
 */
type DescribeJobConfigsReq struct {
	AppId         int32
	Uin           string
	SubAccountUin string
	RequestId     string
	Region        string
	Version       string

	JobId             string `check:"nullable:false|strlen:[12, 12]"`
	JobConfigVersions []int64
	Filters           []struct {
		Name   string
		Values []string
	}
	Offset      int `check:"nullable:true|range:[0, INF)"`
	Limit       int `check:"nullable:true|range:[0, 100]"`
	OnlyDraft   bool
	WorkSpaceId string
	IsSupOwner  int64 // 是否是超管
	Action      string
}
