package model

import (
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/log"
	model4 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/resource_config"
)

/**
 *
 * <AUTHOR>
 * @time 2019/4/26
 * @version 0.4.0
 * @since 0.4.0
 * @see
 */
type CreateJobConfigReq struct {
	AppId         int32
	Uin           string
	SubAccountUin string
	RequestId     string
	Region        string
	Version       string

	JobId              string `check:"nullable:false|strlen:[12, 12]"`
	EntrypointClass    string `check:"nullable:true|strlen:[0, 500]"`
	ProgramArgs        string `check:"nullable:true"`
	ProgramArgsAfterGzip        string
	Properties         []*model.Property
	Remark             string `check:"nullable:true|strlen:[0, 100]"`
	ResourceRefs       []*ResourceRefItem
	DefaultParallelism int16
	COSBucket          string `check:"nullable:true|strlen:[0, 100]"`
	LogCollect         bool
	LogCollectType     int8 // 日志采集类型 2：CLS；3：COS; 4: ES
	AutoDelete         int32

	JobManagerSpec  float32
	TaskManagerSpec float32

	ClsLogsetId string // 日志集ID
	ClsTopicId  string // 日志主题ID

	EsServerlessIndex string // es索引
	EsServerlessSpace string

	WorkSpaceId string
	IsSupOwner  int64 // 是否是超管
	Action      string

	PythonVersion string            // PyFlink 作业 运行的python版本
	LogLevel      string            // 作业日志级别 DEBUG INFO WARN ERROR
	AutoRecover   int               // Oceanus 平台恢复作业开关 1:开启 -1: 关闭
	ClazzLevels   []*log.ClazzLevel // 修改类日志级别

	ExpertModeOn            bool
	ExpertModeConfiguration *ExpertModeConfiguration `json:"ExpertModeConfiguration"`

	TraceModeOn            bool // Trace链路功能
	TraceModeConfiguration *TraceModeConfiguration

	CheckpointIntervalSecond int64 // checkpoint 间隔时间配置
	CheckpointRetainedNum    int8  // checkpoint保存策略 default 0
	CheckpointTimeoutSecond  int64 // checkpoint 超时时间配置

	JobGraph     *JobGraph `json:"JobGraph,omitempty"`
	FlinkVersion string    // flink 版本
	JobName      string

	JobManagerCpu         float32
	JobManagerMem         float32
	TaskManagerCpu        float32
	TaskManagerMem        float32
	LibConfig             string
	UseOldSystemConnector int // 0=默认使用 1=使用新的
	LogCOSBucket          string `check:"nullable:true|strlen:[0, 100]"`
	StateCOSBucket          string `check:"nullable:true|strlen:[0, 100]"`
}

type ResourceRefItem struct {
	ResourceId string
	Version    int64 // 如果不传则默认用最新版本
	Type       int   // 用户引用资源时候，设置引用类型， 例如主资源
}


type ResourceConfigItem struct {
	Id             int64
	ResourceId     string
	VersionId      int64
	CreateTime     string
	ResourceLoc    *model4.ResourceLocation
	Status         int
	CreatorUin     string
	Type       int
	ResourceName   string
}

type ExpertModeConfiguration struct {
	JobGraph          *JobGraph           `json:"JobGraph,omitempty"`
	NodeConfig        []*NodeConfig       `json:"NodeConfig,omitempty"`
	SlotSharingGroups []*SlotSharingGroup `json:"SlotSharingGroups,omitempty"` // Name 为default 的SlotSharingGroup不需要放这里
}

type NodeConfig struct {
	Id          int
	Parallelism int

	StateTTL string

	SlotSharingGroup string            // 需要跟 SlotSharingGroup.Name 匹配; 默认为 default; 为default可以置空
	Configuration    []*model.Property `json:"Configuration,omitempty"`
}

type SlotSharingGroup struct {
	Name              string // SlotSharingGroup的名字 必须唯一
	Description       string // SlotSharingGroup 的描述
	Spec              SlotSharingGroupSpec
	JobGraph          *JobGraph           `json:"JobGraph,omitempty"`
	NodeConfig        []*NodeConfig       `json:"NodeConfig,omitempty"`
	SlotSharingGroups []*SlotSharingGroup `json:"SlotSharingGroups,omitempty"`
	Configuration     []*model.Property   `json:"Configuration,omitempty"`
}

type SlotSharingGroupSpec struct {
	CPU           float32
	HeapMemory    string // 默认为b, 支持单位有 b, kb, mb, gb
	OffHeapMemory string // 默认为b, 支持单位有 b, kb, mb, gb
	ManagedMemory string // 默认为b, 支持单位有 b, kb, mb, gb
}

type TraceModeConfiguration struct {
	Rate     string // 如1%转换为0.01
	Operator string // 1:OUT,2:IN_AND_OUT,3:IN, 按照算子ID顺序配置，可以对每个算子配置IN、OUT、IN_AND_OUT三个值，分别表示采集输入数据、采集输出数据、同时采集输入和输出数据
}
