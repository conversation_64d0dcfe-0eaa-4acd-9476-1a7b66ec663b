package model

import (
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/log"
)

/**
 *
 * <AUTHOR>
 * @time 2019/4/26
 * @version 0.4.0
 * @since 0.4.0
 * @see
 */
type DescribeJobConfigsRsp struct {
	RequestId    string
	TotalCount   int
	JobConfigSet []*JobConfigSetItem
}

type JobConfigSetItem struct {
	JobId                   string
	EntrypointClass         string
	ProgramArgs             string
	Version                 int16
	CreateTime              string
	Remark                  string
	DefaultParallelism      int16
	MaxParallelism          int16
	COSBucket               string
	CreatorUin              string
	Properties              []*model.Property
	ResourceRefDetails      []*ResourceRefDetail
	UpdateTime              string
	LogCollect              int
	LogLevel                string
	JobManagerSpec          float32
	TaskManagerSpec         float32
	ClsLogsetId             string
	ClsTopicId              string
	EsServerlessIndex       string // es索引
	EsServerlessSpace       string
	IndexName               string `json:"IndexName"`
	WorkspaceName           string `json:"WorkspaceName"`
	PythonVersion           string
	AutoRecover             int               // Oceanus 平台恢复作业开关 1:开启 -1: 关闭
	ClazzLevels             []*log.ClazzLevel // 修改类日志级别
	ExpertModeOn            bool
	ExpertModeConfiguration *ExpertModeConfiguration `json:"ExpertModeConfiguration,omitempty"`

	TraceModeOn            bool // Trace链路功能
	TraceModeConfiguration *TraceModeConfiguration
	CheckpointRetainedNum  int8      //checkpoint保存策略
	JobGraph               *JobGraph `json:"JobGraph,omitempty"`
	FlinkVersion           string    // flink 版本
	JobConfigItem          *JobConfigSetItem

	JobManagerCpu  float32
	JobManagerMem  float32
	TaskManagerCpu float32
	TaskManagerMem float32
}

type ResourceRefDetail struct {
	ResourceId    string
	Version       int64
	Name          string
	Connector     string
	Type          int
	SystemProvide int
	JobConfigId   int64 `json:"-"`
	ResourceIntId int64 `json:"-"`
}
