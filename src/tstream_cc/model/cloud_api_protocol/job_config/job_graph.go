package model

type JobGraph struct {
	Nodes []*Node `json:"Nodes"`
	Edges []*Edge `json:"Edges"`
}

type Node struct {
	Id          int    `json:"Id"`          // node id
	Description string `json:"Description"` // node 描述
	Name        string `json:"Name"`        // node 名称
	Parallelism int    `json:"Parallelism"` // node 并行度
}

type Edge struct {
	Source int `json:"Source"` // 边的起始节点 id
	Target int `json:"Target"` // 边的目标节点 id
}
