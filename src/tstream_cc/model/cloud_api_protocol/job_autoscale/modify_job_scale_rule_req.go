package job_autoscale

import "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"

type ModifyJobScaleRuleReq struct {
	apiv3.RequestBase
	RuleId         string `check:"nullable:false|strlen:[13, 13]"`
	ConditionRatio int    `check:"nullable:false|range:[0, 100]"`
	Threshold      int64  `check:"nullable:false"`
	DurationTime   int    `check:"nullable:false"`
	Step           string `check:"nullable:false|strlen:[0, 500000]"`
	Status         int8   `check:"nullable:false|range:[-1, 2]"`
	ReachLimit     int    `check:"nullable:false|range:[1, 1000]"`
	Properties     string `check:"nullable:true"`
	Configuration  string `check:"nullable:true"`
	WorkSpaceId    string
	RuleName       string
}
