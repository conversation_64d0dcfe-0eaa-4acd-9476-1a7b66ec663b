package job_autoscale

import "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"

type CreateJobScalePlanReq struct {
	apiv3.RequestBase
	JobSerialId string `check:"nullable:false"`

	Name     string `check:"nullable:false"`
	Modifier string `check:"nullable:false"`
	// 默认为 auto_scale_basic 后续新增 auto_master 等
	JobScaleRules []*JobScaleRule `check:"nullable:false"`
	WorkSpaceId   string
}

type JobScaleRule struct {
	ReachLimit    int `check:"nullable:false|range:[1, 1000]"`
	PlanSerialId  string
	RuleSerialId  string
	Configuration string `check:"nullable:false"`
	Status        int
}
