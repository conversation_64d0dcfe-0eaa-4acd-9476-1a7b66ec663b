package sql_gateway

import "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"

type RunSqlGatewayStatementReq struct {
	apiv3.RequestBase

	ClusterId    string
	SessionId    string
	Sql          string
	FlinkVersion string
}

type RunSqlGatewaySessionRsp struct {
	ErrorMessage string
	SessionId    string
}

type LogicalType struct {
	Type     string
	NullAble bool
	Length   int64
}

type Column struct {
	Name        string
	LogicalType *LogicalType
	Comment     string
}

type ResultData struct {
	Kind   string
	Fields []interface{}
}

type StatementResult struct {
	Columns   []*Column
	RowFormat string
	Data      []*ResultData
}

type RunSqlGatewayStatementRsp struct {
	ErrorMessage      []string
	SessionId         string
	OperationHandleId string
}

type FetchSqlGatewayResultReq struct {
	apiv3.RequestBase

	ClusterId         string
	SessionId         string
	OperationHandleId string
	ResultUri         string
	FlinkVersion      string
}

type CancelSqlGatewayStatementReq struct {
	apiv3.RequestBase
	ClusterId         string
	SessionId         string
	OperationHandleId string
	FlinkVersion      string
	JobID             string
}

type FetchSqlGatewayResultRsp struct {
	JobID         string
	ErrorMessage  []string
	ResultType    string
	IsQueryResult bool
	ResultKind    string
	Results       StatementResult
	NextResultUri string
}
