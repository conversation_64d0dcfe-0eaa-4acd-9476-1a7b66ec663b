package model

import "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"

type DescribeDependencyUrlReq struct {
	apiv3.RequestBase
	Expired           uint64 //过期时间，单位为秒
	VersionId         int64
	ResourceId        string `check:"nullable:false"`
	ItemSpaceSerialId string `check:"nullable:false"`
}

type DescribeDependencyUrlResp struct {
	Bucket string
	Region string
	Path   string
	Url    string
}
