package model

import model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/resource_config"

type DescribeResourcesRsp struct {
	TotalCount  int
	ResourceSet []*ResourceItem
}

type ResourceItem struct {
	ResourceId                  string
	Name                        string
	ResourceType                int
	Remark                      string
	ResourceLoc                 *model.ResourceLocation
	Region                      string
	AppId                       int64
	OwnerUin                    string
	CreatorUin                  string
	CreateTime                  string
	UpdateTime                  string
	LatestResourceConfigVersion int64
	VersionCount                int
	RefJobCount                 int
	FileName                    string
	WorkSpaceId                 int64
	Connector                   string // 资源所属的连接器
	ConnectorVersion            string // 资源所属的连接器版本
	ConnectionMethod            string // 资源所属的连接方式
	Icon                        string // 资源所属的图标
	ConnectorName               string // 资源所属的连接器名称
	ConnectorUrl                string // 资源所属的连接器url
	RelatedResourceId           string // connector关联的资源id
	RefJobStatusCountSet        []*model.RefJobStatusCountItem
}
