package model

import (
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"
)

type DescribeCatalogsReq struct {
	apiv3.RequestBase
	WorkSpaceId string
	Filters     []struct {
		Name   string
		Values []string
	} `check:"nullable:true"`
}

type DescribeCatalogsRsp struct {
	Catalogs []*CatalogItem
}

type CatalogItem struct {
	Id              int64
	SerialId        string
	Type            int
	Name            string
	DefaultDatabase string
	Properties      string
	Comment         string
	FlinkVersion    string
}
