package model

import "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/sql"

type DescribeMetaReferencesRsp struct {
	ReferenceTables []*MetaReferenceTable `json:"ReferenceTables,omitempty"`
	Variables       []*MetaTableVariable  `json:"Variables,omitempty"`
	Pass            bool                  `json:"Pass"`
	ErrorMessage    string                `json:"ErrorMessage"`
	ErrorCoordinate sql.Coordinate
}

type MetaReferenceTable struct {
	Catalog  string `json:"Catalog"`
	Database string `json:"Database"`
	Table    string `json:"Table"`
	Version  int64  `json:"Version"`
}

type MetaTableVariable struct {
	Catalog         string                    `json:"Catalog"`
	Database        string                    `json:"Database"`
	Table           string                    `json:"Table"`
	Type            int                       `json:"Type"` // 1: Meta Table 2: Temporal Table 3: CDAS
	VariableEntries []*MetaTableVariableEntry `json:"VariableEntries"`
}

type MetaTableVariableEntry struct {
	Key         string `json:"Key"`
	Placeholder string `json:"Placeholder"`
	Value       string `json:"Value"`
	VariableId  string `json:"VariableId"`
}
