package model

type DescribeMetaTableRelatedJobsRsp struct {
	TableRelatedJobs []*MetaTableRelatedJobsItem
}

type MetaTableRelatedJobsItem struct {
	TableId           string
	JobCount          int
	RunningJobCount   int
	RefJobInfos       []*MetaTableRefJobInfo
}

type MetaTableRefJobInfo struct {
	JobId                       string
	PublishedJobConfigVersion   int64
	JobStatus                   int
}