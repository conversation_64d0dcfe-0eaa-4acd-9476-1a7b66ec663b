package model

import (
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"
)

type DescribeExternalMetaTablesReq struct {
	apiv3.RequestBase
	CatalogId    string `check:"nullable:false"`
	ClusterId    string
	DatabaseName string
	TableName    string
	FlinkVersion string
	WorkSpaceId  string
	Filters      []struct {
		Name   string
		Values []string
	} `check:"nullable:true"`
	// 接口异步化参数
	IsAsync     int    `json:"IsAsync"` // 0 同步 1 异步
	AsyncTaskId string `json:"AsyncTaskId"`
}

type SqlServerDescribeTbsReq struct {
	*SqlServerBaseReq
	Interface struct {
		InterfaceName string `json:"interfaceName"`
		Para          struct {
			FlinkClientVersion string      `json:"flinkClientVersion"`
			Cam                interface{} `json:"cam"`
			ResoureceLocs      interface{} `json:"resoureceLocs"`
			HiveVersion        string      `json:"hiveVersion"`
			CatalogName        string      `json:"catalogName"`
			DefaultDatabase    string      `json:"defaultDatabase"`
			HiveConfDir        string      `json:"hiveConfDir"`
			Database           string      `json:"database"`
			Table              string      `json:"table"`
			Hostname           string      `json:"hostname"`
			Port               int         `json:"port"`
			Username           string      `json:"username"`
			Password           string      `json:"password"`
		} `json:"para"`
	} `json:"interface"`
}

type DescribeMetaExternalTableVpcRsp struct {
	Caller     string `json:"caller"`
	Callee     string `json:"callee"`
	EventID    int64  `json:"eventId"`
	Timestamp  int64  `json:"timestamp"`
	ReturnCode int64  `json:"returnCode"`
	ReturnMsg  string `json:"returnMsg"`
	Data       struct {
		Tables []*Table `json:"tables"`
	} `json:"data"`
	CatalogType int
}

type Table struct {
	TableName       string         `json:"tableName"`
	Database        string         `json:"database"`
	Catalog         string         `json:"catalog"`
	Columns         []*TableSchema `json:"tableSchema"`
	TableProperties string         `json:"properties"`
	TableType       string         `json:"tableType"`
}

type TableSchema struct {
	DataType string `json:"dataType"`
	Name     string `json:"name"`
}

type Table2 struct {
	TableName       string
	Database        string
	CatalogId       string
	Catalog         string
	Columns         []*TableSchema2
	TableProperties string
	TableType       string
}

type TableSchema2 struct {
	DataType string
	Name     string
}

type DescribeExternalMetaTablesRsp struct {
	TablesSetItem []*Table2
	AsyncStatus   int
}

type TableProperties struct {
	Password       string   `json:"password"`
	Connector      string    `json:"connector"`
	TableName      string  `json:"table-name"`
	Url            string   `json:"url"`
	Username       string   `json:"username"`
}
