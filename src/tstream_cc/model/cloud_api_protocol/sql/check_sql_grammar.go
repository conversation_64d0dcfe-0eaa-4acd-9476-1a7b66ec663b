package sql

import (
	model1 "tencentcloud.com/tstream_galileo/src/tstream_cc/model"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_config"
)

type CheckSqlGrammarReq struct {
	apiv3.RequestBase

	SqlCheckId         string
	SqlCode            string
	SqlCodeAfterGzip   string
	ClusterId          string
	JobId              string
	JobConfigId        int64
	ResourceRefs       []*model.ResourceRefItem
	MetaTableVariables []struct {
		Catalog         string `json:"Catalog"`
		Database        string `json:"Database"`
		Table           string `json:"Table"`
		Type            int    `json:"Type"` // 1:Meta Table 2: Temporal Table
		VariableEntries []struct {
			Key         string `json:"Key"`
			Value       string `json:"Value"`
			VariableId  string `json:"VariableId"` // 引用变量的Id（新增）
			Placeholder string `json:"Placeholder"`
		} `json:"VariableEntries"`
	}

	WorkSpaceId          string
	ActionMode           int // 1 : 语法检查 2: 检查结果查询
	Parallelism          int
	ShouldReturnJobGraph bool
	DeepSqlCheckEnable   bool
	Properties           []*model1.Property
	FlinkVersion         string
	IsAsync              int    `json:"IsAsync"` // 0 同步 1 异步
	AsyncTaskId          string `json:"AsyncTaskId"`
}

type CheckSqlGrammarRsp struct {
	Pass            bool
	ErrorMessage    string
	ErrorCause      string
	ErrorSolution   string
	ErrorCoordinate Coordinate
	Done            bool
	JobGraph        *model.JobGraph `json:"JobGraph,omitempty"`
}

type CheckSqlDeepGrammarRsp struct {
	Pass            bool
	ErrorMessage    string
	ErrorCause      string
	ErrorSolution   string
	ErrorCoordinate Coordinate
	Done            bool
	JobGraph        *model.JobGraph `json:"JobGraph,omitempty"`
	AsyncStatus     int
}

type Coordinate struct {
	StartRow    uint64
	StartColumn uint64
	EndRow      uint64
	EndColumn   uint64
}
