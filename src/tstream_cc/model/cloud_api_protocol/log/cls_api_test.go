package log

import (
	"fmt"
	"net/url"
	"testing"
)

func TestSearchLogReq_QueryString(t *testing.T) {
	params := url.Values{}
	params.Add("logset_id", " this.LogSetId")
	params.Add("topic_ids", "this.TopicIds")
	params.Add("start_time", "2022-04-23 23:23:23")
	params.Add("end_time", "this.EndTime")
	params.Add("query", "this.Query")
	params.Add("limit", fmt.Sprintf("%d", 1))
	params.Add("sort", "sort")
	t.Log(params.Encode())
}
