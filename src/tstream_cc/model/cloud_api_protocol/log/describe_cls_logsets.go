package log

import "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"

type DescribeClsLogSetsReq struct {
	apiv3.RequestBase
}

type DescribeClsLogSetsRsp struct {
	TotalCount int64               `json:"TotalCount"`
	LogSets    []*OceanusLogSetDes `json:"LogSets"`
}

type OceanusLogSetDes struct {
	LogSetId     string `json:"LogSetId"`
	LogSetName   string `json:"LogSetName"`
	LogSetPeriod int16  `json:"Period"`
	Selected     bool   `json:"Selected"`
}
