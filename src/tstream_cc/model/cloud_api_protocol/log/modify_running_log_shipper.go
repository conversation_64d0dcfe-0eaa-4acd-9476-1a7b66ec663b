package log

import "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"

type ModifyRunningLogShipperReq struct {
	apiv3.RequestBase
	LogSetId string `json:"LogSetId"`
	TopicId  string `json:"TopicId"`
	Enable   bool   `json:"Enable"`
}

type ModifyRunningLogShipperRsp struct {
	ClusterResults []*ClusterResult `json:"ClusterResults"`
}

type ClusterResult struct {
	ClusterId    string `json:"ClusterId"`
	Success      bool   `json:"Success"`
	ErrorMessage string `json:"ErrorMessage"`
}
