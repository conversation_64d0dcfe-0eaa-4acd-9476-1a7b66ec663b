package log

import "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"

type DescribeClsAuthInfoReq struct {
	apiv3.RequestBase
	ClusterId string `json:"ClusterId"`
}

type DescribeClsAuthInfoRsp struct {
	Token       string `json:"Token"`
	SecretId    string `json:"SecretId"`
	SecretKey   string `json:"SecretKey"`
	ExpiredTime int64  `json:"ExpiredTime"`
}

type Credential struct {
	Token       string `json:"token"`
	SecretId    string `json:"secretId"`
	SecretKey   string `json:"secretKey"`
	ExpiredTime int64  `json:"expiredTime"`
}

type CredentialInfo struct {
	Credential           *Credential `json:"credential"`
	ClusterType          int64       `json:"clusterType"`
	ClusterGroupSerialId string      `json:"clusterGroupSerialId"`
	ClusterId            int64       `json:"clusterId"`
	OwnerUin             string      `json:"ownerUin"`
	Region               string      `json:"region"`
	AgentSerialId        string      `json:"agentSerialId"`
	ZkUser               string      `json:"zkUser"`
	ZkPass               string      `json:"zkPass"`
}
