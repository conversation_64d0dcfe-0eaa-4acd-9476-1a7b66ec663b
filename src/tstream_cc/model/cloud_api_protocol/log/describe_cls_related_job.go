package log

import "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"

// author: Archie<PERSON>ao
// date: 2021/10/11 4:14 下午
// description:

type DescribeClsRelatedJobReq struct {
	apiv3.RequestBase
	ClusterId   string `json:"ClusterId"`
	ClsLogsetId string `json:"ClsLogsetId"`
	ClsTopicId  string `json:"ClsTopicId"`
}

type DescribeClsRelatedJobRsp struct {
	JobInfo []*JobInfo `json:"JobInfo"`
}

type JobInfo struct {
	JobName          string `json:"JobName"`          // 作业名称
	JobId            string `json:"JobId"`            // 作业ID
	JobType          int8   `json:"JobType"`          // 作业类型
	JobRunningStatus int8   `json:"JobRunningStatus"` // 作业运行状态
	Region           string `json:"Region"`           // 所在地域
	WorkSpaceId      string `json:"WorkSpaceId"`
}
