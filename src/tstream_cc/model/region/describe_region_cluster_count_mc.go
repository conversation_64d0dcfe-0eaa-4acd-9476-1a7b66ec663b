package region

type DescribeRegionClusterCountMcReq struct {
	InterfaceName string
	OpenId        string
	OwnerUin      string
	Uin           string
	AppId         int64
	ProjectId     int64
}

type DescribeRegionClusterCountMcRsp struct {
	Nums   int64  `json:"nums"`
	IsOpen int64  `json:"isOpen"`
	Unit   string `json:"unit"`
	Prefix string `json:"prefix"`
}

type ClustersCount struct {
	AppId int64
	Num   int64
}
