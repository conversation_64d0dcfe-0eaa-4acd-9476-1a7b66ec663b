package billing

import "encoding/json"

type QueryDeadlineListReq struct {
	Type           string   `check:"nullable:true" json:"type"`
	Uin            string   `check:"nullable:false" json:"uin"`
	AppId          int64    `check:"nullable:false|range:(0,INF)" json:"appId"`
	Region         int      `check:"nullable:true" json:"region"`
	ResourceIds    []string `check:"nullable:true" | json:"resourceIds"`         // 实例 ID
	ProjectIds     []int32  `check:"nullable:true" json:"projectIds"`            // projectId 数组查询
	SearchValue    string   `check:"nullable:true" json:"searchValue"`           // 可选，用户在搜索框输入的搜索信息，业务方自主决定搜索策略
	AutoRenewFlags []int8   `check:"nullable:true" json:"autoRenewFlags"`        // 续费标记 0:正常续费  1:自动续费 2:到期不续
	DeadlineStart  *string  `check:"nullable:true" json:"deadlineStart"`         // 到期起始时间（yyyy // -MM-dd hh:mm:ss)
	DeadlineEnd    *string  `check:"nullable:true" json:"deadlineEnd"`           // 到期结束时间（yyyy-MM-dd hh:mm:ss）
	OrderBy        *string  `check:"nullable:true|enum:deadline" json:"orderBy"` // 可选排序字段 Enum
	SortBy         *string  `check:"nullable:true|enum:DESC,ASC" json:"sortBy"`  // ASC/DESC 选填排序方式
	PageSize       *int     `check:"nullable:true|range:[0,INF)" json:"pageSize"`
	PageNo         *int     `check:"nullable:true|range:[0,INF)" json:"pageNum"` // 页数，从 0 开始
}

type QueryDeadlineListResp struct {
	TotalCnt  int          `json:"totalCnt"`
	Instances []*Instances `json:"instances"`
}

func (q *QueryDeadlineListResp) String() string {
	if b, err := json.Marshal(q); err != nil {
		return ""
	} else {
		return string(b)
	}
}

type Instances struct {
	Uin               string                  `check:"nullable:false|strlen:(0,INF)" json:"uin"`
	AppId             int64                   `check:"nullable:false|range:(0,INF)" json:"appId"`
	ResourceId        string                  `check:"nullable:false|strlen:(0,INF)" json:"resourceId"`
	Region            int                     `check:"nullable:false" json:"region"`
	ZoneId            int                     `check:"nullable:false|range:(0,INF)" json:"zoneId"`
	AutoRenewFlag     int                     `check:"nullable:true|enum:0,1,2" json:"autoRenewFlag"` // 续费方式：0（默认）正常续费续费；1 自动续费；2 不自动续费
	Type              string                  `check:"nullable:false" json:"type"`                    // 业务名称，categoryId 中的商品码, 子产品标签 sp_oceanus_exclusive
	Status            int                     `check:"nullable:false" json:"status"`                  // 资源状态，1 正常，2 隔离，3 销毁
	ProjectId         int32                   `check:"nullable:true|range:[0,INF)" json:"projectId"`
	IsolatedTimestamp string                  `json:"isolatedTimestamp"` // 隔离时间
	ExpireTime        string                  `json:"expireTime"`
	DeadLine          string                  `json:"deadline"`
	InstanceName      string                  `json:"instanceName"`
	PresentInfo       []*KVPair               `json:"presentInfo,omitempty"` // 如果续费管理页面需要自定义参数，用于续费管理页面定制化参数，详细文档 http://tapd.oa.com/pt_jifei/markdown_wikis/view/#1010140771006794563
	GoodsDetail       *NewResourceGoodsDetail `json:"goodsDetail"`
}

func (i *Instances) String() string {
	if b, err := json.Marshal(i); err != nil {
		return ""
	} else {
		return string(b)
	}
}

type KVPair struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}

func (k *KVPair) String() string {
	if b, err := json.Marshal(k); err != nil {
		return ""
	} else {
		return string(b)
	}
}

type ResourceGoodsDetail struct {
	ProductCode                           string    `json:"productCode,omitempty"`
	SubProductCode                        string    `json:"subProductCode,omitempty"`
	OceanusExclusiveComputeCu             int32     `json:"sv_oceanus_compute_exclusive_cu"` // 独享模式-CU, 单位 vCPU
	Pid                                   int       `json:"pid"`
	TimeSpan                              int       `json:"timeSpan,omitempty"`
	TimeUnit                              string    `json:"timeUnit,omitempty"` // 必传字段 商品的时间单位，计费周期，y：年，m：月，d：日，(一次性购买传p) 业务侧不需要额外添加字段表示时间
	ResourceId                            string    `json:"resourceId"`
	CurDeadline                           string    `json:"curDeadline,omitempty"` // 旧配置到期时间，YYYY-MM-DD HH:MM:SS， 预付费 变配必传
	ProductInfo                           []*KVPair `json:"productInfo,omitempty"`
	BillingResourceMode                   string    `json:"billingResourceMode"` // "exclusiveSale"
	ResourceOperationMode                 string    `json:"resourceOperationMode"`
	Duration                              string    `json:"duration"`
	OceanusExclusiveComputeUnderwriteCu14 int       `json:"sv_oceanus_compute_underwrite_cu1c4g"` // 包销计费-CU（1核4G）
	OceanusExclusiveComputeCu12           int       `json:"sv_oceanus_compute_exclusive_cu1c2g"`  // 独享模式-CU（1核2G）
	OceanusExclusiveComputeCu18           int       `json:"sv_oceanus_compute_exclusive_cu1c8g"`  // 独享模式-CU（1核8G）
	OceanusCloudPremium                   int       `json:"sv_oceanus_storage_cloud_premium"`     // 高性能云盘 CLOUD_PREMIUM
	OceanusCloudBssd                      int       `json:"sv_oceanus_storage_balanced_ssd"`      // 通用型SSD云硬盘 CLOUD_BSSD
	OceanusCloudSsd                       int       `json:"sv_oceanus_storage_cloud_ssd"`         // SSD云硬盘 CLOUD_SSD
	OceanusCloudHssd                      int       `json:"sv_oceanus_storage_enhanced_ssd"`      // 增强型SSD云硬盘 CLOUD_HSSD
	OceanusExclusiveComputeMultipleCu14   int       `json:"sv_oceanus_compute_multiple_cu1c4g"`   // 多可用区
}

type NewResourceGoodsDetail struct {
	ProductCode                           string    `json:"productCode,omitempty"`
	SubProductCode                        string    `json:"subProductCode,omitempty"`
	OceanusExclusiveComputeCu             int32     `json:"sv_oceanus_compute_exclusive_cu"` // 独享模式-CU, 单位 vCPU
	Pid                                   int       `json:"pid"`
	TimeSpan                              int       `json:"timeSpan,omitempty"`
	TimeUnit                              string    `json:"timeUnit,omitempty"` // 必传字段 商品的时间单位，计费周期，y：年，m：月，d：日，(一次性购买传p) 业务侧不需要额外添加字段表示时间
	ResourceId                            string    `json:"resourceId"`
	CurDeadline                           string    `json:"curDeadline,omitempty"` // 旧配置到期时间，YYYY-MM-DD HH:MM:SS， 预付费 变配必传
	ProductInfo                           []*KVPair `json:"productInfo,omitempty"`
	ComputeCu                             int       `json:"ComputeCu,omitempty"`
	BillingResourceMode                   string    `json:"billingResourceMode"` // "exclusiveSale"
	ResourceOperationMode                 string    `json:"resourceOperationMode"`
	Duration                              string    `json:"duration"`
	OceanusExclusiveComputeUnderwriteCu14 int       `json:"sv_oceanus_compute_underwrite_cu1c4g"` // 包销计费-CU（1核4G）
	OceanusExclusiveComputeCu12           int       `json:"sv_oceanus_compute_exclusive_cu1c2g"`  // 独享模式-CU（1核2G）
	OceanusExclusiveComputeCu18           int       `json:"sv_oceanus_compute_exclusive_cu1c8g"`  // 独享模式-CU（1核8G）
	OceanusExclusiveComputeMultipleCu14   int       `json:"sv_oceanus_compute_multiple_cu1c4g"`   // 多可用区

}

func (r *ResourceGoodsDetail) String() string {
	if b, err := json.Marshal(r); err != nil {
		return ""
	} else {
		return string(b)
	}
}
