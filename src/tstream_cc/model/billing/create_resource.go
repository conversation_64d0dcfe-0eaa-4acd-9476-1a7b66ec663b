package billing

type CreateResourceReq struct {
	AppId       int64              `check:"nullable:false|range:(0,INF)"`
	TranId      string             `check:"nullable:true|strlen:(0,INF)"`  // 以前旧产品使用的订单号，现在统一使用dealName作为订单号）
	DealName    string             `check:"nullable:false|strlen:[0, INF)"` // 后付费：订单号  计费订单号(部分旧产品使用的tranId)，与用户订单页显示的一致，字符串，防重入使用，业务需要根据该字段做接口幂等
	BigDealId   string             `check:"nullable:true|strlen:[0, INF)"` // 计费大订单号,一个大订单包含一个或多个订单，根据下单的goods区分有多少订单
	Uin         string             `check:"nullable:false|strlen:(0, INF)"`
	OperateUin  string             `check:"nullable:false|strlen:(0, INF)"`
	Region      int                `check:"nullable:false|range:(0,INF)" json:"region"`
	ZoneId      int                `check:"nullable:false|range:(0,INF)"`
	PayMode     int                `check:"nullable:true|enum:0,1"` // 付费模式: 0-按量计费/后付费  1-预付费
	ProjectId   int32              `check:"nullable:true|range:[0,INF)"`
	GoodsDetail *CreateGoodsDetail `check:"nullable:false"`
}

type CreateResourceResp struct {
	FlowId      int          `json:"flowId"`      // 23 位数字, 仅当流程为异步时需要传该字段，异步发货会进行queryFlow查询发货状态；不能为0，为0会判断为失败，会重试调用此接口
	ResourceIds []string     `json:"resourceIds"` // 由业务侧生成的资源id（类似货物身份证，单个资源id不超过64字符，不建议使用纯数字），个数需要和goodsNum保持一致，保证 产品内部(p_xx) 不重复
	Resources   []*Resources `json:"resources"`
}
