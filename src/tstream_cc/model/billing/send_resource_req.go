package billing

/**
 * 后付费 推送用量给计费
 * https://tcb.woa.com/magical-brush/docs/754674275
 */
type SendResourceReq struct {
	Cid string `json:"cid"` // 我们是：200348  cid在接入单研发审核完后自动生成，可在接入单查看，配置好的cid可以在 http://tcb.woa.com/postpayProduct 查看，或者在汇金接入单上查看。
	/**
	 * 外层calcDate 表示本次推送所属结算周期：其精度与cid的结算周期相对应。
	 * 小时结算：YYYYMMDDHH
	 * 日结：YYYYMMDD
	 * 月结：YYYYMM
	 * 结算周期必须为资源使用周期（startTime~endTime）的下一周期（对于企点的实时结算，calcDate必须是startTime所在的小时，因为calcDate要用来对账，所以推量时也会校验calcDate和startTime的一致性）
	 * 如9月3号推送用户9月2号期间使用的日结数据时，结算周期为 20160903。
	 * 如9月3号14时推送用户9月3号13时期间使用的小时结数据时，结算周期为 2016090314。
	 * 如201909推送201908数据时，结算周期为 201909
	 * 对于比按小时还要更细粒度计费的产品，calcDate精确到startTime所在小时即可。比如某一分钟计费的产品，8月13日9点02分推送9点01分使用的数据，结算周期为20210813
	 */
	CalcDate string                     `json:"calcDate"`
	Data     map[string][]*ResourceInfo `json:"data"` // key : appid value : 用户每个集群的用量

}

type ResourceInfo struct {
	SerialId       string `json:"serialId"` // 一般是createResource接口返回的resourceId, 同一个结算周期内，业务需保证计费资源的流水ID唯一，防止重复提交造成重复计费。 就是我们的 serial id
	AppId          int64  `json:"appId"`
	RegionId       int    `json:"regionId"`
	ZoneId         int    `json:"zoneId"`
	PayMode        int    `json:"payMode"`        // 0 后付费,1 预付费
	Platform       int    `json:"platform"`       // 固定 1
	CalcNum        int    `json:"calcNum"`        // 计算量值，计算结果的倍数，即goodsNum，默认为1
	TimeSpan       int    `json:"timeSpan"`       // 计算时长，默认为1，不涉及时长的业务，不用关注此字段。月95模式为 实际天数/当月天数
	Pid            int    `json:"pid"`            // pid字段不可为空，需要在接入时配置pid，映射到一个唯一的四层定义 固定为：1000510
	ProductCode    string `json:"productCode"`    // p_oceanus
	SubProductCode string `json:"subProductCode"` // 独享集群固定值 sp_oceanus_exclusive
	CalcDate       string `json:"calcDate"`       // 正常推送数据的情况，内层calcDate跟外层一致
	StartTime      string `json:"startTime"`      // 资源使用开始时间，如2016-04-01 13:00:00。startTime和endTime必须在同一个自然月内，不允许出现跨月
	/**
	 * 资源使用结束时间，如2016-04-01 13:59:59。
	 * [startTime,endTime] 是calcDate前一周期的子集, 必须是[yyyy-mm-dd HH:00:00, yyyy-mm-dd HH:59:59] 闭区间内的时间点 。
	 * 同一实例的多条记录的时间区间不能有重叠，[2016-05-24 15:00:00 , 2016-05-24 15:20:00]，[2016-05-24 15:20:00 , 2016-05-24 15:59:59] 使用时间发生了重叠，是非法的。
	 * 正确示 [2016-05-24 15:00:00 , 2016-05-24 15:19:59]，[2016-05-24 15:20:00 , 2016-05-24 15:59:59]
	 */
	EndTime string `json:"endTime"`
	/**
	 * 用量 json字符串，我们应该传 "{\"sv_oceanus_compute_exclusive_cu\":\"100\"}" 单价是 0.00015278元/vCPU/秒 ， 100 就是 cu数 * 时间（秒）
	 * 比如 作业 使用 2CU 跑了 60秒，那就是 2*60  120 ，数据应该来自于
	 */
	Parts string `json:"parts"` // 用量

}

type Part struct {
	Sv_oceanus_compute_exclusive_cu string `json:"sv_oceanus_compute_exclusive_cu"` // 后付费 计费项
}
