package billing

import (
	"encoding/json"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/tag"
)

type CheckCreateReq struct {
	Uin         string             `check:"nullable:false"` // 主账号
	AppId       int32              `check:"nullable:false|range:(0,INF)"`
	OperateUin  string             `check:"nullable:false"` // 子账号 订单实际操作者，比如财务协作者支付订单，项目协作者创建订单等场景
	Type        string             `json:"type"`            // 业务名称，categoryId 中的商品码, 子产品标签 sp_oceanus_exclusive, 切了订购关系或新接入(2021.06.17以后)的业务用productCode、subProductCode，type后续将会下线
	Region      int8               `check:"nullable:false" json:"region"`
	ZoneId      int32              `check:"nullable:false|range:(0,INF)"`
	PayMode     int8               `json:"payMode"`                      // 付费模式，0 表示按需计费/后付费，1 表示预付费
	ProjectId   int64              `check:"nullable:true|range:[0,INF)"` // 云平台项目 ID，若无项目概念，则默认为 0
	GoodsDetail *CreateGoodsDetail `check:"nullable:false"`
}

// 预付费：询价、下单（新购、续费、升级）和后付费的：询价、开通、变配的请求都必须包含 CreateGoodsDetail 参数
// 1. 用于价格查询：计费通过 goodsDetail 里面的参数查询价格
// 2. 用于发货：新产品接入，下单时计费会将 goodsDetail 中的自定义参数透传给业务，业务读取里面的参数发货。
// 3. 资源详情页面展示（预付费功能）
// http://tapd.oa.com/pt_jifei/markdown_wikis/show/#1210140771000562561
type CreateGoodsDetail struct {
	TimeUnit string `check:"nullable:false|enum:m"` // 必传字段 商品的时间单位，计费周期，y：年，m：月，d：日，(一次性购买传p) 业务侧不需要额外添加字段表示时间
	// 后付费传 7200 用于冻结
	TimeSpan       int    `check:"nullable:false|range:(0,1000]"` // 时长. 必传字段，商品的时间大小，（一次性购买固定传 1） 业务侧不需要额外添加字段表示时间，根据询价参数算出的价格会乘以时间，业务侧可对这个字段加强校验，例如业务最低售卖时间是整月，那么当入参是1天或40天，侧不应允许下单，避免出现低价高配情况
	GoodsNum       int    `check:"nullable:false|range:(0,100]"`  // 用户购买的实例数量. 一般为大于 0 的整数，必传字段，购买资源实例个数， 业务侧不需要额外添加字段表示个数
	Pid            int    `check:"nullable:true"`                 // 价格模型 ID，商品的每种价格策略会对应配置一条价格信息，用于价格计算
	AutoRenewFlag  int    `check:"nullable:true|enum:0,1,2"`      // 续费方式：0（默认）正常续费续费；1 自动续费；2 不自动续费
	ProductCode    string `json:"productCode,omitempty"`          // 固定值 p_oceanus
	SubProductCode string `json:"subProductCode,omitempty"`       // 独享集群固定值 sp_oceanus_exclusive sp_oceanus_elastic

	// 询价参数 (http://tcb.oa.com/product/defining)
	OceanusExclusiveComputeCu int `json:"sv_oceanus_compute_exclusive_cu"` // 独享模式-CU, 单位 vCPU

	// 产品详情页面展示参数 中文（预付费功能）
	ProductInfo []*KVPair

	// 自定义参数（透传）
	ClusterName      string                 `check:"nullable:true|strlen:[0,INF)"` // 集群名称
	VpcDescriptions  []model.VpcDescription `check:"nullable:true"`                // 关联的 VPC 信息（数组）
	LoginPassword    string                 `check:"nullable:true|strlen:[0,INF)"` // 密码 (Base64)
	Remark           string                 `check:"nullable:true"`                // 集群描述
	Tags             []*tag.Tag             `json:"Tags,omitempty"`                // 集群标签 	// 集群标签
	DefaultCOSBucket string                 `check:"nullable:false"`               // 默认的 COS Bucket
	CLSLogSet        string                 `check:"nullable:true"`                // cls日志集id
	CLSTopicId       string                 `check:"nullable:true"`                // cls日志id
	OrderOrigin      int                    `check:"nullable:true|enum:0,1"`       // 订单来源：0（默认）控制台; 1 点石

	AcceptNonFineGrainedResource bool

	International int

	/**
	 *  规模小于 48CU 集群增加 2CU 管理费用增加， 用来告诉后台实际计算CU是多少，不然后台无法感知， 比如 48 CU 可以是 48 ， 46+2
	 *	 它 和 OceanusExclusiveComputeCu 关系是
	 *  OceanusExclusiveComputeCu = ComputeCu
	 *  或者
	 *  OceanusExclusiveComputeCu = ComputeCu + 2
	 */
	ComputeCu int

	// ClusterID   string `check:"nullable:false|strlen:(0,INF)"` // 集群的 SerialId (例如 cluster-a2b4c6d8)
	// ComputeUnit uint32 `check:"nullable:false|range:(0,INF)"`  // 集群初始计算 CU
	// Storage     uint32 `check:"nullable:false|range:(0,INF)"`  // 存储规模
	ArchGeneration int
	// 集群类型 3：表示统一资源池的母集群, 其他：表示创建独享集群
	// 如果创建独享集群，可以指定母集群, 如果没有找到母集群，随机选择一个母集群
	// 如果创建统一资源池母集群，并且指定AgentSerialId，表示开启一个新的地域
	// 4 包年包月下的eks集群
	ClusterGroupType int8
	AgentSerialId    string
	// 共享集群，包年包月支持按量付费模式，需要传递 包年包月集群Id
	ParentSerialId string
	// 共享集群所属用户
	UniformOwnerUin string
	// 包销模式
	BillingResourceMode                   string   `json:"billingResourceMode"`                  // "exclusiveSale"
	Duration                              string   `json:"duration"`                             // "1y 2y 3y 4y 5y" 按年来
	OceanusExclusiveComputeUnderwriteCu14 int      `json:"sv_oceanus_compute_underwrite_cu1c4g"` // 包销计费-CU（1核4G）
	OceanusExclusiveComputeCu12           int      `json:"sv_oceanus_compute_exclusive_cu1c2g"`  // 独享模式-CU（1核2G）
	OceanusExclusiveComputeCu18           int      `json:"sv_oceanus_compute_exclusive_cu1c8g"`  // 独享模式-CU（1核8G）
	Extparam                              Extparam `json:"extparam"`
	OceanusCloudPremium                   int      `json:"sv_oceanus_storage_cloud_premium"` // 高性能云盘 CLOUD_PREMIUM
	OceanusCloudBssd                      int      `json:"sv_oceanus_storage_balanced_ssd"`  // 通用型SSD云硬盘 CLOUD_BSSD
	OceanusCloudSsd                       int      `json:"sv_oceanus_storage_cloud_ssd"`     // SSD云硬盘 CLOUD_SSD
	OceanusCloudHssd                      int      `json:"sv_oceanus_storage_enhanced_ssd"`  // 增强型SSD云硬盘 CLOUD_HSSD
	MasterInfo                            *SetatsCvm
	WorkerInfo                            *SetatsCvm
	ClusterGroupSerialId                  string
	DataSecurityStatement                 string `json:"dataSecurityStatement"`
	OceanusExclusiveComputeMultipleCu14   int    `json:"sv_oceanus_compute_multiple_cu1c4g"` // 多可用区
	SlaveVpcDescriptions                  []model.VpcDescription
}

type SetatsCvm struct {
	Cpu                float32
	Mem                float32
	Disk               *SetatsDisk
	DefaultParallelism int
}

type SetatsDisk struct {
	DiskType string
	DiskSize int
}

type Extparam struct {
	Token string `json:"token"`
}

type GoodsInfo struct {
	TimeUnit string `json:"timeUnit" check:"nullable:false|enum:m"` // 必传字段 商品的时间单位，计费周期，y：年，m：月，d：日，(一次性购买传p) 业务侧不需要额外添加字段表示时间
	// 后付费传 7200 用于冻结
	TimeSpan       int    `json:"timeSpan" check:"nullable:false|range:(0,1000]"` // 时长. 必传字段，商品的时间大小，（一次性购买固定传 1） 业务侧不需要额外添加字段表示时间，根据询价参数算出的价格会乘以时间，业务侧可对这个字段加强校验，例如业务最低售卖时间是整月，那么当入参是1天或40天，侧不应允许下单，避免出现低价高配情况
	GoodsNum       int    `check:"nullable:false|range:(0,100]"`                  // 用户购买的实例数量. 一般为大于 0 的整数，必传字段，购买资源实例个数， 业务侧不需要额外添加字段表示个数
	Pid            int    `check:"nullable:true"`                                 // 价格模型 ID，商品的每种价格策略会对应配置一条价格信息，用于价格计算
	AutoRenewFlag  int    `check:"nullable:true|enum:0,1,2"`                      // 续费方式：0（默认）正常续费续费；1 自动续费；2 不自动续费
	ProductCode    string `json:"productCode,omitempty"`                          // 固定值 p_oceanus
	SubProductCode string `json:"subProductCode,omitempty"`                       // 独享集群固定值 sp_oceanus_exclusive sp_oceanus_elastic

	// 询价参数 (http://tcb.oa.com/product/defining)
	OceanusExclusiveComputeCu int `json:"sv_oceanus_compute_exclusive_cu"` // 独享模式-CU, 单位 vCPU

	// 产品详情页面展示参数 中文（预付费功能）
	ProductInfo []*KVPair

	// 自定义参数（透传）
	ClusterName      string                 `check:"nullable:true|strlen:[0,INF)"` // 集群名称
	VpcDescriptions  []model.VpcDescription `check:"nullable:true"`                // 关联的 VPC 信息（数组）
	LoginPassword    string                 `check:"nullable:true|strlen:[0,INF)"` // 密码 (Base64)
	Remark           string                 `check:"nullable:true"`                // 集群描述
	Tags             []*tag.Tag             `json:"Tags,omitempty"`                // 集群标签 	// 集群标签
	DefaultCOSBucket string                 `check:"nullable:false"`               // 默认的 COS Bucket
	CLSLogSet        string                 `check:"nullable:true"`                // cls日志集id
	CLSTopicId       string                 `check:"nullable:true"`                // cls日志id
	OrderOrigin      int                    `check:"nullable:true|enum:0,1"`       // 订单来源：0（默认）控制台; 1 点石

	AcceptNonFineGrainedResource bool

	International int

	/**
	 *  规模小于 48CU 集群增加 2CU 管理费用增加， 用来告诉后台实际计算CU是多少，不然后台无法感知， 比如 48 CU 可以是 48 ， 46+2
	 *	 它 和 OceanusExclusiveComputeCu 关系是
	 *  OceanusExclusiveComputeCu = ComputeCu
	 *  或者
	 *  OceanusExclusiveComputeCu = ComputeCu + 2
	 */
	ComputeCu int

	// ClusterID   string `check:"nullable:false|strlen:(0,INF)"` // 集群的 SerialId (例如 cluster-a2b4c6d8)
	// ComputeUnit uint32 `check:"nullable:false|range:(0,INF)"`  // 集群初始计算 CU
	// Storage     uint32 `check:"nullable:false|range:(0,INF)"`  // 存储规模
	ArchGeneration int
	// 集群类型 3：表示统一资源池的母集群, 其他：表示创建独享集群
	// 如果创建独享集群，可以指定母集群, 如果没有找到母集群，随机选择一个母集群
	// 如果创建统一资源池母集群，并且指定AgentSerialId，表示开启一个新的地域
	// 4 包年包月下的eks集群
	ClusterGroupType int8
	AgentSerialId    string
	// 共享集群，包年包月支持按量付费模式，需要传递 包年包月集群Id
	ParentSerialId string
}

func (this *CreateGoodsDetail) String() string {
	if b, err := json.Marshal(this); err != nil {
		return ""
	} else {
		return string(b)
	}
}

type CheckCreateResp struct {
	Status int `json:"status"` // status 0 可以购买  非 0 不可以 参考 billing_constants.go 的定义
}
