package billing

type RenewResourceReq struct {
	AppId       int64            `check:"nullable:false|range:(0,INF)"`
	TranId      string           `check:"nullable:false|strlen:(0,INF)"`
	DealName    string           `check:"nullable:false|strlen:[0, INF)"` // 后付费：订单号  计费订单号(部分旧产品使用的tranId)，与用户订单页显示的一致，字符串，防重入使用，业务需要根据该字段做接口幂等
	Type        string           `json:"type"`                            // 业务名称，categoryId 中的商品码, 子产品标签 sp_oceanus_exclusive
	Uin         string           `check:"nullable:false|strlen:(0,INF)"`
	OperateUin  string           `check:"nullable:false"`
	Region      int              `check:"nullable:false" json:"region"`
	ZoneId      int              `check:"nullable:false|range:(0,INF)"`
	PayMode     int              `check:"nullable:false"`
	ProjectId   int64            `check:"nullable:true"`
	DealId      int64            `check:"nullable:false"`
	ResourceId  string           `check:"nullable:false|strlen:(0,INF)"`
	GoodsDetail RenewGoodsDetail `check:"nullable:false"`
}

type RenewResourceResp struct {
	ExecutionStartTime   string `json:"executionStartTime,omitempty"`   // 同步发货时必须返回，执行接口逻辑的开始时间，yyyy-MM-dd HH:mm:ss，如：2020-06-22 18:22:21
	ExecutionEndTime     string `json:"executionEndTime,omitempty"`     // 同步发货时必须返回，执行接口逻辑的结束时间，隔离销毁时即资源的隔离时间销毁时间，yyyy-MM-dd HH:mm:ss，如：2020-06-22 18:22:22
	ResourceNewStartTime string `json:"resourceNewStartTime,omitempty"` // 同步发货时必须返回，资源创建时间，yyyy-MM-dd HH:mm:ss，如：2020-06-22 18:23:22
	ResourceNewEndTime   string `json:"resourceNewEndTime,omitempty"`   // 同步发货时必须返回，资源到期时间，不管是否发生变化都需返回，yyyy-MM-dd HH:mm:ss，如：2020-07-22 18:23:22；例如资源2020-01-10 00:00:00到期，1月5日用户做续费操作，资源有效期从1月10号延长到2月10号，那返回2020-02-10 00:00:00
	FlowId               int64  `json:"flowId"`
}
