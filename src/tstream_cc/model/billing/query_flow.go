package billing

type QueryFlowReq struct {
	Type       string `check:"nullable:true" json:"type"`
	Uin        string `check:"nullable:false" json:"uin"`
	AppId      int32  `check:"nullable:false|range:(0,INF)" json:"appId"`
	Region     uint8  `check:"nullable:true" json:"region"`
	FlowId     int64  `check:"nullable:false|range:(0,INF)" json:"flowId"`
	ResourceId string `check:"nullable:true" json:"resourceId"`
}

type QueryFlowResp struct {
	Status               int          `json:"status"`                         // 0-成功；1-暂时无法发货计费会继续请求，如新购则重新新购、变配则需要重新变配等；2-发货中，需要重新请求查询发货状态；3-没有发货资源-退款，退款；4-部分资源发货失败，需要退款
	FailedResourceIdList []string     `json:"failedResourceIdList,omitempty"` // 发货失败的资源列表，status=4 时需要返回
	Resources            []*Resources `json:"resources,omitempty"`
}

type Resources struct {
	ResourceId           string `json:"resourceId"`
	ExecutionSuccess     string `json:"executionSuccess,omitempty"`     // 操作是否执行成功，0-成功 1-失败
	ExecutionStartTime   string `json:"executionStartTime,omitempty"`   // 同步发货时必须返回，执行接口逻辑的开始时间，yyyy-MM-dd HH:mm:ss，如：2020-06-22 18:22:21
	ExecutionEndTime     string `json:"executionEndTime,omitempty"`     // 同步发货时必须返回，执行接口逻辑的结束时间，隔离销毁时即资源的隔离时间销毁时间，yyyy-MM-dd HH:mm:ss，如：2020-06-22 18:22:22
	ResourceNewStartTime string `json:"resourceNewStartTime,omitempty"` // 同步发货时必须返回，资源创建时间，yyyy-MM-dd HH:mm:ss，如：2020-06-22 18:23:22
	ResourceNewEndTime   string `json:"resourceNewEndTime,omitempty"`   // 同步发货时必须返回，资源到期时间，不管是否发生变化都需返回，yyyy-MM-dd HH:mm:ss，如：2020-07-22 18:23:22；例如资源2020-01-10 00:00:00到期，1月5日用户做续费操作，资源有效期从1月10号延长到2月10号，那返回2020-02-10 00:00:00
}
