package billing

type DestroyResourceReq struct {
	AppId      int64  `check:"nullable:false|range:(0,INF)"`
	Uin        string `check:"nullable:false|strlen:(0, INF)"`
	OperateUin string `check:"nullable:true|strlen:(0, INF)"`
	Region     int    `check:"nullable:false|range:(0,INF)"`
	ResourceId string `check:"nullable:true|strlen:(0,INF)"`
	ProductBase
	Source  string // 后付费，请求来源，biz--业务手动发起，qn--计费生命周期自动发起
	EventId int64
}

type DestroyResourceResp struct {
	FlowId int64 `check:"nullable:false|range:(0,INF)" json:"flowId"` // 仅当流程为异步时需要传该字段，若传flowId表示隔离中，计费会去queryFlow查询隔离状态
}
