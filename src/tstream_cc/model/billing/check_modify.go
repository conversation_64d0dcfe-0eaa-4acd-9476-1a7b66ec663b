package billing

import "encoding/json"

type CheckModifyReq struct {
	Uin         string             `check:"nullable:false"`
	AppId       int32              `check:"nullable:false|range:(0,INF)"`
	OperateUin  string             `check:"nullable:false"`
	Type        string             `check:"nullable:false"` // 业务名称，categoryId 中的商品码, 子产品标签 sp_oceanus_exclusive
	Region      int8               `check:"nullable:false" json:"region"`
	ZoneId      int32              `check:"nullable:false|range:(0,INF)"`
	ResourceId  string             `check:"nullable:false|strlen:(0,INF)"`  // 资源 id 标识，严格校验此资源 id 的旧配置与新配置是否支持
	PayMode     int8               `json:"payMode"`                         // 付费模式，0 表示按需计费/后付费，1 表示预付费
	ProjectId   int64              `check:"nullable:true" json:"projectId"` // 云平台项目ID，若无项目概念，则默认为 0
	GoodsDetail *ModifyGoodsDetail `check:"nullable:false"`
}

type ModifyGoodsDetail struct {
	GoodsNum    int           `check:"nullable:false|range:(0,INF)"`  // 必传字段，购买资源实例个数， 业务侧不需要额外添加字段表示个数
	CurDeadline string        `check:"nullable:false|strlen:(0,INF)"` // 旧配置到期时间，YYYY-MM-DD HH:MM:SS， 预付费 变配必传
	OldConfig   *ConfigDetail `check:"nullable:false"`                // 旧配置，业务校验&计费询价，业务侧变配下单传过来的，业务侧需要校验旧配置是否存在与合法
	NewConfig   *ConfigDetail `check:"nullable:false"`                // 新配置，业务校验&计费询价，业务侧变配下单传过来的，新配置也需校验是否支持，可参考新购校验方式，避免低价高配
}

func (this *ModifyGoodsDetail) String() string {
	if b, err := json.Marshal(this); err != nil {
		return ""
	} else {
		return string(b)
	}
}

type ConfigDetail struct {
	Pid                       int    `check:"nullable:true" json:"pid"`       // 商品的 pid
	ProductCode               string `json:"productCode,omitempty"`           // 固定值 p_oceanus
	SubProductCode            string `json:"subProductCode,omitempty"`        // 独享集群固定值 sp_oceanus_exclusive
	OceanusExclusiveComputeCu int    `json:"sv_oceanus_compute_exclusive_cu"` // 独享模式-CU, 单位 vCPU
	TimeUnit                  string `check:"nullable:false|enum:m"`          // 必传字段 商品的时间单位，计费周期，y：年，m：月，d：日，(一次性购买传p) 业务侧不需要额外添加字段表示时间
	ModifyMode                string `json:"modifyMode,omitempty"`            // 降配时，必传modifyMode="down"
	/**
	 *  规模小于 48CU 集群增加 2CU 管理费用增加， 用来告诉后台实际计算CU是多少，不然后台无法感知， 比如 48 CU 可以是 48 ， 46+2
	 *	 它 和 OceanusExclusiveComputeCu 关系是
	 *  OceanusExclusiveComputeCu = ComputeCu
	 *  或者
	 *  OceanusExclusiveComputeCu = ComputeCu + 2
	 */
	ComputeCu int

	OceanusExclusiveComputeCu12         int `json:"sv_oceanus_compute_exclusive_cu1c2g"` // 独享模式-CU（1核2G）
	OceanusExclusiveComputeCu18         int `json:"sv_oceanus_compute_exclusive_cu1c8g"` // 独享模式-CU（1核8G）
	OceanusCloudPremium                 int `json:"sv_oceanus_storage_cloud_premium"`    // 高性能云盘 CLOUD_PREMIUM
	OceanusCloudBssd                    int `json:"sv_oceanus_storage_balanced_ssd"`     // 通用型SSD云硬盘 CLOUD_BSSD
	OceanusCloudSsd                     int `json:"sv_oceanus_storage_cloud_ssd"`        // SSD云硬盘 CLOUD_SSD
	OceanusCloudHssd                    int `json:"sv_oceanus_storage_enhanced_ssd"`     // 增强型SSD云硬盘 CLOUD_HSSD
	MasterInfo                          *SetatsCvm
	WorkerInfo                          *SetatsCvm
	OceanusExclusiveComputeMultipleCu14 int `json:"sv_oceanus_compute_multiple_cu1c4g"` // 多可用区
}

func (this *ConfigDetail) String() string {
	if b, err := json.Marshal(this); err != nil {
		return ""
	} else {
		return string(b)
	}
}

type CheckModifyResp struct {
	Status int `json:"status"`
}
