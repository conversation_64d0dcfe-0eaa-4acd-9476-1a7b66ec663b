package billing

// CategoryID 场景（独享集群）
const (
	OceanusExclusiveCategoryIdCreate   = 102113
	OceanusExclusiveCategoryIdRenew    = 102114
	OceanusExclusiveCategoryIdModify   = 102115
	OceanusExclusivePid                = 1000510
	OceanusExclusiveBillingType        = "sp_oceanus_exclusive"
	OceanusProductCode                 = "p_oceanus"
	OceanusExclusiveSubProductCode     = "sp_oceanus_exclusive"
	TimeUnitMonth                      = "m" // 月份
	TimeUnitDay                        = "d" // 日
	ModifyModeScaleDown                = "down"
	ModifyModeScaleUp                  = "up"
	PayModePrepaid                     = 1    // 预付费
	MaximumAllowedTimeSpanMonth        = 48   // 单次最长购买月份
	MaximumAllowedComputeUnitIncrement = 300  // 每次扩容最多允许的 CU 增量值
	MaximumAllowedComputeUnit          = 1500 // 集群最多允许的 CU 值（默认值）
	MinimalAllowedComputeUnit          = 12   // 最小允许购买的 CU 数

	// 后付费

	CalcUnit                      = "h"            // 结算周期
	PayModePost                   = 0              // 后付费
	TimeUnitSecond                = "s"            // 后付费计费周期 s
	PayModePost_Source_BIZ        = "biz"          // 隔离请求来源，biz--业务手动发起
	PayModePost_Source_QN         = "qn"           // 隔离请求来源，qn--计费生命周期自动发起, 隔离用户所有资源，生命周期欠费隔离
	POST_DESTROY                  = "POST_DESTROY" // https://tcb.woa.com/magical-brush/docs/823197437 operateName
	ModifyResourceStatusInterface = "qcloud.operation.ModifyResourceStatus"
	ClientType                    = 4               // 客户端类型，0-资源中心，1-交易，2-QN，3-计费前端，4-产品（这里需要传4）
	SubmitByUser                  = 1               // 是否为用户主动发起，0-否，1-是（这里需要传1）
	Controller_Resource_CU        = 2               // 管控服务默认每秒计算 2CU的用量
	ExclusiveSale                 = "exclusiveSale" // 包销
	TimeUnitYear                  = "y"             // 月份
)

var Durations = []string{"1y", "2y", "3y", "4y"}

// 接口 Status 状态码
// 录入地址：http://tcb.oa.com/productAccess/detail?id=10232&auth_cm_com_ticket=061d8f24-c6ba-4652-be88-253660e11363
const (
	StatusSuccess                    = 0       // 检查成功
	StatusUserNotAuthenticated       = 1002252 // 用户未实名认证
	StatusSystemError                = 1002253 // 默认检查失败 (未知错误，建议提工单解决)
	StatusInvalidClusterName         = 1002254 // 集群名不合法
	StatusDuplicateClusterName       = 1002255 // 集群名已存在
	StatusInvalidCuNum               = 1002256 // CU 数不合法
	StatusInvalidVpcSubnet           = 1002257 // VPC 或子网信息有误
	StatusInvalidBillingType         = 1002258 // 非法 Type 参数
	StatusUnsupportedPayMode         = 1002259 // 不支持的支付模式
	StatusUnsupportedTimeUnit        = 1002260 // 不支持的时间单位。
	StatusInvalidTimeSpan            = 1002261 // 超出允许的购买时长
	StatusOnlyOneClusterIsAllowed    = 1002262 // 每次只允许购买一个集群
	StatusInvalidPid                 = 1002263 // 非法 Pid 参数
	StatusInvalidOtherParams         = 1002264 // 其他参数非法（不重要的参数）
	StatusResourceNotBelongToThisUin = 1002266 // 不允许操作别人的资源
	StatusNotConsistentExpireTime    = 1002267 // 续费时传入的到期时间与数据库不一致
	StatusRenewSpanExceedsLimit      = 1002268 // 续费时长超过限制
	StatusInvalidPassword            = 1002270 // 密码不符合规范
	StatusClusterNotRunning          = 1002271 // 集群未处于运行中状态
	StatusClusterCannotBeScaled      = 1002278 // 该集群不允许变配
	StatusOnlyUpgradeIsSupported     = 1002272 // 只支持升配
	StatusRegionInBlacklist          = 1002279 // 该地域暂不支持购买
	StatusNotAuthorized              = 1002281 // 未授权 不允许创建集群
	StatusInvalidTag                 = 1002282 // 非法标签
	StatusCAMNotAuthorized           = 1002703
)

const (
	OrderStatusInitialized = 1 // 初始
	OrderStatusPending     = 2 // 发货中
	OrderStatusFinished    = 3 // 发货完成

	ResourceStatusNormal    = 1 // 正常
	ResourceStatusIsolated  = 2 // 隔离
	ResourceStatusDestroyed = 3 // 销毁

	NotIsolatedTimestamp          = "0000-00-00 00:00:00" // 默认的隔离时间, 表示未设置
	StandardTimestampFormatString = "2006-01-02 15:04:05" // 标准时间戳格式化字符串
	CalcDateFormatString          = "2006010215"          // 标准时间戳格式化字符串
	CalcDateAlartFormatString     = "20060102"            // 标准时间戳格式化字符串

	NoResourceFlag      = -1
	SetatsResourceType  = 2
	ClusterResourceType = 1
)

const (
	BillingResourceDefault       = 0
	BillingResourceInternational = 1
)

// 0-成功；1-暂时无法发货计费会继续请求，如新购则重新新购、变配则需要重新变配等；2-发货中，需要重新请求查询发货状态；3-没有发货资源-退款，退款；4-部分资源发货失败，需要退款
const (
	FlowStatusSuccess    = 0
	FlowStatusRedo       = 1
	FlowStatusProcessing = 2
	FlowStatusFail       = 3
	FlowStatusPartFail   = 4
)

const (
	ExecutionSuccess = "0"
	ExecutionFail    = "1"
)

const (
	MANAGE_CU    = 2  // 集群规模小于 48 的时候， 会传给计费 额外2CU，业务需要把这个2CU排除在 计算之外
	NO_MANAGE_CU = 48 // 大于等于 48 不收
)

const (
	CLOUD_PREMIUM = "CLOUD_PREMIUM"
	CLOUD_BSSD    = "CLOUD_BSSD"
	CLOUD_SSD     = "CLOUD_SSD"
	CLOUD_HSSD    = "CLOUD_HSSD"
)

var DiskTypeList = []string{
	CLOUD_PREMIUM,
	CLOUD_BSSD,
	CLOUD_SSD,
	CLOUD_HSSD,
}
