package billing

import "encoding/json"

type CheckRenewReq struct {
	Uin         string            `check:"nullable:false|strlen:(0,INF)"`
	AppId       int32             `check:"nullable:false|range:(0,INF)"`
	OperateUin  string            `check:"nullable:false"`
	Type        string            `check:"nullable:false|strlen:(0,INF)"`
	Region      int8              `check:"nullable:false" json:"region"`
	ZoneId      int32             `check:"nullable:false|range:(0,INF)"`
	PayMode     int8              `check:"nullable:false"`
	ProjectId   int64             `check:"nullable:true"`
	ResourceId  string            `check:"nullable:false|strlen:(0,INF)"`
	GoodsDetail *RenewGoodsDetail `check:"nullable:false"`
}

type RenewGoodsDetail struct {
	Pid                       int       `check:"nullable:true" json:"pid"`       // 商品的 pid
	ProductCode               string    `json:"productCode,omitempty"`           // 固定值 p_oceanus
	SubProductCode            string    `json:"subProductCode,omitempty"`        // 独享集群固定值 sp_oceanus_exclusive
	OceanusExclusiveComputeCu int       `json:"sv_oceanus_compute_exclusive_cu"` // 独享模式-CU, 单位 vCPU
	TimeUnit                  string    `check:"nullable:false|enum:m,d"`        // 必传字段 商品的时间单位，计费周期，y：年，m：月，d：日，(一次性购买传p) 业务侧不需要额外添加字段表示时间
	TimeSpan                  int       `check:"nullable:false|range:(0,1000]"`  // 续费的月份（目前只支持月）
	GoodsNum                  int       `check:"nullable:false|range:(0,INF)"`   // 必传字段，购买资源实例个数， 业务侧不需要额外添加字段表示个数
	CurDeadline               string    `check:"nullable:false|strlen:(0,INF)"`  // 旧配置到期时间，YYYY-MM-DD HH:MM:SS， 预付费 变配必传
	AutoRenewFlag             *int      `check:"nullable:true|enum:0,1,2"`       // 续费方式：0（默认）正常续费；1 自动续费；2 不自动续费
	ProductInfo               []*KVPair `json:"productInfo,omitempty"`

	/**
	 *  规模小于 48CU 集群增加 2CU 管理费用增加， 用来告诉后台实际计算CU是多少，不然后台无法感知， 比如 48 CU 可以是 48 ， 46+2
	 *	 它 和 OceanusExclusiveComputeCu 关系是
	 *  OceanusExclusiveComputeCu = ComputeCu
	 *  或者
	 *  OceanusExclusiveComputeCu = ComputeCu + 2
	 */
	ComputeCu int

	// 自定义参数（透传）
	// ClusterName string `check:"nullable:false|strlen:[0,INF)"` // 集群名称
	// ClusterID   string `check:"nullable:false|strlen:(0,INF)"` // 集群的 SerialId (例如 cluster-a2b4c6d8)
	// ComputeUnit int32  `check:"nullable:false|range:(0,INF)"`  // 集群初始计算 CU
	// Storage     int32  `check:"nullable:false|range:(0,INF)"`  // 存储规模
	// 包销模式
	BillingResourceMode                   string `json:"billingResourceMode"`                  // "exclusiveSale"
	Duration                              string `json:"duration"`                             // "1y 2y 3y 4y 5y" 按年来
	OceanusExclusiveComputeUnderwriteCu14 int    `json:"sv_oceanus_compute_underwrite_cu1c4g"` // 包销计费-CU（1核4G）

	OceanusExclusiveComputeCu12         int `json:"sv_oceanus_compute_exclusive_cu1c2g"` // 独享模式-CU（1核2G）
	OceanusExclusiveComputeCu18         int `json:"sv_oceanus_compute_exclusive_cu1c8g"` // 独享模式-CU（1核8G）
	OceanusCloudPremium                 int `json:"sv_oceanus_storage_cloud_premium"`    // 高性能云盘 CLOUD_PREMIUM
	OceanusCloudBssd                    int `json:"sv_oceanus_storage_balanced_ssd"`     // 通用型SSD云硬盘 CLOUD_BSSD
	OceanusCloudSsd                     int `json:"sv_oceanus_storage_cloud_ssd"`        // SSD云硬盘 CLOUD_SSD
	OceanusCloudHssd                    int `json:"sv_oceanus_storage_enhanced_ssd"`     // 增强型SSD云硬盘 CLOUD_HSSD
	OceanusExclusiveComputeMultipleCu14 int `json:"sv_oceanus_compute_multiple_cu1c4g"`  // 多可用区
}

func (this *RenewGoodsDetail) String() string {
	if b, err := json.Marshal(this); err != nil {
		return ""
	} else {
		return string(b)
	}
}

type CheckRenewResp struct {
	Status int `json:"status"`
}
