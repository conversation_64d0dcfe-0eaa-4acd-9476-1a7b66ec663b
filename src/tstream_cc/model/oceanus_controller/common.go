package oceanus_controller

type OwnerRef struct {
	Name string
	Uid  string
}

type Base struct {
	IsEKs          bool
	ArchGeneration int
	ClusterGroupId int64
	ClusterId      int64
	AppId          int32
	OwnerUin       string
	Region         string

	// meta
	WorkLoadKind string
	Namespace    string
	Name         string
	Labels       map[string]string
	OwnerRef     *OwnerRef

	// spec
	Replicas       int
	ServiceName    string
	NodeTypeValues []string

	//meta
	Annotations map[string]string

	// pod
	ServiceAccountName string
	PriorityClassName  string
	NodeSelector       map[string]string
	HostAliases        map[string]string
	// dnsPolicy: ClusterFirst
	// securityContext: {}
	HostNetwork bool

	// container
	AppImage     string
	SidecarImage string
	ListenPort   int
	Env          map[string]string
	DnsPolicy    string
	Nameserver   string

	// serviceAccount
	ServiceAccount string

	// configmap name
	ConfigMapName string
	ClusterType   string
}
