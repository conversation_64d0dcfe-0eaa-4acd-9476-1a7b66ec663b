package timer

import (
	"errors"
	"fmt"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/dlocker"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/notify"
	"tencentcloud.com/tstream_galileo/src/common/notify/tof"
	"tencentcloud.com/tstream_galileo/src/common/timer"
	service2 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	billing "tencentcloud.com/tstream_galileo/src/tstream_cc/model/billing"
	billingTable "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/billing"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service4 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/qcloud"
	sdkTke "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke"
	"time"
)

const EKS_BASE_CPU float64 = 13.25
const EKS_BASE_MEM_G float64 = 25.5

/**
 * 每天检查前一天的推量（24个时间点 00 ～ 23 ） 是否都ok了，如果有问题，就告警
 * 推量 逻辑查看 send_resource_handler.go
 *
 */
func SendResourceAlarm() {
	config := map[string]interface{}{}
	config["times"] = 0                     // Config 必须有 times 和 checkDuration 两个属性, 0 表示无限循环
	config["duration"] = int64(60 * 60 * 2) // in second
	task := timer.NewTask(config)
	task.Handler = &sendResourceAlarm{}
	task.Start()
}

type sendResourceAlarm struct {
}

func (*sendResourceAlarm) Do() {
	defer func() {
		if err := recover(); err != nil {
			err = errorcode.NewStackError(errorcode.InternalError_Timer,
				"", err.(error))
			logger.Errorf("Failed to run sendResourceHandler, with errors:%+v", err)
			return
		}
	}()

	/*
	 * 获取前一天
	 */
	yesterday := time.Now().Add(-time.Hour * 24).Format(billing.CalcDateAlartFormatString)

	logger.Infof("### 开始检查结算日期(%s)", yesterday)
	locker := dlocker.NewDlocker("oceanus-sendResource-alart", yesterday, 60*10)
	err := locker.Lock()
	if err != nil {
		logger.Infof("Another oceanus-sendResource-alart process for %s has lock but not finished yet", yesterday)
		return
	}
	defer func() {
		err := locker.UnLock()
		if err != nil {
			logger.Errorf("could not fetch the lock. %+v", err)
		}
	}()

	failCalDates := check(yesterday)
	if len(failCalDates) < 1 {
		logger.Infof("@ sendResourceAlarm 检查 结算日期%s 完成，没有失败结算时间", yesterday)
		return
	}

	tofMsg := tof.TOFMessage{
		EventName: "后付费推送用量失败告警",
		Time:      time.Now().Format("2006-01-02 15:04:05"),
		EventMessage: map[string]interface{}{
			"failCalDates": strings.Join(failCalDates, "||"),
			"message":      "!!! 以下后付费推送用量失败，请立即处理",
		},
	}
	n := notify.NewNotify()
	n.SyncSendMessageKafka(tofMsg)

	logger.Infof("## Send Resource Alarm message finish")

	list, err := GetEksResource()
	if err != nil {
		logger.Infof("GetEksResource error %v", err)
		return
	}
	if len(list) < 1 {
		return
	}
	for _, resource := range list {
		CheckEKSClusterResources(resource)
	}
}

// https://tcloud4api.woa.com/document/product/457/88688?!preview&!document=1
func CheckEKSClusterResources(br *billingTable.BillingResource) (err error) {

	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Failed to call CheckEKSClusterResources because %+v", err)
			err = errors.New(fmt.Sprintf("%+v", errs))
			return
		}
	}()

	clusterGroupService, err := service4.NewClusterGroupServiceBySerialId(br.ResourceId)
	clusterGroup := clusterGroupService.GetClusterGroup()
	tkeList, err := clusterGroupService.GetTkeList()
	if err != nil {
		logger.Errorf("#GetEKSClusterResources GetTkeList error %v", err)
		return
	}
	if len(tkeList) != 1 {
		logger.Errorf("#GetEKSClusterResources tkeList not 1")
		return
	}

	if tkeList[0].ClusterType != constants.K8S_CLUSTER_TYPE_EKS {
		logger.Errorf("#GetEKSClusterResources serialId is not eks")
		return
	}
	instanceId := tkeList[0].InstanceId

	request := sdkTke.NewGetEKSClusterResourcesRequest()
	request.ClusterIds = []*string{&instanceId}

	prof := qcloud.NewClientProfile()
	prof.HttpProfile.Endpoint = "tke.internal.tencentcloudapi.com"
	prof.HttpProfile.ReqTimeout = 300

	secretId, secretKey, err := service3.GetSecretIdAndKeyOfScs()
	if err != nil {
		logger.Errorf("#GetEKSClusterResources GetSecretIdAndKeyOfScs error %v", err)
		return
	}
	credential := common.NewCredential(secretId, secretKey)
	client, err := sdkTke.NewClient(credential, clusterGroup.Region, prof)
	if err != nil {
		logger.Errorf("#GetEKSClusterResources NewClient error %v", err)
		return
	}
	response, err := client.GetEKSClusterResources(request)
	if err != nil {
		logger.Errorf("#GetEKSClusterResources client.GetEKSClusterResources error %v", err)
		return
	}
	var cpu float64 = 0
	var mem float64 = 0
	if response != nil && response.Response != nil && len(response.Response.ClusterResources) > 0 {
		clusterResource := response.Response.ClusterResources[0]
		if len(clusterResource.SubnetResources) > 0 {
			subnetResource := clusterResource.SubnetResources[0]
			cpu = *subnetResource.CPU - EKS_BASE_CPU
			mem = *subnetResource.Memory - EKS_BASE_MEM_G
			podNum := subnetResource.PodNum
			service2.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
				tx.ExecuteSqlWithArgs("insert into EksResource(SerialId, ActualCpu, ActualMem, ActualPodNum) "+
					"values (?, ?, ?, ?) on duplicate KEY update ActualCpu = ?, ActualMem = ?, ActualPodNum = ?",
					clusterGroup.SerialId, cpu, mem, podNum, cpu, mem, podNum)
				return nil
			}).Close()
		}
	}
	if cpu != 0 && mem != 0 {
		cluster, err1 := clusterGroupService.GetCluster()
		if err1 != nil {
			logger.Errorf("#GetEKSClusterResources GetCluster error %v", err)
			return err1
		}
		totalRunningCpu, totalRunningMem, err1 := service3.GetClusterRunningCpuAndMem(clusterGroup.SerialId, clusterGroup.AppId, clusterGroup.Region, cluster.MemRatio)
		if err1 != nil {
			logger.Errorf("#GetEKSClusterResources GetRunningCU error %v", err)
			return err1
		}
		if err == nil && totalRunningCpu != 0 {
			service2.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
				tx.ExecuteSqlWithArgs("insert into EksResource(SerialId, UsedCpu, UsedMem) "+
					"values (?, ?, ?) on duplicate KEY update UsedCpu = ?, UsedMem = ?",
					clusterGroup.SerialId, totalRunningCpu, totalRunningMem, totalRunningCpu, totalRunningMem)
				return nil
			}).Close()
			diffCpu := (float32(cpu) - totalRunningCpu) / totalRunningCpu
			//diffMem := (mem - usedMem) / usedMem
			// 超过 20%
			if diffCpu > 0.2 {
				errMsg := fmt.Sprintf("## 地域 %s Eks Cluster %s 资源超用 百分之 %f", clusterGroup.Region, clusterGroup.SerialId, diffCpu*100)
				logger.Warningf(errMsg)
				n := notify.NewNotify()
				// 只发送给必要的人
				receiverList, _ := config.GetRainbowConfiguration("Notification", "tof_notification_eks_send_resource_receivers.csv")
				n.SendByCustomParamTof4("!!! EKS资源超用", errMsg, receiverList, "")
			}
		}
	}
	return nil
}

func GetEksResource() (list []*billingTable.BillingResource, err error) {

	return list, nil
}

// yesterday format 20060102
func getAllCalcDate(yesterday string) (allCalcDate []string) {
	result := make([]string, 0)
	for i := 0; i < 10; i++ {
		result = append(result, fmt.Sprintf("%s0%d", yesterday, i))
	}
	for i := 10; i < 24; i++ {
		result = append(result, fmt.Sprintf("%s%d", yesterday, i))
	}
	return result
}

// yesterday format 20060102
func check(yesterday string) (result []string) {
	allCalcDate := getAllCalcDate(yesterday)
	failCalDates := make([]string, 0)
	for _, calcDate := range allCalcDate {
		sql := "select * from Settle where CalcDate = ? and Status = ?"
		args := make([]interface{}, 0)
		args = append(args, calcDate)
		args = append(args, constants.SETTLE_DONE)
		txManager := service2.GetTxManager()
		_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
		if err != nil {
			/**
			 * 如果出现查询错误，默认可以继续操作， 推送计费支持重复推送
			 */
			logger.Warningf(fmt.Sprintf("# SendResourceAlarm 检查推送用量结算周期(%s)是否处理完成失败, 错误信息 %+v", calcDate, err))
			failCalDates = append(failCalDates, calcDate)
			continue
		}
		if len(data) != 1 {
			logger.Error(fmt.Sprintf("# SendResourceAlarm 检查推送用量结算周期(%s)是否处理完成, 目前没有处理完成", calcDate))
			failCalDates = append(failCalDates, calcDate)
			continue
		}
	}
	return failCalDates
}
