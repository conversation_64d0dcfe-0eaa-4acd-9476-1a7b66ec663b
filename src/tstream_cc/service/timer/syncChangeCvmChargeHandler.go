package timer

import (
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/timer"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
)

type syncChangeCvmChargeHandler struct {
}

func SyncChangeCvmChargeHandler() {
	config := map[string]interface{}{}
	config["times"] = 0
	config["duration"] = int64(60 * 60 * 12) //检测周期是12小时
	task := timer.NewTask(config)
	task.Handler = &syncChangeCvmChargeHandler{}
	task.Start()
}

/*
*

	内网集群，并且创建时间大于三个月的正常运行的包年包月集群的CVM修改CVM计费模式为包年包月
*/
func (d *syncChangeCvmChargeHandler) Do() {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("syncChangeCvmChargeHandler panic, with errors:%+v, stack %s", errs, util.Gostack())
		}
	}()

	logger.Infof("syncChangeCvmChargeHandler start to get TKE cluster group")

	tkeClusterGroupConditions, err := service2.ListInnerNetTkeClusterGroupConditions()
	if err != nil {
		logger.Errorf("syncChangeCvmChargeHandler to get tkeInstanceIds error, with errors:%+v", err)
		return
	}

	for _, tkeClusterGroupCondition := range tkeClusterGroupConditions {
		err = service2.ModifyInnerClusterChargeTypeByTkeInstanceId(tkeClusterGroupCondition.TkeInstanceId, tkeClusterGroupCondition.TkeRegion)
		if err != nil {
			logger.Errorf("syncChangeCvmChargeHandler tkeClusterGroupInstanceId[%d] syncChangeCvmChargeHandler error:%+v.", tkeClusterGroupCondition.TkeInstanceId, err)
			continue
		}
	}
}
