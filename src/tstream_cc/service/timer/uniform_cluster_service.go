package timer

import (
	"encoding/json"
	"fmt"
	v20170312 "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/cvm/v20170312"
	v20180525 "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/tke/v20180525"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"math"
	"runtime/debug"
	"strconv"
	"strings"
	"sync"
	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/dlocker"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service3 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/region"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/tke"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service4 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config/flowCC"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cvm"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/monitor"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/region2"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/tke"
	"time"
)

const (
	SA2 = "SA2.2XLARGE32"
	S5  = "S5.2XLARGE32"
)

type ClusterInfo struct {
	cuNum     float64
	elasticCu float64

	runningCu float64
}

type NodeInfo struct {
	cpuLimit float64 //7.8
	memLimit float64 //27908.94921875

	cpuReq  float64 //2.51
	memReq  float64 //7069.265
	ipLimit float64 //39
	ipAlloc float64 //2

	freeCU float64 //5.25
}

type CalcUniformClusterContext struct {
	uniformConfig   *table.UniformConfig
	uniformCalcTime string
	AppId           int32  //共享集群、独享资源
	Zone            string //共享集群、独享资源
	DeploymentMode  int    //共享集群、独享资源

	AgentGroup   *table.ClusterGroup
	agentCluster *table.Cluster
	agentTke     *table2.Tke

	podSpecMap map[float32]struct{}

	clusterGroups  []*table.ClusterGroup
	clusterInfoSet []*ClusterInfo

	k8sNodeMap     map[string]*v1.Node
	k8sZoneNodeMap map[string][]*v1.Node
	tkeInstanceMap map[string]*v20180525.Instance
	cvmMap         map[string]*v20170312.Instance // np- 云原生节点不在里面
	nodeInfoMap    map[string]*NodeInfo

	zoneInfoMap map[string]*table.ZoneInfo
	calcInfo    *table.CalcInfo
}

func (c *CalcUniformClusterContext) CalcUniformClusterCvm() {
	defer func() {
		if errs := recover(); errs != nil {
			c.logInfo("CalcUniformClusterCvm panic, with errors:%+v, %s", errs, string(debug.Stack()))
		}
	}()

	bizId := fmt.Sprintf("optId-%s-%d-%s-%d", c.AgentGroup.SerialId, c.AppId, c.Zone, c.DeploymentMode)
	locker := dlocker.NewDlocker("oceanus-calcUniformClusterCvm", bizId, 600)
	err := locker.Lock()
	if err != nil { // 加锁失败, 且没有 flowId, 就返回 flowId = 0 让计费重试
		logger.Errorf("CalcUniformClusterCvm - Another process has lock but not finished yet")
		return
	}
	defer func() {
		err := locker.UnLock()
		if err != nil {
			logger.Errorf("CalcUniformClusterCvm - could not fetch the lock. %+v", err)
		}
	}()

	c.logInfo("calc uniform cluster")

	err = c.calcUniformCluster()

	c.logInfo("Finished calc ClusterGroup: %+v", err)

	c.printContext()
}

func (c *CalcUniformClusterContext) calcUniformCluster() (err error) {
	c.calcInfo = &table.CalcInfo{}

	if err = c.fillAgent(); err != nil {
		return
	}

	if err = c.fillClusterGroups(); err != nil {
		return
	}

	if err = c.fillConfig(); err != nil {
		return
	}

	if err = c.checkAvailabilityZone(); err != nil {
		return
	}

	if err = c.calcClusterInfo(); err != nil {
		return
	}

	if err = c.calcPodSpecs(); err != nil {
		return
	}

	if err = c.fillClusterCvm(); err != nil {
		return
	}

	if err = c.calcNodeInfo(); err != nil {
		return
	}

	if err = c.calcZoneInfo(); err != nil {
		return
	}

	if err = c.calcNeedCvm(); err != nil {
		return
	}

	if err = c.calcMaxAddCvm(); err != nil {
		return
	}

	if err = c.buyCvm(); err != nil {
		return
	}

	if err = c.saveClusterInfo(); err != nil {
		return
	}

	return
}

func (c *CalcUniformClusterContext) logInfo(format string, v ...interface{}) {
	format = fmt.Sprintf("%s|%d|%s| %s", c.AgentGroup.SerialId, c.AppId, c.Zone, format)
	logger.Infof(format, v...)
}

func (c *CalcUniformClusterContext) printContext() {

	if c.uniformConfig == nil {
		logger.Errorf("uniformConfig is nil")
		return
	}
	if !c.uniformConfig.PrintInfo {
		return
	}

	c.logInfo("cluster ...")
	for _, group := range c.clusterGroups {
		c.logInfo("group %+v", group.SerialId)
	}
	for _, cInfo := range c.clusterInfoSet {
		c.logInfo("cInfo %+v", cInfo)
	}

	c.logInfo("cvm ...")
	for id, node := range c.k8sNodeMap {
		b, _ := json.Marshal(node)
		c.logInfo("node %s, %+v", id, string(b))
	}
	for id, node := range c.tkeInstanceMap {
		b, _ := json.Marshal(node)
		c.logInfo("instance %s, %+v", id, string(b))
	}
	for id, node := range c.cvmMap {
		b, _ := json.Marshal(node)
		c.logInfo("cvm %s, %+v", id, string(b))
	}

	for id, node := range c.nodeInfoMap {
		c.logInfo("nodeInfo %s, %+v", id, node)
	}

	for zone, info := range c.zoneInfoMap {
		b, _ := json.Marshal(info)
		c.logInfo("zoneInfo %s, %+v", zone, string(b))
	}

	return
}

func (c *CalcUniformClusterContext) fillAgent() (err error) {
	clusterGroupService, err := service4.NewClusterGroupService(c.AgentGroup.Id)
	if err != nil {
		return
	}

	if c.agentCluster, err = clusterGroupService.GetActiveCluster(); err != nil {
		return
	}

	if c.agentTke, err = clusterGroupService.GetTke(); err != nil {
		return
	}
	return
}

func (c *CalcUniformClusterContext) fillConfig() (err error) {
	if c.uniformConfig, err = c.AgentGroup.GetUniformConfig(); err != nil {
		logger.Errorf("get uniform config failed, err: %+v", err)
		return
	}
	if c.AppId <= 0 {
		c.uniformCalcTime = c.AgentGroup.UniformCalcTime
		return
	}
	c.uniformConfig = &table.UniformConfig{}
	for _, group := range c.clusterGroups {
		uc, err := group.GetUniformConfig()
		if err != nil {
			return err
		}
		if uc.BuySwitch {
			c.uniformConfig.BuySwitch = uc.BuySwitch
		}
		if uc.DisableRunBuySwitch {
			c.uniformConfig.DisableRunBuySwitch = uc.DisableRunBuySwitch
		}
		if uc.PrintInfo {
			c.uniformConfig.PrintInfo = uc.PrintInfo
		}
		if uc.DisableChooseZone {
			c.uniformConfig.DisableChooseZone = uc.DisableChooseZone
		}
		if uc.DelayMinute > 0 {
			c.uniformConfig.DelayMinute = uc.DelayMinute
		}
		if uc.MaxFreeCU > 0 {
			c.uniformConfig.MaxFreeCU = uc.MaxFreeCU
		}
		if uc.FreeCURate > 0 {
			c.uniformConfig.FreeCURate = uc.FreeCURate
		}
		if uc.MaxCURate > 0 {
			c.uniformConfig.MaxCURate = uc.MaxCURate
		}
		if uc.MinNeedCu > 0 {
			c.uniformConfig.MinNeedCu = uc.MinNeedCu
		}
		if len(uc.BlockedZones) > 0 {
			c.uniformConfig.BlockedZones = uc.BlockedZones
		}
		if uc.DisableAutoCleanBlockedZone {
			c.uniformConfig.DisableAutoCleanBlockedZone = uc.DisableAutoCleanBlockedZone
		}
		if uc.SuppressException {
			c.uniformConfig.SuppressException = uc.SuppressException
		}

		if group.UniformCalcTime != "" {
			c.uniformCalcTime = group.UniformCalcTime
		}
	}
	return
}

func (c *CalcUniformClusterContext) checkAvailabilityZone() (err error) {
	k8sService := k8s.GetK8sService()
	k8sClient, err := k8sService.NewClient([]byte(c.agentCluster.KubeConfig))
	if err != nil {
		return
	}

	if !c.uniformConfig.DisableAutoCleanBlockedZone {
		c.uniformConfig.CleanBlockedZone()
	}

	supportedZones, err := c.AgentGroup.GetSupportedZones()
	if err != nil {
		return
	}
	if c.AppId > 0 {
		supportedZones = c.getMultipleZone()
	}

	for _, zone := range supportedZones {
		ready, err := k8sService.CheckZoneReady(k8sClient, c.getZkNameSpace(zone))
		if err != nil {
			return err
		}
		if ready {
			continue
		}
		c.uniformConfig.AddBlockedZone(zone)
	}

	err = c.saveUniformConfig()
	return
}

func (c *CalcUniformClusterContext) getZkNameSpace(zone string) string {
	if c.AgentGroup.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM {
		return zone
	}
	return constants.OCEANUS_NAMESPACE
}

func (c *CalcUniformClusterContext) fillClusterGroups() (err error) {
	if c.AgentGroup.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE {
		c.clusterGroups = append(c.clusterGroups, c.AgentGroup)
		return
	}

	resourceType := constants.RESOURCE_TYPE_SHARE
	if c.AppId > 0 {
		resourceType = constants.RESOURCE_TYPE_PRIVATE
	}

	sql, args := dao.NewQueryBuilder("select * from ClusterGroup").
		WhereIn("Status", []int{constants.CLUSTER_GROUP_STATUS_RUNNING,
			constants.CLUSTER_GROUP_STATUS_SCALE_PROGRESS,
			constants.CLUSTER_GROUP_STATUS_SCALEDOWN_PROGRESS,
			constants.CLUSTER_GROUP_STATUS_UPGRADE}).
		WhereEq("Type", constants.CLUSTER_GROUP_TYPE_PRIVATE).
		WhereEq("AgentSerialId", c.AgentGroup.SerialId).
		WhereEq("ResourceType", resourceType).
		WhereEq("DeploymentMode", c.DeploymentMode). // 多可用区部署
		CWhereEq(c.AppId > 0, "AppId", c.AppId).
		CWhereEq(c.Zone != "", "Zone", c.Zone).
		OrderByDesc("CreateTime").
		Build()

	c.clusterGroups, err = service.Fetch[table.ClusterGroup](sql, args)

	if len(c.clusterGroups) == 0 {
		err = fmt.Errorf("can not find any clusters")
	}
	return
}

func (c *CalcUniformClusterContext) getSubGroup(parentSerialId string) (subGroup *table.ClusterGroup, err error) {
	sql, args := dao.NewQueryBuilder("select * from ClusterGroup").
		WhereEq("Status", constants.CLUSTER_GROUP_STATUS_RUNNING).
		WhereEq("Type", constants.CLUSTER_GROUP_TYPE_SUB_EKS).
		WhereEq("ParentSerialId", parentSerialId).
		Build()

	subGroup, err = service.FetchRow[table.ClusterGroup](sql, args)
	return
}

func (c *CalcUniformClusterContext) calcPodSpecs() (err error) {
	nonStandardPodSpecs := &model.DescribePodSpecsRsp{
		JobManagerSpec:  []float32{},
		TaskManagerSpec: []float32{},
	}

	standardPodSpecs := &model.DescribePodSpecsRsp{
		JobManagerSpec:  []float32{},
		TaskManagerSpec: []float32{},
	}

	podSpecMap := make(map[float32]struct{})

	group := constants.ConfRainbowGroupCommon
	if err = config.DecodeK8sObjectFromRainbowConfig(group, constants.ConfRainbowKeyNonStandardPodSpecs,
		nonStandardPodSpecs); err != nil {
		return
	}

	if err = config.DecodeK8sObjectFromRainbowConfig(group, constants.ConfRainbowKeyFineGrainedResourcePodSpecs,
		standardPodSpecs); err != nil {
		return
	}

	podSpecMap[0.25] = struct{}{}

	podSpecFunc := func(podSpec []float32) {
		for _, spec := range podSpec {
			podSpecMap[spec] = struct{}{}
		}
	}

	podSpecFunc(nonStandardPodSpecs.JobManagerSpec)
	podSpecFunc(nonStandardPodSpecs.TaskManagerSpec)
	podSpecFunc(standardPodSpecs.JobManagerSpec)
	podSpecFunc(standardPodSpecs.TaskManagerSpec)
	/**
	 * 目前计算资源余量，基准是PodSpec对应的资源剩余个数比如
	 * 0.25: 100个
	 * 2: 100个。
	 * 放开固定比例以后，
	* 需要设定一系列标准PodSpec，如果设置的资源不符合标准，就自动升配到最近的标准PodSpec
	*/
	podSpecFunc(getPodSpecs())

	c.podSpecMap = podSpecMap
	return
}

func getPodSpecs() (podSpecs []float32) {
	lowerBound := 0.25
	upperBound := 7.0
	step := 0.05

	specs := make([]float32, 0)
	for i := lowerBound; i < upperBound; i += step {
		specs = append(specs, float32(i))
	}
	return specs
}

func (c *CalcUniformClusterContext) calcClusterInfo() (err error) {
	waitGroupLen := len(c.clusterGroups)
	wg := sync.WaitGroup{}
	wg.Add(waitGroupLen)
	clusterInfoSetChn := make(chan interface{}, waitGroupLen)
	for i := 0; i < len(c.clusterGroups); i++ {
		clusterGroup := c.clusterGroups[i]
		fn := func(clusterGroup *table.ClusterGroup) interface{} {
			clusterInfo := &ClusterInfo{}
			clusterInfo.cuNum = float64(clusterGroup.CuNum)
			totalRunningCpu, totalRunningMem, err := service2.GetClusterRunningCpuAndMem(clusterGroup.SerialId, clusterGroup.AppId, clusterGroup.Region, c.agentCluster.MemRatio)
			if err != nil {
				return err
			}
			runningCu := service2.GetCuNumFromCpuMem(totalRunningCpu, totalRunningMem, c.agentCluster.MemRatio)
			clusterInfo.runningCu = float64(runningCu)

			if subGroup, err := c.getSubGroup(clusterGroup.SerialId); err != nil {
				return err
			} else if subGroup != nil {
				clusterInfo.elasticCu = float64(subGroup.CuNum)
			}
			return clusterInfo
		}

		go func() {
			clusterInfoSetChn <- fn(clusterGroup)
			wg.Done()
		}()
	}

	wg.Wait()
	close(clusterInfoSetChn)

	for result := range clusterInfoSetChn {
		if err, ok := result.(error); ok {
			return err
		} else if clusterInfo, ok := result.(*ClusterInfo); ok {
			c.clusterInfoSet = append(c.clusterInfoSet, clusterInfo)
		}
	}
	return
}

func (c *CalcUniformClusterContext) getMultipleZone() []string {
	supportedZones := []string{c.Zone}
	if c.DeploymentMode == constants.DeploymentModeMultiple {
		supportedZones, _ = c.AgentGroup.GetSupportedZones()
	}
	return supportedZones
}

func (c *CalcUniformClusterContext) fillClusterCvm() (err error) {
	workerLabel := map[string]string{constants.TKE_CVM_LABEL_KEY: constants.TKE_WORKER_NODE_LABEL_VAL}

	if c.AgentGroup.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM {
		workerLabel[constants.TKE_CVM_NODE_RESOURCE_TYPE] = constants.TKE_CVM_NODE_RESOURCE_TYPE_SHARE
	}

	if c.AppId > 0 {
		if c.DeploymentMode != constants.DeploymentModeMultiple {
			workerLabel[constants.TKE_CVM_NODE_ZONE] = c.Zone
		}
		workerLabel[constants.TKE_CVM_NODE_APPID] = strconv.FormatInt(int64(c.AppId), 10)
		workerLabel[constants.TKE_CVM_NODE_RESOURCE_TYPE] = constants.TKE_CVM_NODE_RESOURCE_TYPE_PRIVATE
	}

	k8sService := k8s.GetK8sService()
	k8sClient, err := k8sService.NewClient([]byte(c.agentCluster.KubeConfig))
	if err != nil {
		return
	}
	workerList, err := k8sService.ListNode(k8sClient, metav1.ListOptions{
		LabelSelector: labels.FormatLabels(workerLabel),
	})
	if err != nil {
		return
	}
	workIdSet := make([]*string, 0)
	k8sNodeMap := make(map[string]*v1.Node)
	c.k8sNodeMap = k8sNodeMap
	k8sZoneNodeMap := make(map[string][]*v1.Node)
	c.k8sZoneNodeMap = k8sZoneNodeMap
	for _, node := range workerList.Items {
		tmpNode := node
		labelMap := tmpNode.GetObjectMeta().GetLabels()
		if instanceId, ok := labelMap["cloud.tencent.com/node-instance-id"]; ok {
			k8sNodeMap[instanceId] = &tmpNode
		} else {
			return fmt.Errorf("not find instance id for %s, %+v", tmpNode.Name, tmpNode)
		}
	}

	tkeInstanceMap := make(map[string]*v20180525.Instance)
	c.tkeInstanceMap = tkeInstanceMap

	tkeService := tke.GetTkeService()
	_, instanceSet, err := tkeService.DescribeClusterAllInstancesWithScsAccountByNetEnvironmentType(
		c.AgentGroup.NetEnvironmentType,
		c.AgentGroup.Region,
		c.agentTke.InstanceId)

	for _, instance := range instanceSet {
		tmpInstance := instance
		if _, ok := k8sNodeMap[*tmpInstance.InstanceId]; !ok {
			continue
		}
		if *tmpInstance.InstanceState == constants.TKE_INSTANCE_STATE_FAILED {
			delete(k8sNodeMap, *tmpInstance.InstanceId)
			c.calcInfo.ErrorInstanceSet = append(c.calcInfo.ErrorInstanceSet, *tmpInstance.InstanceId)
			continue
		}
		if strings.HasPrefix(*tmpInstance.InstanceId, "ins-") { // except np-
			workIdSet = append(workIdSet, tmpInstance.InstanceId)
		}
		tkeInstanceMap[*tmpInstance.InstanceId] = tmpInstance
	}

	cvmService := cvm.GetCvmService()
	cvmMap := make(map[string]*v20170312.Instance)
	c.cvmMap = cvmMap
	cvmInstanceSet, err := cvmService.DescribeInstancesWithScsAccount(c.AgentGroup.NetEnvironmentType, c.AgentGroup.Region, workIdSet)
	if err != nil {
		return err
	}

	for _, instance := range cvmInstanceSet {
		tmpInstance := instance
		if !(*tmpInstance.InstanceState == "PENDING" || *tmpInstance.InstanceState == "RUNNING") {
			delete(k8sNodeMap, *tmpInstance.InstanceId)
			delete(tkeInstanceMap, *tmpInstance.InstanceId)
			c.calcInfo.ErrorInstanceSet = append(c.calcInfo.ErrorInstanceSet, *tmpInstance.InstanceId)
			continue
		}
		cvmMap[*tmpInstance.InstanceId] = tmpInstance
	}

	if len(k8sNodeMap) != len(tkeInstanceMap) {
		return fmt.Errorf("map length not equel, %d, %d, %d", len(k8sNodeMap), len(tkeInstanceMap), len(cvmMap))
	}

	for _, node := range k8sNodeMap {
		tmpNode := node

		if c.AgentGroup.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE {
			k8sZoneNodeMap[c.AgentGroup.Zone] = append(k8sZoneNodeMap[c.AgentGroup.Zone], tmpNode)
		} else {
			labelMap := tmpNode.GetObjectMeta().GetLabels()
			if zone, ok := labelMap[constants.TKE_CVM_NODE_ZONE]; ok {
				k8sZoneNodeMap[zone] = append(k8sZoneNodeMap[zone], tmpNode)
			} else {
				return fmt.Errorf("not find zone for %s, %+v", tmpNode.Name, tmpNode)
			}
		}
	}

	for zone, nodes := range k8sZoneNodeMap {
		for _, node := range nodes {
			labelMap := node.GetObjectMeta().GetLabels()
			instanceId := labelMap["cloud.tencent.com/node-instance-id"]
			cvmInstance, ok := cvmMap[instanceId]
			if !ok {
				continue
			}
			if cvmInstance.Placement == nil {
				continue
			}
			if *cvmInstance.Placement.Zone == zone {
				continue
			}
			return fmt.Errorf("cvm zone is not crrect, Id %s, zone %s, real zone %s", instanceId, zone, *cvmInstance.Placement.Zone)
		}
	}

	return
}

func (c *CalcUniformClusterContext) calcNodeInfo() (err error) {
	now := time.Now()
	startTime := now.Add(-10 * time.Minute).Format("2006-01-02T15:04:05+08:00")
	endTime := now.Format("2006-01-02T15:04:05+08:00")

	if len(c.tkeInstanceMap) <= 0 {
		c.logInfo("tkeInstanceMap is Empty")
		return
	}

	instanceIdSet := make([]*string, 0)
	for instanceId := range c.tkeInstanceMap {
		tmpInstanceId := instanceId
		instanceIdSet = append(instanceIdSet, &tmpInstanceId)
	}

	sd, err := monitor.DescribeTkeStatisticData(c.AgentGroup, c.agentTke, instanceIdSet, startTime, endTime)

	if err != nil {
		return
	}

	if sd.Response.Data == nil {
		return fmt.Errorf("DescribeTkeStatisticData Data is Empty, %s", c.AgentGroup.SerialId)
	}

	b, _ := json.Marshal(sd.Response.Data)

	logger.Debugf("DescribeTkeStatisticData, Cluster: %s, data %s", c.AgentGroup.SerialId, string(b))

	nodeInfoMap := make(map[string]*NodeInfo)

	for _, md := range sd.Response.Data {
		for _, point := range md.Points {
			var instanceId *string
			for _, dimension := range point.Dimensions {
				if *dimension.Name == "un_instance_id" {
					instanceId = dimension.Value
				}
			}
			if instanceId == nil {
				b, _ := json.Marshal(sd.Response.Data)
				c.logInfo("can not find instance_id, %s", string(b))
				return fmt.Errorf("can not find instance_id, %s", c.AgentGroup.SerialId)
			}
			nodeInfo, ok := nodeInfoMap[*instanceId]
			if !ok {
				nodeInfo = &NodeInfo{}
			}
			nodeInfoMap[*instanceId] = nodeInfo

			var metricValue *float64
			var timestamp uint64
			for _, value := range point.Values {
				if *value.Timestamp > timestamp && value.Value != nil {
					metricValue = value.Value
					timestamp = *value.Timestamp
				}
			}
			if metricValue == nil {
				b, _ := json.Marshal(sd.Response.Data)
				c.logInfo("can not find metricValue, %s, %s, %s", c.AgentGroup.SerialId, *instanceId, string(b))
				if !c.uniformConfig.SuppressException {
					return fmt.Errorf("can not find metricValue, %s, %s", c.AgentGroup.SerialId, *instanceId)
				}
				var defaultMetricValue float64
				metricValue = &defaultMetricValue
			}
			if *md.MetricName == "K8sNodeCpuCoreRequestTotal" {
				nodeInfo.cpuReq = *metricValue
			} else if *md.MetricName == "K8sNodeMemoryRequestBytesTotal" {
				nodeInfo.memReq = *metricValue
			} else if *md.MetricName == "K8sNodeAllocatedIpTotal" {
				nodeInfo.ipAlloc = *metricValue
			} else {
				b, _ := json.Marshal(sd.Response.Data)
				c.logInfo("can not find metric, %s", string(b))
				return fmt.Errorf("can not find metric, %s", c.AgentGroup.SerialId)
			}
		}
	}
	c.nodeInfoMap = nodeInfoMap
	return
}

func (c *CalcUniformClusterContext) calcZoneInfo() (err error) {
	supportedZones, err := c.AgentGroup.GetSupportedZones()
	if err != nil {
		return
	}
	if c.AppId > 0 {
		supportedZones = c.getMultipleZone()
	}

	c.zoneInfoMap = make(map[string]*table.ZoneInfo)

	for _, zone := range supportedZones {
		c.zoneInfoMap[zone] = &table.ZoneInfo{}
	}

	req := &region.DescribeFineGrainedResourceZonesReq{RequestBase: apiv3.RequestBase{
		AppId:  int64(c.AgentGroup.AppId),
		Uin:    c.AgentGroup.OwnerUin,
		Region: c.AgentGroup.Region,
	}}
	zoneService := region2.NewDescribeFineGrainedResourceZonesService(req)

	resp, err := zoneService.DescribeFineGrainedResourceZones()
	if err != nil {
		return
	}

	b, _ := json.Marshal(resp)
	logger.Debugf("DescribeFineGrainedResourceZones, Cluster: %s, data %s", c.AgentGroup.SerialId, string(b))

	for _, quota := range resp.ZoneCvmQuota {
		zoneInfo, ok := c.zoneInfoMap[quota.Zone]
		if !ok {
			continue
		}
		if quota.InstanceType == SA2 {
			zoneInfo.HasSA2 += quota.InstanceQuota
		} else if quota.InstanceType == S5 {
			zoneInfo.HasS5 += quota.InstanceQuota
		}
	}

	for zone, zoneNodeSet := range c.k8sZoneNodeMap {
		zoneInfo, ok := c.zoneInfoMap[zone]
		if !ok {
			continue
		}
		zoneInfo.NumOfCuMap = make(map[string]int64)

		for _, node := range zoneNodeSet {
			labelMap := node.GetObjectMeta().GetLabels()
			instanceId := labelMap["cloud.tencent.com/node-instance-id"]

			nodeInfo := c.nodeInfoMap[instanceId]
			c.logInfo("nodeInfo: %s, %+v", instanceId, nodeInfo)

			nodeInfo.cpuLimit = 7.8
			nodeInfo.memLimit = 27908.94921875
			nodeInfo.ipLimit = 39

			baseMem := 1.0
			if c.AgentGroup.CuMem == 2 {
				nodeInfo.memLimit = 12763.95703125
				baseMem = 2.0 / 4.0
			} else if c.AgentGroup.CuMem == 8 {
				nodeInfo.memLimit = 58198.93359375
				baseMem = 8.0 / 4.0
			}

			allocatable := node.Status.Allocatable
			//7800m
			if cpu, ok := allocatable[v1.ResourceCPU]; ok {
				nodeInfo.cpuLimit = float64(cpu.MilliValue()) / 1000
			}
			//28578764Ki
			if mem, ok := allocatable[v1.ResourceMemory]; ok {
				nodeInfo.memLimit = float64(mem.Value()) / 1024 / 1024
			}
			//39
			if ip, ok := allocatable["tke.cloud.tencent.com/direct-eni"]; ok {
				nodeInfo.ipLimit = float64(ip.Value())
			}

			cCpu := float64(int64((nodeInfo.cpuLimit-nodeInfo.cpuReq)/0.25)) * 0.25
			cMem := float64(int64((nodeInfo.memLimit-nodeInfo.memReq)/(3900*baseMem)/0.25)) * 0.25
			logger.Infof("#allocatable %+v ##cCpu %f ##cMem %f ", allocatable, cCpu, cMem)
			nodeInfo.freeCU = math.Min(cCpu, cMem)

			zoneInfo.LimitCPU += math.Ceil(nodeInfo.cpuLimit)

			zoneInfo.ReqCPU += nodeInfo.cpuReq
			zoneInfo.ReqMem += nodeInfo.memReq

			zoneInfo.FreeCU += nodeInfo.freeCU
			for i := range c.podSpecMap {
				zoneInfo.NumOfCuMap[fmt.Sprintf("%f", i)] += int64(nodeInfo.freeCU / float64(i))
			}
		}
	}

	return
}

func (c *CalcUniformClusterContext) calcMaxAddCvm() (err error) {
	//calc max cvm count
	cc, err := flowCC.New(c.AgentGroup.Region).TkeCC().ClusterConfig(c.AgentGroup)
	if err != nil {
		return
	}
	reservedCuPerNode := cc.ReservedCuPerNode
	if c.agentTke.ArchGeneration >= constants.TKE_ARCH_GENERATION_V2 {
		reservedCuPerNode = cc.ReservedCuPerNodeV3
	}
	if ok, w := auth.WhiteListValue(int64(c.AgentGroup.AppId), constants.WHITE_LIST_RESERVED_CU_PER_NODE); ok {
		cu, _ := w.GetIntVal()
		if cu > 0 {
			reservedCuPerNode = int64(cu)
		}
	}

	controllerCuNum := 0
	if c.AgentGroup.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM || c.AgentGroup.AgentSerialId != "" {
		// 统一资源池不预留cu
	} else if c.agentCluster.MemRatio == constants.CVM_MEMRATIO_2 {
		controllerCuNum = 7 //7个CU的管控服务占用
	} else if c.agentTke.ArchGeneration >= constants.TKE_ARCH_GENERATION_V5 {
		controllerCuNum = 5 //5个CU的管控服务占用
	} else if c.agentTke.ArchGeneration >= constants.TKE_ARCH_GENERATION_V2 {
		controllerCuNum = 2 //2个CU的管控服务占用
	}

	maxCURate := 1.5
	if c.uniformConfig.MaxCURate > 0 {
		maxCURate = c.uniformConfig.MaxCURate
	}

	var allCPU float64
	for _, info := range c.zoneInfoMap {
		allCPU += info.LimitCPU
	}
	allCU := allCPU - float64(len(c.k8sNodeMap))*float64(reservedCuPerNode) - float64(controllerCuNum)
	needCU := c.calcInfo.CuNeed + c.calcInfo.CuElastic

	canAddCu := needCU*maxCURate - allCU
	if canAddCu <= 0 {
		canAddCu = 0
	}

	c.calcInfo.MaxAddWorkNum = int(canAddCu / float64(int64(8)-reservedCuPerNode))
	if c.calcInfo.MaxAddWorkNum < 0 {
		c.calcInfo.MaxAddWorkNum = 0
	}
	return
}

func (c *CalcUniformClusterContext) calcNeedCvm() (err error) {
	if len(c.clusterInfoSet) <= 0 {
		c.logInfo("clusterInfoSet is Empty")
		return
	}

	var cuNeed, cuElastic, cuRunning, maxFreeCU float64
	for _, group := range c.clusterGroups {
		cuNeed += float64(group.CuNum)
	}
	for _, info := range c.clusterInfoSet {
		cuElastic += info.elasticCu
		cuRunning += info.runningCu
		maxFreeCU = math.Max(info.cuNum+info.elasticCu-info.runningCu, maxFreeCU)
	}

	c.calcInfo.CuNeed = cuNeed
	c.calcInfo.CuElastic = cuElastic
	c.calcInfo.CuRunning = cuRunning
	c.calcInfo.MaxFreeCU = maxFreeCU

	c.logInfo("cuNeed = %f, cuElastic = %f, cuRunning = %f, maxFreeCU = %f", cuNeed, cuElastic, cuRunning, maxFreeCU)

	var cuFree float64
	for _, info := range c.zoneInfoMap {
		cuFree += info.FreeCU
	}

	c.logInfo("cuFree = %f", cuFree)
	c.calcInfo.CuFree = cuFree

	defaultMaxFreeCU := 200.0
	if c.uniformConfig.MaxFreeCU > 0 {
		defaultMaxFreeCU = float64(c.uniformConfig.MaxFreeCU)
	}
	c.logInfo("defaultMaxFreeCU = %f", defaultMaxFreeCU)

	//最大预留CU数量
	maxFreeCU = math.Min(maxFreeCU, defaultMaxFreeCU)
	c.logInfo("maxFreeCU = %f", maxFreeCU)
	c.calcInfo.MaxFreeCU = maxFreeCU

	freeCURate := 0.6
	if c.AgentGroup.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE {
		freeCURate = 1
	}
	if c.uniformConfig.FreeCURate > 0 {
		freeCURate = c.uniformConfig.FreeCURate
	}
	c.logInfo("freeCURate = %f", freeCURate)
	c.calcInfo.FreeCURate = freeCURate
	//先计算总的CU
	//cuNeed - cuRunning ~ cuFree
	//cuNeed + cuElastic - cuRunning ~ cuFree
	//maxFreeCU
	reserveCu := math.Max((cuNeed+cuElastic-cuRunning)*freeCURate, maxFreeCU)
	c.logInfo("reserveCu = %f", reserveCu)
	c.calcInfo.ReserveCu = reserveCu

	//再计算每个区需要的CU，最小预留14CU
	if len(c.zoneInfoMap) <= 2 {
		for _, info := range c.zoneInfoMap {
			info.NeedCU = reserveCu
		}
	} else {
		for _, info := range c.zoneInfoMap {
			info.NeedCU = reserveCu * 3 / 2 / float64(len(c.zoneInfoMap))
		}
	}
	minNeedCu := 14.0
	if c.AgentGroup.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE {
		minNeedCu = 0
	}
	if c.uniformConfig.MinNeedCu > 0 {
		minNeedCu = c.uniformConfig.MinNeedCu
	}

	for _, info := range c.zoneInfoMap {
		c.logInfo("zoneInfo = %+v", info)
		info.NeedCU = math.Max(info.NeedCU, minNeedCu)
		if info.NeedCU <= info.FreeCU {
			continue
		}
		info.NeedCvm = int64(math.Ceil((info.NeedCU - info.FreeCU) / 7))
	}
	return
}

func (c *CalcUniformClusterContext) buyCvm() (err error) {

	isMutiple := false
	isMainZoneOk := true
	if c.AppId > 0 && c.DeploymentMode == constants.DeploymentModeMultiple {
		isMutiple = true
		if c.uniformConfig.IsBlockedZone(c.Zone) {
			isMainZoneOk = false
		}
		logger.Infof("[%s] isMutiple = %v, isMainZoneOk = %v", c.AgentGroup.SerialId, isMutiple, isMainZoneOk)
	}
	for zone, info := range c.zoneInfoMap {
		if c.uniformConfig.IsBlockedZone(zone) {
			continue
		}

		if info.NeedCvm <= 0 {
			continue
		}

		if !c.uniformConfig.BuySwitch {
			continue
		}
		// 第一次初始化
		if isMutiple && isMainZoneOk && zone != c.Zone && info.FreeCU < 1 {
			logger.Infof("deal slave zone isMutiple = %v, isMainZoneOk = %v, slave zone = %s, main zone = %s, FreeCu %f", isMutiple, isMainZoneOk, zone, c.Zone, info.FreeCU)
			slaveNeedCvm := int64(math.Ceil(float64(GetSlaveCu(c.calcInfo.CuNeed, c.AppId) / constants.DefaultCuPerCvm)))
			logger.Infof("[%s] slaveNeedCvm = %d", c.AgentGroup.SerialId, slaveNeedCvm)
			info.NeedCvm = slaveNeedCvm
		}

		if err = c.flowCreateCvm(zone, info.NeedCvm); err != nil {
			return err
		}

		info.BuyCvmCount = info.NeedCvm
	}
	return
}

func GetSlaveCu(cuNum float64, appId int32) int {
	// 备可用区 默认初始化 50%的资源
	slaveCu := int(math.Round(cuNum / constants.SLAVE_ZONE_CU_RATIO))
	if ok, w := auth.WhiteListValue(int64(appId), constants.WHITE_LIST_MULTIPLE_DEPLOYMENT_MODE_SLAVE_CU_NUM); ok {
		slaveZoneCuRatio, _ := w.GetFloat64Val()
		if slaveZoneCuRatio > 0 {
			slaveCu = int(math.Round(cuNum * slaveZoneCuRatio))
		}
	}
	return slaveCu
}

func (c *CalcUniformClusterContext) flowCreateCvm(zone string, count int64) (err error) {
	clusterGroup := c.AgentGroup
	cluster := c.agentCluster
	tkeInstance := c.agentTke

	if c.AppId > 0 {
		clusterGroup = c.clusterGroups[0]
		clusterGroupService, err := service4.NewClusterGroupService(clusterGroup.Id)
		if err != nil {
			return err
		}

		if cluster, err = clusterGroupService.GetActiveCluster(); err != nil {
			return err
		}

		if tkeInstance, err = clusterGroupService.GetTke(); err != nil {
			return err
		}
	}

	action := constants.FLOW_PARAM_FLOW_ACTION_CREATE_CVM
	//启动transform流程
	docId := fmt.Sprintf("%s_%s", clusterGroup.SerialId, action)

	flowId, err := flow.CreateFlow(constants.FLOW_OCEANUS_UPGRADE_WORKER, docId, 0, map[string]string{
		constants.FLOW_PARAM_REQUEST_ID:       fmt.Sprintf("%s", docId),
		constants.FLOW_PARAM_CLUSTER_GROUP_ID: fmt.Sprintf("%d", clusterGroup.Id),
		constants.FLOW_PARAM_CLUSTER_ID:       fmt.Sprintf("%d", cluster.Id),
		constants.FLOW_PARAM_CLUSTER_TYPE:     fmt.Sprintf("%d", tkeInstance.ClusterType),
		constants.FLOW_PARAM_FLOW_ACTION:      fmt.Sprintf("%s", action),
		constants.FLOW_PARAM_ZONE:             fmt.Sprintf("%s", zone),
		constants.FlowParamNeedCvmCount:       fmt.Sprintf("%d", count),
	}, nil)
	if err != nil {
		err = fmt.Errorf("[%s] createFlow %s fail %v", docId, constants.FLOW_OCEANUS_UPGRADE_WORKER, err)
		return
	}
	logger.Info("[%s] createFlow %s success, flowId %d", docId, constants.FLOW_OCEANUS_UPGRADE_WORKER, flowId)
	return
}

func (c *CalcUniformClusterContext) saveClusterInfo() (err error) {

	calcTime := util.GetCurrentTime()

	uniformCalcInfo := table.UniformCalcInfo{
		ZoneInfoMap: c.zoneInfoMap,
		CalcInfo:    c.calcInfo,
		CalcTime:    calcTime,
	}

	b, err := json.Marshal(uniformCalcInfo)
	if err != nil {
		return
	}

	if c.uniformConfig.BuySwitch {
		c.uniformCalcTime = calcTime
	}

	serialIdSet := make([]string, 0)
	if c.AppId > 0 {
		for _, group := range c.clusterGroups {
			serialIdSet = append(serialIdSet, group.SerialId)
		}
	} else {
		serialIdSet = append(serialIdSet, c.AgentGroup.SerialId)
	}

	service3.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		sql, args := dao.NewUpdateBuilder("UPDATE ClusterGroup").
			Set("UniformCalcInfo", string(b)).
			Set("UniformCalcTime", c.uniformCalcTime).
			WhereIn("SerialId", serialIdSet).
			Build()

		service.TExecute(tx, sql, args)
		return nil
	}).Close()

	return
}

func (c *CalcUniformClusterContext) saveUniformConfig() (err error) {
	b, err := json.Marshal(c.uniformConfig)
	if err != nil {
		return
	}

	serialIdSet := make([]string, 0)
	if c.AppId > 0 {
		for _, group := range c.clusterGroups {
			serialIdSet = append(serialIdSet, group.SerialId)
		}
	} else {
		serialIdSet = append(serialIdSet, c.AgentGroup.SerialId)
	}

	service3.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		sql, args := dao.NewUpdateBuilder("UPDATE ClusterGroup").
			Set("UniformConfig", string(b)).
			WhereIn("SerialId", serialIdSet).
			Build()

		service.TExecute(tx, sql, args)
		return nil
	}).Close()

	return
}
