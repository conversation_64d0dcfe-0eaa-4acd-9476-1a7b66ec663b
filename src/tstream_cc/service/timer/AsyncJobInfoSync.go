package timer

import (
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/timer"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/etl"
)

type asyncJobInfoSyncHandler struct {
}

func AsyncJobInfoSync() {
	checkDuration := 60 * 60 // 默认检测周期是 1 小时

	// 注意以下配置的单位是纳秒

	config := map[string]interface{}{}
	config["times"] = 0 // Config 必须有 times 和 checkDuration 两个属性, 0 表示无限循环
	config["duration"] = int64(checkDuration)
	task := timer.NewTask(config)
	task.Handler = &asyncJobInfoSyncHandler{}
	task.Start()
}

func (*asyncJobInfoSyncHandler) Do() {
	asyncJobInfos, err := service3.GetAsyncJobInfoByType(constants.ASYNC_JOB_INFO_TYPE_ETL_PRE_CHECK, constants.ASYNC_JOB_INFO_STATUS_SUCCESS, constants.ASYNC_JOB_INFO_STATUS_FAIL)
	if err != nil {
		logger.Errorf("GetResourceConfigByStatus return err:%+v", err)
		return
	}
	if asyncJobInfos == nil || len(asyncJobInfos) == 0 {
		return
	}
	logger.Infof("resourceLoc need to sync count %d", len(asyncJobInfos))
	for _, asyncJobInfo := range asyncJobInfos {
		service3.AsyncJobInfoClean(asyncJobInfo.Id)

	}
}
