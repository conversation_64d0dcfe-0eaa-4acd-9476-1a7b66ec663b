package timer

//var (
//	fTestTime  = flag.Int64("test.time", 60*60*24*14, "")
//	fTestDbUrl = flag.String("test.db.url", "root:123456@tcp(127.0.0.1:3306)/galileo?charset=utf8", "")
//)
//
//func init() {
//	testing.Init()
//	flag.Parse()
//	tx, err := dao.NewDataSourceTransactionManager(*fTestDbUrl)
//	if err != nil {
//		fmt.Println("darwin Process create DataSourceTransactionManager err", err)
//		os.Exit(-1)
//	}
//	service.SetTxManager(tx)
//}
