package timer

import (
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/dlocker"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/timer"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service2 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job"
	job_autoscale2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_autoscale"
	table3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/auto_tuning"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job_config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/job_autoscale"
	"time"
)

// 1.定时扫描时间规则
// 2.根据时间规则创建调度
// 3.每次扫描删除已经停止的调度任务
// 4.调度任务定期创建 action 插入，action 新增资源 rule type，以应对不同的 rule，后续还有双跑的 rule 等
// 5.调度任务插入扩容 action 时屏蔽其他规则，将其他 action 设为 ignore - 检测是否正在进行扩缩容？

func AutoScaleTimeBased() {
	config := map[string]interface{}{}
	config["times"] = 0                                                  // Config 必须有 times 和 checkDuration 两个属性, 0 表示无限循环
	config["duration"] = int64(constants.AUTO_SCALE_TIME_BASED_INTERVAL) // 执行间隔 in second
	task := timer.NewTask(config)
	task.Handler = &autoScaleTimeBasedHandler{}
	task.Start()
}

var LOC, _ = time.LoadLocation(constants.TIME_ZONE)

type autoScaleTimeBasedHandler struct {
}

func (*autoScaleTimeBasedHandler) Do() {
	defer func() {
		if err := recover(); err != nil {
			err = errorcode.NewStackError(errorcode.InternalError_Timer, "Failed to run AutoScaleTimeBased", err.(error))
			logger.Errorf("Failed to run AutoScaleJob, with errors:%+v", err)
		}
	}()

	locker := dlocker.NewDlocker("oceanus-AutoScaleTimeBasedTimer", fmt.Sprintf("optId-%s", "AutoScaleTimeBasedTimer"), 3600)
	err := locker.Lock()
	if err != nil { // 加锁失败, 且没有 flowId, 就返回 flowId = 0 让计费重试
		logger.Infof("AutoScaleTimeBased - Another auto scale process has lock but not finished yet")
		return
	}
	defer func() {
		err := locker.UnLock()
		if err != nil {
			logger.Errorf("AutoScaleTimeBased - could not fetch the lock. %+v", err)
		}
	}()

	logger.Infof("AutoScaleTimeBased - Start time based auto scale checking, start time: %s", util.GetCurrentTime())
	// 1.定时查询基于时间的自动扩缩容规则
	jobScaleRules, err := job_autoscale.QueryJobScaleRulesByType("", 0, constants.SCALE_RULES_STATUS_ACTIVE, constants.SCALE_RULES_AUTO_SCALE_TIME_BASED, "")
	logger.Infof("AutoScaleTimeBased - active jobScaleRules num: %d", len(jobScaleRules))
	if err != nil {
		logger.Errorf("AutoScaleTimeBased - failed to query auto_scale_time_based job Scale Rules, because: %v", err)
		return
	}
	for _, rule := range jobScaleRules {
		locker1 := dlocker.NewDlocker(fmt.Sprintf("optId-%s-%s", "IgnoreJobTuningAction", rule.SerialId), fmt.Sprintf("optId-%s-%s", "IgnoreJobTuningAction", rule.SerialId), 5)
		logger.Debugf("AutoScaleTimeBased - IgnoreJobTuningAction - locker1.TryLock: %+v", rule)
		err = locker1.Lock()
		if err != nil {
			logger.Infof("AutoScaleTimeBased-Another process has lock but not finished yet")
			continue
		}
		defer func() {
			logger.Debugf("AutoScaleTimeBased - IgnoreJobTuningAction - Unlocker1.TryLock: %+v", rule)
			err := locker1.UnLock()
			if err != nil {
				logger.Errorf("AutoScaleTimeBased - could not fetch the lock. %+v", err)
			}
		}()
		rule, err = job_autoscale.QueryJobScaleRulesById("", 0, "", rule.SerialId)
		if err != nil {
			logger.Errorf("AutoScaleTimeBased - Fail to query auto_scale_time_based job Scale Rules:%s, because: %v", rule.SerialId, err)
			continue
		}
		logger.Debugf("AutoScaleTimeBased - Begin to check jobScaleRule: %+v", rule)
		job, err := maybeGetJob(rule.JobId, []int64{int64(constants.JOB_STATUS_RUNNING), int64(constants.JOB_STATUS_DELETE)})
		if err != nil {
			logger.Warningf("AutoScaleTimeBased - Fail to get running Job by ID:%s, err:%+v", rule.JobId, err)
			// 不能在这里 cancel job，可能遇到作业重启的情况
			continue
		}
		if job.Status == constants.JOB_STATUS_DELETE {
			err := job_autoscale.ModifyJobScaleRuleStatusByRuleId("", []string{rule.SerialId}, constants.SCALE_RULES_STATUS_DELETE, int64(job.AppId))
			if err != nil {
				logger.Errorf("AutoScaleTimeBased - Fail to modify JobScaleRule Status to DELETE by RuleId:%s, err:%+v", rule.SerialId, err)
				return
			}
		}
		jobInstances, err := service.ListJobInstances(job.Id, constants.JOB_INSTANCE_STATUS_RUNNING)
		if err != nil {
			logger.Errorf("AutoScaleTimeBased - Fail to get JobInstance by ID:%s, err:%+v", rule.JobId, err)
			continue
		}
		if len(jobInstances) != 1 {
			logger.Errorf("AutoScaleTimeBased - JobInstance is not running for job ID:%s", rule.JobId)
			continue
		}

		// 解析 json 配置
		scaleConfig, err := parseTimeBasedScheduleConfig(rule)
		if err != nil {
			logger.Errorf("AutoScaleTimeBased - Fail to parseTimeBasedScheduleConfig for rule: %s, err:%+v", rule.SerialId, err)
			continue
		}
		onceTime := scaleConfig.OnTime
		if scaleConfig.TimeUnit == constants.SCALE_RULES_TIMEUNIT_ONCE {
			newTime := strings.Split(scaleConfig.OnTime, " ")
			if len(newTime) != 2 {
				logger.Errorf("AutoScaleTimeBased - Once JobScaleRule Time is wrong: %s", rule.SerialId)
				continue
			}
			scaleConfig.OnTime = newTime[1]
		}
		//兼容旧的定时任务逻辑
		if rule.PlanSerialId != "" {
			logger.Debugf("AutoScaleTimeBased - execute time is over now for rule: %s", rule.SerialId)
			//在这里判断类型，可以让用户修改了时间也可以在之前取消掉之前的任务，再判断是否在今天加回去
			switch scaleConfig.TimeUnit {
			case constants.SCALE_RULES_TIMEUNIT_ONCE:
				// 定义日期格式
				layout := constants.TIME_SAMPLE
				// 解析日期字符串
				t1, err := time.ParseInLocation(layout, onceTime, LOC)
				if err != nil {
					logger.Errorf("AutoScaleTimeBased - Fail to ParseInLocation Time for rule: %s", rule.SerialId)
					continue
				}
				year := t1.Year()
				month := t1.Month()
				day := t1.Day()
				now := time.Now()
				if year != now.Year() || month != now.Month() || day != now.Day() {
					continue
				}
			case constants.SCALE_RULES_TIMEUNIT_DAY:
			case constants.SCALE_RULES_TIMEUNIT_MONTH:
				now := time.Now()
				day := now.Day()
				flag := contains(scaleConfig.Index, day)
				if !flag {
					continue
				}
			case constants.SCALE_RULES_TIMEUNIT_WEEK:
				now := time.Now()
				weekday := now.Weekday()
				flag := contains(scaleConfig.Index, int(weekday))
				if !flag {
					continue
				}
			default:
				logger.Errorf("AutoScaleTimeBased - Fail to Get TimeUnit for rule: %s", rule.SerialId)
				continue
			}
			pass, err := convertTime(scaleConfig)
			if err != nil {
				logger.Errorf("AutoScaleTimeBased - Fail to convertAndCheckTime for rule: %s", rule.SerialId)
				return
			}
			if !pass {
				logger.Debugf("AutoScaleTimeBased - execute time is over now for rule: %s", rule.SerialId)
				continue
			}
			// 直接创建新的
			//_, newScaleConfig := checkPreActionSucceeded(rule, constants.SCALE_ACTION_TYPE_SCALE_FLEXIBLE, job)
			jobInstance := jobInstances[0]
			txManager := service2.GetTxManager()
			// 不需要清理
			err, exist := job_autoscale.CheckJobTuningActionExist(job.SerialId, jobInstance.Id, constants.SCALE_ACTION_TYPE_TASK_FLEXIBLE_TYPE, scaleConfig.OnTime)
			if err != nil {
				logger.Errorf("AutoScaleTimeBased - Fail to CheckJobTuningActionExistAutoScale for rule: %s", rule.SerialId)
				return
			}
			// job已经存在相同executeTime的action
			if exist {
				continue
			}

			txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
				actionDetail := createJobTuningAction(job, scaleConfig, constants.SCALE_ACTION_TYPE_SCALE_FLEXIBLE, jobInstance.Id, rule)
				if actionDetail == nil {
					logger.Errorf("AutoScaleTimeBased - Unable to create action detail job scale rule:%s", rule.SerialId)
					return nil
				}
				jobTuningAction := job_autoscale.CreateJobTuningActionEntityFromJobScaleRule(job, jobInstance, rule, actionDetail, scaleConfig)
				if jobTuningAction == nil {
					logger.Errorf("AutoScaleTimeBased: CreateJobTuningAction failed for job %s with wrong actionDetail", job.SerialId)
					return nil
				}
				_, err := job_autoscale.CreateJobTuningAction("", jobTuningAction)
				if err != nil {
					logger.Errorf("AutoScaleTimeBased: CreateJobTuningAction failed for job %s, scale rule %s, with errors:%+v", job.SerialId, rule.SerialId, err)
					return nil
				}
				//updateActionSerialId(job, rule, serialId, newScaleConfig)
				return nil
			}).Close()
			continue
		}

		// 旧rule转换成2条新rule
		req := &job_autoscale2.DescribeJobScalePlansReq{}
		req.AppId = rule.AppId
		req.JobSerialId = rule.JobId
		req.Region = rule.Region
		req.Uin = rule.OwnerUin
		req.SubAccountUin = rule.CreatorUin
		// 创建好rule后，下一轮直接通过new rule创建新的action，不再使用老rule。
		err = job_autoscale.CreateNewScalePlanFromOldRule(rule, req, job)
		if err != nil {
			logger.Errorf("AutoScaleTimeBased - Fail to CreateNewScalePlanFromOldRule for rule: %s", rule.SerialId)
			return
		}
		//newRule, err := job_autoscale.CreateNewRuleFromOldRule(rule, job)
		//if err != nil {
		//	logger.Errorf("AutoScaleTimeBased - Fail to CreateNewRuleFromOldRule for rule: %s", rule.SerialId)
		//	return
		//}

		//err = compatibleWithOldRule(rule, newRule, job.CuMem)
		//if err != nil {
		//	logger.Errorf("AutoScaleTimeBased - Fail to compatibleWithOldRule for rule: %s", rule.SerialId)
		//	return
		//}

		// 2条rule均插入
		//for _, r := range newRule {
		//	logger.Debugf("AutoScaleTimeBased - From old rule: %+v convert to new rule: %+v", rule, r)
		//	r.SerialId = rule.SerialId
		//	scaleConfig, err = parseTimeBasedScheduleConfig(r)
		//	if err != nil {
		//		logger.Errorf("AutoScaleTimeBased - Fail to parseTimeBasedScheduleConfig for rule: %s, err:%+v", r.SerialId, err)
		//		continue
		//	}
		//	// hours:min => now year-now month-now day hours:min
		//	pass, err := convertTime(scaleConfig)
		//	if err != nil {
		//		logger.Errorf("AutoScaleTimeBased - Fail to convertAndCheckTime for rule: %s", r.SerialId)
		//		continue
		//	}
		//	if !pass {
		//		logger.Debugf("AutoScaleTimeBased - execute time is over now for rule: %s", r.SerialId)
		//		continue
		//	}
		//	jobInstance := jobInstances[0]
		//	err, exist := job_autoscale.CheckJobTuningActionExist(job.SerialId, jobInstance.Id, constants.SCALE_ACTION_TYPE_TASK_FLEXIBLE_TYPE, scaleConfig.OnTime)
		//	if err != nil {
		//		logger.Errorf("AutoScaleTimeBased - Fail to CheckJobTuningActionExistAutoScale for rule: %s", rule.SerialId)
		//		return
		//	}
		//	// job已经存在相同executeTime的action
		//	if exist {
		//		continue
		//	}
		//	txManager := service2.GetTxManager()
		//	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		//		actionDetail := createJobTuningAction(job, scaleConfig, constants.SCALE_ACTION_TYPE_SCALE_FLEXIBLE, jobInstance.Id, r)
		//		if actionDetail == nil {
		//			logger.Errorf("AutoScaleTimeBased - Unable to create action detail job scale rule:%s", r.SerialId)
		//			return nil
		//		}
		//		jobTuningAction := job_autoscale.CreateJobTuningActionEntityFromJobScaleRule(job, jobInstance, r, actionDetail, scaleConfig)
		//		if jobTuningAction == nil {
		//			logger.Errorf("AutoScaleTimeBased: CreateJobTuningAction failed for job %s with wrong actionDetail", job.SerialId)
		//			return nil
		//		}
		//		_, err := job_autoscale.CreateJobTuningAction("", jobTuningAction)
		//		if err != nil {
		//			logger.Errorf("AutoScaleTimeBased: CreateJobTuningAction failed for job %s, scale rule %s, with errors:%+v", job.SerialId, r.SerialId, err)
		//			return nil
		//		}
		//		//updateActionSerialId(job, rule, serialId, newScaleConfig)
		//		return nil
		//	}).Close()
		//	continue
		//}
	}
}

func contains(slice []int, elem int) bool {
	for _, v := range slice {
		if v == elem {
			return true
		}
	}
	return false
}

func parseTimeBasedScheduleConfig(rule *table2.JobScaleRule) (scaleConfig *job_autoscale.ScaleConfig, err error) {
	if rule.Configuration == "" {
		logger.Errorf("Rule configuration is empty! Job Id, Rule id :%s", rule.JobId, rule.SerialId)
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf(
			"Rule configuration is empty! Job Id: %s, Rule id: %s", rule.JobId, rule.SerialId), nil)
	}
	detail := &job_autoscale.ScaleConfig{}
	err = json.Unmarshal([]byte(rule.Configuration), detail)
	if err != nil {
		logger.Errorf("Failed to Unmarshal rule_configuration for rule %s because %+v. ", rule.SerialId, err)
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("AutoScaleTimeBased - failed to Unmarshal rule_configuration for rule %s", rule.SerialId), nil)
	}
	return detail, nil
}

func maybeGetJob(serialId string, status []int64) (*table.Job, error) {
	listJobQuery := model.ListJobQuery{
		SerialIds:    []string{serialId},
		IsVagueNames: false,
		Offset:       0,
		Limit:        0,
		Status:       status,
	}
	jobs, err := service.ListJobs(&listJobQuery)
	if err != nil {
		return nil, err
	}
	if len(jobs) == 0 {
		return nil, errors.New(fmt.Sprintf("No running job found by ID %s", serialId))
	}
	if len(jobs) > 1 {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "Logic error , jobs length > 1 "+serialId, nil)
	}
	return jobs[0], nil

}

func createJobTuningAction(job *table.Job, scaleConfig *job_autoscale.ScaleConfig, actionType int, jobInstanceId int64, jobScaleRule *table2.JobScaleRule) *table3.ActionDetail {
	// {"flinkJob":"cql-8svtci55-549654","component":"","jobVertex":"","type":"task-parallelism.scale-up","ratio":2.0}
	actionDetailForScaleUp := &table3.ActionDetail{
		FlinkJob:  job.SerialId + "-" + strconv.FormatInt(jobInstanceId, 10),
		JobVertex: "",
		Ratio:     scaleConfig.Ratio,
	}
	actionDetailForScaleDown := &table3.ActionDetail{
		FlinkJob:  job.SerialId + "-" + strconv.FormatInt(jobInstanceId, 10),
		JobVertex: "",
		Ratio:     1 / scaleConfig.Ratio,
	}
	actionDetailForFlexibleScale := &table3.ActionDetail{
		FlinkJob:           job.SerialId + "-" + strconv.FormatInt(jobInstanceId, 10),
		JobVertex:          "",
		DefaultParallelism: scaleConfig.DefaultParallelism,
		TmCuSpec:           scaleConfig.TmCuSpec,
		JmCuSpec:           scaleConfig.JmCuSpec,
		JmCPU:              scaleConfig.JobManagerCpu,
		JmMem:              scaleConfig.JobManagerMem,
		TmCPU:              scaleConfig.TaskManagerCpu,
		TmMem:              scaleConfig.TaskManagerMem,
	}

	if scaleConfig.ScaleType == constants.SCALE_ACTION_TYPE_TASK_PARALLELISM_INT {
		actionDetailForScaleUp.Component = ""
		actionDetailForScaleUp.Type = constants.SCALE_ACTION_TYPE_TASK_PARALLELISM_SCALE_UP
		actionDetailForScaleDown.Component = ""
		actionDetailForScaleDown.Type = constants.SCALE_ACTION_TYPE_TASK_PARALLELISM_SCALE_DOWN
	} else if scaleConfig.ScaleType == constants.SCALE_ACTION_TYPE_TASK_RESOURCE_INT {
		actionDetailForScaleUp.Component = "tm"
		actionDetailForScaleUp.Type = constants.SCALE_ACTION_TYPE_TASK_RESOURCE_SCALE_UP
		actionDetailForScaleDown.Component = "tm"
		actionDetailForScaleDown.Type = constants.SCALE_ACTION_TYPE_TASK_RESOURCE_SCALE_DOWN
	} else if scaleConfig.ScaleType == constants.SCALE_ACTION_TYPE_TASK_FLEXIBLE {
		actionDetailForFlexibleScale.Type = constants.SCALE_ACTION_TYPE_TASK_FLEXIBLE_TYPE
		return actionDetailForFlexibleScale
	} else {
		logger.Errorf("AutoScaleTimeBased - Unsupported scale action type for job rule:%s", jobScaleRule.SerialId)
		return nil
	}
	if actionType == constants.SCALE_ACTION_TYPE_SCALE_UP {
		return actionDetailForScaleUp
	} else if actionType == constants.SCALE_ACTION_TYPE_SCALE_DOWN {
		return actionDetailForScaleDown
	} else {
		logger.Errorf("AutoScaleTimeBased - Unsupported scale action type for job rule:%s", jobScaleRule.SerialId)
		return nil
	}
}

func convertTime(scaleConfig *job_autoscale.ScaleConfig) (pass bool, err error) {
	now := time.Now()
	parsedTime, err := time.ParseInLocation("15:04", scaleConfig.OnTime, LOC)
	if err != nil {
		fmt.Println("Error parsing time:", err)
		return
	}
	executeTime := time.Date(now.Year(), now.Month(), now.Day(), parsedTime.Hour(), parsedTime.Minute(), parsedTime.Second(), 0, now.Location())
	scaleConfig.OnTime = executeTime.Format("2006-01-02 15:04")
	return true, nil
}

// 为了让每次oldrule生成的action的目标资源一致，不然每次递增1倍每次资源都不一样，10分钟执行一次
// newRule的第二个里面目标资源数目就是最开始的数目
//func compatibleWithOldRule(rule *table2.JobScaleRule, newRule []*table2.JobScaleRule, cuMem int8) (err error) {
//	detail := &job_autoscale.ScaleConfig{}
//	detail1 := &job_autoscale.ScaleConfig{}
//	detail2 := &job_autoscale.ScaleConfig{}
//	err = json.Unmarshal([]byte(rule.Configuration), detail)
//	if err != nil {
//		logger.Errorf("AutoScaleTimeBased - Failed to Unmarshal Configuration for rule %s because %+v. ", rule.SerialId, err)
//	}
//	err = json.Unmarshal([]byte(newRule[0].Configuration), detail1)
//	if err != nil {
//		logger.Errorf("AutoScaleTimeBased - Failed to Unmarshal Configuration for rule %v because %+v. ", newRule[0], err)
//	}
//	err = json.Unmarshal([]byte(newRule[1].Configuration), detail2)
//	if err != nil {
//		logger.Errorf("AutoScaleTimeBased - Failed to Unmarshal Configuration for rule %v because %+v. ", newRule[1], err)
//	}
//	// 关闭再启动oldrule 可能会出现.Origin不等于原始配置，需要重新更新（其实不保存在数据库也可以）
//	//if detail.OriginDefaultParallelism != detail2.DefaultParallelism || !service.AlmostEqual(detail.OriginTmCuSpec, detail2.TmCuSpec) || !service.AlmostEqual(detail.OriginJmCuSpec, detail2.JmCuSpec) {
//	//	detail.OriginDefaultParallelism = detail2.DefaultParallelism
//	//	detail.OriginTmCuSpec = detail2.TmCuSpec
//	//	detail.OriginJmCuSpec = detail2.JmCuSpec
//	//	s, err := json.Marshal(detail)
//	//	if err != nil {
//	//		return err
//	//	}
//	//	rule.Configuration = string(s)
//	//	err = job_autoscale.UpdateScaleRule(rule)
//	//	if err != nil {
//	//		logger.Errorf("AutoScaleTimeBased - Failed to UpdateScaleRule for rule %s because %+v. ", rule.SerialId, err)
//	//		return err
//	//	}
//	//}
//	if detail.ScaleType == constants.SCALE_ACTION_TYPE_TASK_RESOURCE_INT {
//		logger.Debugf("AutoScaleTimeBased - compatibleWithOldRule,OriginTmCuSpec:%d;Ratio:%f", detail.OriginTmCuSpec, detail.Ratio)
//		// 第一个是扩容原始配置
//		// 第二个恢复原始配置
//		// jm的cpu和内存不变
//		//detail1.JmMem = detail.JmMem
//		//detail1.JmCPU = detail.JmCPU
//		//detail2.JmMem = detail.JmMem
//		//detail2.JmCPU = detail.JmCPU
//		//
//		//d1tc,_ := decimal.NewFromFloat32(detail.OriginTmCuSpec * detail.Ratio).Round(2).Float64()
//		//d1tm,_ := decimal.NewFromFloat32(detail.TmMem * detail.Ratio * float32(cuMem)).Round(2).Float64()
//		//d2tm,_ := decimal.NewFromFloat32(detail.TmMem * float32(cuMem)).Round(2).Float64()
//		//detail1.TmCPU = float32(d1tc)
//		//detail2.TmCPU = detail.OriginTmCuSpec
//		//detail1.TmMem = float32(d1tm)
//		//detail2.TmMem = float32(d2tm)
//		detail1.TmCuSpec = detail1.TmCuSpec
//		detail2.TmCuSpec = detail2.TmCuSpec
//	} else if detail.ScaleType == constants.SCALE_ACTION_TYPE_TASK_PARALLELISM_INT {
//		logger.Debugf("AutoScaleTimeBased - compatibleWithOldRule,scale up DefaultParallelism:%d;scale down DefaultParallelism:%f", detail1.DefaultParallelism,detail2.DefaultParallelism)
//		detail1.DefaultParallelism = detail1.DefaultParallelism
//		detail2.DefaultParallelism = detail2.DefaultParallelism
//	} else {
//		return errorcode.NewStackError(errorcode.FailedOperationCode, "jobscalerule configuration tasktype for rule: "+rule.SerialId+" is wrong!", err)
//	}
//
//	s1, err := json.Marshal(detail1)
//	if err != nil {
//		return err
//	}
//	newRule[0].Configuration = string(s1)
//	s2, err := json.Marshal(detail2)
//	if err != nil {
//		return err
//	}
//	newRule[1].Configuration = string(s2)
//	return nil
//}
