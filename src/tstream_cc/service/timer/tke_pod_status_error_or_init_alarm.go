package timer

import (
	"context"
	"encoding/json"
	"fmt"
	coreV1 "k8s.io/api/core/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"strconv"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/configure"
	"tencentcloud.com/tstream_galileo/src/common/dlocker"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/logger/model"
	"tencentcloud.com/tstream_galileo/src/common/notify"
	"tencentcloud.com/tstream_galileo/src/common/notify/tof"
	"tencentcloud.com/tstream_galileo/src/common/timer"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/tke"
	"time"
)

/*
*

		新增集群Pod状态异常告警，对初始化超时、error、unknow状态的Pod进行告警
	    TAPD: http://tapd.oa.com/20358692/prong/stories/view/1020358692865788629?url_cache_key=10fea5f6482ea61194e0a4b5f570fbd3&action_entry_type=stories
*/
type tkePodStatusErrorOrInitHandler struct {
	podInitTimeout          int64
	podInitTimeoutAlarmList []tof.TOFMessage
	podErrorAlarmList       []tof.TOFMessage
}

func DetectPodErrorOrInitWithFixedDelay() {

	defaultCheckDuration := 60 * 20  // 默认检测周期是 20 分钟
	defaultPodInitTimeout := 60 * 10 // 默认的Pod初始化超时时间为10分钟，超过该时间的需要对该Pod进行删除

	// 注意以下配置的单位是秒
	checkDuration := configure.GetConfInt64Value("component.properties", "component.timer.tkePodStatusTimeoutAlarmHandler.checkDuration", int64(defaultCheckDuration))
	podInitTimeout := configure.GetConfInt64Value("component.properties", "component.timer.tkePodStatusTimeoutAlarmHandler.podInitTimeout", int64(defaultPodInitTimeout))

	logger.Debugf("TIMER: Check TKE's Pod status duration: %ds", checkDuration)

	config := map[string]interface{}{}
	config["times"] = 0 // Config 必须有 times 和 checkDuration 两个属性, 0 表示无限循环
	config["duration"] = checkDuration
	handler := &tkePodStatusErrorOrInitHandler{
		podInitTimeout:          podInitTimeout,
		podInitTimeoutAlarmList: make([]tof.TOFMessage, 0),
		podErrorAlarmList:       make([]tof.TOFMessage, 0),
	} // 传入参数
	task := timer.NewTask(config)
	task.Handler = handler
	task.Start()
}

func (s *tkePodStatusErrorOrInitHandler) Do() {
	defer func() {
		if err := recover(); err != nil {
			err = errorcode.NewStackError(errorcode.InternalError_Timer,
				"", err.(error))
			logger.Errorf("Failed to check TKE's Pod status, with errors:%+v", err)
		}

		// 发送完消息后清理缓存
		s.podInitTimeoutAlarmList = make([]tof.TOFMessage, 0)
		s.podErrorAlarmList = make([]tof.TOFMessage, 0)
	}()

	locker := dlocker.NewDlocker("oceanus-tkePodStatusErrorOrInitTimer", fmt.Sprintf("optId-%s", "tkePodStatusErrorOrInitTimer"), 1200)
	err := locker.Lock()
	if err != nil {
		if strings.Contains(err.Error(), "Locked by others") {
			logger.Infof("could not fetch the lock. %+v", err)
		} else {
			logger.Errorf("could not fetch the lock. %+v", err)
		}
		return
	}
	defer func() {
		err = locker.UnLock()
		if err != nil {
			logger.Errorf("could not fetch the lock. %+v", err)
		}
	}()

	logger.Debugf("TIMER: Beginning to check TKE's Pod status")

	// 获取目前 Galileo Cluster 表里所有未完成的命令
	logger.Debugf("TIMER: Checking all running clusters(TKE)")
	clusters, err := s.getRunningTkeClusters()
	if err != nil {
		logger.Errorf("TIMER: Failed to get running cluster at this moment, errors:%+v", err)
		return
	}

	if len(clusters) > 0 {
		// 遍历每一个 Cluster 获取 Tke 配置，并查找 Pod 状态为 Error/Init 的进行告警并删除
		for _, cluster := range clusters {
			s.checkPodStateAndSendAlarm(cluster)
		}
	}

	// 结束定时任务
	logger.Debugf("TIMER: Finished checking TKE Pod status")
}

func (s *tkePodStatusErrorOrInitHandler) checkPodStateAndSendAlarm(cluster *table.Cluster) {
	clusterFlag := fmt.Sprintf("clusterId=%s", cluster.UniqClusterId)
	clientset, err := tke.GetTkeService().KubernetesClientsetFromCluster(clusterFlag, cluster)
	if err != nil {
		logger.Errorf("[%s] Failed to get Kubernetes clientset from cluster because %v", clusterFlag, err)
		return
	}

	listOptions := v1.ListOptions{}
	listPods, err := clientset.CoreV1().Pods(v1.NamespaceAll).List(context.TODO(), listOptions)
	if err != nil || len(listPods.Items) == 0 {
		logger.Errorf("[%s] Get Kubernetes pod list is empty", clusterFlag)
		return
	}

	if logger.GetLogLevel() == model.LOG_LEVEL_DEBUG {
		jsonBytes, _ := json.Marshal(listPods)
		logger.Debugf("Pods Info: %s", string(jsonBytes))
	}

	for _, podInfo := range listPods.Items {
		// 先检查Conditions，如果都为true就跳过
		if s.isNormalPodConditions(&podInfo) {
			continue
		}

		if podInfo.Status.Phase == "Failed" || podInfo.Status.Phase == "Unknown" {
			s.generateAndSendPodInitTimeoutAlarmMessage("Oceanus TKE Pod 状态异常告警", nil, podInfo.Name, podInfo.Status.StartTime.Format("2006-01-02 15:04:05"), cluster.UniqClusterId, cluster.Zone, podInfo.Status.Message, string(podInfo.Status.Phase), cluster.ClusterGroupId)
			continue
		}

		// taskManager 因为资源不足，会在90s后删除Pod并重新创建，该场景通过 timer 无法识别，后续放入 Watchdog 来监听 TKE 的事件进行告警，详情参考 TAPD 单
		// 针对 Pod 中每个 Container 进行检查并告警
		for _, container := range podInfo.Status.ContainerStatuses {
			// 当 taskManager 执行完 job 后 container.State 为终止状态 Terminated ，该状态是正常情况，所以此处不对 Terminated 状态进行告警
			if container.State.Waiting != nil {
				// 如果Pod的状态是异常，不用判断是否超时直接告警
				if podInfo.Status.Phase == "Failed" || podInfo.Status.Phase == "Unknown" {
					s.generateAndSendPodInitTimeoutAlarmMessage("Oceanus TKE Pod 状态异常告警", &container, podInfo.Name, podInfo.Status.StartTime.Format("2006-01-02 15:04:05"), cluster.UniqClusterId, cluster.Zone, podInfo.Status.Message, string(podInfo.Status.Phase), cluster.ClusterGroupId)
				} else {
					startTime := podInfo.Status.StartTime
					elapsedTimeDuration := time.Now().Sub(startTime.Time)
					timeoutDuration := time.Duration(s.podInitTimeout) * time.Second
					if elapsedTimeDuration > timeoutDuration {
						s.generateAndSendPodInitTimeoutAlarmMessage("Oceanus TKE Pod 初始化超时告警", &container, podInfo.Name, podInfo.Status.StartTime.Format("2006-01-02 15:04:05"), cluster.UniqClusterId, cluster.Zone, podInfo.Status.Message, string(podInfo.Status.Phase), cluster.ClusterGroupId)
					}
				}
			}
		}

	}

}

func (s *tkePodStatusErrorOrInitHandler) isNormalPodConditions(podInfo *coreV1.Pod) bool {
	if len(podInfo.Status.Conditions) > 0 {
		for _, condition := range podInfo.Status.Conditions {
			if condition.Status == "False" || condition.Status == "Unknown" {
				return false
			}
		}
		return true
	}

	return false
}

func (s *tkePodStatusErrorOrInitHandler) generateAndSendPodInitTimeoutAlarmMessage(eventName string, container *coreV1.ContainerStatus, podName string, startTime string, clusterId string, region string, message string, status string, clusterGroupId int64) {
	containerName := ""
	if container != nil {
		containerName = container.Name
		message = s.findErrorMessage(container)
		status = "Unknown"
		if container.State.Terminated != nil {
			status = "Terminated"
		} else if container.State.Waiting != nil {
			status = "Waiting"
		}
	}
	sql := "SELECT Appid FROM ClusterGroup WHERE Id = ?"
	args := make([]interface{}, 0, 0)
	args = append(args, clusterGroupId)
	_, data, _ := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	appid, _ := strconv.ParseInt(string(data[0]["Appid"]), 10, 64)

	notifyInstance := notify.NewNotify()
	tofMsg := tof.TOFMessage{
		AppId:     appid,
		Region:    region,
		EventName: eventName,
		Time:      time.Now().Format("2006-01-02 15:04:05"),
		EventMessage: map[string]interface{}{
			"clusterId":     clusterId,
			"podName":       podName,
			"containerName": containerName,
			"startTime":     startTime,
			"status":        status,
			"message":       message,
		},
	}
	logger.Debugf("Send alarm Message:", eventName, tofMsg)
	notifyInstance.SyncSendMessageKafka(tofMsg)
}

func (s *tkePodStatusErrorOrInitHandler) getRunningTkeClusters() (clusters []*table.Cluster, err error) {
	sql := "SELECT * FROM Cluster WHERE ClusterGroupId IN (SELECT id FROM ClusterGroup WHERE Status=?) AND RoleType=? AND (SchedulerType=? )"
	args := make([]interface{}, 0)
	args = append(args, constants.CLUSTER_GROUP_STATUS_RUNNING)
	args = append(args, constants.CLUSTER_ROLE_TYPE_ACTIVE)
	args = append(args, constants.CLUSTER_SCHEDULER_TYPE_TKE)
	_, rowsData, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("getRunningCluster sql %s, args %+v, error %+v", sql, args, err)
		return clusters, err
	}
	clusters = make([]*table.Cluster, 0, 0)
	for _, data := range rowsData {
		cluster := &table.Cluster{}
		util.ScanMapIntoStruct(cluster, data)
		clusters = append(clusters, cluster)
	}

	return clusters, err
}

func (s *tkePodStatusErrorOrInitHandler) findErrorMessage(container *coreV1.ContainerStatus) string {
	var message string

	// 以下获取Message有优先级
	if len(message) == 0 {
		if container.LastTerminationState.Waiting != nil {
			message = container.LastTerminationState.Waiting.Message
		}
	}

	if len(message) == 0 {
		if container.LastTerminationState.Terminated != nil {
			message = container.LastTerminationState.Terminated.Message
		}
	}

	if len(message) == 0 {
		if container.State.Waiting != nil {
			message = container.State.Waiting.Message
		}
	}

	if len(message) == 0 {
		if container.State.Terminated != nil {
			message = container.State.Terminated.Message
		}
	}

	return message
}
