package timer

import (
	"tencentcloud.com/tstream_galileo/src/common/flow"
	"tencentcloud.com/tstream_galileo/src/common/timer"
)

func LoadFlow() {
	config := map[string]interface{}{}
	config["times"] = 0    // Config 必须有 times 和 checkDuration 两个属性, 0 表示无限循环
	config["duration"] = int64(3) // in second
	task := timer.NewTask(config)
	task.Handler = &loadFlowHandler{}
	task.Start()
}

type loadFlowHandler struct {
}

func (*loadFlowHandler) Do() {
	_ = (&flow.LoadFlowJob{}).Run()
}
