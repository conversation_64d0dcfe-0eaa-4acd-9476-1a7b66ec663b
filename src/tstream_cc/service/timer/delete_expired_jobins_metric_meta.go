package timer

import (
	"strconv"

	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/timer"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config"
	jobService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job"
)

type deleteExpiredJobInsMetricMetaHandler struct {
	expiredTime int
}

func DeleteExpiredJobInsMetricMetaData() {
	// 注意以下配置的单位是秒
	checkDuration := int64(getDurationDefault())
	logger.Infof("TIMER: Clean JobInsMetricMeta duration: %ds", checkDuration)
	config := map[string]interface{}{}
	config["times"] = 0 // Config 必须有 times 和 checkDuration 两个属性, 0 表示无限循环
	config["duration"] = checkDuration
	handler := &deleteExpiredJobInsMetricMetaHandler{}
	task := timer.NewTask(config)
	task.Handler = handler
	task.Start()
}

func getDurationDefault() int {
	defaultCheckDuration := 60 * 60 * 12 // 默认检测周期是 12 小时，即间隔 12 小时执行一次
	checkDurationStr, err := config.GetRainbowConfiguration("Common", "JobInsMetricMeta.checkDuration")
	if err != nil {
		logger.Warningf("Fail to get Common.event.JobInsMetricMeta.checkDuration from RainbowConfiguration")
		return defaultCheckDuration
	}
	if newDuration, err := strconv.Atoi(checkDurationStr); err != nil {
		logger.Errorf("Fail to convert value:%s", checkDurationStr)
		return defaultCheckDuration
	} else {
		return newDuration
	}
}

func (s *deleteExpiredJobInsMetricMetaHandler) Do() {
	err := jobService.PageQueryJobInsMetricMetas(jobService.DelJobInsMetricMetasFunc)
	if err != nil {
		logger.Errorf("deleteExpiredJobInsMetricMetaHandler exec with error %v", err)
	}
	logger.Infof("deleteExpiredJobInsMetricMetaHandler exec successfully.")
}
