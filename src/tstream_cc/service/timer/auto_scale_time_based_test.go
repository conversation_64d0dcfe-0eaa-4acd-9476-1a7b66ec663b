package timer

import (
	"flag"
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/dlocker"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	"tencentcloud.com/tstream_galileo/src/common/scheduler"
	service2 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/job_autoscale"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource"
	"testing"
	"time"
)

//创建action的定时任务的逻辑
func Test_autoScaleTimeBasedHandler_Do(t *testing.T) {
	service.InitTestDB(service.DBURL)
	dlocker.SetTxManager(service2.GetTxManager())
	au := &autoScaleTimeBasedHandler{}
	go scheduler.Start()
	au.Do()
}

//创建action的定时任务的执行逻辑

//action的执行逻辑
func Test_scaleTimeJob(t *testing.T) {
	service.InitTestDB(service.DBURL)
	flow.SetTaskCenterCallbackInfo(&flow.TaskCenterCallBackInfo{
		CallbackAddr:          *flag.String("test.taskcenter.callback.addr", "http://127.0.0.1:7021/interface", ""),
		CompleteTaskInterface: *flag.String("test.taskcenter.complete.task.interface", "qcloud.galileo.taskcenter.completeTask", ""),
		AlarmTaskInterface:    *flag.String("test.taskcenter.alarm.task.interface", "qcloud.galileo.taskcenter.alarmTask", ""),
	})
	dlocker.SetTxManager(service2.GetTxManager())
	flow.SetTxManager(service2.GetTxManager())
	j := &job_autoscale.AutoScaleJob{}
	j.Run()
}

func Test_timeParse(t *testing.T) {
	layout := "2006-01-02 15:04"
	onTime := "2024-8-1 15:04"
	ti, err := time.Parse(layout, onTime)
	fmt.Println(err)
	year := ti.Year()
	fmt.Println(year)
	month := ti.Month()
	fmt.Println(month)
	day := ti.Day()
	fmt.Println(day)
	now := time.Now()
	fmt.Println(now)
}

func Test(t *testing.T) {
	equal := almostEqual(2.0000000, 2.0000000)
	fmt.Println(equal)
}

func almostEqual(a, b float32) bool {
	return (a-b) < 0.01 && (b-a) < 0.01
}
