package timer

import (
	"encoding/json"
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/configure"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/timer"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
	"time"
)

type calcUniformClusterCvmHandler struct {
}

func CalcUniformClusterCvmHandler() {
	defaultCheckDuration := 60 * 2

	checkDuration := configure.GetConfInt64Value("component.properties", "component.timer.calcUniformClusterCvmHandler.checkDuration",
		int64(defaultCheckDuration))

	config := map[string]interface{}{}
	config["times"] = 0
	config["duration"] = checkDuration
	task := timer.NewTask(config)
	task.Handler = &calcUniformClusterCvmHandler{}
	task.Start()
}

func (d *calcUniformClusterCvmHandler) Do() {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("CalcUniformClusterCvmHandler panic, with errors:%+v, stack %s", errs, util.Gostack())
		}
	}()

	logger.Infof("start calc uniform cluster cvm")
	//共享集群，资源共享
	sql, args := dao.NewQueryBuilder("select * from ClusterGroup").
		WhereEq("Status", constants.CLUSTER_GROUP_STATUS_RUNNING).
		WhereEq("Type", constants.CLUSTER_GROUP_TYPE_UNIFORM).
		OrderByDesc("CreateTime").
		Build()

	clusterGroups, err := service.Fetch[table.ClusterGroup](sql, args)
	if err != nil {
		panic(err)
	}

	for _, cg := range clusterGroups {
		time.Sleep(500 * time.Millisecond)
		context := &CalcUniformClusterContext{AgentGroup: cg}
		go context.CalcUniformClusterCvm()
	}
	//共享集群，资源独享，单可用区
	sql, args = dao.NewQueryBuilder("SELECT DISTINCT cg.AgentSerialId, cg.Zone, cg.AppId FROM ClusterGroup cg "+
		"LEFT JOIN Cluster c ON cg.Id = c.ClusterGroupId LEFT JOIN Tke t ON c.Id = t.ClusterId ").
		WhereEq("cg.Status", constants.CLUSTER_GROUP_STATUS_RUNNING).
		WhereEq("cg.NetEnvironmentType", constants.NETWORK_ENV_CLOUD_VPC).
		WhereEq("cg.Type", constants.CLUSTER_GROUP_TYPE_PRIVATE).
		WhereNe("cg.AgentSerialId", "").
		WhereEq("cg.ResourceType", constants.RESOURCE_TYPE_PRIVATE).
		WhereEq("cg.DeploymentMode", constants.DeploymentModeDefault).
		WhereEq("c.SchedulerType", constants.CLUSTER_SCHEDULER_TYPE_TKE).
		WhereEq("t.ClusterType", constants.K8S_CLUSTER_TYPE_TKE).
		GroupBy("cg.AgentSerialId, cg.Zone, cg.AppId").
		Build()
	clusterGroups, err = service.Fetch[table.ClusterGroup](sql, args)
	if err != nil {
		panic(err)
	}
	for _, cg := range clusterGroups {
		sql, args = dao.NewQueryBuilder("select * from ClusterGroup").
			WhereEq("Status", constants.CLUSTER_GROUP_STATUS_RUNNING).
			WhereEq("SerialId", cg.AgentSerialId).
			Build()

		clusterGroup, err := service.FetchRow[table.ClusterGroup](sql, args)
		if err != nil {
			panic(err)
		}
		if clusterGroup == nil {
			errMsg := fmt.Sprintf("clusterGroup is nil, with cg.AgentSerialId:%s", cg.AgentSerialId)
			logger.Errorf(errMsg)
			continue
		}
		time.Sleep(500 * time.Millisecond)
		context := &CalcUniformClusterContext{AgentGroup: clusterGroup, AppId: cg.AppId, Zone: cg.Zone}
		go context.CalcUniformClusterCvm()
	}

	//共享集群，资源独享， 多可用区， 备区
	sql, args = dao.NewQueryBuilder("SELECT DISTINCT cg.AgentSerialId, cg.Zone, cg.AppId FROM ClusterGroup cg "+
		"LEFT JOIN Cluster c ON cg.Id = c.ClusterGroupId LEFT JOIN Tke t ON c.Id = t.ClusterId ").
		WhereEq("cg.Status", constants.CLUSTER_GROUP_STATUS_RUNNING).
		WhereEq("cg.NetEnvironmentType", constants.NETWORK_ENV_CLOUD_VPC).
		WhereEq("cg.Type", constants.CLUSTER_GROUP_TYPE_PRIVATE).
		WhereNe("cg.AgentSerialId", "").
		WhereEq("cg.ResourceType", constants.RESOURCE_TYPE_PRIVATE).
		WhereEq("cg.DeploymentMode", constants.DeploymentModeMultiple).
		WhereEq("c.SchedulerType", constants.CLUSTER_SCHEDULER_TYPE_TKE).
		WhereEq("t.ClusterType", constants.K8S_CLUSTER_TYPE_TKE).
		GroupBy("cg.AgentSerialId, cg.Zone, cg.AppId").
		Build()
	clusterGroups, err = service.Fetch[table.ClusterGroup](sql, args)
	if err != nil {
		panic(err)
	}
	for _, cg := range clusterGroups {
		logger.Infof("deal slave zone with Agent SerialId %s, AppId %d", cg.AgentSerialId, cg.AppId)
		sql, args = dao.NewQueryBuilder("select * from ClusterGroup").
			WhereEq("Status", constants.CLUSTER_GROUP_STATUS_RUNNING).
			WhereEq("SerialId", cg.AgentSerialId).
			Build()

		clusterGroup, err := service.FetchRow[table.ClusterGroup](sql, args)
		if err != nil {
			panic(err)
		}
		if clusterGroup == nil {
			errMsg := fmt.Sprintf("clusterGroup is nil, with cg.AgentSerialId:%s", cg.AgentSerialId)
			logger.Errorf(errMsg)
			continue
		}
		time.Sleep(500 * time.Millisecond)
		// 获取备份可用区
		tmpSupportedZones := make([]string, 0)
		if len(clusterGroup.SupportedZones) != 0 {
			err = json.Unmarshal([]byte(clusterGroup.SupportedZones), &tmpSupportedZones)
			if err != nil {
				logger.Warningf("Multiple mode unmarshal supportedZones failed, with err:%+v", err)
			}
		}
		// 备区资源计算的时候，检查主区是不是可用，可用的话，加50%，不可用 正常加
		if len(tmpSupportedZones) > 0 {
			logger.Infof("deal Multiple cluster with Agent SerialId %s, AppId %d", clusterGroup.SerialId, cg.AppId)
			context := &CalcUniformClusterContext{AgentGroup: clusterGroup, AppId: cg.AppId, Zone: cg.Zone, DeploymentMode: constants.DeploymentModeMultiple}
			go context.CalcUniformClusterCvm()
		}

	}

	//独享集群
	sql, args = dao.NewQueryBuilder("SELECT cg.* FROM ClusterGroup cg "+
		"LEFT JOIN Cluster c ON cg.Id = c.ClusterGroupId LEFT JOIN Tke t ON c.Id = t.ClusterId").
		WhereEq("cg.Status", constants.CLUSTER_GROUP_STATUS_RUNNING).
		WhereEq("cg.NetEnvironmentType", constants.NETWORK_ENV_CLOUD_VPC).
		WhereEq("cg.Type", constants.CLUSTER_GROUP_TYPE_PRIVATE).
		WhereEq("cg.AgentSerialId", "").
		WhereEq("c.SchedulerType", constants.CLUSTER_SCHEDULER_TYPE_TKE).
		WhereEq("t.ClusterType", constants.K8S_CLUSTER_TYPE_TKE).
		OrderByDesc("cg.CuNum").
		Build()

	clusterGroups, err = service.Fetch[table.ClusterGroup](sql, args)
	if err != nil {
		panic(err)
	}

	for _, cg := range clusterGroups {
		time.Sleep(500 * time.Millisecond)
		context := &CalcUniformClusterContext{AgentGroup: cg}
		go context.CalcUniformClusterCvm()
	}
}
