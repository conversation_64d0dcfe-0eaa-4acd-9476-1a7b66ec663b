package timer

import (
	"tencentcloud.com/tstream_galileo/src/common/configure"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/timer"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"time"
)

type deleteExpiredEventHandler struct {
	expiredTime int
}

func DeleteExpiredEventData() {

	defaultCheckDuration := 60 * 60 * 24 // 默认检测周期是 24 小时，即间隔 24 小时执行一次
	defaultExpiredTime := -90            // 默认删除 90 天前的数据

	// 注意以下配置的单位是秒
	checkDuration := configure.GetConfInt64Value("component.properties", "component.timer.deleteExpiredEventHandler.checkDuration", int64(defaultCheckDuration))
	expiredTime := configure.GetConfIntValue("component.properties", "component.timer.deleteExpiredEventHandler.expiredTime", defaultExpiredTime)

	logger.Infof("TIMER: Check TKE's Pod status duration: %ds", checkDuration)

	config := map[string]interface{}{}
	config["times"] = 0 // Config 必须有 times 和 checkDuration 两个属性, 0 表示无限循环
	config["duration"] = checkDuration
	handler := &deleteExpiredEventHandler{expiredTime}
	task := timer.NewTask(config)
	task.Handler = handler
	task.Start()
}

func (s *deleteExpiredEventHandler) Do() {
	defer func() {
		if err := recover(); err != nil {
			err = errorcode.NewStackError(errorcode.InternalError_Timer,
				"", err.(error))
			logger.Errorf("Failed to run deleteExpiredEventHandler, with errors:%+v", err)
			return
		}
	}()

	targetTime := time.Now().AddDate(0, 0, s.expiredTime).Format("2006-01-02 15:04:05")
	sql := "DELETE FROM EventAlert WHERE createTime <= ?"
	args := make([]interface{}, 0)
	args = append(args, targetTime)
	txManager := service.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.ExecuteSql(sql, args)
		return nil
	}).Close()
	logger.Infof("###TIMER: Delete expired EventAlert data, targetTime: %s", targetTime)
}
