package timer

import (
	"tencentcloud.com/tstream_galileo/src/common/dao"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/timer"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/cluster"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
	tableService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
)

type syncWebUIPrefixHandler struct {
}

func SyncWebUIPrefixHandler() {
	config := map[string]interface{}{}
	config["times"] = 0
	config["duration"] = int64(60 * 3) //默认检测周期是 3 分钟
	task := timer.NewTask(config)
	task.Handler = &syncWebUIPrefixHandler{}
	task.Start()
}

func (d *syncWebUIPrefixHandler) Do() {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("syncWebUIPrefixHandler panic, with errors:%+v, stack %s", errs, util.Gostack())
		}
	}()

	logger.Infof("start sync WebUIPrefix to cluster")

	sql, args := dao.NewQueryBuilder("select * from Cluster").
		WhereEq("RoleType", constants.CLUSTER_ROLE_TYPE_ACTIVE).
		Build()

	clusters, err := service.Fetch[table.Cluster](sql, args)
	if err != nil {
		logger.Errorf("sync WebUIPrefix to cluster error, with errors:%+v", err)
	}

	for _, cluster := range clusters {
		clusterGroup, err := tableService.GetTableService().ListClusterGroupById(cluster.ClusterGroupId)
		if err != nil {
			logger.Errorf("sync ClusterGroupId[%d] WebUIPrefix ListClusterGroupById error.", cluster.ClusterGroupId)
			continue
		}
		if clusterGroup.Status != constants.CLUSTER_GROUP_STATUS_RUNNING {
			logger.Infof("sync ClusterGroupId[%d] WebUIPrefix skip,because of it's not running", cluster.ClusterGroupId)
			continue
		}

		s := service2.NewDescribeWebUIWhiteListService(&model.DescribeWebUIWhiteListReq{
			ClusterId: clusterGroup.SerialId,
		})
		result, err := s.DoSyncWebUIPrefix(cluster)
		if err != nil {
			logger.Errorf("sync ClusterGroupId[%d] WebUIPrefix DoSyncWebUIPrefix error %+v", cluster.ClusterGroupId, err)
			continue
		}
		if !result {
			logger.Infof("sync ClusterGroupId[%d] WebUIPrefix fail.", cluster.ClusterGroupId)
		}
	}
}
