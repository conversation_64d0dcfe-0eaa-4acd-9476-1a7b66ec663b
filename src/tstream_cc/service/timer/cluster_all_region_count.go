package timer

import (
	"tencentcloud.com/tstream_galileo/src/common/configure"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/timer"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	service2 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/oceanus"
	oceanus2 "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/oceanus"

	"time"
)

type clusterAllRegionCountHandler struct {
	createTimeBefore time.Duration
}

func ClusterAllRegionCountHandler() {
	if !isConformRegion() {
		return
	}
	logger.Info("ClusterAllRegionCountHandler")
	defaultCheckDuration := 60 * 10   // 默认检测周期是 10 分钟
	defaultCreateTimeBefore := 60 * 5 // 默认命令超时时间是 5 分钟

	checkDuration := configure.GetConfInt64Value("component.properties", "component.timer.ClusterAllRegionCount.checkDuration",
		int64(defaultCheckDuration))
	createTimeBefore := configure.GetConfInt64Value("component.properties", "component.timer.ClusterAllRegionCount.createTimeBefore",
		int64(defaultCreateTimeBefore))

	config := map[string]interface{}{}
	config["times"] = 0
	config["duration"] = checkDuration
	task := timer.NewTask(config)
	task.Handler = &clusterAllRegionCountHandler{createTimeBefore: time.Second * time.Duration(createTimeBefore)}
	task.Start()
}

func (d *clusterAllRegionCountHandler) Do() {
	regionSet, err := d.getRegionSet()
	if err != nil {
		logger.Errorf("getRegionSet err: %s", err.Error())
		return
	}
	secretId, secretKey, err := service3.GetSecretIdAndKeyOfScs()
	if err != nil {
		logger.Errorf("[%s] getJobSubmissionLogConf err %v", err)
	}
	oceanusService := oceanus.NewOceanusService()
	clusters := map[string]*oceanus2.DescribeRegionClusterCountResponse{}
	for _, region := range regionSet {
		response, err := oceanusService.GatherRegionClusterNum(oceanus.NewDefaultDescribeRegionClusterCountRequest(), secretId, secretKey, "", region)
		time.Sleep(30) //避免每秒调用云API的频率太高
		if err != nil {
			logger.Info("GatherRegionClusterNum  %s err: %s", region, err.Error())
		} else {
			clusters[region] = response
		}
	}
	for _, value := range clusters {
		for _, data := range value.Response.UserClusterCounts {
			if d.isDataExists(*data.AppId, *data.Region) {
				d.updateData(data)
			} else {
				d.insertData(data)
			}
		}
	}
}

// 判断是否在广州地域
func isConformRegion() bool {
	sql := "SELECT Id FROM ClusterGroup"
	cond := dao.NewCondition()
	cond.Eq("Region", "ap-guangzhou")
	where, args := cond.GetWhere()
	count, _, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql+where, args)
	if err != nil {
		logger.Infof("isConformRegion sql %s return %v", sql, err)
		return false
	}
	if count > 0 {
		return true
	}
	return false
}

// 数据库拿地域信息
func (d *clusterAllRegionCountHandler) getRegionSet() (regionSet []string, err error) {
	sql := "SELECT DISTINCT(Region) FROM Region"
	count, data, err := service.GetTxManager().GetQueryTemplate().DoQueryWithArgs(sql)
	if err != nil {
		logger.Infof("getRegionSet sql %s return %v", sql, err)
		return nil, err
	}
	regionSet = make([]string, 0, count)
	for _, d := range data {
		if v, ok := d["Region"]; ok {
			regionSet = append(regionSet, string(v))
		}
	}
	return regionSet, nil
}

// 3.数据库更新数据
// 3.1判断数据是否已经存在
func (d *clusterAllRegionCountHandler) isDataExists(appId int64, region string) bool {
	sql := "SELECT * FROM ClustersAllRegionCount WHERE AppId = ? AND Region = ?"
	count, _, err := service.GetTxManager().GetQueryTemplate().DoQueryWithArgs(sql, appId, region)
	if err != nil {
		logger.Error("isDataExists sql %s return %v", sql, err)
		return true
	}
	if count > 0 {
		return true
	} else {
		return false
	}
}

// 3.2 插入数据
func (d *clusterAllRegionCountHandler) insertData(data *oceanus2.UserClusterCounts) error {
	txManager := service2.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.SaveObject(data, "ClustersAllRegionCount")
		return nil
	}).Close()

	return nil
}

// 3.3 更新数据
func (d *clusterAllRegionCountHandler) updateData(data *oceanus2.UserClusterCounts) error {
	sql := "UPDATE ClustersAllRegionCount SET Num = ? WHERE AppId = ? AND Region = ?"
	txManager := service2.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.ExecuteSqlWithArgs(sql, data.Num, data.AppId, data.Region)
		return nil
	}).Close()
	return nil
}
