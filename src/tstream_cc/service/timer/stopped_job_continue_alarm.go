package timer

import (
	"fmt"
	"strings"

	"tencentcloud.com/tstream_galileo/src/common/dlocker"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/timer"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	barad2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/barad"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/barad"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
)

/**
 * 检测Job状态为停止且JobConfig配置了ContinueAlarm=1的作业，进行告警
 */
type stoppedJobContinueAlarmHandler struct {
}

func CheckStoppedJobContinueAlarmWithFixedDelay() {
	defaultCheckDuration := 60 * 5 // 默认检测周期是 5 分钟

	config := map[string]interface{}{
		"times":    0, // 0 表示无限循环
		"duration": int64(defaultCheckDuration),
	}

	task := timer.NewTask(config)
	task.Handler = &stoppedJobContinueAlarmHandler{}
	task.Start()
}

func (h *stoppedJobContinueAlarmHandler) Do() {
	defer func() {
		if err := recover(); err != nil {
			err = errorcode.NewStackError(errorcode.InternalError_Timer,
				"", err.(error))
			logger.Errorf("Failed to check stopped job continue alarm, with errors:%+v", err)
		}
	}()

	locker := dlocker.NewDlocker("oceanus-stoppedJobContinueAlarmTimer", fmt.Sprintf("optId-%s", "stoppedJobContinueAlarmTimer"), 1200)
	err := locker.Lock()
	if err != nil {
		if strings.Contains(err.Error(), "Locked by others") {
			logger.Infof("could not fetch the lock. %+v", err)
		} else {
			logger.Errorf("could not fetch the lock. %+v", err)
		}
		return
	}
	defer func() {
		err = locker.UnLock()
		if err != nil {
			logger.Errorf("could not release the lock. %+v", err)
		}
	}()

	logger.Debugf("TIMER: Beginning to check stopped jobs with continue alarm")

	// 查询状态为停止且JobConfig配置了ContinueAlarm=1的作业
	stoppedJobs, err := h.findStoppedJobsWithContinueAlarm()
	if err != nil {
		logger.Errorf("TIMER: Find stopped jobs with continue alarm failed, errors: %+v", err)
		return
	}

	// 为每个符合条件的作业发送告警
	for _, job := range stoppedJobs {
		err := h.sendJobStoppedAlarm(job)
		if err != nil {
			logger.Errorf("TIMER: Failed to send alarm for stopped job %s, errors: %+v", job.SerialId, err)
		} else {
			logger.Infof("TIMER: Successfully sent alarm for stopped job %s", job.SerialId)
		}
	}

	logger.Debugf("TIMER: Finished checking stopped jobs with continue alarm")
}

// findStoppedJobsWithContinueAlarm 查找状态为停止且JobConfig配置了ContinueAlarm=1的作业
func (h *stoppedJobContinueAlarmHandler) findStoppedJobsWithContinueAlarm() ([]*table.Job, error) {
	sql := `SELECT j.* FROM Job j WHERE j.Status = ? AND j.ContinueAlarm = ?`

	args := make([]interface{}, 0)
	args = append(args, constants.JOB_STATUS_STOPPED)  // 作业状态为停止
	args = append(args, constants.ContinueAlarmEnable) // ContinueAlarm = 1

	jobs, err := service.Fetch[table.Job](sql, args)
	if err != nil {
		logger.Errorf("findStoppedJobsWithContinueAlarm sql %s, args %+v, error %+v", sql, args, err)
		return nil, err
	}

	validJobs := make([]*table.Job, 0, len(jobs))
	for _, job := range jobs {
		clusterGroup, err := service.GetTableService().ListClusterGroupById(job.ClusterGroupId)
		if err != nil {
			logger.Errorf("findStoppedJobsWithContinueAlarm ListClusterGroupById %d error:%+v", job.ClusterGroupId, err)
			continue
		}
		if clusterGroup.Status == constants.CLUSTER_GROUP_STATUS_ISOLATED ||
			clusterGroup.Status == constants.CLUSTER_GROUP_STATUS_DELETING ||
			clusterGroup.Status == constants.CLUSTER_GROUP_STATUS_DELETED {
			logger.Infof("cluster %s status not valid, should not recover", clusterGroup.SerialId)
			continue
		}
		validJobs = append(validJobs, job)
	}

	return validJobs, nil
}

// sendJobStoppedAlarm 为停止的作业发送告警
func (h *stoppedJobContinueAlarmHandler) sendJobStoppedAlarm(job *table.Job) error {
	eventAlertReq := &barad2.JobAlertReq{
		JobId:     job.SerialId,
		Message:   fmt.Sprintf("Job %s is in STOPPED status.", job.Name),
		EventName: constants.BARAD_EVENT_ALERT_NAME_JOB_STOPPED,
		Status:    constants.BARAD_EVENT_ALERT_ON,
		Type:      constants.EVENT_ENTITY_TYPE_JOB_START_OR_STOP,
		Important: true, // 设置为false，这样1小时内不会重复告警
	}

	retCode, retMsg, err := barad.ProcessEventAlert(eventAlertReq)
	if retCode != 0 {
		return fmt.Errorf("failed to send alert to barad, retCode %d, retMsg %s, with error: %+v", retCode, retMsg, err)
	}

	return nil
}
