package timer

import (
	"context"
	"fmt"
	"strings"

	v1 "k8s.io/api/core/v1"
	extensionsv1beta1 "k8s.io/api/extensions/v1beta1"
	networkingV1 "k8s.io/api/networking/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"
	"k8s.io/client-go/kubernetes"

	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/dlocker"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/timer"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
)

const (
	ingressName = "oceanus-flink-webui"
)

type ingressRouterHandler struct {
	ClusterGroup *table.ClusterGroup
	Cluster      *table.Cluster
	namespace    string
}

func CheckIngressRouterWithFixedDelay() {
	defaultCheckDuration := 60 * 5 // 默认检测周期是 5 分钟

	config := map[string]interface{}{
		"times":    0, // 0 表示无限循环
		"duration": int64(defaultCheckDuration),
	}

	task := timer.NewTask(config)
	task.Handler = &ingressRouterHandler{}
	task.Start()
}

func (h *ingressRouterHandler) Do() {
	defer func() {
		if err := recover(); err != nil {
			err = errorcode.NewStackError(errorcode.InternalError_Timer,
				"", err.(error))
			logger.Errorf("Failed to check ingress router, with errors:%+v", err)
		}
	}()

	locker := dlocker.NewDlocker("oceanus-ingressRouterTimer", fmt.Sprintf("optId-%s", "ingressRouterTimer"), 1200)
	err := locker.Lock()
	if err != nil {
		if strings.Contains(err.Error(), "Locked by others") {
			logger.Infof("could not fetch the lock. %+v", err)
		} else {
			logger.Errorf("could not fetch the lock. %+v", err)
		}
		return
	}
	defer func() {
		err = locker.UnLock()
		if err != nil {
			logger.Errorf("could not release the lock. %+v", err)
		}
	}()

	logger.Debugf("TIMER: Beginning to check ingress router rules")

	sql, args := dao.NewQueryBuilder("select * from ClusterGroup").
		WhereEq("Status", constants.CLUSTER_GROUP_STATUS_RUNNING).
		OrderByDesc("CreateTime").
		Build()

	clusterGroups, err := service.Fetch[table.ClusterGroup](sql, args)
	if err != nil {
		logger.Errorf("Failed to fetch cluster groups: %v", err)
		return
	}

	for _, cg := range clusterGroups {
		clusterGroupService, err := service2.NewClusterGroupService(cg.Id)
		if err != nil {
			logger.Errorf("Failed to fetch cluster group service: %v", err)
			continue
		}
		h.ClusterGroup = cg
		h.namespace = service2.GetDefaultNamespace(h.ClusterGroup)
		if h.Cluster, err = clusterGroupService.GetActiveCluster(); err != nil {
			logger.Errorf("Failed to fetch active cluster: %v", err)
			continue
		}

		// 跳过非 K8s 集群
		if h.Cluster.SchedulerType != constants.SCHEDULER_TYPE_K8S {
			continue
		}
		h.checkAndUpdateIngressForCluster()
	}

	logger.Debugf("TIMER: Finished checking ingress router rules")
}

func (h *ingressRouterHandler) checkAndUpdateIngressForCluster() {
	// 创建 k8s client
	k8sService := k8s.GetK8sService()
	client, err := k8sService.NewClient([]byte(h.Cluster.KubeConfig))
	if err != nil {
		logger.Errorf("Failed to create k8s client for cluster %s: %v", h.ClusterGroup.SerialId, err)
		return
	}

	// 获取所有 Service
	services, err := client.CoreV1().Services(h.namespace).List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		logger.Errorf("Failed to list services in cluster %s: %v", h.ClusterGroup.SerialId, err)
		return
	}

	// 获取Tke的ArchGeneration来判断是否使用v1 API
	archGeneration, err := service.GetTableService().GetTkeArchGeneration(h.Cluster.Id)
	if err != nil {
		logger.Errorf("Failed to get Tke ArchGeneration for cluster %s: %v", h.ClusterGroup.SerialId, err)
		return
	}

	if archGeneration >= constants.TKE_ARCH_GENERATION_V6 {
		// 用 networking/v1 Ingress
		ingresses, err := client.NetworkingV1().Ingresses(h.namespace).List(context.TODO(), metav1.ListOptions{})
		if err != nil {
			logger.Errorf("Failed to list ingresses in cluster %s: %v", h.ClusterGroup.SerialId, err)
			return
		}
		for _, ing := range ingresses.Items {
			if ing.Name != ingressName {
				continue
			}
			ingCopy := ing.DeepCopy()
			for _, svc := range services.Items {
				if err := h.updateIngressForServiceV1(client, &svc, []networkingV1.Ingress{*ingCopy}); err != nil {
					logger.Errorf("Failed to update ingress for service %s in cluster %s: %v", svc.Name, h.ClusterGroup.SerialId, err)
					continue
				}
			}
			return
		}
	} else {
		// 用 extensions/v1beta1 Ingress
		ingresses, err := client.ExtensionsV1beta1().Ingresses(h.namespace).List(context.TODO(), metav1.ListOptions{})
		if err != nil {
			logger.Errorf("Failed to list ingresses in cluster %s: %v", h.ClusterGroup.SerialId, err)
			return
		}
		for _, ing := range ingresses.Items {
			if ing.Name != ingressName {
				continue
			}
			ingCopy := ing.DeepCopy()
			for _, svc := range services.Items {
				if err := h.updateIngressForService(client, &svc, []extensionsv1beta1.Ingress{*ingCopy}); err != nil {
					logger.Errorf("Failed to update ingress for service %s in cluster %s: %v", svc.Name, h.ClusterGroup.SerialId, err)
					continue
				}
			}
			return
		}
	}
}

func (h *ingressRouterHandler) updateIngressForService(client kubernetes.Interface, svc *v1.Service, ingresses []extensionsv1beta1.Ingress) error {
	// 检查是否是 Flink REST service
	if !strings.HasSuffix(svc.Name, "-rest") {
		return nil
	}

	// 获取 service 端口
	var targetPort intstr.IntOrString
	for _, port := range svc.Spec.Ports {
		if port.Name == "rest" {
			targetPort = port.TargetPort
			break
		}
	}

	// 如果没找到合适的端口，使用默认值
	if targetPort.IntValue() == 0 {
		targetPort = intstr.FromInt32(8123)
	}

	// 构建 path
	var path string
	if h.Cluster.CrossTenantEniMode == constants.EnableStaticMode {
		path = fmt.Sprintf("/%s/%s", h.ClusterGroup.SerialId, strings.TrimSuffix(svc.Name, "-rest"))
	} else {
		path = fmt.Sprintf("/%s", strings.TrimSuffix(svc.Name, "-rest"))
	}

	// 更新 ingress 规则
	for _, ing := range ingresses {
		if ing.Name != ingressName {
			continue
		}

		modified := false
		for i := range ing.Spec.Rules {
			rule := &ing.Spec.Rules[i]
			if rule.HTTP == nil {
				rule.HTTP = &extensionsv1beta1.HTTPIngressRuleValue{}
			}

			// 检查是否已存在该路径
			exists := false
			for _, p := range rule.HTTP.Paths {
				if p.Path == path {
					exists = true
					break
				}
			}

			// 如果不存在，添加新路径
			if !exists {
				rule.HTTP.Paths = append(rule.HTTP.Paths, extensionsv1beta1.HTTPIngressPath{
					Path: path,
					Backend: extensionsv1beta1.IngressBackend{
						ServiceName: svc.Name,
						ServicePort: targetPort,
					},
				})
				modified = true
			}
		}

		// 如果有修改，更新 ingress
		if modified {
			_, err := client.ExtensionsV1beta1().Ingresses(h.namespace).Update(context.TODO(), &ing, metav1.UpdateOptions{})
			if err != nil {
				return fmt.Errorf("failed to update ingress: %v", err)
			}
			logger.Infof("Successfully updated ingress rule for service %s in cluster %s", svc.Name, h.ClusterGroup.SerialId)
		}
	}

	return nil
}

// updateIngressForServiceV1 使用 networking/v1 API 更新 Ingress 规则
func (h *ingressRouterHandler) updateIngressForServiceV1(client kubernetes.Interface, svc *v1.Service, ingresses []networkingV1.Ingress) error {
	// 检查是否是 Flink REST service
	if !strings.HasSuffix(svc.Name, "-rest") {
		return nil
	}

	// 获取 service 端口
	var portNumber int32
	for _, port := range svc.Spec.Ports {
		if port.Name == "rest" {
			if port.TargetPort.Type == intstr.Int {
				portNumber = port.TargetPort.IntVal
			} else {
				portNumber = port.Port
			}
			break
		}
	}

	// 如果没找到合适的端口，使用默认值
	if portNumber == 0 {
		portNumber = 8123
	}

	// 构建 path
	var path string
	if h.Cluster.CrossTenantEniMode == constants.EnableStaticMode {
		path = fmt.Sprintf("/%s/%s", h.ClusterGroup.SerialId, strings.TrimSuffix(svc.Name, "-rest"))
	} else {
		path = fmt.Sprintf("/%s", strings.TrimSuffix(svc.Name, "-rest"))
	}

	// 更新 ingress 规则
	for _, ing := range ingresses {
		if ing.Name != ingressName {
			continue
		}

		modified := false
		for i := range ing.Spec.Rules {
			rule := &ing.Spec.Rules[i]
			if rule.HTTP == nil {
				rule.HTTP = &networkingV1.HTTPIngressRuleValue{}
			}

			// 检查是否已存在该路径
			exists := false
			for _, p := range rule.HTTP.Paths {
				if p.Path == path {
					exists = true
					break
				}
			}

			// 如果不存在，添加新路径
			if !exists {
				// 使用 PathTypePrefix 路径类型
				pathType := networkingV1.PathTypePrefix
				rule.HTTP.Paths = append(rule.HTTP.Paths, networkingV1.HTTPIngressPath{
					Path:     path,
					PathType: &pathType,
					Backend: networkingV1.IngressBackend{
						Service: &networkingV1.IngressServiceBackend{
							Name: svc.Name,
							Port: networkingV1.ServiceBackendPort{
								Number: portNumber,
							},
						},
					},
				})
				modified = true
			}
		}

		// 如果有修改，更新 ingress
		if modified {
			_, err := client.NetworkingV1().Ingresses(h.namespace).Update(context.TODO(), &ing, metav1.UpdateOptions{})
			if err != nil {
				return fmt.Errorf("failed to update ingress: %v", err)
			}
			logger.Infof("Successfully updated ingress rule for service %s in cluster %s with v1 API", svc.Name, h.ClusterGroup.SerialId)
		}
	}

	return nil
}
