package password

import (
	"fmt"
	"golang.org/x/crypto/bcrypt"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
)

var (
	passwordService *PasswordService
)

type PasswordService struct {
}

func (o *PasswordService) GenHtpasswdBBrypt(user, password string) (htpasswd string, err error) {
	pwdBytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	return fmt.Sprintf("%s:%s", user, string(pwdBytes)), nil
}

func NewPasswordService() *PasswordService {
	return &PasswordService{}
}

func GetPasswordService() *PasswordService {
	if passwordService == nil {
		passwordService = NewPasswordService()
	}
	return passwordService
}
