package interface_auth

import (
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
)

var (
	controllerNameMap map[string]string
)

func init() {
	controllerNameMap = make(map[string]string)
}

func RegisterControllerNameMap(mc, apiV3 string) {
	if oldApiV3, exist := controllerNameMap[mc]; exist {
		logger.Errorf("Controller %s already mapped to apiV3 %s, will override by %s", mc, oldApiV3, apiV3)
	}
	controllerNameMap[mc] = apiV3
}

func GetMappedControllerName(mc string) (apiV3 string, exist bool) {
	apiV3, exist = controllerNameMap[mc]
	return
}
