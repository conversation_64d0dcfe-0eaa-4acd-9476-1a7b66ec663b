package sql_gateway

import (
	"encoding/base64"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/sql_gateway"
)

type SqlGatewayService struct {
	clusterId string
	sessionId string
}

func NewSqlGatewayService(clusterId string, sessionId string) *SqlGatewayService {
	return &SqlGatewayService{
		clusterId: clusterId,
		sessionId: sessionId,
	}
}

func (s *SqlGatewayService) RunStatement(req *sql_gateway.RunSqlGatewayStatementReq) (rsp *sql_gateway.RunSqlGatewayStatementRsp, err error) {
	logger.Debugf("RunSqlGatewayStatement %v", req)

	// 请求sqlGateway
	sqlGatewayServerService, err := NewSqlGatewayServerService(req.AppId, req.ClusterId, req.RequestId, req.Uin, req.Region)
	if err != nil {
		return nil, err
	}
	sessionId := req.SessionId
	if len(sessionId) == 0 {
		sqlGatewaySessionRsp, err := sqlGatewayServerService.GetSqlGatewaySession(req.FlinkVersion)
		if err != nil {
			return nil, err
		}
		sessionId = sqlGatewaySessionRsp.SessionId
	}
	sql, errBase64 := base64.StdEncoding.DecodeString(req.Sql)
	if err != nil {
		return nil, errBase64
	}

	return sqlGatewayServerService.RunSqlGatewayStatement(sessionId, string(sql), req.FlinkVersion)
}

func (s *SqlGatewayService) FetchResult(req *sql_gateway.FetchSqlGatewayResultReq) (rsp *sql_gateway.FetchSqlGatewayResultRsp, err error) {
	logger.Debugf("FetchResult %v", req)

	sqlGatewayServerService, err := NewSqlGatewayServerService(req.AppId, req.ClusterId, req.RequestId, req.Uin, req.Region)
	if err != nil {
		return nil, err
	}

	// 请求sqlGateway查询结果
	return sqlGatewayServerService.FetchSqlGatewayResult(req.ResultUri, req.SessionId, req.OperationHandleId, req.FlinkVersion)
}

func (s *SqlGatewayService) CancelSqlGatewayStatement(req *sql_gateway.CancelSqlGatewayStatementReq) (err error) {
	logger.Infof("CancelSqlGatewayStatement %v", req)

	sqlGatewayServerService, err := NewSqlGatewayServerService(req.AppId, req.ClusterId, req.RequestId, req.Uin, req.Region)
	if err != nil {
		logger.Errorf("CancelSqlGatewayStatement NewSqlGatewayServerService err %v", err)
		return err
	}

	// 请求sqlGateway查询结果
	return sqlGatewayServerService.CancelSqlGatewayStatement(req.SessionId, req.OperationHandleId, req.FlinkVersion, req.JobID)
}
