package sql_gateway

import (
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	commandc "tencentcloud.com/tstream_galileo/src/common/command"
	command "tencentcloud.com/tstream_galileo/src/common/command/client/clientset/versioned"
	"tencentcloud.com/tstream_galileo/src/common/configure"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/sql_gateway"
	sql_gateway_mode "tencentcloud.com/tstream_galileo/src/tstream_cc/model/sql_gateway"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service_cluster "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
	sql "tencentcloud.com/tstream_galileo/src/tstream_cc/service/sql"
	service_table "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
)

const (
	ConfItemTimeout = "clusterAdmin.timeout"
)

type SqlServerService struct {
	requestId       string
	uin             string
	region          string
	commandClient   *command.Clientset
	clusterId       int64
	clusterSerialId string
}

func NewSqlGatewayServerService(appId int64, clusterSerialId string, requestId string, uin string, region string) (s *SqlServerService, err error) {
	sqlServerService := &SqlServerService{
		requestId:       requestId,
		region:          region,
		uin:             uin,
		clusterSerialId: clusterSerialId,
	}

	tableService := service_table.GetTableService()
	describeClusterFeaturesReq := &model.DescribeClusterFeaturesReq{
		RequestBase: apiv3.RequestBase{
			RequestId: requestId,
			Region:    region,
			Uin:       uin,
			AppId:     appId,
		},
		ClusterIds: []string{clusterSerialId},
	}
	// 1.查询集群特性
	svr := service_cluster.NewDescribeClusterFeaturesService(describeClusterFeaturesReq)
	describeClusterFeaturesRsp, err := svr.DescribeClusterFeatures()
	if err != nil {
		logger.Errorf("[%s] Failed to list ClusterFeatures ,err:%s", requestId, err.Error())
		return sqlServerService, err
	}
	clusterFeatureSet := describeClusterFeaturesRsp.ClusterFeatureSet

	if len(clusterFeatureSet) == 0 {
		msg := fmt.Sprintf(" [%s] Not found active cluster ", requestId)
		logger.Error(msg)
		return sqlServerService, errors.New(msg)
	}
	// 2. 选择SqlServer容器化的集群
	featureSqlServerContainerization, _ := findClusterWithFeature(clusterFeatureSet, constants.FeatureSqlServerContainerization, requestId)
	if featureSqlServerContainerization == nil { //  old cluster
		return sqlServerService, nil
	}
	// 3. 查询集群配置
	cond := dao.NewCondition().
		Eq("SerialId", clusterSerialId)
	where, args := cond.GetWhere()
	clusters, err := tableService.ListClustersBySqlWithArgs(
		"select b.* from ClusterGroup as a join Cluster as b on a.id = b.clusterGroupId "+where, args...)
	if err != nil {
		logger.Errorf("%s Failed to get Cluster ,err:%s", requestId, err.Error())
		return sqlServerService, err
	}
	if len(clusters) == 0 {
		return sqlServerService, errors.New(fmt.Sprint(" Not found active cluster %s", clusterSerialId))
	}
	k8sService := k8s.GetK8sService()
	//  4.获取k8s客户端
	cluster := clusters[0]
	sqlServerService.clusterId = cluster.Id
	client, err := k8sService.NewCommandClient([]byte(cluster.KubeConfig))
	if err != nil {
		logger.Errorf("%s Failed to create command commandClient config:%s", cluster.KubeConfig)
		return sqlServerService, err
	}
	sqlServerService.commandClient = client

	return sqlServerService, nil
}

func (s *SqlServerService) FetchSqlGatewayResult(nextResultUri string, sessionId string, operationId string, flinkVersion string) (*sql_gateway.FetchSqlGatewayResultRsp, error) {
	logger.Debugf("FetchSqlGatewayResult %s %s", sessionId, operationId)

	commandService, err := commandc.NewCommandService(s.clusterId)
	if err != nil {
		return nil, errorcode.FailedOperationCode_FetchSqlPreview.NewWithErr(err)
	}

	resultUri := "/v1/sessions/" + sessionId + "/operations/" + operationId + "/result/0"
	if len(nextResultUri) != 0 {
		resultUri = nextResultUri
	}
	url := "http://" + GetSqlGatewayServiceName(flinkVersion) + ":" + strconv.Itoa(constants.
		SqlGatewayPort) + resultUri

	ssRsp, err := commandService.DoRequest(&commandc.CommandRequest{
		ReqId:    s.requestId,
		Url:      url,
		SendData: "",
		Uin:      s.uin,
		Apikey:   "FetchSqlGatewayResult",
		Timeout:  getTimeout(),
		Callback: "",
	})
	if err != nil {
		return nil, errorcode.FailedOperationCode_FetchSqlPreview.NewWithErr(err)
	}
	rsp := &sql_gateway_mode.SqlGatewayFetchResultRsp{}
	err = json.Unmarshal([]byte(ssRsp), rsp)
	if err != nil {
		logger.Errorf("Error parsing response: %v ret: %v", ssRsp, err)
		return nil, errorcode.FailedOperationCode_FetchSqlGatewayStatement.NewWithMsg("Failed to run sql gateway statement response. ")
	}

	index := strings.Index(ssRsp, "errors")
	if index != -1 {
		fetchSqlGatewayStatementRsp := &sql_gateway.FetchSqlGatewayResultRsp{
			ErrorMessage: make([]string, 0),
		}
		for _, q := range rsp.ErrorMessage {
			fetchSqlGatewayStatementRsp.ErrorMessage = append(fetchSqlGatewayStatementRsp.ErrorMessage, q)
		}
		logger.Infof("fetch result : %v", fetchSqlGatewayStatementRsp)

		return fetchSqlGatewayStatementRsp, nil
	}

	fetchSqlGatewayStatementRsp := &sql_gateway.FetchSqlGatewayResultRsp{
		JobID:         rsp.JobID,
		ErrorMessage:  rsp.ErrorMessage,
		ResultType:    rsp.ResultType,
		IsQueryResult: rsp.IsQueryResult,
		ResultKind:    rsp.ResultKind,
		Results: sql_gateway.StatementResult{
			Columns:   make([]*sql_gateway.Column, 0),
			RowFormat: rsp.Results.RowFormat,
			Data:      make([]*sql_gateway.ResultData, 0),
		},
		NextResultUri: rsp.NextResultUri,
	}

	for _, c := range rsp.Results.Columns {
		t := &sql_gateway.Column{
			Name: c.Name,
			LogicalType: &sql_gateway.LogicalType{
				Type:     c.LogicalType.Type,
				NullAble: c.LogicalType.Nullable,
				Length:   c.LogicalType.Length,
			},
			Comment: c.Comment,
		}

		fetchSqlGatewayStatementRsp.Results.Columns = append(fetchSqlGatewayStatementRsp.Results.Columns, t)
	}

	for _, r := range rsp.Results.Data {
		t := &sql_gateway.ResultData{
			Kind:   r.Kind,
			Fields: make([]interface{}, 0),
		}

		for _, q := range r.Fields {
			t.Fields = append(t.Fields, q)
		}

		fetchSqlGatewayStatementRsp.Results.Data = append(fetchSqlGatewayStatementRsp.Results.Data, t)
	}

	return fetchSqlGatewayStatementRsp, nil
}

func GetSqlGatewayServiceName(flinkVersion string) string {
	sqlGatewayServiceName := constants.SqlGatewayServiceName
	if constants.Flinkversion118 == flinkVersion {
		fv := service2.GetNoDotLowerFlinkVersion(flinkVersion)
		sqlGatewayServiceName = fmt.Sprintf("sql-gateway-%s-rest.default", fv)
	}
	return sqlGatewayServiceName
}

func (s *SqlServerService) RunSqlGatewayStatement(sessionId string, sql, flinkVersion string) (*sql_gateway.RunSqlGatewayStatementRsp, error) {
	logger.Debugf("RunSqlGatewayStatement %s %s", sessionId, sql)

	reqData := make(map[string]string, 0)
	reqData["statement"] = sql
	reqDataJson, err := json.Marshal(reqData)
	if err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}
	commandService, err := commandc.NewCommandService(s.clusterId)
	if err != nil {
		return nil, errorcode.FailedOperationCode_RunSqlPreview.NewWithErr(err)
	}

	url := "http://" + GetSqlGatewayServiceName(flinkVersion) + ":" + strconv.Itoa(constants.
		SqlGatewayPort) + "/v1/sessions/" + sessionId + "/statements/"
	ssRsp, err := commandService.DoRequest(&commandc.CommandRequest{
		ReqId:    s.requestId,
		Url:      url,
		SendData: string(reqDataJson),
		Uin:      s.uin,
		Apikey:   "RunSqlGatewayStatement",
		Timeout:  getTimeout(),
		Callback: "",
	})
	if err != nil {
		return nil, errorcode.FailedOperationCode_RunSqlPreview.NewWithErr(err)
	}
	rsp := &sql_gateway_mode.SqlGatewayRunStatementRsp{}
	err = json.Unmarshal([]byte(ssRsp), rsp)

	if err != nil {
		logger.Errorf("Error parsing response: %v ret: %v", ssRsp, err)
		return nil, errorcode.FailedOperationCode_RunSqlGateway.NewWithMsg("Failed to parse sql server response. ")
	}

	index := strings.Index(ssRsp, "errors")
	if index != -1 {
		sqlGatewayStatementRsp := &sql_gateway.RunSqlGatewayStatementRsp{
			ErrorMessage: make([]string, 0),
		}
		for _, q := range rsp.ErrorMessage {
			sqlGatewayStatementRsp.ErrorMessage = append(sqlGatewayStatementRsp.ErrorMessage, q)
		}
		logger.Infof("run statement : %v", sqlGatewayStatementRsp)

		return sqlGatewayStatementRsp, nil
	}

	sqlGatewayStatementRsp := &sql_gateway.RunSqlGatewayStatementRsp{
		ErrorMessage:      rsp.ErrorMessage,
		OperationHandleId: rsp.OperationHandleId,
		SessionId:         sessionId,
	}
	return sqlGatewayStatementRsp, nil
}

// curl --request POST
// http://localhost:8083/v1/sessions/b9c2aab1-cc37-4bbd-985a-09f34a2f10a6/operations/b4281161-7df9-4a50-9b67-f2de5e1c28e0/cancel
func (s *SqlServerService) CancelSqlGatewayStatement(sessionId string, operationId string, flinkVersion string, jobID string) error {
	logger.Infof("CancelSqlGatewayStatement: ")

	ssReq := &sql_gateway_mode.CancelSqlGatewayReq{}

	sendData, err := json.Marshal(ssReq)
	if err != nil {
		msg := fmt.Sprintf("%s Error converting SendData", s.requestId)
		logger.Error(msg)
		return errorcode.FailedOperationCode_RunSqlGateway.NewWithMsg(msg)
	}

	commandService, err := commandc.NewCommandService(s.clusterId)
	if err != nil {
		logger.Errorf("CancelSqlGatewayStatement failed: %v", err)
		return errorcode.FailedOperationCode_RunSqlGateway.NewWithErr(err)
	}
	url := fmt.Sprintf("http://%s:%s/v1/sessions/%s/operations/%s/cancel",
		GetSqlGatewayServiceName(flinkVersion), strconv.Itoa(constants.SqlGatewayPort), sessionId, operationId)
	logger.Infof("CancelSqlGatewayStatement url: %s", url)
	_, err = commandService.DoRequest(&commandc.CommandRequest{
		ReqId:    s.requestId,
		Url:      url,
		SendData: string(sendData),
		Uin:      s.uin,
		Apikey:   "CancelSqlGatewayStatement",
		Timeout:  getTimeout(),
		Callback: "",
	})
	if err != nil {
		logger.Errorf("CancelSqlGatewayStatement failed: %v", err)
		return errorcode.FailedOperationCode_RunSqlPreview.NewWithErr(err)
	}

	if jobID != "" {
		logger.Infof("CancelSqlGatewayStatement jobID: %s", jobID)
		//取消 session 集群 job
		noDotLowerFlinkVersion := service2.GetNoDotLowerFlinkVersion(flinkVersion)
		sessionId = fmt.Sprintf(constants.ClusterSessionAPPTemplate, s.clusterSerialId, noDotLowerFlinkVersion)
		sessionUrl := fmt.Sprintf("http://%s-rest.default:8081/jobs/%s/yarn-cancel", sessionId, jobID)
		logger.Infof("CancelSqlGatewayStatement_sessionId: %s", sessionId)
		logger.Infof("CancelSqlGatewayStatement_sessionUrl: %s", sessionUrl)
		_, err = commandService.DoRequest(&commandc.CommandRequest{
			ReqId:    s.requestId,
			Url:      sessionUrl,
			Uin:      s.uin,
			Apikey:   "CancelSqlGatewaySession",
			Timeout:  5,
			Callback: "",
		})
		if err != nil {
			logger.Errorf("CancelSqlGatewayStatement_sessionId failed: %v", err)
			return errorcode.FailedOperationCode_RunSqlPreview.NewWithErr(err)
		}
	}

	return nil
}

func (s *SqlServerService) GetSqlGatewaySession(flinkVersion string) (*sql_gateway.RunSqlGatewaySessionRsp, error) {
	logger.Debug("GetSqlGatewaySession: ")

	ssReq := &sql_gateway_mode.SqlGatewaySessionReq{}
	ssReq.SessionName = ""
	ssReq.Properties = make(map[string]string)

	sendData, err := json.Marshal(ssReq)
	if err != nil {
		msg := fmt.Sprintf("%s Error converting SendData", s.requestId)
		logger.Error(msg)
		return nil, errorcode.FailedOperationCode_RunSqlGateway.NewWithMsg(msg)
	}

	commandService, err := commandc.NewCommandService(s.clusterId)
	if err != nil {
		return nil, errorcode.FailedOperationCode_RunSqlGateway.NewWithErr(err)
	}
	url := "http://" + GetSqlGatewayServiceName(flinkVersion) + ":" + strconv.Itoa(constants.
		SqlGatewayPort) + "/v1/sessions"
	ssRsp, err := commandService.DoRequest(&commandc.CommandRequest{
		ReqId:    s.requestId,
		Url:      url,
		SendData: string(sendData),
		Uin:      s.uin,
		Apikey:   "GetSqlGatewaySession",
		Timeout:  getTimeout(),
		Callback: "",
	})
	if err != nil {
		return nil, errorcode.FailedOperationCode_RunSqlPreview.NewWithErr(err)
	}
	rsp := &sql_gateway_mode.SqlGatewaySessionRsp{}
	err = json.Unmarshal([]byte(ssRsp), rsp)
	if err != nil {
		logger.Errorf("Error parsing response: %v ret: %v", ssRsp, err)
		return nil, errorcode.FailedOperationCode_RunSqlGateway.NewWithMsg("Failed to parse sql gateway response.")
	}
	sqlGatewaySessionRsp := &sql_gateway.RunSqlGatewaySessionRsp{
		ErrorMessage: rsp.ErrorMessage,
		SessionId:    rsp.SessionId,
	}
	return sqlGatewaySessionRsp, nil
}

func getTimeout() int64 {
	var timeout = configure.GetConfInt64Value(sql.ConfFile, ConfItemTimeout, 30)
	return timeout
}

func findClusterWithFeature(clusterFeatureSet []*model.ClusterFeatureItem, clusterFeature string, requestId string) (
	cluster *model.ClusterFeatureItem, err error) {
	for _, item := range clusterFeatureSet {
		for _, feature := range item.SupportFeatures {
			if feature == clusterFeature {
				return item, nil
			}
		}
	}
	msg := fmt.Sprintf("[%s]: Not support feature %s.", requestId, clusterFeature)
	return nil, errors.New(msg)
}
