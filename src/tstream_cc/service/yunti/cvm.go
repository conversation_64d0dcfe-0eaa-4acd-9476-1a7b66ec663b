package yunti

import (
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/yunti"
	service5 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
)

func TerminateInstances(returnReason string, cvmInstances []*string, region string, serialId string) error {
	currentTime := util.GetCurrentTime()
	// 销毁CVM且连带销毁数据盘
	CreateCVMReturnOrderReq := &yunti.CreateCVMReturnOrderReq{
		Jsonrpc: "2.0",
		Id:      currentTime,
		Method:  "createCvmReturnOrder",
		Params: struct {
			IsReturnNow       int       `json:"isReturnNow"`
			InstanceList      []*string `json:"instanceList"`
			IsWithDataDisks   int       `json:"isWithDataDisks"`
			ReturnType        int       `json:"returnType"`
			ReturnReasonClass string    `json:"returnReasonClass"`
			Force             bool      `json:"force"`
			AcceptCostShare   bool      `json:"acceptCostShare"`
		}{IsReturnNow: 1, InstanceList: cvmInstances, IsWithDataDisks: 1, ReturnType: 0, ReturnReasonClass: returnReason, Force: true, AcceptCostShare: true},
	}
	CreateCVMReturnOrderRsp := &yunti.CreateCVMReturnOrderRsp{}

	CreateCVMReturnOrderRspStr, err := service5.SendRequest2YunTi("apply", "api", region, CreateCVMReturnOrderReq)
	if err != nil {
		return err
	}
	err = service5.UnPackResp(CreateCVMReturnOrderRspStr, CreateCVMReturnOrderRsp)
	if err != nil {
		return err
	}
	// 判断有没有从云梯返回的error
	if CreateCVMReturnOrderRsp.Error.Code != 0 {
		logger.Errorf("RequestId: %s createCvmReturnOrder with error: %s", currentTime, CreateCVMReturnOrderRsp.Error.Message)
		return errorcode.InternalErrorCode.NewWithMsg(CreateCVMReturnOrderRsp.Error.Message)
	}
	logger.Infof("RequestId: %s, ClusterGroup: %s, createCvmReturnOrder with Orderid: %s", currentTime, serialId, CreateCVMReturnOrderRsp.Result.OrderId)
	return nil
}
