package region

import (
	"encoding/json"
	"errors"
	"fmt"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common"
	regionService "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/region/v20220627"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	jobModel "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/region"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/region"
	commonSerivce "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/qcloud"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/role_auth"
	service4 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/white_list"
)

func GetRegionIdByName(name string) (regionId int, err error) {
	regionList, err := ListRegionV2([]string{name}, nil, nil, nil, nil, nil)
	if err != nil {
		logging.Errorf("GetRegionIdByName %s error: %+v", name, err)
		return regionId, err
	}

	if len(regionList) == 0 {
		msg := fmt.Sprintf("no such region: %s", name)
		return regionId, errorcode.UnsupportedOperationCode.ReplaceDesc(msg)
	}
	regionId = regionList[0].RegionId

	return regionId, err
}

func GetRegionNameById(id int) (regionName string, err error) {
	regionList, err := ListRegionV2(nil, nil, nil, []int{id}, nil, nil)
	if err != nil {
		logging.Errorf("GetRegionNameById %d error: %+v", id, err)
		return regionName, err
	}

	if len(regionList) == 0 {
		msg := fmt.Sprintf("no such region: %d", id)
		return regionName, errorcode.UnsupportedOperationCode.ReplaceDesc(msg)
	}

	regionName = regionList[0].Region
	return regionName, err
}

func GetRegionById(regionId int) (region *table.Region, err error) {
	regionList, err := ListRegionV2(nil, nil, nil, []int{regionId}, nil, nil)
	if err != nil {
		logging.Errorf("GetRegionNameById %d error: %+v", regionId, err)
		return region, err
	}

	if len(regionList) == 0 {
		msg := fmt.Sprintf("no such region: %d", regionId)
		return region, errorcode.UnsupportedOperationCode.ReplaceDesc(msg)
	}

	return regionList[0], err
}

func GetZoneById(id int) (zoneName string, err error) {

	sql := "SELECT * FROM Zone WHERE zoneId = ? LIMIT 1"
	args := make([]interface{}, 0)
	args = append(args, id)

	_, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logging.Errorf("Failed to execute SQL %s with args:%+v. Error:%+v", sql, args, err)
		return "", err
	}

	zoneList := make([]*table.Zone, 0, 0)
	for _, v := range data {
		ref := &table.Zone{}
		_ = util.ScanMapIntoStruct(ref, v)
		zoneList = append(zoneList, ref)
	}

	if len(zoneList) != 1 {
		logging.Errorf("Length of returned zoneList is NOT 1 but %d. %+v", len(zoneList), zoneList)
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, "specified zone id not found", nil)
	}

	return zoneList[0].Zone, nil
}

func ListRegionV2(regions []string, regionNames []string, netEnvTypes []int, regionIds []int, states []int, clusterTypes []int) (regionList []*table.Region, err error) {

	cond := dao.NewCondition()
	if len(regions) > 0 {
		cond.In("region", regions)
	}

	if len(regionNames) > 0 {
		cond.In("regionName", regionNames)
	}

	if len(states) > 0 {
		cond.In("state", states)
	} else {
		cond.Eq("state", constants.REGION_STATUS_ACTIVE)
	}

	if len(netEnvTypes) > 0 {
		cond.In("netEnvType", netEnvTypes)
	}

	if len(regionIds) > 0 {
		cond.In("regionId", regionIds)
	}

	if len(clusterTypes) > 0 {
		cond.In("ClusterType", clusterTypes)
	} else {
		cond.Eq("ClusterType", constants.K8S_CLUSTER_TYPE_TKE)
	}

	where, args := cond.GetWhere()
	sql := "SELECT * FROM Region " + where

	regionList = make([]*table.Region, 0, 0)

	_, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return regionList, err
	}

	for _, v := range data {
		ref := &table.Region{}
		util.ScanMapIntoStruct(ref, v)
		regionList = append(regionList, ref)
	}

	return regionList, err
}

func ListVPCRegion() (regionList []*table.Region, err error) {
	regionList, err = ListRegionV2(nil, nil, []int{constants.NETWORK_ENV_CLOUD_VPC},
		nil, nil, []int{constants.K8S_CLUSTER_TYPE_TKE})
	if err != nil {
		logging.Errorf("ListVPCRegion error: %+v", err)
		return regionList, err
	}

	return regionList, err
}

func GetRegionJobCount(appId int32, regions []string, clusterGroupIds []string) (t []*table.RegionJobCount, err error) {

	if len(regions) == 0 {
		regions, err = getRegions()
		if err != nil {
			logging.Errorf("GetRegionJobCount getRegions error: %+v", err)
			return t, err
		}
	}

	t = make([]*table.RegionJobCount, 0, 0)
	listJobQuery := jobModel.ListJobQuery{
		AppId:           appId,
		Regions:         regions,
		IsVagueNames:    false,
		ClusterGroupIds: clusterGroupIds,
		Offset:          0,
		Limit:           0,
	}
	results, err := service3.ListJobs(&listJobQuery)
	if err != nil {
		logging.Errorf("GetRegionJobCount, errors:%+v", err)
		return t, err
	}

	if len(results) == 0 {
		for _, region := range regions {
			regionCount := &table.RegionJobCount{}
			regionCount.Region = region
			t = append(t, regionCount)
		}

		return
	}

	// 计算每一个Region中的job数量
	regionMap := make(map[string]*table.RegionJobCount, 0)
	for _, value := range results {
		if regionCount, ok := regionMap[value.Region]; ok {
			IncRegionJobCountNum(value.Status, regionCount)
		} else {
			regionCount := &table.RegionJobCount{}
			regionCount.Region = value.Region
			IncRegionJobCountNum(value.Status, regionCount)
			regionMap[value.Region] = regionCount
			t = append(t, regionCount)
		}
	}

	return t, err
}

func GetRegionItemSpaceCount(appId int32, regions []string, uin string, subUin string) (t map[string][]string, err error) {
	isSupOwner := 0
	if uin == subUin {
		isSupOwner = 1
	}
	_, uins, err2 := service2.GetAdminstors(int64(appId), subUin)
	if err2 != nil {
		logging.Errorf(":  query  Administrator error: %+v", err2)
		return t, err
	}

	if ok, _ := service2.Contain(subUin, uins); ok {
		isSupOwner = 1
	}

	var authCheck map[string][]string
	if isSupOwner > 0 {
		authCheck, err = auth.ItemSpaceSearch(int64(appId), uin, regions)
	} else {
		authCheck, err = auth.ItemSpaceSearch(int64(appId), subUin, regions)
	}
	return authCheck, nil
}

func IncRegionJobCountNum(status int8, regionCount *table.RegionJobCount) {
	switch status {
	case constants.JOB_STATUS_RUNNING:
		regionCount.RunningJobCount += 1
	case constants.JOB_STATUS_STOPPED, constants.JOB_STATUS_FINISHED:
		regionCount.StoppedJobCount += 1
	case constants.JOB_STATUS_PAUSED:
		regionCount.PausedJobCount += 1
	case constants.JOB_STATUS_PROGRESS:
		regionCount.ProgressJobCount += 1
	case constants.JOB_STATUS_CONCERNING:
		regionCount.ConcerningJobCount += 1
	case constants.JOB_STATUS_CREATE:
		regionCount.CreatedJobCount += 1
	case constants.JOB_STATUS_INITIALIZED:
		regionCount.InitializedJobCount += 1
	}
	if status != constants.JOB_STATUS_DELETE {
		regionCount.TotalJobCount += 1
	}
}

func IncRegionClusterCountNum(status int8, regionCount *table.RegionClusterCount) {
	// 删除状态的忽略
	if status != constants.CLUSTER_GROUP_STATUS_DELETED && status != constants.CLUSTER_GROUP_STATUS_DELETING {
		regionCount.CreatingClusterCount += 1
		regionCount.TotalClusterCount += 1
	}
}

func getRegions() (regions []string, err error) {
	regionList, err := ListRegionV2(nil, nil, nil, nil, nil, nil)
	if err != nil {
		logging.Errorf("getRegions ListRegionV2 error: %+v", err)
		return regions, err
	}

	tmp := make(map[string]int, 0)
	for _, region := range regionList {
		tmp[region.Region] = 0
	}

	regions = make([]string, 0, 0)
	for key, _ := range tmp {
		regions = append(regions, key)
	}

	return regions, err
}

// FIXME: 功能与 cluster_service 的 GetClusterGroupTotalCount 基本重复
func GetRegionClusterCount(appId int32, regions []string, clusterGroupIds []string) (t []*table.RegionClusterCount, err error) {

	if len(regions) == 0 {
		regions, err = getRegions()
		if err != nil {
			logging.Errorf("GetRegionClusterCount getRegions error: %+v", err)
			return t, err
		}
	}
	results, err := service3.ListClusterGroups(&service3.ListClusterGroupsParam{
		AppId:        appId,
		Regions:      regions,
		Zone:         "",
		ClusterType:  []int8{constants.CLUSTER_GROUP_TYPE_PRIVATE, constants.CLUSTER_GROUP_TYPE_UNIFORM},
		ClusterNames: nil,
		IsVagueNames: false,
		SerialIds:    clusterGroupIds,
		Offset:       0,
		Limit:        0,
		OrderType:    0,
		StatusList:   nil,
	})
	if err != nil {
		logging.Errorf("GetRegionClusterCount ListClusterGroups, errors: %+v", err)
		return t, err
	}

	if len(results) == 0 {
		for _, region := range regions {
			regionCount := &table.RegionClusterCount{}
			regionCount.Region = region
			t = append(t, regionCount)
		}

		return
	}
	logging.Debugf("GetRegionClusterCount result %d", len(results))
	// 计算每一个Region中的集群数量
	regionMap := make(map[string]*table.RegionClusterCount, 0)
	t = make([]*table.RegionClusterCount, 0, 0)

	for _, value := range results {
		if regionCount, ok := regionMap[value.Region]; ok {
			IncRegionClusterCountNum(value.Status, regionCount)
		} else {
			regionCount := &table.RegionClusterCount{}
			regionCount.Region = value.Region
			IncRegionClusterCountNum(value.Status, regionCount)
			regionMap[value.Region] = regionCount
			t = append(t, regionCount)
		}
	}

	logging.Debugf("GetRegionClusterCount t %d", len(t))

	return t, err
}

// http://cloud.region.woa.com/index/#/
func DescribeRegionsAndZonesFromCloud() (*regionService.DescribeRegionsAndZonesResponse, error) {
	secretId, secretKey, err := commonSerivce.GetSecretIdAndKey()
	if err != nil {
		logging.Errorf("StsAssumeRoleReturnExpiredTime GetSecretIdAndKey error: %+v", err)
		return nil, err
	}
	credential := common.NewCredential(
		secretId,
		secretKey,
	)

	prof := qcloud.NewClientProfile()
	prof.HttpProfile.Endpoint = "region.tencentcloudapi.com"
	prof.HttpProfile.ReqTimeout = constants.HTTP_PROFILE_REQ_TIMEOUT

	client, err := regionService.NewClient(credential, "", prof)
	if err != nil {
		logging.Errorf("SregionService.NewClient error: %+v", err)
		return nil, err
	}

	request := regionService.NewDescribeRegionsAndZonesRequest()
	product := "oceanus"
	request.Product = &product
	response, err := client.DescribeRegionsAndZones(request)
	if err != nil {
		logging.Errorf("client.DescribeRegions error: %+v", err)
		return nil, err
	}

	return response, nil
}

/**
 ** map[int][]map[string]interface{}
 ** 1. key-1:   netEnvType [1. Cloud VPC, 2. Support Env, 3. Inner VPC]
 ** 2. value-1: regions
 ** 3. key-2:   region property key
 ** 4. value-2: region property value
 */
func GetRegionZones(callRegion string, appId int32, uin string, regionFilters []string, clusterTypes []int) (t map[int][]*region.RegionZoneDetail, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logging.Errorf("Get RegionZones panic, errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	regionZones := map[int][]*region.RegionZoneDetail{}
	// 内网(云梯)账号的可用区以云官网为准，所以这里注释掉，参考: https://tapd.woa.com/yunti/markdown_wikis/show/#1220416802000190995
	//networkEnvAppIdWhiteList := strings.Split(configure.GetConfStringValue("component.properties", "component.appId.whiteList.netEnv"), ",")
	//appIdStr := strconv.FormatInt(int64(appId), 10)
	//if util.InStrArray(appIdStr, networkEnvAppIdWhiteList) { // 自研上云白名单账号
	//	innerEnvRegionZones, err := getRegionZonesByCondition(appId, []string{}, constants.NETWORK_ENV_INNER_VPC, clusterTypes)
	//	if err != nil {
	//		return t, err
	//	}
	//	service3.SortSliceByField(innerEnvRegionZones, "ZoneId", false)
	//	regionZones[constants.NETWORK_ENV_INNER_VPC] = innerEnvRegionZones
	//	// 自研上云账号不允许使用现网 VPC 环境, 需要直接返回
	//	return regionZones, nil
	//}

	cloudVpcRegionZones, err := getRegionZonesByCondition(appId, regionFilters, constants.NETWORK_ENV_CLOUD_VPC, clusterTypes)
	if err != nil {
		logging.Errorf("GetRegionZones GetWhitelistRegion error: %+v", err)
		return t, err

	}

	// 白名单开放区域, 与自研上云白名单解耦
	allWhitelistRegion, myWhiteListRegion, err := service4.GetWhitelistRegion(uin)
	if err != nil {
		logging.Errorf("GetRegionZones GetWhitelistRegion error: %+v", err)
		return t, err
	}
	allWhitelistRegionSet := service3.SliceToSetString(allWhitelistRegion)
	myWhiteListRegionSet := service3.SliceToSetString(myWhiteListRegion)
	openedCloudVpcRegionZones := make([]*region.RegionZoneDetail, 0, len(cloudVpcRegionZones))

	for _, r := range cloudVpcRegionZones {
		// 不在白名单控制的区域全开放
		if _, exist := allWhitelistRegionSet[r.Region]; !exist {
			openedCloudVpcRegionZones = append(openedCloudVpcRegionZones, r)
			continue
		}
		// 白名单控制区域，检查是否对appId开放
		if _, exist := myWhiteListRegionSet[r.Region]; exist {
			openedCloudVpcRegionZones = append(openedCloudVpcRegionZones, r)
		}
	}

	if callRegion != constants.AP_SHANGHAI_ADC {
		/**
		 * 先从地域管理系统获取全量地域可用区， 其他筛选功能字段还是从数据库取 比如 supportSharedCluster supportInternational ClusterType netEnvType
		 * http://cloud.region.woa.com/index/#/productAreaPage
		 */
		cloudRegionMap := make(map[string]regionService.ResourceRegionInfo, 0)
		cloudZoneMap := make(map[string]regionService.ResourceZoneinfo, 0)
		cloudRegions, _ := DescribeRegionsAndZonesFromCloud()
		if cloudRegions != nil && cloudRegions.Response != nil {
			// 如果请求成功，就走这里
			totalCount := cloudRegions.Response.TotalCount
			if *totalCount > 0 {
				for _, resourceRegionSet := range cloudRegions.Response.ResourceRegionSet {
					cloudRegionMap[*resourceRegionSet.Region] = *resourceRegionSet
					for _, zoneinfo := range resourceRegionSet.ResourceZoneSet {
						cloudZoneMap[*zoneinfo.Zone] = *zoneinfo
					}
				}
			}
		}
		for _, openedCloudVpcRegionZone := range openedCloudVpcRegionZones {
			r := openedCloudVpcRegionZone.Region
			cloudR, exists := cloudRegionMap[r]
			if exists {
				openedCloudVpcRegionZone.RegionDesc = *cloudR.RegionName
			}
			z := openedCloudVpcRegionZone.Zone
			cloudZ, exists := cloudZoneMap[z]
			if exists {
				openedCloudVpcRegionZone.ZoneDesc = *cloudZ.ZoneName
			}
		}
	}

	service3.SortSliceByField(openedCloudVpcRegionZones, "ZoneId", false)
	regionZones[constants.NETWORK_ENV_CLOUD_VPC] = openedCloudVpcRegionZones
	return regionZones, nil
}

func GetRegionShortName(region string) (string, error) {
	sql := "select distinct(shortName) as shortName from Region where region = ?"
	args := make([]interface{}, 0)
	args = append(args, region)
	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return "", nil
	}
	if len(data) == 0 {
		return "", errors.New("query result is empty")
	}
	shortName := string(data[0]["shortName"])
	return shortName, nil
}

func ValidateRegionZones(appId int32, regions []string, networkType int8, zone string, clusterType int) (err error) {
	rzList, err := getRegionZonesByCondition(appId, regions, int(networkType), []int{clusterType})
	if err != nil {
		logging.Errorf("ValidateRegionZones getRegionZonesByCondition %d %+v %d, error: %+v", appId, regions, networkType, err)
		return err
	}

	for _, regionZone := range rzList {
		if regionZone.Zone == zone {
			return err
		}
	}
	b, _ := json.Marshal(rzList)
	logging.Infof("ValidateRegionZones rzList: %s", string(b))
	err = fmt.Errorf("check region zone validate failed, AppId:%d, regions:%+v, networkType:%d, zone:%s",
		appId, regions, networkType, zone)
	return err
}

func getRegionZonesByCondition(appId int32, regions []string, networkType int, clusterTypes []int) (t []*region.RegionZoneDetail, err error) {
	sql := "select c.* from (select b.id, b.zoneId, b.zone, count(distinct(b.zone)) as zoneCount, b.zoneName as zoneDesc," +
		" b.state as zoneState, a.region, a.regionId, a.regionName as regionDesc, a.state as regionState, " +
		"a.supportSharedCluster as regionSupportSharedCluster, " +
		"a.supportInternational as regionSupportInternational from Region as a, " +
		"Zone as b " +
		"where a.region = b.region and a.netEnvType=? and b.netEnvType=? "
	args := make([]interface{}, 0)
	args = append(args, networkType)
	args = append(args, networkType)

	if len(clusterTypes) != 0 {
		sql = sql + " and a.ClusterType in ("
		for i := 0; i < len(clusterTypes); i++ {
			sql = fmt.Sprintf("%s ?", sql)
			if i != len(clusterTypes)-1 {
				sql = sql + ","
			}
			args = append(args, clusterTypes[i])
		}
		sql = sql + ")"

		sql = sql + " and b.ClusterType in ("
		for i := 0; i < len(clusterTypes); i++ {
			sql = fmt.Sprintf("%s ?", sql)
			if i != len(clusterTypes)-1 {
				sql = sql + ","
			}
			args = append(args, clusterTypes[i])
		}
		sql = sql + ")"
	}

	if len(regions) != 0 {
		sql = sql + " and a.region in ("
		for i := 0; i < len(regions); i++ {
			sql = fmt.Sprintf("%s ?", sql)
			if i != len(regions)-1 {
				sql = sql + ","
			}
			args = append(args, regions[i])
		}
		sql = sql + ")"
	}

	sql = fmt.Sprintf("%s group by b.zone) as c order by c.regionId, c.id", sql)
	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logging.Errorf("getRegionZonesByCondition sql: %s, args: %+v, error: %+v", sql, args, err)
		return t, err
	}

	t = make([]*region.RegionZoneDetail, 0, 0)
	for i := 0; i < len(data); i++ {
		regionZone := &region.RegionZoneDetail{}
		err = util.ScanMapIntoStruct(regionZone, data[i])
		if err != nil {
			return t, err
		}
		t = append(t, regionZone)
	}
	return t, nil

}

func GetRegionResourceCount(appId int32, regions []string) (resourceCount map[string]int, err error) {

	resourceCount = make(map[string]int)
	if len(regions) == 0 {
		regions, err = getRegions()
		if err != nil {
			logging.Errorf("GetRegionJobCount getRegions error: %+v", err)
			return
		}
		if len(regions) == 0 {
			return
		}
	}

	cond := dao.NewCondition()
	cond.Eq("AppId", appId)
	cond.In("Region", regions)
	cond.Eq("SystemProvide", constants.USER_PROVIDED)
	cond.Eq("IsResGroup", 0)
	cond.Ne("Status", constants.RESOURCE_STATUS_DELETE)

	where, args := cond.GetWhere()
	sql := "select Region, COUNT(*) as Cnt from Resource" + where + " group by Region"
	_, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return
	}
	for _, d := range data {
		count, _ := strconv.Atoi(string(d["Cnt"]))
		resourceCount[string(d["Region"])] = count
	}
	return resourceCount, nil
}
