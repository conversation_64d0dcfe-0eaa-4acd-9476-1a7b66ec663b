package flowCC

const (
	groupSetats = "ConfigureCenter.Flow.K8S.Setats"
)

type SetatsCC struct {
	cc *CC
}

func newSetatsCC(ccService *CC) *SetatsCC {
	return &SetatsCC{cc: ccService}
}

func (c *SetatsCC) ClusterRole(into interface{}) (interface{}, error) {
	return c.cc.rainbow.DecodeK8sObject(groupSetats, "setats-ClusterRole.yaml", nil, into)
}

func (c *SetatsCC) ClusterRoleBinding(into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupSetats, "setats-ClusterRoleBinding.yaml", nil, into)
}

func (c *SetatsCC) SetatsOperator(params, into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupSetats, "setats-operator-deployment.yaml", params, into)
}

func (c *SetatsCC) Setats(params interface{}) (string, error) {
	return c.cc.getTemplate(groupSetats, "setats-template.yaml", params)
}

func (c *SetatsCC) StorageClass(into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupSetats, "setats-storageClass.yaml", nil, into)
}

func (c *SetatsCC) ManagerReserveCu() (string, error) {
	return c.cc.GetWithGroup(groupSetats, "ManagerReserveCu")
}

func (c *SetatsCC) WorkerReserveCu() (string, error) {
	return c.cc.GetWithGroup(groupSetats, "WorkerReserveCu")
}

func (c *SetatsCC) WorkerReserveCu32() (string, error) {
	return c.cc.GetWithGroup(groupSetats, "WorkerReserveCu32")
}

func (c *SetatsCC) HiveMetastoreUser() (string, error) {
	return c.cc.GetWithGroup(groupSetats, "hiveMetastoreUser")
}

func (c *SetatsCC) HiveMetastorePass() (string, error) {
	return c.cc.GetWithGroup(groupSetats, "hiveMetastorePass")
}

func (c *SetatsCC) HiveMetastore(params, into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupSetats, "hive-metastore-deployment.yaml", params, into)
}

func (c *SetatsCC) HiveSite(params interface{}) (string, error) {
	return c.cc.getTemplate(groupSetats, "hive-site.xml", params)
}
