package flowCC

var groupApps = "ConfigureCenter.Flow.K8S.Apps"

type appsCC struct {
	cc *CC
}

func newAppsCC(cc *CC) *appsCC {
	return &appsCC{cc: cc}
}

// ClusterAdmin into 是 k8s.io/api/apps/v1 StatefulSet, 但这里不引入 k8s 的包
func (c *appsCC) ClusterAdmin(params, into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupApps, "sts-cluster-admin.template.yaml", params, into)
}

func (c *appsCC) ClusterScheduler(params, into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupApps, "sts-cluster-scheduler.template.yaml", params, into)
}

func (c *appsCC) UserAgent(params, into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupApps, "sts-user-agent.template.yaml", params, into)
}

func (c *appsCC) ClusterMaster(params, into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupApps, "sts-cluster-master.template.yaml", params, into)
}

func (c *appsCC) TaskCenter(params, into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupApps, "sts-taskcenter.template.yaml", params, into)
}

func (c *appsCC) Watchdog(params, into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupApps, "sts-watchdog.template.yaml", params, into)
}

func (c *appsCC) NginxReverse(params, into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupApps, "nginx-reverse-proxy.yaml", params, into)
}

func (c *appsCC) CommandController(params, into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupApps, "sts-command-controller.template.yaml", params, into)
}

func (c *appsCC) CommandControllerCrd(_, into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupApps, "crd-commands.oceanus.tencentcloud.com.yaml", nil, into)
}

func (c *appsCC) CredentialsProvider(params, into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupApps, "sts-credentials-provider.template.yaml", params, into)
}

func (c *appsCC) MetricProvider(params, into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupApps, "sts-metric-provider.template.yaml", params, into)
}

func (c *appsCC) HorizontalPodAutoscaler(params, into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupApps, "hpa.template.yaml", params, into)
}

func (c *appsCC) DiagnosisDataUploader(params, into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupApps, "deploy-diagnosis-data-uploader.yaml", params, into)
}

func (c *appsCC) SqlServer(params, into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupApps, "sts-sql-server.template.yaml", params, into)
}

func (c *appsCC) Filebeat(params, into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupApps, "ds-filebeat.template.yaml", params, into)
}
