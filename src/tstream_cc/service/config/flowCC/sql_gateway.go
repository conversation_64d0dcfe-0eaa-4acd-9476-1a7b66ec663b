package flowCC

const (
	SqlGatewayGroup = "ConfigureCenter.Flow.K8S.SqlGateway"
)

type SqlGatewayCC struct {
	cc *CC
}

func newSqlGatewayCC(ccService *CC) *SqlGatewayCC {
	return &SqlGatewayCC{cc: ccService}
}

func (c *SqlGatewayCC) SqlGatewayFlinkConf(params interface{}) (string, error) {
	return c.cc.getTemplate(SqlGatewayGroup, "sql-gateway-flink-conf.yaml", params)
}

func (c *SqlGatewayCC) FlinkSqlGatewayDeployment(params interface{}, info interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(SqlGatewayGroup, "flink-sql-gateway-deployment.yaml", params, info)
}