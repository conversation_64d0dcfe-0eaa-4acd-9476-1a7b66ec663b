package flowCC

import (
	"fmt"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
)

const (
	SqlGatewayGroup = "ConfigureCenter.Flow.K8S.SqlGateway"
)

type SqlGatewayCC struct {
	cc *CC
}

func newSqlGatewayCC(ccService *CC) *SqlGatewayCC {
	return &SqlGatewayCC{cc: ccService}
}

func (c *SqlGatewayCC) SqlGatewayFlinkConf(params interface{}) (string, error) {
	return c.cc.getTemplate(SqlGatewayGroup, "sql-gateway-flink-conf.yaml", params)
}

func (c *SqlGatewayCC) FlinkSqlGatewayDeployment(params interface{}, info interface{}, flinkVersion string) (interface{}, error) {
	key := "flink-sql-gateway-deployment.yaml"
	if constants.Flinkversion118 == flinkVersion {
		key = fmt.Sprintf("flink-sql-gateway-deployment-%s.yaml", flinkVersion)
	}
	return c.cc.decodeK8SObject(SqlGatewayGroup, key, params, info)
}
