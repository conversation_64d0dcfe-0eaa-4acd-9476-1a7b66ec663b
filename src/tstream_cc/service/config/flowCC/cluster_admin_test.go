package flowCC

import (
	"testing"
)

func TestClusterAdminCC_AdminConf(t *testing.T) {
	t.Log(ca.AdminConf(map[string]string{
		"PublicServiceNginxUrl": "http://test/",
	}))
}

func TestClusterAdminCC_FlinkConf(t *testing.T) {
	t.Log(ca.FlinkConf(nil))
}

func TestClusterAdminCC_FlinkLog4jV2(t *testing.T) {
	t.Log(ca.FlinkLog4jV2())
}

func TestClusterAdminCC_Log4jV1(t *testing.T) {
	t.Log(ca.Log4jV1())
}

func TestClusterAdminCC_SqlTemplate(t *testing.T) {
	t.Log(ca.SqlTemplate(nil))
}
