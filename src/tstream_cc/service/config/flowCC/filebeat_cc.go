package flowCC

var (
	groupFilebeat = "ConfigureCenter.Flow.K8S.Filebeat"
)

type FilebeatCC struct {
	cc *CC
}

func newFilebeatCC(ccService *CC) *FilebeatCC {
	return &FilebeatCC{cc: ccService}
}

func (c *FilebeatCC) FilebeatYaml(params interface{}) (string, error) {
	return c.cc.getTemplate(groupFilebeat, "filebeat.yml", params)
}

func (c *FilebeatCC) ClusterRole(into interface{}) (interface{}, error) {
	return c.cc.rainbow.DecodeK8sObject(groupFilebeat, "cs-filebeat.yaml", nil, into)
}

func (c *FilebeatCC) ServiceAccount(into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupFilebeat, "sa-filebeat.yaml", nil, into)
}

func (c *FilebeatCC) ClusterRoleBinding(into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupFilebeat, "crb-filebeat.yaml", nil, into)
}

func (c *FilebeatCC) FilebeatForSetatsYaml(params interface{}) (string, error) {
	return c.cc.getTemplate(groupFilebeat, "filebeat-setats.yml", params)
}

func (c *FilebeatCC) ClusterRoleForSetats(into interface{}) (interface{}, error) {
	return c.cc.rainbow.DecodeK8sObject(groupFilebeat, "cs-filebeat-setats.yaml", nil, into)
}

func (c *FilebeatCC) ServiceAccountForSetats(into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupFilebeat, "sa-filebeat-setats.yaml", nil, into)
}

func (c *FilebeatCC) ClusterRoleBindingForSetats(into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupFilebeat, "crb-filebeat-setats.yaml", nil, into)
}