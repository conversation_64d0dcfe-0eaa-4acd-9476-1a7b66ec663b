package flowCC

const (
	groupCA = "ConfigureCenter.Flow.K8S.ClusterAdmin"
)

type ClusterAdminCC struct {
	cc *CC
}

func newClusterAdminCC(ccService *CC) *ClusterAdminCC {
	return &ClusterAdminCC{cc: ccService}
}

func (c *ClusterAdminCC) SqlTemplate(params interface{}) (string, error) {
	return c.cc.getTemplate(groupCA, "template-new.sql", params)
}

func (c *ClusterAdminCC) AdminConf(params interface{}) (string, error) {
	return c.cc.getTemplate(groupCA, "admin-conf.yaml", params)
}

func (c *ClusterAdminCC) FlinkConf(params interface{}) (string, error) {
	return c.cc.getTemplate(groupCA, "flink-conf-cos.yaml", params)
}

func (c *ClusterAdminCC) Flink111Log4jV2() (string, error) {
	return c.cc.rainbow.Get(groupCA, "log4j-flink-1.11.properties")
}

func (c *ClusterAdminCC) FlinkLog4jV2() (string, error) {
	return c.cc.rainbow.Get(groupCA, "log4j.properties")
}

func (c *ClusterAdminCC) Log4jV1() (string, error) {
	return c.cc.rainbow.Get(groupCA, "log4j-1.properties")
}
