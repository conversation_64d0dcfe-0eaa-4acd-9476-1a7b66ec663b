package flowCC

import (
	"encoding/json"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config"
)

var (
	groupFlow = "ConfigureCenter.Flow.Common"
)

// CC 创建集群 需要的相关配置, 收拢接口，对流程屏蔽 配置如果获取
type CC struct {
	region  string
	rainbow *config.Service

	tke   *TkeCC
	ca    *ClusterAdminCC
	csc   *ClusterSessionCC
	cm    *ClusterMasterCC
	cs    *ClusterSchedulerCC
	wd    *WatchDogCC
	tc    *TaskCenterCC
	nginx *NginxCC
	ua    *UserAgentCC
	hr    *HadoopYarnCC

	metricProviderCC *MetricProvider

	sqlServer *SqlServerCC

	filebeat *FilebeatCC

	apps  *appsCC
	sg    *SqlGatewayCC
	ne    *NodeExpansionCC
	setas *SetatsCC
}

func New(region string) *CC {
	return &CC{
		region:  region,
		rainbow: config.GetRainbowService(),
	}
}

func (c *CC) Get(key string) (value string, err error) {
	return c.rainbow.Get(groupFlow, key, c.region)
}

func (c *CC) decodeK8SObject(group, key string, params, into interface{}) (interface{}, error) {
	return c.rainbow.DecodeK8sObject(group, key, params, into, c.region)
}

func (c *CC) getTemplate(group, key string, params interface{}) (string, error) {
	return c.rainbow.GetTemplate(group, key, params, c.region)
}

func (c *CC) GetWithGroup(group, key string) (string, error) {
	return c.rainbow.Get(group, key, c.region)
}

func (c *CC) SshKey() (string, error) {
	return c.rainbow.Get(groupFlow, "cvm.ssh.key", c.region)
}

func (c *CC) PrepaidPeriod() (int64, error) {
	return c.rainbow.GetNumber(groupFlow, "prepaid.period", c.region)
}

func (c *CC) PooledPrepaidPeriod() (int64, error) {
	return c.rainbow.GetNumber(groupFlow, "pooled.prepaid.period", c.region)
}

func (c *CC) PooledCvmType() (cvmList []string, err error) {
	cvmList = make([]string, 0)
	v, err := c.rainbow.Get(groupFlow, "pooled.cvm", c.region)
	if err != nil {
		return
	}

	err = json.Unmarshal([]byte(v), &cvmList)
	return
}

/*
*
credentials-provider 在eks 下面 获取 集群密钥需要通过设置anno，
[eks.tke.cloud.tencent.com/role-name]=$RoleName，
就可以在pod中通过 curl http://metadata.tencentyun.com/meta-data/cam/security-credentials/$RoleName来获取普通角色的临时秘钥，
这个RoleName的 角色载体 需要是  cvm
*/
func (c *CC) EksCredentialsProviderRoleName() (value string, err error) {
	return c.rainbow.Get(groupFlow, "eksRoleName", c.region)
}

func (c *CC) VPCCidr() (value string, err error) {
	return c.rainbow.Get(groupFlow, "vpc.cidr", c.region)
}

func (c *CC) SubnetCidr() (value string, err error) {
	return c.rainbow.Get(groupFlow, "subnet.cidr", c.region)
}
func (c *CC) SubnetCidrTimes() (value int64, err error) {
	return c.rainbow.GetNumber(groupFlow, "subnet.cidr.times", c.region)
}

func (c *CC) LargeClusterCU() (value int64, err error) {
	return c.rainbow.GetNumber(groupFlow, "large.cluster.cu", c.region)
}

func (c *CC) UcCdbMemoryMB() (value int64, err error) {
	return c.rainbow.GetNumber(groupFlow, "cdb.memory.mb.uniform", c.region)
}

func (c *CC) UcCdbVolumeGB() (value int64, err error) {
	return c.rainbow.GetNumber(groupFlow, "cdb.volume.gb.uniform", c.region)
}

func (c *CC) CdbMemoryMB(cuNum int64) (value int64, err error) {
	largeClusterCu, err := c.LargeClusterCU()
	if err != nil {
		return largeClusterCu, err
	}
	if cuNum >= largeClusterCu {
		return c.rainbow.GetNumber(groupFlow, "large.cdb.memory.mb", c.region)
	}
	return c.rainbow.GetNumber(groupFlow, "cdb.memory.mb", c.region)
}

func (c *CC) CdbVolumeGB(cuNum int64) (value int64, err error) {
	largeClusterCu, err := c.LargeClusterCU()
	if err != nil {
		return largeClusterCu, err
	}
	if cuNum >= largeClusterCu {
		return c.rainbow.GetNumber(groupFlow, "large.cdb.volume.gb", c.region)
	}
	return c.rainbow.GetNumber(groupFlow, "cdb.volume.gb", c.region)
}

func (c *CC) StartupLogListenerSecretId() (string, error) {
	return c.rainbow.Get(groupFlow, "StartupLogListenerSecretId", c.region)
}

func (c *CC) StartupLogListenerSecretKey() (string, error) {
	return c.rainbow.Get(groupFlow, "StartupLogListenerSecretKey", c.region)
}

func (c *CC) CoreSiteXml(params interface{}) (string, error) {
	return c.rainbow.GetTemplate("ConfigureCenter.Flow.K8S.Common", "core-site-cos.xml", params, c.region)
}

func (c *CC) LogListerner(params interface{}) (string, error) {
	return c.rainbow.GetTemplate("ConfigureCenter.Flow.K8S.Common", "loglistener.conf", params, c.region)
}

func (c *CC) ApplicationConf(params interface{}) (string, error) {
	return c.rainbow.GetTemplate("ConfigureCenter.Flow.K8S.Common", "application.conf", params, c.region)
}

func (c *CC) NotifyProperties(params interface{}) (string, error) {
	return c.rainbow.GetTemplate("ConfigureCenter.Flow.K8S.Common", "notify.properties", params, c.region)
}

func (c *CC) CommonLog4j() (string, error) {
	return c.rainbow.GetTemplate("ConfigureCenter.Flow.K8S.Common", "log4j.properties", nil) // 这个是通用配置, 与 ClusterAdmin 的不兼容
}

func (c *CC) TkeCC() *TkeCC {
	if c.tke == nil {
		c.tke = newTkeCC(c)
	}
	return c.tke
}

func (c *CC) ClusterAdminCC() *ClusterAdminCC {
	if c.ca != nil {
		return c.ca
	}
	c.ca = newClusterAdminCC(c)
	return c.ca
}

func (c *CC) UserAgentCC() *UserAgentCC {
	if c.ua != nil {
		return c.ua
	}
	c.ua = newUserAgentCC(c)
	return c.ua
}

func (c *CC) ClusterSessionCC() *ClusterSessionCC {
	if c.csc != nil {
		return c.csc
	}
	c.csc = newClusterSessionCC(c)
	return c.csc
}

func (c *CC) ClusterMasterCC() *ClusterMasterCC {
	if c.cm != nil {
		return c.cm
	}
	c.cm = newClusterMasterCC(c)
	return c.cm
}

func (c *CC) ClusterSchedulerCC() *ClusterSchedulerCC {
	if c.cs != nil {
		return c.cs
	}
	c.cs = newClusterSchedulerCC(c)
	return c.cs
}

func (c *CC) NodeExpansionControllerCC() *NodeExpansionCC {
	if c.ne != nil {
		return c.ne
	}
	c.ne = newNodeExpansionCC(c)
	return c.ne
}

func (c *CC) SetatsCC() *SetatsCC {
	if c.setas != nil {
		return c.setas
	}
	c.setas = newSetatsCC(c)
	return c.setas
}

func (c *CC) WatchDogCC() *WatchDogCC {
	if c.wd != nil {
		return c.wd
	}
	c.wd = newWatchDogCC(c)
	return c.wd
}

func (c *CC) HadoopYarnCC() *HadoopYarnCC {
	if c.hr != nil {
		return c.hr
	}
	c.hr = newHadoopYarnCC(c)
	return c.hr
}

func (c *CC) TaskCenterCC() *TaskCenterCC {
	if c.tc != nil {
		return c.tc
	}
	c.tc = newTaskCenterCC(c)
	return c.tc
}

func (c *CC) NginxCC() *NginxCC {
	if c.nginx == nil {
		c.nginx = newNgnixCC(c)
	}

	return c.nginx
}

func (c *CC) AppsCC() *appsCC {
	if c.apps == nil {
		c.apps = newAppsCC(c)
	}
	return c.apps
}

func (c *CC) MetricProviderCC() *MetricProvider {
	if c.metricProviderCC == nil {
		c.metricProviderCC = newMetricProvider(c)
	}
	return c.metricProviderCC
}

func (c *CC) SqlServerCC() *SqlServerCC {
	if c.sqlServer == nil {
		c.sqlServer = newSqlServerCC(c)
	}
	return c.sqlServer
}

func (c *CC) SqlGatewayCC() *SqlGatewayCC {
	if c.sg != nil {
		return c.sg
	}
	c.sg = newSqlGatewayCC(c)
	return c.sg
}

func (c *CC) FilebeatCC() *FilebeatCC {
	if c.filebeat != nil {
		return c.filebeat
	}
	c.filebeat = newFilebeatCC(c)
	return c.filebeat
}
