package flowCC

const (
	groupCS = "ConfigureCenter.Flow.K8S.ClusterScheduler"
)

type ClusterSchedulerCC struct {
	cc *CC
}

func newClusterSchedulerCC(ccService *CC) *ClusterSchedulerCC {
	return &ClusterSchedulerCC{cc: ccService}
}

func (c *ClusterSchedulerCC) Conf(params interface{}) (string, error) {
	return c.cc.getTemplate(groupCS, "conf.yaml", params)
}

func (c *ClusterSchedulerCC) MergeConf(params interface{}) (string, error) {
	return c.cc.getTemplate(groupCS, "merge-conf.yaml", params)
}

func (c *ClusterSchedulerCC) FlinkConf(params interface{}) (string, error) {
	return c.cc.getTemplate(groupCS, "flink-conf-cos.yaml", params)
}

func (c *ClusterSchedulerCC) Flink111Log4jV2() (string, error) {
	return c.cc.rainbow.Get(groupCS, "log4j-flink-1.11.properties")
}

func (c *ClusterSchedulerCC) FlinkLog4jV2() (string, error) {
	return c.cc.rainbow.Get(groupCS, "log4j.properties")
}

func (c *ClusterSchedulerCC) Log4jV1() (string, error) {
	return c.cc.rainbow.Get(groupCS, "log4j-1.properties")
}

func (c *ClusterSchedulerCC) ConfProperties(params interface{}) (string, error) {
	return c.cc.getTemplate(groupCS, "config.properties", params)
}

func (c *ClusterSchedulerCC) MybatisConf() (string, error) {
	return c.cc.getTemplate(groupCS, "mybatis-config.xml", nil)
}

func (c *ClusterSchedulerCC) MergeMybatisConf(params interface{}) (string, error) {
	return c.cc.getTemplate(groupCS, "merge-mybatis-config.xml", params)
}

func (c *ClusterSchedulerCC) FlinkRunJobBPMN() (string, error) {
	return c.cc.getTemplate(groupCS, "flink_run_job.bpmn", nil)
}

func (c *ClusterSchedulerCC) FlinkRestartJobBPMN() (string, error) {
	return c.cc.getTemplate(groupCS, "flink_restart_job.bpmn", nil)
}

func (c *ClusterSchedulerCC) FlinkStopJobBPMN() (string, error) {
	return c.cc.getTemplate(groupCS, "flink_stop_job.bpmn", nil)
}

func (c *ClusterSchedulerCC) ZkSaslJaas() (string, error) {
	return c.cc.getTemplate(groupCS, "zk_sasl_jaas.conf", nil)
}
