package flowCC

import (
	"encoding/json"
	"testing"

	appsV1 "k8s.io/api/apps/v1"
	coreV1 "k8s.io/api/core/v1"
	rbacV1 "k8s.io/api/rbac/v1"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
)

func TestAccountFlinkCC_ClusterRoleBindingToDefault(t *testing.T) {
	o := &rbacV1.ClusterRoleBinding{}
	t.Log(tke.AccountFlinkCC().ClusterRoleBindingToDefault(o))
}

func TestAccountFlinkCC_ClusterRoleBindingToFlink(t *testing.T) {
	o := &rbacV1.ClusterRoleBinding{}
	t.Log(tke.AccountFlinkCC().ClusterRoleBindingToFlink(o))
}

func TestAccountFlinkCC_ServiceAccount(t *testing.T) {
	o := &coreV1.ServiceAccount{}
	t.Log(tke.AccountFlinkCC().ServiceAccount(o))
}

func TestClsProvisionerCC_ClusterRole(t *testing.T) {
	o := &rbacV1.ClusterRole{}
	t.Log(tke.ClsProvisionerCC().ClusterRole(o))
}

func TestClsProvisionerCC_ClusterRoleBinding(t *testing.T) {
	o := &rbacV1.ClusterRoleBinding{}
	t.Log(tke.ClsProvisionerCC().ClusterRoleBinding(o))
}

func TestClsProvisionerCC_Deployment(t *testing.T) {
	o := &appsV1.Deployment{}
	t.Log(tke.ClsProvisionerCC().Deployment(nil, o))
}

func TestClsProvisionerCC_ServiceAccount(t *testing.T) {
	o := &coreV1.ServiceAccount{}
	t.Log(tke.ClsProvisionerCC().ServiceAccount(o))
}

func TestLogAgentCC_ClusterRole(t *testing.T) {
	o := &rbacV1.ClusterRole{}
	t.Log(tke.LogAgentCC().ClusterRole(o))
}

func TestLogAgentCC_ClusterRoleBinding(t *testing.T) {
	o := &rbacV1.ClusterRoleBinding{}
	t.Log(tke.LogAgentCC().ClusterRoleBinding(o))
}

func TestLogAgentCC_DaemonSet(t *testing.T) {
	o := &appsV1.DaemonSet{}
	t.Log(tke.LogAgentCC().DaemonSet(nil, o))
}

func TestLogAgentCC_ServiceAccount(t *testing.T) {
	o := &coreV1.ServiceAccount{}
	t.Log(tke.LogAgentCC().ServiceAccount(o))
}

func TestOceanusCC_ClusterRoleBinding(t *testing.T) {
	o := &rbacV1.ClusterRoleBinding{}
	t.Log(tke.OceanusCC().ClusterRoleBinding(nil, o))
}

func TestOceanusCC_Namespace(t *testing.T) {
	o := &coreV1.Namespace{}
	t.Log(tke.OceanusCC().Namespace(nil, o))
}

func TestOceanusCC_ServiceAccount(t *testing.T) {
	o := &coreV1.ServiceAccount{}
	t.Log(tke.OceanusCC().ServiceAccount(nil, o))
}

func TestTillerCC_ClusterRoleBinding(t *testing.T) {
	o := &rbacV1.ClusterRoleBinding{}
	t.Log(tke.TillerCC().ClusterRoleBinding(o))
}

func TestTillerCC_DeploymentExtV1(t *testing.T) {
	o := &appsV1.Deployment{}
	t.Log(tke.TillerCC().Deployment(nil, o))
}

func TestTillerCC_ServiceAccount(t *testing.T) {
	o := &coreV1.ServiceAccount{}
	t.Log(tke.TillerCC().ServiceAccount(o))
}

func TestTkeCC_ClusterConfig(t *testing.T) {
	var clusterGroup table.ClusterGroup
	// 测试用例中设置必要的字段
	clusterGroup.Zone = "ap-guangzhou-1"

	c, err := tke.ClusterConfig(&clusterGroup)
	if err != nil {
		t.Fatal(err)
	}
	b, _ := json.MarshalIndent(c, "", "")
	t.Log(string(b))

	// 修改区域进行第二次测试
	clusterGroup.Zone = "ap-guangzhou-3"
	c2, err := tke.ClusterConfig(&clusterGroup)
	if err != nil {
		t.Fatal(err)
	}
	b, _ = json.MarshalIndent(c2, "", "")
	t.Log(string(b))
}

func TestTkeCC_IngressChartPath(t *testing.T) {
	t.Log(tke.IngressChartPath())
}

func TestTkeCC_ZookeeperYaml(t *testing.T) {
	var params interface{}
	t.Log(tke.ZookeeperYaml(params))
}
