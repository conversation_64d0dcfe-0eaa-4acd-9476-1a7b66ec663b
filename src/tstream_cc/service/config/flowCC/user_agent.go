package flowCC

const (
	groupUA = "ConfigureCenter.Flow.K8S.UserAgent"
)

type UserAgentCC struct {
	cc *CC
}

func newUserAgentCC(ccService *CC) *UserAgentCC {
	return &UserAgentCC{cc: ccService}
}

func (c *UserAgentCC) Conf(params interface{}) (string, error) {
	return c.cc.getTemplate(groupUA, "conf.yaml", params)
}

func (c *UserAgentCC) MergeConf(params interface{}) (string, error) {
	return c.cc.getTemplate(groupUA, "merge-conf.yaml", params)
}

func (c *UserAgentCC) Flink111Log4jV2() (string, error) {
	return c.cc.rainbow.Get(groupUA, "log4j-flink-1.11.properties")
}

func (c *UserAgentCC) FlinkLog4jV2() (string, error) {
	return c.cc.rainbow.Get(groupUA, "log4j.properties")
}

func (c *UserAgentCC) Log4jV1() (string, error) {
	return c.cc.rainbow.Get(groupUA, "log4j-1.properties")
}
