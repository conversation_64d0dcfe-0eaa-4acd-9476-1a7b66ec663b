package flowCC

import (
	"encoding/base64"
	"fmt"
	constants2 "tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
)

const (
	groupTke = "ConfigureCenter.Flow.K8S.TKEComponent"
)

type TkeCC struct {
	cc *CC
}

func newTkeCC(cc *CC) *TkeCC {
	return &TkeCC{cc: cc}
}

type InternalTkeClusterConfig struct {
	NetworkType        string
	ServiceCidr        string
	TagKeyProduct      string
	TagValueProduct    string
	TagKeyDepartment   string
	TagValueDepartment string
	TagBusiness1Key    string
	TagBusiness1Value  string
	TagBusiness2Key    string
	TagBusiness2Value  string
	TagPrincipalKey    string
	TagResourceType    string
	DiskPartition      string
	DeptId             int
	ProductId          int
	OpDeptId           int
	Memo               string
	BakOperator        string
	Business1Id        int
	Business2Id        int
	Business3Id        int
	Operator           string
	ChargeType         string
	StaticType         string
	ImageId            string
	ImageName          string
	RenewFlag          string
	SecurityGroup      string
}

// TkeClusterConfig 购买tke集群的相关配置参数
type TkeClusterConfig struct {
	Cidr           string
	MaxNodePodNum  uint64
	MaxServiceNum  uint64
	ControllerSpec string
	OS             string
	Version        string
	UserScript     string
	SecurityGroup  string

	WorkerSpec string

	ReservedCuPerNode   int64
	ReservedCuPerNodeV3 int64

	EksCpuType string

	EksVersion string
}

func (c *TkeCC) InternalClusterConfig() (*InternalTkeClusterConfig, error) {
	config := &InternalTkeClusterConfig{}
	_, err := c.cc.rainbow.GetAny(groupFlow, "tke.internal.cluster.conf", config, c.cc.region)
	if err != nil {
		return nil, err
	}
	return config, nil
}

func (c *TkeCC) ClusterConfig(cg *table.ClusterGroup) (*TkeClusterConfig, error) {
	config := &TkeClusterConfig{}
	_, err := c.cc.rainbow.GetAny(groupFlow, "tke.cluster.conf", config, c.cc.region, cg.Zone)
	if err != nil {
		return nil, err
	}

	script, err := c.cc.rainbow.Get(groupTke, "init-user.script.sh", c.cc.region)

	if err != nil {
		return nil, err
	}

	scriptClean, err := c.cc.rainbow.Get(groupTke, "init-user.script-clean.sh", c.cc.region)
	script = fmt.Sprintf("%s\n%s", script, scriptClean)
	if err != nil {
		return nil, err
	}

	//判断是否是内网环境，内网机器安装铁将军从七彩石获取铁将军安装脚本
	if cg.NetEnvironmentType == constants2.NETWORK_ENV_INNER_VPC {
		scriptInitAnIron, err := c.cc.rainbow.Get(groupTke, "init-AnIronGeneral.sh", c.cc.region)
		script = fmt.Sprintf("%s\n%s", script, scriptInitAnIron)
		if err != nil {
			return nil, err
		}
	}

	config.UserScript = base64.StdEncoding.EncodeToString([]byte(script))
	return config, nil
}

func (c *TkeCC) ENIScript() (string, error) {
	return c.cc.rainbow.Get(groupTke, "init-user.script-node.eni.sh", c.cc.region)
}

func (c *TkeCC) ENIAutoScript() (string, error) {
	return c.cc.rainbow.Get(groupTke, "init-user.script-node.eni.auto.sh", c.cc.region)
}

func (c *TkeCC) IngressChartPath() (string, error) {
	return c.cc.rainbow.Get(groupTke, "nginx.ingress.chart.path", c.cc.region)
}

func (c *TkeCC) IngressChartPathV1() (string, error) {
	return c.cc.rainbow.Get(groupTke, "nginx.ingress.chart.path.v1", c.cc.region)
}

func (c *TkeCC) HadoopChartPath() (string, error) {
	return c.cc.rainbow.Get(groupTke, "hadoop.yarn.chart.path", c.cc.region)
}

func (c *TkeCC) KubeConfigYaml() (string, error) {
	return c.cc.rainbow.Get(groupTke, constants2.CONF_RAINBOW_KEY_KUBECONFIG, c.cc.region)
}

func (c *TkeCC) ClsChartPath() (string, error) {
	return c.cc.rainbow.Get(groupTke, "cls.chart.path", c.cc.region)
}

func (c *TkeCC) TkeZookeeperChartPath() (string, error) {
	return c.cc.rainbow.Get(groupTke, "tke.zookeeper.chart.path", c.cc.region)
}

func (c *TkeCC) ZookeeperChartPath() (string, error) {
	return c.cc.rainbow.Get(groupTke, "zookeeper.chart.path", c.cc.region)
}

func (c *TkeCC) ZookeeperYaml(params interface{}) (string, error) {
	return c.cc.rainbow.GetTemplate(groupTke, "deploy_zookeeper.yaml", params, c.cc.region)
}

func (c *TkeCC) EksZookeeperYaml(params interface{}) (string, error) {
	return c.cc.rainbow.GetTemplate(groupTke, "eks_deploy_zookeeper.yaml", params, c.cc.region)
}

func (c *TkeCC) TillerCC() *TillerCC {
	return &TillerCC{c}
}

func (c *TkeCC) ClsProvisionerCC() *ClsProvisionerCC {
	return &ClsProvisionerCC{c}
}

func (c *TkeCC) LogAgentCC() *LogAgentCC {
	return &LogAgentCC{c}
}

func (c *TkeCC) LogConfigCC() *LogConfigCC {
	return &LogConfigCC{c}
}

func (c *TkeCC) EksConfigCC() *EksConfigCC {
	return &EksConfigCC{c}
}

func (c *TkeCC) OceanusCC() *OceanusCC {
	return &OceanusCC{c}
}

func (c *TkeCC) AccountFlinkCC() *AccountFlinkCC {
	return &AccountFlinkCC{c}
}

type TillerCC struct {
	*TkeCC
}

func (c *TillerCC) ServiceAccount(into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupTke, "sa-tiller.yaml", nil, into)
}

func (c *TillerCC) ClusterRoleBinding(into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupTke, "clusterrolebindings-tiller-cluster-rule.yaml", nil, into)
}

func (c *TillerCC) Deployment(params, into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupTke, "deploy-tiller-deploy.yaml", params, into)
}

type ClsProvisionerCC struct {
	*TkeCC
}

func (c *ClsProvisionerCC) ServiceAccount(into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupTke, "sa-cls-provisioner.yaml", nil, into)
}

func (c *ClsProvisionerCC) ClusterRole(into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupTke, "clusterroles-cls-provisioner.yaml", nil, into)
}

func (c *ClsProvisionerCC) ClusterRoleBinding(into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupTke, "clusterrolebindings-cls-provisioner.yaml", nil, into)
}

func (c *ClsProvisionerCC) Deployment(params, into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupTke, "deploy-cls-provisioner.template.yaml", params, into)
}

func (c *ClsProvisionerCC) EksDeployment(params, into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupTke, "eks-deploy-cls-provisioner.template.yaml", params, into)
}

type EksConfigCC struct {
	*TkeCC
}

func (c *EksConfigCC) EksConfig(params, into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupTke, "eks-config.yaml", params, into)
}

type LogAgentCC struct {
	*TkeCC
}

func (c *LogAgentCC) ServiceAccount(into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupTke, "sa-tke-log-agent.yaml", nil, into)
}

func (c *LogAgentCC) ClusterRole(into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupTke, "clusterroles-tke-log-agent.yaml", nil, into)
}

func (c *LogAgentCC) ClusterRoleBinding(into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupTke, "clusterrolebindings-tke-log-agent.yaml", nil, into)
}

func (c *LogAgentCC) DaemonSet(params, into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupTke, "ds-tke-log-agent.template.yaml", params, into)
}

type LogConfigCC struct {
	*TkeCC
}

func (c *LogConfigCC) Crd(params, into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupTke, "crd-logconfigs.cls.cloud.tencent.com.yaml", params, into)
}

func (c *LogConfigCC) RunningLog(params, into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupTke, "lc-oceanus-runninglog.template.v2.yaml", params, into)
}

type OceanusCC struct {
	*TkeCC
}

func (c *OceanusCC) Namespace(params, into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupTke, "namespace.yaml", params, into)
}

func (c *OceanusCC) StorageClass(params, into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupTke, "storage-class.yaml", params, into)
}

func (c *OceanusCC) PriorityClassHigh(into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupTke, "priorityclass-high.yaml", nil, into)
}

func (c *OceanusCC) ServiceAccount(params, into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupTke, "service-account.yaml", params, into)
}

func (c *OceanusCC) Role(key string, params, into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupTke, key, params, into)
}

func (c *OceanusCC) RoleBinding(params, into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupTke, "role-binding.yaml", params, into)
}

func (c *OceanusCC) ClusterRole(key string, params, into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupTke, key, params, into)
}

func (c *OceanusCC) ClusterRoleBinding(params, into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupTke, "cluster-role-binding.yaml", params, into)
}

type AccountFlinkCC struct {
	*TkeCC
}

func (c *AccountFlinkCC) ServiceAccount(into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupTke, "sa-flink.yaml", nil, into)
}

func (c *AccountFlinkCC) ClusterRoleBindingToDefault(into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupTke, "clusterrolebindings-flink-role-binding-default.yaml", nil, into)
}

func (c *AccountFlinkCC) ClusterRoleBindingToFlink(into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupTke, "clusterrolebindings-flink-role-binding-flink.yaml", nil, into)
}

func (c *TkeCC) IngressCC() *IngressCC {
	return &IngressCC{c}
}

type IngressCC struct {
	*TkeCC
}

func (i *IngressCC) Secrets(params, into interface{}) (interface{}, error) {
	return i.cc.decodeK8SObject(groupTke, "secrets-basic-auth.template.yaml", params, into)
}

func (i *IngressCC) Ingress(params, into interface{}) (interface{}, error) {
	return i.cc.decodeK8SObject(groupTke, "ing-oceanus-flink-webui.yaml", params, into)
}

func (c *TkeCC) ResourceQuotaCC() *ResourceQuotaCC {
	return &ResourceQuotaCC{c}
}

type ResourceQuotaCC struct {
	*TkeCC
}

func (c *ResourceQuotaCC) ResourceQuota(params, into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupTke, "resourceQuota.yaml", params, into)
}

func (c *TkeCC) NetworkPolicyCC() *NetworkPolicyCC {
	return &NetworkPolicyCC{c}
}

type NetworkPolicyCC struct {
	*TkeCC
}

func (c *NetworkPolicyCC) NetworkPolicyNginx(params, into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupTke, "networkpolicy-uniform-nginx-ingress.yaml", params, into)
}

func (c *NetworkPolicyCC) NetworkPolicy(params, into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupTke, "networkpolicy-uniform-cluster.yaml", params, into)
}

func (c *NetworkPolicyCC) OceanusNetworkPolicy(params, into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupTke, "networkpolicy-uniform-cluster-oceanus.yaml", params, into)
}

func (c *OceanusCC) FlinkNamespaceNetworkPolicy(params, into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupTke, "networkpolicy-oceanus.yaml", params, into)
}

func (c *OceanusCC) SqlServerNetworkPolicy(params, into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupTke, "networkpolicy-sqlserver.yaml", params, into)
}

func (c *TkeCC) CorednsCC() *CorednsCC {
	return &CorednsCC{c}
}

type CorednsCC struct {
	*TkeCC
}

func (c *CorednsCC) ClusterRoleBinding(params, into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupTke, "clusterRoleBinding-coredns.yaml", params, into)
}

func (c *CorednsCC) Configmap(params, into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupTke, "configmap-coredns.yaml", params, into)
}

func (c *CorednsCC) Service(params, into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupTke, "service-coredns.yaml", params, into)
}

func (c *CorednsCC) ServiceAccount(params, into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupTke, "serviceAccount-coredns.yaml", params, into)
}

func (c *CorednsCC) Deployment(params, into interface{}) (interface{}, error) {
	return c.cc.decodeK8SObject(groupTke, "deployment-coredns.yaml", params, into)
}
