package config

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	v1 "k8s.io/api/core/v1"
	"net/http"
	"testing"
)

func TestMain(m *testing.M) {
	url := "http://api.rainbow.oa.com:8080"
	appid := "d0095a95-e218-4ada-b298-c91a4314b353"
	evnName := "Default"
	InitRainbowService(&url, &appid, &evnName)
	m.Run()
}

func TestService_GetTableAny(t *testing.T) {
	type DocDetail struct {
		DocId        string
		Link         string
		Title        string
		Introduction string
		Icon         string
		Weight       string
		AddTime      string
		ModifyTime   string
		IsDelete     string
	}

	result, err := GetRainbowService().GetTableAny(&DocDetail{}, "oceanus_console.table.zh")
	if err != nil {
		t.Fatal(err)
	}
	t.Log(len(result))
	t.Log(result)
	for _, v := range result {
		t.Logf("%#v\n", v.(*DocDetail))
	}
}

func TestService_Get(t *testing.T) {

	v, err := GetRainbowService().Get("test", "test")
	if err != nil {
		t.Fatal(err)
	}
	t.Log(v)
	v, err = GetRainbowService().Get("test", "test", "ap-guangzhou", "ap-guangzhou-2")
	if err != nil {
		t.Fatal(err)
	}
	t.Log(v)
	v, err = GetRainbowService().Get("test", "test", "ap-guangzhou", "ap-guangzhou-1")
	if err != nil {
		t.Fatal(err)
	}
	t.Log(v)
	v, err = GetRainbowService().Get("test", "test", "not exist")
	if err != nil {
		t.Fatal(err)
	}
	t.Log(v)
}

func TestService_GetNumber(t *testing.T) {

	v, err := GetRainbowService().GetNumber("test", "test")
	if err != nil {
		t.Fatal(err)
	}
	t.Log(v)
	v, err = GetRainbowService().GetNumber("test", "test", "ap-guangzhou")
	if err != nil {
		t.Fatal(err)
	}
	t.Log(v)
}

func TestService_GetAny(t *testing.T) {

	type Tmp struct {
		A string
		B string
		C int
	}
	tmp := &Tmp{}
	_, err := GetRainbowService().GetAny("test", "json", tmp)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(tmp)
	tmp = &Tmp{}
	_, err = GetRainbowService().GetAny("test", "json", tmp, "ap-hongkong")
	if err != nil {
		t.Fatal(err)
	}
	t.Log(tmp)

	tmp = &Tmp{}
	_, err = GetRainbowService().GetAny("test", "json", tmp, "ap-guangzhou", "ap-guangzhou-1")
	if err != nil {
		t.Fatal(err)
	}
	t.Log(tmp)

	tmp = &Tmp{}
	_, err = GetRainbowService().GetAny("test", "json", tmp, "not exist")
	if err != nil {
		t.Fatal(err)
	}
	t.Log(tmp)
}

func TestService_GetTemplate(t *testing.T) {

	type Tmp struct {
		A string
		B string
		C int
	}
	tmp := &Tmp{
		A: "aaa",
		B: "bb",
		C: 1,
	}
	result, err := GetRainbowService().GetTemplate("test", "template", &tmp)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(result)
}

func TestService_DecodeK8sObject(t *testing.T) {
	sa1 := &v1.ServiceAccount{}
	result, err := GetRainbowService().DecodeK8sObject("test", "sa1", nil, sa1)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(sa1)
	t.Log(result)
	params := struct {
		Test string
	}{
		Test: "test",
	}
	sa := &v1.ServiceAccount{}
	result, err = GetRainbowService().DecodeK8sObject("test", "sa", params, sa)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(sa)
	t.Log(result)

}

func TestService_DecodeK8sObject2(t *testing.T) {
	url := "http://api.rainbow.oa.com:8080"
	appid := "d0095a95-e218-4ada-b298-c91a4314b353"
	evnName := "Default"
	client := &http.Client{}
	data := &demo{}
	data.App_id = appid
	data.Group_name = "Default.ApiAuth"
	data.Is_sync = false
	data.Env_name = evnName
	kv := make([]*key_values, 0)
	values := &key_values{Key: "ABtest", Value: "b", Value_type: 2, Config_op_type: 4}
	kv = append(kv, values)
	data.Key_values = kv
	bytesData, err := json.Marshal(&data)
	fmt.Println(err)
	fmt.Println(string(bytesData))
	fmt.Println(len(bytesData))
	req, _ := http.NewRequest("POST", url, bytes.NewReader(bytesData))
	resp, _ := client.Do(req)
	defer resp.Body.Close()
	body, _ := ioutil.ReadAll(resp.Body)
	fmt.Println(string(body))
}

type demo struct {
	App_id     string        `json:"app_id"`
	Group_name string        `json:"group_name"`
	Is_sync    bool          `json:"is_sync"`
	Env_name   string        `json:"env_name"`
	Key_values []*key_values `json:"key_values"`
}

type key_values struct {
	Key            string `json:"key_values"`
	Value          string `json:"value"`
	Value_type     int    `json:"value_type"`
	Config_op_type int    `json:"config_op_type"`
}
