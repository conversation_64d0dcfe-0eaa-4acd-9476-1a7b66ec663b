package image_registry

import (
	"fmt"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config"
)

var (
	groupImageRegistry = "ConfigureCenter.ImageRegistry"
	flinkImageRegistry = "ConfigureCenter.Flink"
)

// ImageRegistry 所有镜像，tke仓库的信息入口
// 镜像灰度的话，可能通过两种方式
// 1. 可以通过七彩石的下发 控制下发ip
// 2. key.region 单独配置某个地域的 镜像
type ImageRegistry struct {
	region  string
	rainbow *config.Service
}

func New(region string) *ImageRegistry {
	return &ImageRegistry{
		region:  region,
		rainbow: config.GetRainbowService(),
	}
}

func (r *ImageRegistry) TkeRegistry() (string, error) {
	return r.rainbow.Get(groupImageRegistry, "tke.registry.domain", r.region)
}

func (r *ImageRegistry) Namespace() (string, error) {
	return r.rainbow.Get(groupImageRegistry, "tke.image.namespace", r.region)
}

func (r *ImageRegistry) LoginCredentials() (string, error) {
	registry, err := r.TkeRegistry()
	if err != nil {
		return "", err
	}

	params := struct {
		Registry string
	}{Registry: registry}

	return r.rainbow.GetTemplate(groupImageRegistry, "tke.repo.login.credentials", params, r.region)
}

func (r *ImageRegistry) image(key string) (string, error) {
	v, err := r.rainbow.Get(groupImageRegistry, key, r.region)
	if err != nil {
		return "", err
	}

	registry, err := r.TkeRegistry()
	if err != nil {
		return "", err
	}

	ns, err := r.Namespace()
	if err != nil {
		return "", err
	}

	return fmt.Sprintf("%s/%s/%s", registry, ns, v), nil
}

func (r *ImageRegistry) flinkImage(key string) (string, error) {
	v, err := r.rainbow.Get(flinkImageRegistry, key, r.region)
	if err != nil {
		return "", err
	}

	registry, err := r.TkeRegistry()
	if err != nil {
		return "", err
	}

	ns, err := r.Namespace()
	if err != nil {
		return "", err
	}

	return fmt.Sprintf("%s/%s/%s", registry, ns, v), nil
}

// Flink 获取某个version的Flink镜像，version 格式应该为  Flink-1.11 或者 Flink-1.13
func (r *ImageRegistry) Flink(version string) (string, error) {
	return r.flinkImage(version)
}

// PyFlink 获取某个version的Flink镜像，version 格式应该为  Flink-1.11 或者 Flink-1.13
// 对应七彩石上的key为  Flink-1.13-Python-3.7
func (r *ImageRegistry) PyFlink(flinkVersion string, pythonVersion string) (string, error) {
	return r.flinkImage(fmt.Sprintf("%s-%s", flinkVersion, pythonVersion))
}

func (r *ImageRegistry) NodeExpansionController() (string, error) {
	return r.image("NodeExpansionController")
}

func (r *ImageRegistry) SetatsOperator() (string, error) {
	return r.image("SetatsOperator")
}

func (r *ImageRegistry) Setats() (string, error) {
	return r.image("Setats")
}

func (r *ImageRegistry) SetatsHiveMetastore() (string, error) {
	return r.image("SetatsHiveMetastore")
}

func (r *ImageRegistry) ClusterAdmin() (string, error) {
	return r.image("ClusterAdmin")
}

func (r *ImageRegistry) ClusterScheduler() (string, error) {
	return r.image("ClusterScheduler")
}

func (r *ImageRegistry) UniformClusterScheduler() (string, error) {
	return r.image("UniformClusterScheduler")
}

func (r *ImageRegistry) UserAgent() (string, error) {
	return r.image("UserAgent")
}

func (r *ImageRegistry) UniformUserAgent() (string, error) {
	return r.image("UniformUserAgent")
}

func (r *ImageRegistry) ClusterMaster() (string, error) {
	return r.image("ClusterMaster")
}

func (r *ImageRegistry) TaskCenter() (string, error) {
	return r.image("TaskCenter")
}

func (r *ImageRegistry) WatchDog() (string, error) {
	return r.image("WatchDog")
}

func (r *ImageRegistry) NginxReverse() (string, error) {
	return r.image("NginxProxy")
}

func (r *ImageRegistry) HadoopYarn() (string, error) {
	return r.image("HadoopYarn")
}

func (r *ImageRegistry) UniformWatchDog() (string, error) {
	return r.image("UniformWatchDog")
}

func (r *ImageRegistry) LogListener() (string, error) {
	return r.image("LogListener")
}

func (r *ImageRegistry) CommandController() (string, error) {
	return r.image("CommandControllerImage")
}

func (r *ImageRegistry) CredentialsProvider() (string, error) {
	return r.image("CredentialsProvider")
}

func (r *ImageRegistry) MetricProvider() (string, error) {
	return r.image("MetricProvider")
}

func (r *ImageRegistry) DiagnosisDataUploader() (string, error) {
	return r.image("DiagnosisDataUploader")
}

func (r *ImageRegistry) Filebeat() (string, error) {
	return r.image("Filebeat")
}

func (r *ImageRegistry) SqlServer() (string, error) {
	return r.image("SqlServer")
}
