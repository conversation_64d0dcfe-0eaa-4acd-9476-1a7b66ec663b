package sql

import (
	"encoding/json"
	"errors"
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/metadata"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/metadata"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	service_cluster "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	metadata2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/metadata"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/quota"
)

type DoCreateMetaTableService struct {
}

func (service *DoCreateMetaTableService) DoCreateMetaTable(req *model.CreateMetaTableReq, evenId int64) (string, string, *model.CreateMetaTableRsp) {
	logger.Debug(fmt.Sprintf("DoCreateMetaTable req [ %#v ]", req))
	resp := &model.CreateMetaTableRsp{}
	// 1.解析SQL 获取表类型
	var vpcId = ""
	var err error

	//鉴权
	itemSpcId, err := auth.InnerAuthMetaDb(req.WorkSpaceId, req.IsSupOwner, req.DatabaseId, req.AppId, req.SubAccountUin, req.Region, req.Action)
	if err != nil {
		logger.Errorf("%s: User owner uin %s is NOT authenticated, refuse to serve", req.RequestId, req.Uin)
		errCode := errorcode.GetCode(err)
		return errCode.GetCodeStr(), errCode.GetCodeDesc(), nil
	}

	if req.ClusterId != "" {
		vpcId, err = GetClusterVpcId(req)
		if err != nil {
			logger.Error("Failed to Get Cluster VpcId :", req, "\t err:", err)
			return controller.InternalError, controller.NULL, nil
		}
	}
	// 1. parse sql [sql-parser]
	if req.FlinkVersion == "" {
		req.FlinkVersion = constants.FLINK_VERSION_1_11
	}
	parser := NewParseSqlService(req.RequestId, req.Uin, req.AppId, req.Region, req.FlinkVersion, req.ClusterId, itemSpcId)
	parseRsp, err := parser.ParseSql(req.SqlCode, req.Region, 13)

	if err != nil {
		msg := fmt.Sprintf("Failed to  check ddl grammar.")
		logger.Error(msg+": ", req.SqlCode, "\t err:", err)
		return controller.FailedOperation_CheckDdlGrammar, msg, nil
	}
	if !parseRsp.Pass {
		msg := fmt.Sprintf("Failed to  check ddl grammar.")
		logger.Error(msg+": ", req.SqlCode, "\t rsp :", parseRsp)
		return controller.FailedOperation_CheckDdlGrammar, parseRsp.ErrorMessage, nil
	}
	tables := parseRsp.MetaRefs.TemporaryTables
	if len(tables) == 0 {
		msg := fmt.Sprint("Failed to parse ddl.")
		return controller.FailedOperation_CheckDdlGrammar, msg, nil
	}
	metaTable := tables[0]
	// 2. 校验表名
	err = metadata2.CheckNameValidity(metaTable.Name, 100)
	if err != nil {
		logger.Errorf("Failed to invoke CheckNameValidity, with error: %+v", err)
		return controller.InvalidParameterValue, err.Error(), resp
	}
	// 3. 判断表是否已存在
	existed, err := metadata2.CheckMetaTableExisted(req.DatabaseId, metaTable.Name, req.Uin, req.AppId, req.Region)
	if err != nil {
		logger.Error("Failed to CheckMetaTableExisted :", req, "\t err:", err)
		return controller.InternalError, controller.NULL, nil
	}
	if existed {
		msg := fmt.Sprintf("Meta Table %s already existed.", metaTable.Name)
		logger.Warning(msg)
		return controller.InvalidParameterValue_MetaTableExisted, msg, nil
	}

	tableType, err := metadata2.GetTableType(metaTable.Properties)
	if err != nil {
		logger.Error("Failed to get connector type  :", metaTable.Properties, "\t err:", err)
		return controller.FailedOperation_CheckDdlGrammar, err.Error(), nil
	}
	// 4. ddl语法检查
	checkDdlGrammarService := &CheckDdlGrammarService{}
	checkDdlGrammarReq := &model.CheckDdlGrammarReq{
		RequestBase:  req.RequestBase,
		SqlCode:      req.SqlCode,
		ClusterId:    req.ClusterId,
		ResourceRefs: req.ResourceRefs,
		FlinkVersion: req.FlinkVersion,
	}
	_, msg, checkDdlGrammarRsp := checkDdlGrammarService.DoCheckDdlGrammar(checkDdlGrammarReq, 13)
	if msg != "" {
		logger.Error("Failed to CheckDdlGrammar  :", checkDdlGrammarReq, "\t err:", err)
		return controller.FailedOperation_CheckDdlGrammar, msg, nil
	}
	if !checkDdlGrammarRsp.Pass {
		return controller.FailedOperation_CheckDdlGrammar, checkDdlGrammarRsp.ErrorMessage, nil
	}
	descmap := make(map[string]string, 0)
	var description = ""
	if vpcId != "" {
		descmap["VpcId"] = vpcId
		description, err = BuildDescription(descmap)
		if err != nil {
			return controller.InternalError, controller.NULL, nil
		}
	}
	// 5. 保存元表和关联关系表
	metastoreTable := &metadata.MetastoreTable{
		Script:      req.SqlCode,
		DatabaseId:  req.DatabaseId,
		SubUin:      req.SubAccountUin,
		Uin:         req.Uin,
		AppId:       req.AppId,
		Region:      req.Region,
		Comment:     req.Comment,
		Description: description,
		CreateTime:  util.GetCurrentTime(),
		UpdateTime:  util.GetCurrentTime(),
		TableType:   tableType,
		Name:        metaTable.Name,
		TableSchema: metaTable.TableSchema,
		Properties:  metaTable.Properties,
		Type:        metaTable.Type,
		Version:     constants.META_TABLE_VERSION_DEFAULT,
		Status:      constants.METADATA_CATALOG_STATUS_ACTIVE,
	}
	metaTableResourceRefs := make([]*metadata.MetaTableResourceRef, 0)

	for _, versionedCheckDdlRsp := range checkDdlGrammarService.CheckDdlGrammarRspSet {
		// 保证作业Flink版本的表创建成功，否则创建失败；
		if versionedCheckDdlRsp.FlinkVersion == req.FlinkVersion && !versionedCheckDdlRsp.CheckDdlGrammarRsp.Pass {
			return controller.FailedOperation_CheckDdlGrammar, versionedCheckDdlRsp.CheckDdlGrammarRsp.ErrorMessage, nil
		}
		if versionedCheckDdlRsp.CheckDdlGrammarRsp.Pass {
			metaTableResourceRef := &metadata.MetaTableResourceRef{
				ResourceId: constants.METADATA_TABLE_RESOURCE_REF_NO_NEED_REFERENCE_RESOURCE_ID,
				VersionId:  constants.METADATA_TABLE_RESOURCE_REF_NO_NEED_REFERENCE_VERSION,
				Status:     constants.RESOURCE_CONF_STATUS_ACTIVE,
				CreateTime: util.GetCurrentTime(),
				UpdateTime: util.GetCurrentTime(),
				Type:       constants.METADATA_TABLE_RESOURCE_TYPE_SYSTEM_RESOURCE,
			}
			var versionId int64 = -1       // 内置Connector,使用最新版本
			if len(req.ResourceRefs) > 0 { //  用户Connector,用户指定版本
				versionId = req.ResourceRefs[0].Version
				metaTableResourceRef.Type = constants.METADATA_TABLE_RESOURCE_TYPE_USER_RESOURCE
			}
			if len(versionedCheckDdlRsp.ResourceLocs) > 0 {
				if versionedCheckDdlRsp.ResourceLocs[0].ResourceId == constants.METADATA_TABLE_RESOURCE_REF_NO_NEED_REFERENCE_RESOURCE_ID {
					metaTableResourceRef.ResourceId = constants.METADATA_TABLE_RESOURCE_REF_NO_NEED_REFERENCE_RESOURCE_ID
					metaTableResourceRef.VersionId = constants.METADATA_TABLE_RESOURCE_REF_NO_NEED_REFERENCE_VERSION
				} else {
					metaTableResourceRef.ResourceId = versionedCheckDdlRsp.ResourceLocs[0].ResourceId
					metaTableResourceRef.VersionId = versionId
				}
			}
			metaTableResourceRef.Status = constants.RESOURCE_CONF_STATUS_ACTIVE
			metaTableResourceRef.CreateTime = util.GetCurrentTime()
			metaTableResourceRef.UpdateTime = util.GetCurrentTime()
			metaTableResourceRef.FlinkVersion = versionedCheckDdlRsp.FlinkVersion
			metaTableResourceRefs = append(metaTableResourceRefs, metaTableResourceRef)
		}
	}

	// 6. 数据库总数量是否超过配额
	tablesTotalCount, err := metadata2.GetTablesTotalCountByAppId(req.AppId, req.Region)
	if err != nil {
		logger.Errorf("%s: Failed to create table. Exception occurs when invoke GetTablesTotalCountByAppId, error: %+v", req.RequestId, err)
		return controller.InternalError, controller.NULL, nil
	}
	overLimit, msg, err := quota.NewQuota().OverLimit("", int64(req.AppId), quota.MetaTable, int32(tablesTotalCount))
	if err != nil {
		logger.Errorf("%s: Failed to create table. Exception occurs when invoke quota.NewQuota().OverLimit, error: %+v", req.RequestId, err)
		return controller.InternalError, controller.NULL, nil
	}
	if overLimit {
		errMsg := fmt.Sprintf("table %s", msg)
		logger.Errorf("%s: Failed to create table for AppID %d. %s", req.RequestId, req.AppId, errMsg)
		return controller.LimitExceeded_MetaTable, errMsg, nil
	}
	// 7. 保存Table
	tableId, err := SaveMetaTable(metastoreTable, metaTableResourceRefs)
	if err != nil {
		logger.Error("Failed to  save  Meta Table :", req, "\t err:", err)
		return controller.InternalError, controller.NULL, nil
	}
	resp.TableId = tableId
	// 8. 保存TableIdentifier
	identifierService, err := metadata2.NewMetaTableIdentifierService(req.FlinkVersion, metaTable.Properties,
		metaTable.Catalog, metaTable.Database, metaTable.Name)
	if err != nil {
		logger.Error("Failed to  NewMetaTableIdentifierService  :", req, "\t err:", err)
		return controller.InternalError, controller.NULL, nil
	}

	identifier, err := identifierService.BuildMetaTableIdentifier(req.Region, req.AppId, req.Uin, req.SubAccountUin, itemSpcId)
	if err != nil {
		logger.Error("Failed to  BuildMetaTableIdentifier  :", req, "\t err:", err)
		return controller.InternalError, controller.NULL, nil
	}
	identifierId := identifier.Id
	if identifier.SerialId == "" {
		identifierId, err = metadata2.SaveMetaTableIdentifier(identifier)
		if err != nil {
			logger.Error("Failed to  SaveMetaTableIdentifier  :", req, "\t err:", err)
			return controller.InternalError, controller.NULL, nil
		}
	}
	metaTableIdentifierRefs := make([]*metadata.MetaTableIdentifierRef, 0)
	metaTableIdentifierRefs = append(metaTableIdentifierRefs, &metadata.MetaTableIdentifierRef{
		IdentifierId: identifierId,
		TableId:      tableId,
		Status:       constants.RESOURCE_CONF_STATUS_ACTIVE,
		CreateTime:   util.GetCurrentTime(),
		UpdateTime:   util.GetCurrentTime(),
	})
	err = metadata2.SaveIdentifierRefs(metaTableIdentifierRefs)
	if err != nil {
		logger.Error("Failed to  SaveIdentifierRefs  :", req, "\t err:", err)
		return controller.InternalError, controller.NULL, nil
	}

	return controller.OK, controller.NULL, resp
}

func SaveMetaTable(metastoreTable *metadata.MetastoreTable, metaTableResourceRefs []*metadata.MetaTableResourceRef) (id int64, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Add metastoreTable to db panic ,for job:%+v, errors:%+v", metastoreTable, errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()

	txManager := service.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		id = tx.SaveObject(metastoreTable, "MetastoreTable")
		cidUtil := &util.CidUtil{}
		serialId := cidUtil.EncodeId(id, "mtable", "mide", util.GetNowTimestamp(), 8)
		tx.ExecuteSqlWithArgs("UPDATE MetastoreTable set serialId = ?  WHERE id = ? ", serialId, id)
		for _, metaTableResourceRef := range metaTableResourceRefs {
			metaTableResourceRef.MetaTableId = id
			tx.SaveObject(metaTableResourceRef, "MetaTableResourceRef")
		}
		return nil
	}).Close()
	return id, nil
}

func GetClusterVpcId(req *model.CreateMetaTableReq) (vpc string, err error) {
	clusterGroup, err := service2.ListClusterGroupBySerialId(req.ClusterId)
	if err != nil {
		logger.Errorf("[%s]: ListClusterGroupBySerialId %s error %+v", req.RequestId, req.ClusterId, err)
		return
	}
	if clusterGroup == nil {
		msg := fmt.Sprintf("Unknown ClusterId %s", req.ClusterId)
		logger.Error(msg)
		return "", errors.New(msg)
	}
	ccnsMap, err := service_cluster.ListCCNs([]int64{clusterGroup.Id})
	if err != nil {
		msg := fmt.Sprintf("[%s]: ListCCNs by groupId %d error %+v", req.RequestId, clusterGroup.Id, err)
		logger.Error(msg)
		return "", errors.New(msg)
	}
	if len(ccnsMap) == 0 {
		msg := fmt.Sprintf("[%s]: ListCCNs by groupId %d empty", req.RequestId, clusterGroup.Id)
		logger.Error(msg)
		return "", errors.New(msg)
	}
	return ccnsMap[clusterGroup.Id][0].VpcId, nil
}

func BuildDescription(description map[string]string) (string, error) {
	data, err := json.Marshal(&description)
	if err != nil {
		return "", err
	}
	return string(data), nil
}
