package sql

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/juju/errors"
	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service1 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_config"
	model4 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/resource_config"
	model_resource_config "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/resource_config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/sql"
	metadata_model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/metadata"
	sql_model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/sql"
	model2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/resource"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/resource_config"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/metadata"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/variable"
)

const (
	INNEER_CONNECTOR_REF_MODE_MANUAL                 = 0 // 手动选择内置connector
	INNER_CONNECTOR_REF_MODE_AUTO                    = 1 // 自动关联内置connector
	SQL_CHECK_ACTION_MODE_EXECUTE                    = 1 // DEFAULT
	SQL_CHECK_ACTION_MODE_QUERY_RESULT               = 2
	SQL_CHHECK_ACTION_MODE_RUN_SQL_PREVIEW           = 3 // // 运行调试sql的逻辑, 跟 语法检查接近一模一样,复用语法检查的接口
	SQL_CHECK_ACTION_MODE_CHECK_COMPATIBILITY        = 4 // // 运行调试sql的逻辑, 跟 语法检查接近一模一样,复用语法检查的接口
	SQL_CHECK_ACTION_MODE_CHECK_COMPATIBILITY_RESULT = 5 // // 查询检查sql state 兼容性的结果

	CTX_KEY_RUN_SQL_PREVEW_REQ          = "RunSqlPreviewReq"
	CTX_KEY_CHECK_SQL_COMPATIBILITY_REQ = "CheckSqlCompatibilityReq"
)

var supportFlinkVersion = map[string]struct{}{
	"Flink-1.11": {},
	"Flink-1.13": {},
}

type CheckSqlGrammarService struct {
	Req             *sql.CheckSqlGrammarReq
	flinkVersion    string
	parseSqlService *ParseSqlService
	FlinkVersionService
	Variables            []*metadata_model.Variable
	ItemSpaceId          int64
	ctx                  context.Context
	ContainsCustomConsts map[string]bool
	DeepSqlCheckId       int64
}

type CheckSqlGrammarCache struct {
	InnerConnectorRefLocs []*sql_model.ResourceLocationIntegral
	InnerConnectorRefMode int
}

func NewCheckSqlGrammarService(req *sql.CheckSqlGrammarReq, ctx context.Context) *CheckSqlGrammarService {
	return &CheckSqlGrammarService{Req: req, ctx: ctx}
}

func (c *CheckSqlGrammarService) getParseSqlService() {
	c.parseSqlService = NewParseSqlService(c.Req.RequestId, c.Req.Uin, c.Req.AppId, c.Req.Region,
		c.flinkVersion, c.Req.ClusterId, c.ItemSpaceId)
}

func (c *CheckSqlGrammarService) CheckSqlGrammar(ctx context.Context) (rsp interface{}, err error) {

	//鉴权
	c.ItemSpaceId, err = auth.InnerAuthJob(c.Req.WorkSpaceId, c.Req.IsSupOwner, c.Req.JobId, c.Req.AppId, c.Req.SubAccountUin, c.Req.Region, c.Req.Action)
	if err != nil {
		logger.Errorf("%s: User  uin %s is NOT authenticated, refuse to serve", c.Req.RequestId, c.Req.Uin)
		return nil, err
	}

	c.flinkVersion, err = c.GetFlinkVersion(c.Req.AppId, c.Req.Region, c.Req.ClusterId, c.Req.JobId, c.Req.FlinkVersion)
	if err != nil {
		return nil, err
	}
	if c.Req.ActionMode == SQL_CHECK_ACTION_MODE_QUERY_RESULT {
		sqlServer, err := NewSqlServerService(c.Req.AppId, c.Req.ClusterId, c.Req.RequestId, c.Req.Uin, c.Req.Region, c.flinkVersion)
		if err != nil {
			return nil, err
		}
		vpcMode, err := c.isVpcMode(sqlServer)
		if err != nil {
			return nil, err
		}
		describeCheckSqlResult, err := c.describeCheckSqlResult(ctx, sqlServer, vpcMode)
		if err != nil {
			return nil, err
		}
		return describeCheckSqlResult, nil
	} else if c.Req.ActionMode == SQL_CHECK_ACTION_MODE_CHECK_COMPATIBILITY_RESULT {
		sqlServer, err := NewSqlServerService(c.Req.AppId, c.Req.ClusterId, c.Req.RequestId, c.Req.Uin, c.Req.Region, c.flinkVersion)
		if err != nil {
			return nil, err
		}
		describeCheckSqlResult, err := c.describeCheckSqlStateCompatibilityResult(sqlServer)
		if err != nil {
			return nil, err
		}
		return describeCheckSqlResult, nil
	}
	c.getParseSqlService() // init  parse sql service
	// 1. parse sql [sql-parser]
	parseSqlRsp, err := c.ParseSql(ctx)
	if err != nil {
		return nil, errorcode.FailedOperationCode_ParseSql.NewWithErr(err)
	}
	if !parseSqlRsp.Pass {
		rsp := &sql.CheckSqlGrammarRsp{
			Pass:            false,
			ErrorMessage:    parseSqlRsp.ErrorMessage,
			ErrorCoordinate: parseSqlRsp.ErrorCoordinate,
			Done:            true,
		}
		logger.Errorf("[%s]: Failed to parse sql. response :%+v", c.Req.RequestId, parseSqlRsp)
		return rsp, nil
	}
	logger.Debugf("CheckSqlGrammar-parseSqlRsp+ %+v", parseSqlRsp)
	// 2. 检查是否包含隐藏变量
	customVariableCtx := NewHandleCustomVariableCtx().
		WithSqlCode(c.Req.SqlCode).
		WithBase64Encoded(true).
		WithCustomConstAndVals(map[string]interface{}{
			CustomTimestamp: time.Now().Unix() * 1000,
			CustomScanMod:   CustomScanModDefult,
			SpecificOffset:  SpecificOffsetDefault,
		}).WithMetaTableEntry(parseSqlRsp.MetaRefs.TemporaryTables)
	c.ContainsCustomConsts, err = NewCustomConstHandler(customVariableCtx).ContainsCustomConst()
	if err != nil {
		logger.Errorf("check CustomConst const has returned error %v", err)
		return nil, errorcode.FailedOperationCode_GrammarCheckFailure.ReplaceDesc(err.Error())
	}
	logger.Debugf("CheckSqlGrammar-start")
	// 2. 语法检查
	rsp, err = c.checkSqlGrammar(ctx, parseSqlRsp)
	if err != nil {
		if strings.Contains(err.Error(), "ExecuteCommandTimeout") {
			return rsp, errorcode.FailedOperationCode_ExecuteCommandTimeout.ReplaceDesc(err.Error())
		}
		return nil, err
	}
	logger.Debugf("CheckSqlGrammar-end")
	// 3. 处理隐藏变量,异常提示信息中不能出现隐藏变量值
	err = c.replaceVariablesInErrorMessage(rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (c *CheckSqlGrammarService) replaceVariablesInErrorMessage(rsp interface{}) error {
	var errorMessage *string = nil
	if c.Req.ActionMode == SQL_CHHECK_ACTION_MODE_RUN_SQL_PREVIEW {
		r := rsp.(*sql.RunSqlPreviewRsp)
		errorMessage = &r.ErrorMessage
	} else if c.Req.ActionMode == SQL_CHECK_ACTION_MODE_CHECK_COMPATIBILITY {
		errorMessage = common.StringPtr("")
	} else {
		r := rsp.(*sql.CheckSqlGrammarRsp)
		errorMessage = &r.ErrorMessage
	}
	if *errorMessage != "" && len(c.Req.MetaTableVariables) > 0 {
		metaTableVariables := c.Req.MetaTableVariables
		globalVariables := make([]string, 0)
		for _, metaTableVariable := range metaTableVariables {
			for _, entry := range metaTableVariable.VariableEntries {
				if entry.VariableId != "" {
					globalVariables = append(globalVariables, entry.VariableId)
				}
			}
		}
		if len(globalVariables) > 0 {
			newErrorMessage, err := variable.HiddenVariable(globalVariables, *errorMessage, c.Req.Uin, c.Req.AppId, c.Req.Region)
			if err != nil {
				return errorcode.FailedOperationCode_GrammarCheckFailure.ReplaceDesc(err.Error())
			}
			*errorMessage = newErrorMessage
		}
	}
	return nil
}

func (c *CheckSqlGrammarService) mergeResource(innerResources []*sql_model.ResourceLocationIntegral) (
	resourceLocs []*sql_model.ResourceLocationForSqlServer, err error) {
	refs, err := GetResourceLoc(c.Req.ResourceRefs) // 用户程序包
	if err != nil {
		return nil, err
	}
	refSet := make(map[string]bool)
	for _, ref := range refs {
		refSet[ref.SerialId] = true
	}
	for _, ref := range innerResources { // user resource merge inner resource
		if _, ok := refSet[ref.ResourceSerialId]; !ok {
			refs = append(refs, &sql_model.ResourceLocationForSqlServer{
				SerialId:           fmt.Sprintf("%d-%d", ref.ResourceId, ref.VersionId),
				ResourceLocationV2: ref.ResourceLocationV2,
			})
		}
	}
	return refs, nil
}

func (c *CheckSqlGrammarService) checkSqlGrammar(ctx context.Context,
	parseSqlRsp *sql_model.ParseSqlRsp) (rsp interface{}, err error) {
	sqlServer, err := NewSqlServerService(c.Req.AppId, c.Req.ClusterId, c.Req.RequestId, c.Req.Uin, c.Req.Region, c.flinkVersion)
	if c.Req.ShouldReturnJobGraph && !sqlServer.SupportSqlGetStreamGraph {
		return nil, errorcode.UnsupportedOperation_SqlGetStreamGraphUnsupported.New()
	}

	if c.Req.ActionMode == SQL_CHECK_ACTION_MODE_CHECK_COMPATIBILITY && !sqlServer.SupportStateCompatibilityCheck {
		return nil, errorcode.UnsupportedOperation_SqlCheckCompatibilityUnsupported.New()
	}

	if err != nil {
		return nil, err
	}
	var innerConnectorRefMode = INNEER_CONNECTOR_REF_MODE_MANUAL
	// 集群特性的判断逻辑一致，如果集群部署了SqlServer则进行自动匹配Connector
	if sqlServer.FoundSatisfiedCluster {
		innerConnectorRefMode = INNER_CONNECTOR_REF_MODE_AUTO
	}
	vpcMode, err := c.isVpcMode(sqlServer)
	if err != nil {
		return nil, err
	}
	// 集群版本统一后，删除INNEER_CONNECTOR_REF_MODE_MANUAL以及地域SqlParser逻辑
	return c.internalCheckSqlGrammar(ctx, sqlServer, parseSqlRsp, innerConnectorRefMode, vpcMode)
}

func (c *CheckSqlGrammarService) isVpcMode(s *SqlServerService) (vpcMode bool, err error) {
	// 判断SqlServer是否支持Flink1.13
	sqlServerSupportFlink13, err := s.CheckSupportFlink_1_13()
	if err != nil {
		return true, err
	}
	// 兼容存量集群,1.8-20210731全部升级到更高版本去掉改逻辑
	return (c.flinkVersion == constants.FLINK_VERSION_1_13 && sqlServerSupportFlink13) || (c.flinkVersion != constants.FLINK_VERSION_1_13 && s.FoundSatisfiedCluster), nil
}

func (c *CheckSqlGrammarService) describeCheckSqlResult(ctx context.Context,
	sqlServer *SqlServerService, vpcMode bool) (rsp *sql.CheckSqlGrammarRsp, err error) {
	sqlCheckGrammarReq, err := c.buildDescribeSqlCheckResultReq()
	if err != nil {
		msg := fmt.Sprintf("[%s]: Failed to build SqlCheckGrammarReq. ", c.Req.RequestId)
		logger.Errorf("[%s]: Failed to build SqlCheckGrammarReq return err:%+v", c.Req.RequestId, err)
		return nil, errorcode.FailedOperationCode_GrammarCheckFailure.ReplaceDesc(msg)
	}
	flinkConf := make(map[string]string)
	for _, kv := range c.Req.Properties {
		flinkConf[kv.Key] = kv.Value
	}
	// 7.执行语法检查
	rsp, err = sqlServer.CheckSqlGrammar(sqlCheckGrammarReq, vpcMode, constants.CHECK_SQL_GRAMMAR_EVENTID, flinkConf)
	if err != nil {
		msg := fmt.Sprintf("[%s]: Failed invoke internal check sql grammar. ", c.Req.RequestId)
		logger.Errorf("[%s]: Failed invoke internal check sql grammar return err:%+v", c.Req.RequestId, err)
		return nil, errorcode.FailedOperationCode_GrammarCheckFailure.ReplaceDesc(msg)
	}
	return rsp, nil
}

func (c *CheckSqlGrammarService) describeCheckSqlStateCompatibilityResult(sqlServer *SqlServerService) (rsp *sql.CheckSqlCompatibilityRsp, err error) {
	sqlCheckGrammarReq, err := c.buildDescribeSqlCheckResultReq()
	if err != nil {
		msg := fmt.Sprintf("[%s]: Failed to build SqlCheckStateCompatibilityReq. ", c.Req.RequestId)
		logger.Errorf("[%s]: Failed to build SqlCheckStateCompatibilityReq return err:%+v", c.Req.RequestId, err)
		return nil, errorcode.FailedOperationCode_GrammarCheckFailure.ReplaceDesc(msg)
	}
	flinkConf := make(map[string]string)
	for _, kv := range c.Req.Properties {
		flinkConf[kv.Key] = kv.Value
	}

	ctx := c.ctx.Value(CTX_KEY_CHECK_SQL_COMPATIBILITY_REQ).(*checkCompatibilityContext)

	rsp, err = sqlServer.CheckSqlCompatibility(sqlCheckGrammarReq, flinkConf, ctx.execNodeGraph, c.Req.ActionMode, constants.QUERY_SQL_COMPATIBILITY_RESULT_EVENTID)
	if err != nil {
		msg := fmt.Sprintf("[%s]: Failed invoke internal check sql grammar. ", c.Req.RequestId)
		logger.Errorf("[%s]: Failed invoke internal check state compatibility return err:%+v", c.Req.RequestId, err)
		return nil, errorcode.FailedOperationCode_GrammarCheckFailure.ReplaceDesc(msg)
	}
	return rsp, nil
}

func (c *CheckSqlGrammarService) internalCheckSqlGrammar(ctx context.Context,
	sqlServer *SqlServerService, parseSqlRsp *sql_model.ParseSqlRsp,
	innerConnectorRefMode int, vpcMode bool) (rsp interface{}, err error) {
	// 1. 获取元数据表和外部数据源
	metaTables, exCatalogs, err := c.parseSqlService.GetMetaTables(ctx, parseSqlRsp.MetaRefs, c.ItemSpaceId) // Unsupported ExCatalog
	if err != nil {
		msg := fmt.Sprintf("[%s]: Failed to parse metadata ,cause : %v.", c.Req.RequestId, err.Error())
		logger.Errorf(msg+",return err:%+v ", err)
		return &sql.CheckSqlGrammarRsp{
			Pass:         false,
			ErrorMessage: msg,
			Done:         true,
		}, nil
	}
	// 2. 替换临时表变量
	_, variables, err := c.parseSqlService.GetMetaReferences(parseSqlRsp.MetaRefs, metaTables, c.Req.Uin, c.Req.AppId, c.Req.Region, c.ItemSpaceId)
	if err != nil {
		msg := fmt.Sprintf("[%s]: Failed to get meta references", c.Req.RequestId)
		logger.Errorf(msg+",return err:%+v ", err)
		return nil, errorcode.FailedOperationCode_GrammarCheckFailure.ReplaceDesc(msg)
	}
	if CheckIfUsedTempTableVariable(variables) {
		attachVariablesReq, err := c.buildAttachVariablesReq(metaTables, exCatalogs)
		if err != nil {
			msg := fmt.Sprintf("[%s]: Failed to build AttachVariablesReq", c.Req.RequestId)
			logger.Errorf(msg+",return err:%+v ", err)
			return nil, errorcode.FailedOperationCode_GrammarCheckFailure.ReplaceDesc(msg)
		}
		attachVariablesRsp, err := c.parseSqlService.AttachVariables(ctx,
			attachVariablesReq, c.Req.Region, constants.ATTACH_VARIABLES_EVENTID)
		if err != nil {
			msg := fmt.Sprintf("[%s]: Failed to invoke AttachVariables", c.Req.RequestId)
			logger.Errorf(msg+",return err:%+v ", err)
			return nil, errorcode.FailedOperationCode_GrammarCheckFailure.ReplaceDesc(msg)
		}
		c.Req.SqlCode = base64.StdEncoding.EncodeToString([]byte(attachVariablesRsp.SqlCode))
	}
	if len(exCatalogs) > 0 && !sqlServer.SupportExternalCatalog {
		msg := fmt.Sprintf("[%s]: Cluster do not support external catalog.", c.Req.RequestId)
		logger.Infof(msg)
		return nil, errorcode.UnsupportedOperation_ExternalCatalogUnsupported.ReplaceDesc(msg)
	}
	// 引用了多个外部数据源，目前SQL作业只允许引用一个外部数据源
	if len(exCatalogs) > 1 {
		msg := fmt.Sprintf("[%s]: Only one external catalog supported in a job.", c.Req.RequestId)
		logger.Infof(msg)
		return nil, errorcode.UnsupportedOperation_MultiExternalCatalogUnsupported.New()
	}
	// 鉴权
	err = c.catalogAuth(metaTables)
	if err != nil {
		logger.Errorf("[%s]: Catalog does not belong to this workspace :%+v", c.Req.RequestId, err)
		return nil, errors.New("Catalog does not belong to this workspace.")
	}

	// 判断外部数据源是否有drop语句
	if len(exCatalogs) > 0 {
		dropCatalogNames := make(map[string]bool, 0)
		for _, dropTable := range parseSqlRsp.MetaRefs.DropTables {
			dropTableIdentifier := strings.Split(dropTable, ".")
			dropCatalogNames[dropTableIdentifier[0]] = true
		}
		for _, dropDatabaseIdentifier := range parseSqlRsp.MetaRefs.DropDatabases {
			dropDatabaseIdentifier := strings.Split(dropDatabaseIdentifier, ".")
			dropCatalogNames[dropDatabaseIdentifier[0]] = true
		}
		for _, catalog := range exCatalogs {
			if _, ok := dropCatalogNames[catalog.Name]; ok {
				return nil, errorcode.UnsupportedOperation_DropOperationUnsupported.New()
			}
		}
	}
	// 浅度语法 validate sql
	// 如果浅度语法检查，只检查 ParseSql
	if !c.Req.DeepSqlCheckEnable && c.Req.ActionMode == SQL_CHECK_ACTION_MODE_EXECUTE {
		// 构造validate检查请求
		sqlValidateReq, err := c.buildSqlValidateReq(metaTables, exCatalogs)
		if err != nil {
			msg := fmt.Sprintf("[%s]: Failed to build sqlValidateReq.", c.Req.RequestId)
			logger.Errorf(msg+",return err:%+v ", err)
			return nil, errorcode.FailedOperationCode_GrammarCheckFailure.ReplaceDesc(msg)
		}
		validateRsp, err := c.parseSqlService.ParseAndValidateSql(ctx, sqlValidateReq, c.Req.Region)
		if err != nil {
			msg := fmt.Sprintf("[%s]: Failed invoke internal validate sql grammar.", c.Req.RequestId)
			logger.Errorf(msg+",return err:%+v ", err)
			return nil, errorcode.FailedOperationCode_GrammarCheckFailure.ReplaceDesc(msg)
		}
		rsp := &sql.CheckSqlGrammarRsp{
			Pass:            validateRsp.Pass,
			ErrorMessage:    validateRsp.ErrorMessage,
			ErrorCoordinate: validateRsp.ErrorCoordinate,
			Done:            true,
		}
		return rsp, err
	}

	// 3. 获取依赖资源
	resourceLocations, err := GetResourceLoc(c.Req.ResourceRefs)
	if err != nil {
		msg := fmt.Sprintf("[%s]: Failed to get resource Locations", c.Req.RequestId)
		logger.Errorf(msg+",return err:%+v ", err)
		return nil, errorcode.FailedOperationCode_GrammarCheckFailure.ReplaceDesc(msg)
	}

	if innerConnectorRefMode == INNER_CONNECTOR_REF_MODE_AUTO { // 执行Connector自动匹配
		// 3. 解析用户jar包是否存在系统Connector
		analyzeUserJarRsp, err := sqlServer.AnalyzeUserJar(resourceLocations, c.Req.JobId)
		if err != nil {
			msg := fmt.Sprintf("[%s]: Failed to AnalyzeUserJar return err:%+v ", c.Req.RequestId, err)
			logger.Errorf(msg+",return err:%+v ", err)
			return nil, errorcode.FailedOperationCode_GrammarCheckFailure.ReplaceDesc(msg)
		}
		if !analyzeUserJarRsp.AnalyzeSuccess {
			logger.Errorf("[%s]: Failed to analyze user jar, return err:%+v ", c.Req.RequestId, analyzeUserJarRsp.ErrorMessage)
			return nil, errorcode.FailedOperationCode_GrammarCheckFailure.ReplaceDesc(analyzeUserJarRsp.ErrorMessage)
		}
		// 4.自动匹配Connector
		innerResourceRefs, err := c.parseSqlService.GetResourceRefsFromMeta(
			metaTables,
			parseSqlRsp.MetaRefs,
			analyzeUserJarRsp.UserDefinedConnectors,
			exCatalogs)
		if err != nil {
			msg := fmt.Sprintf("[%s]: Failed to get resource from metadata.", c.Req.RequestId)
			logger.Errorf(msg+",return err:%+v ", err)
			return nil, errorcode.FailedOperationCode_GrammarCheckFailure.ReplaceDesc(msg)
		}
		if b, err := json.Marshal(innerResourceRefs); err == nil {
			logger.Debugf("CheckSqlGrammar-innerResourceRefs JSON: %s", string(b))
		} else {
			logger.Errorf("Failed to marshal innerResourceRefs: %v", err)
		}

		// 5.处理JobConfigId的connector替换
		if c.Req.JobConfigId > 0 {
			// 如果传了JobConfigId>0，则替换为对应JobConfigId的connector
			err = service.ReplaceConnectorsByJobConfigId(c.Req.JobConfigId, &innerResourceRefs)
			if err != nil {
				msg := fmt.Sprintf("[%s]: Failed to replace connectors by JobConfigId %d.", c.Req.RequestId, c.Req.JobConfigId)
				logger.Errorf(msg+",return err:%+v ", err)
				return nil, errorcode.FailedOperationCode_GrammarCheckFailure.ReplaceDesc(msg)
			}
		} else {
			// 否则按照replaceOldSystemConnector的逻辑替换
			err = service.ReplaceOldSystemConnectorForCheck(c.Req.JobId, c.Req.AppId, &innerResourceRefs)
			if err != nil {
				msg := fmt.Sprintf("[%s]: Failed to replace old system connectors.", c.Req.RequestId)
				logger.Errorf(msg+",return err:%+v ", err)
				return nil, errorcode.FailedOperationCode_GrammarCheckFailure.ReplaceDesc(msg)
			}
		}

		//6.合并自动匹配与用户依赖
		resourceLocations, err = c.mergeResource(innerResourceRefs)
		if err != nil {
			msg := fmt.Sprintf("[%s]: Failed to merge resource.", c.Req.RequestId)
			logger.Errorf(msg+",return err:%+v ", err)
			return nil, errorcode.FailedOperationCode_GrammarCheckFailure.ReplaceDesc(msg)
		}
	}
	// 7.构造语法检查请求
	sqlCheckGrammarReq, err := c.buildSqlCheckGrammarReq(resourceLocations, metaTables, exCatalogs)
	if err != nil {
		msg := fmt.Sprintf("[%s]: Failed to build SqlCheckGrammarReq.", c.Req.RequestId)
		logger.Errorf(msg+",return err:%+v ", err)
		return nil, errorcode.FailedOperationCode_GrammarCheckFailure.ReplaceDesc(msg)
	}
	if c.Req.ActionMode == SQL_CHECK_ACTION_MODE_EXECUTE {
		// 8.执行语法检查
		flinkConf := make(map[string]string)
		for _, kv := range c.Req.Properties {
			flinkConf[kv.Key] = kv.Value
		}
		rsp, err = sqlServer.CheckSqlGrammar(sqlCheckGrammarReq, vpcMode, constants.CHECK_SQL_GRAMMAR_EVENTID, flinkConf)
		if err != nil {
			msg := fmt.Sprintf("[%s]: Failed invoke internal check sql grammar.", c.Req.RequestId)
			logger.Errorf(msg+",return err:%+v ", err)
			return nil, errorcode.FailedOperationCode_GrammarCheckFailure.ReplaceDesc(msg)
		}
		return rsp, nil
	} else if c.Req.ActionMode == SQL_CHHECK_ACTION_MODE_RUN_SQL_PREVIEW {
		ctx := c.ctx.Value(CTX_KEY_RUN_SQL_PREVEW_REQ).(*previewContext)
		flinkConf := make(map[string]string)
		for _, kv := range ctx.req.Properties {
			flinkConf[kv.Key] = kv.Value
		}
		k8sNs := "kubernetes.namespace"
		if _, ok := flinkConf[k8sNs]; !ok {
			// 共享集群 flink job不在 default ns 了
			flinkConf[k8sNs] = ctx.flinkNamespace
			logger.Infof("flinkConf add %s,  value:%s ", k8sNs, ctx.flinkNamespace)
		}
		rsp, err = sqlServer.RunSqlPreview(sqlCheckGrammarReq, flinkConf, ctx.sessionClusterId, constants.RUN_SQL_PREVIEW_EVENTID)
		return rsp, err
	} else if c.Req.ActionMode == SQL_CHECK_ACTION_MODE_CHECK_COMPATIBILITY {
		ctx := c.ctx.Value(CTX_KEY_CHECK_SQL_COMPATIBILITY_REQ).(*checkCompatibilityContext)
		flinkConf := make(map[string]string)
		for _, kv := range ctx.req.Properties {
			flinkConf[kv.Key] = kv.Value
		}
		rsp, err = sqlServer.CheckSqlCompatibility(sqlCheckGrammarReq, flinkConf, ctx.execNodeGraph, c.Req.ActionMode, constants.CHECK_SQL_COMPATIBILITY_EVENTID)
		return rsp, err
	}

	return nil, errorcode.InvalidParameterCode.NewWithMsg("Unknown ActionMode")
}

func (c *CheckSqlGrammarService) catalogAuth(metaTables []*metadata_model.ReferenceTableWithResource) error {
	for _, meta := range metaTables {
		//鉴权
		_, err := auth.InnerAuthCatalog(c.Req.WorkSpaceId, c.Req.IsSupOwner, meta.MetastoreTableEntity.Id, c.Req.AppId, c.Req.SubAccountUin, c.Req.Region, c.Req.Action)
		if err != nil {
			logger.Errorf("%s: User owner uin %s is not authenticated, refuse to serve", meta.Catalog, c.Req.Uin)
			return err
		}
	}
	return nil
}

func (c *CheckSqlGrammarService) buildSqlValidateReq(metaTables []*metadata_model.ReferenceTableWithResource, exCatalogs []*metadata_model.ExternalCatalogV1) (req map[string]interface{}, err error) {
	sqlCode, err := base64.StdEncoding.DecodeString(c.Req.SqlCode)
	if err != nil {
		return nil, errorcode.NewStackError(errorcode.InvalidParameterCode, "", err)
	}
	constKeyVals := map[string]interface{}{
		CustomTimestamp: time.Now().Unix() * 1000,
		CustomScanMod:   CustomScanModDefult,
		SpecificOffset:  SpecificOffsetDefault,
	}
	if c.ContainsCustomConsts[CustomTimestamp] || c.ContainsCustomConsts[CustomScanMod] || c.ContainsCustomConsts[SpecificOffset] {
		customVariableCtx := NewHandleCustomVariableCtx().
			WithSqlCode(string(sqlCode)).
			WithCustomConstAndVals(constKeyVals)
		sqlCodeStr, err := NewCustomConstHandler(customVariableCtx).ReplaceCustomConst()
		if err != nil {
			logger.Errorf("handle custom variable return error %v", err)
		}
		logger.Debugf("start replace custom variable,2 replacedVariableSqlCodeStr %s", sqlCodeStr)
		sqlCode = []byte(sqlCodeStr)
	}

	variables, err := c.BuildMetaTableVariables() // from request
	if err != nil {
		return nil, err
	}
	c.Variables = variables
	req = make(map[string]interface{})
	req["sqlCode"] = base64.StdEncoding.EncodeToString(sqlCode)
	req["flinkClientVersion"] = c.flinkVersion
	req["jobSerialId"] = c.Req.JobId

	catalogs, err := metadata.BuildMetaCatalogs(exCatalogs)
	if err != nil {
		return
	}
	metadata := metadata_model.Metadata{
		ReferenceTables: metadata.BuildMetaTables(metaTables),
		Catalogs:        catalogs,
		Variables:       variables,
	}
	metadataStr, err := json.Marshal(metadata)
	if err != nil {
		logger.Errorf("JSON marshaling metadata: %+v failed: %s", metadata, err)
		return nil, err
	}
	req["encodedMetadata"] = base64.StdEncoding.EncodeToString(metadataStr)
	return
}

func GetResourceBySqlCheckId(sqlCheckId string) (*model2.Resource, error) {
	resource := &model2.Resource{}
	args := make([]interface{}, 0, 2)
	args = append(args, sqlCheckId)
	args = append(args, constants.RESOURCE_STATUS_ACTIVE)
	sql := "SELECT r.* FROM Resource r join SqlCheckRef scr on r.Id = scr.ResourceId WHERE scr.SqlCheckId=? AND scr.Status=?"
	cnt, resourceData, err := service1.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("GetResourceBySqlCheckId -> DoQuery sql:%s args:%+v error:%+v", sql, args, err)
		return nil, err
	}

	if cnt != 1 {
		err = fmt.Errorf("GetResourceBySqlCheckId -> DoQuery sql:%s args:%+v resource len(%d) != 1", sql, args, cnt)
		return nil, err
	}

	util.ScanMapIntoStruct(resource, resourceData[0])
	return resource, nil
}

func (c *CheckSqlGrammarService) buildSqlCheckGrammarReq(resourceLocs []*sql_model.ResourceLocationForSqlServer,
	metaTables []*metadata_model.ReferenceTableWithResource, exCatalogs []*metadata_model.ExternalCatalogV1) (req map[string]interface{}, err error) {

	sqlCode, err := base64.StdEncoding.DecodeString(c.Req.SqlCode)
	if err != nil {
		return nil, errorcode.NewStackError(errorcode.InvalidParameterCode, "", err)
	}
	constKeyVals := map[string]interface{}{
		CustomTimestamp: time.Now().Unix() * 1000,
		CustomScanMod:   CustomScanModDefult,
		SpecificOffset:  SpecificOffsetDefault,
	}
	if c.ContainsCustomConsts[CustomTimestamp] || c.ContainsCustomConsts[CustomScanMod] || c.ContainsCustomConsts[SpecificOffset] {
		customVariableCtx := NewHandleCustomVariableCtx().
			WithSqlCode(string(sqlCode)).
			WithCustomConstAndVals(constKeyVals)
		sqlCodeStr, err := NewCustomConstHandler(customVariableCtx).ReplaceCustomConst()
		if err != nil {
			logger.Errorf("handle custom variable return error %v", err)
		}
		logger.Debugf("start replace custom variable,2 replacedVariableSqlCodeStr %s", sqlCodeStr)
		sqlCode = []byte(sqlCodeStr)
	}

	variables, err := c.BuildMetaTableVariables() // from request
	if err != nil {
		return nil, err
	}
	c.Variables = variables
	req = make(map[string]interface{})

	if len(string(sqlCode)) == 0 {
		return nil, errors.New("sql is empty, please check it")
	}

	err, resourceConfigItem := translateSqlToCos(c.Req.JobId, string(sqlCode))
	if err != nil {
		return
	}
	if resourceConfigItem != nil && len(resourceConfigItem.ResourceName) > 0 {
		req["sql"] = ""
		if resourceLocs != nil {
			loc := &model4.ResourceLocationV2{
				ResourceType: constants.RESOURCE_TYPE_DEPENDENCY,
				StorageType:  resourceConfigItem.ResourceLoc.StorageType,
				Param:        resourceConfigItem.ResourceLoc.Param,
			}
			resourceLoc := &sql_model.ResourceLocationForSqlServer{
				SerialId:           fmt.Sprintf("%d-%d", resourceConfigItem.Id, resourceConfigItem.VersionId),
				ResourceLocationV2: *loc,
			}
			resourceLocs = append(resourceLocs, resourceLoc)
		}
		req[constants.SQL_FILE_PATH_KEY] = resourceConfigItem.ResourceName
		sqlCheckRef := &table.SqlCheckRef{
			SqlCheckId: c.Req.SqlCheckId,
			ResourceId: resourceConfigItem.Id,
			CreateTime: util.GetCurrentTime(),
			UpdateTime: util.GetCurrentTime(),
			Type:       constants.RESOURCE_TYPE_DEPENDENCY,
			Version:    constants.META_TABLE_VERSION_DEFAULT,
			Status:     constants.REGION_STATUS_ACTIVE,
		}
		err = service.SaveSqlCheckRef(sqlCheckRef)
		if err != nil {
			return
		}
	} else {
		req["sql"] = string(sqlCode)
		req[constants.SQL_FILE_PATH_KEY] = ""
	}

	req["flinkClientVersion"] = c.flinkVersion
	req["jobSerialId"] = c.Req.JobId
	cam, err := GetCam(c.Req.ClusterId)
	if err != nil {
		return
	}
	catalogs, err := metadata.BuildMetaCatalogs(exCatalogs)
	if err != nil {
		return
	}
	req["cam"] = cam
	req["resoureceLocs"] = resourceLocs
	req["metadata"] = metadata_model.Metadata{
		ReferenceTables: metadata.BuildMetaTables(metaTables),
		Catalogs:        catalogs,
		Variables:       variables,
	}
	req["sqlCheckId"] = c.Req.SqlCheckId
	req["parallelism"] = c.Req.Parallelism
	req["shouldReturnJobGraph"] = c.Req.ShouldReturnJobGraph
	req["deepSqlCheckEnable"] = c.Req.DeepSqlCheckEnable
	return
}

func (c *CheckSqlGrammarService) buildDescribeSqlCheckResultReq() (req map[string]interface{}, err error) {
	req = make(map[string]interface{})
	resource, _ := GetResourceBySqlCheckId(c.Req.SqlCheckId)
	resourceName := ""
	if resource != nil {
		req["sql"] = ""
		resourceName = resource.ResourceName
	} else {
		sqlCode, err := base64.StdEncoding.DecodeString(c.Req.SqlCode)
		if err != nil {
			return nil, errorcode.NewStackError(errorcode.InvalidParameterCode, "", err)
		}
		req["sql"] = string(sqlCode)
	}

	req["flinkClientVersion"] = c.flinkVersion
	req["sqlCheckId"] = c.Req.SqlCheckId
	req["metadata"] = metadata_model.Metadata{}
	req["jobSerialId"] = c.Req.JobId
	req["parallelism"] = c.Req.Parallelism
	req["shouldReturnJobGraph"] = c.Req.ShouldReturnJobGraph
	req["deepSqlCheckEnable"] = c.Req.DeepSqlCheckEnable
	req[constants.SQL_FILE_PATH_KEY] = resourceName
	return
}

func (c *CheckSqlGrammarService) buildAttachVariablesReq(
	metaTables []*metadata_model.ReferenceTableWithResource, exCatalogs []*metadata_model.ExternalCatalogV1) (req map[string]interface{}, err error) {

	variables, err := c.BuildMetaTableVariables() // from request
	if err != nil {
		return nil, err
	}
	c.Variables = variables
	req = make(map[string]interface{})
	req["sqlCode"] = c.Req.SqlCode
	req["flinkClientVersion"] = c.flinkVersion
	catalogs, err := metadata.BuildMetaCatalogs(exCatalogs)
	if err != nil {
		return
	}
	req["metadata"] = metadata_model.Metadata{
		ReferenceTables: metadata.BuildMetaTables(metaTables),
		Catalogs:        catalogs,
		Variables:       variables,
	}
	return
}

func (c *CheckSqlGrammarService) BuildMetaTableVariables() (variables []*metadata_model.Variable, err error) {
	variables = make([]*metadata_model.Variable, 0)
	metaTableVariables := c.Req.MetaTableVariables
	systemConf := GetSystemVariables(c.Req.JobId, c.Req.ClusterId)
	if len(metaTableVariables) > 0 {
		for _, metaTableVariable := range metaTableVariables {

			variableEntries := make([]*metadata_model.VariableEntry, 0)
			for _, entry := range metaTableVariable.VariableEntries {
				if entry.VariableId != "" { // 处理全局变量
					if systemConf != nil && service3.StrArrContains(constants.SYSTEM_GLOBAL_VARIABLE_ARRAY, entry.VariableId) { // 处理系统变量
						if _, exists := systemConf[entry.VariableId]; exists {
							logger.Errorf("The map is not empty and contains the key: %s", entry.VariableId)
						} else {
							logger.Errorf("The map is not empty, but does not contain the key: %s", entry.VariableId)
							continue
						}
						logger.Errorf(" #####11 systemVariable Placeholder: %+v, VariableId: %+v ", entry.Placeholder, entry.VariableId)
						entry.Value = systemConf[entry.VariableId]
						logger.Errorf(" #####11 systemVariable Value: %+v ", entry.Value)
					} else {
						globalVariable, err := variable.GetVariableBySerialId(entry.VariableId, c.Req.Uin, c.Req.AppId, c.Req.Region, 0)
						if err != nil {
							return nil, err
						}
						if globalVariable == nil {
							entry.Value = ""
						} else {
							entry.Value = globalVariable.Value
						}

					}
				}
				variableEntries = append(variableEntries, &metadata_model.VariableEntry{
					Key:   entry.Key,
					Value: entry.Value,
				})
			}
			variables = append(variables, &metadata_model.Variable{
				Catalog:         metaTableVariable.Catalog,
				Database:        metaTableVariable.Database,
				Table:           metaTableVariable.Table,
				Type:            metaTableVariable.Type,
				VariableEntries: variableEntries,
			})
		}
	}
	return variables, nil
}

type ResourceLocation struct {
	SerialId string
	model_resource_config.ResourceLocationV2
}

func GetResourceLoc(resourceRefs []*model.ResourceRefItem) ([]*sql_model.ResourceLocationForSqlServer, error) {
	refs, err := service.UpdateRefs(resourceRefs)
	if err != nil {
		bytes, _ := json.Marshal(resourceRefs)
		logger.Errorf("Failed to update resource ref %s error:%+v", string(bytes), err)
		return nil, err
	}

	resourceLocs := make([]*sql_model.ResourceLocationForSqlServer, 0, 0)
	for _, v := range refs {
		resource, err := service.GetResourceByResourceRef(v)
		if err != nil {
			return resourceLocs, err
		}
		resourceConfig, err := service.GetResourceConfigByResourceRef(v)
		if err != nil {
			return resourceLocs, err
		}
		if resourceConfig.Status != constants.RESOURCE_CONF_STATUS_ACTIVE {
			return resourceLocs, errors.New("resourceConfig status not active")
		}
		loc := &model4.ResourceLocationV2{}
		err = json.Unmarshal([]byte(resourceConfig.ResourceLoc), loc)
		if err != nil {
			return resourceLocs, err
		}
		resourceLoc := &sql_model.ResourceLocationForSqlServer{
			SerialId:           fmt.Sprintf("%d-%d", resource.Id, resourceConfig.VersionId),
			ResourceLocationV2: *loc,
		}
		resourceLocs = append(resourceLocs, resourceLoc)
	}
	return resourceLocs, nil
}

func GetResourceLocIntegral(resourceId string, versionId int64, resourceType int) (*sql_model.ResourceLocationIntegral, error) {
	resourceRefs := make([]*model.ResourceRefItem, 0)
	resourceRef := &model.ResourceRefItem{
		ResourceId: resourceId,
		Version:    versionId,
		Type:       resourceType,
	}
	resourceRefs = append(resourceRefs, resourceRef)
	refs, err := service.UpdateRefs(resourceRefs)
	if err != nil {
		bytes, _ := json.Marshal(resourceRefs)
		logger.Errorf("Failed to update resource ref %s error:%+v", string(bytes), err)
		return nil, err
	}

	resourceLoc := &sql_model.ResourceLocationIntegral{}
	for _, v := range refs {
		resource, err := service.GetResourceByResourceRef(v)
		if err != nil {
			return resourceLoc, err
		}
		resourceConfig, err := service.GetResourceConfigByResourceRef(v)
		if err != nil {
			return resourceLoc, err
		}
		if resourceConfig.Status != constants.RESOURCE_CONF_STATUS_ACTIVE {
			return resourceLoc, errors.New("resourceConfig status not active")
		}
		loc := &model4.ResourceLocationV2{}
		err = json.Unmarshal([]byte(resourceConfig.ResourceLoc), loc)
		if err != nil {
			return resourceLoc, err
		}
		resourceLoc = &sql_model.ResourceLocationIntegral{
			ResourceSerialId:   resource.ResourceId,
			ResourceId:         resource.Id,
			VersionId:          resourceRef.Version,
			ResourceLocationV2: *loc,
		}

	}
	return resourceLoc, nil
}

func parseRspToApiV3Rsp(rspData []byte, requestId string) (apiv3Rsp *sql.CheckSqlGrammarRsp, err error) {
	type Rsp struct {
		Pass        bool   `json:"pass"`
		Message     string `json:"errorMessage"`
		StartRow    uint64 `json:"errStartRow"`
		StartColumn uint64 `json:"errStartColumn"`
		EndRow      uint64 `json:"errEndRow"`
		EndColumn   uint64 `json:"errEndColumn"`
	}

	rsp := &Rsp{}
	if err = json.Unmarshal(rspData, rsp); err != nil {
		logger.Infof("[%s]: sql rsp [ %s ] Unmarshal fail: %+v", requestId, string(rspData), err)
		return
	}
	apiv3Rsp = &sql.CheckSqlGrammarRsp{
		Pass:         rsp.Pass,
		ErrorMessage: rsp.Message,
		ErrorCoordinate: sql.Coordinate{
			StartRow:    rsp.StartRow,
			StartColumn: rsp.StartColumn,
			EndRow:      rsp.EndRow,
			EndColumn:   rsp.EndColumn,
		},
		Done: true,
	}
	return apiv3Rsp, nil
}

func (c *CheckSqlGrammarService) isFlinkVersionSupported() bool {
	_, ok := supportFlinkVersion[c.flinkVersion]
	return ok
}

func (c *CheckSqlGrammarService) ParseSql(ctx context.Context) (rsp *sql_model.ParseSqlRsp, err error) {
	return c.parseSqlService.ParseSql(ctx, c.Req.SqlCode, c.Req.Region, 13)
}
