package sql

import (
	"context"

	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/metadata"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/variable"
)

type DoDescribeMetaReferencesService struct {
	cam interface{}
	FlinkVersionService
}

func (service *DoDescribeMetaReferencesService) DoDescribeMetaReferences(req *model.DescribeMetaReferencesReq, evenId int64) (string, string, *model.DescribeMetaReferencesRsp) {

	//鉴权
	itemSpaceId, err := auth.InnerAuthJob(req.WorkSpaceId, req.IsSupOwner, req.JobId, req.AppId, req.SubAccountUin, req.Region, req.Action)
	if err != nil {
		logger.Errorf("%s: User owner uin %s is NOT authenticated, refuse to serve", req.RequestId, req.Uin)
		errCode := errorcode.GetCode(err)
		return errCode.GetCodeStr(), errCode.GetCodeDesc(), nil
	}

	rsp := &model.DescribeMetaReferencesRsp{Pass: false}

	flinkVersion, err := service.GetFlinkVersion(req.AppId, req.Region, req.ClusterId, req.JobId, req.FlinkVersion)
	if err != nil {
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), rsp
	}

	// 1. parse sql [sql-parser]
	parser := NewParseSqlService(req.RequestId, req.Uin, req.AppId, req.Region, flinkVersion, req.ClusterId, itemSpaceId)
	sqlRsp, err := parser.ParseSql(context.Background(), req.SqlCode, req.Region, 13)
	if err != nil {
		rsp.ErrorMessage = err.Error()
		return controller.OK, controller.NULL, rsp
	}
	if !sqlRsp.Pass {
		rsp = &model.DescribeMetaReferencesRsp{
			Pass:            false,
			ErrorMessage:    sqlRsp.ErrorMessage,
			ErrorCoordinate: sqlRsp.ErrorCoordinate,
		}
		return controller.OK, controller.NULL, rsp
	}
	// 2. 获取元数据表
	metaTables, _, err := parser.GetMetaTables(context.Background(), sqlRsp.MetaRefs, itemSpaceId, true)
	if err != nil {
		rsp.ErrorMessage = err.Error()
		return controller.OK, controller.NULL, rsp
	}
	// 3. 获取元数据表定义的变量
	references, variables, err := parser.GetMetaReferences(sqlRsp.MetaRefs, metaTables, req.Uin, req.AppId, req.Region, itemSpaceId)
	if err != nil {
		return controller.FailedOperation_ParseSql, err.Error(), nil
	}
	// 4、替换为全局变量
	for _, tableVariable := range variables {
		for _, tableVariableEntry := range tableVariable.VariableEntries {
			tVariable, exists, err := variable.GetVariableByName(tableVariableEntry.Placeholder, req.Uin, req.AppId, req.Region, itemSpaceId)
			if err != nil {
				return controller.FailedOperation_ParseSql, err.Error(), nil
			}
			if exists {
				tableVariableEntry.Value = ""
				tableVariableEntry.VariableId = tVariable.SerialId
			}
		}
	}
	rsp = &model.DescribeMetaReferencesRsp{
		Pass:            sqlRsp.Pass,
		ErrorMessage:    sqlRsp.ErrorMessage,
		ErrorCoordinate: sqlRsp.ErrorCoordinate,
		ReferenceTables: references,
		Variables:       variables,
	}
	return controller.OK, controller.NULL, rsp
}
