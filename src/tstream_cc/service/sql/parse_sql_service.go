package sql

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"regexp"
	"sort"
	"strings"
	"sync"
	"tencentcloud.com/tstream_galileo/src/common/uuid"

	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/component"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"
	job_config_model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_config"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/metadata"
	model3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/resource_config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/sql"
	metadata_model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/metadata"
	sql_model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/sql"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/resource"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/item_space"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/metadata"
	metadata_service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/metadata"
	resource_service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource"
	service4 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/variable"
)

const (
	ConfFile          = "galileo_cc.properties"
	VariablePatten    = "\\$\\{(.*?)}:(.+)"
	NewVariablePatten = "\\$\\{([^}:]+):([^}]+)}"
	// 仅匹配无默认值形式 ${var}，排除包含冒号的情形，避免与新/老格式重复
	NoDefaultVariablePatten  = "\\$\\{([^}:]+)}"
	ReferenceDefaultCatalog  = "_default"
	ReferenceDefaultDatabase = "_default"
)

type ParseSqlService struct {
	RequestId            string
	Uin                  string
	AppId                int64
	Region               string
	FlinkVersion         string
	ClusterGroupSerialId string
	ItemSpaceId          int64
}

func NewParseSqlService(RequestId string, Uin string, appId int64, region string, flinkVersion string, clusterGroupSerialId string, ItemSpaceId int64) *ParseSqlService {

	return &ParseSqlService{
		FlinkVersion:         flinkVersion,
		Region:               region,
		AppId:                appId,
		Uin:                  Uin,
		RequestId:            RequestId,
		ClusterGroupSerialId: clusterGroupSerialId,
		ItemSpaceId:          ItemSpaceId,
	}
}

func (s *ParseSqlService) DoParseSql(ctx context.Context,
	req *sql_model.ParseSqlReq, evenId int64) (string, string, *sql_model.ParseSqlRsp) {
	rsp, err := s.ParseSql(ctx, req.SqlCode, req.Region, evenId)
	if err != nil {
		rsp := &sql_model.ParseSqlRsp{}
		rsp.ErrorMessage = err.Error()
		return controller.InternalError, controller.NULL, rsp
	}
	return controller.OK, controller.NULL, rsp
}

type ParseSqlRspV1 struct {
	Pass         bool               `json:"pass"`
	ErrorMessage string             `json:"errorMessage"`
	StartRow     uint64             `json:"errStartRow"`
	StartColumn  uint64             `json:"errStartColumn"`
	EndRow       uint64             `json:"errEndRow"`
	EndColumn    uint64             `json:"errEndColumn"`
	MetaRefs     sql_model.MetaRefs `json:"metaRefs"`
}

type AttachVariablesRspV1 struct {
	Pass         bool   `json:"pass"`
	SqlCode      string `json:"sqlCode"`
	ErrorMessage string `json:"errorMessage"`
	StartRow     uint64 `json:"errStartRow"`
	StartColumn  uint64 `json:"errStartColumn"`
	EndRow       uint64 `json:"errEndRow"`
	EndColumn    uint64 `json:"errEndColumn"`
}

func (s *ParseSqlService) ParseSql(ctx context.Context,
	sqlCode string, region string, evenId int64) (rsp *sql_model.ParseSqlRsp, err error) {
	parseSqlReq := s.buildParseSqlReq(sqlCode)

	helper := component.NewTstreamSqlParserHelper(region)
	parsed, err := helper.ParseSql(ctx, parseSqlReq, 13)
	if err != nil {
		logger.Errorf("[%s]: failed to parse sql: %s, error: %+v", s.RequestId, sqlCode, err)
		return nil, err
	}
	parseSqlRspV1 := &ParseSqlRspV1{}
	if err = json.Unmarshal(parsed, parseSqlRspV1); err != nil {
		logger.Errorf("[%s]: tstream sql rsp [ %s ] Unmarshal fail: %+v", s.RequestId, string(parsed), err)
		return nil, err
	}
	tmpTalbes := make(map[string]int, 0)
	tmpLikeTalbes := make(map[string]map[string]string, 0)
	for _, v := range parseSqlRspV1.MetaRefs.TemporaryTables {
		if len(v.LikeSource) == 0 {
			tmpTalbes[strings.Trim(strings.Join([]string{v.Catalog, v.Database, v.Name}, "."), ".")] = 1
		} else {
			if len(v.Properties) > 0 {
				propertyMap := make(map[string]string)
				err := json.Unmarshal([]byte(v.Properties), &propertyMap)
				if err != nil {
					logger.Error("parse property failed")
					return nil, errorcode.FailedOperationCode.ReplaceDesc("parse property failed")
				}
				tmpLikeTalbes[v.LikeSource] = propertyMap
			}
		}
	}

	for tableName, proper := range tmpLikeTalbes {
		if _, ok := tmpTalbes[tableName]; !ok {

			if proper != nil {
				originConnectorType, _ := s.GetMetaTablConnector(ctx, tableName)
				currentConnectorType := proper["connector"]
				logger.Infof("###originConnectorType form metaTable: %s, currentConnectorType: %s", originConnectorType, currentConnectorType)
				if len(currentConnectorType) > 0 && currentConnectorType != originConnectorType {
					sprintf := fmt.Sprintf("can't replace connector %s to %s, with like tableName %s ", originConnectorType, currentConnectorType, tableName)
					logger.Error(sprintf)
					return nil, errorcode.FailedOperationCode_ModifyMetaConnector.ReplaceDesc(sprintf)
				}
			}
		}
	}
	rsp = &sql_model.ParseSqlRsp{
		ErrorMessage: parseSqlRspV1.ErrorMessage,
		Pass:         parseSqlRspV1.Pass,
		ErrorCoordinate: sql.Coordinate{
			StartRow:    parseSqlRspV1.StartRow,
			EndRow:      parseSqlRspV1.EndRow,
			StartColumn: parseSqlRspV1.StartColumn,
			EndColumn:   parseSqlRspV1.EndColumn,
		},
		MetaRefs: parseSqlRspV1.MetaRefs,
	}
	return rsp, nil
}

func (s *ParseSqlService) ParseAndValidateSql(ctx context.Context,
	parseSqlReq map[string]interface{}, region string) (rsp *sql_model.ParseSqlRsp, err error) {
	helper := component.NewTstreamSqlParserHelper(region)
	parsed, err := helper.ParseAndValidateSql(ctx, parseSqlReq, constants.PARSE_AND_VALIDATE_EVENTID)
	if err != nil {
		logger.Errorf("[%s]: failed to parseAndValidate sql: %+v", s.RequestId, err)
		return nil, err
	}
	parseSqlRspV1 := &ParseSqlRspV1{}
	if err = json.Unmarshal(parsed, parseSqlRspV1); err != nil {
		logger.Errorf("[%s]: tstream sql rsp [ %s ] Unmarshal fail: %+v", s.RequestId, string(parsed), err)
		return nil, err
	}

	rsp = &sql_model.ParseSqlRsp{
		ErrorMessage: parseSqlRspV1.ErrorMessage,
		Pass:         parseSqlRspV1.Pass,
		ErrorCoordinate: sql.Coordinate{
			StartRow:    parseSqlRspV1.StartRow,
			EndRow:      parseSqlRspV1.EndRow,
			StartColumn: parseSqlRspV1.StartColumn,
			EndColumn:   parseSqlRspV1.EndColumn,
		},
	}
	return rsp, nil
}

func (s *ParseSqlService) buildParseSqlReq(sqlCode string) (request map[string]interface{}) {
	request = make(map[string]interface{})
	request["sqlCode"] = sqlCode
	request["flinkClientVersion"] = s.FlinkVersion
	return request
}

func (s *ParseSqlService) AttachVariables(ctx context.Context, reqData map[string]interface{},
	region string, evenId int64) (rsp *sql_model.AttachVariablesRsp, err error) {
	helper := component.NewTstreamSqlParserHelper(region)
	parsed, err := helper.AttachVariables(ctx, reqData, 14)
	if err != nil {
		logger.Errorf("[%s]: failed to attach variables %+v", s.RequestId, err)
		return nil, err
	}
	attachVariablesRspV1 := &AttachVariablesRspV1{}
	if err = json.Unmarshal(parsed, attachVariablesRspV1); err != nil {
		logger.Errorf("[%s]: tstream sql rsp [ %s ] Unmarshal fail: %+v", s.RequestId, string(parsed), err)
		return nil, err
	}
	rsp = &sql_model.AttachVariablesRsp{
		ErrorMessage: attachVariablesRspV1.ErrorMessage,
		Pass:         attachVariablesRspV1.Pass,
		SqlCode:      attachVariablesRspV1.SqlCode,
		ErrorCoordinate: sql.Coordinate{
			StartRow:    attachVariablesRspV1.StartRow,
			EndRow:      attachVariablesRspV1.EndRow,
			StartColumn: attachVariablesRspV1.StartColumn,
			EndColumn:   attachVariablesRspV1.EndColumn,
		},
	}
	return rsp, nil
}

func (s *ParseSqlService) buildAttachVariablesReq(sqlCode string, metadata string) (request map[string]interface{}) {
	request = make(map[string]interface{})
	request["sqlCode"] = sqlCode
	request["flinkClientVersion"] = s.FlinkVersion
	request["metadata"] = metadata
	return request
}

func GetTableIdentifier(catalog string, database string, table string) string {
	return catalog + "." + database + "." + table
}

func (s *ParseSqlService) GetMetaReferences(metaRefs sql_model.MetaRefs,
	referenceTables []*metadata_model.ReferenceTableWithResource,
	uin string, appId int64, region string, itemSpaceId int64) (
	metaReferenceTables []*model.MetaReferenceTable, metaTableVariables []*model.MetaTableVariable, err error) {

	metaTableVariables = make([]*model.MetaTableVariable, 0)
	metaReferenceTables = make([]*model.MetaReferenceTable, 0)
	tableSet := make(map[string]bool, 0)

	for _, referenceTable := range referenceTables {
		mTable, err := metadata_service.GetMetaTableByName(referenceTable.Catalog, referenceTable.Database, referenceTable.Table, uin, appId, region, itemSpaceId)
		if err != nil {
			return nil, nil, err
		}
		identifier := GetTableIdentifier(referenceTable.Catalog, referenceTable.Database, referenceTable.Table)
		if _, ok := tableSet[identifier]; !ok {
			tableSet[identifier] = true
			metaReferenceTables = append(metaReferenceTables, &model.MetaReferenceTable{
				Catalog:  referenceTable.Catalog,
				Database: referenceTable.Database,
				Table:    referenceTable.Table,
				Version:  mTable.Version,
			})
			tableVariable, err := s.getMetaVariable(referenceTable.MetastoreTableEntity.Properties,
				referenceTable.Catalog, referenceTable.Database, referenceTable.Table)
			if err != nil {
				return nil, nil, err
			}
			if len(tableVariable.VariableEntries) > 0 {
				tableVariable.Type = constants.METADATA_TABLE_VARIABLE_TYPE_META_TABLE
				metaTableVariables = append(metaTableVariables, tableVariable)
			}
		}
	}
	for _, temporaryTable := range metaRefs.TemporaryTables {
		identifier := GetTableIdentifier(temporaryTable.Catalog, temporaryTable.Database, temporaryTable.Name)
		if _, ok := tableSet[identifier]; !ok {
			tableSet[identifier] = true
			tableVariable, err := s.getMetaVariable(temporaryTable.Properties, ReferenceDefaultCatalog, ReferenceDefaultDatabase, temporaryTable.Name)
			if err != nil {
				return nil, nil, err
			}
			if len(tableVariable.VariableEntries) > 0 {
				tableVariable.Type = constants.METADATA_TABLE_VARIABLE_TYPE_TEMPORAL_TABLE
				metaTableVariables = append(metaTableVariables, tableVariable)
			}
		}
	}
	for _, createCatalog := range metaRefs.CreateCatalogs {
		catalogVariable, err := s.getMetaVariable(createCatalog.Properties, createCatalog.Catalog, "", "")
		if err != nil {
			return nil, nil, err
		}
		if len(catalogVariable.VariableEntries) > 0 {
			catalogVariable.Type = constants.METADATA_TABLE_VARIABLE_TYPE_CDAS
			metaTableVariables = append(metaTableVariables, catalogVariable)
		}
	}
	getDatabase := func(catalogDatabase []string) string {
		if len(catalogDatabase) > 1 {
			return catalogDatabase[1]
		}
		return ""
	}
	for _, createDatabaseAsStmt := range metaRefs.CreateDatabaseAsStmts {
		srcCatalogDatabase := strings.Split(createDatabaseAsStmt.SourceDatabase, ".")
		tgtCatalogDatabase := strings.Split(createDatabaseAsStmt.SinkDatabase, ".")
		srcVariable, err := s.getMetaVariable(createDatabaseAsStmt.SourceProperties, srcCatalogDatabase[0], getDatabase(srcCatalogDatabase), "")
		if err != nil {
			return nil, nil, err
		}
		if len(srcVariable.VariableEntries) > 0 {
			srcVariable.Type = constants.METADATA_TABLE_VARIABLE_TYPE_CDAS
			metaTableVariables = append(metaTableVariables, srcVariable)
		}
		tgtVariable, err := s.getMetaVariable(createDatabaseAsStmt.SinkProperties, tgtCatalogDatabase[0], getDatabase(tgtCatalogDatabase), "")
		if err != nil {
			return nil, nil, err
		}
		if len(tgtVariable.VariableEntries) > 0 {
			tgtVariable.Type = constants.METADATA_TABLE_VARIABLE_TYPE_CDAS
			metaTableVariables = append(metaTableVariables, tgtVariable)
		}
	}
	sort.Slice(metaTableVariables, func(i, j int) bool {
		return metaTableVariables[i].Catalog+metaTableVariables[i].Database+metaTableVariables[i].Table >
			metaTableVariables[j].Catalog+metaTableVariables[j].Database+metaTableVariables[j].Table
	})
	return metaReferenceTables, metaTableVariables, nil
}

func (s *ParseSqlService) getMetaVariable(properties string, catalog string, database string, table string) (metaTableVariable *model.MetaTableVariable, err error) {
	// 定义三种正则
	rNew := regexp.MustCompile(NewVariablePatten)
	rOld := regexp.MustCompile(VariablePatten)
	rNoDefault := regexp.MustCompile(NoDefaultVariablePatten)

	metaTableVariableEntries := make([]*model.MetaTableVariableEntry, 0)
	if properties != "" {
		propertyMap := make(map[string]string)
		err := json.Unmarshal([]byte(properties), &propertyMap)
		if err != nil {
			errMsg := fmt.Sprintf("[%s]: Failed to Unmarshal properties:%s", s.RequestId, properties)
			logger.Errorf(errMsg)
			return nil, errors.New(errMsg)
		}
		for key, value := range propertyMap {
			// 在同一 value 中同时支持多种占位格式：
			// 1) ${var:default}（新格式） 2) ${var}:default（老格式） 3) ${var}（无默认值）
			// 处理顺序：新格式 -> 老格式 -> 无默认值；每一步先收集，再从剩余串中移除，避免重复匹配

			remaining := value

			// 1. 新格式 ${变量名:默认值}
			if matches := rNew.FindAllStringSubmatch(remaining, -1); len(matches) > 0 {
				for _, m := range matches {
					metaTableVariableEntries = append(metaTableVariableEntries, &model.MetaTableVariableEntry{
						Key:         key,
						Placeholder: m[1],
						Value:       m[2],
					})
				}
				remaining = rNew.ReplaceAllString(remaining, "")
			}

			// 2. 老格式 ${变量名}:默认值
			if matches := rOld.FindAllStringSubmatch(remaining, -1); len(matches) > 0 {
				for _, m := range matches {
					metaTableVariableEntries = append(metaTableVariableEntries, &model.MetaTableVariableEntry{
						Key:         key,
						Placeholder: m[1],
						Value:       m[2],
					})
				}
				remaining = rOld.ReplaceAllString(remaining, "")
			}

			// 3. 兜底格式 ${变量名}
			if matches := rNoDefault.FindAllStringSubmatch(remaining, -1); len(matches) > 0 {
				for _, m := range matches {
					metaTableVariableEntries = append(metaTableVariableEntries, &model.MetaTableVariableEntry{
						Key:         key,
						Placeholder: m[1],
						Value:       "",
					})
				}
			}
		}
	}
	metaTableVariable = &model.MetaTableVariable{
		Catalog:         catalog,
		Database:        database,
		Table:           table,
		VariableEntries: metaTableVariableEntries,
	}
	return metaTableVariable, nil
}

// get resource from meta tables and temporary tables
func (s *ParseSqlService) GetResourceRefsFromMeta(
	metaTables []*metadata_model.ReferenceTableWithResource,
	parsedMetaRefs sql_model.MetaRefs,
	excludeConnectors []string,
	exCatalogs []*metadata_model.ExternalCatalogV1) (metaRefs []*sql_model.ResourceLocationIntegral, err error) {
	resourceLocations := make([]*sql_model.ResourceLocationIntegral, 0)
	excludeConnectorSet := make(map[string]bool)
	for _, connector := range excludeConnectors {
		excludeConnectorSet[connector] = true
	}
	properties := make([]string, 0)
	refSet := make(map[string]bool)
	for _, metaTable := range metaTables { // find connector from meta table.
		refs, err := metadata.GetMetaConnectorRef(metaTable.ResourceId, metaTable.VersionId,
			metaTable.MetastoreTableEntity.Properties)
		if err != nil {
			return nil, err
		}
		properties = append(properties, metaTable.MetastoreTableEntity.Properties)
		for _, ref := range refs {
			if ref != nil && ref.VersionId != constants.METADATA_TABLE_RESOURCE_REF_NO_NEED_REFERENCE_VERSION {
				if _, ok := refSet[ref.ResourceSerialId]; !ok {
					resourceLocations = append(resourceLocations, ref)
					refSet[ref.ResourceSerialId] = true
				}
			}
		}
	}

	// find connector from temporary table.
	temporaryTables := parsedMetaRefs.TemporaryTables
	for _, temporaryTable := range temporaryTables {
		resourceLocations, err = s.getResourceLocationsFromConnectorProperties(
			temporaryTable.Properties, excludeConnectorSet, refSet, resourceLocations)
		if err != nil {
			logger.Warningf("Failed to getResourceLocationsFromConnectorProperties "+
				"from temporaryTable because %+v", err)
			return nil, err
		}
		properties = append(properties, temporaryTable.Properties)
	}

	// find connector from CREATE CATALOG statements
	catalogs := parsedMetaRefs.CreateCatalogs
	for _, catalog := range catalogs {
		resourceLocations, err = s.getResourceLocationsFromConnectorProperties(
			catalog.Properties, excludeConnectorSet, refSet, resourceLocations)
		if err != nil {
			logger.Warningf("Failed to getResourceLocationsFromConnectorProperties "+
				"from catalogs because %+v", err)
			return nil, err
		}
		properties = append(properties, catalog.Properties)
	}

	// // find connector from CDAS source and sink options.
	createDatabaseAsStmts := parsedMetaRefs.CreateDatabaseAsStmts
	for _, createDatabaseAsEntry := range createDatabaseAsStmts {
		resourceLocations, err = s.getResourceLocationsFromConnectorProperties(
			createDatabaseAsEntry.SourceProperties, excludeConnectorSet, refSet, resourceLocations)
		if err != nil {
			logger.Warningf("Failed to getResourceLocationsFromConnectorProperties "+
				"from SourceProperties because %+v", err)
			return nil, err
		}
		properties = append(properties, createDatabaseAsEntry.SourceProperties)
		resourceLocations, err = s.getResourceLocationsFromConnectorProperties(
			createDatabaseAsEntry.SinkProperties, excludeConnectorSet, refSet, resourceLocations)
		if err != nil {
			logger.Warningf("Failed to getResourceLocationsFromConnectorProperties "+
				"from SinkProperties because %+v", err)
			return nil, err
		}
		properties = append(properties, createDatabaseAsEntry.SinkProperties)
	}

	for _, catalog := range exCatalogs {
		refs, err := metadata.GetMetaCatalogRefs(catalog.Id)
		if err != nil {
			errMsg := fmt.Sprintf("[%s]: Failed to GetMetaCatalogExRefs.", s.RequestId)
			logger.Errorf(errMsg)
			return nil, errors.New(errMsg)
		}
		for _, ref := range refs {
			locs, err := resource_service.GetResourceLocByResourceId(ref.ResourceId, ref.VersionId)
			if err != nil {
				return nil, err
			}
			for _, loc := range locs {
				if _, ok := refSet[loc.ResourceSerialId]; !ok {
					resourceLocations = append(resourceLocations, &sql_model.ResourceLocationIntegral{
						ResourceSerialId: loc.ResourceSerialId,
						ResourceId:       loc.ResourceId,
						VersionId:        loc.VersionId,
						ResourceLocationV2: model3.ResourceLocationV2{
							StorageType:      loc.StorageType,
							Param:            loc.Param,
							LocalizationPath: metadata.GetExternalCatalogLocalizationPath(catalog.Id),
							ResourceType:     loc.ResourceType,
						},
					})
					refSet[loc.ResourceSerialId] = true
				}
			}
		}
		// exCatalogs reference tables
		for _, table := range catalog.ReferenceTables {
			refs, err := metadata.GetMetaConnectorRef(table.ResourceId, table.VersionId,
				table.MetastoreTableEntity.Properties)
			if err != nil {
				msg := fmt.Sprintf("[%s] Faild to find connector with table %s, option %v", s.RequestId, table.Table, table.MetastoreTableEntity.Properties)
				logger.Error(msg)
				return nil, errorcode.ResourceNotFoundCode_ConnectorNotFound.ReplaceDesc(msg)
			}
			for _, ref := range refs {
				if ref != nil && ref.VersionId != constants.METADATA_TABLE_RESOURCE_REF_NO_NEED_REFERENCE_VERSION {
					if _, ok := refSet[ref.ResourceSerialId]; !ok {
						resourceLocations = append(resourceLocations, ref)
						refSet[ref.ResourceSerialId] = true
					}
				}
			}
			properties = append(properties, table.MetastoreTableEntity.Properties)
		}
	}

	// 优先级：1、用户上传jar 2、用户自定义connector和用户替换的内置connector 3、oceanus系统内置connector
	connector, err := s.replaceUserDefinedConnector(excludeConnectorSet, resourceLocations, properties)
	if err != nil {
		return nil, err
	}

	return connector, nil
}

func (s *ParseSqlService) replaceUserDefinedConnector(excludeConnectorSet map[string]bool,
	resourceLocations []*sql_model.ResourceLocationIntegral, propertiesArr []string) (
	newResourceLocations []*sql_model.ResourceLocationIntegral, err error) {

	logger.Debugf("replaceUserDefinedConnector already match %d connector, resourceLocations: %+v", len(resourceLocations), resourceLocations)

	userConnectors, err := service4.GetUserConnector(s.AppId, s.ItemSpaceId)
	if err != nil {
		return nil, err
	}
	userConnectorMap := make(map[string]*table.Resource)
	for _, userConnector := range userConnectors {
		userConnectorMap[userConnector.Connector] = userConnector
	}

	userResourceLocationsMap := make(map[string]*sql_model.ResourceLocationIntegral)
	for _, properties := range propertiesArr {
		options := make(map[string]string)
		if len(properties) > 0 {
			err := json.Unmarshal([]byte(properties), &options)
			if err != nil {
				return nil, err
			}
		}
		connType, ok := options["connector"]
		if !ok {
			connType, ok = options["connector.type"]
		}
		if ok {
			value, exists := userConnectorMap[connType]
			if exists {
				config, err := service4.GetConnectorConfig(value.Id, 0)
				if err != nil {
					return nil, err
				}
				integral, err := GetResourceLocIntegral(value.ResourceId, config.VersionId, constants.RESOURCE_TYPE_JAR)
				if err != nil {
					return nil, err
				}
				userResourceLocationsMap[connType] = integral
			}
		}
	}

	// 未匹配到任何用户自定义connector
	if len(userResourceLocationsMap) <= 0 {
		logger.Debugf("replaceUserDefinedConnector match 0 user's connector")
		return resourceLocations, nil
	}
	logger.Debugf("replaceUserDefinedConnector match %d user's connector, userResourceLocationsMap: %+v", len(userResourceLocationsMap), userResourceLocationsMap)

	resourceLocationIntegral := make([]*sql_model.ResourceLocationIntegral, 0)
	resourceLocationsMap := make(map[int64]*sql_model.ResourceLocationIntegral)
	// 1.查询已经匹配的connector
	ids := make([]int64, 0)
	for _, v := range resourceLocations {
		ids = append(ids, v.ResourceId)
		resourceLocationsMap[v.ResourceId] = v
	}
	res := make([]*table.Resource, 0)
	if len(resourceLocations) > 0 {
		resource, err := service4.GetResourceByIds(ids)
		if err != nil {
			return nil, err
		}
		res = append(res, resource...)
	}
	// 2. 替换系统内置connector和增加用户自定义connector
	var match bool
	resourceId := make(map[int64]bool)
	for key, value := range userResourceLocationsMap {
		// 2.1 用户上传jar优先
		if _, ok := excludeConnectorSet[key]; !ok {
			for _, v := range res {
				// 2.2 用户自定义connector匹配优先,代替系统匹配
				delimiters := []string{",", "、"}
				var array []string
				str := v.Connector
				for _, delimiter := range delimiters {
					if strings.Contains(str, delimiter) {
						array = append(array, strings.Split(str, delimiter)...)
						break
					}
				}
				if len(array) == 0 {
					array = append(array, str)
				}
				for _, connector := range array {
					if connector == key {
						resourceLocationIntegral = append(resourceLocationIntegral, value)
						match = true
						resourceId[v.Id] = true
					}
				}
			}
			// 2.3 不在系统内置connector和上传jar，选择用户自定义connector
			if !match {
				resourceLocationIntegral = append(resourceLocationIntegral, value)
			}
		}
	}
	logger.Debugf("replaceUserDefinedConnector add %d user's connector, resourceLocationIntegral: %+v", len(resourceLocationIntegral), resourceLocationIntegral)

	// 3. 保留用户上传jar和系统内置
	for key, value := range resourceLocationsMap {
		if _, ok := resourceId[key]; !ok {
			resourceLocationIntegral = append(resourceLocationIntegral, value)
		}
	}

	logger.Debugf("replaceUserDefinedConnector get %d resourceLocationIntegral", len(resourceLocationIntegral))
	return resourceLocationIntegral, nil
}

func (s *ParseSqlService) getResourceLocationsFromConnectorProperties(
	properties string,
	excludeConnectorSet map[string]bool,
	refSet map[string]bool,
	resourceLocations []*sql_model.ResourceLocationIntegral,
) (newResourceLocations []*sql_model.ResourceLocationIntegral, err error) {

	// allow mismatched
	options := make(map[string]string, 0)
	if len(properties) > 0 {
		err := json.Unmarshal([]byte(properties), &options)
		if err != nil {
			return nil, err
		}
	}
	connType, ok := options["connector"]
	if !ok {
		connType, ok = options["connector.type"]
	}
	if ok {
		if _, ok := excludeConnectorSet[connType]; !ok {
			refs, _, mismatched, err := metadata.GetSystemConnectorRef(properties, s.FlinkVersion)
			for _, ref := range refs {
				if !mismatched && ref != nil && ref.ResourceId != constants.METADATA_TABLE_RESOURCE_REF_NO_NEED_REFERENCE_RESOURCE_ID {
					if _, ok := refSet[ref.ResourceSerialId]; !ok {
						resourceLocations = append(resourceLocations, ref)
						refSet[ref.ResourceSerialId] = true
					}
				}
				if err != nil {
					return nil, err
				}
			}
		}
	}
	return resourceLocations, nil
}

func GetMetaTableIdentify(table sql_model.MetaTableEntity) (identify string) {
	if table.Catalog != "" {
		return strings.Join([]string{table.Catalog, table.Database, table.Name}, ".")
	} else {
		return table.Name
	}
}

func (s *ParseSqlService) GetMetaTablConnector(ctx context.Context, metaRefTable string) (
	connectorType string, err error) {
	splits := strings.Split(metaRefTable, ".")
	var catalogName = ""
	if len(splits) == 3 {
		catalogName = splits[0]
		if catalogName == metadata.DefaultCatalogName { // 非临时表，首先从OceanusCatalog中查找
			metaTable, err := metadata.GetMetaTableWithResourceByIdentify(ctx, splits[0], splits[1], splits[2], s.Uin,
				s.AppId, s.Region, s.FlinkVersion, constants.META_TABLE_VERSION_LASTEST, 0)
			if err != nil {
				errMsg := fmt.Sprintf("Failed to find table : %s with flinkVersion %s, "+
					"Please check the matatable details about flinkVersion",
					metaRefTable, s.FlinkVersion)
				logger.Errorf(errMsg)
				return "", errors.New(errMsg)
			}
			return metaTable.TableType, nil
		}
	}
	return "", nil
}

type Task struct {
	ref          string
	catlogName   string
	databaseName string
	tableName    string
	Catalog      *metadata_model.ExternalCatalogV1
}

type metaTableResult struct {
	Task      Task
	MetaTable []*metadata_model.ReferenceTableWithResource
	Err       error
}

func (s *ParseSqlService) GetMetaTables(ctx context.Context, metaRefs sql_model.MetaRefs, itemSpaceId int64, skipExCatalogs bool) (
	inTables []*metadata_model.ReferenceTableWithResource, exCatalogs []*metadata_model.ExternalCatalogV1, err error) {

	inTables = make([]*metadata_model.ReferenceTableWithResource, 0)
	exCatalogs = make([]*metadata_model.ExternalCatalogV1, 0)
	temporaryTables := make(map[string]bool)
	exTableNames := make(map[string]bool, 0)
	exCatalogMap := make(map[string]*metadata_model.ExternalCatalogV1, 0)
	for _, tt := range metaRefs.TemporaryTables {
		identify := GetMetaTableIdentify(tt)
		temporaryTables[identify] = true
	}
	refs := append(metaRefs.SourceTables, metaRefs.TargetTables...)
	tasks := make([]Task, 0)

	for _, ref := range refs {
		splits := strings.Split(ref, ".")
		var catalogName = ""
		if len(splits) == 3 {
			catalogName = splits[0]
			if catalogName == metadata.DefaultCatalogName { // 非临时表，首先从OceanusCatalog中查找
				metaTable, err := metadata.GetMetaTableWithResourceByIdentify(context.Background(),
					splits[0], splits[1], splits[2], s.Uin,
					s.AppId, s.Region, s.FlinkVersion, constants.META_TABLE_VERSION_LASTEST, itemSpaceId)
				if err != nil {
					errMsg := fmt.Sprintf("Failed to find table : %s with flinkVersion %s, "+
						"Please check the matatable details about flinkVersion",
						ref, s.FlinkVersion)
					logger.Errorf(errMsg)
					return inTables, exCatalogs, errors.New(errMsg)
				}
				inTables = append(inTables, &metadata_model.ReferenceTableWithResource{
					Catalog:    splits[0],
					Database:   splits[1],
					Table:      splits[2],
					ResourceId: metaTable.ResourceId,
					VersionId:  metaTable.ResourceVersionId,
					MetastoreTableEntity: &metadata_model.MetastoreTableEntity{
						Id:          metaTable.Id,
						Name:        metaTable.Name,
						Catalog:     splits[0],
						Database:    splits[1],
						TableSchema: metaTable.TableSchema,
						Properties:  metaTable.Properties,
						Type:        metaTable.Type,
						TableType:   metaTable.TableType,
					},
				})
			} else {
				catalogName := splits[0]
				// try to load external catalog
				if _, ok := exTableNames[ref]; !ok {
					metaCatalog, err := metadata.GetMetaCatalog(catalogName, s.Uin, s.AppId, s.Region, itemSpaceId)
					if err != nil {
						errMsg := fmt.Sprintf("Failed to find catalog : %s ", catalogName)
						logger.Errorf(errMsg)
						return nil, exCatalogs, errors.New(errMsg)
					}
					if metaCatalog != nil { // find external catalog
						metaCatalogFlinkVersion := metaCatalog.FlinkVersion
						// 兼容历史metaCatalog 没有flinkVersion, 默认是Flink-1.13
						if metaCatalog.FlinkVersion == "" {
							metaCatalogFlinkVersion = constants.FLINK_VERSION_1_13
						}
						if metaCatalogFlinkVersion != s.FlinkVersion {
							errMsg := fmt.Sprintf("catalog: %s flinkVersion is %s, job flinkVersion is %s, flinkVersion is inconsistent.",
								ref, metaCatalogFlinkVersion, s.FlinkVersion)
							logger.Errorf(errMsg)
							return nil, exCatalogs, errors.New(errMsg)
						}
						catalog := &metadata_model.ExternalCatalogV1{
							Id:              metaCatalog.Id,
							Name:            catalogName,
							Type:            metaCatalog.Type,
							CatalogVersion:  metaCatalog.CatalogVersion,
							SerialId:        metaCatalog.SerialId,
							DefaultDatabase: metaCatalog.DefaultDatabase,
						}
						if metaCatalog.Type == constants.METADATA_CATALOG_TYPE_MYSQL {
							if metaCatalog.Properties != "" {
								mysqlCatalog := &model.MysqlCatalog{}
								err := json.Unmarshal([]byte(metaCatalog.Properties), mysqlCatalog)
								if err != nil {
									errMsg := fmt.Sprintf("Failed to Unmarshal mysql catalog, with errors:%+v", err)
									logger.Errorf(errMsg)
									return nil, exCatalogs, errors.New(errMsg)
								}
								pass, err := base64.StdEncoding.DecodeString(mysqlCatalog.Password)
								if err != nil {
									errMsg := fmt.Sprintf("decode password failed, with errors:%+v", err)
									logger.Errorf(errMsg)
									return nil, exCatalogs, errors.New(errMsg)
								}
								mysqlCatalog.Password = string(pass)
								catalog.Hostname = mysqlCatalog.Hostname
								catalog.Port = mysqlCatalog.Port
								catalog.Username = mysqlCatalog.Username
								catalog.Password = mysqlCatalog.Password
							}
						}
						var tableName = splits[2]
						task := Task{
							ref:          ref,
							catlogName:   catalogName,
							databaseName: splits[1],
							Catalog:      catalog,
							tableName:    splits[2], // 方便后续直接使用
						}
						tasks = append(tasks, task)
						if err != nil {
							errMsg := fmt.Sprintf("Failed to find ExternalMetaTable : %s ", tableName)
							logger.Errorf(errMsg)
							return nil, exCatalogs, errors.New(errMsg)
						}
					}
					exTableNames[ref] = true
				}
			}
		}
	}
	// 查询表变量可以跳过外部表
	if skipExCatalogs {
		return inTables, exCatalogs, nil
	}
	// 并发执行任务
	results := make(chan *metaTableResult, len(tasks))
	var wg sync.WaitGroup

	for _, t := range tasks {
		wg.Add(1)
		go func(t Task) {
			defer wg.Done()
			catalogName := t.catlogName
			databaseName := t.databaseName
			tableName := t.tableName
			ref := t.ref
			var metaTable []*metadata_model.ReferenceTableWithResource
			if _, ok := temporaryTables[ref]; ok {
				metaTable = make([]*metadata_model.ReferenceTableWithResource, 0)
			} else {
				// 调用远程接口，获取metaTable
				newRequestId := uuid.NewRandom().String()
				metaTable, err = s.GetExternalMetaTable(
					catalogName,
					databaseName,
					tableName,
					t.Catalog.SerialId,
					itemSpaceId,
					newRequestId,
				)
			}
			result := &metaTableResult{
				Task:      t,
				MetaTable: metaTable,
				Err:       err,
			}
			results <- result
		}(t)
	}

	// 等待所有任务完成并关闭结果通道
	wg.Wait()
	close(results)

	for res := range results {
		if res.Err != nil {
			errMsg := fmt.Sprintf("Failed to find ExternalMetaTable : %s ", res.Task.tableName)
			logger.Errorf("ExternalMetaTable, err:%+v", res.Err)
			logger.Errorf(errMsg)
			return nil, exCatalogs, errors.New(errMsg)
		}
		catalogName := res.Task.catlogName
		metaTable := res.MetaTable
		catalog := res.Task.Catalog
		if exCatalog, ok := exCatalogMap[catalogName]; ok {
			exCatalog.ReferenceTables = append(exCatalog.ReferenceTables, metaTable...)
		} else {
			catalog.ReferenceTables = append(catalog.ReferenceTables, metaTable...)
			exCatalogMap[catalogName] = catalog
		}
	}

	for _, v := range exCatalogMap {
		exCatalogs = append(exCatalogs, v)
	}
	return inTables, exCatalogs, nil
}

func (s *ParseSqlService) GetExternalMetaTable(catalogName string, databaseName string, tableName string, catalogSerialId string, itemSpaceId int64, newRequestId string) (
	table []*metadata_model.ReferenceTableWithResource, err error) {

	result := make([]*metadata_model.ReferenceTableWithResource, 0)
	itemSpace, err := item_space.GetItemSpaceByItemId(itemSpaceId)
	if err != nil {
		return nil, err
	}
	// 查询外部Catalog
	reqData := &model.DescribeExternalMetaTablesReq{RequestBase: apiv3.RequestBase{RequestId: newRequestId, Region: s.Region, AppId: s.AppId, Uin: s.Uin}, CatalogId: catalogSerialId,
		ClusterId: s.ClusterGroupSerialId, DatabaseName: databaseName, TableName: tableName, FlinkVersion: s.FlinkVersion, WorkSpaceId: itemSpace.SerialId}
	externalTable, err := (&metadata_service.DoDescribeMetaExternalTableService{Request: reqData}).DoDescribeMetaExternalTableInternal(reqData, 1)
	if err != nil {
		logger.Errorf("%s %v", reqData.RequestId, err)
		return nil, err
	}
	if len(externalTable.TablesSetItem) == 0 {
		return nil, errorcode.ResourceNotFoundCode_TableNotExist.ReplaceDesc("Failed to find ExternalMetaTable ")
	}

	// 查询 Table ResourceReference
	refs, _, mismatched, err := metadata_service.GetSystemConnectorRef(externalTable.TablesSetItem[0].TableProperties, s.FlinkVersion)
	if err != nil {
		logger.Errorf("Failed to get system connector, with errors:%+v", err)
		return nil, err
	}
	if mismatched {
		msg := fmt.Sprintf("Failed to get system connector, with options:%s", externalTable.TablesSetItem[0].TableProperties)
		logger.Error(msg)
		return nil, errorcode.ResourceNotFoundCode_SpecNotFound.ReplaceDesc("Failed to get system connector")
	}
	// 组装属性
	for _, ref := range refs {
		referenceTableWithResource := &metadata_model.ReferenceTableWithResource{
			Catalog:  catalogName,
			Database: databaseName,
			Table:    tableName,
		}
		referenceTableWithResource.ResourceId = ref.ResourceId
		referenceTableWithResource.VersionId = ref.VersionId
		referenceTableWithResource.MetastoreTableEntity = &metadata_model.MetastoreTableEntity{
			Properties: externalTable.TablesSetItem[0].TableProperties,
		}
		result = append(result, referenceTableWithResource)
	}

	return result, nil
}
func (s *ParseSqlService) DescribeTableLineage(ctx context.Context,
	sqlCode string, region string, evenId int64) (rsp *sql_model.DescribeTableLineageRspV1, err error) {
	describeTableLineageReq := s.buildDescribeTableLineageReq(sqlCode)
	helper := component.NewTstreamSqlParserHelper(region)
	parsed, err := helper.DescribeTableLineage(ctx, describeTableLineageReq, 14)
	if err != nil {
		return nil, err
	}
	describeTableLineageRspV1 := &sql_model.DescribeTableLineageRspV1{}
	if err = json.Unmarshal(parsed, describeTableLineageRspV1); err != nil {
		logger.Infof("[%s]: tstream sql rsp [ %s ] Unmarshal fail: %+v", s.RequestId, string(parsed), err)
		return nil, err
	}
	return describeTableLineageRspV1, nil
}

func (s *ParseSqlService) buildDescribeTableLineageReq(sqlCode string) (request map[string]interface{}) {
	request = make(map[string]interface{})
	request["sqlCode"] = sqlCode
	request["flinkClientVersion"] = s.FlinkVersion
	return request
}

func CheckIfUsedTempTableVariable(metaTableVariables []*model.MetaTableVariable) (used bool) {
	for _, tableVariable := range metaTableVariables {
		if tableVariable.Type == constants.METADATA_TABLE_VARIABLE_TYPE_TEMPORAL_TABLE ||
			tableVariable.Type == constants.METADATA_TABLE_VARIABLE_TYPE_CDAS {
			return true
		}
	}
	return false
}

func Transform2Variables(metaTableVariables []*metadata_model.Variable) []*model.MetaTableVariable {
	variables := make([]*model.MetaTableVariable, 0)
	for _, tableVariable := range metaTableVariables {
		entries := make([]*model.MetaTableVariableEntry, 0)
		for _, entry := range tableVariable.VariableEntries {
			entries = append(entries, &model.MetaTableVariableEntry{
				Key:   entry.Key,
				Value: entry.Value,
			},
			)
		}
		tableVariable := &model.MetaTableVariable{
			Catalog:         tableVariable.Catalog,
			Database:        tableVariable.Database,
			Table:           tableVariable.Table,
			Type:            tableVariable.Type,
			VariableEntries: entries,
		}
		variables = append(variables, tableVariable)
	}
	return variables
}

func GetSystemVariables(jobId string,
	clusterGroupSerialId string) map[string]string {
	systemConf := make(map[string]string, 0)
	systemConf[constants.SYSTEM_VARIABLE_JOB_SERIAL_ID] = jobId
	systemConf[constants.SYSTEM_VARIABLE_CLUSTER_SERIAL_ID] = clusterGroupSerialId
	return systemConf
}

// build command and get reference resources
func BuildMetadata4Command(ctx context.Context, programArgs string, encryptSqlCode string, uin string,
	appId int64, region string, flinkVersion string,
	itemSpaceId int64, attachVar bool, systemConf map[string]string) (metadataStr string, resourceRefs []*job_config_model.ResourceRefItem, excludeRefs []*job_config_model.ResourceRefItem, newSqlCode string, err error) {
	md := &metadata_model.MetadataV1{}
	newSqlCode = encryptSqlCode
	if programArgs != "" { //
		params := map[string]interface{}{}
		err := json.Unmarshal([]byte(programArgs), &params)
		if err != nil {
			logger.Errorf("Failed to parse program params %s, with errors:%+v", programArgs, err)
			return "", nil, nil, "", err
		}
		metadatastr, ok := params["Metadata"]
		if !ok || metadatastr == "" {
			return "", nil, nil, newSqlCode, nil
		}
		metadatabyte, err := base64.StdEncoding.DecodeString(metadatastr.(string))
		if err := json.Unmarshal(metadatabyte, md); err != nil {
			logger.Errorf("Failed to parse sql job program args : %s", programArgs)
			return "", nil, nil, "", errors.New(fmt.Sprintf("Failed to parse sql job metadata :%s", programArgs))
		}
		resourceRefs = make([]*job_config_model.ResourceRefItem, 0)
		// 处理引用表
		referenceTables := make([]*metadata_model.ReferenceTable, 0)
		for _, r := range md.Metadata.ReferenceTables {
			metaTableWithResource, err := metadata.GetMetaTableWithResourceByIdentify(ctx,
				r.Catalog, r.Database, r.Table, uin, appId, region, flinkVersion, r.Version, itemSpaceId)
			if err != nil {
				logger.Errorf("Failed to query MetastoreTable , with errors:%+v", err)
				return "", nil, nil, "", err
			}
			referenceTables = append(referenceTables, &metadata_model.ReferenceTable{
				Catalog:  r.Catalog,
				Database: r.Database,
				Table:    r.Table,
				MetastoreTableEntity: &metadata_model.MetastoreTableEntity{
					Id:          metaTableWithResource.Id,
					Name:        metaTableWithResource.Name,
					Catalog:     r.Catalog,
					Database:    r.Database,
					TableSchema: metaTableWithResource.TableSchema,
					Properties:  metaTableWithResource.Properties,
					Type:        metaTableWithResource.Type,
					TableType:   metaTableWithResource.TableType,
				},
			})
			// 兜底策略  过滤指定内置Connector
			excludeConnectors, ok := params["ExcludeConnectors"]
			excludeRefs = make([]*job_config_model.ResourceRefItem, 0)
			if ok {
				excludeData, err := json.Marshal(excludeConnectors)
				if err != nil {
					logger.Errorf("Failed to Marshal ExcludeConnectors , with errors:%+v", err)
					return "", nil, nil, "", err
				}
				excepts, _ := metadata.BuildConnectorOptions(string(excludeData))
				if excepts != nil && len(excepts) > 0 {
					for _, options := range excepts {
						// allow mismatched
						refs, _, mismatched, _ := metadata.GetSystemConnectorRef(options, flinkVersion)
						for _, ref := range refs {
							if !mismatched && ref.ResourceId != constants.METADATA_TABLE_RESOURCE_REF_NO_NEED_REFERENCE_RESOURCE_ID {
								excludeRefs = append(excludeRefs, &job_config_model.ResourceRefItem{
									ResourceId: ref.ResourceSerialId,
									Version:    ref.VersionId,
									Type:       constants.RESOURCE_REF_USAGE_TYPE_DEFALUT,
								})
							}
						}
					}
				}
			}
			// 关联resource
			if metaTableWithResource.ResourceId < 0 { // 关联最新版本
				if metaTableWithResource.ResourceId != constants.METADATA_TABLE_RESOURCE_REF_NO_NEED_REFERENCE_RESOURCE_ID {
					resourceLocs, _, mismatched, err := metadata.GetSystemConnectorRef(metaTableWithResource.Properties, flinkVersion)
					if mismatched {
						msg := fmt.Sprintf("Faild to find connector with option %v", metaTableWithResource.Properties)
						logger.Infof(msg)
						return "", nil, nil, "", errors.New(msg)
					}
					if err != nil {
						return "", nil, nil, "", err
					}
					for _, resourceLoc := range resourceLocs {
						if resourceLoc.ResourceId != constants.METADATA_TABLE_RESOURCE_REF_NO_NEED_REFERENCE_RESOURCE_ID {
							resourceRefItem := &job_config_model.ResourceRefItem{
								ResourceId: resourceLoc.ResourceSerialId,
								Version:    resourceLoc.VersionId,
								Type:       constants.RESOURCE_REF_USAGE_TYPE_DEFALUT,
							}
							resourceRefs = append(resourceRefs, resourceRefItem)
						}
					}

				}
			} else {
				resource, err := service4.GetResourceById(metaTableWithResource.ResourceId)
				if err != nil {
					return "", nil, nil, "", err
				}
				resourceRefItem := &job_config_model.ResourceRefItem{
					ResourceId: resource.ResourceId,
					Version:    metaTableWithResource.ResourceVersionId,
					Type:       constants.RESOURCE_REF_USAGE_TYPE_DEFALUT,
				}
				resourceRefs = append(resourceRefs, resourceRefItem)
			}
		}
		// 处理表变量
		variables := make([]*metadata_model.Variable, 0)
		for _, v := range md.Metadata.Variables {
			variableEntries := make([]*metadata_model.VariableEntry, 0)
			for _, ve := range v.VariableEntries {
				if ve.VariableId != "" { // 处理全局变量
					if systemConf != nil && service3.StrArrContains(constants.SYSTEM_GLOBAL_VARIABLE_ARRAY, ve.VariableId) { // 处理系统变量
						if _, exists := systemConf[ve.VariableId]; exists {
							fmt.Printf("The map is not empty and contains the key: %s\n", ve.VariableId)
						} else {
							fmt.Printf("The map is not empty, but does not contain the key: %s", ve.VariableId)
							continue
						}
						logger.Errorf(" ##### systemVariable Placeholder: %+v, VariableId: %+v ", ve.Placeholder, ve.VariableId)
						ve.Value = systemConf[ve.VariableId]
						logger.Errorf(" ##### systemVariable Value: %+v ", ve.Value)
					} else {
						globalVariable, err := variable.GetVariableBySerialId(ve.VariableId, uin, appId, region, 0)
						if err != nil {
							return "", nil, nil, "", err
						}
						if globalVariable == nil {
							ve.Value = ""
						} else {
							ve.Value = globalVariable.Value
						}
					}
				}
				variableEntries = append(variableEntries, &metadata_model.VariableEntry{
					Key:   ve.Key,
					Value: ve.Value,
				})
			}
			variables = append(variables, &metadata_model.Variable{
				VariableEntries: variableEntries,
				Catalog:         v.Catalog,
				Database:        v.Database,
				Table:           v.Table,
				Type:            v.Type,
			})
		}
		// 处理临时表变量
		if attachVar && CheckIfUsedTempTableVariable(Transform2Variables(variables)) {
			// 1. decrypt the sql code
			decryptSqlCode, err := util.AesDecrypt(encryptSqlCode, constants.AES_ENCRYPT_KEY)
			if err != nil {
				msg := fmt.Sprintf("Failed to decrypt sql code,program args %s", programArgs)
				logger.Errorf(msg+",return err:%+v ", err)
				return "", nil, nil, "", err
			}
			// 2. encode the sql code
			encodeSqlCode := base64.StdEncoding.EncodeToString([]byte(decryptSqlCode))
			// 3. attach variables
			attachVariablesReq, err := BuildAttachVariablesReqFromJobConfig(encodeSqlCode, flinkVersion, variables)
			if err != nil {
				msg := fmt.Sprintf("Failed to build AttachVariablesReq,program args %s", programArgs)
				logger.Errorf(msg+",return err:%+v ", err)
				return "", nil, nil, "", err
			}
			pss := &ParseSqlService{
				Uin:          uin,
				FlinkVersion: flinkVersion,
				Region:       region,
			}
			attachVariablesRsp, err := pss.AttachVariables(ctx, attachVariablesReq, region, 14)
			if err != nil {
				msg := fmt.Sprintf("Failed to invoke AttachVariables,program args %s", programArgs)
				logger.Errorf(msg+",return err:%+v ", err)
				return "", nil, nil, "", err
			}
			if attachVariablesRsp.Pass == false {
				msg := fmt.Sprintf("Failed to invoke AttachVariables,program args %s", programArgs)
				logger.Errorf(msg+",return err:%+v ", attachVariablesRsp)
				return "", nil, nil, "", errors.New(attachVariablesRsp.ErrorMessage)
			}
			// 4. re-encrypt the sql code
			logger.Debugf("new sql Code:%s", attachVariablesRsp.SqlCode)
			newSqlCode, err = util.AesEncrypt([]byte(attachVariablesRsp.SqlCode), constants.AES_ENCRYPT_KEY)
			if err != nil {
				logger.Errorf("Failed to encrypt the sqlCode, with errors:%+v", err)
				return "", nil, nil, "", err
			}
		}
		// 处理Catalog
		catalogs := make([]*metadata_model.Catalog, 0)
		for _, v := range md.Metadata.Catalogs {
			catalogs = append(catalogs, &metadata_model.Catalog{
				Name:            v.Name,
				DefaultDatabase: v.DefaultDatabase,
				Type:            v.Type,
				CatalogVersion:  v.CatalogVersion,
				CatalogId:       v.CatalogId,
				ConfigFilePath:  constants.RESOURCE_DEPENDENCY_DEFAULT_PATH,
				Hostname:        v.Hostname,
				Port:            v.Port,
				Username:        v.Username,
				Password:        v.Password,
			})
		}
		metadataV1 := &metadata_model.Metadata{
			ReferenceTables: referenceTables,
			Variables:       variables,
			Catalogs:        catalogs,
		}
		data, err := json.Marshal(metadataV1)
		if err != nil {
			return "", nil, nil, "", err
		}
		return string(data), resourceRefs, excludeRefs, newSqlCode, nil
	}
	return "", nil, nil, newSqlCode, nil
}

func BuildAttachVariablesReqFromJobConfig(
	sqlCode string, flinkVersion string, variables []*metadata_model.Variable) (req map[string]interface{}, err error) {
	req = make(map[string]interface{})
	req["sqlCode"] = sqlCode
	req["flinkClientVersion"] = flinkVersion
	req["metadata"] = metadata_model.Metadata{
		Variables: variables,
	}
	return
}
