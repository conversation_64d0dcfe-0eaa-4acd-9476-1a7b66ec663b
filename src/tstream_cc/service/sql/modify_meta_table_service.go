package sql

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"

	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	meta_model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/metadata"
	sql_model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/sql"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/metadata"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	meta_service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/metadata"
)

type ModifyMetaTableService struct {
}

func (s *ModifyMetaTableService) ModifyMetaTable(req *meta_model.ModifyMetaTableReq, evenId int64) (rsp *meta_model.ModifyMetaTableRsp, err error) {
	rsp = &meta_model.ModifyMetaTableRsp{
		RequestId: req.RequestId,
	}
	// 鉴权
	itemSpaceId, err2 := auth.InnerCreateAuth(req.WorkSpaceId, req.AppId, req.Region)
	if err2 != nil {
		logger.Errorf("%s: User  uin %s is NOT authenticated, refuse to serve", req.RequestId, req.Uin)
		errCode := errorcode.GetCode(err2)
		return nil, errCode.New()
	}
	// 查询 metaTable
	metastoreTable, err := meta_service.GetMetaTableByTableId(req.TableId, req.Uin, req.AppId, req.Region)
	if err != nil {
		logger.Errorf("%s: Failed to get meta table %s", req.RequestId, req.TableId)
		return rsp, errorcode.FailedOperationCode.NewWithErr(err)
	}
	if metastoreTable.Script == req.SqlCode {
		return rsp, errorcode.InvalidParameterCode.ReplaceDesc("The table creation statement is the same as the current version, and no modification is necessary.")
	}

	clusterGroup, err := service3.GetClusterGroupBySerialId(req.ClusterId)
	if err != nil {
		logger.Errorf("get clusterGroup by req.ClusterId:%s error: %#v", req.ClusterId, err)
		return rsp, errorcode.FailedOperationCode.NewWithErr(err)
	}

	cluster, err := service3.GetActiveClusterByClusterGroupId(clusterGroup.Id)
	if err != nil {
		logger.Errorf("[%s]Failed to request GetActiveClusterByClusterGroupId. err:%+v", req.RequestId, err)
		return rsp, errorcode.FailedOperationCode.NewWithErr(err)
	}

	flinkVersionsfromSystem := cluster.GetSupportedFlinkVersion()

	parseRsp := &sql_model.ParseSqlRsp{Pass: false}
	logger.Errorf(" [%s] Start to ParseSql ", req.RequestId)

	for _, flinkVersion := range flinkVersionsfromSystem {
		// DDL解析
		parser := NewParseSqlService(req.RequestId, req.Uin, req.AppId, req.Region, flinkVersion, req.ClusterId, itemSpaceId)
		parseResponse, err := parser.ParseSql(context.Background(), req.SqlCode, req.Region, 13)
		logger.Debugf("### ParseSql,req:%s, parseResponse:%v, flinkVersion:%v, err: %v ", req.RequestId, parseResponse, flinkVersion, err)
		parseRsp = parseResponse
		if err == nil && parseResponse.Pass {
			req.FlinkVersion = flinkVersion
			break
		}
		if err != nil {
			parseRsp.ErrorMessage = err.Error()
		}
	}
	logger.Errorf(" [%s] End to ParseSql ", req.RequestId)

	if !parseRsp.Pass {
		return rsp, errorcode.ModifyTableFailedOperation.ReplaceDesc(parseRsp.ErrorMessage)
	}

	tables := parseRsp.MetaRefs.TemporaryTables
	if len(tables) == 0 {
		logger.Error("Failed to parse ddl :", req.SqlCode, "\t err:", err)
		return rsp, errorcode.FailedOperationCode.ReplaceDesc("Failed to parse ddl.")
	}
	// 校验tableName
	if metastoreTable.Name != tables[0].Name {
		err = errorcode.ModifyTableFailedOperation.ReplaceDesc(
			fmt.Sprintf("The tableName cannot be modified. previous tableName: %s,current tableName: %s", metastoreTable.Name, tables[0].Name))
		return rsp, errorcode.ModifyTableFailedOperation.NewWithErr(err)
	}
	// 校验connector
	err = CheckOptions(metastoreTable.Properties, tables[0].Properties)
	if err != nil {
		logger.Errorf("reqId:%s, CheckOptions, errors:%s", req.RequestId, err)
		return rsp, errorcode.ModifyConnectorFailedOperation.NewWithErr(err)
	}
	checkDdlGrammarReq := &meta_model.CheckDdlGrammarReq{
		RequestBase:  req.RequestBase,
		ClusterId:    req.ClusterId,
		SqlCode:      req.SqlCode,
		FlinkVersion: req.FlinkVersion,
	}
	// DDL语法检查
	/*checkDdlGrammarService := &CheckDdlGrammarService{}
	_, msg, checkDdlGrammarRsp := checkDdlGrammarService.DoCheckDdlGrammar(checkDdlGrammarReq, 13)
	if msg != "" {
		logger.Errorf("reqId:%s, Failed to CheckDdlGrammar , errors:%s", req.RequestId, msg)
		return rsp, errorcode.ModifyTableFailedOperation.ReplaceDesc("Failed to CheckDdlGrammar." + msg)
	}
	if !checkDdlGrammarRsp.Pass {
		return rsp, errorcode.ModifyTableFailedOperation.ReplaceDesc(checkDdlGrammarRsp.ErrorMessage)
	}*/
	// 检查metaTableFlinkVersion
	newMetastoreTable := &metadata.MetastoreTable{
		Id:          metastoreTable.Id,
		Script:      req.SqlCode,
		Properties:  tables[0].Properties,
		TableSchema: tables[0].TableSchema,
	}
	logger.Errorf(" [%s] Start to checkMetaTableFlinkVersion ", checkDdlGrammarReq.RequestId)
	err = s.checkMetaTableFlinkVersion(newMetastoreTable, checkDdlGrammarReq, flinkVersionsfromSystem)
	if err != nil {
		logger.Errorf("reqId:%s, checkMetaTableFlinkVersion, error:%v", req.RequestId, err)
		return rsp, errorcode.ModifyTableFailedOperation.ReplaceDesc(err.Error())
	}
	logger.Errorf(" [%s] End to checkMetaTableFlinkVersion ", checkDdlGrammarReq.RequestId)
	// 更新 metaTable
	err = s.UpdateMetaTable(metastoreTable, req.SqlCode, tables[0].Properties, tables[0].TableSchema, req.SubAccountUin, req.Remark)
	if err != nil {
		return rsp, errorcode.FailedOperationCode.NewWithErr(err)
	}
	return rsp, nil
}

func (s *ModifyMetaTableService) UpdateMetaTableRefs(tableId int64, allMetaTableResourceRefs []*metadata.MetaTableResourceRef, unsupportedFlinkVersions []string) (err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Delete batch of tableID panic, for tableId:%+d, errors:%+v", tableId, errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	metaTableResourceRefs, err := meta_service.GetMetaResourceRefsByType(tableId, constants.METADATA_TABLE_RESOURCE_TYPE_SYSTEM_RESOURCE)
	if err != nil {
		logger.Errorf("UpdateMetaTableRefs, Failed to GetMetaResourceRefsByType by tableId: %v,err:%s", tableId, err.Error())
	}

	//获取该metastoreTable已经支持的flink version
	supportedFlinkVersions := make([]string, 0)
	if metaTableResourceRefs != nil && len(metaTableResourceRefs) > 0 {
		for _, value := range metaTableResourceRefs {
			supportedFlinkVersions = append(supportedFlinkVersions, value.FlinkVersion)
		}
	}

	logger.Errorf("supportedFlinkVersions: %v", supportedFlinkVersions)

	txManager := service.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {

		// 清理当前metastoreTable不支持的 flinkVersion
		if len(unsupportedFlinkVersions) > 0 {
			logger.Errorf("unsupportedFlinkVersions: %v", unsupportedFlinkVersions)
			for _, unsupportedFlinkVersion := range unsupportedFlinkVersions {
				if service2.ContainsString(supportedFlinkVersions, strings.TrimSpace(unsupportedFlinkVersion)) {
					delMetaTableResourceRef := "UPDATE MetaTableResourceRef SET `Status` = ? WHERE MetaTableId = ? and FlinkVersion =? "
					logger.Errorf("tableId:%v, delflinkVersions: %v", tableId, unsupportedFlinkVersion)
					tx.ExecuteSqlWithArgs(delMetaTableResourceRef, constants.METADATA_TABLE_JOB_REF_DELETE, tableId, unsupportedFlinkVersion)
				}
			}
		}

		//metastoreTable需要支持的 flinkVersion
		for _, allMetaTableResourceRef := range allMetaTableResourceRefs {
			flinkVersion := allMetaTableResourceRef.FlinkVersion
			logger.Errorf("allMetaTableResourceRef flinkVersion: %v", flinkVersion)
			if !service2.ContainsString(supportedFlinkVersions, strings.TrimSpace(flinkVersion)) {
				logger.Errorf("tableId:%v, addflinkVersions: %v", tableId, flinkVersion)
				tx.SaveObject(allMetaTableResourceRef, "MetaTableResourceRef")
			}
		}
		return err
	}).Close()
	return err
}

func (s *ModifyMetaTableService) checkMetaTableFlinkVersion(newMetastoreTable *metadata.MetastoreTable,
	checkDdlGrammarReq *meta_model.CheckDdlGrammarReq, flinkVersionsfromSystem []string) (err error) {

	if len(flinkVersionsfromSystem) > 0 {
		//语法检查
		_, metaTableResourceRefs, unsupportedFlinkVersions, err := BuildMetaTableResourceRef(flinkVersionsfromSystem, checkDdlGrammarReq, newMetastoreTable)
		if err != nil {
			msg := fmt.Sprintf(" [%s] Failed to build table resource refrence ", checkDdlGrammarReq.RequestId)
			logger.Errorf("%s ,err:%s", msg, err)
			return err
		}
		if len(unsupportedFlinkVersions) > 0 {
			useUnsupportedFlinkJobs, err := meta_service.GetTableRefJobsWithFlinkVersions(newMetastoreTable.Id, checkDdlGrammarReq.Region, checkDdlGrammarReq.AppId, unsupportedFlinkVersions)
			if err != nil {
				msg := fmt.Sprintf(" Failed to GetTableRefJobsWithFlinkVersions, unsupportedFlinkVersions: %v ", unsupportedFlinkVersions)
				logger.Errorf("%s ,err:%s", msg, err)
				return err
			}
			if len(useUnsupportedFlinkJobs) > 0 {
				msg := fmt.Sprintf("This change does not support versions: %s; it will affect job: [%s], please create a new metaTable.",
					strings.Join(unsupportedFlinkVersions, " "), strings.Join(useUnsupportedFlinkJobs, " "))
				logger.Errorf("%s ", msg)
				return errors.New(msg)
			}
		}
		s.UpdateMetaTableRefs(newMetastoreTable.Id, metaTableResourceRefs, unsupportedFlinkVersions)
	}
	return nil

}

func (s *ModifyMetaTableService) UpdateMetaTable(metastoreTable *metadata.MetastoreTable, newCode string, newOptions string,
	newTableSchema, operatorUin string, remark string) (err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("update metastoreTable to db panic ,for job:%+v, errors:%+v", metastoreTable, errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	metaTableChangeLog := &metadata.MetaTableChangeLog{
		TableId:     metastoreTable.Id,
		Version:     metastoreTable.Version,
		TableSchema: metastoreTable.TableSchema,
		Properties:  metastoreTable.Properties,
		Script:      metastoreTable.Script,
		Status:      constants.METADATA_CATALOG_STATUS_ACTIVE,
		OperatorUin: operatorUin,
		Remark:      metastoreTable.Comment,
		CreateTime:  metastoreTable.CreateTime,
		UpdateTime:  metastoreTable.UpdateTime,
	}
	txManager := service.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.ExecuteSqlWithArgs("UPDATE `MetastoreTable` set `Version` = ?,`Script` = ?,`Properties` = ?, `TableSchema` = ?, `Comment` = ?  WHERE id = ? ",
			metastoreTable.Version+1, newCode, newOptions, newTableSchema, remark, metastoreTable.Id)
		_ = tx.SaveObject(metaTableChangeLog, "MetaTableChangeLog")
		return nil
	}).Close()
	return nil
}

func CheckOptions(oldOptions string, newOptions string) (err error) {
	oldOptionsMap := make(map[string]string)
	err = json.Unmarshal([]byte(oldOptions), &oldOptionsMap)
	if err != nil {
		return err
	}
	newOptionsMap := make(map[string]string)
	err = json.Unmarshal([]byte(newOptions), &newOptionsMap)
	if err != nil {
		return err
	}
	// connector类型不能修改
	if newOptionsMap["connector"] != oldOptionsMap["connector"] {
		return errorcode.ModifyConnectorFailedOperation.ReplaceDesc(
			fmt.Sprintf("The connector type cannot be modified.previous type: %s,current type: %s", oldOptionsMap["connector"], newOptionsMap["connector"]))
	}
	return nil
}
