package sql

import (
	"reflect"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/metadata"
	"testing"
)

func TestParseSqlService_getMetaVariable(t *testing.T) {
	type fields struct {
		RequestId            string
		Uin                  string
		AppId                int64
		Region               string
		FlinkVersion         string
		ClusterGroupSerialId string
	}
	type args struct {
		properties string
		catalog    string
		database   string
		table      string
	}
	tests := []struct {
		name                  string
		fields                fields
		args                  args
		wantMetaTableVariable *model.MetaTableVariable
		wantErr               bool
	}{
		// TODO: Add test cases.
		{
			name: "TestParseSqlService_getMetaVariable",
			fields: fields{
				RequestId:            "",
				Uin:                  "",
				AppId:                0,
				Region:               "",
				FlinkVersion:         "",
				ClusterGroupSerialId: "",
			},
			args: args{
				properties: "{\"connector\":\"1111${xxxx:21111}11${xxxx:datagen}\",\"raw\":\"${xxx}:1111\"}",
				catalog:    "11",
				database:   "11",
				table:      "11",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ParseSqlService{
				RequestId:            tt.fields.RequestId,
				Uin:                  tt.fields.Uin,
				AppId:                tt.fields.AppId,
				Region:               tt.fields.Region,
				FlinkVersion:         tt.fields.FlinkVersion,
				ClusterGroupSerialId: tt.fields.ClusterGroupSerialId,
			}
			gotMetaTableVariable, err := s.getMetaVariable(tt.args.properties, tt.args.catalog, tt.args.database, tt.args.table)
			if (err != nil) != tt.wantErr {
				t.Errorf("getMetaVariable() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotMetaTableVariable, tt.wantMetaTableVariable) {
				t.Errorf("getMetaVariable() gotMetaTableVariable = %v, want %v", gotMetaTableVariable, tt.wantMetaTableVariable)
			}
		})
	}
}
