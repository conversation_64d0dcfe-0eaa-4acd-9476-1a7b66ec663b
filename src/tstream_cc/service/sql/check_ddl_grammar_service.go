package sql

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sync"

	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/uuid"
	"tencentcloud.com/tstream_galileo/src/component"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/metadata"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/sql"
	sql_model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/sql"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/metadata"
)

type CheckDdlGrammarService struct {
	CheckDdlGrammarRspSet []*VersionedCheckDdlRsp
}

type VersionedCheckDdlRsp struct {
	FlinkVersion       string
	CheckDdlGrammarRsp *model.CheckDdlGrammarRsp
	ResourceLocs       []*sql_model.ResourceLocationIntegral
}

func (service *CheckDdlGrammarService) DoCheckDdlGrammar(req *model.CheckDdlGrammarReq, evenId int64) (string, string, *model.CheckDdlGrammarRsp) {
	service.CheckDdlGrammarRspSet = make([]*VersionedCheckDdlRsp, 0)
	rsp := &model.CheckDdlGrammarRsp{}
	clusterGroup, err := service3.GetClusterGroupBySerialId(req.ClusterId)
	if err != nil {
		return controller.FailedOperation_CheckDdlGrammar, err.Error(), nil
	}
	if int64(clusterGroup.AppId) != req.AppId || clusterGroup.OwnerUin != req.Uin {
		return controller.FailedOperation_CheckDdlGrammar, fmt.Sprintf("The appid %d or uin %s has no operation permission.", req.AppId, req.Uin), nil
	}
	// 1. parse sql [sql-parser]
	reqFlinkVersion := req.FlinkVersion
	parser := NewParseSqlService(req.RequestId, req.Uin, req.AppId, req.Region, req.FlinkVersion, req.ClusterId, 0)
	parseRsp, err := parser.ParseSql(context.Background(), req.SqlCode, req.Region, 13)
	if err != nil {
		logger.Errorf("Failed to parse ddl %s, err: %+v", req.SqlCode, err)
		return controller.FailedOperation_ParseSql, err.Error(), nil
	}
	if !parseRsp.Pass {
		rsp.Pass = parseRsp.Pass
		rsp.ErrorMessage = parseRsp.ErrorMessage
		rsp.ErrorCoordinate = &sql.Coordinate{
			StartRow:    parseRsp.ErrorCoordinate.StartRow,
			EndRow:      parseRsp.ErrorCoordinate.EndRow,
			StartColumn: parseRsp.ErrorCoordinate.StartColumn,
			EndColumn:   parseRsp.ErrorCoordinate.EndColumn,
		}
		rsp.Done = true
		return controller.OK, controller.NULL, rsp
	}
	tables := parseRsp.MetaRefs.TemporaryTables
	if len(tables) == 0 {
		msg := fmt.Sprint("Failed to parse ddl.")
		return controller.FailedOperation_ParseSql, msg, nil
	}
	// 2. 匹配Connector
	var resLocations = make([]*sql_model.ResourceLocationForSqlServer, 0)
	if len(req.ResourceRefs) > 0 { // user defined connector
		refLocations, err := metadata.GetResourceLocByRefItem(req.ResourceRefs)
		if err != nil {
			logger.Errorf("[%s]Failed to get ResourceRefs  err:%+v", req.RequestId, err)
			return controller.FailedOperation_CheckDdlGrammar, err.Error(), nil
		}
		for _, resourceLoc := range refLocations {
			if resourceLoc.ResourceId != constants.METADATA_TABLE_RESOURCE_REF_NO_NEED_REFERENCE_RESOURCE_ID {
				resources := &sql_model.ResourceLocationForSqlServer{
					SerialId:           fmt.Sprintf("%d-%d", resourceLoc.ResourceId, resourceLoc.VersionId),
					ResourceLocationV2: resourceLoc.ResourceLocationV2,
				}
				resLocations = append(resLocations, resources)
			}
		}
		// 3. WITH参数校验  [sql-server]
		checkDdlGrammarRsp, err := CheckDdlGrammar(req, resLocations)
		if err != nil {
			logger.Errorf("[%s]Failed to request CheckDdlGrammar ,err:%+v:", req.RequestId, err)
			return controller.FailedOperation_CheckDdlGrammar, err.Error(), nil
		}
		service.CheckDdlGrammarRspSet = append(service.CheckDdlGrammarRspSet, &VersionedCheckDdlRsp{
			FlinkVersion:       req.FlinkVersion,
			ResourceLocs:       refLocations,
			CheckDdlGrammarRsp: checkDdlGrammarRsp,
		})
		return controller.OK, controller.NULL, checkDdlGrammarRsp
	} else { // build-in connector
		cluster, err := service3.GetActiveClusterByClusterGroupId(clusterGroup.Id)
		if err != nil {
			logger.Errorf("[%s]Failed to request GetActiveClusterByClusterGroupId. err:%+v", req.RequestId, err)
			return controller.FailedOperation_CheckDdlGrammar, err.Error(), nil
		}
		flinkVersions := cluster.GetSupportedFlinkVersion()
		if err != nil {
			logger.Errorf("[%s]Failed to get flink versions. err:%+v", req.RequestId, err)
			return controller.FailedOperation_ConnectorNotFound, err.Error(), nil
		}
		var wg sync.WaitGroup
		var mutex sync.Mutex
		var matchResultChan = make(chan bool, len(flinkVersions))
		var errCh = make(chan error, 1)
		for _, flinkVersion := range flinkVersions {
			wg.Add(1)
			// 在循环内部创建新的 req 实例
			newReq := &model.CheckDdlGrammarReq{
				SqlCode:    req.SqlCode,
				ClusterId:  req.ClusterId,
				SqlCheckId: req.SqlCheckId,
			}
			newReq.AppId = req.AppId
			newReq.Uin = req.Uin
			newReq.Region = req.Region
			go func(flinkVersion string, newReq *model.CheckDdlGrammarReq) {
				defer wg.Done()
				var flinkResLocations = make([]*sql_model.ResourceLocationForSqlServer, 0)
				resourceRefLocs, _, mismatched, err := metadata.GetSystemConnectorRef(tables[0].Properties, flinkVersion)
				if err != nil {
					logger.Errorf("[%s]Failed to request CheckDdlGrammar  .err:%+v", newReq.RequestId, err)
				}
				if !mismatched {
					for _, resourceLoc := range resourceRefLocs {
						if resourceLoc.ResourceId != constants.METADATA_TABLE_RESOURCE_REF_NO_NEED_REFERENCE_RESOURCE_ID {
							resourceLocationForSqlServer := &sql_model.ResourceLocationForSqlServer{
								SerialId:           fmt.Sprintf("%d-%d", resourceLoc.ResourceId, resourceLoc.VersionId),
								ResourceLocationV2: resourceLoc.ResourceLocationV2,
							}
							flinkResLocations = append(flinkResLocations, resourceLocationForSqlServer)
						}
					}
					// 3. WITH参数校验  [sql-server]
					newReq.FlinkVersion = flinkVersion
					newReq.RequestId = uuid.NewRandom().String()

					checkDdlGrammarRsp, err := CheckDdlGrammar(newReq, flinkResLocations)
					if err != nil {
						logger.Errorf("[%s] Failed to request CheckDdlGrammar  .err:%+v", newReq.RequestId, err)
						errCh <- err
					}
					mutex.Lock()
					service.CheckDdlGrammarRspSet = append(service.CheckDdlGrammarRspSet, &VersionedCheckDdlRsp{
						FlinkVersion:       flinkVersion,
						ResourceLocs:       resourceRefLocs,
						CheckDdlGrammarRsp: checkDdlGrammarRsp,
					})

					mutex.Unlock()
					matchResultChan <- true
				} else {
					matchResultChan <- false
				}
			}(flinkVersion, newReq)
		}
		wg.Wait()
		close(matchResultChan)
		close(errCh)

		if err, ok := <-errCh; ok {
			return controller.FailedOperation_CheckDdlGrammar, err.Error(), nil
		}
		matched := false
		for result := range matchResultChan {
			if result {
				matched = true
				break
			}
		}

		req.FlinkVersion = reqFlinkVersion
		if !matched {
			msg := fmt.Sprintf(" [%s] Cant not find satisfied connector with option %s ", req.RequestId, tables[0].Properties)
			logger.Errorf("[%s] Cant not find satisfied connector :%+v", req.RequestId, tables[0].Properties)
			return controller.FailedOperation_ConnectorNotFound, msg, nil
		}
		service.CheckDdlGrammarRspSet = service.CheckDdlGrammarRspSet
		for _, grammarRsp := range service.CheckDdlGrammarRspSet {
			if grammarRsp.FlinkVersion == reqFlinkVersion { // 作业内核版本表检查结果
				return controller.OK, controller.NULL, grammarRsp.CheckDdlGrammarRsp
			}
		}
		return controller.OK, controller.NULL, service.CheckDdlGrammarRspSet[0].CheckDdlGrammarRsp
	}
}

func CheckDdlGrammar(req *model.CheckDdlGrammarReq, resourceLocations []*sql_model.ResourceLocationForSqlServer) (rsp *model.CheckDdlGrammarRsp, err error) {
	rsp = &model.CheckDdlGrammarRsp{}
	checkDdlGrammarReq, err := metadata.BuildCheckDdlGrammarReq(req.SqlCode, req.FlinkVersion, resourceLocations, req)
	if err != nil {
		logger.Errorf("BuildCheckDdlGrammarReq return err:%+v", err)
		return nil, err
	}
	checkDdlGrammarReq["sqlCheckId"] = req.SqlCheckId
	var sqlServer = &SqlServerService{
		FoundSatisfiedCluster: false,
	}
	sqlServer, err = NewSqlServerService(req.AppId, req.ClusterId, req.RequestId, req.Uin, req.Region, req.FlinkVersion)
	if err != nil {
		return nil, err
	}

	sqlServerSupportFlink13, err := sqlServer.CheckSupportFlink_1_13()
	if err != nil {
		return nil, err
	}
	if (req.FlinkVersion == constants.FLINK_VERSION_1_13 && sqlServerSupportFlink13) || ( // 兼容存量集群,1.8-20210731全部升级到更高版本去掉改逻辑
	req.FlinkVersion != constants.FLINK_VERSION_1_13 && sqlServer.FoundSatisfiedCluster) {

		rsp, err := sqlServer.CheckDdlGrammar(checkDdlGrammarReq, constants.CHECK_DDL_GRAMMAR_EVENTID)
		if err != nil {
			msg := fmt.Sprintf("[%s]: helper.CheckSqlGrammar return err:%+v", req.RequestId, err)
			logger.Info(msg)
			return rsp, errors.New(msg)
		}
		logger.Infof("##checkddl22: req: %+v, rsp22: %+v", checkDdlGrammarReq, rsp)
		return rsp, nil
	} else {
		helper := component.NewTstreamSqlHelper(req.Region)
		rspData, err := helper.SendRequest(checkDdlGrammarReq, 12, constants.METADATA_CHECKDDLGRAMMAR_PATH, "")
		if err != nil {
			return nil, err
		}
		rsp, err = ParseDdlRspToApiV3Rsp(rspData)
		if err != nil {
			msg := fmt.Sprintf("[%s]: helper.CheckSqlGrammar return err:%+v", req.RequestId, err)
			logger.Info(msg)
			return rsp, errors.New(msg)
		}
		return rsp, nil
	}
}

func ParseDdlRspToApiV3Rsp(rspData []byte) (apiv3Rsp *model.CheckDdlGrammarRsp, err error) {
	type Rsp struct {
		Pass        bool   `json:"pass"`
		Message     string `json:"errorMessage"`
		StartRow    uint64 `json:"errStartRow"`
		StartColumn uint64 `json:"errStartColumn"`
		EndRow      uint64 `json:"errEndRow"`
		EndColumn   uint64 `json:"errEndColumn"`
	}

	rsp := &Rsp{}
	if err = json.Unmarshal(rspData, rsp); err != nil {
		logger.Infof("tstream sql rsp [ %s ] Unmarshal fail: %+v", string(rspData), err)
		return
	}
	apiv3Rsp = &model.CheckDdlGrammarRsp{
		Pass:         rsp.Pass,
		ErrorMessage: rsp.Message,
		ErrorCoordinate: &sql.Coordinate{
			StartRow:    rsp.StartRow,
			StartColumn: rsp.StartColumn,
			EndRow:      rsp.EndRow,
			EndColumn:   rsp.EndColumn,
		},
	}
	return apiv3Rsp, nil
}
