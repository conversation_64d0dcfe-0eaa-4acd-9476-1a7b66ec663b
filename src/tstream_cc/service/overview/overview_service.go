package overview

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/overview"
)

type OverviewService struct {
	req *overview.DescribeResourceOverviewReq
}

func NewOverviewService(req *overview.DescribeResourceOverviewReq) *OverviewService {
	return &OverviewService{req: req}
}

func (s *OverviewService) Overview() (rsp *overview.DescribeResourceOverviewRsp, err error) {
	rsp = &overview.DescribeResourceOverviewRsp{}
	if rsp.CUOverview, err = s.getCUOverview(); err != nil {
		return
	}
	if rsp.ClusterOverview, err = s.getClusterOverview(); err != nil {
		return
	}

	if rsp.JobOverview, err = s.getJobOverview(); err != nil {
		return
	}

	return
}

func (s *OverviewService) getCUOverview() (CUOverview *overview.CUOverview, err error) {
	CUOverview = &overview.CUOverview{}
	CUOverview.TotalCount, err = s.getCUTotalCount()
	if err != nil {
		return
	}
	UseCount, err := s.getCUUseCount()
	if err != nil {
		return
	}
	CUOverview.FreeCountFloat = float32(CUOverview.TotalCount) - UseCount
	CUOverview.FreeCount = int(CUOverview.FreeCountFloat)
	if CUOverview.FreeCount < 0 {
		CUOverview.FreeCount = 0
		CUOverview.FreeCountFloat = 0.0
	}
	return
}

func (s *OverviewService) getCUTotalCount() (count int, err error) {
	where, args := s.sqlAddRunningClusterCondition()
	sql := "select sum(CuNum) as cnt from ClusterGroup"
	result, err := s.parseSqlResultToInt(sql+where, args)
	if err != nil {
		return
	}
	count = result["cnt"]
	return
}

func (s *OverviewService) getCUUseCount() (count float32, err error) {
	sql := "select sum(b.TmRunningCuNum*d.TmCuSpec + b.JmRunningCuNum*d.JmCuSpec) cnt from Job a" +
		" join JobInstance b on b.JobId = a.Id and b.JobConfigId = a.PublishedJobConfigId" +
		" join ClusterGroup c on c.Id = a.ClusterGroupId" +
		" join JobConfig d on d.Id = b.JobConfigId"

	cond := dao.NewCondition().
		Eq("a.status", constants.JOB_STATUS_RUNNING).
		Eq("b.status", constants.JOB_INSTANCE_STATUS_RUNNING).
		Eq("c.AppId", s.req.AppId).
		Eq("c.Region", s.req.Region).
		In("c.Status", []int{
			constants.CLUSTER_GROUP_STATUS_RUNNING,
			constants.CLUSTER_GROUP_STATUS_SCALE_PROGRESS,
			constants.CLUSTER_GROUP_STATUS_SCALEDOWN_PROGRESS,
			constants.CLUSTER_GROUP_STATUS_UPGRADE,
		})
	where, args := cond.GetWhere()
	cnt, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql+where, args)
	// cnt == 0 means no job running, just return 0.0
	// no job running return 1 line with empty value
	if err != nil || cnt <= 0 || string(data[0]["cnt"]) == "" {
		return
	}
	result, err := strconv.ParseFloat(string(data[0]["cnt"]), 32)
	if err != nil {
		return 0.0, errorcode.InternalErrorCode.NewWithErr(err)
	}
	count = float32(result)
	return
}

func (s *OverviewService) sqlAddRunningClusterCondition() (string, []interface{}) {
	sql := " where AppId = ? and region = ? and Status in (?, ?, ?, ?)"
	args := make([]interface{}, 0)
	args = append(args, s.req.AppId)
	args = append(args, s.req.Region)
	args = append(args, constants.CLUSTER_GROUP_STATUS_RUNNING)
	args = append(args, constants.CLUSTER_GROUP_STATUS_SCALE_PROGRESS)
	args = append(args, constants.CLUSTER_GROUP_STATUS_SCALEDOWN_PROGRESS)
	args = append(args, constants.CLUSTER_GROUP_STATUS_UPGRADE)
	return sql, args
}

func (s *OverviewService) parseSqlResultToInt(sql string, args []interface{}) (result map[string]int, err error) {
	result = make(map[string]int)
	_, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return
	}
	if len(data) == 0 {
		return
	}

	for k, v := range data[0] {
		if len(v) == 0 {
			result[k] = 0
		} else {
			count, err := strconv.Atoi(string(v))
			if err != nil {
				return result, errorcode.InternalErrorCode.NewWithErr(err)
			}
			result[k] = count
		}
	}

	return
}

func (s *OverviewService) getClusterOverview() (clusterOverview *overview.ClusterOverview, err error) {
	sql := "SELECT SUM(running) AS RunningCount, SUM(isolated) AS IsolatedCount, SUM(due) AS DueCount " +
		"FROM (" +
		"SELECT " +
		"   if(Status IN (?, ?, ?, ?), 1, 0) AS running, " +
		"   if(Status = ?, 1, 0) AS isolated, " +
		"   if(ExpireTime <= DATE_ADD(now(), INTERVAL 7 DAY), 1, 0) AS due " +
		"FROM (" +
		"   SELECT c.Status AS Status, BillingResource.ExpireTime AS ExpireTime " +
		"   FROM (" +
		"       SELECT * FROM ClusterGroup WHERE Status IN (?, ?, ?, ?, ?) " +
		"           AND AppId = ? AND Region = ?) c " +
		"       LEFT JOIN BillingResource ON c.SerialId = BillingResource.ResourceId) t) tt"
	args := make([]interface{}, 0)
	args = append(args, constants.CLUSTER_GROUP_STATUS_RUNNING)
	args = append(args, constants.CLUSTER_GROUP_STATUS_SCALE_PROGRESS)
	args = append(args, constants.CLUSTER_GROUP_STATUS_SCALEDOWN_PROGRESS)
	args = append(args, constants.CLUSTER_GROUP_STATUS_UPGRADE)

	args = append(args, constants.CLUSTER_GROUP_STATUS_ISOLATED)

	args = append(args, constants.CLUSTER_GROUP_STATUS_RUNNING)
	args = append(args, constants.CLUSTER_GROUP_STATUS_SCALE_PROGRESS)
	args = append(args, constants.CLUSTER_GROUP_STATUS_SCALEDOWN_PROGRESS)
	args = append(args, constants.CLUSTER_GROUP_STATUS_UPGRADE)
	args = append(args, constants.CLUSTER_GROUP_STATUS_ISOLATED)

	args = append(args, s.req.AppId)
	args = append(args, s.req.Region)

	result, err := s.parseSqlResultToInt(sql, args)
	if err != nil {
		return
	}

	clusterOverview = &overview.ClusterOverview{
		RunningCount:          result["RunningCount"],
		IsolatedInstanceCount: result["IsolatedCount"],
		DueInstanceCount:      result["DueCount"],
	}
	return
}

func (s *OverviewService) getJobOverview() (jobOverview *overview.JobOverview, err error) {
	sql := "SELECT SUM(running) AS RunningCount, SUM(stop) AS StopCount, SUM(pause) AS PauseCount " +
		"FROM (" +
		"SELECT " +
		"   if(Status = ?, 1, 0) AS running, " +
		"   if(Status = ? or Status = ?, 1, 0) AS stop, " +
		"   if(Status = ?, 1, 0) AS pause " +
		"FROM Job WHERE Status IN (?, ?, ?, ?) AND ClusterGroupId IN (" +
		"   SELECT Id FROM ClusterGroup where AppId = ? and region = ? and Status in (?, ?, ?, ?, ?, ?)))t"
	args := make([]interface{}, 0)
	args = append(args, constants.JOB_STATUS_RUNNING)
	args = append(args, constants.JOB_STATUS_STOPPED)
	args = append(args, constants.JOB_STATUS_FINISHED)
	args = append(args, constants.JOB_STATUS_PAUSED)

	args = append(args, constants.JOB_STATUS_RUNNING)
	args = append(args, constants.JOB_STATUS_STOPPED)
	args = append(args, constants.JOB_STATUS_FINISHED)
	args = append(args, constants.JOB_STATUS_PAUSED)

	args = append(args, s.req.AppId)
	args = append(args, s.req.Region)
	args = append(args, constants.CLUSTER_GROUP_STATUS_RUNNING)
	args = append(args, constants.CLUSTER_GROUP_STATUS_SCALE_PROGRESS)
	args = append(args, constants.CLUSTER_GROUP_STATUS_SCALEDOWN_PROGRESS)
	args = append(args, constants.CLUSTER_GROUP_STATUS_ISOLATED)

	args = append(args, constants.CLUSTER_GROUP_STATUS_UPGRADE)

	args = append(args, constants.CLUSTER_GROUP_STATUS_RECOVERING)

	result, err := s.parseSqlResultToInt(sql, args)
	if err != nil {
		return
	}
	jobOverview = &overview.JobOverview{
		RunningJobCount: result["RunningCount"],
		StopJobCount:    result["StopCount"],
		PauseJobCount:   result["PauseCount"],
	}

	return
}
