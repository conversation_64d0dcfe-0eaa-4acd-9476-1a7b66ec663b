package debug

import (
	"encoding/base64"
	"fmt"
	"github.com/juju/errors"
	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/debug"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/debug"
)

func DoCreateDebugJob(req *model.CreateDebugJobReq) (string, string, *model.CreateDebugJobResp) {
	// 鉴权
	_, err := auth.InnerAuthJob(req.WorkSpaceId, req.IsSupOwner, req.JobId, req.AppId, req.SubAccountUin, req.Region, req.Action)
	if err != nil {
		logger.Errorf("%s: User  uin %s is NOT authenticated, refuse to serve", req.RequestId, req.Uin)
		errCode := errorcode.GetCode(err)
		return errCode.GetCodeStr(), errCode.GetCodeDesc(), nil
	}

	debugId, err := createDebugJob(req)
	if err != nil {
		logger.Errorf("%s: Failed to create debug job %s, error %+v", req.RequestId, req.JobId, err)
		return controller.InternalError, err.Error(), nil
	}
	return controller.OK, controller.NULL, &model.CreateDebugJobResp{DebugId: debugId}
}

func createDebugJob(req *model.CreateDebugJobReq) (debugId int64, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("%s: create Debug Job to db panic, for job:%s, errors:%+v",
				req.RequestId, req.JobId, errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()

	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		debugConfig, err := buildDebugJobConfig(req)
		if err != nil {
			return err
		}
		configId := tx.SaveObject(debugConfig, "DebugConfig")
		debugId = tx.SaveObject(buildDebugJob(configId), "DebugJob")
		for _, ref := range buildDebugJobRef(req, configId) {
			tx.SaveObject(ref, "DebugResourceRef")
		}
		// 根据JobId获取到作业的Flink版本
		job, err := service.GetJobBySerialId(req.AppId, req.JobId)
		if err != nil {
			logger.Errorf("%s: CreateDebugJob %s error: %+v", req.RequestId, req.JobId, err)
			return err
		}
		if len(job.FlinkVersion) == 0 {
			msg := fmt.Sprintf("Can not get flink version of the job")
			return errors.New(msg)
		}
		resp, err := ListSQLTables(req.RequestId, req.Region, req.JobId, debugId, debugConfig.SqlStatement,job.FlinkVersion)
		if err != nil {
			logger.Errorf("%s: CreateDebugJob %s error: %+v", req.RequestId, req.JobId, err)
			return err
		}
		for _, item := range resp.TableSource {
			source := &table.DebugSource{}
			source.TableName = item.TableName
			source.FieldDelimiter = "," // 暂时使用默认
			source.UpdateTime = util.GetCurrentTime()
			source.ConfigId = configId
			source.InputType = item.TableType
			tx.SaveObject(source, "DebugSource")
		}
		for _, item := range resp.TableSink {
			sink := &table.DebugSink{}
			sink.TableName = item.TableName
			sink.Url = fmt.Sprintf("/DebugResult/%s/%d/%s.txt", req.JobId, debugId, item.TableName)
			sink.DebugJobId = debugId
			tx.SaveObject(sink, "DebugSink")
		}
		return err
	}).Close()
	return
}

func buildDebugJobConfig(req *model.CreateDebugJobReq) (config *table.DebugConfig, err error) {
	sqlCode, err := base64.StdEncoding.DecodeString(req.SqlCode)
	if err != nil {
		return nil, errorcode.NewStackError(errorcode.InvalidParameterCode, "", err)
	}

	config = &table.DebugConfig{}
	config.JobSerialId = req.JobId
	/*
		sqlCodeBytes, err := base64.StdEncoding.DecodeString(req.SqlCode)
		if err != nil {
			logger.Errorf("%s: Decode Job %s sqlCode error: %+v", req.RequestId, req.JobId, err)
			return config, err
		}
		config.SqlStatement = string(sqlCodeBytes)
	*/
	config.SqlStatement = string(sqlCode)
	config.CreateTime = util.GetCurrentTime()
	config.UpdateTime = util.GetCurrentTime()
	return config, err
}

func buildDebugJob(debugConfig int64) (debugJob *table.DebugJob) {
	debugJob = &table.DebugJob{}
	debugJob.Status = constants.DEBUG_JOB_STATUS_CREATE
	debugJob.UpdateTime = util.GetCurrentTime()
	debugJob.CreateTime = util.GetCurrentTime()
	debugJob.ConfigId = debugConfig
	debugJob.ReturnCode = 0
	debugJob.ReturnMsg = ""
	debugJob.IsShare = constants.DEBUG_JOB_NOT_SHARE_CLUSTER
	return debugJob
}

func buildDebugJobRef(req *model.CreateDebugJobReq, debugConfig int64) (resourceRefs []*table.DebugResourceRef) {
	resourceRefs = make([]*table.DebugResourceRef, 0)
	for _, item := range req.ResourceRefs {
		resourceRef := &table.DebugResourceRef{}
		resourceRef.CreateTime = util.GetCurrentTime()
		resourceRef.UpdateTime = util.GetCurrentTime()
		resourceRef.ResourceId = item.ResourceId
		resourceRef.VersionId = item.Version
		resourceRef.Status = constants.RESOURCE_REF_STATUS_ACTIVE
		resourceRef.JobConfigId = debugConfig
		resourceRef.UsageType = item.Type
		resourceRefs = append(resourceRefs, resourceRef)
	}
	return resourceRefs
}
