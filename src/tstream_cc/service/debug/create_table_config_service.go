package debug

import (
	"errors"
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/debug"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/debug"
)

func DoCreateTableConfig(req *model.CreateTableConfigReq) (string, string, *model.CreateTableConfigResp) {
	//鉴权
	_, err := auth.InnerAuthJob(req.WorkSpaceId, req.Is<PERSON>upOwner, req.JobId, req.AppId, req.SubAccountUin, req.Region, req.Action)
	if err != nil {
		logger.Errorf("%s: User  uin %s is NOT authenticated, refuse to serve", req.RequestId, req.Uin)
		errCode := errorcode.GetCode(err)
		return errCode.GetCodeStr(), errCode.GetCodeDesc(), nil
	}

	err = createTableConfig(req)
	if err != nil {
		logger.Errorf("%s: Failed to create table Config %s , error %+v", req.RequestId, req.JobId, err)
		return controller.InternalError, err.Error(), nil
	}
	return controller.OK, controller.NULL, &model.CreateTableConfigResp{}
}

func createTableConfig(req *model.CreateTableConfigReq) (err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("%s: create table config to db panic, for job:%s, errors:%+v",
				req.RequestId, req.JobId, errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()

	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		debugJob := &table.DebugJob{}
		tx.GetObject(req.DebugId, debugJob, "DebugJob")
		tableSourceList, err := ListTableSource([]int64{debugJob.ConfigId}, nil)
		if err != nil {
			logger.Errorf("%s: ListTableSource error: %+v", req.RequestId, err)
			return err
		}
		var tableSourceMap = make(map[string]*table.DebugSource)
		for _, item := range tableSourceList {
			tableSourceMap[item.TableName] = item
		}
		for _, item := range req.TableDescriptions {
			if tableSource, ok := tableSourceMap[item.TableName]; ok {
				tableSource.FileName = item.FileName
				tableSource.Content = item.Path
				tx.UpdateObject(tableSource, tableSource.Id, "DebugSource")
			}
		}
		return nil
	}).Close()
	return err
}
