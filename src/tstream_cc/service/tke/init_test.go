package tke

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha1"
	"encoding/base64"
	"flag"
	"sort"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"testing"
)

var (
	fTestAppId         = flag.Int64("test.appid", **********, "")
	fTestUin           = flag.String("test.uin", "************", "")
	fTestSubAccountUin = flag.String("test.subaccount.uin", "************", "")
	fTestRegion        = flag.String("test.region", "ap-nanjing",
		"e.g. ap-guangzhou ap-beijing ap-shanghai ap-chengdu")
	fTestUniqVpcId                    = flag.String("test.uniq.vpc.id", "vpc-dkj09k2d", "")
	fTestUniqSubnetId                 = flag.String("test.uniq.subnet.id", "subnet-q60aydeq", "")
	fTestZone                         = flag.String("test.zone", "ap-nanjing-1", "")
	fTestClusterCidr                  = flag.String("test.cluster.cidr", "10.0.64.0/20", "")
	fTestMaxNodePodNum                = flag.Uint64("test.max.node.pod.num", 32, "")
	fTestClusterId                    = flag.String("test.cluster.id", "", "")
	fTestClusterInstanceId            = flag.String("test.cluster.instance.id", "", "")
	fTestClusterSerialId              = flag.String("test.cluster.serial.id", "", "cluster-xxx")
	fTestOceanusTencentCloudApiDomain = flag.String("test.oceanus.tencentcloudapi.domain", "oceanus.dev.tencentcloudapi.com", "")
	fTestSecretId                     = flag.String("test.secret.id", "--", "")
	fTestSecretKey                    = flag.String("test.secret.key", "--", "")
	fTestToken                        = flag.String("test.token", "", "")
	fTestSgId                         = flag.String("test.sgid", "sg-gost19xn", "")

	fTestDbUrl = flag.String("test.db.url", "root:root@tcp(127.0.0.1:3306)/online_galileo?charset=utf8", "")
)

type oceanusResponseBody struct {
	Response *oceanusResponse `json:"Response"`
}

type oceanusResponse struct {
	Error       *oceanusError `json:"Error"`
	SecretId    string        `json:"SecretId"`
	SecretKey   string        `json:"SecretKey"`
	Token       string        `json:"Token"`
	ExpiredTime int64         `json:"ExpiredTime"`
}

type oceanusError struct {
	Code    string `json:"Code"`
	Message string `json:"Message"`
}

func init() {
	testing.Init()
	flag.Parse()

}

func initTx(t *testing.T) {
	tx, err := dao.NewDataSourceTransactionManager(*fTestDbUrl)
	if err != nil {
		t.Fatal("darwin Process create DataSourceTransactionManager err", err)
	}
	service.SetTxManager(tx)
}

func sign(secretKey, domain string, params map[string]string) string {
	keys := make([]string, len(params))
	i := 0
	for k, _ := range params {
		keys[i] = k
		i++
	}
	sort.Strings(keys)
	var buf bytes.Buffer
	buf.WriteString("GET")
	buf.WriteString(domain)
	buf.WriteString("?")
	for i, k := range keys {
		buf.WriteString(k)
		buf.WriteString("=")
		buf.WriteString(params[k])
		if i < len(keys)-1 {
			buf.WriteString("&")
		}
	}
	mac := hmac.New(sha1.New, []byte(secretKey))
	mac.Write(buf.Bytes())
	signByte := mac.Sum(nil)
	signature := base64.StdEncoding.EncodeToString(signByte)
	return signature
}
