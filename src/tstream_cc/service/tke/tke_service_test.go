package tke

import (
	"crypto/tls"
	"encoding/json"
	"fmt"
	cvm "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/cvm/v20170312"
	tke "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/tke/v20180525"
	"io/ioutil"
	"k8s.io/client-go/tools/clientcmd"
	"math/rand"
	"net/http"
	"strconv"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/tke"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config/configure_center"
	"testing"
	"time"
)

func TestTkeService_CreateClusterWithScsAccount(t *testing.T) {
	initTx(t)
	tkeService := GetTkeService()

	cc, err := configure_center.CC(*fTestRegion).FlowCC().TkeCC().ClusterConfig(*fTestZone)
	if err != nil {
		t.Fatal(err)
	}

	request := tkeService.NewDefaultCreateClusterRequestBuilder().
		WithClusterCIDRSettingsBasic(*fTestClusterCidr, false, *fTestMaxNodePodNum).
		WithRunInstancesForNode(nil, model.TKE_NODE_ROLE, []*cvm.RunInstancesRequest{
			tkeService.NewDefaultControlNodeRunInstancesRequestBuilder().
				WithPlacement(*fTestZone, model.TKE_PROJECT_ID).
				WithVirtualPrivateCloud(*fTestUniqVpcId, *fTestUniqSubnetId).
				WithSecurityGroup(*fTestSgId).
				Build(),
		}, map[string]string{model.TKE_CVM_LABEL_KEY: model.TKE_CONTROL_NODE_LABEL_VAL}).
		WithInstanceDataDiskMountSettings(model.TKE_CONTROL_NODE_INSTANCE_TYPE, *fTestZone, model.TKE_DATA_DISK_AUTO_FORMAT_AND_MOUNT,
			model.TKE_DATA_DISK_FILE_SYSTEM, model.TKE_DATA_DISK_MOUNT_TARGET, model.TKE_CVM_DISK_TYPE,
			model.TKE_CONTROL_NODE_DISK_SIZE).
		WithClusterBasicSettings(model.TKE_CLUSTER_OS, model.TKE_CLUSTER_VERSION, fmt.Sprintf("%s-test", *fTestUin), *fTestUniqVpcId,
			model.TKE_PROJECT_ID, false).
		WithInstanceAdvancedSettings(map[string]string{model.TKE_CVM_LABEL_KEY: model.TKE_CONTROL_NODE_LABEL_VAL},
			cc.UserScript).
		Build()

	if clusterId, err := tkeService.CreateClusterWithScsAccount(*fTestRegion, request); err != nil {
		t.Error(err)
	} else {
		t.Logf("success, clusterId %s", clusterId)
	}
}

func TestTkeService_DescribeClusterInstancesWithScsAccount(t *testing.T) {
	initTx(t)
	tkeService := GetTkeService()
	if totalCount, instanceSet, err := tkeService.DescribeClusterInstancesWithScsAccount(*fTestRegion,
		tkeService.NewDefaultDescribeClusterInstancesRequestBuilder().
			WithClusterId(*fTestClusterId).
			Build()); err != nil {
		t.Error(err)
	} else {
		b, _ := json.MarshalIndent(instanceSet, "", " ")
		t.Logf("success totalCount %d, instanceSet %s", totalCount, string(b))
	}
}

func TestTkeService_DescribeClustersWithScsAccount(t *testing.T) {
	initTx(t)
	tkeService := GetTkeService()
	if totalCount, clusters, err := tkeService.DescribeClustersWithScsAccount(*fTestRegion,
		tkeService.NewDefaultDescribeClustersRequestBuilder().
			WithClusterIds(*fTestClusterId).
			Build()); err != nil {
		t.Error(err)
	} else {
		b, _ := json.MarshalIndent(clusters, "", " ")
		t.Logf("success totalCount %d, clusters %s", totalCount, string(b))
	}

	if totalCount, clusters, err := tkeService.DescribeClustersWithScsAccount(*fTestRegion,
		tkeService.NewDefaultDescribeClustersRequestBuilder().
			WithFilterClusterName([]string{*fTestClusterSerialId}).
			Build()); err != nil {
		t.Error(err)
	} else {
		b, _ := json.MarshalIndent(clusters, "", " ")
		t.Logf("success totalCount %d, clusters %s", totalCount, string(b))
	}
}

func TestTkeService_DescribeClusters(t *testing.T) {
	initTx(t)
	tkeService := GetTkeService()
	if totalCount, clusters, err := tkeService.DescribeClusters(*fTestSecretId, *fTestSecretKey, *fTestToken, *fTestRegion,
		tkeService.NewDefaultDescribeClustersRequestBuilder().
			WithClusterIds(*fTestClusterId).
			Build()); err != nil {
		t.Error(err)
	} else {
		b, _ := json.MarshalIndent(clusters, "", " ")
		t.Logf("success totalCount %d, clusters %s", totalCount, string(b))
	}
}

func parseClusterSecurityResponse(t *testing.T, response *tke.DescribeClusterSecurityResponse) {
	b, _ := json.MarshalIndent(response, "", " ")
	t.Logf("success %s", string(b))

	if len(*response.Response.JnsGwEndpoint) == 0 {
		t.Fatalf("JnsGwEndpoint not found")
	}

	if kubeConfig, err := clientcmd.Load([]byte(*response.Response.Kubeconfig)); err != nil {
		t.Fatal(err)
	} else if b, err := clientcmd.Write(*kubeConfig); err != nil {
		t.Fatal(err)
	} else {
		t.Logf("kubeConfig:\n%s", string(b))
		b, err = json.MarshalIndent(kubeConfig, "", " ")
		t.Logf("%s", string(b))

		cluster, exist := kubeConfig.Clusters[*fTestClusterId]
		if !exist {
			t.Fatalf("cluster %s not found in kubeConfig", *fTestClusterId)
		}

		cluster.Server = fmt.Sprintf("https://%s", *response.Response.JnsGwEndpoint)

		b, err = clientcmd.Write(*kubeConfig)
		t.Logf("kubeConfig:\n%s", string(b))
		b, err = json.MarshalIndent(kubeConfig, "", " ")
		t.Logf("%s", string(b))
	}
}

func TestTkeService_DescribeClusterSecurity(t *testing.T) {
	initTx(t)
	tkeService := GetTkeService()
	if response, err := tkeService.DescribeClusterSecurity(*fTestSecretId, *fTestSecretKey, *fTestToken, *fTestRegion,
		tkeService.NewDefaultDescribeClusterSecurityRequestBuilder().
			WithClusterId(*fTestClusterId).
			WithJnsGwEndpointEnable(true).
			Build()); err != nil {
		t.Error(err)
	} else {
		parseClusterSecurityResponse(t, response)
	}
}

func TestTkeService_DescribeClusterSecurityWithScsAccount(t *testing.T) {
	initTx(t)
	tkeService := GetTkeService()
	if response, err := tkeService.DescribeClusterSecurityWithScsAccount(*fTestRegion,
		tkeService.NewDefaultDescribeClusterSecurityRequestBuilder().
			WithClusterId(*fTestClusterId).
			WithJnsGwEndpointEnable(true).
			Build()); err != nil {
		t.Error(err)
	} else {
		parseClusterSecurityResponse(t, response)
	}
}

func TestTkeService_DescribeEKSClusterCredential(t *testing.T) {
	initTx(t)
	tkeService := GetTkeService()
	if response, err := tkeService.DescribeEKSClusterCredential(*fTestSecretId, *fTestSecretKey, *fTestToken, *fTestRegion,
		tkeService.NewDefaultDescribeEKSClusterCredentialRequestBuilder().
			WithClusterId(*fTestClusterId).
			WithJnsGatewayEnable(true).
			Build()); err != nil {
		t.Error(err)
	} else {
		b, _ := json.MarshalIndent(response, "", " ")
		t.Logf("success %s", string(b))
	}
}

func TestTkeService_DescribeEKSClusterCredentialWithScsAccount(t *testing.T) {
	initTx(t)
	tkeService := GetTkeService()
	if response, err := tkeService.DescribeEKSClusterCredentialWithScsAccount(*fTestRegion,
		tkeService.NewDefaultDescribeEKSClusterCredentialRequestBuilder().
			WithClusterId(*fTestClusterId).
			WithJnsGatewayEnable(true).
			Build()); err != nil {
		t.Error(err)
	} else {
		b, _ := json.MarshalIndent(response, "", " ")
		t.Logf("success %s", string(b))
	}
}

func TestTkeService_DescribeExistedInstancesWithScsAccount(t *testing.T) {
	initTx(t)
	tkeService := GetTkeService()

	instanceIds := []string{}

	if totalCount, instanceSet, err := tkeService.DescribeClusterInstancesWithScsAccount(*fTestRegion,
		tkeService.NewDefaultDescribeClusterInstancesRequestBuilder().
			WithClusterId(*fTestClusterId).
			Build()); err != nil {
		t.Fatal(err)
	} else {
		b, _ := json.MarshalIndent(instanceSet, "", " ")
		t.Logf("success totalCount %d, instanceSet %s", totalCount, string(b))
		for _, i := range instanceSet {
			instanceIds = append(instanceIds, *i.InstanceId)
		}
	}

	if totalCount, instanceSet, err := tkeService.DescribeExistedInstancesWithScsAccount(*fTestRegion,
		tkeService.NewDefaultDescribeExistedInstancesRequestBuilder().
			WithInstanceIds(instanceIds).
			Build()); err != nil {
		t.Error(err)
	} else {
		b, _ := json.MarshalIndent(instanceSet, "", " ")
		t.Logf("success, totalCount %d, instanceSet %s", totalCount, string(b))
	}
}

func TestTkeService_CreateClusterInstancesWithScsAccount(t *testing.T) {
	initTx(t)
	tkeService := GetTkeService()

	cc, err := configure_center.CC(*fTestRegion).FlowCC().TkeCC().ClusterConfig(*fTestZone)
	if err != nil {
		t.Fatal(err)
	}

	request := tkeService.NewDefaultCreateClusterInstancesRequestBuilder().
		WithClusterId(*fTestClusterId).
		WithRunInstancePara(tkeService.NewDefaultWorkerNodeRunInstancesRequestBuilder().
			WithPlacement(*fTestZone, model.TKE_PROJECT_ID).
			WithVirtualPrivateCloud(*fTestUniqVpcId, *fTestUniqSubnetId).
			WithSecurityGroup(*fTestSgId).
			Build()).
		WithInstanceAdvancedSettings(model.TKE_DATA_DISK_AUTO_FORMAT_AND_MOUNT,
			model.TKE_DATA_DISK_FILE_SYSTEM, model.TKE_DATA_DISK_MOUNT_TARGET, model.TKE_CVM_DISK_TYPE,
			model.TKE_WORKER_NODE_DISK_SIZE, map[string]string{model.TKE_CVM_LABEL_KEY: model.
				TKE_WORKER_NODE_LABEL_VAL}, cc.UserScript).
		Build()

	if err := tkeService.CreateClusterInstancesWithScsAccount(*fTestRegion, request); err != nil {
		t.Error(err)
	} else {
		t.Logf("success")
	}
}

func TestDescribeClsAuthInfo(t *testing.T) {
	domain := *fTestOceanusTencentCloudApiDomain + "/"

	params := map[string]string{}
	params["ClusterId"] = *fTestClusterId
	params["Version"] = "2019-04-22"
	params["Nonce"] = strconv.Itoa(rand.Int())
	params["Timestamp"] = fmt.Sprintf("%d", time.Now().Unix())
	params["Action"] = "DescribeClsAuthInfo"
	params["SecretId"] = *fTestSecretId
	params["Region"] = *fTestRegion
	params["Token"] = ""
	params["Signature"] = sign(*fTestSecretKey, domain, params)

	req, _ := http.NewRequest("GET", "https://"+domain, nil)

	q := req.URL.Query()
	for k, v := range params {
		q.Add(k, v)
	}
	req.URL.RawQuery = q.Encode()

	client := http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
	}

	resp, err := client.Do(req)
	if err != nil {
		t.Fatal(err)
	}
	if resp.StatusCode != http.StatusOK {
		t.Fatalf("%s", resp.Status)
	}
	data, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		t.Fatal(err)
	}
	t.Log("Response body ", string(data))

	rb := oceanusResponseBody{}
	err = json.Unmarshal(data, &rb)
	if err != nil {
		t.Fatal(err)
	}
	b, _ := json.MarshalIndent(rb, "", " ")
	t.Logf("success, %s", string(b))
}

func TestTkeService_DescribeClusterRouteTablesWithScsAccount(t *testing.T) {
	initTx(t)
	if totalCount, response, err := GetTkeService().DescribeClusterRouteTablesWithScsAccount(*fTestRegion, GetTkeService().NewDefaultDescribeClusterRouteTablesRequestBuilder().Build()); err != nil {
		t.Error(err)
	} else {
		b, _ := json.MarshalIndent(response, "", " ")
		t.Logf("success totalCount %d, instanceSet %s", totalCount, string(b))
	}
}

func TestTkeService_GetKubeConfig(t *testing.T) {
	initTx(t)
	if kubeConfig, err := GetTkeService().GetKubeConfig(*fTestSecretId, *fTestSecretKey, "", *fTestRegion, *fTestClusterInstanceId); err != nil {
		t.Error(err)
	} else {
		t.Logf("success\n%s", kubeConfig)
	}
}

func TestTkeService_GetEksKubeConfig(t *testing.T) {
	initTx(t)
	if kubeConfig, err := GetTkeService().GetEksKubeConfig(*fTestSecretId, *fTestSecretKey, "", *fTestRegion, *fTestClusterInstanceId); err != nil {
		t.Error(err)
	} else {
		t.Logf("success\n%s", kubeConfig)
	}
}

func TestTkeService_DeleteClusterWithScsAccount(t *testing.T) {
	initTx(t)
	tkeService := GetTkeService()
	req := tkeService.NewDefaultDeleteClusterRequestBuilder().WithClusterId(*fTestClusterId).
		WithInstanceDeleteMode(model.TKE_DELETE_CLUSTER_MODEL).Build()
	_, err := tkeService.DeleteClusterWithScsAccount(*fTestRegion, req)
	if err != nil {
		t.Fatal(err)
	}

	t.Logf("delete tke %s success", *fTestClusterId)
}

func TestTkeService_DeleteClusterInstancesWithScsAccount(t *testing.T) {
	initTx(t)
	tkeService := GetTkeService()
	req := NewDefaultDeleteClusterInstancesRequestBuilder().WithClusterId(*fTestClusterId).WithInstanceIds(
		[]string{*fTestClusterInstanceId}).Build()
	rsp, err := tkeService.DeleteClusterInstancesWithScsAccount(*fTestRegion, req)
	if err != nil {
		t.Fatal(err)
	}

	b, _ := json.MarshalIndent(rsp, "", " ")
	t.Logf("delete tke instance success, rsp: %s", string(b))
}
