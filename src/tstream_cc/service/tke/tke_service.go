package tke

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"

	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/errors"
	tke "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/tke/v20180525"
	tke3 "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/tke/v20220501"
	v12 "k8s.io/api/apps/v1"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/clientcmd"
	converter "tencentcloud.com/tstream_galileo/src/common/errcode_converter"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	service4 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/tke"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config/common_config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config/configure_center"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cvm"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/qcloud"
	tke2 "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke"
	tke4 "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke/v20220501"
)

var (
	tkeService      *TkeService
	MAX_QUERY_COUNT = 50
)

type TkeService struct {
}

// /////////////////////////////////////////////////////////////////////////////

func (s *TkeService) NewDefaultControlNodeRunInstancesRequestBuilder() *cvm.RunInstancesRequestBuilder {
	//isDev := service4.GetConfStringValue("scsDevEnv")

	// 测试账号为非带宽上移账户，需要设置带宽
	bandwidth := int64(0)
	//if isDev == "true" {
	//	bandwidth = model.TKE_CVM_INTERNET_MAXBANDWIDTH_OUT
	//}
	rainbowOceanusClusterPassword, getPasswordError := common_config.GetRainbowOceanusClusterPassword()
	if getPasswordError != nil {
		logger.Errorf("Failed to get oceanus cluster password in Rainbow")
		return nil
	}

	return cvm.NewRunInstancesRequestBuilder().
		WithInstanceChargeType(constants.TKE_CVM_CHARGE_TYPE).
		WithInstanceChargePrepaid(constants.TKE_CVM_PREPAID_PERIOD, constants.TKE_CVM_RENEW_FLAG).
		WithInstanceType(constants.TKE_CONTROL_NODE_INSTANCE_TYPE).
		WithLoginSettings(rainbowOceanusClusterPassword).
		WithEnhancedService(true, true).
		WithInternetAccessible(constants.TKE_CVM_INTERNET_CHARGE_TYPE, constants.TKE_CVM_PUBLIC_IP_ASSIGNED, bandwidth).
		WithInstanceName(constants.TKE_CONTROL_NODE_LABEL_VAL)
}

func (s *TkeService) NewDefaultWorkerNodeRunInstancesRequestBuilder() *cvm.RunInstancesRequestBuilder {
	//isDev := service4.GetConfStringValue("scsDevEnv")

	// 测试账号为非带宽上移账户，需要设置带宽
	bandwidth := int64(0)
	//if isDev == "true" {
	//	bandwidth = model.TKE_CVM_INTERNET_MAXBANDWIDTH_OUT
	//}
	rainbowOceanusClusterPassword, getPasswordError := common_config.GetRainbowOceanusClusterPassword()
	if getPasswordError != nil {
		logger.Errorf("Failed to get oceanus cluster password in Rainbow")
		return nil
	}
	return cvm.NewRunInstancesRequestBuilder().
		WithInstanceChargeType(constants.TKE_CVM_CHARGE_TYPE).
		WithInstanceChargePrepaid(constants.TKE_CVM_PREPAID_PERIOD, constants.TKE_CVM_RENEW_FLAG).
		WithInstanceType(constants.TKE_WORKER_NODE_INSTANCE_TYPE).
		WithInstanceCount(constants.TKE_WORKER_NODE_NUM).
		WithLoginSettings(rainbowOceanusClusterPassword).
		WithEnhancedService(true, true).
		WithInternetAccessible(constants.TKE_CVM_INTERNET_CHARGE_TYPE, constants.TKE_CVM_PUBLIC_IP_ASSIGNED, bandwidth)
}

func (s *TkeService) NewDefaultCreateInnerClusterRequestBuilder() *CreateClusterRequestBuilder {
	return NewCreateClusterRequestBuilder().
		WithDomain(constants.TKE_API_DOMAIN_INTERNAL).
		WithClusterType(constants.TKE_CLUSTER_TYPE)
}

func (s *TkeService) NewDefaultCreateClusterRequestBuilder() *CreateClusterRequestBuilder {
	return NewCreateClusterRequestBuilder().
		WithDomain(constants.TKE_API_DOMAIN_INTERNAL).
		WithClusterCIDRSettingsService(constants.TKE_MAX_CLUSTER_SERVICE_NUM, constants.TKE_SERVICE_CIDR).
		WithClusterType(constants.TKE_CLUSTER_TYPE)
}

func (s *TkeService) NewDefaultCreateClusterReleaseRequestBuilder() *CreateClusterReleaseRequestBuilder {
	return NewCreateClusterReleaseRequestBuilder().
		WithDomain(constants.TKE_API_DOMAIN_INTERNAL).
		WithChartFrom(constants.TKE_CHART_FROM_MARKET).
		WithClusterType(constants.TKE_CLUSTER_TYPE_SMALL_FORM).
		WithChartNamespace(constants.TKE_CHART_NAMESPACE)
}

func (s *TkeService) NewDefaultUpgradeClusterReleaseRequestBuilder() *UpgradeClusterReleaseRequestBuilder {
	return NewUpgradeClusterReleaseRequestBuilder().
		WithDomain(constants.TKE_API_DOMAIN_INTERNAL).
		WithChartFrom(constants.TKE_CHART_FROM_MARKET).
		WithClusterType(constants.TKE_CLUSTER_TYPE_SMALL_FORM).
		WithChartNamespace(constants.TKE_CHART_NAMESPACE)
}

func (s *TkeService) NewDefaultCancelClusterReleaseRequestBuilder() *CancelClusterReleaseRequestBuilder {
	return NewCancelClusterReleaseRequestBuilder().WithClusterType(constants.TKE_CLUSTER_TYPE_SMALL_FORM)
}

func (s *TkeService) NewDefaultDescribeClusterReleaseDetailsRequestBuilder() *DescribeClusterReleaseDetailsRequestBuilder {
	return NewDescribeClusterReleaseDetailsRequestBuilder().
		WithDomain(constants.TKE_API_DOMAIN_INTERNAL).
		WithClusterType(constants.TKE_CLUSTER_TYPE_SMALL_FORM)
}

func (s *TkeService) CreateCluster(secretId, secretKey, token, region string, request *tke2.CreateClusterRequest) (
	clusterId string, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("CreateCluster error"))

	prof := qcloud.NewClientProfile()
	credential := common.NewTokenCredential(secretId, secretKey, token)
	client, err := tke2.NewClient(credential, region, prof)
	if err != nil {
		return "", converter.GenCommonErrorCode("", err)
	}
	logger.Infof("CreateCluster request: %s", request.ToJsonString())
	response, err := client.CreateCluster(request)
	if err != nil {
		return "", converter.GenCommonErrorCode("", err)
	}
	logger.Infof("CreateCluster response: %s", response.ToJsonString())
	return *response.Response.ClusterId, nil
}

func (s *TkeService) CreateClusterRelease(secretId, secretKey, token, region string, request *tke2.CreateClusterReleaseRequest) (
	release *tke2.PendingRelease, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("DescribeClusterReleaseDetails error"))

	prof := qcloud.NewClientProfile()
	credential := common.NewTokenCredential(secretId, secretKey, token)
	client, err := tke2.NewClient(credential, region, prof)
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}
	logger.Infof("CreateClusterRelease request: %s", request.ToJsonString())
	if response, err := client.CreateClusterRelease(request); err != nil {
		// 当应用已存在的时候, 调用 CreateClusterRelease 接口重复安装会返回 403 的 httpCode, 需要转为 TKE_HELM_STATUS_DEPLOYED 状态的返回结果
		resp := s.convertError(fmt.Sprintf("%v", err), "status code is 403", "CreateClusterRelease", request.Name)
		if resp != nil {
			release = resp.(*tke2.PendingRelease)
			return release, nil
		} else {
			return nil, converter.GenCommonErrorCode("", err)
		}
	} else {
		logger.Infof("CreateClusterRelease response: %s", response.ToJsonString())
		return response.Response.Release, nil
	}
}

func (s *TkeService) UpgradeClusterRelease(secretId, secretKey, token, region string, request *tke2.UpgradeClusterReleaseRequest) (
	release *tke2.PendingRelease, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("DescribeClusterReleaseDetails error"))

	prof := qcloud.NewClientProfile()
	credential := common.NewTokenCredential(secretId, secretKey, token)
	client, err := tke2.NewClient(credential, region, prof)
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}
	logger.Infof("UpgradeClusterRelease request: %s", request.ToJsonString())
	response, err := client.UpgradeClusterRelease(request)
	if response != nil {
		logger.Infof("UpgradeClusterRelease response: %s", response.ToJsonString())
	}
	if err != nil {
		return nil, err
	}
	return response.Response.Release, nil
}

//	 方法说明：
//			由于TKE调整了 CreateClusterRelease 和 DescribeClusterReleaseDetails 接口，有些状态返回在异常信息中，该方法用于解析出业务需要的状态
//	 入参说明：
//			rrMsg: 调用方传入的异常信息；
//	 	errFeature: 异常信息匹配特征；
//	 	funcName: 调用方的方法名（只支持 CreateClusterRelease/DescribeClusterReleaseDetails），用于构造不通类型的返回值
//	 	releaseName: 应用名称。便于输出日志
//	 出参说明：
//	     从异常信息中解析处调用方需要的状态后，构造对应的返回值返回
func (s *TkeService) convertError(errMsg string, errFeature string, funcName string, releaseName *string) interface{} {
	logger.Infof("%s call convertError input param: err: %s, errFeature: %s, funcName: %s, releaseName: %s", funcName, errMsg, errFeature, funcName, *releaseName)

	// 当应用存在的时候调用 CreateClusterRelease 接口重复安装会返回403的 httpCode
	var result interface{}
	var status string
	if strings.Contains(errMsg, errFeature) {
		if funcName == "CreateClusterRelease" {
			status = constants.TKE_HELM_STATUS_DEPLOYED
			result = &tke.PendingRelease{
				Status: common.StringPtr(status),
				Name:   releaseName,
			}
		} else if funcName == "DescribeClusterReleaseDetails" {
			status = constants.TKE_HELM_STATUS_UNINSTALLED
			result = &tke.ReleaseDetails{
				Status: common.StringPtr(status),
				Name:   releaseName,
			}
		} else {
			logger.Infof("Invalid funcName: %s", funcName)
		}

		jsonBytes, _ := json.Marshal(result)
		logger.Infof("%s is %s. %s call convertError output param: %s", *releaseName, status, funcName, string(jsonBytes))
		return result
	}
	return nil
}

func (s *TkeService) DescribeClusterReleaseDetails(secretId, secretKey, token, region string, request *tke.DescribeClusterReleaseDetailsRequest) (
	release *tke.ReleaseDetails, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("DescribeClusterReleaseDetails error"))

	prof := qcloud.NewClientProfile()
	credential := common.NewTokenCredential(secretId, secretKey, token)
	client, err := tke.NewClient(credential, region, prof)
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}
	logger.Infof("DescribeClusterReleaseDetails request: %s", request.ToJsonString())
	if response, err := client.DescribeClusterReleaseDetails(request); err != nil {
		// 当应用不存在的时候调用 DescribeClusterReleaseDetails 接口会返回 406 的 httpCode, 需要转为 TKE_HELM_STATUS_UNINSTALLED 的结果
		resp := s.convertError(fmt.Sprintf("%v", err), "release: not found", "DescribeClusterReleaseDetails", request.Name)
		if resp != nil {
			temp := resp.(*tke.ReleaseDetails)
			release = temp
			return release, nil
		} else {
			return nil, converter.GenCommonErrorCode("", err)
		}

	} else {
		logger.Debugf("DescribeClusterReleaseDetails response: %s", response.ToJsonString())
		return response.Response.Release, nil
	}
}

func (s *TkeService) CreateClusterWithScsAccount(region string, request *tke2.CreateClusterRequest) (clusterId string, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("CreateClusterWithScsAccount error"))

	secretId, secretKey, err := service.GetSecretIdAndKeyOfScs()
	if err != nil {
		return "", converter.GenCommonErrorCode("", err)
	}

	return s.CreateCluster(secretId, secretKey, "", region, request)
}

func (s *TkeService) CreateClusterWithScsAccountByNetEnvironmentType(netEnvironmentType int8, region string, request *tke2.CreateClusterRequest) (clusterId string, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("CreateClusterWithScsAccount error"))

	secretId, secretKey, err := service.GetSecretIdAndKeyByNetworkEnvType(netEnvironmentType)
	if err != nil {
		return "", converter.GenCommonErrorCode("", err)
	}

	return s.CreateCluster(secretId, secretKey, "", region, request)
}

func (s *TkeService) CreateEKSClusterWithScsAccount(region string, request *tke.CreateEKSClusterRequest) (clusterId string, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("CreateEKSClusterWithScsAccount error"))

	secretId, secretKey, err := service.GetSecretIdAndKeyOfScs()
	if err != nil {
		return "", converter.GenCommonErrorCode("", err)
	}

	return s.CreateEKSCluster(secretId, secretKey, region, request)
}

func (s *TkeService) CreateEKSCluster(secretId, secretKey, region string, request *tke.CreateEKSClusterRequest) (
	clusterId string, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("CreateEKSCluster error"))

	prof := qcloud.NewClientProfile()
	// 上海自动驾驶云专区走内网
	if region == constants.AP_SHANGHAI_ADC {
		prof.HttpProfile.Endpoint = constants.TKE_API_DOMAIN_INTERNAL
	}
	credential := common.NewCredential(secretId, secretKey)
	client, err := tke.NewClient(credential, region, prof)
	if err != nil {
		return "", converter.GenCommonErrorCode("", err)
	}
	logger.Infof("CreateEKSCluster request: %s", request.ToJsonString())
	response, err := client.CreateEKSCluster(request)
	if err != nil {
		return "", converter.GenCommonErrorCode("", err)
	}
	logger.Infof("CreateEKSCluster response: %s", response.ToJsonString())
	return *response.Response.ClusterId, nil
}

func (s *TkeService) CreateClusterReleaseWithScsAccount(clusterGroup *table.ClusterGroup, request *tke2.CreateClusterReleaseRequest) (release *tke2.PendingRelease, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("CreateClusterReleaseWithScsAccount error"))

	secretId, secretKey, err := service.GetSecretIdAndKeyByNetworkEnvType(clusterGroup.NetEnvironmentType)
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}

	return s.CreateClusterRelease(secretId, secretKey, "", clusterGroup.Region, request)
}

func (s *TkeService) UpgradeClusterReleaseWithScsAccount(clusterGroup *table.ClusterGroup, request *tke2.UpgradeClusterReleaseRequest) (release *tke2.PendingRelease, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("UpgradeClusterReleaseWithScsAccount error"))

	secretId, secretKey, err := service.GetSecretIdAndKeyByNetworkEnvType(clusterGroup.NetEnvironmentType)
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}

	return s.UpgradeClusterRelease(secretId, secretKey, "", clusterGroup.Region, request)
}

func (s *TkeService) DescribeClusterReleaseDetailsWithScsAccount(clusterGroup *table.ClusterGroup, request *tke.DescribeClusterReleaseDetailsRequest) (release *tke.ReleaseDetails, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("DescribeClusterReleaseDetailsWithScsAccount error"))

	secretId, secretKey, err := service.GetSecretIdAndKeyByNetworkEnvType(clusterGroup.NetEnvironmentType)
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}

	return s.DescribeClusterReleaseDetails(secretId, secretKey, "", clusterGroup.Region, request)
}

func (s *TkeService) NewDefaultDescribeClusterInstancesRequestBuilder() *DescribeClusterInstancesRequestBuilder {
	return NewDescribeClusterInstancesRequestBuilder().
		WithDomain(constants.TKE_API_DOMAIN_INTERNAL)
}

func (s *TkeService) NewDefaultDescribeClusterInstancesRequestBuilderV2022() *DescribeClusterInstancesRequestBuilderV2022 {
	return NewDescribeClusterInstancesRequestBuilderV2022().
		WithDomain(constants.TKE_API_DOMAIN_INTERNAL)
}

func (s *TkeService) DescribeClusterInstances(secretId, secretKey, token, region string, request *tke.DescribeClusterInstancesRequest) (
	totalCount uint64, instanceSet []*tke.Instance, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("DescribeClusterInstances error"))

	prof := qcloud.NewClientProfile()
	credential := common.NewTokenCredential(secretId, secretKey, token)
	client, err := tke.NewClient(credential, region, prof)
	if err != nil {
		return 0, nil, converter.GenCommonErrorCode("", err)
	}
	logger.Infof("DescribeClusterInstances request: %s", request.ToJsonString())
	if response, err := client.DescribeClusterInstances(request); err != nil {
		return 0, nil, converter.GenCommonErrorCode("", err)
	} else {
		logger.Infof("DescribeClusterInstances response: %s", response.ToJsonString())
		return *response.Response.TotalCount, response.Response.InstanceSet, nil
	}
}

func (s *TkeService) DescribeClusterInstancesV2022(secretId, secretKey, token, region string, request *tke4.DescribeClusterInstancesRequest) (
	totalCount uint64, instanceSet []*tke4.Instance, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("DescribeClusterInstancesV2022 error"))

	prof := qcloud.NewClientProfile()
	credential := common.NewTokenCredential(secretId, secretKey, token)
	client, err := tke4.NewClient(credential, region, prof)
	if err != nil {
		return 0, nil, converter.GenCommonErrorCode("", err)
	}
	logger.Infof("DescribeClusterInstancesV2022 request: %s", request.ToJsonString())
	if response, err := client.DescribeClusterInstances(request); err != nil {
		return 0, nil, converter.GenCommonErrorCode("", err)
	} else {
		logger.Infof("DescribeClusterInstancesV2022 response: %s", response.ToJsonString())
		return *response.Response.TotalCount, response.Response.InstanceSet, nil
	}
}

/**
 *  使用Oceanus账号的secretId, secretKey调用Tke的接口完成Oceanus账号下托管的Tke集群的跨租户子网更新
 *  使用场景: 用户的Oceanus集群(Tke)的子网不够使用了导致Flink作业无法启动，可以通过更换子网的方式增加Tke底层绑定的网卡数量
 */
func (s *TkeService) UpdateMetaFeatureWithScsAccount(region string, request *tke2.UpdateMetaFeatureRequest) (err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("UpdateMetaFeature error"))

	secretId, secretKey, err := service.GetSecretIdAndKeyOfScs()
	if err != nil {
		return converter.GenCommonErrorCode("", err)
	}

	return s.UpdateMetaFeature(secretId, secretKey, region, request)
}

func (s *TkeService) UpdateMetaFeature(secretId, secretKey, region string, request *tke2.UpdateMetaFeatureRequest) (err error) {
	client, err := s.newClient(secretId, secretKey, "", region)
	if err != nil {
		return converter.GenCommonErrorCode("", err)
	}

	logger.Infof("UpdateMetaFeature request: %s", request.ToJsonString())
	response, err := client.UpdateMetaFeature(request)

	logger.Infof("UpdateMetaFeature response: %s", response.ToJsonString())
	return err
}

/**
 *  使用Oceanus账号的secretId, secretKey调用Tke的接口完成Oceanus账号下托管的Eks集群的跨租户子网更新
 *  使用场景: 用户的Oceanus集群(Eks)的子网不够使用了导致Flink作业无法启动，可以通过更换子网的方式增加Eks底层绑定的网卡数量
 */
func (s *TkeService) UpdateMetaFeatureForEksWithScsAccount(region string, request *tke2.UpdateMetaFeatureForEksRequest) (err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("UpdateMetaFeatureForEks error"))

	secretId, secretKey, err := service.GetSecretIdAndKeyOfScs()
	if err != nil {
		return converter.GenCommonErrorCode("", err)
	}

	return s.UpdateMetaFeatureForEks(secretId, secretKey, region, request)
}

func (s *TkeService) UpdateMetaFeatureForEks(secretId, secretKey, region string, request *tke2.UpdateMetaFeatureForEksRequest) (err error) {
	client, err := s.newClient(secretId, secretKey, "", region)
	if err != nil {
		return converter.GenCommonErrorCode("", err)
	}

	logger.Infof("UpdateMetaFeatureForEks request: %s", request.ToJsonString())
	response, err := client.UpdateMetaFeatureForEks(request)

	logger.Infof("UpdateMetaFeatureForEks response: %s", response.ToJsonString())
	return err
}

func (s *TkeService) DescribeIPAMDWithScsAccount(region string, request *tke.DescribeIPAMDRequest) (describeIPAMDResponse *tke.DescribeIPAMDResponse, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("DescribeIPAMD error"))

	secretId, secretKey, err := service.GetSecretIdAndKeyOfScs()
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}

	return s.DescribeIPAMD(secretId, secretKey, region, request)
}

func (s *TkeService) DescribeIPAMD(secretId, secretKey, region string, request *tke.DescribeIPAMDRequest) (describeIPAMDResponse *tke.DescribeIPAMDResponse, err error) {

	prof := qcloud.NewClientProfile()
	// 上海自动驾驶云专区走内网
	if region == constants.AP_SHANGHAI_ADC {
		prof.HttpProfile.Endpoint = constants.TKE_API_DOMAIN_INTERNAL
	}

	credential := common.NewCredential(secretId, secretKey)
	client, err := tke.NewClient(credential, region, prof)
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}

	logger.Infof("DescribeIPAMD request: %s", request.ToJsonString())
	response, err := client.DescribeIPAMD(request)
	logger.Infof("DescribeIPAMD response: %s", response.ToJsonString())
	return response, err
}

func (s *TkeService) DescribeClusterInstancesWithScsAccountByNetEnvironmentType(netEnvironmentType int8, region string, request *tke.DescribeClusterInstancesRequest) (totalCount uint64, instanceSet []*tke.Instance, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("DescribeClusterInstancesWithScsAccountByNetEnvironmentType error"))

	secretId, secretKey, err := service.GetSecretIdAndKeyByNetworkEnvType(netEnvironmentType)
	if err != nil {
		return 0, nil, converter.GenCommonErrorCode("", err)
	}

	return s.DescribeClusterInstances(secretId, secretKey, "", region, request)
}

func (s *TkeService) NewDefaultDescribeClustersRequestBuilder() *DescribeClustersRequestBuilder {
	return NewDescribeClustersRequestBuilder().
		WithDomain(constants.TKE_API_DOMAIN_INTERNAL).
		WithOffsetLimit(0, 100)
}

func (s *TkeService) DescribeClusters(secretId, secretKey, token, region string, request *tke.DescribeClustersRequest) (
	totalCount int64, instanceSet []*tke.Cluster, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("DescribeClusters error"))

	prof := qcloud.NewClientProfile()
	credential := common.NewTokenCredential(secretId, secretKey, token)
	client, err := tke.NewClient(credential, region, prof)
	if err != nil {
		return 0, nil, converter.GenCommonErrorCode("", err)
	}
	logger.Infof("###DescribeClusters request: %s", request.ToJsonString())
	if response, err := client.DescribeClusters(request); err != nil {
		return 0, nil, converter.GenCommonErrorCode("", err)
	} else {
		logger.Infof("###DescribeClusters response: %s", response.ToJsonString())
		return *response.Response.TotalCount, response.Response.Clusters, nil
	}
}

func (s *TkeService) DescribeEKSClustersWithScsAccount(region string, request *tke.DescribeEKSClustersRequest) (totalCount uint64, instanceSet []*tke.EksCluster, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("DescribeEksClustersWithScsAccount error"))

	secretId, secretKey, err := service.GetSecretIdAndKeyOfScs()
	if err != nil {
		return 0, nil, err
	}

	return s.DescribeEKSClusters(secretId, secretKey, region, request)
}

func (s *TkeService) DescribeEKSClusters(secretId, secretKey, region string, request *tke.DescribeEKSClustersRequest) (
	totalCount uint64, instanceSet []*tke.EksCluster, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("DescribeEKSClusters error"))

	credential := common.NewCredential(secretId, secretKey)
	prof := qcloud.NewClientProfile()
	// 上海自动驾驶云专区走内网
	if region == constants.AP_SHANGHAI_ADC {
		prof.HttpProfile.Endpoint = constants.TKE_API_DOMAIN_INTERNAL
	}
	client, err := tke.NewClient(credential, region, prof)
	if err != nil {
		return 0, nil, converter.GenCommonErrorCode("", err)
	}
	logger.Infof("###DescribeEKSClusters request: %s", request.ToJsonString())
	if response, err := client.DescribeEKSClusters(request); err != nil {
		return 0, nil, converter.GenCommonErrorCode("", err)
	} else {
		logger.Infof("###DescribeEKSClusters response: %s", response.ToJsonString())
		return *response.Response.TotalCount, response.Response.Clusters, nil
	}
}

func (s *TkeService) DescribeClustersWithScsAccount(region string, request *tke.DescribeClustersRequest) (totalCount int64, instanceSet []*tke.Cluster, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("DescribeClustersWithScsAccount error"))

	secretId, secretKey, err := service.GetSecretIdAndKeyOfScs()
	if err != nil {
		return 0, nil, err
	}

	return s.DescribeClusters(secretId, secretKey, "", region, request)
}

func (s *TkeService) DescribeClustersWithScsAccountByNetEnvironmentType(networkEnvironmentType int8, region string, request *tke.DescribeClustersRequest) (totalCount int64, instanceSet []*tke.Cluster, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("DescribeClustersWithScsAccount error"))

	// 根据网络环境选择不同环境的密钥
	secretId, secretKey, err := service.GetSecretIdAndKeyByNetworkEnvType(networkEnvironmentType)
	if err != nil {
		return 0, nil, err
	}

	return s.DescribeClusters(secretId, secretKey, "", region, request)
}

func (s *TkeService) NewDefaultDescribeClusterSecurityRequestBuilder() *DescribeClusterSecurityRequestBuilder {
	return NewDescribeClusterSecurityRequestBuilder().
		WithDomain(constants.TKE_API_DOMAIN_INTERNAL)
}

func (s *TkeService) DescribeClusterSecurity(secretId, secretKey, token, region string, request *tke2.DescribeClusterSecurityRequest) (
	response *tke2.DescribeClusterSecurityResponse, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("DescribeClusterSecurity error"))

	prof := qcloud.NewClientProfile()
	credential := common.NewTokenCredential(secretId, secretKey, token)
	client, err := tke2.NewClient(credential, region, prof)
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}
	logger.Infof("DescribeClusterSecurity request: %s", request.ToJsonString())
	if response, err := client.DescribeClusterSecurity(request); err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	} else {
		logger.Infof("DescribeClusterSecurity response: %s", response.ToJsonString())
		return response, nil
	}
}

func (s *TkeService) DescribeClusterSecurityWithScsAccount(region string, request *tke2.DescribeClusterSecurityRequest) (response *tke2.DescribeClusterSecurityResponse, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("DescribeClusterSecurityWithScsAccount error"))

	secretId, secretKey, err := service.GetSecretIdAndKeyOfScs()
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}

	return s.DescribeClusterSecurity(secretId, secretKey, "", region, request)
}

func (s *TkeService) NewDefaultDescribeEKSClusterCredentialRequestBuilder() *DescribeEKSClusterCredentialRequestBuilder {
	return NewDescribeEKSClusterCredentialRequestBuilder().
		WithDomain(constants.TKE_API_DOMAIN_INTERNAL)
}

func (s *TkeService) DescribeEKSClusterCredential(secretId, secretKey, token, region string, request *DescribeEKSClusterCredentialRequest) (
	response *DescribeEKSClusterCredentialResponse, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("DescribeEKSClusterCredential error"))

	prof := qcloud.NewClientProfile()
	credential := common.NewTokenCredential(secretId, secretKey, token)
	client, err := tke.NewClient(credential, region, prof)
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}
	logger.Infof("DescribeEKSClusterCredential request: %s", request.ToJsonString())
	if response, err := DescribeEKSClusterCredential(client, request); err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	} else {
		logger.Infof("DescribeEKSClusterCredential response: %s", response.ToJsonString())
		return response, nil
	}
}

func (s *TkeService) DescribeEKSClusterCredentialWithScsAccount(region string, request *DescribeEKSClusterCredentialRequest) (response *DescribeEKSClusterCredentialResponse, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("DescribeEKSClusterCredentialWithScsAccount error"))

	secretId, secretKey, err := service.GetSecretIdAndKeyOfScs()
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}

	return s.DescribeEKSClusterCredential(secretId, secretKey, "", region, request)
}

func (s *TkeService) NewDefaultDescribeExistedInstancesRequestBuilder() *DescribeExistedInstancesRequestBuilder {
	return NewDescribeExistedInstancesRequestBuilder().
		WithDomain(constants.TKE_API_DOMAIN_INTERNAL)
}

func (s *TkeService) DescribeExistedInstances(secretId, secretKey, token, region string, request *tke.DescribeExistedInstancesRequest) (
	totalCount uint64, instanceSet []*tke.ExistedInstance, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("DescribeExistedInstances error"))

	prof := qcloud.NewClientProfile()
	credential := common.NewTokenCredential(secretId, secretKey, token)
	client, err := tke.NewClient(credential, region, prof)
	if err != nil {
		return 0, nil, converter.GenCommonErrorCode("", err)
	}
	logger.Infof("DescribeExistedInstances request: %s", request.ToJsonString())
	if response, err := client.DescribeExistedInstances(request); err != nil {
		return 0, nil, converter.GenCommonErrorCode("", err)
	} else {
		logger.Infof("DescribeExistedInstances response: %s", response.ToJsonString())
		return *response.Response.TotalCount, response.Response.ExistedInstanceSet, nil
	}
}

func (s *TkeService) DescribeExistedInstancesWithScsAccount(region string, request *tke.DescribeExistedInstancesRequest) (totalCount uint64, instanceSet []*tke.ExistedInstance, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("DescribeExistedInstancesWithScsAccount error"))

	secretId, secretKey, err := service.GetSecretIdAndKeyOfScs()
	if err != nil {
		return 0, nil, converter.GenCommonErrorCode("", err)
	}

	return s.DescribeExistedInstances(secretId, secretKey, "", region, request)
}

func (s *TkeService) NewDefaultCreateClusterInstancesRequestBuilder() *CreateClusterInstancesRequestBuilder {
	return NewCreateClusterInstancesRequestBuilder().
		WithDomain(constants.TKE_API_DOMAIN_INTERNAL)
}

func (s *TkeService) NewAddClusterExistedInstancesRequestBuilder() *AddClusterExistedInstancesRequestBuilder {
	return NewAddClusterExistedInstancesRequestBuilder().
		WithDomain(constants.TKE_API_DOMAIN_INTERNAL).
		WithSkipValidateOptions([]string{"VpcCniCIDRCheck"})
}

func (s *TkeService) AddExistedInstances(secretId, secretKey, token, region string, request *tke2.AddExistedInstancesRequest) (err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("AddExistedInstances error"))

	prof := qcloud.NewClientProfile()
	credential := common.NewTokenCredential(secretId, secretKey, token)
	client, err := tke2.NewClient(credential, region, prof)
	if err != nil {
		return converter.GenCommonErrorCode("", err)
	}
	logger.Infof("AddExistedInstances request: %s", request.ToJsonString())
	response, err := client.AddExistedInstances(request)
	if err != nil {
		return converter.GenCommonErrorCode("", err)
	}
	logger.Infof("AddExistedInstances response: %s", response.ToJsonString())
	if len(response.Response.FailedReasons) > 0 {
		b, _ := json.Marshal(response.Response.FailedReasons)
		err = fmt.Errorf("AddExistedInstances failed: %s", string(b))
	}
	return err
}

func (s *TkeService) CreateClusterInstances(secretId, secretKey, token, region string, request *tke.CreateClusterInstancesRequest) (
	err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("CreateClusterInstances error"))

	prof := qcloud.NewClientProfile()
	credential := common.NewTokenCredential(secretId, secretKey, token)
	client, err := tke.NewClient(credential, region, prof)
	if err != nil {
		return converter.GenCommonErrorCode("", err)
	}
	logger.Infof("CreateClusterInstances request: %s", request.ToJsonString())
	response, err := client.CreateClusterInstances(request)
	if err != nil {
		return converter.GenCommonErrorCode("", err)
	}
	logger.Infof("CreateClusterInstances response: %s", response.ToJsonString())
	return nil
}

func (s *TkeService) CreateClusterInstancesWithScsAccountByNetworkEnvType(networkEnvType int8, region string, request *tke.CreateClusterInstancesRequest) (err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("CreateClusterInstancesWithScsAccount error"))

	secretId, secretKey, err := service.GetSecretIdAndKeyByNetworkEnvType(networkEnvType)
	if err != nil {
		return
	}

	return s.CreateClusterInstances(secretId, secretKey, "", region, request)
}

func (s *TkeService) CreateClusterInstancesWithScsAccount(region string, request *tke.CreateClusterInstancesRequest) (err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("CreateClusterInstancesWithScsAccount error"))

	secretId, secretKey, err := service.GetSecretIdAndKeyOfScs()
	if err != nil {
		return
	}

	return s.CreateClusterInstances(secretId, secretKey, "", region, request)
}

func (s *TkeService) AddClusterExistedInstancesWithScsAccountByNetworkEnvType(networkEnvType int8, region string, request *tke2.AddExistedInstancesRequest) (err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("AddClusterExistedInstancesWithScsAccount error"))

	secretId, secretKey, err := service.GetSecretIdAndKeyByNetworkEnvType(networkEnvType)
	if err != nil {
		return
	}

	return s.AddExistedInstances(secretId, secretKey, "", region, request)
}

func (s *TkeService) NewDefaultDescribeClusterRouteTablesRequestBuilder() *DescribeClusterRouteTablesRequestBuilder {
	return NewDescribeClusterRouteTablesRequestBuilder().
		WithDomain(constants.TKE_API_DOMAIN_INTERNAL)
}

func (s *TkeService) DescribeClusterRouteTables(secretId, secretKey, token, region string, request *tke.DescribeClusterRouteTablesRequest) (totalCount int64, routeTableSet []*tke.RouteTableInfo, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("DescribeClusterRouteTables error"))

	prof := qcloud.NewClientProfile()
	credential := common.NewTokenCredential(secretId, secretKey, token)
	client, err := tke.NewClient(credential, region, prof)
	if err != nil {
		return 0, nil, converter.GenCommonErrorCode("", err)
	}
	logger.Infof("DescribeClusterRouteTables request: %s", request.ToJsonString())
	if response, err := client.DescribeClusterRouteTables(request); err != nil {
		return 0, nil, converter.GenCommonErrorCode("", err)
	} else {
		logger.Infof("DescribeClusterRouteTables response: %s", response.ToJsonString())
		return *response.Response.TotalCount, response.Response.RouteTableSet, nil
	}
}

func (s *TkeService) DescribeClusterRouteTablesWithScsAccount(region string, request *tke.DescribeClusterRouteTablesRequest) (totalCount int64, routeTableSet []*tke.RouteTableInfo, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("DescribeClusterRouteTablesWithScsAccount error"))

	secretId, secretKey, err := service.GetSecretIdAndKeyOfScs()
	if err != nil {
		return 0, nil, converter.GenCommonErrorCode("", err)
	}

	return s.DescribeClusterRouteTables(secretId, secretKey, "", region, request)
}

func (s *TkeService) GetKubeConfig(secretId, secretKey, token, region, instanceId string) (kubeConfig string, err error) {
	response, err := s.DescribeClusterSecurity(secretId, secretKey, token, region,
		tkeService.NewDefaultDescribeClusterSecurityRequestBuilder().
			WithClusterId(instanceId).
			WithJnsGwEndpointEnable(true).
			Build())
	if err != nil {
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	jnsGwEndpoint := *response.Response.JnsGwEndpoint
	if len(jnsGwEndpoint) == 0 {
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("jnsGwEndpoint not found for cluster %s", instanceId), nil)
	}

	kConfig, err := clientcmd.Load([]byte(*response.Response.Kubeconfig))
	if err != nil {
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	if tkeCluster, exist := kConfig.Clusters[instanceId]; !exist {
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("cluster %s not found in kubeConfig", instanceId), nil)
	} else {
		tkeCluster.Server = fmt.Sprintf("https://%s", jnsGwEndpoint)
	}
	b, err := clientcmd.Write(*kConfig)
	if err != nil {
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	return string(b), nil
}

func (s *TkeService) GetEksKubeConfig(secretId, secretKey, token, region, instanceId string) (kubeConfig string, err error) {
	response, err := s.DescribeEKSClusterCredential(secretId, secretKey, token, region,
		s.NewDefaultDescribeEKSClusterCredentialRequestBuilder().
			WithClusterId(instanceId).
			WithJnsGatewayEnable(true).
			Build())
	if err != nil {
		return "", err
	}

	var (
		server string
	)
	for _, addr := range response.Response.Addresses {
		if *addr.Type == "Internal" {
			server = fmt.Sprintf("https://%s:%d", *addr.Ip, *addr.Port)
			break
		}
	}
	if len(server) == 0 {
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("Internal server address not fount"), nil)
	}

	certAuthData := base64.StdEncoding.EncodeToString([]byte(*response.Response.Credential.CACert))

	data := &struct {
		CertAuthData string
		Server       string
		ClusterId    string
		Token        string
	}{
		CertAuthData: certAuthData,
		Server:       server,
		ClusterId:    instanceId,
		Token:        *response.Response.Credential.Token,
	}

	flowCC := configure_center.CC(region).FlowCC()

	tpl, err := flowCC.TkeCC().KubeConfigYaml()
	if err != nil {
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	return config.MustRenderConfigFile(tpl, data), nil
}

/*
*

	私有 TKE 仓库鉴权信息
*/
func (s *TkeService) GetOrCreateImagePullSecrets(region string, clientset *kubernetes.Clientset) ([]v1.
	LocalObjectReference,
	*v1.Secret, error) {
	return s.GetOrCreateImagePullSecretsWithNamespace(region, constants.OCEANUS_NAMESPACE, clientset)
}

func (s *TkeService) GetOrCreateImagePullSecretsWithNamespace(region, namespace string, clientset *kubernetes.Clientset) ([]v1.
	LocalObjectReference,
	*v1.Secret, error) {
	secretReference := []v1.LocalObjectReference{{Name: constants.OceanusSecretName}}
	tkeLoginCredentials, err := configure_center.CC(region).ImageRegistry().LoginCredentials()
	if err != nil {
		return nil, nil, err
	}

	// 尝试创建 Secret
	_, err = clientset.CoreV1().Secrets(namespace).Create(&v1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name: constants.OceanusSecretName,
		},
		StringData: map[string]string{
			".dockerconfigjson": tkeLoginCredentials,
		},
		Type: "kubernetes.io/dockerconfigjson",
	})
	if err != nil && !strings.Contains(err.Error(), "already exists") { // Secret 已存在时也会报错, 那种报错忽略即可
		logger.Errorf("Could not create Kubernetes secret because %+v", err)
		return nil, nil, errorcode.NewStackError(errorcode.InternalErrorCode, err.Error(), err)
	}

	// 获取最新的 Secret
	secret, err := clientset.CoreV1().Secrets(namespace).Get(constants.OceanusSecretName, metav1.GetOptions{})
	if err != nil {
		logger.Errorf("Failed to get secret because %+v", err)
		return nil, nil, errorcode.NewStackError(errorcode.InternalErrorCode, err.Error(), err)
	}
	return secretReference, secret, nil
}

/*
*

	获取 TKE ENI 设置
*/
func (s *TkeService) GenerateTkeEniAnnotation() map[string]string {
	return map[string]string{
		"tke.cloud.tencent.com/cross-tenant-eni-enable": "true",
		"tke.cloud.tencent.com/networks":                "tke-bridge,tke-direct-eni,tke-route",
	}
}

// 获取组件的 Label, 用于后续选择
func (s *TkeService) ComponentLabels(componentName string, clusterGroup *table.ClusterGroup, cluster *table.Cluster) map[string]string {
	return map[string]string{
		"app":            componentName,
		"clusterGroupId": strconv.FormatInt(clusterGroup.Id, 10),
		"clusterId":      strconv.FormatInt(cluster.Id, 10),
		"ownerUin":       clusterGroup.OwnerUin,
		"region":         clusterGroup.Region,
	}
}

// 创建 Kubernetes 服务
func (s *TkeService) CreateKubernetesService(
	serviceName string,
	servicePort int32,
	componentName string,
	clientset *kubernetes.Clientset,
	clusterGroup *table.ClusterGroup,
	cluster *table.Cluster,
	owner *v1.Secret,
) (*v1.Service, error) {
	return clientset.CoreV1().Services(constants.OCEANUS_NAMESPACE).Create(&v1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Name:            serviceName,
			Namespace:       constants.OCEANUS_NAMESPACE,
			Labels:          s.ComponentLabels(componentName, clusterGroup, cluster),
			OwnerReferences: s.SecretAsOwnerReference(owner),
		},
		Spec: v1.ServiceSpec{
			Ports: []v1.ServicePort{
				{
					Port:     servicePort,
					Protocol: "TCP",
				},
			},
			Selector: s.ComponentLabels(componentName, clusterGroup, cluster),
		},
	})
}

// 使用 Downward API 从元数据中获取指定信息（例如 Pod 的 IP）, 并作为给定环境变量的值
func (s *TkeService) EnvSettingFromMetaViaDownwardAPI(envVar string, fieldPath string) []v1.EnvVar {
	return []v1.EnvVar{
		{
			Name: envVar,
			ValueFrom: &v1.EnvVarSource{
				FieldRef: &v1.ObjectFieldSelector{
					FieldPath: fieldPath,
				},
			},
		},
	}
}

// 尝试获取名为 serviceName 的 Service
func (s *TkeService) TryGetKubernetesServiceFor(serviceName string, clientset *kubernetes.Clientset) (*v1.Service, error) {
	return clientset.CoreV1().Services(constants.OCEANUS_NAMESPACE).Get(
		serviceName,
		metav1.GetOptions{},
	)
}

func (s *TkeService) TryGetKubernetesStatefulSetFor(name string,
	clientset *kubernetes.Clientset) (*v12.StatefulSet, error) {
	return clientset.AppsV1().StatefulSets(constants.OCEANUS_NAMESPACE).Get(
		name,
		metav1.GetOptions{},
	)
}

func (s *TkeService) TryGetKubernetesDaemonSetFor(name string,
	clientset *kubernetes.Clientset) (*v12.DaemonSet, error) {
	return clientset.AppsV1().DaemonSets(constants.OCEANUS_NAMESPACE).Get(
		name,
		metav1.GetOptions{},
	)
}

// 使用某个 Secret 作为整体部署的 Owner Reference, 这样可以一起删除所有资源
func (s *TkeService) SecretAsOwnerReference(secret *v1.Secret) []metav1.OwnerReference {
	return []metav1.OwnerReference{
		{
			Kind:       "Secret",
			APIVersion: "v1",
			UID:        secret.UID,
			Name:       secret.Name,
		},
	}
}

// 获取 ConfigMap 类型的 Volume Source
func (s *TkeService) VolumeSourceForConfigMap(configMapName string) v1.VolumeSource {
	return v1.VolumeSource{
		ConfigMap: &v1.ConfigMapVolumeSource{
			LocalObjectReference: v1.LocalObjectReference{Name: configMapName},
		},
	}
}

// 获取 EmptyDir 类型的 Volume Source
func (s *TkeService) VolumeSourceForEmptyDir() v1.VolumeSource {
	return v1.VolumeSource{
		EmptyDir: &v1.EmptyDirVolumeSource{},
	}
}

// 为集群获取 Kubernetes 的 Clientset
func (s *TkeService) KubernetesClientsetFromCluster(requestId string, cluster *table.Cluster) (*kubernetes.Clientset, error) {

	cconfig, err := k8s.GetK8sService().NewConfig([]byte(cluster.KubeConfig))
	if err != nil {
		logger.Errorf("[%s] Failed to retrieve k8s client config: %v", requestId, err)
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	// 2. 使用配置初始化 K8s 客户端
	clientset, err := kubernetes.NewForConfig(cconfig)
	if err != nil {
		logger.Errorf("[%s] Failed to create clientset from config: %v", requestId, err)
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	return clientset, nil
}

// 根据是否是联调环境, 决定赋予权限与否
func (s *TkeService) GrantContainerSecurityContextByEnv() *v1.SecurityContext {
	isPrivileged := true
	isDev := service4.GetConfStringValue("scsDevEnv")
	if isDev == "true" { // 联调环境, 赋予特权, 便于执行 route add -net 10.0.0.0/16 gw 172.16.0.33 dev eth0 等调试命令
		logger.Warningf("Granted `privileged` permission to pod, as system is in dev mode")
		return &v1.SecurityContext{
			Privileged: &isPrivileged,
		}
	} else { // 线上环境
		return &v1.SecurityContext{}
	}
}

// 用于 ReadinessProbe 和 LivenessProbe 的端口值 Handler
func (s *TkeService) GetHandlerWithPort(port int32) v1.Handler {
	return v1.Handler{
		HTTPGet: &v1.HTTPGetAction{
			Port: intstr.IntOrString{
				IntVal: port,
			},
		},
	}
}

func (s *TkeService) NewDefaultDeleteClusterRequestBuilder() *DeleteClusterRequestBuilder {
	return NewDeleteClusterRequestBuilder().
		WithDomain(constants.TKE_API_DOMAIN_INTERNAL).
		AddResourceDeleteOptions(constants.TKE_DELETE_CLUSTER_RESOURCE_CSB, constants.TKE_DELETE_CLUSTER_RESOURCE_CDB_DELETE_MODE)
}

func (s *TkeService) DeleteCluster(secretId, secretKey, token, region string,
	request *tke.DeleteClusterRequest) (success bool, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("CreateCluster error"))

	prof := qcloud.NewClientProfile()
	credential := common.NewTokenCredential(secretId, secretKey, token)
	client, err := tke.NewClient(credential, region, prof)
	if err != nil {
		return false, converter.GenCommonErrorCode("", err)
	}
	logger.Infof("DeleteCluster request: %s", request.ToJsonString())
	if response, err := client.DeleteCluster(request); err != nil {
		return false, converter.GenCommonErrorCode("", err)
	} else {
		logger.Infof("CreateCluster response: %s", response.ToJsonString())
		return true, nil
	}
}

func (s *TkeService) DeleteEKSCluster(secretId, secretKey, region string,
	request *tke.DeleteEKSClusterRequest) (success bool, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("CreateCluster error"))

	prof := qcloud.NewClientProfile()
	if region == constants.AP_SHANGHAI_ADC {
		prof.HttpProfile.Endpoint = constants.TKE_API_DOMAIN_INTERNAL
	}
	credential := common.NewCredential(secretId, secretKey)
	client, err := tke.NewClient(credential, region, prof)
	if err != nil {
		return false, converter.GenCommonErrorCode("", err)
	}
	logger.Infof("DeleteEKSCluster request: %s", request.ToJsonString())
	if response, err := client.DeleteEKSCluster(request); err != nil {
		return false, converter.GenCommonErrorCode("", err)
	} else {
		logger.Infof("DeleteEKSCluster response: %s", response.ToJsonString())
		return true, nil
	}
}

func (s *TkeService) DeleteClusterWithScsAccountByNetEnvironmentType(netEnvironmentType int8, region string, request *tke.DeleteClusterRequest) (success bool, err error) {
	secretId, secretKey, err := service.GetSecretIdAndKeyByNetworkEnvType(netEnvironmentType)
	if err != nil {
		return false, converter.GenCommonErrorCode("", err)
	}
	return s.DeleteCluster(secretId, secretKey, "", region, request)
}

func (s *TkeService) DeleteClusterWithScsAccount(region string, request *tke.DeleteClusterRequest) (success bool, err error) {
	secretId, secretKey, err := service.GetSecretIdAndKeyOfScs()
	if err != nil {
		return false, converter.GenCommonErrorCode("", err)
	}
	return s.DeleteCluster(secretId, secretKey, "", region, request)
}

func (s *TkeService) DeleteEKSClusterWithScsAccountByNetEnvironmentType(netEnvironmentType int8, region string, request *tke.DeleteEKSClusterRequest) (success bool, err error) {
	secretId, secretKey, err := service.GetSecretIdAndKeyByNetworkEnvType(netEnvironmentType)
	if err != nil {
		return false, converter.GenCommonErrorCode("", err)
	}
	return s.DeleteEKSCluster(secretId, secretKey, region, request)
}

func (s *TkeService) DeleteEKSClusterWithScsAccount(region string, request *tke.DeleteEKSClusterRequest) (success bool, err error) {
	secretId, secretKey, err := service.GetSecretIdAndKeyOfScs()
	if err != nil {
		return false, converter.GenCommonErrorCode("", err)
	}
	return s.DeleteEKSCluster(secretId, secretKey, region, request)
}

func (s *TkeService) newClient(secretId, secretKey, token, region string) (client *tke2.Client, err error) {
	prof := qcloud.NewClientProfile() // 上海自动驾驶云专区走内网
	if region == constants.AP_SHANGHAI_ADC {
		prof.HttpProfile.Endpoint = constants.TKE_API_DOMAIN_INTERNAL
	}
	credential := common.NewTokenCredential(secretId, secretKey, token)
	client, err = tke2.NewClient(credential, region, prof)
	return
}

func (s *TkeService) NewEnableClusterAuditRequestBuilder(clusterId string) *tke.EnableClusterAuditRequest {
	request := tke.NewEnableClusterAuditRequest()
	request.ClusterId = &clusterId
	return request
}

func (s *TkeService) EnableClusterAuditForTke(secretId, secretKey, token, region, clusterId, logSetId string) (err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("EnableClusterAudit error"))
	client, err := s.newClient(secretId, secretKey, token, region)
	if err != nil {
		return errorcode.InternalErrorCode.NewWithErr(err)
	}
	request := s.NewEnableClusterAuditRequestBuilder(clusterId)
	if logSetId != "" {
		request.LogsetId = &logSetId
	}
	logger.Infof("EnableClusterAuditForTke req: %s", request.ToJsonString())

	resp, err := client.EnableClusterAudit(request)
	if err != nil {
		return errorcode.InternalError_TKE.NewWithErr(err)
	}
	logger.Infof("EnableClusterAuditForTke resp: %s", resp.ToJsonString())
	return nil
}

func (s *TkeService) EnableClusterAuditForEks(secretId, secretKey, token, region, clusterId, logSetId string) (err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("EnableClusterAudit error"))
	client, err := s.newClient(secretId, secretKey, token, region)
	if err != nil {
		return errorcode.InternalErrorCode.NewWithErr(err)
	}
	request := tke2.NewEnableEksAuditRequest()
	request.ClusterId = &clusterId
	if logSetId != "" {
		request.LogsetId = &logSetId
	}
	logger.Infof("EnableClusterAuditForEks req: %s", request.ToJsonString())

	resp, err := client.EnableEksAudit(request)
	if err != nil {
		return errorcode.InternalError_TKE.NewWithErr(err)
	}
	logger.Infof("EnableClusterAuditForEks resp: %s", resp.ToJsonString())
	return nil
}

// 开启集群事件存储
func (s *TkeService) EnableEventPersistence(secretId string, secretKey string, token string, region string, request *tke2.EnableEventPersistenceRequest) (err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("EnableEventPersistence error"))

	prof := qcloud.NewClientProfile()
	prof.HttpProfile.Endpoint = "tke.internal.tencentcloudapi.com"
	prof.HttpProfile.ReqTimeout = 300

	credential := common.NewTokenCredential(secretId, secretKey, token)
	client, err := tke2.NewClient(credential, region, prof)
	if err != nil {
		return converter.GenCommonErrorCode("", err)
	}

	logger.Infof("EnableEventPersistence request: %s", request.ToJsonString())
	if response, err := client.EnableEventPersistence(request); err != nil {
		return converter.GenCommonErrorCode("", err)
	} else {
		logger.Infof("EnableEventPersistence response: %s", response.ToJsonString())
		return nil
	}
}

// turn on EKS cluster Event persistence
func (s *TkeService) EnableEKSEventPersistence(secretId string, secretKey string, token string, region string, request *tke2.EnableEksEventPersistenceRequest) (err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, "Enable EKS EventPersistence error")

	prof := qcloud.NewClientProfile()
	prof.HttpProfile.Endpoint = "tke.internal.tencentcloudapi.com"
	prof.HttpProfile.ReqTimeout = 300

	credential := common.NewTokenCredential(secretId, secretKey, token)
	client, err := tke2.NewClient(credential, region, prof)
	if err != nil {
		return converter.GenCommonErrorCode("", err)
	}

	logger.Infof("Enable EKS EventPersistence request: %s", request.ToJsonString())

	response, err := client.EnableEksEventPersistence(request)
	if err != nil {
		logger.Infof("Enable EKS EventPersistence error: %s", err.Error())
		return converter.GenCommonErrorCode("", err)
	}

	logger.Infof("Enable EKS EventPersistence response: %s", response.ToJsonString())
	return nil
}

// 查询 （审计、事件、日志）开关
func (s *TkeService) DescribeLogSwitches(secretId string, secretKey string, token string, region string, request *tke2.DescribeLogSwitchesRequest) (switchSet []*tke2.Switch, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, "DescribeEKSLogSwitches error")

	prof := qcloud.NewClientProfile()
	prof.HttpProfile.Endpoint = "tke.internal.tencentcloudapi.com"
	prof.HttpProfile.ReqTimeout = 300

	credential := common.NewTokenCredential(secretId, secretKey, token)
	client, err := tke2.NewClient(credential, region, prof)
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}

	logger.Infof("Describe DescribeLogSwitches request: %s", request.ToJsonString())

	response, err := client.DescribeLogSwitches(request)
	if err != nil {
		logger.Infof("Describe DescribeLogSwitches error: %s", err.Error())
		return nil, converter.GenCommonErrorCode("", err)
	}
	logger.Infof("Describe DescribeLogSwitches response: %s", response.ToJsonString())
	return response.Response.SwitchSet, nil

}

func (s *TkeService) DescribeNodePools(secretId string, secretKey string, token string, region string, request *tke4.DescribeNodePoolsRequest) (poolSet []*tke4.NodePool, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, "DescribeNodePools error")

	prof := qcloud.NewClientProfile()
	prof.HttpProfile.Endpoint = "tke.internal.tencentcloudapi.com"
	prof.HttpProfile.ReqTimeout = 300

	credential := common.NewTokenCredential(secretId, secretKey, token)
	client, err := tke4.NewClient(credential, region, prof)
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}

	logger.Infof("Describe DescribeNodePools request: %s", request.ToJsonString())

	response, err := client.DescribeNodePools(request)
	if err != nil {
		logger.Infof("Describe DescribeNodePools error: %s", err.Error())
		return nil, converter.GenCommonErrorCode("", err)
	}
	logger.Infof("Describe DescribeNodePools response: %s", response.ToJsonString())
	return response.Response.NodePools, nil
}

func (s *TkeService) CreateNodePool(secretId string, secretKey string, token string, region string, request *tke3.CreateNodePoolRequest) (nodePoolId *string, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, "CreateNodePool error")

	prof := qcloud.NewClientProfile()
	prof.HttpProfile.Endpoint = "tke.internal.tencentcloudapi.com"
	prof.HttpProfile.ReqTimeout = 300

	credential := common.NewTokenCredential(secretId, secretKey, token)
	client, err := tke3.NewClient(credential, region, prof)
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}

	logger.Infof("CreateNodePool request: %s", request.ToJsonString())

	response, err := client.CreateNodePool(request)
	if err != nil {
		logger.Infof("CreateNodePool error: %s", err.Error())
		return nil, converter.GenCommonErrorCode("", err)
	}
	logger.Infof("CreateNodePool response: %s", response.ToJsonString())
	return response.Response.NodePoolId, nil
}

func (s *TkeService) ModifyNodePool(secretId string, secretKey string, token string, region string, request *tke3.ModifyNodePoolRequest) (err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, "ModifyNodePool error")

	prof := qcloud.NewClientProfile()
	prof.HttpProfile.Endpoint = "tke.internal.tencentcloudapi.com"
	prof.HttpProfile.ReqTimeout = 300

	credential := common.NewTokenCredential(secretId, secretKey, token)
	client, err := tke3.NewClient(credential, region, prof)
	if err != nil {
		return converter.GenCommonErrorCode("", err)
	}

	logger.Infof("ModifyNodePool request: %s", request.ToJsonString())

	response, err := client.ModifyNodePool(request)
	if err != nil {
		logger.Infof("ModifyNodePool error: %s", err.Error())
		return converter.GenCommonErrorCode("", err)
	}
	logger.Infof("ModifyNodePool response: %s", response.ToJsonString())
	return nil
}

// 查询策略
func (s *TkeService) DescribeOpenPolicyList(secretId string, secretKey string, token string, region string, request *tke2.DescribeOpenPolicyListRequest) (policySet []*tke2.OpenPolicyInfo, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, "DescribeOpenPolicyList error")

	prof := qcloud.NewClientProfile()
	prof.HttpProfile.Endpoint = "tke.internal.tencentcloudapi.com"
	prof.HttpProfile.ReqTimeout = 300

	credential := common.NewTokenCredential(secretId, secretKey, token)
	client, err := tke2.NewClient(credential, region, prof)
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}

	logger.Infof("Describe DescribeOpenPolicyList request: %s", request.ToJsonString())

	response, err := client.DescribeOpenPolicyList(request)
	if err != nil {
		logger.Infof("Describe DescribeOpenPolicyList error: %s", err.Error())
		return nil, converter.GenCommonErrorCode("", err)
	}
	logger.Infof("Describe DescribeOpenPolicyList response: %s", response.ToJsonString())
	return response.Response.OpenPolicyInfoList, nil

}

// 集群策略
func (s *TkeService) ModifyOpenPolicyList(secretId string, secretKey string, token string, region string, request *tke2.ModifyOpenPolicyListRequest) (err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("ModifyOpenPolicyList error"))

	prof := qcloud.NewClientProfile()
	prof.HttpProfile.Endpoint = "tke.internal.tencentcloudapi.com"
	prof.HttpProfile.ReqTimeout = 300

	credential := common.NewTokenCredential(secretId, secretKey, token)
	client, err := tke2.NewClient(credential, region, prof)
	if err != nil {
		return converter.GenCommonErrorCode("", err)
	}

	logger.Infof("ModifyOpenPolicyList request: %s", request.ToJsonString())
	if response, err := client.ModifyOpenPolicyList(request); err != nil {
		return converter.GenCommonErrorCode("", err)
	} else {
		logger.Infof("ModifyOpenPolicyList response: %s", response.ToJsonString())
		return nil
	}
}

func (s *TkeService) EnableMetaFeature(secretId, secretKey, token, region string,
	request *tke2.EnableMetaFeatureRequest) (err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("EnableMetaFeature error"))
	client, err := s.newClient(secretId, secretKey, token, region)
	if err != nil {
		return errorcode.InternalErrorCode.NewWithErr(err)
	}
	_, err = client.EnableMetaFeature(request)
	if err != nil {
		return errorcode.InternalError_TKE.NewWithErr(err)
	}
	return nil
}

func (s *TkeService) EnableMetaFeatureForEks(secretId, secretKey, token, region string,
	request *tke2.EnableMetaFeatureForEksRequest) (err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("EnableMetaFeatureForEks error"))
	client, err := s.newClient(secretId, secretKey, token, region)
	if err != nil {
		return errorcode.InternalErrorCode.NewWithErr(err)
	}
	resp, err := client.EnableMetaFeatureForEks(request)
	logger.Infof("EnableMetaFeatureForEks resp: %s", resp.ToJsonString())
	if err != nil {
		return errorcode.InternalError_TKE.NewWithErr(err)
	}
	return nil
}

func (s *TkeService) ModifyClusterSchedulerPolicy(secretId, secretKey, token, region string,
	request *tke2.ModifyClusterSchedulerPolicyRequest) (err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("ModifyClusterSchedulerPolicy error"))
	client, err := s.newClient(secretId, secretKey, token, region)
	if err != nil {
		return errorcode.InternalErrorCode.NewWithErr(err)
	}
	_, err = client.ModifyClusterSchedulerPolicy(request)
	if err != nil {
		return errorcode.InternalError_TKE.NewWithErr(err)
	}
	return nil
}

func (s *TkeService) EnableMetaFeatureWithScsAccount(region string, request *tke2.EnableMetaFeatureRequest) (err error) {
	secretId, secretKey, err := service.GetSecretIdAndKeyOfScs()
	if err != nil {
		return
	}
	return s.EnableMetaFeature(secretId, secretKey, "", region, request)
}

func (s *TkeService) EnableMetaFeatureForEksWithScsAccount(region string, request *tke2.EnableMetaFeatureForEksRequest) (err error) {
	secretId, secretKey, err := service.GetSecretIdAndKeyOfScs()
	if err != nil {
		return
	}
	return s.EnableMetaFeatureForEks(secretId, secretKey, "", region, request)
}

func (s *TkeService) DescribeMetaFeatureProgress(secretId, secretKey, token, region string,
	request *tke2.DescribeMetaFeatureProgressRequest) (found bool, response *tke2.DescribeMetaFeatureProgressResponse,
	err error) {
	found = true
	defer errorcode.DefaultDeferHandler(&err)
	client, err := s.newClient(secretId, secretKey, token, region)
	if err != nil {
		err = errorcode.InternalErrorCode.NewWithErr(err)
		return
	}
	response, err = client.DescribeMetaFeatureProgress(request)
	if err != nil {
		if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
			logger.Infof("DescribeMetaFeatureProgress %s", sdkError.Error())
			if sdkError.GetCode() == "FailedOperation.TaskNotFound" {
				found = false
				err = nil
				return
			}
		}
		err = errorcode.InternalError_TKE.NewWithErr(err)
		return
	}
	return
}

func (s *TkeService) DescribeEksMetaFeatureProgress(secretId, secretKey, token, region string,
	request *tke2.DescribeEksMetaFeatureProgressRequest) (found bool, response *tke2.DescribeEksMetaFeatureProgressResponse,
	err error) {
	found = true
	defer errorcode.DefaultDeferHandler(&err)
	client, err := s.newClient(secretId, secretKey, token, region)
	if err != nil {
		err = errorcode.InternalErrorCode.NewWithErr(err)
		return
	}
	response, err = client.DescribeEksMetaFeatureProgress(request)
	if err != nil {
		err = errorcode.InternalError_TKE.NewWithErr(err)
		return
	}
	found = *response.Response.Status != "disabled"
	logger.Infof("DescribeEksMetaFeatureProgress response: %s", response.ToJsonString())
	return
}

func (s *TkeService) DescribeMetaFeatureProgressWithScsAccount(region string,
	request *tke2.DescribeMetaFeatureProgressRequest) (found bool, response *tke2.DescribeMetaFeatureProgressResponse,
	err error) {
	secretId, secretKey, err := service.GetSecretIdAndKeyOfScs()
	if err != nil {
		return
	}
	return s.DescribeMetaFeatureProgress(secretId, secretKey, "", region, request)
}

func (s *TkeService) DescribeEksMetaFeatureProgressWithScsAccount(region string,
	request *tke2.DescribeEksMetaFeatureProgressRequest) (found bool, response *tke2.DescribeEksMetaFeatureProgressResponse,
	err error) {
	secretId, secretKey, err := service.GetSecretIdAndKeyOfScs()
	if err != nil {
		return
	}
	return s.DescribeEksMetaFeatureProgress(secretId, secretKey, "", region, request)
}

func (s *TkeService) DeleteClusterInstances(secretId, secretKey, token, region string,
	request *tke.DeleteClusterInstancesRequest) (rsp *tke.
	DeleteClusterInstancesResponse, err error) {
	defer errorcode.DefaultDeferHandler(&err)
	client, err := s.newClient(secretId, secretKey, token, region)
	if err != nil {
		err = errorcode.InternalErrorCode.NewWithErr(err)
		return
	}

	logger.Infof("DeleteClusterInstances req: %s", request.ToJsonString())

	rsp, err = client.DeleteClusterInstances(request)
	if err != nil {
		err = errorcode.InternalError_TKE.NewWithErr(err)
	}
	logger.Infof("DeleteClusterInstances resp: %s", rsp.ToJsonString())
	return
}

func (s *TkeService) DeleteClusterInstancesForIdsByNetEnvironmentType(netEnvironmentType int8, region string,
	clusterId string, instances []*string) (rsp *tke.DeleteClusterInstancesResponse, err error) {
	secretId, secretKey, err := service.GetSecretIdAndKeyByNetworkEnvType(netEnvironmentType)
	if err != nil {
		return
	}
	return s.DeleteClusterInstanceForIds(secretId, secretKey, "", region, clusterId, instances)
}

func (s *TkeService) DeleteClusterInstanceForIds(
	secretId, secretKey, token, region, clusterId string, instances []*string) (
	rsp *tke.DeleteClusterInstancesResponse, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("DescribeClusterInstanceForIds error"))
	batchCount := (len(instances) + MAX_QUERY_COUNT - 1) / MAX_QUERY_COUNT

	wg := &sync.WaitGroup{}
	wg.Add(batchCount)

	type Result struct {
		Rsp *tke.DeleteClusterInstancesResponse
		Err error
	}

	result := make(chan *Result, batchCount)

	for i := 0; i < batchCount; i++ {
		go func(cur int) {
			defer wg.Done()
			begin := MAX_QUERY_COUNT * cur
			end := MAX_QUERY_COUNT * (cur + 1)
			if end > len(instances) {
				end = len(instances)
			}

			r, e := s.DeleteClusterInstances(secretId, secretKey, token, region,
				NewDefaultDeleteClusterInstancesRequestBuilder().
					WithClusterId(clusterId).
					WithInstanceIdsPtr(instances[begin:end]).
					Build(),
			)

			result <- &Result{
				Rsp: r,
				Err: e,
			}
		}(i)
	}

	go func() {
		wg.Wait()
		close(result)
	}()

	rsp = &tke.DeleteClusterInstancesResponse{Response: &tke.DeleteClusterInstancesResponseParams{
		SuccInstanceIds:     make([]*string, 0),
		FailedInstanceIds:   make([]*string, 0),
		NotFoundInstanceIds: make([]*string, 0),
	}}

	for r := range result {
		if r.Err != nil {
			err = r.Err
		} else {
			rsp.Response.SuccInstanceIds = append(rsp.Response.SuccInstanceIds, r.Rsp.Response.SuccInstanceIds...)
			rsp.Response.FailedInstanceIds = append(rsp.Response.FailedInstanceIds, r.Rsp.Response.FailedInstanceIds...)
			rsp.Response.NotFoundInstanceIds = append(rsp.Response.NotFoundInstanceIds, r.Rsp.Response.NotFoundInstanceIds...)
			rsp.Response.RequestId = r.Rsp.Response.RequestId
		}
	}
	return
}

func (s *TkeService) DescribeClusterInstanceForIdsWithScsAccountByNetEnvironmentType(
	netEnvironmentType int8, region, clusterId string, instances []*string) (
	totalCount uint64, instanceSet []*tke.Instance, err error) {
	secretId, secretKey, err := service.GetSecretIdAndKeyByNetworkEnvType(netEnvironmentType)
	if err != nil {
		return
	}
	return s.DescribeClusterInstanceForIds(secretId, secretKey, "", region, clusterId, instances)
}

func (s *TkeService) DescribeClusterInstanceForIdsWithScsAccountByNetEnvironmentTypeV2022(
	netEnvironmentType int8, region, clusterId string, instances []*string) (
	totalCount uint64, instanceSet []*tke4.Instance, err error) {
	secretId, secretKey, err := service.GetSecretIdAndKeyByNetworkEnvType(netEnvironmentType)
	if err != nil {
		return
	}
	return s.DescribeClusterInstanceForIdsV2022(secretId, secretKey, "", region, clusterId, instances)
}

func (s *TkeService) DescribeClusterInstanceForIds(
	secretId, secretKey, token, region, clusterId string, instances []*string) (
	totalCount uint64, instanceSet []*tke.Instance, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("DescribeClusterInstanceForIds error"))
	batchCount := (len(instances) + MAX_QUERY_COUNT - 1) / MAX_QUERY_COUNT

	wg := &sync.WaitGroup{}
	wg.Add(batchCount)

	type Result struct {
		InstanceSet []*tke.Instance
		Err         error
	}

	result := make(chan *Result, batchCount)

	for i := 0; i < batchCount; i++ {
		go func(cur int) {
			defer wg.Done()
			begin := MAX_QUERY_COUNT * cur
			end := MAX_QUERY_COUNT * (cur + 1)
			if end > len(instances) {
				end = len(instances)
			}

			_, cvmSet, err := s.DescribeClusterInstances(secretId, secretKey, token, region,
				tkeService.NewDefaultDescribeClusterInstancesRequestBuilder().
					WithClusterId(clusterId).
					WithInstanceIds(instances[begin:end]).
					Build())
			result <- &Result{
				InstanceSet: cvmSet,
				Err:         err,
			}
		}(i)
	}

	go func() {
		wg.Wait()
		close(result)
	}()

	instanceSet = make([]*tke.Instance, 0)
	for r := range result {
		if r.Err != nil {
			err = r.Err
		} else {
			instanceSet = append(instanceSet, r.InstanceSet...)
		}
	}
	totalCount = uint64(len(instanceSet))
	return
}

func (s *TkeService) DescribeClusterInstanceForIdsV2022(
	secretId, secretKey, token, region, clusterId string, instances []*string) (
	totalCount uint64, instanceSet []*tke4.Instance, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("DescribeClusterInstanceForIdsV2022 error"))
	batchCount := (len(instances) + MAX_QUERY_COUNT - 1) / MAX_QUERY_COUNT

	wg := &sync.WaitGroup{}
	wg.Add(batchCount)

	type Result struct {
		InstanceSet []*tke4.Instance
		Err         error
	}

	result := make(chan *Result, batchCount)

	for i := 0; i < batchCount; i++ {
		go func(cur int) {
			defer wg.Done()
			begin := MAX_QUERY_COUNT * cur
			end := MAX_QUERY_COUNT * (cur + 1)
			if end > len(instances) {
				end = len(instances)
			}

			_, cvmSet, err := s.DescribeClusterInstancesV2022(secretId, secretKey, token, region,
				tkeService.NewDefaultDescribeClusterInstancesRequestBuilderV2022().
					WithClusterId(clusterId).
					WithInstanceIds(instances[begin:end]).
					Build())
			result <- &Result{
				InstanceSet: cvmSet,
				Err:         err,
			}
		}(i)
	}

	go func() {
		wg.Wait()
		close(result)
	}()

	instanceSet = make([]*tke4.Instance, 0)
	for r := range result {
		if r.Err != nil {
			err = r.Err
		} else {
			instanceSet = append(instanceSet, r.InstanceSet...)
		}
	}
	totalCount = uint64(len(instanceSet))
	return
}

func (s *TkeService) DescribeClusterAllInstances(secretId, secretKey, token, region, clusterId string) (totalCount uint64, instanceSet []*tke.Instance, err error) {
	defer errorcode.DefaultDeferHandler(&err)
	client, err := s.newClient(secretId, secretKey, token, region)
	if err != nil {
		err = errorcode.InternalErrorCode.NewWithErr(err)
		return
	}
	var offset, limit int64
	offset = 0
	limit = 100

	instanceSet = make([]*tke.Instance, 0, 0)
	for {
		req := tkeService.NewDefaultDescribeClusterInstancesRequestBuilder().WithClusterId(clusterId).
			WithOffsetLimit(offset, limit).Build()
		logger.Debugf("DescribeClusterInstances request: %+v", req)
		rsp, err := client.DescribeClusterInstances(req)
		logger.Debugf("DescribeClusterInstances response: %+v", rsp)
		if err != nil {
			err = errorcode.InternalError_TKE.NewWithErr(err)
			return 0, nil, err
		}
		totalCount = *rsp.Response.TotalCount
		instanceSet = append(instanceSet, rsp.Response.InstanceSet...)
		if totalCount <= uint64(offset+limit) {
			break
		}
		offset += limit
	}
	return
}

func (s *TkeService) DescribeClusterAllInstancesWithScsAccountByNetEnvironmentType(
	netEnvironmentType int8, region, clusterId string) (totalCount uint64, instanceSet []*tke.Instance, err error) {
	secretId, secretKey, err := service.GetSecretIdAndKeyByNetworkEnvType(netEnvironmentType)
	if err != nil {
		return
	}
	return s.DescribeClusterAllInstances(secretId, secretKey, "", region, clusterId)
}

func IsEKSClusterRunning(clusterGroup *table.ClusterGroup, cluster *table.Cluster,
	k8sInstance *table2.Tke) (running bool, err error) {

	describeEKSClustersRequest := tke.NewDescribeEKSClustersRequest()
	describeEKSClustersRequest.Filters = []*tke.Filter{
		{
			Name:   common.StringPtr("ClusterName"),
			Values: common.StringPtrs([]string{clusterGroup.SerialId}),
		},
	}
	secretId, secretKey, err := service.GetSecretIdAndKeyByNetworkEnvType(clusterGroup.NetEnvironmentType)
	if err != nil {
		return false, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	instanceId := k8sInstance.InstanceId
	tkeService := GetTkeService()

	totalCount, clusters, err := tkeService.DescribeEKSClusters(secretId, secretKey, clusterGroup.Region, describeEKSClustersRequest)

	if err != nil {
		return false, errorcode.InternalErrorCode.NewWithErr(err)
	}
	if totalCount != 1 {
		msg := fmt.Sprintf("tke instance count %d for instanceId %s", totalCount, instanceId)
		return false, errorcode.InternalErrorCode.NewWithInfo(msg, err)
	}

	if *clusters[0].Status != constants.TKE_STATUS_RUNNING_STR &&
		*clusters[0].Status != constants.EKS_STATUS_IDLING_STR {
		return false, nil
	}
	return true, nil
}

func IsClusterRunning(clusterGroup *table.ClusterGroup, cluster *table.Cluster,
	k8sInstance *table2.Tke) (running bool, err error) {
	// TODO: eks状态检测
	if k8sInstance.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		running, err := IsEKSClusterRunning(clusterGroup, cluster, k8sInstance)
		if err != nil {
			return false, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
		return running, nil
	}

	secretId, secretKey, err := service.GetSecretIdAndKeyByNetworkEnvType(clusterGroup.NetEnvironmentType)
	if err != nil {
		return false, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	instanceId := k8sInstance.InstanceId
	tkeService := GetTkeService()

	if totalCount, clusters, err := tkeService.DescribeClusters(secretId, secretKey, "", clusterGroup.Region, tkeService.NewDefaultDescribeClustersRequestBuilder().WithClusterIds(instanceId).Build()); err != nil {
		return false, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	} else if totalCount != 1 {
		return false, errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("tke instance count %d for instanceId %s", totalCount, instanceId), nil)
	} else if *clusters[0].ClusterStatus == constants.TKE_STATUS_ABNORMAL_STR {
		return false, errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("tke cluster %s is in abnormal status", instanceId), nil)
	} else if *clusters[0].ClusterStatus != constants.TKE_STATUS_RUNNING_STR {
		return false, nil
	} else {
		return true, nil
	}
}

// /////////////////////////////////////////////////////////////////////////////
func NewTkeService() *TkeService {
	return &TkeService{}
}

func GetTkeService() *TkeService {
	if tkeService == nil {
		tkeService = NewTkeService()
	}
	return tkeService
}

func (s *TkeService) CancelClusterReleaseWithScsAccount(clusterGroup *table.ClusterGroup, request *tke.CancelClusterReleaseRequest) (release *tke.PendingRelease, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("CancelClusterReleaseWithScsAccount error"))

	secretId, secretKey, err := service.GetSecretIdAndKeyByNetworkEnvType(clusterGroup.NetEnvironmentType)
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}

	return s.CancelClusterRelease(secretId, secretKey, "", clusterGroup.Region, request)
}

func (s *TkeService) CancelClusterRelease(secretId string, secretKey string, token string, region string, request *tke.CancelClusterReleaseRequest) (release *tke.PendingRelease, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("CancelClusterRelease error"))

	prof := qcloud.NewClientProfile()
	credential := common.NewTokenCredential(secretId, secretKey, token)
	client, err := tke.NewClient(credential, region, prof)
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}

	logger.Infof("CancelClusterRelease request: %s", request.ToJsonString())
	if response, err := client.CancelClusterRelease(request); err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	} else {
		logger.Infof("CancelClusterRelease response: %s", response.ToJsonString())
		return response.Response.Release, nil
	}
}
