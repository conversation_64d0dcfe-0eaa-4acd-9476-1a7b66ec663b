package tke

import (
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke"
)

type ModifyClusterSchedulerPolicyBuilder struct {
	request *tke.ModifyClusterSchedulerPolicyRequest
}

func newModifyClusterSchedulerPolicyBuilder() *ModifyClusterSchedulerPolicyBuilder {
	return &ModifyClusterSchedulerPolicyBuilder{request: tke.NewModifyClusterSchedulerPolicyRequest()}
}

func NewDefaultModifyClusterSchedulerPolicyBuilder() *ModifyClusterSchedulerPolicyBuilder {
	b := newModifyClusterSchedulerPolicyBuilder().WithDomain(constants.TKE_API_DOMAIN_INTERNAL)
	b.request.Priorities = make([]*tke.SchedulerPolicyPriority, 0)
	return b
}

func (e *ModifyClusterSchedulerPolicyBuilder) Build() *tke.ModifyClusterSchedulerPolicyRequest {
	return e.request
}

func (e *ModifyClusterSchedulerPolicyBuilder) WithDomain(domain string) *ModifyClusterSchedulerPolicyBuilder {
	e.request.SetDomain(domain)
	return e
}

func (e *ModifyClusterSchedulerPolicyBuilder) WithClusterId(clusterId string) *ModifyClusterSchedulerPolicyBuilder {
	e.request.ClusterId = &clusterId
	return e
}

func (e *ModifyClusterSchedulerPolicyBuilder) WithPriority(name string, weight int64) *ModifyClusterSchedulerPolicyBuilder {
	e.request.Priorities = append(e.request.Priorities, &tke.SchedulerPolicyPriority{
		Name:   name,
		Weight: weight,
	})
	return e
}
