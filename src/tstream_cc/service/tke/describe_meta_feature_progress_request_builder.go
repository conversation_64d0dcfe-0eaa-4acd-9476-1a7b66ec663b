package tke

import (
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke"
)

type DescribeMetaFeatureProgressRequestBuilder struct {
	request *tke.DescribeMetaFeatureProgressRequest
}

func newDescribeMetaFeatureProgressRequestBuilder() *DescribeMetaFeatureProgressRequestBuilder {
	return &DescribeMetaFeatureProgressRequestBuilder{request: tke.NewDescribeMetaFeatureProgressRequest()}
}

func NewDefaultDescribeMetaFeatureProgressRequestBuilder() *DescribeMetaFeatureProgressRequestBuilder {
	return newDescribeMetaFeatureProgressRequestBuilder().
		WithDomain(constants.TKE_API_DOMAIN_INTERNAL)
}

func (d *DescribeMetaFeatureProgressRequestBuilder) Build() *tke.DescribeMetaFeatureProgressRequest {
	return d.request
}

func (d *DescribeMetaFeatureProgressRequestBuilder) WithDomain(
	domain string) *DescribeMetaFeatureProgressRequestBuilder {
	d.request.SetDomain(domain)
	return d
}

func (d *DescribeMetaFeatureProgressRequestBuilder) WithClusterId(
	clusterId string) *DescribeMetaFeatureProgressRequestBuilder {
	d.request.ClusterId = &clusterId
	return d
}
