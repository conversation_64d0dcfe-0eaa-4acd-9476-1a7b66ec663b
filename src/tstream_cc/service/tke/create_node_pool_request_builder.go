package tke

import (
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common"
	tke3 "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/tke/v20220501"
)

type CreateNodePoolRequestBuilder struct {
	request *tke3.CreateNodePoolRequest
}

func (d *CreateNodePoolRequestBuilder) WithDomain(domain string) *CreateNodePoolRequestBuilder {
	d.request.SetDomain(domain)
	return d
}

func (d *CreateNodePoolRequestBuilder) WithClusterId(clusterId string) *CreateNodePoolRequestBuilder {
	d.request.ClusterId = &clusterId
	return d
}

func (d *CreateNodePoolRequestBuilder) WithName(name string) *CreateNodePoolRequestBuilder {
	d.request.Name = &name
	return d
}

func (d *CreateNodePoolRequestBuilder) WithType(t string) *CreateNodePoolRequestBuilder {
	d.request.Type = &t
	return d
}

func (d *CreateNodePoolRequestBuilder) WithLabel(name, value string) *CreateNodePoolRequestBuilder {
	if d.request.Labels == nil {
		d.request.Labels = make([]*tke3.Label, 0)
	}
	label := &tke3.Label{
		Name:  common.StringPtr(name),
		Value: common.StringPtr(value),
	}
	d.request.Labels = append(d.request.Labels, label)
	return d
}

func (d *CreateNodePoolRequestBuilder) WithHealthCheckPolicyName(name string) *CreateNodePoolRequestBuilder {
	d.request.Native.HealthCheckPolicyName = &name
	return d
}

func (d *CreateNodePoolRequestBuilder) WithSubnetId(subnet string) *CreateNodePoolRequestBuilder {
	if d.request.Native.SubnetIds == nil {
		d.request.Native.SubnetIds = make([]*string, 0)
	}

	d.request.Native.SubnetIds = append(d.request.Native.SubnetIds, &subnet)
	return d
}

func (d *CreateNodePoolRequestBuilder) WithInstanceTypes(typeSet []string) *CreateNodePoolRequestBuilder {
	if d.request.Native.InstanceTypes == nil {
		d.request.Native.InstanceTypes = make([]*string, 0)
	}

	for _, t := range typeSet {
		d.request.Native.InstanceTypes = append(d.request.Native.InstanceTypes, &t)
	}
	return d
}

func (d *CreateNodePoolRequestBuilder) WithKeyId(keyId string) *CreateNodePoolRequestBuilder {
	if d.request.Native.KeyIds == nil {
		d.request.Native.KeyIds = make([]*string, 0)
	}

	d.request.Native.KeyIds = append(d.request.Native.KeyIds, &keyId)
	return d
}

func (d *CreateNodePoolRequestBuilder) WithSystemDisk(diskType string, diskSize int64) *CreateNodePoolRequestBuilder {
	d.request.Native.SystemDisk = &tke3.Disk{
		DiskType: common.StringPtr(diskType),
		DiskSize: common.Int64Ptr(diskSize),
	}
	return d
}

func (d *CreateNodePoolRequestBuilder) WithRuntimeRootDir(runtimeRootDir string) *CreateNodePoolRequestBuilder {
	d.request.Native.RuntimeRootDir = &runtimeRootDir
	return d
}

func (d *CreateNodePoolRequestBuilder) WithDataDisks(diskType string, diskSize int64, mountTarget string) *CreateNodePoolRequestBuilder {
	if d.request.Native.DataDisks == nil {
		d.request.Native.DataDisks = make([]*tke3.DataDisk, 0)
	}
	d.request.Native.DataDisks = append(d.request.Native.DataDisks, &tke3.DataDisk{
		DiskType:           common.StringPtr(diskType),
		DiskSize:           common.Int64Ptr(diskSize),
		MountTarget:        common.StringPtr(mountTarget),
		AutoFormatAndMount: common.BoolPtr(true),
		FileSystem:         common.StringPtr("ext4"),
	})
	return d
}

func (d *CreateNodePoolRequestBuilder) WithSecurityGroup(securityGroupId string) *CreateNodePoolRequestBuilder {
	if d.request.Native.SecurityGroupIds == nil {
		d.request.Native.SecurityGroupIds = make([]*string, 0)
	}

	d.request.Native.SecurityGroupIds = append(d.request.Native.SecurityGroupIds, &securityGroupId)
	return d
}

func (d *CreateNodePoolRequestBuilder) WithInstanceCharge(chargeType string, period uint64, renewFlag string) *CreateNodePoolRequestBuilder {
	d.request.Native.InstanceChargeType = common.StringPtr(chargeType)
	d.request.Native.InstanceChargePrepaid = &tke3.InstanceChargePrepaid{
		Period:    &period,
		RenewFlag: common.StringPtr(renewFlag),
	}
	return d
}

func (d *CreateNodePoolRequestBuilder) Build() *tke3.CreateNodePoolRequest {
	return d.request
}

func NewCreateNodePoolRequestBuilder() *CreateNodePoolRequestBuilder {
	req := tke3.NewCreateNodePoolRequest()
	req.DeletionProtection = common.BoolPtr(true)
	req.Unschedulable = common.BoolPtr(false)
	req.Native = &tke3.CreateNativeNodePoolParam{
		EnableAutoscaling: common.BoolPtr(false),
		Replicas:          common.Int64Ptr(0),
		AutoRepair:        common.BoolPtr(true),
	}

	return &CreateNodePoolRequestBuilder{request: req}
}
