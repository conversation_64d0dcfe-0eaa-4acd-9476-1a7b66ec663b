package tke

import (
	"tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke"
)

type EnableEventPersistenceRequestBuilder struct {
	request *tke.EnableEventPersistenceRequest
}

func (e *EnableEventPersistenceRequestBuilder) WithClusterId(clusterId string) *EnableEventPersistenceRequestBuilder {
	e.request.ClusterId = &clusterId
	return e
}

func (e *EnableEventPersistenceRequestBuilder) WithLogSetId(logSetId string) *EnableEventPersistenceRequestBuilder {
	e.request.LogsetId = &logSetId
	return e
}

func (e *EnableEventPersistenceRequestBuilder) WithTopicId(topicId string) *EnableEventPersistenceRequestBuilder {
	e.request.TopicId = &topicId
	return e
}

func NewEnableEventPersistenceRequestBuilder() *EnableEventPersistenceRequestBuilder {
	return &EnableEventPersistenceRequestBuilder{request: tke.NewEnableEventPersistenceRequest()}
}

func (e *EnableEventPersistenceRequestBuilder) Build() *tke.EnableEventPersistenceRequest {
	return e.request
}

type EnableEKSEventPersistenceRequestBuilder struct {
	request *tke.EnableEksEventPersistenceRequest
}

func (e *EnableEKSEventPersistenceRequestBuilder) WithClusterId(clusterId string) *EnableEKSEventPersistenceRequestBuilder {
	e.request.ClusterId = &clusterId
	return e
}

func (e *EnableEKSEventPersistenceRequestBuilder) WithLogSetId(logSetId string) *EnableEKSEventPersistenceRequestBuilder {
	e.request.LogsetId = &logSetId
	return e
}

func (e *EnableEKSEventPersistenceRequestBuilder) WithTopicId(topicId string) *EnableEKSEventPersistenceRequestBuilder {
	e.request.TopicId = &topicId
	return e
}

func NewEnableEKSEventPersistenceRequestBuilder() *EnableEKSEventPersistenceRequestBuilder {
	return &EnableEKSEventPersistenceRequestBuilder{request: tke.NewEnableEksEventPersistenceRequest()}
}

func (e *EnableEKSEventPersistenceRequestBuilder) Build() *tke.EnableEksEventPersistenceRequest {
	return e.request
}
