package tke

import (
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common"
	tke3 "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/tke/v20220501"
)

type ModifyNodePoolRequestBuilder struct {
	request *tke3.ModifyNodePoolRequest
}

func (d *ModifyNodePoolRequestBuilder) WithDomain(domain string) *ModifyNodePoolRequestBuilder {
	d.request.SetDomain(domain)
	return d
}

func (d *ModifyNodePoolRequestBuilder) WithClusterId(clusterId string) *ModifyNodePoolRequestBuilder {
	d.request.ClusterId = &clusterId
	return d
}

func (d *ModifyNodePoolRequestBuilder) WithNodePoolId(nodePoolId string) *ModifyNodePoolRequestBuilder {
	d.request.NodePoolId = &nodePoolId
	return d
}

func (d *ModifyNodePoolRequestBuilder) WithName(name string) *ModifyNodePoolRequestBuilder {
	d.request.Name = &name
	return d
}

func (d *ModifyNodePoolRequestBuilder) WithLabel(name, value string) *ModifyNodePoolRequestBuilder {
	if d.request.Labels == nil {
		d.request.Labels = make([]*tke3.Label, 0)
	}
	label := &tke3.Label{
		Name:  common.StringPtr(name),
		Value: common.StringPtr(value),
	}
	d.request.Labels = append(d.request.Labels, label)
	return d
}

func (d *ModifyNodePoolRequestBuilder) WithInstanceTypes(typeSet []string) *ModifyNodePoolRequestBuilder {
	if d.request.Native == nil {
		d.request.Native = &tke3.UpdateNativeNodePoolParam{}
	}

	if d.request.Native.InstanceTypes == nil {
		d.request.Native.InstanceTypes = make([]*string, 0)
	}

	for _, t := range typeSet {
		d.request.Native.InstanceTypes = append(d.request.Native.InstanceTypes, &t)
	}
	return d
}

func (d *ModifyNodePoolRequestBuilder) WithSystemDisk(diskType string, diskSize int64) *ModifyNodePoolRequestBuilder {
	if d.request.Native == nil {
		d.request.Native = &tke3.UpdateNativeNodePoolParam{}
	}

	d.request.Native.SystemDisk = &tke3.Disk{
		DiskType: common.StringPtr(diskType),
		DiskSize: common.Int64Ptr(diskSize),
	}
	return d
}
func (d *ModifyNodePoolRequestBuilder) WithDataDisks(diskType string, diskSize int64, mountTarget string) *ModifyNodePoolRequestBuilder {
	if d.request.Native == nil {
		d.request.Native = &tke3.UpdateNativeNodePoolParam{}
	}

	if d.request.Native.DataDisks == nil {
		d.request.Native.DataDisks = make([]*tke3.DataDisk, 0)
	}
	d.request.Native.DataDisks = append(d.request.Native.DataDisks, &tke3.DataDisk{
		DiskType:           common.StringPtr(diskType),
		DiskSize:           common.Int64Ptr(diskSize),
		MountTarget:        common.StringPtr(mountTarget),
		AutoFormatAndMount: common.BoolPtr(true),
		FileSystem:         common.StringPtr("ext4"),
	})
	return d
}

func (d *ModifyNodePoolRequestBuilder) WithInstanceCharge(chargeType string, period uint64, renewFlag string) *ModifyNodePoolRequestBuilder {
	if d.request.Native == nil {
		d.request.Native = &tke3.UpdateNativeNodePoolParam{}
	}

	d.request.Native.InstanceChargeType = common.StringPtr(chargeType)
	d.request.Native.InstanceChargePrepaid = &tke3.InstanceChargePrepaid{
		Period:    &period,
		RenewFlag: common.StringPtr(renewFlag),
	}
	return d
}

func (d *ModifyNodePoolRequestBuilder) WithReplicas(replicas int64) *ModifyNodePoolRequestBuilder {
	if d.request.Native == nil {
		d.request.Native = &tke3.UpdateNativeNodePoolParam{}
	}

	d.request.Native.Replicas = common.Int64Ptr(replicas)
	return d
}

func (d *ModifyNodePoolRequestBuilder) Build() *tke3.ModifyNodePoolRequest {
	return d.request
}

func NewModifyNodePoolRequestBuilder() *ModifyNodePoolRequestBuilder {
	return &ModifyNodePoolRequestBuilder{request: tke3.NewModifyNodePoolRequest()}
}
