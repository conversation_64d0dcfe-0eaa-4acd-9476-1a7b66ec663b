package tke

import (
	cvm "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/cvm/v20170312"
	//tke "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/tke/v20180525"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common"
	tke2 "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke"
)

type CreateClusterRequestBuilder struct {
	request *tke2.CreateClusterRequest
}

func (this *CreateClusterRequestBuilder) WithDomain(domain string) *CreateClusterRequestBuilder {
	this.request.SetDomain(domain)
	return this
}

func (this *CreateClusterRequestBuilder) WithClusterCIDRSettings(clusterCIDR string, ignoreClusterCIDRConflict bool, maxNodePodNum uint64,
	serviceCIDR string, subnetId string, IgnoreServiceCIDRConflict bool) *CreateClusterRequestBuilder {
	this.WithClusterCIDRSettingsBasic(clusterCIDR, ignoreClusterCIDRConflict, maxNodePodNum)
	this.request.ClusterCIDRSettings.ServiceCIDR = &serviceCIDR
	this.request.ClusterCIDRSettings.EniSubnetIds = []*string{&subnetId}
	this.request.ClusterCIDRSettings.IgnoreServiceCIDRConflict = &IgnoreServiceCIDRConflict

	return this
}

func (this *CreateClusterRequestBuilder) WithClusterCIDRSettingsBasic(clusterCIDR string, ignoreClusterCIDRConflict bool, maxNodePodNum uint64) *CreateClusterRequestBuilder {
	if this.request.ClusterCIDRSettings == nil {
		this.request.ClusterCIDRSettings = &tke2.ClusterCIDRSettings{}
	}
	this.request.ClusterCIDRSettings.ClusterCIDR = &clusterCIDR
	this.request.ClusterCIDRSettings.IgnoreClusterCIDRConflict = &ignoreClusterCIDRConflict
	this.request.ClusterCIDRSettings.MaxNodePodNum = &maxNodePodNum
	return this
}

func (this *CreateClusterRequestBuilder) WithClusterCIDRSettingsService(maxClusterServiceNum uint64, serviceCIDR string) *CreateClusterRequestBuilder {
	if this.request.ClusterCIDRSettings == nil {
		this.request.ClusterCIDRSettings = &tke2.ClusterCIDRSettings{}
	}
	this.request.ClusterCIDRSettings.MaxClusterServiceNum = &maxClusterServiceNum
	this.request.ClusterCIDRSettings.ServiceCIDR = &serviceCIDR
	return this
}

func (this *CreateClusterRequestBuilder) WithClusterType(clusterType string) *CreateClusterRequestBuilder {
	this.request.ClusterType = &clusterType
	return this
}

func (this *CreateClusterRequestBuilder) WithClusterAdvancedSettings(networkType string, IsNonStaticIpMode bool) *CreateClusterRequestBuilder {

	if this.request.ClusterAdvancedSettings == nil {
		this.request.ClusterAdvancedSettings = &tke2.ClusterAdvancedSettings{}
	}
	this.request.ClusterAdvancedSettings.NetworkType = &networkType
	// true为非固定ip，false为固定ip
	this.request.ClusterAdvancedSettings.IsNonStaticIpMode = &IsNonStaticIpMode
	return this
}

func (this *CreateClusterRequestBuilder) WithClusterAdvancedSettingsAudit(auditEnabled bool) *CreateClusterRequestBuilder {

	if this.request.ClusterAdvancedSettings == nil {
		this.request.ClusterAdvancedSettings = &tke2.ClusterAdvancedSettings{}
	}

	this.request.ClusterAdvancedSettings.AuditEnabled = &auditEnabled
	return this
}

func (this *CreateClusterRequestBuilder) WithClusterAdvancedSettingsNetworkTypeAudit(networkType string, IsNonStaticIpMode bool,
	AuditEnabled bool, ipvs bool) *CreateClusterRequestBuilder {

	if this.request.ClusterAdvancedSettings == nil {
		this.request.ClusterAdvancedSettings = &tke2.ClusterAdvancedSettings{}
	}
	this.request.ClusterAdvancedSettings.NetworkType = &networkType
	// true为非固定ip，false为固定ip
	this.request.ClusterAdvancedSettings.IsNonStaticIpMode = &IsNonStaticIpMode
	this.request.ClusterAdvancedSettings.AuditEnabled = &AuditEnabled
	this.request.ClusterAdvancedSettings.IPVS = &ipvs
	return this
}

func (this *CreateClusterRequestBuilder) WithExistedInstancesForNode(nodeRole string, existedInstancesPara *tke2.ExistedInstancesPara, instanceAdvancedSettingsOverride *tke2.InstanceAdvancedSettings) *CreateClusterRequestBuilder {
	node := &tke2.ExistedInstancesForNode{
		NodeRole:                         &nodeRole,
		ExistedInstancesPara:             existedInstancesPara,
		InstanceAdvancedSettingsOverride: instanceAdvancedSettingsOverride,
	}
	this.request.ExistedInstancesForNode = append(this.request.ExistedInstancesForNode, node)
	return this
}

// 2020-07-14 TKE接口对参数label不生效
func (this *CreateClusterRequestBuilder) WithRunInstancesForNode(instanceAdvancedSettingsOverrides []*tke2.InstanceAdvancedSettings, nodeRole string, runInstancesPara []*cvm.RunInstancesRequest, labels map[string]string) *CreateClusterRequestBuilder {
	node := &tke2.RunInstancesForNode{
		NodeRole:                          &nodeRole,
		RunInstancesPara:                  nil,
		InstanceAdvancedSettingsOverrides: nil,
	}
	if instanceAdvancedSettingsOverrides != nil && len(instanceAdvancedSettingsOverrides) > 0 {
		node.InstanceAdvancedSettingsOverrides = instanceAdvancedSettingsOverrides
	}
	if len(runInstancesPara) > 0 {
		for _, para := range runInstancesPara {
			str := para.ToJsonString()
			node.RunInstancesPara = append(node.RunInstancesPara, &str)
		}
	}
	if len(labels) > 0 {
		settings := &tke2.InstanceAdvancedSettings{
			MountTarget:     nil,
			DockerGraphPath: nil,
			UserScript:      nil,
			Unschedulable:   nil,
			Labels:          nil,
			DataDisks:       nil,
			ExtraArgs:       nil,
		}
		for k, v := range labels {
			k, v := k, v
			settings.Labels = append(settings.Labels, &tke2.Label{
				Name:  &k,
				Value: &v,
			})
		}
		node.InstanceAdvancedSettingsOverrides = append(node.InstanceAdvancedSettingsOverrides, settings)
	}

	this.request.RunInstancesForNode = append(this.request.RunInstancesForNode, node)
	return this
}

func (this *CreateClusterRequestBuilder) WithClusterBasicSettingsWithLabel(tagSpecification []*tke2.TagSpecification, clusterOs, clusterVersion, clusterName, vpcId string, projectId int64, needWorkSecurityGroup bool) *CreateClusterRequestBuilder {
	this.WithClusterBasicSettings(clusterOs, clusterVersion, clusterName, vpcId, projectId, needWorkSecurityGroup)
	this.request.ClusterBasicSettings.TagSpecification = tagSpecification
	return this
}

func (this *CreateClusterRequestBuilder) WithClusterBasicSettings(clusterOs, clusterVersion, clusterName, vpcId string, projectId int64, needWorkSecurityGroup bool) *CreateClusterRequestBuilder {
	IsAutoUpgrade := true
	this.request.ClusterBasicSettings = &tke2.ClusterBasicSettings{
		ClusterOs:             &clusterOs,
		ClusterVersion:        &clusterVersion,
		ClusterName:           &clusterName,
		ClusterDescription:    nil,
		VpcId:                 &vpcId,
		ProjectId:             &projectId,
		TagSpecification:      nil,
		OsCustomizeType:       nil,
		NeedWorkSecurityGroup: &needWorkSecurityGroup,
		//添加参数，写死为 true
		AutoUpgradeClusterLevel: &tke2.AutoUpgradeClusterLevel{IsAutoUpgrade: &IsAutoUpgrade},
	}
	return this
}

func (this *CreateClusterRequestBuilder) WithExtensionAddons() *CreateClusterRequestBuilder {
	this.request.ExtensionAddons = []*tke2.ExtensionAddon{
		{
			AddonName:  common.StringPtr("APP"),
			AddonParam: common.StringPtr("{\"kind\":\"App\",\"spec\":{\"chart\":{\"chartName\":\"networkpolicy\",\"chartVersion\":\"1.0.0\"}}}"),
		},
		{
			AddonName:  common.StringPtr("CBS"),
			AddonParam: common.StringPtr("{\"kind\":\"App\",\"spec\":{\"chart\":{\"chartName\":\"cbs\",\"chartVersion\":\"1.1.10\"},\"values\":{\"values\":[\"rootdir=/var/lib/containerd\"],\"rawValues\":\"e30=\",\"rawValuesType\":\"json\"}}}"),
		},
	}
	return this
}

func (this *CreateClusterRequestBuilder) WithInstanceDataDiskMountSettings(instanceType, zone string,
	autoFormatAndMount bool, fileSystem, mountTarget, diskType string, diskSize int64) *CreateClusterRequestBuilder {
	this.request.InstanceDataDiskMountSettings = append(this.request.InstanceDataDiskMountSettings, &tke2.InstanceDataDiskMountSetting{
		InstanceType: &instanceType,
		DataDisks: []*tke2.DataDisk{
			{
				DiskType:           &diskType,
				FileSystem:         &fileSystem,
				DiskSize:           &diskSize,
				AutoFormatAndMount: &autoFormatAndMount,
				MountTarget:        &mountTarget,
			},
		},
		Zone: &zone,
	})
	return this
}

// 全局labels
func (this *CreateClusterRequestBuilder) WithInstanceAdvancedSettings(labels map[string]string, userScript string) *CreateClusterRequestBuilder {
	this.request.InstanceAdvancedSettings = &tke2.InstanceAdvancedSettings{
		MountTarget:     nil,
		DockerGraphPath: nil,
		UserScript:      &userScript,
		Unschedulable:   nil,
		Labels:          nil,
		DataDisks:       nil,
		ExtraArgs:       nil,
	}
	if len(labels) > 0 {
		for k, v := range labels {
			k, v := k, v
			this.request.InstanceAdvancedSettings.Labels = append(this.request.InstanceAdvancedSettings.Labels, &tke2.Label{
				Name:  &k,
				Value: &v,
			})
		}
	}

	return this
}

func (this *CreateClusterRequestBuilder) Build() *tke2.CreateClusterRequest {
	return this.request
}

func NewCreateClusterRequestBuilder() *CreateClusterRequestBuilder {
	return &CreateClusterRequestBuilder{request: tke2.NewCreateClusterRequest()}
}
