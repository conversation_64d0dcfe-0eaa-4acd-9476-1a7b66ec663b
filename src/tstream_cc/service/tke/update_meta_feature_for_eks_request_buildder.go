package tke

import (
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	tke2 "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke"
)

type UpdateMetaFeatureForEksRequestBuilder struct {
	request *tke2.UpdateMetaFeatureForEksRequest
}

func newUpdateMetaFeatureForEksRequestBuilder() *UpdateMetaFeatureForEksRequestBuilder {
	return &UpdateMetaFeatureForEksRequestBuilder{request: tke2.NewUpdateMetaFeatureForEksRequest()}
}

func NewDefaultUpdateMetaFeatureForEksRequestBuilder() *UpdateMetaFeatureForEksRequestBuilder {
	return newUpdateMetaFeatureForEksRequestBuilder().WithDomain(constants.TKE_API_DOMAIN_INTERNAL)
}

func (e *UpdateMetaFeatureForEksRequestBuilder) Build() *tke2.UpdateMetaFeatureForEksRequest {
	return e.request
}

func (e *UpdateMetaFeatureForEksRequestBuilder) WithDomain(domain string) *UpdateMetaFeatureForEksRequestBuilder {
	e.request.SetDomain(domain)
	return e
}

func (e *UpdateMetaFeatureForEksRequestBuilder) WithClusterId(clusterId string) *UpdateMetaFeatureForEksRequestBuilder {
	e.request.ClusterId = &clusterId
	return e
}

func (e *UpdateMetaFeatureForEksRequestBuilder) WithBusiness(business string) *UpdateMetaFeatureForEksRequestBuilder {
	e.request.Business = &business
	return e
}

func (e *UpdateMetaFeatureForEksRequestBuilder) WithFeatureType(featureType string) *UpdateMetaFeatureForEksRequestBuilder {
	e.request.FeatureType = &featureType
	return e
}

func (e *UpdateMetaFeatureForEksRequestBuilder) WithTenantParam(param *tke2.TenantParam) *UpdateMetaFeatureForEksRequestBuilder {
	e.request.TenantParam = param
	return e
}
