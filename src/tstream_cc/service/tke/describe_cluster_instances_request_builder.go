package tke

import tke "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/tke/v20180525"

type DescribeClusterInstancesRequestBuilder struct {
	request *tke.DescribeClusterInstancesRequest
}

func (this *DescribeClusterInstancesRequestBuilder) WithDomain(domain string) *DescribeClusterInstancesRequestBuilder {
	this.request.SetDomain(domain)
	return this
}

func (this *DescribeClusterInstancesRequestBuilder) WithClusterId(clusterId string) *DescribeClusterInstancesRequestBuilder {
	this.request.ClusterId = &clusterId
	return this
}

func (this *DescribeClusterInstancesRequestBuilder) WithInstanceIds(InstanceIds []*string) *DescribeClusterInstancesRequestBuilder {
	this.request.InstanceIds = InstanceIds
	return this
}

func (this *DescribeClusterInstancesRequestBuilder) WithOffsetLimit(offset, limit int64) *DescribeClusterInstancesRequestBuilder {
	this.request.Offset = &offset
	this.request.Limit = &limit
	return this
}

func (this *DescribeClusterInstancesRequestBuilder) Build() *tke.DescribeClusterInstancesRequest {
	return this.request
}

func NewDescribeClusterInstancesRequestBuilder() *DescribeClusterInstancesRequestBuilder {
	return &DescribeClusterInstancesRequestBuilder{request: tke.NewDescribeClusterInstancesRequest()}
}
