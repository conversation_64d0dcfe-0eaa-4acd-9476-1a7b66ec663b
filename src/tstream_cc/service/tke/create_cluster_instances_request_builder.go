package tke

import (
	cvm "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/cvm/v20170312"
	tke "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/tke/v20180525"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
)

type CreateClusterInstancesRequestBuilder struct {
	request *tke.CreateClusterInstancesRequest
}

func (b *CreateClusterInstancesRequestBuilder) WithDomain(domain string) *CreateClusterInstancesRequestBuilder {
	b.request.SetDomain(domain)
	return b
}

func (b *CreateClusterInstancesRequestBuilder) WithClusterId(clusterId string) *CreateClusterInstancesRequestBuilder {
	b.request.ClusterId = &clusterId
	return b
}

func (b *CreateClusterInstancesRequestBuilder) WithRunInstancePara(runInstancePara *cvm.RunInstancesRequest) *CreateClusterInstancesRequestBuilder {
	str := runInstancePara.ToJsonString()
	b.request.RunInstancePara = &str
	return b
}

func (b *CreateClusterInstancesRequestBuilder) WithUnschedule() *CreateClusterInstancesRequestBuilder {
	unschedule := int64(1)
	b.request.InstanceAdvancedSettings.Unschedulable = &unschedule
	return b
}

func (b *CreateClusterInstancesRequestBuilder) WithSkipValidateOptions(
	options []string) *CreateClusterInstancesRequestBuilder {
	b.request.SkipValidateOptions = make([]*string, 0, 0)
	for _, op := range options {
		b.request.SkipValidateOptions = append(b.request.SkipValidateOptions, &op)
	}
	return b
}

func (b *CreateClusterInstancesRequestBuilder) WithTaints(
	taints []*tke.Taint) *CreateClusterInstancesRequestBuilder {
	if taints != nil && len(taints) > 0 {
		logger.Infof("CreateClusterInstancesRequestBuilder with taints: %v", taints)
		b.request.InstanceAdvancedSettings.Taints = taints
	}
	return b
}

func (b *CreateClusterInstancesRequestBuilder) WithInstanceAdvancedSettings(autoFormatAndMount bool, fileSystem, mountTarget, diskType string, diskSize int64, labels map[string]string, userScript string) *CreateClusterInstancesRequestBuilder {

	b.request.InstanceAdvancedSettings = &tke.InstanceAdvancedSettings{
		MountTarget:     nil,
		DockerGraphPath: nil,
		UserScript:      &userScript,
		Unschedulable:   nil,
		Labels:          nil,
		DataDisks: []*tke.DataDisk{
			{
				DiskType:           &diskType,
				FileSystem:         &fileSystem,
				DiskSize:           &diskSize,
				AutoFormatAndMount: &autoFormatAndMount,
				MountTarget:        &mountTarget,
			},
		},
		ExtraArgs: nil,
	}
	if len(labels) > 0 {
		for k, v := range labels {
			k, v := k, v
			b.request.InstanceAdvancedSettings.Labels = append(b.request.InstanceAdvancedSettings.Labels, &tke.Label{
				Name:  &k,
				Value: &v,
			})
		}
	}

	return b
}

func (b *CreateClusterInstancesRequestBuilder) Build() *tke.CreateClusterInstancesRequest {
	return b.request
}

func NewCreateClusterInstancesRequestBuilder() *CreateClusterInstancesRequestBuilder {
	return &CreateClusterInstancesRequestBuilder{request: tke.NewCreateClusterInstancesRequest()}
}
