package tke

import (
	tke "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/tke/v20180525"
)

type CancelClusterReleaseRequestBuilder struct {
	request *tke.CancelClusterReleaseRequest
}

func (this *CancelClusterReleaseRequestBuilder) WithClusterId(clusterId string) *CancelClusterReleaseRequestBuilder {
	this.request.ClusterId = &clusterId
	return this
}

func (this *CancelClusterReleaseRequestBuilder) WithClusterType(clusterType string) *CancelClusterReleaseRequestBuilder {
	this.request.ClusterType = &clusterType
	return this
}

func (this *CancelClusterReleaseRequestBuilder) WithId(id string) *CancelClusterReleaseRequestBuilder {
	this.request.ID = &id
	return this
}

func NewCancelClusterReleaseRequestBuilder() *CancelClusterReleaseRequestBuilder {
	return &CancelClusterReleaseRequestBuilder{request: tke.NewCancelClusterReleaseRequest()}
}

func (this *CancelClusterReleaseRequestBuilder) Build() *tke.CancelClusterReleaseRequest {
	return this.request
}
