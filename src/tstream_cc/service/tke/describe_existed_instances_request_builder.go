package tke

import tke "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/tke/v20180525"

type DescribeExistedInstancesRequestBuilder struct {
	request *tke.DescribeExistedInstancesRequest
}

func (this *DescribeExistedInstancesRequestBuilder) WithDomain(domain string) *DescribeExistedInstancesRequestBuilder {
	this.request.SetDomain(domain)
	return this
}

func (this *DescribeExistedInstancesRequestBuilder) WithClusterId(clusterId string) *DescribeExistedInstancesRequestBuilder {
	this.request.ClusterId = &clusterId
	return this
}

func (this *DescribeExistedInstancesRequestBuilder) WithInstanceIds(instanceIds []string) *DescribeExistedInstancesRequestBuilder {
	if len(instanceIds) > 0 {
		for _, id := range instanceIds {
			id := id
			this.request.InstanceIds = append(this.request.InstanceIds, &id)
		}
	}
	return this
}

func (this *DescribeExistedInstancesRequestBuilder) WithVagueInstanceName(instanceName string) *DescribeExistedInstancesRequestBuilder {
	this.request.VagueInstanceName = &instanceName
	return this
}

func (this *DescribeExistedInstancesRequestBuilder) Build() *tke.DescribeExistedInstancesRequest {
	return this.request
}

func NewDescribeExistedInstancesRequestBuilder() *DescribeExistedInstancesRequestBuilder {
	return &DescribeExistedInstancesRequestBuilder{request: tke.NewDescribeExistedInstancesRequest()}
}
