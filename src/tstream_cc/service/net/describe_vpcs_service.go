package service

import (
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/component"
	"fmt"
	"strings"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/net"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/region"
)

func DoDescribeVpcList(req *model.DescribeVpcReq) (int64, string, interface{}) {
	params := buildParams(req)
	regionName := service.SwitchRegion(req.Region)
	regionId, err := region.GetRegionIdByName(regionName)
	if err != nil {
		logger.Errorf("DoDescribeVpcList -> GetRegionIdByName %s error:%+v", regionName, err)
		return constants.SYSERR, err.Error(), nil
	}
	netCenter := component.NewComponentNetCenter(regionId)
	detail, err := netCenter.DescribeVpcList("/vpc/getVpcList?" + params)
	if err != nil {
		logger.Errorf("DoDescribeVpcList -> DescribeVpcList error:%+v", err)
		return constants.SYSERR, err.Error(), nil
	}
	return constants.SUCCESS, constants.OK, detail
}

func buildParams(req *model.DescribeVpcReq) (params string) {
	paramsList := make([]string, 0, 0)
	if req.AppId > 0 {
		paramsList = append(paramsList, "appId=" + fmt.Sprintf("%d", req.AppId))
	}

	if req.VpcId > 0 {
		paramsList = append(paramsList, "vpcId=" + fmt.Sprintf("%d", req.VpcId))
	}

	if req.UniqVpcId != "" {
		paramsList = append(paramsList, "uniqVpcId=" + req.UniqVpcId)
	}

	if len(req.Ip) > 0 {
		paramsList = append(paramsList, "ipList=" + strings.Join(req.Ip, ","))
	}

	if req.SubnetId > 0 {
		paramsList = append(paramsList, "subnetId=" + fmt.Sprintf("%d", req.SubnetId))
	}

	if req.UniqSubnetId != "" {
		paramsList = append(paramsList, "uniqSubnetId=" + req.UniqSubnetId)
	}

	if req.Region != "" {
		paramsList = append(paramsList, "region=" + req.Region)
	}

	return strings.Join(paramsList, "&")
}
