package service

import (
	"errors"
	"fmt"
	"strconv"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/controller"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/configuration_center"
)

/**
 *
 * <AUTHOR>
 * @time 2019/4/28
 * @version 0.4.0
 * @since 0.4.0
 * @see
 */
func GetConfigurationValueByKey(key string) (string, error) {
	txManager := service.GetTxManager()
	sql := "SELECT ConfigurationValue FROM ConfigurationCenter WHERE ConfigurationKey=?"
	count, data, err := txManager.GetQueryTemplate().DoQueryWithArgs(sql, key)
	if err != nil {
		return controller.NULL, err
	} else if count == 0 {
		msg := fmt.Sprintf("Key[%s] not found in ConfigurationCenter", key)
		return controller.NULL, errorcode.InternalErrorCode_UnexpectedRecordNums.NewWithInfo(msg, nil)
	} else if count > 1 {
		msg := fmt.Sprintf("Multiple values found for Key[%s] in ConfigurationCenter", key)
		return controller.NULL, errorcode.InternalErrorCode_UnexpectedRecordNums.NewWithInfo(msg, nil)
	}

	configurationValue := string(data[0]["ConfigurationValue"])
	return configurationValue, nil
}

/*
*

	强行获取 Key 对应的 Value, 如果获取不到则会 panic, 请注意捕获
*/
func MustGetConfigurationValueByKey(key string) string {
	value, err := GetConfigurationValueByKey(key)
	if err != nil {
		logger.Errorf("Failed to get value for %s from ConfigurationCenter because %+v", key, err)
		panic(errorcode.NewStackError(errorcode.InternalErrorCode, "", err))
	}
	return value
}

func GetConfigurationIntValueByKey(key string) (int64, error) {
	if str, err := GetConfigurationValueByKey(key); err != nil {
		return 0, err
	} else if iVal, err := strconv.ParseInt(str, 10, 64); err != nil {
		return 0, err
	} else {
		return iVal, nil
	}
}

func GetConfigurationIntValue(key string, defaultValue int64) (int64, error) {
	if str, err := GetConfigurationValueByKey(key); err != nil {
		return defaultValue, err
	} else if iVal, err := strconv.ParseInt(str, 10, 64); err != nil {
		return defaultValue, err
	} else {
		return iVal, nil
	}
}

/**
 * 通过配置中心的配置类型查找出配置的集合
 */
func GetConfigurationByType(confType int) (kv map[string]string, err error) {
	txManager := service.GetTxManager()
	sql := "SELECT * FROM ConfigurationCenter WHERE Type=?"

	kv = make(map[string]string, 0)
	count, data, err := txManager.GetQueryTemplate().DoQueryWithArgs(sql, confType)
	if err != nil {
		return nil, err
	} else if count == 0 {
		return kv, err
	} else if count > 1 {
		for _, item := range data {
			conf := &table.ConfigurationCenter{}
			util.ScanMapIntoStruct(conf, item)
			kv[conf.ConfigurationKey] = conf.ConfigurationValue
		}
		return kv, err
	}
	return
}

func GetConfigurationValue(key string, defaultValue string) (value string, err error) {
	defer func() {
		if err := recover(); err != nil {
			logger.Warningf("Failed to get configuration by value due to panic %+v, use default value instead", err, defaultValue)
			value = defaultValue
		}
	}()

	txManager := service.GetTxManager()
	sql := "SELECT ConfigurationValue FROM ConfigurationCenter WHERE ConfigurationKey=?"
	count, data, err := txManager.GetQueryTemplate().DoQueryWithArgs(sql, key)
	if err != nil {
		return controller.NULL, err
	} else if count == 0 {
		return defaultValue, nil
	} else if count > 1 {
		return controller.NULL, errors.New(fmt.Sprintf("Multiple values found for Key[%s] in ConfigurationCenter", key))
	}

	configurationValue := string(data[0]["ConfigurationValue"])

	return configurationValue, nil
}

func GetConfigurationValueWithPreferred(baseKey, suffix string) (value string, err error) {
	key := fmt.Sprintf("%s.%s", baseKey, suffix)
	for i := 0; i < 2; i++ {
		value, err = GetConfigurationValue(key, "")
		if err != nil || len(value) > 0 {
			return
		}

		key = baseKey
	}
	err = errorcode.NewStackError(errorcode.InternalErrorCode,
		fmt.Sprintf("%s[.%s] not found in configuration center", baseKey, suffix), nil)
	return
}

func ProtocolCompatibleEnable() (bool, error) {
	value, err := GetConfigurationValue("protocolCompatible.enable", "false")
	if err != nil {
		return false, err
	}
	b, err := strconv.ParseBool(value)
	if err != nil {
		return false, err
	}
	return b, nil
}

func GetCkafkaUrl(region string) (string, error) {
	configurationKey := fmt.Sprintf("ckafka.ckafkaUrl.%s", region)
	configurationValue, err := GetConfigurationValueByKey(configurationKey)
	if err != nil {
		return controller.NULL, err
	} else {
		return configurationValue, nil
	}
}

func GetMaxJobConfigsPerJob() (int, error) {
	configurationKey := "jobconfig.numbers.perJob.max"
	configurationValue, err := GetConfigurationValueByKey(configurationKey)
	if err != nil {
		return -1, err
	} else {
		return strconv.Atoi(configurationValue)
	}
}

func GetPlatformUrl(region string) (string, error) {
	configurationKey := fmt.Sprintf("ckafka.platformUrl.%s", region)
	configurationValue, err := GetConfigurationValueByKey(configurationKey)
	if err != nil {
		return controller.NULL, err
	} else {
		return configurationValue, nil
	}
}

func GetCdpUrl(region string) (string, error) {
	configurationKey := fmt.Sprintf("cdp.cdpUrl.%s", region)
	configurationValue, err := GetConfigurationValueByKey(configurationKey)
	if err != nil {
		return controller.NULL, err
	} else {
		return configurationValue, nil
	}
}

func GetCdpInnerUserKey() (string, error) {
	configurationKey := "cdp.innerUserKey"
	configurationValue, err := GetConfigurationValueByKey(configurationKey)
	if err != nil {
		return controller.NULL, err
	} else {
		return configurationValue, nil
	}
}

func GetCdbUrlByRegionAndType(region string, cdbType string) (string, error) {
	configurationKey := fmt.Sprintf("cdb.cdbUrl.%s.%s", cdbType, region)
	configurationValue, err := GetConfigurationValueByKey(configurationKey)
	if err != nil {
		return controller.NULL, err
	} else {
		return configurationValue, nil
	}
}

func GetScsUserAppId() (string, error) {
	configurationKey := "scsUserAppId"
	configurationValue, err := GetConfigurationValueByKey(configurationKey)
	if err != nil {
		return controller.NULL, err
	} else {
		return configurationValue, nil
	}
}

func GetScsUserUin() (string, error) {
	configurationKey := "scsUserUin"
	configurationValue, err := GetConfigurationValueByKey(configurationKey)
	if err != nil {
		return controller.NULL, err
	} else {
		return configurationValue, nil
	}
}

func GetAccountUrl(region string) (string, error) {
	configurationKey := fmt.Sprintf("accountUrl.%s", region)
	configurationValue, err := GetConfigurationValueByKey(configurationKey)
	if err != nil {
		return controller.NULL, err
	} else {
		return configurationValue, nil
	}
}

func GetScsUserUniqVpcId() (string, error) {
	configurationKey := "scsUserUniqVpcId"
	configurationValue, err := GetConfigurationValueByKey(configurationKey)
	if err != nil {
		return controller.NULL, err
	} else {
		return configurationValue, nil
	}
}

func GetScsUserUniqSubnetId() (string, error) {
	configurationKey := "scsUserUniqSubnetId"
	configurationValue, err := GetConfigurationValueByKey(configurationKey)
	if err != nil {
		return controller.NULL, err
	} else {
		return configurationValue, nil
	}
}

// https://iwiki.woa.com/p/**********
// https://yehe-sg.woa.com/qcloud-intl-account/internalAccountManagement
func GetSecretKeyOfGetAreaByUin() (string, error) {
	configurationKey := "GetAreaByUinSecretKey"
	configurationValue, err := GetConfigurationValueByKey(configurationKey)
	if err != nil {
		return controller.NULL, err
	} else {
		return configurationValue, nil
	}
}

func GetSecretIdOfGetAreaByUin() (string, error) {
	configurationKey := "GetAreaByUinSecretId"
	configurationValue, err := GetConfigurationValueByKey(configurationKey)
	if err != nil {
		return controller.NULL, err
	} else {
		return configurationValue, nil
	}
}

func GetSecretKeyOfScs() (string, error) {
	configurationKey := "SecretKey"
	configurationValue, err := GetConfigurationValueByKey(configurationKey)
	if err != nil {
		return controller.NULL, err
	} else {
		return configurationValue, nil
	}
}

func GetSubAccountSecretKeyOfScs() (string, error) {
	configurationKey := "SubAccountSecretKey"
	configurationValue, err := GetConfigurationValueByKey(configurationKey)
	if err != nil {
		return controller.NULL, err
	} else {
		return configurationValue, nil
	}
}

func GetSecretKeyOfCos() (string, error) {
	configurationKey := "CosSecretKey"
	configurationValue, err := GetConfigurationValueByKey(configurationKey)
	if err != nil {
		return controller.NULL, err
	} else {
		return configurationValue, nil
	}
}

func GetSecretKeyOfCosWriteOnly() (string, error) {
	configurationKey := "CosWriteOnlySecretKey"
	configurationValue, err := GetConfigurationValueByKey(configurationKey)
	if err != nil {
		return controller.NULL, fmt.Errorf("get secret key %s failed: %w", configurationKey, err)
	} else {
		return configurationValue, nil
	}
}

func GetSecretIdOfCosWriteOnly() (string, error) {
	configurationKey := "CosWriteOnlySecretId"
	configurationValue, err := GetConfigurationValueByKey(configurationKey)
	if err != nil {
		return controller.NULL, fmt.Errorf("get secret id %s failed: %w", configurationKey, err)
	} else {
		return configurationValue, nil
	}
}

func GetSecretKeyOfOnline() (string, error) {
	configurationKey := "OnlineSecretKey"
	configurationValue, err := GetConfigurationValueByKey(configurationKey)
	if err != nil {
		return controller.NULL, err
	} else {
		return configurationValue, nil
	}
}

func GetSubAccountSecretKeyOfOnline() (string, error) {
	configurationKey := "SubAccountOnlineSecretKey"
	configurationValue, err := GetConfigurationValueByKey(configurationKey)
	if err != nil {
		return controller.NULL, err
	} else {
		return configurationValue, nil
	}
}

func GetSecretKeyOfTest() (string, error) {
	configurationKey := "TestSecretKey"
	configurationValue, err := GetConfigurationValueByKey(configurationKey)
	if err != nil {
		return controller.NULL, err
	} else {
		return configurationValue, nil
	}
}

func GetSecretKeyOfInner() (string, error) {
	configurationKey := "InnerSecretKey"
	configurationValue, err := GetConfigurationValueByKey(configurationKey)
	if err != nil {
		return controller.NULL, err
	} else {
		return configurationValue, nil
	}
}

func GetNetCenterAddress() (string, error) {
	configurationKey := "NetCenterAddress"
	configurationValue, err := GetConfigurationValueByKey(configurationKey)
	if err != nil {
		return controller.NULL, err
	} else {
		return configurationValue, nil
	}
}

func GetSecretIdOfScs() (string, error) {
	configurationKey := "SecretId"
	configurationValue, err := GetConfigurationValueByKey(configurationKey)
	if err != nil {
		return controller.NULL, err
	} else {
		return configurationValue, nil
	}
}

func GetSubAccountSecretIdOfScs() (string, error) {
	configurationKey := "SubAccountSecretId"
	configurationValue, err := GetConfigurationValueByKey(configurationKey)
	if err != nil {
		return controller.NULL, err
	} else {
		return configurationValue, nil
	}
}

func GetSecretIdOfCos() (string, error) {
	configurationKey := "CosSecretId"
	configurationValue, err := GetConfigurationValueByKey(configurationKey)
	if err != nil {
		return controller.NULL, err
	} else {
		return configurationValue, nil
	}
}

func GetSecretIdOfOnline() (string, error) {
	configurationKey := "OnlineSecretId"
	configurationValue, err := GetConfigurationValueByKey(configurationKey)
	if err != nil {
		return controller.NULL, err
	} else {
		return configurationValue, nil
	}
}

func GetSubAccountSecretIdOfOnline() (string, error) {
	configurationKey := "SubAccountOnlineSecretId"
	configurationValue, err := GetConfigurationValueByKey(configurationKey)
	if err != nil {
		return controller.NULL, err
	} else {
		return configurationValue, nil
	}
}

func GetOceanusCloudApiUinOfOnline() (string, error) {
	configurationKey := "OnlineOceanusCloudApiUin"
	configurationValue, err := GetConfigurationValueByKey(configurationKey)
	if err != nil {
		return controller.NULL, err
	} else {
		return configurationValue, nil
	}
}

func GetOceanusCloudApiUinOfScs() (string, error) {
	configurationKey := "OceanusCloudApiUin"
	configurationValue, err := GetConfigurationValueByKey(configurationKey)
	if err != nil {
		return controller.NULL, err
	} else {
		return configurationValue, nil
	}
}

func GetSecretIdOfTest() (string, error) {
	configurationKey := "TestSecretId"
	configurationValue, err := GetConfigurationValueByKey(configurationKey)
	if err != nil {
		return controller.NULL, err
	} else {
		return configurationValue, nil
	}
}

func GetSecretIdInner() (string, error) {
	configurationKey := "InnerSecretId"
	configurationValue, err := GetConfigurationValueByKey(configurationKey)
	if err != nil {
		return controller.NULL, err
	} else {
		return configurationValue, nil
	}
}

func GetTestCamUrl() (string, error) {
	configurationKey := "TestCamUrl"
	configurationValue, err := GetConfigurationValueByKey(configurationKey)
	if err != nil {
		return controller.NULL, err
	} else {
		return configurationValue, nil
	}
}

func GetScsWorkspace() (string, error) {
	configurationKey := "Workspace"
	configurationValue, err := GetConfigurationValueByKey(configurationKey)
	if err != nil {
		return controller.NULL, err
	} else {
		return configurationValue, nil
	}
}

func GetCreateClusterWhiteList() (whiteList []string, err error) {
	configurationKey := "ClusterWhiteList"
	configurationValue, err := GetConfigurationValue(configurationKey, "")
	if err != nil {
		return nil, err
	}

	if configurationValue == "" {
		return nil, err
	} else {
		return strings.Split(configurationValue, ","), err
	}
}

func GetDefaultResourceMaxSize() (string, string, error) {
	resourceDefaultMaxSize, err := GetConfigurationValueByKey("ResourceDefaultMaxSize")
	if err != nil {
		return controller.NULL, controller.NULL, err
	}
	dependencyDefaultMaxSize, err := GetConfigurationValueByKey("DependencyDefaultMaxSize")
	if err != nil {
		return controller.NULL, controller.NULL, err
	}
	return resourceDefaultMaxSize, dependencyDefaultMaxSize, nil
}

func GetNetcenterUrl(region string, vpcId string, subnetId string) (string, error) {
	configurationKey := "netcenterUrl." + region + "." + vpcId + "." + subnetId
	configurationValue, err := GetConfigurationValueByKey(configurationKey)
	if err != nil {
		return controller.NULL, err
	} else {
		return configurationValue, nil
	}
}

func GetEntrypointClass(jobType int8) (string, error) {
	if jobType == constants.JOB_TYPE_SQL || jobType == constants.JOB_TYPE_ETL {
		configurationKey := "SQL.EntrypointClass"
		configurationValue, err := GetConfigurationValueByKey(configurationKey)
		if err != nil {
			return controller.NULL, err
		} else {
			return configurationValue, nil
		}
	} else {
		return controller.NULL, errors.New("GetEntrypointClass only support SQL Job")
	}
}

func GetJarFilePath(jobType int8) (string, error) {
	if jobType == constants.JOB_TYPE_SQL || jobType == constants.JOB_TYPE_ETL {
		configurationKey := "SQL.JarFilePath"
		configurationValue, err := GetConfigurationValueByKey(configurationKey)
		if err != nil {
			return controller.NULL, err
		} else {
			return configurationValue, nil
		}
	} else {
		return controller.NULL, errors.New("GetJarFilePath only support SQL Job")
	}
}

func GetShipFiles(jobType int8) (string, error) {
	if jobType == constants.JOB_TYPE_SQL || jobType == constants.JOB_TYPE_ETL {
		configurationKey := "SQL.ShipFiles"
		configurationValue, err := GetConfigurationValueByKey(configurationKey)
		if err != nil {
			return controller.NULL, err
		} else {
			return configurationValue, nil
		}
	} else {
		return controller.NULL, errors.New("GetShipFiles only support SQL Job")
	}
}
