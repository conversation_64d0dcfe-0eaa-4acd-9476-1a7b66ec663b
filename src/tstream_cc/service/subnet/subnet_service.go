package subnet

import (
	"context"
	"encoding/json"
	"fmt"
	tke3 "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/tke/v20180525"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/tools/clientcmd"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/dlocker"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	subnetModel "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/subnet"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	clusterService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
	tke "tencentcloud.com/tstream_galileo/src/tstream_cc/service/tke"
	tke2 "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke"
	netv1client "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/client/generated/clientset/versioned/typed/networking/v1"
)

const (
	NAMESPACE         = "kube-system"
	IpamdPodName      = "tke-eni-ipamd"
	StaticMode        = "--static-ip=false"
	TkeCTENIConfigKey = "TKE_ENI_IPAMD_CROSS_TENANT"
	LabelName         = "k8s-app"
	SEARCH_TYPE       = 1
)

type ModifySubnetService struct {
	Req *subnetModel.ModifySubnetReq
}

func (m *ModifySubnetService) GetTkeIPAMDStatus(instanceId string) (bool, error) {
	describeIPAMDRequest := tke3.NewDescribeIPAMDRequest()
	describeIPAMDRequest.ClusterId = &instanceId

	logger.Infof("requestId : [%s] describeIPAMDRequest is %+v", m.Req.RequestId, describeIPAMDRequest)
	describeIPAMDResponse, err := tke.GetTkeService().DescribeIPAMDWithScsAccount(m.Req.Region, describeIPAMDRequest)
	if err != nil {
		logger.Errorf("DescribeIPMDWithScsAccount, clusterId:%s,msg:%s", m.Req.ClusterId, err.Error())
		return false, errorcode.FailedOperationCode_ModifySubnetFailure.ReplaceDesc(err.Error())
	}
	logger.Infof("requestId : [%s] describeIPAMDResponse.Response.Phase is %s", m.Req.RequestId, describeIPAMDResponse.Response.Phase)
	if *describeIPAMDResponse.Response.Phase == "running" {
		return true, nil
	}
	return false, nil
}

func (m *ModifySubnetService) GetEksIPAMDStatus(instanceId string) (bool, error) {

	req := tke.NewDefaultDescribeEksMetaFeatureProgressRequestBuilder().WithClusterId(instanceId).Build()
	found, rsp, err := tke.GetTkeService().DescribeEksMetaFeatureProgressWithScsAccount(m.Req.Region, req)
	if err != nil {
		return false, err
	}
	if !found {
		msg := fmt.Sprintf("tke %s EnableEksMetaFeature Process Not Found", m.Req.ClusterId)
		err = errorcode.InternalErrorCode.NewWithInfo(msg, nil)
		return false, err
	}
	logger.Infof("DescribeEksMetaFeatureProgress: %s", rsp.ToJsonString())
	if *rsp.Response.Status == "succeeded" {
		return true, nil
	}
	return false, nil
}

func (m *ModifySubnetService) SearchResult() (bool, error) {
	clusterGroupService, err := clusterService.NewClusterGroupServiceBySerialId(m.Req.ClusterId)
	if err != nil {
		logger.Errorf("requestId : [%s] NewClusterGroupServiceBySerialId clusterId:%s,msg:%s", m.Req.RequestId, m.Req.ClusterId, err.Error())
		return false, errorcode.FailedOperationCode_ModifySubnetFailure.ReplaceDesc(err.Error())
	}
	instance, err := clusterGroupService.GetTke()
	if err != nil {
		logger.Errorf("requestId : [%s] clusterGroupService.GetTke :%s,msg:%s", m.Req.RequestId, m.Req.ClusterId, err.Error())
		return false, errorcode.FailedOperationCode_ModifySubnetFailure.ReplaceDesc(err.Error())
	}
	clusterGroup := clusterGroupService.GetClusterGroup()
	cluster, _ := clusterGroupService.GetActiveCluster()
	isEks, _ := clusterService.IsEks(clusterGroup.Id)
	rst := false
	if isEks {
		rst, err = m.GetEksIPAMDStatus(instance.InstanceId)
		if err != nil {
			return rst, err
		}
	} else {
		rst, err = m.GetTkeIPAMDStatus(instance.InstanceId)
		if err != nil {
			return rst, err
		}
	}

	if rst {
		// 忽略这个设置错误
		// 更新sql server
		ip, _ := m.GetEniIp(m.Req, clusterGroup, cluster, constants.SqlServer.ResourceName, isEks)
		if ip != "" {
			_ = m.DeleteVpcEni(m.Req, cluster, ip)
		}
		if instance.ArchGeneration <= constants.TKE_ARCH_GENERATION_V2 {
			_ = m.RestartSts(m.Req, clusterGroup, cluster, constants.SqlServer.ResourceName)
		}
		if instance.ArchGeneration >= constants.TKE_ARCH_GENERATION_V3 {
			_ = m.RestartDeployment(m.Req, clusterGroup, cluster, constants.SqlServer.ResourceName)
		}

		if clusterGroup.AgentSerialId != "" {
			// 忽略这个设置错误
			// 更新UserAgent
			ip, _ := m.GetEniIp(m.Req, clusterGroup, cluster, constants.UserAgent.ResourceName, isEks)
			if ip != "" {
				_ = m.DeleteVpcEni(m.Req, cluster, ip)
			}
			_ = m.RestartDeployment(m.Req, clusterGroup, cluster, constants.UserAgent.ResourceName)
		} else {
			// 忽略这个设置错误
			// 更新ClusterScheduler
			ip, _ := m.GetEniIp(m.Req, clusterGroup, cluster, constants.ClusterScheduler.ResourceName, isEks)
			if ip != "" {
				_ = m.DeleteVpcEni(m.Req, cluster, ip)
			}
			if instance.ArchGeneration <= constants.TKE_ARCH_GENERATION_V2 {
				_ = m.RestartSts(m.Req, clusterGroup, cluster, constants.ClusterScheduler.ResourceName)
			}
			if instance.ArchGeneration >= constants.TKE_ARCH_GENERATION_V3 {
				_ = m.RestartDeployment(m.Req, clusterGroup, cluster, constants.ClusterScheduler.ResourceName)
			}

		}

		return true, nil
	}
	return false, nil
}

func (m *ModifySubnetService) ModifySubnet() (rsp *subnetModel.ModifySubnetRsp, anyError error) {

	defer errorcode.DefaultDeferHandler(&anyError)
	// 共享集群下的多可用区部署，不需要调用tke接口，
	// tke.cloud.tencent.com/cross-tenant-eni-config: '{"AppId":1257058945,"Uin":"100006386216","UniqVpcId":"vpc-ef2bda10","SubnetId":"subnet-mb4cys35"}'
	isShareMultiple := false
	if m.Req.Zone != "" {
		isShareMultiple = true
	}
	if m.Req.Type == SEARCH_TYPE {
		result, err := m.SearchResult()
		return &subnetModel.ModifySubnetRsp{
			Result: result,
		}, err
	}
	// 1. 进行参数校验
	if m.Req.OldSubnetId == m.Req.NewSubnetId {
		return
	}
	if err := m.valid(m.Req); err != nil {
		return &subnetModel.ModifySubnetRsp{}, err
	}

	// 加锁避免并发修改
	locker := dlocker.NewDlocker("ModifySubnet", fmt.Sprintf("%s-%s-%s", m.Req.ClusterId, m.Req.OldSubnetId, m.Req.NewSubnetId), 6000)
	err := locker.Lock()
	if err != nil {
		logger.Errorf("ModifySubnet locker error: %s", err.Error())
		return &subnetModel.ModifySubnetRsp{}, errorcode.FailedOperationCode_ModifySubnetFailure.ReplaceDesc("Maybe someone else is modifying,please retry later!")
	}
	defer locker.UnLock()

	// 2. 更换集群子网
	// 2.1 获取集群ClusterGroup信息
	clusterGroupService, err := clusterService.NewClusterGroupServiceBySerialId(m.Req.ClusterId)
	if err != nil {
		logger.Errorf("clusterId:%s,msg:%s", m.Req.ClusterId, err.Error())
		return &subnetModel.ModifySubnetRsp{}, errorcode.FailedOperationCode_ModifySubnetFailure.ReplaceDesc(err.Error())
	}
	// 资源级校验
	// SerialId/AppId/OwnerUin/CreatorUin检查记录是否存在
	clusterGroup := clusterGroupService.GetClusterGroup()
	// 若有一个与传入的数据不想等则资源修改非法
	if clusterGroup.AppId != int32(m.Req.AppId) || clusterGroup.OwnerUin != m.Req.Uin {
		logger.Errorf("illegal request,clusterGroup AppId: %d, request: AppId: %d,clusterGroup OwnerUin: %s, request OwnerUin: %s", clusterGroup.AppId, m.Req.AppId, clusterGroup.OwnerUin, m.Req.Uin)
		return &subnetModel.ModifySubnetRsp{}, errorcode.FailedOperationCode_ModifySubnetFailure.ReplaceDesc("illegal request")
	}
	// 非VPC公有云集群不支持更换子网
	if clusterGroup.NetEnvironmentType != constants.NETWORK_ENV_CLOUD_VPC {
		logger.Warningf("The cluster is not support modify the subnet,NetEnvironmentType: %d", clusterGroup.NetEnvironmentType)
		return &subnetModel.ModifySubnetRsp{}, errorcode.UnsupportedOperationCode.ReplaceDesc("The cluster is not support modify the subnet")
	}
	// GroupId/VpcId/SubnetId(OldSubnetId)检查记录是否存在
	groupPeerVpc, err := clusterGroupService.GetGroupPeerVpc()
	if err != nil {
		logger.Errorf("GetGroupPeerVpc error, clusterId:%s,msg:%s", m.Req.ClusterId, err.Error())
		return &subnetModel.ModifySubnetRsp{}, errorcode.FailedOperationCode_ModifySubnetFailure.ReplaceDesc(err.Error())
	}
	// 不是同一个vpc下的子网是不允许的
	if groupPeerVpc.VpcId != m.Req.VpcId {
		logger.Errorf("groupPeerVpc.VpcId:%s,m.Req.VpcId:%s is different", groupPeerVpc.VpcId, m.Req.VpcId)
		return &subnetModel.ModifySubnetRsp{}, errorcode.FailedOperationCode_ModifySubnetFailure.ReplaceDesc("vpc is different")
	}
	// 2.2 获取Cluster信息kubeconfig信息并初始化k8s客户端
	cluster, err := clusterGroupService.GetActiveCluster()
	if err != nil {
		logger.Errorf("GetActiveCluster error, clusterId:%s,msg:%s", m.Req.ClusterId, err.Error())
		return &subnetModel.ModifySubnetRsp{}, errorcode.FailedOperationCode_ModifySubnetFailure.ReplaceDesc(err.Error())
	}

	if isShareMultiple {
		if clusterGroup.DeploymentMode != constants.DeploymentModeMultiple {
			logger.Errorf("The cluster is not support modify the subnet,DeploymentMode: %d", clusterGroup.DeploymentMode)
			return &subnetModel.ModifySubnetRsp{},
				errorcode.FailedOperationCode_ModifySubnetFailure.
					ReplaceDesc(fmt.Sprintf("The cluster is not support modify the subnet,DeploymentMode: %d", clusterGroup.DeploymentMode))
		}
		zoneSubnets, err := cluster.GetSupportedZoneSubnets()
		if err != nil {
			logger.Errorf("GetSupportedZoneSubnets error, clusterId:%s,msg:%s", m.Req.ClusterId, err.Error())
			return &subnetModel.ModifySubnetRsp{}, errorcode.FailedOperationCode_ModifySubnetFailure.ReplaceDesc(err.Error())
		}
		subnet := zoneSubnets[m.Req.Zone]
		if subnet == "" {
			logger.Errorf("zone %s does not support", m.Req.Zone)
			return &subnetModel.ModifySubnetRsp{}, errorcode.FailedOperationCode_ModifySubnetFailure.ReplaceDesc(fmt.Sprintf("zone %s does not support", m.Req.Zone))
		}
	}

	// 如果不是tke集群不支持更换
	if cluster.SchedulerType != constants.CLUSTER_SCHEDULER_TYPE_TKE {
		logger.Warningf("The cluster is not support modify the subnet,SchedulerType: %d", cluster.SchedulerType)
		return &subnetModel.ModifySubnetRsp{}, errorcode.UnsupportedOperationCode.ReplaceDesc("The cluster is not support modify the subnet")
	}

	// 多可用区 不需要
	if !isShareMultiple {
		// groupPeerVpc.AppId = 0 表明， 是一个正常的用户， Oceanus集群和VPC都在用户账号下面
		// groupPeerVpc.AppId ！= 0 表明，就是wedata的用户， Oceanus集群托管到wedata的运营账号， VPC是用户的
		appId := groupPeerVpc.AppId
		ownerUin := groupPeerVpc.OwnerUin
		if appId == 0 || ownerUin == "" {
			appId = clusterGroup.AppId
			ownerUin = clusterGroup.OwnerUin
		}

		// 构建新的跨租户参数
		tenantParam := &tke2.TenantParam{
			AppId:     uint64(appId),
			Uin:       ownerUin,
			UniqVpcId: groupPeerVpc.VpcId,
			// 子网设置为更换的新子网
			SubnetId: m.Req.NewSubnetId,
		}

		// tke、eks集群调用不同的云API接口
		isEks, isEksErr := clusterService.IsEks(clusterGroup.Id)
		logger.Infof("requestId : [%s] ModifySubnet isEks : %s , clusterGroup.Id : %s", m.Req.RequestId, isEks, clusterGroup.Id)
		if isEksErr != nil {
			logger.Errorf("Determine whether the cluster is eks error, clusterId:%s,msg:%s", m.Req.ClusterId, isEksErr.Error())
			return &subnetModel.ModifySubnetRsp{}, errorcode.FailedOperationCode_ModifySubnetFailure.ReplaceDesc(isEksErr.Error())
		}

		if isEks {
			// eks集群
			// 构建更新跨租户参数的请求
			updateMetaFeatureForEksRequest := tke.NewDefaultUpdateMetaFeatureForEksRequestBuilder().
				WithClusterId(cluster.UniqClusterId).
				WithTenantParam(tenantParam).
				WithFeatureType(constants.TkeMetaFeatureTypeCrossTenant).
				WithBusiness(constants.EksMetaFeatureBusiness).
				Build()
			// 2.2 更新EKS集群的跨租户参数
			err3 := tke.GetTkeService().UpdateMetaFeatureForEksWithScsAccount(clusterGroup.Region, updateMetaFeatureForEksRequest)
			if err3 != nil {
				logger.Errorf("UpdateMetaFeatureForEksWithScsAccount, clusterId:%s,msg:%s", m.Req.ClusterId, err3.Error())
				return &subnetModel.ModifySubnetRsp{}, errorcode.FailedOperationCode_ModifySubnetFailure.ReplaceDesc(err3.Error())
			}
		} else {
			// 非eks集群
			// 构建更新跨租户参数的请求
			updateMetaFeatureRequest := tke.NewDefaultUpdateMetaFeatureRequestBuilder().
				WithClusterId(cluster.UniqClusterId).
				WithTenantParam(tenantParam).
				Build()

			// 2.2 更新TKE集群的跨租户参数
			err2 := tke.GetTkeService().UpdateMetaFeatureWithScsAccount(clusterGroup.Region, updateMetaFeatureRequest)
			if err2 != nil {
				logger.Errorf("UpdateMetaFeatureWithScsAccount, clusterId:%s,msg:%s", m.Req.ClusterId, err2.Error())
				return &subnetModel.ModifySubnetRsp{}, errorcode.FailedOperationCode_ModifySubnetFailure.ReplaceDesc(err2.Error())
			}
		}
	}
	// 修改备可用区的子网, 只更新 cluster 的 SupportedZoneSubnets
	if isShareMultiple && m.Req.Zone != clusterGroup.Zone {
		supportedZoneSubnets := make(map[string]string)
		if len(cluster.SupportedZoneSubnets) != 0 {
			err = json.Unmarshal([]byte(cluster.SupportedZoneSubnets), &supportedZoneSubnets)
			if err != nil {
				logger.Errorf("Unmarshal error, clusterId:%s,msg:%s", m.Req.ClusterId, err.Error())
			}
		}
		supportedZoneSubnets[m.Req.Zone] = m.Req.NewSubnetId
		szs, err := json.Marshal(supportedZoneSubnets)
		if err != nil {
			logger.Errorf("Marshal error, clusterId:%s,msg:%s", m.Req.ClusterId, err.Error())
			return &subnetModel.ModifySubnetRsp{}, errorcode.FailedOperationCode_ModifySubnetFailure.ReplaceDesc(err.Error())
		}
		service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
			args := make([]interface{}, 0)
			args = append(args, szs)
			args = append(args, cluster.Id)
			args = append(args, cluster.CreatorUin)
			tx.ExecuteSql("UPDATE Cluster SET SupportedZoneSubnets=? WHERE Id=? AND CreatorUin=?", args)
			return nil
		}).Close()
	} else {
		// 2.3 更新ClusterGroupPeerVpc对应的SubnetId的值
		groupPeerVpc.SubnetId = m.Req.NewSubnetId
		txManager := service.GetTxManager()
		txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
			tx.UpdateObject(groupPeerVpc, groupPeerVpc.Id, "ClusterGroupPeerVpc")
			return nil
		}).Close()

		//更新cluster的clusterConfig
		clusterConfig := cluster.ClusterConfig
		newClusterConfig := strings.Replace(clusterConfig, m.Req.OldSubnetId, m.Req.NewSubnetId, -1)
		err = clusterService.ModifyClusterConfig(cluster.Id, cluster.CreatorUin, newClusterConfig)
		if err != nil {
			logger.Errorf("ModifyClusterConfig error, clusterId:%s,msg:%s", m.Req.ClusterId, err.Error())
			return nil, err
		}
	}
	return &subnetModel.ModifySubnetRsp{}, nil
}

func DefaultNamespace(cg *table.ClusterGroup) string {
	result := constants.OCEANUS_NAMESPACE
	if cg.AgentSerialId != "" {
		result = fmt.Sprintf("oceanus-%s", cg.SerialId)
	}
	return result
}

func (m *ModifySubnetService) GetEniIp(req *subnetModel.ModifySubnetReq, clusterGroup *table.ClusterGroup,
	cluster *table.Cluster, app string, isEks bool) (string, error) {

	// 获取弹性网卡ip，设置webui地址.
	// 获取Deployment所关联的Pod信息
	namespace := DefaultNamespace(clusterGroup)
	k8sClient, err := tke.GetTkeService().KubernetesClientsetFromCluster("ModifySubnetService", cluster)
	if err != nil {
		errMsg := fmt.Sprintf("[%s] Failed to get k8s client %v", req.RequestId, err)
		logger.Errorf(errMsg)
		return "", errorcode.NewStackError(errorcode.FailedOperationCode, errMsg, err)
	}
	pods, err := k8sClient.CoreV1().Pods(namespace).List(context.TODO(), metav1.ListOptions{
		LabelSelector: fmt.Sprintf("app==%s", app),
	})
	if err != nil {
		return "", errorcode.FailedOperationCode.NewWithErr(err)
	}
	logger.Infof("### %s pods is %+v", app, pods)
	ip := ""
	for _, pod := range pods.Items {
		logger.Infof("Pod Name: %s\n", pod.Name)
		// 可以根据需要输出更多的Pod信息
		// 例如：pod.Status.Phase, pod.Status.StartTime, pod.Status.PodIP 等
		// 可以根据需要进一步操作Pod，例如获取日志、删除Pod等
		/**
		TKE tke.cloud.tencent.com/networks-status
		[{
			"name": "tke-bridge",
			"interface": "eth0",
			"ips": ["**********"],
			"mac": "fe:dd:b6:6b:f7:29",
			"default": true,
			"dns": {}
		}, {
			"name": "tke-direct-eni",
			"interface": "eth1",
			"ips": ["*************"],
			"mac": "20:90:6F:4F:ED:C5",
			"dns": {}
		}, {
			"name": "tke-route",
			"ips": ["*******"],
			"dns": {}
		}]

		EKS tke.cloud.tencent.com/attached-cross-tenant-eni

		{"eniID":"eni-dat2gtio","mac":"20:90:6F:40:07:82","primaryIP":"***********","subnetCIDR":"**********/24","attachedInsID":"eks-c2eqwv46"}
		*/
		if !strings.Contains(pod.Name, app) {
			continue
		}

		if !isEks {
			strNetworksStatus, exist := pod.Annotations["tke.cloud.tencent.com/networks-status"]
			if !exist {
				errMsg := fmt.Sprintf("Pod %s can't find tke.cloud.tencent.com/networks-status, please check.", pod.Name)
				logger.Errorf(errMsg)
				return "", errorcode.UnsupportedOperationCode.ReplaceDesc(errMsg)
			}
			logger.Infof("tke.cloud.tencent.com/networks-status info is %s", strNetworksStatus)
			ip, err = ParseIp(strNetworksStatus)
			if err != nil {
				return "", errorcode.FailedOperationCode.NewWithErr(err)
			}
		} else {
			attachedCrossTenantEni, exist := pod.Annotations["tke.cloud.tencent.com/attached-cross-tenant-eni"]
			if !exist {
				errMsg := fmt.Sprintf("Pod %s can't find tke.cloud.tencent.com/attached-cross-tenant-eni, please check.", pod.Name)
				logger.Errorf(errMsg)
				return "", errorcode.UnsupportedOperationCode.ReplaceDesc(errMsg)
			}
			logger.Infof("tke.cloud.tencent.com/attached-cross-tenant-eni info is %s", attachedCrossTenantEni)
			ip, err = ParseEksIp(attachedCrossTenantEni)
			if err != nil {
				return "", errorcode.FailedOperationCode.NewWithErr(err)
			}
		}
	}

	return ip, nil
}

func ParseIp(strNetworksStatus string) (string, error) {
	ip := ""
	networksStatusList := make([]map[string]interface{}, 0, 0)
	err := json.Unmarshal([]byte(strNetworksStatus), &networksStatusList)
	logger.Debugf("after Unmarshal  %+v", networksStatusList)
	if err != nil {
		logger.Errorf("json.Unmarshal([]byte(%s), []map[string]interface{}) error, %+v", strNetworksStatus, err)
		return "", errorcode.FailedOperationCode.NewWithErr(err)
	}
	for _, networksStatus := range networksStatusList {
		logger.Debugf("networksStatus is %+v", networksStatus)
		if _, exist := networksStatus["name"]; !exist {
			continue
		}
		if networksStatus["name"] != "tke-direct-eni" {
			continue
		}
		if _, exist := networksStatus["ips"]; !exist {
			continue
		}
		ips := networksStatus["ips"]
		ipList, ok := ips.([]interface{})
		if !ok {
			continue
		}
		if len(ipList) < 1 {
			continue
		}
		ip = ipList[0].(string)
		break
	}
	return ip, nil
}

func ParseEksIp(attachedCrossTenantEni string) (string, error) {
	attachedCrossTenantEniList := make(map[string]interface{}, 0)
	err := json.Unmarshal([]byte(attachedCrossTenantEni), &attachedCrossTenantEniList)
	logger.Debugf("after Unmarshal  %+v", attachedCrossTenantEniList)
	if err != nil {
		logger.Errorf("json.Unmarshal([]byte(%s), map[string]interface{}) error, %+v", attachedCrossTenantEni, err)
		return "", errorcode.FailedOperationCode.NewWithErr(err)
	}
	if _, exist := attachedCrossTenantEniList["primaryIP"]; !exist {
		logger.Error("attachedCrossTenantEniList primaryIP does not exist")
		return "", errorcode.FailedOperationCode.NewWithMsg("attachedCrossTenantEniList primaryIP does not exist")
	}
	return attachedCrossTenantEniList["primaryIP"].(string), nil
}

func (m *ModifySubnetService) DeleteVpcEni(req *subnetModel.ModifySubnetReq, cluster *table.Cluster, eniIp string) error {
	clientConfig, err := clientcmd.RESTConfigFromKubeConfig([]byte(cluster.KubeConfig))
	if err != nil {
		errMsg := fmt.Sprintf("[%s] Failed to get k8s client config: %v", req.RequestId, err)
		logger.Errorf(errMsg)
		return errorcode.NewStackError(errorcode.FailedOperationCode, errMsg, err)
	}
	// 忽略证书校验 避免 JNS GW 裁撤时连接失败
	clientConfig.Insecure = true
	clientConfig.CAData = nil
	netv1Client, err := netv1client.NewForConfig(clientConfig)
	if err != nil {
		errMsg := fmt.Sprintf("[%s] Failed to set netv1Client config: %v", req.RequestId, err)
		logger.Errorf(errMsg)
		return errorcode.NewStackError(errorcode.FailedOperationCode, errMsg, err)
	}

	vpcENIs, err := netv1Client.VpcENIs().List(metav1.ListOptions{
		LabelSelector: fmt.Sprintf("tke.cloud.tencent.com/primary-vpc-ip==%s", eniIp),
	})
	if err != nil {
		errMsg := fmt.Sprintf("[%s] Failed to set netv1Client config: %v", req.RequestId, err)
		logger.Errorf(errMsg)
		return errorcode.NewStackError(errorcode.FailedOperationCode, errMsg, err)
	}
	logger.Infof("vpcENIs is %v", vpcENIs)
	logger.Infof("vpcENIs.Items are %v", vpcENIs.Items)
	if len(vpcENIs.Items) == 1 {
		name := vpcENIs.Items[0].Name
		logger.Infof("delete vpcENI name is %s", name)
		err = netv1Client.VpcENIs().Delete(name, &metav1.DeleteOptions{})
		errMsg := fmt.Sprintf("[%s] Delete netv1Client error: %v", req.RequestId, err)
		logger.Errorf(errMsg)
	}
	return nil
}

func (m *ModifySubnetService) RestartSts(req *subnetModel.ModifySubnetReq, clusterGroup *table.ClusterGroup, cluster *table.Cluster, app string) error {
	k8sClient, err := tke.GetTkeService().KubernetesClientsetFromCluster("ModifySubnetService-RestartService", cluster)
	if err != nil {
		errMsg := fmt.Sprintf("[%s] Failed to get k8s client %v", req.RequestId, err)
		logger.Errorf(errMsg)
		return errorcode.NewStackError(errorcode.FailedOperationCode, errMsg, err)
	}
	namespace := DefaultNamespace(clusterGroup)

	k8sService := k8s.NewK8sService()
	sts, err := k8sService.GetStatefulSetAppsV1(k8sClient, namespace, app)
	if err != nil || sts == nil {
		errMsg := fmt.Sprintf("[%s] Failed to get sts %s from ns  %s error %v", app, namespace, err)
		logger.Errorf(errMsg)
		return errorcode.NewStackError(errorcode.FailedOperationCode, errMsg, err)
	}

	annotations := sts.Spec.Template.Annotations
	annotations["restartTime"] = fmt.Sprintf("%d", util.GetNowTimestamp())

	sts.Spec.Template.Annotations = annotations
	_, err = k8sService.ApplyStatefulSet(k8sClient, sts)
	if err != nil {
		errMsg := fmt.Sprintf("[%s] Failed to apply sts %s from ns  %s error %v", app, namespace, err)
		logger.Errorf(errMsg)
		return errorcode.NewStackError(errorcode.FailedOperationCode, errMsg, err)
	}
	return nil
}

func (m *ModifySubnetService) RestartDeployment(req *subnetModel.ModifySubnetReq, clusterGroup *table.ClusterGroup, cluster *table.Cluster, app string) error {
	// 节点弹性网卡不重启
	if clusterGroup.NetEniType == constants.CLUSTER_NET_ENI_NODE {
		return nil
	}
	k8sClient, err := tke.GetTkeService().KubernetesClientsetFromCluster("ModifySubnetService-RestartService", cluster)
	if err != nil {
		errMsg := fmt.Sprintf("[%s] Failed to get k8s client %v", req.RequestId, err)
		logger.Errorf(errMsg)
		return errorcode.NewStackError(errorcode.FailedOperationCode, errMsg, err)
	}
	namespace := DefaultNamespace(clusterGroup)

	k8sService := k8s.NewK8sService()
	deploy, err := k8sService.GetDeploymentAppsV1(k8sClient, namespace, app)
	if err != nil || deploy == nil {
		errMsg := fmt.Sprintf("[%s] Failed to get deployment %s from ns  %s error %v", app, namespace, err)
		logger.Errorf(errMsg)
		return errorcode.NewStackError(errorcode.FailedOperationCode, errMsg, err)
	}

	annotations := deploy.Spec.Template.Annotations
	annotations["restartTime"] = fmt.Sprintf("%d", util.GetNowTimestamp())

	deploy.Spec.Template.Annotations = annotations
	_, err = k8sService.ApplyDeploymentAppsV1(k8sClient, deploy)
	if err != nil {
		errMsg := fmt.Sprintf("[%s] Failed to apply deployment %s from ns  %s error %v", app, namespace, err)
		logger.Errorf(errMsg)
		return errorcode.NewStackError(errorcode.FailedOperationCode, errMsg, err)
	}
	return nil
}

func (m *ModifySubnetService) valid(req *subnetModel.ModifySubnetReq) error {
	// 1. 参数基本校验
	if req.Region == "" {
		return errorcode.InvalidParameterValueCode.ReplaceDesc("Region can not be empty")
	}
	if string(req.AppId) == "" {
		return errorcode.InvalidParameterValueCode.ReplaceDesc("AppId can not be empty")
	}
	if req.Uin == "" {
		return errorcode.InvalidParameterValueCode.ReplaceDesc("Uin can not be empty")
	}
	if req.SubAccountUin == "" {
		return errorcode.InvalidParameterValueCode.ReplaceDesc("SubAccountUin can not be empty")
	}
	err := service2.CheckNameValidityV2(req.ClusterId, 50)
	if err != nil {
		return err
	}

	return nil
}
