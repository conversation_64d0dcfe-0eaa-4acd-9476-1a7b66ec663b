package variable

import (
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/variable"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource"

	"testing"
)

func init() {

}

func TestDoDescribeVariables(t *testing.T) {
	service2.InitTestDB(service2.WALLYDB)
	req := &model.DescribeVariablesReq{}
	req.AppId = 1251006373
	req.Region = "ap-shanghai"
	req.AppId = 1257058945
	req.Uin = "100006386216"
	value := make([]string, 0, 0)
	value = append(value, "_")
	filter := Filter{Name: "Name", Values: value}
	req.Filters = append(req.Filters, filter)

	DoDescribeVariables(req)
}

type Filter struct {
	Name   string
	Values []string
}
