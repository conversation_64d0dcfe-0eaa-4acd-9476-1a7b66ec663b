package async

import (
	"sync"
	"sync/atomic"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/async"
	"time"
)

// errors
var (
	// return if pool size <= 0
	ErrInvalidPoolCap = errorcode.NewStackError(errorcode.InternalErrorCode, "invalid pool cap", nil)
	// put task but pool already closed
	ErrPoolAlreadyClosed = errorcode.NewStackError(errorcode.InternalErrorCode, "pool already closed", nil)
)

// running status
const (
	RUNNING = 1
	STOPED  = 0
)

// Pool task pool
type Pool struct {
	capacity       uint64
	runningWorkers uint64
	status         int64
	chTask         chan *table.AsyncJobInfo
	PanicHandler   func(interface{})
	sync.Mutex
}

// NewPool init pool
func NewPool(capacity uint64) (*Pool, error) {
	if capacity <= 0 {
		return nil, ErrInvalidPoolCap
	}
	p := &Pool{
		capacity: capacity,
		status:   RUNNING,
		chTask:   make(chan *table.AsyncJobInfo, capacity),
	}

	return p, nil
}

func (p *Pool) checkWorker() {
	p.Lock()
	defer p.Unlock()

	if p.runningWorkers == 0 && len(p.chTask) > 0 {
		p.run()
	}
}

// GetCap get capacity
func (p *Pool) GetCap() uint64 {
	return p.capacity
}

// GetRunningWorkers get running workers
func (p *Pool) GetRunningWorkers() uint64 {
	return atomic.LoadUint64(&p.runningWorkers)
}

func (p *Pool) incRunning() {
	atomic.AddUint64(&p.runningWorkers, 1)
}

func (p *Pool) decRunning() {
	atomic.AddUint64(&p.runningWorkers, ^uint64(0))
}

// Put put a task to pool
func (p *Pool) Put(task *table.AsyncJobInfo) error {
	p.Lock()
	defer p.Unlock()
	if p.status == STOPED {
		return ErrPoolAlreadyClosed
	}

	// run worker
	if p.GetRunningWorkers() < p.GetCap() {
		p.run()
	}

	// send task
	if p.status == RUNNING {
		p.chTask <- task
	}

	return nil
}

func (p *Pool) run() {
	p.incRunning()

	go func() {
		defer func() {
			p.decRunning()
			if r := recover(); r != nil {
				if p.PanicHandler != nil {
					p.PanicHandler(r)
				} else {
					logger.Errorf("Worker panic: %s\n", r)
				}
			}
			p.checkWorker() // check worker avoid no worker running
		}()

		for {
			select {
			case task, ok := <-p.chTask:
				if !ok {
					return
				}
				handler, ok := HandlerMapping[task.Handler]
				if ok {
					handler.Init(task.Context)
					err := handler.Handler(task.Params, task)
					if err != nil {
						logger.Errorf("Handler return err %+v", err)
					}
				} else {
					logger.Errorf("unknown handler %s", task.Handler)
				}
			}
		}
	}()
}

func (p *Pool) setStatus(status int64) bool {
	p.Lock()
	defer p.Unlock()

	if p.status == status {
		return false
	}

	p.status = status

	return true
}

// Close close pool graceful
func (p *Pool) Close() {

	if !p.setStatus(STOPED) { // stop put task
		return
	}

	for len(p.chTask) > 0 { // wait all task be consumed
		time.Sleep(1e6) // reduce CPU load
	}

	close(p.chTask)
}
