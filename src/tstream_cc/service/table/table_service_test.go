package service

import (
	"encoding/json"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/log"
	"testing"
)

func TestTableService_ListCdbByClusterId(t *testing.T) {
	if cdbList, err := GetTableService().ListCdbByClusterId(*fTestClusterId); err != nil {
		t.Error(err)
	} else {
		b, _ := json.MarshalIndent(cdbList, "", " ")
		t.Logf("success, %s", string(b))
	}
}

func TestTableService_ListEmrByClusterId(t *testing.T) {
	if emrList, err := GetTableService().ListEmrByClusterId(*fTestClusterId); err != nil {
		t.Error(err)
	} else {
		b, _ := json.MarshalIndent(emrList, "", " ")
		t.Logf("success, %s", string(b))

		for _, emr := range emrList {
			if config, err := emr.GetConfig(); err != nil {
				t.Error(err)
			} else {
				b, _ := json.MarshalIndent(config, "", " ")
				t.Logf("%s", string(b))
			}
		}
	}
}

func TestTableService_ListCvmSaleConfByInstanceType(t *testing.T) {
	if cvmSaleConfList, err := GetTableService().ListCvmSaleConfByInstanceType(*fTestCvmInstanceType, 0, 100); err != nil {
		t.Error(err)
	} else {
		b, _ := json.MarshalIndent(cvmSaleConfList, "", " ")
		t.Logf("success, count %d, %s", len(cvmSaleConfList), string(b))
	}
}

func TestTableService_PutRunningLogRemark(t *testing.T) {
	if result, err := GetTableService().PutRunningLogRemark(*fTestUin, *fTestRegion, &log.ClusterResult{
		ClusterId:    *fTestClusterSerialId,
		Success:      true,
		ErrorMessage: "test",
	}); err != nil {
		t.Error(err)
	} else {
		b, _ := json.MarshalIndent(result, "", " ")
		t.Logf("success, %s", string(b))
	}
}

func TestTableService_DeleteRunningLogRemark(t *testing.T) {
	if result, err := GetTableService().DeleteRunningLogRemark(*fTestUin, *fTestRegion, &log.ClusterResult{
		ClusterId:    *fTestClusterSerialId,
		Success:      true,
		ErrorMessage: "test",
	}); err != nil {
		t.Error(err)
	} else {
		b, _ := json.MarshalIndent(result, "", " ")
		t.Logf("success, %s", string(b))
	}
}

func TestTableService_InClusterAutoCreateBlackRegionList(t *testing.T) {
	if yes, err := GetTableService().InClusterAutoCreateBlackRegionList(*fTestRegion); err != nil {
		t.Error(err)
	} else {
		t.Logf("success, %v", yes)
	}
}

func TestTableService_ListUserUseClsTopicId(t *testing.T) {
	rsp, err := GetTableService().ListUserUseClsTopicId(*fTestUin, *fTestRegion)
	if err != nil {
		t.Fatal(err)
	}
	b, _ := json.MarshalIndent(rsp, "", " ")
	t.Logf("success, %s", string(b))
}
