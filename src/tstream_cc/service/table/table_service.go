package service

import (
	"encoding/json"
	"fmt"
	"github.com/juju/errors"
	"io/ioutil"
	"os"
	"os/exec"
	"strconv"
	"strings"
	"syscall"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/log"
	table4 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/Cvm"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cdb"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	table3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/emr"
	table6 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job_instance"
	table5 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/log"
	table8 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/password"
	table9 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/resource"
	table7 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/tke"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config/common_config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config/configure_center"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/configuration_center"
)

var (
	tableService *TableService
)

type TableService struct {
}

func Fetch[T interface{}](sql string, args []interface{}) (result []*T, err error) {
	txManager := service.GetTxManager()
	result = make([]*T, 0)

	cnt, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to query from db, with errors:%+v", err)
		return nil, err
	}

	for i := 0; i < cnt; i++ {
		t := new(T)
		err = util.ScanMapIntoStruct(t, data[i])
		if err != nil {
			logger.Errorf("Failed to ScanMapIntoStruct, with errors:%+v", err)
			return nil, err
		}
		result = append(result, t)
	}

	return result, nil
}

func FetchRow[T interface{}](sql string, args []interface{}) (result *T, err error) {
	txManager := service.GetTxManager()

	cnt, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to query from db, with errors:%+v", err)
		return nil, err
	}
	if cnt <= 0 {
		return
	}

	result = new(T)
	err = util.ScanMapIntoStruct(result, data[0])
	if err != nil {
		logger.Errorf("Failed to ScanMapIntoStruct, with errors:%+v", err)
		return nil, err
	}
	return result, nil
}

func FetchString(sql string, args []interface{}) (result string, err error) {
	txManager := service.GetTxManager()

	cnt, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to query from db, with errors:%+v", err)
		return
	}
	if cnt <= 0 {
		return
	}
	for _, v := range data[0] {
		result = string(v)
		continue
	}
	return result, nil
}

func FetchStringSlice(sql string, args []interface{}) (result []string, err error) {
	txManager := service.GetTxManager()

	cnt, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to query from db, with errors:%+v", err)
		return
	}
	result = make([]string, 0)
	for i := 0; i < cnt; i++ {
		for _, v := range data[i] {
			result = append(result, string(v))
		}
	}
	return result, nil
}

func FetchInt(sql string, args []interface{}) (result int, err error) {
	resultStr, err := FetchString(sql, args)
	if err != nil || len(resultStr) == 0 {
		return
	}
	return strconv.Atoi(resultStr)
}

func FetchInt64(sql string, args []interface{}) (result int64, err error) {
	resultStr, err := FetchString(sql, args)
	if err != nil || len(resultStr) == 0 {
		return
	}
	return strconv.ParseInt(resultStr, 10, 64)
}

func FetchFloat64(sql string, args []interface{}) (result float64, err error) {
	resultStr, err := FetchString(sql, args)
	if err != nil || len(resultStr) == 0 {
		return
	}
	return strconv.ParseFloat(resultStr, 64)
}

func TFetch[T interface{}](t *dao.Transaction, sql string, args []interface{}) (result []*T, err error) {
	result = make([]*T, 0)

	cnt, data, err := t.Query(sql, args)
	if err != nil {
		logger.Errorf("Failed to query from db, with errors:%+v", err)
		return nil, err
	}

	for i := 0; i < cnt; i++ {
		t := new(T)
		err = util.ScanMapIntoStruct(t, data[i])
		if err != nil {
			logger.Errorf("Failed to ScanMapIntoStruct, with errors:%+v", err)
			return nil, err
		}
		result = append(result, t)
	}

	return result, nil
}

func TFetchRow[T interface{}](t *dao.Transaction, sql string, args []interface{}) (result *T, err error) {
	cnt, data, err := t.Query(sql, args)
	if err != nil {
		logger.Errorf("Failed to query from db, with errors:%+v", err)
		return nil, err
	}
	if cnt <= 0 {
		return
	}

	result = new(T)
	err = util.ScanMapIntoStruct(result, data[0])
	if err != nil {
		logger.Errorf("Failed to ScanMapIntoStruct, with errors:%+v", err)
		return nil, err
	}
	return result, nil
}

func TFetchString(t *dao.Transaction, sql string, args []interface{}) (result string, err error) {
	cnt, data, err := t.Query(sql, args)
	if err != nil {
		logger.Errorf("Failed to query from db, with errors:%+v", err)
		return
	}
	if cnt <= 0 {
		return
	}
	for _, v := range data[0] {
		result = string(v)
		continue
	}
	return result, nil
}

func TFetchInt(t *dao.Transaction, sql string, args []interface{}) (result int, err error) {
	resultStr, err := TFetchString(t, sql, args)
	if err != nil || len(resultStr) == 0 {
		return
	}
	return strconv.Atoi(resultStr)
}

func TFetchInt64(t *dao.Transaction, sql string, args []interface{}) (result int64, err error) {
	resultStr, err := TFetchString(t, sql, args)
	if err != nil || len(resultStr) == 0 {
		return
	}
	return strconv.ParseInt(resultStr, 10, 64)
}

func TFetchFloat64(t *dao.Transaction, sql string, args []interface{}) (result float64, err error) {
	resultStr, err := TFetchString(t, sql, args)
	if err != nil || len(resultStr) == 0 {
		return
	}
	return strconv.ParseFloat(resultStr, 64)
}

func TExecute(t *dao.Transaction, sql string, args []interface{}) int64 {
	result := t.ExecuteSql(sql, args)

	rowAffected, err := result.RowsAffected()
	if err != nil {
		logger.Errorf("Transaction->UpdateObject->RowsAffected sql: %s, args:%+v, errs: %+v", sql, args, err)
		panic(errors.Trace(err))
	}

	return rowAffected
}

func (this TableService) ListClusterGroupById(id int64) (clusterGroup *table.ClusterGroup, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("ListClusterGroupById error"))

	cnt, data, err := service.GetTxManager().GetQueryTemplate().DoQueryWithArgs("select * from ClusterGroup where id = ?", id)
	if err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	if cnt != 1 {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("ClusterGroup %d, cnt %d", id, cnt), nil)
	}

	clusterGroup = &table.ClusterGroup{}
	err = util.ScanMapIntoStruct(clusterGroup, data[0])
	if err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	return clusterGroup, nil
}

func (o *TableService) ListclusterGroupsBySqlWithArgs(sql string, args ...interface{}) (clusterGroups []*table.ClusterGroup, err error) {
	txManager := service.GetTxManager()
	clusterGroups = make([]*table.ClusterGroup, 0, 0)

	cnt, data, err := txManager.GetQueryTemplate().DoQueryWithArgs(sql, args...)
	if err != nil {
		logger.Errorf("Failed to query ClusterGroups from db, with errors:%+v", err)
		return nil, err
	}

	for i := 0; i < cnt; i++ {
		clusterGroup := &table.ClusterGroup{}
		err = util.ScanMapIntoStruct(clusterGroup, data[i])
		if err != nil {
			logger.Errorf("Failed to get ScanMapIntoStruct, with errors:%+v", err)
			return nil, err
		}
		clusterGroups = append(clusterGroups, clusterGroup)
	}

	return clusterGroups, nil
}

func (this TableService) ListClusterById(id int64) (cluster *table.Cluster, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("ListClusterById error"))

	cnt, data, err := service.GetTxManager().GetQueryTemplate().DoQueryWithArgs("select * from Cluster where id = ?", id)
	if err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, err.Error(), err)
	}
	if cnt != 1 {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("Cluster %d, cnt %d", id, cnt), nil)
	}

	cluster = &table.Cluster{}
	err = util.ScanMapIntoStruct(cluster, data[0])
	if err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	return cluster, nil
}

func (o *TableService) ListClustersBySqlWithArgs(sql string, args ...interface{}) (
	clusters []*table.Cluster, err error) {
	txManager := service.GetTxManager()
	clusters = make([]*table.Cluster, 0, 0)

	cnt, data, err := txManager.GetQueryTemplate().DoQueryWithArgs(sql, args...)
	if err != nil {
		logger.Errorf("Failed to query Clusters from db, with errors:%+v", err)
		return nil, err
	}

	for i := 0; i < cnt; i++ {
		cluster := &table.Cluster{}
		err = util.ScanMapIntoStruct(cluster, data[i])
		if err != nil {
			logger.Errorf("Failed to get ScanMapIntoStruct, with errors:%+v", err)
			return nil, err
		}
		clusters = append(clusters, cluster)
	}

	return clusters, nil
}

func (this *TableService) ListTaskFlowStatus(flowId, processKey, taskCode string) (flowStatus *flow.FlowStatus, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("ListTaskFlowStatus error"))

	iFlowId, err := strconv.ParseInt(flowId, 10, 64)
	cnt, data, err := service.GetTxManager().GetQueryTemplate().DoQueryWithArgs("select * from taskflow_status where flowId=? and processKey=? and taskCode=?", iFlowId, processKey, taskCode)
	if err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	if cnt == 0 {
		return nil, nil
	} else if cnt != 1 {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode,
			fmt.Sprintf("taskflow_status(%d, %s, %s)=%d", iFlowId, processKey, taskCode, cnt), nil)
	}
	flowStatus = &flow.FlowStatus{}
	if err := util.ScanMapIntoStruct(flowStatus, data[0]); err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	} else {
		return flowStatus, nil
	}
}

func (this *TableService) ListCdbWithEncodeByClusterId(clusterId int64) (cdbList []*table2.Cdb, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("ListCdbWithEncodeByClusterId error"))

	if cnt, data, err := service.GetTxManager().GetQueryTemplate().DoQueryWithArgs("select * from Cdb where ClusterId=?", clusterId); err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	} else if cnt == 0 {
		return []*table2.Cdb{}, nil
	} else {
		cdbList = make([]*table2.Cdb, 0, cnt)
		for i := 0; i < cnt; i++ {
			cdb := &table2.Cdb{}
			if err := util.ScanMapIntoStruct(cdb, data[i]); err != nil {
				return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
			}
			cdbList = append(cdbList, cdb)
		}
		return cdbList, nil
	}
}

func (this *TableService) ListCdbByClusterId(clusterId int64) (cdbList []*table2.Cdb, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("ListCdbByClusterId error"))

	if cnt, data, err := service.GetTxManager().GetQueryTemplate().DoQueryWithArgs("select * from Cdb where ClusterId=?", clusterId); err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	} else if cnt == 0 {
		return []*table2.Cdb{}, nil
	} else {
		cdbList = make([]*table2.Cdb, 0, cnt)
		for i := 0; i < cnt; i++ {
			cdb := &table2.Cdb{}
			if err := util.ScanMapIntoStruct(cdb, data[i]); err != nil {
				return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
			}
			if len(cdb.Password) > 0 {
				rainbowOceanusPasswordEncodeKey, err := common_config.GetRainbowOceanusPasswordEncodeKey()
				if err != nil {
					logger.Errorf("Failed to get oceanus cluster password encode key in Rainbow")
					return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
				}
				if password, err := util.DecodePassword(cdb.Password, rainbowOceanusPasswordEncodeKey); err != nil {
					return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
				} else {
					cdb.Password = password
				}
			}

			cdbList = append(cdbList, cdb)
		}
		return cdbList, nil
	}
}

func (this *TableService) ListEmrByClusterId(clusterId int64) (emrList []*table3.Emr, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("ListCdbByClusterId error"))

	if cnt, data, err := service.GetTxManager().GetQueryTemplate().DoQueryWithArgs("select * from Emr where ClusterId=?", clusterId); err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	} else if cnt == 0 {
		return []*table3.Emr{}, nil
	} else {
		emrList = make([]*table3.Emr, 0, cnt)
		for i := 0; i < cnt; i++ {
			emr := &table3.Emr{}
			if err := util.ScanMapIntoStruct(emr, data[i]); err != nil {
				return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
			}
			emrList = append(emrList, emr)
		}
		return emrList, nil
	}
}

func (this *TableService) ListTkeByClusterId(clusterId int64) (tkeList []*table7.Tke, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("ListTkeByClusterId error"))

	if cnt, data, err := service.GetTxManager().GetQueryTemplate().DoQueryWithArgs("select * from Tke where ClusterId=?", clusterId); err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	} else if cnt == 0 {
		return []*table7.Tke{}, nil
	} else {
		tkeList = make([]*table7.Tke, 0, cnt)
		for i := 0; i < cnt; i++ {
			tke := &table7.Tke{}
			if err := util.ScanMapIntoStruct(tke, data[i]); err != nil {
				return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
			}
			tkeList = append(tkeList, tke)
		}
		return tkeList, nil
	}
}

func (o *TableService) GetTkeClusterType(clusterId int64) (clusterType int, err error) {
	if tkeList, err := o.ListTkeByClusterId(clusterId); err != nil {
		return 0, err
	} else if len(tkeList) != 1 {
		return 0, errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("found tke %d intance for clusterId %d", len(tkeList), clusterId), nil)
	} else {
		return tkeList[0].ClusterType, nil
	}
}

func (o *TableService) GetTkeArchGeneration(clusterId int64) (archGeneration int, err error) {
	if tkeList, err := o.ListTkeByClusterId(clusterId); err != nil {
		return 0, err
	} else if len(tkeList) != 1 {
		return 0, errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("found tke %d intance for clusterId %d", len(tkeList), clusterId), nil)
	} else {
		return tkeList[0].ArchGeneration, nil
	}
}

/*
*
批量运行 SQL 代码
如果遇到任何报错, 则抛出 panic, 请注意捕获
*/
func (this TableService) MustRunSqlInBatch(template string, user string, password string, ip string, port int, requestId string) {

	// 1. 将 Template 写到临时目录
	tempSqlFile, err := ioutil.TempFile("", "controller-template-*.sql")
	if err != nil {
		logger.Errorf("[%s] Failed to create SQL template in system tmp folder because %+v", requestId, err)
		panic(errorcode.NewStackError(errorcode.InternalErrorCode, "", err))
	}
	defer os.Remove(tempSqlFile.Name())
	defer tempSqlFile.Close()

	count, err := tempSqlFile.WriteString(template)
	logger.Infof("[%s] Written %d characters in SQL file", requestId, count)

	// 2. 调用 mysql 命令导入 (请确保部署机器上有合适版本的 mysql 命令)
	cmd := exec.Command(
		"mysql", "-u", user, "-p"+password, "--host", ip, "--port", strconv.Itoa(port), "-e", "source "+tempSqlFile.Name(),
	)
	if err := cmd.Start(); err != nil {
		logger.Errorf("[%s] Failed to run `mysql` command because %+v", requestId, err)
		panic(errorcode.NewStackError(errorcode.InternalErrorCode, "", err))
	}

	// 3. 检验是否成功
	if err := cmd.Wait(); err != nil {
		if exitError, ok := err.(*exec.ExitError); ok {
			if status, ok := exitError.Sys().(syscall.WaitStatus); ok {
				logger.Errorf("[%s] Non-zero exit code: %d", requestId, status.ExitStatus())
				panic(errorcode.NewStackError(errorcode.InternalErrorCode, "non-zero exit code", err))
			}
		} else {
			logger.Errorf("[%s] Failed to wait for `mysql` command to run because %+v", requestId, err)
			panic(errorcode.NewStackError(errorcode.InternalErrorCode, "", err))
		}
	}
	logger.Infof("[%s] Successfully run `mysql` command to create database structures.", requestId)
}

func (this *TableService) ListCvmSaleConfByInstanceType(instanceType string, offset, limit int) (
	cvmSaleConfList []*table4.CvmSaleConfig, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("ListCvmSaleConfByInstanceType error"))

	cnt, data, err := service.GetTxManager().GetQueryTemplate().
		DoQueryWithArgs("select * from CvmSaleConf where InstanceType=? limit ?,?", instanceType, offset, limit)
	if err != nil {
		return nil, err
	}
	if cnt == 0 {
		return []*table4.CvmSaleConfig{}, nil
	}

	cvmSaleConfList = make([]*table4.CvmSaleConfig, 0, cnt)
	for i := 0; i < cnt; i++ {
		conf := &table4.CvmSaleConfig{}
		if err := util.ScanMapIntoStruct(conf, data[i]); err != nil {
			return nil, errorcode.InternalErrorCode.NewWithErr(err)
		}
		cvmSaleConfList = append(cvmSaleConfList, conf)
	}
	return cvmSaleConfList, nil
}

func (o *TableService) ListCvmSaleConf(region, zone, instanceType string, cpu, memory int64,
	status, generation, offset, limit int) (cvmSaleConfList []*table4.CvmSaleConfig, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("ListCvmSaleConf error"))

	cond := dao.NewCondition()
	if len(region) > 0 {
		cond.Eq("Region", region)
	}
	if len(zone) > 0 {
		cond.Eq("Zone", zone)
	}
	if len(instanceType) > 0 {
		cond.Eq("InstanceType", instanceType)
	}
	if cpu > 0 {
		cond.Eq("Cpu", cpu)
	}
	if memory > 0 {
		cond.Eq("Memory", memory)
	}
	if status >= 0 {
		cond.Eq("Status", status)
	}
	if generation > 0 {
		cond.Eq("Generation", generation)
	}
	where, args := cond.GetWhere()

	sql := "select * from CvmSaleConf" + where
	if offset >= 0 && limit > 0 {
		sql += " limit ?,?"
		args = append(args, offset, limit)
	}

	cnt, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return nil, err
	}
	cvmSaleConfList = make([]*table4.CvmSaleConfig, 0, cnt)
	for i := 0; i < cnt; i++ {
		conf := &table4.CvmSaleConfig{}
		if err := util.ScanMapIntoStruct(conf, data[i]); err != nil {
			return nil, errorcode.InternalErrorCode.NewWithErr(err)
		}
		cvmSaleConfList = append(cvmSaleConfList, conf)
	}
	return cvmSaleConfList, nil
}

func (o *TableService) ListActiveCvmSaleConf() (cvmSaleConfList []*table4.CvmSaleConfig, err error) {
	return o.ListCvmSaleConf("", "", "", 0, 0,
		constants.CvmSaleConfActive, 0, 0, -1)
}

func (o *TableService) ListActiveCvmSaleConfWithCpuMemory(cpu, memory int64) (
	cvmSaleConfList []*table4.CvmSaleConfig, err error) {
	return o.ListCvmSaleConf("", "", "", cpu, memory,
		constants.CvmSaleConfActive, 0, 0, -1)
}

func (o *TableService) ListActiveCvmInstanceType() (instanceTypes []string, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("ListActiveCvmInstanceType error"))

	cnt, data, err := service.GetTxManager().GetQueryTemplate().
		DoQueryWithArgs("select distinct(InstanceType) as InstanceType from CvmSaleConf where Status=?",
			constants.CvmSaleConfActive)
	if err != nil {
		return nil, err
	}
	instanceTypes = make([]string, 0, cnt)
	for i := 0; i < cnt; i++ {
		instanceTypes = append(instanceTypes, string(data[i]["InstanceType"]))
	}
	return
}

func (o *TableService) GetSubmissionLogSetAndTopic(region string) (logSetId, logTopicId string, err error) {
	txManager := service.GetTxManager()
	if txManager == nil {
		logger.Errorf("TxManager is nil")
		return "", "", errorcode.NewStackError(errorcode.InternalErrorCode, "TxManager is nil", nil)
	}
	sql := "SELECT * FROM JobSubmissionLogTopic Where Region=? AND Status = 1"
	args := make([]interface{}, 0)
	args = append(args, region)
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to query submission log topic for region: %+v, with errors: %+v", region, err)
		return "", "", err
	}

	if len(data) == 0 {
		errMsg := fmt.Sprintf("getSubmissionLogTopic logic error, no topicId is found for region: %+v", region)
		logger.Error(errMsg)
		return "", "", errorcode.NewStackError(errorcode.InternalErrorCode, errMsg, nil)
	}

	if len(data) > 1 {
		errMsg := fmt.Sprintf("getSubmissionLogTopic logic error, find more than one logTopic with region:%+v", region)
		logger.Error(errMsg)
		return "", "", errorcode.NewStackError(errorcode.InternalErrorCode, errMsg, nil)
	}
	submissionLogTopic := table5.JobSubmissionLogTopic{}
	err = util.ScanMapIntoStruct(&submissionLogTopic, data[0])

	return submissionLogTopic.LogSetId, submissionLogTopic.LogTopicId, nil
}

func (o *TableService) GetJobInstancesByCreateTimeRange(jobId int64, startTime, endTime string) ([]*table6.JobInstance, error) {
	sql := "SELECT * FROM JobInstance WHERE jobId=? AND CreateTime BETWEEN ? AND ? ORDER BY RunningOrderId DESC"
	args := make([]interface{}, 0)
	args = append(args, jobId, startTime, endTime)
	txManager := service.GetTxManager()
	if txManager == nil {
		errMsg := fmt.Sprintf("Failed to get txManager")
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, errMsg, nil)
	}
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to get job instances, with errors:%+v", err)
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, errMsg, nil)
	}
	jobInstanceList := make([]*table6.JobInstance, 0)
	for i := 0; i < len(data); i++ {
		jobInstance := &table6.JobInstance{}
		err := util.ScanMapIntoStruct(jobInstance, data[i])
		if err != nil {
			errMsg := fmt.Sprintf("Failed to transfer job instance into struct, with errors:%+v", err)
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, errMsg, nil)
		}
		jobInstanceList = append(jobInstanceList, jobInstance)
	}
	return jobInstanceList, nil
}

// 注意没有运行日志的配置也返回成功，调用方需要判断
func (o *TableService) GetJobRunningLogTopic(ownerUin, region string) (*table5.JobRunningLogTopic, error) {
	cnt, data, err := service.GetTxManager().GetQueryTemplate().DoQueryWithArgs(
		"SELECT * FROM JobRunningLogTopic WHERE Status=1 AND OwnerUin=? AND Region=?", ownerUin, region)
	if err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	if cnt == 0 {
		return nil, nil
	}

	if cnt > 1 {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("%d logTopics are enabled for uin %s region %s", cnt, ownerUin, region), nil)
	}

	inst := &table5.JobRunningLogTopic{}
	if err = util.ScanMapIntoStruct(inst, data[0]); err != nil {
		return nil, err
	}

	return inst, nil
}

func (o *TableService) GetTkeClusterKubeConfig(clusterId string) ([]byte, error) {
	cnt, data, err := service.GetTxManager().GetQueryTemplate().DoQueryWithArgs(
		"SELECT * FROM Cluster WHERE UniqClusterId=?", clusterId)
	if err != nil {
		return nil, err
	}
	if cnt != 1 {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("%d KubeConfig for Cluster %s", cnt, clusterId), nil)
	}

	return data[0]["KubeConfig"], nil
}

func (o *TableService) GetJobRunningLogConf() (*log.JobRunningLogConfListener, error) {
	value, err := service2.GetConfigurationValueByKey("JobRunningLogConf_new")
	if err != nil {
		return nil, err
	}

	conf := &log.JobRunningLogConfListener{}
	if err := json.Unmarshal([]byte(value), conf); err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}
	logger.Infof("JobRunningLogConf_new: %v", conf)
	return conf, nil
}

func (o *TableService) GetJobRunningLogEsConf() (*log.JobRunningLogConfListener, error) {
	value, err := service2.GetConfigurationValueByKey("JobRunningLogConf_es")
	if err != nil {
		return nil, err
	}

	conf := &log.JobRunningLogConfListener{}
	if err := json.Unmarshal([]byte(value), conf); err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}
	logger.Infof("JobRunningLogConf_es: %v", conf)
	return conf, nil
}

// 在获取集群事件时也用到了该防返回的账号信息，修改请谨慎
func (o *TableService) GetJobSubmissionLogConf() (*log.JobSubmissionLogConf, error) {
	value, err := service2.GetConfigurationValueByKey("JobSubmissionLogConf")
	if err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	conf := &log.JobSubmissionLogConf{}
	if err := json.Unmarshal([]byte(value), conf); err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	return conf, nil
}

func (o *TableService) PutRunningLogRemark(uin, region string, result *log.ClusterResult) (remark *log.ModifyRunningLogShipperRsp, err error) {
	return o.modifyRunningLogRemark(uin, region, result, false)
}

func (o *TableService) DeleteRunningLogRemark(uin, region string, result *log.ClusterResult) (remark *log.ModifyRunningLogShipperRsp, err error) {
	return o.modifyRunningLogRemark(uin, region, result, true)
}

func (o *TableService) modifyRunningLogRemark(uin, region string, result *log.ClusterResult, del bool) (remark *log.ModifyRunningLogShipperRsp, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("modifyRunningLogRemark error"))

	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		topic, err := o.GetJobRunningLogTopic(uin, region)
		if err != nil {
			return err
		}
		if topic == nil {
			return errorcode.NewStackError(errorcode.InternalErrorCode, "InstanceNotFound", nil)
		}

		remark = &log.ModifyRunningLogShipperRsp{}
		if len(topic.Remark) > 0 {
			if err := json.Unmarshal([]byte(topic.Remark), remark); err != nil {
				return errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
			}
		}
		uniqMap := make(map[string]*log.ClusterResult, len(remark.ClusterResults))
		if len(remark.ClusterResults) > 0 {
			for _, c := range remark.ClusterResults {
				uniqMap[c.ClusterId] = c
			}
		}
		if del {
			delete(uniqMap, result.ClusterId)
		} else {
			uniqMap[result.ClusterId] = result
		}

		remark.ClusterResults = make([]*log.ClusterResult, 0, len(uniqMap))
		for _, c := range uniqMap {
			remark.ClusterResults = append(remark.ClusterResults, c)
		}
		b, _ := json.Marshal(remark)

		tx.ExecuteSqlWithArgs("update JobRunningLogTopic set Remark=? where id=?", string(b), topic.Id)

		return nil
	}).Close()

	return
}

func (o *TableService) InClusterAutoCreateBlackRegionList(region string) (yes bool, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("InClusterAutoCreateBlackRegionList error"))

	list, err := configure_center.CC(region).ClusterAutoCreateBlackList()
	if err != nil {
		return false, err
	}
	for _, r := range strings.Split(list, ",") {
		if r == region {
			return true, nil
		}
	}
	return false, nil
}

func (o *TableService) ListFlinkUiAuthInfo(clusterGroupId int64) (authInfo *table8.FlinkUiAuthInfo, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("ListFlinkUiAuthInfo error"))

	if cnt, data, err := service.GetTxManager().GetQueryTemplate().DoQueryWithArgs(
		"select * from FlinkUiAuthInfo where ClusterGroupId=?", clusterGroupId); err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	} else if cnt == 0 {
		return nil, nil
	} else if cnt > 1 {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("clustergroup %d has %d flink ui auth info", clusterGroupId, cnt), nil)
	} else {
		authInfo = &table8.FlinkUiAuthInfo{}
		if err := util.ScanMapIntoStruct(authInfo, data[0]); err != nil {
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
		return authInfo, nil
	}
}

func (o *TableService) CheckClusterGroupExist(appId int64, serialId string) (err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("CheckClusterGroupExist error"))

	args := make([]interface{}, 0)
	args = append(args, serialId)
	args = append(args, appId)
	sql := "SELECT * FROM ClusterGroup WHERE SerialId=? and AppId=?"
	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to query Clusters from db, with errors:%+v", err)
		return errorcode.NewStackError(errorcode.InternalErrorCode, "cannot get cluster from DB", err)
	}
	if len(data) != 1 {
		err = fmt.Errorf("CheckClusterGroupExist error, active cluster size is NOT 1 but len(%d)", len(data))
		return errorcode.NewStackError(errorcode.ResourceNotFoundCode, "cluster size is not 1", err)
	}
	return nil
}

func (o *TableService) ListUserUseClsTopicId(uin, region string) (topicIdSet map[string]struct{}, err error) {
	topicIdSet = make(map[string]struct{})
	sql := "select CLSTopicId from Cluster where RoleType = ? and ClusterGroupId in (" +
		"select Id from ClusterGroup where OwnerUin = ? and Region = ? and Status != ? and Status != ?)"
	_, data, err := service.GetTxManager().GetQueryTemplate().DoQueryWithArgs(sql,
		constants.CLUSTER_ROLE_TYPE_ACTIVE, uin, region,
		constants.CLUSTER_GROUP_STATUS_DELETED, constants.CLUSTER_GROUP_STATUS_DELETING)

	if err != nil {
		return nil, err
	}
	for _, d := range data {
		topicIdSet[string(d["CLSTopicId"])] = struct{}{}
	}

	// TODO 看后面 GetJobRunningLogTopic 怎么修改
	// 2.12 之前的集群，日志集信息是存储在 JobRunningLogTopic, 每个uin， 一个地域下的集群，共用一个topicId
	cnt, data, err := service.GetTxManager().GetQueryTemplate().DoQueryWithArgs(
		"SELECT * FROM JobRunningLogTopic WHERE Status=1 AND OwnerUin=? AND Region=?", uin, region)
	if err != nil {
		return nil, errorcode.InternalErrorCode.New()
	}
	if cnt == 0 {
		return topicIdSet, nil
	}

	if cnt > 1 {
		return nil, errorcode.InternalErrorCode.New()
	}

	inst := &table5.JobRunningLogTopic{}
	if err = util.ScanMapIntoStruct(inst, data[0]); err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}

	topicIdSet[inst.LogTopicId] = struct{}{}
	delete(topicIdSet, "")
	return topicIdSet, nil
}

func (o *TableService) GetLogConfigCrdConf() (conf *log.JobRunningLogConfCrd, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("GetLogConfigCrdConf error"))

	value, err := service2.GetConfigurationValueByKey("JobRunningLogConf_new")
	if err != nil {
		return nil, err
	}

	conf = &log.JobRunningLogConfCrd{}
	if err := json.Unmarshal([]byte(value), conf); err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}
	return conf, nil
}

func (o *TableService) GetK8sEventTopicByRegion(region string) (eventTopic *table5.JobK8sEventTopic, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("GetK8sEventTopicByRegion error"))

	cnt, data, err := service.GetTxManager().GetQueryTemplate().DoQueryWithArgs(
		"SELECT * FROM JobK8sEventTopic WHERE Status=1 AND Region=?", region)
	if err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	if cnt == 0 || cnt > 1 {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("%d kubernetes event logTopic is not enabled for region %s", cnt, region), nil)
	}

	inst := &table5.JobK8sEventTopic{}
	if err = util.ScanMapIntoStruct(inst, data[0]); err != nil {
		return nil, err
	}

	return inst, nil
}

func (o *TableService) ListSystemConnectors(flinkVersion string) (connectors []*table9.SystemConnector, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("ListSystemConnectors error"))
	connectors = make([]*table9.SystemConnector, 0)

	sql := "SELECT * FROM SystemConnector"
	condition := dao.NewCondition()
	condition.Ne("Status", constants.RESOURCE_STATUS_DELETE)
	if flinkVersion != "" {
		condition.Eq("FlinkVersion", flinkVersion)
	} else {
		condition.Ne("FlinkVersion", constants.FlinkVersion)
		condition.Ne("FlinkVersion", constants.Flinkversion118)
	}

	where, args := condition.GetWhere()
	sql = sql + where

	_, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}
	for i := 0; i < len(data); i++ {
		systemConnector := &table9.SystemConnector{}
		err = util.ScanMapIntoStruct(systemConnector, data[i])
		if err != nil {
			return nil, errorcode.InternalErrorCode.NewWithErr(err)
		}
		connectors = append(connectors, systemConnector)
	}
	return connectors, nil
}

func NewTableService() *TableService {
	return &TableService{}
}

func GetTableService() *TableService {
	if tableService == nil {
		tableService = NewTableService()
	}

	return tableService
}
