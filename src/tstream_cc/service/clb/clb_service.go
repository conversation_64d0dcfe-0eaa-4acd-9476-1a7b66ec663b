package clb

import (
	"errors"
	"fmt"

	clb "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/profile"
	converter "tencentcloud.com/tstream_galileo/src/common/errcode_converter"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/clb"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config/configure_center"
)

type Target struct {
	Region         string
	LoadBalancerId string
	ListenerId     string
	Ip             string
	Domain         string
	Url            string
}

func RegisterFlinkUiTarget(target *Target) (code int, retmsg string, err error) {
	secretId, secretKey, err := service.GetSecretIdAndKeyOfScs()
	if err != nil {
		errMsg := fmt.Sprintf("RegisterTargets: GetSecretIdAndKeyOfScs err: %v", err)
		return model.CLB_API_NORMAL_ERROR, "", converter.GenSecCommonErrorCode(converter.CLB_ERROR, converter.ERROR_CODE_58000, converter.INTERNAL_ERROR_MESSAGE, errors.New(errMsg))
	}
	credential := common.NewCredential(
		secretId,
		secretKey,
	)
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = model.CLB_ENDPOINT
	client, _ := clb.NewClient(credential, target.Region, cpf)

	request := clb.NewRegisterTargetsRequest()
	request.LoadBalancerId = common.StringPtr(target.LoadBalancerId)
	request.ListenerId = common.StringPtr(target.ListenerId)
	domain := configure_center.CC(target.Region).GetFlinkUiDomain()
	if domain == "" {
		return model.CLB_API_NORMAL_ERROR, "", errors.New("domain is empty")
	}
	request.Domain = common.StringPtr(target.Domain)
	request.Url = common.StringPtr(target.Url)
	request.Targets = []*clb.Target{
		{
			EniIp: common.StringPtr(target.Ip),
			Port:  common.Int64Ptr(80),
		},
	}

	logger.Infof("RegisterTargets request: %s", request.ToJsonString())
	response, err := client.RegisterTargets(request)
	if err != nil {
		errMsg := fmt.Sprintf("RegisterTargets err: %v", err)
		return model.CLB_API_NORMAL_ERROR, "", converter.GenCommonErrorCode(converter.INTERNAL_ERROR_MESSAGE, errors.New(errMsg))
	}
	logger.Infof("RegisterTargets response: %s", response.ToJsonString())
	return 0, "success", nil
}

func DeregisterFlinkUiTarget(target *Target) (code int, retmsg string, err error) {
	secretId, secretKey, err := service.GetSecretIdAndKeyOfScs()
	if err != nil {
		errMsg := fmt.Sprintf("DeregisterFlinkUiTarget: GetSecretIdAndKeyOfScs err: %v", err)
		return model.CLB_API_NORMAL_ERROR, "", converter.GenSecCommonErrorCode(converter.CLB_ERROR, converter.ERROR_CODE_58001, converter.INTERNAL_ERROR_MESSAGE, errors.New(errMsg))
	}
	credential := common.NewCredential(
		secretId,
		secretKey,
	)
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = model.CLB_ENDPOINT
	client, _ := clb.NewClient(credential, target.Region, cpf)

	// 实例化一个请求对象,每个接口都会对应一个request对象
	request := clb.NewDeregisterTargetsRequest()
	request.LoadBalancerId = common.StringPtr(target.LoadBalancerId)
	request.ListenerId = common.StringPtr(target.ListenerId)
	request.Domain = common.StringPtr(target.Domain)
	request.Targets = []*clb.Target{
		{
			EniIp: common.StringPtr(target.Ip),
			Port:  common.Int64Ptr(80),
		},
	}
	logger.Infof("DeregisterFlinkUiTarget request: %s", request.ToJsonString())
	// 返回的resp是一个DeregisterTargetsResponse的实例，与请求对象对应
	response, err := client.DeregisterTargets(request)
	if err != nil {
		errMsg := fmt.Sprintf("DeregisterFlinkUiTarget err: %v", err)
		return model.CLB_API_NORMAL_ERROR, "", converter.GenCommonErrorCode(converter.INTERNAL_ERROR_MESSAGE, errors.New(errMsg))
	}
	logger.Infof("DeregisterFlinkUiTarget response: %s", response.ToJsonString())
	return 0, "success", nil
}

func DescribeTargetHealth(loadBalancerIds []string, region string) (loadBalancers []*clb.LoadBalancerHealth, retmsg string, err error) {
	secretId, secretKey, err := service.GetSecretIdAndKeyOfScs()
	if err != nil {
		errMsg := fmt.Sprintf("DescribeTargetHealth: GetSecretIdAndKeyOfScs err: %v", err)
		return nil, "", converter.GenSecCommonErrorCode(converter.CLB_ERROR, converter.ERROR_CODE_58002, converter.INTERNAL_ERROR_MESSAGE, errors.New(errMsg))
	}
	credential := common.NewCredential(
		secretId,
		secretKey,
	)
	// 实例化一个client选项，可选的，没有特殊需求可以跳过
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = model.CLB_ENDPOINT
	// 实例化要请求产品的client对象,clientProfile是可选的
	client, _ := clb.NewClient(credential, region, cpf)

	// 实例化一个请求对象,每个接口都会对应一个request对象
	request := clb.NewDescribeTargetHealthRequest()

	request.LoadBalancerIds = common.StringPtrs(loadBalancerIds)

	logger.Infof("DescribeTargetHealth request: %s", request.ToJsonString())
	// 返回的resp是一个DeregisterTargetsResponse的实例，与请求对象对应
	response, err := client.DescribeTargetHealth(request)
	if err != nil {
		errMsg := fmt.Sprintf("DescribeTargetHealth err: %v", err)
		return nil, "", converter.GenCommonErrorCode(converter.INTERNAL_ERROR_MESSAGE, errors.New(errMsg))
	}
	logger.Infof("DescribeTargetHealth response: %s", response.ToJsonString())
	return response.Response.LoadBalancers, "success", nil
}

// CheckTargetExist 判断节点是否存在
func CheckTargetExist(loadBalancerId string, ip string, region string) (bool, error) {
	logger.Infof("CheckTargetExist: loadBalancerId: %s, ip: %s", loadBalancerId, ip)
	loadBalancers, _, err := DescribeTargetHealth([]string{loadBalancerId}, region)
	if err != nil {
		return false, err
	}
	if len(loadBalancers) == 0 {
		return false, nil
	}
	// 遍历LoadBalancers
	for _, lb := range loadBalancers {
		// 遍历Listeners
		for _, listener := range lb.Listeners {
			if *listener.Port != 443 {
				continue
			}
			// 遍历Rules
			for _, rule := range listener.Rules {
				// 遍历Targets
				for _, target := range rule.Targets {
					// 判断IP和Port是否为"*********"和80，并获取HealthStatus
					if *target.IP == ip && *target.Port == 80 {
						return true, nil
					}
				}
			}
		}
	}
	return false, nil
}

// CheckTargetHealth 检查目标的健康状态
func CheckTargetHealth(loadBalancerId string, ip string, region string) (bool, error) {
	loadBalancers, _, err := DescribeTargetHealth([]string{loadBalancerId}, region)
	if err != nil {
		return false, err
	}
	if len(loadBalancers) == 0 {
		return false, nil
	}
	// 遍历LoadBalancers
	for _, lb := range loadBalancers {
		// 遍历Listeners
		for _, listener := range lb.Listeners {
			if *listener.Port != 443 {
				continue
			}
			// 遍历Rules
			for _, rule := range listener.Rules {
				// 遍历Targets
				for _, target := range rule.Targets {
					// 判断IP和Port是否匹配，并检查健康状态
					if *target.IP == ip && *target.Port == 80 {
						// 如果HealthStatus为true，表示健康
						return *target.HealthStatus, nil
					}
				}
			}
		}
	}
	// 如果没有找到目标，则认为不健康
	return false, nil
}

func CreateRule(target *Target) (code int, retmsg string, err error) {
	secretId, secretKey, err := service.GetSecretIdAndKeyOfScs()
	if err != nil {
		errMsg := fmt.Sprintf("CreateRule: GetSecretIdAndKeyOfScs err: %v", err)
		return model.CLB_API_NORMAL_ERROR, "", converter.GenSecCommonErrorCode(converter.CLB_ERROR, converter.ERROR_CODE_58000, converter.INTERNAL_ERROR_MESSAGE, errors.New(errMsg))
	}
	credential := common.NewCredential(
		secretId,
		secretKey,
	)
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = model.CLB_ENDPOINT
	client, _ := clb.NewClient(credential, target.Region, cpf)

	request := clb.NewCreateRuleRequest()
	request.LoadBalancerId = common.StringPtr(target.LoadBalancerId)
	request.ListenerId = common.StringPtr(target.ListenerId)
	request.Rules = []*clb.RuleInput{
		{
			Domain: common.StringPtr(target.Domain),
			Url:    common.StringPtr(target.Url),
		},
	}
	logger.Infof("CreateRule request: %s", request.ToJsonString())
	response, err := client.CreateRule(request)
	if err != nil {
		errMsg := fmt.Sprintf("CreateRule err: %v", err)
		return model.CLB_API_NORMAL_ERROR, "", converter.GenCommonErrorCode(converter.INTERNAL_ERROR_MESSAGE, errors.New(errMsg))
	}
	logger.Infof("CreateRule response: %s", response.ToJsonString())
	return 0, "success", nil
}

func DeleteRule(target *Target) (code int, retmsg string, err error) {
	secretId, secretKey, err := service.GetSecretIdAndKeyOfScs()
	if err != nil {
		errMsg := fmt.Sprintf("DeleteRule: GetSecretIdAndKeyOfScs err: %v", err)
		return model.CLB_API_NORMAL_ERROR, "", converter.GenSecCommonErrorCode(converter.CLB_ERROR, converter.ERROR_CODE_58000, converter.INTERNAL_ERROR_MESSAGE, errors.New(errMsg))
	}
	credential := common.NewCredential(
		secretId,
		secretKey,
	)
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = model.CLB_ENDPOINT
	client, _ := clb.NewClient(credential, target.Region, cpf)

	request := clb.NewDeleteRuleRequest()
	request.LoadBalancerId = common.StringPtr(target.LoadBalancerId)
	request.ListenerId = common.StringPtr(target.ListenerId)
	request.Domain = common.StringPtr(target.Domain)
	request.Url = common.StringPtr(target.Url)

	// 返回的resp是一个DeleteRuleResponse的实例，与请求对象对应
	response, err := client.DeleteRule(request)
	if err != nil {
		errMsg := fmt.Sprintf("DeleteRule err: %v", err)
		return model.CLB_API_NORMAL_ERROR, "", converter.GenCommonErrorCode(converter.INTERNAL_ERROR_MESSAGE, errors.New(errMsg))
	}
	logger.Infof("DeleteRule response: %s", response.ToJsonString())
	return 0, "success", nil
}

func DescribeListeners(loadBalancerId string, region string) (listeners []*clb.Listener, retmsg string, err error) {
	secretId, secretKey, err := service.GetSecretIdAndKeyOfScs()
	if err != nil {
		errMsg := fmt.Sprintf("DescribeListeners: GetSecretIdAndKeyOfScs err: %v", err)
		return nil, "", converter.GenSecCommonErrorCode(converter.CLB_ERROR, converter.ERROR_CODE_58000, converter.INTERNAL_ERROR_MESSAGE, errors.New(errMsg))
	}
	credential := common.NewCredential(
		secretId,
		secretKey,
	)
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = model.CLB_ENDPOINT
	client, _ := clb.NewClient(credential, region, cpf)

	request := clb.NewDescribeListenersRequest()
	request.LoadBalancerId = common.StringPtr(loadBalancerId)

	// 返回的resp是一个DeleteRuleResponse的实例，与请求对象对应
	response, err := client.DescribeListeners(request)
	if err != nil {
		errMsg := fmt.Sprintf("DescribeListeners err: %v", err)
		return nil, "", converter.GenCommonErrorCode(converter.INTERNAL_ERROR_MESSAGE, errors.New(errMsg))
	}
	logger.Infof("DescribeListeners response: %s", response.ToJsonString())
	return response.Response.Listeners, "success", nil
}

func CheckRuleExist(loadBalancerId string, url string, region string) (bool, error) {
	logger.Infof("CheckRuleExist: loadBalancerId: %s, url: %s", loadBalancerId, url)
	listeners, _, err := DescribeListeners(loadBalancerId, region)
	if err != nil {
		return false, err
	}
	if len(listeners) == 0 {
		return false, nil
	}
	for _, listener := range listeners {
		if *listener.Port != 443 {
			continue
		}
		// 遍历Rules
		for _, rule := range listener.Rules {
			if *rule.Url == url {
				return true, nil
			}
		}
	}

	return false, nil
}
