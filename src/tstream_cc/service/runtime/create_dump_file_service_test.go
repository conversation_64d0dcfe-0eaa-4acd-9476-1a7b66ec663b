package runtime

import (
	"bytes"
	"fmt"
	"io"
	"os"
	"testing"

	v1 "k8s.io/api/core/v1"
	"k8s.io/client-go/tools/remotecommand"
	"k8s.io/kubectl/pkg/scheme"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/runtime"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
)

func TestCreateDumpFileService_execCmd(t *testing.T) {

	f, err := os.Open(".kube")
	if err != nil {
		fmt.Printf("open .kube with error %v \n", err)
	}
	data, err := io.ReadAll(f)
	if err != nil {
		fmt.Printf("read .kube with error %v \n", err)
	}

	clientset, err := k8s.GetK8sService().NewClient(data)
	if err != nil {
		t.<PERSON>("err %v", err)
	}

	podName := "cql-6xluan4w-5819-taskmanager-1-1"
	namespace := "default"

	req := clientset.CoreV1().RESTClient().Post().
		Resource("pods").
		Name(podName).
		Namespace(namespace).
		SubResource("exec").VersionedParams(&v1.PodExecOptions{
		Command:   []string{"bash", "-c", "ls /opt"},
		Stdin:     true,
		Stdout:    true,
		Stderr:    true,
		Container: model.FlinkPodMainContainer,
		TTY:       false, // 打开linux终端
	}, scheme.ParameterCodec)
	config, err := k8s.GetK8sService().NewConfig(data)
	if err != nil {
		fmt.Printf("err %v \n", err)
	}
	executor, err := remotecommand.NewSPDYExecutor(config, "POST", req.URL())
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
	}
	var stdout, stderr bytes.Buffer
	err = executor.Stream(remotecommand.StreamOptions{
		Stdout: &stdout,
		Stderr: &stderr,
		Tty:    false,
	})
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
	fmt.Printf("Output:\n%s\n", stdout.String())
}
