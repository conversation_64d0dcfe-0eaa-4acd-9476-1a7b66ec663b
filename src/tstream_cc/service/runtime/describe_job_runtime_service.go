package runtime

import (
	"context"
	b64 "encoding/base64"
	"encoding/json"
	"strconv"
	"strings"
	"time"

	"github.com/emirpasic/gods/sets/treeset"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"tencentcloud.com/tstream_galileo/src/common/auth"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/runtime"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job"
	watchdogModel "tencentcloud.com/tstream_galileo/src/tstream_cc/model/watchdog"
	service1 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
)

type DescribeJobRuntimeInfoService struct {
	job *table.Job
}

func (s *DescribeJobRuntimeInfoService) DoDescribeJobRuntimeInfo(req *model.DescribeJobRuntimeInfoReq) (
	*model.DescribeJobRuntimeInfoRsp, error) {
	//鉴权
	_, err := auth.InnerAuthJob(req.WorkSpaceId, req.IsSupOwner, req.JobId, req.AppId, req.SubAccountUin, req.Region, req.Action)
	if err != nil {
		logger.Errorf("%s: User owner uin %s is NOT authenticated, refuse to serve", req.RequestId, req.Uin)
		return model.EmptyJobRuntimeInfo(), err
	}
	// 检查作业是否存在
	job, err := service1.WhetherJobExists(int32(req.AppId), req.Region, req.JobId)
	if err != nil {
		logger.Errorf("%s: job [%s] not exists in appId [%s]", req.RequestId, req.JobId, req.AppId)
		return model.EmptyJobRuntimeInfo(), err
	}
	s.job = job
	jobRuntimeInfos := make([]*model.JobRuntimeInfo, 0)
	if contains(req.IncludeInfo, model.TaskManagers) {
		tms, err := s.getJobRunningPods(req, "taskmanager")
		if err != nil {
			logger.Errorf("%s:get job running taskmanagers with error %v", req.RequestId, err)
		} else {
			bytes, err := json.Marshal(tms)
			if err != nil {
				logger.Errorf("%s,get job running taskmanagers with format error %v", req.RequestId, err)
			} else {
				jobRuntimeInfos = append(jobRuntimeInfos, &model.JobRuntimeInfo{
					Key:   model.TaskManagers,
					Value: b64.StdEncoding.EncodeToString(bytes),
				})
			}
		}
	}
	if contains(req.IncludeInfo, model.JobManagers) {
		jms, err := s.getJobRunningPods(req, "jobmanager")
		if err != nil {
			logger.Errorf("%s:get job running jobmanagers with error %v", req.RequestId, err)
		} else {
			bytes, err := json.Marshal(jms)
			if err != nil {
				logger.Errorf("%s,get job running jobmanagers with format error %v", req.RequestId, err)
			} else {
				jobRuntimeInfos = append(jobRuntimeInfos, &model.JobRuntimeInfo{
					Key:   model.JobManagers,
					Value: b64.StdEncoding.EncodeToString(bytes),
				})
			}
		}
	}
	if contains(req.IncludeInfo, model.StreamGraph) {
		jobPlan, err := s.getJobPlan(req)
		if err != nil {
			logger.Errorf("%s:get job plan with error %v", req.RequestId, err)
		} else {
			jobRuntimeInfos = append(jobRuntimeInfos, &model.JobRuntimeInfo{
				Key:   model.StreamGraph,
				Value: b64.StdEncoding.EncodeToString([]byte(jobPlan)),
			})
		}
	}
	if contains(req.IncludeInfo, model.SubTasks) {
		subTaskInfo, err := s.getSubTasks(req)
		if err != nil {
			logger.Errorf("%s:get job sub task with error %v", req.RequestId, err)
		} else {
			jobRuntimeInfos = append(jobRuntimeInfos, &model.JobRuntimeInfo{
				Key:   model.SubTasks,
				Value: b64.StdEncoding.EncodeToString(subTaskInfo),
			})
		}
	}
	if contains(req.IncludeInfo, watchdogModel.KafkaMetricRecordsLag) {
		kafkaTopicInfo, err := s.getKafkaTopic(req, watchdogModel.KafkaMetricRecordsLag)
		if err != nil {
			logger.Errorf("%s:get job KafkaTopic with error %v", req.RequestId, err)
		} else {
			jobRuntimeInfos = append(jobRuntimeInfos, &model.JobRuntimeInfo{
				Key:   watchdogModel.KafkaMetricRecordsLag,
				Value: b64.StdEncoding.EncodeToString(kafkaTopicInfo),
			})
		}
	}
	if contains(req.IncludeInfo, watchdogModel.KafkaMetricLatency) {
		kafkaTopicInfo, err := s.getKafkaTopic(req, watchdogModel.KafkaMetricLatency)
		if err != nil {
			logger.Errorf("%s:get job KafkaTopic with error %v", req.RequestId, err)
		} else {
			jobRuntimeInfos = append(jobRuntimeInfos, &model.JobRuntimeInfo{
				Key:   watchdogModel.KafkaMetricLatency,
				Value: b64.StdEncoding.EncodeToString(kafkaTopicInfo),
			})
		}
	}
	if contains(req.IncludeInfo, watchdogModel.OperatorsNumRecordsIn) {
		operatorsNumRecorsInMeta, err := s.getOperatorInfo(req, watchdogModel.OperatorsNumRecordsIn)
		if err != nil {
			logger.Errorf("%s:get job Operators with error %v", req.RequestId, err)
		} else {
			jobRuntimeInfos = append(jobRuntimeInfos, &model.JobRuntimeInfo{
				Key:   watchdogModel.OperatorsNumRecordsIn,
				Value: b64.StdEncoding.EncodeToString(operatorsNumRecorsInMeta),
			})
		}
	}
	if contains(req.IncludeInfo, watchdogModel.OperatorsNumRecordsOut) {
		operatorsNumRecordsOutMeta, err := s.getOperatorInfo(req, watchdogModel.OperatorsNumRecordsOut)
		if err != nil {
			logger.Errorf("%s:get job Operators with error %v", req.RequestId, err)
		} else {
			jobRuntimeInfos = append(jobRuntimeInfos, &model.JobRuntimeInfo{
				Key:   watchdogModel.OperatorsNumRecordsOut,
				Value: b64.StdEncoding.EncodeToString(operatorsNumRecordsOutMeta),
			})
		}
	}
	rsp := &model.DescribeJobRuntimeInfoRsp{
		JobRuntimeInfo: jobRuntimeInfos,
	}
	return rsp, nil
}

func contains(info []string, key string) bool {
	for _, v := range info {
		if v == key {
			return true
		}
	}
	return false
}

func (s *DescribeJobRuntimeInfoService) getJobRunningPods(req *model.DescribeJobRuntimeInfoReq, podType string) ([]string, error) {
	pods := make([]string, 0)
	nameSpace := "default"
	cluster, err := service2.GetClusterByJobId(req.JobId)
	if err != nil {
		logger.Errorf("get cluster by job id %s return error %v,reqId %s", req.JobId, err, req.RequestId)
		return pods, err
	}
	clusterGroup, err := service2.GetClusterGroupByJobId(req.JobId)
	if err != nil {
		logger.Errorf("get ClusterGroup id %s return error %v,reqId %s", cluster.ClusterGroupId, err, req.RequestId)
		return pods, err
	}
	if clusterGroup.AgentSerialId != "" {
		// 共享集群
		nameSpace = clusterGroup.SerialId
	}
	client, err := k8s.GetK8sService().NewClient([]byte(cluster.KubeConfig))
	if err != nil {
		logger.Errorf("%s: k8s.newClient with error %v", req.RequestId, err)
		return pods, err
	}
	label := labels.SelectorFromSet(map[string]string{
		"jobSerialId": req.JobId,
		"component":   podType,
	})
	podList, err := client.CoreV1().Pods(nameSpace).List(context.TODO(), metav1.ListOptions{
		LabelSelector: label.String(),
	})
	if err != nil {
		logger.Errorf("%s: get pods from k8s has return error %v", req.RequestId, err)
		return pods, err
	}
	tmTreeSet := treeset.NewWith(func(a, b interface{}) int {
		return compareTms(a.(string), b.(string))
	})
	for _, tmPod := range podList.Items {
		tmTreeSet.Add(tmPod.Name)
	}
	for _, v := range tmTreeSet.Values() {
		pods = append(pods, v.(string))
	}
	return pods, nil
}

func compareTms(tm1, tm2 string) int {
	// 输入格式是 cql-ld68ia25-3476-taskmanager-1-128 即按照下划线分为 6 部分
	partsA := strings.Split(tm1, "-")
	partsB := strings.Split(tm2, "-")

	if len(partsA) != 6 || len(partsB) != 6 {
		logger.Errorf("Illegal taskmanager id format, cannot compare. A: %s, B: %s", tm1, tm2)
		return 0
	}

	runtimeIdOfA, err := strconv.Atoi(partsA[2]) // 先比较 3476 部分（RuntimeId）
	if err != nil {
		logger.Errorf("Failed to get runtime id from string %s", tm1)
		return 0
	}
	runtimeIdOfB, err := strconv.Atoi(partsB[2])
	if err != nil {
		logger.Errorf("Failed to get runtime id from string %s", tm2)
		return 0
	}

	if runtimeIdOfA != runtimeIdOfB {
		return runtimeIdOfA - runtimeIdOfB
	}

	taskManagerIdOfA, err := strconv.Atoi(partsA[5]) // 再比较 128 部分（TaskManager 序列号）
	if err != nil {
		logger.Errorf("Failed to get taskmanager id from string %s", tm1)
		return 0
	}
	taskManagerIdOfB, err := strconv.Atoi(partsB[5])
	if err != nil {
		logger.Errorf("Failed to get taskmanager id from string %s", tm2)
		return 0
	}
	return taskManagerIdOfA - taskManagerIdOfB
}

func (s *DescribeJobRuntimeInfoService) getJobPlan(req *model.DescribeJobRuntimeInfoReq) (string, error) {
	jobInstance, err := service.GetRunningJobInstanceByJobId(s.job.Id)
	if err != nil {
		logger.Errorf("%s:get running job instance with error %v", req.RequestId, err)
		return "", err
	}
	return jobInstance.FlinkJobPlan, nil
}

func (s *DescribeJobRuntimeInfoService) getSubTasks(req *model.DescribeJobRuntimeInfoReq) ([]byte, error) {
	jobPlanStr, err := s.getJobPlan(req)
	if err != nil {
		logger.Errorf("%s: get job plan with error %v", req.RequestId, err)
		return []byte(""), err
	}
	jobPlan := &model.FlinkJobPlan{}
	err = json.Unmarshal([]byte(jobPlanStr), jobPlan)
	if err != nil {
		logger.Errorf("%s: get job plan with format error %v", req.RequestId, err)
		return []byte(""), err
	}
	resultSubtasks := make([]*model.SubTaskInfo, 0)
	for _, node := range jobPlan.Nodes {
		for i := 0; i < node.Parallelism; i++ {
			resultSubtasks = append(resultSubtasks, &model.SubTaskInfo{
				TaskId:          node.Id,
				TaskDescription: node.Description + "_" + strconv.Itoa(i),
				SubTaskIndex:    strconv.Itoa(i),
			})
		}
	}
	result, err := json.Marshal(resultSubtasks)
	if err != nil {
		logger.Errorf("%s: marshal subtask with format error %v", req.RequestId, err)
		return []byte(""), err
	}
	return result, nil
}

func (s *DescribeJobRuntimeInfoService) getKafkaTopic(req *model.DescribeJobRuntimeInfoReq, metricName string) ([]byte, error) {
	jobInsMetricMetas, err := service.QueryJobInsMetricMetas(s.job.SerialId, -1, metricName, time.Time{}, -1, -1)
	if err != nil {
		logger.Errorf("%s: QueryJobInsMetricMetas with error %v", req.RequestId, err)
		return []byte(""), err
	}
	var meta string
	for _, item := range jobInsMetricMetas {
		meta = item.Metas
	}
	if meta == "" {
		logger.Errorf("%s: parse meta %s with error %v", req.RequestId, meta, err)
		return []byte(""), err
	}
	metaMap := make(map[string]string)
	err = json.Unmarshal([]byte(meta), &metaMap)
	if err != nil {
		logger.Errorf("%s: parse meta %s with error %v", req.RequestId, meta, err)
		return []byte(""), err
	}
	topics := metaMap[watchdogModel.Topics]
	arr := strings.Split(topics, watchdogModel.TopicSpliter)
	if len(arr) <= 0 {
		logger.Errorf("%s: parse meta %s with error %v", req.RequestId, meta, err)
		return []byte(""), err
	}
	result, err := json.Marshal(arr)
	if err != nil {
		logger.Errorf("%s: marshal meta with format error %v", req.RequestId, err)
		return []byte(""), err
	}
	return result, nil
}

func (s *DescribeJobRuntimeInfoService) getOperatorInfo(req *model.DescribeJobRuntimeInfoReq, metricName string) ([]byte, error) {
	jobInsMetricMetas, err := service.QueryJobInsMetricMetas(s.job.SerialId, -1, metricName, time.Time{}, -1, -1)
	if err != nil {
		logger.Errorf("%s: QueryJobInsMetricMetas with error %v", req.RequestId, err)
		return []byte(""), err
	}
	var meta string
	for _, item := range jobInsMetricMetas {
		meta = item.Metas
	}
	if meta == "" {
		logger.Errorf("%s: parse meta %s with error %v", req.RequestId, meta, err)
		return []byte(""), err
	}
	metaMap := make(map[string]string)
	err = json.Unmarshal([]byte(meta), &metaMap)
	if err != nil {
		logger.Errorf("%s: parse meta %s with error %v", req.RequestId, meta, err)
		return []byte(""), err
	}
	operators := metaMap[watchdogModel.Operators]

	// 去除转义字符
	var result map[string]interface{}
	err = json.Unmarshal([]byte(operators), &result)
	if err != nil {
		logger.Errorf("Error unmarshalling JSON: %v", err)
		return []byte(""), err
	}

	// 将去除转义的内容重新编码成 JSON 字符串
	prettyJSON, err := json.Marshal(result)
	if err != nil {
		logger.Errorf("Error marshalling JSON: %v", err)
		return []byte(""), err
	}
	return prettyJSON, nil
}
