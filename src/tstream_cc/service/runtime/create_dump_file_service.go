package runtime

import (
	"bytes"
	"fmt"
	"strings"
	"time"

	v1 "k8s.io/api/core/v1"

	"k8s.io/client-go/tools/remotecommand"
	"k8s.io/kubectl/pkg/scheme"
	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"

	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/runtime"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
)

type CreateDumpFileService struct {
}

func (s *CreateDumpFileService) DoCreateDumpFile(req *model.CreateDumpFileReq) (
	*model.CreateDumpFileRsp, error) {
	rsp := &model.CreateDumpFileRsp{
		DumpResults: make([]*model.DumpResult, 0),
	}
	//鉴权
	_, err := auth.InnerAuthJob(req.WorkSpaceId, req.IsSupOwner, req.JobId, req.AppId, req.SubAccountUin, req.Region, req.Action)
	if err != nil {
		logger.Errorf("%s: User owner uin %s is NOT authenticated, refuse to serve", req.RequestId, req.Uin)
		return rsp, err
	}
	// 需要加入白名单才能进行 dump
	canbeDump := auth.IsInWhiteList(req.AppId, constants.WHITE_LIST_CREATE_DUMP_FILE)
	if !canbeDump {
		logger.Errorf("%s user %d cannot create dump file", req.RequestId, req.AppId)
		return rsp, errorcode.UnauthorizedOperationCode.New()
	}
	mainContainer := model.FlinkPodMainContainer
	// flink 1.11 main container 单独处理
	job, err2 := service.GetJobByJobSerialId(req.JobId)
	if err2 != nil {
		logger.Errorf("%s get job with error %v", req.RequestId, err2)
		return rsp, err
	}
	if job.FlinkVersion == "Flink-1.11" {
		mainContainer = model.Flink1_11PodMainContainer
	}
	dumpCmds := []string{model.ThreadDumpCmd, model.HeapDumpCmd, model.Upload2CosCmd}
	for _, podName := range req.PodNames {
		for _, dumpCmd := range dumpCmds {
			// 给文件名加上时间
			if strings.Contains(dumpCmd, "%d") {
				dumpCmd = fmt.Sprintf(dumpCmd, time.Now().UnixMilli())
			}
			cmd := []string{"bash", "-c", dumpCmd}
			execResult, err := s.execCmd(req, podName, mainContainer, cmd)
			if err != nil {
				logger.Errorf("%s: exec %s on pod %s has return error %v", req.RequestId, cmd, podName, err)
				rsp.DumpResults = append(rsp.DumpResults, &model.DumpResult{
					PodName:   podName,
					Cmd:       dumpCmd,
					Result:    false,
					OutputMsg: execResult + err.Error(),
				})
			} else {
				rsp.DumpResults = append(rsp.DumpResults, &model.DumpResult{
					PodName:   podName,
					Cmd:       dumpCmd,
					Result:    true,
					OutputMsg: execResult,
				})
			}
		}
	}
	return rsp, nil
}

func (s *CreateDumpFileService) execCmd(req *model.CreateDumpFileReq, podName, container string, cmd []string) (string, error) {
	execResult := ""
	nameSpace := "default"
	cluster, err := service2.GetClusterByJobId(req.JobId)
	if err != nil {
		logger.Errorf("get cluster by job id %s return error %v,reqId %s", req.JobId, err, req.RequestId)
		return execResult, err
	}
	clusterGroup, err := service2.GetClusterGroupByJobId(req.JobId)
	if err != nil {
		logger.Errorf("get ClusterGroup by job id %s return error %v,reqId %s", req.JobId, err, req.RequestId)
		return execResult, err
	}
	if clusterGroup.AgentSerialId != "" {
		nameSpace = clusterGroup.SerialId
	}
	client, err := k8s.GetK8sService().NewClient([]byte(cluster.KubeConfig))
	if err != nil {
		logger.Errorf("%s: k8s.newClient with error %v", req.RequestId, err)
		return execResult, err
	}
	config, err := k8s.GetK8sService().NewConfig([]byte(cluster.KubeConfig))
	if err != nil {
		logger.Errorf("%s:NewConfig has return error %v", req.RequestId, err)
		return execResult, err
	}
	k8sReq := client.CoreV1().RESTClient().Post().
		Resource("pods").
		Name(podName).
		Namespace(nameSpace).
		SubResource("exec").
		Param("container", container)
	k8sReq.VersionedParams(
		&v1.PodExecOptions{
			Container: container,
			Command:   cmd,
			Stdin:     false,
			Stdout:    true,
			Stderr:    true,
			TTY:       false,
		},
		scheme.ParameterCodec,
	)
	executor, err := remotecommand.NewSPDYExecutor(config, "POST", k8sReq.URL())
	if err != nil {
		logger.Errorf("%s:NewSPDYExecutor has return error %v", req.RequestId, err)
		return execResult, err
	}
	var stdout, stderr bytes.Buffer
	err = executor.Stream(remotecommand.StreamOptions{
		Stdin:  nil,
		Stdout: &stdout,
		Stderr: &stderr,
	})
	if err != nil {
		logger.Errorf("%s:exec command has return error %v \n stderr %s", req.RequestId, err, strings.TrimSpace(stderr.String()))
		return execResult, err
	}
	logger.Infof("%s: exec command %s result:\n%s\n", req.RequestId, cmd, strings.TrimSpace(stdout.String()))
	execResult = strings.TrimSpace(fmt.Sprintf("stdout: %s \n stderr: %s", stdout.String(), stderr.String()))

	return execResult, nil
}
