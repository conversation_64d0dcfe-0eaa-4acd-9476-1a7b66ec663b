package emr

import (
	"encoding/json"
	"fmt"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/emr"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"testing"
)

func TestEmrService_CreateInstanceWithScsAccount(t *testing.T) {
	secretId, secretKey, err := service.GetSecretIdAndKeyOfScs()
	if err != nil {
		t.Error(err)
	}

	emrService := GetEmrService()
	request := emrService.NewDefaultCreateInstanceRequestBuilder().
		WithInstanceName(fmt.Sprintf("%s-test", *fTestUin)).
		WithPlacement(model.EMR_PROJECT_ID, *fTestZone).
		WithVPCSettings(*fTestUniqVpcId, *fTestUniqSubnetId).
		WithPreExecuteFileSettings(model.EMR_INIT_FILE_WHEN_RUN, *fTestCosFileURI, secretId, secretKey, nil).
		WithSecurityGroup(*fTestSgId).
		Build()

	if err := emrService.CreateInstanceWithScsAccount(*fTestRegion, request); err != nil {
		t.Error(err)
	} else {
		t.Logf("success")
	}
}

func TestEmrService_DescribeInstancesWithScsAccount(t *testing.T) {
	emrService := GetEmrService()
	request := emrService.NewDefaultDescribeInstancesRequestBuilder().
		WithInstanceIds([]string{*fTestInstanceId}).
		Build()
	if totalCount, clusterList, err := emrService.DescribeInstancesWithScsAccount(*fTestRegion, request); err != nil {
		t.Error(err)
	} else {
		b, _ := json.MarshalIndent(clusterList, "", " ")
		t.Logf("success, count %d, clusterList %s", totalCount, string(b))
	}
}

func TestEmrService_DescribeClusterNodesWithScsAccount(t *testing.T) {
	emrService := GetEmrService()

	for _, nodeFlag := range []string{model.EMR_NODE_FLAG_MASTER, model.EMR_NODE_FLAG_COMMON} {
		if totalCount, nodeList, err := emrService.DescribeClusterNodesWithScsAccount(*fTestRegion,
			emrService.NewDefaultDescribeClusterNodesRequestBuilder().
				WithInstanceId(*fTestInstanceId).
				WithNodeFlag(nodeFlag).
				Build()); err != nil {
			t.Error(err)
		} else {
			b, _ := json.MarshalIndent(nodeList, "", " ")
			t.Logf("success, %s count %d, clusterList %s", nodeFlag, totalCount, string(b))
		}
	}
}

func TestEmrService_TerminateInstanceWithScsAccount(t *testing.T) {
	emrService := GetEmrService()

	req := emrService.NewDefaultTerminateInstanceRequestBuilder().WithInstanceId(*fTestInstanceId).Build()
	_, err := emrService.TerminateInstanceWithScsAccount(*fTestRegion, req)
	if err != nil {
		t.Fatal(err)
	}

	t.Logf("delete emr %s sucess", *fTestInstanceId)
}
