package emr

import emr "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/emr/v20190103"

type CreateInstanceRequestBuilder struct {
	request *emr.CreateInstanceRequest
}

/////////////////////////////////////////////////////////////////////////////////////////
func (this *CreateInstanceRequestBuilder) WithDomain(domain string) *CreateInstanceRequestBuilder {
	this.request.SetDomain(domain)
	return this
}

func (this *CreateInstanceRequestBuilder) WithProductId(productId uint64) *CreateInstanceRequestBuilder {
	this.request.ProductId = &productId
	return this
}

func (this *CreateInstanceRequestBuilder) WithVPCSettings(vpcId, subnetId string) *CreateInstanceRequestBuilder {
	this.request.VPCSettings = &emr.VPCSettings{
		VpcId:    &vpcId,
		SubnetId: &subnetId,
	}
	return this
}

func (this *CreateInstanceRequestBuilder) WithSoftware(software []string) *CreateInstanceRequestBuilder {
	if len(software) > 0 {
		for _, s := range software {
			s := s
			this.request.Software = append(this.request.Software, &s)
		}
	}
	return this
}

func (this *CreateInstanceRequestBuilder) WithMaster(count int64, spec, diskType string, storageType, cpu, memSize, diskSize int64) *CreateInstanceRequestBuilder {
	if this.request.ResourceSpec == nil {
		this.request.ResourceSpec = &emr.NewResourceSpec{}
	}

	this.request.ResourceSpec.MasterCount = &count
	this.request.ResourceSpec.MasterResourceSpec = &emr.Resource{
		Spec:         &spec,
		StorageType:  &storageType,
		DiskType:     &diskType,
		MemSize:      &memSize,
		Cpu:          &cpu,
		DiskSize:     &diskSize,
		RootSize:     nil,
		MultiDisks:   nil,
		Tags:         nil,
		InstanceType: nil,
		LocalDiskNum: nil,
		DiskNum:      nil,
	}

	return this
}

func (this *CreateInstanceRequestBuilder) WithCore(count int64, spec, diskType string, storageType, cpu, memSize, diskSize int64) *CreateInstanceRequestBuilder {
	if this.request.ResourceSpec == nil {
		this.request.ResourceSpec = &emr.NewResourceSpec{}
	}

	this.request.ResourceSpec.CoreCount = &count
	this.request.ResourceSpec.CoreResourceSpec = &emr.Resource{
		Spec:         &spec,
		StorageType:  &storageType,
		DiskType:     &diskType,
		MemSize:      &memSize,
		Cpu:          &cpu,
		DiskSize:     &diskSize,
		RootSize:     nil,
		MultiDisks:   nil,
		Tags:         nil,
		InstanceType: nil,
		LocalDiskNum: nil,
		DiskNum:      nil,
	}

	return this
}

func (this *CreateInstanceRequestBuilder) WithCommon(count int64, spec, diskType string, storageType, cpu, memSize, diskSize int64) *CreateInstanceRequestBuilder {
	if this.request.ResourceSpec == nil {
		this.request.ResourceSpec = &emr.NewResourceSpec{}
	}

	this.request.ResourceSpec.CommonCount = &count
	this.request.ResourceSpec.CommonResourceSpec = &emr.Resource{
		Spec:         &spec,
		StorageType:  &storageType,
		DiskType:     &diskType,
		MemSize:      &memSize,
		Cpu:          &cpu,
		DiskSize:     &diskSize,
		RootSize:     nil,
		MultiDisks:   nil,
		Tags:         nil,
		InstanceType: nil,
		LocalDiskNum: nil,
		DiskNum:      nil,
	}

	return this
}

func (this *CreateInstanceRequestBuilder) WithSupportHA(supportHA uint64) *CreateInstanceRequestBuilder {
	this.request.SupportHA = &supportHA
	return this
}

func (this *CreateInstanceRequestBuilder) WithInstanceName(instanceName string) *CreateInstanceRequestBuilder {
	this.request.InstanceName = &instanceName
	return this
}

func (this *CreateInstanceRequestBuilder) WithPayMode(payMode uint64) *CreateInstanceRequestBuilder {
	this.request.PayMode = &payMode
	return this
}

func (this *CreateInstanceRequestBuilder) WithPlacement(projectId int64, zone string) *CreateInstanceRequestBuilder {
	this.request.Placement = &emr.Placement{
		ProjectId: &projectId,
		Zone:      &zone,
	}
	return this
}

func (this *CreateInstanceRequestBuilder) WithTime(timeSpan uint64, timeUnit string) *CreateInstanceRequestBuilder {
	this.request.TimeSpan = &timeSpan
	this.request.TimeUnit = &timeUnit
	return this
}

func (this *CreateInstanceRequestBuilder) WithLoginSettings(password string) *CreateInstanceRequestBuilder {
	this.request.LoginSettings = &emr.LoginSettings{
		Password:    &password,
		PublicKeyId: nil,
	}
	return this
}

func (this *CreateInstanceRequestBuilder) WithSgId(sgId string) *CreateInstanceRequestBuilder {
	this.request.SgId = &sgId
	return this
}

func (this *CreateInstanceRequestBuilder) WithPreExecuteFileSettings(whenRun, cosFileURI, cosSecretId, cosSecretKey string, args []string) *CreateInstanceRequestBuilder {
	setting := &emr.PreExecuteFileSettings{
		Path:         nil,
		Args:         nil,
		Bucket:       nil,
		Region:       nil,
		Domain:       nil,
		RunOrder:     nil,
		WhenRun:      &whenRun,
		CosFileName:  nil,
		CosFileURI:   &cosFileURI,
		CosSecretId:  &cosSecretId,
		CosSecretKey: &cosSecretKey,
		AppId:        nil,
	}
	if len(args) > 0 {
		for _, arg := range args {
			arg := arg
			setting.Args = append(setting.Args, &arg)
		}
	}

	this.request.PreExecutedFileSettings = append(this.request.PreExecutedFileSettings, setting)

	return this
}

func (this *CreateInstanceRequestBuilder) WithAutoRenew(autoRenew uint64) *CreateInstanceRequestBuilder {
	this.request.AutoRenew = &autoRenew
	return this
}

func (this *CreateInstanceRequestBuilder) WithSecurityGroup(sg string) *CreateInstanceRequestBuilder {
	this.request.SgId = &sg
	return this
}

func (this *CreateInstanceRequestBuilder) WithApplicationRole(role string) *CreateInstanceRequestBuilder {
	this.request.ApplicationRole = &role
	return this
}

func (this *CreateInstanceRequestBuilder) Build() *emr.CreateInstanceRequest {
	return this.request
}

func NewCreateInstanceRequestBuilder() *CreateInstanceRequestBuilder {
	return &CreateInstanceRequestBuilder{request: emr.NewCreateInstanceRequest()}
}
