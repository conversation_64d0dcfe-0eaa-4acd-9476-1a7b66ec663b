package service

import (
	"crypto/hmac"
	"crypto/md5"
	"crypto/sha1"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	billing3 "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/billing/v20180709"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/profile"
	vpc "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/vpc/v20170312"
	"github.com/shopspring/decimal"
	"io"
	"io/ioutil"
	"math/rand"
	"net/http"
	"reflect"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service2 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/billing"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job"
	table6 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table"
	table3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/Cvm"
	table4 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	tableItemSpace "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/item_space"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job"
	table5 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job_config"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job_instance"
	setats2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/setats"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/watchdog"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/xingyun"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config/configure_center"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/configuration_center"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/white_list"
	sts "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/sts"
	wedata "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/wedata"
	"time"
)

/**
 *
 * <AUTHOR>
 * @time 2019/4/29
 * @version 0.4.0
 * @since 0.4.0
 * @see
 */
func CheckNameValidity(name string) (err error) {
	re, _ := regexp.Match("^[\u4e00-\u9fa5a-zA-Z0-9_.-]{1,50}$", []byte(name))
	if !re {
		msg := fmt.Sprintf("Invalid Name: `%s`, only [alpha,number,-_] is allowed, length should less than 50", name)
		return errorcode.InvalidParameterValueCode.ReplaceDesc(msg)
	}
	return nil
}

func CheckNameValidityV2(name string, length int) (err error) {
	re, _ := regexp.Match("^[\u4e00-\u9fa5a-zA-Z0-9_.-]{1,"+strconv.Itoa(length)+"}$", []byte(name))
	if !re {
		msg := fmt.Sprintf("Invalid Name: `%s`, only [alpha,number,-_,.] is allowed, length should less than %d", name, length)
		return errorcode.InvalidParameterValueCode.ReplaceDesc(msg)
	}
	return nil
}

/**
 * Hadoop集群创建完成，更新 创建时间，更准确的记录集群的开始产生费用的时间
 */
func UpdateClusterHadoopYarnStartTime(id int64) (err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("UpdateClusterSessionStartTime panic, with errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	sql := "UPDATE ClusterHadoopYarn SET StartTime = ? WHERE Id = ?"
	service2.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.ExecuteSqlWithArgs(sql, util.GetCurrentTime(), id)
		return nil
	}).Close()
	return nil
}

/**
 * session集群创建完成，更新 创建时间，更准确的记录集群的开始产生费用的时间
 */
func UpdateClusterSessionStartTime(id int64) (err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("UpdateClusterSessionStartTime panic, with errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	sql := "UPDATE ClusterSession SET StartTime = ? WHERE Id = ?"
	service2.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.ExecuteSqlWithArgs(sql, util.GetCurrentTime(), id)
		return nil
	}).Close()
	return nil
}

func UpdateClusterSessionStopTime(id int64, stopTime string) (err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("UpdateClusterSessionStopTime panic, with errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	sql := "UPDATE ClusterSession SET StopTime = ? WHERE Id = ?"
	service2.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.ExecuteSqlWithArgs(sql, stopTime, id)
		return nil
	}).Close()
	return nil
}

func UpdateClusterHadoopYarnStopTime(id int64, stopTime string) (err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("UpdateHadoopYarnStopTime panic, with errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	sql := "UPDATE ClusterHadoopYarn SET StopTime = ? WHERE Id = ?"
	service2.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.ExecuteSqlWithArgs(sql, stopTime, id)
		return nil
	}).Close()
	return nil
}

/**
 * SqlGateway集群创建完成，更新 创建时间，更准确的记录集群的开始产生费用的时间
 */
func UpdateSqlGatewayStartTime(id int64) (err error) {

	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("UpdateSqlGatewayStartTime panic, with errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()

	sql := "UPDATE SqlGateway SET StartTime = ? WHERE Id = ?"
	service2.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.ExecuteSqlWithArgs(sql, util.GetCurrentTime(), id)
		return nil
	}).Close()
	return nil
}

func UpdateSqlGatewayStopTime(id int64, stopTime string) (err error) {

	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("UpdateSqlGatewayStopTime panic, with errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()

	sql := "UPDATE SqlGateway SET StopTime = ? WHERE Id = ?"
	service2.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.ExecuteSqlWithArgs(sql, stopTime, id)
		return nil
	}).Close()
	return nil
}

func SwitchClusterSessionStatusTo(id int64, status int) (err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Get cluster id panic, with errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	sql := "UPDATE ClusterSession SET Status = ? WHERE Id = ?"
	args := make([]interface{}, 0)
	args = append(args, status)
	args = append(args, id)
	txManager := service2.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		rs := tx.ExecuteSql(sql, args)
		_, err := rs.RowsAffected()
		if err != nil {
			logger.Errorf("Failed to update ClusterSession status , with sql: %s, with args:%+v", sql, args)
			return err
		}
		return nil
	}).Close()
	return err
}

func SwitchClusterHadoopYarnStatusTo(id int64, status int) (err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Get cluster id panic, with errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	sql := "UPDATE ClusterHadoopYarn SET Status = ? WHERE Id = ?"
	args := make([]interface{}, 0)
	args = append(args, status)
	args = append(args, id)
	txManager := service2.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		rs := tx.ExecuteSql(sql, args)
		_, err := rs.RowsAffected()
		if err != nil {
			logger.Errorf("Failed to update ClusterHadoopYarn status , with sql: %s, with args:%+v", sql, args)
			return err
		}
		return nil
	}).Close()
	return err
}

func GetClusterHadoopYarnBySerialId(serialId string) (clusterHadoopYarn *table4.ClusterHadoopYarn, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("GetClusterHadoopYarnBySerialId error"))
	sql := "select * from ClusterHadoopYarn  where ClusterGroupSerialId = ?  "
	args := make([]interface{}, 0)
	args = append(args, serialId)

	txManager := service2.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to execute the sql:%s, with args:%+v, with errors:%+v", sql, args, err)
		return &table4.ClusterHadoopYarn{}, err
	}
	if len(data) == 0 {
		logger.Warningf("Failed to query the cluster Hadoop, because the cluster Hadoop size is 0")
		return nil, errors.New("cluster Hadoop query size is 0")
	}
	clusterHadoopYarn = &table4.ClusterHadoopYarn{}
	err = util.ScanMapIntoStruct(clusterHadoopYarn, data[0])
	if err != nil {
		logger.Errorf("Failed to convert bytes into cluster Hadoop, with errors:%+v", err)
		return &table4.ClusterHadoopYarn{}, err
	}
	return clusterHadoopYarn, nil
}

func GetClusterSessionBySerialIdAndFlinkVersion(serialId string, flinkVersion string) (clusterSession *table4.ClusterSession, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("GetClusterSessionBySerialIdAndFlinkVersion error"))
	sql := "select * from ClusterSession  where  ClusterGroupSerialId = ?  and FlinkVersion = ?"
	args := make([]interface{}, 0)
	args = append(args, serialId)
	args = append(args, flinkVersion)

	txManager := service2.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to execute the sql:%s, with args:%+v, with errors:%+v", sql, args, err)
		return &table4.ClusterSession{}, err
	}
	if len(data) == 0 {
		logger.Warningf("Failed to query the cluster session, because the cluster session size is 0")
		return nil, errors.New("cluster session query size is 0")
	}
	clusterSession = &table4.ClusterSession{}
	err = util.ScanMapIntoStruct(clusterSession, data[0])
	if err != nil {
		logger.Errorf("Failed to convert bytes into cluster session, with errors:%+v", err)
		return &table4.ClusterSession{}, err
	}
	return clusterSession, nil
}

func GetClusterSessionsBySerialId(serialId string) (clusterSession []*table4.ClusterSession, err error) {
	sql := "select * from ClusterSession  where  ClusterGroupSerialId = ?"
	args := make([]interface{}, 0)
	args = append(args, serialId)
	clusterSessionList := make([]*table4.ClusterSession, 0)
	txManager := service2.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to execute the sql:%s, with args:%+v, with errors:%+v", sql, args, err)
		return nil, err
	}
	for i := 0; i < len(data); i++ {
		clusterSession := &table4.ClusterSession{}
		err = util.ScanMapIntoStruct(clusterSession, data[i])
		if err != nil {
			return nil, err
		}
		clusterSessionList = append(clusterSessionList, clusterSession)
	}
	return clusterSessionList, nil
}

func GetEksSettleBySerialId(serialId string) (eksSettles []*table6.EksSettle, err error) {
	sql := "select * from EksSettle where ResourceId = ?"
	args := make([]interface{}, 0)
	args = append(args, serialId)
	eksSettleList := make([]*table6.EksSettle, 0)
	txManager := service2.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to execute the sql:%s, with args:%+v, with errors:%+v", sql, args, err)
		return nil, err
	}
	for i := 0; i < len(data); i++ {
		eksSettle := &table6.EksSettle{}
		err = util.ScanMapIntoStruct(eksSettle, data[i])
		if err != nil {
			return nil, err
		}
		eksSettleList = append(eksSettleList, eksSettle)
	}
	return eksSettleList, nil
}

func BuildClusterSessionItem(session *table4.ClusterSession) *table4.ClusterSessionItem {
	props := make([]*model3.Property, 0)
	json.Unmarshal([]byte(session.Properties), &props)
	clusterSessionItem := &table4.ClusterSessionItem{
		ClusterGroupSerialId: session.ClusterGroupSerialId,
		AppId:                session.AppId,
		OwnerUin:             session.OwnerUin,
		CreatorUin:           session.CreatorUin,
		Region:               session.Region,
		Zone:                 session.Zone,
		Status:               session.Status,
		CuNum:                session.CuNum,
		FlinkVersion:         session.FlinkVersion,
		Properties:           props,
		JobManagerCuSpec:     session.JobManagerCuSpec,
		TaskManagerCuSpec:    session.TaskManagerCuSpec,
		TaskManagerNum:       session.TaskManagerNum,
		CreateTime:           session.CreateTime,
		UpdateTime:           session.UpdateTime,
	}
	return clusterSessionItem
}

// 返回没有删除的状态
func ListClusterGroupByParentSerialId(serialId string) (count int, t *table4.ClusterGroup, err error) {
	txManager := service2.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQueryWithArgs("SELECT * FROM ClusterGroup WHERE ParentSerialId=? and Status != -2", serialId)
	if err != nil {
		return 0, nil, err
	} else if len(data) == 1 {
		clusterGroup := &table4.ClusterGroup{}
		err = util.ScanMapIntoStruct(clusterGroup, data[0])
		return 1, clusterGroup, nil
	} else if len(data) == 0 {
		return 0, nil, nil
	} else {
		return len(data), nil, errorcode.FailedOperationCode.NewWithMsg(fmt.Sprintf("cluster %s exists more than 1 sub eks cluster", serialId))
	}
}

func GetCuNumFromCpuMem(cpu float32, memory float32, memRatio int8) float32 {
	temp := memory / float32(memRatio)
	if cpu >= temp {
		return GetFloat2Dot(cpu)
	} else {
		return GetFloat2Dot(temp)
	}
}

func GetFreeCuNumFromCpuMem(cpu float32, memory float32, memRatio int8) float32 {
	temp := memory / float32(memRatio)
	if cpu > temp {
		return GetFloat2Dot(temp)
	} else {
		return GetFloat2Dot(cpu)
	}
}

// 统一资源池下 包年包月下会有按量付费。混合计费
func GetClusterTotalCU(clusterGroup *table4.ClusterGroup) (clusterTotalCu float32) {
	clusterTotalCu = float32(clusterGroup.CuNum)
	count, subCg, err := ListClusterGroupByParentSerialId(clusterGroup.SerialId)
	if err != nil {
		logger.Warningf("### when getClusterTotalCU, fail to ListClusterGroupByParentSerialId with %s", clusterGroup.SerialId)
		return clusterTotalCu
	}
	if count != 1 {
		return clusterTotalCu
	}
	if subCg.Status != constants.CLUSTER_GROUP_STATUS_RUNNING {
		return clusterTotalCu
	}
	return clusterTotalCu + float32(subCg.CuNum)

}

func GetClusterRunningCpuAndMem(serialId string, appId int32, region string, memRatio int8) (totalRunningCpu, totalRunningMem float32, err error) {

	listJobQuery := model.ListJobQuery{
		AppId:           appId,
		Regions:         []string{region},
		IsVagueNames:    false,
		Status:          []int64{constants.JOB_STATUS_RUNNING},
		ClusterGroupIds: []string{serialId},
		Offset:          0,
		Limit:           -1,
	}
	jobs, err := ListJobs(&listJobQuery)
	if err != nil {
		return totalRunningCpu, totalRunningMem, err
	}

	jmc, jmm, tmc, tmm, err := GetClusterJobRunningCPUAndMem(jobs)
	if err != nil {
		return totalRunningCpu, totalRunningMem, err
	}
	totalRunningCpu = jmc + tmc
	totalRunningMem = jmm + tmm
	// 查询ClusterGroup对应的session集群
	clusterSessions, err := GetClusterSessionsBySerialId(serialId)
	if err != nil {
		return totalRunningCpu, totalRunningMem, err
	}
	// RunningCu要扣除掉Session集群使用的CU数
	for i := 0; i < len(clusterSessions); i++ {
		cs := clusterSessions[i]
		// 开启中，开启，停止中这些状态都要扣
		if cs.Status == constants.ClusterSessionCreating ||
			cs.Status == constants.ClusterSessionStopping ||
			cs.Status == constants.ClusterSessionRunning {
			if cs.JobManagerCpu != 0 {
				totalRunningCpu += cs.JobManagerCpu + cs.TaskManagerCpu*float32(cs.TaskManagerNum)
				totalRunningMem += cs.JobManagerMem + cs.TaskManagerMem*float32(cs.TaskManagerNum)
			} else {
				sessionClusterRunningCu := cs.JobManagerCuSpec + cs.TaskManagerCuSpec*float32(cs.TaskManagerNum)
				totalRunningCpu += sessionClusterRunningCu
				totalRunningMem += sessionClusterRunningCu * float32(memRatio)
			}
		}
	}
	// Calculate the CU used by SqlGateway.
	sqlGateways, err := GetSqlGatewaysByGroupSerialId(serialId)
	if err != nil {
		return totalRunningCpu, totalRunningMem, err
	}
	for _, gateway := range sqlGateways {
		if gateway.Status == constants.SqlGatewayCreating ||
			gateway.Status == constants.SqlGatewayRunning ||
			gateway.Status == constants.SqlGatewayStopping {
			if gateway.Cpu != 0 {
				totalRunningCpu += gateway.Cpu
				totalRunningMem += gateway.Mem
			} else {
				totalRunningCpu += gateway.CuSpec
				totalRunningMem += gateway.CuSpec * float32(memRatio)
			}
		}
	}
	// setats
	count, _setats, err := GetSetatsByClusterGroupSerialId(serialId)
	if err != nil {
		logger.Errorf("Failed to GetClusterRunningCpuAndMem get setats by ClusterGroupSerialId id:%s, with errors:%+v", serialId, err)
		return totalRunningCpu, totalRunningMem, err
	}
	if count == 1 && _setats.Status != constants.SETATS_ISOLATED {
		setatsCpu := _setats.MasterCpu + float32(_setats.WorkerDefaultParallelism)*_setats.WorkerCpu
		totalRunningCpu += setatsCpu
		totalRunningMem += setatsCpu * float32(memRatio)
	}

	// 计算the CU used by yarn
	yarns, err := GetHadoopYarnsByGroupSerialId(serialId)
	if err != nil {
		return totalRunningCpu, totalRunningMem, err
	}
	for _, yarn := range yarns {
		if yarn.Status == constants.ClusterHadoopYarnCreating ||
			yarn.Status == constants.ClusterHadoopYarnRunning ||
			yarn.Status == constants.ClusterHadoopYarnStopping {
			if yarn.Cpu != 0 {
				totalRunningCpu += yarn.Cpu
				totalRunningMem += yarn.Cpu * float32(memRatio)
			}
		}
	}
	return GetFloat2Dot(totalRunningCpu), GetFloat2Dot(totalRunningMem), nil
}

func GetSetatsByClusterGroupSerialId(clusterGroupSerialId string) (count int, setats *setats2.Setats, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Get setats panic, with errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	sql := "select * from Setats where ClusterGroupSerialId=? and Status != -2"
	args := make([]interface{}, 0)
	args = append(args, clusterGroupSerialId)
	txManager := service2.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to execute the sql:%s, with args:%+v, with errors:%+v", sql, args, err)
		return 0, nil, err
	}
	if len(data) == 0 {
		logger.Errorf("Failed to query the setats, because the setats size is 0")
		return 0, nil, nil
	}
	setats = &setats2.Setats{}
	err = util.ScanMapIntoStruct(setats, data[0])
	if err != nil {
		logger.Errorf("Failed to convert bytes into setats, with errors:%+v", err)
		return 0, nil, err
	}
	return 1, setats, nil
}

func GetOperateJobCPUAndMem(serialId string, appId int32, region string, memRatio int8) (totalRunningCpu, totalRunningMem float32, err error) {
	listJobQuery := model.ListJobQuery{
		AppId:           appId,
		Regions:         []string{region},
		IsVagueNames:    false,
		Status:          []int64{constants.JOB_STATUS_PROGRESS},
		ClusterGroupIds: []string{serialId},
		Offset:          0,
		Limit:           -1,
	}
	jobs, err := ListJobs(&listJobQuery)
	if err != nil {
		return totalRunningCpu, totalRunningMem, err
	}
	jc, jm, tc, tm, err := GetClusterOperateJobRunningCPUAndMem(jobs, memRatio)
	if err != nil {
		logger.Errorf("GetClusterOperateJobRunningCPUAndMem failed: %+v", err)
		return 0, 0, err
	}
	return GetFloat2Dot(jc + tc), GetFloat2Dot(jm + tm), nil
}

// 获取一批作业实例 当时 作业 使用的CU数目
func GetJobInstanceRunningCuNum(jobInstances []*table2.JobInstance, cuMem int8) (list []*table2.JobInstanceCu, err error) {
	defer errorcode.DefaultDeferHandler(&err)

	if len(jobInstances) == 0 {
		return []*table2.JobInstanceCu{}, nil
	}

	var jobInstanceIds []int64
	for _, jobInstance := range jobInstances {
		jobInstanceIds = append(jobInstanceIds, jobInstance.Id)
	}

	sql := "select a.Id as JobInstanceId, a.TmRunningCuNum,a.JmRunningCuNum,a.TmTotalCpu,a.TmTotalMem,b.* from" +
		" JobInstance a join JobConfig b on b.Id = a.JobConfigId"

	cond := dao.NewCondition()
	cond.In("a.Id", jobInstanceIds)
	where, args := cond.GetWhere()
	sql += where

	_, data, err := service2.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return []*table2.JobInstanceCu{}, err
	}
	if len(data) == 0 {
		return []*table2.JobInstanceCu{}, nil
	}
	jobInstanceCus := make([]*table2.JobInstanceCu, 0)
	for _, ji := range data {
		jobRunningInfo := &table5.JobRunningInfo{}
		err := util.ScanMapIntoStruct(jobRunningInfo, ji)
		if err != nil {
			logger.Errorf("Failed to scan map into struct: %+v", ji)
			return []*table2.JobInstanceCu{}, err
		}
		var tmTotalRunningCPU float32 = 0
		var tmTotalRunningMem float32 = 0
		var jmTotalRunningCPU float32 = 0
		var jmTotalRunningMem float32 = 0
		// 兼容老的 浮点数不能直接==0.
		if jobRunningInfo.JobManagerMem < constants.FLOAT_TOLERATE || jobRunningInfo.JobManagerCpu < constants.FLOAT_TOLERATE || jobRunningInfo.TaskManagerCpu < constants.FLOAT_TOLERATE || jobRunningInfo.TaskManagerMem < constants.FLOAT_TOLERATE {
			// tm
			if jobRunningInfo.ExpertModeConfiguration != "" && jobRunningInfo.ExpertModeConfiguration != "{}" {
				// 老版本sql高级配置没有TmTotalCpu和TmTotalMem，按tm规格算
				if jobRunningInfo.TmTotalCpu != 0 && jobRunningInfo.TmTotalMem != 0 {
					// 新版本高级配置作业
					tmTotalRunningCPU = jobRunningInfo.TmTotalCpu
					tmTotalRunningMem = jobRunningInfo.TmTotalMem
				} else {
					// 老版本sql高级配置没有TmTotalCpu和TmTotalMem，按tm规格算
					tmTotalRunningMem = jobRunningInfo.TmCuSpec * float32(cuMem) * float32(jobRunningInfo.TmRunningCuNum)
					tmTotalRunningCPU = jobRunningInfo.TmCuSpec * float32(jobRunningInfo.TmRunningCuNum)
				}
			} else {
				tmTotalRunningMem = jobRunningInfo.TmCuSpec * float32(cuMem) * float32(jobRunningInfo.TmRunningCuNum)
				tmTotalRunningCPU = jobRunningInfo.TmCuSpec * float32(jobRunningInfo.TmRunningCuNum)
			}
			// jm
			jmTotalRunningCPU = jobRunningInfo.JmCuSpec * float32(jobRunningInfo.JmRunningCuNum)
			jmTotalRunningMem = jobRunningInfo.JmCuSpec * float32(cuMem) * float32(jobRunningInfo.JmRunningCuNum)
		} else {
			if jobRunningInfo.ExpertModeConfiguration != "" && jobRunningInfo.ExpertModeConfiguration != "{}" {
				if jobRunningInfo.TmTotalCpu != 0 && jobRunningInfo.TmTotalMem != 0 {
					// 新版本高级配置作业
					tmTotalRunningCPU = jobRunningInfo.TmTotalCpu
					tmTotalRunningMem = jobRunningInfo.TmTotalMem
				} else {
					// 老版本sql高级配置没有TmTotalCpu和TmTotalMem，按tm规格算
					tmTotalRunningCPU = jobRunningInfo.TaskManagerCpu * float32(jobRunningInfo.TmRunningCuNum)
					tmTotalRunningMem = jobRunningInfo.TaskManagerMem * float32(jobRunningInfo.TmRunningCuNum)
				}
			} else {
				tmTotalRunningCPU = jobRunningInfo.TaskManagerCpu * float32(jobRunningInfo.TmRunningCuNum)
				tmTotalRunningMem = jobRunningInfo.TaskManagerMem * float32(jobRunningInfo.TmRunningCuNum)
			}
			jmTotalRunningCPU = jobRunningInfo.JobManagerCpu * float32(jobRunningInfo.JmRunningCuNum)
			jmTotalRunningMem = jobRunningInfo.JobManagerMem * float32(jobRunningInfo.JmRunningCuNum)
		}
		var totalRunningCPU = tmTotalRunningCPU + jmTotalRunningCPU
		var totalRunningMem = tmTotalRunningMem + jmTotalRunningMem
		totalCu := GetCuNumFromCpuMem(totalRunningCPU, totalRunningMem, cuMem)
		jobInstanceCus = append(jobInstanceCus, &table2.JobInstanceCu{
			Id:      jobRunningInfo.JobInstanceId,
			TotalCu: totalCu,
		})

	}
	return jobInstanceCus, nil
}

func GetJobRunningCPUAndMem(job *table.Job) (float32, float32, float32, float32, error) {
	arr := make([]*table.Job, 1)
	arr[0] = job
	jc, jm, tc, tm, err := GetClusterJobRunningCPUAndMem(arr)
	if err != nil {
		logger.Errorf("Failed to GetClusterJobRunningCPUAndMem for %d because %+v", job.SerialId, err)
		return 0, 0, 0, 0, err
	}
	jmc := GetFloat2Dot(jc)
	jmm := GetFloat2Dot(jm)
	tmc := GetFloat2Dot(tc)
	tmm := GetFloat2Dot(tm)
	return jmc, jmm, tmc, tmm, nil
}

func GetFloat2Dot(from float32) float32 {
	to, _ := decimal.NewFromFloat32(from).Round(2).Float64()
	return float32(to)
}

func GetClusterEachJobCpuAndMem(jobs []*table.Job) (JobRunningInfos map[int64]table5.JobRunningInfo, err error) {
	defer errorcode.DefaultDeferHandler(&err)

	JobRunningInfos = make(map[int64]table5.JobRunningInfo)
	if len(jobs) == 0 {
		return
	}
	var jobIds []int64
	var publishedJobConfigIds []int64
	tempMap := make(map[int64]int8)
	for _, job := range jobs {
		jobIds = append(jobIds, job.Id)
		publishedJobConfigIds = append(publishedJobConfigIds, job.PublishedJobConfigId)
		tempMap[job.Id] = job.CuMem
	}

	sql := "select a.TmRunningCuNum,a.JmRunningCuNum,a.TmTotalCpu,a.TmTotalMem,b.* from" +
		" JobInstance a join JobConfig b on b.Id = a.JobConfigId"

	cond := dao.NewCondition()
	cond.In("a.JobId", jobIds)
	cond.In("a.JobConfigId", publishedJobConfigIds)
	cond.Eq("a.Status", constants.JOB_INSTANCE_STATUS_RUNNING)
	where, args := cond.GetWhere()
	sql += where

	_, data, err := service2.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to get job:%v JobRunningInfo,err: %+v", jobs, err)
		return
	}
	if len(data) == 0 {
		return
	}

	for _, d := range data {
		jobRunningInfo := &table5.JobRunningInfo{}
		err := util.ScanMapIntoStruct(jobRunningInfo, d)
		if err != nil {
			logger.Errorf("Failed to scan map into struct: %+v", d)
			return JobRunningInfos, err
		}
		var tmTotalRunningCPU float32 = 0
		var tmTotalRunningMem float32 = 0
		var jmTotalRunningCPU float32 = 0
		var jmTotalRunningMem float32 = 0
		// 兼容老的 浮点数不能直接==0.
		if jobRunningInfo.JobManagerMem < constants.FLOAT_TOLERATE || jobRunningInfo.JobManagerCpu < constants.FLOAT_TOLERATE || jobRunningInfo.TaskManagerCpu < constants.FLOAT_TOLERATE || jobRunningInfo.TaskManagerMem < constants.FLOAT_TOLERATE {
			// tm
			if jobRunningInfo.ExpertModeConfiguration != "" && jobRunningInfo.ExpertModeConfiguration != "{}" {
				// 老版本sql高级配置没有TmTotalCpu和TmTotalMem，按tm规格算
				if jobRunningInfo.TmTotalCpu != 0 && jobRunningInfo.TmTotalMem != 0 {
					// 新版本高级配置作业
					tmTotalRunningCPU = jobRunningInfo.TmTotalCpu
					tmTotalRunningMem = jobRunningInfo.TmTotalMem
				} else {
					// 老版本sql高级配置没有TmTotalCpu和TmTotalMem，按tm规格算
					tmTotalRunningMem = jobRunningInfo.TmCuSpec * float32(tempMap[jobRunningInfo.JobId]) * float32(jobRunningInfo.TmRunningCuNum)
					tmTotalRunningCPU = jobRunningInfo.TmCuSpec * float32(jobRunningInfo.TmRunningCuNum)
				}
			} else {
				tmTotalRunningMem = jobRunningInfo.TmCuSpec * float32(tempMap[jobRunningInfo.JobId]) * float32(jobRunningInfo.TmRunningCuNum)
				tmTotalRunningCPU = jobRunningInfo.TmCuSpec * float32(jobRunningInfo.TmRunningCuNum)
			}
			// jm
			jmTotalRunningCPU = jobRunningInfo.JmCuSpec * float32(jobRunningInfo.JmRunningCuNum)
			jmTotalRunningMem = jobRunningInfo.JmCuSpec * float32(tempMap[jobRunningInfo.JobId]) * float32(jobRunningInfo.JmRunningCuNum)
		} else {
			if jobRunningInfo.ExpertModeConfiguration != "" && jobRunningInfo.ExpertModeConfiguration != "{}" {
				if jobRunningInfo.TmTotalCpu != 0 && jobRunningInfo.TmTotalMem != 0 {
					// 新版本高级配置作业
					tmTotalRunningCPU = jobRunningInfo.TmTotalCpu
					tmTotalRunningMem = jobRunningInfo.TmTotalMem
				} else {
					// 老版本sql高级配置没有TmTotalCpu和TmTotalMem，按tm规格算
					tmTotalRunningCPU = jobRunningInfo.TaskManagerCpu * float32(jobRunningInfo.TmRunningCuNum)
					tmTotalRunningMem = jobRunningInfo.TaskManagerMem * float32(jobRunningInfo.TmRunningCuNum)
				}
			} else {
				tmTotalRunningCPU = jobRunningInfo.TaskManagerCpu * float32(jobRunningInfo.TmRunningCuNum)
				tmTotalRunningMem = jobRunningInfo.TaskManagerMem * float32(jobRunningInfo.TmRunningCuNum)
			}
			jmTotalRunningCPU = jobRunningInfo.JobManagerCpu * float32(jobRunningInfo.JmRunningCuNum)
			jmTotalRunningMem = jobRunningInfo.JobManagerMem * float32(jobRunningInfo.JmRunningCuNum)
		}

		jobRunningInfo.TaskManagerCpu = tmTotalRunningCPU
		jobRunningInfo.TaskManagerMem = tmTotalRunningMem
		jobRunningInfo.JobManagerMem = jmTotalRunningMem
		jobRunningInfo.JobManagerCpu = jmTotalRunningCPU
		JobRunningInfos[jobRunningInfo.JobId] = *jobRunningInfo
	}
	return JobRunningInfos, nil
}

// 获取一批作业的总 TmRunningCuNum 数, 避免 for 循环调用导致的查询性能问题
func GetClusterJobRunningCPUAndMem(jobs []*table.Job) (jmTotalRunningCPU, jmTotalRunningMem, tmTotalRunningCPU, tmTotalRunningMem float32, err error) {
	defer errorcode.DefaultDeferHandler(&err)

	JobRunningInfos, err := GetClusterEachJobCpuAndMem(jobs)
	if err != nil {
		logger.Errorf("Failed to get cluster different job cpu and mem, with error:%+v", err)
		return 0, 0, 0, 0, err
	}
	for _, jobRunningInfo := range JobRunningInfos {
		jmTotalRunningCPU += jobRunningInfo.JobManagerCpu
		jmTotalRunningMem += jobRunningInfo.JobManagerMem
		tmTotalRunningCPU += jobRunningInfo.TaskManagerCpu
		tmTotalRunningMem += jobRunningInfo.TaskManagerMem
	}

	return GetFloat2Dot(jmTotalRunningCPU), GetFloat2Dot(jmTotalRunningMem), GetFloat2Dot(tmTotalRunningCPU), GetFloat2Dot(tmTotalRunningMem), nil
}

func GetClusterOperateJobRunningCPUAndMem(jobs []*table.Job, memRatio int8) (jmTotalRunningCPU, jmTotalRunningMem, tmTotalRunningCPU, tmTotalRunningMem float32, err error) {
	defer errorcode.DefaultDeferHandler(&err)

	if len(jobs) == 0 {
		return 0, 0, 0, 0, nil
	}
	var jobIds []int64
	var publishedJobConfigIds []int64
	for _, job := range jobs {
		jobIds = append(jobIds, job.Id)
		publishedJobConfigIds = append(publishedJobConfigIds, job.PublishedJobConfigId)
	}
	sql := "select * FROM JobConfig "
	cond := dao.NewCondition()
	cond.In("JobId", jobIds)
	cond.In("Id", publishedJobConfigIds)
	where, args := cond.GetWhere()
	sql += where

	_, data, err := service2.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to get job:%v JobRunningInfo,err: %+v", jobs, err)
		return 0, 0, 0, 0, err
	}
	for _, d := range data {
		jobConfig := &table5.JobConfig{}
		err := util.ScanMapIntoStruct(jobConfig, d)
		if err != nil {
			logger.Errorf("Failed to scan map into struct: %+v", d)
			return 0, 0, 0, 0, err
		}
		slots := GetNumberOfTaskSlots(jobConfig)
		CheckJobConfigCPUAndMem(jobConfig, memRatio)
		tmTotalRunningCPU += jobConfig.TaskManagerCpu * float32(jobConfig.DefaultParallelism) / float32(slots)
		tmTotalRunningMem += jobConfig.TaskManagerMem * float32(jobConfig.DefaultParallelism) / float32(slots)
		jmTotalRunningCPU += jobConfig.JobManagerCpu
		jmTotalRunningMem += jobConfig.JobManagerMem
	}
	return float32(jmTotalRunningCPU), float32(jmTotalRunningMem), float32(tmTotalRunningCPU), float32(tmTotalRunningMem), nil
}

func CheckJobNameExisted(appId int32, region string, jobName string, itemSpaceId int64) (isPass bool, errorCode string, msg string) {
	listJobQuery := model.ListJobQuery{
		AppId:        appId,
		Regions:      []string{region},
		Names:        []string{jobName},
		ItemSpaceIds: []int64{itemSpaceId},
		IsVagueNames: false,
		Offset:       0,
		Limit:        0,
	}
	jobs, err := ListJobs(&listJobQuery)
	if err != nil {
		return false, controller.InternalError, err.Error()
	}
	if len(jobs) > 1 {
		msg := fmt.Sprintf("Logic error? Found %d other than 1 job for AppId: %d, JobName: %s", len(jobs), appId, jobName)
		return false, controller.InternalError, msg
	} else if len(jobs) == 1 {
		msg := fmt.Sprintf("JobName: `%s` has existed", jobName)
		return false, controller.FailedOperation_DuplicatedJobName, msg
	}
	return true, "", ""
}

func FindJobBySerialID(serialId string) (*table.Job, error) {
	listJobQuery := model.ListJobQuery{
		AppId:        0,
		SerialIds:    []string{serialId},
		IsVagueNames: false,
		Offset:       0,
		Limit:        0,
	}
	jobs, err := ListJobs(&listJobQuery)
	if err != nil {
		return nil, err
	}

	if len(jobs) != 1 {
		return nil, errorcode.ResourceNotFound_Job.ReplaceDesc(fmt.Sprintf("%s not found", serialId))
	}
	return jobs[0], nil
}

func ListClusterGroupsBySerialIds(serialIds []string) (clusterGroups []*table4.ClusterGroup, err error) {
	clusterGroups = make([]*table4.ClusterGroup, 0)
	if len(serialIds) < 1 {
		return clusterGroups, nil
	}
	var sql = "SELECT * FROM ClusterGroup"
	cond := dao.NewCondition()
	if len(serialIds) > 0 {
		serialIds = UniqueSliceString(serialIds)
		cond.In("SerialId", serialIds)
	}

	where, args := cond.GetWhere()
	sql += where

	txManager := service2.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return clusterGroups, err
	}

	for i := 0; i < len(data); i++ {
		clusterGroup := &table4.ClusterGroup{}
		err = util.ScanMapIntoStruct(clusterGroup, data[i])
		if err != nil {
			return clusterGroups, errorcode.InternalErrorCode_UnMarshalFailed.NewWithErr(err)
		}
		clusterGroups = append(clusterGroups, clusterGroup)
	}

	return clusterGroups, nil
}

type ListClusterGroupsParam struct {
	AppId              int32
	Regions            []string
	Zone               string
	ClusterType        []int8
	ClusterNames       []string
	IsVagueNames       bool
	SerialIds          []string
	Offset             int
	Limit              int
	OrderType          int
	StatusList         []int
	PayModeList        []int // 0 后付费,1 预付费, 2包销
	DeploymentModeList []int // 0 单可用区 1多可用区
}

func GetListClusterGroupsSql(listClusterGroupsParam *ListClusterGroupsParam) (string, []interface{}) {
	var sql = "SELECT cg.* FROM ClusterGroup cg "
	if len(listClusterGroupsParam.PayModeList) == 1 {
		sql = "SELECT cg.* FROM ClusterGroup cg LEFT JOIN BillingResource br ON cg.SerialId = br.ResourceId "
	}
	cond := dao.NewCondition()

	if listClusterGroupsParam.AppId > 0 {
		cond.Eq("cg.AppId", listClusterGroupsParam.AppId)
	}

	if len(listClusterGroupsParam.Regions) > 0 {
		cond.In("cg.Region", listClusterGroupsParam.Regions)
	}

	// paymode 0 后付费,1 预付费
	// 0 的话 CuNum = 0 1 不是
	if len(listClusterGroupsParam.PayModeList) == 1 {
		if listClusterGroupsParam.PayModeList[0] == billing.PayModePost {
			cond.Eq("br.PayMode", billing.PayModePost)
		}
		if listClusterGroupsParam.PayModeList[0] == billing.PayModePrepaid {
			cond.Eq("br.PayMode", billing.PayModePrepaid)
		}
		// 包销
		if listClusterGroupsParam.PayModeList[0] == 2 {
			cond.Eq("br.BillingResourceMode", "exclusiveSale")
		}
	}

	if len(listClusterGroupsParam.DeploymentModeList) > 0 {
		cond.In("cg.DeploymentMode", listClusterGroupsParam.DeploymentModeList)
	}

	if len(listClusterGroupsParam.StatusList) == 0 {
		listClusterGroupsParam.StatusList = []int{
			constants.CLUSTER_GROUP_STATUS_CREATING,
			constants.CLUSTER_GROUP_STATUS_RUNNING,
			constants.CLUSTER_GROUP_STATUS_INIT_PROGRESS,
			constants.CLUSTER_GROUP_STATUS_SCALE_PROGRESS,
			constants.CLUSTER_GROUP_STATUS_ISOLATED,
			constants.CLUSTER_GROUP_STATUS_SCALEDOWN_PROGRESS,

			constants.CLUSTER_GROUP_STATUS_UPGRADE,

			constants.CLUSTER_GROUP_STATUS_RECOVERING,
		}
	}
	cond.In("cg.Status", listClusterGroupsParam.StatusList)

	if listClusterGroupsParam.Zone != "" {
		cond.Eq("cg.Zone", listClusterGroupsParam.Zone)
	}
	if len(listClusterGroupsParam.ClusterType) > 0 {
		cond.In("cg.Type", listClusterGroupsParam.ClusterType)
	}

	if len(listClusterGroupsParam.ClusterNames) > 0 {
		if listClusterGroupsParam.IsVagueNames {
			likeCond := dao.NewCondition().EnableOr()
			for i := 0; i < len(listClusterGroupsParam.ClusterNames); i++ {
				likeCond.Like("cg.Name", "%"+listClusterGroupsParam.ClusterNames[i]+"%")
			}

			cond.Condition(likeCond)
		} else {
			cond.In("cg.Name", listClusterGroupsParam.ClusterNames)
		}
	}
	if len(listClusterGroupsParam.SerialIds) > 0 {
		listClusterGroupsParam.SerialIds = UniqueSliceString(listClusterGroupsParam.SerialIds)
		cond.In("cg.SerialId", listClusterGroupsParam.SerialIds)
	}

	where, args := cond.GetWhere()
	sql += where
	return sql, args
}

func ListClusterGroups(listClusterGroupsParam *ListClusterGroupsParam) (clusterGroups []*table4.ClusterGroup, err error) {

	sql, args := GetListClusterGroupsSql(listClusterGroupsParam)

	switch listClusterGroupsParam.OrderType {
	case constants.CLUSTER_INFO_ORDERBY_CTREATETIME_DESC:
		sql += " ORDER BY cg.CreateTime DESC "
	case constants.CLUSTER_INFO_ORDERBY_CTREATETIME_ASC:
		sql += " ORDER BY cg.CreateTime ASC "
	case constants.CLUSTER_INFO_ORDERBY_STATUS:
		sql += " ORDER BY cg.Status DESC "
	default:
		sql += " ORDER BY cg.CreateTime DESC "
	}

	if listClusterGroupsParam.Limit > 0 {
		sql += " LIMIT ?, ?"
		args = append(args, listClusterGroupsParam.Offset)
		args = append(args, listClusterGroupsParam.Limit)
	} else {
		sql += " LIMIT ?, ?"
		args = append(args, listClusterGroupsParam.Offset)
		args = append(args, -1)
	}

	txManager := service2.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return clusterGroups, err
	}
	clusterGroups = make([]*table4.ClusterGroup, 0)
	for i := 0; i < len(data); i++ {
		clusterGroup := &table4.ClusterGroup{}
		err = util.ScanMapIntoStruct(clusterGroup, data[i])
		if err != nil {
			return clusterGroups, errorcode.InternalErrorCode_UnMarshalFailed.NewWithErr(err)
		}
		clusterGroups = append(clusterGroups, clusterGroup)
	}

	return clusterGroups, nil
}

func CheckClusterIdWithAppId(clusterId string, appId int64) error {
	var sql = "SELECT * FROM ClusterGroup"
	cond := dao.NewCondition()
	cond.Eq("AppId", appId)
	cond.Eq("SerialId", clusterId)
	where, args := cond.GetWhere()
	sql += where
	txManager := service2.GetTxManager()
	count, _, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return err
	}
	if count == 1 {
		return nil
	} else {
		return errorcode.ResourceNotFound_Cluster.New()
	}
}

func CheckUniqClusterIdWithAppId(uniqClusterId string, appId int64) error {
	var sql = "SELECT * FROM ClusterGroup where Id = (SELECT ClusterGroupId FROM Cluster where UniqClusterId = ?) AND AppId = ?"
	args := make([]interface{}, 0)
	args = append(args, uniqClusterId)
	args = append(args, appId)
	txManager := service2.GetTxManager()
	count, _, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return err
	}
	if count == 1 {
		return nil
	} else {
		return errorcode.ResourceNotFound_Cluster.New()
	}
}

func CheckJobIdWithAppId(jobId string, appId int64) error {
	var sql = "SELECT * FROM Job"
	cond := dao.NewCondition()
	cond.Eq("AppId", appId)
	cond.Eq("SerialId", jobId)
	where, args := cond.GetWhere()
	sql += where
	txManager := service2.GetTxManager()
	count, _, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return err
	}
	if count == 1 {
		return nil
	} else {
		return errorcode.ResourceNotFound_Job.New()
	}
}

func FindJobByApplicationID(applicationId string) (*table.Job, error) {
	var sql string
	args := make([]interface{}, 0)
	sql = "SELECT Job.* FROM `JobInstance`, Job WHERE JobInstance.JobId = Job.Id AND JobInstance.ApplicationId = ?"
	args = append(args, applicationId)

	txManager := service2.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Error("Failed to query with sql:", sql, ", errors:", err.Error())
		return &table.Job{}, err
	}

	t := make([]*table.Job, len(data))
	for i := 0; i < len(data); i++ {
		temp := &table.Job{}
		_ = util.ScanMapIntoStruct(temp, data[i])
		t[i] = temp
	}

	if len(t) == 0 {
		logger.Error("Failed to query job with applicationId ", applicationId, " because it does not exist")
		return &table.Job{}, errors.New("job does not exist")
	}

	if len(t) > 1 {
		logger.Error("Failed to query job with applicationId ", applicationId, " because multiple jobs are found")
		return &table.Job{}, errors.New("multiple jobs are found")
	}
	return t[0], nil
}

func ListJobs(query *model.ListJobQuery) ([]*table.Job, error) {

	sql := "SELECT j.* FROM Job j INNER JOIN ClusterGroup cg ON j.ClusterGroupId = cg.Id"
	orderBy := ""
	if len(query.OrderByList) > 0 {
		for i := 0; i < len(query.OrderByList); i++ {
			by := query.OrderByList[i]
			orderBy += fmt.Sprintf("%s %s", by.Field, by.Type)
			if i != len(query.OrderByList)-1 {
				orderBy += " , "
			}
		}
	}
	if orderBy != "" && strings.Contains(orderBy, constants.JOB_ORDER_BY_CURRENTRUNMILLIS) {
		sql = "SELECT j.*, TIMESTAMPDIFF(MICROSECOND, j.StartTime, now()) as CurrentRunMillis FROM Job j INNER JOIN ClusterGroup cg ON j.ClusterGroupId = cg.Id"
	}
	cond := dao.NewCondition()

	cond.Ne("j.Status", constants.JOB_STATUS_DELETE)

	if query.AppId > 0 {
		cond.Eq("j.AppId", query.AppId)
	}

	if len(query.Regions) > 0 {
		cond.In("j.Region", query.Regions)
	}

	if len(query.SerialIds) > 0 {
		serialIds := UniqueSliceString(query.SerialIds)
		cond.In("j.SerialId", serialIds)
	}

	if len(query.JobIds) > 0 {
		jobIds := UniqueSliceInt64(query.JobIds)
		cond.In("j.Id", jobIds)
	}

	if len(query.ItemSpaceIds) > 1 || (len(query.ItemSpaceIds) == 1 && query.ItemSpaceIds[0] != 0) {
		ItemSpaceIds := UniqueSliceInt64(query.ItemSpaceIds)
		cond.In("j.ItemSpaceId", ItemSpaceIds)
	}

	if len(query.Names) > 0 {
		names := UniqueSliceString(query.Names)
		if query.IsVagueNames {
			likeCond := dao.NewCondition().EnableOr()

			for i := 0; i < len(names); i++ {
				likeCond.Like("j.Name", "%"+names[i]+"%")
			}
			cond.Condition(likeCond)
		} else {
			cond.In("j.Name", names)
		}
	}
	if len(query.Status) > 0 {
		cond.In("j.Status", query.Status)
	}
	if len(query.Type) > 0 {
		cond.In("j.Type", query.Type)
	}

	if len(query.ManageType) > 0 {
		cond.Eq("j.ManageType", query.ManageType)
	}

	if query.FlinkJobType > 0 {
		cond.Eq("j.FlinkJobType", query.FlinkJobType)
	}

	if len(query.ClusterGroupIds) > 0 {
		exclusiveClusterGroupIds, hasShareCluster := ClassifyClusterGroupIds(query.ClusterGroupIds)
		if hasShareCluster && len(exclusiveClusterGroupIds) > 0 { // 若同时过滤独享集群及共享集群作业
			cond.Condition(dao.NewCondition().
				EnableOr().
				Eq("cg.Type", constants.CLUSTER_GROUP_TYPE_SHARED).
				In("cg.SerialId", exclusiveClusterGroupIds))
		} else if hasShareCluster { // 若仅仅过滤共享集群作业
			cond.Eq("cg.Type", constants.CLUSTER_GROUP_TYPE_SHARED)
		} else if !hasShareCluster { // 若仅仅过滤独享集群作业
			cond.In("cg.SerialId", exclusiveClusterGroupIds)
		}
	}

	if len(query.ClusterGroupNames) > 0 {
		clusterGroupNames := UniqueSliceString(query.ClusterGroupNames)
		if query.IsVagueNames {
			likeCond := dao.NewCondition().EnableOr()

			for i := 0; i < len(clusterGroupNames); i++ {
				likeCond.Like("cg.Name", "%"+clusterGroupNames[i]+"%")
			}
			cond.Condition(likeCond)
		} else {
			cond.In("cg.Name", clusterGroupNames)
		}
	}

	where, args := cond.GetWhere()
	sql += where

	if orderBy != "" {
		sql += fmt.Sprintf(" ORDER BY %s, j.CreateTime DESC ", orderBy)
	} else {
		sql += " ORDER BY j.CreateTime DESC "
	}
	if query.Limit > 0 {
		sql += " LIMIT ?, ?"
		args = append(args, query.Offset)
		args = append(args, query.Limit)
	} else {
		sql += " LIMIT ?, ?"
		args = append(args, query.Offset)
		// 默认返回20条，临时设置为全部返回
		args = append(args, -1)
	}

	txManager := service2.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return nil, err
	}

	jobs := make([]*table.Job, 0)
	for i := 0; i < len(data); i++ {
		job := &table.Job{}
		err = util.ScanMapIntoStruct(job, data[i])
		if err != nil {
			return nil, errorcode.InternalErrorCode_UnMarshalFailed.NewWithErr(err)
		}
		jobs = append(jobs, job)
	}

	return jobs, nil
}

func ListJobConfigByQuery(query *model.ListJobQuery) (*table5.JobConfig, error) {
	sql := "SELECT jc.* FROM Job j INNER JOIN JobConfig jc ON j.Id = jc.JobId"
	cond := dao.NewCondition()

	cond.Ne("j.Status", constants.JOB_STATUS_DELETE)
	cond.Ne("jc.VersionId", constants.JOB_CONFIG_DRAFT_VERSION)
	if query.AppId > 0 {
		cond.Eq("j.AppId", query.AppId)
	}

	if len(query.Regions) > 0 {
		cond.In("j.Region", query.Regions)
	}

	if len(query.SerialId) > 0 {
		cond.Eq("j.SerialId", query.SerialId)
	}

	if len(query.ManageType) > 0 {
		cond.Eq("j.ManageType", query.ManageType)
	}
	where, args := cond.GetWhere()
	sql += where

	sql += " ORDER BY jc.CreateTime DESC limit 1 "
	txManager := service2.GetTxManager()

	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return nil, err
	}

	if len(data) != 1 {
		return nil, nil
	}

	jobConfig := &table5.JobConfig{}
	err = util.ScanMapIntoStruct(jobConfig, data[0])
	if err != nil {
		return nil, errorcode.InternalErrorCode_UnMarshalFailed.NewWithErr(err)
	}
	return jobConfig, nil
}

func ListDraftJobConfigquery(SerialId string) (*table5.JobConfig, error) {
	sql := "SELECT jc.* FROM Job j INNER JOIN JobConfig jc ON j.Id = jc.JobId"
	cond := dao.NewCondition()

	cond.Ne("j.Status", constants.JOB_STATUS_DELETE)
	cond.Eq("jc.VersionId", constants.JOB_CONFIG_DRAFT_VERSION)
	cond.Eq("j.SerialId", SerialId)

	where, args := cond.GetWhere()
	sql += where

	sql += " ORDER BY jc.CreateTime DESC limit 1 "
	txManager := service2.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return nil, err
	}

	if len(data) != 1 {
		return nil, nil
	}

	jobConfig := &table5.JobConfig{}
	err = util.ScanMapIntoStruct(jobConfig, data[0])
	if err != nil {
		return nil, errorcode.InternalErrorCode_UnMarshalFailed.NewWithErr(err)
	}
	return jobConfig, nil
}

/*
*
对ClusterGroupId(实际是SerialId)分成两类：--, 独享集群ClusterId
*/
func ClassifyClusterGroupIds(clusterGroupIds []string) (exclusiveClusterGroupIds []string, hasShareCluster bool) {
	exclusiveClusterGroupIds = make([]string, 0)
	for _, value := range clusterGroupIds {
		if value == constants.DOUBLE_BAR {
			hasShareCluster = true
		} else {
			exclusiveClusterGroupIds = append(exclusiveClusterGroupIds, value)
		}
	}
	return exclusiveClusterGroupIds, hasShareCluster
}

func ListJobInstancesNotInStatus(jobId int64, status int8) ([]*table2.JobInstance, error) {
	sql := "SELECT * FROM JobInstance WHERE jobId=? AND status!=?"
	args := make([]interface{}, 0)
	args = append(args, jobId)
	args = append(args, status)

	txManager := service2.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Exception occurs when query JobInstance from db, with errors:%+v", err)
		return nil, err
	}

	jobInstances := make([]*table2.JobInstance, 0)
	for i := 0; i < len(data); i++ {
		jobInstance := &table2.JobInstance{}
		err = util.ScanMapIntoStruct(jobInstance, data[i])
		if err != nil {
			logger.Errorf("Failed to convert bytes into table.JobInstance, with errors:%+v", err)
			return []*table2.JobInstance{}, err
		}
		jobInstances = append(jobInstances, jobInstance)
	}

	return jobInstances, nil
}

func ListJobInstances(jobId int64, status int8) ([]*table2.JobInstance, error) {
	sql := "SELECT * FROM JobInstance WHERE jobId=? AND status=?"
	args := make([]interface{}, 0)
	args = append(args, jobId)
	args = append(args, status)

	txManager := service2.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Exception occurs when query JobInstance from db, with errors:%+v", err)
		return nil, err
	}

	jobInstances := make([]*table2.JobInstance, 0)
	for i := 0; i < len(data); i++ {
		jobInstance := &table2.JobInstance{}
		err = util.ScanMapIntoStruct(jobInstance, data[i])
		if err != nil {
			logger.Errorf("Failed to convert bytes into table.JobInstance, with errors:%+v", err)
			return []*table2.JobInstance{}, err
		}
		jobInstances = append(jobInstances, jobInstance)
	}

	return jobInstances, nil
}

func ListJobInstancesBySerialIds(serialIds []string) (t map[string][]*table2.JobInstance, err error) {

	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("GetJobInstance panic, errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	sql := "select a.*, b.serialId from JobInstance as a, Job as b where a.jobId = b.id and  b.serialId in( "
	args := make([]interface{}, 0)
	for i := 0; i < len(serialIds); i++ {
		sql = sql + "?"
		if i != len(serialIds)-1 {
			sql = sql + ","
		}
		args = append(args, serialIds[i])
	}
	sql = sql + ")  order by (a.CreateTime) desc"
	txManager := service2.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to get job instances, with errors:%+v", err)
		return map[string][]*table2.JobInstance{}, nil
	}
	t = map[string][]*table2.JobInstance{}
	for i := 0; i < len(data); i++ {
		jobInstance := &table2.JobInstance{}
		err := util.ScanMapIntoStruct(jobInstance, data[i])
		if err != nil {
			logger.Errorf("Failed to transfer job instance into struct, with errors:%+v", err)
			return map[string][]*table2.JobInstance{}, nil
		}
		jobSerialId := string(data[i]["serialId"])
		if _, ok := t[jobSerialId]; !ok {
			jobInstances := make([]*table2.JobInstance, 0)
			jobInstances = append(jobInstances, jobInstance)
			t[jobSerialId] = jobInstances
		} else {
			jobInstances := t[jobSerialId]
			jobInstances = append(jobInstances, jobInstance)
			t[jobSerialId] = jobInstances
		}
	}
	return t, nil
}

func ListJobInstanceByRunningOrderId(jobId int64, runningOrderId []int64) ([]*table2.JobInstance, error) {
	args := make([]interface{}, 0, 1+len(runningOrderId))
	sql := "SELECT * FROM JobInstance WHERE jobId=?"
	args = append(args, jobId)

	if len(runningOrderId) > 0 {
		questions := make([]string, 0, len(runningOrderId))
		for _, id := range runningOrderId {
			questions = append(questions, "?")
			args = append(args, id)
		}
		sql += fmt.Sprintf(" AND RunningOrderId in (%s)", strings.Join(questions, ","))
	}

	_, data, err := service2.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return nil, err
	}

	jobInstanceList := make([]*table2.JobInstance, 0)
	for i := 0; i < len(data); i++ {
		jobInstance := &table2.JobInstance{}
		err := util.ScanMapIntoStruct(jobInstance, data[i])
		if err != nil {
			return nil, errorcode.InternalErrorCode_UnMarshalFailed.NewWithErr(err)
		}
		jobInstanceList = append(jobInstanceList, jobInstance)
	}
	return jobInstanceList, nil
}

func GetSqlPlaceholder(obj interface{}) (placeholder string, args []interface{}, err error) {
	if reflect.TypeOf(obj).Kind() != reflect.Slice {
		return "", nil, errorcode.InternalErrorCode_TypeIncompatible.New()
	}
	s := reflect.ValueOf(obj)
	placeholderList := make([]string, s.Len())
	args = make([]interface{}, 0, s.Len())
	for i := 0; i < s.Len(); i++ {
		placeholderList[i] = "?"
		args = append(args, s.Index(i).Interface())
	}
	return strings.Join(placeholderList, ","), args, nil
}

func GetJobInstanceByJobInstanceId(jobId int64, jobInstanceId int64) (*table2.JobInstance, error) {
	sql := "SELECT * FROM JobInstance WHERE jobId=? AND id=?"
	args := make([]interface{}, 0)
	args = append(args, jobId)
	args = append(args, jobInstanceId)

	txManager := service2.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Exception occurs when query JobInstance from db, with errors:%+v", err)
		return nil, err
	}

	jobInstances := make([]*table2.JobInstance, 0)
	for i := 0; i < len(data); i++ {
		jobInstance := &table2.JobInstance{}
		err = util.ScanMapIntoStruct(jobInstance, data[i])
		if err != nil {
			logger.Errorf("Failed to convert bytes into table.JobInstance, with errors:%+v", err)
			return nil, err
		}
		jobInstances = append(jobInstances, jobInstance)
	}
	if len(jobInstances) != 1 {
		logger.Errorf(" GetJobInstanceByJobInstanceId sql: %s, args: %+v cnt(%d) != 1", sql, args, len(data))
		err = fmt.Errorf(" GetJobInstanceByJobInstanceId found jobinstance error, cnt(%d) != 1", len(data))
		return nil, err
	}
	return jobInstances[0], nil
}

func GetJobInstanceByJobRuntimeId(jobInstanceId int64) (*table2.JobInstance, error) {
	sql := "SELECT * FROM JobInstance WHERE id=?"
	args := make([]interface{}, 0)
	args = append(args, jobInstanceId)

	txManager := service2.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Exception occurs when query JobInstance from db, with errors:%+v", err)
		return nil, err
	}

	jobInstances := make([]*table2.JobInstance, 0)
	for i := 0; i < len(data); i++ {
		jobInstance := &table2.JobInstance{}
		err = util.ScanMapIntoStruct(jobInstance, data[i])
		if err != nil {
			logger.Errorf("Failed to convert bytes into table.JobInstance, with errors:%+v", err)
			return nil, err
		}
		jobInstances = append(jobInstances, jobInstance)
	}
	if len(jobInstances) != 1 {
		logger.Errorf(" GetJobInstanceByJobRuntimeId sql: %s, args: %+v cnt(%d) != 1", sql, args, len(data))
		err = fmt.Errorf(" GetJobInstanceByJobInstanceId found jobinstance error, cnt(%d) != 1", len(data))
		return nil, err
	}
	return jobInstances[0], nil
}

func GetJobInstanceByJobInstanceIds(jobInstanceIds []int64) ([]*model.VersionIdRsp, error) {
	if len(jobInstanceIds) == 0 {
		return nil, errors.New("jobInstanceIds is nil")
	}
	args := make([]interface{}, 0, 0)
	sql := "SELECT JobInstance.Id,JobConfig.VersionId FROM JobInstance,JobConfig WHERE JobConfig.Id = JobInstance.JobConfigId "
	questions := make([]string, 0, len(jobInstanceIds))
	for _, id := range jobInstanceIds {
		questions = append(questions, "?")
		args = append(args, id)
	}
	sql += fmt.Sprintf("AND JobInstance.Id IN (%s)", strings.Join(questions, ","))
	_, data, err := service2.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return nil, err
	}
	VersionIdList := make([]*model.VersionIdRsp, 0)
	for i := 0; i < len(data); i++ {
		VersionId := &model.VersionIdRsp{}
		err := util.ScanMapIntoStruct(VersionId, data[i])
		if err != nil {
			return nil, errorcode.InternalErrorCode_UnMarshalFailed.NewWithErr(err)
		}
		VersionIdList = append(VersionIdList, VersionId)
	}
	return VersionIdList, nil
}

func GetJobConfigBySerialIdAndVersionId(jobSerialId string, jobConfigVersionId int16) (t *table5.JobConfig, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("GetJobConfigByVersionId panic ,errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	sql := "SELECT jc.* FROM JobConfig jc  INNER JOIN Job j ON j.Id = jc.JobId "
	cond := dao.NewCondition()
	cond.Eq("j.SerialId", jobSerialId)
	cond.Eq("jc.VersionId", jobConfigVersionId)
	where, args := cond.GetWhere()
	sql += where
	_, data, err := service2.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Error("Failed to query with sql:", sql, ", errors:", err.Error())
		return t, err
	}
	if len(data) == 0 {
		logger.Errorf("not find job config, with jobSerialId:%s and job config version id:%d", jobSerialId, jobConfigVersionId)
		return t, errors.New("not find Job Config")
	}
	for _, val := range data {
		result := &table5.JobConfig{}
		util.ScanMapIntoStruct(result, val)
		t = result
	}
	return t, nil
}

func GetJobConfigByVersionId(jobId int64, jobConfigVersionId int64) (t *table5.JobConfig, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("GetJobConfigByVersionId panic ,errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	sql := "SELECT * FROM JobConfig "
	cond := dao.NewCondition()
	cond.Eq("JobId", jobId)
	cond.Eq("VersionId", jobConfigVersionId)
	where, args := cond.GetWhere()
	sql += where
	_, data, err := service2.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Error("Failed to query with sql:", sql, ", errors:", err.Error())
		return t, err
	}
	if len(data) == 0 {
		logger.Errorf("not find job config, with job:%d and job config version id:%d", jobId, jobConfigVersionId)
		return t, errors.New("not find Job Config")
	}
	for _, val := range data {
		result := &table5.JobConfig{}
		util.ScanMapIntoStruct(result, val)
		t = result
	}
	return t, nil
}

func GetJobConfigByJobInstanceId(jobId int64, jobInstanceId int64) (t *table5.JobConfig, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("GetJobConfig panic ,errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	//sql := "SELECT * FROM JobConfig where Id = (select JobConfigId from JobInstance where Id =)"

	sql := "SELECT jf.* FROM JobConfig jf join JobInstance ji on ji.JobConfigId=jf.Id where ji.Id = ? "
	args := make([]interface{}, 0)
	args = append(args, jobInstanceId)
	_, data, err := service2.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Error("Failed to query with sql:", sql, ", errors:", err.Error())
		return t, err
	}
	if len(data) == 0 {
		logger.Errorf("not find job config, with jobId:%d, with jobInstanceId:%d", jobId, jobInstanceId)
		return t, errors.New("not find Job Config")
	}
	val := data[0]
	result := &table5.JobConfig{}
	util.ScanMapIntoStruct(result, val)
	return result, nil
}

func GetJobConfigByIds(jobConfigIds []int64) (t []*table5.JobConfig, err error) {
	if len(jobConfigIds) < 1 {
		logger.Errorf("GetJobConfigByIds failed, params[jobConfigIds] length is 0")
		return t, errors.New("GetJobConfigByIds failed, params[jobConfigIds] length is 0")
	}
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("GetJobConfig panic ,errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	sql := "SELECT * FROM JobConfig "
	cond := dao.NewCondition()
	cond.In("id", jobConfigIds)
	where, args := cond.GetWhere()
	sql += where
	_, data, err := service2.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Error("Failed to query with sql:", sql, ", errors:", err.Error())
		return t, err
	}
	if len(data) == 0 {
		logger.Errorf("not find job config, with job serialId:%s, with job config id:%d", args)
		return t, errors.New("not find Job Config")
	}
	for _, val := range data {
		result := &table5.JobConfig{}
		util.ScanMapIntoStruct(result, val)
		t = append(t, result)
	}
	return t, nil
}

func WhetherJobExists(appId int32, region string, serialId string) (*table.Job, error) {
	// 支持传入空字符串, 以忽略 region 过滤条件
	var regions []string
	if len(region) == 0 {
		regions = nil
	} else {
		regions = []string{region}
	}
	listJobQuery := model.ListJobQuery{
		AppId:        appId,
		Regions:      regions,
		SerialIds:    []string{serialId},
		IsVagueNames: false,
		Offset:       0,
		Limit:        0,
	}
	jobs, err := ListJobs(&listJobQuery)
	if err != nil {
		return nil, err
	} else if len(jobs) > 1 {
		return nil, errorcode.InternalErrorCode_UnexpectedRecordNums.NewWithInfo(serialId, nil)
	} else if len(jobs) == 0 {
		msg := fmt.Sprintf("JobNotFound: Job with appId: %d, regionId: %s, serialId: %s", appId, region, serialId)
		return nil, errorcode.ResourceNotFound_Job.ReplaceDesc(msg)
	}
	return jobs[0], nil
}

/*
*
对SQL类型的作业，检查StartMode是否合规
*/
func CheckStartMode(startMode string) error {
	re, _ := regexp.Match("(?i)LATEST|EARLIEST|T1\\d{12}", []byte(startMode))
	if !re {
		msg := fmt.Sprintf("Invalid startMode: `%s`, only LATEST, EARLIEST, T+Timestamp like T1557394288000 is allowed", startMode)
		return errors.New(msg)
	}

	return nil
}

/*
*
检查CheckpointInterval是否合规
*/
func CheckCheckpointIntervalValid(checkpointInterval int64) error {
	if checkpointInterval > 0 && checkpointInterval < constants.CHECKPOINT_INTERVAL_LOWER_LIMIT {
		return errors.New(fmt.Sprintf("Invalid CheckpointInterval, can be <= 0 or >= %d", constants.CHECKPOINT_INTERVAL_LOWER_LIMIT))
	}

	return nil
}
func WhiteListCheckCheckpointIntervalValid(checkpointInterval int64) error {
	if checkpointInterval > 0 && checkpointInterval < constants.WHITE_LIST_CHECKPOINT_INTERVAL_LOWER_LIMIT {
		return errors.New(fmt.Sprintf("Invalid CheckpointInterval, can be <= 0 or >= %d", constants.CHECKPOINT_INTERVAL_LOWER_LIMIT))
	}
	return nil
}

func GetCurrentRunMillis(startTime string) (int64, error) {
	if startTime == "0000-00-00 00:00:00" {
		logger.Warningf("GetCurrentRunMillis_startTime is 0000-00-00 00:00:00, return 0")
		return 0, nil
	}
	timeLayout := "2006-01-02 15:04:05"
	loc, _ := time.LoadLocation("Local")
	formatTime, err := time.ParseInLocation(timeLayout, startTime, loc)
	if err != nil {
		logger.Errorf("Failed to parse startTime: %s", startTime)
		return -1, err
	}

	// Fixme 国际化时考虑时区问题
	startTimeStamp := formatTime.Unix()
	currentRunMillis := util.GetNowTimestamp() - startTimeStamp*1000

	return currentRunMillis, nil
}

func SwitchDefaultTime(time string) (newTime string) {
	if time == "0000-00-00 00:00:00" {
		newTime = "-"
		return
	}
	newTime = time
	return
}

func UpdateJob(jobId int64, publishedJobConfigId int64, lastPublishedJobConfigId int64, tx *dao.Transaction, publishedFlinkVersion string) (int64, error) {
	// 更新最新配置Id/已发布配置Id 为 newLatestJobConfigId，更新最新配置版本号为 (lastLatestJobConfigVersionId + 1)
	result := tx.ExecuteSqlWithArgs("UPDATE Job SET publishedJobConfigId=?,  lastPublishedJobConfigId = ?, FlinkVersion=? "+
		" WHERE id=? ", publishedJobConfigId, lastPublishedJobConfigId, publishedFlinkVersion, jobId)
	return result.RowsAffected()
}

func UpdaetJobConfigFlinkVersionByJobId(jobConfigId int64, flinkVersion string) (err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("UpdateJobConfig FlinkVersion panic, errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	sql := "UPDATE JobConfig SET FlinkVersion = ? WHERE Id = ? and FlinkVersion = '' "
	args := make([]interface{}, 0)
	args = append(args, flinkVersion)
	args = append(args, jobConfigId)
	txManager := service2.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		rs := tx.ExecuteSql(sql, args)
		_, err := rs.RowsAffected()
		if err != nil {
			logger.Errorf("Failed to update JobConfig FlinkVersion , with sql: %s, with args:%+v", sql, args)
			return err
		}
		return nil
	}).Close()
	return err
}

func UpdateJobConfigFromDraft(jobId int64, jobConfigId int64, tx *dao.Transaction) (int64, error) {
	sql := "UPDATE JobConfig jc1,  ( SELECT EntrypointClass, ProgramArgs, CheckpointInterval, SqlCode  FROM JobConfig   WHERE JobId = ? and VersionId = -1 ) jc2 " +
		"set jc1.EntrypointClass = jc2.EntrypointClass, jc1.ProgramArgs = jc2.ProgramArgs, jc1.CheckpointInterval = jc2.CheckpointInterval, jc1.SqlCode = jc2.SqlCode, jc1.UpdateTime=? where jc1.Id = ?"
	args := make([]interface{}, 0)
	args = append(args, jobId)
	args = append(args, util.GetCurrentTime())
	args = append(args, jobConfigId)

	result := tx.ExecuteSql(sql, args)
	return result.RowsAffected()
}

func UpdateJobConfig(jobId int64, jobConfigVersion int64, jobConfigId int64, status int, tx *dao.Transaction) (int64, error) {
	sql := "UPDATE JobConfig set Status=? "
	args := make([]interface{}, 0)
	args = append(args, status)
	if status == constants.JOB_CONFIG_STATUS_ON_PUBLISHED {
		sql += ", UpdateTime=? "
		args = append(args, util.GetCurrentTime())
	}
	sql += "where Status !=? "
	args = append(args, constants.JOB_CONFIG_STATUS_DELETE)
	if jobId != 0 {
		sql += " and jobId=?"
		args = append(args, jobId)
	}
	if jobConfigVersion != 0 {
		sql += " and versionId = ? "
		args = append(args, jobConfigVersion)
	}
	if jobConfigId != 0 {
		sql += " and id = ? "
		args = append(args, jobConfigId)
	}
	result := tx.ExecuteSql(sql, args)
	return result.RowsAffected()
}

func UpdateJobConfigProperties(jobId int64, jobConfigVersion int64, jobConfigId int64, properties string, tx *dao.Transaction) (int64, error) {
	sql := "UPDATE JobConfig set Properties= ? "
	args := make([]interface{}, 0)
	args = append(args, properties)
	sql += "where Status !=? "
	args = append(args, constants.JOB_CONFIG_STATUS_DELETE)
	if jobId != 0 {
		sql += " and jobId=?"
		args = append(args, jobId)
	}
	if jobConfigVersion != 0 {
		sql += " and versionId = ? "
		args = append(args, jobConfigVersion)
	}
	if jobConfigId != 0 {
		sql += " and id = ? "
		args = append(args, jobConfigId)
	}
	result := tx.ExecuteSql(sql, args)
	return result.RowsAffected()
}

func CheckVpcSubnetExist(vpcId string, subnet string, region, secretId, secretKey, token string) (exits bool, err error) {

	isDev := service2.GetConfStringValue("scsDevEnv")
	if isDev == "true" {
		logger.Warningf("Dev environment detected, no VPC / subnet check is enforced.")
		return true, nil
	}

	cpf := profile.NewClientProfile()
	cpf.HttpProfile.ReqMethod = "POST"
	cpf.HttpProfile.ReqTimeout = constants.HTTP_PROFILE_REQ_TIMEOUT
	cpf.SignMethod = "HmacSHA1"

	credential := common.NewTokenCredential(secretId, secretKey, token)

	client, err := vpc.NewClient(credential, region, cpf)

	request := vpc.NewDescribeSubnetsRequest()
	request.SubnetIds = []*string{&subnet}

	request.SetDomain("vpc.internal.tencentcloudapi.com")
	response, err := client.DescribeSubnets(request)

	if err != nil {
		logger.Errorf("CheckVpcSubnetExist DescribeSubnets error: %+v", err)
		return exits, err
	}

	if len(response.Response.SubnetSet) != 1 {
		errMsg := fmt.Sprintf("CheckVpcSubnetExist %s result SubnetSet != 1, len is %d, requestId is %s", subnet, len(response.Response.SubnetSet), *response.Response.RequestId)
		logger.Errorf(errMsg)
		err = fmt.Errorf(errMsg)
		return exits, err
	}

	// 因为只有一个，所以无所谓, 一个子网对应一个VPC
	for _, value := range response.Response.SubnetSet {
		vpcIdRemote := fmt.Sprintf("%+v", *value.VpcId)
		if !strings.EqualFold(vpcIdRemote, vpcId) {
			return exits, err
		}
	}

	exits = true
	return exits, err
}

// 调用sts assumeRole的专用函数、需要先进行地域转换
func SwitchRegion(r string) (region string) {

	// 不考虑金融区和open区, Sts AssumeRole 接口目前不支持的地域有武汉、福州、长沙、深圳、石家庄, 需要转到就近地域
	switch r {
	case constants.WUHAN:
		region = constants.CHONGQING
	case constants.FUZHOU:
		region = constants.SHANGHAI
	case constants.CHANGSHA:
		region = constants.GUANGZHOU
	case constants.SHENZHEN:
		region = constants.GUANGZHOU
	case constants.SHIJIAZHUANG:
		region = constants.BEIJING
	case constants.TAIPEI:
		region = constants.HONGKONG
	default:
		region = r
	}
	if region != r {
		logger.Warningf("switch region %s to %s", r, region)
	}
	return
}

func StsAssumeRoleWithSubAccountUinByNetEnvType(netEnvType int, ownerUin, subAccountUin, region string) (tmpSecretId, tmpSecretKey, token string,
	pass bool, err error) {
	isDev := service2.GetConfStringValue("scsDevEnv")

	secretId := ""
	secretKey := ""
	// 如果是测试环境，进行StsAssumeRole的时候需要的是线上环境的sid和skey，所以需要进行兼容转换
	// 如果是正式环境，则直接使用sid和skey即可
	if isDev == "true" {
		secretId, secretKey, err = GetSecretIdAndKeyOfOnline()
	} else {
		secretId, secretKey, err = GetSecretIdAndKeyByNetworkEnvType(int8(netEnvType))
	}
	if err != nil {
		logger.Errorf("StsAssumeRole GetScsSecretKey error: %+v", err)
		return tmpSecretId, tmpSecretKey, token, pass, err
	}

	cpf := profile.NewClientProfile()
	cpf.HttpProfile.ReqMethod = "POST"
	cpf.HttpProfile.ReqTimeout = constants.HTTP_PROFILE_REQ_TIMEOUT
	cpf.SignMethod = "HmacSHA1"

	region = SwitchRegion(region)

	credential := common.NewCredential(secretId, secretKey)
	client, err := sts.NewClient(credential, region, cpf)
	if err != nil {
		logger.Errorf("StsAssumeRole sts.NewClient error: %+v", err)
		return tmpSecretId, tmpSecretKey, token, pass, err
	}

	roleArn := fmt.Sprintf("qcs::cam::uin/%s:roleName/%s", ownerUin, "Oceanus_QCSRole")
	roleSessionName := "Oceanus"

	request := sts.NewAssumeRoleRequest()
	request.RoleArn = &roleArn
	request.RoleSessionName = &roleSessionName

	if subAccountUin != "" {
		userUin, err := strconv.ParseUint(subAccountUin, 10, 64)
		if err != nil {
			fmt.Printf("strconv.ParseInt subAccountUin %s error %+v", subAccountUin, err)
		}
		request.UserUin = &userUin
	}

	request.SetDomain("sts.internal.tencentcloudapi.com")

	response, err := client.AssumeRole(request)
	if err != nil {
		logger.Errorf("StsAssumeRole AssumeRole error: %+v", err)
		return tmpSecretId, tmpSecretKey, token, pass, err
	}

	tmpSecretId = *response.Response.Credentials.TmpSecretId
	tmpSecretKey = *response.Response.Credentials.TmpSecretKey
	token = *response.Response.Credentials.Token
	pass = true
	responseStr, _ := json.Marshal(response)
	logger.Debug(string(responseStr))

	// 如果角色没有授权，err会发生异常
	return tmpSecretId, tmpSecretKey, token, pass, err
}

func StsAssumeRoleWithSubAccountUin(ownerUin, subAccountUin, region string) (tmpSecretId, tmpSecretKey, token string,
	pass bool, err error) {
	isDev := service2.GetConfStringValue("scsDevEnv")

	secretId := ""
	secretKey := ""
	// 如果是测试环境，进行StsAssumeRole的时候需要的是线上环境的sid和skey，所以需要进行兼容转换
	// 如果是正式环境，则直接使用sid和skey即可
	if isDev == "true" {
		secretId, secretKey, err = GetSecretIdAndKeyOfOnline()
	} else {
		secretId, secretKey, err = GetSecretIdAndKeyOfScs()
	}
	if err != nil {
		logger.Errorf("StsAssumeRole GetScsSecretKey error: %+v", err)
		return tmpSecretId, tmpSecretKey, token, pass, err
	}

	cpf := profile.NewClientProfile()
	cpf.HttpProfile.ReqMethod = "POST"
	cpf.HttpProfile.ReqTimeout = constants.HTTP_PROFILE_REQ_TIMEOUT
	cpf.SignMethod = "HmacSHA1"

	region = SwitchRegion(region)

	credential := common.NewCredential(secretId, secretKey)
	client, err := sts.NewClient(credential, region, cpf)
	if err != nil {
		logger.Errorf("StsAssumeRole sts.NewClient error: %+v", err)
		return tmpSecretId, tmpSecretKey, token, pass, err
	}

	roleArn := fmt.Sprintf("qcs::cam::uin/%s:roleName/%s", ownerUin, "Oceanus_QCSRole")
	roleSessionName := "Oceanus"

	request := sts.NewAssumeRoleRequest()
	request.RoleArn = &roleArn
	request.RoleSessionName = &roleSessionName
	request.SourceIdentity = &subAccountUin
	request.SetDomain("sts.internal.tencentcloudapi.com")

	response, err := client.AssumeRole(request)
	if err != nil {
		logger.Errorf("StsAssumeRole AssumeRole error: %+v", err)
		return tmpSecretId, tmpSecretKey, token, pass, err
	}

	tmpSecretId = *response.Response.Credentials.TmpSecretId
	tmpSecretKey = *response.Response.Credentials.TmpSecretKey
	token = *response.Response.Credentials.Token
	pass = true
	responseStr, _ := json.Marshal(response)
	logger.Debug(string(responseStr))

	// 如果角色没有授权，err会发生异常
	return tmpSecretId, tmpSecretKey, token, pass, err
}

func StsAssumeRoleByNetEnvType(netEnvType int, ownerUin, subAccountUin, region string) (tmpSecretId, tmpSecretKey, token string, pass bool, err error) {

	isDev := service2.GetConfStringValue("scsDevEnv")
	secretId := ""
	secretKey := ""
	// 如果是测试环境，进行StsAssumeRole的时候需要的是线上环境的sid和skey，所以需要进行兼容转换
	// 如果是正式环境，则直接使用sid和skey即可
	if isDev == "true" {
		secretId, secretKey, err = GetSecretIdAndKeyOfOnline()
	} else {
		secretId, secretKey, err = GetSecretIdAndKeyByNetworkEnvType(int8(netEnvType))
	}
	if err != nil {
		return tmpSecretId, tmpSecretKey, token, pass, err
	}

	cpf := profile.NewClientProfile()
	cpf.HttpProfile.ReqMethod = "POST"
	cpf.HttpProfile.ReqTimeout = constants.HTTP_PROFILE_REQ_TIMEOUT
	cpf.SignMethod = "HmacSHA1"

	region = SwitchRegion(region)

	credential := common.NewCredential(secretId, secretKey)
	client, err := sts.NewClient(credential, region, cpf)
	if err != nil {
		return tmpSecretId, tmpSecretKey, token, pass, errorcode.AuthFailure_UnauthorizedOperation.NewWithErr(err)
	}

	roleArn := fmt.Sprintf("qcs::cam::uin/%s:roleName/%s", ownerUin, "Oceanus_QCSRole")
	roleSessionName := "Oceanus"

	request := sts.NewAssumeRoleRequest()
	request.RoleArn = &roleArn
	request.RoleSessionName = &roleSessionName
	request.SourceIdentity = &subAccountUin
	request.SetDomain("sts.internal.tencentcloudapi.com")

	response, err := client.AssumeRole(request)
	if err != nil {
		return tmpSecretId, tmpSecretKey, token, pass, errorcode.AuthFailure_UnauthorizedOperation.NewWithErr(err)
	}

	tmpSecretId = *response.Response.Credentials.TmpSecretId
	tmpSecretKey = *response.Response.Credentials.TmpSecretKey
	token = *response.Response.Credentials.Token
	pass = true
	responseStr, _ := json.Marshal(response)
	logger.Debug(string(responseStr))

	// 如果角色没有授权，err会发生异常
	return tmpSecretId, tmpSecretKey, token, pass, nil
}

func StsAssumeRole(ownerUin, subAccountUin, region string) (tmpSecretId, tmpSecretKey, token string, pass bool, err error) {

	isDev := service2.GetConfStringValue("scsDevEnv")
	secretId := ""
	secretKey := ""
	// 如果是测试环境，进行StsAssumeRole的时候需要的是线上环境的sid和skey，所以需要进行兼容转换
	// 如果是正式环境，则直接使用sid和skey即可
	if isDev == "true" {
		secretId, secretKey, err = GetSecretIdAndKeyOfOnline()
	} else {
		secretId, secretKey, err = GetSecretIdAndKeyOfScs()
	}
	if err != nil {
		return tmpSecretId, tmpSecretKey, token, pass, err
	}

	cpf := profile.NewClientProfile()
	cpf.HttpProfile.ReqMethod = "POST"
	cpf.HttpProfile.ReqTimeout = constants.HTTP_PROFILE_REQ_TIMEOUT
	cpf.SignMethod = "HmacSHA1"

	region = SwitchRegion(region)

	credential := common.NewCredential(secretId, secretKey)
	client, err := sts.NewClient(credential, region, cpf)
	if err != nil {
		return tmpSecretId, tmpSecretKey, token, pass, errorcode.AuthFailure_UnauthorizedOperation.NewWithErr(err)
	}

	roleArn := fmt.Sprintf("qcs::cam::uin/%s:roleName/%s", ownerUin, "Oceanus_QCSRole")
	roleSessionName := "Oceanus"

	request := sts.NewAssumeRoleRequest()
	request.RoleArn = &roleArn
	request.RoleSessionName = &roleSessionName
	request.SourceIdentity = &subAccountUin
	request.SetDomain("sts.internal.tencentcloudapi.com")

	response, err := client.AssumeRole(request)
	if err != nil {
		logger.Errorf("StsAssumeRole error: %+v", err)
		if response != nil {
			b, _ := json.Marshal(response)
			logger.Errorf("StsAssumeRole error response: %+v", string(b))
		}
		return tmpSecretId, tmpSecretKey, token, pass, errorcode.AuthFailure_UnauthorizedOperation.NewWithErr(err)
	}

	tmpSecretId = *response.Response.Credentials.TmpSecretId
	tmpSecretKey = *response.Response.Credentials.TmpSecretKey
	token = *response.Response.Credentials.Token
	pass = true
	//responseStr, _ := json.Marshal(response)
	//logger.Debug(string(responseStr))

	// 如果角色没有授权，err会发生异常
	return tmpSecretId, tmpSecretKey, token, pass, nil
}

func StsAssumeRoleWithAuth(secretId, secretKey, ownerUin, subAccountUin, region string, duration uint64, policy string) (
	tmpSecretId, tmpSecretKey, token string, expiredTime int64, err error) {
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.ReqMethod = "POST"
	cpf.HttpProfile.ReqTimeout = constants.HTTP_PROFILE_REQ_TIMEOUT
	cpf.SignMethod = "HmacSHA1"

	region = SwitchRegion(region)

	credential := common.NewCredential(secretId, secretKey)
	client, err := sts.NewClient(credential, region, cpf)
	if err != nil {
		logger.Errorf("StsAssumeRole sts.NewClient error: %+v", err)
		return tmpSecretId, tmpSecretKey, token, 0, err
	}

	roleArn := fmt.Sprintf("qcs::cam::uin/%s:roleName/%s", ownerUin, "Oceanus_QCSRole")
	roleSessionName := "Oceanus"

	request := sts.NewAssumeRoleRequest()
	request.RoleArn = &roleArn
	request.RoleSessionName = &roleSessionName
	request.SourceIdentity = &subAccountUin

	request.SetDomain("sts.internal.tencentcloudapi.com")
	request.DurationSeconds = &duration
	if policy != "" {
		request.Policy = &policy
	}

	response, err := client.AssumeRole(request)
	if err != nil {
		logger.Errorf("StsAssumeRole AssumeRole error: %+v", err)
		return tmpSecretId, tmpSecretKey, token, 0, err
	}

	tmpSecretId = *response.Response.Credentials.TmpSecretId
	tmpSecretKey = *response.Response.Credentials.TmpSecretKey
	token = *response.Response.Credentials.Token
	expiredTime = *response.Response.ExpiredTime

	logger.Infof("aussme role: %s", *response.Response.RequestId)

	return tmpSecretId, tmpSecretKey, token, expiredTime, err
}

func StsAssumeRoleReturnExpiredTime(ownerUin, subAccountUin, region string, duration uint64) (
	tmpSecretId, tmpSecretKey, token string, expiredTime int64, err error) {

	secretId, secretKey, err := GetSecretIdAndKey()
	if err != nil {
		logger.Errorf("StsAssumeRoleReturnExpiredTime GetSecretIdAndKey error: %+v", err)
		return "", "", "", 0, err
	}

	return StsAssumeRoleWithAuth(secretId, secretKey, ownerUin, subAccountUin, region, duration, "")
}

func IsDraftConfig(configId int64) (isDraft bool, jobConfig *table5.JobConfig, err error) {
	args := make([]interface{}, 0)
	args = append(args, configId)
	args = append(args, constants.JOB_CONFIG_DRAFT_VERSION)

	sql := "SELECT * FROM JobConfig WHERE Id=? AND VersionId=?"
	cnt, data, err := service2.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("IsDraftConfig sql: %s args: %+v error: %+v", sql, args, err)
		return isDraft, jobConfig, err
	}

	if cnt != 1 {
		return isDraft, jobConfig, err
	}

	isDraft = true
	jobConfig = &table5.JobConfig{}
	util.ScanMapIntoStruct(jobConfig, data[0])

	return isDraft, jobConfig, err
}

func IsTceEnv() bool {
	isTce := service2.GetConfStringValue("scsTceEnv")
	if isTce == "true" {
		return true
	} else {
		return false
	}
}

/*
*

	是否是内部用户, 这里的内部用户指的是走本地逻辑提交任务的用户
*/
func IsInnerUser(appId int32, uin, subAccountUin string) (flag bool, err error) {
	// 跳过智能监控问题
	inner, err := service3.GetInnerUserTransitions(appId, uin, subAccountUin)
	if err != nil {
		logger.Errorf("Failed to list InnerUserTransitions, error:%+v", err)
		return flag, err
	}

	if len(inner) > 0 {
		logger.Info("Inner User appId %d uin %s subUin %s", appId, uin, subAccountUin)
		flag = true
		return flag, err
	}
	return flag, err
}

func IsContainStr(items []string, item string) bool {
	for _, eachItem := range items {
		if eachItem == item {
			return true
		}
	}
	return false
}

func GetOceanusCloudApiUin() (uin string, err error) {
	isDev := service2.GetConfStringValue("scsDevEnv")
	if isDev == "true" {
		uin, err = GetOceanusCloudApiUinOfOnline()
	} else {
		uin, err = GetOceanusCloudApiUinOfScs()
	}
	if err != nil {
		logger.Errorf("GetOceanusCloudApiUin error: %+v", err)
		return "", err
	}

	return uin, nil
}

func GetOceanusCloudApiUinOfOnline() (uin string, err error) {
	if uin, err = service.GetOceanusCloudApiUinOfOnline(); err != nil {
		return "", err
	}
	return uin, nil
}

func GetOceanusCloudApiUinOfScs() (uin string, err error) {
	if uin, err = service.GetOceanusCloudApiUinOfScs(); err != nil {
		return "", err
	}
	return uin, nil
}

func GetSecretIdAndKey() (secretId, secretKey string, err error) {
	isDev := service2.GetConfStringValue("scsDevEnv")
	if isDev == "true" {
		secretId, secretKey, err = GetSecretIdAndKeyOfOnline()
	} else {
		secretId, secretKey, err = GetSecretIdAndKeyOfScs()
	}
	if err != nil {
		logger.Errorf("GetSecretIdAndKey error: %+v", err)
		return "", "", err
	}

	return secretId, secretKey, nil
}

func GetSecretIdAndKeyOfScs() (secretId, secretKey string, err error) {
	if secretId, err = service.GetSecretIdOfScs(); err != nil {
		return "", "", err
	}
	if secretKey, err = service.GetSecretKeyOfScs(); err != nil {
		return "", "", err
	}
	return secretId, secretKey, nil
}

func GetSecretIdAndKeyOfCos() (secretId, secretKey string, err error) {
	if secretId, err = service.GetSecretIdOfCos(); err != nil {
		return "", "", err
	}
	if secretKey, err = service.GetSecretKeyOfCos(); err != nil {
		return "", "", err
	}
	return secretId, secretKey, nil
}

func GetSubAccountSecretIdAndKey() (secretId, secretKey string, err error) {
	isDev := service2.GetConfStringValue("scsDevEnv")
	if isDev == "true" {
		secretId, secretKey, err = GetSubAccountSecretIdAndKeyOfOnline()
	} else {
		secretId, secretKey, err = GetSubAccountSecretIdAndKeyOfScs()
	}
	if err != nil {
		logger.Errorf("GetSecretIdAndKey error: %+v", err)
		return "", "", err
	}

	return secretId, secretKey, nil
}

func GetSubAccountSecretIdAndKeyOfScs() (secretId, secretKey string, err error) {
	if secretId, err = service.GetSubAccountSecretIdOfScs(); err != nil {
		return "", "", err
	}
	if secretKey, err = service.GetSubAccountSecretKeyOfScs(); err != nil {
		return "", "", err
	}
	return secretId, secretKey, nil
}

func GetSubAccountSecretIdAndKeyOfOnline() (secretId, secretKey string, err error) {
	if secretId, err = service.GetSubAccountSecretIdOfOnline(); err != nil {
		return "", "", err
	}
	if secretKey, err = service.GetSubAccountSecretKeyOfOnline(); err != nil {
		return "", "", err
	}
	return secretId, secretKey, nil
}

func GetTmpSecretIdAndKeyOfCos(region, userUin, bucket string, userAppId int64) (tmpSecretId, tmpSecretKey, token string, err error) {
	logger.Debugf("TmpCosSecret_GetTmpSecretIdAndKeyOfCos with region %s userUin %s bucket %s userAppId %d", region, userUin, bucket, userAppId)
	secretId, secretKey, err := GetSecretIdAndKey()
	if err != nil {
		logger.Errorf("GetTmpSecretIdAndKeyOfCos GetSecretIdAndKey error: %+v", err)
		return "", "", "", err
	}

	mainAppId, err := config.GetRainbowConfiguration("Common", "MainAppId")
	if err != nil {
		logger.Errorf("GetTmpSecretIdAndKeyOfCos Failed to get ConfigureCenter.Common.MainAppId from Rainbow because %+v. ", err)
		return "", "", "", err
	}

	ownerUin, err := config.GetRainbowConfiguration("Common", "OwnerUin")
	if err != nil {
		logger.Errorf("GetTmpSecretIdAndKeyOfCos Failed to get ConfigureCenter.Common.OwnerUin from Rainbow because %+v. ", err)
		return "", "", "", err
	}

	resources := make([]string, 0)
	resources = append(resources, fmt.Sprintf("qcs::cos:%s:uid/%s:%s/SystemResource/*", region, mainAppId, bucket))
	resources = append(resources, fmt.Sprintf("qcs::cos:%s:uid/%s:%s/%d/%s/*", region, mainAppId, bucket, userAppId, userUin))

	data := &struct {
		Region    string
		MainAppId string
		Bucket    string
		UserAppId int64
		UserUin   string
	}{
		Region:    region,
		MainAppId: mainAppId,
		Bucket:    bucket,
		UserAppId: userAppId,
		UserUin:   userUin,
	}
	var policy interface{}
	err = config.DecodeK8sObjectFromRainbowConfigTemplate(
		"Common", "ReadOnlyCosPolicy.json", data, &policy)
	if err != nil {
		logger.Errorf("GetTmpSecretIdAndKeyOfCos DecodeK8sObjectFromRainbowConfigTemplate from Common.ReadOnlyCosPolicy.json with data %+v err : %+v", data, err)
		return "", "", "", err
	}
	b, err := json.Marshal(policy)
	if err != nil {
		logger.Errorf("GetTmpSecretIdAndKeyOfCos json.Marshal(policy) from Common.ReadOnlyCosPolicy.json with data %+v err : %+v", data, err)
		return "", "", "", err
	}

	tmpSecretId, tmpSecretKey, token, _, err = StsAssumeRoleWithAuth(secretId, secretKey,
		ownerUin, ownerUin, region, constants.ASSUME_ROLE_EXPIRED_DURATION, string(b))
	if err != nil {
		logger.Errorf("GetTmpSecretIdAndKeyOfCos StsAssumeRoleWithAuth err : %+v", err)
		return "", "", "", err
	}
	return tmpSecretId, tmpSecretKey, token, nil
}

func GetSecretIdAndKeyOfOnline() (secretId, secretKey string, err error) {
	if secretId, err = service.GetSecretIdOfOnline(); err != nil {
		return "", "", err
	}
	if secretKey, err = service.GetSecretKeyOfOnline(); err != nil {
		return "", "", err
	}
	return secretId, secretKey, nil
}

func GetSecretIdAndKeyOfTest() (secretId, secretKey string, err error) {
	if secretId, err = service.GetSecretIdOfTest(); err != nil {
		return "", "", err
	}
	if secretKey, err = service.GetSecretKeyOfTest(); err != nil {
		return "", "", err
	}
	return secretId, secretKey, nil
}

func GetSecretIdAndKeyOfInner() (secretId, secretKey string, err error) {
	if secretId, err = service.GetSecretIdInner(); err != nil {
		return "", "", err
	}
	if secretKey, err = service.GetSecretKeyOfInner(); err != nil {
		return "", "", err
	}
	return secretId, secretKey, nil
}

func GetSecretIdAndKeyByNetworkEnvType(networkEnvironmentType int8) (secretId, secretKey string, err error) {
	if networkEnvironmentType == constants.NETWORK_ENV_CLOUD_VPC {
		return GetSecretIdAndKeyOfScs()
	} else if networkEnvironmentType == constants.NETWORK_ENV_INNER_VPC {
		return GetSecretIdAndKeyOfInner()
	} else {
		return "", "", errorcode.NewStackError(errorcode.InternalErrorCode_UnknownNetType, fmt.Sprintf("unkonw network environment type %d", networkEnvironmentType), nil)
	}
}

// slice element should be struct/*struct, sort by field of struct
func SortSliceByField(slice interface{}, field string, desc bool) {
	rv := reflect.ValueOf(slice)
	et := rv.Type().Elem()

	sort.Slice(slice, func(i, j int) bool {
		ei := rv.Index(i)
		ej := rv.Index(j)

		if et.Kind() == reflect.Ptr {
			ei = reflect.Indirect(ei)
			ej = reflect.Indirect(ej)
		}
		fvi := ei.FieldByName(field)
		fvj := ej.FieldByName(field)

		switch fvi.Kind() {
		case reflect.Bool:
			if desc {
				return fvi.Bool() == true && fvj.Bool() == false
			} else {
				return fvi.Bool() == false && fvj.Bool() == true
			}
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			if desc {
				return fvi.Int() > fvj.Int()
			} else {
				return fvi.Int() < fvj.Int()
			}
		case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
			if desc {
				return fvi.Uint() > fvj.Uint()
			} else {
				return fvi.Uint() < fvj.Uint()
			}
		case reflect.Float32, reflect.Float64:
			if desc {
				return fvi.Float() > fvj.Float()
			} else {
				return fvi.Float() < fvj.Float()
			}
		case reflect.String:
			if desc {
				return strings.Compare(fvi.String(), fvj.String()) > 0
			} else {
				return strings.Compare(fvi.String(), fvj.String()) < 0
			}
		default:
			return desc
		}
	})
}

func UniqueSliceString(slice []string) []string {
	if len(slice) == 0 {
		return slice
	}

	m := make(map[string]struct{}, len(slice))
	for _, s := range slice {
		m[s] = struct{}{}
	}
	slice = make([]string, 0, len(m))
	for s, _ := range m {
		slice = append(slice, s)
	}
	return slice
}

func UniqueSliceInt64(slice []int64) []int64 {
	if len(slice) == 0 {
		return slice
	}

	m := make(map[int64]struct{}, len(slice))
	for _, s := range slice {
		m[s] = struct{}{}
	}
	slice = make([]int64, 0, len(m))
	for s, _ := range m {
		slice = append(slice, s)
	}
	return slice
}

func InSliceString(e string, slice []string) bool {
	if len(slice) == 0 {
		return false
	}
	for _, s := range slice {
		if e == s {
			return true
		}
	}
	return false
}

func InSliceFloat32(e float32, slice []float32) bool {
	if len(slice) == 0 {
		return false
	}
	for _, s := range slice {
		if e == s {
			return true
		}
	}
	return false
}

func SliceToSetString(slice []string) (set map[string]struct{}) {
	set = make(map[string]struct{}, len(slice))
	if len(slice) == 0 {
		return set
	}
	for _, s := range slice {
		set[s] = struct{}{}
	}
	return set
}

func CheckImageCompatible(image1 string, image2 string) (compatible bool, err error) {
	ivs1 := strings.Split(image1, ":")
	if len(ivs1) != 2 || len(ivs1[0]) == 0 || len(ivs1[1]) == 0 {
		msg := fmt.Sprintf("image version %s format error", image1)
		return false, errors.New(msg)
	}
	ivs2 := strings.Split(image2, ":")
	if len(ivs2) != 2 || len(ivs2[0]) == 0 || len(ivs2[1]) == 0 {
		msg := fmt.Sprintf("image version %s format error", image2)
		return false, errors.New(msg)
	}
	if ivs1[0] != ivs2[0] {
		return false, nil
	}
	// TODO check version part
	return true, nil
}

func ImageToVersion(imageVersion string) (version string, err error) {
	ivs := strings.Split(imageVersion, ":")
	if len(ivs) != 2 {
		msg := fmt.Sprintf("image version %s format error", imageVersion)
		return version, errorcode.InternalErrorCode_UnexpectedRecordNums.ReplaceDesc(msg)
	}
	version = ivs[1]
	return version, err
}

func ContainerCheck(container string, resource constants.Resource) error {
	for _, c := range resource.Containers {
		if c == container {
			return nil
		}
	}
	return errorcode.InternalErrorCode_NoContainerFound.ReplaceDesc("no container in resource")
}

func IsDevEnv() bool {
	isDev := service2.GetConfStringValue("scsDevEnv")
	return isDev == "true"
}

// AddDiagnosisVolumeMounted2Properties 高级参数加上DiagnosisVolumeMounted
func AddDiagnosisVolumeMounted2Properties(props string) (string, error) {
	properties := make([]*model3.Property, 0)
	if props != "" {
		err := json.Unmarshal([]byte(props), &properties)
		if err != nil {
			logger.Errorf("properties cannot Unmarshal to Property")
			return "", err
		}
	}
	var diagnosisVolumeMountedEnable bool
	var fixedDiagnosisVolume2Enable bool
	for _, property := range properties {
		if property.Key == constants.DIAGNOSIS_DATA_VOLUME_MOUNTED_EKY {
			if property.Value == constants.DIAGNOSIS_DATA_VOLUME_MOUNTED_VALUE {
				diagnosisVolumeMountedEnable = true
			} else {
				property.Value = constants.DIAGNOSIS_DATA_VOLUME_MOUNTED_VALUE
				fixedDiagnosisVolume2Enable = true
			}
		}
	}
	if diagnosisVolumeMountedEnable {
		return props, nil
	} else if fixedDiagnosisVolume2Enable {
		// 修正 DIAGNOSIS_DATA_VOLUME_MOUNTED_VALUE 参数的值
	} else {
		properties = append(properties, &model3.Property{
			Key:   constants.DIAGNOSIS_DATA_VOLUME_MOUNTED_EKY,
			Value: constants.DIAGNOSIS_DATA_VOLUME_MOUNTED_VALUE,
		})
	}
	marshalBytes, err := json.Marshal(properties)
	if err != nil {
		logger.Errorf("properties cannot Marshal to json")
		return "", err
	}
	return string(marshalBytes), nil
}

// RemoveDiagnosisVolumeMountedFromProperties 高级参数移除DiagnosisVolumeMounted
func RemoveDiagnosisVolumeMountedFromProperties(propsStr string) ([]*model3.Property, error) {
	logger.Debugf("origin properties is [%s]", propsStr)
	props := make([]*model3.Property, 0)
	if len(propsStr) != 0 {
		err := json.Unmarshal([]byte(propsStr), &props)
		if err != nil {
			return props, err
		}
	}
	// 对外隐藏DIAGNOSIS_DATA_VOLUME_MOUNTED参数
	var delIndex int
	var containsDiagnosisDataVolumeMountedKey bool
	for i, prop := range props {
		if prop.Key == constants.DIAGNOSIS_DATA_VOLUME_MOUNTED_EKY {
			delIndex = i
			containsDiagnosisDataVolumeMountedKey = true
		}
	}
	if containsDiagnosisDataVolumeMountedKey {
		props = append(props[:delIndex], props[delIndex+1:]...)
	}
	return props, nil
}

func CheckIsExistInSpace(clusterGroupId int64, ItemSpaceId int64, appid int64, region string) (err error) {

	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Update ItemSpace panic, with errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))

		}
	}()

	sql := "SELECT * FROM ItemSpacesClusters "
	cond := dao.NewCondition()

	if clusterGroupId != 0 {
		cond.Eq("ClusterGroupId", clusterGroupId)
	}
	if ItemSpaceId != 0 {
		cond.Eq("ItemSpaceId", ItemSpaceId)
	}

	cond.Eq("status", constants.ITEM_SPACE_CLUSTERGROUPS_STATUS_USEABLE)

	where, args := cond.GetWhere()
	sql += where
	txManager := service2.GetTxManager()
	_, datas, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return err
	}

	if len(datas) > 1 {
		logger.Errorf("Logic error ?  Multiple clusterGroup and itemSpace binding relationships the sql:%s, with args:%+v, by Appid: %v", sql, args, appid)
		err = errors.New("Logic error ? Multiple clusterGroup and itemSpace binding relationships ")
		return err
	}
	if len(datas) == 0 {
		itemCluster, _ := BuildItemSpacesClusters(appid, region, clusterGroupId)
		itemCluster.ItemSpaceId = ItemSpaceId
		txManager := service2.GetTxManager()
		txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
			tx.SaveObject(itemCluster, "ItemSpacesClusters")
			return nil
		}).Close()
	}
	return nil
}

func BuildItemSpacesClusters(appid int64, region string, clusterGroupId int64) (*tableItemSpace.ItemSpacesClusters, error) {
	itemSpacesClusters := &tableItemSpace.ItemSpacesClusters{}
	itemSpacesClusters.AppId = appid
	itemSpacesClusters.Region = region
	itemSpacesClusters.ClusterGroupId = clusterGroupId
	itemSpacesClusters.CreateTime = util.GetCurrentTime()
	itemSpacesClusters.UpdateTime = itemSpacesClusters.CreateTime
	itemSpacesClusters.Status = constants.ITEM_SPACE_CLUSTERGROUPS_STATUS_USEABLE
	return itemSpacesClusters, nil
}

func ExtractSubUinAuthoClusterGroups(AllGroupSerialIds []string, ItemSpaceId int64) (clusterGroupTable []*table4.ClusterGroup, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Get ClusterGroup table  panic, with errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	sql := "SELECT * from ClusterGroup WHERE Id IN ( SELECT ClusterGroupId from ItemSpacesClusters WHERE ItemSpaceId = ? AND `Status` != ? ) AND `Status` != ?"
	args := make([]interface{}, 0)
	args = append(args, ItemSpaceId)
	args = append(args, constants.ITEM_SPACE_CLUSTERGROUPS__DELETED)
	args = append(args, constants.CLUSTER_GROUP_STATUS_DELETED)
	txManager := service2.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to execute the sql:%s, with args:%+v, with errors:%+v", sql, args, err)
		return nil, err
	}

	clusterGroups := make([]*table4.ClusterGroup, 0)
	for i := 0; i < len(data); i++ {
		clusterGroup := &table4.ClusterGroup{}
		err = util.ScanMapIntoStruct(clusterGroup, data[i])
		if err != nil {
			return nil, errorcode.InternalErrorCode_UnMarshalFailed.NewWithErr(err)
		}
		if len(AllGroupSerialIds) > 0 {
			if ok, _ := Contain(clusterGroup.SerialId, AllGroupSerialIds); ok {
				clusterGroups = append(clusterGroups, clusterGroup)
			}
		} else {
			clusterGroups = append(clusterGroups, clusterGroup)
		}
	}

	return clusterGroups, nil
}

func QueryCluserItemSpaceCorrelation(itemSpaceIds []int64, clusterGroupSerialId []string, isUseable bool) (tables map[int64][]*tableItemSpace.ItemSpaceClusterItem, err error) {
	errorcode.DefaultDeferHandler(&err)
	sql := "SELECT a.ClusterGroupId ,b.`Name`as ClusterName,b.SerialId as ClusterGroupSerialId,c.SerialId  as WorkSpaceId,c.ItemSpaceName as WorkSpaceName ,a.`Status`, c.ProjectId FROM ItemSpacesClusters a INNER " +
		"JOIN ClusterGroup b ON a.ClusterGroupId = b.Id  INNER JOIN ItemSpace c ON c.Id = a.ItemSpaceId"

	cond := dao.NewCondition()
	if len(itemSpaceIds) > 0 {
		cond.In("a.ItemSpaceId", itemSpaceIds)
	}
	if len(clusterGroupSerialId) > 0 {
		cond.In("b.SerialId", clusterGroupSerialId)
	}

	if isUseable {
		cond.Ne("a.Status", constants.ITEM_SPACE_CLUSTERGROUPS__DELETED)
	}

	where, args := cond.GetWhere()
	sql += where + " ORDER BY c.ItemSpaceName desc "

	txManager := service2.GetTxManager()
	_, datas, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to execute the sql:%s, with args:%+v, with errors:%+v", sql, args, err)
		return tables, err
	}
	if len(datas) == 0 {
		return tables, nil
	}

	t1 := make(map[int64][]*tableItemSpace.ItemSpaceClusterItem, 0)
	for _, data := range datas {
		table := &tableItemSpace.ItemSpaceClusterItem{}
		err = util.ScanMapIntoStruct(table, data)
		if err != nil {
			return nil, errorcode.InternalErrorCode_UnMarshalFailed.NewWithErr(err)
		}
		table.ProjectIdStr = strconv.FormatInt(table.ProjectId, 10)
		t1[table.ClusterGroupId] = append(t1[table.ClusterGroupId], table)
	}
	//6、对结果进行按照字典顺序排序
	for _, value := range t1 {
		sort.Slice(value, func(i, j int) bool {
			return value[i].WorkSpaceName > value[j].WorkSpaceName
		})
	}
	return t1, nil
}

func Contain(obj interface{}, target interface{}) (bool, error) {
	targetValue := reflect.ValueOf(target)
	kind := reflect.TypeOf(target).Kind()
	switch kind {
	case reflect.Slice, reflect.Array:
		for i := 0; i < targetValue.Len(); i++ {
			if targetValue.Index(i).Interface() == obj {
				return true, nil
			}
		}
	case reflect.Map:
		if targetValue.MapIndex(reflect.ValueOf(obj)).IsValid() {
			return true, nil
		}
	}

	return false, nil
}

func ListToSet(list []string) []string {
	stringMap := make(map[string]bool, 0)
	for _, elem := range list {
		stringMap[elem] = false
	}
	sets := make([]string, 0, 0)
	for k, _ := range stringMap {
		sets = append(sets, k)
	}
	return sets
}

func ItemSpaceIdToSerialId(SerialId string, stat int64, appId int64) (items *tableItemSpace.ItemSpace, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Get ItemSpace table  panic, with errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	sql := " SELECT * from ItemSpace WHERE SerialId = ? AND AppId = ? AND `Status` = ? "
	args := make([]interface{}, 0)
	args = append(args, SerialId)
	args = append(args, appId)
	args = append(args, stat)

	txManager := service2.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to execute the sql:%s, with args:%+v, with errors:%+v", sql, args, err)
		return nil, err
	}

	if len(data) == 0 {
		logger.Errorf("Failed to execute the sql:%s, with args:%+v, with errors:%+v", sql, args, err)
		err = errors.New("Can not find ItemSpadeId ")
		return nil, err
	}

	itemSpace := &tableItemSpace.ItemSpace{}
	err = util.ScanMapIntoStruct(itemSpace, data[0])
	if err != nil {
		return nil, errorcode.InternalErrorCode_UnMarshalFailed.NewWithErr(err)
	}

	return itemSpace, nil
}

func SerialIdToItemSpaceId(ItemSpaceId int64, stat int64) (items *tableItemSpace.ItemSpace, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Get ItemSpace table  panic, with errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	sql := " SELECT * from ItemSpace WHERE Id = ? AND `Status` = ? "
	args := make([]interface{}, 0)
	args = append(args, ItemSpaceId)
	args = append(args, stat)

	txManager := service2.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to execute the sql:%s, with args:%+v, with errors:%+v", sql, args, err)
		return nil, err
	}

	if len(data) == 0 {
		logger.Errorf("Failed to execute the sql:%s, with args:%+v, with errors:%+v", sql, args, err)
		err = errors.New("Can not find ItemSpadeId ")
		return nil, err
	}

	itemSpace := &tableItemSpace.ItemSpace{}
	err = util.ScanMapIntoStruct(itemSpace, data[0])
	if err != nil {
		return nil, errorcode.InternalErrorCode_UnMarshalFailed.NewWithErr(err)
	}

	return itemSpace, nil
}

// ParseFlinkDuration parses a duration string.
// A duration string is a possibly signed sequence of
// decimal numbers, each with optional fraction and a unit suffix,
// such as "300ms", "-1.5h" or "2h45m".
// Valid time units are "ns", "us" (or "µs"), "ms", "s", "min", "h".
func ParseFlinkDuration(s string) (time.Duration, error) {
	// [-+]?([0-9]*(\.[0-9]*)?[a-z]+)+
	orig := s
	var d int64
	neg := false

	// Consume [-+]?
	if s != "" {
		c := s[0]
		if c == '-' || c == '+' {
			neg = c == '-'
			s = s[1:]
		}
	}
	// Special case: if all that is left is "0", this is zero.
	if s == "0" {
		return 0, nil
	}
	if s == "" {
		return 0, errors.New("time: invalid duration " + quote(orig))
	}
	for s != "" {
		var (
			v, f  int64       // integers before, after decimal point
			scale float64 = 1 // value = v + f/scale
		)

		var err error

		// The next character must be [0-9.]
		if !(s[0] == '.' || '0' <= s[0] && s[0] <= '9') {
			return 0, errors.New("time: invalid duration " + quote(orig))
		}
		// Consume [0-9]*
		pl := len(s)
		v, s, err = leadingInt(s)
		if err != nil {
			return 0, errors.New("time: invalid duration " + quote(orig))
		}
		pre := pl != len(s) // whether we consumed anything before a period

		// Consume (\.[0-9]*)?
		post := false
		if s != "" && s[0] == '.' {
			s = s[1:]
			pl := len(s)
			f, scale, s = leadingFraction(s)
			post = pl != len(s)
		}
		if !pre && !post {
			// no digits (e.g. ".s" or "-.s")
			return 0, errors.New("time: invalid duration " + quote(orig))
		}

		// Consume unit.
		i := 0
		for ; i < len(s); i++ {
			c := s[i]
			if c == '.' || '0' <= c && c <= '9' {
				break
			}
		}
		if i == 0 {
			return 0, errors.New("time: missing unit in duration " + quote(orig))
		}
		u := s[:i]
		s = s[i:]
		unit, ok := unitMap[u]
		if !ok {
			return 0, errors.New("time: unknown unit " + quote(u) + " in duration " + quote(orig))
		}
		if v > (1<<63-1)/unit {
			// overflow
			return 0, errors.New("time: invalid duration " + quote(orig))
		}
		v *= unit
		if f > 0 {
			// float64 is needed to be nanosecond accurate for fractions of hours.
			// v >= 0 && (f*unit/scale) <= 3.6e+12 (ns/h, h is the largest unit)
			v += int64(float64(f) * (float64(unit) / scale))
			if v < 0 {
				// overflow
				return 0, errors.New("time: invalid duration " + quote(orig))
			}
		}
		d += v
		if d < 0 {
			// overflow
			return 0, errors.New("time: invalid duration " + quote(orig))
		}
	}

	if neg {
		d = -d
	}
	return time.Duration(d), nil
}

func quote(s string) string {
	return "\"" + s + "\""
}

var errLeadingInt = errors.New("time: bad [0-9]*") // never printed

// leadingInt consumes the leading [0-9]* from s.
func leadingInt(s string) (x int64, rem string, err error) {
	i := 0
	for ; i < len(s); i++ {
		c := s[i]
		if c < '0' || c > '9' {
			break
		}
		if x > (1<<63-1)/10 {
			// overflow
			return 0, "", errLeadingInt
		}
		x = x*10 + int64(c) - '0'
		if x < 0 {
			// overflow
			return 0, "", errLeadingInt
		}
	}
	return x, s[i:], nil
}

// leadingFraction consumes the leading [0-9]* from s.
// It is used only for fractions, so does not return an error on overflow,
// it just stops accumulating precision.
func leadingFraction(s string) (x int64, scale float64, rem string) {
	i := 0
	scale = 1
	overflow := false
	for ; i < len(s); i++ {
		c := s[i]
		if c < '0' || c > '9' {
			break
		}
		if overflow {
			continue
		}
		if x > (1<<63-1)/10 {
			// It's possible for overflow to give a positive number, so take care.
			overflow = true
			continue
		}
		y := x*10 + int64(c) - '0'
		if y < 0 {
			overflow = true
			continue
		}
		x = y
		scale *= 10
	}
	return x, scale, s[i:]
}

var unitMap = map[string]int64{
	"ns":   int64(time.Nanosecond),
	"us":   int64(time.Microsecond),
	"µs":   int64(time.Microsecond), // U+00B5 = micro symbol
	"μs":   int64(time.Microsecond), // U+03BC = Greek letter mu
	"ms":   int64(time.Millisecond),
	"s":    int64(time.Second),
	"min":  int64(time.Minute),
	"m":    int64(time.Minute),
	"h":    int64(time.Hour),
	"hour": int64(time.Hour),
}

func GetExternalCatalogLocalizationPath(catalogId int64) (localizationPath string) {
	return "catalog-" + strconv.FormatInt(catalogId, 10)
}

// 支持 UTF-8 字符阶段的 substring 方法
func Substr(input string, start int, length int) string {
	asRunes := []rune(input)

	if start >= len(asRunes) {
		return ""
	}

	if start+length > len(asRunes) {
		length = len(asRunes) - start
	}

	return string(asRunes[start : start+length])
}

func GetResourceSecretIdAndKey(uin string, subAccountUin string, region string) (tmpSecretId, tmpSecretKey, token string, err error) {
	pass := false
	tmpSecretId, tmpSecretKey, token, pass, err = StsAssumeRole(uin, subAccountUin, region)
	if err != nil {
		logger.Errorf("Failed to StsAssumeRole %s %s error: %+v", uin, region, err)
		err = errorcode.NewStackError(errorcode.UnauthorizedOperationCode, "Failed to get user role!", nil)
		return
	}
	if !pass {
		logger.Errorf("Failed to DoCheckSavepoint -> StsAssumeRole %s %s not pass", uin, region)
		err = errorcode.NewStackError(errorcode.UnauthorizedOperationCode, "Failed to get credential token!", nil)
		return
	}
	return
}

func NewSavepointParam(savepointPath string, regions string) (param *watchdog.SavepointParam, err error) {

	//当发生异常时将状态置为不可用,并输出错误信息
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Failed to call NewSavepointParam because %+v", err)
			err = errors.New(fmt.Sprintf("%+v", errs))
			return
		}
	}()

	// cosn://bucket/**********/************/cql-4nsztjch/3/flink-savepoints/savepoint-000000-50844a1c2110
	// cosn://bucket/**********/************/cql-4nsztjch/3/flink-checkpoints/jobid/jobinstance/chk-x
	if len(savepointPath) <= 0 {
		logger.Errorf("InvalidParameterValue.SavepointPath. Savepoint path is blank")
		return nil, errors.New("invalidParameterValue.SavepointPath. Savepoint path is blank")
	}
	// 判断 savepointpath 是否以 cos 开头
	if !strings.HasPrefix(savepointPath, "cosn://") {
		msg := fmt.Sprintf("Savepoint path is not started with cosn://! %s", savepointPath)
		logger.Error(msg)
		return nil, errors.New(msg)
	}
	pathWithoutCos := strings.Split(savepointPath, "//")[1]
	bucket := strings.Split(pathWithoutCos, "/")[0]
	path := strings.Join(strings.Split(pathWithoutCos, "/")[1:], "/")
	region := SwitchRegion(regions)

	param = &watchdog.SavepointParam{
		Bucket: bucket,
		Path:   path,
		Region: region,
	}
	return param, nil
}

/**
 * 根据CU规格来计算Pod的内存
 */
func GetMemoryByCuSpec(cuSpec float64, mType string, archGeneration int, memRatio int8) string {
	oneCUMemory := float64(constants.OneCUMemory2)
	if archGeneration >= constants.TKE_ARCH_GENERATION_V2 {
		oneCUMemory = float64(constants.OneCUMemory1)
	}
	var baseMemRatio float64 = 1
	if memRatio != constants.CVM_DEFAULT_MEMRATIO && memRatio != 0 {
		baseMemRatio = float64(memRatio) / float64(constants.CVM_DEFAULT_MEMRATIO)
	}
	return fmt.Sprintf("%d"+mType, int(cuSpec*oneCUMemory*baseMemRatio))
}

/**
 * 根据CU规格来计算Pod的内存
 */
func GetMemoryByMemG(memG float64, mType string, archGeneration int) string {
	oneCUMemory := float64(constants.OneCUMemory2 / 4)
	if archGeneration >= constants.TKE_ARCH_GENERATION_V2 {
		oneCUMemory = float64(constants.OneCUMemory1 / 4)
	}
	return fmt.Sprintf("%d"+mType, int(memG*oneCUMemory))
}

/**
 * JM deployment的CPU计算
 * e.g 0.5cu = 500m   1cu = 1  2cu =2
 */
func GetDeploymentCpu(jmCpuSpec float64) string {
	if jmCpuSpec < 1 {
		return fmt.Sprintf("%dm", int(jmCpuSpec*1000))
	}
	return fmt.Sprintf("%f", jmCpuSpec)
}

func GetNoDotLowerFlinkVersion(flinkVersion string) string {
	// 要将Flink版本(e.g Flink-1.13)中的点号给替换掉，否则后续tke端创建service的时候会报错导致创建不成功
	flinkVersionReplaceDot := strings.Replace(flinkVersion, ".", "-", -1)
	// 要转为小写的，否则创建的时候tke端报错
	lowerFlinkVersion := strings.ToLower(flinkVersionReplaceDot)
	return lowerFlinkVersion
}

func ContainsKey(m map[string]string, key string) bool {
	_, ok := m[key]
	return ok
}

func ContainsElement(indexs []*string, target *string) bool {
	for _, element := range indexs {
		if element != nil && target != nil && *element == *target {
			return true
		}
	}
	return false
}

func ContainsString(arr []string, target string) bool {
	for _, str := range arr {
		if str == target {
			return true
		}
	}
	return false
}

/***********************************调用云梯api需要的工具类***********************************/

func GetYunTiApiUrl(yuntiApiDomain, apiKeyName, apiKeySecret string) string {
	timeStr := strconv.FormatInt(time.Now().Unix(), 10)
	signData := getSign(timeStr, apiKeyName, apiKeySecret)
	return fmt.Sprintf("%s?api_key=%s&api_sign=%s&api_ts=%s", yuntiApiDomain, apiKeyName, signData, timeStr)
}

func hmacSHA1(apiKeySecret string, signData string) string {
	mac := hmac.New(sha1.New, []byte(apiKeySecret))
	mac.Write([]byte(signData))
	return hex.EncodeToString(mac.Sum(nil))
}

func getSign(time, apiKeyName, apiKeySecret string) string {
	signData := time + apiKeyName
	return hmacSHA1(apiKeySecret, signData)
}

// 发送请求到云梯
func SendRequest2YunTi(interfaceName, apiName, region string, reqdata interface{}) (string, error) {
	cc := configure_center.CC(region)
	apiKeyName, apiKeySecret := cc.GetYuntiApiKey(constants.YUNTI_API_KEY_NAME, constants.YUNTI_API_KEY_SECRET)
	yuntiDomain := cc.GetYuntiApiUrl(constants.YUNTI_API_URL)
	apiUrl := fmt.Sprintf("%s/%s/%s", yuntiDomain, interfaceName, apiName)
	// 请求URL
	yunTiApiUrl := GetYunTiApiUrl(apiUrl, apiKeyName, apiKeySecret)

	data, err := json.Marshal(reqdata)
	if err != nil {
		return constants.EMPTY, errorcode.InternalErrorCode_MarshalFailed.NewWithErr(err)
	}
	senddata := string(data)

	// 发送请求
	req, err := http.NewRequest("POST", yunTiApiUrl, strings.NewReader(senddata))
	if err != nil {
		return constants.EMPTY, err
	}
	// Set IP to User-Agent
	req.Header.Set("User-Agent", fmt.Sprintf("Galileo/1.0 IP:%s", util.GetLocalAddressMust()))
	http.DefaultClient.Timeout = time.Duration(constants.HTTP_REQUEST_TIMEOUT) * time.Second
	resp, err := http.DefaultClient.Do(req)
	defer func() {
		if resp != nil {
			resp.Body.Close()
		}
	}()
	if err != nil {
		logger.Error("[common] sendRequest request:", senddata, "\t err:", err)
		return constants.EMPTY, err
	}
	logger.Errorf("SendRequest2YunTiresp is %+v, StatusCode %d,  Status %s", resp, resp.StatusCode, resp.Status)
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logger.Error("[common] sendRequest  url:", yunTiApiUrl, ",request:", senddata, "\t read response err:", err)
		return constants.EMPTY, err
	}
	ret := string(body)

	logger.Infof("[common] sendRequest  url:", yunTiApiUrl, ",request:", senddata, "\t response is: \t", ret)

	return ret, nil
}

// 星云平台存储密码 参考文档https://iwiki.woa.com/p/511228602
func InitCvmPasswd2XingYun(ips []string, password, region string) (string, error) {
	//获取星云平台的密钥
	cc := configure_center.CC(region)
	rtx, token, operator := cc.GetXingYunRtxTokenOperator()
	//获取星云平台请求链接地址
	url := cc.GetXingYunApiUrl(constants.XING_YUN_API_URL_INIT_CVM_Password)
	params := map[string]interface{}{
		"ipList":    ips,
		"rtx":       rtx,
		"timestamp": int(time.Now().Unix()),
		"eventId":   rand.Intn(99999-10000) + 10000,
	}
	params_ := make(map[string]string)
	for key, value := range params {
		strKey := strings.ToLower(key)
		switch value.(type) {
		case []string:
			// 将 ipList 转换为符合格式的字符串
			params_[strKey] = strings.ToLower(strings.Join([]string{key, "['" + strings.Join(value.([]string), "', '") + "']"}, "="))
		case int:
			params_[strKey] = strings.ToLower(strings.Join([]string{key, strconv.Itoa(value.(int))}, "="))
		case string:
			params_[strKey] = strings.ToLower(strings.Join([]string{key, value.(string)}, "="))
		}
	}

	items := make([]string, 0, len(params_))
	for _, value := range params_ {
		items = append(items, value)
	}
	sort.Strings(items)

	p := strings.Join(items, "&") + token
	h := md5.New()
	_, err := io.WriteString(h, p)
	if err != nil {
		logger.Errorf("Error calculating MD5:", err)
		return "", err
	}
	sign := fmt.Sprintf("%x", h.Sum(nil))
	fmt.Println(sign)
	params["sign"] = sign
	//入库ip的密码
	params["password"] = password
	params["operator"] = operator
	// 发送 POST 请求
	client := &http.Client{}
	data, err := json.Marshal(params)
	if err != nil {
		logger.Errorf("Error marshaling params:", err)
		return "", err
	}

	req, err := http.NewRequest(http.MethodPost, url, strings.NewReader(string(data)))
	if err != nil {
		logger.Errorf("Error creating request:", err)
		return "", err
	}

	req.Header.Set("Content-Type", "application/json")

	logger.Infof("Init password request %+v", req)
	res, err := client.Do(req)
	if err != nil {
		logger.Errorf("Error sending request:", err)
		return "", err
	}
	defer res.Body.Close()
	result, err := ioutil.ReadAll(res.Body)
	logger.Debugf("result:", result)
	var saveCvmPasswordRsp = &xingyun.SaveCvmPasswordRsp{}
	err = json.Unmarshal(result, &saveCvmPasswordRsp)
	if err != nil {
		logger.Errorf("Error json Unmarshal:", err)
		return "", nil
	}
	//当返回值不为100时返回错误
	if saveCvmPasswordRsp.Code != 100 {
		logger.Errorf("saveCvmPassword From XingYun Lack of query permission +%v", saveCvmPasswordRsp)
		return "", nil
	}
	return "", nil
}

// 星云平台通过ip查询密码接口,通过返回值判断是否密码入库 参考https://iwiki.woa.com/p/511228602文档
func GetWhetherInitPwd(ip, region string) (int, error) {
	//获取星云平台的密钥
	cc := configure_center.CC(region)
	rtx, token, _ := cc.GetXingYunRtxTokenOperator()
	//获取星云平台请求链接地址
	url := cc.GetXingYunApiUrl(constants.XING_YUN_API_URL_GET_CVM_Passowrd)
	params := map[string]interface{}{
		"ipList":       ip,
		"rtx":          rtx,
		"timestamp":    int(time.Now().Unix()),
		"eventId":      rand.Intn(99999-10000) + 10000,
		"ignoreVerify": 1,
	}
	params_ := make(map[string]string)
	for key, value := range params {
		strKey := strings.ToLower(key)
		switch value.(type) {
		case int:
			params_[strKey] = strings.ToLower(strings.Join([]string{key, strconv.Itoa(value.(int))}, "="))
		case string:
			params_[strKey] = strings.ToLower(strings.Join([]string{key, value.(string)}, "="))
		}
	}

	items := make([]string, 0, len(params_))
	for _, value := range params_ {
		items = append(items, value)
	}
	sort.Strings(items)

	p := strings.Join(items, "&") + token

	h := md5.New()
	_, err := io.WriteString(h, p)
	if err != nil {
		logger.Errorf("Error calculating MD5:", err)
		return -1, err
	}
	sign := fmt.Sprintf("%x", h.Sum(nil))

	logger.Debugf("=======sign=====%s ip=====%s", sign, ip)

	params["sign"] = sign

	// 发送 POST 请求
	client := &http.Client{}
	data, err := json.Marshal(params)
	if err != nil {
		logger.Errorf("Error marshaling params:", err)
		return -1, err
	}

	req, err := http.NewRequest(http.MethodPost, url, strings.NewReader(string(data)))
	if err != nil {
		logger.Errorf("Error creating request:", err)
		return -1, err
	}

	req.Header.Set("Content-Type", "application/json")

	res, err := client.Do(req)
	if err != nil {
		logger.Errorf("Error sending request:", err)
		return -1, err
	}
	defer res.Body.Close()
	result, err := ioutil.ReadAll(res.Body)
	if err != nil {
		logger.Errorf("Error decoding response:", err)
		return -1, err
	}
	logger.Debugf("result:", string(result))
	//星云查询接口返回值
	var getCvmPasswordRsp = &xingyun.GetCvmPasswordRsp{}
	err = json.Unmarshal(result, &getCvmPasswordRsp)
	if err != nil {
		logger.Errorf("Error unmarshaling response:", err)
		return -1, err
	}

	if getCvmPasswordRsp.Code != 100 {
		logger.Errorf("GetCvmPassword From XingYun Lack of query permission")
		return -1, errorcode.FailedOperationCode.NewWithMsg("GetCvmPassword From XingYun Lack of query permission" + getCvmPasswordRsp.Message)
	} else {
		//鉴权成功后进入判断 ret为0查询成功非0失败
		answer := getCvmPasswordRsp.Data[ip]
		return answer.Ret, nil
	}
}

// 解包
func UnPackResp(resp string, response interface{}) error {
	if len(resp) == 0 {
		return errorcode.InternalErrorCode_NoRsp.NewWithInfo("resp msg is empty", nil)
	}

	d := json.NewDecoder(strings.NewReader(resp))
	d.UseNumber()
	if err := d.Decode(&response); err != nil {
		logger.Error("unPackResp Decode err:", err)
		return errorcode.InternalErrorCode_UnMarshalFailed.NewWithErr(err)
	}

	return nil
}

// 按配置拼接参数
func ResourceConfigSpec(prefer *table3.CvmSaleConfig) string {
	return fmt.Sprintf("%dc%dg", prefer.Cpu, prefer.Memory)
}

/**
 * 内网购买CDB从返回的报错信息中获取到订单号
 */
func ParseOrderIdFromErrorMsg(errorMsg string) string {

	pattern := `dealNames:\[(\"[\d]+\")\]`
	re := regexp.MustCompile(pattern)
	match := re.FindStringSubmatch(errorMsg)
	if match != nil {
		return match[1][1 : len(match[1])-1]
	}

	pattern1 := `dealNames:\[(\\\"[\d]+\\\")\]`
	re1 := regexp.MustCompile(pattern1)
	match1 := re1.FindStringSubmatch(errorMsg)
	if match1 != nil {
		return strings.ReplaceAll(match1[1], "\\\"", "")
	}

	return constants.EMPTY
}

func DescribeDealsByCond(region string, netEnvironmentType int8, request *billing3.DescribeDealsByCondRequest) (*billing3.DescribeDealsByCondResponse, error) {
	if region == constants.EMPTY {
		logger.Errorf("invalid param region[%s] is empty ", region)
		return nil, errorcode.InvalidParameterCode.New()
	}
	secretId, secretKey, err := GetSecretIdAndKeyByNetworkEnvType(netEnvironmentType)
	if err != nil {
		return nil, err
	}
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = "billing.tencentcloudapi.com"

	// 实例化要请求产品的client对象,clientProfile是可选的
	credential := common.NewCredential(
		secretId,
		secretKey,
	)

	client, _ := billing3.NewClient(credential, region, cpf)
	return client.DescribeDealsByCond(request)
}

func GetCOSNFlinkConf(region string, appId int32, dynamicProperties []*model3.Property) []*model3.Property {
	properties := make([]*model3.Property, 0)
	properties = append(properties,
		&model3.Property{
			Key:   "fs.AbstractFileSystem.cosn.impl",
			Value: "org.apache.hadoop.fs.CosN",
		},
		&model3.Property{
			Key:   "fs.cosn.impl",
			Value: "org.apache.hadoop.fs.CosFileSystem",
		},
		&model3.Property{
			Key:   "fs.cosn.credentials.provider",
			Value: "org.apache.flink.fs.cos.OceanusCOSCredentialsProvider",
		},
		&model3.Property{
			Key:   "fs.cosn.bucket.region",
			Value: region,
		},
		&model3.Property{
			Key:   "fs.cosn.trsf.fs.AbstractFileSystem.ofs.impl",
			Value: "com.qcloud.chdfs.fs.CHDFSDelegateFSAdapter",
		},
		&model3.Property{
			Key:   "fs.cosn.trsf.fs.ofs.impl",
			Value: "com.qcloud.chdfs.fs.CHDFSHadoopFileSystemAdapter",
		},
		&model3.Property{
			Key:   "fs.cosn.trsf.fs.ofs.tmp.cache.dir",
			Value: "/tmp/chdfs/",
		},
		&model3.Property{
			Key:   "fs.cosn.trsf.fs.ofs.bucket.region",
			Value: region,
		},
		&model3.Property{
			Key:   "fs.cosn.trsf.fs.ofs.upload.flush.flag",
			Value: "true",
		})
	var crossAccountAccess = constants.OCEANUS_COS_PARAM_FS_COSN_CROSS_ACCOUNT_ACCESS_DISABLE
	for _, property := range dynamicProperties {
		if property.Key == constants.OCEANUS_COS_PARAM_FS_COSN_CROSS_ACCOUNT_ACCESS_KEY {
			crossAccountAccess = property.Value
		}
	}
	// If oceanus.fs.cosn.cross.account.access enable, it means cross-account access to COS. In this case,
	// it is necessary to remove the appId from the configuration.
	if crossAccountAccess == constants.OCEANUS_COS_PARAM_FS_COSN_CROSS_ACCOUNT_ACCESS_DISABLE {
		properties = append(properties, &model3.Property{
			Key:   "fs.cosn.userinfo.appid",
			Value: strconv.FormatInt(int64(appId), 10),
		},
			&model3.Property{
				Key:   "fs.cosn.trsf.fs.ofs.user.appid",
				Value: strconv.FormatInt(int64(appId), 10),
			},
		)
	}
	return properties
}
func GetSqlGatewaysByGroupSerialId(groupSerialId string) (sqlGateways []*table4.SqlGateway, err error) {
	cond := dao.NewCondition()
	cond.Eq("c.SerialId", groupSerialId)
	where, args := cond.GetWhere()
	sql := " SELECT a.* FROM SqlGateway a JOIN Cluster b ON a.ClusterId = b.Id JOIN ClusterGroup c ON b.ClusterGroupId = c.Id " + where
	_, data, err := service2.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return nil, err
	}
	sqlGateways = make([]*table4.SqlGateway, 0)
	for i := 0; i < len(data); i++ {
		sqlGateway := &table4.SqlGateway{}
		err = util.ScanMapIntoStruct(sqlGateway, data[i])
		if err != nil {
			return nil, err
		}
		sqlGateways = append(sqlGateways, sqlGateway)
	}
	return sqlGateways, nil
}

func GetHadoopYarnsByGroupSerialId(groupSerialId string) (yarns []*table4.ClusterHadoopYarn, err error) {

	sql := "select * from ClusterHadoopYarn  where  ClusterGroupSerialId = ?"
	args := make([]interface{}, 0)
	args = append(args, groupSerialId)
	txManager := service2.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to execute the sql:%s, with args:%+v, with errors:%+v", sql, args, err)
		return nil, err
	}
	yarnList := make([]*table4.ClusterHadoopYarn, 0)
	for i := 0; i < len(data); i++ {
		yarn := &table4.ClusterHadoopYarn{}
		err = util.ScanMapIntoStruct(yarn, data[i])
		if err != nil {
			return nil, err
		}
		yarnList = append(yarnList, yarn)
	}
	return yarnList, nil
}

func GetFlinkVersion(job *table.Job, jobConfig *table5.JobConfig) string {
	flinkVersion := job.FlinkVersion
	if jobConfig.FlinkVersion != "" {
		flinkVersion = jobConfig.FlinkVersion
	}
	return flinkVersion
}

func StrArrContains(ss []string, s string) bool {
	for _, v := range ss {
		trimmedStr := strings.TrimSpace(v)
		if trimmedStr == s {
			return true
		}
	}
	return false
}

func CheckJobConfigCPUAndMem(jobConfig *table5.JobConfig, cuMem int8) {
	if AlmostEqual(jobConfig.JobManagerCpu, 0) || AlmostEqual(jobConfig.JobManagerMem, 0) || AlmostEqual(jobConfig.TaskManagerCpu, 0) || AlmostEqual(jobConfig.TaskManagerMem, 0) {
		jobConfig.JobManagerCpu = 1 * jobConfig.JmCuSpec
		jobConfig.TaskManagerCpu = 1 * jobConfig.TmCuSpec
		jobConfig.JobManagerMem = float32(cuMem) * jobConfig.JmCuSpec
		jobConfig.TaskManagerMem = float32(cuMem) * jobConfig.TmCuSpec
	}
}
func AlmostEqual(a, b float32) bool {
	return (a-b) < constants.FLOAT_TOLERATE && (b-a) < constants.FLOAT_TOLERATE
}

func GetNumberOfTaskSlots(jobConfig *table5.JobConfig) (numberOfSlots int) {
	numberOfSlots = 1
	if len(jobConfig.Properties) > 0 {
		properties := make([]*model3.Property, 0)

		err := json.Unmarshal([]byte(jobConfig.Properties), &properties)
		if err != nil {
			logger.Errorf("jobConfig cannot Unmarshal to Property, error %v", err)
			return
		}

		for _, property := range properties {
			if property.Key == constants.NUMBER_OF_TASK_SLOTS_KEY {
				numberOfTaskSlots, err := strconv.Atoi(property.Value)
				if err != nil {
					logger.Errorf("value of properties.numberOfTaskSlots invalid: " + property.Value)
					return
				}
				numberOfSlots = numberOfTaskSlots
				return
			}
		}
	}
	return
}

func SendHttpRequest(url string, method string, body string, headers map[string]string, username, password string) ([]byte, error) {
	client := &http.Client{}
	req, err := http.NewRequest(method, url, strings.NewReader(body))
	// 设置 Basic Auth
	if username != "" && password != "" {
		req.SetBasicAuth(username, password)
	}
	logger.Debugf("send request url %s", url)
	if err != nil {
		return nil, err
	}
	for k, v := range headers {
		req.Header.Add(k, v)
	}
	resp, err := client.Do(req)
	logger.Debugf("resp   %s", resp)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	respBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	logger.Debugf("resp body  %s", respBody)
	return respBody, nil
}

type InPlaceScaleParam struct {
	Parallelism        int    `json:"parallelism"`
	SerializedJobGraph string `json:"serialized-jobgraph"`
}

// Difference result=a-b
func Difference(source, toRemove []string) []string {
	if source == nil {
		return nil
	}

	// 如果没有需要移除的元素，直接返回源切片的副本。
	if len(toRemove) == 0 {
		// 创建一个新切片并复制内容，以避免调用方意外修改原始的 source 切片。
		result := make([]string, len(source))
		copy(result, source)
		return result
	}

	toRemoveSet := make(map[string]struct{}, len(toRemove))
	for _, item := range toRemove {
		toRemoveSet[item] = struct{}{}
	}

	var result []string

	// 遍历源切片，如果元素不在 toRemoveSet 中，则将其添加到结果切片。
	for _, item := range source {
		if _, found := toRemoveSet[item]; !found {
			result = append(result, item)
		}
	}

	return result
}

func AccessWeDataRoleKey(Region, OwnerUin, CreatorUin string) (string, string, string, int64, error) {
	secretId, secretKey, token, pass, err := StsAssumeRole(OwnerUin, CreatorUin, Region)
	if err != nil {
		return "", "", "", 0, err
	}
	if !pass {
		return "", "", "", 0, errorcode.NewStackError(errorcode.InternalErrorCode, "StsAssumeRole not pass", nil)
	}
	credential := common.NewTokenCredential(secretId, secretKey, token)
	request := wedata.NewAccessWeDataRoleKeyRequest()
	request.OwnerUin = &OwnerUin

	prof := profile.NewClientProfile()
	// 上海自动驾驶云专区走内网
	if Region == constants.AP_SHANGHAI_ADC {
		prof.HttpProfile.Endpoint = "wedata.internal.tencentcloudapi.com"
	}

	client, err := wedata.NewClient(credential, Region, prof)
	if err != nil {
		logger.Errorf("describeWeDataRoleKey -> wedata.NewClient with error :[%v]", err)
		return "", "", "", 0, err
	}
	response, err := client.AccessWeDataRoleKey(request)
	if err != nil {
		logger.Errorf("describeWeDataRoleKey -> DescribeWeDataRoleKey with error :[%v]", err)
		return "", "", "", 0, err
	}
	return *response.Response.SecretId, *response.Response.SecretKey, *response.Response.Token, *response.Response.ExpiredTime, nil
}
