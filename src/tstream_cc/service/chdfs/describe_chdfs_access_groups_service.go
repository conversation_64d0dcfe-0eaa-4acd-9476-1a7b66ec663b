package chdfs

import (
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/chdfs"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
)

type DescribeCHDFSAccessGroupsService struct {
	request *chdfs.DescribeCHDFSAccessGroupsReq
}

func NewDescribeCHDFSAccessGroupsService (req *chdfs.DescribeCHDFSAccessGroupsReq) *DescribeCHDFSAccessGroupsService {
	return &DescribeCHDFSAccessGroupsService{
		request: req,
	}
}

func (o *DescribeCHDFSAccessGroupsService) DescribeCHDFSAccessGroups() (rsp *chdfs.DescribeCHDFSAccessGroupsRsp, err error) {

	rsp = &chdfs.DescribeCHDFSAccessGroupsRsp{
		CHDFSAccessGroups: make([]*chdfs.CHDFSAccessGroup, 0, 0),
	}
	// 鉴权 (子账号，只能看见自己空间内绑定的集群，超管可以看到所有)
	authClusterIds, err, _, _ :=auth.ExtractSubUinAuthClustersSet(o.request.WorkSpaceId, o.request.IsSupOwner, []string{o.request.ClusterId},o.request.AppId, o.request.SubAccountUin, o.request.Uin)
	if err != nil {
		logger.Errorf("[%s]: has no operation permission to access cluster %s ,error: %+v", o.request.RequestId, o.request.ClusterId,err)
		return nil, errorcode.AuthFailure_UnauthorizedOperation.ReplaceDesc(fmt.Sprintf("[%s]: has no operation permission to access cluster %s",o.request.RequestId,o.request.ClusterId))
	}
	var clusterAuthorized = false
	for _, authClusterId := range authClusterIds {
		if authClusterId == o.request.ClusterId {
			clusterAuthorized = true
		}
	}
	clusterGroup, err := service2.GetClusterGroupBySerialId(o.request.ClusterId)
	if err != nil {
		logger.Errorf("[%s] [%s] Failed to GetClusterGroupBySerialId,error %+v",o.request.RequestId,o.request.ClusterId,err)
		return nil, errorcode.ResourceNotFoundCode_ClusterNotFound.ReplaceDesc(fmt.Sprintf("[%s] Not found cluster %s.",o.request.RequestId,o.request.ClusterId))
	}
	if int64(clusterGroup.AppId) != o.request.AppId || clusterGroup.OwnerUin != o.request.Uin || !clusterAuthorized {
		return nil, errorcode.FailedOperationCode.ReplaceDesc(fmt.Sprintf("The appid %d or uin %s has no operation permission.",o.request.AppId,o.request.Uin))
	}
	cHdfsServiceOpt := NewCHdfsService(o.request.ClusterId,o.request.Region,o.request.Uin,o.request.SubAccountUin)
	err = cHdfsServiceOpt.InitCHdfsService(false)
	if err != nil {
		logger.Errorf("[%s] Failed to InitCHdfsService,error: %+v",o.request.RequestId, err)
		return nil, errorcode.FailedOperationCode.ReplaceDesc(err.Error())
	}
	accessGroup, found, err := cHdfsServiceOpt.FindOceanusAccessGroupByCluster(o.request.ClusterId)
	if err != nil {
		logger.Errorf("[%s] Failed to FindOceanusAccessGroup,error: %+v",o.request.RequestId, err)
		return nil, errorcode.FailedOperationCode.ReplaceDesc(err.Error())
	}
	if found {
		rsp.CHDFSAccessGroups = append(rsp.CHDFSAccessGroups, &chdfs.CHDFSAccessGroup{
			AccessGroupId: accessGroup.AccessGroupId,
			AccessGroupName: accessGroup.AccessGroupName,
		})
	}
	return rsp,nil
}
