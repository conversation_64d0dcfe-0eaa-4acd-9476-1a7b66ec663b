package chdfs

import (
	"fmt"
	chdfs2 "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/chdfs/v20201112"
	tencentCloudErr "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/errors"
	"strconv"
	"strings"
	"sync"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/chdfs"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
)

type DescribeCHdfsFileSystemsService struct {
	request *chdfs.DescribeCHdfsFileSystemsReq
}

func NewDescribeCHdfsFileSystemsService(req *chdfs.DescribeCHdfsFileSystemsReq) *DescribeCHdfsFileSystemsService {
	return &DescribeCHdfsFileSystemsService{
		request: req,
	}
}

func (o *DescribeCHdfsFileSystemsService) DescribeCHdfsFileSystems() (rsp *chdfs.DescribeCHdfsFileSystemsRsp, err error) {

	rsp = &chdfs.DescribeCHdfsFileSystemsRsp{}
	clusterGroup, err := service2.GetClusterGroupBySerialId(o.request.ClusterId)
	if err != nil {
		return nil, errorcode.FailedOperationCode.NewWithErr(err)
	}
	if int64(clusterGroup.AppId) != o.request.AppId || clusterGroup.OwnerUin != o.request.Uin {
		return nil, errorcode.FailedOperationCode.ReplaceDesc(fmt.Sprintf("The appid %d or uin %s has no operation permission.", o.request.AppId, o.request.Uin))
	}
	cHdfsService := NewCHdfsService(o.request.ClusterId, o.request.Region, o.request.Uin, o.request.SubAccountUin)
	filters := make(map[string][]string, len(o.request.Filters))
	for _, filter := range o.request.Filters {
		filters[filter.Name] = filter.Values
	}
	err = cHdfsService.InitCHdfsService(true)
    if err != nil {
    	logger.Errorf("[%s] Failed to InitCHdfsService,error %+v",o.request.RequestId, err)
    	return nil, errorcode.FailedOperationCode.NewWithErr(err)
	}
	var bucketArchTypeFilter = BucketArchTypeAll
	if bucketArchTypeValues, ok := filters["BucketArchType"]; ok {
		if len(bucketArchTypeValues) == 0 {
			return nil, errorcode.InvalidParameterValueCode.ReplaceDesc("BucketArchType values empty.")
		}
		bucketArchTypeFilter = strings.ToUpper(bucketArchTypeValues[0])
	}
	var accessStatusFilter = AccessStatusUnknown
	if accessStatusValues, ok := filters["AccessStatus"]; ok {
		if len(accessStatusValues) == 0 {
			return nil, errorcode.InvalidParameterValueCode.ReplaceDesc("AccessStatus values empty.")
		}
		accessStatusFilter, err = strconv.Atoi(accessStatusValues[0])
		if err != nil {
			logger.Errorf("[%s] Failed convert access status %s to int ,error %+v", o.request.RequestId, accessStatusValues[0], err)
			return nil, errorcode.FailedOperationCode.NewWithErr(err)
		}
	}
	// 1. 查询元数据加速桶
	metaBuckets, err := cHdfsService.ListMetaBuckets()
	if err != nil {
		logger.Errorf("[%s] Failed to ListMetaBuckets,error %+v", o.request.RequestId, err)
		return nil, errorcode.FailedOperationCode.NewWithErr(err)
	}
	cHdfsServiceOpt := NewCHdfsService(o.request.ClusterId, o.request.Region, o.request.Uin, o.request.SubAccountUin)
	err = cHdfsServiceOpt.InitCHdfsService(false)
	if err != nil {
		logger.Errorf("[%s] Failed to InitCHdfsService,error %+v", o.request.RequestId, err)
		return nil, errorcode.FailedOperationCode.NewWithErr(err)
	}
	// 2.查询集群vpc下的权限组，运营账号
	cHdfsFileSystemAssociatedSet := make(map[string]*MountedCHdfsFileSystem)
	oceanusAccessGroup, oceanusAccessGroupFound, err := cHdfsServiceOpt.FindOceanusAccessGroupByCluster(o.request.ClusterId)
	if err != nil {
		logger.Errorf("[%s] Failed to DescribeAccessGroupsByCluster,error %+v", o.request.RequestId, err)
		return nil, errorcode.FailedOperationCode.NewWithErr(err)
	}
	// 如果已有Oceanus权限组，判断该权限组是否绑定了Fs
	if oceanusAccessGroupFound {
		// 3.查询vpc下的权限组绑定的FileSystems
		cHdfsFileSystemsAssociated, err := cHdfsService.DescribeCHdfsFileSystemsByAccessGroups(oceanusAccessGroup)
		if err != nil {
			logger.Errorf("[%s] Failed to DescribeCHdfsFileSystemsByAccessGroups,error %+v", o.request.RequestId, err)
			return nil, errorcode.FailedOperationCode.NewWithErr(err)
		}
		for _, cHdfsFileSystemAssociated := range cHdfsFileSystemsAssociated {
			cHdfsFileSystemAssociatedSet[cHdfsFileSystemAssociated.FileSystemId] = cHdfsFileSystemAssociated
		}
	}
	// 检查FileSystem是否已经授权访问
	var getAccessStatusAndMountPointId func(cHdfsFileSystemAssociatedSet map[string]*MountedCHdfsFileSystem, fileSystemId string) (int, string)
	getAccessStatusAndMountPointId = func(cHdfsFsAssociatedSet map[string]*MountedCHdfsFileSystem, fileSystemId string) (int, string) {
		var accessStatus = AccessStatusNotAssociated
		var mountPointId = ""
		if cHdfsFsAssociated, ok := cHdfsFsAssociatedSet[fileSystemId]; ok {
			accessStatus = AccessStatusAssociated
			mountPointId = cHdfsFsAssociated.MountPointId
		}
		return accessStatus, mountPointId
	}
	var assembleMetaCos  func(cHdfsService *CHdfsService,rsp *chdfs.DescribeCHdfsFileSystemsRsp) error
	assembleMetaCos = func(cHdfsService *CHdfsService,rsp *chdfs.DescribeCHdfsFileSystemsRsp) error {
		var wg sync.WaitGroup
		semaphore := make(chan struct{}, 10)
		result := make(chan error, len(metaBuckets))
		for _, bucket := range metaBuckets {
			wg.Add(1)
			semaphore <- struct{}{}
			go func(bucket string) {
				defer wg.Done()
				defer func() { <-semaphore }()
				// 查询加速桶默认挂载点
				mountPoint, err := cHdfsService.DescribeMountPoint(bucket)
				if err != nil {
					sdkErr, ok := err.(*tencentCloudErr.TencentCloudSDKError);
					if ok && sdkErr.GetCode() == "ResourceNotFound.MountPointNotExists"  {
						// 元数据加速桶默认的挂载点被删除了，COS控制台依然显示是加速桶，但是提示'未查询到存储桶对应的 HDFS 文件系统 ID'
						// 因挂载地址不能配置，用户无法在CHDFS控制台重新创建挂载点,CHDFS 应禁止删除加速桶默认挂载点，预计2023.5月底上线
						logger.Warningf("[%s] The default mount point %s may have been deleted,error %+v",o.request.RequestId, bucket,err)
					} else {
						logger.Errorf("[%s] Failed to DescribeMountPoint,error %+v",o.request.RequestId, err)
						result <- errorcode.FailedOperationCode.NewWithErr(err)
					}
				} else {
					accessStatus, mountPointId := getAccessStatusAndMountPointId(cHdfsFileSystemAssociatedSet, *mountPoint.FileSystemId)
					cosFileSystem := &chdfs.CHdfsFileSystem{
						FileSystemName: bucket,
						FileSystemId: 	*mountPoint.FileSystemId,
						BucketArchType: BucketArchTypeCos,
						AccessStatus:   accessStatus,
						MountPointId:   mountPointId,
					}
					if accessStatus == AccessStatusNotAssociated {
						cosFileSystem.MountPointId = bucket
					}
					rsp.CHdfsFileSystems = append(rsp.CHdfsFileSystems, cosFileSystem)
				}
			}(bucket)
		}
		wg.Wait()
		close(semaphore)
		close(result)
		for err := range result {
			if err != nil {
				return err
			}
		}
		return nil
	}
	var assembleCHdfs func(cHdfsService *CHdfsService, rsp *chdfs.DescribeCHdfsFileSystemsRsp) error
	assembleCHdfs = func(cHdfsService *CHdfsService, rsp *chdfs.DescribeCHdfsFileSystemsRsp) error {
		// 查询CHdfs并排除加速桶
		cHdfsFileSystems, err := cHdfsService.DescribeCHdfsFileSystemsExcludeMetaBucket(metaBuckets)
		if err != nil {
			logger.Errorf("[%s] Failed to DescribeCHdfsFileSystemsExcludeMetaBucket,error %+v", o.request.RequestId, err)
			return errorcode.FailedOperationCode.NewWithErr(err)
		}
		var wg sync.WaitGroup
		semaphore := make(chan struct{}, 10)
		result := make(chan error, len(metaBuckets))
		for _, cHdfsFileSystem := range cHdfsFileSystems {
			wg.Add(1)
			semaphore <- struct{}{}
			go func(cHdfsFileSystem *chdfs2.FileSystem) {
				defer wg.Done()
				defer func() { <-semaphore }()
				accessStatus, mountPointId := getAccessStatusAndMountPointId(cHdfsFileSystemAssociatedSet, *cHdfsFileSystem.FileSystemId)
				rsp.CHdfsFileSystems = append(rsp.CHdfsFileSystems, &chdfs.CHdfsFileSystem{
					FileSystemName: *cHdfsFileSystem.FileSystemName,
					FileSystemId:   *cHdfsFileSystem.FileSystemId,
					BucketArchType: BucketArchTypeCHdfs,
					AccessStatus:   accessStatus,
					MountPointId:   mountPointId,
				})
			}(cHdfsFileSystem)
		}
		wg.Wait()
		close(semaphore)
		close(result)
		for err := range result {
			if err != nil {
				return err
			}
		}
		return nil
	}
	// 根据BucketArchType过滤
	switch bucketArchTypeFilter {
		case BucketArchTypeCos:
			err := assembleMetaCos(cHdfsService, rsp)
			if err != nil {
				logger.Errorf("[%s] Failed to AssembleMetaCos,error %+v",o.request.RequestId, err)
				return nil,errorcode.FailedOperationCode.NewWithErr(err)
			}
		case BucketArchTypeCHdfs:
			err := assembleCHdfs(cHdfsService, rsp)
			if err != nil {
				logger.Errorf("[%s] Failed to AssembleCHdfs,error %+v",o.request.RequestId, err)
				return nil,errorcode.FailedOperationCode.NewWithErr(err)
			}
		case BucketArchTypeAll:
			err := assembleMetaCos(cHdfsService, rsp)
			if err != nil {
				logger.Errorf("[%s] Failed to AssembleMetaCos,error %+v",o.request.RequestId, err)
				return nil,errorcode.FailedOperationCode.NewWithErr(err)
			}
			err = assembleCHdfs(cHdfsService, rsp)
			if err != nil {
				logger.Errorf("[%s] Failed to AssembleCHdfs,error %+v",o.request.RequestId, err)
				return nil,errorcode.FailedOperationCode.NewWithErr(err)
			}

	default:
			logger.Errorf("[%s] Unsupported BucketArchType %s",o.request.RequestId, bucketArchTypeFilter)
			return nil, errorcode.UnsupportedOperationCode.ReplaceDesc(fmt.Sprintf("[%s] Unsupported BucketArchType %s",o.request.RequestId, bucketArchTypeFilter))
	}
	// 根据授权状态过滤
	if accessStatusFilter != AccessStatusUnknown {
		cHdfsFileSystemsFiltered := make([]*chdfs.CHdfsFileSystem, 0)
		switch accessStatusFilter {
			case AccessStatusNotAssociated:
				for _, cHdfsFileSystem := range rsp.CHdfsFileSystems {
					if cHdfsFileSystem.AccessStatus == AccessStatusNotAssociated {
						cHdfsFileSystemsFiltered = append(cHdfsFileSystemsFiltered, cHdfsFileSystem)
					}
				}
			case AccessStatusAssociated:
				for _, cHdfsFileSystem := range rsp.CHdfsFileSystems {
					if cHdfsFileSystem.AccessStatus == AccessStatusAssociated {
						cHdfsFileSystemsFiltered = append(cHdfsFileSystemsFiltered, cHdfsFileSystem)
					}
				}
			default:
			    logger.Errorf("[%s] Unsupported AccessStatus %d",o.request.RequestId, accessStatusFilter)
				return nil, errorcode.UnsupportedOperationCode.ReplaceDesc(fmt.Sprintf("[%s] Unsupported AccessStatus %d",o.request.RequestId, accessStatusFilter))
		}
		rsp.CHdfsFileSystems = cHdfsFileSystemsFiltered
	}
	return rsp, nil
}