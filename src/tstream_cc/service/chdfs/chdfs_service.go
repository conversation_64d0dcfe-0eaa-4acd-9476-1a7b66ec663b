package chdfs

import (
	chdfs "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/chdfs/v20201112"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common"
	service4 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	logger "tencentcloud.com/tstream_galileo/src/main/credentials_provider/common/log"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cos"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/qcloud"
)

const (
	MountPointStatusOpen    uint64 = 1
	AccessGroupVpcTypeCvm   uint64 = 1
	BucketArchTypeCos                = "COS"
	BucketArchTypeCHdfs              = "OFS"
	BucketArchTypeAll                = "ALL"  // query filter
	AccessStatusNotAssociated   	 = 1
	AccessStatusAssociated   		 = 2
	AccessStatusUnknown   		     = -1
	AccessRuleAddressCvm   		     = "*********/16"
	AccessRuleAddressCvmDev   		 = "10.0.0.0/16"  // ConfigureCenter.Flow.Common vpc.cidr
	AccessRuleAccessModeReadWrite uint64   = 2
	AccessRuleAccessPriority 	  uint64   = 10
)

type CHdfsService struct {
	clusterId 		string
	region 			string
	uin 			string
	subAccountUin 	string
	secretId 		string
	secretKey 		string
	token 			string
	client *chdfs.	Client
}

func NewCHdfsService(clusterId string,region string,uin string,subUin string) *CHdfsService {
	return &CHdfsService{
		region: region,
		uin: uin,
		subAccountUin: subUin,
		clusterId:clusterId,
	}
}

func (this *CHdfsService) InitCHdfsService(assumeRole bool)  (err error) {
	this.token = ""
	if assumeRole {
		this.secretId, this.secretKey, this.token, _, err = service.StsAssumeRole(this.uin, this.subAccountUin, this.region)
		if err != nil {
			return  err
		}
	}else {
		this.secretId, this.secretKey, err = service.GetSecretIdAndKeyOfScs()
		if err != nil {
			return  err
		}
	}
	prof := qcloud.NewClientProfile()
	credential := common.NewTokenCredential(this.secretId, this.secretKey, this.token)
	this.client, err = chdfs.NewClient(credential, this.region, prof)
	if err != nil {
		return  err
	}
	return  nil
}

func (this *CHdfsService) GenerateMountPointName()  string {
	return "oceanus-" + this.clusterId
}

func (this *CHdfsService) GenerateAccessGroupName()  string {
	return "oceanus-" + this.clusterId
}

func SliceToMap(strs []string) map[string]struct{} {
	set := make(map[string]struct{}, len(strs))
	for _, str := range strs {
		set[str] = struct{}{}
	}
	return set
}

func (this *CHdfsService) DescribeCHdfsFileSystems()  (fileSystems []*chdfs.FileSystem,err error) {
	describeFileSystemsReq := chdfs.NewDescribeFileSystemsRequest()
	describeFileSystemsRsp, err := this.client.DescribeFileSystems(describeFileSystemsReq)
	if err != nil {
		return  nil,err
	}
	return describeFileSystemsRsp.Response.FileSystems,  nil
}
// 加速桶和CHdfs有必要进行区别,
// COS的策略未来会主推加速桶，CHdfs入口未来可能收敛或者下线，所以对于使用加速桶的客户不会感知CHdfs。
// 加速桶会自动创建挂载点(以COS同名作为id)，这个挂载点在COS的控制台可以进行直接操作，比如绑定/解绑权限组，
// 所以，对于加速桶，要基于自动创建的挂载点进行授权
func (this *CHdfsService) DescribeCHdfsFileSystemsExcludeMetaBucket(metaBuckets []string)  (fileSystems []*chdfs.FileSystem,err error) {
	describeFileSystemsReq := chdfs.NewDescribeFileSystemsRequest()
	describeFileSystemsRsp, err := this.client.DescribeFileSystems(describeFileSystemsReq)
	if err != nil {
		return  nil,err
	}
	cHdfsFileSystems := describeFileSystemsRsp.Response.FileSystems
	cHdfsFileSystemsExcluded := make([]*chdfs.FileSystem,0)
	metaBucketSet := SliceToMap(metaBuckets)
	// 根据是否有桶名命名的mountId，判断是否是加速桶
	// CHdfsFileSystem 没有附带加速桶信息，只能根据如上的方式进行区别
	for _, cHdfsFileSystem := range cHdfsFileSystems {
		mountPoints, err := this.DescribeMountPoints(*cHdfsFileSystem.FileSystemId)
		if err != nil {
			return  nil,err
		}
		var metaBucketFlag = false
		for _, mountPoint := range mountPoints {
			if _,ok := metaBucketSet[*mountPoint.MountPointId];ok {
				metaBucketFlag  = true
			}
		}
		if ! metaBucketFlag {
			cHdfsFileSystemsExcluded = append(cHdfsFileSystemsExcluded, cHdfsFileSystem)
		}
		metaBucketFlag = false
	}
	return cHdfsFileSystemsExcluded,  nil
}

// 运营账号 InitCHdfsService(false)
func (this *CHdfsService)CreateCreateAccessGroup(vpcId string)  (accessGroupId string,err error) {
	oceanusAccessGroupName := this.GenerateAccessGroupName()
	var accessGroupVpcTypeCvm = AccessGroupVpcTypeCvm
	createAccessGroupReq := chdfs.NewCreateAccessGroupRequest()
	createAccessGroupReq.VpcId = &vpcId
	createAccessGroupReq.AccessGroupName = &oceanusAccessGroupName
	createAccessGroupReq.VpcType = &accessGroupVpcTypeCvm
	createAccessGroupRsp, err := this.client.CreateAccessGroup(createAccessGroupReq)
	if err != nil {
		return  "",err
	}
	return *createAccessGroupRsp.Response.AccessGroup.AccessGroupId, nil
}
// 运营账号 InitCHdfsService(false)
func (this *CHdfsService)CreateAccessRules(accessGroupId string)  error {
	var addressCvm = AccessRuleAddressCvm
	isDev := service4.GetConfStringValue("scsDevEnv")
    if isDev == "true" {
		addressCvm = AccessRuleAddressCvmDev
	}
	var accessModeReadWrite = AccessRuleAccessModeReadWrite
	var accessPriority = AccessRuleAccessPriority
	createAccessRulesReq := chdfs.NewCreateAccessRulesRequest()
	createAccessRulesReq.AccessGroupId = &accessGroupId
	createAccessRulesReq.AccessRules = []*chdfs.AccessRule{
		{
		Address:    &addressCvm,
		AccessMode: &accessModeReadWrite,
		Priority:	&accessPriority,
	}}
	_, err := this.client.CreateAccessRules(createAccessRulesReq)
	if err != nil {
		return  err
	}
	return nil
}

// 运营账号 InitCHdfsService(false)
func (this *CHdfsService) FindOceanusAccessGroupByCluster(clusterId string)  (accessGroup *chdfs.AccessGroup,found bool,err error) {
	clusterGroup, err := service2.GetClusterGroupBySerialId(clusterId)
	if err != nil {
		return nil, false,err
	}
	cluster, err := service2.GetActiveClusterByClusterGroupId(clusterGroup.Id)
	if err != nil {
		return nil, false,err
	}
	//cluster.VpcId
	describeAccessGroupsReq := chdfs.NewDescribeAccessGroupsRequest()
	describeAccessGroupsReq.VpcId = &cluster.VpcId
	describeAccessGroupsRsp, err := this.client.DescribeAccessGroups(describeAccessGroupsReq)
	if err != nil {
		return  nil,false,err
	}
	accessGroups := describeAccessGroupsRsp.Response.AccessGroups
	for _, accessGroup := range accessGroups {
		if *accessGroup.VpcId == cluster.VpcId &&
			*accessGroup.AccessGroupName == this.GenerateAccessGroupName(){
			return accessGroup,true,nil
		}
	}
	return nil,false,nil
}

type MountedCHdfsFileSystem struct {

	AccessGroupId 		   string
	// 根据mountId命名可以区分出是否是加速桶
	// 加速桶默认mountId: bucket-name,CHdfs则以FileSystemId开头
	MountPointId 		   string

	FileSystemId   		   string

	FileSystemName   	   string
}

func (this *CHdfsService) DescribeCHdfsFileSystemsByAccessGroups(accessGroup *chdfs.AccessGroup)  (mountedCHdfsFileSystems []*MountedCHdfsFileSystem,err error) {
	// 1. 查询权限组绑定的挂载点
	describeMountPointsReq := chdfs.NewDescribeMountPointsRequest()
	describeMountPointsReq.AccessGroupId = accessGroup.AccessGroupId
	describeMountPointsRsp, err := this.client.DescribeMountPoints(describeMountPointsReq)
	if err != nil {
		return  nil,err
	}
	var mountPoints = describeMountPointsRsp.Response.MountPoints
	// 2.查询文件系统列表
	cHdfsFileSystems, err := this.DescribeCHdfsFileSystems()
	var cHdfsFileSystemSet =make(map[string]*chdfs.FileSystem, len(cHdfsFileSystems))
	for _, cHdfsFileSystem := range cHdfsFileSystems {
		cHdfsFileSystemSet[*cHdfsFileSystem.FileSystemId] = cHdfsFileSystem
	}
	for _, mountPoint := range mountPoints {
		if _,ok:=cHdfsFileSystemSet[*mountPoint.FileSystemId]; ok {
			mountedCHdfsFileSystems = append(mountedCHdfsFileSystems,&MountedCHdfsFileSystem{
				AccessGroupId:	*accessGroup.AccessGroupId,
				MountPointId:	*mountPoint.MountPointId,
				FileSystemId:	*mountPoint.FileSystemId,
				FileSystemName: *cHdfsFileSystemSet[*mountPoint.FileSystemId].FileSystemName,
			})
		}
	}
	return mountedCHdfsFileSystems,nil
}

func (this *CHdfsService) CreateMountPoint(fileSystemId string)  (mountPointId string,err error) {
	oceanusMountPointName := this.GenerateMountPointName()
	var mountPointStatus = MountPointStatusOpen
	createMountPointReq := chdfs.NewCreateMountPointRequest()
	createMountPointReq.FileSystemId = &fileSystemId
	createMountPointReq.MountPointName = &oceanusMountPointName
	createMountPointReq.MountPointStatus = &mountPointStatus
	createMountPointRsp, err := this.client.CreateMountPoint(createMountPointReq)
	if err != nil {
		return  "",err
	}
	return  *createMountPointRsp.Response.MountPoint.MountPointId, nil
}

func (this *CHdfsService) DescribeMountPoints(fileSystemId string)  (mountPoints []*chdfs.MountPoint,err error) {
	describeMountPointsReq := chdfs.NewDescribeMountPointsRequest()
	describeMountPointsReq.FileSystemId = &fileSystemId
	describeMountPointsRsp, err := this.client.DescribeMountPoints(describeMountPointsReq)
	if err != nil {
		return  nil,err
	}
	return  describeMountPointsRsp.Response.MountPoints, nil
}

func (this *CHdfsService) FindOceanusMountPoint(fileSystemId string)  (mountPoint *chdfs.MountPoint,found bool,err error) {
	describeMountPointsReq := chdfs.NewDescribeMountPointsRequest()
	describeMountPointsReq.FileSystemId = &fileSystemId
	describeMountPointsRsp, err := this.client.DescribeMountPoints(describeMountPointsReq)
	if err != nil {
		return  nil,false,err
	}
	mountPoints := describeMountPointsRsp.Response.MountPoints
	for _, mountPoint := range mountPoints {
		if *mountPoint.MountPointName == this.GenerateMountPointName() {
			return mountPoint,true,nil
		}
	}
	return  nil, false,nil
}

func (this *CHdfsService) DescribeMountPoint(mountPointId string)  (mountPoints *chdfs.MountPoint,err error) {
	describeMountPointReq := chdfs.NewDescribeMountPointRequest()
	describeMountPointReq.MountPointId = &mountPointId
	describeMountPointsRsp, err := this.client.DescribeMountPoint(describeMountPointReq)
	if err != nil {
		return  nil,err
	}
	return  describeMountPointsRsp.Response.MountPoint, nil
}

func (this *CHdfsService) AssociateAccessGroups(accessGroupIds []*string,mountPointId *string)  error {
	associateAccessGroupsReq := chdfs.NewAssociateAccessGroupsRequest()
	associateAccessGroupsReq.AccessGroupIds = accessGroupIds
	associateAccessGroupsReq.MountPointId = mountPointId
	_, err := this.client.AssociateAccessGroups(associateAccessGroupsReq)
	if err != nil {
		return  err
	}
	return  nil
}
// 查询元数据加速桶
func (this *CHdfsService) ListMetaBuckets() (buckets []string, err error) {
	cosClient := cos.CosClient {
		Region:    this.region,
		SecretId:  this.secretId,
		SecretKey: this.secretKey,
		Token:     this.token,
	}
	cosBuckets, err := cosClient.ListCosBuckets()
	if err != nil {
		return nil,err
	}
	for _, bucket := range cosBuckets {
		cosClient := cos.CosClient {
			Bucket:    bucket,
			Region:    this.region,
			SecretId:  this.secretId,
			SecretKey: this.secretKey,
			Token:     this.token,
		}
		isMetaBucket, err := cosClient.IsMetaBucket()
		if err != nil {
			logger.Errorf("Failed to check meta bucket, with errors:%+v",err)
		} else {
			if isMetaBucket {
				buckets = append(buckets, bucket)
			}
		}
	}
	return buckets, nil
}
