package cost

import (
	"encoding/json"
	"errors"
	"fmt"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/profile"
	cvm "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/cvm/v20170312"
	monitor2 "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/monitor/v20180724"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/labels"
	"strings"
	table3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/Cvm"
	table4 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/monitor"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
	"time"

	cdbapi "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/cdb/v20170320"
	tkeSDK "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/tke/v20180525"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/dlocker"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/cost"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/tke"
	service4 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cbs"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cdb"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config/configure_center"
	cvm3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cvm"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/tke"
	yuntiService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/yunti"
	taskHandler "tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler"
)

func UpgradeCdb(req *cost.UpgradeCdbReq) (retCode string, errMsg string, response interface{}) {
	defer func() {
		if errs := recover(); errs != nil {
			msg := fmt.Sprintf("upgrade cdb %s panic , for clusterGroup:%s, errors:%+v", req.RequestId, req.SerialId, errs)
			retCode = controller.InternalError
			errMsg = msg
		}
	}()
	locker := dlocker.NewDlocker("oceanus-upgrade-cdb", req.SerialId, 8000)
	err := locker.Lock()
	if err != nil { // 加锁失败, 且没有 flowId, 就返回 flowId = 0 让计费重试
		logger.Infof("Another upgrade cdb process for %s has lock but not finished yet", req.SerialId)
		return controller.InternalError, "Another  upgrade cdb process instance is still going, not ready yet", nil
	}
	defer locker.UnLock()

	groupService, err := service2.NewClusterGroupServiceBySerialId(req.SerialId)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}

	cg := groupService.GetClusterGroup()

	cdbTable, err := groupService.GetCdb()
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}

	cdbInstance, err := DescribeDBInstance(cg.Region, cdbTable.InstanceId)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}

	response = cost.UpgradeCdbResp{OldMemory: *cdbInstance.Memory, OldVolume: *cdbInstance.Volume}

	if *cdbInstance.Memory == req.Memory && *cdbInstance.Volume == req.Volume {
		return controller.OK, "", response
	}

	if *cdbInstance.Memory > req.Memory && !req.CanDown {
		err = fmt.Errorf("cdb memory %d is large than req memory %d, CanDown is false", *cdbInstance.Memory, req.Memory)
		return controller.InternalError, err.Error(), response
	}

	if *cdbInstance.Volume > req.Volume && !req.CanDown {
		err = fmt.Errorf("cdb volume %d is large than req volume %d, CanDown is false", *cdbInstance.Memory, req.Memory)
		return controller.InternalError, err.Error(), response
	}

	if req.DryUpgrade {
		return controller.OK, "", response
	}

	cdbService := cdb.GetCdbService()
	err = cdbService.UpgradeDBInstanceResourceByNetworkEnvType(cg.NetEnvironmentType, cg.Region, cdbTable.InstanceId, req.Memory, req.Volume)
	if err != nil {
		return controller.InternalError, err.Error(), response
	}

	cdbService.UpdateInstanceInfo(cdbTable.Id, req.Memory, req.Volume)

	return controller.OK, "", response
}

func DescribeDBInstance(region, instanceId string) (*cdbapi.InstanceInfo, error) {
	cdbService := cdb.GetCdbService()
	describeReq := cdbService.NewDefaultDescribeDBInstancesRequestBuilder().WithInstancesIds([]string{instanceId}).Build()
	totalCount, cdbSet, err := cdbService.DescribeDBInstancesWithScsAccount(region, describeReq)
	if totalCount > 1 || err != nil {
		msg := fmt.Sprintf("DescribeDBInstances returned multiple instances or err, %s", instanceId)
		return nil, errors.New(msg)
	} else if totalCount == 0 || len(cdbSet) == 0 {
		msg := fmt.Sprintf("CDB instance does not exist, return success instead, %s", instanceId)
		return nil, errors.New(msg)
	}
	return cdbSet[0], nil
}

func DescribeRegionSet() (string, string, interface{}) {
	sql := "SELECT DISTINCT(Region) FROM ClusterGroup"
	cond := dao.NewCondition()
	cond.Ne("Status", constants.CLUSTER_GROUP_STATUS_DELETED)
	where, args := cond.GetWhere()
	count, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql+where, args)
	if err != nil {
		logger.Infof("getRegionSet sql %s return %v", sql, err)
		return controller.InternalError, err.Error(), nil
	}
	regionSet := make([]*string, 0, count)
	for _, d := range data {
		if v, ok := d["Region"]; ok {
			region := string(v)
			regionSet = append(regionSet, &region)
		}
	}
	return controller.OK, "", cost.DescribeRegionSetResp{RegionSet: regionSet}
}

func DescribeResourceOwner(req *cost.DescribeResourceOwnerReq) (retCode string, errMsg string, response interface{}) {
	defer func() {
		if errs := recover(); errs != nil {
			msg := fmt.Sprintf("describe resource owner %s panic , for resourceId:%s, errors:%+v", req.RequestId, req.ResourceId, errs)
			retCode = controller.InternalError
			errMsg = msg
		}
	}()
	locker := dlocker.NewDlocker("describe resource owner", req.ResourceId, 8000)
	err := locker.Lock()
	if err != nil {
		logger.Infof("Another describe resource owner process for %s has lock but not finished yet", req.ResourceId)
		return controller.InternalError, "Another describe resource owner process instance is still going, not ready yet", nil
	}
	defer locker.UnLock()

	var clusterGroup *table.ClusterGroup
	var k8sInstance *table2.Tke

	switch {
	case strings.HasPrefix(req.ResourceId, "cluster-"):
		clusterGroup, k8sInstance, err = describeResourceOwnerForCluster(req.ResourceId)
	case strings.HasPrefix(req.ResourceId, "cql-"):
		clusterGroup, k8sInstance, err = describeResourceOwnerForJob(req.ResourceId)
	case strings.HasPrefix(req.ResourceId, "cls-"):
		clusterGroup, k8sInstance, err = describeResourceOwnerForTke(req.ResourceId)
	case strings.HasPrefix(req.ResourceId, "ins-"):
		clusterGroup, k8sInstance, err = describeResourceOwnerForCvm(req.Region, req.ResourceId)
	case strings.HasPrefix(req.ResourceId, "np-"):
		clusterGroup, k8sInstance, err = describeResourceOwnerForNpCvm(req.Region, req.ResourceId)
	default:
		err = fmt.Errorf("%s not match resource id (%s, %s, %s, %s)", req.ResourceId, "cluster-xxxxxxxx", "cls-xxxxxxxx", "ins-xxxxxxxx", "np-xxxxxxxx-xxxxx")
	}
	resp := cost.DescribeResourceOwnerResp{
		ResourceId: req.ResourceId,
	}
	if clusterGroup != nil {
		resp.ClusterId = clusterGroup.SerialId
		resp.OwnerUin = clusterGroup.OwnerUin
		resp.OwnerAppId = clusterGroup.AppId
	}
	if k8sInstance != nil {
		resp.TkeId = k8sInstance.InstanceId
	}

	if err != nil {
		resp.ErrorMsg = err.Error()
	}
	if resp.ErrorMsg == "" && clusterGroup != nil && clusterGroup.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM {
		resp.ErrorMsg = fmt.Errorf("share cluster %s, not sure owner, please contact oceanus team", clusterGroup.SerialId).Error()
	}
	return controller.OK, "", resp
}

func describeResourceOwnerForCluster(serialId string) (clusterGroup *table.ClusterGroup, k8sInstance *table2.Tke, err error) {
	groupService, err := service2.NewClusterGroupServiceBySerialId(serialId)
	if err != nil {
		return
	}

	clusterGroup = groupService.GetClusterGroup()

	k8sInstance, err = groupService.GetTke()
	return
}

func describeResourceOwnerForNpCvm(region, serialId string) (clusterGroup *table.ClusterGroup, k8sInstance *table2.Tke, err error) {
	sql, args := dao.NewQueryBuilder("SELECT * FROM ClusterGroup").
		WhereEq("Type", constants.CLUSTER_GROUP_TYPE_UNIFORM).
		WhereEq("Status", constants.CLUSTER_GROUP_STATUS_RUNNING).
		Build()

	clusterGroups, err := service3.Fetch[table.ClusterGroup](sql, args)
	if err != nil {
		return
	}
	tkeService := tke.GetTkeService()
	for _, group := range clusterGroups {
		groupService, err := service2.NewClusterGroupServiceByGroup(group)
		if err != nil {
			return nil, nil, err
		}

		tkeInstance, err := groupService.GetTke()
		if err != nil {
			return nil, nil, err
		}
		_, instanceSet, err := tkeService.DescribeClusterInstanceForIdsWithScsAccountByNetEnvironmentType(group.NetEnvironmentType, region, tkeInstance.InstanceId, []*string{&serialId})
		if err != nil {
			return nil, nil, err
		}
		if len(instanceSet) > 0 {
			clusterGroup = group
			break
		}
	}

	if clusterGroup == nil {
		err = fmt.Errorf("cluster group %s not found", serialId)
		return
	}
	privateGroup, k8sInstance, err := describeTkeForCvm(clusterGroup, serialId)
	if privateGroup != nil {
		clusterGroup = privateGroup
	}
	return
}

func describeTkeForCvm(clusterGroup *table.ClusterGroup, cvmId string) (privateGroup *table.ClusterGroup, k8sInstance *table2.Tke, err error) {
	groupService, err := service2.NewClusterGroupServiceByGroup(clusterGroup)
	if err != nil {
		return
	}

	clusterGroup = groupService.GetClusterGroup()

	k8sInstance, err = groupService.GetTke()
	if err != nil {
		return
	}

	if clusterGroup.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE {
		return
	} else if clusterGroup.Type != constants.CLUSTER_GROUP_TYPE_UNIFORM {
		err = fmt.Errorf("cluster group %s type error", clusterGroup.SerialId)
		return
	}

	cluster, err := groupService.GetCluster()
	if err != nil {
		return
	}

	k8sService := k8s.GetK8sService()
	kubConfig := []byte(cluster.KubeConfig)
	client, err := k8sService.NewClient(kubConfig)
	if err != nil {
		return
	}

	nodeList, err := k8sService.ListNode(client, metav1.ListOptions{
		LabelSelector: fmt.Sprintf("%s==%s", "cloud.tencent.com/node-instance-id", cvmId),
	})
	if err != nil {
		return
	}
	if len(nodeList.Items) == 0 {
		err = fmt.Errorf("node %s not found, in cluster %s", cvmId, clusterGroup.SerialId)
		return
	}
	node := nodeList.Items[0]
	labelMap := node.GetObjectMeta().GetLabels()
	resourceType, ok := labelMap[constants.TKE_CVM_NODE_RESOURCE_TYPE]
	if !ok {
		err = fmt.Errorf("node %s resource type not found, in cluster %s", cvmId, clusterGroup.SerialId)
		return
	}
	if resourceType == constants.TKE_CVM_NODE_RESOURCE_TYPE_PRIVATE {
		appId, ok := labelMap[constants.TKE_CVM_NODE_APPID]
		if !ok {
			err = fmt.Errorf("node %s appid not found, in cluster %s", cvmId, clusterGroup.SerialId)
			return
		}
		sql, args := dao.NewQueryBuilder("SELECT * FROM ClusterGroup").
			WhereEq("AppId", appId).
			Build()

		privateGroup, err = service3.FetchRow[table.ClusterGroup](sql, args)
	} else {
		opts := metav1.ListOptions{
			FieldSelector: "spec.nodeName=" + node.Name,
			LabelSelector: "type=flink-native-kubernetes",
		}

		jobPodList, err := k8sService.ListPod(client, "", opts)
		if err != nil {
			err = fmt.Errorf("node %s job pod list error, in cluster %s", cvmId, clusterGroup.SerialId)
			return privateGroup, k8sInstance, err
		}
		jobSet := make([]string, 0)
		for _, item := range jobPodList.Items {
			jobSet = append(jobSet, item.Name)
		}
		err = fmt.Errorf("share cvm, not sure owner, please check JobSet (%s), or contact oceanus team", strings.Join(jobSet, ","))
		return privateGroup, k8sInstance, err
	}
	return
}

func describeResourceOwnerForCvm(region, serialId string) (clusterGroup *table.ClusterGroup, k8sInstance *table2.Tke, err error) {
	cvmService := cvm3.GetCvmService()

	instances := []*string{&serialId}

	instanceSet, err := cvmService.DescribeInstancesWithScsAccount(constants.NETWORK_ENV_CLOUD_VPC, region, instances)
	if err != nil {
		return
	}
	if len(instanceSet) <= 0 {
		instanceSet, err = cvmService.DescribeInstancesWithScsAccount(constants.NETWORK_ENV_INNER_VPC, region, instances)
	}
	if err != nil {
		return
	}
	if len(instanceSet) <= 0 {
		err = fmt.Errorf("cvm instance %s not found", serialId)
		return
	}
	instance := instanceSet[0]
	vpcId := instance.VirtualPrivateCloud.VpcId

	sql, args := dao.NewQueryBuilder("SELECT cg.* FROM ClusterGroup cg LEFT JOIN Cluster c ON cg.Id = c.ClusterGroupId").
		WhereEq("c.VpcId", vpcId).
		Build()

	clusterGroups, err := service3.Fetch[table.ClusterGroup](sql, args)
	if err != nil {
		return
	}
	if len(clusterGroups) == 1 {
		clusterGroup = clusterGroups[0]
	} else {
		for _, group := range clusterGroups {
			if group.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM {
				clusterGroup = group
				break
			}
		}
	}
	if clusterGroup == nil {
		err = fmt.Errorf("cluster group %s not found", serialId)
		return
	}
	privateGroup, k8sInstance, err := describeTkeForCvm(clusterGroup, serialId)
	if privateGroup != nil {
		clusterGroup = privateGroup
	}
	return
}

func describeResourceOwnerForTke(serialId string) (clusterGroup *table.ClusterGroup, k8sInstance *table2.Tke, err error) {
	sql, args := dao.NewQueryBuilder("SELECT cg.* FROM ClusterGroup cg LEFT JOIN Cluster c ON cg.Id = c.ClusterGroupId LEFT JOIN Tke t ON c.Id = t.ClusterId").
		WhereEq("t.InstanceId", serialId).
		Build()

	clusterGroup, err = service3.FetchRow[table.ClusterGroup](sql, args)
	if err != nil {
		return
	}

	groupService, err := service2.NewClusterGroupServiceByGroup(clusterGroup)
	if err != nil {
		return
	}

	clusterGroup = groupService.GetClusterGroup()

	k8sInstance, err = groupService.GetTke()
	return
}

func describeResourceOwnerForJob(serialId string) (clusterGroup *table.ClusterGroup, k8sInstance *table2.Tke, err error) {
	if len(serialId) >= 12 {
		serialId = serialId[:12]
	}

	sql, args := dao.NewQueryBuilder("SELECT cg.* FROM ClusterGroup cg LEFT JOIN Job j ON j.ClusterGroupId = cg.Id").
		WhereEq("j.SerialId", serialId).
		Build()

	clusterGroup, err = service3.FetchRow[table.ClusterGroup](sql, args)
	if err != nil {
		return
	}

	groupService, err := service2.NewClusterGroupServiceByGroup(clusterGroup)
	if err != nil {
		return
	}

	clusterGroup = groupService.GetClusterGroup()

	k8sInstance, err = groupService.GetTke()
	return
}

func ModifyClusterOpenPolicy(req *cost.ModifyClusterOpenPolicyReq) (retCode string, errMsg string, response interface{}) {
	defer func() {
		if errs := recover(); errs != nil {
			msg := fmt.Sprintf("modify cluster open policy %s panic , for clusterGroup:%s, errors:%+v", req.RequestId, req.SerialId, errs)
			retCode = controller.InternalError
			errMsg = msg
		}
	}()
	locker := dlocker.NewDlocker("modify cluster open policy", req.SerialId, 8000)
	err := locker.Lock()
	if err != nil {
		logger.Infof("Another modify cluster open policy process for %s has lock but not finished yet", req.SerialId)
		return controller.InternalError, "Anothermodify cluster open policy  process instance is still going, not ready yet", nil
	}
	defer locker.UnLock()

	groupService, err := service2.NewClusterGroupServiceBySerialId(req.SerialId)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}

	clusterGroup := groupService.GetClusterGroup()

	tkeList, err := groupService.GetTkeList()
	if err != nil {
		msg := fmt.Sprintf("modify cluster open policy -> GetTkeList(%s) error:%+v", req.SerialId, err)
		logger.Error(msg)
		return controller.InternalError, msg, nil
	}
	if len(tkeList) != 1 {
		return controller.InternalError, fmt.Sprintf("tke length is %d, not 1", len(tkeList)), nil
	}
	tkeInstance := tkeList[0]

	h := &taskHandler.ApplyTKEHandler{}
	err = h.CheckAndOpenPolicyList(clusterGroup, tkeInstance)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}

	return controller.OK, "", cost.ModifySchedulerPolicyResp{}
}

func EnableClusterAudit(req *cost.EnableClusterAuditReq) (retCode string, errMsg string, response interface{}) {
	defer func() {
		if errs := recover(); errs != nil {
			msg := fmt.Sprintf("enable cluster audit %s panic , for clusterGroup:%s, errors:%+v", req.RequestId, req.SerialId, errs)
			retCode = controller.InternalError
			errMsg = msg
		}
	}()
	locker := dlocker.NewDlocker("enable cluster audit", req.SerialId, 8000)
	err := locker.Lock()
	if err != nil {
		logger.Infof("Another enable cluster audit process for %s has lock but not finished yet", req.SerialId)
		return controller.InternalError, "Another enable cluster audit  process instance is still going, not ready yet", nil
	}
	defer locker.UnLock()

	groupService, err := service2.NewClusterGroupServiceBySerialId(req.SerialId)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}

	clusterGroup := groupService.GetClusterGroup()

	tkeList, err := groupService.GetTkeList()
	if err != nil {
		msg := fmt.Sprintf("enable cluster audit -> GetTkeList(%s) error:%+v", req.SerialId, err)
		logger.Error(msg)
		return controller.InternalError, msg, nil
	}
	if len(tkeList) != 1 {
		return controller.InternalError, fmt.Sprintf("tke length is %d, not 1", len(tkeList)), nil
	}
	tkeInstance := tkeList[0]

	h := &taskHandler.ApplyTKEHandler{}
	err = h.CheckAndEnableClusterAudit(clusterGroup, tkeInstance)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}

	return controller.OK, "", cost.ModifySchedulerPolicyResp{}
}

func EnableClusterEventPersistence(req *cost.EnableClusterEventPersistenceReq) (retCode string, errMsg string, response interface{}) {
	defer func() {
		if errs := recover(); errs != nil {
			msg := fmt.Sprintf("enable cluster event persist %s panic , for clusterGroup:%s, errors:%+v", req.RequestId, req.SerialId, errs)
			retCode = controller.InternalError
			errMsg = msg
		}
	}()
	locker := dlocker.NewDlocker("enable cluster event", req.SerialId, 8000)
	err := locker.Lock()
	if err != nil {
		logger.Infof("Another enable cluster persist process for %s has lock but not finished yet", req.SerialId)
		return controller.InternalError, "Another enable cluster persist  process instance is still going, not ready yet", nil
	}
	defer locker.UnLock()

	groupService, err := service2.NewClusterGroupServiceBySerialId(req.SerialId)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}

	clusterGroup := groupService.GetClusterGroup()

	tkeList, err := groupService.GetTkeList()
	if err != nil {
		msg := fmt.Sprintf("enable cluster audit -> GetTkeList(%s) error:%+v", req.SerialId, err)
		logger.Error(msg)
		return controller.InternalError, msg, nil
	}
	if len(tkeList) != 1 {
		return controller.InternalError, fmt.Sprintf("tke length is %d, not 1", len(tkeList)), nil
	}
	tkeInstance := tkeList[0]

	h := &taskHandler.ApplyTKEHandler{}
	err = h.CheckAndEnableEventPersistence(clusterGroup, tkeInstance)
	if err != nil {
		return controller.InternalError, fmt.Sprintf("tke length is %d, not 1", len(tkeList)), nil
	}

	return controller.OK, "", cost.EnableClusterEventPersistenceResp{}
}

func ModifySchedulerPolicy(req *cost.ModifySchedulerPolicyReq) (retCode string, errMsg string, response interface{}) {
	defer func() {
		if errs := recover(); errs != nil {
			msg := fmt.Sprintf("modify scheduler policy %s panic , for clusterGroup:%s, errors:%+v", req.RequestId, req.SerialId, errs)
			retCode = controller.InternalError
			errMsg = msg
		}
	}()
	locker := dlocker.NewDlocker("modify scheduler policy", req.SerialId, 8000)
	err := locker.Lock()
	if err != nil {
		logger.Infof("Another modify scheduler policy process for %s has lock but not finished yet", req.SerialId)
		return controller.InternalError, "Another modify scheduler policy  process instance is still going, not ready yet", nil
	}
	defer locker.UnLock()

	groupService, err := service2.NewClusterGroupServiceBySerialId(req.SerialId)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}

	clusterGroup := groupService.GetClusterGroup()

	tkeList, err := groupService.GetTkeList()
	if err != nil {
		msg := fmt.Sprintf("UpgradeController -> GetTkeList(%s) error:%+v", req.SerialId, err)
		logger.Error(msg)
		return controller.InternalError, msg, nil
	}
	if len(tkeList) != 1 {
		return controller.InternalError, fmt.Sprintf("tke length is %d, not 1", len(tkeList)), nil
	}
	tkeInstance := tkeList[0]

	tkeService := tke.NewTkeService()
	secretId, secretKey, err := service4.GetSecretIdAndKeyByNetworkEnvType(clusterGroup.NetEnvironmentType)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}

	builder := tke.NewDefaultModifyClusterSchedulerPolicyBuilder().WithClusterId(tkeInstance.InstanceId)
	for _, priority := range req.Priorities {
		builder.WithPriority(priority.Name, priority.Weight)
	}
	mReq := builder.Build()
	logger.Infof("ModifyClusterSchedulerPolicy Request: %s", mReq.ToJsonString())

	err = tkeService.ModifyClusterSchedulerPolicy(secretId, secretKey, "", clusterGroup.Region, mReq)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}

	return controller.OK, "", cost.ModifySchedulerPolicyResp{}
}

func GetMonitorData(req *cost.GetJobMonitorDataReq) (retCode string, errMsg string, response interface{}) {
	defer func() {
		if errs := recover(); errs != nil {
			msg := fmt.Sprintf("GetMonitorData %s panic, errors:%+v", req.RequestId, errs)
			retCode = controller.InternalError
			errMsg = msg
		}
	}()

	sql, args := dao.NewQueryBuilder("select * from Job").
		WhereIn("SerialId", common.StringValues(req.JobSet)).
		Build()

	jobSet, err := service3.Fetch[table4.Job](sql, args)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}

	uinJobsMap := make(map[string][]*string)
	for _, j := range jobSet {
		uinJobsMap[j.OwnerUin] = append(uinJobsMap[j.OwnerUin], &j.SerialId)
	}

	dataResp := &cost.GetJobMonitorDataResp{
		Period:     req.Period,
		MetricName: req.MetricName,
		StartTime:  req.StartTime,
		EndTime:    req.EndTime,
	}

	for uin, jobs := range uinJobsMap {
		secretId, secretKey, token, _, err := service4.StsAssumeRole(uin, uin, req.Region)
		if err != nil {
			return controller.InternalError, err.Error(), nil
		}
		credential := common.NewTokenCredential(
			secretId,
			secretKey,
			token,
		)
		cpf := profile.NewClientProfile()
		cpf.HttpProfile.ReqMethod = "POST"
		cpf.HttpProfile.Endpoint = "monitor.internal.tencentcloudapi.com"
		cpf.HttpProfile.ReqTimeout = 300

		client, _ := monitor2.NewClient(credential, req.Region, cpf)
		request := monitor2.NewGetMonitorDataRequest()
		request.Namespace = common.StringPtr("QCE/TSTREAM")
		request.MetricName = common.StringPtr(req.MetricName)
		request.Period = common.Uint64Ptr(req.Period)
		request.StartTime = common.StringPtr(req.StartTime)
		request.EndTime = common.StringPtr(req.EndTime)
		instances := make([]*monitor2.Instance, 0)
		for _, job := range jobs {
			dimensions := make([]*monitor2.Dimension, 0)
			dimensions = append(dimensions, &monitor2.Dimension{
				Name:  common.StringPtr("tjob_id"),
				Value: job})
			instances = append(instances, &monitor2.Instance{
				Dimensions: dimensions,
			})
		}
		request.Instances = instances

		resp, err := client.GetMonitorData(request)
		if err != nil {
			return controller.InternalError, err.Error(), nil
		}
		dataResp.DataPoints = append(dataResp.DataPoints, resp.Response.DataPoints...)
	}
	return controller.OK, "", dataResp
}

func BuyController(req *cost.BuyControllerReq) (retCode string, errMsg string, response interface{}) {
	defer func() {
		if errs := recover(); errs != nil {
			msg := fmt.Sprintf("buy controller %s panic , for clusterGroup:%s, errors:%+v", req.RequestId, req.SerialId, errs)
			retCode = controller.InternalError
			errMsg = msg
		}
	}()
	locker := dlocker.NewDlocker("oceanus-buy-controller", req.SerialId, 8000)
	err := locker.Lock()
	if err != nil {
		logger.Infof("Another buy controller process for %s has lock but not finished yet", req.SerialId)
		return controller.InternalError, "Another buy controller process instance is still going, not ready yet", nil
	}
	defer locker.UnLock()

	groupService, err := service2.NewClusterGroupServiceBySerialId(req.SerialId)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}

	clusterGroup := groupService.GetClusterGroup()

	cluster, err := groupService.GetActiveCluster()
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}
	tkeList, err := groupService.GetTkeList()
	if err != nil {
		msg := fmt.Sprintf("UpgradeController -> GetTkeList(%s) error:%+v", req.SerialId, err)
		logger.Error(msg)
		return controller.InternalError, msg, nil
	}
	if len(tkeList) != 1 {
		return controller.InternalError, fmt.Sprintf("tke length is %d, not 1", len(tkeList)), nil
	}
	k8sInstance := tkeList[0]

	if k8sInstance.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		return controller.OK, "", cost.DescribeCvmResp{Message: "Cluster is eks cluster"}
	}

	if clusterGroup.Type == constants.CLUSTER_GROUP_TYPE_SUB_EKS {
		return controller.OK, "", cost.DescribeCvmResp{Message: "Cluster is sub eks cluster"}
	}

	taskExecRequest := &flow.TaskExecRequest{
		Params: map[string]string{
			constants.FLOW_PARAM_REQUEST_ID:       fmt.Sprintf("%s_%s", req.SerialId, req.RequestId),
			constants.FLOW_PARAM_CLUSTER_GROUP_ID: fmt.Sprintf("%d", clusterGroup.Id),
			constants.FLOW_PARAM_CLUSTER_ID:       fmt.Sprintf("%d", cluster.Id),
			constants.FLOW_PARAM_CLUSTER_TYPE:     fmt.Sprintf("%d", k8sInstance.ClusterType),
		}}

	if req.Count <= 0 {
		return controller.InternalError, fmt.Sprintf("Count can not less zero"), nil
	}
	//共享集群母集群
	if clusterGroup.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM || clusterGroup.AgentSerialId != "" {
		c, err := deploy.New(taskExecRequest)
		if err != nil {
			return controller.InternalError, err.Error(), nil
		}
		cc, err := c.FlowCC.TkeCC().ClusterConfig(clusterGroup)
		if err != nil {
			return controller.InternalError, err.Error(), nil
		}
		cvmConfList, err := service3.GetTableService().ListCvmSaleConfByInstanceType(cc.WorkerSpec, 0, 1)
		if err != nil {
			return controller.InternalError, err.Error(), nil
		}
		if len(cvmConfList) <= 0 {
			err = fmt.Errorf("can not find cvm")
			return controller.InternalError, err.Error(), nil
		}
		h := &taskHandler.ApplyTKEHandler{}
		zone := req.Zone
		if zone == "" {
			zone = clusterGroup.Zone
		}
		if support, err := clusterGroup.IsSupportZone(zone); err != nil {
			return controller.InternalError, err.Error(), nil
		} else if !support {
			err = fmt.Errorf("cluster %s not support zone %s", clusterGroup.SerialId, zone)
			return controller.InternalError, err.Error(), nil
		}

		zoneId, err := taskHandler.GetZoneShortId(zone)
		if err != nil {
			return controller.InternalError, err.Error(), nil
		}
		name := fmt.Sprintf("%s-%d-", constants.TKE_CONTROL_NODE_LABEL_VAL, zoneId)
		label := h.GetControllerLabel(c)
		label[constants.TKE_CVM_NODE_ZONE] = zone
		label[constants.TKE_CVM_ZK_LABEL_KEY] = constants.TKE_ZK_NODE_LABEL_VAL
		buyWorkerCount, err := h.CheckAndAddWorker(c,
			zone,
			req.Count,
			cvmConfList[0],
			false,
			label,
			constants.TKE_CONTROL_NODE_DISK_SIZE_UNIFORM,
			constants.TKE_CVM_DISK_TYPE,
			name,
			nil, "", constants.TKE_NODE_SYSTEM_DISK_SIZE)
		if err != nil {
			return controller.InternalError, err.Error(), nil
		}
		req.Count = buyWorkerCount
	} else if clusterGroup.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && clusterGroup.AgentSerialId == "" &&
		k8sInstance.ArchGeneration < constants.TKE_ARCH_GENERATION_V5 { //独享集群，并且版本小于5
		upgradeControllerService, err := taskHandler.NewUpgradeControllerService(req.RequestId, taskExecRequest)
		if err != nil {
			return controller.InternalError, err.Error(), nil
		}
		if err = upgradeControllerService.Init(); err != nil {
			return controller.InternalError, err.Error(), nil
		}
		controllerSpec := req.Spec
		if controllerSpec == "" {
			controllerSpec, err = upgradeControllerService.GetNewControllerSpec()
			if err != nil {
				return controller.InternalError, err.Error(), nil
			}
		}
		logger.Infof("upgrade controller spec: %s", controllerSpec)
		if controllerSpec != constants.TKE_CONTROL_NODE_INSTANCE_TYPE && !req.Force {
			return controller.OK, "", cost.BuyControllerResp{Count: 0}
		}

		err = upgradeControllerService.AddNewController(controllerSpec, req.Count)
		if err != nil {
			return controller.InternalError, err.Error(), nil
		}
	} else {
		err = fmt.Errorf("cluster can not add controller %+v", *clusterGroup)
		return controller.InternalError, err.Error(), nil
	}

	return controller.OK, "", cost.BuyControllerResp{Count: req.Count}
}

func UpgradeController(req *cost.UpgradeControllerReq) (retCode string, errMsg string, response interface{}) {
	defer func() {
		if errs := recover(); errs != nil {
			msg := fmt.Sprintf("upgrade controller %s panic , for clusterGroup:%s, errors:%+v", req.RequestId, req.SerialId, errs)
			retCode = controller.InternalError
			errMsg = msg
		}
	}()
	locker := dlocker.NewDlocker("oceanus-upgrade-controller", req.SerialId, 8000)
	err := locker.Lock()
	if err != nil { // 加锁失败, 且没有 flowId, 就返回 flowId = 0 让计费重试
		logger.Infof("Another upgrade controller process for %s has lock but not finished yet", req.SerialId)
		return controller.InternalError, "Another  upgrade controller process instance is still going, not ready yet", nil
	}
	defer locker.UnLock()

	if strings.Compare(configure_center.CC(req.Region).LatestClusterVersion(), "20230105-6.9.0") < 0 {
		return controller.InternalError, "Cluster Version less than 20230105-6.9.0", nil
	}

	groupService, err := service2.NewClusterGroupServiceBySerialId(req.SerialId)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}

	_, err = groupService.CanUpgrade()
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}

	tkeList, err := groupService.GetTkeList()
	if err != nil {
		msg := fmt.Sprintf("UpgradeController -> GetTkeList(%s) error:%+v", req.SerialId, err)
		logger.Error(msg)
		return controller.InternalError, msg, nil
	}
	if len(tkeList) != 1 {
		return controller.InternalError, fmt.Sprintf("tke length is %d, not 1", len(tkeList)), nil
	}
	tke := tkeList[0]
	if tke.ArchGeneration > constants.TKE_ARCH_GENERATION_V2 {
		return controller.InternalError, fmt.Sprintf("arch generation [%d] can not upgrade", tke.ArchGeneration), nil
	}
	if tke.ArchGeneration == constants.TKE_ARCH_GENERATION_V1 {
		err := upgradeControllerV2(groupService, tke)
		if err != nil {
			return controller.InternalError, err.Error(), nil
		}
		return controller.OK, "", cost.UpgradeControllerResp{}
	}

	flowId, err := upgradeController(req, groupService)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}

	return controller.OK, "", cost.UpgradeControllerResp{
		FlowId: flowId,
	}
}

// V2 升级成V3版本，但是不改变ZK的部署位置，其它的都改变
func upgradeControllerV2(groupService *service2.ClusterGroupService, tke *table2.Tke) (err error) {
	clusterGroup := groupService.GetClusterGroup()
	activeCluster, err := groupService.GetActiveCluster()
	if err != nil {
		return err
	}

	applyTKEHandler := &taskHandler.ApplyTKEHandler{}
	cc, err := configure_center.CC(clusterGroup.Region).FlowCC().TkeCC().ClusterConfig(clusterGroup)
	if err != nil {
		return err
	}

	txManager := service.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tke.ArchGeneration = constants.TKE_ARCH_GENERATION_V2
		err = applyTKEHandler.AddNodeLabel(clusterGroup, activeCluster, tke, 2, cc, false)
		if err != nil {
			return err
		}

		tx.UpdateObject(tke, tke.Id, "Tke")
		return nil
	}).Close()
	return nil
}

func upgradeController(req *cost.UpgradeControllerReq, groupService *service2.ClusterGroupService) (flowId int64, err error) {
	clusterList, err := groupService.GetClusterList()
	if err != nil {
		return 0, err
	}
	var activeCluster *table.Cluster
	for _, item := range clusterList {
		if item.RoleType == constants.CLUSTER_ROLE_TYPE_ACTIVE {
			activeCluster = item
		}
	}

	clusterGroup := groupService.GetClusterGroup()
	txManager := service.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		clusterGroup.Status = constants.CLUSTER_GROUP_STATUS_UPGRADE
		clusterGroup.UpdateTime = time.Now().Format("2006-01-02 15:04:05")

		tx.UpdateObject(clusterGroup, clusterGroup.Id, "ClusterGroup")

		docId := fmt.Sprintf("%s@%s", req.SerialId, req.RequestId)
		isEks, _ := service2.IsEks(activeCluster.ClusterGroupId)
		clusterType := constants.K8S_CLUSTER_TYPE_TKE
		if isEks {
			clusterType = constants.K8S_CLUSTER_TYPE_EKS
		}
		flowId, err = flow.CreateFlow(constants.FLOW_OCEANUS_UPGRADE_CONTROLLER, docId, 0, map[string]string{
			constants.FLOW_PARAM_REQUEST_ID:       req.RequestId,
			constants.FLOW_PARAM_CLUSTER_GROUP_ID: fmt.Sprintf("%d", clusterGroup.Id),
			constants.FLOW_PARAM_CLUSTER_ID:       fmt.Sprintf("%d", activeCluster.Id),
			constants.FLOW_PARAM_CLUSTER_TYPE:     fmt.Sprintf("%d", clusterType),
		}, nil)
		if err != nil {
			logger.Errorf("[%s] createFlow %s fail %v", req.RequestId, constants.FLOW_OCEANUS_UPGRADE_CONTROLLER, err)
			return err
		}

		logger.Infof("[%s] createFlow %s success, flowId %d", req.RequestId, constants.FLOW_OCEANUS_UPGRADE_CONTROLLER, flowId)

		return nil
	}).Close()
	return flowId, nil
}

func DeleteUnschedulerWorker(req *cost.DeleteUnschedulerWorkerReq) (retCode string, errMsg string, response interface{}) {
	defer func() {
		if errs := recover(); errs != nil {
			msg := fmt.Sprintf("buy worker %s panic , for clusterGroup:%s, errors:%+v", req.RequestId, req.SerialId, errs)
			retCode = controller.InternalError
			errMsg = msg
		}
	}()
	locker := dlocker.NewDlocker("delete-unscheduler-worker", req.SerialId, 8000)
	err := locker.Lock()
	if err != nil {
		logger.Infof("Another buy worker process for %s has lock but not finished yet", req.SerialId)
		return controller.InternalError, "Another  buy worker process instance is still going, not ready yet", nil
	}
	defer locker.UnLock()

	groupService, err := service2.NewClusterGroupServiceBySerialId(req.SerialId)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}

	clusterGroup := groupService.GetClusterGroup()

	cluster, err := groupService.GetActiveCluster()
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}
	clusterType, err := service3.GetTableService().GetTkeClusterType(cluster.Id)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}

	taskExecRequest := &flow.TaskExecRequest{
		Params: map[string]string{
			constants.FLOW_PARAM_REQUEST_ID:       fmt.Sprintf("%s_%s", req.SerialId, req.RequestId),
			constants.FLOW_PARAM_CLUSTER_GROUP_ID: fmt.Sprintf("%d", clusterGroup.Id),
			constants.FLOW_PARAM_CLUSTER_ID:       fmt.Sprintf("%d", cluster.Id),
			constants.FLOW_PARAM_CLUSTER_TYPE:     fmt.Sprintf("%d", clusterType),
		}}

	upgradeWorkerService, err := taskHandler.NewWorkerService(req.RequestId, taskExecRequest)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}
	if err = upgradeWorkerService.Init(); err != nil {
		return controller.InternalError, err.Error(), nil
	}

	if len(req.InstanceIdSet) <= 0 {
		unschedulerSet, err := upgradeWorkerService.ListUnschedulerWorker()
		req.InstanceIdSet = unschedulerSet
		if err != nil {
			return controller.InternalError, err.Error(), nil
		}
	}
	if req.DryScheduler || len(req.InstanceIdSet) < 0 {
		return controller.OK, "", cost.DeleteUnschedulerWorkerResp{Count: int64(len(req.InstanceIdSet)), InstanceIdSet: req.InstanceIdSet}
	}

	resp := cost.DeleteUnschedulerWorkerResp{Count: int64(len(req.InstanceIdSet)), InstanceIdSet: req.InstanceIdSet}

	err = upgradeWorkerService.DeleteWorkerFromTke(req.InstanceIdSet)
	if err != nil {
		resp.ErrorMsg = err.Error()
		return controller.OK, "", resp
	}

	cvmService := cvm3.GetCvmService()
	cvmInstanceSet, err := cvmService.DescribeInstancesWithScsAccount(clusterGroup.NetEnvironmentType, clusterGroup.Region, req.InstanceIdSet)
	if err != nil {
		resp.ErrorMsg = err.Error()
		return controller.OK, "", resp
	}

	if len(cvmInstanceSet) == 0 {
		resp.ErrorMsg = "no cvm found"
		return controller.OK, "", resp
	}
	canPooledIds := make([]*string, 0)
	if req.PoolCvm {
		canPooledIds, err = taskHandler.FindCanPooledCvm(clusterGroup, req.InstanceIdSet)
		if err != nil {
			resp.ErrorMsg = err.Error()
			return controller.OK, "", resp
		}
		taskHandler.PoolCVM(clusterGroup, canPooledIds)
		resp.PooledIdSet = canPooledIds
	}

	deleteUnschedulerSet := make([]*string, 0)
	for _, instance := range cvmInstanceSet {
		if inSlice(canPooledIds, *instance.InstanceId) {
			continue
		}
		if *instance.InstanceState != "TERMINATING" {
			deleteUnschedulerSet = append(deleteUnschedulerSet, instance.InstanceId)
		}
	}
	resp.DeleteIdSet = deleteUnschedulerSet
	// 如果是弹性网卡模式，先删除节点的弹性网卡
	if clusterGroup.NetEniType == constants.CLUSTER_NET_ENI_NODE && (clusterGroup.NodeEniType == constants.CLUSTER_NODE_ENI_ETH1 ||
		clusterGroup.NodeEniType == constants.CLUSTER_NODE_ENI_ETH0) {
		for _, instanceId := range deleteUnschedulerSet {
			err = taskHandler.DeleteEniForNode(*instanceId, clusterGroup, groupService)
			if err != nil {
				resp.ErrorMsg = fmt.Sprintf("failed to delete ENIs: %v", err)
				return controller.OK, "", resp
			}
		}
	}

	if clusterGroup.NetEnvironmentType == constants.NETWORK_ENV_INNER_VPC {
		serialId := clusterGroup.SerialId
		err = yuntiService.TerminateInstances(fmt.Sprintf("CSIG流计算Oceanus集群%s销毁", serialId), deleteUnschedulerSet, clusterGroup.Region, serialId)
		if err != nil {
			resp.ErrorMsg = err.Error()
		}
		return controller.OK, "", resp
	}

	err = cbs.GetCbsService(clusterGroup.NetEnvironmentType, clusterGroup.Region).TerminateCbsFromCVM(deleteUnschedulerSet)
	if err != nil {
		resp.ErrorMsg = err.Error()
		return controller.OK, "", resp
	}

	err = cvmService.TerminateInstancesWithScsAccount(clusterGroup.Region, deleteUnschedulerSet)
	if err != nil {
		resp.ErrorMsg = err.Error()
		return controller.OK, "", resp
	}

	return controller.OK, "", resp
}

func inSlice(ss []*string, s string) bool {
	if len(ss) == 0 {
		return false
	}
	for _, v := range ss {
		if *v == s {
			return true
		}
	}
	return false
}

func DeleteUnuseCdb(req *cost.DeleteUnuseCdbReq) (retCode string, errMsg string, response interface{}) {
	defer func() {
		if errs := recover(); errs != nil {
			msg := fmt.Sprintf("delete unuse %s panic , for clusterGroup:%s, errors:%+v", req.RequestId, req.SerialId, errs)
			retCode = controller.InternalError
			errMsg = msg
		}
	}()
	locker := dlocker.NewDlocker("delete-unuse-cdb", req.SerialId, 8000)
	err := locker.Lock()
	if err != nil {
		logger.Infof("Another delete cdb process for %s has lock but not finished yet", req.SerialId)
		return controller.InternalError, "Another delete cdb process instance is still going, not ready yet", nil
	}
	defer locker.UnLock()

	groupService, err := service2.NewClusterGroupServiceBySerialId(req.SerialId)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}

	clusterGroup := groupService.GetClusterGroup()

	if len(req.CdbIdSet) <= 0 {
		cdb, err := groupService.GetCdb()
		if err != nil {
			return controller.InternalError, err.Error(), nil
		}
		req.CdbIdSet = append(req.CdbIdSet, &cdb.InstanceId)
	}
	if req.DryScheduler || len(req.CdbIdSet) < 0 {
		return controller.OK, "", cost.DeleteUnschedulerWorkerResp{Count: int64(len(req.CdbIdSet)), InstanceIdSet: req.CdbIdSet}
	}

	resp := cost.DeleteUnuseCdbResp{Count: int64(len(req.CdbIdSet)), CdbIdSet: req.CdbIdSet}

	cdbService := cdb.GetCdbService()
	for _, instance := range req.CdbIdSet {
		describeReq := cdbService.NewDefaultDescribeDBInstancesRequestBuilder().WithInstancesIds([]string{*instance}).Build()
		totalCount, cdbSet, err := cdbService.DescribeDBInstancesWithScsAccountByNetEnvironmentType(clusterGroup.NetEnvironmentType, clusterGroup.Region, describeReq)
		if totalCount > 1 || err != nil {
			msg := fmt.Sprintf("cdb instance %s count %d", *instance, totalCount)
			return controller.InternalError, msg, nil
		} else if totalCount == 0 || len(cdbSet) == 0 {
			logger.Errorf("CDB instance does not exist, return success instead.", totalCount)
			continue
		}

		if cdbService.IsInstanceIsolate(cdbSet[0]) {
			logger.Infof("cdb %s status %d, isolate done, skip it", *instance, cdbSet[0].Status)
			continue
		}

		req := cdbService.NewDefaultIsolateDBInstanceRequestBuilder().WithInstance(*instance).Build()
		_, err = cdbService.IsolateDBInstanceWithScsAccountByNetEnvironmentType(clusterGroup.NetEnvironmentType, clusterGroup.Region, req)
		if err != nil {
			// 检查设备的状态， 如果为  4， 5， 那表明在隔离中或者 已隔离， 那无需在调用隔离了
			logger.Infof("IsolateDBInstance return err:%v", err)
			return controller.InternalError, err.Error(), nil
		}
	}

	return controller.OK, "", resp
}

func SchedulerWorker(req *cost.SchedulerWorkerReq) (retCode string, errMsg string, response interface{}) {
	defer func() {
		if errs := recover(); errs != nil {
			msg := fmt.Sprintf("buy worker %s panic , for clusterGroup:%s, errors:%+v", req.RequestId, req.SerialId, errs)
			retCode = controller.InternalError
			errMsg = msg
		}
	}()
	locker := dlocker.NewDlocker("scheduler-worker", req.SerialId, 8000)
	err := locker.Lock()
	if err != nil {
		logger.Infof("Another buy worker process for %s has lock but not finished yet", req.SerialId)
		return controller.InternalError, "Another  buy worker process instance is still going, not ready yet", nil
	}
	defer locker.UnLock()

	groupService, err := service2.NewClusterGroupServiceBySerialId(req.SerialId)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}

	clusterGroup := groupService.GetClusterGroup()

	cluster, err := groupService.GetActiveCluster()
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}
	clusterType, err := service3.GetTableService().GetTkeClusterType(cluster.Id)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}

	taskExecRequest := &flow.TaskExecRequest{
		Params: map[string]string{
			constants.FLOW_PARAM_REQUEST_ID:       fmt.Sprintf("%s_%s", req.SerialId, req.RequestId),
			constants.FLOW_PARAM_CLUSTER_GROUP_ID: fmt.Sprintf("%d", clusterGroup.Id),
			constants.FLOW_PARAM_CLUSTER_ID:       fmt.Sprintf("%d", cluster.Id),
			constants.FLOW_PARAM_CLUSTER_TYPE:     fmt.Sprintf("%d", clusterType),
		}}

	upgradeWorkerService, err := taskHandler.NewWorkerService(req.RequestId, taskExecRequest)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}
	if err = upgradeWorkerService.Init(); err != nil {
		return controller.InternalError, err.Error(), nil
	}

	if len(req.InstanceIdSet) <= 0 {
		if req.ForceVersion > 0 {
			upgradeWorkerService.Tke.ArchGeneration = req.ForceVersion
		}
		_, unStandardCvmIdSet, err := upgradeWorkerService.ComputeStandardWorkerNum()
		if err != nil {
			return controller.InternalError, err.Error(), nil
		}
		req.InstanceIdSet = unStandardCvmIdSet
	}

	if req.DryScheduler {
		return controller.OK, "", cost.SchedulerWorkerResp{Count: int64(len(req.InstanceIdSet)), InstanceIdSet: req.InstanceIdSet}
	}
	errInstanceIdSet := make([]*string, 0)
	for _, instanceId := range req.InstanceIdSet {
		err := upgradeWorkerService.ScheduleWorker(*instanceId, !req.Scheduler)
		if err != nil {
			errInstanceIdSet = append(errInstanceIdSet, instanceId)
			logger.Errorf("instance %s, schedule err: %s", *instanceId, err.Error())
		}
		time.Sleep(time.Millisecond * 100)
	}

	return controller.OK, "", cost.SchedulerWorkerResp{Count: int64(len(req.InstanceIdSet)),
		InstanceIdSet: req.InstanceIdSet, ErrInstanceIdSet: errInstanceIdSet,
	}
}

func BuyWorker(req *cost.BuyWorkerReq) (retCode string, errMsg string, response interface{}) {
	defer func() {
		if errs := recover(); errs != nil {
			msg := fmt.Sprintf("buy worker %s panic , for clusterGroup:%s, errors:%+v", req.RequestId, req.SerialId, errs)
			retCode = controller.InternalError
			errMsg = msg
		}
	}()
	locker := dlocker.NewDlocker("oceanus-buy-worker", req.SerialId, 8000)
	err := locker.Lock()
	if err != nil {
		logger.Infof("Another buy worker process for %s has lock but not finished yet", req.SerialId)
		return controller.InternalError, "Another  buy worker process instance is still going, not ready yet", nil
	}
	defer locker.UnLock()

	groupService, err := service2.NewClusterGroupServiceBySerialId(req.SerialId)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}

	clusterGroup := groupService.GetClusterGroup()

	cluster, err := groupService.GetActiveCluster()
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}
	tkeList, err := groupService.GetTkeList()
	if err != nil {
		msg := fmt.Sprintf("UpgradeController -> GetTkeList(%s) error:%+v", req.SerialId, err)
		logger.Error(msg)
		return controller.InternalError, msg, nil
	}
	if len(tkeList) != 1 {
		return controller.InternalError, fmt.Sprintf("tke length is %d, not 1", len(tkeList)), nil
	}
	k8sInstance := tkeList[0]

	if k8sInstance.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		return controller.OK, "", cost.DescribeCvmResp{Message: "Cluster is eks cluster"}
	}

	if clusterGroup.Type == constants.CLUSTER_GROUP_TYPE_SUB_EKS {
		return controller.OK, "", cost.DescribeCvmResp{Message: "Cluster is sub eks cluster"}
	}

	taskExecRequest := &flow.TaskExecRequest{
		Params: map[string]string{
			constants.FLOW_PARAM_REQUEST_ID:       fmt.Sprintf("%s_%s", req.SerialId, req.RequestId),
			constants.FLOW_PARAM_CLUSTER_GROUP_ID: fmt.Sprintf("%d", clusterGroup.Id),
			constants.FLOW_PARAM_CLUSTER_ID:       fmt.Sprintf("%d", cluster.Id),
			constants.FLOW_PARAM_CLUSTER_TYPE:     fmt.Sprintf("%d", k8sInstance.ClusterType),
		}}

	c, err := deploy.New(taskExecRequest)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}
	h := &taskHandler.ApplyTKEHandler{}
	var workerConf *table3.CvmSaleConfig

	var forceSpec bool

	if req.Spec == "" {
		wn, cn, cvmConf, err := h.ComputeWorkerNodeAndCuNum(clusterGroup.CuNum, c)
		if err != nil {
			return controller.InternalError, err.Error(), nil
		}
		logger.Infof("CalcClusterNodeCount: %d, %d", cn, wn)
		workerConf, forceSpec, err = taskHandler.ChangeWorkSpec(clusterGroup, cvmConf)
		if err != nil {
			return controller.InternalError, err.Error(), nil
		}
	} else {
		forceSpec = true
		cvmConfList, err := service3.GetTableService().ListCvmSaleConfByInstanceType(req.Spec, 0, 1)
		if err != nil {
			return controller.InternalError, err.Error(), nil
		}
		if len(cvmConfList) <= 0 {
			return controller.InternalError, "can not find cvm conf", nil
		}
		workerConf = cvmConfList[0]
	}
	logger.Infof("buy worker spec: %s", workerConf.InstanceType)
	if workerConf.InstanceType != constants.TKE_WORKER_NODE_INSTANCE_TYPE && !req.Force {
		return controller.OK, "", cost.BuyWorkerResp{Count: 0}
	}

	if req.Count <= 0 {
		return controller.InternalError, "Request count is zone", nil
	}

	if req.DryBuy {
		return controller.OK, "", cost.BuyWorkerResp{Count: req.Count}
	}

	zone := req.Zone
	if zone == "" {
		zone = clusterGroup.Zone
	}
	if support, err := clusterGroup.IsSupportZone(zone); err != nil {
		return controller.InternalError, err.Error(), nil
	} else if !support {
		err = fmt.Errorf("cluster %s not support zone %s", clusterGroup.SerialId, zone)
		return controller.InternalError, err.Error(), nil
	}

	diskType, diskSize, err := clusterGroup.GetDiskInfo()
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}

	if req.DiskType != "" {
		diskType = req.DiskType
	}
	if req.DiskSize > 0 {
		diskSize = req.DiskSize
	}

	label := h.GetWorkerLabel(clusterGroup, zone)

	if len(req.Labels) > 0 {
		label = req.Labels
	}

	buyCount, err := h.CheckAndAddWorker(c,
		zone,
		req.Count,
		workerConf,
		forceSpec,
		label,
		diskSize,
		diskType,
		constants.TKE_WORKER_NODE_LABEL_VAL, req.Taints, "", constants.TKE_NODE_SYSTEM_DISK_SIZE)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}

	return controller.OK, "", cost.BuyWorkerResp{Count: buyCount}
}

func RenewCvm(req *cost.RenewCvmReq) (retCode string, errMsg string, response interface{}) {
	defer func() {
		if errs := recover(); errs != nil {
			msg := fmt.Sprintf("RenewCvm %s panic , for clusterGroup:%s, errors:%+v", req.RequestId, req.SerialId, errs)
			retCode = controller.InternalError
			errMsg = msg
		}
	}()
	locker := dlocker.NewDlocker("oceanus-renew-cvm", req.SerialId, 8000)
	err := locker.Lock()
	if err != nil {
		logger.Infof("Another RenewCvm process for %s has lock but not finished yet", req.SerialId)
		return controller.InternalError, "Another RenewCvm process instance is still going, not ready yet", nil
	}
	defer locker.UnLock()

	groupService, err := service2.NewClusterGroupServiceBySerialId(req.SerialId)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}

	clusterGroup := groupService.GetClusterGroup()
	cluster, err := groupService.GetActiveCluster()
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}
	logger.Infof("%s active cluster Id: %d", clusterGroup.SerialId, cluster.Id)
	tkeList, err := groupService.GetTkeList()
	if err != nil {
		msg := fmt.Sprintf("UpgradeController -> GetTkeList(%s) error:%+v", req.SerialId, err)
		logger.Error(msg)
		return controller.InternalError, msg, nil
	}
	if len(tkeList) != 1 {
		return controller.InternalError, fmt.Sprintf("tke length is %d, not 1", len(tkeList)), nil
	}
	k8sInstance := tkeList[0]

	if clusterGroup.NetEnvironmentType == constants.NETWORK_ENV_INNER_VPC {
		return controller.OK, "", cost.RenewCvmResp{Message: "Cluster is yunti cluster"}
	}

	if k8sInstance.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		return controller.OK, "", cost.RenewCvmResp{Message: "Cluster is eks cluster"}
	}

	if clusterGroup.Type == constants.CLUSTER_GROUP_TYPE_SUB_EKS {
		return controller.OK, "", cost.RenewCvmResp{Message: "Cluster is sub eks cluster"}
	}

	if clusterGroup.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && clusterGroup.AgentSerialId != "" {
		return controller.OK, "", cost.RenewCvmResp{Message: "Cluster is share cluster"}
	}
	tkeService := tke.GetTkeService()
	_, instanceSet, err := tkeService.DescribeClusterAllInstancesWithScsAccountByNetEnvironmentType(
		clusterGroup.NetEnvironmentType,
		clusterGroup.Region,
		k8sInstance.InstanceId)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}
	if len(instanceSet) == 0 {
		return controller.InternalError, fmt.Sprintf("no cluster instance fond in %s", clusterGroup.SerialId), nil
	}

	cvmIdSet := make([]*string, 0)
	for _, instance := range instanceSet {
		if strings.HasPrefix(*instance.InstanceId, "ins-") { //except np-
			cvmIdSet = append(cvmIdSet, instance.InstanceId)
		}
	}

	cvmService := cvm3.GetCvmService()
	cvmSet, err := cvmService.DescribeInstancesWithScsAccount(clusterGroup.NetEnvironmentType, clusterGroup.Region, cvmIdSet)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}
	if len(cvmSet) == 0 {
		return controller.InternalError, fmt.Sprintf("no cvm instance fond in %s", clusterGroup.SerialId), nil
	}

	renewIdSet := make([]*string, 0)
	for _, instance := range cvmSet {
		// "ExpiredTime": "2024-07-04T07:02:34Z"
		expiredTime, err := time.Parse("2006-01-02T15:04:05Z", *instance.ExpiredTime)
		if err != nil {
			return controller.InternalError, err.Error(), nil
		}

		if time.Now().Add(31 * 24 * time.Hour).Before(expiredTime) {
			continue
		}
		renewIdSet = append(renewIdSet, instance.InstanceId)
	}
	if len(renewIdSet) == 0 {
		return controller.InternalError, fmt.Sprintf("no renew instance fond in %s", clusterGroup.SerialId), nil
	}

	err = cvmService.RenewInstancesWithScsAccount(clusterGroup.Region, renewIdSet, constants.TKE_CVM_PREPAID_PERIOD)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}
	return controller.OK, "", cost.RenewCvmResp{Count: int64(len(renewIdSet))}
}

func getWorker(cg *table.ClusterGroup, cluster *table.Cluster, labelSet []*string) (instances []*string, labelSelectSet []*string, err error) {
	label := map[string]string{constants.TKE_CVM_LABEL_KEY: constants.TKE_WORKER_NODE_LABEL_VAL}

	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM {
		label[constants.TKE_CVM_NODE_RESOURCE_TYPE] = constants.TKE_CVM_NODE_RESOURCE_TYPE_SHARE
	}

	if cg.AgentSerialId != "" && cg.ResourceType == constants.RESOURCE_TYPE_PRIVATE {
		label = taskHandler.GetPrivateWorkerLabel(cg)
	}

	if len(labelSet) > 0 {
		label = make(map[string]string)
		for _, kvPair := range labelSet {
			kv := strings.Split(*kvPair, "=")
			if len(kv) != 2 {
				continue
			}
			label[kv[0]] = kv[1]
		}
	}

	if len(label) <= 0 {
		return nil, nil, fmt.Errorf("no label select for %s", cg.SerialId)
	}

	labelSelectSet = make([]*string, 0)
	for k, v := range label {
		kv := fmt.Sprintf("%s=%s", k, v)
		labelSelectSet = append(labelSelectSet, &kv)
	}

	k8sService := k8s.GetK8sService()
	k8sClient, err := k8sService.NewClient([]byte(cluster.KubeConfig))
	if err != nil {
		return
	}
	workerList, err := k8sService.ListNode(k8sClient, metav1.ListOptions{
		LabelSelector: labels.FormatLabels(label),
	})
	if err != nil {
		return
	}
	instances = make([]*string, 0)
	for _, node := range workerList.Items {
		labelMap := node.GetObjectMeta().GetLabels()
		if instanceId, ok := labelMap["cloud.tencent.com/node-instance-id"]; ok {
			instances = append(instances, &instanceId)
		} else {
			err = fmt.Errorf("not find instance id for %s, %+v", node.Name, node)
			return
		}
	}
	return
}

func DescribeInvocationTasks(req *cost.DescribeInvocationTasksReq) (retCode string, errMsg string, response interface{}) {
	defer func() {
		if errs := recover(); errs != nil {
			msg := fmt.Sprintf("DescribeInvocationTasks %s panic , errors:%+v", req.RequestId, errs)
			retCode = controller.InternalError
			errMsg = msg
		}
	}()
	locker := dlocker.NewDlocker("oceanus-DescribeInvocationTasks-cvm", req.SerialId, 8000)
	err := locker.Lock()
	if err != nil {
		logger.Infof("Another DescribeInvocationTasks process for %s has lock but not finished yet", req.SerialId)
		return controller.InternalError, "Another DescribeInvocationTasks process instance is still going, not ready yet", nil
	}
	defer locker.UnLock()

	groupService, err := service2.NewClusterGroupServiceBySerialId(req.SerialId)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}

	clusterGroup := groupService.GetClusterGroup()
	if len(req.InvocationTaskIds) == 0 {
		return controller.InternalError, fmt.Sprintf("no InvocationTaskIds fond in %s", req.SerialId), nil
	}

	cvmService := cvm3.GetCvmService()
	count, respSet, err := cvmService.DescribeInvocationTasksForIdsWithScsAccount(clusterGroup.NetEnvironmentType, clusterGroup.Region, req.InvocationTaskIds)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}
	return controller.OK, "", cost.DescribeInvocationTasksResp{RespSet: respSet, Count: count}
}

func DescribeInvocations(req *cost.DescribeInvocationsReq) (retCode string, errMsg string, response interface{}) {
	defer func() {
		if errs := recover(); errs != nil {
			msg := fmt.Sprintf("DescribeInvocations %s panic , errors:%+v", req.RequestId, errs)
			retCode = controller.InternalError
			errMsg = msg
		}
	}()
	locker := dlocker.NewDlocker("oceanus-DescribeInvocations-cvm", req.SerialId, 8000)
	err := locker.Lock()
	if err != nil {
		logger.Infof("Another DescribeInvocations process for %s has lock but not finished yet", req.SerialId)
		return controller.InternalError, "Another DescribeInvocations process instance is still going, not ready yet", nil
	}
	defer locker.UnLock()

	groupService, err := service2.NewClusterGroupServiceBySerialId(req.SerialId)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}

	clusterGroup := groupService.GetClusterGroup()
	if len(req.InvocationIds) == 0 {
		return controller.InternalError, fmt.Sprintf("no InvocationIds fond in %s", req.SerialId), nil
	}

	cvmService := cvm3.GetCvmService()
	count, respSet, err := cvmService.DescribeInvocationsForIdsWithScsAccount(clusterGroup.NetEnvironmentType, clusterGroup.Region, req.InvocationIds)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}
	return controller.OK, "", cost.DescribeInvocationsResp{RespSet: respSet, Count: count}
}

func ResizeDisk(req *cost.ResizeDiskReq) (retCode string, errMsg string, response interface{}) {
	defer func() {
		if errs := recover(); errs != nil {
			msg := fmt.Sprintf("ResizeDisk %s panic , for cg:%s, errors:%+v", req.RequestId, req.SerialId, errs)
			retCode = controller.InternalError
			errMsg = msg
		}
	}()
	locker := dlocker.NewDlocker("resize_disk", req.SerialId, 8000)
	err := locker.Lock()
	if err != nil {
		logger.Infof("Another ResizeDisk process for %s has lock but not finished yet", req.SerialId)
		return controller.InternalError, "Another ResizeDisk process instance is still going, not ready yet", nil
	}
	defer locker.UnLock()

	groupService, err := service2.NewClusterGroupServiceBySerialId(req.SerialId)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}

	cg := groupService.GetClusterGroup()
	cluster, err := groupService.GetCluster()
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}
	logger.Infof("%s cluster Id: %d", cg.SerialId, cluster.Id)
	k8sInstance, err := groupService.GetTke()
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}

	if k8sInstance.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		return controller.OK, "", cost.RenewCvmResp{Message: "Cluster is eks cluster"}
	}

	if cg.Type == constants.CLUSTER_GROUP_TYPE_SUB_EKS {
		return controller.OK, "", cost.RenewCvmResp{Message: "Cluster is sub eks cluster"}
	}

	if cg.AgentSerialId != "" && cg.ResourceType == constants.RESOURCE_TYPE_SHARE {
		return controller.OK, "", cost.RenewCvmResp{Message: "Cluster is resource share cluster, please use uniform cluster"}
	}
	if len(req.InstanceIdSet) == 0 {
		req.InstanceIdSet, req.LabelSet, err = getWorker(cg, cluster, req.LabelSet)
		if err != nil {
			msg := fmt.Sprintf("RunCommand -> getWorker(%s) error:%+v", req.SerialId, err)
			logger.Error(msg)
			return controller.InternalError, msg, nil
		}
	}

	cvmIdSet := make([]*string, 0)
	for _, instanceId := range req.InstanceIdSet {
		if strings.HasPrefix(*instanceId, "ins-") { //except np-
			cvmIdSet = append(cvmIdSet, instanceId)
		}
	}

	req.InstanceIdSet = cvmIdSet

	if len(req.InstanceIdSet) == 0 {
		return controller.InternalError, fmt.Sprintf("no cluster instance fond in %s", cg.SerialId), nil
	}

	b, _ := json.Marshal(req.InstanceIdSet)

	logger.Infof("ResizeDisk -> Resize instance is %s", string(b))

	resp := cost.ResizeDiskResp{InstanceIdSet: req.InstanceIdSet, LabelSet: req.LabelSet}

	if req.DryRun {
		return controller.OK, "", resp
	}

	cvmService := cvm3.GetCvmService()
	cvmSet, err := cvmService.DescribeInstancesWithScsAccount(cg.NetEnvironmentType, cg.Region, req.InstanceIdSet)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}
	if len(cvmSet) == 0 {
		return controller.InternalError, fmt.Sprintf("no cvm instance fond in %s", cg.SerialId), nil
	}

	cvmMap := make(map[string]*cvm.Instance)
	for _, instance := range cvmSet {
		cvmMap[*instance.InstanceId] = instance
	}

	tkeService := tke.GetTkeService()
	_, instanceSet, err := tkeService.DescribeClusterInstanceForIdsWithScsAccountByNetEnvironmentTypeV2022(
		cg.NetEnvironmentType, cg.Region, k8sInstance.InstanceId, req.InstanceIdSet)

	cbsService := cbs.GetCbsService(cg.NetEnvironmentType, cg.Region)

	for _, instance := range instanceSet {
		for _, disk := range instance.Regular.InstanceAdvancedSettings.DataDisks {
			if *disk.MountTarget != "/var/lib/docker" {
				continue
			}

			if *disk.DiskSize >= int64(req.DiskSize) {
				continue
			}

			diskId := *disk.DiskId
			if diskId == "" {
				cvmInstance := cvmMap[*instance.InstanceId]
				for _, dataDisk := range cvmInstance.DataDisks {
					if *dataDisk.DiskSize == *disk.DiskSize {
						diskId = *dataDisk.DiskId
					}
				}
			}

			_, err = cbsService.CloudApi.ResizeDiskForId(cg.NetEnvironmentType, cg.Region, *disk.DiskId, req.DiskSize)
			if err != nil {
				logger.Errorf("ResizeDisk -> instance %s, ResizeDiskForId(%s) error:%+v", *instance.InstanceId, *disk.DiskId, err)
				resp.ErrorSet = append(resp.ErrorSet, instance.InstanceId)
			}
		}
	}

	resp.Count = len(req.InstanceIdSet)

	return controller.OK, "", resp
}

func ModifyDiskType(req *cost.ModifyDiskTypeReq) (retCode string, errMsg string, response interface{}) {
	defer func() {
		if errs := recover(); errs != nil {
			msg := fmt.Sprintf("ModifyDiskType %s panic , for cg:%s, errors:%+v", req.RequestId, req.SerialId, errs)
			retCode = controller.InternalError
			errMsg = msg
		}
	}()
	locker := dlocker.NewDlocker("modify_disk_type", req.SerialId, 8000)
	err := locker.Lock()
	if err != nil {
		logger.Infof("Another ModifyDiskType process for %s has lock but not finished yet", req.SerialId)
		return controller.InternalError, "Another ModifyDiskType process instance is still going, not ready yet", nil
	}
	defer locker.UnLock()

	groupService, err := service2.NewClusterGroupServiceBySerialId(req.SerialId)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}

	cg := groupService.GetClusterGroup()
	cluster, err := groupService.GetCluster()
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}
	logger.Infof("%s cluster Id: %d", cg.SerialId, cluster.Id)
	k8sInstance, err := groupService.GetTke()
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}

	if k8sInstance.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		return controller.OK, "", cost.RenewCvmResp{Message: "Cluster is eks cluster"}
	}

	if cg.Type == constants.CLUSTER_GROUP_TYPE_SUB_EKS {
		return controller.OK, "", cost.RenewCvmResp{Message: "Cluster is sub eks cluster"}
	}

	if cg.AgentSerialId != "" && cg.ResourceType == constants.RESOURCE_TYPE_SHARE {
		return controller.OK, "", cost.RenewCvmResp{Message: "Cluster is resource share cluster, please use uniform cluster"}
	}
	if len(req.InstanceIdSet) == 0 {
		req.InstanceIdSet, req.LabelSet, err = getWorker(cg, cluster, req.LabelSet)
		if err != nil {
			msg := fmt.Sprintf("ModifyDiskType -> getWorker(%s) error:%+v", req.SerialId, err)
			logger.Error(msg)
			return controller.InternalError, msg, nil
		}
	}

	cvmIdSet := make([]*string, 0)
	for _, instanceId := range req.InstanceIdSet {
		if strings.HasPrefix(*instanceId, "ins-") { //except np-
			cvmIdSet = append(cvmIdSet, instanceId)
		}
	}

	req.InstanceIdSet = cvmIdSet

	if len(req.InstanceIdSet) == 0 {
		return controller.InternalError, fmt.Sprintf("no cluster instance fond in %s", cg.SerialId), nil
	}

	b, _ := json.Marshal(req.InstanceIdSet)

	logger.Infof("ModifyDiskType -> ModifyDisk instance is %s", string(b))

	resp := cost.ResizeDiskResp{InstanceIdSet: req.InstanceIdSet, LabelSet: req.LabelSet}

	if req.DryRun {
		return controller.OK, "", resp
	}

	cvmService := cvm3.GetCvmService()
	cvmSet, err := cvmService.DescribeInstancesWithScsAccount(cg.NetEnvironmentType, cg.Region, req.InstanceIdSet)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}
	if len(cvmSet) == 0 {
		return controller.InternalError, fmt.Sprintf("no cvm instance fond in %s", cg.SerialId), nil
	}

	diskTypes := []string{"CLOUD_PREMIUM", "CLOUD_BSSD", "CLOUD_SSD", "CLOUD_HSSD"}

	diskTypeIndex := func(diskType string) int {
		for i, dt := range diskTypes {
			if dt == diskType {
				return i
			}
		}
		return -1
	}

	reqDiskTypeIndex := diskTypeIndex(req.DiskType)

	if reqDiskTypeIndex < 0 {
		return controller.InternalError, fmt.Sprintf("no DiskType find %s, in", req.DiskType), nil
	}

	tkeService := tke.GetTkeService()
	_, instanceSet, err := tkeService.DescribeClusterInstanceForIdsWithScsAccountByNetEnvironmentTypeV2022(
		cg.NetEnvironmentType, cg.Region, k8sInstance.InstanceId, req.InstanceIdSet)

	cbsService := cbs.GetCbsService(cg.NetEnvironmentType, cg.Region)

	for _, instance := range instanceSet {
		for _, disk := range instance.Regular.InstanceAdvancedSettings.DataDisks {
			if *disk.MountTarget != "/var/lib/docker" {
				continue
			}

			if diskTypeIndex(*disk.DiskType) >= reqDiskTypeIndex {
				continue
			}

			_, err = cbsService.CloudApi.ModifyDiskTypeForId(cg.NetEnvironmentType, cg.Region, *disk.DiskId, req.DiskType)
			if err != nil {
				logger.Errorf("ModifyDiskType -> instance %s, ModifyDiskTypeForId(%s) error:%+v", *instance.InstanceId, *disk.DiskId, err)
				resp.ErrorSet = append(resp.ErrorSet, instance.InstanceId)
			}
		}
	}

	resp.Count = len(req.InstanceIdSet)

	return controller.OK, "", resp
}

func RunCommand(req *cost.RunCommandReq) (retCode string, errMsg string, response interface{}) {
	defer func() {
		if errs := recover(); errs != nil {
			msg := fmt.Sprintf("RunCommand %s panic , for clusterGroup:%s, errors:%+v", req.RequestId, req.SerialId, errs)
			retCode = controller.InternalError
			errMsg = msg
		}
	}()
	locker := dlocker.NewDlocker("run_command", req.SerialId, 8000)
	err := locker.Lock()
	if err != nil {
		logger.Infof("Another RunCommand process for %s has lock but not finished yet", req.SerialId)
		return controller.InternalError, "Another RunCommand process instance is still going, not ready yet", nil
	}
	defer locker.UnLock()

	groupService, err := service2.NewClusterGroupServiceBySerialId(req.SerialId)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}

	clusterGroup := groupService.GetClusterGroup()
	cluster, err := groupService.GetCluster()
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}
	logger.Infof("%s cluster Id: %d", clusterGroup.SerialId, cluster.Id)
	k8sInstance, err := groupService.GetTke()
	if err != nil {
		msg := fmt.Sprintf("RunCommand -> GetTke(%s) error:%+v", req.SerialId, err)
		logger.Error(msg)
		return controller.InternalError, msg, nil
	}

	if k8sInstance.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		return controller.OK, "", cost.RenewCvmResp{Message: "Cluster is eks cluster"}
	}

	if clusterGroup.Type == constants.CLUSTER_GROUP_TYPE_SUB_EKS {
		return controller.OK, "", cost.RenewCvmResp{Message: "Cluster is sub eks cluster"}
	}

	if clusterGroup.AgentSerialId != "" && clusterGroup.ResourceType == constants.RESOURCE_TYPE_SHARE {
		return controller.OK, "", cost.RenewCvmResp{Message: "Cluster is resource share cluster, please use uniform cluster"}
	}
	if len(req.InstanceIdSet) == 0 {
		req.InstanceIdSet, req.LabelSet, err = getWorker(clusterGroup, cluster, req.LabelSet)
		if err != nil {
			msg := fmt.Sprintf("RunCommand -> getWorker(%s) error:%+v", req.SerialId, err)
			logger.Error(msg)
			return controller.InternalError, msg, nil
		}
	}

	cvmIdSet := make([]*string, 0)
	for _, instanceId := range req.InstanceIdSet {
		if strings.HasPrefix(*instanceId, "ins-") { //except np-
			cvmIdSet = append(cvmIdSet, instanceId)
		}
	}

	req.InstanceIdSet = cvmIdSet

	if len(req.InstanceIdSet) == 0 {
		return controller.InternalError, fmt.Sprintf("no cluster instance fond in %s", clusterGroup.SerialId), nil
	}

	b, _ := json.Marshal(req.InstanceIdSet)

	logger.Infof("RunCommand -> Run instances is %s", string(b))

	resp := cost.RunCommandResp{InstanceIdSet: req.InstanceIdSet, LabelSet: req.LabelSet}

	if req.DryRun {
		return controller.OK, "", resp
	}

	cvmService := cvm3.GetCvmService()
	cvmSet, err := cvmService.DescribeInstancesWithScsAccount(clusterGroup.NetEnvironmentType, clusterGroup.Region, req.InstanceIdSet)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}
	if len(cvmSet) == 0 {
		return controller.InternalError, fmt.Sprintf("no cvm instance fond in %s", clusterGroup.SerialId), nil
	}

	respSet, err := cvmService.RunCommandForIdsWithScsAccount(clusterGroup.NetEnvironmentType, clusterGroup.Region, req.InstanceIdSet, req.Command)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}

	resp.RespSet = respSet
	resp.Count = len(respSet)

	return controller.OK, "", resp
}

func UpgradeWorker(req *cost.UpgradeWorkerReq) (retCode string, errMsg string, response interface{}) {
	defer func() {
		if errs := recover(); errs != nil {
			msg := fmt.Sprintf("upgrade worker %s panic , for clusterGroup:%s, errors:%+v", req.RequestId, req.SerialId, errs)
			retCode = controller.InternalError
			errMsg = msg
		}
	}()
	locker := dlocker.NewDlocker("oceanus-upgrade-worker", req.SerialId, 8000)
	err := locker.Lock()
	if err != nil { // 加锁失败, 且没有 flowId, 就返回 flowId = 0 让计费重试
		logger.Infof("Another upgrade worker process for %s has lock but not finished yet", req.SerialId)
		return controller.InternalError, "Another  upgrade worker process instance is still going, not ready yet", nil
	}
	defer locker.UnLock()

	groupService, err := service2.NewClusterGroupServiceBySerialId(req.SerialId)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}

	_, err = groupService.CanUpgrade()
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}

	// TODO 判断集群版本，等各种信息

	flowId, err := upgradeWorker(req, groupService)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}

	return controller.OK, "", cost.UpgradeWorkerResp{
		FlowId: flowId,
	}
}

func upgradeWorker(req *cost.UpgradeWorkerReq, groupService *service2.ClusterGroupService) (flowId int64, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("upgrade Worker %s panic , for clusterGroup:%s, errors:%+v",
				req.RequestId, req.SerialId, errs)
		}
	}()
	//clusterList, err := groupService.GetClusterList()
	//if err != nil {
	//	return 0, err
	//}
	//var activeCluster *table.Cluster
	//for _, item := range clusterList {
	//	if item.RoleType == constants.CLUSTER_ROLE_TYPE_ACTIVE {
	//		activeCluster = item
	//	}
	//}

	clusterGroup := groupService.GetClusterGroup()
	txManager := service.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		clusterGroup.Status = constants.CLUSTER_GROUP_STATUS_UPGRADE
		clusterGroup.UpdateTime = time.Now().Format("2006-01-02 15:04:05")

		tx.UpdateObject(clusterGroup, clusterGroup.Id, "ClusterGroup")

		//docId := fmt.Sprintf("%s@%s", req.SerialId, req.RequestId)
		//isEks, _ := service2.IsEks(activeCluster.ClusterGroupId)
		//clusterType := constants.K8S_CLUSTER_TYPE_TKE
		//if isEks {
		//	clusterType = constants.K8S_CLUSTER_TYPE_EKS
		//}
		//flowId, err = flow.CreateFlow(constants.FLOW_OCEANUS_UPGRADE_WORKER, docId, 0, map[string]string{
		//	constants.FLOW_PARAM_REQUEST_ID:       req.RequestId,
		//	constants.FLOW_PARAM_CLUSTER_GROUP_ID: fmt.Sprintf("%d", clusterGroup.Id),
		//	constants.FLOW_PARAM_CLUSTER_ID:       fmt.Sprintf("%d", activeCluster.Id),
		//	constants.FLOW_PARAM_CLUSTER_TYPE:     fmt.Sprintf("%d", clusterType),
		//}, nil)
		//if err != nil {
		//	logger.Errorf("[%s] createFlow %s fail %v", req.RequestId, constants.FLOW_OCEANUS_UPGRADE_WORKER, err)
		//	return err
		//}

		//logger.Infof("[%s] createFlow %s success, flowId %d", req.RequestId, constants.FLOW_OCEANUS_UPGRADE_WORKER, flowId)
		//
		return nil
	}).Close()
	return flowId, nil
}

func DescribeCvmSet(req *cost.DescribeCvmReq) (retCode string, errMsg string, response interface{}) {
	defer func() {
		if errs := recover(); errs != nil {
			msg := fmt.Sprintf("describe cvm %s panic , for clusterGroup:%s, errors:%+v", req.RequestId, req.SerialId, errs)
			retCode = controller.InternalError
			errMsg = msg
		}
	}()

	groupService, err := service2.NewClusterGroupServiceBySerialId(req.SerialId)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}

	clusterGroup := groupService.GetClusterGroup()
	cluster, err := groupService.GetActiveCluster()
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}

	tkeList, err := groupService.GetTkeList()
	if err != nil {
		msg := fmt.Sprintf("UpgradeController -> GetTkeList(%s) error:%+v", req.SerialId, err)
		logger.Error(msg)
		return controller.InternalError, msg, nil
	}
	if len(tkeList) != 1 {
		return controller.InternalError, fmt.Sprintf("tke length is %d, not 1", len(tkeList)), nil
	}
	k8sInstance := tkeList[0]

	if k8sInstance.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		return controller.OK, "", cost.DescribeCvmResp{Message: "Cluster is eks cluster"}
	}

	if clusterGroup.Type == constants.CLUSTER_GROUP_TYPE_SUB_EKS {
		return controller.OK, "", cost.DescribeCvmResp{Message: "Cluster is sub eks cluster"}
	}

	secretId, secretKey, err := service4.GetSecretIdAndKeyByNetworkEnvType(clusterGroup.NetEnvironmentType)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}
	k8sService := k8s.GetK8sService()
	var totalCount uint64
	var instanceSet []*tkeSDK.Instance
	var nodeList *v1.NodeList

	instanceId := k8sInstance.InstanceId
	tkeService := tke.GetTkeService()

	if clusterGroup.AgentSerialId != "" {
		label := map[string]string{
			constants.TKE_CVM_NODE_RESOURCE_TYPE: constants.TKE_CVM_NODE_RESOURCE_TYPE_SHARE,
		}
		if clusterGroup.ResourceType == constants.RESOURCE_TYPE_PRIVATE {
			label = taskHandler.GetPrivateWorkerLabel(clusterGroup)
		}
		client, err := k8sService.NewClient([]byte(cluster.KubeConfig))
		if err != nil {
			return controller.InternalError, err.Error(), nil
		}
		nodeList, err = k8sService.ListNode(client, metav1.ListOptions{
			LabelSelector: labels.FormatLabels(label),
		})
		if err != nil {
			return controller.InternalError, err.Error(), nil
		}
		tkeInstanceSet := make([]*string, 0)
		for _, node := range nodeList.Items {
			labelMap := node.GetObjectMeta().GetLabels()
			nodeInstanceId, ok := labelMap["cloud.tencent.com/node-instance-id"]
			if !ok {
				msg := fmt.Sprintf("not find instance id for %s, name %s", clusterGroup.SerialId, node.Name)
				return controller.InternalError, msg, nil
			}
			tkeInstanceSet = append(tkeInstanceSet, &nodeInstanceId)
		}
		totalCount, instanceSet, err = tkeService.DescribeClusterInstanceForIdsWithScsAccountByNetEnvironmentType(
			clusterGroup.NetEnvironmentType, clusterGroup.Region, instanceId, tkeInstanceSet)
	} else {
		totalCount, instanceSet, err = tkeService.DescribeClusterAllInstances(secretId, secretKey, "",
			clusterGroup.Region, instanceId)

		if err != nil {
			return controller.InternalError, err.Error(), nil
		}
	}
	instanceMap := make(map[string]*cost.CvmItem)
	instanceIdSet := make([]*string, 0)

	for _, instance := range instanceSet {
		item := &cost.CvmItem{}
		item.InstanceId = *instance.InstanceId
		item.InstanceState = *instance.InstanceState
		item.Labels = make([]*cost.Label, 0)
		for _, label := range instance.InstanceAdvancedSettings.Labels {
			if *label.Name == constants.TKE_CVM_LABEL_KEY {
				if *label.Value == constants.TKE_CONTROL_NODE_LABEL_VAL {
					item.CvmType = constants.TKE_CONTROL_NODE_LABEL_VAL
				} else if *label.Value == constants.TKE_WORKER_NODE_LABEL_VAL {
					item.CvmType = constants.TKE_WORKER_NODE_LABEL_VAL
				}
			}
		}
		instanceMap[*instance.InstanceId] = item

	}

	b, _ := json.Marshal(instanceSet)
	logger.Infof("instanceIdSet: %s", string(b))

	cvmService := cvm3.GetCvmService()
	cvmInstanceSet, err := cvmService.DescribeInstancesForIds(secretId, secretKey, clusterGroup.Region, instanceIdSet)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}
	for _, instance := range cvmInstanceSet {
		cvmItem := instanceMap[*instance.InstanceId]
		cvmItem.Uuid = *instance.Uuid
		cvmItem.InstanceName = *instance.InstanceName
		cvmItem.InstanceType = *instance.InstanceType
		cvmItem.SystemDisk = *instance.SystemDisk.DiskSize
		cvmItem.DataDisk = make([]*int64, 0)
		for _, disk := range instance.DataDisks {
			cvmItem.DataDisk = append(cvmItem.DataDisk, disk.DiskSize)
		}
	}

	if req.DPod {
		// list node
		ipInstanceMap := make(map[string]string)
		err = listNode(clusterGroup, cluster, nodeList, instanceMap, ipInstanceMap)
		if err != nil {
			return controller.InternalError, err.Error(), nil
		}
		// list pod
		err = listPod(clusterGroup, cluster, instanceMap, ipInstanceMap)
		if err != nil {
			return controller.InternalError, err.Error(), nil
		}
	}

	controllerSet := make([]*cost.CvmItem, 0)
	workerSet := make([]*cost.CvmItem, 0)
	otherSet := make([]*cost.CvmItem, 0)

	for _, item := range instanceMap {
		if item.CvmType == constants.TKE_CONTROL_NODE_LABEL_VAL {
			controllerSet = append(controllerSet, item)
		} else if item.CvmType == constants.TKE_WORKER_NODE_LABEL_VAL {
			workerSet = append(workerSet, item)
		} else {
			otherSet = append(otherSet, item)
		}
	}

	return controller.OK, "", cost.DescribeCvmResp{
		TotalCount:      totalCount,
		ControllerCount: len(controllerSet),
		WorkerCount:     len(workerSet),
		ControllerSet:   controllerSet,
		WorkerSet:       workerSet,
		OtherSet:        otherSet,
	}
}

func listNode(clusterGroup *table.ClusterGroup, cluster *table.Cluster, nodeList *v1.NodeList, instanceMap map[string]*cost.CvmItem, ipInstanceMap map[string]string) (err error) {
	k8sService := k8s.GetK8sService()
	kubConfig := []byte(cluster.KubeConfig)
	client, err := k8sService.NewClient(kubConfig)
	if err != nil {
		return
	}
	if nodeList == nil {
		nodeList, err = k8sService.ListNode(client, metav1.ListOptions{})
		if err != nil {
			return
		}
	}

	for _, node := range nodeList.Items {
		labelMap := node.GetObjectMeta().GetLabels()
		instanceId, ok := labelMap["cloud.tencent.com/node-instance-id"]
		if !ok {
			msg := fmt.Sprintf("not find instance id for %s, name %s", clusterGroup.SerialId, node.Name)
			logger.Error(msg)
			continue
		}
		ipInstanceMap[node.Name] = instanceId
		cvmItem, ok := instanceMap[instanceId]
		if !ok {
			msg := fmt.Sprintf("not find instance id for %s, name %s", instanceId, node.Name)
			logger.Error(msg)
			continue
		}
		cvmItem.IP = node.Name
		for name, value := range labelMap {
			if !strings.HasPrefix(name, "oceanus-") {
				continue
			}
			cvmItem.Labels = append(cvmItem.Labels, &cost.Label{Name: name, Value: value})
		}
	}
	return
}

func listPod(clusterGroup *table.ClusterGroup, cluster *table.Cluster, instanceMap map[string]*cost.CvmItem, ipInstanceMap map[string]string) (err error) {
	k8sService := k8s.GetK8sService()
	kubConfig := []byte(cluster.KubeConfig)
	client, err := k8sService.NewClient(kubConfig)
	if err != nil {
		return
	}
	jobNamespace := "default"
	if clusterGroup.AgentSerialId != "" {
		jobNamespace = clusterGroup.SerialId
	} else if clusterGroup.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM {
		jobNamespace = ""
	}

	podList, err := k8sService.ListPod(client, jobNamespace, metav1.ListOptions{})
	if err != nil {
		return
	}
	logger.Infof("%s has %d pod in %s namespace", clusterGroup.SerialId, len(podList.Items), jobNamespace)
	for _, pod := range podList.Items {
		if strings.HasPrefix(pod.Name, "cql-") {
			jobId := pod.Name[:12]
			nodeIp := pod.Status.HostIP
			instanceId, ok := ipInstanceMap[nodeIp]
			if !ok {
				msg := fmt.Sprintf("not find instance id for %s", nodeIp)
				logger.Error(msg)
				continue
			}
			cvmItem, ok := instanceMap[instanceId]
			if !ok {
				msg := fmt.Sprintf("not find instance id for %s", instanceId)
				logger.Error(msg)
				continue
			}
			if len(cvmItem.Jobs) == 0 {
				cvmItem.Jobs = make(map[string]struct{})
			}
			cvmItem.Jobs[jobId] = struct{}{}
		}
	}

	oceanusNamespace := "oceanus"
	if clusterGroup.AgentSerialId != "" {
		oceanusNamespace = oceanusNamespace + "-" + clusterGroup.SerialId
	}

	oceanusPodList, err := k8sService.ListPod(client, oceanusNamespace, metav1.ListOptions{})
	if err != nil {
		return
	}
	logger.Infof("%s has %d pod in oceanus namespace", clusterGroup.SerialId, len(oceanusPodList.Items))
	for _, pod := range oceanusPodList.Items {
		nodeIp := pod.Status.HostIP
		instanceId, ok := ipInstanceMap[nodeIp]
		if !ok {
			msg := fmt.Sprintf("not find instance id for %s", nodeIp)
			logger.Error(msg)
			continue
		}
		cvmItem, ok := instanceMap[instanceId]
		if !ok {
			msg := fmt.Sprintf("not find instance id for %s", instanceId)
			logger.Error(msg)
			continue
		}
		if len(cvmItem.Oceanus) == 0 {
			cvmItem.Oceanus = make(map[string]struct{}, 0)
		}
		cvmItem.Oceanus[pod.Name] = struct{}{}
	}
	return
}

func DescribeTkeStatisticData(req *cost.DescribeTkeStatisticDataReq) (retCode string, errMsg string, response interface{}) {

	cgs, err := service2.NewClusterGroupServiceBySerialId(req.SerialId)

	if err != nil {
		return controller.InternalError, err.Error(), nil
	}

	cg := cgs.GetClusterGroup()
	t, err := cgs.GetTke()

	if err != nil {
		return controller.InternalError, err.Error(), nil
	}

	tkeService := tke.GetTkeService()
	_, instanceSet, err := tkeService.DescribeClusterAllInstancesWithScsAccountByNetEnvironmentType(
		cg.NetEnvironmentType,
		cg.Region,
		t.InstanceId)

	instanceIdSet := make([]*string, 0)

	for _, instance := range instanceSet {
		instanceIdSet = append(instanceIdSet, instance.InstanceId)
	}

	response, err = monitor.DescribeTkeStatisticData(cg, t, instanceIdSet, req.StartTime, req.EndTime)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}

	return controller.OK, "", response
}
