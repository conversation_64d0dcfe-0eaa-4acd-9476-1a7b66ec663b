package cost

import (
	"encoding/json"
	"fmt"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/cost"
	"testing"
)

func TestBuyWorker(t *testing.T) {
	retCode, errMsg, response := BuyWorker(&cost.BuyWorkerReq{
		RequestBase:  apiv3.RequestBase{},
		SerialId:     "",
		Count:        0,
		DiskSize:     0,
		DiskType:     "",
		ForceVersion: 0,
		Spec:         "",
		Unschedule:   false,
		Force:        false,
		DryBuy:       false,
		Zone:         "",
		Labels:       nil,
		Taints:       nil,
	})
	fmt.Println(retCode)
	fmt.Println(errMsg)
	b, _ := json.Marshal(response)
	fmt.Println(string(b))
}
