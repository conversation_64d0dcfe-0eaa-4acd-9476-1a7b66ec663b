package metric_provider

import (
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config/common_config"
)

var hasInitedMetricVariables = false
var metricAccessKeySecret string
var metricAccessKeyId string

func InitMetricVariables() {
	if hasInitedMetricVariables {
		return
	}

	metricAccessKeySecret = config.MustGetRainbowConfigurationWithRetry(constants.METRIC_GROUP_KEY, constants.METRIC_ACCESS_KEY_SECRET, 10)
	metricAccessKeyId = config.MustGetRainbowConfigurationWithRetry(constants.METRIC_GROUP_KEY, constants.METRIC_ACCESS_KEY_ID, 10)

	hasInitedMetricVariables = true
}

func DescribeKey(userName string, password string) (retCode int64, retMsg string, err error, keyId string, keySecret string) {
	rainbowOceanusClusterPassword, getPasswordError := common_config.GetRainbowOceanusClusterPassword()
	if getPasswordError != nil {
		logger.Errorf("Failed to get oceanus cluster password in Rainbow")
		return constants.SYSERR, "Failed to get oceanus cluster config in Rainbow", nil, "", ""
	}
	if constants.USER_NAME != userName || rainbowOceanusClusterPassword != password {
		logger.Errorf("user %s or password %s is incorrect", userName, password)
		return constants.SYSERR, "The user name or password is incorrect.", nil, "", ""
	}

	if len(metricAccessKeyId) == 0 {
		return constants.SYSERR, "Failed to get ACCESS_KEY_ID  from Metric in Rainbow", nil, "", ""
	}

	if len(metricAccessKeySecret) == 0 {
		return constants.SYSERR, "Failed to get ACCESS_KEY_SECRET from Metric in Rainbow", nil, "", ""
	}

	return constants.SUCCESS, "", nil, metricAccessKeyId, metricAccessKeySecret
}
