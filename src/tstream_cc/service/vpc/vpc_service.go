package vpc

import (
	"fmt"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common"
	vpc "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/vpc/v20170312"
	converter "tencentcloud.com/tstream_galileo/src/common/errcode_converter"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/vpc"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/qcloud"
)

var (
	vpcService *VpcService
)

type VpcService struct {
}

func (this *VpcService) NewDefaultCreateVpcRequest(vpcName, cidrBlock string) *vpc.CreateVpcRequest {
	request := vpc.NewCreateVpcRequest()
	request.SetDomain(model.VPC_API_DOMAIN_INTERNAL)
	request.VpcName = &vpcName
	request.CidrBlock = &cidrBlock

	return request
}

func (this *VpcService) CreateVpc(secretId, secretKey, token, region string, request *vpc.CreateVpcRequest) (
	response *vpc.CreateVpcResponse, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("CreateVpc error"))

	prof := qcloud.NewClientProfile()
	credential := common.NewTokenCredential(secretId, secretKey, token)
	client, err := vpc.NewClient(credential, region, prof)
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}
	logger.Infof("CreateVpc request: %s", request.ToJsonString())
	if response, err := client.CreateVpc(request); err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	} else {
		logger.Infof("CreateVpc response: %s", response.ToJsonString())
		return response, nil
	}
}

func (this *VpcService) CreateVpcWithScsAccount(region string, request *vpc.CreateVpcRequest) (response *vpc.CreateVpcResponse, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("CreateVpcWithScsAccount error"))

	secretId, secretKey, err := service.GetSecretIdAndKeyOfScs()
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}

	return this.CreateVpc(secretId, secretKey, "", region, request)
}

func (this *VpcService) NewDefaultCreateSubnetRequest(vpcId, subNetName, cidrBlock, zone string) *vpc.CreateSubnetRequest {
	request := vpc.NewCreateSubnetRequest()
	request.SetDomain(model.VPC_API_DOMAIN_INTERNAL)
	request.VpcId = &vpcId
	request.SubnetName = &subNetName
	request.CidrBlock = &cidrBlock
	request.Zone = &zone

	return request
}

func (this *VpcService) CreateSubnet(secretId, secretKey, token, region string, request *vpc.CreateSubnetRequest) (
	response *vpc.CreateSubnetResponse, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("CreateSubnet error"))

	prof := qcloud.NewClientProfile()
	credential := common.NewTokenCredential(secretId, secretKey, token)
	client, err := vpc.NewClient(credential, region, prof)
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}
	logger.Infof("CreateSubnet request: %s", request.ToJsonString())
	if response, err := client.CreateSubnet(request); err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	} else {
		logger.Infof("CreateSubnet response: %s", response.ToJsonString())
		return response, nil
	}
}

func (this *VpcService) CreateSubnetScsAccount(region string, request *vpc.CreateSubnetRequest) (response *vpc.CreateSubnetResponse, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("CreateSubnetScsAccount error"))

	secretId, secretKey, err := service.GetSecretIdAndKeyOfScs()
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}

	return this.CreateSubnet(secretId, secretKey, "", region, request)
}

func (this *VpcService) NewDefaultDescribeSubnetsRequestBuilder() *DescribeSubnetsRequestBuilder {
	return NewDescribeSubnetsRequestBuilder().WithDomain(model.VPC_API_DOMAIN_INTERNAL)
}

func (this *VpcService) DescribeSubnets(secretId, secretKey, token, region string, request *vpc.DescribeSubnetsRequest) (
	totalAccount uint64, subNetSet []*vpc.Subnet, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("DescribeSubnets error"))

	prof := qcloud.NewClientProfile()
	credential := common.NewTokenCredential(secretId, secretKey, token)
	client, err := vpc.NewClient(credential, region, prof)
	if err != nil {
		return 0, nil, converter.GenCommonErrorCode("", err)
	}

	logger.Infof("DescribeSubnets request: %s", request.ToJsonString())
	response, err := client.DescribeSubnets(request)
	if err != nil {
		return 0, nil, converter.GenCommonErrorCode("", err)
	}
	logger.Infof("DescribeSubnets response: %s", response.ToJsonString())

	return *response.Response.TotalCount, response.Response.SubnetSet, nil
}

func (this *VpcService) DescribeSubnetsWithScsAccount(region string, request *vpc.DescribeSubnetsRequest) (
	totalAccount uint64, subNetSet []*vpc.Subnet, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("DescribeSubnetsWithScsAccount error"))

	secretId, secretKey, err := service.GetSecretIdAndKeyOfScs()
	if err != nil {
		return 0, nil, converter.GenCommonErrorCode("", err)
	}

	return this.DescribeSubnets(secretId, secretKey, "", region, request)
}

func (this *VpcService) NewDefaultDescribeVpcsRequestBuilder() *DescribeVpcsRequestBuilder {
	return NewDescribeVpcsRequestBuilder().WithDomain(model.VPC_API_DOMAIN_INTERNAL)
}

func (this *VpcService) DescribeVpcs(secretId, secretKey, token, region string, request *vpc.DescribeVpcsRequest) (
	totalAccount uint64, vpcSet []*vpc.Vpc, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("DescribeVpcs error"))

	prof := qcloud.NewClientProfile()
	credential := common.NewTokenCredential(secretId, secretKey, token)
	client, err := vpc.NewClient(credential, region, prof)
	if err != nil {
		return 0, nil, converter.GenCommonErrorCode("", err)
	}

	logger.Infof("DescribeVpcs request: %s", request.ToJsonString())
	response, err := client.DescribeVpcs(request)
	if err != nil {
		return 0, nil, converter.GenCommonErrorCode("", err)
	}
	logger.Infof("DescribeVpcs response: %s", response.ToJsonString())

	return *response.Response.TotalCount, response.Response.VpcSet, nil
}

func (this *VpcService) DescribeVpcsWithScsAccount(region string, request *vpc.DescribeVpcsRequest) (
	totalAccount uint64, vpcSet []*vpc.Vpc, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("DescribeVpcsWithScsAccount error"))

	secretId, secretKey, err := service.GetSecretIdAndKeyOfScs()
	if err != nil {
		return 0, nil, converter.GenCommonErrorCode("", err)
	}

	return this.DescribeVpcs(secretId, secretKey, "", region, request)
}

func (o *VpcService) NewDefaultDescribeAssistantCidrRequestBuilder() *DescribeAssistantCidrRequestBuilder {
	return NewDescribeAssistantCidrRequestBuilder().WithDomain(model.VPC_API_DOMAIN_INTERNAL)
}

func (this *VpcService) DescribeAssistantCidr(secretId, secretKey, token, region string, request *vpc.DescribeAssistantCidrRequest) (
	totalAccount uint64, vpcSet []*vpc.AssistantCidr, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("DescribeAssistantCidr error"))

	prof := qcloud.NewClientProfile()
	credential := common.NewTokenCredential(secretId, secretKey, token)
	client, err := vpc.NewClient(credential, region, prof)
	if err != nil {
		return 0, nil, converter.GenCommonErrorCode("", err)
	}

	logger.Infof("DescribeAssistantCidr request: %s", request.ToJsonString())
	response, err := client.DescribeAssistantCidr(request)
	if err != nil {
		return 0, nil, converter.GenCommonErrorCode("", err)
	}
	logger.Infof("DescribeAssistantCidr response: %s", response.ToJsonString())

	return *response.Response.TotalCount, response.Response.AssistantCidrSet, nil
}

func (this *VpcService) DescribeAssistantCidrWithScsAccount(region string, request *vpc.DescribeAssistantCidrRequest) (
	totalAccount uint64, vpcSet []*vpc.AssistantCidr, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("DescribeAssistantCidrWithScsAccount error"))

	secretId, secretKey, err := service.GetSecretIdAndKeyOfScs()
	if err != nil {
		return 0, nil, converter.GenCommonErrorCode("", err)
	}

	return this.DescribeAssistantCidr(secretId, secretKey, "", region, request)
}

func (this *VpcService) NewDefaultDeleteVpcRequestBuilder() *DeleteVpcRequestBuilder {
	return NewDeleteVpcRequestBuilder().WithDomain(model.VPC_API_DOMAIN_INTERNAL)
}

func (this *VpcService) DeleteVpc(secretId, secretKey, token, region string,
	request *vpc.DeleteVpcRequest) (success bool, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("DeleteVpc error"))

	prof := qcloud.NewClientProfile()
	credential := common.NewTokenCredential(secretId, secretKey, token)
	client, err := vpc.NewClient(credential, region, prof)
	if err != nil {
		return false, converter.GenCommonErrorCode("", err)
	}

	logger.Infof("DeleteVpc request: %s", request.ToJsonString())
	response, err := client.DeleteVpc(request)
	if err != nil {
		return false, converter.GenCommonErrorCode("", err)
	}
	logger.Infof("DeleteVpc response: %s", response.ToJsonString())

	return true, nil
}

func (this *VpcService) DeleteVpcWithScsAccount(region string, request *vpc.DeleteVpcRequest) (success bool, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("DeleteVpcWithScsAccount error"))

	secretId, secretKey, err := service.GetSecretIdAndKeyOfScs()
	if err != nil {
		return false, converter.GenCommonErrorCode("", err)
	}

	return this.DeleteVpc(secretId, secretKey, "", region, request)
}

func (this *VpcService) NewDefaultDescribeVpcResourceDashboardRequestBuilder() *DescribeVpcResourceDashboardRequestBuilder {
	return NewDescribeVpcResourceDashboardRequestBuilder().WithDomain(model.VPC_API_DOMAIN_INTERNAL)
}

func (this *VpcService) DescribeVpcResourceDashboard(secretId, secretKey, token, region string,
	request *vpc.DescribeVpcResourceDashboardRequest) (dashboard []*vpc.ResourceDashboard, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("DescribeVpcResourceDashboard error"))

	prof := qcloud.NewClientProfile()
	credential := common.NewTokenCredential(secretId, secretKey, token)
	client, err := vpc.NewClient(credential, region, prof)
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}

	logger.Infof("DeleteVpc request: %s", request.ToJsonString())
	response, err := client.DescribeVpcResourceDashboard(request)
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}
	logger.Infof("DeleteVpc response: %s", response.ToJsonString())

	return response.Response.ResourceDashboardSet, nil
}

func (this *VpcService) DescribeVpcResourceDashboardWithScsAccount(region string, request *vpc.DescribeVpcResourceDashboardRequest) (dashboard []*vpc.ResourceDashboard, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("DescribeVpcResourceDashboardWithScsAccount error"))

	secretId, secretKey, err := service.GetSecretIdAndKeyOfScs()
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}
	return this.DescribeVpcResourceDashboard(secretId, secretKey, "", region, request)
}

func (this *VpcService) DescribeSecurityGroups(region string, networkEnvironmentType int8, request *vpc.DescribeSecurityGroupsRequest) (
	securityGroupSet []*vpc.SecurityGroup, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("DescribeSecurityGroups error"))
	prof := qcloud.NewClientProfile()
	secretId, secretKey, err := service.GetSecretIdAndKeyByNetworkEnvType(networkEnvironmentType)
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}
	credential := common.NewTokenCredential(secretId, secretKey, "")
	client, err := vpc.NewClient(credential, region, prof)
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}

	logger.Infof("DescribeSecurityGroups request: %s", request.ToJsonString())
	response, err := client.DescribeSecurityGroups(request)
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}
	logger.Infof("DescribeSecurityGroups response: %s", response.ToJsonString())

	return response.Response.SecurityGroupSet, nil
}

func (this *VpcService) DescribeSecurityGroupPolicies(region string, networkEnvironmentType int8, request *vpc.DescribeSecurityGroupPoliciesRequest) (policySet *vpc.SecurityGroupPolicySet, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("DescribeVpcSecurityGroup error"))
	prof := qcloud.NewClientProfile()
	secretId, secretKey, err := service.GetSecretIdAndKeyByNetworkEnvType(networkEnvironmentType)
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}
	credential := common.NewTokenCredential(secretId, secretKey, "")
	client, err := vpc.NewClient(credential, region, prof)
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}

	logger.Infof("DescribeSecurityGroupPolicies request: %s", request.ToJsonString())
	response, err := client.DescribeSecurityGroupPolicies(request)
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}
	logger.Infof("DescribeSecurityGroupPolicies response: %s", response.ToJsonString())

	return response.Response.SecurityGroupPolicySet, nil
}

func (this *VpcService) ModifySecurityGroupPolicies(region string, netEnvironmentType int8, request *vpc.ModifySecurityGroupPoliciesRequest) (
	rsp *vpc.ModifySecurityGroupPoliciesResponse, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("ModifySecurityGroupPolicies error"))
	prof := qcloud.NewClientProfile()
	secretId, secretKey, err := service.GetSecretIdAndKeyByNetworkEnvType(netEnvironmentType)
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}
	credential := common.NewTokenCredential(secretId, secretKey, "")
	client, err := vpc.NewClient(credential, region, prof)
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}
	logger.Infof("ModifySecurityGroupPolicies request: %s", request.ToJsonString())
	return client.ModifySecurityGroupPolicies(request)
}

func (this *VpcService) DeleteSecurityGroupPolicies(region string, netEnvironmentType int8, request *vpc.DeleteSecurityGroupPoliciesRequest) (
	rsp *vpc.DeleteSecurityGroupPoliciesResponse, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("DeleteSecurityGroupPolicies error"))
	prof := qcloud.NewClientProfile()
	secretId, secretKey, err := service.GetSecretIdAndKeyByNetworkEnvType(netEnvironmentType)
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}
	credential := common.NewTokenCredential(secretId, secretKey, "")
	client, err := vpc.NewClient(credential, region, prof)
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}
	logger.Infof("DeleteSecurityGroupPoliciesRequest request: %s", request.ToJsonString())
	return client.DeleteSecurityGroupPolicies(request)
}

func NewVpcService() *VpcService {
	return &VpcService{}
}

func GetVpcService() *VpcService {
	if vpcService == nil {
		vpcService = NewVpcService()
	}

	return vpcService
}
