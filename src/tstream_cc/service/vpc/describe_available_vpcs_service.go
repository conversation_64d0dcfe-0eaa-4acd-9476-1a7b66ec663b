package vpc

import (
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/profile"
	vpc "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/vpc/v20170312"
	"strconv"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/vpc"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/yunti"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	yuntiService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/yunti"
)

func DoDescribeAvailableVPCs(req *model.DescribeAvailableVPCsReq) (rsp *model.DescribeAvailableVPCsRsp, err error) {
	// 检查是否内网账号，如果是则只获取4381账号下专门给我们Oceanus规划好的vpc信息
	requestBase := &apiv3.RequestBase{
		RequestId: req.RequestId,
		Region:    req.Region,
		Uin:       req.Uin,
	}
	CheckAccountReq := &yunti.CheckAccountReq{
		RequestBase: *requestBase,
	}
	CheckOwnerUinRsp, err := yuntiService.CheckOwnUin(CheckAccountReq)
	if err != nil {
		logger.Errorf("%s: Failed to SendRequest2YunTi for checkOwnUin, with error: %+v", req.RequestId, err)
		return nil, err
	}
	sid := ""
	sKey := ""
	token := ""
	// 如果是内网(云梯)账号
	if CheckOwnerUinRsp.Result.Total > 0 {
		// 固定使用内网托管账号的secretId和secretKey来获取Oceanus的专用vpc
		sid, sKey, err = service.GetSecretIdAndKeyOfInner()
		if err != nil {
			return nil, err
		}
	} else {
		// 临时secret
		sid, sKey, token, _, err = service.StsAssumeRole(req.Uin, "", req.Region)
		if err != nil {
			return nil, err
		}
	}

	// 请求数据
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.ReqMethod = "POST"
	cpf.HttpProfile.ReqTimeout = constants.HTTP_PROFILE_REQ_TIMEOUT
	cpf.SignMethod = "HmacSHA1"

	credential := common.NewTokenCredential(sid, sKey, token)
	client, err := vpc.NewClient(credential, req.Region, cpf)
	if err != nil {
		return nil, err
	}

	request := vpc.NewDescribeVpcsRequest()
	request.SetDomain("vpc.internal.tencentcloudapi.com")

	// filter
	if len(req.Filters) > 0 {
		for _, filter := range req.Filters {
			if len(filter.Values) > 0 {
				vpcFilter := &vpc.Filter{Name: nil, Values: nil}
				vpcFilter.Name = &filter.Name
				for _, value := range filter.Values {
					vpcFilter.Values = append(vpcFilter.Values, &value)
				}
				request.Filters = append(request.Filters, vpcFilter)
			}
		}
	}

	// 如果是内网(云梯)账号
	if CheckOwnerUinRsp.Result.Total > 0 {
		// 添加一个Filter只过滤出Oceanus的vpc
		oceanusVpcFilter := &vpc.Filter{
			Name:   common.StringPtr("vpc-name"),
			Values: common.StringPtrs([]string{"Oceanus"}),
		}
		request.Filters = append(request.Filters, oceanusVpcFilter)
	}

	// vpcIds
	if len(req.VpcIds) > 0 {
		for _, vpcId := range req.VpcIds {
			request.VpcIds = append(request.VpcIds, &vpcId)
		}
	}

	// limit
	if req.Limit > 0 {
		offSetStr := strconv.Itoa(req.Offset)
		request.Offset = &offSetStr
		limitStr := strconv.Itoa(req.Limit)
		request.Limit = &limitStr
	}
	logger.Infof("%s, DoDescribeAvailableVPCs DescribeAvailableVPCs request: %s", req.RequestId, request.ToJsonString())
	response, err := client.DescribeVpcs(request)
	if err != nil {
		return nil, err
	}
	logger.Infof("%s, DoDescribeAvailableVPCs DescribeAvailableVPCs response: %s", req.RequestId, response.ToJsonString())

	describeAvailableVPCsRsp := &model.DescribeAvailableVPCsRsp{}
	describeAvailableVPCsRsp.VpcSet = response.Response.VpcSet
	describeAvailableVPCsRsp.TotalCount = *response.Response.TotalCount
	return describeAvailableVPCsRsp, nil
}
