package vpc

import (
	"fmt"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common"
	converter "tencentcloud.com/tstream_galileo/src/common/errcode_converter"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/qcloud"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/vpc"
)

var (
	vpcServiceEx *VpcServiceEx
)

type VpcServiceEx struct {
}

func NewVpcServiceEx() *VpcServiceEx {
	return &VpcServiceEx{}
}

func GetVpcServiceEx() *VpcServiceEx {
	if vpcServiceEx == nil {
		vpcServiceEx = NewVpcServiceEx()
	}

	return vpcServiceEx
}

func (this *VpcServiceEx) NewDefaultCreateNetworkInterfaceExBuilder() *CreateNetworkInterfaceExBuilder {
	return NewCreateNetworkInterfaceExBuilder()
}

func (this *VpcServiceEx) CreateNetworkInterfaceEx(secretId, secretKey, token, region string, request *vpc.CreateNetworkInterfaceExRequest) (
	response *vpc.CreateNetworkInterfaceExResponse, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("CreateNetworkInterfaceEx error"))

	prof := qcloud.NewClientProfile()
	credential := common.NewTokenCredential(secretId, secretKey, token)
	client, err := vpc.NewClient(credential, region, prof)
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}
	logger.Infof("CreateNetworkInterfaceEx request: %s", request.ToJsonString())
	if response, err := client.CreateNetworkInterfaceEx(request); err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	} else {
		logger.Infof("CreateNetworkInterfaceEx response: %s", response.ToJsonString())
		return response, nil
	}
}

func (this *VpcServiceEx) CreateNetworkInterfaceExWithScsAccount(region string, request *vpc.CreateNetworkInterfaceExRequest) (
	response *vpc.CreateNetworkInterfaceExResponse, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("CreateNetworkInterfaceExWithScsAccount error"))

	secretId, secretKey, err := service.GetSecretIdAndKeyOfScs()
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}

	return this.CreateNetworkInterfaceEx(secretId, secretKey, "", region, request)
}

func (this *VpcServiceEx) NewDefaultAttachNetworkInterfaceExBuilder() *AttachNetworkInterfaceExBuilder {
	return NewAttachNetworkInterfaceExBuilder()
}

func (this *VpcServiceEx) AttachNetworkInterfaceEx(secretId, secretKey, token, region string, request *vpc.AttachNetworkInterfaceExRequest) (
	response *vpc.AttachNetworkInterfaceExResponse, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("AttachNetworkInterfaceEx error"))

	prof := qcloud.NewClientProfile()
	credential := common.NewTokenCredential(secretId, secretKey, token)
	client, err := vpc.NewClient(credential, region, prof)
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}
	logger.Infof("AttachNetworkInterfaceEx request: %s", request.ToJsonString())
	if response, err := client.AttachNetworkInterfaceEx(request); err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	} else {
		logger.Infof("AttachNetworkInterfaceEx response: %s", response.ToJsonString())
		return response, nil
	}
}

func (this *VpcServiceEx) AttachNetworkInterfaceExWithScsAccount(region string, request *vpc.AttachNetworkInterfaceExRequest) (
	response *vpc.AttachNetworkInterfaceExResponse, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("AttachNetworkInterfaceExWithScsAccount error"))

	secretId, secretKey, err := service.GetSecretIdAndKeyOfScs()
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}

	return this.AttachNetworkInterfaceEx(secretId, secretKey, "", region, request)
}

func (this *VpcServiceEx) NewDefaultDescribeVpcTaskResultBuilder() *DescribeVpcTaskResultBuilder {
	return NewDescribeVpcTaskResultBuilder()
}

func (this *VpcServiceEx) DescribeVpcTaskResult(secretId, secretKey, token, region string, request *vpc.DescribeVpcTaskResultRequest) (
	response *vpc.DescribeVpcTaskResultResponse, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("DescribeVpcTaskResult error"))

	prof := qcloud.NewClientProfile()
	credential := common.NewTokenCredential(secretId, secretKey, token)
	client, err := vpc.NewClient(credential, region, prof)
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}
	logger.Infof("DescribeVpcTaskResult request: %s", request.ToJsonString())
	if response, err := client.DescribeVpcTaskResult(request); err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	} else {
		logger.Infof("DescribeVpcTaskResult response: %s", response.ToJsonString())
		return response, nil
	}
}

func (this *VpcServiceEx) DescribeVpcTaskResultWithScsAccount(region string, request *vpc.DescribeVpcTaskResultRequest) (
	response *vpc.DescribeVpcTaskResultResponse, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("DescribeVpcTaskResultWithScsAccount error"))

	secretId, secretKey, err := service.GetSecretIdAndKeyOfScs()
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}

	return this.DescribeVpcTaskResult(secretId, secretKey, "", region, request)
}

func (this *VpcServiceEx) NewDefaultDescribeNetworkInterfacesExBuilder() *DescribeNetworkInterfacesExBuilder {
	return NewDescribeNetworkInterfacesExBuilder()
}

func (this *VpcServiceEx) DescribeNetworkInterfacesEx(secretId, secretKey, token, region string, request *vpc.DescribeNetworkInterfacesExRequest) (
	response *vpc.DescribeNetworkInterfacesExResponse, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("DescribeNetworkInterfacesEx error"))

	prof := qcloud.NewClientProfile()
	credential := common.NewTokenCredential(secretId, secretKey, token)
	client, err := vpc.NewClient(credential, region, prof)
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}
	logger.Infof("DescribeNetworkInterfacesEx request: %s", request.ToJsonString())
	if response, err := client.DescribeNetworkInterfacesEx(request); err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	} else {
		logger.Infof("DescribeNetworkInterfacesEx response: %s", response.ToJsonString())
		return response, nil
	}
}

func (this *VpcServiceEx) DescribeNetworkInterfacesExWithScsAccount(region string, request *vpc.DescribeNetworkInterfacesExRequest) (
	response *vpc.DescribeNetworkInterfacesExResponse, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("DescribeNetworkInterfacesExWithScsAccount error"))

	secretId, secretKey, err := service.GetSecretIdAndKeyOfScs()
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}

	return this.DescribeNetworkInterfacesEx(secretId, secretKey, "", region, request)
}

func (this *VpcServiceEx) NewDefaultDeleteNetworkInterfaceExBuilder() *DeleteNetworkInterfaceExBuilder {
	return NewDeleteNetworkInterfaceExBuilder()
}

func (this *VpcServiceEx) DeleteNetworkInterfaceEx(secretId, secretKey, token, region string, request *vpc.DeleteNetworkInterfaceExRequest) (
	response *vpc.DeleteNetworkInterfaceExResponse, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("DeleteNetworkInterfaceEx error"))

	prof := qcloud.NewClientProfile()
	credential := common.NewTokenCredential(secretId, secretKey, token)
	client, err := vpc.NewClient(credential, region, prof)
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}
	logger.Infof("DeleteNetworkInterfaceEx request: %s", request.ToJsonString())
	if response, err := client.DeleteNetworkInterfaceEx(request); err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	} else {
		logger.Infof("DeleteNetworkInterfaceEx response: %s", response.ToJsonString())
		return response, nil
	}
}
func (this *VpcServiceEx) DeleteNetworkInterfaceExWithScsAccount(region string, request *vpc.DeleteNetworkInterfaceExRequest) (
	response *vpc.DeleteNetworkInterfaceExResponse, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("DeleteNetworkInterfaceExWithScsAccount error"))

	secretId, secretKey, err := service.GetSecretIdAndKeyOfScs()
	if err != nil {
		return nil, converter.GenCommonErrorCode("", err)
	}

	return this.DeleteNetworkInterfaceEx(secretId, secretKey, "", region, request)
}
