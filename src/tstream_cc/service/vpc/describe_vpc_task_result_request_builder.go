package vpc

import (
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/vpc"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/vpc"
)

type DescribeVpcTaskResultBuilder struct {
	request *vpc.DescribeVpcTaskResultRequest
}

func (d *DescribeVpcTaskResultBuilder) WithVpcAppId(vpcAppId uint64) *DescribeVpcTaskResultBuilder {
	d.request.VpcAppId = &vpcAppId
	return d
}

func (d *DescribeVpcTaskResultBuilder) WithVpcUin(vpcUin string) *DescribeVpcTaskResultBuilder {
	d.request.VpcUin = &vpcUin
	return d
}

func (d *DescribeVpcTaskResultBuilder) WithVpcSubAccountUin(vpcSubAccountUin string) *DescribeVpcTaskResultBuilder {
	d.request.VpcSubAccountUin = &vpcSubAccountUin
	return d
}

func (d *DescribeVpcTaskResultBuilder) WithTaskId(taskId string) *DescribeVpcTaskResultBuilder {
	d.request.TaskId = &taskId
	return d
}

func (d *DescribeVpcTaskResultBuilder) Build() *vpc.DescribeVpcTaskResultRequest {
	return d.request
}

func NewDescribeVpcTaskResultBuilder() *DescribeVpcTaskResultBuilder {
	b := &DescribeVpcTaskResultBuilder{request: vpc.NewDescribeVpcTaskResultRequest()}
	b.request.SetDomain(model.VPC_API_DOMAIN_INTERNAL)
	return b
}
