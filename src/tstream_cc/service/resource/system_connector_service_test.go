package service

import (
	"encoding/json"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"testing"
)

type TestSuite struct {
	Connector   string
	CatalogType string
	HiveVersion string
	Path        string

	MissMatch        bool
	ExpectNum        int
	HaveNullResource bool
}

func doTest(t *testing.T, test *TestSuite) {
	initDb()

	options := make(map[string]string)
	if len(*fTestConnector) > 0 {
		options["connector"] = *fTestConnector
	}
	if len(*fTestCatalogType) > 0 {
		options["catalog-type"] = *fTestCatalogType
	}
	if len(*fTestHiveVersion) > 0 {
		options["hive-version"] = *fTestHiveVersion
	}
	if len(*fTestPath) > 0 {
		options["path"] = *fTestPath
	}

	if resouces, _, missMatch, err := GetSystemConnectorByOptions(options, *fTestFlinkVersion); err != nil {
		t.Error(err)
	} else if missMatch {
		if test != nil && !test.MissMatch {
			t.Error("miss match")
		} else {
			t.Log("miss match")
		}
	} else {
		b, _ := json.MarshalIndent(resouces, "", " ")
		t.Logf("\n%s", string(b))
		if test != nil {
			if test.ExpectNum != len(resouces) {
				t.Errorf("expect %d connectors, but got %d", test.ExpectNum, len(resouces))
			}
			if test.HaveNullResource {
				got := false
				for _, res := range resouces {
					if res.ResourceId == constants.METADATA_TABLE_RESOURCE_REF_NO_NEED_REFERENCE_RESOURCE_ID {
						got = true
						break
					}
				}
				if !got {
					t.Error("expect a null resource")
				}
			}
		}
	}
}

func TestGetSystemConnectorByOptions(t *testing.T) {
	doTest(t, nil)
}

func TestGetSystemConnectorByOptionsIT(t *testing.T) {
	testSuite := []*TestSuite{
		{
			Connector: "kafka",
			ExpectNum: 1,
		},
		{
			Connector: "upsert-kafka",
			ExpectNum: 1,
		},
		{
			Connector: "jdbc",
			ExpectNum: 1,
		},
		{
			Connector:   "hive",
			HiveVersion: "3.1.1",
			ExpectNum:   1,
		},
		{
			Connector:   "hive",
			HiveVersion: "2.3.7",
			MissMatch:   true,
		},
		{
			Connector:   "iceberg",
			CatalogType: "hadoop",
			ExpectNum:   1,
		},
		{
			Connector:   "iceberg",
			CatalogType: "hive",
			HiveVersion: "2.3.4",
			ExpectNum:   2,
		},
		{
			Connector:   "iceberg",
			CatalogType: "hive",
			HiveVersion: "2.3.7",
			MissMatch:   true,
		},
		{
			Connector:   "iceberg",
			CatalogType: "cycle-ref",
			HiveVersion: "2.3.8",
			// iceberg: {"Options":[{"Key":"connector","Value":"iceberg"},{"Key":"catalog-type","Value":"cycle-ref"}],"ReferConnectors":[[{"Key":"connector","Value":"hive"}],[{"Key":"connector","Value":"iceberg"},{"Key":"catalog-type","Value":"hadoop"}]]}
			// hive: {"Key":"hive-version","Value":"2.3.8"}],"ReferConnectors":[[{"Key":"connector","Value":"iceberg"},{"Key":"catalog-type","Value":"hive"}]]}
			ExpectNum: 2,
		},
		{
			Connector:        "filesystem",
			Path:             "hdfs://xxx",
			ExpectNum:        1,
			HaveNullResource: true,
		},
		{
			Connector: "filesystem",
			Path:      "cosn://xxx",
			ExpectNum: 1,
		},
		{
			Connector:   "iceberg",
			CatalogType: "multi",
			HiveVersion: "2.3.4",
			Path:        "cosn://xxx",
			// {"Options":[{"Key":"connector","Value":"iceberg"},{"Key":"catalog-type","Value":"multi"}],"ReferConnectors":[[{"Key":"connector","Value":"hive"}],[{"Key":"connector","Value":"filesystem"}],[{"Key":"connector","Value":"clickhouse"}]]}
			ExpectNum: 4, // iceberg, hive, cos, clickhouse
		},
	}

	for _, test := range testSuite {
		t.Run(test.Connector+test.HiveVersion+test.CatalogType, func(t *testing.T) {
			fTestConnector = &test.Connector
			fTestCatalogType = &test.CatalogType
			fTestHiveVersion = &test.HiveVersion
			fTestPath = &test.Path
			doTest(t, test)
		})
	}
}
