package service

import (
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	sql_model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/sql"
	"time"

	"tencentcloud.com/tstream_galileo/src/common/dao"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"

	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_config"
	model4 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/resource_config"
	table3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/bucket"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job"
	model2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/resource"
	model3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/resource_config"
	service5 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/bucket"
	service6 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/configuration_center"
	service4 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource_config"
)

func DoCreateResourceRefs(request *model.CreateJobConfigReq, jobConfigId int64) (success bool, err error) {
	// 有些作业不需要创建资源引用
	if request.ResourceRefs == nil || len(request.ResourceRefs) == 0 {
		logger.Warning("resource reference not exist, skip")
		success = true
		return success, err
	}
	_, resources, err := GetResourcesAndCheckReferences(request.ResourceRefs)
	if err != nil {
		return success, err
	}
	resourceRefs, err := buildResourceRefs(request, resources, jobConfigId)
	if err != nil {
		return false, err
	}
	return SaveNewResourceRefs(resourceRefs)
}

func GetResourcesAndCheckReferences(refs []*model.ResourceRefItem) (success bool, resources map[string]*model2.Resource,
	err error) {
	m := map[string]*model2.Resource{}
	for _, resourceRefItem := range refs {
		args := make([]interface{}, 0, 0)
		args = append(args, resourceRefItem.ResourceId)
		sql := "SELECT * FROM Resource WHERE ResourceId = ?"
		cnt, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
		if err != nil {
			logger.Errorf("check for resource references -> DoQuery sql:%s args:%+v, error:%+v", sql, args, err)
			return success, m, err
		}

		if cnt != 1 {
			err = fmt.Errorf("check for resource references error, cnt(%d) != 1", cnt)
			logger.Errorf("check for resource references error, sql:%s args:%+v, cnt(%d) != 1", sql, args, cnt)
			return success, m, err
		}

		resource := &model2.Resource{}
		err = util.ScanMapIntoStruct(resource, data[0])
		if err != nil {
			logger.Errorf("Failed to scan map into Resource, with errors:%+v", err)
			return success, m, err
		}
		if resource.Status != constants.BUCKET_REF_STATUS_ACTIVE {
			err = fmt.Errorf("check for resource references error, ResourceId %d state unavailable", resource.Id)
			logger.Errorf("check for resource references error, resourceId %d state unavailable, current status:%d", resource.Id, resource.Status)
			return success, m, err
		}
		m[resourceRefItem.ResourceId] = resource
		if resource.IsResGroup == constants.RESOURCE_GROUP_ENABLED {
			continue
		}
		// check resource config version
		if _, err = GetResourceConfigByResourceRefItem(resourceRefItem); err != nil {
			return success, m, err
		}
	}
	success = true
	return success, m, err
}

func ListResourceRef(resourceIds []int64, jobConfigIds []int64, versionIds []int64, statusList []int) (ResourceRefList []*model2.ResourceRef, err error) {

	sql := "SELECT * FROM ResourceRef "
	var args []interface{}

	condition := dao.NewCondition()

	if len(resourceIds) > 0 {
		condition = condition.In("ResourceId", resourceIds)
	}

	if len(jobConfigIds) > 0 {
		condition = condition.In("JobConfigId", jobConfigIds)
	}

	if len(versionIds) > 0 {
		condition = condition.In("VersionId", versionIds)
	}

	if len(statusList) > 0 {
		condition = condition.In("Status", statusList)
	} else {
		condition = condition.Eq("Status", constants.RESOURCE_REF_STATUS_ACTIVE)
	}

	where, args := condition.GetWhere()
	sql = sql + where

	_, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("ListResourceRef sql %s, args: %+v, error:%+v", sql, args, err)
		return ResourceRefList, err
	}

	ResourceRefList = make([]*model2.ResourceRef, 0, 0)
	for _, value := range data {
		ref := &model2.ResourceRef{}
		util.ScanMapIntoStruct(ref, value)
		ResourceRefList = append(ResourceRefList, ref)
	}

	return ResourceRefList, err
}

func SaveNewResourceRefs(resourceRefs []*model2.ResourceRef) (success bool, err error) {
	logger.Infof("Save resource ref size %d", len(resourceRefs))
	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		for _, value := range resourceRefs {

			isDraft, _, err := service5.IsDraftConfig(value.JobConfigId)
			if err != nil {
				logger.Errorf("saveNewResourceRefs IsDraftConfig error: %+v", err)
				return err
			}

			// 如果更新的resourceRef中的jobConfigId是草稿
			// 将这个草稿之前的引用关系全部解除
			// 插入新的引用关系
			//
			// 如果更新的resourceRef中的jobConfigId是正常的jobConfigId
			// 检查是否已经存在，如果存在则不插入新的记录
			// 如果不存在，加入新的记录
			logger.Infof("saveNewResourceRefs isDraft %t JobConfigId %d", isDraft, value.JobConfigId)
			if isDraft {
				logger.Infof("save draft resourceRef")
				args := make([]interface{}, 0, 0)
				args = append(args, constants.RESOURCE_REF_STATUS_DELETE)
				args = append(args, value.JobConfigId)
				args = append(args, value.ResourceId)
				tx.ExecuteSql("UPDATE ResourceRef Set Status=? WHERE JobConfigId=? AND ResourceId=?", args)
				tx.SaveObject(value, "ResourceRef")
			} else {
				logger.Infof("save config resourceRef")
				// 检查数据是否已经存在
				refList, err := ListResourceRef([]int64{value.ResourceId}, []int64{value.JobConfigId}, []int64{value.VersionId}, nil)
				if err != nil {
					logger.Errorf("saveNewResourceRefs ListResourceRef error: %+v", err)
					return err
				}
				if len(refList) > 0 {
					if len(refList) != 1 {
						logger.Errorf("saveNewResourceRefs error, resourceId %d JobConfigId %d VersionId %d len(%d) != 1",
							value.ResourceId, value.JobConfigId, value.VersionId, len(refList))
					}
					// skip
					continue
				} else {
					tx.SaveObject(value, "ResourceRef")
				}
			}
		}
		success = true
		return err
	}).Close()
	return success, err
}

func GetResourceRefByJobConfigId(jobConfigId int64) (resourceRefs []*model.ResourceRefItem, err error) {
	resourceRefs = make([]*model.ResourceRefItem, 0, 0)
	sql := "SELECT a.*, b.ResourceId as ResourceSerialId FROM ResourceRef as a, Resource as b WHERE a.ResourceId = b.Id and a.JobConfigId=? AND a.Status=? ORDER BY  a.Id ASC "
	args := make([]interface{}, 0, 2)
	args = append(args, jobConfigId)
	args = append(args, constants.BUCKET_REF_STATUS_ACTIVE)

	cnt, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("getResourceRefByJobConfigId sql:%s args:%+v error: %+v", sql, args, err)
		return resourceRefs, err
	}

	if cnt > 0 {
		for _, v := range data {
			ref := &model2.ResourceRef{}
			util.ScanMapIntoStruct(ref, v)
			refItem := &model.ResourceRefItem{}
			refItem.ResourceId = string(v["ResourceSerialId"])
			refItem.Version = ref.VersionId
			refItem.Type = ref.UsageType
			resourceRefs = append(resourceRefs, refItem)
		}
	}

	return resourceRefs, err
}

/**
 * 用户传递进来的类型进行转换，过滤部分值
 */
func GetResourceRefType(Type int) int {
	_, ok := constants.ResourceRefUsageTypeSet[Type]
	if !ok {
		return constants.RESOURCE_REF_USAGE_TYPE_DEFALUT
	}
	return Type
}

func GetSavepointDirectory(job *table2.Job, cosBucket string, jobRunningOrderId int64) (savepointDirectory string, err error) {

	if cosBucket != "" {
		return GetCOSSavepointUrl(cosBucket, job, jobRunningOrderId), nil
	}

	// 如果作业的集群支持COS
	// ClusterIsEnableLegacyCOS 这里是以前的旧逻辑， 很久很久前，有些集群是用到cos。 一个集群只能用一个 cos bucket。 而且，这个是通过在db 表里去配置
	// 2.11 迭代之后， 更改成，新部署的集群都使用 cos，不再使用hdfs。  然后 集群的 cos bucket 直接放到了 cluster 表里面
	// TODO(统一这里的逻辑)

	// 如果是历史版本的 EMR + COS
	enabledLegacyCOS, err := service3.ClusterIsEnableLegacyCOS(job.ClusterGroupId, job.ClusterId)
	if err != nil {
		logger.Errorf("GetSavepointDirectory -> ClusterIsEnableLegacyCOS job %s, ClusterGroupId %d, ClusterId %d, error: %+v",
			job.SerialId, job.ClusterGroupId, job.ClusterId, err)
		return savepointDirectory, err
	}
	if enabledLegacyCOS {
		bucketJobRefList, err := service3.ListBucketJobRefs(nil, "", job.OwnerUin, []string{job.SerialId}, nil)
		if err != nil {
			return savepointDirectory, err
		}
		// 假设集群开启了cos，但是找不到作业和cos bucket之间的引用，这时候会有问题
		if len(bucketJobRefList) == 0 {
			logger.Errorf("Job %s cluster is enabledLegacyCOS cos? %t, but can not found bucketName ref", job.SerialId, enabledLegacyCOS)
			err = fmt.Errorf("can not found bucketName by job %s", job.SerialId)
			return savepointDirectory, err
		}
		return GetCOSSavepointUrl(bucketJobRefList[0].BucketName, job, jobRunningOrderId), err
	}

	// 如果是新版 2.11 TKE COS
	cluster, err := service6.GetClusterByClusterId(job.ClusterId)
	if err != nil {
		return savepointDirectory, err
	}
	if cluster.DefaultCOSBucket != "" {
		return GetCOSSavepointUrl(cluster.DefaultCOSBucket, job, jobRunningOrderId), err
	} else {
		return GetHDFSSavepointUrl(job, jobRunningOrderId), err
	}
}

func GetHDFSCheckpointUrl(job *table2.Job, jobRunningOrderId int64) string {
	return getCheckpointUrl(fmt.Sprintf("hdfs:///state"), job, jobRunningOrderId)
}

func GetHDFSSavepointUrl(job *table2.Job, jobRunningOrderId int64) string {
	return getSavepointUrl(fmt.Sprintf("hdfs:///state"), job, jobRunningOrderId)
}

func GetCOSCheckpointUrl(bucketName string, job *table2.Job, jobRunningOrderId int64) string {
	return getCheckpointUrl(fmt.Sprintf("cosn://%s", bucketName), job, jobRunningOrderId)
}

func GetCOSSavepointUrl(bucketName string, job *table2.Job, jobRunningOrderId int64) string {
	return getSavepointUrl(fmt.Sprintf("cosn://%s", bucketName), job, jobRunningOrderId)
}

func getCheckpointUrl(fileSystemPrefix string, job *table2.Job, jobRunningOrderId int64) string {
	return fmt.Sprintf("%s/%d/%s/%s/%d/flink-checkpoints",
		fileSystemPrefix, job.AppId, job.CreatorUin, job.SerialId, jobRunningOrderId)
}

func getSavepointUrl(fileSystemPrefix string, job *table2.Job, jobRunningOrderId int64) string {
	return fmt.Sprintf("%s/%d/%s/%s/%d/flink-savepoints",
		fileSystemPrefix, job.AppId, job.CreatorUin, job.SerialId, jobRunningOrderId)
}

/**
 * 1.通过jobID找到作业实例
 * 2.判断该作业所在的集群和集群组到底是否开启了COS，如果通过代表这个集群可以访问cos
 * 3.通过bucketJobRef列表找到分配的bucket，组合成路径
 * 4.返回
 */
func GetStateDynamicProperties(job *table2.Job, cosBucket string,
	jobRunningOrderId int64) (dynamicProperties map[string]string, err error) {
	args := make([]interface{}, 0, 0)

	// ClusterIsEnableLegacyCOS 这里是以前的旧逻辑， 很久很久前，有些集群是用到cos。 一个集群只能用一个 cos bucket。 而且，这个是通过在db 表里去配置
	// 2.11 迭代之后， 更改成，新部署的集群都使用 cos，不再使用hdfs。  然后 集群的 cos bucket 直接放到了 cluster 表里面
	// TODO(统一这里的逻辑)
	enabledCOS, err := service3.ClusterIsEnableLegacyCOS(job.ClusterGroupId, job.ClusterId)
	if err != nil {
		logger.Errorf("GetDynamicProperties -> ClusterIsEnableLegacyCOS error:%+v", err)
		return dynamicProperties, err
	}

	properties := make(map[string]string, 0)
	logger.Infof("Job %s is on a cluster with COS enabled? %d", job.SerialId, func() int {
		if enabledCOS {
			return 1
		} else {
			return 0
		}
	}())

	SetHDFS := func() {
		properties[constants.FLINK_CHECKPOINT_DIR_V2] = GetHDFSCheckpointUrl(job, jobRunningOrderId)
		properties[constants.FLINK_CHECKPOINT_DIR_V1] = GetHDFSCheckpointUrl(job, jobRunningOrderId)
		properties[constants.FLINK_SAVEPOINT_DIR] = GetHDFSSavepointUrl(job, jobRunningOrderId)
	}

	SetCos := func(bucket string) {
		properties[constants.FLINK_CHECKPOINT_DIR_V2] = GetCOSCheckpointUrl(bucket, job, jobRunningOrderId)
		properties[constants.FLINK_CHECKPOINT_DIR_V1] = GetCOSCheckpointUrl(bucket, job, jobRunningOrderId)
		properties[constants.FLINK_SAVEPOINT_DIR] = GetCOSSavepointUrl(bucket, job, jobRunningOrderId)
	}

	// 这里是以前的旧逻辑。
	// 新的集群， COS bucket 是配置在 Cluster 表里面的
	if enabledCOS {
		args = make([]interface{}, 0, 0)
		args = append(args, job.SerialId)
		args = append(args, constants.RESOURCE_REF_STATUS_ACTIVE)

		sql := "SELECT * FROM BucketJobRef WHERE JobId=? AND Status=?"
		cnt, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
		if err != nil {
			logger.Errorf("GetDynamicProperties -> DoQuery sql:%s args:%+v error:%+v", sql, args, err)
			return dynamicProperties, err
		}
		if cnt != 0 {
			ref := &table3.BucketJobRef{}
			util.ScanMapIntoStruct(ref, data[0])
			SetCos(ref.BucketName)
		} else {
			logger.Infof("GetDynamicProperties -> BucketJobRef size is 0")
		}
	} else if cosBucket != "" {
		SetCos(cosBucket)
	} else {
		SetHDFS()
	}

	return properties, err
}

/**
 * 更新传入的Refs，如果包含资源组，将资源组的应用更新到Refs中
 */
func UpdateRefs(refs []*model.ResourceRefItem) (result []*model.ResourceRefItem, err error) {
	//for _, v := range refs {
	//	if resource, err := GetResourceByResourceRef(v); err != nil {
	//		return result, err
	//	} else {
	//		// 1. 检查传递进来的资源是否是资源组
	//		if resource.IsResGroup == constants.RESOURCE_GROUP_ENABLED {
	//			resGroupList, err := ListResGroups([]string{resource.ResourceId}, nil)
	//			if err != nil {
	//				return result, err
	//			}
	//			// 2. 将资源组的依赖加入到原来的ResourceRef中
	//			for _, resGroup := range resGroupList {
	//				item := &model.ResourceRefItem{ResourceId: resGroup.ResourceId, Version: resGroup.Version}
	//				result = append(result, item)
	//			}
	//		} else {
	//			result = append(result, v)
	//		}
	//	}
	//}
	//不再替换最新的connector，以jobconfig表记录为最终版本，实际版本=JobConfig.VersionId
	logger.Debugf("Update default ref size:%d, to refs size: %d", len(refs), len(result))

	logger.Infof("Update default ref UpdateRefs refs : %s , result : %s", refs, result)

	return refs, err
}

func BuildLocalizationContextsV2(job *table2.Job, refs []*model.ResourceRefItem, resourceConfigItem *model.ResourceConfigItem) (contexts []*model2.LocalizationContext, dependencies []*model2.UserDependencyContext, err error) {

	contexts = make([]*model2.LocalizationContext, 0, 0)
	dependencies = make([]*model2.UserDependencyContext, 0, 0)
	if len(refs) == 0 && resourceConfigItem == nil {
		return contexts, dependencies, err
	}
	refsCopy := make([]*model.ResourceRefItem, len(refs))
	copy(refsCopy, refs)
	refs, err = UpdateRefs(refs)
	logger.Infof("BuildLocalizationContextsV2 useOldSysConnector UpdateRefs refs : %s", refs)
	if err != nil {
		bytes, _ := json.Marshal(refs)
		logger.Errorf("Failed to update resource ref %s error:%+v", string(bytes), err)
		return contexts, dependencies, err
	}

	cluster, err := service6.GetClusterByClusterId(job.ClusterId)
	if err != nil {
		logger.Errorf("Failed to GetClusterByClusterId %d error:%+v", job.ClusterId, err)
		return contexts, dependencies, err
	}

	tmpSecretId := ""
	tmpSecretKey := ""
	token := ""
	for _, v := range refs {
		context := &model2.LocalizationContext{}

		resource, err := GetResourceByResourceRef(v)
		if err != nil {
			return contexts, dependencies, err
		}
		resourceConfig, err := GetResourceConfigByResourceRef(v)
		if err != nil {
			return contexts, dependencies, err
		}
		if resourceConfig.Status != constants.RESOURCE_CONF_STATUS_ACTIVE {
			return contexts, dependencies, errors.New("resourceConfig status not active")
		}

		context.UsageType = v.Type

		context.CreatorUin = resource.CreatorUin
		context.ResourceName = resource.ResourceName
		context.Version = resourceConfig.VersionId

		loc := &model4.ResourceLocation{}
		err = json.Unmarshal([]byte(resourceConfig.ResourceLoc), loc)
		if err != nil {
			logger.Errorf("Failed to Unmarshal %+v", err)
			return contexts, dependencies, err
		}

		context.Region = service4.NewResourceLocationService(resource, loc).GetResourceLocRegion()
		context.StorageType = switchStorageType(loc.StorageType)
		context.Bucket = loc.Param.Bucket
		context.Path = loc.Param.Path

		logger.Infof("BuildLocalizationContextsV2 context.Path context.Path : %s", context.Path)

		exists, err := service3.BucketExists(context.Bucket)
		if err != nil {
			logger.Errorf("Failed to BucketExists %+v", err)
			return contexts, dependencies, err
		}

		// 如果bucket是我们维护的，则用传入SecretId，SecretKey，否则获取临时的SecretId，SecretKey，token
		// PS：其实所有的都可以走临时密钥
		if exists {
			if !strings.Contains(cluster.SupportedFeatures, constants.FeatureTmpCosSecret) {
				SecretId, SecretKey, err := service5.GetSecretIdAndKeyOfCos()
				if err != nil {
					logger.Errorf("BuildLocalizationContextsV2 GetSecretIdAndKeyOfCos error %+v", err)
					return contexts, dependencies, err
				}
				context.SecretKey = SecretKey
				context.SecretId = SecretId
				context.Token = ""
			} else {
				if tmpSecretId == "" {
					tmpSecretId, tmpSecretKey, token, err =
						service5.GetTmpSecretIdAndKeyOfCos(context.Region, job.OwnerUin, context.Bucket, int64(job.AppId))
					if err != nil {
						logger.Errorf("TmpCosSecret_GetTmpSecretIdAndKeyOfCos with region %s error %+v", context.Region, err)
						return contexts, dependencies, err
					}
				}
				context.SecretId = tmpSecretId
				context.SecretKey = tmpSecretKey
				context.Token = token
			}
		} else {
			SecretId, SecretKey, Token, pass, err := service5.StsAssumeRole(job.OwnerUin, job.CreatorUin, job.Region)
			if err != nil {
				logger.Errorf("Failed to StsAssumeRole %s %s %s error: %+v",
					job.OwnerUin, job.CreatorUin, job.Region, err)
				return contexts, dependencies, err
			}
			if !pass {
				logger.Errorf("Failed to BuildLocalizationContextsV2 -> StsAssumeRole %s %s %s not pass",
					job.OwnerUin, job.CreatorUin, job.Region)
				return contexts, dependencies, fmt.Errorf("StsAssumeRole not pass")
			}
			context.SecretKey = SecretKey
			context.SecretId = SecretId
			context.Token = Token
		}

		workSpace, err := service2.GetScsWorkspace()
		if err != nil {
			return contexts, dependencies, err
		}

		context.WorkSpace = workSpace
		contexts = append(contexts, context)
		if context.UsageType == constants.RESOURCE_REF_USAGE_TYPE_DEPENDENCY {
			dependencies = append(dependencies, createUserDependencyContext(context))
		}
	}
	if resourceConfigItem != nil && resourceConfigItem.ResourceLoc != nil {
		context := &model2.LocalizationContext{}

		context.UsageType = resourceConfigItem.Type

		context.CreatorUin = resourceConfigItem.CreatorUin
		context.ResourceName = resourceConfigItem.ResourceName
		context.Version = resourceConfigItem.VersionId

		loc := resourceConfigItem.ResourceLoc

		context.Region = loc.Param.Region
		context.StorageType = switchStorageType(loc.StorageType)
		context.Bucket = loc.Param.Bucket
		context.Path = loc.Param.Path

		if !strings.Contains(cluster.SupportedFeatures, constants.FeatureTmpCosSecret) {
			SecretId, SecretKey, err := service5.GetSecretIdAndKeyOfCos()
			if err != nil {
				logger.Errorf("BuildLocalizationContextsV2 GetSecretIdAndKeyOfCos error %+v", err)
				return contexts, dependencies, err
			}
			context.SecretKey = SecretKey
			context.SecretId = SecretId
			context.Token = ""
		} else {
			if tmpSecretId == "" {
				tmpSecretId, tmpSecretKey, token, err =
					service5.GetTmpSecretIdAndKeyOfCos(context.Region, job.OwnerUin, context.Bucket, int64(job.AppId))
				if err != nil {
					logger.Errorf("TmpCosSecret_GetTmpSecretIdAndKeyOfCos with region %s error %+v", context.Region, err)
					return contexts, dependencies, err
				}
			}
			context.SecretId = tmpSecretId
			context.SecretKey = tmpSecretKey
			context.Token = token
		}

		workSpace, err := service2.GetScsWorkspace()
		if err != nil {
			return contexts, dependencies, err
		}

		context.WorkSpace = workSpace
		contexts = append(contexts, context)
		if context.UsageType == constants.RESOURCE_REF_USAGE_TYPE_DEPENDENCY {
			dependencies = append(dependencies, createUserDependencyContext(context))
		}
	}
	logger.Infof("BuildLocalizationContextsV2 refs size %d, contexts size %d", len(refs), len(contexts))
	return contexts, dependencies, err
}

func createUserDependencyContext(context *model2.LocalizationContext) *model2.UserDependencyContext {
	dependency := &model2.UserDependencyContext{}
	dependency.UsageType = context.UsageType
	dependency.Bucket = context.Bucket
	dependency.Region = context.Region
	dependency.CreatorUin = context.CreatorUin
	dependency.Path = context.Path
	dependency.ResourceName = context.ResourceName
	dependency.StorageType = context.StorageType
	dependency.Version = context.Version
	dependency.WorkSpace = context.WorkSpace
	var err error
	dependency.DispatchPath, err = config.GetRainbowConfiguration(constants.RESOURCE_DEPENDENCY_PATH_CONFIG_GROUP, constants.RESOURCE_DEPENDENCY_PATH_CONFIG_KEY)
	if err != nil {
		dependency.DispatchPath = constants.RESOURCE_DEPENDENCY_DEFAULT_PATH
	}
	return dependency
}

func GetResourceConfigByResourceRef(v *model.ResourceRefItem) (*model3.ResourceConfig, error) {
	resourceConfig := &model3.ResourceConfig{}
	args := make([]interface{}, 0)
	sql := ""

	logger.Infof("GetResourceConfigByResourceRef GetResourceConfigByResourceRef ResourceRefItem : %s", v)

	if v.Version == constants.RESOURCE_REF_VERSION_ID_USE_LATEST {
		args = append(args, v.ResourceId)
		args = append(args, v.ResourceId)
		args = append(args, constants.RESOURCE_CONF_STATUS_ACTIVE)
		sql = "SELECT a.* FROM ResourceConfig as a, Resource as b WHERE a.ResourceId = b.Id And b.ResourceId=? " +
			" AND VersionId=(SELECT max(a.VersionId) FROM ResourceConfig as a, Resource as b WHERE a.ResourceId = b.Id AND b.ResourceId=? AND a.Status = ?) "
	} else {
		args = append(args, v.ResourceId)
		args = append(args, v.Version)
		sql = "SELECT a.* FROM ResourceConfig as a, Resource as b WHERE a.ResourceId = b.Id AND b.ResourceId=? AND a.VersionId=?"
	}

	cnt, resourceConfigData, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("BuildLocalizationContexts -> DoQuery sql:%s args:%+v error:%+v", sql, args, err)
		return nil, err
	}

	if cnt != 1 {
		err = fmt.Errorf("BuildLocalizationContexts -> DoQuery sql:%s args:%+v resource config len(%d) != 1",
			sql, args, cnt)
		return nil, err
	}

	util.ScanMapIntoStruct(resourceConfig, resourceConfigData[0])
	return resourceConfig, nil
}

func GetResourceByResourceRef(v *model.ResourceRefItem) (*model2.Resource, error) {
	resource := &model2.Resource{}
	args := make([]interface{}, 0, 2)
	args = append(args, v.ResourceId)
	args = append(args, constants.RESOURCE_STATUS_ACTIVE)
	sql := "SELECT * FROM Resource WHERE ResourceId=? AND Status=?"
	cnt, resourceData, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("BuildLocalizationContexts -> DoQuery sql:%s args:%+v error:%+v", sql, args, err)
		return nil, err
	}

	if cnt != 1 {
		err = fmt.Errorf("BuildLocalizationContexts -> DoQuery sql:%s args:%+v resource len(%d) != 1", sql, args, cnt)
		return nil, err
	}

	util.ScanMapIntoStruct(resource, resourceData[0])
	return resource, nil
}

/**
 * 目前默认只支持COS
 */
func switchStorageType(storageType int) string {
	switch storageType {
	case constants.STORAGE_TYPE_COS:
		return "COS"
	default:
		return "COS"
	}
}

func BuildLocalizationContextsV1(jobConfigId int64) (contexts []*model2.LocalizationContext, err error) {
	contexts = make([]*model2.LocalizationContext, 0, 0)
	args := make([]interface{}, 0, 0)
	args = append(args, jobConfigId)
	args = append(args, constants.RESOURCE_REF_STATUS_ACTIVE)
	sql := "SELECT * FROM ResourceRef WHERE JobConfigId=? AND Status=?"
	cnt, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("BuildLocalizationContexts -> DoQuery sql:%s args:%+v error:%+v", sql, args, err)
		return contexts, err
	}
	if cnt == 0 {
		logger.Warningf("BuildLocalizationContexts -> jobConfigId %d ResourceRef cnt == 0", jobConfigId)
	}

	for _, v := range data {
		ref := &model2.ResourceRef{}
		context := &model2.LocalizationContext{}
		util.ScanMapIntoStruct(ref, v)

		args = make([]interface{}, 0, 0)
		if ref.VersionId == constants.RESOURCE_REF_VERSION_ID_USE_LATEST {
			args = append(args, ref.ResourceId)
			args = append(args, constants.RESOURCE_REF_STATUS_ACTIVE)
			args = append(args, ref.ResourceId)
			args = append(args, constants.RESOURCE_REF_STATUS_ACTIVE)
			sql = "SELECT * FROM ResourceConfig WHERE ResourceId=? AND Status=? " +
				" AND VersionId=(SELECT max(VersionId) FROM ResourceConfig WHERE ResourceId=? AND Status=?)"
		} else {
			args = append(args, ref.ResourceId)
			args = append(args, constants.RESOURCE_REF_STATUS_ACTIVE)
			args = append(args, ref.VersionId)
			sql = "SELECT * FROM ResourceConfig WHERE ResourceId=? AND Status=? AND VersionId=?"
		}

		cnt, resourceConfigData, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
		if err != nil {
			logger.Errorf("BuildLocalizationContexts -> DoQuery sql:%s args:%+v error:%+v", sql, args, err)
			return contexts, err
		}

		if cnt != 1 {
			err = fmt.Errorf("BuildLocalizationContexts -> DoQuery sql:%s args:%+v resource config len(%d) != 1",
				sql, args, cnt)
			return contexts, err
		}

		resourceConfig := &model3.ResourceConfig{}
		util.ScanMapIntoStruct(resourceConfig, resourceConfigData[0])

		args = make([]interface{}, 0, 0)
		args = append(args, ref.ResourceId)
		args = append(args, constants.RESOURCE_STATUS_ACTIVE)
		sql = "SELECT * FROM Resource WHERE Id=? AND Status=?"
		cnt, resourceData, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
		if err != nil {
			logger.Errorf("BuildLocalizationContexts -> DoQuery sql:%s args:%+v error:%+v", sql, args, err)
			return contexts, err
		}

		if cnt != 1 {
			err = fmt.Errorf("BuildLocalizationContexts -> DoQuery sql:%s args:%+v resource len(%d) != 1", sql, args, cnt)
			return contexts, err
		}

		resource := &model2.Resource{}
		util.ScanMapIntoStruct(resource, resourceData[0])

		context.UsageType = ref.UsageType
		context.Region = resource.Region
		context.CreatorUin = resource.CreatorUin
		context.ResourceName = resource.ResourceName
		context.Version = resourceConfig.VersionId

		loc := &model4.ResourceLocation{}
		json.Unmarshal([]byte(resourceConfig.ResourceLoc), loc)

		context.StorageType = switchStorageType(loc.StorageType)
		context.Bucket = loc.Param.Bucket
		context.Path = loc.Param.Path

		SecretId, SecretKey, err := service5.GetSecretIdAndKeyOfScs()
		if err != nil {
			return contexts, err
		}

		context.SecretKey = SecretId
		context.SecretId = SecretKey
		contexts = append(contexts, context)
	}

	return contexts, err
}

func buildResourceRefs(req *model.CreateJobConfigReq, resources map[string]*model2.Resource, jobConfigId int64) ([]*model2.ResourceRef, error) {

	resourceRefs := make([]*model2.ResourceRef, 0, 0)
	for _, value := range req.ResourceRefs {
		resourceRef := &model2.ResourceRef{}
		resourceRef.Status = constants.RESOURCE_REF_STATUS_ACTIVE
		if _, ok := resources[value.ResourceId]; ok {
			resourceRef.ResourceId = resources[value.ResourceId].Id
		} else {
			logger.Errorf("resource not found, with id:%s", value.ResourceId)
			return nil, errors.New(controller.ResourceNotFound)
		}
		resourceRef.JobConfigId = jobConfigId
		if value.Version <= 0 { // 如果不传或者写0则默认用最新版本
			value.Version = constants.RESOURCE_REF_VERSION_ID_USE_LATEST
		}
		resourceRef.VersionId = value.Version
		resourceRef.UsageType = GetResourceRefType(value.Type)
		resourceRefs = append(resourceRefs, resourceRef)
		resourceRef.CreateTime = time.Now().Format("2006-01-02 15:04:05")
	}
	logger.Infof("[AppId %d] build resource ref size: %d", req.AppId, len(resourceRefs))
	return resourceRefs, nil
}

func DoDeleteResourceRefs(JobIds []string) (success bool, err error) {
	return DeleteResourceRefs(JobIds)
}

func DeleteResourceRefs(JobIds []string) (success bool, err error) {
	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		for _, id := range JobIds {
			args := make([]interface{}, 0, 0)
			args = append(args, id)
			cnt, data, err := tx.Query("SELECT * FROM Job WHERE SerialId=?", args)
			if cnt != 1 {
				err = fmt.Errorf("SerialId %s len %d", id, cnt)
				return err
			}
			job := &table2.Job{}
			_ = util.ScanMapIntoStruct(job, data[0])

			// 此处GetJobConfigByStatus，查询状态 JOB_CONFIG_STATUS_PUBLISHED_SUCC
			jobConfigs, err := service.GetJobConfigsByStatus(job.Id, []int{constants.JOB_CONFIG_STATUS_ON_PUBLISHED,
				constants.JOB_CONFIG_STATUS_PUBLISHED_FAIL, constants.JOB_CONFIG_STATUS_PUBLISHED_SUCC,
				constants.JOB_CONFIG_STATUS_UN_PUBLISHED, constants.JOB_CONFIG_STATUS_DELETE})
			if err != nil {
				return err
			}

			if len(jobConfigs) > 0 {
				sql := "UPDATE ResourceRef SET Status=? WHERE JobConfigId IN ("
				args = make([]interface{}, 0, 0)
				args = append(args, constants.RESOURCE_REF_STATUS_DELETE)
				for i := 0; i < len(jobConfigs); i++ {
					sql += "?"
					if i != len(jobConfigs)-1 {
						sql += ","
					}
					args = append(args, jobConfigs[i].Id)
				}
				sql += ")"
				tx.ExecuteSql(sql, args)
			}

			args = make([]interface{}, 0, 0)
			args = append(args, constants.BUCKET_REF_STATUS_DELETE)
			args = append(args, id)
			tx.ExecuteSql("UPDATE BucketJobRef SET Status=? WHERE JobId=?", args)
		}
		success = true
		return err
	}).Close()
	return success, err
}

func CheckJobConfigResourceRefReady(jobConfigId int64) (bool, error) {
	resourceRefs, err := GetResourceRefByJobConfigId(jobConfigId)
	if err != nil {
		return false, err
	}

	resourceRefs, err = UpdateRefs(resourceRefs)
	if err != nil {
		bytes, _ := json.Marshal(resourceRefs)
		logger.Errorf("Failed to update resource ref %s error:%+v", string(bytes), err)
		return false, err
	}

	for _, ref := range resourceRefs {
		resourceConfig, err := GetResourceConfigByResourceRef(ref)
		logger.Infof("#CheckJobConfigResourceRefReady: %+v", resourceConfig)
		if err != nil {
			return false, err
		}
		if resourceConfig.Status != constants.RESOURCE_CONF_STATUS_ACTIVE {
			return false, nil
		}
	}
	return true, nil
}

// ReplaceConnectorsByJobConfigId 根据指定的JobConfigId替换connector
func ReplaceConnectorsByJobConfigId(jobConfigId int64, innerResourceRefs *[]*sql_model.ResourceLocationIntegral) error {
	logger.Infof("ReplaceConnectorsByJobConfigId jobConfigId: %d", jobConfigId)

	// 获取指定的作业配置
	jobConfig, err := service.GetJobConfigById(jobConfigId)
	if err != nil {
		logger.Errorf("Failed to get job config by id: %d, with errors: %+v", jobConfigId, err)
		return err
	}

	// 获取指定作业配置的资源引用
	targetRefs, err := service4.GetResourceRefByJobConfigId(jobConfig.Id)
	if err != nil {
		logger.Errorf("Failed to get resource ref by job config id: %d, with errors: %+v", jobConfig.Id, err)
		return err
	}

	// 创建目标资源引用的映射，用于快速查找
	targetRefMap := make(map[string]int64)
	for _, ref := range targetRefs {
		resource, err := service4.GetResourceByResourceRef(ref)
		if err != nil {
			logger.Errorf("Failed to get resource by resource ref: %v, error: %+v", ref, err)
			continue
		}
		if resource.SystemProvide != constants.SYSTEM_PROVIDED {
			continue
		}
		targetRefMap[ref.ResourceId] = ref.Version
	}

	// 遍历当前资源位置，如果找到匹配的目标版本，则替换版本号
	for i := range *innerResourceRefs {
		if targetVersion, exists := targetRefMap[(*innerResourceRefs)[i].ResourceSerialId]; exists {
			// 直接替换VersionId
			(*innerResourceRefs)[i].VersionId = targetVersion
			logger.Infof("Replaced connector %s with version %d from JobConfigId %d", (*innerResourceRefs)[i].ResourceSerialId, targetVersion, jobConfigId)
		}
	}

	return nil
}

// ReplaceOldSystemConnectorForCheck 按照replaceOldSystemConnector的逻辑替换connector（用于语法检查）
func ReplaceOldSystemConnectorForCheck(jobId, uin string, appId int64, region string, itemSpaceId int64, innerResourceRefs *[]*sql_model.ResourceLocationIntegral) error {
	// 获取Job信息
	job, err := common.GetJobBySerialId(jobId, uin, appId, region, itemSpaceId)
	if err != nil {
		logger.Errorf("Failed to get job by serial id: %s, with errors: %+v", jobId, err)
		return err
	}
	if job == nil {
		logger.Infof("Job not found for serial id: %s, skip connector replacement", jobId)
		return nil
	}

	// 获取旧版本作业配置ID
	var jobConfigId int64
	if job.PublishedJobConfigId != -1 && job.PublishedJobConfigId != 0 {
		jobConfigId = job.PublishedJobConfigId
	} else if job.LatestJobConfigId != -1 && job.LatestJobConfigId != 0 {
		jobConfigId = job.LatestJobConfigId
	} else {
		logger.Infof("No valid job config found for job: %s, skip connector replacement", jobId)
		return nil
	}

	logger.Infof("ReplaceOldSystemConnectorForCheck jobConfigId: %d", jobConfigId)

	// 获取旧版本作业配置
	jobConfig, err := common.GetJobConfigById(jobConfigId)
	if err != nil {
		logger.Errorf("Failed to get job config by id: %d, with errors: %+v", jobConfigId, err)
		return err
	}

	// 获取旧版本资源引用
	oldRefs, err := service4.GetResourceRefByJobConfigId(jobConfig.Id)
	if err != nil {
		logger.Errorf("Failed to get resource ref by job config id: %d, with errors: %+v", jobConfig.Id, err)
		return err
	}

	// 创建旧版本资源引用的映射，用于快速查找
	oldRefMap := make(map[string]int64)
	for _, ref := range oldRefs {
		resource, err := service4.GetResourceByResourceRef(ref)
		if err != nil {
			logger.Errorf("Failed to get resource by resource ref: %v, error: %+v", ref, err)
			continue
		}
		if resource.SystemProvide != constants.SYSTEM_PROVIDED {
			continue
		}
		oldRefMap[ref.ResourceId] = ref.Version
	}

	// 遍历当前资源位置，如果找到匹配的旧版本，则替换版本号
	for i := range *innerResourceRefs {
		if oldVersion, exists := oldRefMap[(*innerResourceRefs)[i].ResourceSerialId]; exists {
			// 直接替换VersionId
			(*innerResourceRefs)[i].VersionId = oldVersion
			logger.Infof("Replaced connector %s with old version %d", (*innerResourceRefs)[i].ResourceSerialId, oldVersion)
		}
	}

	return nil
}
