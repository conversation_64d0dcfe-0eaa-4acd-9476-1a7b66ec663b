package service

import (
	"fmt"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/resource"
	"testing"
)

/**
删除一个错误的ID
*/
func Test_DeleteResourcesWithErrorID(t *testing.T) {
	initDb()

	// 删除一个不属于他的资源ID
	delReq := &model.DeleteResourceReq{
		RequestBase: apiv3.RequestBase{
			AppId:         **********,
			Uin:           "***********",
			SubAccountUin: "***********",
			Region:        "ap-guangzhou",
		},
		ResourceIds: []string{"1"},
	}
	rsp1, err := DoDeleteResources(delReq)
	if err != nil {
		t.Logf("DeleteResources检查参数合法性不通过 %s", err.Error())
		println(fmt.Sprintf("DeleteResources检查参数合法性不通过 %s", err.Error()))
	} else {
		t.<PERSON><PERSON><PERSON>("DeleteResources检查参数合法性通过 %+v", rsp1)
		println(fmt.Sprintf("DeleteResources检查参数合法性通过 %+v", rsp1))
	}
}

/**
删除一个ID
*/

func Test_DeleteResources(t *testing.T) {
	initDb()

	//创建作业
	req := MakeCreateResourceReq()
	rsp, _ := DoCreateResource(req)

	//删除作业
	delReq := &model.DeleteResourceReq{
		RequestBase: apiv3.RequestBase{
			AppId:         **********,
			Uin:           "***********",
			SubAccountUin: "***********",
			Region:        "ap-guangzhou",
		},
		ResourceIds: []string{rsp.ResourceId},
	}
	DoDeleteResources(delReq)
}

/**
删除一个不存在的ID
*/

func Test_DeleteResourcesWithOtherId(t *testing.T) {
	initDb()

	//创建作业
	req := MakeCreateResourceReq()
	rsp, _ := DoCreateResource(req)

	//删除作业
	delReq := &model.DeleteResourceReq{
		RequestBase: apiv3.RequestBase{
			AppId:         **********,
			Uin:           "***********",
			SubAccountUin: "***********",
			Region:        "ap-guangzhou",
		},
		ResourceIds: []string{rsp.ResourceId, "1000"},
	}
	DoDeleteResources(delReq)
}

/**
多次删除一个ID
*/
func Test_DeleteResourcesWithSameId(t *testing.T) {
	initDb()

	//创建作业
	req := MakeCreateResourceReq()
	rsp, _ := DoCreateResource(req)

	//删除作业
	delReq := &model.DeleteResourceReq{
		RequestBase: apiv3.RequestBase{
			AppId:         **********,
			Uin:           "***********",
			SubAccountUin: "***********",
			Region:        "ap-guangzhou",
		},
		ResourceIds: []string{rsp.ResourceId, rsp.ResourceId},
	}
	DoDeleteResources(delReq)
}

/**
多次删除一个ID
*/
func Test_DeleteResourcesWithJobRef(t *testing.T) {
	initDb()

	//删除作业
	println("测试删除作业：资源被作业引用")
	delReq := &model.DeleteResourceReq{
		RequestBase: apiv3.RequestBase{
			AppId:         **********,
			Uin:           "***********",
			SubAccountUin: "***********",
			Region:        "ap-guangzhou",
		},
		ResourceIds: []string{"92"},
	}
	DoDeleteResources(delReq)
}
