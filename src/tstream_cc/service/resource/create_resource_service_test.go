package service

import (
	"fmt"
	"testing"
)

func Test_CreateResource(t *testing.T) {
	initDb()
	rsp, err := DoCreateResource(MakeCreateResourceReq())
	if err != nil {
		t.<PERSON>rro<PERSON>("create resource  FAILED %+v", err)
		println(fmt.Sprintf("create resource  FAILED %+v", err))
	} else {
		t.Logf("create resource success %+v", rsp)
		println(fmt.Sprintf("create resource success %+v", rsp))
	}
}

func Test_CreateSameResourceName(t *testing.T) {
	initDb()
	req := MakeCreateResourceReq()
	DoCreateResource(req)
	rsp, err := DoCreateResource(req)

	if err != nil {
		println(fmt.Sprintf("CreateSameResourceName SUCCESS %+v", err))
		t.Logf("CreateSameResourceName SUCCESS %+v", err)
	} else {
		println(fmt.Sprintf("CreateSameResourceName isOk %+v", rsp))
		t.<PERSON><PERSON>("CreateSameResourceName isOk %+v", rsp)
	}
}
