package service

import (
	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/resource"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/resource"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	cluster "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
)

func DoDescribeSystemResources(req *model.DescribeSystemResourcesReq) (rsp *model.DescribeSystemResourcesRsp,
	err error) {

	rsp = &model.DescribeSystemResourcesRsp{
		TotalCount:  0,
		ResourceSet: make([]*model.SystemResourceItem, 0),
	}
	itemSpcIds := make([]int64, 0)
	if req.WorkSpaceId != "" {
		itemSpcIds, err = auth.ExtractSubUinAuthItemSpaceIdSet(req.WorkSpaceId, req.IsSupOwner, req.AppId, req.SubAccountUin, req.Uin, req.Region)
		if err != nil {
			logger.Errorf("%s: ExtractSubUinAuthItemSpaceIdSet : Obtain Catalog that has permissions error: %+v", req.RequestId, err)
			return rsp, err
		}
	}

	// 1. 检查集群ID和Filters
	if err = CheckSystemClusterAndFilters(req); err != nil {
		return
	}

	resourceId := make([]string, 0)
	// 2.查自定义connector
	userConnector, err := ListResources(req.AppId, req.Region, nil, nil, nil, nil, nil, nil, false, req.Offset, req.Limit, -1, constants.USER_CONNECTOR, itemSpcIds)
	if err != nil {
		return nil, err
	}
	for _, v := range userConnector {
		resourceId = append(resourceId, v.ResourceId)
	}

	// 3.查内置connector
	systemResources, err := ListSystemResourceRefById(req)
	if err != nil {
		return
	}
	for _, v := range systemResources {
		resourceId = append(resourceId, v)
	}

	totalCount, resourceSet, err := GetResources(buildDescribeResourcesReq(req, resourceId), constants.CONNECTOR, nil)
	if err != nil {
		return
	}

	resourceDetailSet, err := BuildSystemResSetFromResourceEntity(resourceSet)
	if err != nil {
		return
	}

	rsp = &model.DescribeSystemResourcesRsp{TotalCount: totalCount, ResourceSet: resourceDetailSet}

	return
}

/**
 * 检查请求合法性
 */
func CheckSystemClusterAndFilters(req *model.DescribeSystemResourcesReq) (err error) {

	if len(req.Filters) > 2 {
		return errorcode.InvalidParameterValueCode.ReplaceDesc("Filters should not contain more than 2 elements")
	}
	//885324049 查询资源校验 AppId
	clusterGroup, err := cluster.ListClusterGroupBySerialIdV2(req.ClusterId, int(req.AppId))
	if err != nil {
		return
	}

	if clusterGroup == nil {
		return errorcode.ResourceNotFound_Cluster.ReplaceDesc(req.ClusterId)
	}

	for _, filter := range req.Filters {
		if filter.Name != "ResourceName" {
			return errorcode.InvalidParameterValueCode.ReplaceDesc("Unknown filter.name, can only be one of `ResourceName`")
		}
		if len(filter.Values) > 5 {
			return errorcode.InvalidParameterValueCode.ReplaceDesc("Filter.Values should not contain more than 5 elements")
		}
	}

	return
}

func buildDescribeResourcesReq(req *model.DescribeSystemResourcesReq, resources []string) *model.DescribeResourcesReq {
	describeResourceReq := &model.DescribeResourcesReq{}
	describeResourceReq.AppId = req.AppId
	describeResourceReq.Uin = req.Uin
	describeResourceReq.SubAccountUin = req.SubAccountUin
	describeResourceReq.RequestId = req.RequestId
	describeResourceReq.Region = req.Region
	describeResourceReq.Version = req.Version

	describeResourceReq.ResourceIds = resources
	describeResourceReq.Limit = req.Limit
	describeResourceReq.Offset = req.Offset
	return describeResourceReq
}

func BuildSystemResSetFromResourceEntity(resources []*table.Resource) (details []*model.SystemResourceItem, err error) {
	details = make([]*model.SystemResourceItem, 0, 0)
	for _, res := range resources {
		item := &model.SystemResourceItem{}
		item.ResourceId = res.ResourceId
		if res.SystemProvide == constants.USER_CONNECTOR {
			item.Name = res.Connector
		} else {
			item.Name = res.ResourceName
		}
		item.Region = res.Region
		item.Remark = res.Remark
		item.ResourceType = res.ResourceType
		item.SystemProvide = res.SystemProvide
		resourceConfig, err := GetResourceConfigByResourceId(res.Id)
		if err != nil {
			return details, err
		}
		item.LatestResourceConfigVersion = resourceConfig.VersionId
		details = append(details, item)
	}
	service.SortSliceByField(details, "Name", false)
	return details, err
}
