package service

import (
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/cluster"
)

func DoCheckConnectorName(req *model.CheckConnectorNameReq) (*model.CheckConnectorNameRsp, error) {
	if req == nil {
		return nil, errorcode.InternalErrorCode.New()
	}

	connectorNameExists, err := CheckConnectorName(req.Name)
	if err != nil {
		return nil, err
	}
	rsp := &model.CheckConnectorNameRsp{Exists: connectorNameExists}
	return rsp, nil
}

func CheckConnectorName(name string) (exits bool, err error) {
	connectorConfigItems, err := GetRainbowConnectorConfig()
	if err != nil {
		return false, err
	}

	if len(connectorConfigItems) > 0 {
		for _, v := range connectorConfigItems {
			if !v.Hide && v.Connector == name {
				exits = true
				return
			}
		}
	}

	args := make([]interface{}, 0, 0)
	args = append(args, constants.SYSTEM_PROVIDED)
	args = append(args, constants.RESOURCE_STATUS_ACTIVE)
	args = append(args, name)
	count, _, err := service.GetTxManager().GetQueryTemplate().DoQuery(
		"SELECT * FROM Resource WHERE SystemProvide=? AND Status=? AND Connector =? ", args)
	if err != nil {
		return exits, err
	}
	// 名字已经存在
	if count != 0 {
		exits = true
	}
	return
}
