package service

import (
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/resource"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/resource"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
)

func ListSystemResourceRefById(req *model.DescribeSystemResourcesReq) (resources []string, err error) {

	resources = make([]string, 0, 0)
	flinkVersion := req.FlinkVersion
	if flinkVersion == "" {
		group, err := service2.ListClusterGroupBySerialId(req.ClusterId)
		if err != nil {
			return nil, err
		}

		clusterList, err := service2.ListClusters(group.Id)
		if err != nil {
			return nil, err
		}
		for _, cluster := range clusterList {
			if cluster.RoleType != constants.CLUSTER_ROLE_TYPE_ACTIVE {
				continue
			}
			// 如果 FlinkVersion 未设置，跳过查找，需要把集群的版本显示的设置Flink版本才能查询
			if cluster.FlinkVersion == "" {
				logger.Debugf("ClusterGroup %s Cluster %d flinkVersion is empty, skip query system resource",
					req.ClusterId, group.Id)
				continue
			}
			flinkVersion = cluster.FlinkVersion
		}
	}

	refList, err := ListSystemResourceRef([]string{flinkVersion}, nil)
	for _, ref := range refList {
		resources = append(resources, ref.ResourceId)
	}
	return resources, err
}

func ListSystemResourceRef(flinkVersions []string, resourceIds []string) ([]*table.SystemResourceRef, error) {

	sql := "SELECT * FROM SystemResourceRef"
	var args []interface{}

	condition := dao.NewCondition()
	condition.Ne("Status", constants.RESOURCE_STATUS_DELETE)

	if len(resourceIds) > 0 {
		condition = condition.In("ResourceId", resourceIds)
	}

	if len(flinkVersions) > 0 {
		condition = condition.In("FlinkVersion", flinkVersions)
	}

	where, args := condition.GetWhere()
	sql = sql + where

	_, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return nil, err
	}

	systemResourceRefList := make([]*table.SystemResourceRef, 0)
	for i := 0; i < len(data); i++ {
		systemResourceRef := &table.SystemResourceRef{}
		err = util.ScanMapIntoStruct(systemResourceRef, data[i])
		if err != nil {
			return nil, errorcode.InternalErrorCode.NewWithErr(err)
		}
		systemResourceRefList = append(systemResourceRefList, systemResourceRef)
	}

	return systemResourceRefList, err
}
