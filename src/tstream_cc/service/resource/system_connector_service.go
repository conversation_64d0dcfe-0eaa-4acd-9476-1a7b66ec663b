package service

import (
	"encoding/json"
	"errors"
	"fmt"
	"path/filepath"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/common/uuid"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"
	model1 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/bucket"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_config"
	model5 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/resource"
	model3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/resource_config"
	sql_model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/sql"
	table1 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/resource"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/resource_config"
	common "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service_bucket "tencentcloud.com/tstream_galileo/src/tstream_cc/service/bucket"
	service6 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	cos_service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cos"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
)

var (
	nullResource = &sql_model.ResourceLocationIntegral{ // 不引入任何connector
		ResourceId: constants.METADATA_TABLE_RESOURCE_REF_NO_NEED_REFERENCE_RESOURCE_ID,
		VersionId:  constants.METADATA_TABLE_RESOURCE_REF_NO_NEED_REFERENCE_VERSION,
	}
)

type DecodedSystemConnector struct {
	Connector   *table.SystemConnector
	optionsJson []*ConnectorOption
}

func GetSystemConnectorByOptions(connectorOptions map[string]string, flinkVersion string) (resourceLocation []*sql_model.ResourceLocationIntegral, systemConnector *table.SystemConnector, mismatched bool, err error) {
	connectors, err := service2.GetTableService().ListSystemConnectors(flinkVersion)
	if err != nil {
		return nil, nil, false, err
	}

	// connector -> match_flag
	systemConnectors := make(map[*DecodedSystemConnector]bool, len(connectors))
	for _, conn := range connectors {
		if len(conn.Options) == 0 {
			continue
		}
		optionsJson := make([]*ConnectorOption, 0)
		err = json.Unmarshal([]byte(conn.Options), &optionsJson)
		if err != nil {
			return nil, nil, false, errorcode.InternalErrorCode.NewWithErr(err)
		}
		if len(optionsJson) == 0 {
			continue
		}
		systemConnectors[&DecodedSystemConnector{
			Connector:   conn,
			optionsJson: optionsJson,
		}] = false
	}

	mergeConnectorOptions := func(options []*Option) map[string]string {
		if len(options) == 0 {
			return connectorOptions
		}
		merged := make(map[string]string, len(connectors))
		for k, v := range connectorOptions {
			merged[k] = v
		}
		for _, opt := range options {
			merged[opt.Key] = opt.Value
		}
		return merged
	}

	resourceLocsMap := make(map[*sql_model.ResourceLocationIntegral]struct{})
	sysConnectors := make([]*table.SystemConnector, 0)
	match := func(options map[string]string) (*ConnectorOption, bool, error) {
		for conn, accessed := range systemConnectors {
			matched, option, _ := matchOptions(options, conn.optionsJson)
			if !matched {
				continue
			}
			if accessed {
				return option, true, nil
			}

			systemConnectors[conn] = true
			if locs, err := getConnectorResourceLocs(options, conn.Connector); err != nil {
				return nil, false, err
			} else {
				sysConnectors = append(sysConnectors, conn.Connector)
				for _, loc := range locs {
					resourceLocsMap[loc] = struct{}{}
				}
			}

			return option, false, nil
		}
		return nil, false, errorcode.InternalErrorCode.ReplaceDesc(
			fmt.Sprintf("Failed to find connector with option %v.", options))
	}

	// dfs algorithm to get all refer connectors and deal with cycle reference.
	var getAllReferConnectors func(options map[string]string) bool
	getAllReferConnectors = func(options map[string]string) bool {
		option, accessed, err := match(options)
		if err != nil {
			logger.Errorf("match res err : %s", err)
			msg := fmt.Sprintf("Failed to find connector with option %v", options)
			logger.Error(msg)
			return false
		}
		if accessed || len(option.ReferConnectors) == 0 {
			return true
		}

		for _, connOptions := range option.ReferConnectors {
			if matched := getAllReferConnectors(mergeConnectorOptions(connOptions)); !matched {
				return false
			}
		}
		return true
	}

	if matched := getAllReferConnectors(connectorOptions); !matched {
		return nil, systemConnector, true, nil
	}
	if len(sysConnectors) > 0 {
		systemConnector = sysConnectors[0]
	}
	resourceLocs := make([]*sql_model.ResourceLocationIntegral, 0, 0)
	for loc, _ := range resourceLocsMap {
		resourceLocs = append(resourceLocs, loc)
	}
	return resourceLocs, systemConnector, false, nil
}

func getConnectorResourceLocs(connectorOptions map[string]string, systemConnector *table.SystemConnector) (
	resourceLocs []*sql_model.ResourceLocationIntegral, err error) {
	/**
	 特殊处理
	 1. filesystem connector
	  SystemConnector中的配置
	  ResourceId: connector-cos
	  Options:
	   [{
		 "Options": [{
			"Key": "connector/connector.type",
			"Value": "filesystem"
		  }]
	   }]
	   根据path判断是否需要关联connector-cos：
	      path配置的是hdfs路径，无需使用connector
	      path配置的是cos路径，使用connector-cos
	*/
	connector, ok := connectorOptions["connector"]
	if !ok {
		connector = connectorOptions["connector.type"]
	}
	if strings.TrimSpace(connector) == "filesystem" {
		path, _ := connectorOptions["path"]
		if !strings.HasPrefix(path, "cosn") { // 不是cosn目录，不引入connector-cos
			resourceLocs = append(resourceLocs, nullResource)
			return resourceLocs, nil
		}
	}

	// common ref rule
	if systemConnector.VersionId != constants.METADATA_TABLE_RESOURCE_REF_NO_NEED_REFERENCE_VERSION { // 需要引入Connector
		resourceLocs, err = GetResourceLocByResourceSerialIdId(systemConnector.ResourceId, systemConnector.VersionId)
		if err != nil {
			return nil, err
		}
		return resourceLocs, nil
	} else {
		resourceLocs = append(resourceLocs, nullResource)
		return resourceLocs, nil
	}
}

/*
*
ES7的配置如下：只要有一个`Options`匹配上即可

	 [{
	    "Options": [{
			"Key": "connector/connector.type",
			"Value": "elasticsearch"
			},
			{
			"Key": "version/connector.version",
			"Value": "7"
			}]
	 },
	 {
	    "Options": [{
			"Key": "connector/connector.type",
			"Value": "elasticsearch-7"
			}
		]}
	 ]
*/
func matchOptions(connectorOptions map[string]string, optionsJson []*ConnectorOption) (
	matched bool, matchOption *ConnectorOption, err error) {
	defer func() {
		if errs := recover(); err != nil {
			logger.Errorf(" Match options %s panic, errors:%+v", connectorOptions, errs)
			//err = errors.New(fmt.Sprintf("%+v", errs))
			matched = false
		}
	}()
	if len(optionsJson) == 0 {
		return false, nil, nil
	}
	for _, configuredOption := range optionsJson {
	OuterLoop:
		for _, option := range configuredOption.Options {
			keys := strings.Split(option.Key, "/")
			var findKey = false
			for _, key := range keys {
				value, ok := connectorOptions[key]
				if ok {
					findKey = true
					matched = value == option.Value
				}
			}
			if !matched || !findKey { // option mismatched
				matched = false
				break OuterLoop
			}
		}
		if matched { // options matched, skip
			return true, configuredOption, nil
		}
	}
	return false, nil, nil
}

type Option struct {
	Key   string
	Value string
}

type ConnectorOption struct {
	Options         []*Option
	ReferConnectors [][]*Option
}

func GetResourceLocByResourceSerialIdId(resourceId string, versionId int64) ([]*sql_model.ResourceLocationIntegral, error) {
	locs := make([]*sql_model.ResourceLocationIntegral, 0)

	resourceRefItem := &model.ResourceRefItem{
		ResourceId: resourceId,
		Version:    versionId,
		Type:       constants.RESOURCE_REF_USAGE_TYPE_DEFALUT,
	}
	logger.Debugf("CheckSqlGrammar-resourceRefItem2: %#v", resourceRefItem)
	resourceConfig, err := GetResourceConfigByResourceRefItem(resourceRefItem)
	if err != nil {
		return nil, err
	}
	logger.Debugf("CheckSqlGrammar-resourceConfig2: %#v", resourceConfig)
	refs, err := UpdateRefs([]*model.ResourceRefItem{{
		ResourceId: resourceId,
		Version:    resourceConfig.VersionId,
		Type:       constants.RESOURCE_REF_USAGE_TYPE_DEFALUT,
	}})
	if err != nil {
		return nil, err
	}
	for _, ref := range refs {
		resourceConfig, err := GetResourceConfigByResourceRefItem(ref)
		if err != nil {
			return nil, err
		}
		loc := &model3.ResourceLocationV2{ResourceType: constants.RESOURCE_TYPE_JAR}
		err = json.Unmarshal([]byte(resourceConfig.ResourceLoc), loc)
		if err != nil {
			return nil, err
		}
		locs = append(locs, &sql_model.ResourceLocationIntegral{
			ResourceSerialId:   resourceId,
			ResourceId:         resourceConfig.ResourceId,
			VersionId:          resourceConfig.VersionId,
			ResourceLocationV2: *loc,
		})
	}
	return locs, nil
}

func GetResourceLocByResourceId(resourceId int64, versionId int64) ([]*sql_model.ResourceLocationIntegral, error) {
	resource, err := GetResourceById(resourceId)
	if err != nil {
		return nil, err
	}
	resourceConfig, err := GetResourceConfigByResourceIdV2(resourceId, versionId)
	if err != nil {
		return nil, err
	}
	locs := make([]*sql_model.ResourceLocationIntegral, 0)
	resourceRefItem := &model.ResourceRefItem{
		ResourceId: resource.ResourceId,
		Version:    resourceConfig.VersionId,
		Type:       constants.RESOURCE_REF_USAGE_TYPE_DEFALUT,
	}
	refs, err := UpdateRefs([]*model.ResourceRefItem{resourceRefItem})
	if err != nil {
		return nil, err
	}

	for _, ref := range refs {
		resourceRefItem := &model.ResourceRefItem{
			ResourceId: ref.ResourceId,
			Version:    versionId,
			Type:       constants.RESOURCE_REF_USAGE_TYPE_DEFALUT,
		}

		resourceConfig, err := GetResourceConfigByResourceRefItem(resourceRefItem)
		if err != nil {
			return nil, err
		}

		loc := &model3.ResourceLocationV2{}
		err = json.Unmarshal([]byte(resourceConfig.ResourceLoc), loc)
		loc.ResourceType = resource.ResourceType
		if err != nil {
			return nil, err
		}
		locs = append(locs, &sql_model.ResourceLocationIntegral{
			ResourceId:         resourceId,
			ResourceSerialId:   resource.ResourceId,
			VersionId:          resourceConfig.VersionId,
			ResourceLocationV2: *loc,
		})
	}
	return locs, nil
}

func GetSystemConnectorFlinkVersions() (flinkVersions []string, err error) {
	sql := "SELECT DISTINCT FlinkVersion  FROM SystemConnector "
	condition := dao.NewCondition()
	condition.Ne("Status", constants.RESOURCE_STATUS_DELETE)
	condition.Ne("VersionId", -2)
	where, args := condition.GetWhere()
	sql = sql + where + " ORDER BY FlinkVersion ASC"
	_, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return flinkVersions, err
	}
	if len(data) == 0 {
		return flinkVersions, errors.New("Failed to find flink version. ")
	}
	flinkVersions = make([]string, 0)
	for i := 0; i < len(data); i++ {
		flinkVersions = append(flinkVersions, string(data[i]["FlinkVersion"]))
	}
	return flinkVersions, nil
}

func GetSystemConnectors(flinkVersion string) (innerConnectors []string, err error) {
	connectors := make([]string, 0)
	connset := make(map[string]bool)
	sql := "SELECT * FROM SystemConnector"
	condition := dao.NewCondition()
	condition.Ne("Status", constants.RESOURCE_STATUS_DELETE)
	condition.Ne("VersionId", -2)
	condition.Eq("FlinkVersion", flinkVersion)

	where, args := condition.GetWhere()
	sql = sql + where

	_, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return nil, err
	}
	for i := 0; i < len(data); i++ {
		systemConnector := &table.SystemConnector{}
		err = util.ScanMapIntoStruct(systemConnector, data[i])
		if err != nil {
			logger.Errorf("Failed to scan map into struct  SystemConnector, errors:%+v", err)
		} else {
			optionStr := systemConnector.Options
			optionsJson := make([]*ConnectorOption, 0)
			err = json.Unmarshal([]byte(optionStr), &optionsJson)
			if err != nil {
				logger.Errorf("Failed to unmarshal optionStr, errors:%+v", optionStr, err)
			} else {
				for _, configuredOption := range optionsJson {
					for _, option := range configuredOption.Options {
						keys := strings.Split(option.Key, "/")
						for _, key := range keys {
							if key == "connector" || key == "connector.type" {
								if _, ok := connset[option.Value]; !ok {
									// filesystem也是系统Connector
									if option.Value != "filesystem" {
										connectors = append(connectors, option.Value)
									}
								}
								connset[option.Value] = true
							}
						}
					}
				}
			}
		}
	}
	return connectors, nil
}

func TranslateSqlToCos(job *table1.Job, content string, fileSuffix string) (error, *model.ResourceConfigItem) {
	// 上传code 到oceanus 的 cos上
	cluster, err := service6.GetClusterByClusterId(job.ClusterId)
	if err != nil {
		logger.Errorf("Failed to GetClusterByClusterId %d error:%+v", job.ClusterId, err)
		return err, nil
	}
	if !strings.Contains(cluster.SupportedFeatures, constants.FeatureSupportSqlToCosFile) {
		logger.Warningf("not support translateSqlToCos, not contain %+v", constants.FeatureSupportSqlToCosFile)
		return nil, nil
	}
	oceanusSecretId, oceanusSecretKey, err := common.GetSecretIdAndKeyOfCos()
	if err != nil {
		logger.Errorf("UploadJarToUserCos Failed to Get Oceanus SecretIdAndKeyOfCos,err: %+v", err)
		return errorcode.NewStackError(errorcode.InternalErrorCode, "uploadJarToUserCos Failed to get Oceanus GetSecretIdAndKeyOfCos", err), nil
	}
	bucketReq := model1.GetResourceBucketReq{0, "", job.Region, "", ""}
	region, bucketName, err := service_bucket.GetResourceBucketName(&bucketReq)

	client := cos_service.NewCosClient(oceanusSecretId, oceanusSecretKey, "", region, bucketName)
	timeStr := util.GetCurrentTimeStr()
	strUUidSql := strings.ReplaceAll(uuid.New(), "-", "") + "." + fileSuffix
	targetPath := fmt.Sprintf("%d/%s/%s/%s/%s/%s",
		job.AppId, job.CreatorUin, job.SerialId, timeStr, fileSuffix, strUUidSql)
	err = client.PutForCos(targetPath, content)
	if err != nil {
		logger.Errorf("UploadSqlFileToOceanusCosFailed, err: %+v", err)
		return err, nil
	}
	resourceLoc := &model3.ResourceLocation{StorageType: constants.STORAGE_TYPE_COS}

	param := &model3.ParamType{
		Bucket: bucketName,
		Path:   targetPath,
		Region: region,
	}
	resourceLoc.Param = param
	createResourceReq := &model5.CreateResourceReq{
		RequestBase: apiv3.RequestBase{
			AppId:         int64(job.AppId),
			SubAccountUin: job.CreatorUin,
			Uin:           job.OwnerUin,
			Region:        region,
		},
		ResourceType: constants.RESOURCE_REF_USAGE_TYPE_DEPENDENCY,
		FolderId:     "root",
		ResourceLoc:  resourceLoc,
		Name:         filepath.Base(targetPath),
	}
	id, resourceId, _, versionId, err := SaveNewResource(createResourceReq, job.ItemSpaceId)
	if err != nil {
		logger.Errorf("BuildResourceLoc: failed to save new resource error: %+v", err)
		return err, nil
	}
	resourceConfigItem := &model.ResourceConfigItem{
		Id:           id,
		CreatorUin:   job.CreatorUin,
		VersionId:    versionId,
		ResourceName: filepath.Base(targetPath),
		ResourceId:   resourceId,
		ResourceLoc:  resourceLoc,
		Type:         constants.RESOURCE_REF_USAGE_TYPE_DEPENDENCY,
	}
	return nil, resourceConfigItem
}

func SaveSqlCheckRef(sqlCheckRefTable *table2.SqlCheckRef) (err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Add SqlCheckRef to db panic, for sqlCheckRefTable: %+v, errors:%+v", sqlCheckRefTable, errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	txManager := service.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.SaveObject(sqlCheckRefTable, "SqlCheckRef")
		return nil
	}).Close()
	return nil
}
