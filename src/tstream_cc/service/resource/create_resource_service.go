package service

import (
	"fmt"
	"path"
	"time"
	"unicode"

	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/resource"
	model2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/resource_config"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/resource"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/quota"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource_config"
)

func DoCreateResource(req *model.CreateResourceReq) (rsp *model.CreateResourceRsp, err error) {

	if req == nil {
		return nil, errorcode.InternalErrorCode.New()
	}

	if req.Name == "" {
		req.Name = path.Base(req.ResourceLoc.Param.Path)
	}

	//检查Region合法性
	if !IsLegitimate(req.ResourceLoc.Param.Region) {
		return nil, errorcode.InvalidParameterValue_Legitimate.ReplaceDesc("Region is not legitimate")
	}

	//检查Bucket合法性
	if !IsLegitimate(req.ResourceLoc.Param.Bucket) {
		return nil, errorcode.InvalidParameterValue_Legitimate.ReplaceDesc("Bucket is not legitimate")
	}

	if req.FolderId == "" {
		req.FolderId = constants.FOLDER_ROOT_ID
	}

	logger.Infof("ResourceLoc.Param: %+v", req.ResourceLoc.Param)

	// 鉴权
	itemSpcId, err2 := auth.InnerCreateAuth(req.WorkSpaceId, req.AppId, req.Region)
	if err2 != nil {
		logger.Errorf("%s: User  uin %s is NOT authenticated, refuse to serve", req.RequestId, req.Uin)
		return nil, err2
	}

	resourceTotalCount, err := GetResourceTotalCount(req.RequestId, req.AppId, req.Region, nil, nil, nil, nil, nil, constants.USER_PROVIDED, nil)
	if err != nil {
		return nil, err
	}

	overLimit, msg, err := quota.NewQuota().OverLimit("", int64(req.AppId), quota.Resource, int32(resourceTotalCount))
	if err != nil {
		return nil, err
	}
	if overLimit {
		errMsg := fmt.Sprintf("resource %s", msg)
		return nil, errorcode.LimitExceededCode.ReplaceDesc(errMsg)
	}
	_, resourceId, _, maxVersion, err := SaveNewResource(req, itemSpcId)
	if err != nil {
		// FIXME, 搞个 返回码的ERROR interface， 在最底层产生的error， 都先定义后
		// 这样最外层的controller，可以正确捕获到 真正的错误
		return nil, err
	}
	return &model.CreateResourceRsp{ResourceId: resourceId, Version: maxVersion, RequestId: req.RequestId}, nil
}

func SaveNewResource(req *model.CreateResourceReq, itemSpaceId int64) (Id int64, resourceId string, configId, maxVersion int64, err error) {
	defer errorcode.DefaultDeferHandler(&err)
	resource := buildResource4Req(req, itemSpaceId)
	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		resourceId := tx.SaveObject(resource, "Resource")
		resource.Id = resourceId
		resourceConfigReq := buildCreateResourceConfigReq(req, resource.ResourceId)
		configId, maxVersion, err = service2.SaveNewResourceConfig(resourceConfigReq, resource)
		return err
	}).Close()
	return resource.Id, resource.ResourceId, configId, maxVersion, err
}

func buildResource4Req(req *model.CreateResourceReq, itemSpaceId int64) *table.Resource {
	resource := &table.Resource{}
	resource.AppId = req.AppId

	resource.CreatorUin = req.SubAccountUin
	resource.OwnerUin = req.Uin
	resource.CreateTime = time.Now().Format("2006-01-02 15:04:05")
	resource.Remark = req.Remark
	cidUtil := &util.CidUtil{}
	resourceSerialId := cidUtil.EncodeId(util.GetNowTimestamp(), "resource", "resource", util.GetNowTimestamp(), 8)
	resource.ResourceId = resourceSerialId
	resource.ResourceFrom = req.ResourceFrom
	resource.UpdateTime = resource.CreateTime
	resource.ResourceType = req.ResourceType
	resource.ResourceName = req.Name
	resource.SystemProvide = req.SystemProvide
	resource.Status = constants.RESOURCE_STATUS_ACTIVE
	resource.Region = req.Region
	resource.FolderId = req.FolderId
	resource.FileName = req.Name
	resource.ItemSpaceId = itemSpaceId
	return resource
}

func buildCreateResourceConfigReq(req *model.CreateResourceReq, ResourceId string) *model2.CreateResourceConfigReq {
	createResourceConfigReq := &model2.CreateResourceConfigReq{}
	createResourceConfigReq.AppId = req.AppId
	createResourceConfigReq.Uin = req.Uin
	createResourceConfigReq.SubAccountUin = req.SubAccountUin
	createResourceConfigReq.Region = req.Region
	createResourceConfigReq.RequestId = req.RequestId
	createResourceConfigReq.Version = req.Version
	createResourceConfigReq.ResourceId = ResourceId
	createResourceConfigReq.ResourceLoc = req.ResourceLoc
	createResourceConfigReq.Remark = req.ResourceConfigRemark
	return createResourceConfigReq
}

/**
 * 检查资源名称是否同名
 */
func CheckResourceName(appId int64, name string, resourceType int) (exits bool, err error) {
	args := make([]interface{}, 0, 0)
	args = append(args, appId)
	args = append(args, name)
	args = append(args, constants.RESOURCE_STATUS_ACTIVE)
	args = append(args, resourceType)

	count, _, err := service.GetTxManager().GetQueryTemplate().DoQuery(
		"SELECT * FROM Resource WHERE AppId = ? AND ResourceName=? AND Status=? AND ResourceType=? ", args)
	if err != nil {
		return exits, err
	}
	// 名字已经存在
	if count != 0 {
		exits = true
	}
	return
}

// 检查bucket和region的合法性
func IsLegitimate(str string) bool {
	for _, x := range []rune(str) {
		if !unicode.IsDigit(x) && !unicode.IsLetter(x) && x != '-' {
			return false
		}
	}
	return true
}
