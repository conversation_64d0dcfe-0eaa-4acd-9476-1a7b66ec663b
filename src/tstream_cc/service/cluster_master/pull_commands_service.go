package cluster_master

import (
	"encoding/base64"
	"errors"
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	table1 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster_master"
	service4 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	tableService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
)

/**
 *
 * <AUTHOR>
 * @time 2019/4/28
 * @version 0.4.0
 * @since 0.4.0
 * @see
 */
func ListCommands(clusterGroupId int64, batchSize int8, status int8) (commands []*cluster_master.Command, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("List commands panic, with errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()

	txManager := service.GetTxManager()
	if txManager == nil {
		logger.Errorf("TxManager is nil")
		return []*cluster_master.Command{}, errors.New("TxManager is nil")
	}

	clusterList, err := service4.ListClusters(clusterGroupId)
	if err != nil || len(clusterList) == 0 {
		logger.Errorf("Failed to query Clusters from db, with errors:%+v", err)
		return []*cluster_master.Command{}, err
	}

	cluster := clusterList[0]

	clusterIds := make([]int64, 0)

	sqlCluster := "SELECT DISTINCT t1.ClusterId AS Id FROM Tke t1 JOIN Tke t2 ON t1.InstanceId = t2.InstanceId WHERE t2.ClusterId = ? AND t1.Status = 1 "
	argsCluster := make([]interface{}, 0)
	argsCluster = append(argsCluster, cluster.Id)

	_, dataClusters, err := txManager.GetQueryTemplate().DoQuery(sqlCluster, argsCluster)
	if err != nil {
		logger.Error("Failed to query with sql:", sqlCluster, ", errors:", err.Error())
		return []*cluster_master.Command{}, err
	}
	for _, ref := range dataClusters {
		clusterItem := &table1.Cluster{}
		err = util.ScanMapIntoStruct(clusterItem, ref)
		if err != nil {
			continue
		}
		clusterIds = append(clusterIds, clusterItem.Id)
	}

	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		sql := "SELECT * FROM Command WHERE  "
		args := make([]interface{}, 0)
		clusterIds := UniqueSliceInt64(clusterIds)

		if len(clusterIds) == 0 {
			logger.Errorf("Failed to get Command by clusterIds, clusterIds is empty" )
			return nil
		}
		sql += "  ClusterId IN("
		for i := 1; i < len(clusterIds); i++ {
			sql += "?,"
			args = append(args, clusterIds[i])
		}
		sql += "?)"
		args = append(args, clusterIds[0])

		sql += " and status=? ORDER BY createTime DESC LIMIT ?"
		args = append(args, status)
		args = append(args, batchSize)

		logger.Infof("ListCommands sql: %s, args: %+v", sql, args)
		_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
		if err != nil {
			logger.Errorf("Failed to get commands, with errors:%+v", err)
			return err
		}
		commands = make([]*cluster_master.Command, 0)
		for i := 0; i < len(data); i++ {
			command := &cluster_master.Command{}
			err = util.ScanMapIntoStruct(command, data[i])
			if err != nil {
				logger.Errorf("Failed to convert bytes into Command, with errors:%+v", err)
				return err
			}
			result := tx.ExecuteSqlWithArgs("UPDATE Command SET Status=?, FetchTime=? WHERE Id=? AND Status=?",
				constants.CLUSTER_MASTER_COMMAND_FETCH, util.GetCurrentTime(), command.Id, constants.CLUSTER_MASTER_COMMAND_CREATE)
			rowsAffected, err := result.RowsAffected()
			if err != nil {
				return err
			} else if rowsAffected != 1 {
				msg := fmt.Sprintf("Failed to Update Command with Id: %d, rowsAffected=%d", command.Id, rowsAffected)
				logger.Error(msg)
				return errors.New(msg)
			}
			//云api 不支持字符串为json格式，需要转化为base64传递
			archGeneration, err := tableService.GetTableService().GetTkeArchGeneration(command.ClusterId)
			if archGeneration >= constants.TKE_ARCH_GENERATION_V3 {
				base64Params := base64.StdEncoding.EncodeToString([]byte(command.Params))
				command.Params = base64Params
			}
			commands = append(commands, command)
		}
		return nil
	}).Close()

	return commands, err
}

func UniqueSliceInt64(slice []int64) []int64 {
	if len(slice) == 0 {
		return slice
	}

	m := make(map[int64]struct{}, len(slice))
	for _, s := range slice {
		m[s] = struct{}{}
	}
	slice = make([]int64, 0, len(m))
	for s, _ := range m {
		slice = append(slice, s)
	}
	return slice
}
