package cluster_master

import (
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/auth"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job_config"
	"time"

	barad2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/barad"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/barad"
	service5 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/bucket"
	jobService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/job_autoscale"

	"tencentcloud.com/tstream_galileo/src/common/dao"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/log"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cluster_master_protocol"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster_master"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	clusterService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config"
	_ "tencentcloud.com/tstream_galileo/src/tstream_cc/service/configuration_center"
	service4 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/log"
)

/**
 *
 * <AUTHOR>
 * @time 2019/4/28
 * @version 0.4.0
 * @since 0.4.0
 * @see
 */
// todo check rowsAffected everywhere
// 代码比较Ugly，下个迭代重构
func FinishCommands(cmdResults []*cluster_master_protocol.CommandResult) (err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Finish commands panic, with errors: %+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()

	txManager := service.GetTxManager()
	if txManager == nil {
		return errors.New("TxManager is nil")
	}
	jobList := make([]*table.Job, 0, 0)
	stoppedJobList := make([]*table.Job, 0)
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		for i := 0; i < len(cmdResults); i++ {
			cmdResult := cmdResults[i]
			command, err := GetCommandById(cmdResult.CommandId)
			if err != nil {
				logger.Warningf("%s: Failed to GetCommandById because %+v", cmdResult.RequestId, err)
				return err
			}
			retMap := cmdResult.Result
			jobId, err := strconv.ParseInt(retMap["jobId"], 10, 64)
			if err != nil {
				logger.Warningf("%s: Failed parse int of jobId because %+v", cmdResult.RequestId, err)
				return err
			}
			if command.JobId != jobId {
				return errors.New(fmt.Sprintf("%s: logic error: JobId not matched when finish command, commandJobId: %d, resultJobId: %d", cmdResult.RequestId, command.JobId, jobId))
			}

			jobRuntimeId, err := strconv.ParseInt(retMap["jobRuntimeId"], 10, 64)
			if err != nil {
				logger.Warningf("%s: Failed parse int of jobRuntimeId because %+v", cmdResult.RequestId, err)
				return err
			}
			logger.Debugf("%s: Finish command, jobRuntimeId: %d would be updated", cmdResult.RequestId, jobRuntimeId)
			action, err := strconv.Atoi(retMap["action"])
			if command.Action != int8(action) {
				return errors.New(fmt.Sprintf("%s: logic error: Action not matched when finish command, commandAction: %d, resultAction: %d", cmdResult.RequestId, command.Action, action))
			}
			logger.Debugf("%s: Finish command, action: %d", cmdResult.RequestId, action)
			listJobQuery := model.ListJobQuery{
				AppId:        0,
				JobIds:       []int64{jobId},
				IsVagueNames: false,
				Offset:       0,
				Limit:        0,
			}
			jobs, err := service2.ListJobs(&listJobQuery)
			if err != nil {
				logger.Errorf("%s: Failed to ListJobs, error: %+v", cmdResult.RequestId, err)
				return err
			} else if len(jobs) == 0 {
				errMsg := "Failed to ListJobs, jobs.size=0"
				logger.Error(cmdResult.RequestId + ": " + errMsg)
				return err
			} else if len(jobs) > 1 {
				errMsg := fmt.Sprintf("%s: Failed to ListJobs, jobs.size != 1, actually is %d", cmdResult.RequestId, len(jobs))
				logger.Error(errMsg)
				return err
			}

			succeed, err := strconv.ParseBool(retMap["succeed"])
			if err != nil {
				logger.Warningf("%s: Failed parse bool of `succeed` because %+v", cmdResult.RequestId, err)
				return err
			}
			status, err := strconv.Atoi(retMap["status"])
			if err != nil {
				logger.Warningf("%s: Failed Atoi of `status` because %+v", cmdResult.RequestId, err)
				return err
			}
			clusterGroup, err := service5.ListClusterGroupById(command.ClusterGroupId)
			if err != nil {
				logger.Errorf("%s: Failed to ListClusterGroupById, error: %+v", command.ClusterGroupId, err)
				return err
			}
			isSharedClusterGroup := false
			if clusterGroup.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && clusterGroup.AgentSerialId != "" {
				isSharedClusterGroup = true
			}
			if !succeed { // 提交失败时的处理流程
				if status == constants.JOB_STATUS_CREATE { // 如果原来的状态是未初始化, 发布失败后应该回退到未发布状态
					status = constants.JOB_STATUS_INITIALIZED
				}
				// fixme：并非所有的失败流程都是启动失败，这里需要调整
				errorMsg := retMap["errorMsg"]
				logger.Debugf("[%s] Job flow failed, action: %d, because: %s", command.JobSerialId, action, errorMsg)

				// 获取 K8S 的事件分析作业的异常原因
				messageList := AnalyzeK8sEventReturnMessage(&command.JobSerialId, &command.Region, &errorMsg)
				messageBytes, err := json.Marshal(messageList)
				if err != nil {
					logger.Errorf("AnalyzeK8sEventReturnMessage error, because: %+v", err)
					errorMsg = "[\"The job failed to start, and an error occurred while obtaining the cause.\"]"
				}

				errorMsg = string(messageBytes)

				// FIXME: 需要分命令处理异常, 而不是以最终状态来判断, 这样不准确

				if status == constants.JOB_STATUS_RUNNING { // 如果提交失败, 回退的状态是运行中 RUNNING, 则说明原来的没有动（极小的可能是新作业的启动成功但是流程失败, 这种情况目前没有遇到过）, 那么其他参数先不更新
					sql := "UPDATE Job SET Status=?, LastOpResult=? WHERE Id=?" // FIXME: 其他参数, 例如 TmRunningCuNum, JmRunningCuNum, StopTime, TotalRunMillis 等如何设置?
					tx.ExecuteSqlWithArgs(sql, status, errorMsg, jobId)         // 追加结果校验
				} else { // 提交失败, 且作业已经不再运行, 需要改为 PAUSED, STOPPED 或者 INITIALIZED
					sql := "UPDATE Job SET Status=?, LastOpResult=?, TmRunningCuNum=0, JmRunningCuNum=0,RunningCpu=0,RunningMem=0 WHERE Id=?" // 作业停止后需要把 RunningCuNum 设置为 0
					tx.ExecuteSqlWithArgs(sql, status, errorMsg, jobId)
					// 提交失败，作业非运行状态，清理所有资源
					eInfo := fmt.Sprintf("***** jobSerialId %s to status %d failed", jobs[0].SerialId, status)
					err1 := service4.CleanHistoryJobData(jobs[0], eInfo, tx)
					if err1 != nil {
						logger.Warningf("%s: CleanHistoryJobData error: %+v", eInfo, err1)
					}
					sql = "UPDATE JobInstance SET Status=?, StopTime=? WHERE Id=? "
					tx.ExecuteSqlWithArgs(sql, constants.JOB_INSTANCE_STATUS_HISTORY, util.GetCurrentTime(), jobRuntimeId)
					if status == constants.JOB_STATUS_STOPPED {
						stoppedJobList = append(stoppedJobList, jobs[0])
					}
				}

				// TODO 运行时间要处理，临时 JobRuntimeId 也要处理
				oldJobRuntimeId, err := strconv.ParseInt(retMap["oldJobRuntimeId"], 10, 64)
				if err != nil { // 如果 retMap 不包含 oldJobRuntimeId, 则有两种可能
					// 如果是暂停或者停止作业, 则是正常的, 取 jobRuntimeId 即可, 按照正常结束流程 (TODO: 消除重复代码)
					if action == constants.CLUSTER_MASTER_COMMAND_ACTION_CANCEL_JOB_WITH_SAVEPOINT || action == constants.CLUSTER_MASTER_COMMAND_ACTION_CANCEL_JOB {
						curRuntime, err := service2.GetCurrentRunMillis(jobs[0].StartTime)
						if err != nil {
							logger.Errorf("%s: Failed to GetCurrentRunMillis for job, error: %+v", cmdResult.RequestId, err)
							return err
						}

						if status == constants.JOB_STATUS_RUNNING {
							sql := "UPDATE Job SET Status=? WHERE Id=?"
							tx.ExecuteSqlWithArgs(sql, status, jobId)

							sql = "UPDATE JobInstance SET Status=? WHERE Id=? "
							tx.ExecuteSqlWithArgs(sql, constants.JOB_INSTANCE_STATUS_RUNNING, jobRuntimeId)
						} else {
							sql := "UPDATE Job SET Status=?, TotalRunMillis=?, TmRunningCuNum=0, JmRunningCuNum=0,RunningCpu=0,RunningMem=0 WHERE Id=?" // 作业停止后需要把 RunningCuNum 设置为 0
							tx.ExecuteSqlWithArgs(sql, status, jobs[0].TotalRunMillis+curRuntime, jobId)

							sql = "UPDATE JobInstance SET Status=?, StopTime=? WHERE Id=? "
							tx.ExecuteSqlWithArgs(sql, constants.JOB_INSTANCE_STATUS_HISTORY, util.GetCurrentTime(), jobRuntimeId)
						}
					} else { // 否则是启动, 重启作业, 此时必须包含 oldJobRuntimeId, 没有就报错
						logger.Warningf("%s: Logic error? Failed to get oldJobRuntimeId, err: %d", cmdResult.RequestId, oldJobRuntimeId)
					}
				} else {
					logger.Debugf("%s: Start to revert back JobInstance status, newJobInstanceId: %d <-> oldJobInstanceId: %d", cmdResult.RequestId, jobRuntimeId, oldJobRuntimeId)
					deleteNewJobRuntimeIdSql := "UPDATE JobInstance SET status=? WHERE id=? AND status=?"
					tx.ExecuteSqlWithArgs(deleteNewJobRuntimeIdSql, constants.JOB_INSTANCE_STATUS_HISTORY, jobRuntimeId, constants.JOB_INSTANCE_STATUS_CREATE)
					if status == constants.JOB_STATUS_RUNNING { // Only update job instance status when regress status is RUNNING
						recoverOldJobRuntimeIdSql := "UPDATE JobInstance SET status=? WHERE id=? AND status=?"
						tx.ExecuteSqlWithArgs(recoverOldJobRuntimeIdSql, constants.JOB_INSTANCE_STATUS_RUNNING, oldJobRuntimeId, constants.JOB_INSTANCE_STATUS_HISTORY)
					}
					var rollbackPublishJobConfigIdSql string
					if jobs[0].LastPublishedJobConfigId != -1 {
						rollbackPublishJobConfigIdSql = "UPDATE Job set PublishedJobConfigId = LastPublishedJobConfigId where id = ?"
					} else {
						rollbackPublishJobConfigIdSql = "UPDATE Job set LastPublishedJobConfigId = PublishedJobConfigId where id = ?"
					}
					tx.ExecuteSqlWithArgs(rollbackPublishJobConfigIdSql, jobs[0].Id)
				}

				// 对于停止作业的流程，结束流程时判断是否有操作中的快照，如果有则将其设为已失败
				if (action == constants.CLUSTER_MASTER_COMMAND_ACTION_CANCEL_JOB || action == constants.CLUSTER_MASTER_COMMAND_ACTION_CANCEL_JOB_WITH_SAVEPOINT) && !isSharedClusterGroup {
					err = service.UpdateInProgressSavepointWhenCommandFinished(jobId, command.JobSerialId, tx, cmdResult.RequestId)
					if err != nil {
						logger.Errorf("%s: Logic error? Failed to find in-progress savepoint record for job %d", cmdResult.RequestId, jobId)
					}
				}
			} else if action == constants.CLUSTER_MASTER_COMMAND_ACTION_RUN_JOB { // 作业成功运行
				flinkJobId, applicationId, flinkJobPlan, runningCuNumStr := retMap["flinkJobId"], retMap["applicationId"], retMap["flinkJobPlan"], retMap["runningCuNum"]
				// 更新jobInstance 状态, 获取 Flink JobID 和 YARN ApplicationID
				runningCuNum, err := strconv.Atoi(runningCuNumStr)
				if err != nil {
					logger.Warningf("%s: runningCuNum is not a number: %+v", cmdResult.RequestId, err)
					return errors.New("runningCuNum is not a number: " + runningCuNumStr)
				}

				runningCuNum, err = CalcTmNum(runningCuNum, jobRuntimeId)
				if err != nil {
					logger.Errorf("%s: calc runningCuNum err: %+v", cmdResult.RequestId, err)
					return err
				}

				// 更新 job 状态
				sql := "UPDATE Job SET Status=?, StartTime=?, TmRunningCuNum=?, JmRunningCuNum=? WHERE Id=?"
				tx.ExecuteSqlWithArgs(sql, status, util.GetCurrentTime(), runningCuNum, constants.DEFAULT_JM_RUNNING_CU, jobId)
				sql = "UPDATE JobInstance SET Status=?, StartTime=?, FlinkJobId=?, ApplicationId=?, FlinkJobPlan=?, TmRunningCuNum=?, JmRunningCuNum = ? WHERE Id=?" // 注意设置的是 RunningCuNum
				tx.ExecuteSqlWithArgs(sql, constants.JOB_INSTANCE_STATUS_RUNNING, util.GetCurrentTime(), flinkJobId, applicationId, flinkJobPlan, runningCuNum, constants.DEFAULT_JM_RUNNING_CU, jobRuntimeId)

				// jar作业设置了并行度就在这里更新他们所有位置的并行度
				if jobs[0].Type == constants.JOB_TYPE_JAR && auth.IsInWhiteList(int64(jobs[0].AppId), constants.WHITE_LIST_SCALE_VERTEX_PARALLELISM) {
					err = updateJobPlanMaxParallelism(flinkJobPlan, tx, jobs[0])
					if err != nil {
						logger.Errorf("%s: Failed to Update parallelism for job, error: %+v", cmdResult.RequestId, err)
						return err
					}
				}
				logger.Debugf("%s: Finished finish job job %d", cmdResult.RequestId, jobId)
				// 异步添加WebUI路由规则
				jobService.NewJobWebUIRouterManager().AddJobWebUIRouterAsync(jobRuntimeId, jobs[0], clusterGroup)

			} else if action == constants.CLUSTER_MASTER_COMMAND_ACTION_RESTART_JOB { // 作业成功重启
				flinkJobId, applicationId, flinkJobPlan, runningCuNumStr := retMap["flinkJobId"], retMap["applicationId"], retMap["flinkJobPlan"], retMap["runningCuNum"]
				runningCuNum, err := strconv.Atoi(runningCuNumStr)
				if err != nil {
					logger.Warningf("%s: runningCuNum is not a number: %+v", cmdResult.RequestId, err)
					return errors.New("runningCuNum is not a number: " + runningCuNumStr)
				}

				runningCuNum, err = CalcTmNum(runningCuNum, jobRuntimeId)
				if err != nil {
					logger.Errorf("%s: calc runningCuNum err: %+v", cmdResult.RequestId, err)
					return err
				}

				// 更新 job 状态及总运行时间
				curRuntime, err := service2.GetCurrentRunMillis(jobs[0].StartTime)
				if err != nil {
					logger.Errorf("%s: Failed to GetCurrentRunMillis for job, error: %+v", cmdResult.RequestId, err)
					return err
				}
				sql := "UPDATE Job SET Status=?, StartTime=?, TotalRunMillis=?, TmRunningCuNum=?, JmRunningCuNum = ? WHERE Id=?"
				tx.ExecuteSqlWithArgs(sql, status, util.GetCurrentTime(), jobs[0].TotalRunMillis+curRuntime, runningCuNum, constants.DEFAULT_JM_RUNNING_CU, jobId)

				// 更新jobInstance 状态, 获取 Flink JobID 和 YARN ApplicationID
				sql = "UPDATE JobInstance SET Status=?, StartTime=?, FlinkJobId=?, ApplicationId=?, FlinkJobPlan=?, TmRunningCuNum=?, JmRunningCuNum=? WHERE Id=?"
				tx.ExecuteSqlWithArgs(sql, constants.JOB_INSTANCE_STATUS_RUNNING, util.GetCurrentTime(), flinkJobId, applicationId, flinkJobPlan, runningCuNum, constants.DEFAULT_JM_RUNNING_CU, jobRuntimeId)

				if jobs[0].Type == constants.JOB_TYPE_JAR && auth.IsInWhiteList(int64(jobs[0].AppId), constants.WHITE_LIST_SCALE_VERTEX_PARALLELISM) {
					err = updateJobPlanMaxParallelism(flinkJobPlan, tx, jobs[0])
					if err != nil {
						logger.Errorf("%s: Failed to Update parallelism for job, error: %+v", cmdResult.RequestId, err)
						return err
					}
				}
				jobService.NewJobWebUIRouterManager().AddJobWebUIRouterAsync(jobRuntimeId, jobs[0], clusterGroup)
			} else if action == constants.CLUSTER_MASTER_COMMAND_ACTION_CANCEL_JOB || action == constants.CLUSTER_MASTER_COMMAND_ACTION_CANCEL_JOB_WITH_SAVEPOINT { // 作业成功停止或暂停
				var curRuntime int64 = 0
				if jobs[0].StartTime != "" && jobs[0].StartTime != "0000-00-00 00:00:00" {
					curRuntime, err = service2.GetCurrentRunMillis(jobs[0].StartTime)
					if err != nil {
						logger.Errorf("%s: Failed to GetCurrentRunMillis for job, error: %+v", cmdResult.RequestId, err)
						return err
					}
				}
				sql := "UPDATE Job SET Status=?, TotalRunMillis=?, TmRunningCuNum=0, JmRunningCuNum=0,RunningCpu=0,RunningMem=0 WHERE Id=?" // 作业停止后需要把 RunningCuNum 设置为 0
				tx.ExecuteSqlWithArgs(sql, status, jobs[0].TotalRunMillis+curRuntime, jobId)

				sql = "UPDATE JobInstance SET Status=?, StopTime=? WHERE Id=? "
				tx.ExecuteSqlWithArgs(sql, constants.JOB_INSTANCE_STATUS_HISTORY, util.GetCurrentTime(), jobRuntimeId)

				// 停止作业，作业非运行状态，清理所有资源
				eInfo := fmt.Sprintf("***** jobSerialId %s stop to status %d ", jobs[0].SerialId, status)
				err1 := service4.CleanHistoryJobData(jobs[0], eInfo, tx)
				if err1 != nil {
					logger.Warningf("%s: CleanHistoryJobData error: %+v", eInfo, err1)
				}

				// 对于停止作业的流程，结束流程时判断是否有操作中的快照，如果有则将其设为已失败
				if !isSharedClusterGroup {
					err = service.UpdateInProgressSavepointWhenCommandFinished(jobId, command.JobSerialId, tx, cmdResult.RequestId)
					if err != nil {
						logger.Errorf("%s: Logic error? Failed to find in-progress savepoint record for job %d", cmdResult.RequestId, jobId)
					}
				}

				// 异步删除WebUI路由规则
				jobService.NewJobWebUIRouterManager().DeleteJobWebUIRouterAsync(jobRuntimeId, jobs[0], clusterGroup)
			} else { // 未知命令, 需要报错
				logger.Errorf("%s: Logic error? Failed to cope with action %d, because it is not recognized", cmdResult.RequestId, action)
				return errors.New(fmt.Sprintf("Unsupported action %d", action))
			}

			var jobConfigStatus int
			var updateJobConfigVersionSql string
			if !succeed {
				jobConfigStatus = constants.JOB_CONFIG_STATUS_PUBLISHED_FAIL
				updateJobConfigVersionSql = "UPDATE Job set PublishedJobConfigId = LastPublishedJobConfigId where id = ?"
			} else {
				jobConfigStatus = constants.JOB_CONFIG_STATUS_PUBLISHED_SUCC
				updateJobConfigVersionSql = "UPDATE Job set LastPublishedJobConfigId = PublishedJobConfigId where id = ?"
			}
			_, err = service2.UpdateJobConfig(0, 0, jobs[0].PublishedJobConfigId, jobConfigStatus, tx)
			if err != nil {
				logger.Errorf("%s: Failed to update the job config, with job config id:%d, with errors:%+v", cmdResult.RequestId, jobs[0].PublishedJobConfigId, err)
				return err
			}
			affectedResult := tx.ExecuteSqlWithArgs(updateJobConfigVersionSql, jobs[0].Id)
			_, err = affectedResult.RowsAffected()
			if err != nil {
				logger.Errorf("%s: Failed to update published job config version, with errors:%+v", cmdResult.RequestId, err)
				return err
			}
			command.Status = constants.CLUSTER_MASTER_COMMAND_FINISH
			command.FinishTime = util.GetCurrentTime()
			// todo check lock
			rowsAffected := tx.UpdateObjectWithLock(command, command.Id, "Command", true)
			if rowsAffected != 1 {
				logger.Warningf("%s: Failed to UpdateObjectWithLock because rowAffected is not 1 but %d", cmdResult.RequestId, rowsAffected)
				return errors.New(fmt.Sprintf("rowsAffected: %d, != 1, is logic error?", rowsAffected))
			}
			//jobs[0].Status = int8(status)
			jobList = append(jobList, jobs[0])
		}
		return nil
	}).Close()
	if len(jobList) < 1 {
		return
	}
	for _, job := range jobList {
		UpdateClassLogLevel(job)
		clusterService.RecordEKSResource(job.ClusterId, job.AppId, job.Region, job.OwnerUin)
		updateJobRunningCu(job)
		// 每次关闭、启动作业均忽略之前的action，因为jobinstance不同。
		rules, err := job_autoscale.QueryJobScaleRules(job.SerialId, 0, constants.SCALE_RULES_STATUS_ACTIVE, "")
		if err != nil {
			logger.Errorf("QueryJobScaleRules error, %+v", err)
			return err
		}
		err = job_autoscale.TryLockAndIgnoreJobTuningAction(job.SerialId, rules)
		if err != nil {
			logger.Errorf("TryLockAndIgnoreJobTuningAction error, %+v", err)
			continue
		}
	}
	for _, job := range stoppedJobList {
		// 发送停止事件到事件中心
		eventAlertReq := &barad2.JobAlertReq{
			JobId:     job.SerialId,
			Message:   "作业变为 STOPPED 状态。",
			EventName: constants.BARAD_EVENT_ALERT_NAME_JOB_STOPPED,
			Status:    constants.BARAD_EVENT_ALERT_OFF,
			Type:      constants.EVENT_ENTITY_TYPE_JOB_START_OR_STOP,
		}
		_, _, err := barad.ProcessEventAlert(eventAlertReq)
		if err != nil {
			logger.Errorf(" process job stop event with error %v", err)
		}
	}
	return
}

// 更新作业的运行CU数，用于全局排序
func updateJobRunningCu(job *table.Job) {
	//cluster, err := clusterService.GetClusterByJobId(job.SerialId)
	//if err != nil {
	//	logger.Errorf("finish command updateJobRunningCu GetClusterByJobId %s with error %v", job.SerialId, err)
	//	return
	//}

	jc, jm, tc, tm, err := service2.GetJobRunningCPUAndMem(job)
	if err != nil {
		return
	}
	jobRunningCu := service2.GetCuNumFromCpuMem(jc+tc, jm+tm, job.CuMem)
	logger.Infof("finish command updateJobRunningCu job %s, jc %d, jm %d, tc %d, tm %d,runningCu %d", job.SerialId, jc, jm, tc, tm, jobRunningCu)
	// runningCpu和runningMem 让updatejobresource更新。
	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		sql := "UPDATE Job SET RunningCu=? WHERE Id=?" // 作业停止后需要把 RunningCuNum 设置为 0
		tx.ExecuteSqlWithArgs(sql, jobRunningCu, job.Id)
		return nil
	}).Close()
}

func CalcTmNum(runningCuNum int, jobRuntimeId int64) (tmNum int, err error) {
	jobConfig, err := service.GetJobConfigByJobInstanceId(jobRuntimeId)
	if err != nil {
		logger.Errorf("GetJobConfigByJobInstanceId, error %v", err)
		return
	}

	properties := make([]*model3.Property, 0)

	if len(jobConfig.Properties) == 0 {
		return runningCuNum, nil
	}

	err = json.Unmarshal([]byte(jobConfig.Properties), &properties)
	if err != nil {
		logger.Errorf("jobConfig cannot Unmarshal to Property, error %v", err)
		return
	}
	numberOfTaskSlots := 1
	for _, property := range properties {
		if property.Key != constants.NUMBER_OF_TASK_SLOTS_KEY {
			continue
		}
		numberOfTaskSlots, err = strconv.Atoi(property.Value)
		if err != nil {
			logger.Warningf("jobRuntimeId %s: numberOfTaskSlots is not a number: %+v", jobRuntimeId, err)
			err = errors.New("numberOfTaskSlots is not a number: " + property.Value)
			return
		}
	}

	nonInt := 0
	if runningCuNum%numberOfTaskSlots > 0 {
		nonInt += 1
	}
	tmNum = runningCuNum/numberOfTaskSlots + nonInt
	return tmNum, nil
}

func GetCommandById(id int64) (command *cluster_master.Command, err error) {
	sql := "SELECT * FROM Command WHERE Id=?"
	cnt, rowsData, err := service.GetTxManager().GetQueryTemplate().DoQueryWithArgs(sql, id)
	if err != nil {
		return nil, err
	} else if cnt == 0 {
		return nil, errors.New(fmt.Sprintf("No Command found for id: %d", id))
	} else if cnt > 1 {
		return nil, errors.New(fmt.Sprintf("Logic error? more than 1 Command found for id: %d", id))
	}
	command = &cluster_master.Command{}
	err = util.ScanMapIntoStruct(command, rowsData[0])

	return command, err
}

func UpdateClassLogLevel(job *table.Job) {
	jobInstance, err := service4.GetRunningJobInstanceByJobId(job.Id)
	if err != nil {
		logger.Warningf("finish command UpdateClassLogLevel %d for GetRunningJobInstanceByJobId with error %v, skip it", job.Id, err)
		return
	}
	jobConfigs, err2 := service2.GetJobConfigByIds([]int64{jobInstance.JobConfigId})
	if err2 != nil {
		logger.Warningf("finish command UpdateClassLogLevel for GetJobConfigByIds with error %v", err2)
		return
	}

	if jobConfigs[0].ClazzLevels == "" || jobConfigs[0].ClazzLevels == constants.CLASS_LOG_LEVEL_DEFALUT_VALUE {
		logger.Debugf("clazzLevels is empty, use default.")
		return
	}

	cluster, err := clusterService.GetClusterByJobId(job.SerialId)
	if err != nil {
		logger.Errorf("finish command UpdateClassLogLevel GetClusterByJobId %s with error %v", job.SerialId, err)
		return
	}
	// 检测 cluster 是否支持修改日志级别
	if !strings.Contains(cluster.SupportedFeatures, constants.CLUSTER_LOG_LEVEL_FEATURE) {
		logger.Errorf("cluster %s not support %s", cluster.UniqClusterId, constants.CLUSTER_LOG_LEVEL_FEATURE)
		return
	}
	client, err := k8s.GetK8sService().NewClient([]byte(cluster.KubeConfig))
	if err != nil {
		logger.Errorf("finish command UpdateClassLogLevel k8s.newClient with error %v", err)
		return
	}
	clazzLevels := make([]*log.ClazzLevel, 0, 0)
	err = json.Unmarshal([]byte(jobConfigs[0].ClazzLevels), &clazzLevels)
	if err != nil {
		logger.Errorf("finish command UpdateClassLogLevel json.Unmarshal(%s)  with error [%v]", jobConfigs[0].ClazzLevels, err)
		return
	}
	if len(clazzLevels) < 1 {
		return
	}

	modifyJobLogLevelReq := &model.ModifyJobLogLevelReq{
		JobId:              job.SerialId,
		LogLevel:           "OTHER",
		FlinkVersion:       service2.GetFlinkVersion(job, jobConfigs[0]),
		ClazzLevels:        clazzLevels,
		IsModifyClazzLevel: true,
	}
	_, err = service4.ModifyConfigMap(modifyJobLogLevelReq, client, cluster)
	if err != nil {
		logger.Errorf("finish command UpdateClassLogLevel ModifyConfigMap  with %v error [%v]", modifyJobLogLevelReq, err)
		return
	}
	logger.Infof("finish UpdateClassLogLevel...")
	return
}

func AnalyzeK8sEventReturnMessage(jobId, region, errMsg *string) (messageList []string) {
	beginTime := time.Now().UnixNano()
	messageList = make([]string, 0)
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("AnalyseK8sEventReturnMessage panic, with errors: %+v", errs)
		}

		if len(messageList) == 0 || len(messageList) == 1 {
			messageList = make([]string, 0)
			if len(*errMsg) == 0 {
				messageList = append(messageList, constants.DEFAULT_JOB_ERROR_INFO_CN)
			} else {
				messageList = append(messageList, constants.DEFAULT_JOB_ERROR_INFO_CN_DETAIL)
				messageList = append(messageList, *errMsg)
			}
		} else {
			messageList = distinct(messageList)
		}

		logger.Debugf("AnalyseK8sEventReturnMessage elapsed time: %d", (time.Now().UnixNano()-beginTime)/1000000)
	}()

	req := &log.DescribeJobK8sEventReq{}
	req.JobId = *jobId
	req.Limit = 20
	req.Region = *region
	// 默认获取最近10分钟的非 Normal 的事件进行分析
	req.EndTime = time.Now().Unix() * 1000
	req.StartTime = req.EndTime - 10*60*1000

	eventService := service3.NewDescribeJobK8sEventService()
	errorCode, clsErrMsg, resp := eventService.DescribeJobK8sEvent(req)

	if controller.OK != errorCode {
		logger.Errorf("Failed to search event data from CLS because %s. ", clsErrMsg)
		return messageList
	}

	// 获取中文原因与事件特征的映射关系
	eventFeatureJsonStr, err := config.GetRainbowConfiguration("JobKubernetesEventMappings", "kubernetes_event_feature.csv")
	if err != nil {
		logger.Errorf("Failed to get k8s_event_feature.csv from Rainbow because %+v. ", err)
		return messageList
	}

	eventFeature := make(map[string]string, 0)
	err = json.Unmarshal([]byte(eventFeatureJsonStr), &eventFeature)
	if err != nil {
		logger.Errorf("Failed to Unmarshal event feature because %+v. ", err)
		return messageList
	}

	// 方法入参 errMsg 是作业调度失败时，CA 默认传入的英文错误描述 "Job could not be started due to insufficient resources"
	// 对该错误描述进行中文翻译
	if strings.Contains(*errMsg, constants.DEFAULT_JOB_ERROR_INFO_FROM_CA) {
		temp := eventFeature[constants.DEFAULT_JOB_ERROR_INFO_FROM_CA]
		messageList = append(messageList, temp)
	} else {
		temp := eventFeature[constants.DEFAULT_JOB_ERROR_INFO_FROM_CA]
		messageList = append(messageList, temp)
		if len(*errMsg) > 0 {
			messageList = append(messageList, *errMsg)
		}
	}

	// 获取可以忽略的事件特征，如果忽略的特征的逻辑异常，不影响下面的流程
	eventIgnoreFeature := make([]string, 0)
	eventIgnoreFeatureJsonStr, err := config.GetRainbowConfiguration("JobKubernetesEventMappings", "kubernetes_event_ignore_feature.csv")
	err = json.Unmarshal([]byte(eventIgnoreFeatureJsonStr), &eventIgnoreFeature)
	if err != nil {
		logger.Errorf("Failed to Unmarshal event ignore feature because %+v. ", err)
	}

	if len(resp.Response.Results) > 0 {
		for _, dataObj := range resp.Response.Results {
			jsonByte := []byte(*dataObj.LogJson)
			jsonObj := make(map[string]interface{}, 0)
			err := json.Unmarshal(jsonByte, &jsonObj)
			if err != nil {
				logger.Errorf("Failed to Unmarshal event data(%+v) because %+v. ", *dataObj.LogJson, err)
				continue
			}
			eventMap := jsonObj["event"].(map[string]interface{})
			eventMsg := eventMap["message"].(string)

			ignore := false
			for _, v := range eventIgnoreFeature {
				if strings.Contains(eventMsg, v) {
					ignore = true
					break
				}
			}

			if ignore {
				continue
			}

			isFind := false
			for k, v := range eventFeature {
				if strings.Contains(eventMsg, k) {
					isFind = true
					messageList = append(messageList, v)
					// 此处的 break 暂时注掉，解决一个 event 中包含多个特征值
					//break
				}
			}

			if !isFind {
				messageList = append(messageList, eventMsg)
				logger.Warningf("Events cannot be converted. event.message: ", eventMsg)
			}

		}
	}
	return
}

func distinct(arr []string) []string {
	result := make([]string, 0)
	for _, str := range arr {
		isFind := false
		for _, str2 := range result {
			if str == str2 {
				isFind = true
				break
			}
		}
		if !isFind && len(str) > 0 {
			result = append(result, str)
		}
	}
	return result
}

func updateJobPlanMaxParallelism(inputJSON string, tx *dao.Transaction, job *table.Job) error {
	var data table2.Job
	err := json.Unmarshal([]byte(inputJSON), &data)
	if err != nil {
		logger.Errorf("JSON 解析失败:%s", err)
		return err
	}

	maxParallelism := 0
	for _, node := range data.Nodes {
		if node.Parallelism > maxParallelism {
			maxParallelism = node.Parallelism
		}
	}
	_, err = service4.UpdateJobConfigDefaultParallelism(job.Id, job.PublishedJobConfigId, int16(maxParallelism), tx)
	if err != nil {
		logger.Errorf("job %s Failed to update job config DefaultParallelism,err %s", job.SerialId, err)
		return err
	}

	err = service4.UpdateJobInstanceParallelism(job.Id, maxParallelism)
	if err != nil {
		logger.Errorf("job %s Failed to update jobInstance DefaultParallelism,err %s", job.SerialId, err)
		return err
	}

	return nil
}
