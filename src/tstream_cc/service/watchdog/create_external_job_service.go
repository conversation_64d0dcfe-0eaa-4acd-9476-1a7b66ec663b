package watchdog

import (
	"encoding/json"
	"fmt"
	"github.com/juju/errors"
	"path/filepath"
	"strconv"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/common/uuid"
	service5 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model5 "tencentcloud.com/tstream_galileo/src/tstream_cc/model"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"
	model1 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/item_space"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_autoscale"
	model2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_config"
	model4 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/resource"
	model3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/resource_config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/draft"
	table4 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	table5 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job_config"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job_instance"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service4 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	draft3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/draft"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/item_space"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/item_space"
	service7 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job"
	jobautoscale2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job_autoscale"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job_config"
	service6 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource"
	service1 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource_config"
	"time"
)

func GetItemWorkSpaceId(req *model.CreateJobReq) (string, error) {
	itemSpace := &model1.CreateItemSpaceReq{}
	itemSpace.WorkSpaceName = "external"
	itemSpace.Uin = req.Uin
	itemSpace.SubAccountUin = req.SubAccountUin
	itemSpace.AppId = int64(req.AppId)
	itemSpace.Region = req.Region
	ExistItemSpaceTable, _ := service3.GetItemSpaceBySpaceName(itemSpace.AppId, itemSpace.Region, itemSpace.WorkSpaceName)
	if ExistItemSpaceTable != nil {
		return ExistItemSpaceTable.SerialId, nil
	}
	_, err, rsp := service3.DoCreateItemSpace(itemSpace)
	if err != err && !strings.Contains(err, "existed") {
		logger.Errorf("%s: create ItemSpace: %s error: %+v", req.RequestId, itemSpace.WorkSpaceName, err)
		return "", errors.New(err)
	}
	createItemSpaceRsp := rsp.(*model1.CreateItemSpaceRsp)

	return createItemSpaceRsp.WorkSpaceId, nil
}

func BuildCorrelation(req *model.CreateJobReq) error {
	correlationReq := &model1.CreateCorrelationReq{}
	correlationReq.WorkSpaceId = req.WorkSpaceId
	correlationReq.Uin = req.Uin
	correlationReq.SubAccountUin = req.SubAccountUin
	correlationReq.AppId = int64(req.AppId)
	correlationReq.Region = req.Region
	correlationReq.ClusterGroupSerialId = []string{req.ClusterId}
	_, err, _ := service3.DoCreateCorrelation(correlationReq)
	if err != "" {
		logger.Errorf("%s: create Correlation: %s error: %+v", req.RequestId, correlationReq.WorkSpaceId, err)
		return errors.New(err)
	}
	return nil
}

func BuildJobReq(clusterGroup *table4.ClusterGroup, reqData *model.CreateExternalJobReq) *model.CreateJobReq {
	reqJobData := &model.CreateJobReq{}
	reqJobData.AppId = clusterGroup.AppId
	reqJobData.Uin = clusterGroup.OwnerUin
	reqJobData.SubAccountUin = clusterGroup.CreatorUin
	reqJobData.Region = clusterGroup.Region
	reqJobData.ClusterType = clusterGroup.Type
	manageType := constants.MANAGE_TYPE_EXTERNAL
	if reqData.ManageType != "" {
		manageType = reqData.ManageType
	}
	reqJobData.ManageType = manageType

	reqJobData.JobType = reqData.JobType
	reqJobData.Name = reqData.Name
	reqJobData.ClusterId = reqData.ClusterId
	reqJobData.FlinkVersion = reqData.FlinkVersion
	if reqData.FlinkJobType > 0 {
		reqJobData.FlinkJobType = reqData.FlinkJobType
	}

	return reqJobData
}

func BuildDraftConfigReq(clusterGroup *table4.ClusterGroup, req *model.CreateExternalJobReq, jobReq *model.CreateJobReq) *draft.ModifyDraftReq {
	mcReq := &draft.ModifyDraftReq{
		AppId:              clusterGroup.AppId,
		Region:             clusterGroup.Region,
		JobId:              req.SerialId,
		EntrypointClass:    req.EntrypointClass,
		ProgramArgs:        req.ProgramArgs,
		Properties:         req.Properties,
		DefaultParallelism: req.DefaultParallelism,
		Uin:                clusterGroup.OwnerUin,
		SubAccountUin:      clusterGroup.CreatorUin,
		JobManagerSpec:     req.JobManagerSpec,
		TaskManagerSpec:    req.TaskManagerSpec,
		LogCollect:         false,
		WorkSpaceId:        jobReq.WorkSpaceId,
		IsSupOwner:         1,
		Action:             "ModifyDraftConfig",
		AutoRecover:        constants.AutoRecoverDisable,
		FlinkVersion:       req.FlinkVersion,
		JobManagerCpu:      req.JobManagerCpu,
		JobManagerMem:      req.JobManagerMem,
		TaskManagerCpu:     req.TaskManagerCpu,
		TaskManagerMem:     req.TaskManagerMem,
	}

	cluster, err := service4.GetActiveClusterByClusterGroupId(clusterGroup.Id)
	if err != nil {
		logger.Errorf("Failed to get cluster by cluster group id for %d because %+v", clusterGroup.Id, err)
		return mcReq
	}
	if cluster.DefaultCOSBucket != "" {
		// 默认采集到cos
		mcReq.LogCollect = true
		mcReq.LogCollectType = constants.JobLogCollectTypeCOS
		mcReq.LogLevel = constants.LOG_LEVEL_INFO
		mcReq.COSBucket = cluster.DefaultCOSBucket
	}
	return mcReq
}

func BuildJobConfigReq(clusterGroup *table4.ClusterGroup, reqData *model.CreateExternalJobReq, reqJobData *model.CreateJobReq) *model2.CreateJobConfigReq {
	reqJobConfigData := &model2.CreateJobConfigReq{}
	reqJobConfigData.AppId = clusterGroup.AppId
	reqJobConfigData.Uin = clusterGroup.OwnerUin
	reqJobConfigData.Action = "CreateJobConfig"
	reqJobConfigData.SubAccountUin = clusterGroup.CreatorUin
	reqJobConfigData.Region = clusterGroup.Region
	reqJobConfigData.WorkSpaceId = reqJobData.WorkSpaceId
	reqJobConfigData.Version = reqJobData.Version
	reqJobConfigData.JobId = reqData.SerialId
	reqJobConfigData.ProgramArgs = reqData.ProgramArgs
	reqJobConfigData.EntrypointClass = reqData.EntrypointClass
	reqJobConfigData.DefaultParallelism = reqData.DefaultParallelism
	reqJobConfigData.JobManagerSpec = reqData.JobManagerSpec
	reqJobConfigData.TaskManagerSpec = reqData.TaskManagerSpec
	reqJobConfigData.IsSupOwner = 1

	reqJobConfigData.Properties = reqData.Properties
	// 外部作业不开启自动恢复

	if reqData.ManageType == constants.MANAGE_TYPE_EXTERNAL {
		reqJobConfigData.AutoRecover = constants.AutoRecoverDisable
	} else {
		reqJobConfigData.AutoRecover = constants.AutoRecoverEnable
	}
	cluster, err := service4.GetActiveClusterByClusterGroupId(clusterGroup.Id)
	if err != nil {
		logger.Errorf("Failed to get cluster by cluster group id for %d because %+v", clusterGroup.Id, err)
		return reqJobConfigData
	}
	if cluster.DefaultCOSBucket != "" {
		// 默认采集到cos
		reqJobConfigData.LogCollect = true
		reqJobConfigData.LogCollectType = constants.JobLogCollectTypeCOS
		reqJobConfigData.LogLevel = constants.LOG_LEVEL_INFO
		reqJobConfigData.COSBucket = cluster.DefaultCOSBucket
	}

	reqJobConfigData.JobManagerCpu = reqData.JobManagerCpu
	reqJobConfigData.JobManagerMem = reqData.JobManagerMem
	reqJobConfigData.TaskManagerCpu = reqData.TaskManagerCpu
	reqJobConfigData.TaskManagerMem = reqData.TaskManagerMem

	return reqJobConfigData
}

func GetSystemConnector(builtInConnectorStr string, createJobReq *model.CreateJobReq) (resourceRefItems []*model2.ResourceRefItem, err error) {
	if len(builtInConnectorStr) == 0 {
		return nil, nil
	}
	describeSystemResourcesReq := &model4.DescribeSystemResourcesReq{}
	describeSystemResourcesReq.AppId = int64(createJobReq.AppId)
	describeSystemResourcesReq.Uin = createJobReq.Uin
	describeSystemResourcesReq.SubAccountUin = createJobReq.SubAccountUin
	describeSystemResourcesReq.Region = createJobReq.Region
	describeSystemResourcesReq.Version = createJobReq.Version
	describeSystemResourcesReq.FlinkVersion = createJobReq.FlinkVersion
	describeSystemResourcesReq.ClusterId = createJobReq.ClusterId
	describeSystemResourcesReq.Limit = 100
	describeSystemResourcesReq.Offset = 0
	rsp, err := service6.DoDescribeSystemResources(describeSystemResourcesReq)
	if err != nil {
		logger.Errorf("DoDescribeSystemResources req: %#v err: %#v", describeSystemResourcesReq, err)
		return nil, err
	}
	builtInConnectors := strings.Split(builtInConnectorStr, ",")

	resourceRefItems = make([]*model2.ResourceRefItem, 0, 0)

	for _, systemResourceItem := range rsp.ResourceSet {
		if service2.StrArrContains(builtInConnectors, systemResourceItem.Name) {
			item := &model2.ResourceRefItem{}
			item.ResourceId = systemResourceItem.ResourceId
			// type作为依赖存在是RESOURCE_REF_USAGE_TYPE_DEPENDENCY_JAR:0, 而 systemResourceItem.ResourceType：1
			item.Type = constants.RESOURCE_REF_USAGE_TYPE_DEPENDENCY_JAR
			item.Version = systemResourceItem.LatestResourceConfigVersion
			resourceRefItems = append(resourceRefItems, item)
		}
	}
	return resourceRefItems, nil
}

func checkNeedCreateJobConfig(newJobConfig *table5.JobConfig, latestJobConfig *table5.JobConfig) bool {
	if newJobConfig == nil || latestJobConfig == nil {
		logger.Infof("checkNeedCreateJobConfig newJobConfig or latestJobConfig is nil .")
		return true
	}
	// 比较并行度， cu, mem 是否有修改
	if newJobConfig.DefaultParallelism != latestJobConfig.DefaultParallelism {
		logger.Infof("checkNeedCheckJobConfig newJobConfig.DefaultParallelism: %#v, latestJobConfig.DefaultParallelism: %#v not equal.", newJobConfig.DefaultParallelism, latestJobConfig.DefaultParallelism)
		return true
	}
	if newJobConfig.JmCuSpec != latestJobConfig.JmCuSpec {
		logger.Infof("checkNeedCheckJobConfig newJobConfig.JmCuSpec: %#v, latestJobConfig.JmCuSpec: %#v not equal.", newJobConfig.JmCuSpec, latestJobConfig.JmCuSpec)
		return true
	}
	if newJobConfig.TmCuSpec != latestJobConfig.TmCuSpec {
		logger.Infof("checkNeedCheckJobConfig newJobConfig.TmCuSpec: %#v, latestJobConfig.TmCuSpec: %#v not equal.", newJobConfig.TmCuSpec, latestJobConfig.TmCuSpec)
		return true
	}

	if newJobConfig.JobManagerCpu != latestJobConfig.JobManagerCpu {
		logger.Infof("checkNeedCheckJobConfig newJobConfig.JobManagerCpu: %#v, latestJobConfig.JobManagerCpu: %#v not equal.", newJobConfig.JobManagerCpu, latestJobConfig.JobManagerCpu)
		return true
	}
	if newJobConfig.JobManagerMem != latestJobConfig.JobManagerMem {
		logger.Infof("checkNeedCheckJobConfig newJobConfig.JobManagerMem: %#v, latestJobConfig.JobManagerMem: %#v not equal.", newJobConfig.JobManagerMem, latestJobConfig.JobManagerMem)
		return true
	}

	if newJobConfig.TaskManagerMem != latestJobConfig.TaskManagerMem {
		logger.Infof("checkNeedCheckJobConfig newJobConfig.TaskManagerMem: %#v, latestJobConfig.TaskManagerMem: %#v not equal.", newJobConfig.TaskManagerMem, latestJobConfig.TaskManagerMem)
		return true
	}
	if newJobConfig.TaskManagerCpu != latestJobConfig.TaskManagerCpu {
		logger.Infof("checkNeedCheckJobConfig newJobConfig.TaskManagerCpu: %#v, latestJobConfig.TaskManagerCpu: %#v not equal.", newJobConfig.TaskManagerCpu, latestJobConfig.TaskManagerCpu)
		return true
	}

	// 比较cosBucket
	if newJobConfig.COSBucket != latestJobConfig.COSBucket {
		logger.Infof("checkNeedCheckJobConfig newJobConfig.COSBucket: %#v, latestJobConfig.COSBucket: %#v not equal.", newJobConfig.COSBucket, latestJobConfig.COSBucket)
		return true
	}

	// 日志采集类型
	if newJobConfig.LogCollect != latestJobConfig.LogCollect {
		logger.Infof("checkNeedCheckJobConfig newJobConfig.LogCollect: %#v, latestJobConfig.LogCollect: %#v not equal.", newJobConfig.LogCollect, latestJobConfig.LogCollect)
		return true
	}

	// 比较高级参数是否相同
	if newJobConfig.Properties != latestJobConfig.Properties {
		logger.Infof("checkNeedCheckJobConfig newJobConfig.Properties: %#v, latestJobConfig.Properties: %#v not equal.", newJobConfig.Properties, latestJobConfig.Properties)
		return true
	}

	if newJobConfig.EntrypointClass != latestJobConfig.EntrypointClass {
		logger.Infof("checkNeedCheckJobConfig newJobConfig.EntrypointClass: %#v, latestJobConfig.EntrypointClass: %#v not equal.", newJobConfig.EntrypointClass, latestJobConfig.EntrypointClass)
		return true
	}
	// 比较sqlCode是否相同
	if newJobConfig.SqlCode != latestJobConfig.SqlCode {
		logger.Infof("checkNeedCheckJobConfig newJobConfig.SqlCode: %#v, latestJobConfig.SqlCode: %#v not equal.", newJobConfig.SqlCode, latestJobConfig.SqlCode)
		return true
	}
	// 比较资源是否有修改

	if newJobConfig.LibConfig != latestJobConfig.LibConfig {
		logger.Infof("checkNeedCheckJobConfig newJobConfig.LibConfig: %#v, latestJobConfig.LibConfig: %#v not equal.", newJobConfig.LibConfig, latestJobConfig.LibConfig)
		return true
	}
	return false
}

func getLibConfig(reqData *model.CreateExternalJobReq, builtInConnectorStr string) string {
	libConfig := make(map[string]string)
	jarDependencies := reqData.JarDependencies
	if len(jarDependencies) > 0 {
		libConfig["jarDependencies"] = jarDependencies
	}
	jarShipToSystem := reqData.JarShipToSystem
	if len(jarShipToSystem) > 0 {
		libConfig["jarShipToSystem"] = jarShipToSystem
	}
	if len(builtInConnectorStr) > 0 {
		libConfig["builtInConnector"] = builtInConnectorStr
	}
	// 将map转换为JSON字符串
	jsonData, err := json.Marshal(libConfig)
	if err != nil {
		fmt.Println("Error marshalling JSON:", err)
		logger.Errorf("Failed to marshallingLibConfig JSON: %#v, errors: %#v", libConfig, err)
		return ""
	}
	return string(jsonData)
}

func CreateExternalJobConfig(reqData *model.CreateExternalJobReq) (err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Failed to CreateExternalJobConfig, errors: %+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()

	serialId := reqData.ClusterId
	serialIds := make([]string, 0)
	serialIds = append(serialIds, serialId)
	clusterGroups, err := service2.ListClusterGroups(&service2.ListClusterGroupsParam{
		AppId:        0,
		Regions:      nil,
		Zone:         "",
		ClusterType:  nil,
		ClusterNames: nil,
		IsVagueNames: false,
		SerialIds:    serialIds,
		Offset:       0,
		Limit:        0,
		OrderType:    0,
		StatusList:   []int{constants.CLUSTER_GROUP_STATUS_RUNNING},
	})
	if err != nil {
		return err
	} else if len(clusterGroups) == 0 {
		return errors.New(fmt.Sprintf("No ClusterGroup found "+
			" serialId: %s", serialId))
	} else if len(clusterGroups) > 1 {
		return errors.New(fmt.Sprintf("Logic error? More than one ClusterGroup found serialId: %s", serialId))
	}
	clusterGroup := clusterGroups[0]
	reqJobData := BuildJobReq(clusterGroup, reqData)

	properties := reqData.Properties
	newProperties := make([]*model5.Property, 0)
	builtInConnectorStr := ""
	itemSpaceId := ""
	publicDependency := ""
	if len(properties) > 0 {
		for _, property := range properties {
			if property.Key == constants.CREATE_JOB_SCALE_RULE_KEY {
				reqData.CreateJobScale = property.Value
				continue
			} else if property.Key == constants.BUILT_IN_CONNECTOR_KEY {
				builtInConnectorStr = property.Value
				continue
			} else if property.Key == constants.WORK_SPACE_ID_KEY || property.Key == "Oceanus."+constants.WORK_SPACE_ID_KEY {
				itemSpaceId = property.Value
				logger.Infof("#itemSpaceId11:%s", itemSpaceId)
				continue
			} else if property.Key == constants.PUBLIC_DEPENDENCY_KEY || property.Key == "Oceanus."+constants.PUBLIC_DEPENDENCY_KEY {
				publicDependency = property.Value
				continue
			} else if property.Key == constants.JOB_MANAGER_CPU_KEY {
				jobManagerCpuStr := property.Value
				jobManagerCpu, err := strconv.ParseFloat(jobManagerCpuStr, 64)
				if err == nil {
					reqData.JobManagerCpu = float32(jobManagerCpu)
				}
				continue
			} else if property.Key == constants.JOB_MANAGER_MEM_KEY {
				jobManagerMemStr := property.Value
				jobManagerMem, err := strconv.ParseFloat(jobManagerMemStr, 64)
				if err == nil {
					reqData.JobManagerMem = float32(jobManagerMem)
				}
				continue
			} else if property.Key == constants.TASK_MANAGER_CPU_KEY {
				taskManagerCpuStr := property.Value
				taskManagerCpu, err := strconv.ParseFloat(taskManagerCpuStr, 64)
				if err == nil {
					reqData.TaskManagerCpu = float32(taskManagerCpu)
				}
				continue
			} else if property.Key == constants.TASK_MANAGER_MEM_KEY {
				taskManagerMemStr := property.Value
				taskManagerMem, err := strconv.ParseFloat(taskManagerMemStr, 64)
				if err == nil {
					reqData.TaskManagerMem = float32(taskManagerMem)
				}
				continue
			}
			newProperties = append(newProperties, property)
		}
		logger.Debugf("##### newProperties:%+v", newProperties)
		reqData.Properties = newProperties
	}

	// 兼容用户没有传 WorkSpaceId
	if len(itemSpaceId) == 0 {
		itemSpaceId, err = GetItemWorkSpaceId(reqJobData)
		logger.Infof("#itemSpaceId222:%s", itemSpaceId)
		if err != nil {
			logger.Errorf("%s: Query  ItemSpaces: external, error: %+v", reqData.RequestId, err)
			return err
		}
	}

	correlations, err := service3.QueryCorrelation([]string{itemSpaceId}, serialIds, true, 0)
	if err != nil {
		logger.Errorf("%s: Query  ItemSpacesClusters error: %+v", reqData.RequestId, err)
		return err
	}

	reqJobData.WorkSpaceId = itemSpaceId
	// 集群没有可用的空间
	if len(correlations) == 0 {
		//  空间关联cluster
		err := BuildCorrelation(reqJobData)
		if err != nil {
			logger.Errorf("%s: Create Correlation error: %+v", reqData.RequestId, err)
			return err
		}
	}

	if reqJobData.Version == "" {
		reqJobData.Version = "2019-04-22"
	}

	itemTable, err := item_space.GetItemSpaceByItemSpaceId(itemSpaceId, int64(reqJobData.AppId))
	if err != nil {
		logger.Errorf(" itemSpaceId %s: GetItemSpaceByItemSpaceId error: %+v", itemSpaceId, err)
		return err
	}

	listJobQuery := model.ListJobQuery{
		AppId:        reqJobData.AppId,
		Regions:      []string{reqJobData.Region},
		Names:        []string{reqData.Name},
		IsVagueNames: false,
		Offset:       0,
		Limit:        0,
		ItemSpaceIds: []int64{itemTable.Id},
		ManageType:   reqJobData.ManageType,
		FlinkJobType: reqJobData.FlinkJobType,
	}
	jobs, err := service2.ListJobs(&listJobQuery)
	jobSerialId := ""
	// 1. 创建job
	if err != nil {
		return err
	} else if len(jobs) > 1 {
		return errors.New("Found more job by Name: " + reqData.Name)
	} else if len(jobs) == 0 {
		isOk, msg, rspJob := service7.DoCreateJob(reqJobData)
		if isOk != controller.OK {
			return errors.New(msg)
		}
		jobSerialId = rspJob.JobId
	} else {
		jobSerialId = jobs[0].SerialId
	}

	reqData.SerialId = jobSerialId

	systemResourceItems, err := GetSystemConnector(builtInConnectorStr, reqJobData)
	libConfig := getLibConfig(reqData, builtInConnectorStr)

	// 2. modify draft config
	mcReq := BuildDraftConfigReq(clusterGroup, reqData, reqJobData)
	mcReq.LibConfig = libConfig
	resourceRefItems := BuildResourceLoc(reqData, reqJobData, clusterGroup.Id, itemSpaceId, systemResourceItems, publicDependency)
	if resourceRefItems != nil {
		mcReq.ResourceRefs = resourceRefItems
	}
	_, err = draft3.DoModifyDraftConfig(mcReq)
	if err != nil {
		logger.Errorf("jobSerialId: %s: Failed to modify draft config, with errors:%+v", jobSerialId, err)
		return err
	}

	err = CheckResourceLocReadyRetry(resourceRefItems)
	if err != nil {
		logger.Errorf("CheckResourceLocReady failed, with errors:%+v", err)
		return err
	}

	listJobConfigQuery := model.ListJobQuery{
		AppId:      reqJobData.AppId,
		Regions:    []string{reqJobData.Region},
		SerialId:   jobSerialId,
		ManageType: reqJobData.ManageType,
	}
	latestJobConfig, err := service2.ListJobConfigByQuery(&listJobConfigQuery)

	reqJobConfigData := BuildJobConfigReq(clusterGroup, reqData, reqJobData)
	reqJobConfigData.LibConfig = libConfig
	if resourceRefItems != nil {
		reqJobConfigData.ResourceRefs = resourceRefItems
	}

	jobConfig := &table5.JobConfig{}
	updateJobConfigFromDraft := false

	if err != nil {
		return err
	} else if reqData.ManageType != constants.MANAGE_TYPE_EXTERNAL {
		// 3. 创建jobConfig
		draftjobConfig, err := service2.ListDraftJobConfigquery(jobSerialId)
		if err != nil {
			logger.Errorf("jobSerialId: %s: Failed to get draft config, with errors:%+v", jobSerialId, err)
			return err
		}

		needCreateJobConfig := checkNeedCreateJobConfig(draftjobConfig, latestJobConfig)
		if needCreateJobConfig {
			createJobConfigRsp, err := service.DoCreateJobConfig(reqJobConfigData)
			if err != nil {
				logger.Errorf("CreateJobConfig failed, jobSerialId: %s,  error: %v", jobSerialId, err)
				return err
			}
			jobConfigVersionId := createJobConfigRsp.Version
			jobConfig, err = service2.GetJobConfigBySerialIdAndVersionId(jobSerialId, jobConfigVersionId)
			if err != nil {
				logger.Errorf("GetJobConfigBySerialIdAndVersionId failed, jobSerialId: %s, jobConfigVersionId:%d, error: %v", jobSerialId, jobConfigVersionId, err)
				return err
			}
		} else {
			jobConfig = latestJobConfig
		}

	} else if latestJobConfig != nil && reqData.ManageType == constants.MANAGE_TYPE_EXTERNAL {
		return errors.New("Found more jobConfig by jobSerialId: " + jobSerialId)
	} else {
		jobConfig = latestJobConfig
		updateJobConfigFromDraft = true
	}

	txManager := service5.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		// 更新 job 配置
		sql := "UPDATE Job SET TmRunningCuNum=?, JmRunningCuNum=?, PublishedJobConfigId = ?, LastPublishedJobConfigId=? WHERE Id=?"
		tx.ExecuteSqlWithArgs(sql, jobConfig.TmCuSpec, constants.DEFAULT_JM_RUNNING_CU, jobConfig.Id, jobConfig.Id, jobConfig.JobId)

		if updateJobConfigFromDraft {
			service2.UpdateJobConfigFromDraft(jobConfig.JobId, jobConfig.Id, tx)
		} else {
			service2.UpdateJobConfig(jobConfig.JobId, int64(jobConfig.VersionId), jobConfig.Id, constants.JOB_CONFIG_STATUS_PUBLISHED_SUCC, tx)
		}

		// 如果设置了runJob参数 就调用runJob
		if reqData.RunMode == constants.RUN_MODE_NOT_START_JOB {
			jobInstances, err := service2.ListJobInstanceByRunningOrderId(jobConfig.JobId, nil)
			if err != nil {
				logger.Error("[CreateExternalJobConfig] ListJobInstances failed, jobId: %s,  error: %v", jobConfig.JobId, err)
				return err
			}
			if len(jobInstances) >= 1 {
				return nil
			}
			// 创建jobConfigInstance
			jobInstance, err := BuildJobInstance(reqData, jobConfig)
			if err != nil {
				logger.Errorf(" jobConfigId: %d: Failed to build job instance, with errors:%+v", jobConfig.Id, err)
				return err
			}
			tx.SaveObject(jobInstance, "JobInstance")
		} else {
			return RunJobWithRetry(reqJobConfigData, int64(jobConfig.VersionId), reqData.SavepointPath, 10)
		}
		return nil
	}).Close()
	CreateJobScaleRule(reqData, reqJobData)
	return nil
}

func RunJobWithRetry(req *model2.CreateJobConfigReq, versionId int64, savepointPath string, retryCnt int) (err error) {
	cnt := 0
	for cnt < retryCnt {
		err = RunJob(req, versionId, savepointPath)

		if err == nil {
			return nil
		}
		logger.Errorf("Failed to run job with CreateJobConfigReq: %+v, versionId: %d,  because %+v", req, versionId, err)

		cnt = cnt + 1
		time.Sleep(1 * time.Second)
	}
	return err
}

func CreateJobScaleRule(reqData *model.CreateExternalJobReq, reqJobData *model.CreateJobReq) {
	if len(reqData.CreateJobScale) == 0 {
		return
	}
	createJobScaleRuleReq := &job_autoscale.CreateJobScaleRuleReq{}
	err := json.Unmarshal([]byte(reqData.CreateJobScale), createJobScaleRuleReq)
	if err != nil {
		logger.Errorf("reqData.CreateJobScale: %s cannot convert to model.CreateJobScaleRuleReq, err:%v", reqData.CreateJobScale, err)
		return
	}
	createJobScaleRuleReq.JobId = reqData.SerialId
	createJobScaleRuleReq.AppId = int64(reqJobData.AppId)
	createJobScaleRuleReq.Uin = reqJobData.Uin
	createJobScaleRuleReq.SubAccountUin = reqJobData.SubAccountUin

	createJobScaleRuleReq.DurationTime = constants.SCALE_DEALY_SECONDS
	createJobScaleRuleReq.ConditionRatio = constants.SCALE_CONDITIONRATIO

	createJobScaleRuleReq.Region = reqJobData.Region
	createJobScaleRuleReq.ScalingType = "auto"
	createJobScaleRuleReq.Step = "2"
	createJobScaleRuleReq.Threshold = 80
	createJobScaleRuleReq.WorkSpaceId = reqJobData.WorkSpaceId
	createJobScaleRuleReq.Version = reqJobData.Version
	createJobScaleRuleReq.RequestId = reqData.RequestId
	if createJobScaleRuleReq.ReachLimit == 0 {
		createJobScaleRuleReq.ReachLimit = 64
	}
	tableRules, err := jobautoscale2.QueryJobScaleRules(createJobScaleRuleReq.JobId, createJobScaleRuleReq.AppId, 0, reqData.RequestId)
	if err != nil {
		logger.Errorf("Failed to QueryJobScaleRules, with JobId:%s,  with error err :%v", createJobScaleRuleReq.JobId, err)
		return
	}
	if len(tableRules) == 0 {
		// 创建jobScaleRule
		_, err = jobautoscale2.DoCreateJobScaleRule(createJobScaleRuleReq)
		if err != nil {
			logger.Errorf("Failed to DoCreateJobScaleRule, with createJobScaleRuleReq:%v,  with error err :%v", createJobScaleRuleReq, err)
			return
		}
	} else if len(tableRules) >= 1 {
		modifyJobScaleRuleReq := BuildModifyJobScaleRuleReq(createJobScaleRuleReq)

		modifyJobScaleRuleReq.RuleId = tableRules[0].SerialId
		modifyJobScaleRuleReq.Status = constants.SCALE_RULES_STATUS_ACTIVE
		_, err = jobautoscale2.DoModifyJobScaleRule(modifyJobScaleRuleReq)
		if err != nil {
			logger.Errorf("Failed to DoModifyJobScaleRule, with modifyJobScaleRuleReq:%v,  with error err :%v", modifyJobScaleRuleReq, err)
			return
		}
	}
}

func BuildModifyJobScaleRuleReq(createJobScaleRuleReq *job_autoscale.CreateJobScaleRuleReq) *job_autoscale.ModifyJobScaleRuleReq {
	modifyJobScaleRuleReq := &job_autoscale.ModifyJobScaleRuleReq{}
	modifyJobScaleRuleReq.AppId = createJobScaleRuleReq.AppId
	modifyJobScaleRuleReq.Uin = createJobScaleRuleReq.Uin
	modifyJobScaleRuleReq.SubAccountUin = createJobScaleRuleReq.SubAccountUin
	modifyJobScaleRuleReq.DurationTime = 300
	modifyJobScaleRuleReq.ConditionRatio = 100
	modifyJobScaleRuleReq.Region = createJobScaleRuleReq.Region
	modifyJobScaleRuleReq.Step = "2"
	modifyJobScaleRuleReq.Threshold = 80
	modifyJobScaleRuleReq.WorkSpaceId = createJobScaleRuleReq.WorkSpaceId
	modifyJobScaleRuleReq.Version = createJobScaleRuleReq.Version
	modifyJobScaleRuleReq.RequestId = createJobScaleRuleReq.RequestId
	modifyJobScaleRuleReq.ReachLimit = createJobScaleRuleReq.ReachLimit
	modifyJobScaleRuleReq.Configuration = createJobScaleRuleReq.Configuration
	modifyJobScaleRuleReq.RuleName = createJobScaleRuleReq.RuleName
	return modifyJobScaleRuleReq
}

func RunJob(req *model2.CreateJobConfigReq, versionId int64, savepointPath string) error {
	runJobsDesp := model.RunJobDescription{JobId: req.JobId, RunType: constants.JOB_RUN_TYPE_RUN, JobConfigVersion: versionId,
		StartMode: constants.START_MODE_LATEST, SavepointPath: savepointPath}

	runJobsDespArray := make([]model.RunJobDescription, 0)
	runJobsDespArray = append(runJobsDespArray, runJobsDesp)
	jobRunReq := &model.RunJobsReq{AppId: req.AppId, Uin: req.Uin, SubAccountUin: req.SubAccountUin,
		RequestId: uuid.NewRandom().String(), Region: req.Region, Version: "1.0", WorkSpaceId: req.WorkSpaceId, IsSupOwner: 1,
		RunJobDescriptions: runJobsDespArray}
	errCode, msg, _ := service7.DoRunJobs(jobRunReq, true)
	if errCode != controller.OK {
		logger.Errorf("Failed to start job, with serialId:%s, with appId:%d, with error msg :%s", req.JobId, req.AppId, msg)
		return errors.New("Failed to start job")
	}
	return nil
}

func DoCreateExternalJobConfig(reqData *model.CreateExternalJobReq) (status int64, msg string, resp *model2.CreateJobConfigRsp) {
	err := CreateExternalJobConfig(reqData)
	if err != nil {
		logger.Errorf("%s: Failed to create external job with jobName %s , error %+v", reqData.RequestId, reqData.Name, err)
		return controller.SYSERR, err.Error(), nil
	}
	return controller.SUCCESS, controller.NULL, &model2.CreateJobConfigRsp{}
}

func BuildJobInstance(reqData *model.CreateExternalJobReq, newJobConfig *table5.JobConfig) (*table2.JobInstance, error) {
	jobInstance := &table2.JobInstance{}
	jobInstance.JobId = newJobConfig.JobId
	jobInstance.JobConfigId = newJobConfig.Id
	jobInstance.DefaultParallelism = newJobConfig.DefaultParallelism
	jobInstance.Status = constants.JOB_INSTANCE_STATUS_CREATE
	jobInstance.ApplicationId = reqData.Name
	jobInstance.CreateTime = util.GetCurrentTime()
	jobInstance.RunningOrderId = 1
	return jobInstance, nil
}

func CheckResourceLocReadyRetry(refs []*model2.ResourceRefItem) error {
	timeout := time.After(5 * time.Minute)
	ticker := time.NewTicker(3 * time.Second)
	defer ticker.Stop()
	for {
		select {
		case <-timeout:
			logger.Errorf("### CheckResourceLocReady timeout")
			return errors.New("CheckResourceLocReady timeout")
		case <-ticker.C:
			allReady, err := CheckResourceLocReady(refs)
			if err != nil {
				return err
			}
			if allReady {
				return nil
			}
		}
	}
}

func CheckResourceLocReady(refs []*model2.ResourceRefItem) (ready bool, err error) {
	for _, v := range refs {
		resourceConfig, err := service6.GetResourceConfigByResourceRef(v)
		if err != nil {
			return false, err
		}
		if resourceConfig.Status != constants.RESOURCE_CONF_STATUS_ACTIVE {
			logger.Errorf("### resourceConfig: %+v  not Ready", resourceConfig)
			return false, nil
		}
	}
	return true, nil
}

func BuildResourceLoc(reqData *model.CreateExternalJobReq, reqJobData *model.CreateJobReq, clusterGroupId int64,
	itemSpaceId string, systemResourceRefItems []*model2.ResourceRefItem, publicDependency string) []*model2.ResourceRefItem {
	jarDependencies := reqData.JarDependencies
	jarShipToSystem := reqData.JarShipToSystem
	if len(jarDependencies) == 0 && len(jarShipToSystem) == 0 && len(publicDependency) == 0 {
		return nil
	}
	clusters, err := service4.ListClusters(clusterGroupId)
	if err != nil || len(clusters) == 0 {
		logger.Errorf("Failed to ListClusters: %d, with errors:%+v", clusterGroupId, err)
		return nil
	}
	cosBucket := clusters[0].DefaultCOSBucket

	combined := jarDependencies
	if len(jarDependencies) > 0 && len(jarShipToSystem) > 0 {
		combined += ";"
	}
	combined += jarShipToSystem

	resourceRefs := make([]*model2.ResourceRefItem, 0)
	createResourceReq := &model4.CreateResourceReq{
		RequestBase: apiv3.RequestBase{
			AppId:         int64(reqJobData.AppId),
			SubAccountUin: reqJobData.SubAccountUin,
			Uin:           reqJobData.Uin,
			Region:        reqJobData.Region,
		},
		ResourceType: constants.RESOURCE_TYPE_JAR,
		FolderId:     "root",
	}

	//设置新作业的ItemSpaceId
	itemTable, err := item_space.GetItemSpaceByItemSpaceId(itemSpaceId, int64(reqJobData.AppId))
	if err != nil {
		logger.Errorf("BuildResourceLoc: GetItemSpaceByItemSpaceId error: %+v", err)
		return nil
	}
	for _, v := range strings.Split(combined, ";") {
		if len(v) == 0 {
			continue
		}
		resourceLoc := &model3.ResourceLocation{StorageType: constants.RESOURCE_TYPE_JAR}

		//  jar 作业 主程序 resource type:1
		usageType := constants.RESOURCE_REF_USAGE_TYPE_DEPENDENCY_JAR
		if v == jarDependencies {
			usageType = constants.RESOURCE_REF_USAGE_TYPE_MAIN
		} else if !strings.HasSuffix(v, ".jar") {
			usageType = constants.RESOURCE_REF_USAGE_TYPE_DEPENDENCY
		}

		if strings.HasPrefix(v, "/") {
			v = strings.TrimPrefix(v, "/")
		}

		param := &model3.ParamType{
			Bucket: cosBucket,
			Path:   v,
			Region: reqJobData.Region,
		}
		resourceLoc.Param = param

		resourceRefItem, err := service1.DescribeResourceConfigByPath(v, cosBucket)
		if err != nil {
			logger.Errorf("Path: %s, Bucket: %s, Failed to DescribeResourceConfigByPath , error %+v", v, cosBucket, err)
		}

		// resourceRefItem 已存在
		if resourceRefItem == nil {
			logger.Infof("##DescribeResourceConfigByPath by path:%s, cosBucket:%s is empty", v, cosBucket)
			createResourceReq.ResourceLoc = resourceLoc
			createResourceReq.Name = filepath.Base(v)
			_, resourceId, _, versionId, err := service6.SaveNewResource(createResourceReq, itemTable.Id)
			if err != nil {
				logger.Errorf("BuildResourceLoc: failed to save new resource error: %+v", err)
				continue
			}

			resourceRefItem = &model2.ResourceRefItem{ResourceId: resourceId, Version: versionId}
		}
		resourceRefItem.Type = usageType
		resourceRefs = append(resourceRefs, resourceRefItem)
	}
	resourceRefs = append(resourceRefs, systemResourceRefItems...)

	if len(publicDependency) > 0 {
		nameFilters := make([]string, 0)
		if strings.Contains(publicDependency, constants.COMMA) {
			nameFilters = strings.Split(publicDependency, constants.COMMA)
		} else if strings.Contains(publicDependency, constants.SEMICOLON) {
			nameFilters = strings.Split(publicDependency, constants.SEMICOLON)
		}
		if len(nameFilters) > 0 {
			service6.ListResources(int64(reqJobData.AppId), reqJobData.Region, []int64{}, []string{}, nameFilters, []int{}, []string{}, []string{}, false, 0, 100, -1, -1, []int64{itemTable.Id})
			publicDependencyResources, err := service6.ListResources(int64(reqJobData.AppId), reqJobData.Region, []int64{}, []string{}, nameFilters, []int{}, []string{}, []string{}, false, 0, 100, -1, -1, []int64{itemTable.Id})
			if err != nil {
				logger.Errorf("###ListResources error ", err)
			}
			if publicDependencyResources != nil && len(publicDependencyResources) > 0 {
				seen := make(map[string]struct{}) // 用于记录已出现的 ResourceName
				for _, publicDependencyResource := range publicDependencyResources {
					if _, exists := seen[publicDependencyResource.ResourceName]; !exists {
						usageType := constants.RESOURCE_REF_USAGE_TYPE_DEPENDENCY_JAR
						if !strings.HasSuffix(publicDependencyResource.ResourceName, ".jar") {
							usageType = constants.RESOURCE_REF_USAGE_TYPE_DEPENDENCY
						}
						publicResourceRefItem := &model2.ResourceRefItem{ResourceId: publicDependencyResource.ResourceId, Type: usageType}
						// 获取资源配置
						resourceConfig, err := service6.GetResourceConfigByResourceId(publicDependencyResource.Id)
						if err != nil {
							logger.Errorf("###GetResourceConfigByResourceId by id:%+v,  error ", publicDependencyResource.Id, err)
							continue
						}
						seen[publicDependencyResource.ResourceName] = struct{}{}
						publicResourceRefItem.Version = resourceConfig.VersionId
						resourceRefs = append(resourceRefs, publicResourceRefItem)
					}
				}
			}
		}
	}
	return resourceRefs
}
