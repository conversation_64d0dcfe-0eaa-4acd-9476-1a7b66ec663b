package role_info

import (
	"math/rand"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/role_info"
	"time"
	timer "tencentcloud.com/tstream_galileo/src/tstream_cc/service/timer"
)

/**
 * Created by <PERSON> on 2021/12/27 8:52 下午.
 * At tencent
 */

func DoDescribeSynchronousVersion(req *model.DescribeSynchronousVersionReq) (string, string, interface{}) {
	rand.Seed(time.Now().UnixNano())
	intn := rand.Intn(1000)
	time.Sleep(time.Duration(intn) * time.Millisecond)
	rsp := &model.DescribeSynchronousVersionRsp{}
	rsp.Version = time.Now().UnixNano()
	/**
	  * 自定义角色，额外触发一次数据同步，不然页面看起来会有问题，比如在上海删除一个角色，删除成功后，页面其实还会展示
	  * 因为 删除调用的 广州数据库， 查询是 上海， 上海同步广州数据库有个延时
	  * 所以需要 主动触发一次
	 */
	timer.RegionSynWithHandlerVar.ProcessSyn()
	return controller.OK, controller.NULL, rsp
}
