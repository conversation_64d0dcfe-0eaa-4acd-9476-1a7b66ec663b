package k8s

import (
	"encoding/json"
	"fmt"
	"k8s.io/apimachinery/pkg/api/errors"
	v12 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/watch"
	"strings"
	v2 "tencentcloud.com/tstream_galileo/src/common/command/apis/oceanus.tencentcloud.com/v1"
	command "tencentcloud.com/tstream_galileo/src/common/command/client/clientset/versioned"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"time"
)

type commandService struct {
	k8sService *K8sService
}

func (this *commandService) watchOnce(watcher watch.Interface, timer *time.Timer) (*v2.Command, error) {
	var err error
	for {
		select {
		case e, ok := <-watcher.ResultChan():
			if !ok {
				logger.Errorf("watcher closed , breaking out.")
				err = fmt.Errorf("unexpected close")
				return nil, err
			}
			if e.Type == watch.Modified {
				rsp := e.Object.(*v2.Command)
				return rsp, nil
			}
			//由于watch会被重启，所以也要监听Add事件
			if e.Type == watch.Added {
				rsp := e.Object.(*v2.Command)
				if rsp.Spec.Rsp != "" {
					return rsp, nil
				}
			}

		case <-timer.C:
			logger.Errorf("watch timeout")
			return nil, fmt.Errorf("timeout error")
		}
	}
	return nil, err
}

func (this *commandService) ExecuteCommandAsync(client *command.Clientset, cmd *v2.Command, timeout int64, handler func(req *v2.Command, rsp *v2.Command, err error)) error {
	if cmd == nil || client == nil {
		return fmt.Errorf("client and cmd is required")
	}
	name := cmd.Name
	namespace := cmd.Namespace
	logger.Debugf("Start watching cmd %s in namespace %s", name, namespace)
	//异步执行
	go func() {
		//清理资源
		var clear = func(cmd *v2.Command, watcher watch.Interface) {
			if watcher != nil {
				watcher.Stop()
				logger.Info("Successfully stopped the watcher")
			}

			if cmd != nil {
				_, err := this.DeleteCommand(client, cmd)
				if err != nil {
					logger.Errorf("delete cmd %s failed,err:%s", name, err.Error())
				}
				logger.Debugf("Successfully deleted the command:%s", cmd.Spec.RequestId)
			}
		}

		var watcher watch.Interface
		timer := time.NewTimer(time.Duration(timeout) * time.Second)
		isFirst := true

		defer func() {
			err := recover()
			if err != nil {
				clear(cmd, watcher)
				handler(cmd, nil, fmt.Errorf("unexpected error:%v ", err))
			}
		}()

		//因为长期Watch，有可能会被TKE主动断开，因此当时间未到时应该主动重试
		for {
			//建立watcher
			var err error
			watcher, err = client.OceanusV1().Commands(namespace).Watch(v12.ListOptions{
				FieldSelector:  "metadata.name=" + name,
				TimeoutSeconds: &timeout,
			})

			if err != nil {
				logger.Errorf("watch cmd %s failed,err:%s", name, err.Error())
				handler(cmd, nil, err)
				return
			}
			//第一次时建立command
			if isFirst {
				_, err = this.ApplyCommand(client, cmd)
				if err != nil {
					logger.Errorf("apply cmd %s failed,err:%s", name, err.Error())
					handler(cmd, nil, err)
					return
				}
				isFirst = false
			}

			rspCmd, err := this.watchOnce(watcher, timer)
			//如果超时，则返回
			if err != nil && strings.Contains(err.Error(), "timeout") {
				clear(cmd, watcher)
				handler(cmd, nil, err)
				return
			}
			//如果获取到cmd，则返回
			if rspCmd != nil && err == nil {
				clear(rspCmd, watcher)
				handler(cmd, rspCmd, nil)
				return
			}
			//其他错误则重启watch
			watcher.Stop()
		}
	}()
	return nil
}

func (this *commandService) ExecuteCommandSync(client *command.Clientset, cmd *v2.Command, timeout int64) (*v2.Command, error) {
	if timeout > 60 {
		msg := fmt.Sprintf("timeout is not allowed to exceed 60 seconds in sync mode")
		logger.Error(msg)
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", fmt.Errorf(msg))
	}
	timeout = 60
	name := cmd.Name
	namespace := cmd.Namespace
	logger.Debugf("start watching cmd %s in namespace %s", name, namespace)
	watcher, err := client.OceanusV1().Commands(namespace).Watch(v12.ListOptions{
		FieldSelector:  "metadata.name=" + name,
		TimeoutSeconds: &timeout,
	})
	if err != nil {
		logger.Errorf("watch cmd %s failed,err:%s", name, err.Error())
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	defer func() {
		watcher.Stop()
		_, err := this.DeleteCommand(client, cmd)
		if err != nil {
			logger.Errorf("delete cmd %s failed,err:%s", name, err.Error())
		}
	}()

	_, err = this.ApplyCommand(client, cmd)
	// 已存在时也会报错, 报错忽略即可
	if err != nil && !strings.Contains(err.Error(), "already exists") {
		logger.Errorf("apply cmd %s failed,err:%s", name, err.Error())
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	for {
		e := <-watcher.ResultChan()
		if e.Object == nil {
			msg := fmt.Sprintf("%s execution timeout,try again or check the input", name)
			logger.Error(msg)
			return nil, errorcode.FailedOperationCode_ExecuteCommandTimeout.ReplaceDesc(msg)
		}
		if e.Type == watch.Modified {
			rsp := e.Object.(*v2.Command)
			return rsp, nil
		}
	}
}

func (this *commandService) DeleteCommand(client *command.Clientset, delCmd *v2.Command) (idDel bool, err error) {
	name := delCmd.Name
	namespace := delCmd.Namespace
	intf := client.OceanusV1().Commands(namespace)
	_, err = intf.Get(name, v12.GetOptions{})
	if err != nil {
		if !errors.IsNotFound(err) {
			return false, errorcode.NewStackError(errorcode.InternalErrorCode, err.Error(), err)
		}
	} else {
		logger.Debugf("Delete Command %s in namespace %s", name, namespace)
		if err = intf.Delete(name, &v12.DeleteOptions{}); err != nil {
			return false, errorcode.NewStackError(errorcode.InternalErrorCode, err.Error(), err)
		}
	}
	return true, nil
}

func (this *commandService) ApplyCommand(client *command.Clientset, newCmd *v2.Command) (*v2.Command, error) {
	name := newCmd.Name
	namespace := newCmd.Namespace
	intf := client.OceanusV1().Commands(namespace)
	cmd, err := intf.Get(name, v12.GetOptions{})
	if err != nil {
		if !errors.IsNotFound(err) {
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, err.Error(), err)
		}
		//如果已经存在，就先删除再新增
	} else {
		logger.Debugf("Delete Command %s in namespace %s", name, namespace)
		if err = intf.Delete(name, &v12.DeleteOptions{}); err != nil {
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, err.Error(), err)
		}
	}
	logger.Debugf("Create Command %s in namespace %s", name, namespace)
	if cmd, err = intf.Create(newCmd); err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	b, _ := json.Marshal(cmd)
	logger.Debugf("ApplyCommand %s", string(b))
	return cmd, nil
}
