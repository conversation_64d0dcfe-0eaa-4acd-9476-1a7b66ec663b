package apps

import (
	admissionv1 "k8s.io/api/admissionregistration/v1"
	appsV1 "k8s.io/api/apps/v1"
	autoscalingV2 "k8s.io/api/autoscaling/v2"
	coreV1 "k8s.io/api/core/v1"
	networkingV1 "k8s.io/api/networking/v1"
	rbacV1 "k8s.io/api/rbac/v1"
	v12 "k8s.io/api/scheduling/v1"
	v13 "k8s.io/api/storage/v1"
	apiExtensionsV1 "k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1"
	"k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1beta1"
	"k8s.io/apiextensions-apiserver/pkg/client/clientset/clientset"
	"k8s.io/client-go/kubernetes"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
)

type Client interface {
	KubeConfig() []byte
	K8sService() *k8s.K8sService
	ClientSet() *kubernetes.Clientset
	ClientSetEx() *clientset.Clientset
}

// App 代表一种k8s的资源， 可以是 Deployment, 也可以是 DaemonSet， StatefulSet , CRD, 或者 ServiceAccount
// 我们部署k8s资源的时候，通常步骤
//  1. 从七彩石上获取模板
//  2. 根据集群信息生成模板的参数
//  3. 渲染模板，反序列成k8s的struct, 然后调用对应的api部署
type App interface {
	// App  Deployment, StatefulSet, DaemonSet, ServiceAccount
	AppType() interface{}
	// Params 渲染模板需要的参数，如果不需要参数，则返回 nil, nil
	Params() (interface{}, error)
	// Decode 根据参数，模板渲染，解析得到的 AppType
	Decode(params, into interface{}) (interface{}, error)
	// Transform 更改Decode之后的对象
	Transform(s Client, v interface{}) (interface{}, error)
	// Apply 部署对应的k8s资源
	Apply(s Client, v interface{}) (interface{}, error)
	// Ready 检查该资源是否完成
	Ready(s Client, v interface{}) (bool, error)
}

type ApplyReadyApp struct {
}

func (p *ApplyReadyApp) AppType() interface{} {
	return nil
}
func (p *ApplyReadyApp) Params() (interface{}, error) {
	return nil, nil
}
func (p *ApplyReadyApp) Decode(_, _ interface{}) (interface{}, error) {
	return nil, nil
}
func (p *ApplyReadyApp) Transform(_ Client, _ interface{}) (interface{}, error) {
	return nil, nil
}

type ApplyApp struct {
	ApplyReadyApp
}

func (p *ApplyApp) Ready(_ Client, _ interface{}) (bool, error) {
	return true, nil
}

type ReadyApp struct {
	ApplyReadyApp
}

func (p *ReadyApp) Apply(_ Client, _ interface{}) (interface{}, error) {
	return nil, nil
}

type nilParams struct {
}

func (p *nilParams) Params() (interface{}, error) {
	return nil, nil
}

type DecodeFunc func(into interface{}) (interface{}, error)
type funcDecode struct {
	f DecodeFunc
}

func (d *funcDecode) Decode(_, into interface{}) (interface{}, error) {
	return d.f(into)
}

// ServiceAccount k8s的ServiceAccount
type ServiceAccount struct {
}

func (s *ServiceAccount) AppType() interface{} {
	return &coreV1.ServiceAccount{}
}

func (s *ServiceAccount) Transform(_ Client, v interface{}) (interface{}, error) {
	return v, nil
}

func (s *ServiceAccount) Apply(c Client, v interface{}) (interface{}, error) {
	return c.K8sService().ApplyServiceAccount(c.ClientSet(), v.(*coreV1.ServiceAccount))
}

func (s *ServiceAccount) Ready(_ Client, _ interface{}) (bool, error) {
	return true, nil
}

type ClusterRoleBinding struct {
	nilParams
	funcDecode
}

func NewClusterRoleBinding(f DecodeFunc) App {
	return &ClusterRoleBinding{
		funcDecode: funcDecode{f: f},
	}
}

func (c *ClusterRoleBinding) AppType() interface{} {
	return &rbacV1.ClusterRoleBinding{}
}

func (c *ClusterRoleBinding) Transform(_ Client, v interface{}) (interface{}, error) {
	return v, nil
}

func (c *ClusterRoleBinding) Apply(s Client, v interface{}) (interface{}, error) {
	return s.K8sService().ApplyClusterRoleBinding(s.ClientSet(), v.(*rbacV1.ClusterRoleBinding))
}

func (c *ClusterRoleBinding) Ready(_ Client, _ interface{}) (bool, error) {
	return true, nil
}

type RoleBinding struct {
}

func (c *RoleBinding) AppType() interface{} {
	return &rbacV1.RoleBinding{}
}

func (c *RoleBinding) Transform(_ Client, v interface{}) (interface{}, error) {
	return v, nil
}

func (c *RoleBinding) Apply(s Client, v interface{}) (interface{}, error) {
	return s.K8sService().ApplyRoleBinding(s.ClientSet(), v.(*rbacV1.RoleBinding))
}

func (c *RoleBinding) Ready(_ Client, _ interface{}) (bool, error) {
	return true, nil
}

type Role struct {
}

func (c *Role) AppType() interface{} {
	return &rbacV1.Role{}
}

func (c *Role) Transform(_ Client, v interface{}) (interface{}, error) {
	return v, nil
}

func (c *Role) Apply(s Client, v interface{}) (interface{}, error) {
	return s.K8sService().ApplyRole(s.ClientSet(), v.(*rbacV1.Role))
}

func (c *Role) Ready(_ Client, _ interface{}) (bool, error) {
	return true, nil
}

type ClusterRole struct {
	nilParams
	funcDecode
}

func NewClusterRole(f DecodeFunc) App {
	return &ClusterRole{
		funcDecode: funcDecode{f: f},
	}
}

func (c *ClusterRole) AppType() interface{} {
	return &rbacV1.ClusterRole{}
}

func (c *ClusterRole) Transform(_ Client, v interface{}) (interface{}, error) {
	return v, nil
}

func (c *ClusterRole) Apply(s Client, v interface{}) (interface{}, error) {
	return s.K8sService().ApplyClusterRole(s.ClientSet(), v.(*rbacV1.ClusterRole))
}

func (c *ClusterRole) Ready(_ Client, _ interface{}) (bool, error) {
	return true, nil
}

type Deployment struct {
}

func (d *Deployment) AppType() interface{} {
	return &appsV1.Deployment{}
}

func (d *Deployment) Transform(_ Client, v interface{}) (interface{}, error) {
	return v, nil
}

func (d *Deployment) Apply(s Client, v interface{}) (interface{}, error) {
	return s.K8sService().ApplyDeploymentAppsV1(s.ClientSet(), v.(*appsV1.Deployment))
}

func (d *Deployment) Ready(s Client, v interface{}) (bool, error) {
	return s.K8sService().DeploymentAppsV1Ready(s.ClientSet(), v.(*appsV1.Deployment))
}

type DaemonSet struct {
}

func (d *DaemonSet) AppType() interface{} {
	return &appsV1.DaemonSet{}
}

func (d *DaemonSet) Transform(_ Client, v interface{}) (interface{}, error) {
	return v, nil
}

func (d *DaemonSet) Apply(s Client, v interface{}) (interface{}, error) {
	return s.K8sService().ApplyDaemonSet(s.ClientSet(), v.(*appsV1.DaemonSet))
}

func (d *DaemonSet) Ready(s Client, v interface{}) (bool, error) {
	return s.K8sService().DaemonSetReady(s.ClientSet(), v.(*appsV1.DaemonSet))
}

type Secret struct {
	nilParams
	funcDecode
}

func NewSecret(f DecodeFunc) App {
	return &Secret{
		funcDecode: funcDecode{f: f},
	}
}

func (c *Secret) AppType() interface{} {
	return &coreV1.Secret{}
}

func (c *Secret) Transform(_ Client, v interface{}) (interface{}, error) {
	return v, nil
}

func (c *Secret) Apply(s Client, v interface{}) (interface{}, error) {
	return s.K8sService().ApplySecrets(s.ClientSet(), v.(*coreV1.Secret))
}

func (c *Secret) Ready(s Client, v interface{}) (bool, error) {
	return s.K8sService().SecretsReady(s.ClientSet(), v.(*coreV1.Secret))
}

type AdmissionCrd struct {
}

func (c *AdmissionCrd) AppType() interface{} {
	return &admissionv1.MutatingWebhookConfiguration{}
}

func (c *AdmissionCrd) Transform(_ Client, v interface{}) (interface{}, error) {
	return v, nil
}

func (c *AdmissionCrd) Apply(s Client, v interface{}) (interface{}, error) {
	return s.K8sService().ApplyAdmissionCrd(s.ClientSet(), v.(*admissionv1.MutatingWebhookConfiguration))
}

func (c *AdmissionCrd) Ready(s Client, v interface{}) (bool, error) {
	return s.K8sService().AdmissionCrdReady(s.ClientSet(), v.(*admissionv1.MutatingWebhookConfiguration))
}

type Crd struct {
}

func (c *Crd) AppType() interface{} {
	return &apiExtensionsV1.CustomResourceDefinition{}
}

func (c *Crd) Transform(_ Client, v interface{}) (interface{}, error) {
	return v, nil
}

func (c *Crd) Apply(s Client, v interface{}) (interface{}, error) {
	return s.K8sService().ApplyCrd(s.ClientSetEx(), v.(*apiExtensionsV1.CustomResourceDefinition))
}

func (c *Crd) Ready(s Client, v interface{}) (bool, error) {
	return s.K8sService().CrdReady(s.ClientSetEx(), v.(*apiExtensionsV1.CustomResourceDefinition))
}

type CrdV1beta1 struct {
}

func (c *CrdV1beta1) AppType() interface{} {
	return &v1beta1.CustomResourceDefinition{}
}

func (c *CrdV1beta1) Transform(_ Client, v interface{}) (interface{}, error) {
	return v, nil
}

func (c *CrdV1beta1) Apply(s Client, v interface{}) (interface{}, error) {
	return s.K8sService().ApplyCrdV1Beta1(s.ClientSetEx(), v.(*v1beta1.CustomResourceDefinition))
}

func (c *CrdV1beta1) Ready(s Client, v interface{}) (bool, error) {
	return s.K8sService().CrdV1Beta1Ready(s.ClientSetEx(), v.(*v1beta1.CustomResourceDefinition))
}

type NetworkPolicy struct {
	nilParams
	funcDecode
}

func NewNetworkPolicyApp(f DecodeFunc) App {
	return &NetworkPolicy{
		funcDecode: funcDecode{f: f},
	}
}

func (n *NetworkPolicy) AppType() interface{} {
	return &networkingV1.NetworkPolicy{}
}

func (n *NetworkPolicy) Transform(_ Client, v interface{}) (interface{}, error) {
	return v, nil
}

func (n *NetworkPolicy) Apply(s Client, v interface{}) (interface{}, error) {
	return s.K8sService().ApplyNetworkPolicy(s.ClientSet(), v.(*networkingV1.NetworkPolicy))
}

func (n *NetworkPolicy) Ready(_ Client, _ interface{}) (bool, error) {
	return true, nil
}

type ResourceQuota struct {
}

func (n *ResourceQuota) AppType() interface{} {
	return &coreV1.ResourceQuota{}
}

func (n *ResourceQuota) Apply(s Client, v interface{}) (interface{}, error) {
	return s.K8sService().ApplyResourceQuota(s.ClientSet(), v.(*coreV1.ResourceQuota))
}

func (n *ResourceQuota) Transform(_ Client, v interface{}) (interface{}, error) {
	return v, nil
}

func (n *ResourceQuota) Ready(_ Client, _ interface{}) (bool, error) {
	return true, nil
}

type Namespace struct {
}

func (n *Namespace) AppType() interface{} {
	return &coreV1.Namespace{}
}

func (n *Namespace) Transform(_ Client, v interface{}) (interface{}, error) {
	return v, nil
}

func (n *Namespace) Apply(s Client, v interface{}) (interface{}, error) {
	return s.K8sService().ApplyNamespace(s.ClientSet(), v.(*coreV1.Namespace))
}

func (n *Namespace) Ready(_ Client, _ interface{}) (bool, error) {
	return true, nil
}

type StorageClass struct {
}

func (n *StorageClass) AppType() interface{} {
	return &v13.StorageClass{}
}

func (n *StorageClass) Transform(_ Client, v interface{}) (interface{}, error) {
	return v, nil
}

func (n *StorageClass) Apply(s Client, v interface{}) (interface{}, error) {
	return s.K8sService().ApplyStorageClass(s.ClientSet(), v.(*v13.StorageClass))
}

func (n *StorageClass) Ready(_ Client, _ interface{}) (bool, error) {
	return true, nil
}

type PriorityClass struct {
	nilParams
	funcDecode
}

func NewPriorityClassApp(f DecodeFunc) App {
	return &PriorityClass{
		funcDecode: funcDecode{f: f},
	}
}

func (n *PriorityClass) AppType() interface{} {
	return &v12.PriorityClass{}
}

func (n *PriorityClass) Transform(_ Client, v interface{}) (interface{}, error) {
	return v, nil
}

func (n *PriorityClass) Apply(s Client, v interface{}) (interface{}, error) {
	return s.K8sService().ApplyPriorityClass(s.ClientSet(), v.(*v12.PriorityClass))
}

func (n *PriorityClass) Ready(_ Client, _ interface{}) (bool, error) {
	return true, nil
}

type StatefulSet struct {
}

func (s *StatefulSet) AppType() interface{} {
	return &appsV1.StatefulSet{}
}

func (s *StatefulSet) Transform(_ Client, v interface{}) (interface{}, error) {
	return v, nil
}

func (s *StatefulSet) Apply(c Client, v interface{}) (interface{}, error) {
	return c.K8sService().ApplyStatefulSet(c.ClientSet(), v.(*appsV1.StatefulSet))
}

func (s *StatefulSet) Ready(c Client, v interface{}) (bool, error) {
	return c.K8sService().StatefulSetReady(c.ClientSet(), v.(*appsV1.StatefulSet))
}

type HorizontalPodAutoscaler struct {
}

func (h *HorizontalPodAutoscaler) AppType() interface{} {
	return &autoscalingV2.HorizontalPodAutoscaler{}
}

func (h *HorizontalPodAutoscaler) Transform(_ Client, v interface{}) (interface{}, error) {
	return v, nil
}

func (h *HorizontalPodAutoscaler) Apply(c Client, v interface{}) (interface{}, error) {
	return c.K8sService().ApplyHorizontalPodAutoscaler(c.ClientSet(), v.(*autoscalingV2.HorizontalPodAutoscaler))
}

func (h *HorizontalPodAutoscaler) Ready(c Client, v interface{}) (bool, error) {
	return c.K8sService().HorizontalPodAutoscalerReady(c.ClientSet(), v.(*autoscalingV2.HorizontalPodAutoscaler))
}
