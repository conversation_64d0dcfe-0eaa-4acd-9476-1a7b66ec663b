package apps

import (
	"k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
)

type ServiceOptions struct {
	name      string
	namespace string
	protocol  string
	port      int32
	targetPort      int32
	owner     []metav1.OwnerReference
	labels    map[string]string
	selector  map[string]string
}

type AssignServiceOption func(o *ServiceOptions)

func newServiceOptions(opts ...AssignServiceOption) ServiceOptions {
	options := ServiceOptions{
		namespace: constants.OCEANUS_NAMESPACE,
		protocol:  "TCP",
	}
	for _, opt := range opts {
		opt(&options)
	}
	return options
}

func ServiceWithName(name string) AssignServiceOption {
	return func(o *ServiceOptions) {
		o.name = name
	}
}

func ServiceWithNamespace(namespace string) AssignServiceOption {
	return func(o *ServiceOptions) {
		o.namespace = namespace
	}
}

func ServiceWithProtocol(protocol string) AssignServiceOption {
	return func(o *ServiceOptions) {
		o.protocol = protocol
	}
}

func ServiceWithOwnerReference(owner []metav1.OwnerReference) AssignServiceOption {
	return func(o *ServiceOptions) {
		o.owner = owner
	}
}

func ServiceWithLabels(labels map[string]string) AssignServiceOption {
	return func(o *ServiceOptions) {
		o.labels = labels
	}
}

func ServiceWithSelector(selector map[string]string) AssignServiceOption {
	return func(o *ServiceOptions) {
		o.selector = selector
	}
}

func ServiceWithPort(port int32) AssignServiceOption {
	return func(o *ServiceOptions) {
		o.port = port
	}
}

func ServiceWithTargetPort(targetPort int32) AssignServiceOption {
	return func(o *ServiceOptions) {
		o.targetPort = targetPort
	}
}

type Service struct {
	nilParams
	opt *ServiceOptions
}

func (s *Service) AppType() interface{} {
	return &v1.Service{}
}

func (s *Service) Decode(params, into interface{}) (interface{}, error) {
	srv := into.(*v1.Service)
	srv.Name = s.opt.name
	srv.Namespace = s.opt.namespace
	if len(s.opt.owner) > 0 {
		srv.OwnerReferences = s.opt.owner
	}
	srv.Labels = s.opt.labels
	srv.Spec.Ports = []v1.ServicePort{{Port: s.opt.port, TargetPort: intstr.IntOrString{IntVal: s.opt.targetPort}, Protocol: v1.Protocol(s.opt.protocol)}}
	srv.Spec.Selector = s.opt.selector
	return srv, nil
}

func (s *Service) Transform(_ Client, v interface{}) (interface{}, error) {
	return v, nil
}

func (s *Service) Apply(c Client, v interface{}) (interface{}, error) {
	return c.K8sService().ApplyService(c.ClientSet(), v.(*v1.Service))
}

func (s *Service) Ready(_ Client, _ interface{}) (bool, error) {
	return true, nil
}

func NewService(opts ...AssignServiceOption) App {
	option := newServiceOptions(opts...)
	return &Service{
		opt: &option,
	}
}

type ServiceBuilder struct {
	opt []AssignServiceOption
}

func (s *ServiceBuilder) Build() App {
	return NewService(s.opt...)
}

func (s *ServiceBuilder) WithOption(opts ...AssignServiceOption) *ServiceBuilder {
	s.opt = append(s.opt, opts...)
	return s
}

func NewServiceBuilder(opts ...AssignServiceOption) *ServiceBuilder {
	builder := &ServiceBuilder{}
	builder.opt = make([]AssignServiceOption, 0)
	builder.opt = append(builder.opt, opts...)
	return builder
}
