package k8s

import (
	"encoding/json"
	"fmt"
	v15 "git.code.oa.com/tke/log-agent/pkg/apis/cls.cloud.tencent.com/v1"
	logconfig "git.code.oa.com/tke/log-agent/pkg/generated/clientset/versioned"
	admissionv1 "k8s.io/api/admissionregistration/v1"
	v1 "k8s.io/api/apps/v1"
	autoscalingV2 "k8s.io/api/autoscaling/v2beta2"
	v13 "k8s.io/api/core/v1"
	"k8s.io/api/extensions/v1beta1"
	networkingV1 "k8s.io/api/networking/v1"
	v14 "k8s.io/api/rbac/v1"
	v16 "k8s.io/api/scheduling/v1"
	v17 "k8s.io/api/storage/v1"
	v1beta12 "k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1beta1"
	"k8s.io/apimachinery/pkg/types"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"

	apiExtensionsV1 "k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1"

	"k8s.io/apiextensions-apiserver/pkg/client/clientset/clientset"
	"k8s.io/apimachinery/pkg/api/errors"
	v12 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	metrics "k8s.io/metrics/pkg/client/clientset/versioned"
	"sync"
	command "tencentcloud.com/tstream_galileo/src/common/command/client/clientset/versioned"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config"
)

var (
	k8sService *K8sService
)

type instanceIdResult struct {
	Err        error
	InstanceId string
}

type K8sService struct {
	FlinkService   *flinkService
	IngressService *ingressService
	CommandService *commandService
}

func (s *K8sService) NewMetricClient(kubeConfig []byte) (client *metrics.Clientset, err error) {
	kc, err := s.NewConfig(kubeConfig)
	if err != nil {
		return nil, err
	}
	if client, err = metrics.NewForConfig(kc); err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	} else {
		return client, nil
	}
}

func (s *K8sService) NewConfig(kubeConfig []byte) (config *rest.Config, err error) {
	kc, err := clientcmd.NewClientConfigFromBytes(kubeConfig)
	if err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	config, err = kc.ClientConfig()
	if err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	// 忽略证书校验 避免 JNS GW 裁撤时连接失败
	config.Insecure = true
	config.CAData = nil

	return config, nil
}

func (s *K8sService) NewClient(kubeConfig []byte) (client *kubernetes.Clientset, err error) {
	kc, err := s.NewConfig(kubeConfig)
	if err != nil {
		return nil, err
	}

	if client, err = kubernetes.NewForConfig(kc); err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	} else {
		return client, nil
	}
}

func (s *K8sService) NewCommandClient(kubeConfig []byte) (client *command.Clientset, err error) {
	kc, err := s.NewConfig(kubeConfig)
	if err != nil {
		return nil, err
	}

	if client, err = command.NewForConfig(kc); err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	} else {
		return client, nil
	}
}

func (s *K8sService) NewExtensionClient(kubeConfig []byte) (client *clientset.Clientset, err error) {
	kc, err := s.NewConfig(kubeConfig)
	if err != nil {
		return nil, err
	}

	if client, err = clientset.NewForConfig(kc); err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	} else {
		return client, nil
	}
}

func (s *K8sService) NewLogConfigClient(kubeConfig []byte) (client *logconfig.Clientset, err error) {
	kc, err := s.NewConfig(kubeConfig)
	if err != nil {
		return nil, err
	}

	if client, err = logconfig.NewForConfig(kc); err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	} else {
		return client, nil
	}
}

func (s *K8sService) ApplyService(client *kubernetes.Clientset, vs *v13.Service) (*v13.Service, error) {
	si := client.CoreV1().Services(vs.Namespace)
	ss, err := si.Get(vs.Name, v12.GetOptions{})
	if err != nil {
		if !errors.IsNotFound(err) {
			return nil, errorcode.InternalErrorCode.NewWithErr(err)
		}
		logger.Infof("Create service %s in namespace %s", vs.Name, vs.Namespace)
		ss, err = si.Create(vs)
		if err != nil {
			return nil, errorcode.InternalErrorCode.NewWithErr(err)
		}
		return ss, nil
	}
	ss, err = si.Update(ss)
	if err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}
	return ss, nil
}

func (s *K8sService) ApplyServiceAccount(client *kubernetes.Clientset, newSa *v13.ServiceAccount) (instanceId string, err error) {
	namespace := newSa.ObjectMeta.Namespace
	name := newSa.ObjectMeta.Name

	intf := client.CoreV1().ServiceAccounts(namespace)
	sa, err := intf.Get(name, v12.GetOptions{})
	if err != nil {
		if !errors.IsNotFound(err) {
			return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
		logger.Infof("Create serviceaccount %s in namespace %s", name, namespace)
		if sa, err = intf.Create(newSa); err != nil {
			return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
	} else {
		if len(sa.Secrets) > 0 {
			newSa.Secrets = make([]v13.ObjectReference, 0, len(sa.Secrets))
			for _, s := range sa.Secrets {
				newSa.Secrets = append(newSa.Secrets, s)
			}
		}
		logger.Infof("Update serviceaccount %s in namespace %s", name, namespace)
		if sa, err = intf.Update(newSa); err != nil {
			return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
	}

	b, _ := json.Marshal(sa)
	logger.Infof("ApplyServiceAccount %s", string(b))

	return sa.Name, nil
}

func (s *K8sService) ApplyRole(client *kubernetes.Clientset, newRole *v14.Role) (instanceId string, err error) {
	namespace := newRole.ObjectMeta.Namespace
	intf := client.RbacV1().Roles(namespace)
	name := newRole.Name
	role, err := intf.Get(name, v12.GetOptions{})
	if err != nil {
		if !errors.IsNotFound(err) {
			return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
		logger.Infof("Create Role %s", name)
		if role, err = intf.Create(newRole); err != nil {
			return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
	} else {
		logger.Infof("Update Role %s", name)
		if role, err = intf.Update(newRole); err != nil {
			return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
	}

	b, _ := json.Marshal(role)
	logger.Infof("ApplyRole %s", string(b))

	return role.Name, nil
}

func (s *K8sService) ApplyClusterRole(client *kubernetes.Clientset, newClusterRole *v14.ClusterRole) (instanceId string, err error) {
	intf := client.RbacV1().ClusterRoles()

	name := newClusterRole.Name
	clusterRole, err := intf.Get(name, v12.GetOptions{})
	if err != nil {
		if !errors.IsNotFound(err) {
			return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
		logger.Infof("Create ClusterRole %s", name)
		if clusterRole, err = intf.Create(newClusterRole); err != nil {
			return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
	} else {
		logger.Infof("Update ClusterRole %s", name)
		if clusterRole, err = intf.Update(newClusterRole); err != nil {
			return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
	}

	b, _ := json.Marshal(clusterRole)
	logger.Infof("ApplyDeploymentExtV1Beta1 %s", string(b))

	return clusterRole.Name, nil
}

func (s *K8sService) ApplyRoleBinding(client *kubernetes.Clientset, newRoleBinding *v14.RoleBinding) (instanceId string, err error) {
	name := newRoleBinding.Name
	namespace := newRoleBinding.Namespace

	intf := client.RbacV1().RoleBindings(namespace)
	roleBinding, err := intf.Get(name, v12.GetOptions{})
	if err != nil {
		if !errors.IsNotFound(err) {
			return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
		logger.Infof("Create rolebinding %s in namespace %s", name, namespace)
		if roleBinding, err = intf.Create(newRoleBinding); err != nil {
			return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
	} else {
		logger.Infof("Update rolebinding %s in namespace %s", name, namespace)
		if roleBinding, err = intf.Update(newRoleBinding); err != nil {
			return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
	}

	b, _ := json.Marshal(roleBinding)
	logger.Infof("ApplyRoleBinding %s", string(b))

	return roleBinding.Name, nil
}

func (s *K8sService) ApplyClusterRoleBinding(client *kubernetes.Clientset, newClusterRoleBinding *v14.ClusterRoleBinding) (instanceId string, err error) {
	name := newClusterRoleBinding.Name
	namespace := newClusterRoleBinding.Namespace

	intf := client.RbacV1().ClusterRoleBindings()
	clusterRoleBinding, err := intf.Get(name, v12.GetOptions{})
	if err != nil {
		if !errors.IsNotFound(err) {
			return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
		logger.Infof("Create clusterrolebinding %s in namespace %s", name, namespace)
		if clusterRoleBinding, err = intf.Create(newClusterRoleBinding); err != nil {
			return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
	} else {
		logger.Infof("Update clusterrolebinding %s in namespace %s", name, namespace)
		if clusterRoleBinding, err = intf.Update(newClusterRoleBinding); err != nil {
			return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
	}

	b, _ := json.Marshal(clusterRoleBinding)
	logger.Infof("ApplyTillerClusterRoleBinding %s", string(b))

	return clusterRoleBinding.Name, nil
}

func (s *K8sService) DeleteClusterRoleBinding(client *kubernetes.Clientset, name string) (err error) {
	err = client.RbacV1().ClusterRoleBindings().Delete(name, &v12.DeleteOptions{})
	if err != nil {
		logger.Errorf("delete clusterRoleBinding failed，name:%s,err: %+v", name, err)
		return nil
	} else {
		return nil
	}
}

func (s *K8sService) ListDeployment(client *kubernetes.Clientset, namespace string, opts v12.ListOptions) (
	dList *v1.DeploymentList, err error) {
	intf := client.AppsV1().Deployments(namespace)
	dList, err = intf.List(opts)
	if err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}
	return
}

func (s *K8sService) GetDeploymentAppsV1(client *kubernetes.Clientset, namespace, name string) (*v1.Deployment, error) {
	intf := client.AppsV1().Deployments(namespace)
	deploy, err := intf.Get(name, v12.GetOptions{})
	if err != nil {
		if !errors.IsNotFound(err) {
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
		return nil, nil
	}
	b, _ := json.Marshal(deploy)
	logger.Infof("GetDeploymentAppsV1 %s", string(b))

	return deploy, nil
}

func (s *K8sService) GetStatefulSetAppsV1(client *kubernetes.Clientset, namespace, name string) (*v1.StatefulSet, error) {
	intf := client.AppsV1().StatefulSets(namespace)
	deploy, err := intf.Get(name, v12.GetOptions{})
	if err != nil {
		if !errors.IsNotFound(err) {
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
		return nil, nil
	}
	b, _ := json.Marshal(deploy)
	logger.Infof("GetStatefulSetAppsV1 %s", string(b))

	return deploy, nil
}

func (s *K8sService) GetDeploymentExtV1Beta1(client *kubernetes.Clientset, namespace, name string) (*v1beta1.Deployment, error) {
	intf := client.ExtensionsV1beta1().Deployments(namespace)
	deploy, err := intf.Get(name, v12.GetOptions{})
	if err != nil {
		if !errors.IsNotFound(err) {
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
		return nil, nil
	}
	b, _ := json.Marshal(deploy)
	logger.Infof("GetDeploymentExtV1Beta1 %s", string(b))

	return deploy, nil
}

func (s *K8sService) ApplyDeploymentExtV1Beta1(client *kubernetes.Clientset, newDeploy *v1beta1.Deployment) (*v1beta1.Deployment, error) {
	namespace := newDeploy.Namespace
	name := newDeploy.Name

	intf := client.ExtensionsV1beta1().Deployments(namespace)
	deploy, err := intf.Get(name, v12.GetOptions{})
	if err != nil {
		if !errors.IsNotFound(err) {
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
		logger.Infof("Create deploy %s in namespace %s", name, namespace)
		if deploy, err = intf.Create(newDeploy); err != nil {
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
	} else {
		logger.Infof("Update deploy %s in namespace %s", name, namespace)
		if deploy, err = intf.Update(newDeploy); err != nil {
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
	}

	b, _ := json.Marshal(deploy)
	logger.Infof("ApplyDeploymentExtV1Beta1 %s", string(b))

	return deploy, nil
}

func (s *K8sService) DeploymentExtV1Beta1Ready(client *kubernetes.Clientset, deploy *v1beta1.Deployment) (ready bool, err error) {
	namespace := deploy.Namespace
	name := deploy.Name

	intf := client.ExtensionsV1beta1().Deployments(namespace)
	if deploy, err = intf.Get(name, v12.GetOptions{}); err != nil {
		return false, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	} else {
		if deploy.Status.UnavailableReplicas != 0 {
			logger.Infof("deploy not ready: %s", name)
		}
		return deploy.Status.UnavailableReplicas == 0, nil
	}
}

func (s *K8sService) ApplyDeploymentAppsV1(client *kubernetes.Clientset, newDeploy *v1.Deployment) (*v1.Deployment, error) {
	namespace := newDeploy.Namespace
	name := newDeploy.Name

	intf := client.AppsV1().Deployments(namespace)
	deploy, err := intf.Get(name, v12.GetOptions{})
	if err != nil {
		if !errors.IsNotFound(err) {
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
		logger.Infof("Create deploy %s in namespace %s", name, namespace)
		if deploy, err = intf.Create(newDeploy); err != nil {
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
	} else {
		logger.Infof("Update deploy %s in namespace %s", name, namespace)
		if deploy, err = intf.Update(newDeploy); err != nil {
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
	}

	b, _ := json.Marshal(deploy)
	logger.Infof("ApplyDeploymentAppsV1 %s", string(b))

	return deploy, nil
}

func (s *K8sService) DeploymentAppsV1Ready(client *kubernetes.Clientset, deploy *v1.Deployment) (ready bool, err error) {
	namespace := deploy.Namespace
	name := deploy.Name

	intf := client.AppsV1().Deployments(namespace)
	if deploy, err = intf.Get(name, v12.GetOptions{}); err != nil {
		return false, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	} else {
		if deploy.Status.UnavailableReplicas != 0 {
			logger.Infof("deploy not ready: %s", name)
		}
		return deploy.Status.UnavailableReplicas == 0, nil
	}
}

func (s *K8sService) ApplyAdmissionCrd(client *kubernetes.Clientset, newCrd *admissionv1.MutatingWebhookConfiguration) (
	*admissionv1.MutatingWebhookConfiguration, error) {
	name := newCrd.Name

	intf := client.AdmissionregistrationV1().MutatingWebhookConfigurations()
	crd, err := intf.Get(name, v12.GetOptions{})
	if err != nil {
		if !errors.IsNotFound(err) {
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
		logger.Infof("Create AdmissionCrd %s", name)
		if crd, err = intf.Create(newCrd); err != nil {
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
	} else {
		logger.Infof("Update AdmissionCrd %s ", name)
		newCrd.ObjectMeta.ResourceVersion = crd.ObjectMeta.ResourceVersion
		if crd, err = intf.Update(newCrd); err != nil {
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
	}

	b, _ := json.Marshal(crd)
	logger.Infof("ApplyCrd %s", string(b))

	return crd, nil
}

func (s *K8sService) AdmissionCrdReady(client *kubernetes.Clientset, crd *admissionv1.MutatingWebhookConfiguration) (
	ready bool, err error) {
	name := crd.Name
	intf := client.AdmissionregistrationV1().MutatingWebhookConfigurations()
	crd, err = intf.Get(name, v12.GetOptions{})
	if err != nil {
		logger.Errorf("AdmissionCrdReady error %+v", err)
		return false, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	} else {
		return true, nil
	}
}

func (s *K8sService) ApplyCrd(client *clientset.Clientset, newCrd *apiExtensionsV1.CustomResourceDefinition) (
	*apiExtensionsV1.CustomResourceDefinition, error) {
	name := newCrd.Name

	intf := client.ApiextensionsV1().CustomResourceDefinitions()
	crd, err := intf.Get(name, v12.GetOptions{})
	if err != nil {
		if !errors.IsNotFound(err) {
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
		logger.Infof("Create Crd %s", name)
		if crd, err = intf.Create(newCrd); err != nil {
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
	} else {
		logger.Infof("Update Crd %s ", name)
		newCrd.ObjectMeta.ResourceVersion = crd.ObjectMeta.ResourceVersion
		if crd, err = intf.Update(newCrd); err != nil {
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
	}

	b, _ := json.Marshal(crd)
	logger.Infof("ApplyCrd %s", string(b))

	return crd, nil
}

func (s *K8sService) CrdReady(client *clientset.Clientset, crd *apiExtensionsV1.CustomResourceDefinition) (
	ready bool, err error) {
	name := crd.Name

	intf := client.ApiextensionsV1().CustomResourceDefinitions()
	crd, err = intf.Get(name, v12.GetOptions{})
	if err != nil {
		return false, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	} else {
		for _, cond := range crd.Status.Conditions {
			if cond.Status != apiExtensionsV1.ConditionTrue {
				logger.Infof("Crd not ready: %s", name)
				return false, nil
			}
		}
		return true, nil
	}
}

func (s *K8sService) ApplyCrdV1Beta1(client *clientset.Clientset, newCrd *v1beta12.CustomResourceDefinition) (
	*v1beta12.CustomResourceDefinition, error) {
	name := newCrd.Name

	intf := client.ApiextensionsV1beta1().CustomResourceDefinitions()
	crd, err := intf.Get(name, v12.GetOptions{})
	if err != nil {
		if !errors.IsNotFound(err) {
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
		logger.Infof("Create Crd %s", name)
		if crd, err = intf.Create(newCrd); err != nil {
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
	} else {
		logger.Infof("Update Crd %s ", name)
		newCrd.ObjectMeta.ResourceVersion = crd.ObjectMeta.ResourceVersion
		if crd, err = intf.Update(newCrd); err != nil {
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
	}

	b, _ := json.Marshal(crd)
	logger.Infof("ApplyCrd %s", string(b))

	return crd, nil
}

func (s *K8sService) CrdV1Beta1Ready(client *clientset.Clientset, crd *v1beta12.CustomResourceDefinition) (
	ready bool, err error) {
	name := crd.Name

	intf := client.ApiextensionsV1beta1().CustomResourceDefinitions()
	crd, err = intf.Get(name, v12.GetOptions{})
	if err != nil {
		return false, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	} else {
		for _, cond := range crd.Status.Conditions {
			if cond.Status != v1beta12.ConditionTrue {
				logger.Infof("Crd not ready: %s", name)
				return false, nil
			}
		}
		return true, nil
	}
}

func (s *K8sService) ApplyStatefulSet(client *kubernetes.Clientset, sts *v1.StatefulSet) (*v1.StatefulSet, error) {
	ns := client.AppsV1().StatefulSets(sts.Namespace)
	ss, err := ns.Get(sts.Name, v12.GetOptions{})
	if err != nil {
		if !errors.IsNotFound(err) {
			return nil, errorcode.InternalErrorCode.NewWithErr(err)
		}
		if ss, err = ns.Create(sts); err != nil {
			return nil, errorcode.InternalErrorCode.NewWithErr(err)
		}
		return ss, nil
	}
	ss, err = ns.Update(sts)
	if err != nil {
		return ss, errorcode.InternalErrorCode.NewWithErr(err)
	}
	return ss, nil
}

func (s *K8sService) ApplyStorageClass(client *kubernetes.Clientset, sc *v17.StorageClass) (*v17.StorageClass, error) {
	sci := client.StorageV1().StorageClasses()
	nSC, err := sci.Get(sc.Name, v12.GetOptions{})
	if err != nil {
		if !errors.IsNotFound(err) {
			return nil, errorcode.InternalErrorCode.NewWithErr(err)
		}
		return sci.Create(sc)
	}
	if nSC.VolumeBindingMode == sc.VolumeBindingMode {
		return nSC, nil
	}

	if err = sci.Delete(nSC.Name, &v12.DeleteOptions{}); err != nil {
		return nSC, errorcode.InternalErrorCode.NewWithErr(err)
	}
	return sci.Create(sc)
}

func (s *K8sService) ApplyHorizontalPodAutoscaler(client *kubernetes.Clientset, sts *autoscalingV2.HorizontalPodAutoscaler) (*autoscalingV2.HorizontalPodAutoscaler, error) {
	ns := client.AutoscalingV2beta2().HorizontalPodAutoscalers(sts.Namespace)
	ss, err := ns.Get(sts.Name, v12.GetOptions{})
	if err != nil {
		if !errors.IsNotFound(err) {
			return nil, errorcode.InternalErrorCode.NewWithErr(err)
		}
		if ss, err = ns.Create(sts); err != nil {
			return nil, errorcode.InternalErrorCode.NewWithErr(err)
		}
		return ss, nil
	}
	ss, err = ns.Update(sts)
	if err != nil {
		return ss, errorcode.InternalErrorCode.NewWithErr(err)
	}
	return ss, nil
}

func (s *K8sService) DeleteConfigMap(client *kubernetes.Clientset, sts *v13.ConfigMap) (bool, error) {
	ns := client.CoreV1().ConfigMaps(sts.Namespace)
	_, err := ns.Get(sts.Name, v12.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			return true, nil
		} else {
			return false, errorcode.InternalErrorCode.NewWithErr(err)
		}
	}
	if err = ns.Delete(sts.Name, &v12.DeleteOptions{}); err != nil {
		return false, errorcode.InternalErrorCode.NewWithErr(err)
	}
	return true, nil
}

func (s *K8sService) DeleteService(client *kubernetes.Clientset, sts *v13.Service) (bool, error) {
	ns := client.CoreV1().Services(sts.Namespace)
	_, err := ns.Get(sts.Name, v12.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			return true, nil
		} else {
			return false, errorcode.InternalErrorCode.NewWithErr(err)
		}
	}
	if err = ns.Delete(sts.Name, &v12.DeleteOptions{}); err != nil {
		return false, errorcode.InternalErrorCode.NewWithErr(err)
	}
	return true, nil
}

func (s *K8sService) DeleteDeployments(client *kubernetes.Clientset, namespace string, opts v12.ListOptions) (deleted []string, err error) {
	deleted = make([]string, 0)
	intf := client.AppsV1().Deployments(namespace)
	deploymentList, err := intf.List(opts)
	if err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	if len(deploymentList.Items) == 0 {
		return deleted, nil
	}
	result := make(chan error, len(deploymentList.Items))
	wg := &sync.WaitGroup{}
	wg.Add(len(deploymentList.Items))

	for _, deployment := range deploymentList.Items {
		name := deployment.Name
		go func() {
			defer wg.Done()
			err := intf.Delete(name, &v12.DeleteOptions{})
			result <- err
		}()
		deleted = append(deleted, name)
	}
	go func() {
		wg.Wait()
		close(result)
	}()

	for err := range result {
		if err != nil {
			return deleted, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
	}
	return deleted, nil
}

func (s *K8sService) DeleteDeployment(client *kubernetes.Clientset, sts *v1.Deployment) (bool, error) {
	ns := client.AppsV1().Deployments(sts.Namespace)
	_, err := ns.Get(sts.Name, v12.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			return true, nil
		} else {
			return false, errorcode.InternalErrorCode.NewWithErr(err)
		}
	}
	if err = ns.Delete(sts.Name, &v12.DeleteOptions{}); err != nil {
		return false, errorcode.InternalErrorCode.NewWithErr(err)
	}
	return true, nil
}

func (s *K8sService) DeleteStatefulSet(client *kubernetes.Clientset, sts *v1.StatefulSet) (bool, error) {
	ns := client.AppsV1().StatefulSets(sts.Namespace)
	_, err := ns.Get(sts.Name, v12.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			return true, nil
		} else {
			return false, errorcode.InternalErrorCode.NewWithErr(err)
		}
	}
	if err = ns.Delete(sts.Name, &v12.DeleteOptions{}); err != nil {
		return false, errorcode.InternalErrorCode.NewWithErr(err)
	}
	return true, nil
}

func (s *K8sService) DeleteDaemonSet(client *kubernetes.Clientset, sts *v1.DaemonSet) (bool, error) {
	ns := client.AppsV1().DaemonSets(sts.Namespace)
	_, err := ns.Get(sts.Name, v12.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			return true, nil
		} else {
			return false, errorcode.InternalErrorCode.NewWithErr(err)
		}
	}
	if err = ns.Delete(sts.Name, &v12.DeleteOptions{}); err != nil {
		return false, errorcode.InternalErrorCode.NewWithErr(err)
	}
	return true, nil
}

func (s *K8sService) StatefulSetReady(client *kubernetes.Clientset, sts *v1.StatefulSet) (bool, error) {

	intf := client.AppsV1().StatefulSets(sts.Namespace)
	ds, err := intf.Get(sts.Name, v12.GetOptions{})
	if err != nil {
		return false, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	return ds.Status.ReadyReplicas == *ds.Spec.Replicas, nil
}

func (s *K8sService) HorizontalPodAutoscalerReady(client *kubernetes.Clientset, sts *autoscalingV2.HorizontalPodAutoscaler) (bool, error) {
	intf := client.AutoscalingV2beta2().HorizontalPodAutoscalers(sts.Namespace)
	ds, err := intf.Get(sts.Name, v12.GetOptions{})
	if err != nil || ds == nil {
		return false, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	return true, nil
}

func (s *K8sService) ApplyDaemonSet(client *kubernetes.Clientset, newDs *v1.DaemonSet) (*v1.DaemonSet, error) {
	namespace := newDs.Namespace
	name := newDs.Name

	intf := client.AppsV1().DaemonSets(namespace)
	ds, err := intf.Get(name, v12.GetOptions{})
	if err != nil {
		if !errors.IsNotFound(err) {
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
		logger.Infof("Create daemonset %s in namespace %s", name, namespace)
		if ds, err = intf.Create(newDs); err != nil {
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
	} else {
		logger.Infof("Update daemonset %s in namespace %s", name, namespace)
		if ds, err = intf.Update(newDs); err != nil {
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
	}

	b, _ := json.Marshal(ds)
	logger.Infof("ApplyDaemonSet %s", string(b))

	return ds, nil
}

func (s *K8sService) DaemonSetReady(client *kubernetes.Clientset, ds *v1.DaemonSet) (ready bool, err error) {
	namespace := ds.Namespace
	name := ds.Name

	intf := client.AppsV1().DaemonSets(namespace)
	if ds, err := intf.Get(name, v12.GetOptions{}); err != nil {
		return false, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	} else {
		if ds.Status.NumberUnavailable != 0 {
			logger.Infof("daemonet not ready: %s", name)
		}
		return ds.Status.NumberUnavailable == 0, nil
	}
}

func (s *K8sService) ApplyResourceQuota(client *kubernetes.Clientset, resourceQuota *v13.ResourceQuota) (*v13.ResourceQuota, error) {
	namespace := resourceQuota.Namespace
	name := resourceQuota.Name

	intf := client.CoreV1().ResourceQuotas(namespace)
	rs, err := intf.Get(name, v12.GetOptions{})
	if err != nil {
		if !errors.IsNotFound(err) {
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
		logger.Infof("Create resourceQuota %s in namespace %s", name, namespace)
		if rs, err = intf.Create(resourceQuota); err != nil {
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
	} else {
		logger.Infof("Update resourceQuota %s in namespace %s", name, namespace)
		if rs, err = intf.Update(resourceQuota); err != nil {
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
	}

	b, _ := json.Marshal(rs)
	logger.Infof("ApplyResourceQuota %s", string(b))

	return rs, nil
}

func (s *K8sService) ApplyNamespace(client *kubernetes.Clientset, newNs *v13.Namespace) (instanceId string, err error) {
	name := newNs.Name
	labels := map[string]string{
		"name": name,
	}
	newNs.Labels = labels
	intf := client.CoreV1().Namespaces()
	ns, err := intf.Get(name, v12.GetOptions{})
	if err != nil {
		if !errors.IsNotFound(err) {
			return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
		logger.Infof("Create namespace %s", name)
		if ns, err = intf.Create(newNs); err != nil {
			return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
	} else {
		logger.Infof("Update namespace %s", name)
		if ns, err = intf.Update(newNs); err != nil {
			return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
	}

	b, _ := json.Marshal(ns)
	logger.Infof("ApplyNamespace %s", string(b))

	return ns.Name, nil
}

func DeleteNamespace(client *kubernetes.Clientset, name string) (err error) {

	intf := client.CoreV1().Namespaces()
	_, err = intf.Get(name, v12.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			return nil
		} else {
			return errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
	}
	logger.Infof("Delete namespace %s", name)
	err = intf.Delete(name, &v12.DeleteOptions{})
	if err != nil {
		return errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	return nil
}

func (s *K8sService) ApplyPriorityClass(client *kubernetes.Clientset, newPc *v16.PriorityClass) (instanceId string, err error) {
	name := newPc.Name

	intf := client.SchedulingV1().PriorityClasses()
	pc, err := intf.Get(name, v12.GetOptions{})
	if err != nil {
		if !errors.IsNotFound(err) {
			return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
		logger.Infof("Create PriorityClas %s", name)
		if pc, err = intf.Create(newPc); err != nil {
			return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
	} else {
		logger.Infof("Update PriorityClas %s", name)
		if pc, err = intf.Update(newPc); err != nil {
			return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
	}

	b, _ := json.Marshal(pc)
	logger.Infof("ApplyPriorityClas %s", string(b))

	return pc.Name, nil
}

func (s *K8sService) ApplyNetworkPolicy(client *kubernetes.Clientset, newNp *networkingV1.NetworkPolicy) (instanceId string, err error) {
	name := newNp.Name

	intf := client.NetworkingV1().NetworkPolicies(newNp.Namespace)
	np, err := intf.Get(name, v12.GetOptions{})
	if err != nil {
		if !errors.IsNotFound(err) {
			return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
		logger.Infof("Create NetworkPolicy %s", name)
		if np, err = intf.Create(newNp); err != nil {
			return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
	} else {
		logger.Infof("Update NetworkPolicy %s", name)
		if np, err = intf.Update(newNp); err != nil {
			return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
	}

	b, _ := json.Marshal(np)
	logger.Infof("ApplyNetworkPolicy %s", string(b))

	return np.Name, nil
}

func (s *K8sService) GetConfigMap(client *kubernetes.Clientset, namespace, name string) (cm *v13.ConfigMap, err error) {
	intf := client.CoreV1().ConfigMaps(namespace)
	cm, err = intf.Get(name, v12.GetOptions{})
	if err != nil {
		if !errors.IsNotFound(err) {
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
		return nil, nil
	}

	b, _ := json.Marshal(cm)
	logger.Infof("GetConfigMap %s", string(b))

	return
}

func (s *K8sService) ApplyConfigMap(client *kubernetes.Clientset, newCm *v13.ConfigMap) (instanceId *v13.ConfigMap, err error) {
	name := newCm.Name
	namespace := newCm.Namespace

	intf := client.CoreV1().ConfigMaps(namespace)
	cm, err := intf.Get(name, v12.GetOptions{})
	if err != nil {
		if !errors.IsNotFound(err) {
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
		logger.Infof("Create ConfigMap %s in namespace %s", name, namespace)
		if cm, err = intf.Create(newCm); err != nil {
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
	} else {
		logger.Infof("Update ConfigMap %s in namespace %s", name, namespace)
		if cm, err = intf.Update(newCm); err != nil {
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
	}

	b, _ := json.Marshal(cm)
	logger.Infof("ApplyConfigMap %s", string(b))

	return cm, nil
}

func (s *K8sService) DeletePod(client *kubernetes.Clientset, namespace string, podName string) (err error) {
	intf := client.CoreV1().Pods(namespace)
	err = intf.Delete(podName, &v12.DeleteOptions{})
	if err != nil {
		return errorcode.InternalErrorCode.NewWithErr(err)
	}
	return
}

func (s *K8sService) DeletePods(client *kubernetes.Clientset, namespace string, opts v12.ListOptions) (deleted []string, err error) {
	deleted = make([]string, 0)
	intf := client.CoreV1().Pods(namespace)
	podList, err := intf.List(opts)

	if err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	if len(podList.Items) == 0 {
		return deleted, nil
	}
	result := make(chan error, len(podList.Items))
	wg := &sync.WaitGroup{}
	wg.Add(len(podList.Items))

	for _, pod := range podList.Items {
		name := pod.Name
		go func() {
			defer wg.Done()
			err := intf.Delete(name, &v12.DeleteOptions{})
			result <- err
		}()

		deleted = append(deleted, name)
	}

	go func() {
		wg.Wait()
		close(result)
	}()

	for err := range result {
		if err != nil {
			return deleted, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
	}

	return deleted, nil
}

func (s *K8sService) ApplySecrets(client *kubernetes.Clientset, newSecrets *v13.Secret) (instanceId string, err error) {
	namespace := newSecrets.Namespace
	name := newSecrets.Name

	intf := client.CoreV1().Secrets(namespace)
	secrets, err := intf.Get(name, v12.GetOptions{})
	if err != nil {
		if !errors.IsNotFound(err) {
			return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
		logger.Infof("Create secrets %s in namespace %s", name, namespace)
		if secrets, err = intf.Create(newSecrets); err != nil {
			return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
	} else {
		logger.Infof("Update secrets %s in namespace %s", name, namespace)
		if secrets, err = intf.Update(newSecrets); err != nil {
			return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
	}

	b, _ := json.Marshal(secrets)
	logger.Infof("ApplySecrets %s", string(b))

	return secrets.Name, nil
}

func (s *K8sService) DeleteSecrets(client *kubernetes.Clientset, namespace, name string) (err error) {
	intf := client.CoreV1().Secrets(namespace)
	secrets, err := intf.Get(name, v12.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			return nil
		}
		logger.Errorf("Get secrets %s, err %+v", name, err)
		return errorcode.InternalErrorCode.NewWithErr(err)
	}
	logger.Infof("Delete secrets %s in namespace %s", name, namespace)
	err = intf.Delete(secrets.Name, &v12.DeleteOptions{})

	return err
}

func (s *K8sService) SecretsReady(client *kubernetes.Clientset, newSecrets *v13.Secret) (ready bool, err error) {
	namespace := newSecrets.Namespace
	name := newSecrets.Name

	intf := client.CoreV1().Secrets(namespace)
	_, err = intf.Get(name, v12.GetOptions{})
	if err != nil {
		logger.Errorf("SecretsReady error %+v", err)
		return false, err
	} else {
		return true, nil
	}
}

func (s *K8sService) ApplyIngress(client *kubernetes.Clientset, newIng *v1beta1.Ingress) (instanceId string, err error) {
	namespace := newIng.Namespace
	name := newIng.Name

	intf := client.ExtensionsV1beta1().Ingresses(namespace)
	ing, err := intf.Get(name, v12.GetOptions{})
	if err != nil {
		if !errors.IsNotFound(err) {
			return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
		logger.Infof("Create ingress %s in namespace %s", name, namespace)
		if ing, err = intf.Create(newIng); err != nil {
			return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
	} else {
		logger.Infof("Update ingress %s in namespace %s", name, namespace)

		// 保留ingress规则，避免升级集群时flinkui失效
		newIng.Spec.Rules = ing.Spec.Rules
		if ing, err = intf.Update(newIng); err != nil {
			return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
	}

	b, _ := json.Marshal(ing)
	logger.Infof("ApplyIngress %s", string(b))

	return ing.Name, nil
}

func (s *K8sService) ApplyLogConfig(client *logconfig.Clientset, newLc *v15.LogConfig) (instanceId string, err error) {
	name := newLc.Name

	intf := client.ClsV1().LogConfigs()
	lc, err := intf.Get(name, v12.GetOptions{})
	if err != nil {
		if !errors.IsNotFound(err) {
			return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
		logger.Infof("Create LogConfig %s", name)
		if lc, err = intf.Create(newLc); err != nil {
			return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
	} else {
		logger.Infof("Update LogConfig %s", name)
		newLc.ResourceVersion = lc.ResourceVersion
		if lc, err = intf.Update(newLc); err != nil {
			return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
	}

	b, _ := json.Marshal(lc)
	logger.Infof("ApplyLogConfig %s", string(b))

	return lc.Name, nil
}

func (s *K8sService) NodeSchedule(client *kubernetes.Clientset, nodeName string, unschedule bool) (node *v13.Node, err error) {
	scheduleSpec := fmt.Sprintf(`{"spec":{"unschedulable":%v}}`, unschedule)
	intf := client.CoreV1().Nodes()
	node, err = intf.Patch(nodeName, types.StrategicMergePatchType, []byte(scheduleSpec))
	if err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}
	return
}

func (s *K8sService) AddNodeLabel(client *kubernetes.Clientset, nodeName string, labels map[string]string) (node *v13.Node, err error) {
	b, _ := json.Marshal(labels)
	logger.Infof("AddNodeLabel, nodeName %s, labels %s", nodeName, string(b))
	labelStr, _ := json.Marshal(labels)
	scheduleSpec := fmt.Sprintf(`{"metadata":{"labels":%s}}`, labelStr)
	intf := client.CoreV1().Nodes()
	node, err = intf.Patch(nodeName, types.StrategicMergePatchType, []byte(scheduleSpec))
	if err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}
	b, _ = json.Marshal(node)
	logger.Infof("AddNodeLabel, nodeName %s, node %s", nodeName, string(b))
	return
}

func (s *K8sService) DeletePVC(client *kubernetes.Clientset, namespace string, pvcName string) (pvName string, err error) {
	intf := client.CoreV1().PersistentVolumeClaims(namespace)
	pvc, err := intf.Get(pvcName, v12.GetOptions{})
	if err != nil {
		if !errors.IsNotFound(err) {
			return "", errorcode.InternalErrorCode.NewWithErr(err)
		}
		return "", nil
	}
	err = intf.Delete(pvcName, &v12.DeleteOptions{})
	if err != nil {
		return "", errorcode.InternalErrorCode.NewWithErr(err)
	}
	return pvc.Spec.VolumeName, nil
}

func (s *K8sService) DeletePV(client *kubernetes.Clientset, pvName string) (err error) {
	intf := client.CoreV1().PersistentVolumes()
	err = intf.Delete(pvName, &v12.DeleteOptions{})
	if err != nil {
		return errorcode.InternalErrorCode.NewWithErr(err)
	}
	return
}

func (s *K8sService) DeleteNode(client *kubernetes.Clientset, nodeName string, options *v12.DeleteOptions) (node *v13.Node, err error) {
	intf := client.CoreV1().Nodes()
	err = intf.Delete(nodeName, options)
	if err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}
	return
}

func (s *K8sService) ListNode(client *kubernetes.Clientset, opts v12.ListOptions) (nodeList *v13.NodeList, err error) {
	logger.Infof("ListNode, opts [%+v]", opts)

	intf := client.CoreV1().Nodes()
	nodeList, err = intf.List(opts)
	if err != nil {
		logger.Errorf("ListNode, err [%+v]", err)
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}
	return
}

func (s *K8sService) ListPod(client *kubernetes.Clientset, namespace string, opts v12.ListOptions) (
	podList *v13.PodList, err error) {

	logger.Infof("ListPod, namespace [%s], opts [%+v]", namespace, opts)

	podList, err = client.CoreV1().Pods(namespace).List(opts)
	if err != nil {
		logger.Infof("ListPod, err [%+v]", err)
		if !errors.IsNotFound(err) {
			return nil, errorcode.InternalErrorCode.NewWithErr(err)
		}
		return &v13.PodList{}, nil
	}

	logger.Infof("ListPod, pod length %d", len(podList.Items))
	return
}

func (s *K8sService) GetPod(client *kubernetes.Clientset, namespace, podName string) (
	pod *v13.Pod, err error) {
	intf := client.CoreV1().Pods(namespace)
	pod, err = intf.Get(podName, v12.GetOptions{})
	if err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}
	return
}

func (s *K8sService) GetService(client *kubernetes.Clientset, namespace, svcName string) (
	svc *v13.Service, err error) {
	intf := client.CoreV1().Services(namespace)
	svc, err = intf.Get(svcName, v12.GetOptions{})
	if err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}
	return
}

func (s *K8sService) UpdateStatefulSetAppsV1(client *kubernetes.Clientset, namespace string, name string, containerName string, image string) (string, error) {
	intf := client.AppsV1().StatefulSets(namespace)
	ss, err := intf.Get(name, v12.GetOptions{})
	if err != nil {
		msg := fmt.Sprintf("can not get resource, namespace %s, name %s", namespace, name)
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, msg, err)
	}
	containers := &ss.Spec.Template.Spec.Containers
	find := false
	oldImage := ""
	for i := range *containers {
		c := *containers
		if c[i].Name == containerName {
			find = true
			oldImage = c[i].Image
			if oldImage == image {
				return oldImage, nil
			}
			if compatible, err := service.CheckImageCompatible(oldImage, image); err != nil || !compatible {
				msg := fmt.Sprintf("Image not compatible, oldImage %s, newImage %s", oldImage, image)
				return "", errorcode.NewStackError(errorcode.InternalErrorCode, msg, err)
			}
			c[i].Image = image
		}
	}
	if !find {
		msg := fmt.Sprintf("Can not find container %s, in namespace %s, name %s", containerName, namespace, name)
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, msg, err)
	}
	ss, err = intf.Update(ss)
	b, _ := json.Marshal(ss)
	if err != nil {
		msg := fmt.Sprintf("update stateful set error, UpdateStatefulSetAppsV1 %s", string(b))
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, msg, nil)
	}
	logger.Infof("UpdateStatefulSetAppsV1 %s", string(b))
	return oldImage, nil
}

// UpdateDaemonSetAppsV1 更新Kubernetes中的DaemonSet
func (s *K8sService) UpdateDaemonSetAppsV1(client *kubernetes.Clientset, namespace string, name string, containerName string, image string) (string, error) {
	intf := client.AppsV1().DaemonSets(namespace)
	ss, err := intf.Get(name, v12.GetOptions{})
	if err != nil {
		msg := fmt.Sprintf("can not get resource, namespace %s, name %s", namespace, name)
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, msg, err)
	}
	containers := &ss.Spec.Template.Spec.Containers
	find := false
	oldImage := ""
	for i := range *containers {
		c := *containers
		if c[i].Name == containerName {
			find = true
			oldImage = c[i].Image
			if oldImage == image {
				return oldImage, nil
			}
			if compatible, err := service.CheckImageCompatible(oldImage, image); err != nil || !compatible {
				msg := fmt.Sprintf("Image not compatible, oldImage %s, newImage %s", oldImage, image)
				return "", errorcode.NewStackError(errorcode.InternalErrorCode, msg, err)
			}
			c[i].Image = image
		}
	}
	if !find {
		msg := fmt.Sprintf("Can not find container %s, in namespace %s, name %s", containerName, namespace, name)
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, msg, err)
	}
	ss, err = intf.Update(ss)
	b, _ := json.Marshal(ss)
	if err != nil {
		msg := fmt.Sprintf("update daemon set error, UpdateDaemonSetAppsV1 %s", string(b))
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, msg, nil)
	}
	logger.Infof("UpdateDaemonSetAppsV1 %s", string(b))
	return oldImage, nil
}

func (s *K8sService) StatefulSetAppsV1Ready(client *kubernetes.Clientset, namespace string, name string) (ready bool, err error) {
	intf := client.AppsV1().StatefulSets(namespace)
	if ss, err := intf.Get(name, v12.GetOptions{}); err != nil {
		return false, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	} else {
		replicas := *ss.Spec.Replicas
		if ss.Status.ReadyReplicas < replicas {
			return false, nil
		}
		return true, nil
	}
}

func (s *K8sService) CheckZoneReady(client *kubernetes.Clientset, namespace string) (ready bool, err error) {
	intf := client.AppsV1().StatefulSets(namespace)
	if ss, err := intf.Get(constants.ComponentZooKeeper, v12.GetOptions{}); err != nil {
		return false, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	} else {
		replicas := *ss.Spec.Replicas
		if ss.Status.ReadyReplicas <= replicas/2 {
			return false, nil
		}
		return true, nil
	}
}

func (s *K8sService) DeploymentAppsV1Ready1(client *kubernetes.Clientset, namespace string, name string) (ready bool, err error) {
	intf := client.AppsV1().StatefulSets(namespace)
	if ss, err := intf.Get(name, v12.GetOptions{}); err != nil {
		return false, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	} else {
		replicas := *ss.Spec.Replicas
		if ss.Status.ReadyReplicas < replicas {
			return false, nil
		}
		return true, nil
	}
}

func (s *K8sService) StatefulSetContainerImageVersion(client *kubernetes.Clientset, namespace string, name string, containerName string) (string, error) {
	intf := client.AppsV1().StatefulSets(namespace)
	ss, err := intf.Get(name, v12.GetOptions{})
	if err != nil {
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	containers := ss.Spec.Template.Spec.Containers
	for _, c := range containers {
		if c.Name == containerName {
			return c.Image, nil
		}
	}
	return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", nil)
}

func (s *K8sService) DeploymentContainerImageVersion(client *kubernetes.Clientset, namespace string, name string, containerName string) (string, error) {
	intf := client.AppsV1().Deployments(namespace)
	ss, err := intf.Get(name, v12.GetOptions{})
	if err != nil {
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	containers := ss.Spec.Template.Spec.Containers
	for _, c := range containers {
		if c.Name == containerName {
			return c.Image, nil
		}
	}
	return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", nil)
}

func (s *K8sService) NewResourceConfig(group, key string, into interface{}) (err error) {
	return config.DecodeK8sObjectFromRainbowConfig(group, key, into)
}

func (s *K8sService) RenderResourceConfig(yamlTpl string, data, into interface{}) (err error) {
	return config.DecodeK8sObjectFromStrConfigTemplate(yamlTpl, data, into)
}

func (s *K8sService) NewResourceConfigByTemplate(group, key string, data, into interface{}) (err error) {
	return config.DecodeK8sObjectFromRainbowConfigTemplate(group, key, data, into)
}

func NewK8sService() *K8sService {
	k8sService := &K8sService{
		FlinkService:   &flinkService{},
		IngressService: &ingressService{},
		CommandService: &commandService{},
	}
	k8sService.FlinkService.k8sService = k8sService
	k8sService.IngressService.k8sService = k8sService
	k8sService.CommandService.k8sService = k8sService

	return k8sService
}

func GetK8sService() *K8sService {
	if k8sService == nil {
		k8sService = NewK8sService()
	}
	return k8sService
}
