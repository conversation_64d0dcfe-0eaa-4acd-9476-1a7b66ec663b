package k8s

import (
	"context"

	v13 "git.woa.com/tke/logconfig/pkg/apis/cls.cloud.tencent.com/v1"
	logconfig "git.woa.com/tke/logconfig/pkg/generated/clientset/versioned"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cls"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config/configure_center"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/configuration_center"
	service4 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
)

type flinkService struct {
	k8sService *K8sService
}

func (o *flinkService) ApplyControllerLogConfig(client *logconfig.Clientset, region string) (instanceId string, err error) {
	topicId, err := service.GetConfigurationValueByKey(constants.CONF_KEY_CONTROLLER_LOG_TOPIC_ID)
	if err != nil {
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	secretId, secretKey, err := service3.GetSecretIdAndKeyOfScs()
	if err != nil {
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	newLc := &v13.LogConfig{}
	data := &struct {
		TopicId string
	}{
		TopicId: topicId,
	}
	if err := o.k8sService.NewResourceConfigByTemplate(constants.CONF_RAINBOW_GROUP_TKE, constants.CONF_RAINBOW_KEY_OCEANUS_CONTROLLER_LC,
		data, newLc); err != nil {
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	//machineGroups, err := cls.ListTopicMachineGroups(secretId, secretKey, "", region, topicId)
	adapter := &cls.CloudApiAdapter{}
	machineGroups, err := adapter.ListTopicMachineGroups(secretId, secretKey, "", region, topicId)
	if err != nil {
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	// 开新地域并发创建可能会出现竞争冲突，新地域我们会创建集群验证，即可避免该问题
	if len(machineGroups.MachineGroups) > 0 {
		//newLc.Spec.ClsDetail.Mode = "append"
	}

	return o.k8sService.ApplyLogConfig(client, newLc)
}

func (o *flinkService) ApplyRunningLogConfig(region string, client *logconfig.Clientset, topicId string) (
	instanceId string, err error) {
	// append crd instance lc
	conf, err := service4.GetTableService().GetLogConfigCrdConf()
	if err != nil {
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	newLc := &v13.LogConfig{}
	data := &struct {
		Name           string
		BeginningRegex string
		TopicId        string
	}{
		Name:           conf.Crd.Name,
		BeginningRegex: conf.Crd.BeginningRegex,
		TopicId:        topicId,
	}
	_, err = configure_center.CC(region).FlowCC().TkeCC().LogConfigCC().RunningLog(data, newLc)
	if err != nil {
		return "", errorcode.InternalErrorCode.NewWithErr(err)
	}
	// todo 修改为正常
	newLc.ObjectMeta.Name = "oceanus-" + topicId
	newLc.Spec.InputDetail.ContainerStdout.IncludeLabels["logCollect"] = topicId
	instanceId, err = o.k8sService.ApplyLogConfig(client, newLc)
	if err != nil {
		return "", err
	}

	return instanceId, nil
}

// DelRunningLogConfig 删除logConfig
func (o *flinkService) DelRunningLogConfig(kubeConfig, topicId string) error {
	client, err := o.k8sService.NewLogConfigClient([]byte(kubeConfig))
	if err != nil {
		logger.Errorf("get logConfigClient failed with err [%v]", err)
		return err
	}
	logConfigName := "oceanus-" + topicId
	deleteOptions := metav1.DeleteOptions{}
	err = client.ClsV1().LogConfigs().Delete(context.TODO(), logConfigName, deleteOptions)
	if err != nil {
		logger.Errorf("delete logConfig with err [%v]", err)
		return err
	}
	return nil
}
