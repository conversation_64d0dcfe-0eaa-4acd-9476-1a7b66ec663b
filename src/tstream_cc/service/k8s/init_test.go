package k8s

import (
	"flag"
	"fmt"
	logconfig "git.woa.com/tke/logconfig/pkg/generated/clientset/versioned"
	"io/ioutil"
	"k8s.io/apiextensions-apiserver/pkg/client/clientset/clientset"
	"k8s.io/client-go/kubernetes"
	metrics "k8s.io/metrics/pkg/client/clientset/versioned"
	"os"
	"path"
	command "tencentcloud.com/tstream_galileo/src/common/command/client/clientset/versioned"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"testing"
)

var (
	fTestDbUrl      = flag.String("test.db.url", "root:root@tcp(127.0.0.1:3306)/online_galileo?charset=utf8", "")
	fTestKubeConfig = flag.String("test.kube.config", path.Join(os.Getenv("HOME"), ".kube/config"), "")
	fTestNamespace  = flag.String("test.namespace", "default", "")
	fTestDeployment = flag.String("test.deployment", "tiller-deploy", "")
	fTestRegion     = flag.String("test.region", "ap-nanjing", "")
	fTestClusterId  = flag.String("test.cluster.id", "cls-3nkrmbti", "")
	fTestAppid      = flag.Int64("test.appid", 1257058945, "dev appid")
	fTestUser       = flag.String("test.user", "admin", "")
	fTestPasswd     = flag.String("test.passwd", "admin321", "")
	fTestUin        = flag.String("test.uin", "100006386216", "")
	fTestUniqVpcId  = flag.String("test.uniq.vpc.id", "vpc-dkj09k2d", "")
	fTestTopicId    = flag.String("test.topic.id", "", "cls topic id")

	k8sClient          *kubernetes.Clientset
	k8sExtensionClient *clientset.Clientset
	lcClient           *logconfig.Clientset
	cmdClient          *command.Clientset
	metricsClient      *metrics.Clientset
)

func init() {
	testing.Init()
	flag.Parse()

	tx, err := dao.NewDataSourceTransactionManager(*fTestDbUrl)
	if err != nil {
		fmt.Println("darwin Process create DataSourceTransactionManager err", err)
		os.Exit(-1)
	}
	service.SetTxManager(tx)
}

func initK8sClient(t *testing.T) {
	if k8sClient != nil && k8sExtensionClient != nil && lcClient != nil {
		return
	}
	k8sService := GetK8sService()

	if env := os.Getenv("KUBECONFIG"); len(env) > 0 {
		fTestKubeConfig = &env
	}
	kubeConfig, err := ioutil.ReadFile(*fTestKubeConfig)
	if err != nil {
		t.Fatalf("fail to read %s, %v", *fTestKubeConfig, err)
		return
	}

	if k8sClient == nil {
		k8sClient, err = k8sService.NewClient(kubeConfig)
		if err != nil {
			t.Fatalf("fail to new client %v", err)
			return
		}
	}
	if k8sExtensionClient == nil {
		k8sExtensionClient, err = k8sService.NewExtensionClient(kubeConfig)
		if err != nil {
			t.Fatalf("fail to new extension client %v", err)
			return
		}
	}

	if lcClient == nil {
		lcClient, err = k8sService.NewLogConfigClient(kubeConfig)
		if err != nil {
			t.Fatalf("fail to new logconfig client %v", err)
			return
		}
	}

	if cmdClient == nil {
		cmdClient, err = k8sService.NewCommandClient(kubeConfig)
		if err != nil {
			t.Fatalf("fail to new command client %v", err)
			return
		}
	}
	if metricsClient == nil {
		metricsClient, err = k8sService.NewMetricClient(kubeConfig)
		if err != nil {
			t.Fatalf("fail to new command client %v", err)
			return
		}
	}
}
