package k8s

import (
	"context"
	"encoding/base64"
	"strings"
	"sync"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/tke"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"

	"k8s.io/api/extensions/v1beta1"

	networkingV1 "k8s.io/api/networking/v1"
	networkingV1beta1 "k8s.io/api/networking/v1beta1"

	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	v12 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"
	"k8s.io/client-go/kubernetes"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config/configure_center"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/password"
)

type ingressService struct {
	k8sService *K8sService
}

func (this *ingressService) ApplyNginxIngressSecrets(region string, k8sClient *kubernetes.Clientset, user,
	passwd string, clusterGroup *table.ClusterGroup) (instanceId string, err error) {
	passwdService := password.GetPasswordService()
	htpasswd, err := passwdService.GenHtpasswdBBrypt(user, passwd)
	if err != nil {
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	ns := constants.DEFAULT_NAMESPACE
	if clusterGroup.AgentSerialId != "" {
		ns = clusterGroup.SerialId
	}
	newSecrets := &v1.Secret{}
	data := &struct {
		Key       string
		Value     string
		Namespace string
	}{
		Key:       "auth", // 无关紧要
		Value:     base64.StdEncoding.EncodeToString([]byte(htpasswd)),
		Namespace: ns,
	}

	_, err = configure_center.CC(region).FlowCC().TkeCC().IngressCC().Secrets(data, newSecrets)
	if err != nil {
		return "", err
	}

	return this.k8sService.ApplySecrets(k8sClient, newSecrets)
}

func (this *ingressService) ApplyNginxIngress(region string, k8sClient *kubernetes.Clientset, clusterGroup *table.ClusterGroup, cluster *table.Cluster, tke *table2.Tke) (instanceId string,
	err error) {
	if tke.ArchGeneration >= constants.TKE_ARCH_GENERATION_V6 {
		newIng := &networkingV1.Ingress{}

		ns := constants.DEFAULT_NAMESPACE
		if clusterGroup.AgentSerialId != "" {
			ns = clusterGroup.SerialId
		}
		data := &struct {
			Namespace string
		}{
			Namespace: ns,
		}

		_, err = configure_center.CC(region).FlowCC().TkeCC().IngressCC().Ingress(data, newIng)
		if err != nil {
			logger.Errorf("ApplyNginxIngress in ns:%s, error: %v", ns, err)
			return "", err
		}
		existFunc := func(key string, mmap map[string]string) (exist bool) {
			_, exist = mmap[key]
			return exist
		}
		// 新集群创建flinkui路径 为/cluster-xxx/cql-xxx
		annotations := newIng.Annotations
		if constants.EnableStaticMode == cluster.CrossTenantEniMode {
			if existFunc("nginx.ingress.kubernetes.io/configuration-snippet", annotations) {
				annotations["nginx.ingress.kubernetes.io/configuration-snippet"] = "rewrite ^/[^/]+/([^/]+)/(.*) /$2 break;"
			}
		}
		return this.k8sService.ApplyIngress(k8sClient, newIng)
	} else {
		newIng := &v1beta1.Ingress{}

		ns := constants.DEFAULT_NAMESPACE
		if clusterGroup.AgentSerialId != "" {
			ns = clusterGroup.SerialId
		}
		data := &struct {
			Namespace string
		}{
			Namespace: ns,
		}

		_, err = configure_center.CC(region).FlowCC().TkeCC().IngressCC().Ingress(data, newIng)
		if err != nil {
			logger.Errorf("ApplyNginxIngress in ns:%s, error: %v", ns, err)
			return "", err
		}
		existFunc := func(key string, mmap map[string]string) (exist bool) {
			_, exist = mmap[key]
			return exist
		}
		// 新集群创建flinkui路径 为/cluster-xxx/cql-xxx
		annotations := newIng.Annotations
		if constants.EnableStaticMode == cluster.CrossTenantEniMode {
			if existFunc("nginx.ingress.kubernetes.io/configuration-snippet", annotations) {
				annotations["nginx.ingress.kubernetes.io/configuration-snippet"] = "rewrite ^/[^/]+/([^/]+)/(.*) /$2 break;"
			}
		}
		return this.k8sService.ApplyIngressV1beta1(k8sClient, newIng)
	}
}

func (o *ingressService) ApplyIngressAll(region string, k8sClient *kubernetes.Clientset, user,
	passwd string, clusterGroup *table.ClusterGroup, cluster *table.Cluster, tke *table2.Tke) (instanceId string, err error) {
	const endPointNum = 2
	instanceArray := make([]string, 0, endPointNum)
	result := make(chan *instanceIdResult, endPointNum)
	wg := &sync.WaitGroup{}
	wg.Add(endPointNum)

	go func() {
		defer wg.Done()
		name, err := o.ApplyNginxIngressSecrets(region, k8sClient, user, passwd, clusterGroup)
		result <- &instanceIdResult{
			Err:        err,
			InstanceId: name,
		}
	}()

	go func() {
		defer wg.Done()
		name, err := o.ApplyNginxIngress(region, k8sClient, clusterGroup, cluster, tke)
		result <- &instanceIdResult{
			Err:        err,
			InstanceId: name,
		}
	}()

	go func() {
		wg.Wait()
		close(result)
	}()

	for rlt := range result {
		if rlt.Err != nil {
			return "", rlt.Err
		} else {
			instanceArray = append(instanceArray, rlt.InstanceId)
		}
	}

	return strings.Join(instanceArray, ","), nil
}

/**
 * 添加新的Ingress
 */
func (o *ingressService) AddIngressRule(k8sClient *kubernetes.Clientset, ingressInit *networkingV1.Ingress, serviceName string, servicePort int, path string, clusterId int64) error {
	// 判断是否为v1 API
	archGeneration, err := service.GetTableService().GetTkeArchGeneration(clusterId)
	if err != nil {
		logger.Errorf("Failed to get Tke ArchGeneration for cluster %d: %v", clusterId, err)
		return err
	}
	if archGeneration >= constants.TKE_ARCH_GENERATION_V6 {
		// 对于Kubernetes 1.17+版本使用v1 API
		ingress, err := k8sClient.NetworkingV1().Ingresses(ingressInit.Namespace).Get(context.TODO(), ingressInit.Name, v12.GetOptions{})
		if err != nil {
			if errors.IsNotFound(err) {
				logger.Error("Ingress not found, ingressName: %s", ingressInit.Name)
			}
			return err
		}

		pathType := networkingV1.PathTypePrefix
		backend := networkingV1.IngressBackend{
			Service: &networkingV1.IngressServiceBackend{
				Name: serviceName,
				Port: networkingV1.ServiceBackendPort{
					Number: int32(servicePort),
				},
			},
		}

		if len(ingress.Spec.Rules) == 0 || ingress.Spec.Rules[0].HTTP == nil {
			// 刚刚创建的集群则添加一条rule
			ingress.Spec.Rules[0] = networkingV1.IngressRule{
				IngressRuleValue: networkingV1.IngressRuleValue{
					HTTP: &networkingV1.HTTPIngressRuleValue{
						Paths: []networkingV1.HTTPIngressPath{
							{
								Path:     "/" + path,
								PathType: &pathType,
								Backend:  backend,
							},
						},
					},
				},
			}
		} else {
			// 否则直接添加backend
			ingress.Spec.Rules[0].HTTP.Paths = append(ingress.Spec.Rules[0].HTTP.Paths, networkingV1.HTTPIngressPath{
				Backend:  backend,
				Path:     "/" + path,
				PathType: &pathType,
			})
		}

		logger.Infof("start to add ingress with v1 API")
		// 添加ingress
		_, err = k8sClient.NetworkingV1().Ingresses(ingress.Namespace).Update(context.TODO(), ingress, v12.UpdateOptions{})
		logger.Infof("Add ingress end with v1 API")

		if err != nil {
			return err
		}

		return nil
	}
	// 其他情况使用v1beta1
	return o.addIngressRuleV1beta1(k8sClient, ingressInit, serviceName, servicePort, path)
}

/**
 * 针对Kubernetes 1.16及以下版本的v1beta1 API实现
 */
func (o *ingressService) addIngressRuleV1beta1(k8sClient *kubernetes.Clientset, ingressInit *networkingV1.Ingress, serviceName string, servicePort int, path string) error {
	// 转换为v1beta1 Ingress对象
	ingressV1beta1 := &networkingV1beta1.Ingress{
		ObjectMeta: v12.ObjectMeta{
			Name:      ingressInit.Name,
			Namespace: ingressInit.Namespace,
		},
	}

	// 获取Ingress
	ingress, err := k8sClient.NetworkingV1beta1().Ingresses(ingressV1beta1.Namespace).Get(context.TODO(), ingressV1beta1.Name, v12.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			logger.Error("Ingress not found, ingressName: %s", ingressV1beta1.Name)
		}
		return err
	}

	// 构建v1beta1 Backend
	backend := networkingV1beta1.IngressBackend{
		ServiceName: serviceName,
		ServicePort: intstr.IntOrString{
			Type:   intstr.Int,
			IntVal: int32(servicePort),
		},
	}

	if len(ingress.Spec.Rules) == 0 || ingress.Spec.Rules[0].HTTP == nil {
		// 刚刚创建的集群则添加一条rule
		ingress.Spec.Rules = []networkingV1beta1.IngressRule{
			{
				IngressRuleValue: networkingV1beta1.IngressRuleValue{
					HTTP: &networkingV1beta1.HTTPIngressRuleValue{
						Paths: []networkingV1beta1.HTTPIngressPath{
							{
								Path:    "/" + path,
								Backend: backend,
							},
						},
					},
				},
			},
		}
	} else {
		// 否则直接添加backend
		ingress.Spec.Rules[0].HTTP.Paths = append(ingress.Spec.Rules[0].HTTP.Paths, networkingV1beta1.HTTPIngressPath{
			Backend: backend,
			Path:    "/" + path,
		})
	}

	logger.Infof("start to add ingress with v1beta1 API")
	// 添加ingress
	_, err = k8sClient.NetworkingV1beta1().Ingresses(ingress.Namespace).Update(context.TODO(), ingress, v12.UpdateOptions{})
	logger.Infof("Add ingress end with v1beta1 API")

	if err != nil {
		return err
	}

	return nil
}

/**
 * 根据path来删除IngressRule
 */
func (o *ingressService) DeleteIngressRule(k8sClient *kubernetes.Clientset, ingressInit *networkingV1.Ingress, serviceName string, servicePort int, clusterId int64) error {
	// 判断是否为v1 API
	archGeneration, err := service.GetTableService().GetTkeArchGeneration(clusterId)
	if err != nil {
		logger.Errorf("Failed to get Tke ArchGeneration for cluster %d: %v", clusterId, err)
		return err
	}
	if archGeneration >= constants.TKE_ARCH_GENERATION_V6 {
		ingress, err := k8sClient.NetworkingV1().Ingresses(ingressInit.Namespace).Get(context.TODO(), ingressInit.Name, v12.GetOptions{})
		if err != nil {
			if errors.IsNotFound(err) {
				logger.Error("Ingress not found, ingressName: %s", ingressInit.Name)
				return nil
			}
			return err
		}

		// 轮询Rule中的paths删除指定的backend
		found := false
		for i, rule := range ingress.Spec.Rules {
			if rule.HTTP == nil {
				continue
			}
			for j, httpPath := range rule.HTTP.Paths {
				if httpPath.Backend.Service != nil && httpPath.Backend.Service.Name == serviceName && httpPath.Backend.Service.Port.Number == int32(servicePort) {
					rule.HTTP.Paths[j] = rule.HTTP.Paths[len(rule.HTTP.Paths)-1]
					ingress.Spec.Rules[i].HTTP.Paths = rule.HTTP.Paths[:len(rule.HTTP.Paths)-1]
					found = true
					break
				}
			}
			if found {
				if len(ingress.Spec.Rules[i].HTTP.Paths) == 0 {
					ingress.Spec.Rules[i] = ingress.Spec.Rules[len(ingress.Spec.Rules)-1]
					ingress.Spec.Rules = ingress.Spec.Rules[:len(ingress.Spec.Rules)-1]
				}
				if len(ingress.Spec.Rules) == 0 {
					ingress.Spec.Rules = append(ingress.Spec.Rules, networkingV1.IngressRule{})
				}
				break
			}
		}

		_, err = k8sClient.NetworkingV1().Ingresses(ingress.Namespace).Update(context.TODO(), ingress, v12.UpdateOptions{})

		if err != nil {
			return err
		}

		return nil
	}
	// 其他情况使用v1beta1
	return o.deleteIngressRuleV1beta1(k8sClient, ingressInit, serviceName, servicePort)
}

/**
 * 针对Kubernetes 1.16及以下版本的v1beta1 API实现删除IngressRule
 */
func (o *ingressService) deleteIngressRuleV1beta1(k8sClient *kubernetes.Clientset, ingressInit *networkingV1.Ingress, serviceName string, servicePort int) error {
	// 转换为v1beta1 Ingress对象
	ingressV1beta1 := &networkingV1beta1.Ingress{
		ObjectMeta: v12.ObjectMeta{
			Name:      ingressInit.Name,
			Namespace: ingressInit.Namespace,
		},
	}

	// 获取Ingress
	ingress, err := k8sClient.NetworkingV1beta1().Ingresses(ingressV1beta1.Namespace).Get(context.TODO(), ingressV1beta1.Name, v12.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			logger.Error("Ingress not found, ingressName: %s", ingressV1beta1.Name)
			return nil
		}
		return err
	}

	// 轮询Rule中的paths删除指定的backend
	found := false
	for i, rule := range ingress.Spec.Rules {
		if rule.HTTP == nil {
			continue
		}
		for j, httpPath := range rule.HTTP.Paths {
			if httpPath.Backend.ServiceName == serviceName && httpPath.Backend.ServicePort.IntVal == int32(servicePort) {
				rule.HTTP.Paths[j] = rule.HTTP.Paths[len(rule.HTTP.Paths)-1]
				ingress.Spec.Rules[i].HTTP.Paths = rule.HTTP.Paths[:len(rule.HTTP.Paths)-1]
				found = true
				break
			}
		}
		if found {
			if len(ingress.Spec.Rules[i].HTTP.Paths) == 0 {
				ingress.Spec.Rules[i] = ingress.Spec.Rules[len(ingress.Spec.Rules)-1]
				ingress.Spec.Rules = ingress.Spec.Rules[:len(ingress.Spec.Rules)-1]
			}
			if len(ingress.Spec.Rules) == 0 {
				ingress.Spec.Rules = append(ingress.Spec.Rules, networkingV1beta1.IngressRule{})
			}
			break
		}
	}

	_, err = k8sClient.NetworkingV1beta1().Ingresses(ingress.Namespace).Update(context.TODO(), ingress, v12.UpdateOptions{})

	if err != nil {
		return err
	}

	return nil
}

/**
 * 添加作业WebUI路由规则
 */
func (o *ingressService) AddJobWebUIRouter(k8sClient *kubernetes.Clientset, namespace, ingressName, serviceNamePrefix, path string, servicePort int, clusterId int64) error {
	logger.Infof("AddJobWebUIRouter for path %s in namespace %s", path, namespace)

	// 构建Ingress对象
	ingressInit := &networkingV1.Ingress{
		ObjectMeta: v12.ObjectMeta{
			Name:      ingressName,
			Namespace: namespace,
		},
	}

	// 服务名称
	serviceName := serviceNamePrefix + "-rest"

	// 调用添加规则方法
	return o.AddIngressRule(k8sClient, ingressInit, serviceName, servicePort, path, clusterId)
}

/**
 * 删除作业WebUI路由规则
 */
func (o *ingressService) DeleteJobWebUIRouter(k8sClient *kubernetes.Clientset, namespace, ingressName, serviceNamePrefix, path string, servicePort int, clusterId int64) error {
	logger.Infof("DeleteJobWebUIRouter for path %s in namespace %s", path, namespace)

	// 构建Ingress对象
	ingressInit := &networkingV1.Ingress{
		ObjectMeta: v12.ObjectMeta{
			Name:      ingressName,
			Namespace: namespace,
		},
	}

	// 服务名称
	serviceName := serviceNamePrefix + "-rest"

	// 调用删除规则方法
	return o.DeleteIngressRule(k8sClient, ingressInit, serviceName, servicePort, clusterId)
}
