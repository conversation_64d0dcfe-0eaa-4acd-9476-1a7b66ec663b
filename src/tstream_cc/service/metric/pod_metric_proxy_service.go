package metric

import (
	"encoding/base64"
	"encoding/json"
	"errors"

	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/profile"
	monitor2 "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/monitor/v20180724"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/selection"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/metric"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
)

type DescribeMetricProxyService struct {
	cluster *table.Cluster
	insMap  map[string]string
}

func (s *DescribeMetricProxyService) DoDescribeMetric(req *metric.DescribeMetricProxyReq) (*metric.DescribeMetricProxyRsp, error) {
	//鉴权
	if req.ClusterId != "" {
		clusterGroup, err := service2.ListClusterGroupBySerialIdV2(req.ClusterId, int(req.AppId))
		if err != nil {
			logger.Errorf("%s: GetClusterGroupBySerialId with error: %v", req.RequestId, err)
			return metric.EmptyMetricProxyRsp(), err
		}
		cluster, err := service2.GetActiveClusterByClusterGroupId(clusterGroup.Id)
		if err != nil {
			logger.Errorf("%s: GetActiveClusterByClusterGroupId with error: %v", req.RequestId, err)
			return metric.EmptyMetricProxyRsp(), err
		}
		s.cluster = cluster
		s.fillMetricIns(req)
	}
	if req.Instances == nil || len(req.Instances) == 0 {
		logger.Infof("%s ,req.Instances is nil , req is %v", req.RequestId, req)
		return metric.EmptyMetricProxyRsp(), nil
	}
	metricDataResponse, err := s.getMonitorDataFromBarad(req)
	if err != nil {
		logger.Errorf("%s:get pod metric with error %v", req.RequestId, err)
		return metric.EmptyMetricProxyRsp(), err
	}
	if metricDataResponse.Response == nil || metricDataResponse.Response.DataPoints == nil {
		logger.Errorf("%s:get metric from barad has returned empty", req.RequestId)
		return metric.EmptyMetricProxyRsp(), errors.New("get metric from barad has returned empty")
	}
	return s.rspMap(metricDataResponse), nil
}

func (s *DescribeMetricProxyService) GetSetasCVMIns(req *metric.DescribeMetricProxyReq) (map[string]string, error) {
	result := make(map[string]string, 0)
	logger.Infof("[%s] Querying CVM instances for cluster: %v", req.RequestId, s.cluster)
	clientSet, err := k8s.GetK8sService().NewClient([]byte(s.cluster.KubeConfig))
	if err != nil {
		logger.Errorf("%s:NewClient with error %v", req.RequestId, err)
		return result, err
	}
	selector := labels.NewSelector()
	req1, err := labels.NewRequirement(constants.TKE_CVM_LABEL_KEY, selection.In, []string{constants.SETATS_WORKER_LABEL, constants.SETATS_MANAGER_LABEL})
	if err != nil {
		logger.Errorf("cannot found setas node by label , init label selector with  error %v", err)
		return result, err
	}
	selector = selector.Add(*req1)
	workerList, err := k8s.GetK8sService().ListNode(clientSet, metav1.ListOptions{
		LabelSelector: selector.String(),
	})
	if err != nil {
		logger.Errorf(" Failed to list node, with errors:%+v", err)
		return result, err
	}
	for _, node := range workerList.Items {
		labelMap := node.GetObjectMeta().GetLabels()
		if instanceId, ok := labelMap["cloud.tencent.com/node-instance-id"]; ok {
			result[instanceId] = labelMap[constants.TKE_CVM_LABEL_KEY]
		} else {
			logger.Errorf("not find instance id for %s, %+v", node.Name, node)
			continue
		}
	}
	return result, nil
}

func (s *DescribeMetricProxyService) fillMetricIns(req *metric.DescribeMetricProxyReq) {
	if req.MetricType == metric.SetatsMetric {
		count, setas, err := service.GetSetatsByClusterGroupSerialId(req.ClusterId)
		if err != nil {
			logger.Errorf("%s: GetSetatsByClusterGroupSerialId with error: %v", req.RequestId, err)
			return
		}
		if count <= 0 || setas == nil {
			logger.Errorf("canot found seats for clusterId %s", req.ClusterId)
			return
		}
		insMap, err := s.GetSetasCVMIns(req)
		if err != nil {
			logger.Errorf("%s: GetSetasCVMIns with error: %v", req.RequestId, err)
			return
		}
		if req.Instances == nil || len(req.Instances) == 0 {
			req.Instances = make([]*metric.Instance, 0)
		}
		for ins := range insMap {
			req.Instances = append(req.Instances, &metric.Instance{
				Dimensions: []*metric.Dimension{
					{
						Name:  common.StringPtr("InstanceId"),
						Value: common.StringPtr(ins),
					},
				},
			})
		}
		s.insMap = insMap
		logger.Infof("successfully fill metric instance for req %+v", req)
	}
}

func (s *DescribeMetricProxyService) getMonitorDataFromBarad(req *metric.DescribeMetricProxyReq) (*monitor2.GetMonitorDataResponse, error) {
	secretId, secretKey, err := service3.GetSecretIdAndKeyOfScs()
	if err != nil {
		logger.Errorf("%s:get secretId and secreteKey with error %v", req.RequestId, err)
		return nil, err
	}
	credential := common.NewCredential(
		secretId,
		secretKey,
	)
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.ReqMethod = "POST"
	cpf.HttpProfile.Endpoint = "monitor.internal.tencentcloudapi.com"
	cpf.HttpProfile.ReqTimeout = 300
	client, err := monitor2.NewClient(credential, req.Region, cpf)
	if err != nil {
		logger.Errorf("%s:An API client has built with error %v", req.RequestId, err)
		return nil, err
	}
	request := monitor2.NewGetMonitorDataRequest()
	request.Namespace = common.StringPtr(req.Namespace)
	request.MetricName = common.StringPtr(req.MetricName)
	if req.Period < 0 {
		request.Period = common.Uint64Ptr(300)
	} else {
		request.Period = common.Uint64Ptr(uint64(req.Period))
	}
	request.StartTime = common.StringPtr(req.StartTime)
	request.EndTime = common.StringPtr(req.EndTime)
	insts := make([]*monitor2.Instance, 0)
	for _, instance := range req.Instances {
		dimension := make([]*monitor2.Dimension, 0)
		for _, di := range instance.Dimensions {
			dimension = append(dimension, &monitor2.Dimension{
				Name:  common.StringPtr(*di.Name),
				Value: common.StringPtr(*di.Value),
			})
		}
		insts = append(insts, &monitor2.Instance{
			Dimensions: dimension,
		})
	}
	request.Instances = insts

	logger.Infof("%s:Cloud API GetMonitorData request: %s", req.RequestId, request.ToJsonString())
	response, err := client.GetMonitorData(request)
	if err != nil {
		logger.Errorf("%s:Cloud API DescribeStatisticData has returned error %v", req.RequestId, err)
		return response, err
	}
	logger.Infof("%s:DescribeStatisticData has returned %s", req.RequestId, response.ToJsonString())
	return response, nil
}

// rspMap DescribeStatisticDataRsp -> DescribePodMetricRsp
func (s *DescribeMetricProxyService) rspMap(response *monitor2.GetMonitorDataResponse) *metric.DescribeMetricProxyRsp {
	metricRsp := &metric.DescribeMetricProxyRsp{}
	metricRsp.RequestId = response.Response.RequestId
	metricRsp.Period = response.Response.Period
	metricRsp.StartTime = response.Response.StartTime
	metricRsp.EndTime = response.Response.EndTime
	metricRsp.MetricName = response.Response.MetricName
	metricDatas := make([]*metric.DataPointProxy, 0)
	for _, point := range response.Response.DataPoints {
		pointc := &metric.DataPointProxy{}
		pointc.Dimensions = make([]*metric.Dimension, 0)
		pointc.Timestamps = make([]*float64, 0)
		pointc.Values = make([]*float64, 0)
		for _, d := range point.Dimensions {
			pointc.Dimensions = append(pointc.Dimensions, &metric.Dimension{
				Name:  d.Name,
				Value: d.Value,
			})
		}
		for _, t := range point.Timestamps {
			pointc.Timestamps = append(pointc.Timestamps, t)
		}
		for _, v := range point.Values {
			pointc.Values = append(pointc.Values, v)
		}
		metricDatas = append(metricDatas, pointc)
	}
	metricRsp.DataPoints = metricDatas
	s.fillRspAdditionalInfo(metricRsp)

	return metricRsp
}

func (s *DescribeMetricProxyService) fillRspAdditionalInfo(rsp *metric.DescribeMetricProxyRsp) {
	if len(s.insMap) > 0 {
		additionalInfo := metric.AdditionalInfo{
			InstanceMap: s.insMap,
		}
		insJsonBytes, err := json.Marshal(additionalInfo)
		if err != nil {
			logger.Errorf("DescribeMetricProxyService.insMap is not json format %v", s.insMap)
		}
		if insJsonBytes != nil && len(insJsonBytes) > 0 {
			rsp.AdditionalInfo = common.StringPtr(base64.StdEncoding.EncodeToString(insJsonBytes))
		}
	}
}
