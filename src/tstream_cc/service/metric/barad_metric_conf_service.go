package metric

import (
	"encoding/json"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/metric"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config"
)

type BaradMetricConfService struct {
}

func (b *BaradMetricConfService) DoGetBaradMetricConf(
	req *metric.DescribeBaradMetricConfReq) (*metric.DescribeBaradMetricConfRsp, error) {
	rsp := &metric.DescribeBaradMetricConfRsp{RequestId: req.RequestId}
	whiteList, err := b.getBaradMetricWhiteList()
	if err != nil {
		logger.Errorf("getBaradMetricWhiteList return error %v", err)
		return rsp, err
	}
	rsp.MetricWhiteList = whiteList
	regex, err := b.getBaradMetricRegex()
	if err != nil {
		logger.Errorf("getBaradMetricRegex return error %v", err)
		return rsp, err
	}
	rsp.MetricRegex = regex
	return rsp, nil
}

func (b *BaradMetricConfService) getBaradMetricWhiteList() ([]string, error) {
	groupName := "Metric"
	key := "barad_metric_white_list"
	value, err := config.GetRainbowConfiguration(groupName, key)
	if err != nil {
		logger.Errorf("cannot get [%s] in group [%s] from rainbow", key, groupName)
		return make([]string, 0), err
	}
	metricWhiteLists := make([]string, 0)
	err = json.Unmarshal([]byte(value), &metricWhiteLists)
	if err != nil {
		logger.Errorf("Failed to Unmarshal barad_metric_white_list because %+v. ", err)
		return metricWhiteLists, err
	}
	return metricWhiteLists, nil
}

func (b *BaradMetricConfService) getBaradMetricRegex() ([]*metric.BaradMetricRegexKV, error) {
	groupName := "Metric"
	key := "barad_metric_regex"
	value, err := config.GetRainbowConfiguration(groupName, key)
	if err != nil {
		logger.Errorf("cannot get [%s] in group [%s] from rainbow", key, groupName)
		return make([]*metric.BaradMetricRegexKV, 0), err
	}
	metricWhiteLists := make([]*metric.BaradMetricRegexKV, 0)
	err = json.Unmarshal([]byte(value), &metricWhiteLists)
	if err != nil {
		logger.Errorf("Failed to Unmarshal barad_metric_white_list because %+v. ", err)
		return metricWhiteLists, err
	}
	return metricWhiteLists, nil
}
