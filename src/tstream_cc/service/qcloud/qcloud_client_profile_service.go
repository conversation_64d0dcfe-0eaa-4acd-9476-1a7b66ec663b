package qcloud

import (
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/profile"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
)

func NewClientProfile() *profile.ClientProfile {
	return NewClientProfileWithTimeout(constants.QCLOUD_API_TIMEOUT)
}

func NewClientProfileWithTimeout(timeout int) *profile.ClientProfile {
	prof := profile.NewClientProfile()
	prof.HttpProfile.ReqMethod = "POST"
	prof.HttpProfile.ReqTimeout = timeout
	prof.SignMethod = "HmacSHA1"

	return prof
}
