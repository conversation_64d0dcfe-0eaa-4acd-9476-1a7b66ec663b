package tag

import (
	"encoding/json"
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/component"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/mc_tag"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/tag"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource_auth"
	"time"
)

var (
	tagService *TagService
)

type TagService struct {
}

func NewTagService() *TagService {
	return &TagService{}
}

func GetTagService() *TagService {
	if tagService == nil {
		tagService = NewTagService()
	}

	return tagService
}

// 检验tags是否合法，组成tag的key-value对，需满足
// 1. key必须存在
// 2. value可为空
// 3. key-value真实存在
func (o *TagService) ValidateTags(uin string, inputTags []*tag.Tag) (tags []*tag.Tag, err error) {
	const pageSize = 10000
	tagComponent := component.NewTagComponent()
	tags = make([]*tag.Tag, 0, len(inputTags))
	if len(inputTags) == 0 {
		return
	}

	tagKeys := make([]string, 0, len(inputTags))
	// tag去重
	tagMap := map[string]struct{}{}
	for _, t := range inputTags {
		if len(t.TagKey) == 0 {
			return nil, errorcode.NewStackError(
				errorcode.FailedOperationCode.MakeSecondCodeWithoutCodeNum("GetTagValues",
					fmt.Sprintf("Empty tagKey for tagValue %s", t.TagValue)),
				"", nil)
		}

		if _, exist := tagMap[t.TagKey]; exist {
			return nil, errorcode.NewStackError(
				errorcode.FailedOperationCode.MakeSecondCodeWithoutCodeNum("GetTagValues",
					fmt.Sprintf("Duplicate tagKey %s", t.TagKey)),
				"", nil)
		}
		tagMap[t.TagKey] = struct{}{}
		tags = append(tags, t)
		tagKeys = append(tagKeys, t.TagKey)
	}

	// tagkey -> { tagvalue1 ->, tagvalue2 -> }
	candiateTags := map[string]map[string]struct{}{}

	pageSizeStr := fmt.Sprintf("%d", pageSize)
	for i := 0; i < len(tagKeys); i += pageSize {
		j := i + pageSize
		if j > len(tagKeys) {
			j = len(tagKeys)
		}

		rspStr, err := tagComponent.GetTagValues(uin, fmt.Sprintf("%d", i/pageSize+1), pageSizeStr,
			tagKeys[i:j], time.Now().Unix(), map[string]string{})
		if err != nil {
			return nil, err
		}
		response := &mc_tag.GetTagValuesResponse{}
		if err := json.Unmarshal([]byte(rspStr), response); err != nil {
			return nil, errorcode.NewStackError(
				errorcode.FailedOperationCode.MakeSecondCodeWithoutCodeNum("GetTagValues", err.Error()),
				"", nil)
		}

		if len(response.Rows) == 0 {
			continue
		}

		for _, t := range response.Rows {
			if tValues, exist := candiateTags[t.TagKey]; !exist {
				candiateTags[t.TagKey] = map[string]struct{}{t.TagValue: {}}
			} else {
				tValues[t.TagValue] = struct{}{}
			}
		}
	}

	for _, t := range tags {
		tValues, exist := candiateTags[t.TagKey]
		if !exist {
			return nil, errorcode.NewStackError(
				errorcode.FailedOperationCode.MakeSecondCodeWithoutCodeNum("GetTagValues",
					fmt.Sprintf("tagKey %s not found", t.TagKey)),
				"", nil)
		}

		if len(t.TagValue) == 0 {
			continue
		}
		// 只有tagValue不为空，才校验是否真实存在
		if _, exist = tValues[t.TagValue]; !exist {
			return nil, errorcode.NewStackError(
				errorcode.FailedOperationCode.MakeSecondCodeWithoutCodeNum("GetTagValues",
					fmt.Sprintf("tagValue %s for tagKey %s not found", t.TagValue, t.TagKey)),
				"", nil)
		}
	}

	return tags, nil
}

func (o *TagService) GetResourceTags(uin, region string, resourceIds []string, prefix string) (tags []*mc_tag.TagResource, err error) {
	const pageSize = 50
	tags = make([]*mc_tag.TagResource, 0)
	if len(resourceIds) == 0 {
		return
	}

	totalPages := len(resourceIds) / pageSize
	if len(resourceIds)%pageSize > 0 {
		totalPages++
	}

	tagComponent := component.NewTagComponent()

	for page := 0; page < totalPages; page++ {
		start := page * pageSize
		end := start + pageSize
		if end > len(resourceIds) {
			end = len(resourceIds)
		}
		pageOfResourceIds := resourceIds[start:end]

		for i := 1; true; i++ {
			response := &mc_tag.GetResourceTagsByResourceIdsResponse{}
			if rspStr, err := tagComponent.GetResourceTagsByResourceIds(uin, region,
				resource_auth.RESOURCE_SERVICE_TYPE, prefix, pageOfResourceIds,
				time.Now().Unix(), map[string]string{
					"page": fmt.Sprintf("%d", i),
					"rp":   fmt.Sprintf("%d", 1000),
				}); err != nil {
				return nil, err
			} else if err := json.Unmarshal([]byte(rspStr), response); err != nil {
				return nil, errorcode.InternalErrorCode.NewWithErr(err)
			}

			if len(response.Rows) == 0 {
				break
			}
			for _, t := range response.Rows {
				tags = append(tags, t)
			}
		}
	}
	return tags, nil
}

func (o *TagService) GetResourceTagsForJob(uin, region string, resourceIds []string) (tags []*mc_tag.TagResource, err error) {
	const pageSize = 50
	tags = make([]*mc_tag.TagResource, 0)
	if len(resourceIds) == 0 {
		return
	}

	totalPages := len(resourceIds) / pageSize
	if len(resourceIds)%pageSize > 0 {
		totalPages++
	}

	tagComponent := component.NewTagComponent()

	for page := 0; page < totalPages; page++ {
		start := page * pageSize
		end := start + pageSize
		if end > len(resourceIds) {
			end = len(resourceIds)
		}
		pageOfResourceIds := resourceIds[start:end]

		for i := 1; true; i++ {
			response := &mc_tag.GetResourceTagsByResourceIdsResponse{}
			if rspStr, err := tagComponent.GetResourceTagsByResourceIds(uin, region,
				resource_auth.RESOURCE_SERVICE_TYPE, resource_auth.RESOURCE_PREFIX_JOB, pageOfResourceIds,
				time.Now().Unix(), map[string]string{
					"page": fmt.Sprintf("%d", i),
					"rp":   fmt.Sprintf("%d", 1000),
				}); err != nil {
				return nil, errorcode.InternalErrorCode.NewWithErr(err)
			} else if err := json.Unmarshal([]byte(rspStr), response); err != nil {
				return nil, errorcode.InternalErrorCode.NewWithErr(err)
			}

			if len(response.Rows) == 0 {
				break
			}
			for _, t := range response.Rows {
				tags = append(tags, t)
			}
		}
	}

	return tags, nil
}
