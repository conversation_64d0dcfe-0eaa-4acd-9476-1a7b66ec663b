package tag

import (
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/component"
	service3 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/tag"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource_auth"
	model2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource_auth/model"
	"time"
)

// 为作业绑定标签
func JobBindToTag(requestId string, uin string, region string, tags []*tag.Tag, jobSerialId string) error {
	if len(tags) == 0 {
		return nil
	}
	for _, tag := range tags {
		tagComponent := component.NewTagComponent()
		response, err := tagComponent.BatchAddResourcesTag(uin, uin,
			region, resource_auth.RESOURCE_SERVICE_TYPE, resource_auth.RESOURCE_PREFIX_JOB,
			tag.TagKey, tag.TagValue, []string{jobSerialId}, time.Now().Unix(), map[string]string{})
		if err != nil {
			logger.Errorf("reqId:[%s], Failed JobBindToTag with jobSerialId:%s error:%+v", requestId, jobSerialId, err)
			return err
		}
		logger.Infof("reqId:[%s], JobBindToTag with jobSerialId:%s response:%+v", requestId, jobSerialId, response)
	}
	return nil
}

// 根据相关编号为作业解绑标签
func JobUnBindToTagWithJobSerialIds(requestId string, jobSerialIds []string) error {
	uin := ""
	region := ""
	if len(jobSerialIds) > 0 {
		sql := "SELECT * FROM Job "
		cond := dao.NewCondition()
		cond.In("SerialId", jobSerialIds)
		where, args := cond.GetWhere()
		sql += where
		sql += "LIMIT 1"
		txManager := service3.GetTxManager()
		_, datas, err := txManager.GetQueryTemplate().DoQuery(sql, args)
		if err != nil {
			logger.Errorf("reqId:%s Failed to JobUnBindToTagWithJobSerialIds execute the sql:%s, with args:%+v, with errors:%+v", requestId, sql, args, err)
			return err
		}
		for _, data := range datas {
			job := &table.Job{}
			err = util.ScanMapIntoStruct(job, data)
			if err != nil {
				logger.Errorf("reqId:%s Failed to JobUnBindToTagWithJobSerialIds ScanMapIntoStruct with errors:%+v", requestId, err)
				return err
			}
			uin = job.OwnerUin
			region = job.Region
			break
		}
	}

	return JobUnBindToTag(requestId, jobSerialIds, uin, region)
}

func JobUnBindToTagWithClusterGroupId(requestId string, clusterGroupId int64) error {
	logger.Infof("reqId:%s begin to JobUnBindToTagWithClusterGroupId with clusterGroupId : %d", requestId, clusterGroupId)
	uin := ""
	region := ""
	jobSerialIds := make([]string, 0)
	if clusterGroupId > 0 {
		sql := "SELECT * FROM Job "
		cond := dao.NewCondition()
		cond.Eq("ClusterGroupId", clusterGroupId)
		where, args := cond.GetWhere()
		sql += where
		txManager := service3.GetTxManager()
		_, datas, err := txManager.GetQueryTemplate().DoQuery(sql, args)
		if err != nil {
			logger.Errorf("reqId:%s Failed to JobUnBindToTagWithClusterGroupId execute the sql:%s, with args:%+v, with errors:%+v", requestId, sql, args, err)
			return err
		}
		for _, data := range datas {
			job := &table.Job{}
			err = util.ScanMapIntoStruct(job, data)
			if err != nil {
				logger.Errorf("reqId:%s Failed to JobUnBindToTagWithClusterGroupId ScanMapIntoStruct with errors:%+v", requestId, err)
				return err
			}
			jobSerialIds = append(jobSerialIds, job.SerialId)
			uin = job.OwnerUin
			region = job.Region
		}
	}
	if len(jobSerialIds) == 0 {
		return nil
	}

	return JobUnBindToTag(requestId, jobSerialIds, uin, region)
}

func JobUnBindToTag(requestId string, jobSerialIds []string, uin string, region string) error {
	tagComponent := component.NewTagComponent()
	tags, err := GetTagService().GetResourceTagsForJob(uin, region, jobSerialIds)
	if err != nil {
		logger.Errorf("GetResourceTagsForJob Error, %s", strings.Join(jobSerialIds, ","))
		return err
	}
	if len(tags) == 0 {
		return nil
	}
	for _, t := range tags {
		response, deleteTagErr := tagComponent.BatchDeleteResourcesTag(uin, uin,
			region, resource_auth.RESOURCE_SERVICE_TYPE, resource_auth.RESOURCE_PREFIX_JOB,
			t.TagKey, t.TagValue, jobSerialIds, time.Now().Unix(), map[string]string{})
		if deleteTagErr != nil {
			logger.Errorf("reqId:[%s], JobUnBindToTag with jobSerialIds:%+v t.TagKey:%s t.TagValue:%s deleteTagErr:%+v", requestId, jobSerialIds, t.TagKey, t.TagValue, deleteTagErr)
			return deleteTagErr
		}
		logger.Infof("reqId:[%s], JobUnBindToTag with jobSerialIds:%+v t.TagKey:%s t.TagValue:%s response:%+v", requestId, jobSerialIds, t.TagKey, t.TagValue, response)
	}
	return nil
}

// 标签过滤作业
func FilterJobsByTags(req *model.DescribeJobsReq) (jobSerialIds []string, empty bool, err error) {
	// 校验标签合法性
	tags, err := getTagsWithReq(req)
	if err != nil {
		logger.Errorf("reqId:%s Failed to getTagsWithReq, error:%+v", req.RequestId, err)
		return nil, true, err
	}

	if tags == nil {
		logger.Errorf("reqId:%s Failed to getTagsWithReq, tags == nil", req.RequestId)
		return nil, true, nil
	}

	if len(tags) == 0 {
		return nil, true, nil
	}

	tags, err = GetTagService().ValidateTags(req.Uin, tags)
	if err != nil {
		logger.Errorf("reqId:%s Failed to GetTagService().ValidateTags, error:%+v", req.RequestId, err)
		return nil, true, err
	}

	if tags == nil {
		logger.Errorf("reqId:%s Failed to GetTagService().ValidateTags, tags == nil", req.RequestId)
		return nil, true, nil
	}

	if len(tags) == 0 {
		return nil, true, nil
	}
	tagList := make([]*model2.TagKeyValue, 0, len(tags))
	for _, t := range tags {
		tagList = append(tagList, &model2.TagKeyValue{
			Key:   t.TagKey,
			Value: t.TagValue,
		})
	}

	jobsByTag, err := resource_auth.GetResourceManager().ParseTagListForJob(&resource_auth.ResourceContext{
		AppId:         req.AppId,
		Uin:           req.Uin,
		SubAccountUin: req.SubAccountUin,
		Action:        "",
		ResourceIds:   nil,
		Ip:            "",
		Token:         "",
		Region:        req.Region,
	}, tagList)
	if err != nil {
		logger.Errorf("reqId:%s Failed to GetResourceManager().ParseTagListForJob, error:%+v", req.RequestId, err)
		return nil, true, err
	}

	if len(req.JobIds) == 0 {
		return jobsByTag, len(jobsByTag) == 0, nil
	}
	jobsByTagMap := make(map[string]struct{}, len(jobsByTag))
	for _, id := range jobsByTag {
		jobsByTagMap[id] = struct{}{}
	}

	for _, id := range req.JobIds {
		if _, exist := jobsByTagMap[id]; exist {
			jobSerialIds = append(jobSerialIds, id)
		}
	}

	return jobSerialIds, len(jobSerialIds) == 0, nil
}

func getTagsWithReq(req *model.DescribeJobsReq) (tags []*tag.Tag, err error) {
	if len(req.Filters) == 0 {
		return nil, nil
	}
	tagsKv := make([]string, 0)
	for _, filter := range req.Filters {
		if filter.Name == "Tags" {
			tagsKv = filter.Values
			break
		}
	}
	if len(tagsKv) == 0 {
		return nil, nil
	}
	tags = make([]*tag.Tag, 0, len(tagsKv))
	for _, kv := range tagsKv {
		arr := strings.SplitN(kv, ":", 2)

		tags = append(tags, &tag.Tag{
			TagKey: arr[0],
			TagValue: func() string {
				if len(arr) >= 2 {
					return arr[1]
				} else {
					return ""
				}
			}(),
		})
	}
	return tags, nil
}

// 查询作业绑定的标签信息
func SetJobTagMapToJobSet(requestId string, uin string, region string, jobSet []*model.JobSetItem) []*model.JobSetItem {
	jobSerialIds := make([]string, len(jobSet))
	for _, item := range jobSet {
		jobSerialIds = append(jobSerialIds, item.JobId)
	}
	jobTagMap, err := getJobTagMap(requestId, uin, region, jobSerialIds)
	if err != nil {
		logger.Errorf("reqId:[%s], setJobTagMapToJobSet error value:%+v ", requestId, err)
	}
	for _, item := range jobSet {
		item.Tags = jobTagMap[item.JobId]
	}
	return jobSet
}

// 查询作业绑定的标签信息
func getJobTagMap(requestId string, uin string, region string, jobSerialIds []string) (map[string][]*tag.Tag, error) {
	tags, err := GetTagService().GetResourceTagsForJob(uin, region, jobSerialIds)
	if err != nil {
		logger.Errorf("reqId:[%s], getJobTagMap error value:%+v ", requestId, err)
		return nil, err
	}
	jobTagMap := make(map[string][]*tag.Tag, 0)
	for _, t := range tags {
		jobTagMap[t.ResourceId] = append(jobTagMap[t.ResourceId],
			&tag.Tag{
				TagKey:   t.TagKey,
				TagValue: t.TagValue,
			})
	}
	logger.Debugf("reqId:[%s], getJobTagMap jobTagMap value:%+v with uin:%s region:%s jobSerialIds:%+v", requestId, jobTagMap, uin, region, jobSerialIds)
	return jobTagMap, nil
}
