package version

import "testing"

func TestCompareImageVersion(t *testing.T) {
	testCases := []struct {
		v1       string
		v2       string
		expected int
	}{
		// 相同主要版本号
		{"1", "1", 0},
		{"1.0", "1.0", 0},
		{"1.0.0", "1.0.0", 0},
		{"1-abc", "1-def", 0},
		{"1.0-abc", "1.0-def", 0},
		{"1.0.0-abc", "1.0.0-def", 0},

		// 主要版本号不同
		{"1", "2", -1},
		{"1", "0", 1},
		{"2", "1", 1},
		{"2.0", "1.0", 1},
		{"1.0", "2.0", -1},
		{"1.0.0", "2.0.0", -1},
		{"1.0.0", "1.1.0", -1},
		{"2.0.0", "1.0.0", 1},
		{"2.0.0", "2.1.0", -1},
		{"1-abc", "2-def", -1},
		{"2-abc", "1-def", 1},
		{"1.0-abc", "2.0-def", -1},
		{"2.0-abc", "1.0-def", 1},
		{"1.0.0-abc", "2.0.0-def", -1},
		{"2.0.0-abc", "1.0.0-def", 1},
		{"1.0.0-abc", "1.1.0-def", -1},
		{"2.0.0-abc", "1.0.0-def", 1},

		// 次要版本号不同
		{"1.1", "1.2", -1},
		{"1.2", "1.1", 1},
		{"1.2.0", "1.1.0", 1},
		{"1.1.0", "1.2.0", -1},
		{"1.2.0", "1.2.1", -1},
		{"1.1-abc", "1.2-def", -1},
		{"1.2-abc", "1.1-def", 1},
		{"1.2.0-abc", "1.1.0-def", 1},
		{"1.1.0-abc", "1.2.0-def", -1},
		{"1.2.0-abc", "1.2.1-def", -1},

		// 修订版本号不同
		{"1.2.3", "1.2.4", -1},
		{"1.2.4", "1.2.3", 1},
		{"1.2.3", "1.2.10", -1},
		{"1.2.10", "1.2.3", 1},
		{"1.2.3-abc", "1.2.4-def", -1},
		{"1.2.4-abc", "1.2.3-def", 1},
		{"1.2.3-abc", "1.2.10-def", -1},
		{"1.2.10-abc", "1.2.3-def", 1},

		// 不同格式的版本号
		{"1", "1.0", 0},
		{"1", "1.0.0", 0},
		{"1.0", "1.0.0", 0},
		{"1.0", "1.1", -1},
		{"1.1", "1.0", 1},
		{"1.0.0", "1.0.1", -1},
		{"1.0.1", "1.0.0", 1},
		{"1.0.0", "1.1.0", -1},
		{"1.1.0", "1.0.0", 1},
		{"1.0", "2.0.0", -1},
		{"2.0.0", "1.0", 1},
		{"1.1", "2.0", -1},
		{"2.0", "1.1", 1},
		{"1.0", "1.0-abc", 0},
		{"1.0-abc", "1.0", 0},
		{"1.0", "1.1-abc", -1},
		{"1.1-abc", "1.0", 1},
		{"1.0", "1.0.0-abc", 0},
		{"1.0.0-abc", "1.0", 0},
		{"1.0", "1.1.0-abc", -1},
		{"1.1.0-abc", "1.0", 1},
		{"1-abc", "2-abc", -1},
		{"2-abc", "1-abc", 1},
		{"1.0-abc", "2.0-abc", -1},
		{"2.0-abc", "1.0-abc", 1},
		{"1.0.0-abc", "2.0.0-abc", -1},
		{"2.0.0-abc", "1.0.0-abc", 1},
		{"1.0.0-abc", "1.1.0-abc", -1},
		{"2.0.0-abc", "1.0.0-abc", 1},

		// 大于10的版本号
		{"10", "9", 1},
		{"10", "11", -1},
		{"10", "10.0", 0},
		{"10.0", "10.1", -1},
		{"10.1", "10.0", 1},
		{"10.0", "10.0.1", -1},
		{"10.0.1", "10.0", 1},
		{"10.0", "10.1.0", -1},
		{"10.1.0", "10.0", 1},
		{"10.0", "11.0.0", -1},
		{"11.0.0", "10.0", 1},
		{"10-abc", "9-abc", 1},
		{"10-abc", "11-abc", -1},
		{"10-abc", "10.0-abc", 0},
		{"10.0-abc", "10.1-abc", -1},
		{"10.1-abc", "10.0-abc", 1},
		{"10.0-abc", "10.0.1-abc", -1},
		{"10.0.1-abc", "10.0-abc", 1},
		{"10.0-abc", "10.1.0-abc", -1},
		{"10.1.0-abc", "10.0-abc", 1},
		{"10.0-abc", "11.0.0-abc", -1},
		{"11.0.0-abc", "10.0-abc", 1},

		// 小于10的版本号
		{"1", "9", -1},
		{"1", "11", -1},
		{"1.0", "9.0", -1},
		{"1.0", "11.0", -1},
		{"1.0.0", "9.0.0", -1},
		{"1.0.0", "11.0.0", -1},
		{"1-abc", "9-abc", -1},
		{"1-abc", "11-abc", -1},
		{"1.0-abc", "9.0-abc", -1},
		{"1.0-abc", "11.0-abc", -1},
		{"1.0.0-abc", "9.0.0-abc", -1},
		{"1.0.0-abc", "11.0.0-abc", -1},
	}

	for _, tc := range testCases {
		result := CompareImageVersion(tc.v1, tc.v2)
		if result != tc.expected {
			t.Errorf("Mismatched result for v1: %s, v2: %s. Expected %d, got %d", tc.v1, tc.v2, tc.expected, result)
		}
	}
}
