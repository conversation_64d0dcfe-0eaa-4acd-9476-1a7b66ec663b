package service

import (
	"context"
	"fmt"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sync"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/log"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/daemon"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
)

var (
	clusterResultMutex sync.Mutex
)

type ModifyRunningLogShipperService struct {
}

func (this *ModifyRunningLogShipperService) getTkeClusters(req *log.ModifyRunningLogShipperReq, tx *dao.Transaction,
	uin string) ([]*log.TkeCluster, error) {
	cnt, data, err := tx.QueryWithArgs("select b.*,a.Region,a.OwnerUin from ClusterGroup a join Cluster b on b.ClusterGroupId=a.Id where a.Status>0 and a.Region=? and a.OwnerUin=? and b.RoleType>0 and b.SchedulerType=? and (b.ClsTopicId is null or b.ClsTopicId='')",
		req.Region, uin, constants.CLUSTER_SCHEDULER_TYPE_TKE)
	if err != nil {
		return nil, err
	}

	clusterList := make([]*log.TkeCluster, 0, cnt)
	if cnt == 0 {
		return clusterList, nil
	}
	for i := 0; i < cnt; i++ {
		cluster := &log.TkeCluster{}
		if err := util.ScanMapIntoStruct(cluster, data[i]); err != nil {
			return nil, err
		}
		clusterList = append(clusterList, cluster)
	}
	return clusterList, nil
}

func (this *ModifyRunningLogShipperService) disableTkeClusterLog(req *log.ModifyRunningLogShipperReq, cluster *log.TkeCluster,
	crdConf *log.JobRunningLogConfCrd) error {
	lcClient, err := NewLogConfigClientFromBytes([]byte(cluster.KubeConfig))
	if err != nil {
		logger.Errorf("[%s] NewLogConfigClientFromBytes for %s err %v", req.RequestId, cluster.UniqClusterId, err)
		return err
	}
	lcConfig := lcClient.ClsV1().LogConfigs()

	if _, err := lcConfig.Get(context.TODO(), crdConf.Crd.Name, metav1.GetOptions{}); err != nil {
		logger.Infof("[%s] Get Crd for %s err %v", req.RequestId, cluster.UniqClusterId, err)
		// crd不存在，不用删除，直接成功
		return nil
	}
	if err := lcConfig.Delete(context.TODO(), crdConf.Crd.Name, metav1.DeleteOptions{}); err != nil {
		logger.Errorf("[%s] Delete Crd for %s err %v", req.RequestId, cluster.UniqClusterId, err)
		return err
	} else {
		logger.Infof("[%s] Delete Crd for %s successfully", req.RequestId, cluster.UniqClusterId)
		return nil
	}
}

func (this *ModifyRunningLogShipperService) enableRunningLogShipper(req *log.ModifyRunningLogShipperReq, tx *dao.Transaction,
	uin string, clusters []*log.TkeCluster, crdConf *log.JobRunningLogConfCrd, rsp *log.ModifyRunningLogShipperRsp) error {
	// 数据库操作
	_ = tx.ExecuteSqlWithArgs("UPDATE JobRunningLogTopic SET Status=0 WHERE OwnerUin=? AND Region=? AND Status=1",
		uin, req.Region)

	if len(clusters) > 0 {
		_ = tx.ExecuteSqlWithArgs("insert INTO JobRunningLogTopic(`OwnerUin`,`Region`,`LogSetId`,`LogTopicId`,`Status`) VALUES(?,?,?,?,1) on duplicate key update `Status`=1", uin, req.Region, req.LogSetId, req.TopicId)
		// 后台处理
		daemon.GetEnableRunningLogDaemon().AppendTask(&daemon.EnableRunningLogRequest{
			RequestId: req.RequestId,
			Uin:       req.Uin,
			Region:    req.Region,
			LogSetId:  req.LogSetId,
			TopicId:   req.TopicId,
			Clusters:  clusters,
			CrdConf:   crdConf,
		})
	}

	for _, c := range clusters {
		clusterResult := &log.ClusterResult{
			ClusterId:    c.UniqClusterId,
			Success:      true,
			ErrorMessage: "request appended",
		}
		rsp.ClusterResults = append(rsp.ClusterResults, clusterResult)
	}

	return nil
}

func (this *ModifyRunningLogShipperService) disableRunningLogShipper(req *log.ModifyRunningLogShipperReq, tx *dao.Transaction,
	uin string, clusters []*log.TkeCluster, crdConf *log.JobRunningLogConfCrd, rsp *log.ModifyRunningLogShipperRsp) error {
	// 数据库操作
	_ = tx.ExecuteSqlWithArgs("UPDATE JobRunningLogTopic SET Status=0 WHERE OwnerUin=? AND Region=? AND Status=1",
		uin, req.Region)

	// tke crd操作
	if len(clusters) == 0 {
		return nil
	}
	waitGroup := &sync.WaitGroup{}
	waitGroup.Add(len(clusters))
	for _, c := range clusters {
		go func(c *log.TkeCluster) {
			clusterResult := &log.ClusterResult{
				ClusterId:    c.UniqClusterId,
				Success:      true,
				ErrorMessage: "",
			}

			if err := this.disableTkeClusterLog(req, c, crdConf); err != nil {
				logger.Errorf("[%s] disableTkeClusterLog %v err : %v", req.RequestId, c, err)
				clusterResult.Success = false
				clusterResult.ErrorMessage = err.Error()
			} else {
				logger.Infof("[%s] disableTkeClusterLog %v successfully", req.RequestId, c)
			}

			clusterResultMutex.Lock()
			rsp.ClusterResults = append(rsp.ClusterResults, clusterResult)
			clusterResultMutex.Unlock()

			waitGroup.Done()
		}(c)
	}
	waitGroup.Wait()

	return nil
}

func (this *ModifyRunningLogShipperService) doModifyRunningLogShipper(req *log.ModifyRunningLogShipperReq, tx *dao.Transaction,
	rsp *log.ModifyRunningLogShipperRsp) error {

	clusters, err := this.getTkeClusters(req, tx, req.Uin)
	if err != nil {
		logger.Errorf("[%s] getTkeClusters err : %v", req.RequestId, err)
		return err
	}

	crdConf, err := service3.GetTableService().GetLogConfigCrdConf()
	if err != nil {
		logger.Errorf("[%s] GetLogConfigCrdConf err : %v", req.RequestId, err)
		return err
	}

	if req.Enable {
		return this.enableRunningLogShipper(req, tx, req.Uin, clusters, crdConf, rsp)
	} else {
		return this.disableRunningLogShipper(req, tx, req.Uin, clusters, crdConf, rsp)
	}
}

func (this *ModifyRunningLogShipperService) ModifyRunningLogShipper(req *log.ModifyRunningLogShipperReq) (strErr, msg string,
	rsp *log.ModifyRunningLogShipperRsp) {
	defer func() {
		if err := recover(); err != nil {
			strErr, msg, rsp = controller.InternalError, fmt.Sprintf("%v", err), nil
		}
	}()

	rsp = &log.ModifyRunningLogShipperRsp{}
	txManager := service.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		return this.doModifyRunningLogShipper(req, tx, rsp)
	}).Close()

	return controller.OK, controller.NULL, rsp
}
