package service

import (
	"context"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/errors"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/profile"
	es "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/es/v20180416"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
)

type ApplyEsServerlessService struct {
	SecretId  string
	SecretKey string
	Token     string
	Region    string
}

const (
	tkeEniIpSwitchKey = "tke.cloud.tencent.com/cross-tenant-eni-enable"
	tkeEniIpSwitchVal = "true"

	tkeEniIpNetworksKey = "tke.cloud.tencent.com/networks"
	tkeEniIpNetworksVal = "tke-bridge,tke-direct-eni,tke-route"
)

func GetEsEndPoint(region string) string {
	esEndPoint := "es.tencentcloudapi.com"
	if region == constants.AP_SHANGHAI_ADC {
		esEndPoint = constants.EsApiDomainInternal
	}
	return esEndPoint
}

// CreateServerlessDi
func (s *ApplyEsServerlessService) CreateServerlessDi(request *es.CreateServerlessDiRequest) (
	response *es.CreateServerlessDiResponse, err error) {
	credential := common.NewCredential(s.SecretId, s.SecretKey)
	if s.Token != "" {
		credential.Token = s.Token
	}
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = GetEsEndPoint(s.Region)
	client, err := es.NewClient(credential, s.Region, cpf)
	if err != nil {
		logger.Errorf("es.NewClient return error %v", err)
		return response, err
	}
	response, err = client.CreateServerlessDi(request)
	if _, ok := err.(*errors.TencentCloudSDKError); ok {
		logger.Errorf("An API error has returned: %s", err)
		return response, err
	}
	if err != nil {
		logger.Errorf("An API [CreateServerlessDi] error has returned: %s", err)
		return response, err
	}
	logger.Infof("CreateServerlessDi rsp %s", response.ToJsonString())
	return response, err
}

// DescribeServerlessDi
func (s *ApplyEsServerlessService) DescribeServerlessDi(request *es.DescribeServerlessDiRequest) (
	response *es.DescribeServerlessDiResponse, err error) {
	credential := common.NewCredential(s.SecretId, s.SecretKey)
	if s.Token != "" {
		credential.Token = s.Token
	}
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = GetEsEndPoint(s.Region)
	client, err := es.NewClient(credential, s.Region, cpf)
	if err != nil {
		logger.Errorf("es.NewClient return error %v", err)
		return response, err
	}
	response, err = client.DescribeServerlessDi(request)
	if _, ok := err.(*errors.TencentCloudSDKError); ok {
		logger.Errorf("An API error has returned: %s", err)
		return response, err
	}
	if err != nil {
		logger.Errorf("An API [DescribeServerlessDi] error has returned: %s", err)
		return response, err
	}
	logger.Infof("DescribeServerlessDi rsp %s", response.ToJsonString())
	return response, err
}

// UpdateFilebeat 更新filebeat, 增加弹性网卡
func (s *ApplyEsServerlessService) UpdateFilebeat(kubeConfig string, filebeatNameSpace, filebeatName string) error {
	client, err := k8s.GetK8sService().NewClient([]byte(kubeConfig))
	if err != nil {
		logger.Errorf("k8s.newClient with error %v", err)
		return err
	}
	ds, err := client.AppsV1().DaemonSets(filebeatNameSpace).Get(context.TODO(), filebeatName, v1.GetOptions{})
	if err != nil {
		logger.Errorf("get daemonset %s with error %v", filebeatName, err)
		return err
	}
	ds.Spec.Template.ObjectMeta.Annotations[tkeEniIpSwitchKey] = tkeEniIpSwitchVal
	ds.Spec.Template.ObjectMeta.Annotations[tkeEniIpNetworksKey] = tkeEniIpNetworksVal
	ds, err = client.AppsV1().DaemonSets(filebeatNameSpace).Update(context.TODO(), ds, v1.UpdateOptions{})
	if err != nil {
		logger.Errorf("update filebeat with error %v", err)
		return err
	}
	logger.Infof("update filebeat result %+v", ds.Spec.Template.ObjectMeta.Annotations)
	return nil
}

// FilebeatContainsEniIp 判断filebeat是否已经分配弹性网卡
func (s *ApplyEsServerlessService) FilebeatContainsEniIp(kubeConfig string, filebeatNameSpace, filebeatName string) (bool, error) {
	client, err := k8s.GetK8sService().NewClient([]byte(kubeConfig))
	if err != nil {
		logger.Errorf("k8s.newClient with error %v", err)
		return false, err
	}
	ds, err := client.AppsV1().DaemonSets(filebeatNameSpace).Get(context.TODO(), filebeatName, v1.GetOptions{})
	if err != nil {
		logger.Errorf("get daemonset with error %v", err)
		return false, err
	}
	s2 := ds.Spec.Template.ObjectMeta.Annotations[tkeEniIpSwitchKey]
	s3 := ds.Spec.Template.ObjectMeta.Annotations[tkeEniIpNetworksKey]
	return s2 == tkeEniIpSwitchVal && s3 == tkeEniIpNetworksVal, err
}

// CreateServerlessInstanceUser 创建 es serverless 索引固定用户
func (s *ApplyEsServerlessService) CreateServerlessInstanceUser(request *es.CreateServerlessInstanceUserRequest) (
	response *es.CreateServerlessInstanceUserResponse, err error) {
	credential := common.NewCredential(s.SecretId, s.SecretKey)
	if s.Token != "" {
		credential.Token = s.Token
	}
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = GetEsEndPoint(s.Region)
	client, err := es.NewClient(credential, s.Region, cpf)
	if err != nil {
		logger.Errorf("es.NewClient return error %v", err)
		return response, err
	}
	response, err = client.CreateServerlessInstanceUser(request)
	if _, ok := err.(*errors.TencentCloudSDKError); ok {
		logger.Errorf("An API error has returned: %s", err)
		return response, err
	}
	if err != nil {
		logger.Errorf("An API error has returned: %s", err)
		return response, err
	}
	logger.Infof("createServerlessInstanceUser rsp %s", response.ToJsonString())
	return response, err
}
