package service

import (
	"context"
	"fmt"
	"testing"
	"time"
)

//func TestDescribeJobRunningLogStatus(t *testing.T) {
//	initDb()
//
//	if strErr, msg, rsp := (&DescribeJobRunningLogStatusService{}).DescribeJobRunningLogStatus(
//		&log.DescribeJobRunningLogStatusReq{
//			RequestBase: apiv3.RequestBase{
//				Action:        "DescribeJobRunningLogStatus",
//				Region:        "ap-guangzhou",
//				RequestId:     "xxx",
//				Uin:           *fTestOwnerUin,
//				SubAccountUin: *fTestSubAccountUin,
//			},
//		}); strErr != controller.OK {
//		t.<PERSON>rrorf("%s: %s", strErr, msg)
//	} else {
//		b, _ := json.MarshalIndent(rsp, "", "  ")
//		t.Logf("%s", string(b))
//	}
//}

func TestTimeout(t *testing.T) {
	ctxWithTimeout, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	errChan := make(chan error)
	go func() {
		time.Sleep(4 * time.Second)

		errChan <- nil
	}()
	select {
	case <-ctxWithTimeout.Done():
		fmt.Printf("describe job config, fillEsName has Done error:[%v] ", ctxWithTimeout.Err())
	case err := <-errChan:
		if err != nil {
			fmt.Printf("describe job config, fillEsName has error: [%v]\n", err)
		}
		fmt.Printf("finished")
	}
}
