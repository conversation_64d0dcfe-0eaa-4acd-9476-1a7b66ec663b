package service

import (
	"encoding/json"
	"errors"
	"fmt"

	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service1 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/log"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cls"
)

// author: ArchieYao
// date: 2021/10/9 4:09 下午
// description:

type DescribeClusterClsService struct {
}

// GetClusterClsInfo 获取集群绑定的CLS信息
func (d *DescribeClusterClsService) GetClusterClsInfo(req *log.DescribeClusterClsReq) (rsp *log.DescribeClusterClsRsp, err error) {
	rsp = &log.DescribeClusterClsRsp{}
	if req.ClusterId == "" {
		logger.Errorf(" req.UniqClusterId is empty, cannot get cluster cls info.")
		return rsp, errors.New(errorcode.InvalidParameter_InvalidClusterId.GetCodeStr())
	}
	ClusterId := req.ClusterId
	sql, cnt, data, err := QueryClusterBySerialId(ClusterId, req.AppId)
	if err != nil {
		logger.Errorf("sql [%s] with args[%s] execute failed.", sql, ClusterId)
		return rsp, err
	}
	if cnt <= 0 {
		logger.Warningf("cannot find records by sql[%s] with args[%s].", sql, ClusterId)
		rsp.ClsInfo = make([]*log.ClusterClsInfo, 0)
		return rsp, errors.New(fmt.Sprintf("cannot find records by %s", ClusterId))
	}
	if cnt > 1 {
		logger.Errorf("logic Error, find multi records by [%s]", ClusterId)
		return rsp, errors.New(fmt.Sprintf("logic Error, find multi records by %s", ClusterId))
	}
	cluster := &table.Cluster{}
	err = util.ScanMapIntoStruct(cluster, data[0])
	if err != nil {
		logger.Errorf("[%v] scanMapToStruct [%v] Error.", data[0], cluster)
		return rsp, err
	}
	if cluster.LogConfig == "" {
		logger.Warningf("cluster has no logConfig info.")
		rsp.ClsInfo = make([]*log.ClusterClsInfo, 0)
		return rsp, nil
	}
	err = json.Unmarshal([]byte(cluster.LogConfig), &rsp.ClsInfo)
	if err != nil {
		logger.Errorf("cluster.logConfig unmarshal to CLusterClsInfo failed.")
		return rsp, err
	}
	err = fillClsName(req, rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

// QueryClusterBySerialId 查询Cluster
func QueryClusterBySerialId(clusterId string, appId int64) (string, int, map[int]map[string][]byte, error) {
	//sql := "SELECT * FROM Cluster WHERE UniqClusterId = ?"
	args := make([]interface{}, 0)
	args = append(args, appId)
	args = append(args, clusterId)
	sql := "SELECT c.* FROM Cluster c  join ClusterGroup cg on cg.id=c.ClusterGroupId WHERE cg.AppId=? and cg.SerialId=?"
	cnt, data, err := service1.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	return sql, cnt, data, err
}

// fillClsName 填充日志集名称和日志主题名称
func fillClsName(req *log.DescribeClusterClsReq, rsp *log.DescribeClusterClsRsp) error {
	var logSetIds []*string
	var logTopicIds []*string
	for i := range rsp.ClsInfo {
		logSetIds = append(logSetIds, &rsp.ClsInfo[i].ClsLogSetId)
		logTopicIds = append(logTopicIds, &rsp.ClsInfo[i].ClsLogTopicId)
	}
	secretId, secretKey, token, _, err := service.StsAssumeRole(req.Uin, req.SubAccountUin, req.Region)
	if err != nil {
		logger.Errorf("StsAssumeRole with error :[%v]", err)
		return err
	}
	// todo 替换为StsAssumeRole的key和id，
	//commonParam := &cls.CloudApiCommonParams{Region: req.Region, SecretId: cls.TestSecretId, SecretKey: cls.TestSecretId}
	commonParam := &cls.CloudApiCommonParams{Region: req.Region, SecretId: secretId, SecretKey: secretKey, Token: token}
	logsets, errLogSet := cls.DescribeLogsetsByIds(commonParam, logSetIds, req.Uin, req.SubAccountUin)
	if errLogSet != nil {
		return errLogSet
	}
	logTopics, errTopic := cls.DescribeTopicsByIds(commonParam, logTopicIds, req.Uin, req.SubAccountUin)
	if errTopic != nil {
		return errTopic
	}
	for _, info := range rsp.ClsInfo {
		for _, logset := range logsets {
			if *logset.LogsetId == info.ClsLogSetId {
				info.ClsLogSetName = *logset.LogsetName
			}
		}
		for _, topic := range logTopics {
			if *topic.TopicId == info.ClsLogTopicId {
				info.ClsLogTopicName = *topic.TopicName
			}
		}
	}
	return nil
}
