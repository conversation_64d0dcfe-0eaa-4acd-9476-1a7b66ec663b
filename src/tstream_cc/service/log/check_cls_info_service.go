package service

import (
	"errors"
	"fmt"

	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service1 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/log"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cls"
)

// author: ArchieYao
// date: 2021/11/9 7:30 下午
// description:

type CheckClsInfoService struct {
}

// CheckClsInfo 检测ClsInfo
func (c CheckClsInfoService) CheckClsInfo(req *log.CheckClsInfoReq) (rsp *log.CheckClsInfoRsp, err error) {
	rsp = &log.CheckClsInfoRsp{}
	result, err := ClsTopicBelongToLogSet(req)
	if err != nil {
		return rsp, err
	}
	if !result {
		// topic 不属于 logset
		return rsp, nil
	}
	_, err, clusters := CheckFromCluster(req.ClsTopicId, req.AppId)
	if err != nil {
		logger.Errorf("get Cluster failed error : [%v]", err)
		return rsp, err
	}
	rsp.ClusterIds = clusters
	return rsp, nil
}

// CheckFromCluster 检测Cluster是否已经绑定CLS, true: 已经绑定
func CheckFromCluster(ClsTopicId string, appId int64) (bool, error, []string) {
	clusters := make([]string, 0)
	if ClsTopicId == "" {
		logger.Errorf("ClsTopicId is not valid")
		return false, nil, clusters
	}
	args := make([]interface{}, 0)
	args = append(args, appId)
	args = append(args, "%"+ClsTopicId+"%")
	sql := "SELECT cg.* FROM Cluster c  join ClusterGroup cg on cg.id=c.ClusterGroupId where cg.AppId=? and LogConfig!='' and  LogConfig like ?"
	count, data, err := service1.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	logger.Infof("count [%d]", count)
	if err != nil {
		logger.Errorf("CheckFromCluster error :[%v]", err)
		return false, err, clusters
	}
	if count > 0 && len(data) > 0 {
		for _, v := range data {
			clusterGroup := &table.ClusterGroup{}
			err = util.ScanMapIntoStruct(clusterGroup, v)
			if err != nil {
				return false, errorcode.InternalErrorCode.NewWithErr(err), clusters
			}
			clusters = append(clusters, clusterGroup.SerialId)
		}
		return true, nil, clusters
	}
	return false, nil, clusters
}

func ClsTopicBelongToLogSet(req *log.CheckClsInfoReq) (bool, error) {
	ClsTopicId := req.ClsTopicId
	ClsLogsetId := req.ClsLogsetId
	if ClsTopicId == "" || ClsLogsetId == "" {
		logger.Errorf("ClsTopicBelongToLogSet check failed , ClsTopicId or ClsLogsetId is empty")
		return false, nil
	}
	var logTopicIds []*string
	logTopicIds = append(logTopicIds, &ClsTopicId)
	secretId, secretKey, token, _, err := service.StsAssumeRole(req.Uin, req.SubAccountUin, req.Region)
	if err != nil {
		logger.Errorf("StsAssumeRole with error :[%v]", err)
		return false, err
	}
	// todo 替换为StsAssumeRole的key和id，
	//commonParam := &cls.CloudApiCommonParams{Region: req.Region, SecretId: cls.TestSecretId, SecretKey: cls.TestSecretId}
	commonParam := &cls.CloudApiCommonParams{Region: req.Region, SecretId: secretId, SecretKey: secretKey, Token: token}
	logTopics, err := cls.DescribeTopicsByIds(commonParam, logTopicIds, req.Uin, req.SubAccountUin)
	if err != nil {
		logger.Errorf("cls.DescribeTopicsByIds with err [%v]", err)
		return false, err
	}
	if logTopics == nil || len(logTopics) <= 0 {
		msg := fmt.Sprintf("cannot get topic by id %s", ClsTopicId)
		logger.Errorf(msg)
		return false, errors.New(msg)
	}
	return *logTopics[0].LogsetId == ClsLogsetId, nil
}
