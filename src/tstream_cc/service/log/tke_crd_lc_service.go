package service

import (
	logconfig "git.woa.com/tke/logconfig/pkg/generated/clientset/versioned"
	"k8s.io/client-go/tools/clientcmd"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
)

func NewLogConfigClient(context, kubeconfigPath string) (logconfig.Interface, error) {
	config, err := clientcmd.NewNonInteractiveDeferredLoadingClientConfig(
		&clientcmd.ClientConfigLoadingRules{ExplicitPath: kubeconfigPath},
		&clientcmd.ConfigOverrides{CurrentContext: context}).ClientConfig()

	if err != nil {
		logger.Errorf("Failed to get k8s client config: %v", err)
		return nil, err
	}

	logConfigClient, err := logconfig.NewForConfig(config)
	if err != nil {
		logger.Errorf("Failed to get logconfig client: %v", err)
		return nil, err
	}

	return logConfigClient, nil
}

func NewLogConfigClientFromBytes(configBytes []byte) (logconfig.Interface, error) {

	config, err := k8s.GetK8sService().NewConfig(configBytes)
	if err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}

	logConfigClient, err := logconfig.NewForConfig(config)
	if err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}

	return logConfigClient, nil
}
