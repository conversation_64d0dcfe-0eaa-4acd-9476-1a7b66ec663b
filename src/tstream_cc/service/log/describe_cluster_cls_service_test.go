package service

import (
	"encoding/json"
	"fmt"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/log"
	"testing"
)

// author: Archie<PERSON>ao
// date: 2021/10/9 7:05 下午
// description:

func TestClsInfo(t *testing.T) {
	str := "[ { \"ClsLogSetId\":\"111\", \"ClsLogTopicId\":\"222\", \"Default\": true }, { \"ClsLogSetId\": \"333\", \"ClsLogTopicId\": \"444\", \"Default\": false } ]"

	rsp := &log.DescribeClusterClsRsp{}

	json.Unmarshal([]byte(str), &rsp.ClsInfo)

	fmt.Println("str: " + str)
	fmt.Println(fmt.Sprintf("clsInfo: %v", rsp.ClsInfo[1]))
}

func TestClsInfo1(t *testing.T) {
	str := "[ { \"ClsLogSetId\":\"111\", \"ClsLogTopicId\":\"222\", \"Default\": true }, { \"ClsLogSetId\": \"333\", \"ClsLogTopicId\": \"444\", \"Default\": false } ]"

	infos := &[]log.ClusterClsInfo{}

	json.Unmarshal([]byte(str), infos)

	fmt.Println("str: " + str)
	fmt.Println(fmt.Sprintf("clsInfo: %v", infos))
}
