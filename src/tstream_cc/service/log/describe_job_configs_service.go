package service

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"sort"
	"strconv"
	"time"

	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common"
	es "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/es/v20180416"
	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service2 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/log"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job_config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service4 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	service5 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job_config"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource_config"
)

/**
 *
 * <AUTHOR>
 * @time 2019/4/29
 * @version 0.4.0
 * @since 0.4.0
 * @see
 */
func DoDescribeJobConfigs(req *model.DescribeJobConfigsReq) (rsp *model.DescribeJobConfigsRsp, err error) {

	// 因为云api上，JobConfigVersions字段被定义为 []uint64了， 没有办法传-1
	if req.OnlyDraft {
		req.JobConfigVersions = []int64{-1}
	}
	// 1. 检查作业是否存在
	job, err := service.WhetherJobExists(req.AppId, req.Region, req.JobId)
	if err != nil {
		return nil, err
	}
	//鉴权
	err = auth.InnerAuthById(req.WorkSpaceId, req.IsSupOwner, job.ItemSpaceId, int64(req.AppId), req.SubAccountUin, req.Action, false)
	if err != nil {
		logger.Errorf("%s: User  uin %s is NOT authenticated, refuse to serve", req.RequestId, req.Uin)
		return
	}

	filters := map[string][]string{}
	if len(req.Filters) != 0 {
		for i := 0; i < len(req.Filters); i++ {
			filterKey := req.Filters[i].Name
			filterVals := req.Filters[i].Values
			filters[filterKey] = filterVals
		}
	}

	// 2. 查询作业配置
	jobConfigs, err := service5.ListJobConfigs(job.Id, []int64{}, req.JobConfigVersions, filters)
	if err != nil {
		return nil, err
	}

	sort.Slice(jobConfigs, func(i, j int) bool {
		return jobConfigs[i].VersionId > jobConfigs[j].VersionId
	})

	jobConfigIds := make([]int64, 0, 0)
	var currentVersion int16
	for _, jobConfig := range jobConfigs {
		jobConfigIds = append(jobConfigIds, jobConfig.Id)
		if jobConfig.Id == job.PublishedJobConfigId {
			currentVersion = jobConfig.VersionId
		}
	}
	jobConfigResourceRef, err := GetJobConfigResourceRefByJobConfigIds(jobConfigIds)
	if err != nil {
		return nil, err
	}

	jobConfigSet := make([]*model.JobConfigSetItem, 0)
	limit := req.Limit
	if limit == 0 {
		limit = 20
	}

	draftJobConfigResourceRef := make(map[int64][]*model.ResourceRefDetail)
	draftJobConfigMap := make(map[int64]*table.JobConfig)
	// 查询运行作业版本对应的未发布生效的参数草稿
	if job.Status == constants.JOB_STATUS_RUNNING {
		draftVersions := []int64{int64(-currentVersion * 10)}
		filter := make(map[string][]string)
		filter["Status"] = []string{strconv.FormatInt(constants.JOB_CONFIG_STATUS_UN_PUBLISHED, 10)}
		draftJobConfigs, err := service5.ListJobConfigs(job.Id, []int64{}, draftVersions, filter)
		if err != nil {
			return nil, err
		}
		draftJobConfigIds := make([]int64, 0)
		for _, jobConfig := range draftJobConfigs {
			draftJobConfigIds = append(draftJobConfigIds, jobConfig.Id)
			draftJobConfigMap[jobConfig.JobId] = jobConfig
		}
		draftJobConfigResourceRef, err = GetJobConfigResourceRefByJobConfigIds(draftJobConfigIds)
		if err != nil {
			return nil, err
		}
	}

	for i := req.Offset; i < len(jobConfigs) && i < req.Offset+limit; i++ {
		draftJobConfig := draftJobConfigMap[job.Id]
		var oldJobConfig *table.JobConfig
		if draftJobConfig != nil {
			oldJobConfig = &table.JobConfig{}
			jsonData, err := json.Marshal(jobConfigs[i])
			if err != nil {
				logger.Errorf("Error marshaling to JSON:", err)
				return nil, err
			}
			err = json.Unmarshal(jsonData, oldJobConfig)
			if err != nil {
				logger.Errorf("Error unmarshaling to JSON:", err)
				return nil, err
			}
		}

		jobConfigSetItem, err := BuildJobConfigSetItemFromEntity(job, jobConfigs[i], draftJobConfig, job.Type)
		if err != nil {
			return nil, err
		}

		v1, _ := jobConfigResourceRef[jobConfigs[i].Id]
		// 只有运行中的作业才有运行暂存草稿，如果有改动则替换
		if draftJobConfig != nil && job.PublishedJobConfigId == oldJobConfig.Id {
			runningJobConfigSetItem, err := BuildJobConfigSetItemFromEntity(job, oldJobConfig, nil, job.Type)
			if err != nil {
				return nil, err
			}

			v2, _ := draftJobConfigResourceRef[draftJobConfig.Id]

			if AreResourceDifferent(v1, v2) {
				jobConfigSetItem.ResourceRefDetails = v2
				runningJobConfigSetItem.ResourceRefDetails = v1
			} else {
				jobConfigSetItem.ResourceRefDetails = v1
				runningJobConfigSetItem.ResourceRefDetails = v2
			}
			jobConfigSetItem.JobConfigItem = runningJobConfigSetItem
		} else {
			jobConfigSetItem.ResourceRefDetails = v1
		}
		jobConfigSet = append(jobConfigSet, jobConfigSetItem)
	}
	ctxWithTimeout, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	errChan := make(chan error)
	go func() {
		err = fillEsName(req, jobConfigSet)
		if err != nil {
			logger.Errorf("describe job config, fillEsName has error: [%v]\n", err)
		}
		errChan <- err
	}()
	select {
	case <-ctxWithTimeout.Done():
		logger.Errorf("describe job config, fillEsName has error:[%v] ", ctxWithTimeout.Err())
	case err := <-errChan:
		if err != nil {
			logger.Errorf("describe job config, fillEsName has error: [%v]\n", err)
		}
	}
	describeJobConfigsRsp := &model.DescribeJobConfigsRsp{}
	describeJobConfigsRsp.TotalCount = len(jobConfigs)
	describeJobConfigsRsp.JobConfigSet = jobConfigSet
	describeJobConfigsRsp.RequestId = req.RequestId
	return describeJobConfigsRsp, nil
}

func AreResourceDifferent(v1, v2 []*model.ResourceRefDetail) bool {
	// 首先判断两个列表长度是否相同，如果不同则直接返回 true
	if len(v1) != len(v2) {
		return true
	}

	// 遍历两个列表，比较每个 ResourceId
	for i := range v1 {
		if v1[i].ResourceId != v2[i].ResourceId {
			return true // 只要有一个不同，返回 true
		}
	}

	// 如果所有 ResourceId 都相同，返回 false
	return false
}

func fillEsName(req *model.DescribeJobConfigsReq, jobConfigSetItems []*model.JobConfigSetItem) error {
	if len(jobConfigSetItems) == 0 {
		return nil
	}
	indexs := make([]*string, 0)
	spaces := make([]*string, 0)
	for _, jobConfigSetItem := range jobConfigSetItems {
		if jobConfigSetItem.EsServerlessIndex != "" && jobConfigSetItem.EsServerlessSpace != "" && jobConfigSetItem.LogCollect == constants.JobLogCollectEnabledOnES {
			esServerlessIndex := common.StringPtr(jobConfigSetItem.EsServerlessIndex)
			if !service.ContainsElement(indexs, esServerlessIndex) {
				indexs = append(indexs, esServerlessIndex)
			}
			esServerlessSpace := common.StringPtr(jobConfigSetItem.EsServerlessSpace)
			if !service.ContainsElement(spaces, esServerlessSpace) {
				spaces = append(spaces, esServerlessSpace)
			}
		}
	}
	if len(indexs) > 0 && len(spaces) > 0 {
		secretId, secretKey, token, _, err := service.StsAssumeRole(req.Uin, req.SubAccountUin, req.Region)
		if err != nil {
			logger.Errorf("StsAssumeRole with error :[%v]", err)
			return err
		}

		esService := &EsServerlessService{
			SecretId:  secretId,
			SecretKey: secretKey,
			Token:     token,
			Region:    req.Region,
		}
		desEsSpaceReq := es.NewDescribeServerlessSpacesRequest()
		desEsSpaceReq.SpaceIds = spaces
		dssr, err := esService.describeServerlessSpaces(desEsSpaceReq, req.Uin, req.SubAccountUin)
		if err != nil {
			return err
		}
		for _, ss := range dssr.Response.ServerlessSpaces {
			for _, jobConfigSetItem := range jobConfigSetItems {
				if jobConfigSetItem.EsServerlessSpace == *ss.SpaceId {
					jobConfigSetItem.WorkspaceName = *ss.SpaceName
				}
			}
		}
		desEsIndexReq := es.NewDescribeServerlessInstancesRequest()
		desEsIndexReq.InstanceIds = indexs
		desEsIndexReq.SpaceIds = spaces
		dsir, err := esService.describeServerlessInstances(desEsIndexReq, req.Uin, req.SubAccountUin)
		if err != nil {
			return err
		}
		if len(dsir.Response.IndexMetaFields) <= 0 {
			logger.Errorf("cannot get index , req %s", desEsIndexReq.ToJsonString())
			return fmt.Errorf("cannot get index , req %s", desEsIndexReq.ToJsonString())
		}
		for _, ss := range dsir.Response.IndexMetaFields {
			for _, jobConfigSetItem := range jobConfigSetItems {
				if jobConfigSetItem.EsServerlessIndex == *ss.InstanceId {
					jobConfigSetItem.IndexName = *ss.IndexName
				}
			}
		}
	}
	return nil
}

func BuildJobConfigSetItemFromEntity(job *table2.Job, jobConfig *table.JobConfig, draftJobConfig *table.JobConfig, jobType int8) (*model.JobConfigSetItem, error) {
	if draftJobConfig != nil && job.PublishedJobConfigId == jobConfig.Id {
		logger.Infof("jobConfig:%v, running job replace response %v", jobConfig, draftJobConfig)
		if draftJobConfig.ProgramArgs != jobConfig.ProgramArgs {
			jobConfig.ProgramArgs = draftJobConfig.ProgramArgs
		}
		if draftJobConfig.CheckpointInterval != jobConfig.CheckpointInterval {
			jobConfig.CheckpointInterval = draftJobConfig.CheckpointInterval
		}
		if draftJobConfig.ClsLogsetId != jobConfig.ClsLogsetId {
			jobConfig.ClsLogsetId = draftJobConfig.ClsLogsetId
		}
		if draftJobConfig.ClsTopicId != jobConfig.ClsTopicId {
			jobConfig.ClsTopicId = draftJobConfig.ClsTopicId
		}
		if draftJobConfig.EsServerlessIndex != jobConfig.EsServerlessIndex {
			jobConfig.EsServerlessIndex = draftJobConfig.EsServerlessIndex
		}
		if draftJobConfig.EsServerlessSpace != jobConfig.EsServerlessSpace {
			jobConfig.EsServerlessSpace = draftJobConfig.EsServerlessSpace
		}
		if draftJobConfig.PythonVersion != jobConfig.PythonVersion {
			jobConfig.PythonVersion = draftJobConfig.PythonVersion
		}
		if draftJobConfig.AutoRecover != jobConfig.AutoRecover {
			jobConfig.AutoRecover = draftJobConfig.AutoRecover
		}
		if draftJobConfig.LogLevel != jobConfig.LogLevel {
			jobConfig.LogLevel = draftJobConfig.LogLevel
		}
		if draftJobConfig.CheckpointRetainedNum != jobConfig.CheckpointRetainedNum {
			jobConfig.CheckpointRetainedNum = draftJobConfig.CheckpointRetainedNum
		}
		if draftJobConfig.CheckpointTimeoutSecond != jobConfig.CheckpointTimeoutSecond {
			jobConfig.CheckpointTimeoutSecond = draftJobConfig.CheckpointTimeoutSecond
		}
		if draftJobConfig.DefaultParallelism != jobConfig.DefaultParallelism {
			jobConfig.DefaultParallelism = draftJobConfig.DefaultParallelism
		}
		if draftJobConfig.MaxParallelism != jobConfig.MaxParallelism {
			jobConfig.MaxParallelism = draftJobConfig.MaxParallelism
		}
		if draftJobConfig.COSBucket != jobConfig.COSBucket {
			jobConfig.COSBucket = draftJobConfig.COSBucket
		}
		if draftJobConfig.JmCuSpec != jobConfig.JmCuSpec {
			jobConfig.JmCuSpec = draftJobConfig.JmCuSpec
		}
		if draftJobConfig.TmCuSpec != jobConfig.TmCuSpec {
			jobConfig.TmCuSpec = draftJobConfig.TmCuSpec
		}
		if draftJobConfig.Properties != jobConfig.Properties {
			jobConfig.Properties = draftJobConfig.Properties
		}
		if draftJobConfig.LogCollect != jobConfig.LogCollect {
			jobConfig.LogCollect = draftJobConfig.LogCollect
		}
		if draftJobConfig.FlinkVersion != jobConfig.FlinkVersion {
			jobConfig.FlinkVersion = draftJobConfig.FlinkVersion
		}
		if draftJobConfig.JobManagerCpu != jobConfig.JobManagerCpu {
			jobConfig.JobManagerCpu = draftJobConfig.JobManagerCpu
		}
		if draftJobConfig.JobManagerMem != jobConfig.JobManagerMem {
			jobConfig.JobManagerMem = draftJobConfig.JobManagerMem
		}
		if draftJobConfig.TaskManagerCpu != jobConfig.TaskManagerCpu {
			jobConfig.TaskManagerCpu = draftJobConfig.TaskManagerCpu
		}
		if draftJobConfig.TaskManagerMem != jobConfig.TaskManagerMem {
			jobConfig.TaskManagerMem = draftJobConfig.TaskManagerMem
		}
		if draftJobConfig.ExpertModeConfiguration != jobConfig.ExpertModeConfiguration {
			jobConfig.ExpertModeConfiguration = draftJobConfig.ExpertModeConfiguration
		}
		if draftJobConfig.TraceModeConfiguration != jobConfig.TraceModeConfiguration {
			jobConfig.TraceModeConfiguration = draftJobConfig.TraceModeConfiguration
		}
		if draftJobConfig.ClazzLevels != jobConfig.ClazzLevels {
			jobConfig.ClazzLevels = draftJobConfig.ClazzLevels
		}
		if draftJobConfig.EntrypointClass != jobConfig.EntrypointClass {
			jobConfig.EntrypointClass = draftJobConfig.EntrypointClass
		}
		if draftJobConfig.JobGraph != jobConfig.JobGraph {
			jobConfig.JobGraph = draftJobConfig.JobGraph
		}
	}

	jobConfigSetItem := &model.JobConfigSetItem{}
	jobConfigSetItem.JobId = job.SerialId
	jobConfigSetItem.EntrypointClass = jobConfig.EntrypointClass
	jobConfigSetItem.ProgramArgs = jobConfig.ProgramArgs
	jobConfigSetItem.CreatorUin = jobConfig.CreatorUin
	jobConfigSetItem.ClsLogsetId = jobConfig.ClsLogsetId
	jobConfigSetItem.ClsTopicId = jobConfig.ClsTopicId
	jobConfigSetItem.EsServerlessIndex = jobConfig.EsServerlessIndex
	jobConfigSetItem.EsServerlessSpace = jobConfig.EsServerlessSpace
	jobConfigSetItem.PythonVersion = jobConfig.PythonVersion
	jobConfigSetItem.AutoRecover = jobConfig.AutoRecover
	jobConfigSetItem.LogLevel = jobConfig.LogLevel
	jobConfigSetItem.CheckpointRetainedNum = jobConfig.CheckpointRetainedNum
	jobConfigSetItem.CheckpointTimeoutSecond = jobConfig.CheckpointTimeoutSecond
	jobConfigSetItem.CheckpointIntervalSecond = jobConfig.CheckpointInterval

	clazzLevels := make([]*log.ClazzLevel, 0, 0)
	if jobConfig.ClazzLevels != "" {
		err := json.Unmarshal([]byte(jobConfig.ClazzLevels), &clazzLevels)
		if err != nil {
			logger.Errorf("BuildJobConfigSetItemFromEntity json.Unmarshal(%s)  with error [%v]", jobConfig.ClazzLevels, err)
		}
	}
	jobConfigSetItem.ClazzLevels = clazzLevels
	programArgs, err := buildProgramArgs(jobConfig, jobType)
	if err != nil {
		return nil, err
	}
	jobConfigSetItem.ProgramArgs = programArgs
	jobConfigSetItem.Version = jobConfig.VersionId
	//jobConfigSetItem.Status = jobConfig.Status
	jobConfigSetItem.DefaultParallelism = jobConfig.DefaultParallelism
	jobConfigSetItem.MaxParallelism = jobConfig.MaxParallelism
	jobConfigSetItem.COSBucket = jobConfig.COSBucket
	jobConfigSetItem.CreateTime = service.SwitchDefaultTime(jobConfig.CreateTime)
	jobConfigSetItem.UpdateTime = service.SwitchDefaultTime(jobConfig.UpdateTime)
	jobConfigSetItem.Remark = jobConfig.Remark
	properties, err := service.RemoveDiagnosisVolumeMountedFromProperties(jobConfig.Properties)
	if err != nil {
		logger.Errorf("RemoveDiagnosisVolumeMountedFromProperties with error [%v]", err)
		return nil, err
	}
	jobConfigSetItem.Properties = properties
	if logCollect, err := GetLogCollect(job, jobConfig); err != nil {
		return nil, err
	} else {
		jobConfigSetItem.LogCollect = logCollect
	}

	if jobConfig.TraceModeConfiguration != "" && jobConfig.TraceModeConfiguration != "{}" {
		jobConfigSetItem.TraceModeOn = true
		jobConfigSetItem.TraceModeConfiguration = &model.TraceModeConfiguration{}
		err = json.Unmarshal([]byte(jobConfig.TraceModeConfiguration), jobConfigSetItem.TraceModeConfiguration)
		if err != nil {
			return nil, errorcode.InternalErrorCode.NewWithErr(err)
		}
	}

	if jobConfig.JobGraph != "" {
		jobConfigSetItem.JobGraph = &model.JobGraph{}
		err = json.Unmarshal([]byte(jobConfig.JobGraph), jobConfigSetItem.JobGraph)
		if err != nil {
			return nil, errorcode.InternalErrorCode.NewWithErr(err)
		}
	}
	service.CheckJobConfigCPUAndMem(jobConfig, job.CuMem)
	jobConfigSetItem.JobManagerSpec, jobConfigSetItem.TaskManagerSpec = jobConfig.JmCuSpec, jobConfig.TmCuSpec
	jobConfigSetItem.JobManagerCpu, jobConfigSetItem.JobManagerMem = jobConfig.JobManagerCpu, jobConfig.JobManagerMem
	jobConfigSetItem.TaskManagerCpu, jobConfigSetItem.TaskManagerMem = jobConfig.TaskManagerCpu, jobConfig.TaskManagerMem

	if jobConfig.ExpertModeConfiguration != "" && jobConfig.ExpertModeConfiguration != "{}" {
		jobConfigSetItem.ExpertModeOn = true
		jobConfigSetItem.ExpertModeConfiguration = &model.ExpertModeConfiguration{}
		err = json.Unmarshal([]byte(jobConfig.ExpertModeConfiguration), jobConfigSetItem.ExpertModeConfiguration)
		if err != nil {
			return nil, errorcode.InternalErrorCode.NewWithErr(err)
		}
	}
	jobConfigSetItem.FlinkVersion = job.FlinkVersion
	if jobConfig.FlinkVersion != "" {
		jobConfigSetItem.FlinkVersion = jobConfig.FlinkVersion
	} else {
		// 更新数据库jobConfig flinkversion
		service.UpdaetJobConfigFlinkVersionByJobId(jobConfig.Id, job.FlinkVersion)
	}

	return jobConfigSetItem, nil
}

func buildProgramArgs(jobConfig *table.JobConfig, jobType int8) (string, error) {
	if jobType == constants.JOB_TYPE_SQL || jobType == constants.JOB_TYPE_ETL {
		tmpMap := map[string]interface{}{}
		if len(jobConfig.ProgramArgs) != 0 {
			err := json.Unmarshal([]byte(jobConfig.ProgramArgs), &tmpMap)
			if err != nil {
				return "", errorcode.InternalErrorCode.NewWithErr(err)
			}
		}
		tmpMap["CheckpointInterval"] = jobConfig.CheckpointInterval
		decryptSqlCode, err := service5.DeEncodeAndDeEncryptSqlCode(jobConfig.SqlCode)
		if err != nil {
			return "", err
		}
		tmpMap["SqlCode"] = decryptSqlCode
		metadata, ok := tmpMap["Metadata"]
		if ok {
			metadatastr := metadata.(string)
			if len(metadatastr) != 0 {
				metadataCode, err := base64.StdEncoding.DecodeString(metadatastr)
				if err != nil {
					return "", err
				}
				tmpMap["Metadata"] = string(metadataCode)
			}
		}
		tmpMapBytes, err := json.Marshal(tmpMap)
		if err != nil {
			return "", errorcode.InternalErrorCode.NewWithErr(err)
		}
		return string(tmpMapBytes), nil
	} else {
		return jobConfig.ProgramArgs, nil
	}
}

func GetJobConfigResourceRefByJobConfigIds(jobConfigIds []int64) (map[int64][]*model.ResourceRefDetail, error) {
	result, err := getJobConfigOriginalResourceRef(jobConfigIds)
	if err != nil {
		return nil, err
	}
	fixResourceRefVersion(result)
	return result, nil
}

func getJobConfigOriginalResourceRef(jobConfigIds []int64) (map[int64][]*model.ResourceRefDetail, error) {
	result := make(map[int64][]*model.ResourceRefDetail)
	if len(jobConfigIds) == 0 {
		return result, nil
	}
	cond := dao.NewCondition()
	cond.In("ResourceRef.JobConfigId", jobConfigIds)
	cond.Ne("ResourceRef.Status", constants.RESOURCE_REF_STATUS_DELETE)
	where, args := cond.GetWhere()
	sql := "SELECT  ResourceRef.VersionId AS Version,  ResourceRef.UsageType as Type," +
		"ResourceRef.JobConfigId AS JobConfigId,  " +
		"Resource.ResourceId AS ResourceId,  Resource.ResourceName AS Name, " +
		"Resource.SystemProvide as SystemProvide, " +
		"Resource.Connector as Connector, " +
		"Resource.Id AS ResourceIntId  FROM  ResourceRef,  " +
		"Resource" + where + " AND ResourceRef.ResourceId = Resource.Id"
	_, data, err := service2.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return nil, err
	}

	for _, d := range data {
		detail := &model.ResourceRefDetail{}
		err = util.ScanMapIntoStruct(detail, d)
		if err != nil {
			return nil, errorcode.InternalErrorCode.NewWithErr(err)
		}
		v, ok := result[detail.JobConfigId]
		if !ok {
			v = make([]*model.ResourceRefDetail, 0, 0)
		}
		if detail.SystemProvide == constants.USER_CONNECTOR {
			detail.Name = detail.Connector
		}
		v = append(v, detail)
		result[detail.JobConfigId] = v
	}
	return result, nil
}

func fixResourceRefVersion(resourceRefs map[int64][]*model.ResourceRefDetail) error {
	resourceIds := make([]int64, 0, 0)
	resourceIdSet := make(map[int64]struct{})
	for _, details := range resourceRefs {
		for _, detail := range details {
			if detail.Version == constants.RESOURCE_REF_VERSION_ID_USE_LATEST {
				if _, ok := resourceIdSet[detail.ResourceIntId]; !ok {
					resourceIdSet[detail.ResourceIntId] = struct{}{}
					resourceIds = append(resourceIds, detail.ResourceIntId)
				}
			}
		}
	}

	resourceConfigMaxVersion, err := service3.GetResourceConfigMaxVersion(resourceIds)
	if err != nil {
		logger.Errorf("GetResourceConfigMaxVersion return err:%+v", err)
		return err
	}

	for _, details := range resourceRefs {
		for _, detail := range details {
			if detail.Version == constants.RESOURCE_REF_VERSION_ID_USE_LATEST {
				if v, ok := resourceConfigMaxVersion[detail.ResourceIntId]; ok {
					detail.Version = v
				}
			}
		}
	}
	return nil
}

func GetLogCollect(job *table2.Job, jobConfig *table.JobConfig) (logCollect int, err error) {
	if jobConfig.ClsLogsetId != "" && jobConfig.ClsTopicId != "" && jobConfig.LogCollect == constants.JobLogCollectEnabled {
		// 对于可以绑定多个CLS的作业来说，CLS信息在jobConfig中
		return constants.JobLogCollectEnabled, nil
	}
	if jobConfig.LogCollect == constants.JobLogCollectEnabledOnCos {
		// 运行日志上传到COS时，logCollect=4
		return constants.JobLogCollectEnabledOnCos, nil
	}

	if jobConfig.LogCollect == constants.JobLogCollectEnabledOnES {
		// 运行日志上传到ES时，logCollect=5
		return constants.JobLogCollectEnabledOnES, nil
	}

	cgService, err := service4.NewClusterGroupService(job.ClusterGroupId)
	if err != nil {
		return
	}

	_, topicId, historyCluster, err := cgService.GetClsInfo()
	if err != nil {
		return
	}
	// 新集群以jobConfig为准
	if !historyCluster {
		return jobConfig.LogCollect, nil
	}

	// 历史集群处理方案：https://iwiki.woa.com/pages/viewpage.action?pageId=537112625
	// 1. 已启用日志采集的集群，显示为不适用
	// 2. 未启用日志采集的集群，显示为未启用

	// 地域日志集不存在，即未开启
	if len(topicId) == 0 {
		return constants.JobLogCollectHistoryDisabled, nil
	}
	// 地域日志集存在，还需检查crd，若crd已删除的处理，表明客户已提工单处理，进入历史集群未开启日志采集状态
	if cluster, err := cgService.GetActiveCluster(); err != nil {
		return 0, err
	} else if exist, err := service4.LogConfigCrdExists(cluster); err != nil {
		return 0, err
	} else if !exist {
		return constants.JobLogCollectHistoryDisabled, nil
	}

	return constants.JobLogCollectHistoryEnabled, nil
}
