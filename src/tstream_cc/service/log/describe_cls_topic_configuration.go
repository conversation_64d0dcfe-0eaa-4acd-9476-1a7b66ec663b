package service

import (
	"encoding/base64"
	"encoding/json"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/log"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cls"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
)

type DescribeClsTopicConfigurationService struct {
}

func (this *DescribeClsTopicConfigurationService) DescribeClsTopicConfiguration(req *log.DescribeClsTopicConfigurationReq) (
	strErr, msg string, rsp *log.DescribeClsTopicConfigurationRsp) {
	perfectConf, err := service2.GetTableService().GetJobRunningLogConf()
	if err != nil {
		logger.Errorf("[%s] getJobRunningLogConf err : %v", req.RequestId, err)
		return controller.InternalError, err.Error(), nil
	}

	secretId, secretKey, token, pass, err := service.StsAssumeRole(req.Uin, req.SubAccountUin, req.Region)
	if err != nil || !pass {
		logger.Errorf("[%s] stsAssumeRolePreferred err : %v", req.RequestId, err)
		return controller.FailedOperation_UserNotAuthenticated, err.Error(), nil
	}

	//userConfig, err := cls.GetIndexWithAuth(secretId, secretKey, token, req.Region, req.TopicId)
	adapter := &cls.CloudApiAdapter{}
	userConfig, err := adapter.GetIndexWithAuth(secretId, secretKey, token, req.Region, req.TopicId)
	if err != nil {
		logger.Errorf("[%s] GetIndexWithAuth err : %v", req.RequestId, err)
		return controller.InternalError, err.Error(), nil
	}

	perfect, config := func() (bool, string) {
		b1, _ := json.Marshal(perfectConf)
		b2 := []byte("")

		if userConfig.Effective {
			b2, _ = json.Marshal(&log.JobRunningLogConfListener{Rule: userConfig.Rule})
		}

		return string(b1) == string(b2), base64.StdEncoding.EncodeToString([]byte(userConfig.String()))
	}()

	rsp = &log.DescribeClsTopicConfigurationRsp{
		Perfect:            perfect,
		IndexConfiguration: config,
	}

	return controller.OK, controller.NULL, rsp
}
