package service

import (
	"fmt"
	"reflect"
	"strconv"
	"time"

	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/log"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job_config"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job_instance"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cls"
)

type GetJobInstanceService struct {
}

func (this *GetJobInstanceService) GetJobInstance(req *log.GetJobInstanceReq) (rsp *log.GetJobInstanceRsp, err error) {
	job, err := service.WhetherJobExists(0, req.Region, req.JobId)
	if err != nil {
		return nil, err
	}

	// SELECT "__TAG__.pod_label_jobRunningOrderId" WHERE "__TAG__.pod_label_jobSerialId"='cql-7qcgratz' GROUP BY "__TAG__.pod_label_jobRunningOrderId" LIMIT 1000
	// 字段加上双引号，性能更优
	query := fmt.Sprintf("*| SELECT \"%s\" WHERE \"%s\"='%s' GROUP BY \"%s\" LIMIT %d",
		req.RunningOrderIdKey, req.JobSerialIdKey, req.JobId, req.RunningOrderIdKey, MAX_CLS_RESULT_SQL_GROUP_BY)
	//query := fmt.Sprintf("SELECT %s WHERE %s='%s' GROUP BY %s LIMIT %d",
	//	req.RunningOrderIdKey, req.JobSerialIdKey, req.JobId, req.RunningOrderIdKey, MAX_CLS_RESULT_SQL_GROUP_BY)
	//clsRsp, err := cls.SearchLogWithAuth(req.SecretId, req.SecretKey, req.Token, req.Region,
	adapter := &cls.CloudApiAdapter{}
	clsRsp, err := adapter.SearchLogWithAuth(req.SecretId, req.SecretKey, req.Token, req.Region,
		&log.SearchLogReq{
			LogSetId:  req.LogSetId,
			TopicIds:  req.TopicId,
			StartTime: req.StartTime,
			EndTime:   req.EndTime,
			Query:     query,
			Limit:     MAX_CLS_RESULT_PER_REQUEST,
			Context:   req.Cursor,
			Sort:      "",
		})
	if err != nil {
		return nil, err
	}
	if !clsRsp.SqlFlag {
		return nil, errorcode.InternalError_CLS.New()
	}
	rsp = &log.GetJobInstanceRsp{
		Cursor:          clsRsp.Context,
		ListOver:        clsRsp.ListOver,
		JobInstanceList: nil,
	}
	if len(clsRsp.Results) == 0 {
		return rsp, nil
	}

	jobInstanceList := make([]*log.JobInstanceList, 0, len(clsRsp.Results))
	runningOrderIdList := make([]int64, 0, len(clsRsp.Results))
	for _, it := range clsRsp.Results {
		logger.Debugf("result : it %v ", it)
		runningOrderIdIntf, exist := it[req.RunningOrderIdKey]
		if !exist {
			logger.Warningf("[%s] key not exist %s", req.RequestId, req.RunningOrderIdKey)
			continue
		}
		// 索引类型long\double都是解析出float64
		runningOrderId, ok := runningOrderIdIntf.(float64)
		if !ok {
			logger.Warningf("runningOrderIdIntf: [%v] [%s] value of key %s is not float64, is %s, so use strconv.ParseFloat(runningOrderIdIntf.(string), 64)", runningOrderIdIntf, req.RequestId, req.RunningOrderIdKey,
				reflect.TypeOf(runningOrderIdIntf).Name())
			if reflect.TypeOf(runningOrderIdIntf).Name() == "string" {
				runningOrderId, err = strconv.ParseFloat(runningOrderIdIntf.(string), 64)
				if err != nil {
					logger.Errorf("runningOrderIdIntf: [%v] use strconv.ParseFloat(runningOrderIdIntf.(string), 64) error %v", runningOrderIdIntf, err)
					continue
				}
			} else {
				continue
			}
		}
		runningOrderIdList = append(runningOrderIdList, int64(runningOrderId))
		jobInstanceList = append(jobInstanceList, &log.JobInstanceList{
			RunningOrderId:        int64(runningOrderId),
			JobInstanceCreateTime: "-",
			JobInstanceStartTime:  "-",
			JobInstanceStopTime:   "-",
			StartingMillis:        0,
		})
	}
	if len(runningOrderIdList) == 0 {
		return rsp, nil
	}

	jobInstanceListDb, err := service.ListJobInstanceByRunningOrderId(job.Id, runningOrderIdList)
	if err != nil {
		return nil, err
	}
	jobInstanceDbMap := make(map[int64]*table.JobInstance, len(jobInstanceListDb))
	if len(jobInstanceListDb) > 0 {
		for _, it := range jobInstanceListDb {
			jobInstanceDbMap[it.RunningOrderId] = it
		}
	}

	getTimeStr := func(t string) string {
		if t == DEFAULT_TIME_STR {
			return "-"
		} else {
			return t
		}
	}

	getStartingMillis := func(inst *table.JobInstance) int64 {
		loc, _ := time.LoadLocation("Local")
		createTime, err := time.ParseInLocation("2006-01-02 15:04:05", inst.CreateTime, loc)
		if err != nil {
			return 0
		}
		if inst.StartTime == DEFAULT_TIME_STR {
			return 0
		}
		startTime, err := time.ParseInLocation("2006-01-02 15:04:05", inst.StartTime, loc)
		if err != nil {
			return 0
		}
		return (startTime.Unix() - createTime.Unix()) * 1000
	}

	for _, it := range jobInstanceList {
		inst, exist := jobInstanceDbMap[it.RunningOrderId]
		if !exist {
			continue
		}

		it.JobInstanceCreateTime = getTimeStr(inst.CreateTime)
		it.JobInstanceStartTime = getTimeStr(inst.StartTime)
		it.JobInstanceStopTime = getTimeStr(inst.StopTime)
		it.StartingMillis = getStartingMillis(inst)
	}

	service.SortSliceByField(jobInstanceList, "RunningOrderId", true)
	rsp.JobInstanceList = jobInstanceList
	return rsp, nil
}

// GetJobInstanceV2 仅获取作业实例
func (this *GetJobInstanceService) GetJobInstanceV2(req *log.GetJobInstanceReq) (rsp *log.GetJobInstanceRsp, jobConfigsMap map[int64]*table2.JobConfig, err error) {
	job, err := service.WhetherJobExists(0, req.Region, req.JobId)
	if err != nil {
		return nil, nil, err
	}
	rsp = &log.GetJobInstanceRsp{
		JobInstanceList: nil,
	}
	// 查出所有job instance
	jobInstanceListDb, err := service.ListJobInstanceByRunningOrderId(job.Id, nil)
	if err != nil {
		logger.Errorf("query jobInstance failed with err [%v]", err)
		return nil, nil, err
	}
	var jobConfigIds []int64
	jobInstanceList := make([]*log.JobInstanceList, 0, len(jobInstanceListDb))
	for _, instance := range jobInstanceListDb {
		jobInstanceList = append(jobInstanceList, &log.JobInstanceList{
			RunningOrderId:        instance.RunningOrderId,
			JobInstanceCreateTime: "-",
			JobInstanceStartTime:  "-",
			JobInstanceStopTime:   "-",
			StartingMillis:        0,
		})
		jobConfigIds = append(jobConfigIds, instance.JobConfigId)
	}

	jobConfigs, err := service.GetJobConfigByIds(jobConfigIds)
	jobConfigsMap = make(map[int64]*table2.JobConfig)
	if err != nil {
		logger.Errorf("query jobConfigs failed with err [%v]", err)
		return nil, nil, err
	}

	jobInstanceDbMap := make(map[int64]*table.JobInstance, len(jobInstanceListDb))
	if len(jobInstanceListDb) > 0 {
		for _, it := range jobInstanceListDb {
			jobInstanceDbMap[it.RunningOrderId] = it
		}
	}
	getTimeStr := func(t string) string {
		if t == DEFAULT_TIME_STR {
			return "-"
		} else {
			return t
		}
	}
	getStartingMillis := func(inst *table.JobInstance) int64 {
		loc, _ := time.LoadLocation("Local")
		createTime, err := time.ParseInLocation("2006-01-02 15:04:05", inst.CreateTime, loc)
		if err != nil {
			return 0
		}
		if inst.StartTime == DEFAULT_TIME_STR {
			return 0
		}
		startTime, err := time.ParseInLocation("2006-01-02 15:04:05", inst.StartTime, loc)
		if err != nil {
			return 0
		}
		return (startTime.Unix() - createTime.Unix()) * 1000
	}

	for _, it := range jobInstanceList {
		inst, exist := jobInstanceDbMap[it.RunningOrderId]
		if !exist {
			continue
		}
		it.JobInstanceCreateTime = getTimeStr(inst.CreateTime)
		it.JobInstanceStartTime = getTimeStr(inst.StartTime)
		it.JobInstanceStopTime = getTimeStr(inst.StopTime)
		it.StartingMillis = getStartingMillis(inst)
		for _, jobConfig := range jobConfigs {
			if inst.JobConfigId == jobConfig.Id {
				jobConfigsMap[it.RunningOrderId] = jobConfig
			}
		}
	}

	service.SortSliceByField(jobInstanceList, "RunningOrderId", true)
	rsp.JobInstanceList = jobInstanceList
	return rsp, jobConfigsMap, nil
}
