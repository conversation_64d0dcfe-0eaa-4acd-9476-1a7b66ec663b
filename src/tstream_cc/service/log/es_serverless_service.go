package service

import (
	"encoding/json"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/errors"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/profile"
	es "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/es/v20180416"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service2 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/serverless"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config"
)

type EsServerlessService struct {
	SecretId  string
	SecretKey string
	Token     string
	Region    string
}

// createServerlessInstance 创建 es serverless 索引
func (s *EsServerlessService) createServerlessInstance(request *es.CreateServerlessInstanceRequest) (
	response *es.CreateServerlessInstanceResponse, err error) {
	credential := common.NewCredential(s.SecretId, s.SecretKey)
	if s.Token != "" {
		credential.Token = s.Token
	}
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = GetEsEndPoint(s.Region)
	client, err := es.NewClient(credential, s.Region, cpf)
	if err != nil {
		logger.Errorf("NewClient has return error %v", err)
		return response, err
	}
	response, err = client.CreateServerlessInstance(request)
	if _, ok := err.(*errors.TencentCloudSDKError); ok {
		logger.Errorf("An API error has returned: %s", err)
		return response, err
	}
	if err != nil {
		logger.Errorf("An API error has returned: %s", err)
		return response, err
	}
	logger.Infof("createServerlessInstance rsp %s", response.ToJsonString())
	return response, err
}

// describeServerlessInstances 查询 es serverless 索引
func (s *EsServerlessService) describeServerlessInstances(request *es.DescribeServerlessInstancesRequest, uin, subAccountUin string) (
	response *es.DescribeServerlessInstancesResponse, err error) {
	credential := common.NewCredential(s.SecretId, s.SecretKey)
	if s.Token != "" {
		credential.Token = s.Token
	}
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = GetEsEndPoint(s.Region)
	cpf.HttpProfile.ReqTimeout = constants.HTTP_PROFILE_REQ_TIMEOUT
	client, err := es.NewClient(credential, s.Region, cpf)
	if err != nil {
		logger.Errorf("NewClient has return error %v", err)
		return response, err
	}
	response, err = client.DescribeServerlessInstances(request)
	//if _, ok := err.(*errors.TencentCloudSDKError); ok {
	//	logger.Errorf("An API error has returned: %s", err)
	//	return response, err
	//}
	if err != nil {
		logger.Errorf("An API error has returned: %s", err)
		response, err = queryServerlessInstance(uin, subAccountUin, s.Region, request.InstanceIds, request.SpaceIds)
		if err != nil {
			logger.Errorf("queryServerlessInstance from DB has return error %v", err)
			return response, err
		}
		return response, err
	}
	SaveOrUpdateServerlessInstance(uin, subAccountUin, s.Region, response)
	logger.Infof("describeServerlessInstances rsp %s", response.ToJsonString())
	return response, err
}

// createServerlessSpace 创建 es serverless 空间
func (s *EsServerlessService) createServerlessSpace(request *es.CreateServerlessSpaceRequest) (
	response *es.CreateServerlessSpaceResponse, err error) {
	credential := common.NewCredential(s.SecretId, s.SecretKey)
	if s.Token != "" {
		credential.Token = s.Token
	}
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = GetEsEndPoint(s.Region)
	client, err := es.NewClient(credential, s.Region, cpf)
	if err != nil {
		logger.Errorf("NewClient has return error %v", err)
		return response, err
	}
	response, err = client.CreateServerlessSpace(request)
	if _, ok := err.(*errors.TencentCloudSDKError); ok {
		logger.Errorf("An API error has returned: %s", err)
		return response, err
	}
	if err != nil {
		logger.Errorf("An API error has returned: %s", err)
		return response, err
	}
	logger.Infof("createServerlessSpace rsp %s", response.ToJsonString())
	return response, err
}

// describeServerlessSpaces 获取 es serverless 空间
func (s *EsServerlessService) describeServerlessSpaces(request *es.DescribeServerlessSpacesRequest, uin, subAccountUin string) (
	response *es.DescribeServerlessSpacesResponse, err error) {
	credential := common.NewCredential(s.SecretId, s.SecretKey)
	if s.Token != "" {
		credential.Token = s.Token
	}
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = GetEsEndPoint(s.Region)
	cpf.HttpProfile.ReqTimeout = constants.HTTP_PROFILE_REQ_TIMEOUT
	client, err := es.NewClient(credential, s.Region, cpf)
	if err != nil {
		logger.Errorf("NewClient has return error %v", err)
		return response, err
	}
	response, err = client.DescribeServerlessSpaces(request)
	//if _, ok := err.(*errors.TencentCloudSDKError); ok {
	//	logger.Errorf("An API error has returned: %s", err)
	//	return response, err
	//}
	if err != nil {
		logger.Errorf("An API error has returned: %s", err)
		response, err = queryServerlessSpace(uin, subAccountUin, s.Region, request.SpaceIds)
		if err != nil {
			logger.Errorf("queryServerlessSpace from DB has return error %v", err)
			return response, err
		}
		return response, err
	}

	SaveOrUpdateServerlessSpace(uin, subAccountUin, s.Region, response)
	logger.Infof("describeServerlessSpaces rsp %s", response.ToJsonString())
	return response, err
}

// searchServerlessData 查询日志
func (s *EsServerlessService) searchServerlessData(request *es.SearchServerlessDataRequest) (
	response *es.SearchServerlessDataResponse, err error) {
	credential := common.NewCredential(s.SecretId, s.SecretKey)
	if s.Token != "" {
		credential.Token = s.Token
	}
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = GetEsEndPoint(s.Region)
	client, _ := es.NewClient(credential, s.Region, cpf)
	response, err = client.SearchServerlessData(request)
	if _, ok := err.(*errors.TencentCloudSDKError); ok {
		logger.Errorf("An API error has returned: %s", err)
		return response, err
	}
	if err != nil {
		logger.Errorf("An API error has returned: %s", err)
		return response, err
	}
	logger.Infof("searchServerlessData rsp %s", response.ToJsonString())
	return response, err
}

// describeServerlessInstanceUsers 查询 es serverless 索引用户名和密码
func (s *EsServerlessService) describeServerlessInstanceUsers(request *es.DescribeServerlessInstanceUsersRequest, uin, subAccountUin string) (
	response *es.DescribeServerlessInstanceUsersResponse, err error) {
	credential := common.NewCredential(s.SecretId, s.SecretKey)
	if s.Token != "" {
		credential.Token = s.Token
	}
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = GetEsEndPoint(s.Region)
	client, _ := es.NewClient(credential, s.Region, cpf)
	response, err = client.DescribeServerlessInstanceUsers(request)
	//if _, ok := err.(*errors.TencentCloudSDKError); ok {
	//	logger.Errorf("An API error has returned: %s", err)
	//	return response, err
	//}
	if err != nil {
		logger.Errorf("An API error has returned: %s", err)
		response, err = queryServerlessInstanceUser(uin, subAccountUin, s.Region, request.InstanceId)
		if err != nil {
			logger.Errorf("queryServerlessSpace from DB has return error %v", err)
			return response, err
		}
		return response, err
	}

	SaveOrUpdateServerlessInstanceUser(uin, subAccountUin, s.Region, response, request.InstanceId)
	logger.Infof("describeServerlessInstanceUsers rsp %s", response.ToJsonString())
	return response, err
}

// searchServerlessInstance 查询日志
func (s *EsServerlessService) searchServerlessInstance(request *es.SearchServerlessInstanceRequest) (
	response *es.SearchServerlessInstanceResponse, err error) {
	credential := common.NewCredential(s.SecretId, s.SecretKey)
	if s.Token != "" {
		credential.Token = s.Token
	}
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = GetEsEndPoint(s.Region)
	client, _ := es.NewClient(credential, s.Region, cpf)
	logger.Infof("searchServerlessInstance request:{}", request)
	response, err = client.SearchServerlessInstance(request)
	if _, ok := err.(*errors.TencentCloudSDKError); ok {
		logger.Errorf("An API error has returned: %s", err)
		return response, err
	}
	if err != nil {
		logger.Errorf("An API error has returned: %s", err)
		return response, err
	}
	logger.Infof("searchServerlessInstance rsp %s", response.ToJsonString())
	return response, err
}

func queryServerlessInstance(uin, SubAccountUin, region string, instanceIds, spaceIds []*string) (response *es.DescribeServerlessInstancesResponse, err error) {
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf("%s: Failed to query ServerlessInstance, with error: %+v", uin, err)
		}
	}()
	response = &es.DescribeServerlessInstancesResponse{
		Response: &es.DescribeServerlessInstancesResponseParams{},
	}

	txm := service2.GetTxManager()
	args := []interface{}{uin, SubAccountUin, region}
	sql := "select * from ServerlessInstance where Uin = ? and SubAccountUin = ? and region = ?"
	if len(instanceIds) > 0 {
		sql += " and InstanceId in ("
		for i, id := range instanceIds {
			if i > 0 {
				sql += ","
			}
			sql += "?"
			args = append(args, *id)
		}
		sql += ")"
	}
	if len(spaceIds) > 0 {
		sql += " and SpaceId in ("
		for i, id := range spaceIds {
			if i > 0 {
				sql += ","
			}
			sql += "?"
			args = append(args, *id)
		}
		sql += ")"
	}
	count, data, err := txm.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("%s: Failed to query YunTiRsp, with error: %+v", uin, err)
		return
	}
	logger.Debugf("%s: query DB result: %+v", data)
	indexMetaFields := make([]*es.ServerlessIndexMetaField, 0)
	for _, d := range data {
		instance := &serverless.ServerlessInstance{}
		err = util.ScanMapIntoStruct(instance, d)
		if err != nil {
			logger.Errorf("%s: Failed to ScanMapIntoStruct, with error: %+v", uin, err)
			return
		}
		fild := &es.ServerlessIndexMetaField{
			IndexNetworkField: &es.ServerlessIndexNetworkField{},
			DiDataList:        make([]*es.DiData, 0),
		}
		fild.IndexName = &instance.IndexName
		fild.InstanceId = &instance.InstanceId
		logger.Debugf("%s: query DB instance: %+v", uin, instance)
		err := json.Unmarshal([]byte(instance.IndexNetworkField), fild.IndexNetworkField)
		if err != nil {
			logger.Errorf("%s: Failed to Unmarshal, with error: %+v", uin, err)
			return nil, err
		}
		fild.SpaceId = &instance.SpaceId
		fild.SpaceName = &instance.SpaceName
		err = json.Unmarshal([]byte(instance.DiDataList), &fild.DiDataList)
		if err != nil {
			logger.Errorf("%s: Failed to Unmarshal, with error: %+v", uin, err)
			return nil, err
		}
		indexMetaFields = append(indexMetaFields, fild)
	}
	response.Response.IndexMetaFields = indexMetaFields
	response.Response.TotalCount = common.Int64Ptr(int64(count))

	return response, nil
}

func SaveOrUpdateServerlessInstance(uin, subAccountUin, region string, response *es.DescribeServerlessInstancesResponse) {
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf("%s: Failed to save ServerlessInstance, with error: %+v", uin, err)
		}
	}()
	txm := service2.GetTxManager()
	if *response.Response.TotalCount == 0 {
		return
	}
	txm.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		sql := "insert into ServerlessInstance(Uin,SubAccountUin,Region,IndexName,InstanceId,IndexNetworkField,SpaceId,SpaceName,DiDataList) values"
		data := make([]interface{}, 0)
		for i, s := range response.Response.IndexMetaFields {
			if i > 0 {
				sql += ","
			}
			sql += "(?,?,?,?,?,?,?,?,?)"
			indexNetworkField, err := json.Marshal(s.IndexNetworkField)
			if err != nil {
				logger.Errorf("%s: Failed to Marshal, with error: %+v", uin, err)
				return err
			}
			diDataList, err := json.Marshal(s.DiDataList)
			if err != nil {
				logger.Errorf("%s: Failed to Marshal, with error: %+v", uin, err)
				return err
			}
			args := []interface{}{uin, subAccountUin, region, *s.IndexName, *s.InstanceId, string(indexNetworkField), *s.SpaceId, *s.SpaceName, string(diDataList)}
			data = append(data, args...)
		}
		sql += " on duplicate key update DiDataList=values(DiDataList),IndexNetworkField=values(IndexNetworkField),SpaceName=values(SpaceName),IndexName=values(IndexName)"
		tx.ExecuteSqlWithArgs(sql, data...)
		return nil
	}).Close()
	return
}

func queryServerlessSpace(uin, SubAccountUin, region string, ids []*string) (response *es.DescribeServerlessSpacesResponse, err error) {
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf("%s: Failed to query ServerlessSpace, with error: %+v", uin, err)
		}
	}()
	response = &es.DescribeServerlessSpacesResponse{
		Response: &es.DescribeServerlessSpacesResponseParams{},
	}
	txm := service2.GetTxManager()
	args := []interface{}{uin, SubAccountUin, region}
	sql := "select * from ServerlessSpace where Uin = ? and SubAccountUin = ? and region = ?"
	if len(ids) > 0 {
		sql += " and SpaceId in ("
		for i, id := range ids {
			if i > 0 {
				sql += ","
			}
			sql += "?"
			args = append(args, *id)
		}
		sql += ")"
	}
	count, data, err := txm.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("%s: Failed to query YunTiRsp, with error: %+v", uin, err)
		return
	}
	spaces := make([]*es.ServerlessSpace, 0)
	for _, d := range data {
		instance := &serverless.ServerlessSpace{}
		err = util.ScanMapIntoStruct(instance, d)
		space := &es.ServerlessSpace{}
		space.SpaceId = &instance.SpaceId
		space.SpaceName = &instance.SpaceName
		spaces = append(spaces, space)
	}
	response.Response.ServerlessSpaces = spaces
	response.Response.TotalCount = common.Int64Ptr(int64(count))
	return response, nil
}

func SaveOrUpdateServerlessSpace(uin, subAccountUin, region string, response *es.DescribeServerlessSpacesResponse) {
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf("%s: Failed to save ServerlessSpace, with error: %+v", uin, err)
		}
	}()
	txm := service2.GetTxManager()
	if *response.Response.TotalCount == 0 {
		return
	}
	txm.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		sql := "insert into ServerlessSpace(Uin,SubAccountUin,Region,SpaceId,SpaceName) values"
		data := make([]interface{}, 0)
		for i, s := range response.Response.ServerlessSpaces {
			if i > 0 {
				sql += ","
			}
			sql += "(?,?, ?, ?, ?)"
			args := []interface{}{uin, subAccountUin, region, *s.SpaceId, *s.SpaceName}
			data = append(data, args...)
		}
		sql += " on duplicate key update SpaceName = values(SpaceName)"
		tx.ExecuteSqlWithArgs(sql, data...)
		return nil
	}).Close()
	return
}

// userType 都是2
func queryServerlessInstanceUser(uin, SubAccountUin, region string, id *string) (response *es.DescribeServerlessInstanceUsersResponse, err error) {
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf("%s: Failed to query ServerlessInstanceUser, with error: %+v", uin, err)
		}
	}()
	response = &es.DescribeServerlessInstanceUsersResponse{
		Response: &es.DescribeServerlessInstanceUsersResponseParams{},
	}
	txm := service2.GetTxManager()
	args := []interface{}{uin, SubAccountUin, region, *id}
	sql := "select * from ServerlessInstanceUser where Uin = ? and SubAccountUin = ? and region = ? and instanceId = ?"
	count, data, err := txm.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("%s: Failed to query YunTiRsp, with error: %+v", uin, err)
		return
	}

	value, err := config.GetRainbowConfiguration("ConfigureCenter.Flow.Common", "esServerlessUser")
	if err != nil {
		logger.Errorf("%s: Failed to get username and password  from rainbow, with error: %+v", uin, err)
		return
	}

	users := make([]*es.ServerlessInstanceUser, 0)
	for _, d := range data {
		instance := &serverless.ServerlessInstanceUser{}
		err = util.ScanMapIntoStruct(instance, d)
		user := &es.ServerlessInstanceUser{}
		user.CreateTime = &instance.CreateTime
		user.Username = &value
		user.Password = &value
		users = append(users, user)
	}
	response.Response.InstanceUsers = users
	response.Response.TotalCount = common.Int64Ptr(int64(count))
	return response, nil
}

// User只
func SaveOrUpdateServerlessInstanceUser(uin, subAccountUin, region string, response *es.DescribeServerlessInstanceUsersResponse, id *string) {
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf("%s: Failed to save ServerlessInstanceUser, with error: %+v", uin, err)
		}
	}()
	txm := service2.GetTxManager()
	if *response.Response.TotalCount == 0 {
		return
	}
	txm.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		sql := "insert into ServerlessInstanceUser(Uin, SubAccountUin, Region, InstanceId, Username, Password, CreateTime) values"
		data := make([]interface{}, 0)
		for i, user := range response.Response.InstanceUsers {
			if i > 0 {
				sql += ","
			}
			sql += "(?, ?, ?, ?, ?, ?,?)"
			args := []interface{}{uin, subAccountUin, region, *id, "", "", *user.CreateTime}
			data = append(data, args...)
		}
		sql += " on duplicate key update Password = values(Password),Username = values(Username)"
		tx.ExecuteSqlWithArgs(sql, data...)
		return nil
	}).Close()
	return
}
