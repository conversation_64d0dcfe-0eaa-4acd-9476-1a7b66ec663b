package service

//
//import (
//	"flag"
//	"fmt"
//	"io/ioutil"
//	"os"
//	"tencentcloud.com/tstream_galileo/src/common/dao"
//	"tencentcloud.com/tstream_galileo/src/common/errorcode"
//	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
//	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
//	"testing"
//	"time"
//
//	logconfig "git.woa.com/tke/logconfig/pkg/generated/clientset/versioned"
//)
//
//var (
//	fTestSecretId = flag.String("test.secret.id", "--",
//		"oceanus account")
//	fTestSecretKey = flag.String("test.secret.key", "--",
//		"oceanus account")
//	fTestOwnerUin      = flag.String("test.owner.uin", "************", "")
//	fTestSubAccountUin = flag.String("test.subaccount.uin", "************", "")
//	fTestRegion        = flag.String("test.region", "ap-guangzhou",
//		"e.g. ap-guangzhou ap-beijing ap-shanghai ap-chengdu")
//	fTestAuthDuration = flag.Uint64("test.auth.duration", 43200, "")
//	fTestToken        = flag.String("test.token", "", "")
//	fTestLogSetId     = flag.String("test.logset.id", "b3504390-7317-44fd-8646-f8653055dc03", "")
//	fTestTopicIds     = flag.String("test.topic.id", "c2694bcd-8741-4888-85b3-3781506a799f", "sep by ','")
//	fTestLogSetPeriod = flag.Int("test.logset.period", 7, "")
//	fTestClsGroupName = flag.String("test.cls.group.name", "test-xxx", "")
//	fTestClsGroupId   = flag.String("test.cls.group.id", "", "")
//
//	fTestStartTime = flag.Int64("test.start.time", time.Now().Add(-24*time.Hour).Unix(), "")
//	fTestEndTime   = flag.Int64("test.end.time", time.Now().Unix(), "")
//	fTestQuery     = flag.String("test.query", "", "")
//	fTestContext   = flag.String("test.context", "", "")
//	fTestLimit     = flag.Int("test.limit", 100, "")
//	fTestDbUrl     = flag.String("test.db.url", ":@tcp(127.0.0.1:3306)/tstream_galileo_20191031?charset=utf8", "")
//
//	fTestKubeConfigPath = flag.String("test.kube.config.path",
//		fmt.Sprintf("%s/.kube/config", os.Getenv("HOME")), "")
//	fTestKubeContext = flag.String("test.kube.context", "cls-cf5wqdwy-context-default", "")
//	fTestLcName      = flag.String("test.lc.name", "oceanus", "crd lc name")
//	fTestClusterId   = flag.String("test.cluster.id", "cls-cf5wqdwy", "")
//)
//
//var (
//	dbInited bool
//
//	logcClient logconfig.Interface
//)
//
//func init() {
//	testing.Init()
//	flag.Parse()
//}
//
//func initDb() {
//	if dbInited {
//		return
//	}
//
//	if tx, err := dao.NewDataSourceTransactionManager(*fTestDbUrl); err == nil {
//		dbInited = true
//		service.SetTxManager(tx)
//	} else {
//		panic(errorcode.NewStackError(errorcode.InternalErrorCode, "", err))
//	}
//}
//
//func initK8s() {
//	if logcClient != nil {
//		return
//	}
//
//	//if c, err := NewLogConfigClient(*fTestKubeContext, *fTestKubeConfigPath); err != nil {
//	//	panic(err)
//	//} else {
//	//	logcClient = c
//	//}
//
//	initDb()
//	configBytes, err := service2.GetTableService().GetTkeClusterKubeConfig(*fTestClusterId)
//	if err != nil {
//		panic(errorcode.NewStackError(errorcode.InternalErrorCode, "", err))
//	}
//
//	if c, err := NewLogConfigClientFromBytes(configBytes); err != nil {
//		panic(errorcode.NewStackError(errorcode.InternalErrorCode, "", err))
//	} else {
//		logcClient = c
//	}
//}
//
//func readKubeConfig(t *testing.T) (kubeConfig string) {
//	if env := os.Getenv("KUBECONFIG"); len(env) == 0 {
//		t.Fatalf("envrionment variable KUBECONFIG not set")
//	} else if b, err := ioutil.ReadFile(env); err != nil {
//		t.Fatalf("failed to read %s, %v", env, err)
//	} else {
//		return string(b)
//	}
//	return
//}
