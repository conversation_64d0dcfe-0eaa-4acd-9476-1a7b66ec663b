package service

//func TestStsAssumeRole(t *testing.T) {
//	if tmpSecretId, tmpSecretKey, token, expiredTime, err := service.StsAssumeRoleWithAuth(
//		*fTestSecretId, *fTestSecretKey, *fTestOwnerUin, *fTestSubAccountUin, *fTestRegion, *fTestAuthDuration, ""); err != nil {
//		t.Error(err)
//	} else {
//		t.Log("success")
//		fmt.Printf("\nsecretId: %s\nsecretKey: %s\ntoken: %s\nexpiredTime: %d\n",
//			tmpSecretId, tmpSecretKey, token, expiredTime)
//	}
//}
