package service

import (
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/log"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cls"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
)

type DescribeJobSubmissionLogSetService struct {
}

func (this *DescribeJobSubmissionLogSetService) DescribeJobSubmissionLogSet(req *log.DescribeJobSubmissionLogSetReq) (
	strErr, msg string, rsp *log.DescribeJobSubmissionLogSetRsp) {
	logSetId, _, err := service.GetTableService().GetSubmissionLogSetAndTopic(req.Region)
	if err != nil {
		logger.Errorf("[%s] DescribeJobSubmissionLogSet err : %v", req.RequestId, err)
		return controller.InternalError, err.Error(), nil
	}

	logger.Infof("[%s] logSetId: %s", req.RequestId, logSetId)

	secretId, secretKey, err := service2.GetSecretIdAndKeyOfScs()
	if err != nil {
		logger.Errorf("[%s] getJobSubmissionLogConf err : %v", req.RequestId, err)
		return controller.InternalError, err.Error(), nil
	}

	//logSetDesc, err := cls.GetLogSetWithAuth(conf.SecretId, conf.SecretKey, "", req.Region, logSetId)
	adapter := &cls.CloudApiAdapter{}
	logSetDesc, err := adapter.GetLogSetWithAuth(secretId, secretKey, "", req.Region, logSetId, req.Uin, req.SubAccountUin)
	if err != nil {
		logger.Errorf("[%s] DescribeJobSubmissionLogSet err : %v", req.RequestId, err)
		return controller.InternalError, err.Error(), nil
	}

	rsp = &log.DescribeJobSubmissionLogSetRsp{
		LogSetId:   logSetDesc.LogSetId,
		LogSetName: logSetDesc.LogSetName,
		Period:     logSetDesc.LogSetPeriod,
	}

	return controller.OK, controller.NULL, rsp
}
