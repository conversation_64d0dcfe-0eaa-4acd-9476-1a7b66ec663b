package service

import (
	"bytes"
	"fmt"
	"gopkg.in/yaml.v3"
	"io/ioutil"
	"k8s.io/client-go/kubernetes"
	"k8s.io/helm/pkg/chartutil"
	helm2 "k8s.io/helm/pkg/helm"
	"k8s.io/helm/pkg/helm/portforwarder"
	"k8s.io/helm/pkg/proto/hapi/chart"
	"k8s.io/helm/pkg/proto/hapi/release"
	"k8s.io/helm/pkg/proto/hapi/services"
	"k8s.io/helm/pkg/timeconv"
	"os"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/helm"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	http2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/http"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
	"time"
)

var (
	helService *HelmService
)

type HelmService struct {
}

func (this *HelmService) setupConnection(kubeConfig []byte) (tillerHost string, err error) {

	config, err := k8s.GetK8sService().NewConfig(kubeConfig)
	if err != nil {
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	client, err := kubernetes.NewForConfig(config)
	if err != nil {
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	tillerTunnel, err := portforwarder.New(helm.TILLER_NAMESPACE, client, config)
	if err != nil {
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	tillerHost = fmt.Sprintf("127.0.0.1:%d", tillerTunnel.Local)
	logger.Infof("helm server %s", tillerHost)

	return tillerHost, nil
}

func (this *HelmService) NewClient(kubeConfig []byte) (client helm2.Interface, err error) {
	if tillerHost, err := this.setupConnection(kubeConfig); err != nil {
		return nil, err
	} else {
		options := []helm2.Option{helm2.Host(tillerHost), helm2.ConnectTimeout(helm.TILLER_CONNECTION_TIMEOUT)}
		return helm2.NewClient(options...), nil
	}
}

// filterList returns a list scrubbed of old releases.
func (this *HelmService) filterList(rels []*release.Release) []*release.Release {
	idx := map[string]int32{}

	for _, r := range rels {
		name, version := r.GetName(), r.GetVersion()
		if max, ok := idx[name]; ok {
			// check if we have a greater version already
			if max > version {
				continue
			}
		}
		idx[name] = version
	}

	uniq := make([]*release.Release, 0, len(idx))
	for _, r := range rels {
		if idx[r.GetName()] == r.GetVersion() {
			uniq = append(uniq, r)
		}
	}
	return uniq
}

func (this *HelmService) getListResult(rels []*release.Release, next string) *helm.ListResult {
	listReleases := []*helm.ListRelease{}
	for _, r := range rels {
		md := r.GetChart().GetMetadata()
		t := "-"
		if tspb := r.GetInfo().GetLastDeployed(); tspb != nil {
			t = timeconv.String(tspb)
		}

		lr := &helm.ListRelease{
			Name:       r.GetName(),
			Revision:   r.GetVersion(),
			Updated:    t,
			Status:     r.GetInfo().GetStatus().GetCode().String(),
			Chart:      fmt.Sprintf("%s-%s", md.GetName(), md.GetVersion()),
			AppVersion: md.GetAppVersion(),
			Namespace:  r.GetNamespace(),
		}
		listReleases = append(listReleases, lr)
	}

	return &helm.ListResult{
		Releases: listReleases,
		Next:     next,
	}
}

func (this *HelmService) ListByName(client helm2.Interface, namespace, name string) (result *helm.ListResult, err error) {

	res, err := client.ListReleases(helm2.ReleaseListNamespace(namespace), helm2.ReleaseListFilter(name))
	if err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	empty := &helm.ListResult{
		Next:     "",
		Releases: make([]*helm.ListRelease, 0, 0),
	}
	if res == nil {
		return empty, nil
	}
	rels := res.GetReleases()
	if rels == nil {
		return empty, nil
	}

	rels = this.filterList(rels)
	return this.getListResult(rels, res.Next), nil
}

func (this *HelmService) DeleteByName(client helm2.Interface, name string) (err error) {

	res, err := client.DeleteRelease(name, helm2.DeletePurge(true))
	if err != nil {
		return errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	if rel := res.GetRelease(); rel != nil {
		logger.Infof("delete realase, namespace: %s, name %s", rel.Namespace, rel.Name)
	} else {
		logger.Infof("release is empty, name %s", name)
	}
	return nil
}

func (this *HelmService) setKeyValue(key []string, val interface{}, data map[string]interface{}) error {
	if len(key) == 0 {
		return nil
	}
	k := key[0]
	if len(k) == 0 {
		return errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("key with invalid .. pattern"), nil)
	}
	intf, ok := data[k]

	if len(key) == 1 {
		if ok {
			return errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("duplicated key"), nil)
		} else {
			data[k] = val
			return nil
		}
	}

	internal := map[string]interface{}{}
	if ok {
		if internal, ok = intf.(map[string]interface{}); !ok {
			return errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("invalid key type"), nil)
		}
	} else {
		data[k] = internal
	}
	return this.setKeyValue(key[1:], val, internal)
}

func (this *HelmService) getInstallVals(options *helm.InstallOptions) ([]byte, error) {
	base := map[string]interface{}{}

	if len(options.ValuesMap) > 0 {
		for key, val := range options.ValuesMap {
			if err := this.setKeyValue(strings.Split(key, "."), val, base); err != nil {
				return nil, err
			}
		}
	}

	if vals, err := yaml.Marshal(base); err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	} else {
		return vals, nil
	}
}

func (this *HelmService) IngressNamespace(cg *table.ClusterGroup) string {
	result := constants.DEFAULT_NAMESPACE
	if cg.AgentSerialId != "" {
		result = constants.OCEANUS_NAMESPACE + "-" + cg.SerialId
	}
	return result
}

func (this *HelmService) HadoopNamespace(cg *table.ClusterGroup) string {
	result := constants.DEFAULT_NAMESPACE
	if cg != nil && cg.AgentSerialId != "" {
		result = cg.SerialId
	}
	return result
}

func (this *HelmService) IngressName(cg *table.ClusterGroup) string {
	result := constants.TKE_NGINX_INGRESS_RELEASE_NAME
	if cg.AgentSerialId != "" {
		result = constants.TKE_NGINX_INGRESS_RELEASE_NAME + "-" + cg.SerialId
	}
	return result
}

func (this *HelmService) HadoopYarnName(cg *table.ClusterGroup) string {
	result := constants.TKE_HADOOP_YARN_RELEASE_NAME
	if cg.AgentSerialId != "" {
		result = constants.TKE_HADOOP_YARN_RELEASE_NAME + "-" + cg.SerialId
	}
	return result
}

func (this *HelmService) readHttpFile(name string) ([]byte, error) {
	httpClient := http2.NewHttpClient(helm.HTTP_DIAL_TIMEOUT*time.Second, helm.HTTP_TIMEOUT*time.Second)

	resp, err := httpClient.Get(name)
	if err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	defer resp.Body.Close()

	if data, err := ioutil.ReadAll(resp.Body); err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	} else {
		return data, nil
	}
}

func (this *HelmService) readLocalFile(name string) ([]byte, error) {
	f, err := os.Open(name)
	if err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	defer f.Close()

	if data, err := ioutil.ReadAll(f); err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	} else {
		return data, nil
	}
}

func (this *HelmService) load(name string) (helmChart *chart.Chart, err error) {
	var (
		data []byte
	)
	if strings.HasPrefix(name, "https://") || strings.HasPrefix(name, "http://") {
		data, err = this.readHttpFile(name)
	} else {
		data, err = this.readLocalFile(name)
	}

	if err != nil {
		return nil, err
	}

	if helmChart, err = chartutil.LoadArchive(bytes.NewBuffer(data)); err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	} else {
		return helmChart, nil
	}
}

func (this *HelmService) Install(client helm2.Interface, namespace string, options *helm.InstallOptions) (status *services.GetReleaseStatusResponse, err error) {
	if len(options.ValueBytes) == 0 {
		valBytes, err := this.getInstallVals(options)
		if err != nil {
			return nil, err
		}
		options.ValueBytes = valBytes
	}

	logger.Infof("rawVals: %s", string(options.ValueBytes))

	chartRequested, err := this.load(options.ChartPath)
	if err != nil {
		return nil, err
	}

	res, err := client.InstallReleaseFromChart(
		chartRequested,
		namespace,
		helm2.ValueOverrides(options.ValueBytes),
		helm2.ReleaseName(options.Name),
		helm2.InstallDryRun(false),
		helm2.InstallReuseName(false),
		helm2.InstallDisableHooks(false),
		helm2.InstallDisableCRDHook(false),
		helm2.InstallSubNotes(false),
		helm2.InstallTimeout(helm.INSTALL_TIMEOUT),
		helm2.InstallWait(false),
		helm2.InstallDescription("installed by galileo"))
	if err != nil {
		logger.Errorf("install helm:[%s] error in ns: %s, error: %v", options.Name, namespace, err)
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	rel := res.GetRelease()
	if rel == nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "install release is null", nil)
	}

	if status, err := client.ReleaseStatus(rel.Name); err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	} else {
		return status, nil
	}
}

func (this *HelmService) Status(client helm2.Interface, name string) (status *services.GetReleaseStatusResponse, err error) {
	if status, err = client.ReleaseStatus(name, helm2.StatusReleaseVersion(0)); err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	} else {
		return status, nil
	}
}

func NewHelmService() *HelmService {
	return &HelmService{}
}

func GetHelmService() *HelmService {
	if helService == nil {
		helService = NewHelmService()
	}
	return helService
}
