package service

import (
	"bytes"
	"fmt"
	"io/ioutil"
	"os"
	"strings"
	"time"

	"k8s.io/apimachinery/pkg/api/meta"
	"k8s.io/client-go/discovery"
	"k8s.io/client-go/discovery/cached/memory"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/restmapper"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/client-go/tools/clientcmd/api"

	"gopkg.in/yaml.v3"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/helm"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	http2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/http"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"

	"helm.sh/helm/v3/pkg/action"
	"helm.sh/helm/v3/pkg/chart"
	"helm.sh/helm/v3/pkg/chart/loader"
	"helm.sh/helm/v3/pkg/release"
)

var (
	helService *HelmService
)

// RESTClientGetter is a simple implementation of RESTClientGetter interface
type RESTClientGetter struct {
	config    *rest.Config
	namespace string
}

func NewRESTClientGetter(config *rest.Config, namespace string) *RESTClientGetter {
	return &RESTClientGetter{
		config:    config,
		namespace: namespace,
	}
}

func (r *RESTClientGetter) ToRESTConfig() (*rest.Config, error) {
	return r.config, nil
}

func (r *RESTClientGetter) ToDiscoveryClient() (discovery.CachedDiscoveryInterface, error) {
	discoveryClient, err := discovery.NewDiscoveryClientForConfig(r.config)
	if err != nil {
		return nil, err
	}
	return memory.NewMemCacheClient(discoveryClient), nil
}

func (r *RESTClientGetter) ToRESTMapper() (meta.RESTMapper, error) {
	discoveryClient, err := r.ToDiscoveryClient()
	if err != nil {
		return nil, err
	}
	return restmapper.NewDeferredDiscoveryRESTMapper(discoveryClient), nil
}

func (r *RESTClientGetter) ToRawKubeConfigLoader() clientcmd.ClientConfig {
	return &simpleClientConfig{namespace: r.namespace}
}

// simpleClientConfig is a minimal implementation of clientcmd.ClientConfig
type simpleClientConfig struct {
	namespace string
}

func (c *simpleClientConfig) RawConfig() (api.Config, error) {
	return api.Config{}, nil
}

func (c *simpleClientConfig) ClientConfig() (*rest.Config, error) {
	return nil, nil
}

func (c *simpleClientConfig) Namespace() (string, bool, error) {
	return c.namespace, false, nil
}

func (c *simpleClientConfig) ConfigAccess() clientcmd.ConfigAccess {
	return nil
}

type HelmService struct {
}

func (this *HelmService) NewClient(kubeConfig []byte, namespace string) (*action.Configuration, error) {
	config, err := k8s.GetK8sService().NewConfig(kubeConfig)
	if err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	// Create a Helm action configuration
	actionConfig := new(action.Configuration)

	// Get the client getter
	clientGetter := NewRESTClientGetter(config, namespace)

	// Initialize action configuration with a custom RESTClientGetter
	if err := actionConfig.Init(clientGetter, namespace, os.Getenv("HELM_DRIVER"), logger.Infof); err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	return actionConfig, nil
}

// filterList returns a list scrubbed of old releases.
func (this *HelmService) filterList(rels []*release.Release) []*release.Release {
	idx := map[string]int{}

	for _, r := range rels {
		name, version := r.Name, r.Version
		if max, ok := idx[name]; ok {
			// check if we have a greater version already
			if max > version {
				continue
			}
		}
		idx[name] = version
	}

	uniq := make([]*release.Release, 0, len(idx))
	for _, r := range rels {
		if idx[r.Name] == r.Version {
			uniq = append(uniq, r)
		}
	}
	return uniq
}

func (this *HelmService) getListResult(rels []*release.Release, next string) *helm.ListResult {
	listReleases := []*helm.ListRelease{}
	for _, r := range rels {
		md := r.Chart.Metadata
		t := "-"
		if tspb := r.Info.LastDeployed; !tspb.IsZero() {
			t = tspb.String()
		}

		lr := &helm.ListRelease{
			Name:       r.Name,
			Revision:   int32(r.Version),
			Updated:    t,
			Status:     r.Info.Status.String(),
			Chart:      fmt.Sprintf("%s-%s", md.Name, md.Version),
			AppVersion: md.AppVersion,
			Namespace:  r.Namespace,
		}
		listReleases = append(listReleases, lr)
	}

	return &helm.ListResult{
		Releases: listReleases,
		Next:     next,
	}
}

func (this *HelmService) ListByName(actionConfig *action.Configuration, namespace, name string) (result *helm.ListResult, err error) {
	client := action.NewList(actionConfig)
	client.Filter = name
	client.AllNamespaces = false
	client.StateMask = action.ListAll
	rels, err := client.Run()
	if err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	empty := &helm.ListResult{
		Next:     "",
		Releases: make([]*helm.ListRelease, 0, 0),
	}
	if len(rels) == 0 {
		return empty, nil
	}

	rels = this.filterList(rels)
	return this.getListResult(rels, ""), nil
}

func (this *HelmService) DeleteByName(actionConfig *action.Configuration, name string) (err error) {
	client := action.NewUninstall(actionConfig)

	_, err = client.Run(name)
	if err != nil {
		return errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	logger.Infof("release deleted, name %s", name)
	return nil
}

func (this *HelmService) setKeyValue(key []string, val interface{}, data map[string]interface{}) error {
	if len(key) == 0 {
		return nil
	}
	k := key[0]
	if len(k) == 0 {
		return errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("key with invalid .. pattern"), nil)
	}
	intf, ok := data[k]

	if len(key) == 1 {
		if ok {
			return errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("duplicated key"), nil)
		} else {
			data[k] = val
			return nil
		}
	}

	internal := map[string]interface{}{}
	if ok {
		if internal, ok = intf.(map[string]interface{}); !ok {
			return errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("invalid key type"), nil)
		}
	} else {
		data[k] = internal
	}
	return this.setKeyValue(key[1:], val, internal)
}

func (this *HelmService) getInstallVals(options *helm.InstallOptions) ([]byte, error) {
	base := map[string]interface{}{}

	if len(options.ValuesMap) > 0 {
		for key, val := range options.ValuesMap {
			if err := this.setKeyValue(strings.Split(key, "."), val, base); err != nil {
				return nil, err
			}
		}
	}

	if vals, err := yaml.Marshal(base); err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	} else {
		return vals, nil
	}
}

func (this *HelmService) IngressNamespace(cg *table.ClusterGroup) string {
	result := constants.DEFAULT_NAMESPACE
	if cg.AgentSerialId != "" {
		result = cg.SerialId
	}
	return result
}

func (this *HelmService) HadoopNamespace(cg *table.ClusterGroup) string {
	result := constants.DEFAULT_NAMESPACE
	if cg != nil && cg.AgentSerialId != "" {
		result = cg.SerialId
	}
	return result
}

func (this *HelmService) IngressName(cg *table.ClusterGroup) string {
	result := constants.TKE_NGINX_INGRESS_RELEASE_NAME
	if cg.AgentSerialId != "" {
		result = constants.TKE_NGINX_INGRESS_RELEASE_NAME + "-" + cg.SerialId
	}
	return result
}

func (this *HelmService) HadoopYarnName(cg *table.ClusterGroup) string {
	result := constants.TKE_HADOOP_YARN_RELEASE_NAME
	if cg.AgentSerialId != "" {
		result = constants.TKE_HADOOP_YARN_RELEASE_NAME + "-" + cg.SerialId
	}
	return result
}

func (this *HelmService) readHttpFile(name string) ([]byte, error) {
	httpClient := http2.NewHttpClient(helm.HTTP_DIAL_TIMEOUT*time.Second, helm.HTTP_TIMEOUT*time.Second)

	resp, err := httpClient.Get(name)
	if err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	defer resp.Body.Close()

	if data, err := ioutil.ReadAll(resp.Body); err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	} else {
		return data, nil
	}
}

func (this *HelmService) readLocalFile(name string) ([]byte, error) {
	f, err := os.Open(name)
	if err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	defer f.Close()

	if data, err := ioutil.ReadAll(f); err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	} else {
		return data, nil
	}
}

func (this *HelmService) load(name string) (*chart.Chart, error) {
	var (
		data []byte
		err  error
	)
	if strings.HasPrefix(name, "https://") || strings.HasPrefix(name, "http://") {
		data, err = this.readHttpFile(name)
	} else {
		data, err = this.readLocalFile(name)
	}

	if err != nil {
		logger.Errorf("load helm chart error: %v", err)
		return nil, err
	}

	if helmChart, err := loader.LoadArchive(bytes.NewBuffer(data)); err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	} else {
		return helmChart, nil
	}
}

func (this *HelmService) Install(actionConfig *action.Configuration, namespace string, options *helm.InstallOptions) (status *release.Release, err error) {
	if len(options.ValueBytes) == 0 {
		valBytes, err := this.getInstallVals(options)
		if err != nil {
			return nil, err
		}
		options.ValueBytes = valBytes
	}

	logger.Infof("rawVals: %s", string(options.ValueBytes))

	chartRequested, err := this.load(options.ChartPath)
	if err != nil {
		return nil, err
	}

	// Parse values from the value bytes
	values := map[string]interface{}{}
	if err := yaml.Unmarshal(options.ValueBytes, &values); err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "failed to parse values", err)
	}
	logger.Debugf("ParseValues: %v", values)
	// Create install client
	client := action.NewInstall(actionConfig)
	client.Namespace = namespace
	client.ReleaseName = options.Name
	client.Timeout = helm.INSTALL_TIMEOUT * time.Second
	client.Wait = false

	// Install the chart
	rel, err := client.Run(chartRequested, values)
	if err != nil {
		logger.Errorf("install helm:[%s] error in ns: %s, error: %v", options.Name, namespace, err)
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	if rel == nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "install release is null", nil)
	}

	// Get the status of the release
	statusClient := action.NewStatus(actionConfig)
	rel, err = statusClient.Run(rel.Name)
	if err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	return rel, nil
}

func (this *HelmService) Status(actionConfig *action.Configuration, name string) (*release.Release, error) {
	client := action.NewStatus(actionConfig)

	rel, err := client.Run(name)
	if err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	return rel, nil
}

func NewHelmService() *HelmService {
	return &HelmService{}
}

func GetHelmService() *HelmService {
	if helService == nil {
		helService = NewHelmService()
	}
	return helService
}
