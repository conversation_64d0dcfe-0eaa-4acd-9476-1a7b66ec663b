package service

import (
	"encoding/json"
	"io/ioutil"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/helm"
	"testing"
)

func TestHelmService_ListByName(t *testing.T) {
	helService := GetHelmService()

	if kubeConfig, err := ioutil.ReadFile(*fTestKubeConfig); err != nil {
		t.<PERSON>al(err)
	} else if client, err := helService.NewClient(kubeConfig); err != nil {
		t.Fatal(err)
	} else if result, err := helService.ListByName(client, "", *fTestReleaseName); err != nil {
		t.Error(err)
	} else {
		b, _ := json.MarshalIndent(result, "", " ")
		t.Logf("success, count %d, %s", len(result.Releases), b)
	}
}

func TestHelmService_Install(t *testing.T) {
	helService := GetHelmService()

	if kubeConfig, err := ioutil.ReadFile(*fTestKubeConfig); err != nil {
		t.Fatal(err)
	} else if client, err := helService.NewClient(kubeConfig); err != nil {
		t.Fatal(err)
	} else if status, err := helService.Install(client, helService.IngressNamespace(nil), &helm.InstallOptions{
		ChartPath: *fTestChartPath,
		Name:      *fTestReleaseName,
		ValuesMap: map[string]interface{}{
			"controller.image.repository":     *fTestControllerImageRepo,
			"defaultBackend.image.repository": *fTestDefaultBackendImageRepo,
		},
	}); err != nil {
		t.Error(err)
	} else {
		b, _ := json.MarshalIndent(status, "", " ")
		t.Logf("success, %s", string(b))
	}
}

func TestHelmService_Status(t *testing.T) {
	if kubeConfig, err := ioutil.ReadFile(*fTestKubeConfig); err != nil {
		t.Fatal(err)
	} else if client, err := helService.NewClient(kubeConfig); err != nil {
		t.Fatal(err)
	} else if status, err := helService.Status(client, *fTestReleaseName); err != nil {
		t.Error(err)
	} else {
		b, _ := json.MarshalIndent(status, "", " ")
		t.Logf("success, %s", string(b))
	}
}
