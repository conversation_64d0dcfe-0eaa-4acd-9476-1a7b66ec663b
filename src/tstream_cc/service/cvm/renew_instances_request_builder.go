package cvm

import (
	cvm "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/cvm/v20170312"
)

const DEFAULT_RENEW_PREPAID int64 = 1

type RenewInstancesRequestBuilder struct {
	request *cvm.RenewInstancesRequest
}

func (t *RenewInstancesRequestBuilder) WithDomain(domain string) *RenewInstancesRequestBuilder {
	t.request.SetDomain(domain)
	return t
}

func (t *RenewInstancesRequestBuilder) WithInstances(instances []string) *RenewInstancesRequestBuilder {
	tmp := make([]*string, len(instances))
	for _, i := range instances {
		tmp = append(tmp, &i)
	}
	return t.WithInstancesPtr(tmp)
}

func (t *RenewInstancesRequestBuilder) WithInstancesPtr(instances []*string) *RenewInstancesRequestBuilder {
	t.request.InstanceIds = instances
	return t
}

func (t *RenewInstancesRequestBuilder) WithInstancesInstanceChargePrepaid(period int64, renewFlag string) *RenewInstancesRequestBuilder {
	t.request.InstanceChargePrepaid = &cvm.InstanceChargePrepaid{
		Period:    &period,
		RenewFlag: &renewFlag,
	}
	return t
}

func (t *RenewInstancesRequestBuilder) Build() *cvm.RenewInstancesRequest {
	return t.request
}

func NewRenewInstancesRequestBuilder() *RenewInstancesRequestBuilder {
	return &RenewInstancesRequestBuilder{request: cvm.NewRenewInstancesRequest()}
}
