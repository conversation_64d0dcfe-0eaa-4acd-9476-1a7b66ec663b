package cvm

import (
	"flag"
	"fmt"
	"os"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/tke"
	"testing"
)

type sliceValue []string

func newSliceValue(vals []string, p *[]string) *sliceValue {
	*p = vals
	return (*sliceValue)(p)
}

func (s *sliceValue) Set(val string) error {
	*s = sliceValue(strings.Split(val, ","))
	return nil
}

func (s *sliceValue) Get() interface{} { return []string(*s) }

func (s *sliceValue) String() string { return strings.Join([]string(*s), ",") }

var (
	fTestAppid         = flag.Int64("test.appid", **********, "")
	fTestUin           = flag.String("test.uin", "************", "")
	fTestSubAccountUin = flag.String("test.subaccount.uin", "************", "")
	fTestRegion        = flag.String("test.region", "ap-guangzhou",
		"e.g. ap-guangzhou ap-beijing ap-shanghai ap-chengdu")
	fTestUniqVpcId       = flag.String("test.uniq.vpc.id", "vpc-mjmrypy9", "流计算测试 VPC 1180200|172.28.0.0/16")
	fTestUniqSubnetId    = flag.String("test.uniq.subnet.id", "subnet-0rewz4vk", "测试子网 812842")
	fTestZone            = flag.String("test.zone", "ap-guangzhou-4", "")
	fTestCvmInstanceId   = flag.String("test.cvm.instance.id", "cvm-gfl299la", "cdb instance id")
	fTestClusterSerialId = flag.String("test.cluster.serial.id", "", "cluster-xxx")
	fTestCvmInstanceType = flag.String("test.cvm.instance.type", model.TKE_WORKER_NODE_INSTANCE_TYPE, "")
	fTestRequestRole     = flag.String("test.request.role", "QCLOUD_EMR", "")

	fTestDbUrl = flag.String("test.db.url", "root:root@tcp(127.0.0.1:3306)/online_galileo?charset=utf8", "")
)

var (
	fTestCvmInstanceSet []*string
	txManager           *dao.DataSourceTransactionManager
)

func init() {
	testing.Init()

	var tmp []string
	flag.Var(newSliceValue([]string{}, &tmp), "test.cvm.instance.idset", "cvm instance id list, "+
		"eg: cvm-gfl299la,cvm-gll1881a")

	flag.Parse()

	fTestCvmInstanceSet = make([]*string, 0, len(tmp))
	for _, s := range tmp {
		ss := s
		fTestCvmInstanceSet = append(fTestCvmInstanceSet, &ss)
	}
}

func initTxManager() {
	if txManager != nil {
		return
	}
	tx, err := dao.NewDataSourceTransactionManager(*fTestDbUrl)
	if err != nil {
		fmt.Println("darwin Process create DataSourceTransactionManager err", err)
		os.Exit(-1)
	}
	service.SetTxManager(tx)
	txManager = tx
}
