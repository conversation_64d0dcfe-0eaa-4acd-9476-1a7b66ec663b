package draft

import (
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/configure"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/component"
	txService "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	modelCluster "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/cluster"
	cloudApiDraft "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/draft"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/draft"
	table3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job_config"
	table1 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/resource"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service5 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job_config"
	service7 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/log"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/region"
	service4 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource"
	service6 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
)

func DoCloudApiModifyDraft(req *cloudApiDraft.ModifyDraftReq) (rsp *cloudApiDraft.ModifyDraftResp, err error) {
	mcReq := &draft.ModifyDraftReq{
		AppId:                   int32(req.AppId),
		Region:                  req.Region,
		JobId:                   req.JobId,
		EntrypointClass:         req.EntrypointClass,
		ProgramArgs:             req.ProgramArgs,
		Remark:                  req.Remark,
		Properties:              req.Properties,
		DefaultParallelism:      req.DefaultParallelism,
		ResourceRefs:            req.ResourceRefs,
		UserType:                req.UserType,
		Uin:                     req.Uin,
		SubAccountUin:           req.SubAccountUin,
		COSBucket:               req.COSBucket,
		LogCollect:              req.LogCollect,
		JobManagerSpec:          req.JobManagerSpec,
		TaskManagerSpec:         req.TaskManagerSpec,
		ClsLogsetId:             req.ClsLogsetId,
		ClsTopicId:              req.ClsTopicId,
		EsServerlessIndex:       req.EsServerlessIndex,
		EsServerlessSpace:       req.EsServerlessSpace,
		LogCollectType:          req.LogCollectType,
		PythonVersion:           req.PythonVersion,
		WorkSpaceId:             req.WorkSpaceId,
		IsSupOwner:              req.IsSupOwner,
		Action:                  req.Action,
		AutoRecover:             req.AutoRecover,
		LogLevel:                req.LogLevel,
		ClazzLevels:             req.ClazzLevels,
		ExpertModeOn:            req.ExpertModeOn,
		ExpertModeConfiguration: req.ExpertModeConfiguration,
		TraceModeConfiguration:  req.TraceModeConfiguration,
		TraceModeOn:             req.TraceModeOn,
		CheckpointRetainedNum:   req.CheckpointRetainedNum,
		JobGraph:                req.JobGraph,
		FlinkVersion:            req.FlinkVersion,
		Version:                 req.JobVersion,
		JobManagerCpu:           req.JobManagerCpu,
		JobManagerMem:           req.JobManagerMem,
		TaskManagerCpu:          req.TaskManagerCpu,
		TaskManagerMem:          req.TaskManagerMem,
	}
	_, err = DoModifyDraftConfig(mcReq)
	return &cloudApiDraft.ModifyDraftResp{}, err
}

func DoModifyDraftConfig(req *draft.ModifyDraftReq) (resp *draft.ModifyDraftResp, err error) {
	defer errorcode.DefaultDeferHandler(&err)

	// 0. 检查作业是否存在
	job, err := service.WhetherJobExists(req.AppId, req.Region, req.JobId)
	if err != nil {
		return nil, err
	}

	// 1. 新版 EtlJob 画布作业入参ProgramArgs的key为 EtlJobCanvas，并且为 base64 格式，这里将其转为Json
	req.ProgramArgs, err = service2.ParseProgramArgsBase64ToJson(req.ProgramArgs, job.Type)
	if err != nil {
		return nil, err
	}

	// 鉴权
	err = auth.InnerAuthById(req.WorkSpaceId, req.IsSupOwner, job.ItemSpaceId, int64(req.AppId), req.SubAccountUin, req.Action, false)
	if err != nil {
		logger.Errorf(" User  uin %s is NOT authenticated, refuse to serve", req.Uin)
		return nil, err
	}

	// 2. 检查 引用资源信息
	ok, mainResourceCount := service2.CheckResourceRefs(job, req.ResourceRefs)
	if !ok {
		msg := fmt.Sprintf("main ResourceRefs count %d invalid", mainResourceCount)
		return nil, errorcode.InvalidParameterValueCode.ReplaceDesc(msg)
	}

	regionId, err := service3.GetRegionIdByName(req.Region)
	if err != nil {
		return nil, err
	}
	account := component.NewComponentAccount(regionId)
	scsDevEnv := configure.GetConfBoolValue("galileo_cc.properties", "scsDevEnv", false)
	var ownerUin string
	if scsDevEnv {
		devUserAppId := configure.GetConfInt64Value("galileo_cc.properties", "scsDevEnvAppId", 0)
		ownerUin, err = account.GetOwnerUinByAppId(devUserAppId)
	} else {
		ownerUin, err = account.GetOwnerUinByAppId(int64(req.AppId))
	}
	if err != nil {
		return nil, err
	}
	programArgs, checkpointInterval, sqlCode, err := service2.ExtractElementsFromProgramArgs(req.ProgramArgs, job.Type)
	if err != nil {
		logger.Errorf("Failed to get elements from programArgs, with program args:%+v, with errors:%+v", req.ProgramArgs, err)
		return nil, err
	}
	if job.Type == constants.JOB_TYPE_SQL || job.Type == constants.JOB_TYPE_ETL {
		if !auth.IsInWhiteList(int64(req.AppId), constants.WHITE_LIST_SUPPORT_LOW_INTERVAL_CHECK_POINT) {
			err = service.CheckCheckpointIntervalValid(checkpointInterval)
			if err != nil {
				logger.Errorf("Failed to create JobConfig, %s", err.Error())
				return nil, err
			}
		} else {
			if checkpointInterval < constants.WHITE_LIST_CHECKPOINT_INTERVAL_LOWER_LIMIT && checkpointInterval > 0 {
				err = errors.New(fmt.Sprintf("checkpointInterval must be greater than 1, but got %d", checkpointInterval))
				return nil, err
			}
		}
	}

	publishedJobConfigs, err := service2.ListJobConfigsByIds([]int64{job.PublishedJobConfigId})
	if err != nil {
		return nil, err
	}

	// 不传默认0或传-1则代表草稿态编辑;非运行态的作业参数直接修改;运行态的参数创建一个暂存作业参数版本,暂存版本Version=-Version*10
	var draftVersion int64
	if req.Version == 0 {
		draftVersion = constants.JOB_CONFIG_DRAFT_VERSION
	} else if job.Status == constants.JOB_STATUS_RUNNING && len(publishedJobConfigs) == 1 && req.Version == publishedJobConfigs[0].VersionId {
		draftVersion = int64(-req.Version * 10)
	} else {
		draftVersion = int64(req.Version)
	}
	jobConfigs, err := service2.ListJobConfigs(job.Id, []int64{}, []int64{draftVersion}, nil)
	if err != nil {
		return nil, err
	}

	cluster, err := service5.GetActiveClusterByClusterGroupId(job.ClusterGroupId)
	if err != nil {
		return
	}

	tkeClusterType := -1
	/**
	emr集群不处理
	*/
	if cluster.SchedulerType != constants.CLUSTER_SCHEDULER_TYPE_EMR {
		// ESK 集群屏蔽 flink.kubernetes.diagnosis-collection-enabled 高级参数
		tkeClusterType, err = service6.GetTableService().GetTkeClusterType(cluster.Id)
		if err != nil {
			logger.Errorf("GetTkeClusterType failed for cluster id %s", cluster.Id)
			return nil, err
		}
		if tkeClusterType == constants.K8S_CLUSTER_TYPE_EKS {
			for _, property := range req.Properties {
				if property.Key == constants.DIAGNOSIS_DATA_COLLECT_ENABLED_KEY {
					return nil, errorcode.InvalidParameter_JobConfigPropertiesParamError.
						ReplaceDesc(fmt.Sprintf("flink.kubernetes.diagnosis-collection-enabled not valid in EKS cluster."))
				}
			}
		}
	}

	err = service2.CheckJobConfigProperties(tkeClusterType, int64(req.AppId), req.Properties, req.DefaultParallelism)
	if err != nil {
		return nil, err
	}

	if req.JobManagerCpu == 0 {
		if ok, err := service2.CheckPodSpecs(req.AppId, cluster, req.JobManagerSpec, req.TaskManagerSpec); err != nil {
			return nil, err
		} else if !ok {
			err = errorcode.InvalidParameterValueCode.ReplaceDesc(fmt.Sprintf(
				"PodSpec(%f,%f) not supported", req.JobManagerSpec, req.TaskManagerSpec))
			return nil, err
		}
	} else {
		if ok, err := service2.CheckCpuMem(req.AppId, cluster, req.JobManagerCpu, req.JobManagerMem, req.TaskManagerCpu, req.TaskManagerMem); err != nil {
			return nil, err
		} else if !ok {
			err = errorcode.InvalidParameterValueCode.ReplaceDesc(fmt.Sprintf(
				"Pod Cpu Mem(%f, %f, %f, %f) not valid", req.JobManagerCpu, req.JobManagerMem, req.TaskManagerCpu, req.TaskManagerMem))
			return nil, err
		}
	}
	if req.LogCollectType == constants.JobLogCollectTypeCLS &&
		len(cluster.LogConfig) > 0 &&
		(!strings.Contains(cluster.LogConfig, req.ClsLogsetId) || !strings.Contains(cluster.LogConfig, req.ClsTopicId)) {
		logger.Errorf("req.ClsLogsetId or req.ClsTopicId not belong to cluster.LogConfig")
		return nil, errorcode.InvalidParameterValueCode.ReplaceDesc(fmt.Sprintf("req.ClsLogsetId or req.ClsTopicId not belong to cluster"))
	}

	// 检查 es index status ready
	err = service2.CheckEsIndexReady(req.LogCollectType, req.EsServerlessIndex, req.EsServerlessSpace, cluster)
	if err != nil {
		return nil, err
	}

	podSpec := func(jobManagerSpec, taskManagerSpec, jobManagerCpu, jobManagerMem, taskManagerCpu, taskManagerMem float32) (float32, float32, float32, float32, float32, float32, error) {
		if jobManagerSpec == 0 && taskManagerSpec == 0 && jobManagerCpu == 0 &&
			jobManagerMem == 0 && taskManagerCpu == 0 && taskManagerMem == 0 {
			cluster, err := service5.GetClusterByJobId(req.JobId)
			if err != nil {
				logger.Errorf("fail to GetClusterByJobId with error [%v]", err)
				return 0, 0, 0, 0, 0, 0, err
			}
			return 0, 0, 1, float32(cluster.MemRatio), 1, float32(cluster.MemRatio), nil
		} else {
			return jobManagerSpec, taskManagerSpec, jobManagerCpu, jobManagerMem, taskManagerCpu, taskManagerMem, nil
		}
	}

	if req.AutoRecover == 0 {
		// 如果接口没有带AutoRecover参数，使用默认值
		req.AutoRecover = constants.AutoRecoverEnable
	}

	// 只有sql作业才有configuratoin
	if req.ExpertModeOn && job.Type == constants.JOB_TYPE_SQL && req.ExpertModeConfiguration != nil {
		if err := service2.ValidateExpertModeConfiguration(req.ExpertModeConfiguration); err != nil {
			return nil, err
		}
	}

	strClazzLevels := constants.CLASS_LOG_LEVEL_DEFALUT_VALUE

	if len(req.ClazzLevels) > 0 {
		bytesClazzLevels, err := json.Marshal(req.ClazzLevels)
		if err != nil {
			errMsg := fmt.Sprintf("Failed to json marshal ClazzLevels struct: %+v, errors:%+v", req.ClazzLevels, err)
			logger.Errorf(errMsg)
			return nil, errorcode.InvalidParameterValueCode.ReplaceDesc(errMsg)
		}
		strClazzLevels = string(bytesClazzLevels)
	}

	if len(jobConfigs) == 0 { // 如果没有草稿, 则该版本的作业需要新建一个作业草稿
		maxParallelism, props, err := service2.GetMaxParallelism(req.DefaultParallelism, req.Properties, true)
		if err != nil {
			return nil, err
		}

		jobManagerSpec, taskManagerSpec, jobManagerCpu, jobManagerMem, taskManagerCpu, taskManagerMem, err :=
			podSpec(req.JobManagerSpec, req.TaskManagerSpec, req.JobManagerCpu, req.JobManagerMem, req.TaskManagerCpu, req.TaskManagerMem)
		if err != nil {
			return nil, err
		}

		jobConfig := table.JobConfig{
			CreatorUin:            ownerUin,
			JobId:                 job.Id,
			VersionId:             int16(draftVersion),
			EntrypointClass:       req.EntrypointClass,
			ProgramArgs:           programArgs,
			CheckpointInterval:    checkpointInterval,
			SqlCode:               sqlCode,
			Status:                constants.JOB_CONFIG_STATUS_UN_PUBLISHED,
			Remark:                "",
			CreateTime:            util.GetCurrentTime(),
			MaxParallelism:        maxParallelism,
			DefaultParallelism:    req.DefaultParallelism,
			Properties:            props,
			COSBucket:             req.COSBucket,
			JmCuSpec:              jobManagerSpec,
			TmCuSpec:              taskManagerSpec,
			ClsLogsetId:           req.ClsLogsetId,
			ClsTopicId:            req.ClsTopicId,
			PythonVersion:         req.PythonVersion,
			AutoRecover:           req.AutoRecover,
			LogLevel:              req.LogLevel,
			ClazzLevels:           strClazzLevels,
			CheckpointRetainedNum: req.CheckpointRetainedNum,
			EsServerlessIndex:     req.EsServerlessIndex,
			EsServerlessSpace:     req.EsServerlessSpace,
			FlinkVersion:          req.FlinkVersion,
			JobManagerCpu:         jobManagerCpu,
			JobManagerMem:         jobManagerMem,
			TaskManagerCpu:        taskManagerCpu,
			TaskManagerMem:        taskManagerMem,
			LibConfig:             req.LibConfig,
		}

		if req.TraceModeOn && req.TraceModeConfiguration != nil {
			b, err := json.Marshal(req.TraceModeConfiguration)
			if err != nil {
				return nil, errorcode.InvalidParameterValueCode.NewWithErr(err)
			}
			jobConfig.TraceModeConfiguration = string(b)
		} else {
			jobConfig.TraceModeConfiguration = "{}"
		}

		if req.JobGraph != nil {
			b, err := json.Marshal(req.JobGraph)
			if err != nil {
				return nil, errorcode.InvalidParameterValueCode.NewWithErr(err)
			}
			jobConfig.JobGraph = string(b)
		}

		if req.LogCollect && (req.LogCollectType == constants.JobLogCollectTypeCLS || req.LogCollectType == 0) { // LogCollectType=0 兼容历史接口
			jobConfig.LogCollect = constants.JobLogCollectEnabled
		} else if req.LogCollect && req.LogCollectType == constants.JobLogCollectTypeCOS {
			jobConfig.LogCollect = constants.JobLogCollectEnabledOnCos
			properties, err := service.AddDiagnosisVolumeMounted2Properties(props)
			if err != nil {
				logger.Errorf("AddDiagnosisVolumeMounted2Properties with error [%v]", err)
				return nil, err
			}
			jobConfig.Properties = properties
		} else if req.LogCollect && req.LogCollectType == constants.JobLogCollectTypeES {
			jobConfig.LogCollect = constants.JobLogCollectEnabledOnES
		} else {
			jobConfig.LogCollect = constants.JobLogCollectDisabled
		}

		if req.ExpertModeOn {
			b, err := json.Marshal(req.ExpertModeConfiguration)
			if err != nil {
				return nil, errorcode.InvalidParameterValueCode.NewWithErr(err)
			}
			// todo 现在jar作业的配置按jar代码算，之后可能要改成从stream得到jobgraph之后传算子配置
			s := string(b)
			if s == "" {
				s = "{\"msg\":\"高级jar作业\"}"
			}
			jobConfig.ExpertModeConfiguration = s
		}

		logger.Infof("jobConfig:[%v]", jobConfig)
		txManager := txService.GetTxManager()
		txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
			jobConfigId := tx.SaveObject(jobConfig, "JobConfig")
			if req.ResourceRefs != nil && len(req.ResourceRefs) != 0 {
				err = DoCreateResourceRefOnDraftConfig(job, jobConfigId, req.ResourceRefs, tx)
				if err != nil {
					return err
				}
			}
			err = service2.UpdateJobConfigSqlCode(job.Id, jobConfigId, sqlCode)
			if err != nil {
				return err
			}
			return nil
		}).Close()
	} else { // 如果存在草稿
		maxParallelism, props, err := service2.GetMaxParallelism(req.DefaultParallelism, req.Properties, false)
		if err != nil {
			return nil, err
		}

		jobConfig := jobConfigs[0]
		if req.DefaultParallelism != 0 {
			jobConfig.DefaultParallelism = req.DefaultParallelism
		}
		jobConfig.Properties = props
		jobConfig.ProgramArgs = programArgs

		if len(req.EntrypointClass) != 0 {
			jobConfig.EntrypointClass = req.EntrypointClass
		}
		if len(sqlCode) != 0 {
			jobConfig.SqlCode = sqlCode
		}

		jobManagerSpec, taskManagerSpec, jobManagerCpu, jobManagerMem, taskManagerCpu, taskManagerMem, err :=
			podSpec(req.JobManagerSpec, req.TaskManagerSpec, req.JobManagerCpu, req.JobManagerMem, req.TaskManagerCpu, req.TaskManagerMem)
		if err != nil {
			return nil, err
		}

		jobConfig.CheckpointInterval = checkpointInterval
		jobConfig.MaxParallelism = maxParallelism
		jobConfig.COSBucket = req.COSBucket
		jobConfig.JmCuSpec = jobManagerSpec
		jobConfig.TmCuSpec = taskManagerSpec
		jobConfig.ClsLogsetId = req.ClsLogsetId
		jobConfig.ClsTopicId = req.ClsTopicId
		jobConfig.PythonVersion = req.PythonVersion
		jobConfig.AutoRecover = req.AutoRecover
		jobConfig.LogLevel = req.LogLevel
		jobConfig.ClazzLevels = strClazzLevels
		jobConfig.CheckpointRetainedNum = req.CheckpointRetainedNum
		jobConfig.EsServerlessIndex = req.EsServerlessIndex
		jobConfig.EsServerlessSpace = req.EsServerlessSpace
		jobConfig.FlinkVersion = req.FlinkVersion
		jobConfig.Status = constants.JOB_CONFIG_STATUS_UN_PUBLISHED
		jobConfig.JobManagerCpu = jobManagerCpu
		jobConfig.JobManagerMem = jobManagerMem
		jobConfig.TaskManagerCpu = taskManagerCpu
		jobConfig.TaskManagerMem = taskManagerMem
		jobConfig.LibConfig = req.LibConfig

		if req.TraceModeOn && req.TraceModeConfiguration != nil {
			b, err := json.Marshal(req.TraceModeConfiguration)
			if err != nil {
				return nil, errorcode.InvalidParameterValueCode.NewWithErr(err)
			}
			jobConfig.TraceModeConfiguration = string(b)
		} else {
			jobConfig.TraceModeConfiguration = "{}"
		}
		if req.JobGraph != nil {
			b, err := json.Marshal(req.JobGraph)
			if err != nil {
				return nil, errorcode.InvalidParameterValueCode.NewWithErr(err)
			}
			jobConfig.JobGraph = string(b)
		}

		if req.LogCollect && (req.LogCollectType == constants.JobLogCollectTypeCLS || req.LogCollectType == 0) { // LogCollectType=0 兼容历史接口
			jobConfig.LogCollect = constants.JobLogCollectEnabled
		} else if req.LogCollect && req.LogCollectType == constants.JobLogCollectTypeCOS {
			jobConfig.LogCollect = constants.JobLogCollectEnabledOnCos
			properties, err := service.AddDiagnosisVolumeMounted2Properties(props)
			if err != nil {
				logger.Errorf("AddDiagnosisVolumeMounted2Properties with error [%v]", err)
				return nil, err
			}
			jobConfig.Properties = properties
		} else if req.LogCollect && req.LogCollectType == constants.JobLogCollectTypeES {
			jobConfig.LogCollect = constants.JobLogCollectEnabledOnES
		} else {
			jobConfig.LogCollect = constants.JobLogCollectDisabled
		}

		if req.ExpertModeOn {
			b, err := json.Marshal(req.ExpertModeConfiguration)
			if err != nil {
				return nil, errorcode.InvalidParameterValueCode.NewWithErr(err)
			}
			// todo 现在jar作业的配置按jar代码算，之后可能要改成从stream得到jobgraph之后传算子配置
			s := string(b)
			if s == "" {
				s = "{\"msg\":\"高级jar作业\"}"
			}
			jobConfig.ExpertModeConfiguration = s
		} else {
			jobConfig.ExpertModeConfiguration = "{}"
		}

		txManager := txService.GetTxManager()
		txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
			tx.UpdateObject(jobConfig, jobConfig.Id, "JobConfig")
			err = DoCreateResourceRefOnDraftConfig(job, jobConfig.Id, req.ResourceRefs, tx)
			if err != nil {
				return err
			}
			// 3. 更新sqlCode
			err = service2.UpdateJobConfigSqlCode(job.Id, jobConfig.Id, sqlCode)
			if err != nil {
				return err
			}
			return nil
		}).Close()

	}
	return &draft.ModifyDraftResp{}, nil
}

func DoCreateDraftConfigByDefault(jobId int64, creatorUin string, jobType int8, logCollect int, cluster *table3.Cluster) (err error) {

	var sqlCode string
	var checkpointInterval int64
	if jobType == constants.JOB_TYPE_SQL || jobType == constants.JOB_TYPE_ETL {
		sqlCode, err = util.AesEncrypt([]byte(""), constants.AES_ENCRYPT_KEY)
		if err != nil {
			logger.Errorf("Failed to encrypt the sqlCode, with errors:%+v", err)
			return err
		}
		checkpointInterval = 600 // SQL 作业默认开启快照选项，周期设置为 10 min，兼顾性能和容错性
	} else {
		sqlCode = ""
		checkpointInterval = 0
	}
	supportedFeatures, err2 := cluster.GetSupportedFeatures()
	if err2 != nil {
		logger.Errorf("%s get support feature failed with error %v", cluster.Id, err2)
		return err
	}
	jobConfig := &table.JobConfig{
		JobId:              jobId,
		CreatorUin:         creatorUin,
		VersionId:          constants.JOB_CONFIG_DRAFT_VERSION,
		EntrypointClass:    "",
		ProgramArgs:        "",
		CheckpointInterval: checkpointInterval,
		SqlCode:            sqlCode,
		Status:             constants.JOB_CONFIG_STATUS_UN_PUBLISHED,
		Remark:             "",
		CreateTime:         util.GetCurrentTime(),
		MaxParallelism:     service2.GetDefaultMaxParallelism(),
		Properties:         service2.GetDefaultMaxParallelismKvPair(),
		DefaultParallelism: 1,
		LogLevel:           getDefaultLogLevelByFeature(supportedFeatures),
		LogCollect:         logCollect,
		// 创建草稿使用默认值，开启作业失败自动恢复
		AutoRecover: constants.AutoRecoverEnable,
		// 创建草稿使用默认值, 默认开启快照保留策略
		CheckpointRetainedNum: constants.DefaultCheckpoingRetainNum,
	}
	defaultLogCollectConf := &modelCluster.DefaultLogCollectConf{}
	if cluster.DefaultLogCollectConf != "" { // 集群的日志默认采集配置不为空，此时创建作业草稿的日志集和日志主题应该为默认配置
		err = json.Unmarshal([]byte(cluster.DefaultLogCollectConf), defaultLogCollectConf)
		if err != nil {
			logger.Errorf("cluster.DefaultLogCollectConf cannot convert to model.DefaultLogCollectConf")
			return err
		}
		if defaultLogCollectConf.LogCollectType == constants.CLUSTER_DEFAULT_LOG_COLLECT_CLS {
			jobConfig.ClsLogsetId = defaultLogCollectConf.Conf.ClsLogsetId
			jobConfig.ClsTopicId = defaultLogCollectConf.Conf.ClsTopicId
			jobConfig.LogCollect = constants.JobLogCollectEnabled
		}

		if defaultLogCollectConf.LogCollectType == constants.CLUSTER_DEFAULT_LOG_COLLECT_ES_SERVERLESS {
			jobConfig.EsServerlessIndex = defaultLogCollectConf.Conf.EsServerlessIndex
			jobConfig.EsServerlessSpace = defaultLogCollectConf.Conf.EsServerlessSpace
			jobConfig.LogCollect = constants.JobLogCollectEnabledOnES
		}

		if defaultLogCollectConf.LogCollectType == constants.CLUSTER_DEFAULT_LOG_COLLECT_COS {
			jobConfig.COSBucket = defaultLogCollectConf.Conf.CosBucket
			jobConfig.LogCollect = constants.JobLogCollectEnabledOnCos
			properties, err := service.AddDiagnosisVolumeMounted2Properties(jobConfig.Properties)
			if err != nil {
				logger.Errorf("AddDiagnosisVolumeMounted2Properties with error [%v]", err)
				return err
			}
			jobConfig.Properties = properties
		}
	}

	txManager := txService.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.SaveObject(jobConfig, "JobConfig")
		return nil
	}).Close()
	return nil
}

func DoCreateResourceRefOnDraftConfig(job *table2.Job, jobConfigId int64, resourceRefs []*model.ResourceRefItem,
	tx *dao.Transaction) (err error) {
	logger.Debugf("create resource refs:%+v", resourceRefs)
	_, resources, err := service4.GetResourcesAndCheckReferences(resourceRefs)
	if err != nil {
		logger.Errorf("Failed to get GetResourcesAndCheckReferences errors:%+v", err)
		return err
	}

	// SQL作业中的connector不允许修改和删除，已经由系统匹配好了，前端不会透传内置connector，需要保留
	resourcesIds := make([]int64, 0)
	if job.Type == constants.JOB_TYPE_SQL {
		jobConfigResourceRef, err := service7.GetJobConfigResourceRefByJobConfigIds([]int64{jobConfigId})
		if err != nil {
			return err
		}
		for _, item := range jobConfigResourceRef[jobConfigId] {
			if item.SystemProvide == constants.SYSTEM_PROVIDED || item.SystemProvide == constants.USER_CONNECTOR {
				resourcesIds = append(resourcesIds, item.ResourceIntId)
				continue
			}
		}
	}

	// 先删掉旧的引用关系
	if len(resourcesIds) > 0 {
		args := make([]interface{}, 0, 0)
		args = append(args, constants.RESOURCE_REF_STATUS_DELETE)
		args = append(args, jobConfigId)
		updateSql := "UPDATE ResourceRef Set Status=? WHERE JobConfigId=? AND ResourceId not in ("
		for i, id := range resourcesIds {
			if i != len(resourcesIds)-1 {
				updateSql += "?, "
				args = append(args, id)
				continue
			}
			updateSql += "?)"
			args = append(args, id)
			break
		}
		tx.ExecuteSql(updateSql, args)
	} else {
		args := make([]interface{}, 0, 0)
		args = append(args, constants.RESOURCE_REF_STATUS_DELETE)
		args = append(args, jobConfigId)
		tx.ExecuteSql("UPDATE ResourceRef Set Status=? WHERE JobConfigId=?", args)
	}

	for _, resourceRef := range resourceRefs {
		resource, ok := resources[resourceRef.ResourceId]
		if !ok {
			logger.Errorf("Failed to get resources, with resourceId:%s query result is empty", resourceRef.ResourceId)
			return errors.New(controller.ResourceNotFound)
		}
		tableRef := table1.ResourceRef{
			ResourceId:  resource.Id,
			JobConfigId: jobConfigId,
			VersionId:   resourceRef.Version,
			Status:      constants.RESOURCE_REF_STATUS_ACTIVE,
			UsageType:   service4.GetResourceRefType(resourceRef.Type),
			CreateTime:  time.Now().Format("2006-01-02 15:04:05"),
		}
		tx.SaveObject(tableRef, "ResourceRef")
	}
	return nil
}

// getDefaultLogLevelByFeature 通过集群版本，获取默认日志级别
func getDefaultLogLevelByFeature(clusterFeature []string) string {
	if len(clusterFeature) == 0 {
		return ""
	}
	for _, v := range clusterFeature {
		// 如果集群支持日志级别特性，默认日志级别为 INFO
		if constants.CLUSTER_LOG_LEVEL_FEATURE == v {
			return constants.LOG_LEVEL_INFO
		}
	}
	return ""
}

func ReplaceJobConfig(job *table2.Job) error {
	jobConfigs, err := service2.ListJobConfigsByIds([]int64{job.PublishedJobConfigId})
	if err != nil {
		return err
	}
	if len(jobConfigs) == 0 {
		return nil
	}
	jobConfig := jobConfigs[0]
	draftVersion := int64(-jobConfig.VersionId) * 10
	filter := make(map[string][]string)
	filter["Status"] = []string{strconv.FormatInt(constants.JOB_CONFIG_STATUS_UN_PUBLISHED, 10)}
	draftJobConfigs, err := service2.ListJobConfigs(job.Id, []int64{}, []int64{draftVersion}, filter)
	if err != nil {
		return err
	}
	// 判定是否有未发布的作业参数草稿
	if len(draftJobConfigs) > 0 {
		logger.Infof("ReplaceJobConfig jobId:%d,draftVersion:%d", job.Id, draftVersion)
		draftJobConfig := draftJobConfigs[0]
		jobConfig.ProgramArgs = draftJobConfig.ProgramArgs
		jobConfig.CheckpointInterval = draftJobConfig.CheckpointInterval
		jobConfig.COSBucket = draftJobConfig.COSBucket
		jobConfig.MaxParallelism = draftJobConfig.MaxParallelism
		jobConfig.DefaultParallelism = draftJobConfig.DefaultParallelism
		jobConfig.Properties = draftJobConfig.Properties
		jobConfig.LogCollect = draftJobConfig.LogCollect
		jobConfig.JmCuSpec = draftJobConfig.JmCuSpec
		jobConfig.TmCuSpec = draftJobConfig.TmCuSpec
		jobConfig.ClsLogsetId = draftJobConfig.ClsLogsetId
		jobConfig.ClsTopicId = draftJobConfig.ClsTopicId
		jobConfig.AutoRecover = draftJobConfig.AutoRecover
		jobConfig.LogLevel = draftJobConfig.LogLevel
		jobConfig.UseOldSysConnector = draftJobConfig.UseOldSysConnector
		jobConfig.ExpertModeConfiguration = draftJobConfig.ExpertModeConfiguration
		jobConfig.EsServerlessIndex = draftJobConfig.EsServerlessIndex
		jobConfig.EsServerlessSpace = draftJobConfig.EsServerlessSpace
		jobConfig.TraceModeConfiguration = draftJobConfig.TraceModeConfiguration
		jobConfig.CheckpointRetainedNum = draftJobConfig.CheckpointRetainedNum
		jobConfig.JobGraph = draftJobConfig.JobGraph
		jobConfig.FlinkVersion = draftJobConfig.FlinkVersion
		jobConfig.JobManagerCpu = draftJobConfig.JobManagerCpu
		jobConfig.JobManagerMem = draftJobConfig.JobManagerMem
		jobConfig.TaskManagerCpu = draftJobConfig.TaskManagerCpu
		jobConfig.TaskManagerMem = draftJobConfig.TaskManagerMem
		jobConfig.LibConfig = draftJobConfig.LibConfig

		draftJobConfig.Status = constants.JOB_CONFIG_STATUS_PUBLISHED_SUCC

		// 比较资源是否有修改
		jobConfigResourceRef, err := service7.GetJobConfigResourceRefByJobConfigIds([]int64{jobConfig.Id})
		if err != nil {
			return err
		}

		draftJobConfigResourceRef, err := service7.GetJobConfigResourceRefByJobConfigIds([]int64{draftJobConfig.Id})
		if err != nil {
			return err
		}
		resourceRefs := make([]*model.ResourceRefItem, 0)
		diff := false
		if service7.AreResourceDifferent(jobConfigResourceRef[jobConfig.Id], draftJobConfigResourceRef[draftJobConfig.Id]) {
			diff = true
			logger.Infof("ReplaceJobConfig resourceRefs jobConfigId:%d", draftJobConfig.Id)
			for _, v := range draftJobConfigResourceRef[draftJobConfig.Id] {
				resourceRefItem := &model.ResourceRefItem{}
				resourceRefItem.ResourceId = v.ResourceId
				resourceRefItem.Version = v.Version
				resourceRefItem.Type = v.Type
				resourceRefs = append(resourceRefs, resourceRefItem)
			}
		}

		txManager := txService.GetTxManager()
		txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
			tx.UpdateObject(jobConfig, jobConfig.Id, "JobConfig")
			tx.UpdateObject(draftJobConfig, draftJobConfig.Id, "JobConfig")

			if diff {
				err := DoCreateResourceRefOnDraftConfig(job, jobConfig.Id, resourceRefs, tx)
				if err != nil {
					return err
				}
			}
			return nil
		}).Close()

	}
	return nil
}
