package job_autoscale

import (
	"encoding/json"
	"strconv"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/auto_tuning"
)

type AutoScaleConfig struct {
	DelaySeconds int
	BackoffTime  int
	//WhiteList      []string
	RestartTimeout int
}

type AutoScaleContext struct {
	Processing      map[string]*ScalingTask
	Config          *AutoScaleConfig
	RequestId       string
	Region          string
	TesterStartTime string
	TesterEndTime   string
}

func getCheckResource() (backoffTime int, delaySeconds int, timeout int, err error) {
	//whiteList, err = GetWhiteList()
	//if err != nil {
	//	logger.Errorf("%+v", err)
	//	return 0, nil, 0, 0, err
	//}
	delaySeconds, err = getDelayTime()
	if err != nil {
		logger.Errorf("%+v", err)
		delaySeconds = constants.SCALE_DEALY_SECONDS
	}
	backoffTime, err = getBackoffTime()
	if err != nil {
		logger.Errorf("%+v", err)
		backoffTime = constants.SCALE_BACKOFF_SECONDS
	}
	timeout, err = getRestartTimeout()
	if err != nil {
		logger.Errorf("%+v", err)
		timeout = constants.SCALE_RESTART_TIMEOUT_SECONDS
	}
	err = nil
	return
}

func (this *AutoScaleContext) GetTasks() map[string]*ScalingTask {
	return this.Processing
}

func (this *AutoScaleContext) GetTask() *ScalingTask {
	for _, v := range this.Processing {
		return v
	}
	return nil
}

func NewAutoScaleContext(action *auto_tuning.JobTuningAction) (*AutoScaleContext, error) {
	backoffTime, delaySeconds, restartTimeout, err := getCheckResource()
	if action.ActionType != constants.SCALE_ACTION_TYPE_TASK_FLEXIBLE_TYPE {
		autoScaleRuleConfig := &AutoScaleRuleConfig{}
		err = json.Unmarshal([]byte(action.Configuration), &autoScaleRuleConfig)
		if err != nil {
			logger.Errorf("Failed to unmarshal configuration:%s autoScaleRuleConfig, with errors:%+v", action.Configuration, err)
		}
		if autoScaleRuleConfig.CooldownTimeMinutes != "" {
			backoffTime, err = strconv.Atoi(autoScaleRuleConfig.CooldownTimeMinutes)
			if err != nil {
				logger.Errorf("Failed to convert cooldownTimeMinutes to int, with errors:%+v", err)
				return nil, err
			}
			backoffTime = backoffTime * 60
		}
	}
	return &AutoScaleContext{
		Processing: make(map[string]*ScalingTask, 0),
		Config: &AutoScaleConfig{
			DelaySeconds: delaySeconds,
			BackoffTime:  backoffTime,
			//WhiteList:      whiteList,
			RestartTimeout: restartTimeout,
		},
	}, nil
}

func (this *AutoScaleContext) Add(task *ScalingTask) {
	if task.Status == constants.SCALE_JOB_STATUS_READY {
		this.Processing[task.Action.JobSerialId] = task
	}
}

func (this *AutoScaleContext) IsStatusConsistentWith(status string) bool {
	for _, v := range this.Processing {
		if v.Status != status {
			return false
		}
	}
	return true
}
