package job_autoscale

import (
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"sort"
	"strconv"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service2 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_autoscale"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job_config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service4 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/configuration_center"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job_config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/quota"
	"time"
)

func DoCreateJobScalePlan(req *job_autoscale.CreateJobScalePlanReq) (*job_autoscale.CreateJobScalePlanRsp, error) {
	_, err := auth.InnerAuthJob(req.WorkSpaceId, req.IsSupOwner, req.JobSerialId, req.AppId, req.SubAccountUin, req.Region, req.Action)
	if err != nil {
		logger.Errorf("%s: User  uin %s is NOT authenticated, refuse to serve", req.RequestId, req.Uin)
		return nil, err
	}

	job, err := service.WhetherJobExists(int32(req.AppId), req.Region, req.JobSerialId)
	if err != nil {
		return nil, err
	}

	//plan个数不能超过5个
	query := &model.ListJobScalePlanQuery{
		AppId:       int32(req.AppId),
		JobSerialId: req.JobSerialId,
	}
	cnt, _, err := listJobScalePlan(query)
	if err != nil {
		logger.Errorf("Failed to list jobscaleplan, with errors:%+v", err)
		return nil, err
	}
	if cnt >= 5 {
		logger.Errorf("JobScalePlan has reached its maximum scale！")
		return nil, errors.New("JobScalePlan has reached its maximum scale！")
	}

	// 验证时间
	tasks := make([]*ScaleConfig, 0)
	for _, v := range req.JobScaleRules {
		detail := &ScaleConfig{}
		err = json.Unmarshal([]byte(v.Configuration), detail)
		tasks = append(tasks, detail)
	}
	err = CheckTime(tasks)
	if err != nil {
		logger.Errorf("Create scale plan check time is wrong:%s", err)
		return nil, err
	}

	//验证名字是否符合标准
	err = service.CheckNameValidityV2(req.Name, 50)
	if err != nil {
		return nil, err
	}

	repeat, err := checkNameRepeat(req.JobSerialId, req.Name)
	if err != nil {
		return nil, err
	}
	if repeat {
		return nil, errors.New("JobScalePlan's Name is repeat！")
	}

	logger.Infof("CreateJobScalePlanEntityFromReq %+v", req)
	plan := CreateJobScalePlanEntityFromReq(req)
	plan.Zone = job.Zone
	rules := make([]*table.JobScaleRule, 0)
	for _, v := range req.JobScaleRules {
		rule := CreateJobScaleRuleEntityFromReq(req, v)
		rule.Zone = job.Zone
		rule.ReachLimit = v.ReachLimit
		rules = append(rules, rule)
	}
	// plan不能没有rule
	if len(rules) == 0 {
		return nil, errors.New("Scale Plan need at least one rule")
	}

	logger.Infof("CreateScalePlan %+v", plan)
	planSerialId, err := createScalePlan(req.RequestId, plan, rules)
	if err != nil {
		return nil, err
	}
	return &job_autoscale.CreateJobScalePlanRsp{PlanSerialId: planSerialId}, nil
}

func CreateJobScalePlanEntityFromReq(req *job_autoscale.CreateJobScalePlanReq) *table.JobScalePlan {
	return &table.JobScalePlan{
		AppId:       req.AppId,
		JobSerialId: req.JobSerialId,
		Region:      req.Region,
		Name:        req.Name,
		Modifier:    req.Modifier,
		CreatorUin:  req.SubAccountUin,
		OwnerUin:    req.Uin,
		Status:      constants.SCALE_RULES_STATUS_INACTIVE,
		CreateTime:  util.GetCurrentTime(),
		UpdateTime:  util.GetCurrentTime(),
	}
}
func CreateJobScaleRuleEntityFromReq(req *job_autoscale.CreateJobScalePlanReq, rule *job_autoscale.JobScaleRule) *table.JobScaleRule {
	item := &table.JobScaleRule{}
	item.JobId = req.JobSerialId
	item.Region = req.Region
	item.Status = constants.SCALE_RULES_STATUS_INACTIVE
	item.Configuration = rule.Configuration
	item.AppId = req.AppId
	item.CreatorUin = req.SubAccountUin
	item.OwnerUin = req.Uin
	item.RuleName = constants.SCALE_RULES_AUTO_SCALE_TIME_BASED
	item.CreateTime = util.GetCurrentTime()
	item.UpdateTime = util.GetCurrentTime()
	return item
}
func checkNameRepeat(serialId, name string) (bool, error) {
	txManager := service2.GetTxManager()
	sql := "SELECT * from JobScalePlan where JobSerialId = ? And Name = ? AND Status != ?"
	args := make([]interface{}, 0, 0)
	args = append(args, serialId)
	args = append(args, name)
	args = append(args, constants.SCALE_PLAN_STATUS_DELETE)
	cnt, _, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Error occurs when get JobScalePlan from db, with errors:%+v", err)
		return false, err
	}
	if cnt != 0 {
		return true, nil
	}
	return false, nil
}

func createScalePlan(reqId string, plan *table.JobScalePlan, rules []*table.JobScaleRule) (id string, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, reqId+"CreateScaleRule error!")
	txManager := service2.GetTxManager()

	var planSerialId string
	var ruleSerialId string

	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		id := tx.SaveObject(plan, "JobScalePlan")
		cidUtil := &util.CidUtil{}
		planSerialId = cidUtil.EncodeId(id, "plan", "plan", util.GetNowTimestamp(), 8)
		tx.ExecuteSqlWithArgs("UPDATE JobScalePlan set SerialId = ?  WHERE id = ? ", planSerialId, id)
		for _, v := range rules {
			v.PlanSerialId = planSerialId
			id := tx.SaveObject(v, "JobScaleRule")
			ruleSerialId = cidUtil.EncodeId(id, "rule", "rule", util.GetNowTimestamp(), 8)
			tx.ExecuteSqlWithArgs("UPDATE JobScaleRule set SerialId = ?  WHERE id = ? ", ruleSerialId, id)
		}

		return nil
	}).Close()
	return planSerialId, nil

}

func DoDeleteJobScalePlan(req *job_autoscale.DeleteJobScalePlanReq) (*job_autoscale.DeleteJobScalePlanRsp, error) {
	rsp := &job_autoscale.DeleteJobScalePlanRsp{}

	_, err := auth.InnerAuthJob(req.WorkSpaceId, req.IsSupOwner, req.JobSerialId, req.AppId, req.SubAccountUin, req.Region, req.Action)
	if err != nil {
		logger.Errorf("%s: User  uin %s is NOT authenticated, refuse to serve", req.RequestId, req.Uin)
		return nil, err
	}

	_, err = ExistJobScalePlanByPlanSerialId(req.PlanSerialId, int32(req.AppId))
	if err != nil {
		return nil, err
	}

	err = changeScalePlanStates(int32(req.AppId), req.PlanSerialId, constants.SCALE_PLAN_STATUS_DELETE)
	if err != nil {
		return nil, err
	}

	return rsp, nil
}

func ExistJobScalePlanByPlanSerialId(planSerialId string, appId int32) (*table.JobScalePlan, error) {
	sql := "Select * from JobScalePlan"
	cond := dao.NewCondition()
	cond.Eq("SerialId", planSerialId)
	if appId != 0 {
		cond.Eq("AppId", appId)
	}
	cond.Ne("Status", constants.SCALE_PLAN_STATUS_DELETE)
	txManager := service2.GetTxManager()
	where, args := cond.GetWhere()
	sql += where
	cnt, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Error occurs when get JobScalePlan from db, with errors:%+v", err)
		return nil, err
	}

	if cnt != 1 {
		logger.Errorf("JobScalePlan:%s not exist", planSerialId)
		return nil, errors.New("JobScalePlan not exist")
	}

	plan := &table.JobScalePlan{}
	err = util.ScanMapIntoStruct(plan, data[0])
	if err != nil {
		logger.Errorf("Failed to convert JobScalePlan data bytes into struct, with errors:%+v", err)
		return nil, err
	}

	return plan, nil
}

//同时更新计划以及规则的状态
func updateJobScalePlanStatus(sql1 string, sql2 string, args []interface{}) (err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, "UpdateJobScalePlan error!")
	txManager := service2.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		result := tx.ExecuteSql(sql1, args)
		rowsAffected, err := result.RowsAffected()
		if err != nil {
			logger.Errorf("Failed to update jobscaleplan, with errors:%+v", err)
			return err
		}
		if rowsAffected == 0 {
			logger.Errorf("Failed to update jobscaleplan, with affected rows is 0")
			return errors.New("Failed to update jobscaleplan, with affected rows is 0")
		}

		result = tx.ExecuteSql(sql2, args)
		rowsAffected, err = result.RowsAffected()
		if err != nil {
			logger.Errorf("Failed to update jobscalerule, with errors:%+v", err)
			return err
		}
		if rowsAffected == 0 {
			logger.Warningf("Failed to update jobscalerule, with affected rows is 0")
		}
		return nil
	}).Close()
	return nil
}

func DoDescribeJobScalePlan(req *job_autoscale.DescribeJobScalePlansReq) (*job_autoscale.DescribeJobScalePlansRsp, error) {
	_, err := auth.InnerAuthJob(req.WorkSpaceId, req.IsSupOwner, req.JobSerialId, req.AppId, req.SubAccountUin, req.Region, req.Action)
	if err != nil {
		logger.Errorf("%s: User  uin %s is NOT authenticated, refuse to serve", req.RequestId, req.Uin)
		return nil, err
	}

	rsp := &job_autoscale.DescribeJobScalePlansRsp{}
	planSet := make([]*job_autoscale.PlanSetItem, 0, 0)

	job, err := service.WhetherJobExists(int32(req.AppId), req.Region, req.JobSerialId)
	if err != nil {
		return nil, err
	}

	//兼容old定时计划
	rule, err := checkOldRule(req)
	if err != nil {
		return nil, err
	}
	//存在旧rule,且job存在线上的jobconfig
	if rule != nil && job.PublishedJobConfigId != constants.JOB_CONFIG_STATUS_PUBLISHED_FAIL {
		err = CreateNewScalePlanFromOldRule(rule, req, job)
		if err != nil {
			return nil, err
		}
	}

	var data []*table.JobScalePlan
	if len(req.Filters) > 0 { // 3. 如果指定了Filters
		data, err = GetJobScalePlansByFilters(req)
		if err != nil {
			return nil, err
		}
	} else {
		query := &model.ListJobScalePlanQuery{
			AppId:        int32(req.AppId),
			JobSerialId:  req.JobSerialId,
			IsVagueNames: false,
			OrderByList:  req.OrderBy,
		}
		_, data, err = listJobScalePlan(query)
		if err != nil {
			return nil, err
		}
	}

	planSet, err = queryJobScalePlanRules(data, job.CuMem, req.AppId)
	if err != nil {
		return nil, err
	}

	scaleReachLimit, err := quota.NewQuota().GetQuota("", req.AppId, quota.ScaleReachLimit)
	if scaleReachLimit > 0 {
		rsp.ReachLimitCpu = constants.REACHLIMIT
		rsp.ReachLimitMem = constants.REACHLIMIT * int(job.CuMem)
	} else {
		rsp.ReachLimitCpu = int(scaleReachLimit)
		rsp.ReachLimitMem = int(scaleReachLimit) * int(job.CuMem)
	}
	rsp.PlanSet = planSet
	rsp.TotalCount = len(planSet)
	return rsp, nil
}

func checkOldRule(req *job_autoscale.DescribeJobScalePlansReq) (*table.JobScaleRule, error) {
	oldRules, err := queryOldScaleRules(int32(req.AppId), req.JobSerialId)
	if err != nil {
		logger.Errorf("Failed to query old JobScalePlanScale for job %s because %+v. ", req.JobSerialId, err)
		return nil, err
	}
	if len(oldRules) == 0 {
		return nil, nil
	}
	if len(oldRules) != 1 {
		logger.Errorf("Old time based scale rule find more than one:%+v", oldRules)
		return nil, errors.New("Old time based scale rule find more than one")
	}
	rule := oldRules[0]
	return rule, nil
}

func CreateNewScalePlanFromOldRule(rule *table.JobScaleRule, req *job_autoscale.DescribeJobScalePlansReq, job *table2.Job) error {
	logger.Infof("old scale plan rules: %+v convert to new rules", rule)
	txManager := service2.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		//删除旧rule
		if err := ModifyJobScaleRuleStatusByRuleId(req.RequestId, []string{rule.SerialId}, constants.SCALE_RULES_STATUS_DELETE, req.AppId); err != nil {
			return err
		}
		// 如果已经存在启动的rule
		rules, err := QueryJobScaleRules(req.JobSerialId, req.AppId, constants.SCALE_RULES_STATUS_ACTIVE, req.RequestId)
		if err != nil {
			return err
		}
		// 排除启动的旧rule
		if len(rules) > 1 || (len(rules) == 1 && rules[0].SerialId != rule.SerialId) {
			logger.Errorf("Job: %s already had run job scale rule or plan", req.JobSerialId)
			// 存在冲突，将老规则关掉
			rule.Status = constants.SCALE_RULES_STATUS_INACTIVE
		}

		createPlanReq := &job_autoscale.CreateJobScalePlanReq{}
		createPlanReq.AppId = req.AppId
		createPlanReq.JobSerialId = job.SerialId
		createPlanReq.Uin = req.Uin
		createPlanReq.SubAccountUin = req.SubAccountUin
		createPlanReq.Region = req.Region
		createPlanReq.Modifier = "system"
		createPlanReq.Name = "自定义调优"
		plan := CreateJobScalePlanEntityFromReq(createPlanReq)
		plan.Zone = job.Zone
		plan.Status = rule.Status
		rules, err = CreateNewRuleFromOldRule(rule, job)
		if err != nil {
			logger.Errorf("Failed to create new rule from old rule:%s ,because %+v. ", rule.SerialId, err)
			return err
		}
		logger.Infof("converted new rule:  %+v", rules)
		if err != nil {
			return err
		}
		logger.Infof("CreateNewScalePlan %+v", plan)
		_, err = createScalePlan(req.RequestId, plan, rules)
		if err != nil {
			return err
		}
		return nil
	}).Close()
	return nil
}

func CreateNewRuleFromOldRule(rule *table.JobScaleRule, job *table2.Job) (rules []*table.JobScaleRule, err error) {
	detail := &ScaleConfig{}
	detail1 := &ScaleConfig{}
	detail2 := &ScaleConfig{}
	err = json.Unmarshal([]byte(rule.Configuration), detail)
	if err != nil {
		logger.Errorf("Failed to Unmarshal rule_configuration for rule %s because %+v. ", rule.SerialId, err)
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("AutoScaleTimeBased - failed to Unmarshal rule_configuration for rule %s", rule.SerialId), nil)
	}
	//查询线上的job config
	jobconfig, err := service3.GetPublishedJobConfig(int32(job.AppId), job.SerialId)
	if err != nil {
		return nil, err
	}
	detail1.ScaleType = constants.SCALE_ACTION_TYPE_TASK_FLEXIBLE
	detail2.ScaleType = constants.SCALE_ACTION_TYPE_TASK_FLEXIBLE
	detail1.Frequency = detail.Frequency
	detail2.Frequency = detail.Frequency
	detail1.TimeUnit = detail.TimeUnit
	detail2.TimeUnit = detail.TimeUnit
	detail1.OnTime = detail.StartTime
	detail2.OnTime = detail.EndTime
	// 执行了第一次扩容行为的rule（LastSucceededActionType是0）
	//if detail.PreActionSerialId != "" {
	//	action, err := QueryJobTuningActionById(job.SerialId, detail.PreActionSerialId)
	//	if err != nil {
	//		logger.Errorf("Failed to query JobTuningAction %s because %+v. ", action.SerialId, err)
	//		return nil, err
	//	}
	//	if action.Status == 2 {
	//		if action.ActionType == constants.SCALE_ACTION_TYPE_TASK_PARALLELISM_SCALE_UP || action.ActionType == constants.SCALE_ACTION_TYPE_TASK_RESOURCE_SCALE_UP {
	//			detail.LastSucceededActionType = constants.SCALE_ACTION_TYPE_SCALE_UP
	//		} else {
	//			detail.LastSucceededActionType = constants.SCALE_ACTION_TYPE_SCALE_DOWN
	//		}
	//	} else if action.Status == 1 {
	//		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "jobtunningaction is running,need wait a time to flush the window!", nil)
	//	}
	//	// 其它状态视作失败
	//}
	// 记录LastSucceededActionType的地方放在了扩缩容flow的endopoint，成功的话一定会有
	if detail.LastSucceededActionType == constants.SCALE_ACTION_TYPE_SCALE_UP {
		if detail.ScaleType == constants.SCALE_ACTION_TYPE_TASK_RESOURCE_INT {
			detail1.TmCuSpec = jobconfig.TmCuSpec
			detail1.JmCuSpec = jobconfig.JmCuSpec
			detail2.TmCuSpec = jobconfig.TmCuSpec / detail.Ratio
			detail2.JmCuSpec = jobconfig.JmCuSpec
			detail1.DefaultParallelism = int(jobconfig.DefaultParallelism)
			detail2.DefaultParallelism = int(jobconfig.DefaultParallelism)
		} else if detail.ScaleType == constants.SCALE_ACTION_TYPE_TASK_PARALLELISM_INT {
			detail1.TmCuSpec = jobconfig.TmCuSpec
			detail1.JmCuSpec = jobconfig.JmCuSpec
			detail2.TmCuSpec = jobconfig.TmCuSpec
			detail2.JmCuSpec = jobconfig.JmCuSpec
			detail1.DefaultParallelism = int(jobconfig.DefaultParallelism)
			detail2.DefaultParallelism = int(math.Floor(float64(jobconfig.DefaultParallelism)/float64(detail.Ratio) + 0.5))
			// 测试环境会出现暂停之后出现，启用其它规则之后，导致DefaultParallelism = 0的情况。
			if detail2.DefaultParallelism == 0 {
				detail2.DefaultParallelism = 1
			}
		} else {
			return nil, errorcode.NewStackError(errorcode.FailedOperationCode, "jobscalerule configuration tasktype for job "+job.SerialId+" is wrong!", err)
		}
	} else {
		// 前一个action类型是down，以及新建的rule
		if detail.ScaleType == constants.SCALE_ACTION_TYPE_TASK_RESOURCE_INT {
			detail1.TmCuSpec = jobconfig.TmCuSpec * detail.Ratio
			detail1.JmCuSpec = jobconfig.JmCuSpec
			detail2.TmCuSpec = jobconfig.TmCuSpec
			detail2.JmCuSpec = jobconfig.JmCuSpec
			detail1.DefaultParallelism = int(jobconfig.DefaultParallelism)
			detail2.DefaultParallelism = int(jobconfig.DefaultParallelism)
		} else if detail.ScaleType == constants.SCALE_ACTION_TYPE_TASK_PARALLELISM_INT {
			detail1.TmCuSpec = jobconfig.TmCuSpec
			detail1.JmCuSpec = jobconfig.JmCuSpec
			detail2.TmCuSpec = jobconfig.TmCuSpec
			detail2.JmCuSpec = jobconfig.JmCuSpec
			detail1.DefaultParallelism = int(math.Floor(float64(jobconfig.DefaultParallelism)*float64(detail.Ratio) + 0.5))
			detail2.DefaultParallelism = int(jobconfig.DefaultParallelism)
		} else {
			return nil, errorcode.NewStackError(errorcode.FailedOperationCode, "jobscalerule configuration tasktype for job "+job.SerialId+" is wrong!", err)
		}
	}

	rules, err = CreateJobScaleRuleEntityFromOldRule(rule, []*ScaleConfig{detail1, detail2})
	if err != nil {
		return nil, err
	}
	return rules, nil
}

func CreateJobScaleRuleEntityFromOldRule(oldRule *table.JobScaleRule, configurations []*ScaleConfig) (rules []*table.JobScaleRule, err error) {
	for _, configuration := range configurations {
		c, err := json.Marshal(configuration)
		if err != nil {
			return nil, err
		}

		item := &table.JobScaleRule{}
		item.JobId = oldRule.JobId
		item.Region = oldRule.Region
		item.Status = oldRule.Status
		item.Zone = oldRule.Zone
		item.Properties = oldRule.Properties
		item.Configuration = string(c)
		item.AppId = oldRule.AppId
		item.ReachLimit = oldRule.ReachLimit
		item.Step = oldRule.Step
		item.Threshold = oldRule.Threshold
		item.ConditionRatio = oldRule.ConditionRatio
		item.CreatorUin = oldRule.CreatorUin
		item.OwnerUin = oldRule.OwnerUin
		item.RuleName = constants.SCALE_RULES_AUTO_SCALE_TIME_BASED
		item.CreateTime = util.GetCurrentTime()
		item.UpdateTime = util.GetCurrentTime()
		rules = append(rules, item)
	}
	return rules, nil
}

func queryOldScaleRules(appId int32, jobSerialId string) (_ []*table.JobScaleRule, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, "Failed to query old JobScalePlanScale")

	sql := " SELECT * FROM JobScaleRule WHERE AppId = ? and JobId = ? and RuleName = ? and Status <> ? and (PlanSerialId is null OR PlanSerialId = '')"
	args := make([]interface{}, 0, 0)
	args = append(args, appId)
	args = append(args, jobSerialId)
	args = append(args, constants.SCALE_RULES_AUTO_SCALE_TIME_BASED)
	args = append(args, constants.SCALE_RULES_STATUS_DELETE)
	txManager := service2.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Error occurs when get JobScalePlan's old Rule from db, with errors:%+v", err)
		return nil, err
	}
	rules := make([]*table.JobScaleRule, 0, 0)
	for _, d := range data {
		rule := &table.JobScaleRule{}
		err = util.ScanMapIntoStruct(rule, d)
		if err != nil {
			logger.Errorf("Failed to convert JobScaleRule data bytes into struct, with errors:%+v", err)
			return nil, err
		}
		rules = append(rules, rule)
	}
	return rules, nil
}

func queryJobScalePlanRules(plans []*table.JobScalePlan, cuMem int8, appId int64) (_ []*job_autoscale.PlanSetItem, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, "Failed to query JobScalePlanRules")
	sql := " SELECT * FROM JobScaleRule WHERE AppId = ? and PlanSerialId = ? and JobId = ? and Status <> ?"
	txManager := service2.GetTxManager()
	planSet := make([]*job_autoscale.PlanSetItem, 0, 0)

	if len(plans) == 0 {
		return planSet, nil
	}

	scaleReachLimit, err := quota.NewQuota().GetQuota("", appId, quota.ScaleReachLimit)
	if err != nil {
		// 不用返回err，会只用rule的ReachLimit
		logger.Errorf("Failed to get scaleReachLimit from quota, with errors:%+v", err)
	}

	for _, plan := range plans {
		planItem := &job_autoscale.PlanSetItem{
			SerialId:    plan.SerialId,
			JobSerialId: plan.JobSerialId,
			Name:        plan.Name,
			Status:      plan.Status,
			Modifier:    plan.Modifier,
			Zone:        plan.Zone,
			Region:      plan.Region,
			CreateTime:  plan.CreateTime,
			UpdateTime:  plan.UpdateTime,
		}

		args := make([]interface{}, 0, 0)
		args = append(args, plan.AppId)
		args = append(args, plan.SerialId)
		args = append(args, plan.JobSerialId)
		args = append(args, constants.SCALE_RULES_STATUS_DELETE)
		_, data1, err := txManager.GetQueryTemplate().DoQuery(sql, args)
		if err != nil {
			logger.Errorf("Error occurs when get JobScalePlan's Rule from db, with errors:%+v", err)
			return nil, err
		}
		ruleSet := make([]*job_autoscale.PlanRuleSetItem, 0, 0)
		for _, r := range data1 {
			rule := &table.JobScaleRule{}
			err = util.ScanMapIntoStruct(rule, r)
			if err != nil {
				logger.Errorf("Failed to convert JobScaleRule data bytes into struct, with errors:%+v", err)
				return nil, err
			}
			s := &ScaleConfig{}
			err := json.Unmarshal([]byte(rule.Configuration), s)
			if err != nil {
				logger.Errorf("Failed to unmarshal ScaleConfig, with errors:%+v", err)
				return nil, err
			}
			// 将老的rule的configuration显示。
			if s.JobManagerCpu == 0 || s.JobManagerMem == 0 || s.TaskManagerCpu == 0 || s.TaskManagerMem == 0 {
				s.JobManagerCpu = s.JmCuSpec
				s.JobManagerMem = service.GetFloat2Dot(s.JmCuSpec * float32(cuMem))
				s.TaskManagerCpu = s.TmCuSpec
				s.TaskManagerMem = service.GetFloat2Dot(s.TmCuSpec * float32(cuMem))
				json, err := json.Marshal(s)
				if err != nil {
					logger.Errorf("Failed to marshal ScaleConfig, with errors:%+v", err)
					return nil, err
				}
				rule.Configuration = string(json)
			}
			// 返回的时候判断是否有配额还是按默认限制
			reachLimitCpu := rule.ReachLimit
			reachLimitMem := rule.ReachLimit * int(cuMem)
			if scaleReachLimit > 0 {
				reachLimitCpu = int(scaleReachLimit)
				reachLimitMem = int(scaleReachLimit) * int(cuMem)
			}
			ruleItem := &job_autoscale.PlanRuleSetItem{
				PlanSerialId:  plan.SerialId,
				JobSerialId:   rule.JobId,
				RuleSerialId:  rule.SerialId,
				RuleName:      rule.RuleName,
				Status:        rule.Status,
				ScalingType:   "auto",
				Properties:    rule.Properties,
				Configuration: rule.Configuration,
				Zone:          rule.Zone,
				Region:        rule.Region,
				CreateTime:    rule.CreateTime,
				UpdateTime:    rule.UpdateTime,
				ReachLimitCpu: reachLimitCpu,
				ReachLimitMem: reachLimitMem,
			}
			ruleSet = append(ruleSet, ruleItem)
		}
		planItem.RuleSet = ruleSet
		planItem.TotalCount = len(ruleSet)
		planSet = append(planSet, planItem)
	}
	return planSet, nil
}

func GetJobScalePlansByFilters(req *job_autoscale.DescribeJobScalePlansReq) ([]*table.JobScalePlan, error) {
	var nameFilters []string
	for i := 0; i < len(req.Filters); i++ {
		if req.Filters[i].Name == "Name" {
			nameFilters = req.Filters[i].Values
		} else {
			logger.Errorf("Invalid filter name: %s", req.Filters[i].Name)
			return nil, errors.New(fmt.Sprintf("Invalid filter name: %s", req.Filters[i].Name))
		}
	}
	query := &model.ListJobScalePlanQuery{
		AppId:        int32(req.AppId),
		JobSerialId:  req.JobSerialId,
		Names:        nameFilters,
		IsVagueNames: true,
		OrderByList:  req.OrderBy,
	}
	_, data, err := listJobScalePlan(query)
	if err != nil {
		return nil, err
	}

	return data, nil
}

func listJobScalePlan(query *model.ListJobScalePlanQuery) (count int, _ []*table.JobScalePlan, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, "Failed to list JobScalePlan")
	sql := " SELECT * FROM JobScalePlan"
	orderBy := ""
	if len(query.OrderByList) > 0 {
		for i := 0; i < len(query.OrderByList); i++ {
			by := query.OrderByList[i]
			if by.Field == "" || by.Type == "" {
				return 0, nil, errors.New("Order by nil field or type")
			}
			orderBy += fmt.Sprintf("%s %s", by.Field, by.Type)
			if i != len(query.OrderByList)-1 {
				orderBy += " , "
			}
		}
	}

	cond := dao.NewCondition()
	cond.Eq("AppId", query.AppId)
	cond.Eq("JobSerialId", query.JobSerialId)
	if query.Status != 0 {
		cond.Eq("Status", query.Status)
	} else {
		cond.Ne("Status", constants.SCALE_RULES_STATUS_DELETE)
	}

	if len(query.Names) > 0 {
		names := service.UniqueSliceString(query.Names)
		if query.IsVagueNames {
			likeCond := dao.NewCondition().EnableOr()

			for i := 0; i < len(names); i++ {
				likeCond.Like("Name", "%"+names[i]+"%")
			}
			cond.Condition(likeCond)
		} else {
			cond.In("j.Name", names)
		}
	}
	where, args := cond.GetWhere()
	sql += where

	if orderBy != "" {
		sql += fmt.Sprintf(" ORDER BY %s, CreateTime DESC ", orderBy)
	} else {
		sql += " ORDER BY CreateTime DESC "
	}

	txManager := service2.GetTxManager()
	cnt, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Error occurs when get JobScalePlan from db, with errors:%+v", err)
		return 0, nil, err
	}

	plans := make([]*table.JobScalePlan, 0, 0)
	for i := 0; i < len(data); i++ {
		plan := &table.JobScalePlan{}
		err = util.ScanMapIntoStruct(plan, data[i])
		if err != nil {
			logger.Errorf("Failed to convert JobScalePlan data bytes into struct, with errors:%+v", err)
			return 0, nil, err
		}
		plans = append(plans, plan)
	}
	return cnt, plans, nil
}

func DoModifyJobScalePlan(req *job_autoscale.ModifyJobScalePlanReq) (*job_autoscale.ModifyJobScalePlanRsp, error) {
	_, err := auth.InnerAuthJob(req.WorkSpaceId, req.IsSupOwner, req.JobSerialId, req.AppId, req.SubAccountUin, req.Region, req.Action)
	if err != nil {
		logger.Errorf("%s: User  uin %s is NOT authenticated, refuse to serve", req.RequestId, req.Uin)
		return nil, err
	}

	// plan不能没有rule
	if len(req.JobScaleRules) == 0 {
		return nil, errors.New("Scale Plan need at least one rule")
	}
	//校验时间
	tasks := make([]*ScaleConfig, 0)
	for _, v := range req.JobScaleRules {
		detail := &ScaleConfig{}
		err = json.Unmarshal([]byte(v.Configuration), detail)
		tasks = append(tasks, detail)
	}
	err = CheckTime(tasks)
	if err != nil {
		logger.Errorf("%s:Modify scale plan check time is wrong:%s", req.RequestId, err)
		return nil, err
	}
	if err != nil {
		return nil, err
	}

	ruleSerialIds, err := modifyJobScalePlan(req)
	if err != nil {
		return nil, err
	}

	rules, err := QueryJobScaleRulesByType(req.JobSerialId, req.AppId, 0, constants.SCALE_RULES_AUTO_SCALE_TIME_BASED, req.PlanSerialId)
	if err != nil {
		return nil, err
	}
	deleteRuleSerialIds := make([]string, 0, 0)
	// 遍历，标记需要删除的rule
	for _, rule := range rules {
		flag := false
		for _, ruleSerialId := range ruleSerialIds {
			if ruleSerialId == rule.SerialId {
				flag = true
			}
		}
		if !flag {
			deleteRuleSerialIds = append(deleteRuleSerialIds, rule.SerialId)
		}
	}
	//删除没有的rule
	err = ModifyJobScaleRuleStatusByRuleId(req.RequestId, deleteRuleSerialIds, constants.SCALE_RULES_STATUS_DELETE, req.AppId)
	if err != nil {
		return nil, err
	}
	// 忽略JOB所有action
	rules, err = QueryJobScaleRules(req.JobSerialId, 0, constants.SCALE_RULES_STATUS_ACTIVE, "")
	if err != nil {
		logger.Errorf("QueryJobScaleRules error, %+v", err)
		return nil, err
	}
	err = TryLockAndIgnoreJobTuningAction(req.JobSerialId, rules)
	if err != nil {
		logger.Errorf("IgnoreJobTuningAction - err:%s", err)
		return nil, err
	}
	rsp := &job_autoscale.ModifyJobScalePlanRsp{
		PlanSerialId: req.PlanSerialId,
	}
	return rsp, nil
}

func CheckTime(tasks []*ScaleConfig) error {
	layout1 := "2006-01-02 15:04"
	layout2 := "15:04"
	// 所有的时间，是秒形式
	allTime := make([]*timeLine, 0)
	// once类型Time 的数组
	onceTime := make([]*ruleTime, 0)
	dayTime := make([]*ruleTime, 0)
	weekTime := make([]*ruleTime, 0)
	monthTime := make([]*ruleTime, 0)
	errs := make([]string, 0)

	for _, v := range tasks {
		if v.TimeUnit == constants.SCALE_RULES_TIMEUNIT_ONCE {
			onTime, err := time.Parse(layout1, v.OnTime)
			if err != nil {
				return err
			}
			allTime = append(allTime, &timeLine{
				time:     onTime.Unix(),
				timeUnit: constants.SCALE_RULES_TIMEUNIT_ONCE,
			})
			newTime := strings.Split(v.OnTime, " ")
			if len(newTime) != 2 {
				logger.Errorf("CheckTime -  Time format is wrong: %s", v.OnTime)
				continue
			}
			onTime, err = time.Parse(layout2, newTime[1])
			if err != nil {
				return err
			}
			onceTime = append(onceTime, &ruleTime{
				ruleTime: onTime,
				index:    v.Index,
				timeunit: v.TimeUnit,
			})
		} else if v.TimeUnit == constants.SCALE_RULES_TIMEUNIT_DAY {
			onTime, err := time.Parse(layout2, v.OnTime)
			if err != nil {
				return err
			}
			dayTime = append(dayTime, &ruleTime{
				ruleTime: onTime,
				index:    v.Index,
				timeunit: v.TimeUnit,
			})
		} else if v.TimeUnit == constants.SCALE_RULES_TIMEUNIT_WEEK {
			onTime, err := time.Parse(layout2, v.OnTime)
			if err != nil {
				return err
			}
			weekTime = append(weekTime, &ruleTime{
				ruleTime: onTime,
				index:    v.Index,
				timeunit: v.TimeUnit,
			})
		} else if v.TimeUnit == constants.SCALE_RULES_TIMEUNIT_MONTH {
			onTime, err := time.Parse(layout2, v.OnTime)
			if err != nil {
				return err
			}
			monthTime = append(monthTime, &ruleTime{
				ruleTime: onTime,
				index:    v.Index,
				timeunit: v.TimeUnit,
			})
		}
	}
	now := time.Now()
	// 检查每日
	err := checkDayTime(dayTime, weekTime, monthTime, onceTime)
	if err != nil {
		logger.Errorf("checkDayTime - err:%v", err)
		return err
	}

	flag := checkConflict(weekTime, monthTime, onceTime)
	if !flag {
		if len(errs) > 0 {
			return errors.New(fmt.Sprintf("time conflicts:%v", errs))
		}
		return nil
	}

	// 默认是2，但是如果数据库设置了，以数据库为准
	deadLine := 2
	res, err := service4.GetConfigurationValueByKey(constants.CONF_KEY_CHECK_TIME_CONFLICT_DEAD_LINE)
	if err != nil {
		logger.Errorf("get deadline by database fail:%s", err)
	} else {
		deadLine, err = strconv.Atoi(res)
		if err != nil {
			logger.Errorf("deadline convert to int fail:%s", err)
		}
	}

	ptTime := addMonthTime(monthTime, now, deadLine)
	allTime = append(allTime, ptTime...)
	ptTime = addWeekTime(weekTime, now, deadLine)
	allTime = append(allTime, ptTime...)

	sort.Slice(allTime, func(i, j int) bool {
		return allTime[i].time < allTime[j].time
	})

	const thirtyMinutesInSeconds = 30 * 60 // 30分钟转换为秒
	for i := 0; i < len(allTime)-1; i++ {
		// 计算相邻两个时间戳的差值
		if allTime[i+1].time-allTime[i].time < thirtyMinutesInSeconds {
			logger.Errorf("time conflict[%stime:%s, %stime:%s]。", allTime[i].timeUnit, time.Unix(allTime[i].time, 0), allTime[i+1].timeUnit, time.Unix(allTime[i+1].time, 0))
			return errors.New(fmt.Sprintf("time conflict[%stime:%s, %stime:%s]。", allTime[i].timeUnit, time.Unix(allTime[i].time, 0), allTime[i+1].timeUnit, time.Unix(allTime[i+1].time, 0)))
			//errs = append(errs, fmt.Sprintf("time conflict[%stime:%s, %stime:%s]。", allTime[i].timeUnit, time.Unix(allTime[i].time, 0), allTime[i+1].timeUnit, time.Unix(allTime[i+1].time, 0)))
		}
	}
	return nil
}

func checkConflict(weekTime, monthTime, onceTime []*ruleTime) bool {
	timeWithoutDay := make([]*ruleTime, 0)
	timeWithoutDay = append(timeWithoutDay, onceTime...)
	timeWithoutDay = append(timeWithoutDay, weekTime...)
	timeWithoutDay = append(timeWithoutDay, monthTime...)

	// 排序后比较会出现i和i-1比较会出现隔夜的被略过，导致bug。
	// 所以得如下比较方法
	flag := false
	for i := 0; i < len(timeWithoutDay); i++ {
		for j := i + 1; j < len(timeWithoutDay); j++ {
			// 计算相邻两个时间戳的差值
			diff := computeTimeDiff(timeWithoutDay[i].ruleTime, timeWithoutDay[i+1].ruleTime)
			if diff <= 30*time.Minute {
				flag = true
				break
			}

			//// 不同周期的30分钟内，一定会重复
			//if timeWithoutDay[i].timeunit != timeWithoutDay[i+1].timeunit {
			//	flag = true
			//} else {
			//	// 若是相同周期的不同index，30分钟重复也没关系，需要考虑隔夜问题
			//	for _, j := range timeWithoutDay[i].index {
			//		yD := j - 1
			//		if yD == -1 {
			//			yD = 6
			//		}
			//		tD := j + 1
			//		if tD == 7 {
			//			tD = 0
			//		}
			//		if contains(timeWithoutDay[i+1].index, j) {
			//			flag = true
			//			break
			//		}
			//		//0点30之前的-1，23点30之后的+1
			//		if timeWithoutDay[i].ruleTime.Hour() == 0 && timeWithoutDay[i].ruleTime.Minute() <= 30 {
			//			if contains(timeWithoutDay[i+1].index, yD) {
			//				flag = true
			//				break
			//			}
			//		} else if timeWithoutDay[i].ruleTime.Hour() == 23 && timeWithoutDay[i].ruleTime.Minute() >= 30 {
			//			if contains(timeWithoutDay[i+1].index, tD) {
			//				flag = true
			//				break
			//			}
			//		}
			//	}
			//}
			//if flag {
			//	break
			//}
		}
	}
	return flag
}

func addMonthTime(monthTime []*ruleTime, now time.Time, deadLine int) (ptTime []*timeLine) {
	if len(monthTime) != 0 {
		end := now.AddDate(deadLine, 1, 0) // 当前时间加上2年
		for t := now; !t.After(end); t = t.AddDate(0, 1, 0) {
			// 这里可能会少算最后的半个月
			for _, m := range monthTime {
				for _, index := range m.index {
					endDay := time.Date(t.Year(), t.Month()+1, 1, 0, 0, 0, 0, time.Local).AddDate(0, 0, -1)
					if index > endDay.Day() {
						continue
					}
					targetTime := time.Date(t.Year(), t.Month(), index, m.ruleTime.Hour(), m.ruleTime.Minute(), 0, 0, time.Local)
					// 舍弃now之前的，如果now是月中，就会舍弃这个月前面的index
					if targetTime.Before(now) {
						continue
					}
					ptTime = append(ptTime, &timeLine{
						time:     targetTime.Unix(),
						timeUnit: m.timeunit,
					})
				}
			}
		}
	}
	return ptTime
}

func addWeekTime(weekTime []*ruleTime, now time.Time, deadLine int) (ptTime []*timeLine) {
	if len(weekTime) != 0 {
		twoYearsLater := now.AddDate(deadLine, 0, 0)
		for t := now; t.Before(twoYearsLater); t = t.AddDate(0, 0, 7) {
			for _, w := range weekTime {
				for _, index := range w.index {
					indexDay := t.AddDate(0, 0, (index-int(t.Weekday())+7)%7)
					targetDay := time.Date(indexDay.Year(), indexDay.Month(), indexDay.Day(), w.ruleTime.Hour(), w.ruleTime.Minute(), 0, 0, time.Local)
					if targetDay.Before(now) {
						continue
					}
					ptTime = append(ptTime, &timeLine{
						time:     targetDay.Unix(),
						timeUnit: w.timeunit,
					})
				}
			}
		}
	}
	return ptTime
}

func checkDayTime(dayTime, weekTime, monthTime, onceTime []*ruleTime) error {
	layout2 := "15:04"
	if len(dayTime) > 0 {
		for i := 0; i < len(dayTime); i++ {
			for j := i + 1; j < len(dayTime); j++ {
				//计算时间差
				diff := computeTimeDiff(dayTime[i].ruleTime, dayTime[j].ruleTime)
				// 判断是否在30分钟内
				if diff < 30*time.Minute {
					logger.Errorf("time conflict,day time:%s,daily time:%s", dayTime[i].ruleTime.Format(layout2), dayTime[j].ruleTime.Format(layout2))
					return errors.New(fmt.Sprintf("time conflict[daily time:%s,daily time:%s]。", dayTime[i].ruleTime.Format(layout2), dayTime[j].ruleTime.Format(layout2)))
					//errs = append(errs, fmt.Sprintf("time conflict[daily time:%s,daily time:%s]。", dayTime[i].ruleTime.Format(layout2), dayTime[j].ruleTime.Format(layout2)))
				}
			}
			for _, m := range monthTime {
				//计算时间差
				diff := computeTimeDiff(dayTime[i].ruleTime, m.ruleTime)
				// 判断是否在30分钟内
				if diff < 30*time.Minute {
					logger.Errorf("time conflict,day time:%s,month time:%s", dayTime[i].ruleTime.Format(layout2), m.ruleTime.Format(layout2))
					return errors.New(fmt.Sprintf("time conflict[daily time:%s,monthly time:%s]。", dayTime[i].ruleTime.Format(layout2), m.ruleTime.Format(layout2)))
					//errs = append(errs, fmt.Sprintf("time conflict[daily time:%s,monthly time:%s]。", dayTime[i].ruleTime.Format(layout2), m.ruleTime.Format(layout2)))
				}
			}
			for _, w := range weekTime {
				//计算时间差
				diff := computeTimeDiff(dayTime[i].ruleTime, w.ruleTime)
				// 判断是否在30分钟内
				if diff < 30*time.Minute {
					logger.Errorf("time conflict, daily time:%s, weekly time:%s", dayTime[i].ruleTime.Format(layout2), w.ruleTime.Format(layout2))
					return errors.New(fmt.Sprintf("time conflict[daily time:%s,weekly time:%s]。", dayTime[i].ruleTime.Format(layout2), w.ruleTime.Format(layout2)))
					//errs = append(errs, fmt.Sprintf("time conflict[daily time:%s,weekly time:%s]。", dayTime[i].ruleTime.Format(layout2), w.ruleTime.Format(layout2)))
				}
			}
			for _, o := range onceTime {
				//计算时间差
				diff := computeTimeDiff(dayTime[i].ruleTime, o.ruleTime)
				// 判断是否在30分钟内
				if diff < 30*time.Minute {
					logger.Errorf("time conflict, daily time:%s, once time:%s", dayTime[i].ruleTime.Format(layout2), o.ruleTime.Format(layout2))
					return errors.New(fmt.Sprintf("time conflict[daily time:%s, once time:%s]。", dayTime[i].ruleTime.Format(layout2), o.ruleTime.Format(layout2)))
					//errs = append(errs, fmt.Sprintf("time conflict[daily time:%s, once time:%s]。", dayTime[i].ruleTime.Format(layout2), o.ruleTime.Format(layout2)))
				}
			}
		}
	}
	return nil
}

func computeTimeDiff(a, b time.Time) time.Duration {
	diff := a.Sub(b)
	if diff < 0 {
		diff = -diff // 取绝对值
	}
	// 处理跨越午夜的情况
	if diff > 12*time.Hour {
		diff = 24*time.Hour - diff
	}
	return diff
}

func modifyJobScalePlan(req *job_autoscale.ModifyJobScalePlanReq) (ruleSerialIds []string, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, "modifyJobScalePlan failed")
	plan, err := ExistJobScalePlanByPlanSerialId(req.PlanSerialId, int32(req.AppId))
	if err != nil {
		return nil, err
	}

	sql := " Update JobScalePlan Set Modifier = ?,UpdateTime = ? WHERE AppId = ? and Status <> ? and SerialId = ?"
	txManager := service2.GetTxManager()
	args := make([]interface{}, 0, 0)
	args = append(args, req.Modifier)
	args = append(args, util.GetCurrentTime())
	args = append(args, req.AppId)
	args = append(args, constants.SCALE_RULES_STATUS_DELETE)
	args = append(args, req.PlanSerialId)

	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		result := tx.ExecuteSql(sql, args)
		rowsAffected, err := result.RowsAffected()
		if err != nil {
			logger.Errorf("Failed to modify jobscaleplan, with errors:%+v", err)
			return err
		}
		if rowsAffected == 0 {
			logger.Infof("Failed to modify jobscaleplan, with affected rows is 0")
		}

		for _, rule := range req.JobScaleRules {
			if rule.RuleSerialId == "" {
				item := &table.JobScaleRule{}
				item.JobId = req.JobSerialId
				item.Region = req.Region
				item.Status = plan.Status
				item.Configuration = rule.Configuration
				item.AppId = req.AppId
				item.CreatorUin = req.SubAccountUin
				item.OwnerUin = req.Uin
				item.RuleName = constants.SCALE_RULES_AUTO_SCALE_TIME_BASED
				item.Zone = plan.Zone
				item.ReachLimit = rule.ReachLimit
				item.CreateTime = util.GetCurrentTime()
				item.UpdateTime = util.GetCurrentTime()
				item.PlanSerialId = req.PlanSerialId
				id := tx.SaveObject(item, "JobScaleRule")
				cidUtil := &util.CidUtil{}
				ruleSerialId := cidUtil.EncodeId(id, "rule", "rule", util.GetNowTimestamp(), 8)
				result = tx.ExecuteSqlWithArgs("UPDATE JobScaleRule set SerialId = ?  WHERE id = ? ", ruleSerialId, id)
				ruleSerialIds = append(ruleSerialIds, ruleSerialId)
				rowsAffected, err := result.RowsAffected()
				if rowsAffected == 0 {
					logger.Errorf("Failed to insert jobscalerule, with affected rows is 0")
					return errors.New("Failed to insert jobscalerule, with affected rows is 0")
				}
				if err != nil {
					logger.Errorf("Failed to insert jobscalerule, with errors:%+v", err)
					return err
				}
			} else {
				sql = " Update JobScaleRule Set Configuration = ?,Status = ?,UpdateTime = ? WHERE AppId = ? and Status <> ? and SerialId = ?"
				args = make([]interface{}, 0, 0)
				args = append(args, rule.Configuration)
				// 和plan状体一致
				args = append(args, plan.Status)
				args = append(args, util.GetCurrentTime())
				args = append(args, req.AppId)
				args = append(args, constants.SCALE_RULES_STATUS_DELETE)
				args = append(args, rule.RuleSerialId)
				ruleSerialIds = append(ruleSerialIds, rule.RuleSerialId)
				result := tx.ExecuteSql(sql, args)
				rowsAffected, err := result.RowsAffected()
				if err != nil {
					logger.Errorf("Failed to modify jobscalerule, with errors:%+v", err)
					return err
				}
				if rowsAffected == 0 {
					logger.Infof("Failed to modify jobscalerule, with affected rows is 0")
				}
			}
		}
		return nil
	}).Close()
	return ruleSerialIds, nil
}

func DoRunJobScalePlan(req *job_autoscale.RunJobScalePlanReq) (*job_autoscale.RunJobScalePlanRsp, error) {
	rsp := &job_autoscale.RunJobScalePlanRsp{
		PlanSerialId: req.PlanSerialId,
	}

	_, err := auth.InnerAuthJob(req.WorkSpaceId, req.IsSupOwner, req.JobSerialId, req.AppId, req.SubAccountUin, req.Region, req.Action)
	if err != nil {
		logger.Errorf("%s: User  uin %s is NOT authenticated, refuse to serve", req.RequestId, req.Uin)
		return nil, err
	}

	rules, err := QueryJobScaleRules(req.JobSerialId, req.AppId, constants.SCALE_RULES_STATUS_ACTIVE, req.RequestId)
	if err != nil {
		return nil, err
	}
	if len(rules) > 0 {
		logger.Errorf("%s:Job: %s already had run job scale rule or plan", req.RequestId, req.JobSerialId)
		return nil, errors.New("Job already had run job scale plan")
	}

	_, err = ExistJobScalePlanByPlanSerialId(req.PlanSerialId, int32(req.AppId))
	if err != nil {
		return nil, err
	}

	err = changeScalePlanStates(int32(req.AppId), req.PlanSerialId, constants.SCALE_RULES_STATUS_ACTIVE)
	if err != nil {
		return nil, err
	}

	return rsp, nil
}

func DoStopJobScalePlan(req *job_autoscale.StopJobScalePlanReq) (*job_autoscale.StopJobScalePlanRsp, error) {
	rsp := &job_autoscale.StopJobScalePlanRsp{
		PlanSerialId: req.PlanSerialId,
	}

	_, err := auth.InnerAuthJob(req.WorkSpaceId, req.IsSupOwner, req.JobSerialId, req.AppId, req.SubAccountUin, req.Region, req.Action)
	if err != nil {
		logger.Errorf("%s: User  uin %s is NOT authenticated, refuse to serve", req.RequestId, req.Uin)
		return nil, err
	}
	_, err = ExistJobScalePlanByPlanSerialId(req.PlanSerialId, int32(req.AppId))
	if err != nil {
		return nil, err
	}

	err = changeScalePlanStates(int32(req.AppId), req.PlanSerialId, constants.SCALE_RULES_STATUS_INACTIVE)
	if err != nil {
		return nil, err
	}
	// 删除之前的action
	rules, err := QueryJobScaleRules(req.JobSerialId, req.AppId, constants.SCALE_RULES_STATUS_ACTIVE, "")
	if err != nil {
		logger.Errorf("QueryJobScaleRules error, %+v", err)
		return nil, err
	}
	err = TryLockAndIgnoreJobTuningAction(req.JobSerialId, rules)
	if err != nil {
		logger.Errorf("%s: ,Failed to ignore job tuning action, with errors:%+v", req.RequestId, err)
		return nil, err
	}

	return rsp, nil
}

func changeScalePlanStates(appId int32, planSerialId string, targetStatus int8) error {
	sql1 := "update JobScalePlan set Status = ?,UpdateTime = ? where SerialId = ? and AppId = ? "
	sql2 := "update JobScaleRule set Status = ?,UpdateTime = ? where PlanSerialId = ? and AppId = ? "

	args := make([]interface{}, 0, 0)
	args = append(args, targetStatus)
	args = append(args, util.GetCurrentTime())
	args = append(args, planSerialId)
	args = append(args, appId)

	err := updateJobScalePlanStatus(sql1, sql2, args)
	return err
}

func contains(slice []int, elem int) bool {
	for _, v := range slice {
		if v == elem {
			return true
		}
	}
	return false
}

type ScaleConfig struct {
	StartTime         string  `json:"startTime"`
	EndTime           string  `json:"endTime"`
	Frequency         int     `json:"frequency"`
	TimeUnit          string  `json:"timeUnit"` // 每周、每月、once、每天
	Ratio             float32 `json:"ratio"`
	ScaleType         int8    `json:"scaleType"`         // 1 = 并行度 2 = tm cu 数
	PreActionSerialId string  `json:"preActionSerialId"` // 前序 action 的 serial id
	//CurrentActionSerialId   string  `json:"currentActionSerialId"`   // 当前 action 的 serial id
	LastSucceededActionType int8    `json:"lastSucceededActionType"` // 上一次成功的 action 类型
	Index                   []int   `json:"index"`                   // 每周、每月、once、每天
	OnTime                  string  `json:"onTime"`
	DefaultParallelism      int     `json:"defaultParallelism"`
	JmCuSpec                float32 `json:"jmCuSpec"`
	TmCuSpec                float32 `json:"tmCuSpec"`

	JobManagerCpu  float32 `json:"JobManagerCpu"`
	JobManagerMem  float32 `json:"JobManagerMem"`
	TaskManagerCpu float32 `json:"TaskManagerCpu"`
	TaskManagerMem float32 `json:"TaskManagerMem"`

	//OriginDefaultParallelism int     `json:"originDefaultParallelism"`
	//OriginJmCuSpec           float32 `json:"originJmCuSpec"`
	//OriginTmCuSpec           float32 `json:"originTmCuSpec"`
	TargetValue float64 `json:"targetValue"`
}

type AutoScaleRuleConfig struct {
	NewAutoScaleRule                     int    `json:"newAutoScaleRule,omitempty"`
	AutoScaleStrategy                    int    `json:"autoScaleStrategy,omitempty"`
	CooldownTimeMinutes                  string `json:"cooldown.time.minutes,omitempty"`
	MemScaleMax                          string `json:"mem.scale.max,omitempty"`
	CpuScaleMax                          string `json:"cpu.scale.max,omitempty"`
	ResourcesCpuMax                      string `json:"resources.cpu.max,omitempty"`
	ResourcesMemoryMax                   string `json:"resources.memory.max,omitempty"`
	ResourcesParallelismMax              string `json:"parallelism.scale.max,omitempty"`
	ResourcesParallelismMin              string `json:"parallelism.scale.min,omitempty"`
	ParallelismScaleUpInterval           string `json:"parallelism.scale-up.interval,omitempty"`
	ParallelismScaleDownInterval         string `json:"parallelism.scale-down.interval,omitempty"`
	SlotUsageScaleUpInterval             string `json:"slot-usage-detector.scale-up.sample-interval,omitempty"`
	SlotUsageScaleUpThreshold            string `json:"slot-usage-detector.scale-up.threshold,omitempty"`
	SlotUsageScaleDownThreshold          string `json:"slot-usage-detector.scale-down.threshold,omitempty"`
	TmMemoryUsageScaleDownSampleInterval string `json:"mem.scale-down.interval,omitempty"`
	TmMemoryUsageScaleDownThreshold      string `json:"tm-memory-usage.scale-down.threshold,omitempty"`
	TmMemoryUsageScaleUpSampleInterval   string `json:"mem.scale-up.interval,omitempty"`
	TmMemoryUsageScaleUpThreshold        string `json:"tm-memory-usage.scale-up.threshold,omitempty"`
	ResourceMaxDelay                     string `json:"delay-detector.scale-up.threshold,omitempty"`
	SingleResourcesMemoryMax             string `json:"resources.memory-scale-up.max,omitempty"`
	TmCpuUsageScaleDownThreshold         string `json:"tm-cpu-usage.scale-down.threshold,omitempty"`
	TmCpuUsageScaleUpThreshold           string `json:"tm-cpu-usage.scale-up.threshold,omitempty"`
	InLongExtraScale                     string `json:"inlong.extra.scale,omitempty"`
	InLongExtraScaleDownThreshold        string `json:"inlong.extra.scale-down.threshold,omitempty"`
	InPlaceTime                          string `json:"inPlaceTime,omitempty"`
}

type ruleTime struct {
	ruleTime time.Time
	index    []int
	timeunit string
}

type timeLine struct {
	time     int64
	timeUnit string
}
