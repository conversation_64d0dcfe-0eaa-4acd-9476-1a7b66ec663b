package job_autoscale

import (
	"encoding/json"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service2 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_autoscale"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job_config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config"
)

func QueryJobScaleSuggestion(req *job_autoscale.DescribeJobScaleSuggestionReq) (*table.JobScaleSuggestion, error) {
	sql := "SELECT * FROM JobScaleSuggestion  "
	cond := dao.NewCondition()
	cond.Eq("JobId", req.JobId)
	if req.AppId != 0 {
		cond.Eq("AppId", req.AppId)
	}
	cond.Ne("Status", constants.SCALE_SUGGESTION_STATUS_DELETE)
	where, args := cond.GetWhere()
	sql += where
	txManager := service2.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("%s Exception occurs when query JobScaleSuggest from db, with errors:%+v", req.RequestId, err)
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	if len(data) > 1 {
		logger.Errorf("%s Only one suggestion is allowed for job: %s", req.RequestId, req.JobId)
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode_UnexpectedRecordNums, "", err)
	}
	if len(data) == 0 {
		return nil, nil
	}
	suggestion := &table.JobScaleSuggestion{}
	err = util.ScanMapIntoStruct(suggestion, data[0])
	if err != nil {
		logger.Errorf("%s Failed to convert bytes into table.JobScaleSuggestion, with errors:%+v", req.RequestId, err)
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode_UnMarshalFailed, "", err)
	}
	return suggestion, nil
}

func ApplySuggestionMsg(suggestion *table.JobScaleSuggestion) (string, error) {
	mapping, err := GetSuggestionMapping()
	if err != nil {
		return "", err
	}

	ruleItem := &job_autoscale.RuleItem{}
	err = json.Unmarshal([]byte(suggestion.RuleItem), ruleItem)
	if err != nil {
		logger.Errorf("Failed to Unmarshal errorMapping because %+v. ", err)
		return "", err
	}

	fullMsg := ""
	for _, v := range strings.Split(suggestion.MsgCodeList, ",") {
		tmp, ok := mapping[v]
		if !ok {
			logger.Errorf("Unknown suggestion msg code %s,%+v", v, suggestion)
		}
		msg := config.MustRenderConfigFile(tmp, ruleItem)
		fullMsg += msg + "\n"
	}
	return fullMsg, nil
}

func DoDescribeJobScaleSuggestion(req *job_autoscale.DescribeJobScaleSuggestionReq) (*job_autoscale.DescribeJobScaleSuggestionRsp, error) {
	startTime := util.GetNowTimestamp()
	defer func() {
		logger.Infof("%s: DoDescribeJobScaleSuggestion use %d", req.RequestId, util.GetNowTimestamp()-startTime)
	}()
	suggestion, err := QueryJobScaleSuggestion(req)
	if err != nil {
		return nil, err
	}
	msg, err := ApplySuggestionMsg(suggestion)
	if err != nil {
		return nil, err
	}
	rsp := &job_autoscale.DescribeJobScaleSuggestionRsp{}
	rsp.JobId = suggestion.JobId
	rsp.JobConfigVersion = suggestion.JobConfigVersion
	rsp.RuleItem = suggestion.RuleItem
	rsp.Suggestion = msg
	return rsp, nil
}

func CreateScaleSuggestion(reqId string, suggestion *table.JobScaleSuggestion) (err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, reqId+"CreateScaleSuggestion error!")
	txManager := service2.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		args := make([]interface{}, 0, 0)
		args = append(args, constants.SCALE_SUGGESTION_STATUS_DELETE)
		args = append(args, suggestion.JobId)
		args = append(args, constants.SCALE_SUGGESTION_STATUS_OK)
		tx.ExecuteSql("UPDATE JobScaleSuggestion SET Status = ? WHERE JobId = ? AND Status = ?", args)
		tx.SaveObject(suggestion, "JobScaleSuggestion")
		return nil
	}).Close()
	return nil

}
