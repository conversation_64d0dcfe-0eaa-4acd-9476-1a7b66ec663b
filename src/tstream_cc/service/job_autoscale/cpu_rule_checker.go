package job_autoscale

import (
	"fmt"
	"math"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_autoscale"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job"
)

type CpuRuleTester struct {
}

func pagingQuery(taskManagerList []interface{}, task *ScalingTask, startTime int64, endTime int64) ([]*model.JobContainerMetric, error) {
	metrics := make([]*model.JobContainerMetric, 0)
	ids := make([]string, 0)
	for _, tm := range taskManagerList {
		tmId := tm.(string)
		ids = append(ids, tmId)
		if len(ids) == 20 {
			rsp, err := queryMetrics(task, startTime, endTime, ids)
			if err != nil {
				return nil, err
			}
			for _, m := range rsp.JobTaskManagerMetrics {
				metrics = append(metrics, m)
			}
			ids = make([]string, 0)
		}
	}
	if len(ids) != 0 {
		rsp, err := queryMetrics(task, startTime, endTime, ids)
		if err != nil {
			return nil, err
		}
		for _, m := range rsp.JobTaskManagerMetrics {
			metrics = append(metrics, m)
		}
	}
	return metrics, nil
}

func (this *CpuRuleTester) TestIfNeedScale(task *ScalingTask, context *AutoScaleContext) (bool, string, error) {
	endTime := (util.GetNowTimestamp() - int64(context.Config.DelaySeconds)*1000) / 1000
	startTime := endTime - int64(task.Rule.DurationTime)*60

	context.TesterStartTime = util.TimestampToLocalTime(startTime)
	context.TesterEndTime = util.TimestampToLocalTime(endTime)

	logger.Debugf("Time query span %s -> %s", context.TesterStartTime, context.TesterEndTime)

	//1. 先获取关联的TaskManager
	rsp, err := queryMetrics(task, startTime, endTime, nil)
	if err != nil {
		return false, err.Error(), err
	}
	if len(rsp.TaskManagerList) != int(task.DefaultParallelism) {
		return false, fmt.Sprintf("requestId:%s,job:%s taskManagerList and parallelism do not match,should be %d but %d", task.RequestId, task.Rule.JobId, int(task.DefaultParallelism), len(rsp.TaskManagerList)), nil
	}
	//2. 分页多次获取
	metrics, err := pagingQuery(rsp.TaskManagerList, task, startTime, endTime)
	if err != nil {
		return false, fmt.Sprintf("rule %+v query metric data error:%+v", task.Rule, err), err
	}
	if len(metrics) != int(task.DefaultParallelism) {
		return false, fmt.Sprintf("requestId:%s,job:%s taskManagerList and parallelism do not match,should be %d but %d", task.RequestId, task.Rule.JobId, int(task.DefaultParallelism), len(metrics)), nil
	}
	//3. 判断是否应该扩容或是缩容
	ratio := math.Max(math.Ceil(float64(len(metrics))*float64(task.Rule.ConditionRatio)/100.0), 1)
	cnt := 0
	for _, metric := range metrics {
		if checkSingleTM(task.Rule, metric.ContainerMetrics) {
			cnt += 1
		}
	}
	if cnt != 0 && cnt >= int(ratio) {
		return true, controller.OK, nil
	}
	return false, fmt.Sprintf("%s Rule %+v Test failed, tm number need %d but %d", task.RequestId, task.Rule, int(ratio), cnt), nil
}

func queryMetrics(task *ScalingTask, startTime int64, endTime int64, taskManagers []string) (*model.JobTaskManagerMetricRsp, error) {
	req := &model.JobTaskManagerMetricReq{
		JobId: task.Rule.JobId,
		RequestBase: apiv3.RequestBase{
			AppId:         int64(task.Job.AppId),
			RequestId:     task.RequestId,
			Uin:           task.Job.CreatorUin,
			Region:        task.Job.Region,
			SubAccountUin: task.Job.OwnerUin,
		},
		StartTimeStamp:    startTime,
		EndTimeStamp:      endTime,
		Interval:          60,
		ExcludePodMetrics: false,
		TaskManagers:      taskManagers,
	}
	logger.Debugf("JobTaskManagerMetricReq: %+v", req)
	status, msg, rsp := service.DoDescribeJobTaskManagerMetric(req)
	if status != controller.OK {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("requestId:%s,job:%s stop scale because get metric error:%s", task.RequestId, task.Rule.JobId, msg), nil)
	}
	return rsp, nil
}

func checkSingleTM(rule *job_autoscale.RuleSetItem, items []*model.JobContainerMetricSingleItem) bool {
	if len(items) <= 0 {
		return false
	}
	pass := true
	logger.Debugf("Metric data: %+v,Rule: %+v", items, rule)
	switch rule.RuleName {
	case constants.CPU_INCREASE_RULE:
		threshold := float64(rule.Threshold) / 100.0
		for _, item := range items {
			if item.CpuLoad < threshold {
				pass = false
			}
		}
	case constants.CPU_DECREASE_RULE:
		threshold := float64(rule.Threshold) / 100.0
		for _, item := range items {
			if item.CpuLoad > threshold {
				pass = false
			}
		}
	}
	return pass
}
