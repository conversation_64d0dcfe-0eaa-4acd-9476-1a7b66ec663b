package job_autoscale

import (
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"strconv"
	"strings"
	commandc "tencentcloud.com/tstream_galileo/src/common/command"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cluster_admin_protocol"
	table1 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	table8 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/password"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/watchdog"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/quota"
	service5 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
	"time"

	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/notify"
	"tencentcloud.com/tstream_galileo/src/common/notify/tof"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service2 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/barad"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_autoscale"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/auto_tuning"
	table4 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/auto_tuning"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job_config"
	table3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job_instance"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	alert "tencentcloud.com/tstream_galileo/src/tstream_cc/service/barad"
	service4 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job"
)

type RuleTester interface {
	TestIfNeedScale(task *ScalingTask, context *AutoScaleContext) (bool, string, error)
}

var ruleTesterMap map[string]RuleTester

const (
	DX_FLINK_NAME_SPACE = ".default"
	K8S_SUFFIX          = ".svc.cluster.local"
)

func RegistRuleTester(ruleName string, checker RuleTester) {
	_, ok := ruleTesterMap[ruleName]
	if !ok {
		ruleTesterMap[ruleName] = checker
	}
}

func init() {
	ruleTesterMap = map[string]RuleTester{}
	RegistRuleTester(constants.SCALE_RULE_NAME, &CpuRuleTester{})
}

type JobInfo struct {
	Id                   int64
	SerialId             string
	AppId                int32
	OwnerUin             string
	CreatorUin           string
	Region               string
	Zone                 string
	Status               int8
	Type                 int8
	ClusterGroupId       int64
	TmRunningCuNum       int16
	JmRunningCuNum       int8
	PublishedJobConfigId int64
	StartTime            string
	LastScaleTime        string
	FlinkVersion         string
	SpaceId              string
	CuMem                int8
}

type ClusterInfo struct {
	SerialId string
	Status   int8 // 集群状态，1：创建中，2：运行中
	AppId    int32
	Region   string
	CuNum    int16
	PayMode  int // 0 后付费,1 预付费
}

type ScalingTask struct {
	AppId        int64
	Rule         *job_autoscale.RuleSetItem
	Action       *auto_tuning.JobTuningAction
	ActionDetail *auto_tuning.ActionDetail
	Job          *JobInfo
	ClusterGroup *ClusterInfo
	JobConfigId  int64
	VersionId    int16
	PlanName     []byte
	// 资源
	DefaultParallelism         int16
	TmCuSpec                   float32
	JmCuSpec                   float32
	StepSize                   int
	TargetTmCUSpec             float32
	TargetJmCUSpec             float32
	TargetParallelism          int
	SupportFineGrainedResource bool
	TargetRunningCu            float32
	RunningCu                  float32
	// 扩缩容任务状态
	Status         string
	RequestId      string
	SavepointId    string
	EventErrorCode string
	StartTime      string
	EndTime        string
	Cg             *table1.ClusterGroup
	Cluster        *table1.Cluster

	TmCPU            float32
	JmCPU            float32
	TmMem            float32
	JmMem            float32
	TargetTmCPU      float32
	TargetJmCPU      float32
	TargetTmMem      float32
	TargetJmMem      float32
	UsedMaxCPU       float32
	UsedMaxMem       float32
	UsedCPU          float32
	UsedMem          float32
	FreeCPU          float32
	FreeMem          float32
	TargetRunningCPU float32
	TargetRunningMem float32
	RunningCPU       float32
	RunningMem       float32
	Slot             int

	ScaleMode        string
	WebUiUrlPrefix   string
	FlinkJobId       string
	FlinkUsername    string
	FlinkPassword    string
	FlinkRequestId   string
	ScaleGranularity string
	JobGraph         string
	TargetJobGraph   string

	WebUiUrlSuffix string
}

func NewScalingTask(action *table4.JobTuningAction, planName string, appId int64) *ScalingTask {
	return &ScalingTask{
		AppId:     appId,
		Action:    action,
		PlanName:  []byte(planName),
		StartTime: util.GetCurrentTime(),
	}
}

//func NewScalingTask(rule *job_autoscale.RuleSetItem) *ScalingTask {
//	return &ScalingTask{
//		Rule:      rule,
//		StartTime: util.GetCurrentTime(),
//	}
//}

// 检查入口
func (this *ScalingTask) DoCheckLists(context *AutoScaleContext) bool {
	funs := []func(context *AutoScaleContext) (bool, string, error){
		// 获取 jobinfo jobconfiginfo clusterinfo
		this.checkResources,
		/// 白名单
		//this.checkWhiteList,
		// 任务对应的 flow 状态，如果 flow 处于运行中，则不进行处理
		this.checkIsRunning,
		// 检测作业运行时间，是否满足异常状态的持续时间 + 状态检测的延迟时间 5 min
		this.checkDurationTime,
		// action 是否可以执行
		this.checkIsActionValid,
		// 检测是否有足够的资源扩缩容、是否已经达到上下限
		this.checkIfCUIsSufficient,
		// 检测 cpu 是否满足扩容条件
		//this.checkIfScale,
		// 检查使用哪种扩容方式
		this.CheckScaleType,
	}
	for index, f := range funs {
		logger.Infof("AutoScale - Begin to do scale pre-check for action %s with function index: %d", this.Action.JobSerialId, index)
		r, msg, err := f(context)
		if err != nil || !r {
			logger.Warningf("AutoScale - Do scale pre-check failed and skip following check! Cause by: %+v, error msg: %s. Action detail: %+v", err, msg, this.Action)
			return false
		}
	}
	this.SetJobStatus(constants.SCALE_JOB_STATUS_READY)
	logger.Warningf("AutoScaleJob: check succeeded! Scale task detail: %+v", this)
	return true
}

/*
*
检查job的相关资源是否正常
*/
func (this *ScalingTask) checkResources(context *AutoScaleContext) (bool, string, error) {
	job, err := maybeGetJob(this.Action.JobSerialId, []int64{int64(constants.JOB_STATUS_RUNNING)})
	if err != nil {
		logger.Errorf("AutoScale - Fail to get Job by ID:%s, err:%+v", this.Action.JobSerialId, err)
		return false, controller.ResourceNotFound_JobId, err
	}
	spaceId, err := auth.ConverItemSpaceSerialId(job.ItemSpaceId)
	if err != nil {
		logger.Errorf("AutoScale - Fail to converItemSpaceSerialId by ID:%s, err:%+v", job.ItemSpaceId, err)
		return false, controller.InternalError, err
	}
	this.Job = &JobInfo{
		Id:                   job.Id,
		SerialId:             job.SerialId,
		AppId:                job.AppId,
		OwnerUin:             job.OwnerUin,
		CreatorUin:           job.CreatorUin,
		Region:               job.Region,
		Zone:                 job.Zone,
		Status:               job.Status,
		Type:                 job.Type,
		ClusterGroupId:       job.ClusterGroupId,
		TmRunningCuNum:       job.TmRunningCuNum,
		JmRunningCuNum:       job.JmRunningCuNum,
		PublishedJobConfigId: job.PublishedJobConfigId,
		LastScaleTime:        job.LastScaleTime,
		StartTime:            job.StartTime,
		SpaceId:              spaceId,
		CuMem:                job.CuMem,
		FlinkVersion:         job.FlinkVersion,
	}
	context.Region = job.Region
	//// 暂时只支持SQL任务
	//if job.Type != constants.JOB_TYPE_SQL {
	//	return false, fmt.Sprintf("Job %s type should be sql!", this.Action.JobSerialId), nil
	//}
	clusterGroup, err := service4.GetClusterGroupByClusterId(job.ClusterId)
	if err != nil {
		return false, fmt.Sprintf("Fail to get Cluster group by %s!", this.Action.JobSerialId), err
	}
	if clusterGroup.Status != constants.CLUSTER_GROUP_STATUS_RUNNING {
		return false, fmt.Sprintf("Job %s Must in a running cluster!", this.Action.JobSerialId), nil
	}
	this.Cg = clusterGroup

	// 获取集群 paymode 信息
	billingResource, err := service4.GetBillingResourceBySerialId(clusterGroup.SerialId)
	if err != nil {
		return false, fmt.Sprintf("Fail to get Cluster billingResource by cluster id %s for job %s!", clusterGroup.SerialId, this.Action.JobSerialId), err
	}

	this.ClusterGroup = &ClusterInfo{
		SerialId: clusterGroup.SerialId,
		Status:   clusterGroup.Status,
		Region:   clusterGroup.Region,
		CuNum:    clusterGroup.CuNum,
		AppId:    clusterGroup.AppId,
		PayMode:  billingResource.PayMode,
	}

	cluster, err := service4.GetClusterByClusterId(job.ClusterId)
	if err != nil {
		return false, fmt.Sprintf("Fail to get Cluster by cluster id %d for job %s", job.ClusterId, this.Action.JobSerialId), err
	}
	this.Cluster = cluster

	if this.SupportFineGrainedResource, err = service4.SupportFineGrainedResource(cluster); err != nil {
		return false, fmt.Sprintf("Failed to get Cluster SupportFineGrainedResource feature by cluster id %d", job.ClusterId), err
	}

	jobInstance, err := service.GetJobInstanceByJobInstanceId(this.Job.Id, this.Action.JobInstanceId)
	if err != nil {
		return false, fmt.Sprintf("Failed to find job instance %d for Job %s", this.Action.JobInstanceId, this.Action.JobSerialId), nil
	}
	// todo 按理说什么type都可以
	ableVertexScaleType := []string{constants.SCALE_ACTION_TYPE_TASK_PARALLELISM_SCALE_UP, constants.SCALE_ACTION_TYPE_TASK_PARALLELISM_SCALE_DOWN, constants.SCALE_ACTION_TYPE_TASK_FLEXIBLE_TYPE}
	if this.Job.Type == constants.JOB_TYPE_JAR && service.ContainsString(ableVertexScaleType, this.Action.ActionType) && auth.IsInWhiteList(this.AppId, constants.WHITE_LIST_SCALE_VERTEX_PARALLELISM) {
		logger.Debugf("AutoScale - skip Parallelism check")
		this.ScaleGranularity = constants.SCALEVERTEX
		this.JobGraph = jobInstance.FlinkJobPlan
	} else {
		// 调优调整并行度 检测 jobInstance TmRunningCuNum 和 DefaultParallelism 值 是否一致，不一致 表示并行度不以parallelism.default 为准，调优不生效
		if jobInstance.TmRunningCuNum != jobInstance.DefaultParallelism {
			this.EventErrorCode = constants.SCALE_EVENT_TYPE_OTHER_LEVELS_OF_PARALLELISM_HAVE_BEEN_SET
			logger.Errorf("###checkResources, job instance %d for Job %s, action:%s, TmRunningCuNum: %d and DefaultParallelism:%d are not inconsistent ", this.Action.JobInstanceId, this.Action.JobSerialId, this.Action.ActionType, jobInstance.TmRunningCuNum, jobInstance.DefaultParallelism)
			return false, fmt.Sprintf("job instance %d for Job %s,  TmRunningCuNum: %d and DefaultParallelism:%d are not inconsistent ", this.Action.JobInstanceId, this.Action.JobSerialId, jobInstance.TmRunningCuNum, jobInstance.DefaultParallelism), nil
		}
	}
	jobConfig, err := service2.GetJobConfigById(jobInstance.JobConfigId)
	if err != nil {
		logger.Errorf("Fail to get jobConfig by ID:%d for job %s, job instance %d", jobInstance.JobConfigId, this.Action.JobSerialId, jobInstance.Id)
		return false, controller.InternalError, err
	}
	slots := service3.GetNumberOfTaskSlots(jobConfig)
	this.Slot = slots
	this.DefaultParallelism = jobConfig.DefaultParallelism
	this.VersionId = jobConfig.VersionId
	this.TmCuSpec = jobConfig.TmCuSpec
	this.JmCuSpec = jobConfig.JmCuSpec
	this.JobConfigId = jobConfig.Id

	service.CheckJobConfigCPUAndMem(jobConfig, job.CuMem)
	this.TmCPU = jobConfig.TaskManagerCpu
	this.JmCPU = jobConfig.JobManagerCpu
	this.TmMem = jobConfig.TaskManagerMem
	this.JmMem = jobConfig.JobManagerMem

	//url, err := service3.GetJobWebUIURLPrefix(job, cluster, this.ClusterGroup.SerialId)
	//logger.Debugf("AutoScale - webui url:%s", url)
	//this.WebUiUrlPrefix = url
	//
	//url = this.WebUiUrlPrefix

	//getJobsIdUrl := url + "jobs/overview"
	//rsp, err, authInfo := getJobFromFlinkUi(getJobsIdUrl, this)
	//if err != nil {
	//	logger.Errorf("AutoScale - get job info from flink ui err : %s, action :%s", err, this.Action.SerialId)
	//	return false, controller.InternalError, err
	//}
	//info := &Jobs{}
	//err = json.Unmarshal(rsp, &info)
	//if err != nil {
	//	logger.Errorf("AutoScale - InPlace - unmarshal jobid failed for Job %s, err: %s", this.Job.SerialId, err)
	//	return false, controller.InternalError, err
	//}
	//// TODO
	//if len(info.Jobs) != 1 {
	//	logger.Errorf("AutoScale - rsp err count %d", info.Jobs)
	//	return false, controller.InternalError, err
	//}
	authInfo, err := service5.GetTableService().ListFlinkUiAuthInfo(this.Cluster.ClusterGroupId)
	if err != nil {
		logger.Errorf("AutoScale - InPlace - get flink authInfo failed for Job %s, err: %s", this.Job.SerialId, err)
	}
	err = authInfo.DecodePassword()
	if err != nil {
		logger.Errorf("AutoScale - InPlace - decode authInfo failed for Job %s, err: %s", this.Job.SerialId, err)
		return false, controller.InternalError, err
	}
	this.FlinkJobId = jobInstance.FlinkJobId
	this.FlinkUsername = authInfo.User
	this.FlinkPassword = authInfo.Password

	return true, controller.OK, nil
}

/*
*
检查任务启动时间是否满足检测间隔
*/
func (this *ScalingTask) checkDurationTime(context *AutoScaleContext) (bool, string, error) {
	startTime, err := time.ParseInLocation("2006-01-02 15:04:05", this.Job.StartTime, time.Local)
	if err != nil {
		logger.Errorf("Job %s startTime invalid: start time: %s", this.Action.JobSerialId, this.Job.StartTime)
		return false, controller.InternalError, err
	}

	// 需要有足够的退避时间
	if this.Job.LastScaleTime != "0000-00-00 00:00:00" {
		interval := util.SubDate(this.Job.LastScaleTime, util.GetCurrentTime())
		backoffTime := int64(context.Config.BackoffTime)
		if interval < backoffTime {
			this.EventErrorCode = constants.SCALE_EVENT_TYPE_COLD_DOWN_TIME_UNMATCHED
			return false, fmt.Sprintf("The scale interval between the current time and the last scaled time is %d"+
				", needs to be greater than backoffTime %d! JobSerialId %s", interval, backoffTime, this.Action.JobSerialId), nil
		}
	}

	//延迟秒数（默认为 10 min）= 需要的间隔
	need := int64(context.Config.DelaySeconds)
	now := time.Now().Unix() - startTime.Unix()
	if now > need {
		return true, controller.OK, nil
	} else {
		this.EventErrorCode = constants.SCALE_EVENT_TYPE_COLD_DOWN_TIME_UNMATCHED
		return false, fmt.Sprintf("The running time of job %s is not enough, needs %d now %d", this.Action.JobSerialId, need, now), nil
	}
}

/**
必须要任务ID 或者 集群ID在白名单内，
*/
//func (this *ScalingTask) checkWhiteList(context *AutoScaleContext) (bool, string, error) {
//	if util.InStrArray(this.Action.JobSerialId, context.Config.WhiteList) {
//		return true, controller.OK, nil
//	}
//	if util.InStrArray(this.ClusterGroup.SerialId, context.Config.WhiteList) {
//		return true, controller.OK, nil
//	}
//	return false, fmt.Sprintf("%s %s Cluster ID or job ID %s is not in the whitelist", context.RequestId, this.Action.JobSerialId, this.ClusterGroup.SerialId), nil
//}

/*
*
flow 不能处于 running 状态
*/
func (this *ScalingTask) checkIsRunning(context *AutoScaleContext) (bool, string, error) {
	docId := constants.SCALE_FLOW_DOC_ID + "_" + this.Action.JobSerialId
	flows, err := flow.GetFlowByStrDocId(docId)
	if err != nil {
		return false, fmt.Sprintf("Fail to get flow by rule: %+v", this.Action), err
	}
	if len(flows) > 0 {
		this.EventErrorCode = constants.SCALE_FLOW_RUNNING
		return false, fmt.Sprintf("Action %+v have running flow!", this.Action), nil
	}
	return true, "", nil
}

//func (this *ScalingTask) getJobClusterJobs(context *AutoScaleContext) ([]*table.Job, error) {
//	var err error
//	job, err := maybeGetJob(this.Action.JobSerialId, []int64{})
//	if err != nil {
//		logger.Errorf("Fail to get job %s", job.SerialId)
//		return nil, err
//	}
//	cls, err := service4.ListClusterGroupById(job.ClusterGroupId)
//	if err != nil{
//		logger.Errorf("Fail to get cluster %s", job.ClusterId)
//		return nil, err
//	}
//	listJobQuery := model.ListJobQuery{
//		IsVagueNames:    false,
//		Offset:          0,
//		Limit:           0,
//		ClusterGroupIds: []string{cls.SerialId},
//	}
//	jobs, err := service.ListJobs(&listJobQuery)
//	return jobs, nil
//}

func (this *ScalingTask) checkIsActionValid(context *AutoScaleContext) (bool, string, error) {
	var err error
	if this.ActionDetail, err = this.getActionDetail(this.Action.ActionDetail); err != nil {
		return false, fmt.Sprintf("Action failed to get detail! Action detail: %+v ", this.Action), err
	}
	if this.ActionDetail.Type != this.Action.ActionType {
		return false, fmt.Sprintf("Action type and action detail type is not same! Action detail: %+v ", this.Action), nil
	}

	actionTypeList := []string{constants.SCALE_ACTION_TYPE_TASK_PARALLELISM_SCALE_UP, constants.SCALE_ACTION_TYPE_TASK_PARALLELISM_SCALE_DOWN,
		constants.SCALE_ACTION_TYPE_TASK_RESOURCE_SCALE_UP, constants.SCALE_ACTION_TYPE_TASK_RESOURCE_SCALE_DOWN, constants.SCALE_ACTION_TYPE_TASK_FLEXIBLE_TYPE}
	if !util.InStrArray(this.Action.ActionType, actionTypeList) {
		return false, fmt.Sprintf("Action type not in allowed list! Action detail: %+v ", this.Action), nil
	}
	actionType := strings.Split(this.Action.ActionType, constants.SCALE_ACTION_SEPERATOR)[0]
	//scheduleType := strings.Split(actionDetail.Type, constants.SCALE_ACTION_SEPERATOR)[1]
	if actionType == constants.SCALE_ACTION_TYPE_TASK_RESOURCE {
		if this.ActionDetail.Component != constants.SCALE_ACTION_COMPONENT_TASK_MANAGER && this.ActionDetail.Component != constants.SCALE_ACTION_COMPONENT_JOB_MANAGER {
			return false, fmt.Sprintf("Action type is %s, but no wrong component is set to %s! Action detail: %+v ",
				constants.SCALE_ACTION_TYPE_TASK_RESOURCE, this.ActionDetail.Component, this.Action), nil
		}
	}
	return true, controller.OK, nil
}

/*
*
预先检查是否有足够的资源扩缩容、是否已经达到上下限
*/
func (this *ScalingTask) checkIfCUIsSufficient(context *AutoScaleContext) (bool, string, error) {
	var err error
	if this.ActionDetail, err = this.getActionDetail(this.Action.ActionDetail); err != nil {
		return false, fmt.Sprintf("AutoScale - Action failed to get detail! Action detail: %+v ", this.Action), nil
	}

	if this.TargetTmCPU, this.TargetTmMem, this.TargetJmCPU, this.TargetJmMem, this.TargetParallelism, err = this.getTargetResource(this.ActionDetail); err != nil {
		return false, fmt.Sprintf("Action failed to get resource! Action detail: %+v", this.Action), err
	}
	// 兼容老的规格扩缩容（等前端更新后就不会显示spec了，但是老前端的rule都是1:x的规格，需要在这里兼容。
	this.TargetTmCUSpec = service.GetCuNumFromCpuMem(this.TargetTmCPU, this.TargetTmMem, this.Job.CuMem)
	this.TargetJmCUSpec = service.GetCuNumFromCpuMem(this.TargetJmCPU, this.TargetJmMem, this.Job.CuMem)
	// 细粒度资源 cu 数可选 0.25 0.5 1 2
	if this.TargetTmCPU < 0.25 {
		this.EventErrorCode = constants.SCALE_EVENT_TYPE_EXCEED_TM_CPU_LOWER_LIMIT
		return false, fmt.Sprintf("Action %s has wrong target cpu ! TargetTmCPU: %f,  failed to get target resource for job %s!",
			this.Action.SerialId, this.TargetTmCPU, this.Action.SerialId), nil
	}
	if this.TargetTmMem < 1 {
		this.EventErrorCode = constants.SCALE_EVENT_TYPE_EXCEED_TM_MEM_LOWER_LIMIT
		return false, fmt.Sprintf("Action %s has wrong target cpu ! TargetTmMem: %f,  failed to get target resource for job %s!",
			this.Action.SerialId, this.TargetTmMem, this.Action.SerialId), nil
	}
	if this.TargetJmCPU < 0.25 {
		this.EventErrorCode = constants.SCALE_EVENT_TYPE_EXCEED_JM_CPU_LOWER_LIMIT
		return false, fmt.Sprintf("Action %s has wrong target cpu ! TargetJmCPU: %f,  failed to get target resource for job %s!",
			this.Action.SerialId, this.TargetJmCPU, this.Action.SerialId), nil
	}
	if this.TargetJmMem < 1 {
		this.EventErrorCode = constants.SCALE_EVENT_TYPE_EXCEED_JM_MEM_LOWER_LIMIT
		return false, fmt.Sprintf("Action %s has wrong target cpu ! TargetJmMem: %f,  failed to get target resource for job %s!",
			this.Action.SerialId, this.TargetJmMem, this.Action.SerialId), nil
	}
	//if this.TargetTmCUSpec == 0.25 || this.TargetJmCUSpec == 0.25 {
	//	if !auth.IsInWhiteList(int64(this.Job.AppId), constants.WHITE_LIST_OPEN_FINE_GRAINED) {
	//		this.EventErrorCode = constants.SCALE_EVENT_TYPE_EXCEED_TM_CU_LOWER_LIMIT
	//		return false, fmt.Sprintf("App doesn't support 0.25 cu config! "+
	//			"Action %s has wrong target cu specification! TargetTmCUSpec: %f, TargetJmCUSpec: %f, failed to get target resource for job %s!",
	//			this.Action.SerialId, this.TargetTmCUSpec, this.TargetJmCUSpec, this.Action.SerialId), nil
	//	}
	//}

	//if this.TargetTmCUSpec > 2 || this.TargetJmCUSpec > 2 {
	//	// 如果是常规 pod 规格
	//	if !auth.IsInWhiteList(int64(this.Job.AppId), constants.WHITE_LIST_OPEN_NON_STANDARD_POD_SPEC) {
	//		this.EventErrorCode = constants.SCALE_EVENT_TYPE_EXCEED_TM_CU_UPPER_LIMIT
	//		logger.Errorf("###NON_STANDARD_POD_SPEC, TargetTmCUSpec: %f, TargetJmCUSpec: %f, failed to get target resource for job %s! ", this.TargetTmCUSpec, this.TargetJmCUSpec, this.Action.SerialId)
	//		return false, fmt.Sprintf("Action %s has wrong target cu specification! TargetTmCUSpec: %f, TargetJmCUSpec: %f, failed to get target resource for job %s!",
	//			this.Action.SerialId, this.TargetTmCUSpec, this.TargetJmCUSpec, this.Action.SerialId), nil
	//	}
	//}
	//s, _ := json.Marshal(graph)
	//baseOldGraph := base64.StdEncoding.EncodeToString(s)
	//if err != nil {
	//	logger.Errorf("AutoScale - encode failed,err %s", err)
	//	task.ChangeToFail(constants.SCALE_EVENT_TYPE_RESTART_JOB_FAILED)
	//	task.SaveStateToDB()
	//}

	if this.TargetParallelism < 1 {
		this.EventErrorCode = constants.SCALE_EVENT_TYPE_EXCEED_LOWER_LIMIT
		return false, fmt.Sprintf("Action get wrong resource! TargetTmCUSpec: %f, TargetJmCUSpec: %f, TargetParallelism: %d, Action detail: %+v",
			this.TargetTmCUSpec, this.TargetJmCUSpec, this.TargetParallelism, this.Action), nil
	}

	// specifically for scale down of parallelism, it can't be lower when default parallelism is 1
	if this.Action.ActionType == constants.SCALE_ACTION_TYPE_TASK_PARALLELISM_SCALE_DOWN && this.TargetParallelism == int(this.DefaultParallelism) {
		this.EventErrorCode = constants.SCALE_EVENT_TYPE_EXCEED_LOWER_LIMIT
		logger.Debugf("AutoScale - Action %s has wrong target parallelism! TargetTmCUSpec: %f, TargetJmCUSpec: %f, TargetParallelism: %d, Default parallelism: %d,Action detail: %+v",
			this.Action.SerialId, this.TargetTmCUSpec, this.TargetJmCUSpec, this.TargetParallelism, this.DefaultParallelism, this.Action)
		return false, "Parallelism can't be lower than 1", nil
	}

	// 这里主要是比较定时调优的，自动调优不会因为 source sink并行度重复触发。
	if constants.SCALEVERTEX == this.ScaleGranularity {
		scaleGraph, err := service2.JobVertexScale(this.JobGraph, float64(this.ActionDetail.Ratio), this.TargetParallelism, this.Action.ActionType)
		if err != nil {
			logger.Errorf("AutoScale - scale vertex failed,err %s", err)
			return false, fmt.Sprintf("Action failed to scale job graph for job %s!",
				this.Action.SerialId, this.TargetJmCPU, this.Action.SerialId), nil
		}
		s1, _ := util.ObjectToString(scaleGraph)
		logger.Debugf("AutoScale - %s - scaled graph:%s", this.Job.SerialId, string(s1))
		this.TargetJobGraph = string(s1)
		if service2.CompareJobGraph(this.JobGraph, this.TargetJobGraph) && service.AlmostEqual(this.TargetTmMem, this.TmMem) && service.AlmostEqual(this.TargetTmCPU, this.TmCPU) && service.AlmostEqual(this.TargetJmCPU, this.JmCPU) && service.AlmostEqual(this.TargetJmMem, this.JmMem) {
			this.EventErrorCode = constants.SCALE_VERTEX_SAME_GRAPH
			return false, fmt.Sprintf("AutoScale - Action %s jobgraph %s is same before %s or target resource is the same as before! TargetTmMem: %f, TargetTmCPU: %f,TargetJmCPU: %f, TargetJmMem: %f, TargetParallelism: %d,CurrentTmMem: %f, CurrentTmCPU: %f,CurrentJmCPU: %f, CurrentJmMem: %f, CurrentParallelism: %d",
				this.Action.SerialId, this.TargetJobGraph, this.JobGraph, this.TargetTmMem, this.TargetTmCPU, this.TargetJmCPU, this.TargetJmMem, this.TargetParallelism, this.TmMem, this.TmCPU, this.JmCPU, this.JmMem, this.DefaultParallelism), nil
		}
	} else {
		// 并行度 math.ceil(1 * 0.5) = 1
		if service.AlmostEqual(this.TargetTmMem, this.TmMem) && service.AlmostEqual(this.TargetTmCPU, this.TmCPU) && service.AlmostEqual(this.TargetJmCPU, this.JmCPU) && service.AlmostEqual(this.TargetJmMem, this.JmMem) && int16(this.TargetParallelism) == this.DefaultParallelism {
			return false, fmt.Sprintf("AutoScale - Action target resource is the same as before! TargetTmMem: %f, TargetTmCPU: %f,TargetJmCPU: %f, TargetJmMem: %f, TargetParallelism: %d,CurrentTmMem: %f, CurrentTmCPU: %f,CurrentJmCPU: %f, CurrentJmMem: %f, CurrentParallelism: %d, Action detail: %+v",
				this.TargetTmMem, this.TargetTmCPU, this.TargetJmCPU, this.TargetJmMem, this.TargetParallelism, this.TmMem, this.TmCPU, this.JmCPU, this.JmMem, this.DefaultParallelism, this.Action), nil
		}
	}

	targetCPU := service.GetFloat2Dot(float32(this.TargetParallelism)*this.TargetTmCPU + this.TargetJmCPU)
	targetMem := service.GetFloat2Dot(float32(this.TargetParallelism)*this.TargetTmMem + this.TargetJmMem)
	this.TargetRunningCPU = float32(targetCPU)
	this.TargetRunningMem = float32(targetMem)
	//if targetCU > float32(this.Rule.ReachLimit) {
	//	this.EventErrorCode = constants.SCALE_EVENT_TYPE_EXCEED_TM_CU_UPPER_LIMIT
	//	return false, fmt.Sprintf("Job %s in cluster %s exceeds cu limit! need:%f CU, limit: %f", this.Action.JobSerialId, this.ClusterGroup.SerialId, targetCU, float32(this.Rule.ReachLimit)), nil
	//}
	// 64Cu 转换成64Cu所使用的CPU和Mem
	if this.Rule.RuleName == constants.SCALE_RULES_AUTO_SCALE_TIME_BASED {
		ReachLimitCPU := float32(this.Rule.ReachLimit)
		ReachLimitMem := float32(this.Rule.ReachLimit) * float32(this.Job.CuMem)
		// todo 白名单ReachLimit 的判断

		// 从配额中拿限制
		scaleReachLimit, err := quota.NewQuota().GetQuota("", int64(this.AppId), quota.ScaleReachLimit)
		if err != nil {
			logger.Errorf("AutoScale - Action %s get scaleReachLimit quota failed! Action detail: %+v", this.Action.SerialId, this.Action)
		}
		if err == nil && scaleReachLimit > 0 {
			ReachLimitCPU = float32(scaleReachLimit)
			ReachLimitMem = float32(scaleReachLimit) * float32(this.Job.CuMem)
		}
		if this.TargetRunningCPU > ReachLimitCPU || this.TargetRunningMem > ReachLimitMem {
			this.EventErrorCode = constants.SCALE_EVENT_TYPE_EXCEED_TM_CU_UPPER_LIMIT
			return false, fmt.Sprintf("Job %s in cluster %s exceeds cu limit! need:%f CPU %f Mem, limit: %f %f", this.Action.JobSerialId, this.ClusterGroup.SerialId, this.TargetRunningCPU, this.TargetRunningMem, ReachLimitCPU, ReachLimitMem), nil
		}
	} else {
		// 9.21自动调优的上限在wd中判断
		autoScaleRuleConfig := &AutoScaleRuleConfig{}
		err := json.Unmarshal([]byte(this.Rule.Configuration), &autoScaleRuleConfig)
		if err != nil || autoScaleRuleConfig.NewAutoScaleRule == 0 {
			logger.Infof("AutoScale - Job %s,rule %s use ReachLimit to restrict resource.because: %s or use old wd version .", this.Action.JobSerialId, this.Rule.RuleId, err.Error())
			// 有错误或者是老版本,就按ReachLimit
			ReachLimitCPU := float32(this.Rule.ReachLimit)
			ReachLimitMem := float32(this.Rule.ReachLimit) * float32(this.Job.CuMem)
			if this.TargetRunningCPU > ReachLimitCPU || this.TargetRunningMem > ReachLimitMem {
				this.EventErrorCode = constants.SCALE_EVENT_TYPE_EXCEED_TM_CU_UPPER_LIMIT
				return false, fmt.Sprintf("Job %s in cluster %s exceeds cu limit! need:%f CPU %f Mem, limit: %f %f", this.Action.JobSerialId, this.ClusterGroup.SerialId, this.TargetRunningCPU, this.TargetRunningMem, ReachLimitCPU, ReachLimitMem), nil
			}
		}
		// 如果没有err说明是新版本的，上限由wd判断。
	}

	// 按量计费集群不计算运行 cu
	if this.ClusterGroup.PayMode == 0 {
		logger.Infof("Job %s in cluster %s is eks cluster, skip to check cluster cu!", this.Action.JobSerialId, this.ClusterGroup.SerialId)
		return true, controller.OK, nil
	}

	runningCPU, runningMem, _, err := this.GetJobRunningCPUAndMem()
	if err != nil {
		return false, fmt.Sprintf("Fail to getRunningCU, action: %+v, Job: %+v, instance %d", this.ActionDetail, this.Action.JobSerialId, this.Action.JobInstanceId), err
	}
	this.RunningCPU = runningCPU
	this.RunningMem = runningMem

	logger.Infof("Job %s in cluster %s free:%f CPU %f Mem, need:%f CPU %f Mem, current:%f CPU %f Mem ", this.Action.JobSerialId, this.ClusterGroup.SerialId, this.FreeCPU, this.FreeMem, targetCPU, targetMem, runningCPU, runningMem)
	// 集群资源不足
	if this.TargetRunningCPU > this.FreeCPU+runningCPU+constants.FLOAT_TOLERATE || this.TargetRunningMem > this.FreeMem+runningMem+constants.FLOAT_TOLERATE {
		this.EventErrorCode = constants.SCALE_EVENT_TYPE_CU_INSUFFICIENT
		return false, fmt.Sprintf("Job %s in cluster %s do not have sufficient CU to scale up, need:%f CPU %f Mem, current:%f CPU %f Mem , free:%f CPU %f Mem ", this.Action.JobSerialId, this.ClusterGroup.SerialId, targetCPU, targetMem, runningCPU, runningMem, this.FreeCPU, this.FreeMem), nil
	}
	useCPU := this.TargetRunningCPU - this.RunningCPU
	useMem := this.TargetRunningMem - this.RunningMem
	// 缩容的资源已经在之前加了，不能再加。
	if useCPU > 0 {
		this.FreeCPU -= useCPU
	}
	if useMem > 0 {
		this.FreeMem -= useMem
	}
	return true, controller.OK, nil

	//需要扩展或是缩减的并行度
	//if this.StepSize, err = this.getStepSize(); err != nil {
	//	return false, fmt.Sprintf("%s Job %+v failed to getStepSize", context.RequestId, this.Rule), nil
	//}
	//logger.Infof("the step size is: %d", this.StepSize)
	////并行度转换成CU，这里默认Jm只有一个 以后可能不一定
	//stepCU := float32(this.StepSize)*this.TmCuSpec + this.JmCuSpec
	//var target int
	//if this.Rule.ScalingType == constants.INCREASE_RULE_KEYWORD {
	//	freeCU, err := this.getFreeCU()
	//	if err != nil {
	//		return false, fmt.Sprintf("Fail to getFreeCU,rule: %+v,cluster: %+v", this.Rule, this.ClusterGroup.SerialId), err
	//	}
	//	if stepCU > freeCU {
	//		return false, fmt.Sprintf("%s Job %s in cluster %s do not have sufficient CU to scale up,need:%f only have:%f ", context.RequestId, this.Rule.JobId, this.ClusterGroup.SerialId, stepCU, freeCU), nil
	//	}
	//	target = int(this.DefaultParallelism) + this.StepSize
	//	if target > this.Rule.ReachLimit {
	//		return false, fmt.Sprintf("%s %s Target parallelism %d exceeded the limit %d", context.RequestId, this.Rule.JobId, target, this.Rule.ReachLimit), nil
	//	}
	//} else if this.Rule.ScalingType == constants.DECREASE_RULE_KEYWORD {
	//	target = int(this.DefaultParallelism) - this.StepSize
	//	if target < this.Rule.ReachLimit || target <= 0 {
	//		return false, fmt.Sprintf("%s %s Target parallelism %d exceeded the limit %d", context.RequestId, this.Rule.JobId, target, this.Rule.ReachLimit), nil
	//	}
	//} else {
	//	return false, fmt.Sprintf("%+v Unknown scaling type:%s", this.Rule, this.Rule.ScalingType), nil
	//}
	//this.TargetParallelism = target
	//return true, controller.OK, nil
}

/*
*
检测是否满足扩缩容规则
已废弃，目前由 watchdog 检测
*/
func (this *ScalingTask) checkIfScale(context *AutoScaleContext) (bool, string, error) {
	ruleTester, ok := ruleTesterMap[this.Rule.RuleName]
	if !ok {
		return false, fmt.Sprintf("Rule %s is unsupported rule : %s", this.Rule.JobId, this.Rule.RuleName), nil
	}
	return ruleTester.TestIfNeedScale(this, context)
}

// -------------- 辅助方法 -----------------------
func checkJobInstanceStatus(jobInstanceId int64) (t *table3.JobInstance, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("AutoScaleJob: GetJobInstance panic ,errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	sql := "SELECT * FROM JobInstance WHERE Id= ? and Status = ?"
	args := make([]interface{}, 0)
	args = append(args, jobInstanceId)
	args = append(args, constants.JOB_INSTANCE_STATUS_RUNNING)

	_, data, err := service2.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Error("AutoScaleJob: Failed to query with sql:", sql, ", errors:", err.Error())
		return &table3.JobInstance{}, err
	}

	if len(data) == 0 {
		logger.Error("AutoScaleJob: checkJobInstanceStatus not find job Instance: ", jobInstanceId)
		return &table3.JobInstance{}, errors.New("not find Job Instance")
	}

	if len(data) > 1 {
		logger.Error("AutoScaleJob: logic error, not only one JobInstance query sql:", sql)
		return &table3.JobInstance{}, errors.New("logic error, not only one JobInstance")
	}

	result := &table3.JobInstance{}
	_ = util.ScanMapIntoStruct(result, data[0])

	return result, nil
}

func maybeGetJob(serialId string, status []int64) (*table.Job, error) {
	listJobQuery := model.ListJobQuery{
		SerialIds:    []string{serialId},
		IsVagueNames: false,
		Offset:       0,
		Limit:        0,
		Status:       status,
	}
	jobs, err := service.ListJobs(&listJobQuery)
	if err != nil {
		return nil, err
	}
	if len(jobs) == 0 {
		return nil, errors.New(fmt.Sprintf("No running job found by ID %s", serialId))
	}
	if len(jobs) > 1 {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode_UnexpectedRecordNums, "Logic error , jobs length > 1 "+serialId, nil)
	}
	return jobs[0], nil

}

func (this *ScalingTask) GetJobRunningCPUAndMem() (float32, float32, int16, error) {
	job, err := maybeGetJob(this.Job.SerialId, []int64{int64(constants.JOB_STATUS_RUNNING)})
	if err != nil {
		logger.Errorf("maybeGetJob with SerialId %s failed, err:%+v", this.Job.SerialId, err)
		return 0, 0, 0, err
	}
	//cluster, err := service4.GetClusterByJobId(job.SerialId)
	jc, jm, tc, tm, err := service.GetJobRunningCPUAndMem(job)
	if err != nil {
		logger.Errorf("GetClusterJobRunningCPUAndMem failed, err:%+v", err)
		return 0, 0, 0, err
	}
	runningCPU := service.GetFloat2Dot(jc + tc)
	runningMem := service.GetFloat2Dot(jm + tm)
	return float32(runningCPU), float32(runningMem), job.TmRunningCuNum, nil
}

func (this *ScalingTask) getStepSize() (stepSize int, err error) {
	if strings.HasSuffix(this.Rule.Step, "%") {
		step, err := strconv.Atoi(this.Rule.Step[0 : len(this.Rule.Step)-1])
		if err != nil {
			return -1, err
		}
		stepSize = int(math.Ceil(float64(this.DefaultParallelism) * float64(step) / 100.0))
		if stepSize == 0 {
			stepSize = 1
		}
	} else {
		if step, err := strconv.Atoi(this.Rule.Step); err != nil {
			return -1, err
		} else {
			stepSize = step
		}
	}
	return
}

func (this *ScalingTask) getActionDetail(detail string) (actionDetail *table4.ActionDetail, err error) {
	actionDetail = &table4.ActionDetail{}
	if detail != "" {
		if err = json.Unmarshal([]byte(detail), actionDetail); err != nil {
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "failed to get action detail", err)
		}
	} else {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "Action detail for "+this.Action.SerialId+" is empty!", err)
	}
	return actionDetail, nil
}

func (this *ScalingTask) getTargetResource(actionDetail *table4.ActionDetail) (TargetTmCPU float32, TargetTmMem float32, TargetJmCPU float32, TargetJmMem float32, TargetParallelism int, err error) {
	// task-resource.scale-down
	// 增加灵活定时调优
	actionType := strings.Split(actionDetail.Type, constants.SCALE_ACTION_SEPERATOR)[0]
	//scheduleType := strings.Split(actionDetail.Type, constants.SCALE_ACTION_SEPERATOR)[1]
	if actionType == constants.SCALE_ACTION_TYPE_TASK_PARALLELISM {
		// 四舍五入
		if actionDetail.DefaultParallelism == 0 {
			return this.TmCPU, this.TmMem, this.JmCPU, this.JmMem, int(math.Floor(float64(this.DefaultParallelism)*float64(actionDetail.Ratio) + 0.5)), nil
		}
		// watchdog 其实已经向上取整了。（新wd才有）
		targetParallelism := actionDetail.DefaultParallelism
		return this.TmCPU, this.TmMem, this.JmCPU, this.JmMem, targetParallelism, nil
	} else if actionType == constants.SCALE_ACTION_TYPE_TASK_RESOURCE {
		if !this.SupportFineGrainedResource {
			msg := fmt.Sprintf("Action %s failed cluster %s didn't support FineGrainedResource, can not change jm/tm cu for job %s!", this.Action.SerialId, this.ClusterGroup.SerialId, this.Action.JobSerialId)
			logger.Errorf(msg)
			return this.TmCPU, this.TmMem, this.JmCPU, this.JmMem, int(this.DefaultParallelism), errorcode.NewStackError(errorcode.InternalErrorCode, msg, nil)
		}
		if actionDetail.Component == constants.SCALE_ACTION_COMPONENT_JOB_MANAGER {
			// 老wd
			if actionDetail.JmCPU == 0 && actionDetail.JmMem == 0 {
				JmCPU := float32(float64(this.JmCPU) * float64(actionDetail.Ratio))
				JmMem := float32(float64(this.JmMem) * float64(actionDetail.Ratio))
				return this.TmCPU, this.TmMem, JmCPU, JmMem, int(this.DefaultParallelism), nil
			}
			JmCPU := actionDetail.JmCPU
			JmMem := actionDetail.JmMem
			return this.TmCPU, this.TmMem, JmCPU, JmMem, int(this.DefaultParallelism), nil
		} else if actionDetail.Component == constants.SCALE_ACTION_COMPONENT_TASK_MANAGER {
			// 老wd
			if actionDetail.TmMem == 0 && actionDetail.TmCPU == 0 {
				TmCPU := float32(float64(this.TmCPU) * float64(actionDetail.Ratio))
				TmMem := float32(float64(this.TmMem) * float64(actionDetail.Ratio))
				return TmCPU, TmMem, this.JmCPU, this.JmMem, int(this.DefaultParallelism), nil
			}
			targetTmMem := this.TmMem
			targetTmCPU := this.TmCPU
			if actionDetail.TmMem != 0 {
				targetTmMem = actionDetail.TmMem
			}
			if actionDetail.TmCPU != 0 {
				targetTmCPU = actionDetail.TmCPU
			}
			return targetTmCPU, targetTmMem, this.JmCPU, this.JmMem, int(this.DefaultParallelism), nil
		} else {
			return 0, 0, 0, 0, 0, errorcode.NewStackError(errorcode.InternalErrorCode, "Action resource for "+this.Action.SerialId+" is empty!", err)
		}
	} else if actionType == constants.SCALE_ACTION_SCHEDULE_TYPE_SCALE_FLEXIBLE {
		// 先转换之后再判断是否支持SupportFineGrainedResource
		if (actionDetail.TmCPU != 1 || actionDetail.TmMem != float32(this.Cluster.MemRatio) || actionDetail.JmCPU != 1 || actionDetail.JmMem != float32(this.Cluster.MemRatio)) && !this.SupportFineGrainedResource {
			msg := fmt.Sprintf("Action %s failed cluster %s didn't support FineGrainedResource, can not change jm/tm cu for job %s!", this.Action.SerialId, this.ClusterGroup.SerialId, this.Action.JobSerialId)
			logger.Errorf(msg)
			return this.TmCPU, this.TmMem, this.JmCPU, this.JmMem, int(this.DefaultParallelism), errorcode.NewStackError(errorcode.InternalErrorCode, msg, nil)
		}
		return actionDetail.TmCPU, actionDetail.TmMem, actionDetail.JmCPU, actionDetail.JmMem, actionDetail.DefaultParallelism, nil
	} else {
		return 0, 0, 0, 0, 0, errorcode.NewStackError(errorcode.InternalErrorCode, "Action detail for "+this.Action.SerialId+" is empty!", err)
	}
}

// 资源缩容，对cu 取0.5 的整数倍
func RoundCu(actionType string, cu float32) float32 {
	if actionType == constants.SCALE_ACTION_TYPE_TASK_RESOURCE_SCALE_DOWN {
		if cu <= constants.FINEGRAINEDRESOURCE_05 {
			return cu
		}
		return float32(math.Round(float64(cu)*2) / 2)
	}
	return cu
}

// -------------------- 流程使用 ------------------------
func (this *ScalingTask) IfStatusValid(status string) bool {
	if this.Status != status {
		logger.Errorf("AutoScaleJob: Logical error,task status is invalid, should %s but %+v", status, this)
		return false
	} else {
		return true
	}
}

func (this *ScalingTask) StartSavepoint(params map[string]string) (error, bool, string) {
	if this == nil || !this.CheckIfContinue(constants.SCALE_JOB_STATUS_READY) {
		return nil, false, ""
	}
	code, msg, rsp := service3.DoTriggerJobSavepoint(&model.TriggerJobSavepointReq{
		RequestBase: apiv3.RequestBase{
			Region:        this.Job.Region,
			AppId:         int64(this.Job.AppId),
			Uin:           this.Job.OwnerUin,
			SubAccountUin: this.Job.CreatorUin,
			RequestId:     this.RequestId,
		},
		JobId:       this.Job.SerialId,
		WorkSpaceId: this.Job.SpaceId,
		Description: "由自动扩缩容程序创建的快照",
	})
	if rsp == nil {
		return errorcode.NewStackError(errorcode.InternalErrorCode, msg, nil), false, msg
	}
	if code != controller.OK {
		logger.Errorf("AutoScale - DoTriggerJobSavepoint failed for job %s, Response: %+v", this.Job.SerialId, rsp)
		if strings.Contains(rsp.ErrorMsg, "more than 1 in progress savepoint found") {
			return nil, true, rsp.ErrorMsg
		}
		return errorcode.NewStackError(errorcode.InternalErrorCode, msg, nil), false, msg
	}
	if rsp.SavepointTrigger == false {
		logger.Errorf("AutoScale - DoTriggerJobSavepoint failed for job %s, Response: %+v", this.Job.SerialId, rsp)
		return errorcode.NewStackError(errorcode.InternalErrorCode, rsp.ErrorMsg, nil), false, rsp.ErrorMsg
	}
	this.SavepointId = rsp.SavepointId
	this.SetJobStatus(constants.SCALE_JOB_STATUS_START_SAVEPOINT)
	this.SaveStateToDB()
	return nil, false, ""
}

func (this *ScalingTask) UpdateJobConfig(parallelism int, jmSpec, tmSpec, jmCPU, jmMem, tmCPU, tmMem float32) (err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("update job config panic, for job:%+v, errors:%+v", this.Job, errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	txManager := service2.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		sql := "UPDATE JobConfig set  JmCuSpec = ?, TmCuSpec = ?, JobManagerCpu = ?, JobManagerMem = ?, TaskManagerCpu = ?, TaskManagerMem = ?"
		args := make([]interface{}, 0)
		args = append(args, jmSpec)
		args = append(args, tmSpec)
		args = append(args, jmCPU)
		args = append(args, jmMem)
		args = append(args, tmCPU)
		args = append(args, tmMem)
		if parallelism != 0 {
			sql += " ,DefaultParallelism=? "
			args = append(args, parallelism)
		}
		sql += " where jobId =? AND Id =? "

		args = append(args, this.Job.Id)
		args = append(args, this.JobConfigId)
		result := tx.ExecuteSql(sql, args)
		_, err = result.RowsAffected()
		if err != nil {
			logger.Errorf("Failed to update JobConfig, cause by: %+v, task: %+v", err, this)
			return err
		}
		return nil
	}).Close()
	return nil
}

func (this *ScalingTask) UpdateJobConfigDefaultParallelism(parallelism int) (count int64, err error) {

	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("update job parallelism  panic ,for job:%+v, errors:%+v", this.Job, errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
			count = 0
		}
	}()

	if parallelism == 0 {
		return 0, errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("parallelism should not be 0,job:%+v", this.Job), nil)
	}

	txManager := service2.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		sql := "UPDATE JobConfig set DefaultParallelism=? where jobId =? AND Id =?"
		args := make([]interface{}, 0)
		args = append(args, parallelism)
		args = append(args, this.Job.Id)
		args = append(args, this.JobConfigId)
		result := tx.ExecuteSql(sql, args)
		count, err = result.RowsAffected()
		if err != nil {
			logger.Errorf("Failed to update DefaultParallelism cause by: %+v,task: %+v", err, this.Action)
			return err
		}
		return nil
	}).Close()

	return
}

func (this *ScalingTask) GetCurrentJobConfig() (config *table2.JobConfig, err error) {
	jobConfig, err := service2.GetJobConfigById(this.JobConfigId)
	if err != nil {
		logger.Errorf("Fail to get jobConfig by ID:%d for job %s", this.JobConfigId, this.Action.JobSerialId)
		return nil, err
	}
	return jobConfig, nil
}

func (this *ScalingTask) SetJobStatus(status string) {
	logger.Infof("%s status changed from %s to %s", this.Action.JobSerialId, this.Status, status)
	this.Status = status
}

func (this *ScalingTask) DoRunJob(runType int8, savepointId string, useJobGraph bool) error {

	jobs := make([]model.RunJobDescription, 0)
	jobs = append(jobs, model.RunJobDescription{
		JobId:            this.Action.JobSerialId,
		JobConfigVersion: int64(this.VersionId),
		RunType:          runType,
		StartMode:        constants.START_MODE_LATEST,
		SavepointId:      savepointId,
		IsScale:          true,
	})
	errCode, msg, _ := service3.DoRunJobs(&model.RunJobsReq{
		AppId:              this.Job.AppId,
		Uin:                this.Job.OwnerUin,
		SubAccountUin:      this.Job.CreatorUin,
		RequestId:          this.RequestId,
		Region:             this.Job.Region,
		RunJobDescriptions: jobs,
		WorkSpaceId:        this.Job.SpaceId,
	}, true)
	if errCode != controller.OK {
		logger.Errorf("AutoScale - Fail to restart job: %s, caused by %s", this.Action.JobSerialId, msg)
		//如果执行出错，恢复并行度
		return errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("AutoScale - restart job failed! Action: %+v", this.Action), nil)
	}
	return nil
}

func (this *ScalingTask) GetJobStatus() (*table.Job, error) {
	//job状态是否是运行中,否则等待
	job, err := maybeGetJob(this.Action.JobSerialId, nil)
	if err != nil {
		logger.Errorf("Fail to get job:%s status", this.Action.JobSerialId)
		return nil, err
	}
	return job, nil
}

func (this *ScalingTask) SendWarningInfo() {
	notifyAlarm := notify.NewNotify()
	notifyAlarm.SendByCustomParamTof4("", "", "", "")
}

func (this *ScalingTask) GetSavepointStatus() (int8, error) {
	savepoint, err := service2.GetSavepointBySerialId(this.SavepointId)
	if err != nil {
		return -1, err
	}
	return savepoint.Status, nil
}

func (this *ScalingTask) GetSavepoint() (*watchdog.SavepointEntity, error) {
	savepoint, err := service2.GetSavepointBySerialId(this.SavepointId)
	if err != nil {
		return nil, err
	}
	return savepoint, nil
}

func (this *ScalingTask) DisableRulesByJobId() {

}

func (this *ScalingTask) ChangeToFail(errCode string) {
	logger.Infof("task is failed with code %s", errCode)
	this.EventErrorCode = errCode
}
func (this *ScalingTask) setCurrentAndTargetResource(context *AutoScaleContext) (err error) {
	if this.ActionDetail, err = this.getActionDetail(this.Action.ActionDetail); err != nil {
		logger.Errorf("AutoScale - SaveScaleEvent failed to get action detail! Action detail: %+v ", this.Action)
		return err
	}
	job, err := maybeGetJob(this.Action.JobSerialId, []int64{int64(constants.JOB_STATUS_RUNNING)})
	if err != nil {
		logger.Errorf("AutoScale - SaveScaleEvent failed to get job! Action detail: %+v ", this.Action)
		return err
	}
	jobInstance, err := service.GetJobInstanceByJobInstanceId(this.Job.Id, this.Action.JobInstanceId)
	if err != nil {
		logger.Errorf("AutoScale - SaveScaleEvent failed to get job instance! Action detail: %+v ", this.Action)
		return err
	}
	jobConfig, err := service2.GetJobConfigById(jobInstance.JobConfigId)
	if err != nil {
		logger.Errorf("AutoScale - SaveScaleEvent failed to get job config! Action detail: %+v ", this.Action)
		return err
	}
	service.CheckJobConfigCPUAndMem(jobConfig, job.CuMem)
	this.TmCPU = jobConfig.TaskManagerCpu
	this.JmCPU = jobConfig.JobManagerCpu
	this.TmMem = jobConfig.TaskManagerMem
	this.JmMem = jobConfig.JobManagerMem
	this.DefaultParallelism = jobConfig.DefaultParallelism
	if this.TargetTmCPU, this.TargetTmMem, this.TargetJmCPU, this.TargetJmMem, this.TargetParallelism, err = this.getTargetResource(this.ActionDetail); err != nil {
		logger.Errorf("AutoScale - SaveScaleEvent failed to get target resource! Action detail: %+v ", this.Action)
		return err
	}
	return nil
}

// 保存 event 更新 action
func (this *ScalingTask) MaybeSaveScaleEvent(context *AutoScaleContext) (result bool, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("AutoScale - SaveScaleEvent panic ,errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
			result = false
		}
	}()
	// 如果task的target为0，需要补全才能显示正确的调优信息
	if this.TargetTmCPU == 0 && this.TargetTmMem == 0 && this.TargetJmCPU == 0 && this.TargetJmMem == 0 && this.TargetParallelism == 0 {
		err := this.setCurrentAndTargetResource(context)
		if err != nil {
			// 有error也不要返回，最多显示的event行为不对
			logger.Errorf("AutoScale - SaveScaleEvent failed to set current and target resource! Action detail: %+v ", this.Action)
		}
	}
	if this.EventErrorCode != "" {
		event := &table2.JobScaleEvent{}
		event.JobId = this.Action.JobSerialId
		event.PlanName = string(this.PlanName)
		event.Status = constants.SCALE_EVENT_STATUS_FAILED
		event.AppId = int64(this.Job.AppId)
		event.ErrorCode = this.EventErrorCode
		event.StartTime = this.StartTime
		event.EndTime = util.GetCurrentTime()
		ruleItem := CreateRuleItem(context, this)
		item, err := json.Marshal(ruleItem)
		if err != nil {
			return false, err
		}
		event.RuleItem = string(item)
		actionConfigItem := CreateScaleActionConfig(context, this)
		if actionConfigItem.RuleName == constants.SCALE_RULES_AUTO_SCALE_TIME_BASED {
			event.ScaleType = int8(constants.SCALE_RULES_AUTO_SCALE_TIME_BASED_TYPE)
		} else {
			event.ScaleType = int8(constants.SCALE_RULES_AUTO_SCALE_BASIC_TYPE)
		}
		actionConfig, err := json.Marshal(actionConfigItem)
		if err != nil {
			return false, err
		}
		event.ScalingActionConfig = string(actionConfig)
		if _, err = CreateScaleEvent(event); err != nil {
			return false, err
		}
		// 最后修改 action
		this.Action.Status = constants.SCALE_ACTION_STATUS_FAILED
		err = UpdateJobTuningAction(this.Action)
		if err != nil {
			logger.Errorf("AutoScaleJob failed to update action %s to status FAILED for job %s", this.Action.SerialId, this.Action.JobSerialId)
			return false, err
		}

		//if this.EventErrorCode == constants.SCALE_EVENT_TYPE_MODIFY_PARALLELISM_FAILED || this.EventErrorCode == constants.SCALE_EVENT_TYPE_RESTART_JOB_FAILED {
		//	ids := []string{this.Rule.RuleId}
		//	if err := ModifyJobScaleRuleStatusByRuleId(context.RequestId, ids, constants.SCALE_RULES_STATUS_INACTIVE, int64(this.Job.AppId)); err != nil {
		//		return true, err
		//	}
		//}

		eventAlertReq := &barad.JobAlertReq{
			JobId:     this.Action.JobSerialId,
			Message:   "Job tuning failed, error: " + this.EventErrorCode,
			EventName: constants.BARAD_EVENT_ALERT_JOB_SCALE_FAIL, // 七彩石，新增调优失败事件
			Status:    constants.BARAD_EVENT_ALERT_ON,
			Type:      constants.EVENT_ENTITY_TYPE_JOB_EXCEPTIONS,
		}
		retCode, retMsg, err := alert.ProcessEventAlert(eventAlertReq)
		if err != nil {
			logger.Errorf("AutoScaleJob failed to process event alert for job %s, error: %+v", this.Action.JobSerialId, err)
			return false, err
		}
		if retCode != 0 {
			logger.Errorf("AutoScaleJob failed to process event alert for job %s, error: %+v", this.Action.JobSerialId, retMsg)
			return false, errors.New(retMsg)
		}

		return true, nil
	} else if this.CheckIfContinue(constants.SCALE_JOB_STATUS_FINISH_RESTART_COMMAND) || this.CheckIfContinue(constants.SCALE_JOB_STATUS_FINISH_INPLACE_COMMAND) {
		event := &table2.JobScaleEvent{}
		event.JobId = this.Action.JobSerialId
		event.PlanName = string(this.PlanName)
		event.Status = constants.SCALE_EVENT_STATUS_SUCCESS
		event.AppId = int64(this.Job.AppId)
		event.StartTime = this.StartTime
		event.EndTime = util.GetCurrentTime()
		ruleItem := CreateRuleItem(context, this)
		item, err := json.Marshal(ruleItem)
		if err != nil {
			return false, err
		}
		event.RuleItem = string(item)
		actionConfigItem := CreateScaleActionConfig(context, this)
		if actionConfigItem.RuleName == constants.SCALE_RULES_AUTO_SCALE_TIME_BASED {
			event.ScaleType = int8(constants.SCALE_RULES_AUTO_SCALE_TIME_BASED_TYPE)
		} else {
			event.ScaleType = int8(constants.SCALE_RULES_AUTO_SCALE_BASIC_TYPE)
		}
		actionConfig, err := json.Marshal(actionConfigItem)
		if err != nil {
			return false, err
		}
		event.ScalingActionConfig = string(actionConfig)
		if _, err = CreateScaleEvent(event); err != nil {
			return false, err
		}
		// 兼容老rule转换，老rule转换必须要有preActionSerialId以及preActionSaleType
		scaleType := constants.SCALE_ACTION_TYPE_SCALE_DOWN
		if actionConfigItem.TargetParallelism > actionConfigItem.CurrentParallelism || actionConfigItem.TargetTmCUSpec > actionConfigItem.CurrentTmCPU {
			scaleType = constants.SCALE_ACTION_TYPE_SCALE_UP
		}
		err = updateRulePreActionSerialId(ruleItem.RuleId, this.Action.SerialId, int8(scaleType))
		if err != nil {
			logger.Errorf("AutoScaleJob failed to update action %s to status SUCCESS for job %s", this.Action.SerialId, this.Action.JobSerialId)
			return false, err
		}
		// 最后修改 action
		this.Action.Status = constants.SCALE_ACTION_STATUS_SUCCESS
		err = UpdateJobTuningAction(this.Action)
		if err != nil {
			logger.Errorf("AutoScaleJob failed to update action %s to status SUCCESS for job %s", this.Action.SerialId, this.Action.JobSerialId)
			return false, err
		}

		return true, nil
	} else {
		// 这个地方是因为线上2个机器会出现这个东西，出现ready状态
		logger.Infof("%s AutoScale - unknown status %s", this.Job.SerialId, this.Status)
		return true, nil
	}

}

func (this *ScalingTask) GetRealParallelism() (int, error) {
	i, err := this.GetRunningJobInstanceBySerialId(this.Action.JobSerialId)
	if err != nil {
		return 0, err
	}
	return int(i.DefaultParallelism), nil
}

func (this *ScalingTask) GetRunningJobInstanceBySerialId(serialId string) (t *table3.JobInstance, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("GetJobInstance panic ,errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()

	sql := "SELECT JobInstance.* FROM JobInstance, Job WHERE " +
		"JobInstance.JobId = Job.Id AND Job.Status = ? AND JobInstance.Status = ? AND Job.SerialId=?"
	args := make([]interface{}, 0)
	args = append(args, constants.JOB_STATUS_RUNNING)
	args = append(args, constants.JOB_INSTANCE_STATUS_RUNNING)
	args = append(args, serialId)

	_, data, err := service2.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Error("Failed to query with sql:", sql, ", errors:", err.Error())
		return &table3.JobInstance{}, err
	}

	if len(data) == 0 {
		logger.Error("not find job Instance: ", serialId)
		return &table3.JobInstance{}, errors.New("not find Job Instance")
	}

	if len(data) > 1 {
		logger.Error("logic error, not only one JobInstance query sql:", sql)
		return &table3.JobInstance{}, errors.New("logic error, not only one JobInstance")
	}

	result := &table3.JobInstance{}
	_ = util.ScanMapIntoStruct(result, data[0])

	return result, nil
}

func (this *ScalingTask) UpdateLastScaleTime() (err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("update job lastScaleTime  panic ,for task:%+v, errors:%+v", this.Action, errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	txManager := service2.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		sql := "UPDATE Job set LastScaleTime=? where SerialId =? AND AppId =?"
		args := make([]interface{}, 0)
		args = append(args, util.GetCurrentTime())
		args = append(args, this.Job.SerialId)
		args = append(args, this.Job.AppId)
		result := tx.ExecuteSql(sql, args)
		count, err := result.RowsAffected()
		if err != nil {
			logger.Errorf("Fail to update LastScaleTime because: %+v,task:%+v", err, this.Action)
			return err
		}
		if count != 1 {
			logger.Errorf("update LastScaleTime RowsAffected should be 1 but %d,task:%+v", count, this.Action)
			return errors.New("update LastScaleTime RowsAffected should be 1 but " + strconv.FormatInt(count, 10) + "for job " + this.Job.SerialId)
		}
		return nil
	}).Close()
	return
}

func (this *ScalingTask) Alarm() {
	alarmMessage := "以下任务在自动扩缩容时重启失败: \n\n"
	alarmMessage += fmt.Sprintf("ID: %s, Region: %s, ClusterGroup: %s , AppID: %d, Status: %d, JobType: %d, VersionId: %d, LastSavepoint: %s, Rule: %s, RequestId: %s, Rule: %+v \n",
		this.Job.SerialId, this.Job.Region, this.ClusterGroup.SerialId, this.Job.AppId, this.Job.Status, this.Job.Type, this.VersionId, this.SavepointId, this.Action.ActionType, this.RequestId, this.Action)
	eventMsg := map[string]interface{}{
		"jobSerialId":          this.Job.SerialId,
		"clusterGroupSerialId": this.ClusterGroup.SerialId,
		"status":               this.Job.Status,
		"type":                 this.Job.Type,
		"versionId":            this.VersionId,
		"savepointId":          this.SavepointId,
		"actionType":           this.Action.ActionType,
		"requestId":            this.RequestId,
		"rule":                 this.Action,
	}
	msg := tof.TOFMessage{
		AppId:        int64(this.Job.AppId),
		Region:       this.Job.Region,
		EventName:    "Galileo 自动扩缩容 任务重启失败告警",
		Time:         time.Now().Format("2006-01-02 15:04:05"),
		EventMessage: eventMsg,
	}
	notifyInstance := notify.NewNotify()
	notifyInstance.SyncSendMessageKafka(msg)

	eventAlertReq := &barad.JobAlertReq{
		JobId:     this.Action.JobSerialId,
		Message:   "Job tuning warning: " + constants.SCALE_EVENT_TYPE_RESTART_JOB_TIMEOUT,
		EventName: constants.BARAD_EVENT_ALERT_JOB_SCALE_TIMEOUT, // 七彩石，新增调优失败事件
		Status:    constants.BARAD_EVENT_ALERT_ON,
		Type:      constants.EVENT_ENTITY_TYPE_JOB_EXCEPTIONS,
	}
	retCode, retMsg, err := alert.ProcessEventAlert(eventAlertReq)
	if err != nil {
		logger.Errorf("AutoScaleJob failed to process event alert for job %s, error: %+v", this.Action.JobSerialId, err)
	}
	if retCode != 0 {
		logger.Errorf("AutoScaleJob failed to process event alert for job %s, error: %+v", this.Action.JobSerialId, retMsg)
	}
}

func (this *ScalingTask) AlarmConfigModifyFailed() {
	alarmMessage := "以下任务在原地扩缩容时修改作业配置或者作业实例资源失败: \n\n"
	alarmMessage += fmt.Sprintf("ID: %s, Region: %s, ClusterGroup: %s , AppID: %d, Status: %d, JobType: %d, VersionId: %d, LastSavepoint: %s, Rule: %s, RequestId: %s, Rule: %+v \n",
		this.Job.SerialId, this.Job.Region, this.ClusterGroup.SerialId, this.Job.AppId, this.Job.Status, this.Job.Type, this.VersionId, this.SavepointId, this.Action.ActionType, this.RequestId, this.Action)
	eventMsg := map[string]interface{}{
		"jobSerialId":          this.Job.SerialId,
		"clusterGroupSerialId": this.ClusterGroup.SerialId,
		"status":               this.Job.Status,
		"type":                 this.Job.Type,
		"versionId":            this.VersionId,
		"savepointId":          this.SavepointId,
		"actionType":           this.Action.ActionType,
		"requestId":            this.RequestId,
		"rule":                 this.Action,
	}
	msg := tof.TOFMessage{
		AppId:        int64(this.Job.AppId),
		Region:       this.Job.Region,
		EventName:    "Galileo 自动扩缩容 原地扩缩容失败告警",
		Time:         time.Now().Format("2006-01-02 15:04:05"),
		EventMessage: eventMsg,
	}
	notifyInstance := notify.NewNotify()
	notifyInstance.SyncSendMessageKafka(msg)

	eventAlertReq := &barad.JobAlertReq{
		JobId:     this.Action.JobSerialId,
		Message:   "Job tuning warning: " + constants.SCALE_EVENT_TYPE_INPLACE_MODIFY_CONFIG_FAILED,
		EventName: constants.BARAD_EVENT_ALERT_JOB_SCALE_TIMEOUT, // 七彩石，新增调优失败事件
		Status:    constants.BARAD_EVENT_ALERT_ON,
		Type:      constants.EVENT_ENTITY_TYPE_JOB_EXCEPTIONS,
	}
	retCode, retMsg, err := alert.ProcessEventAlert(eventAlertReq)
	if err != nil {
		logger.Errorf("AutoScaleJob failed to process event alert for job %s, error: %+v", this.Action.JobSerialId, err)
	}
	if retCode != 0 {
		logger.Errorf("AutoScaleJob failed to process event alert for job %s, error: %+v", this.Action.JobSerialId, retMsg)
	}
}

type RuntimeState struct {
	SavepointId    string
	TaskStatus     string
	TaskErrCode    string
	FlinkRequestId string
}

func (this *ScalingTask) SaveStateToDB() (err error) {
	state := &RuntimeState{}
	state.SavepointId = this.SavepointId
	state.TaskStatus = this.Status
	state.TaskErrCode = this.EventErrorCode
	state.FlinkRequestId = this.FlinkRequestId

	data, _ := json.Marshal(state)

	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("AutoScale - update job lastScaleTime  panic, for task:%+v, errors:%+v", this.Action, errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	txManager := service2.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		sql := "UPDATE JobTuningAction set Properties=? where SerialId =?"
		args := make([]interface{}, 0)
		args = append(args, data)
		args = append(args, this.Action.SerialId)
		result := tx.ExecuteSql(sql, args)
		count, err := result.RowsAffected()
		if err != nil {
			logger.Errorf("AutoScale - Fail to update Properties because: %+v, task:%+v", err, this.Action)
			return err
		}
		if count != 1 {
			logger.Errorf("AutoScale - update JobTuningAction RowsAffected should be 1 but %d, task:%+v", count, this.Action)
			return errorcode.NewStackError(errorcode.InternalErrorCode, "", nil)
		}
		return nil
	}).Close()
	return nil
}

func (this *ScalingTask) GetStateFromDB() error {

	sql := "SELECT * FROM JobTuningAction  "
	cond := dao.NewCondition()
	cond.Eq("SerialId", this.Action.SerialId)
	cond.NIn("Status", []int8{constants.SCALE_ACTION_STATUS_IGNORED, constants.SCALE_ACTION_STATUS_UNSATISFIED})
	where, args := cond.GetWhere()
	sql += where

	txManager := service2.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("%s Exception occurs when query JobTuningAction from db, action id %s, job id %s with errors:%+v", this.RequestId, this.Action.SerialId, this.Action.JobSerialId, err)
		return errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	if len(data) < 1 {
		logger.Errorf("JobScaleRule with %s should be %d but %d", this.Action.SerialId, 1, len(data))
		return errorcode.InternalErrorCode.NewWithInfo("", nil)
	}
	action := &table4.JobTuningAction{}
	err = util.ScanMapIntoStruct(action, data[0])
	if err != nil {
		logger.Errorf("%s Failed to convert bytes into table.JobTuningAction action id %s, job id %s, with errors:%+v", this.RequestId, this.Action.SerialId, this.Action.JobSerialId, err)
		return errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	if action.Properties != "" {
		state := &RuntimeState{}
		if err = json.Unmarshal([]byte(action.Properties), state); err != nil {
			logger.Errorf("%s Failed to convert properties into map for action id %s, job id %s, with errors:%+v", this.RequestId, this.Action.SerialId, this.Action.JobSerialId, err)
			return errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
		logger.Debugf("AutoScale - InPlace -action properties:%s", action.Properties)
		this.SavepointId = state.SavepointId
		this.Status = state.TaskStatus
		this.EventErrorCode = state.TaskErrCode
		this.FlinkRequestId = state.FlinkRequestId
	}
	return nil
}

func (this *ScalingTask) CheckIfContinue(need string) bool {
	if this.Status != need {
		logger.Warningf("AutoScale - task status is invalid, Need %s but %s, Job %s, Action %s", need, this.Status, this.Job.SerialId, this.Action.SerialId)
		return false
	}
	if this.EventErrorCode != "" {
		logger.Warningf("AutoScale - error code is not nil but %s for Job %s, Action %s", this.EventErrorCode, this.Job.SerialId, this.Action.SerialId)
		return false
	}
	return true
}

// 查出ErrorCode，和成功以及该ErrorCode的的Event比较目标资源，相同则代表已经记录事件，不需要再次记录。
// true 代表continue，false代表需要保存事件
func (this *ScalingTask) preCheckRepeatErrCode() (bool, error) {
	// 智能调优的action不走这个判断
	if this.Action.ActionType != constants.SCALE_ACTION_TYPE_TASK_FLEXIBLE_TYPE {
		return false, nil
	}
	tx := service2.GetTxManager()
	sql := "SELECT * FROM JobScaleEvent WHERE JobId=? AND (ErrorCode = ? OR Status = 1) AND StartTime >= NOW() - INTERVAL " + constants.DELAY_TIME_EX + " MINUTE"
	args := make([]interface{}, 0)
	args = append(args, this.Action.JobSerialId)
	args = append(args, this.EventErrorCode)
	cnt, data, err := tx.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("AutoScale - Error occurs when get JobScaleEvent from db, with errors:%+v", err)
		return false, err
	}
	if cnt == 0 {
		return false, nil
	}

	detail, err := this.getActionDetail(this.Action.ActionDetail)
	if err != nil {
		logger.Errorf("AutoScale - Error occurs when get ActionDetail from db, with errors:%+v", err)
		return false, err
	}
	for i := 0; i < len(data); i++ {
		tableScaleEvent := &table2.JobScaleEvent{}
		err = util.ScanMapIntoStruct(tableScaleEvent, data[i])
		if err != nil {
			logger.Errorf("AutoScale - A Failed to convert bytes into table.JobScaleEvent, with errors:%+v", err)
			return false, err
		}
		actionConfig := &job_autoscale.ScalingActionConfig{}
		if tableScaleEvent.ScalingActionConfig != "" {
			err = json.Unmarshal([]byte(tableScaleEvent.ScalingActionConfig), actionConfig)
			if err != nil {
				logger.Errorf("AutoScale - Fail to parse ScalingActionConfig of scaleEvent  %+v", tableScaleEvent)
				continue
			}
		} else {
			logger.Debugf("AutoScale - %s ScalingActionConfig is empty", this.Action.SerialId)
		}
		actionDetail, err := this.getActionDetail(actionConfig.ActionDetail)
		if err != nil {
			logger.Errorf("AutoScale - A Failed to convert bytes into table.JobScaleEvent, with errors:%+v", err)
			return false, err
		}
		// 老rule在创建action的地方没有cpu、mem
		if actionDetail.TmCPU == 0 && actionDetail.JmCPU == 0 && actionDetail.JmMem == 0 && actionDetail.TmMem == 0 {
			// 证明是相同action
			if detail.TmCuSpec == actionDetail.TmCuSpec && detail.JmCuSpec == actionDetail.JmCuSpec && detail.DefaultParallelism == actionDetail.DefaultParallelism {
				logger.Debugf("AutoScale - %s ScalingActionConfig is same as last one,ErrorCode:%s, continue", this.Action.SerialId, this.EventErrorCode)
				return true, nil
			}
		} else {
			if service.AlmostEqual(detail.TmMem, actionDetail.TmMem) && service.AlmostEqual(detail.JmMem, actionDetail.JmMem) && service.AlmostEqual(detail.TmCPU, actionDetail.TmCPU) && service.AlmostEqual(detail.JmCPU, actionDetail.JmCPU) && detail.DefaultParallelism == actionDetail.DefaultParallelism {
				logger.Debugf("AutoScale - %s ScalingActionConfig is same as last one,ErrorCode:%s,  continue", this.Action.SerialId, this.EventErrorCode)
				return true, nil
			}
		}

	}
	return false, nil
}

func updateRulePreActionSerialId(ruleSerialId, actionSerialId string, scaleType int8) error {

	rule, err := QueryJobScaleRulesById("", 0, "", ruleSerialId)
	if err != nil {
		logger.Errorf("AutoScaleTimeBased - Failed to get JobScaleRule from db, with errors:%+v", err)
		return err
	}

	// 自动调优不需要这个
	if rule.RuleName == constants.SCALE_RULES_AUTO_SCALE_BASIC {
		return nil
	}

	newScaleConfig := &ScaleConfig{}
	err = json.Unmarshal([]byte(rule.Configuration), newScaleConfig)
	if err != nil {
		logger.Errorf("AutoScaleTimeBased: Failed to Unmarshal Configuration for rule %s because %+v. ", ruleSerialId, err)
		return err
	}
	// 加入这些参数
	newScaleConfig.PreActionSerialId = actionSerialId
	newScaleConfig.LastSucceededActionType = scaleType
	newScaleConfigStr, err := json.Marshal(newScaleConfig)
	if err != nil {
		logger.Errorf("AutoScaleTimeBased - Failed to marshal newScaleConfig because %+v. ", err)
		return err
	}
	configuration := string(newScaleConfigStr)
	txm := service2.GetTxManager()
	txm.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		sql := "UPDATE JobScaleRule SET Configuration = ? WHERE SerialId = ?"
		args := make([]interface{}, 0)
		args = append(args, configuration)
		args = append(args, ruleSerialId)
		tx.ExecuteSql(sql, args)
		return nil
	}).Close()
	return nil
}

func (this *ScalingTask) CheckScaleType(context *AutoScaleContext) (bool, string, error) {
	// 目前只有1.16支持
	enable, err := service4.SupportInPlaceScale(this.Cluster)
	if err != nil {
		logger.Errorf("AutoScale - failed to get cluster features %s", err)
		return false, "", err
	}
	if this.Job.FlinkVersion != constants.FLINK_VERSION_1_16 || !enable {
		this.ScaleMode = constants.RestartScale
		return true, controller.OK, nil
	}
	disable, err := this.CheckIsDisableInPlaceScale()
	if err != nil {
		logger.Warningf("AutoScale - job %s is temporary ban in place scale", this.Job.SerialId)
		return false, "", err
	}
	if this.TargetTmCPU != this.TmCPU || this.TargetTmMem != this.TmMem || this.TargetJmCPU != this.JmCPU || this.TargetJmMem != this.JmMem {
		// 只有单独调整并行度的时候采用原地扩缩容
		logger.Debugf("AutoScale - job %s is not in place scale,"+
			"because TargetTmCPU %f != TmCPU %f or TargetTmMem %f != TmMem %f or TargetJmCPU %f != JmCPU %f or TargetJmMem %f != JmMem %f",
			this.Job.SerialId, this.TargetTmCPU, this.TmCPU, this.TargetTmMem, this.TmMem, this.TargetJmCPU, this.JmCPU, this.TargetJmMem, this.JmMem)
		this.ScaleMode = constants.RestartScale
		return true, controller.OK, nil
	}
	if disable {
		this.ScaleMode = constants.RestartScale
	} else {
		this.ScaleMode = constants.InPlaceScale
	}
	return true, controller.OK, nil
}

func (this *ScalingTask) DisableInPlaceScale() {
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf("AutoScale - disable cluster panic: %+v", err)
		}
	}()
	now := time.Now()
	txm := service2.GetTxManager()
	args := make([]interface{}, 0)
	args = append(args, this.Job.SerialId)
	endTime := now.Add(24 * time.Hour)
	format := endTime.Format(constants.TIME_SAMPLE_MINUTE)
	args = append(args, format)
	sql := "insert into TemporarilyDisableInPlaceScale (JobSerialId,EndTime) VALUES (?,?) ON DUPLICATE KEY UPDATE EndTime=VALUES(EndTime)"
	txm.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		result := tx.ExecuteSql(sql, args)
		fmt.Println(result)
		return nil
	}).Close()
}

func (this *ScalingTask) CheckIsDisableInPlaceScale() (bool, error) {
	now := time.Now()
	txm := service2.GetTxManager()
	sql := "SELECT EndTime FROM TemporarilyDisableInPlaceScale where JobSerialId=? AND EndTime >= ?"
	args := make([]interface{}, 0)
	args = append(args, this.Job.SerialId)
	format := now.Format(constants.TIME_SAMPLE_MINUTE)
	args = append(args, format)
	cnt, _, err := txm.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return false, err
	}
	if cnt == 0 {
		return false, nil
	}
	return true, nil
}
func getJobFromFlinkUi(getJobsIdUrl string, task *ScalingTask) ([]byte, error, *table8.FlinkUiAuthInfo) {
	authInfo, err := service5.GetTableService().ListFlinkUiAuthInfo(task.Cluster.ClusterGroupId)
	if err != nil {
		logger.Errorf("AutoScale - InPlace - get flink authInfo failed for Job %s, err: %s", task.Job.SerialId, err)
	}
	err = authInfo.DecodePassword()
	if err != nil {
		logger.Errorf("AutoScale - InPlace - decode authInfo failed for Job %s, err: %s", task.Job.SerialId, err)
		return nil, err, nil
	}
	rsp, err := service.SendHttpRequest(getJobsIdUrl, "GET", "", nil, authInfo.User, authInfo.Password)
	if err != nil {
		logger.Errorf("AutoScale - InPlace - doInPlaceScalingHandler get job id from flink url %s failed for Job %s, err: %s", getJobsIdUrl, task.Job.SerialId, err)
		return nil, err, nil
	}
	return rsp, nil, authInfo
}

type Job struct {
	Jid       string `json:"jid"` // 直接匹配 JSON 字段名
	Name      string `json:"name"`
	State     string `json:"state"`
	StartTime int64  `json:"start-time"`
	EndTime   int64  `json:"end-time"`
	Duration  int64  `json:"duration"`
	LastMod   int64  `json:"last-modification"`
	Tasks     Tasks  `json:"tasks"`
}

// Tasks 结构体映射 tasks 字段
type Tasks struct {
	Total        int `json:"total"`
	Created      int `json:"created"`
	Scheduled    int `json:"scheduled"`
	Deploying    int `json:"deploying"`
	Running      int `json:"running"`
	Finished     int `json:"finished"`
	Canceling    int `json:"canceling"`
	Canceled     int `json:"canceled"`
	Failed       int `json:"failed"`
	Reconciling  int `json:"reconciling"`
	Initializing int `json:"initializing"`
}

// Jobs 结构体映射根节点
type Jobs struct {
	Jobs []Job `json:"jobs"`
}

type InPlaceScaleParam struct {
	Parallelism        int    `json:"parallelism,omitempty"`
	SerializedJobGraph string `json:"serialized-jobgraph,omitempty"`
}

func (this *ScalingTask) CrdQuery(body string) (res string, err error) {

	logger.Info("===ClusterId")
	logger.Info(this.Cluster.Id)

	clusterGroup, err := service4.GetClusterGroupByClusterId(this.Cluster.Id)
	if err != nil {
		logger.Errorf("%d Failed to GetClusterGroupByClusterId ,err:%s", this.Cluster.Id, err.Error())
		return "", err
	}
	jobInStance, err := this.GetRunningJobInstanceBySerialId(this.Job.SerialId)
	if err != nil {
		return "", err
	}
	sendData, url, err := ConvertRequestSSToCS(body, jobInStance.ApplicationId, this.WebUiUrlSuffix, this.RequestId, this.Cluster.Id, clusterGroup)
	if err != nil {
		return "", err
	}

	commandService, err := commandc.NewCommandService(this.Cluster.Id)
	if err != nil {
		return "", err
	}
	ssRsp, err := commandService.DoRequest(&commandc.CommandRequest{
		ReqId:    this.RequestId,
		Url:      url,
		SendData: sendData,
		Uin:      this.Job.CreatorUin,
		Apikey:   "FlinkInPlaceScale",
		Timeout:  60,
		Callback: "",
	})
	logger.Info("===")
	logger.Info(ssRsp)

	response, err := ConvertResponseSSToCS(ssRsp, this.RequestId, clusterGroup)
	if err != nil {
		logger.Errorf("Error call flink server %v", err)
		return "", errors.New("Error call flink server. ")
	}

	return response, nil
}

func ConvertRequestSSToCS(ssReq string, applicationId string, urlPath string, requestId string, clusterId int64, clusterGroup *table1.ClusterGroup) (string, string, error) {
	logger.Debugf("%s AutoScale - InPlace - #### urlPath: %s ,req:%s", requestId, urlPath, string(ssReq))

	if clusterGroup.AgentSerialId == "" {
		flinkServiceName := applicationId + "-rest" + DX_FLINK_NAME_SPACE + K8S_SUFFIX
		url := fmt.Sprintf("http://%s:%s%s", flinkServiceName, strconv.Itoa(constants.FlinkPort), urlPath)
		logger.Debugf("%s AutoScale - InPlace - #### url: %s ", requestId, url)
		return ssReq, url, nil
	} else {
		// 直接在这里确定命名空间，cs只做转发，不做处理。
		flinkServiceName := applicationId + "-rest" + "." + clusterGroup.SerialId + K8S_SUFFIX
		url := fmt.Sprintf("http://%s:%s%s", flinkServiceName, strconv.Itoa(constants.FlinkPort), urlPath)
		logger.Debugf("%s AutoScale - InPlace - #### url: %s ", requestId, url)
		return ssReq, url, nil
	}
}

func ConvertResponseSSToCS(ssResponse string, requestId string, clusterGroup *table1.ClusterGroup) (string, error) {
	logger.Debugf("%s ##### ConvertResponseSSToCS ssResponse %s ", requestId, ssResponse)
	//if clusterGroup.AgentSerialId == "" {
	return ssResponse, nil
}

type FlinkServerCSRsp struct {
	cluster_admin_protocol.QaeResponse
	Data struct {
		cluster_admin_protocol.ClusterAdminResponse
		Params struct {
			CommonResponse string `json:"commonResponse"`
		} `json:"params"`
	} `json:"data"`
}

type FlinkServerCSPara struct {
	Params          Params `json:"params"`
	ClusterId       int64  `json:"clusterId"`
	FlinkServerName string `json:"flinkServerName"`
	FlinkServerPath string `json:"flinkServerPath"`
	ClusterSerialId string `json:"clusterSerialId"`
}
type Params struct {
	CommonRequest string `json:"commonRequest"`
	RequestId     string `json:"requestId"`
}

//type FlinkServerReq struct {
//	*SqlServerBaseReq
//	Interface struct {
//		InterfaceName string `json:"interfaceName"`
//		Para          struct {
//			FlinkClientVersion string      `json:"flinkClientVersion"`
//			Cam                interface{} `json:"cam"`
//			ResoureceLocs      interface{} `json:"resoureceLocs"`
//			HiveVersion        string      `json:"hiveVersion"`
//			CatalogName        string      `json:"catalogName"`
//			DefaultDatabase    string      `json:"defaultDatabase"`
//			HiveConfDir        string      `json:"hiveConfDir"`
//			Hostname           string      `json:"hostname"`
//			Port               int         `json:"port"`
//			Username           string      `json:"username"`
//			Password           string      `json:"password"`
//		} `json:"para"`
//	} `json:"interface"`
//}
