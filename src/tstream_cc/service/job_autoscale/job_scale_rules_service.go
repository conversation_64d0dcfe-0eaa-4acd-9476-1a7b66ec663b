package job_autoscale

import (
	"errors"
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service2 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_autoscale"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job_config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
)

func CreateJobScaleEntityFromReq(req *job_autoscale.CreateJobScaleRuleReq) *table.JobScaleRule {
	item := &table.JobScaleRule{}
	item.JobId = req.JobId
	item.Region = req.Region
	item.RuleName = req.RuleName
	// deprecation fields
	item.ConditionRatio = req.ConditionRatio
	item.Threshold = req.Threshold
	item.DurationTime = req.DurationTime
	item.Step = req.Step
	item.ReachLimit = req.ReachLimit
	item.Status = constants.SCALE_RULES_STATUS_ACTIVE
	item.Properties = req.Properties
	if req.Configuration != "" {
		item.Configuration = req.Configuration
	} else {
		// 从七彩石中加载默认规则
		item.Configuration = GetDefaultConfiguration(req.RuleName)
	}
	item.AppId = req.AppId
	item.CreatorUin = req.SubAccountUin
	item.OwnerUin = req.Uin
	item.CreateTime = util.GetCurrentTime()
	item.UpdateTime = util.GetCurrentTime()
	return item
}

func DoCreateJobScaleRule(req *job_autoscale.CreateJobScaleRuleReq) (*job_autoscale.CreateJobScaleRuleRsp, error) {
	// 鉴权
	_, err := auth.InnerAuthJob(req.WorkSpaceId, req.IsSupOwner, req.JobId, req.AppId, req.SubAccountUin, req.Region, req.Action)
	if err != nil {
		logger.Errorf("%s: User  uin %s is NOT authenticated, refuse to serve", req.RequestId, req.Uin)
		return nil, err
	}

	job, err := service.WhetherJobExists(int32(req.AppId), req.Region, req.JobId)
	if err != nil {
		return nil, err
	}
	rule := CreateJobScaleEntityFromReq(req)
	logger.Infof("CreateJobScaleEntityFromReq %+v", rule)
	rule.Zone = job.Zone
	id, err := CreateScaleRule(req.RequestId, rule)
	if err != nil {
		return nil, err
	}
	return &job_autoscale.CreateJobScaleRuleRsp{RuleId: id}, nil
}

func QueryJobScaleRules(jobId string, appId int64, status int8, requestId string) ([]*table.JobScaleRule, error) {
	sql := "SELECT * FROM JobScaleRule  "
	cond := dao.NewCondition()
	if jobId != "" {
		cond.Eq("JobId", jobId)
	}
	if appId != 0 {
		cond.Eq("AppId", appId)
	}
	if status != 0 {
		cond.Eq("Status", status)
	}
	cond.Ne("Status", constants.SCALE_RULES_STATUS_DELETE)
	where, args := cond.GetWhere()
	sql += where

	txManager := service2.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("%s Exception occurs when query JobScaleRule from db, with errors:%+v", requestId, err)
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	tableRules := make([]*table.JobScaleRule, 0) //声明tableRules为JobScaleRule数组对象
	for i := 0; i < len(data); i++ {
		rule := &table.JobScaleRule{}               //声明rule为JobScaleRule对象
		err = util.ScanMapIntoStruct(rule, data[i]) //将db查询出来的data强转成JobScaleRule对象
		if err != nil {
			logger.Errorf("%s Failed to convert bytes into table.JobScaleRule, with errors:%+v", requestId, err)
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode_UnMarshalFailed, "", err)
		}
		tableRules = append(tableRules, rule)
	}
	return tableRules, nil
}

func QueryJobScaleRulesByType(jobId string, appId int64, status int8, ruleType string, planSerialId string) ([]*table.JobScaleRule, error) {
	sql := "SELECT * FROM JobScaleRule  "
	cond := dao.NewCondition()
	if jobId != "" {
		cond.Eq("JobId", jobId)
	}
	if appId != 0 {
		cond.Eq("AppId", appId)
	}
	if status != 0 {
		cond.Eq("Status", status)
	}
	if ruleType != "" {
		cond.Eq("RuleName", ruleType)
	}
	if planSerialId != "" {
		cond.Eq("PlanSerialId", planSerialId)
	}
	cond.Ne("Status", constants.SCALE_RULES_STATUS_DELETE)
	where, args := cond.GetWhere()
	sql += where

	txManager := service2.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Exception occurs when query JobScaleRule with type %s from db, with errors:%+v", ruleType, err)
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	tableRules := make([]*table.JobScaleRule, 0) //声明tableRules为JobScaleRule数组对象
	for i := 0; i < len(data); i++ {
		rule := &table.JobScaleRule{}               //声明rule为JobScaleRule对象
		err = util.ScanMapIntoStruct(rule, data[i]) //将db查询出来的data强转成JobScaleRule对象
		if err != nil {
			logger.Errorf("Failed to convert bytes [%s] into table.JobScaleRule, with type %s, errors:%+v", data[i], ruleType, err)
			continue
		}
		tableRules = append(tableRules, rule)
	}
	return tableRules, nil
}

func QueryJobScaleRulesById(jobId string, appId int64, ruleType string, serialId string) (*table.JobScaleRule, error) {
	sql := "SELECT * FROM JobScaleRule  "
	cond := dao.NewCondition()
	if jobId != "" {
		cond.Eq("JobId", jobId)
	}
	if appId != 0 {
		cond.Eq("AppId", appId)
	}
	if ruleType != "" {
		cond.Eq("RuleName", ruleType)
	}
	cond.Eq("SerialId", serialId)

	where, args := cond.GetWhere()
	sql += where

	txManager := service2.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Exception occurs when query JobScaleRule with type %s from db, with errors:%+v", ruleType, err)
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	tableRules := make([]*table.JobScaleRule, 0) //声明tableRules为JobScaleRule数组对象

	for i := 0; i < len(data); i++ {
		rule := &table.JobScaleRule{}               //声明rule为JobScaleRule对象
		err = util.ScanMapIntoStruct(rule, data[i]) //将db查询出来的data强转成JobScaleRule对象
		if err != nil {
			logger.Errorf("Failed to convert bytes into table.JobScaleRule, with type %s, errors:%+v", ruleType, err)
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode_UnMarshalFailed, "", err)
		}
		tableRules = append(tableRules, rule)
	}
	if len(tableRules) != 1 {
		logger.Errorf("QueryJobScaleRulesById failed sql: %s, args: %+v cnt(%d) != 1", sql, args, len(data))
		err = fmt.Errorf("QueryJobScaleRulesById found rule error, cnt(%d) != 1", len(data))
		return nil, err
	}
	return tableRules[0], nil
}

func DoDescribeJobScaleRules(req *job_autoscale.DescribeJobScaleRulesReq) (*job_autoscale.DescribeJobScaleRulesRsp, error) {

	// 鉴权
	_, err := auth.InnerAuthJob(req.WorkSpaceId, req.IsSupOwner, req.JobId, req.AppId, req.SubAccountUin, req.Region, req.Action)
	if err != nil {
		logger.Errorf("%s: User  uin %s is NOT authenticated, refuse to serve", req.RequestId, req.Uin)
		return nil, err
	}

	requestId := req.RequestId
	startTime := util.GetNowTimestamp()
	defer func() {
		logger.Infof("%s: DoDescribeJobScaleRules use %d", requestId, util.GetNowTimestamp()-startTime)
	}()

	tableRules, err := QueryJobScaleRules(req.JobId, req.AppId, 0, req.RequestId)
	if err != nil {
		return nil, err
	}
	ruleSet := BuildRuleSetFromRuleEntity(tableRules)
	describeJobsRsp := &job_autoscale.DescribeJobScaleRulesRsp{RequestId: requestId, TotalCount: len(tableRules), RuleSet: ruleSet}
	return describeJobsRsp, nil
}

func BuildRuleSetFromRuleEntity(rules []*table.JobScaleRule) (RuleItems []*job_autoscale.RuleSetItem) {
	RuleItems = make([]*job_autoscale.RuleSetItem, 0)
	for _, rule := range rules {
		item := &job_autoscale.RuleSetItem{}
		item.JobId = rule.JobId
		item.RuleId = rule.SerialId
		item.Region = rule.Region
		item.RuleName = rule.RuleName
		item.ScalingType = "auto"
		item.ConditionRatio = rule.ConditionRatio
		item.Threshold = rule.Threshold
		item.DurationTime = rule.DurationTime
		item.Step = rule.Step
		item.Zone = rule.Zone
		item.Status = rule.Status
		item.ReachLimit = rule.ReachLimit
		item.CreateTime = rule.CreateTime
		item.UpdateTime = rule.UpdateTime
		item.Properties = ""
		item.Configuration = rule.Configuration
		RuleItems = append(RuleItems, item)
	}
	return RuleItems
}

func ModifyJobScaleRuleStatusByRuleId(requestId string, ruleIds []string, status int8, appId int64) (err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, requestId+" CreateScaleRule error!")
	txManager := service2.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		for i := 0; i < len(ruleIds); i++ {
			args := make([]interface{}, 0)
			args = append(args, ruleIds[i])
			args = append(args, appId)
			sql := "SELECT * FROM JobScaleRule WHERE SerialId=? AND AppId=?"
			_, data, err := tx.Query(sql, args)
			if err != nil {
				logger.Errorf("QueryScaleRule sql %s, args: %+v, error: %+v", sql, args, err)
				return err
			}

			if len(data) != 1 {
				logger.Errorf(" ModifyJobScaleRule sql: %s, args: %+v cnt(%d) != 1", sql, args, len(data))
				err = fmt.Errorf(" ModifyJobScaleRule found rule error, cnt(%d) != 1", len(data))
				return err
			}

			rule := &table.JobScaleRule{}
			util.ScanMapIntoStruct(rule, data[0])

			sql = "UPDATE JobScaleRule SET Status=? WHERE AppId=? AND SerialId=?"
			result := tx.ExecuteSqlWithArgs(sql, status, appId, ruleIds[i])
			rowsAffected, err := result.RowsAffected()
			if err != nil {
				logger.Errorf("Failed to Update Rule[%s] to `Delete` Status, with errors: %+v", ruleIds[i], err)
				return err
			} else if rowsAffected != 1 {
				msg := fmt.Sprintf("Failed to Update Rule[%s] to `Delete` Status, rowsAffected != 1, actual: %d", ruleIds[i], rowsAffected)
				logger.Error(msg)
				return errorcode.NewStackError(errorcode.InternalErrorCode_UnexpectedRecordNums, "", nil)
			}
		}
		return err
	}).Close()
	return nil
}

func DoDeleteJobScaleRule(req *job_autoscale.DeleteJobScaleRuleReq) (rsp *job_autoscale.DeleteJobScaleRuleRsp, err error) {

	// 鉴权
	for _, RuleId := range req.RuleIds {
		rule, err := checkRuleExist(RuleId, req.AppId)
		if err != nil {
			return nil, err
		}
		// 鉴权
		_, err = auth.InnerAuthJob(req.WorkSpaceId, req.IsSupOwner, rule.JobId, req.AppId, req.SubAccountUin, req.Region, req.Action)
		if err != nil {
			logger.Errorf("%s: User  uin %s is NOT authenticated, refuse to serve", req.RequestId, req.Uin)
			return nil, err
		}
	}

	if err := ModifyJobScaleRuleStatusByRuleId(req.RequestId, req.RuleIds, constants.SCALE_RULES_STATUS_DELETE, req.AppId); err != nil {
		return nil, err
	}
	return &job_autoscale.DeleteJobScaleRuleRsp{}, nil
}

func DoModifyJobScaleRule(req *job_autoscale.ModifyJobScaleRuleReq) (*job_autoscale.ModifyJobScaleRuleRsp, error) {
	rule, err := checkRuleExist(req.RuleId, req.AppId)
	if err != nil {
		return nil, err
	}
	// 鉴权
	_, err = auth.InnerAuthJob(req.WorkSpaceId, req.IsSupOwner, rule.JobId, req.AppId, req.SubAccountUin, req.Region, req.Action)
	if err != nil {
		logger.Errorf("%s: User  uin %s is NOT authenticated, refuse to serve", req.RequestId, req.Uin)
		return nil, err
	}

	rule.ConditionRatio = req.ConditionRatio
	rule.Threshold = req.Threshold
	rule.DurationTime = req.DurationTime
	rule.Step = req.Step
	// 检查是否已经有调优任务启动
	if req.Status == constants.SCALE_RULES_STATUS_ACTIVE {
		rules, err := QueryJobScaleRules(rule.JobId, req.AppId, constants.SCALE_RULES_STATUS_ACTIVE, req.RequestId)
		if err != nil {
			return nil, err
		}
		// 请求的ruleId和
		if len(rules) > 0 {
			for _, rule := range rules {
				if rule.SerialId == req.RuleId {
					continue
				}
				// 其它rule不应该开启。
				logger.Errorf("Job: %s already had run job scale rule or plan", rule.SerialId)
				return nil, errors.New("Job already had run job scale plan")
			}
		}
	}
	if req.Status != 0 {
		rule.Status = req.Status
	}
	rule.ReachLimit = req.ReachLimit
	if req.Properties != "" {
		rule.Properties = req.Properties
	}
	if req.Configuration != "" {
		rule.Configuration = req.Configuration
	}
	if req.RuleName != "" {
		rule.RuleName = req.RuleName
	}
	rule.UpdateTime = util.GetCurrentTime()
	err = UpdateScaleRule(rule)
	// 清除修改前的action,仅删除定时调优的action。
	rules := make([]*table.JobScaleRule, 0)
	rules = append(rules, rule)
	err = TryLockAndIgnoreJobTuningAction(rule.JobId, rules)
	if err != nil {
		logger.Errorf("Failed to ignore job tuning action")
		return nil, err
	}
	if err != nil {
		return nil, err
	}
	describeJobsRsp := &job_autoscale.ModifyJobScaleRuleRsp{}
	return describeJobsRsp, nil
}

func CreateScaleRule(reqId string, rule *table.JobScaleRule) (id string, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, reqId+"CreateScaleRule error!")
	err, _ = checkJobScaleRuleExist(rule.JobId, rule.RuleName)
	if err != nil {
		return "", err
	}
	rule.CreateTime = util.GetCurrentTime()
	rule.UpdateTime = util.GetCurrentTime()
	txManager := service2.GetTxManager()
	var serialId string
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		id := tx.SaveObject(rule, "JobScaleRule")
		cidUtil := &util.CidUtil{}
		serialId = cidUtil.EncodeId(id, "rule", "rule", util.GetNowTimestamp(), 8)
		tx.ExecuteSqlWithArgs("UPDATE JobScaleRule set SerialId = ?  WHERE id = ? ", serialId, id)
		return nil
	}).Close()
	return serialId, nil
}

func UpdateScaleRule(rule *table.JobScaleRule) (err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, "Failed to update JobScaleRule")
	rule.UpdateTime = util.GetCurrentTime()
	txManager := service2.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		rowsAffected := tx.UpdateObject(rule, rule.Id, "JobScaleRule")
		if rowsAffected != 1 {
			msg := fmt.Sprintf("Failed to update Id:%d, JobScaleRule:%+v, rows affected: %d", rule.Id, rule, rowsAffected)
			logger.Error(msg)
			return errorcode.NewStackError(errorcode.InternalErrorCode_UnexpectedRecordNums, "", nil)
		}
		return nil
	}).Close()
	return nil
}

func checkJobScaleRuleExist(id string, rule string) (error, map[int]map[string][]byte) {
	sql := "SELECT * FROM JobScaleRule  "
	cond := dao.NewCondition()
	cond.Eq("JobId", id)
	cond.Eq("RuleName", rule)
	cond.Ne("Status", constants.SCALE_RULES_STATUS_DELETE)
	where, args := cond.GetWhere()
	sql += where
	txManager := service2.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Exception occurs when query JobScaleRule from db, with errors:%+v", err)
		return errorcode.NewStackError(errorcode.InternalErrorCode, "", err), nil
	}
	if data != nil && len(data) > 0 {
		return errorcode.InvalidParameterCode.ReplaceDesc(fmt.Sprintf("JobId[%s] with the same rule name[%s] already exists", id, rule)), nil
	}
	return nil, data
}

func checkRuleExist(id string, appId int64) (*table.JobScaleRule, error) {
	sql := "SELECT * FROM JobScaleRule  "
	cond := dao.NewCondition()
	cond.Eq("SerialId", id)
	if appId != 0 {
		cond.Eq("AppId", appId)
	}
	cond.Ne("Status", constants.SCALE_RULES_STATUS_DELETE)
	where, args := cond.GetWhere()
	sql += where
	txManager := service2.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Exception occurs when query JobScaleRule from db, with errors:%+v", err)
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	if data == nil || len(data) <= 0 {
		return nil, errorcode.ResourceNotFoundCode_ResourceNotFound.ReplaceDesc(fmt.Sprintf("RuleId[%s] is not found", id))
	}
	rule := &table.JobScaleRule{} //声明rule为JobScaleRule对象
	err = util.ScanMapIntoStruct(rule, data[0])
	return rule, err
}

func ModifyJobScaleConfiguration(config string, ruleSerialId string) error {
	txManager := service2.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		sql := "update JobScaleRule set Configuration = ? where  SerialId = ?"
		tx.ExecuteSqlWithArgs(sql, config, ruleSerialId)
		return nil
	}).Close()
	return nil
}
