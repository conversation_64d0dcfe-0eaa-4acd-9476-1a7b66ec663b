package job_autoscale

import (
	"encoding/json"
	"fmt"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/dlocker"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service2 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_autoscale"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/auto_tuning"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job"
	table4 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job_config"
	table3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job_instance"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"time"
)

func DoCreateJobTuningAction(req *job_autoscale.CreateJobTuningActionReq) (*job_autoscale.CreateJobTuningActionResp, error) {
	job, err := service.FindJobBySerialID(req.JobSerialId)
	if err != nil {
		logger.Errorf("%s: Failed to find job with job id %s", req.RequestId, req.JobSerialId)
		return nil, err
	}
	rule, err := QueryJobScaleRulesByType(job.SerialId, int64(job.AppId), constants.SCALE_RULES_STATUS_ACTIVE, constants.SCALE_RULES_AUTO_SCALE_BASIC, "")
	if err != nil {
		logger.Errorf("Failed to query job scale rules by type %s, with errors:%+v", job.SerialId, err)
		return nil, err
	}
	if len(rule) == 0 {
		logger.Warningf("No auto_scale_basic rule found By job:%s", job.SerialId)
	}
	autoScaleRuleConfig := &AutoScaleRuleConfig{}
	action := CreateJobTuningActionEntityFromReq(req, rule[0])
	err = json.Unmarshal([]byte(rule[0].Configuration), &autoScaleRuleConfig)
	if err != nil {
		// 老自动调优就解析失败，不报错，直接忽略
		logger.Errorf("Failed to unmarshal configuration:%s autoScaleRuleConfig, with errors:%+v", rule[0].Configuration, err)
	}
	backoffTime := 10
	if autoScaleRuleConfig.CooldownTimeMinutes != "" {
		backoffTime, err = strconv.Atoi(autoScaleRuleConfig.CooldownTimeMinutes)
		if err != nil {
			logger.Errorf("Failed to convert cooldownTimeMinutes to int, with errors:%+v", err)
			return nil, err
		}
	}
	err, result := CheckJobTuningActionExistAutoScale(req.JobSerialId, req.JobInstanceId, req.ActionType, backoffTime)
	if err != nil {
		return nil, err
	}
	if result {
		return nil, errorcode.InvalidParameterCode.ReplaceDesc(fmt.Sprintf("JobId[%s] with the same "+
			"actionType[%s] already exists", action.JobSerialId, action.ActionType))
	}
	serialId, err := CreateJobTuningAction(req.RequestId, action)
	if err != nil {
		logger.Errorf("%s: CreateJobTuningAction failed for job %s with errors:%+v", req.RequestId, req.JobSerialId, err)
		return nil, err
	}
	logger.Infof("%s: CreateJobTuningAction successful for job %s", req.RequestId, req.JobSerialId)
	return &job_autoscale.CreateJobTuningActionResp{SerialId: serialId, IsSucc: controller.OK}, nil
}

func CreateJobTuningActionEntityFromReq(req *job_autoscale.CreateJobTuningActionReq, rule *table4.JobScaleRule) *table.JobTuningAction {
	item := &table.JobTuningAction{}
	item.JobSerialId = req.JobSerialId
	item.JobInstanceId = req.JobInstanceId
	item.Status = constants.SCALE_ACTION_STATUS_INITIALIZE
	item.ActionType = req.ActionType
	item.ActionDetail = req.ActionDetail
	item.Configuration = req.Configuration
	item.Diagnosis = req.Diagnosis
	item.CreateTime = util.GetCurrentTime()
	item.UpdateTime = util.GetCurrentTime()
	now := time.Now()
	item.ExecuteTime = now.Format("2006-01-02 15:04")
	item.RuleSerialId = rule.SerialId
	return item
}

func CreateJobTuningActionEntityFromJobScaleRule(job *table2.Job, jobInstance *table3.JobInstance, rule *table4.JobScaleRule,
	actionDetail *table.ActionDetail, scaleConfig *ScaleConfig) *table.JobTuningAction {
	actionDetailStr, err := json.Marshal(actionDetail)
	if err != nil {
		logger.Errorf("Failed to marshal actionDetail because %+v. ", err)
		return nil
	}
	item := &table.JobTuningAction{}
	item.JobSerialId = job.SerialId
	item.JobInstanceId = jobInstance.Id
	item.Status = constants.SCALE_ACTION_STATUS_INITIALIZE
	item.ActionType = actionDetail.Type
	item.ActionDetail = string(actionDetailStr)
	item.Configuration = rule.Configuration
	item.Diagnosis = ""
	item.CreateTime = util.GetCurrentTime()
	item.UpdateTime = util.GetCurrentTime()
	item.RuleSerialId = rule.SerialId
	item.ExecuteTime = scaleConfig.OnTime
	return item
}

func CreateJobTuningAction(reqId string, action *table.JobTuningAction) (id string, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, reqId+"CreateScaleRule error!")
	txManager := service2.GetTxManager()
	var serialId string
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		id := tx.SaveObject(action, "JobTuningAction")
		cidUtil := &util.CidUtil{}
		serialId = cidUtil.EncodeId(id, "action", "action", util.GetNowTimestamp(), 8)
		tx.ExecuteSqlWithArgs("UPDATE JobTuningAction set SerialId = ?  WHERE id = ? ", serialId, id)
		return nil
	}).Close()
	return serialId, nil
}

func CheckJobTuningActionExist(serialId string, jobInstanceId int64, actionType string, executeTime string) (error, bool) {
	sql := "SELECT * FROM JobTuningAction  "
	cond := dao.NewCondition()
	cond.Eq("JobSerialId", serialId)
	cond.Eq("ActionType", actionType)
	cond.Eq("JobInstanceId", jobInstanceId)
	cond.Eq("ExecuteTime", executeTime)
	cond.NIn("Status", []int8{constants.SCALE_ACTION_STATUS_SUCCESS, constants.SCALE_ACTION_STATUS_FAILED, constants.SCALE_ACTION_STATUS_IGNORED, constants.SCALE_ACTION_STATUS_UNSATISFIED})
	where, args := cond.GetWhere()
	sql += where
	txManager := service2.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Exception occurs when query JobTuningAction from db, with errors:%+v", err)
		return errorcode.NewStackError(errorcode.InternalErrorCode, "", err), false
	}
	if data != nil && len(data) > 0 {
		return nil, true
	}
	return nil, false
}

func IgnoreJobTuningAction(ruleSerialId string) error {
	locker := dlocker.NewDlocker(fmt.Sprintf("optId-%s-%s", "IgnoreJobTuningAction", ruleSerialId), fmt.Sprintf("optId-%s-%s", "IgnoreJobTuningAction", ruleSerialId), 5)
	err := locker.TryLock(25 * time.Second)
	if err != nil {
		logger.Infof("Another process has lock but not finished yet")
		return err
	}
	defer func() {
		logger.Debugf("TryLockAndIgnoreJobTuningAction Try UnLock IgnoreJobTuningAction")
		err := locker.UnLock()
		if err != nil {
			logger.Errorf("AutoScaleTimeBased - could not fetch the lock. %+v", err)
		}
	}()
	txManager := service2.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		sql := "UPDATE JobTuningAction SET Status = ?,UpdateTime = ? WHERE ruleSerialId = ? AND Status = ?"
		args := make([]interface{}, 0)
		args = append(args, constants.SCALE_ACTION_STATUS_IGNORED)
		args = append(args, time.Now())
		args = append(args, ruleSerialId)
		args = append(args, constants.SCALE_ACTION_STATUS_INITIALIZE)
		tx.ExecuteSql(sql, args)
		return nil
	}).Close()
	return nil
}

func TryLockAndIgnoreJobTuningAction(jobSerialId string, rules []*table4.JobScaleRule) error {
	logger.Debugf("job:%s Try Lock IgnoreJobTuningAction", jobSerialId)
	for _, r := range rules {
		logger.Debugf("TryLockAndIgnoreJobTuningAction Try Lock IgnoreJobTuningAction")
		err := IgnoreJobTuningAction(r.SerialId)
		if err != nil {
			logger.Errorf("Could not fetch the lock. %+v", err)
			return err
		}
	}
	return nil
}

type tmp struct {
	Time string
	Id   int64
}

func QueryJobTuningActions(status int8, requestId string) ([]*table.JobTuningAction, error) {
	txManager := service2.GetTxManager()
	// 获取最近一个小时和未来15分钟内不同job中最大ExecuteTime的actioin Id。
	//1、即只会尝试执行最新的调优action
	//2、当未来15分钟存在其它调优，则不应该执行先前的调优。
	//3、如果job在期间重启过，jobinstance会不同，可能查出已经停止的，后面会将action给过滤掉，等待30s再次扫描（如果很多。。。）
	//4、当一个action完成会有finishcommand忽略所有的action，此时会重新创建一遍新的action，该action会被再次创建，并且会因为资源相同跳过，所以不会执行它之前的action导致出错。
	//5、并且jobinstanceId变了，导致之前的action不会执行。
	sql := "SELECT t.ExecuteTime, t.Id FROM JobTuningAction t JOIN (" +
		"SELECT JobSerialId, MAX(ExecuteTime) AS maxExecuteTime FROM JobTuningAction WHERE" +
		" ExecuteTime >= NOW() - INTERVAL " + constants.DELAY_TIME +
		" MINUTE AND ExecuteTime <= NOW() + INTERVAL " + constants.FUTURE_TIME +
		// 这里不过滤状态是防止自动调优，执行完后面的又执行前面的（和定时调优不同，定时调优不会间隔这么短）
		// 但是这里如果不限制Status，定时调优如果先定一个后面的时间，再往前调一点时间则不会执行这个时间的调优（一直扫的是之前的）
		// Todo：这里可能有问题
		// 限制了状态，自动调优成功了jobinstance会变，后面会判断（会忽略这个action），所以不会出现执行完后面的又执行前面的。
		// 定时调优如果修改rule，会忽略所有的acting，所以不会执行前面的action。
		" MINUTE AND Status = 0  GROUP BY JobSerialId ) s ON t.JobSerialId = s.JobSerialId AND t.ExecuteTime = s.maxExecuteTime  Where status = ?"
	args := make([]interface{}, 0)
	args = append(args, status)
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	arr := make([]int64, 0)
	for i := 0; i < len(data); i++ {
		tmp := &tmp{}
		err = util.ScanMapIntoStruct(tmp, data[i])
		if err != nil {
			logger.Errorf("Failed to convert bytes into table.JobScaleEvent, with errors:%+v", err)
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
		arr = append(arr, tmp.Id)
	}
	// 通过上面id查出对应action
	if len(arr) < 1 {
		return nil, nil
	}
	sql = "SELECT * FROM JobTuningAction "
	cond := dao.NewCondition()
	cond.In("Id", arr)
	where, args := cond.GetWhere()
	sql += where
	_, data, err = txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Exception occurs when query JobTuningAction from db, with errors:%+v", err)
		return nil, errorcode.NewStackError(errorcode.InternalError_Timer, "", err)
	}

	actions := make([]*table.JobTuningAction, 0) //声明tableRules为JobScaleRule数组对象
	for i := 0; i < len(data); i++ {
		action := &table.JobTuningAction{}            //声明rule为JobScaleRule对象
		err = util.ScanMapIntoStruct(action, data[i]) //将db查询出来的data强转成JobScaleRule对象
		if err != nil {
			logger.Errorf("%s Failed to convert bytes into table.JobTuningAction, with errors:%+v", requestId, err)
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
		actions = append(actions, action)
	}
	return actions, nil
}

func QueryJobTuningActionById(jobSerialId string, actionSerialId string) (*table.JobTuningAction, error) {
	sql := "SELECT * FROM JobTuningAction  "
	cond := dao.NewCondition()
	if jobSerialId != "" {
		cond.Eq("JobSerialId", jobSerialId)
	}
	cond.Eq("SerialId", actionSerialId)
	where, args := cond.GetWhere()
	sql += where

	txManager := service2.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Exception occurs when query JobTuningAction from db, with errors:%+v", err)
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "Exception occurs when query JobTuningAction from db", err)
	}

	actions := make([]*table.JobTuningAction, 0) //声明tableRules为JobScaleRule数组对象
	for i := 0; i < len(data); i++ {
		action := &table.JobTuningAction{}            //声明rule为JobScaleRule对象
		err = util.ScanMapIntoStruct(action, data[i]) //将db查询出来的data强转成JobScaleRule对象
		if err != nil {
			logger.Errorf("Failed to convert bytes into table.JobTuningAction, with errors:%+v", err)
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "Failed to convert bytes into table.JobTuningAction", err)
		}
		actions = append(actions, action)
	}
	if len(actions) != 1 {
		logger.Errorf("Unable to find action with serial id %s", actionSerialId)
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "Unable to find action with serial id", err)
	}
	return actions[0], nil
}

func UpdateJobTuningAction(action *table.JobTuningAction) (err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, "Failed to update JobTuningAction")
	action.UpdateTime = util.GetCurrentTime()
	txManager := service2.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		rowsAffected := tx.UpdateObject(action, action.Id, "JobTuningAction")
		if rowsAffected != 1 {
			msg := fmt.Sprintf("Failed to update Id:%d, JobScaleRule:%+v, rows affected: %d", action.Id, action, rowsAffected)
			logger.Error(msg)
			return errorcode.NewStackError(errorcode.InternalErrorCode, "", nil)
		}
		return nil
	}).Close()
	return nil
}

// 检查是否存在同作业同 ActionType 的 action
// watchdog 中一次只能传递一条 action，不同 ActionType 的 action 可以保存，但是需要按顺序执行
func CheckJobTuningActionExistAutoScale(serialId string, jobInstanceId int64, actionType string, coolDownTime int) (error, bool) {
	sql := "SELECT * FROM JobTuningAction  "
	cond := dao.NewCondition()
	cond.Eq("JobSerialId", serialId)
	cond.Eq("ActionType", actionType)
	cond.Eq("JobInstanceId", jobInstanceId)
	cond.NIn("Status", []int8{constants.SCALE_ACTION_STATUS_SUCCESS, constants.SCALE_ACTION_STATUS_FAILED, constants.SCALE_ACTION_STATUS_IGNORED, constants.SCALE_ACTION_STATUS_UNSATISFIED})
	where, args := cond.GetWhere()
	sql += where
	s := strconv.Itoa(coolDownTime)
	sql += "AND ExecuteTime >= NOW() - INTERVAL " + s + " MINUTE"
	txManager := service2.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Exception occurs when query JobTuningAction from db, with errors:%+v", err)
		return errorcode.NewStackError(errorcode.InternalErrorCode, "", err), false
	}
	if data != nil && len(data) > 0 {
		return nil, true
	}
	return nil, false
}
