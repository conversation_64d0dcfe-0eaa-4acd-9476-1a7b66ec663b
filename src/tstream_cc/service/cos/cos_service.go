package cos

import (
	"context"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/pkg/errors"
	"github.com/tencentyun/cos-go-sdk-v5"
	"github.com/tencentyun/cos-go-sdk-v5/debug"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
)

//goland:noinspection GoNameStartsWithPackageName
type CosClient struct {
	Bucket    string
	Region    string
	SecretId  string
	SecretKey string
	Token     string

	cosClient *cos.Client
	cosOnce   sync.Once
}

func (o *CosClient) initCosClient() error {
	o.cosOnce.Do(func() {
		if o.Region == "" {
			panic(errorcode.NewStackError(errorcode.InvalidParameter_InvalidRegion, "Region field of COSClient is empty", nil))
		}
		serviceUrl, err := url.Parse(fmt.Sprintf("https://cos.%s.myqcloud.com", o.Region))
		if err != nil {
			panic(err)
		}

		bucketURL, err := cos.NewBucketURL(o.Bucket, o.Region, true)
		if err != nil {
			panic(err)
		}

		o.cosClient = cos.NewClient(&cos.BaseURL{
			BucketURL:  bucketURL,
			ServiceURL: serviceUrl,
		}, &http.Client{
			Transport: &cos.AuthorizationTransport{
				SecretID:     o.SecretId,
				SecretKey:    o.SecretKey,
				SessionToken: o.Token,
				Transport: &debug.DebugRequestTransport{
					RequestHeader:  true,
					RequestBody:    true,
					ResponseHeader: true,
					ResponseBody:   false,
				},
			},
		})
	})

	if o.cosClient == nil {
		return errorcode.NewStackError(errorcode.InternalError_COSClient, "Failed to init COS client because of nil", nil)
	}

	return nil
}

func (o *CosClient) initCosClient2() error {
	o.cosOnce.Do(func() {
		if o.Region == "" {
			panic(errorcode.NewStackError(errorcode.InvalidParameter_InvalidRegion, "Region field of COSClient is empty", nil))
		}
		serviceUrl, err := url.Parse(fmt.Sprintf("https://cos.%s.myqcloud.com", o.Region))
		if err != nil {
			panic(err)
		}

		var bucketURL *url.URL
		if o.Bucket != "" {
			bucketURL, err = cos.NewBucketURL(o.Bucket, o.Region, true)
			if err != nil {
				panic(err)
			}
		}

		o.cosClient = cos.NewClient(&cos.BaseURL{
			BucketURL:  bucketURL,
			ServiceURL: serviceUrl,
		}, &http.Client{
			Transport: &cos.AuthorizationTransport{
				SecretID:     o.SecretId,
				SecretKey:    o.SecretKey,
				SessionToken: o.Token,
				Transport: &debug.DebugRequestTransport{
					RequestHeader:  true,
					RequestBody:    true,
					ResponseHeader: true,
					ResponseBody:   false,
				},
			},
		})
	})

	if o.cosClient == nil {
		return errorcode.NewStackError(errorcode.InternalError_COSClient, "Failed to init COS client because of nil", nil)
	}

	return nil
}

func (o *CosClient) ListCosBuckets() (buckets []string, err error) {
	err = o.initCosClient2()
	if err != nil {
		return nil, err
	}

	result, resp, err := o.cosClient.Service.Get(context.Background())
	if resp != nil {
		defer resp.Body.Close()
	}
	if err != nil || resp.StatusCode != 200 {
		logErrorStatus(err)
		return nil, errorcode.NewStackError(errorcode.InternalError_COSClient, "COS client responded with error status code "+strconv.Itoa(resp.StatusCode), err)
	}

	buckets = make([]string, 0)
	for _, bucket := range result.Buckets {
		buckets = append(buckets, bucket.Name)
	}

	return buckets, nil
}

func (o *CosClient) WhetherBucketExists() (exist bool, err error) {

	err = o.initCosClient()
	if err != nil {
		return false, err
	}

	resp, err := o.cosClient.Bucket.Head(context.Background())
	if resp != nil {
		defer resp.Body.Close()
	}
	if err != nil {
		logErrorStatus(err)
		return false, err
	}

	if resp.StatusCode == 200 {
		return true, nil
	} else if resp.StatusCode == 403 {
		logger.Errorf("No permission to access this COS bucket: %s", o.Bucket)
		return false, nil
	} else if resp.StatusCode == 404 {
		logger.Errorf("Bucket not found: %s", o.Bucket)
		return false, nil
	} else { // 理论上不应出现
		return false, errorcode.NewStackError(errorcode.InternalError_COSClient, "COS client responded with illegal code "+strconv.Itoa(resp.StatusCode), nil)
	}
}

func (o *CosClient) GetObjectContent(path string) (content string, err error) {

	err = o.initCosClient()
	if err != nil {
		return "", err
	}

	resp, err := o.cosClient.Object.Get(context.Background(), path, nil)
	if resp != nil {
		defer resp.Body.Close()
	}
	if err != nil {
		logErrorStatus(err)
		if resp != nil && resp.StatusCode == 404 {
			return "", errorcode.ResourceNotFoundCode.ReplaceDesc("cos: " + o.Bucket + ",path:" + path)
		}
		msg := fmt.Sprintf("get result from cos fail for path: %s", path)
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, msg, err)
	}
	bs, _ := ioutil.ReadAll(resp.Body)
	return string(bs), nil
}

func (o *CosClient) GetObjectWithDownloadUrl(path string) (content, url string, err error) {

	err = o.initCosClient()
	if err != nil {
		return "", "", err
	}

	resp, err := o.cosClient.Object.Get(context.Background(), path, nil)
	if resp != nil {
		defer resp.Body.Close()
	}
	if err != nil {
		logErrorStatus(err)
		if resp != nil && resp.StatusCode == 404 {
			return "", "", errorcode.NewStackError(errorcode.ResourceUnavailableCode_DebugResultNotReady,
				fmt.Sprintf("cos path: %s", path), nil)
		}
		msg := fmt.Sprintf("get result from cos fail for path: %s", path)
		return "", "", errorcode.NewStackError(errorcode.InternalErrorCode, msg, err)
	}
	bs, _ := ioutil.ReadAll(resp.Body)
	opt := &cos.PresignedURLOptions{
		SignMerged: true,
	}
	downloadUrl, err := o.cosClient.Object.GetPresignedURL(context.Background(), http.MethodGet, path, o.SecretId,
		o.SecretKey, time.Hour*12, opt)
	if err != nil {
		logErrorStatus(err)
		return "", "", errorcode.NewStackError(errorcode.ResourceUnavailableCode_DebugResultNotReady,
			fmt.Sprintf("cos path: %s", path), nil)
	}
	return string(bs), downloadUrl.String(), nil
}

// cluster session resourceConfig 的cos path
// path :objectKey := fmt.Sprintf("%d/%s/upload/%s", req.AppId, req.Uin, time.Now().Format("20060102150405"))
// gate way resourceConfig 的cos path
func (o *CosClient) GetPresignedURL(path, httpMethod string, duration time.Duration) (url string, err error) {
	validPath := isValidPath(path)
	if !validPath {
		logger.Errorf(fmt.Sprintf("path %s is invalid", path))
		return "", errors.New(fmt.Sprintf("path %s is invalid", path))
	}
	if err = o.initCosClient(); err != nil {
		return "", err
	}
	opt := &cos.PresignedURLOptions{
		SignMerged: true,
	}
	if cosUrl, err := o.cosClient.Object.GetPresignedURL(context.Background(), httpMethod, path, o.SecretId, o.SecretKey,
		duration, opt); err != nil {
		return "", errorcode.InternalError_COSClient.NewWithInfo("GetPresignedURL", err)
	} else {
		return cosUrl.String(), nil
	}
}

// isValidPath 函数用于判断路径是否有效
func isValidPath(path string) bool {
	// 检查路径是否为空或等于 "/"
	if path == "" || path == "/" {
		return false
	}
	// 检查路径是否包含 "./"
	if strings.Contains(path, "./") {
		return false
	}
	// 检查路径是否包含 "%2f"
	if strings.Contains(strings.ToLower(path), "%2f") {
		return false
	}
	return true
}

// ListObjects 列出某个目录下的COS object
// @param path COS 路径
// @param maker 由于list最多返回1000条数据，当结果大于1000条时，结果中会返回NextMarker，下一次请求的maker就是上一次返回的NewMaker；
func (o *CosClient) ListObjects(path string, maker string) (result *cos.BucketGetResult, err error) {
	err = o.initCosClient()
	if err != nil {
		return nil, err
	}
	// todo add retry
	result, cosRsp, err := o.cosClient.Bucket.Get(context.Background(), &cos.BucketGetOptions{
		Prefix:  path,
		Marker:  maker,
		MaxKeys: 100,
	})
	if err != nil {
		logger.Errorf("cosClient.Bucket.Get Object with error %v", err)
		return nil, err
	}
	if cosRsp.StatusCode != http.StatusOK {
		logger.Errorf("request cosClient.Bucket.Get Object with error code not 200 but %d", cosRsp.StatusCode)
		return nil, errors.New(fmt.Sprintf("request cosClient.Bucket.Get Object with error code %d", cosRsp.StatusCode))
	}
	if err != nil || cosRsp.StatusCode != http.StatusOK {
		logErrorStatus(err)
	}
	return result, nil
}

func (o *CosClient) IsExist(path string) (bool, error) {
	err := o.initCosClient()
	if err != nil {
		logger.Errorf("initCosClient error %v", err)
		return false, err
	}
	exists, err := o.cosClient.Object.IsExist(context.Background(), path)
	if err != nil {
		logger.Errorf("cosClient.Object.IsExist path %s with error %v", path, err)
	}
	return exists, err
}

func (o *CosClient) CopyOnPutForTargetCos(sourcePath, targetPath string, targetCosClient *CosClient) (err error) {
	defer func() {
		if errs := recover(); errs != nil {
			fmt.Println("CopyOnPutForTargetCos Close err,", errs)
			return
		}
	}()
	err = targetCosClient.initCosClient()
	if err != nil {
		logger.Errorf("init target CosClient error %v", err)
		return err
	}
	err = o.initCosClient()
	if err != nil {
		logger.Errorf("init source CosClient error %v")
		return err
	}

	resp, err := o.cosClient.Object.Get(context.Background(), sourcePath, nil)
	if resp != nil {
		defer func() {
			err := resp.Body.Close()
			if err != nil {
				logger.Errorf("Close body error %v", err)
			}
		}()
	}
	if err != nil {
		logErrorStatus(err)
		if resp != nil && resp.StatusCode == 404 {
			logger.Errorf("CopyOnPutForTargetCos source path %s not found", sourcePath)
			return errorcode.ResourceNotFoundCode.ReplaceDesc("cos: " + o.Bucket + ",path:" + sourcePath)
		}
		msg := fmt.Sprintf("CopyOnPutForTargetCos get result from cos fail for path: %s", sourcePath)
		return errorcode.NewStackError(errorcode.InternalErrorCode, msg, err)
	}

	_, err = targetCosClient.cosClient.Object.Put(context.Background(), targetPath, resp.Body, nil)
	if err != nil {
		logger.Errorf("cosClient.Object.CopyObject with error %v", err)
		return err
	}
	return nil
}

// DeleteObjects 删除COS上的对象
func (o *CosClient) DeleteObjects(objects []cos.Object) (err error) {
	err = o.initCosClient()
	if err != nil {
		return err
	}
	delResult, cosRsp, err := o.cosClient.Object.DeleteMulti(context.Background(), &cos.ObjectDeleteMultiOptions{
		Objects: objects,
	})
	if err != nil {
		logger.Errorf("cosClient.Object.Delete with error %v", err)
		return err
	}
	if cosRsp.StatusCode != http.StatusOK {
		logger.Errorf("request cosClient.Object.Delete with error code not 200 but %d", cosRsp.StatusCode)
		return errors.New(fmt.Sprintf("request cosClient.Object.Delete with error code %d", cosRsp.StatusCode))
	}
	if err != nil || cosRsp.StatusCode != http.StatusOK {
		logErrorStatus(err)
	}
	for _, v := range delResult.Errors {
		logger.Errorf("%s delete with error %v", v.Key, v.Message)
	}
	return nil
}

func logErrorStatus(err error) {
	if err == nil {
		return
	}

	if cos.IsNotFoundError(err) {
		logger.Infof("ERROR: Resource is not existed")
	} else if e, ok := cos.IsCOSError(err); ok {
		logger.Infof("ERROR: Code: %v\n", e.Code)
		logger.Infof("ERROR: Message: %v\n", e.Message)
		logger.Infof("ERROR: Resource: %v\n", e.Resource)
		logger.Infof("ERROR: RequestId: %v\n", e.RequestID)
	} else {
		logger.Warningf("ERROR: %v\n", err)
	}
}

func NewCosClient(secretId, secretKey, token, region, bucket string) *CosClient {
	return &CosClient{
		Bucket:    bucket,
		Region:    region,
		SecretId:  secretId,
		SecretKey: secretKey,
		Token:     token,
	}
}

/**
 * 检查 COS Bucket 是否存在
 *
 * 云 API 专用
 */
func CheckWhetherCOSBucketExists(uin string, subAccountUin string, region string, cosBucket string) error {
	if cosBucket == "" {
		logger.Debugf("COS bucket for Uin %s (SubAccountUin %s) in %s is empty, skip check", uin, subAccountUin, region)
		return nil
	}

	sid, sKey, token, _, err := service.StsAssumeRole(uin, subAccountUin, region)
	if err != nil {
		logger.Errorf("GetTokenCredential error: %+v", err)
		return errorcode.NewStackError(errorcode.AuthFailure_UnauthorizedOperation, "Failed to get credential token", err)
	}
	cosClient := CosClient{
		Bucket:    cosBucket,
		Region:    region,
		SecretId:  sid,
		SecretKey: sKey,
		Token:     token,
	}

	exists, err := cosClient.WhetherBucketExists()
	if err != nil {
		logger.Errorf("Failed to check whether COS bucket %s exists because %+v", cosBucket, err)
		return errorcode.NewStackError(errorcode.InternalError_COSClient, "Failed to validate COS bucket because of COS client error", err)
	}
	if !exists {
		logger.Errorf("COS bucket %s in %d does not exist or no permission to access", cosBucket, region)
		return errorcode.NewStackError(errorcode.ResourceNotFound_COSBucket, "COS bucket does not exist or no permission to access", nil)
	}

	return nil
}

func (o *CosClient) IsMetaBucket() (metaBucket bool, err error) {
	err = o.initCosClient()
	if err != nil {
		return false, err
	}
	headRsp, err := o.cosClient.Bucket.Head(context.Background())
	if err != nil {
		logger.Errorf("cosClient.Bucket.Head  with error %v", err)
		return false, err
	}
	if headRsp.StatusCode != http.StatusOK {
		logger.Errorf("request cosClient.Bucket.Head  with error code not 200 but %d", headRsp.StatusCode)
		return false, errors.New(fmt.Sprintf("request cosClient.Bucket.Head  with error code %d", headRsp.StatusCode))
	}
	return headRsp.Response.Header.Get("x-cos-bucket-arch") == "OFS", nil
}

/**
 * 检查 COS Bucket 是否存在
 *
 * 云 API 专用
 */
func CheckWhetherCOSBucketExistsByNetEnviroment(NetEnviroment int, uin string, subAccountUin string, region string, cosBucket string) error {
	if cosBucket == "" {
		logger.Debugf("COS bucket for Uin %s (SubAccountUin %s) in %s is empty, skip check", uin, subAccountUin, region)
		return nil
	}

	sid, sKey, token, _, err := service.StsAssumeRoleWithSubAccountUinByNetEnvType(NetEnviroment, uin, subAccountUin, region)
	if err != nil {
		logger.Errorf("GetTokenCredential error: %+v", err)
		return errorcode.NewStackError(errorcode.AuthFailure_UnauthorizedOperation, "Failed to get credential token", err)
	}
	cosClient := CosClient{
		Bucket:    cosBucket,
		Region:    region,
		SecretId:  sid,
		SecretKey: sKey,
		Token:     token,
	}

	exists, err := cosClient.WhetherBucketExists()
	if err != nil {
		logger.Errorf("Failed to check whether COS bucket %s exists because %+v", cosBucket, err)
		return errorcode.NewStackError(errorcode.InternalError_COSClient, "Failed to validate COS bucket because of COS client error", err)
	}
	if !exists {
		logger.Errorf("COS bucket %s in %d does not exist or no permission to access", cosBucket, region)
		return errorcode.NewStackError(errorcode.ResourceNotFound_COSBucket, "COS bucket does not exist or no permission to access", nil)
	}

	return nil
}

/*
*
获取临时签名的路径
*/
type URLToken struct {
	SessionToken string `url:"x-cos-security-token,omitempty" header:"-"`
}

func (o *CosClient) GetTempPresignedURL(path, httpMethod string, duration time.Duration) (cosUrl string, err error) {
	if err = o.initCosClient(); err != nil {
		return "", err
	}
	opt := &cos.PresignedURLOptions{
		SignMerged: true,
	}

	if presignedUrl, err := o.cosClient.Object.GetPresignedURL(context.Background(), httpMethod, path, o.SecretId, o.SecretKey,
		duration, opt); err != nil {
		return "", errorcode.InternalError_COSClient.NewWithInfo("GetTempPresignedURL", err)
	} else {
		query := presignedUrl.Query()
		query.Set("response-content-disposition", "attachment")

		presignedUrl.RawQuery = query.Encode()
		// 解码 URL
		presignedUrl.RawPath, err = url.QueryUnescape(presignedUrl.RawPath)

		if err != nil {
			fmt.Println("Failed to decode URL:", err)
			return "", err
		}
		return presignedUrl.String(), nil
	}
}
