package resource_auth

import (
	"fmt"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource_auth/model"
)

const (
	RESOURCE_SERVICE_TYPE   = "oceanus"
	RESOURCE_PREFIX_CLUSTER = "cluster"
	RESOURCE_PREFIX_JOB     = "job"
	RESOURCE_NAMESPACE      = "qcs"
	RESOURCE_ACCOUNT        = "uin"
	RESOURCE_PREFIX_SETATS  = "setats"
)

var rdb resourceDescBuilder

var rdp resourceDescParser

func init() {
	rdb = resourceDescBuilder{
		ServiceType: RESOURCE_SERVICE_TYPE,
	}
	rdp = resourceDescParser{}
}
func GetResourceDescBuilder() resourceDescBuilder {
	return rdb
}
func GetResourceDescParser() resourceDescParser {
	return rdp
}

type resourceDescParser struct {
}
type resourceDescBuilder struct {
	ServiceType string
}

func (this resourceDescBuilder) BuildResourceDesc(projectId int, region string, uin string, resourceId string) (resourceDesc string) {
	ret := "qcs"
	if projectId >= 0 {
		ret = ret + fmt.Sprintf(":id/%d", projectId)
	} else {
		ret = ret + ":"
	}
	ret = ret + fmt.Sprintf(":%s:%s:uin/%s:%s/%s", this.ServiceType, region, uin, RESOURCE_PREFIX_CLUSTER, resourceId)
	return ret
}

func (this resourceDescParser) ParseResourceDesc(resourceDesc string) (isAny bool, desc *model.ResourceDesc, err error) {
	desc = &model.ResourceDesc{}
	if resourceDesc == "*" {
		return true, desc, nil
	}

	strs := strings.Split(resourceDesc, ":")
	if len(strs) != 6 {
		stackErr := errorcode.NewStackError(errorcode.InvalidParameterCode,
			fmt.Sprintf("Invalid resourceDesc"), nil)
		logging.Error(stackErr.Error())
		return false, nil, stackErr
	}
	desc.NameSpace = strs[0]
	desc.ProjectId = strs[1]
	desc.ServiceType = strs[2]
	desc.Region = strs[3]

	uinParas := strings.Split(strs[4], "/")
	if len(uinParas) != 2 {
		stackErr := errorcode.NewStackError(errorcode.InvalidParameterCode,
			fmt.Sprintf("Invalid uniPara:[%s]", strs[4]), nil)
		logging.Error(stackErr.Error())
		return false, nil, stackErr
	}
	desc.Uin = uinParas[1]

	resourceParas := strings.Split(strs[5], "/")
	if len(uinParas) != 2 {
		stackErr := errorcode.NewStackError(errorcode.InvalidParameterCode,
			fmt.Sprintf("Invalid resourcePara:[%s]", strs[5]), nil)
		logging.Error(stackErr.Error())
		return false, nil, stackErr
	}
	desc.ResourcePrefix = resourceParas[0]
	desc.ResourceId = resourceParas[1]

	if desc.ResourceId == "*" {
		return true, desc, nil
	}
	return false, desc, nil
}
