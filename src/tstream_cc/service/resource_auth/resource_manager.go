package resource_auth

import (
	"encoding/json"
	"fmt"
	"math/rand"
	"strings"
	"sync"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/component"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/mc_tag"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource_auth/model"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
	"time"
)

var rm *resourceManager

const (
	ERROR_CODE_NO_PERMISSION = 11008
	STRATEGY_TYPE_DENY       = 0
	STRATEGY_TYPE_ALLOW      = 1
)

type ResourceContext struct {
	AppId         int64
	Uin           string
	SubAccountUin string
	Action        string
	ResourceIds   []string
	Ip            string
	Token         string
	Region        string //需要v2版本的地域，例如bj
}

type resourceManager struct {
	Mock bool
}

func init() {
	rm = &resourceManager{}
}

func GetResourceManager() *resourceManager {
	return rm
}

func (this *resourceManager) CheckAuthByClusterIds(ctx *ResourceContext) (err error, permission bool) {
	authComponent := component.NewAuthComponent()

	para := make(map[string]string)
	if len(ctx.Token) != 0 {
		para["token"] = ctx.Token
	}
	if len(ctx.Ip) != 0 {
		para["ip"] = ctx.Ip
	}
	if len(ctx.Action) != 0 {
		para["action"] = ctx.Action
	}
	//拼装resourceIds
	builder := GetResourceDescBuilder()
	resourceDescs := make([]string, 0, 0)
	if len(ctx.ResourceIds) > 0 {
		for _, originalResourceId := range ctx.ResourceIds {
			if !strings.HasPrefix(originalResourceId, "cluster-") {
				stackErr := errorcode.NewStackError(errorcode.InvalidParameter_InvalidInstanceName,
					fmt.Sprintf("invalid InstanceName [%s]", originalResourceId), nil)
				//logging.Error(stackErr.Error())
				return stackErr, false
			}

			_, uinMocked, _, _ := authComponent.MockForTestEnvIfScsDevEnv(ctx.AppId, ctx.Uin, ctx.SubAccountUin)

			projectId := -1
			resourceDesc := builder.BuildResourceDesc(projectId, ctx.Region, uinMocked, originalResourceId)
			resourceDescs = append(resourceDescs, resourceDesc)
		}
	}
	ret, err := authComponent.CheckAuth(ctx.AppId, ctx.Uin, ctx.SubAccountUin, resourceDescs, int64(generateEventId()), para)
	if err != nil {
		stackErr := errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("CheckAuth fail."), err)
		//logging.Error(stackErr.Error())
		return stackErr, false
	}

	if len(ret) != 0 {
		returnCode := authComponent.GetReturnCode()
		if returnCode == ERROR_CODE_NO_PERMISSION {
			stackErr := errorcode.NewStackError(errorcode.AuthFailure_UnauthorizedOperation,
				fmt.Sprintf("authorize fail, ctx:%+v", ctx), nil)
			//暂时不关系具体哪个资源校验失败
			return stackErr, false
		} else if returnCode != 0 {
			stackErr := errorcode.NewStackError(errorcode.InternalErrorCode,
				fmt.Sprintf("CheckAuth err. Context:%+v, returnCode:%d", ctx, returnCode), nil)
			//logging.Error(stackErr.Error())
			return stackErr, false
		} else {
			return nil, true
		}

	}
	stackErr := errorcode.NewStackError(errorcode.InternalErrorCode,
		fmt.Sprintf("ChechAuth return empty, ctx:%+v", ctx), nil)
	//logging.Error(stackErr.Error())
	return stackErr, false
}

// 按照CAM规范，仅提供给集群/作业列表接口使用
// 对于子账户，只拉取出有权限的资源
func (this *resourceManager) GetAccessibleResource(ctx *ResourceContext) (allowResourceIds []string, isAll bool, err error) {
	camComponent := component.NewCamComponent()
	allowResourceIds = make([]string, 0, 0)

	_, uinMocked, subAccountUinMocked, _ := camComponent.MockForTestEnvIfScsDevEnv(ctx.AppId, ctx.Uin, ctx.SubAccountUin)

	b, _ := json.Marshal(ctx)
	logging.Debugf("ResourceContext: %s, mocked: uin %s, subAccountUin %s", string(b), uinMocked, subAccountUinMocked)

	//主账号对任何资源都有访问权限
	if uinMocked == subAccountUinMocked {
		return allowResourceIds, true, nil
	}

	para := make(map[string]string)
	if len(ctx.Token) != 0 {
		para["token"] = ctx.Token
	}
	if len(ctx.Action) != 0 {
		para["action"] = ctx.Action
	}
	if len(ctx.Ip) == 0 {
		stackErr := errorcode.NewStackError(errorcode.InternalErrorCode, "Missing parameter `Ip`", nil)
		//logging.Error(stackErr.Error())
		return allowResourceIds, false, stackErr
	}
	builder := GetResourceDescBuilder()
	resourceDesc := builder.BuildResourceDesc(-1, ctx.Region, uinMocked, "*")

	ret, err := camComponent.CheckResource(ctx.AppId, ctx.Uin, ctx.SubAccountUin, []string{resourceDesc}, generateEventId(), para)
	if err != nil {
		stackErr := errorcode.NewStackError(errorcode.InternalErrorCode,
			fmt.Sprintf("CheckResource fail. ctx:%+v", ctx), err)
		//logging.Error(stackErr.Error())
		return nil, false, stackErr
	}

	logging.Debug(fmt.Sprintf("CheckResource response:%s", ret))

	if len(ret) == 0 {
		return allowResourceIds, false, nil
	}

	if camComponent.GetReturnCode() == ERROR_CODE_NO_PERMISSION {
		//无任何权限时，返回空资源列表
		return nil, false, nil
	}

	allowed, denied, getFromDb, isAll, err := this.ParseCheckResourceResponse(ctx, ret)
	if err != nil {
		return nil, false, err
	}
	logging.Debugf("ParseCheckResourceResponse response: allowed %v, denied %v, getFromDb %v, isAll %v",
		allowed, denied, getFromDb, isAll)

	if !getFromDb {
		return allowResourceIds, isAll, nil
	}

	//从数据库中获取
	condition := dao.NewCondition()
	condition.Eq("AppId", ctx.AppId)
	condition.Gte("status", constants.CLUSTER_GROUP_STATUS_CREATING)
	condition.Eq("Type", constants.CLUSTER_GROUP_TYPE_PRIVATE)

	if len(allowed) > 0 {
		condition.In("SerialId", allowed)
	}
	if len(denied) > 0 {
		condition.NIn("SerialId", denied)
	}
	where, args := condition.GetWhere()

	clusterGroups, err := service3.GetTableService().ListclusterGroupsBySqlWithArgs("SELECT * FROM ClusterGroup"+where, args...)
	if err != nil {
		return nil, false, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	for _, cg := range clusterGroups {
		allowResourceIds = append(allowResourceIds, cg.SerialId)
	}

	return allowResourceIds, false, nil
}

func (o *resourceManager) ParseCheckResourceResponse(ctx *ResourceContext, response string) (
	allowedArray []string, deniedArray []string, getFromDb bool, isAll bool, err error) {
	result := model.CheckResourceResponse{}
	err = json.Unmarshal([]byte(response), &result)
	if err != nil {
		stackErr := errorcode.NewStackError(errorcode.InternalErrorCode, "json unmarshal fail.", err)
		return nil, nil, false, false, stackErr
	}

	//对resourceList进行处理
	anyByResourceList, resourceIdsByResourceList, err := o.ParseResourceList(ctx, result.ResourceList)
	logging.Debug(fmt.Sprintf("anyByResourceList:%v,resourceIdsByResourceList:%+v", anyByResourceList, resourceIdsByResourceList))
	if err != nil {
		return nil, nil, false, false, err
	}
	denyAllByResourceList := anyByResourceList && result.Type == STRATEGY_TYPE_DENY
	allowAllByResourceList := anyByResourceList && result.Type == STRATEGY_TYPE_ALLOW

	//对tagList进行处理
	resourceIdsByTagList, err := o.ParseTagList(ctx, result.TagList)
	logging.Debug(fmt.Sprintf("resourceIdsByTagList:%+v", resourceIdsByTagList))
	if err != nil {
		return nil, nil, false, false, err
	}

	// 计算逻辑：http://tapd.oa.com/QCloud_2015/markdown_wikis/show/#1210103951000770957
	allowed := make(map[string]struct{}, 0)
	denied := make(map[string]struct{}, 0)
	if !anyByResourceList {
		for _, id := range resourceIdsByResourceList {
			if result.Type == STRATEGY_TYPE_ALLOW {
				allowed[id] = struct{}{}
			} else {
				denied[id] = struct{}{}
			}
		}
	}
	for _, id := range resourceIdsByTagList {
		if result.TagType == STRATEGY_TYPE_ALLOW {
			allowed[id] = struct{}{}
		} else {
			denied[id] = struct{}{}
		}
	}

	// 计算逻辑：http://tapd.oa.com/QCloud_2015/markdown_wikis/show/#1210103951000770957
	if result.Type == STRATEGY_TYPE_ALLOW && result.TagType == STRATEGY_TYPE_ALLOW {
		if allowAllByResourceList {
			// 全集
			return nil, nil, false, true, nil
		}
		if len(allowed) == 0 {
			// 无权限
			return nil, nil, false, false, nil
		}
		// allowed>0,denied=0 => allowed **************************
	} else if result.Type == STRATEGY_TYPE_ALLOW && result.TagType == STRATEGY_TYPE_DENY {
		if allowAllByResourceList {
			if len(denied) == 0 {
				// 全集
				return nil, nil, false, true, nil
			}
			// allowed=0,denied>0 => 全集-denied **************************
		} else if len(allowed) == 0 {
			// 无权限
			return nil, nil, false, false, nil
		} // allowed>0,denied>=0 => allowed - denied **************************
	} else if result.Type == STRATEGY_TYPE_DENY && result.TagType == STRATEGY_TYPE_ALLOW {
		if denyAllByResourceList {
			if len(allowed) == 0 {
				// 无权限
				return nil, nil, false, false, nil
			}
			// allowed>0,denied=0 => allowed **************************
		} else if len(denied) == 0 {
			// 全集
			return nil, nil, false, true, nil
		} else {
			allowed = map[string]struct{}{}
			// allowed=0,denied>0 => 全集-denied **************************
		}
	} else {
		if denyAllByResourceList {
			return nil, nil, false, false, nil
		}
		if len(denied) == 0 {
			return nil, nil, false, true, nil
		}
		// allowed=0,denied>0 => 全集-denied **************************
	}

	allowedArray = make([]string, 0, len(allowed))
	deniedArray = make([]string, 0, len(denied))
	if len(allowed) > 0 && len(denied) > 0 {
		for id, _ := range allowed {
			if _, exist := denied[id]; !exist {
				allowedArray = append(allowedArray, id)
			}
		}
		if len(allowedArray) == 0 {
			return nil, nil, false, false, nil
		}
	} else {
		for id, _ := range allowed {
			allowedArray = append(allowedArray, id)
		}
		for id, _ := range denied {
			deniedArray = append(deniedArray, id)
		}
	}
	return allowedArray, deniedArray, true, false, nil
}

// empty: 当err = nil时有意义，当没有合法的集群列表，且用户没有传递集群列表时，应该直接返回空集群列表
// 注意这里返回了共享集群（若有）
func (o *resourceManager) GetAccessibleClusters(ctx *ResourceContext, withSharedClusters bool) (clusterGroupIds []string, empty bool, err error) {
	clusterIds, isAll, err := o.GetAccessibleResource(&ResourceContext{
		AppId:         ctx.AppId,
		Uin:           ctx.Uin,
		SubAccountUin: ctx.SubAccountUin,
		Action:        fmt.Sprintf("%s:DescribeClusters", RESOURCE_SERVICE_TYPE),
		ResourceIds:   nil, // 无需
		Ip:            "127.0.0.1",
		Token:         ctx.Token,
		Region:        ctx.Region,
	})
	if err != nil {
		code := errorcode.GetCode(err)
		// 未授权则显示为0（作业数、集群数）
		if code.GetCodeStr() == errorcode.AuthFailure_UnauthorizedOperation.GetCodeStr() {
			return nil, true, nil
		}
		return nil, true, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	logging.Debugf("isAll: %v, clusterIds: %v", isAll, clusterIds)

	if isAll {
		return nil, false, nil
	}

	if !withSharedClusters {
		return clusterIds, len(clusterIds) == 0, nil
	}

	if clusterGroups, err := service2.ListClusterGroups(&service2.ListClusterGroupsParam{
		AppId:        int32(ctx.AppId),
		Regions:      []string{ctx.Region},
		Zone:         "",
		ClusterType:  []int8{constants.CLUSTER_GROUP_TYPE_SHARED},
		ClusterNames: nil,
		IsVagueNames: true,
		SerialIds:    nil,
		Offset:       0,
		Limit:        0,
		OrderType:    0,
		StatusList:   nil,
	}); err != nil {
		return nil, true, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	} else if len(clusterGroups) > 0 {
		for _, cg := range clusterGroups {
			clusterIds = append(clusterIds, cg.SerialId)
		}
	}

	return clusterIds, len(clusterIds) == 0, nil
}

func (this *resourceManager) ParseResourceList(ctx *ResourceContext, resourceList []string) (
	any bool, parsedResourceList []string, err error) {
	parsedResourceList = make([]string, 0, 0)
	rdp := GetResourceDescParser()
	for _, resource := range resourceList {
		any, desc, err := rdp.ParseResourceDesc(resource)
		if err != nil {
			return false, nil, err
		}
		if any {
			return true, nil, nil
		}

		parsedResourceList = append(parsedResourceList, desc.ResourceId)

	}
	return false, parsedResourceList, nil
}

func getResourceByDenyStrategy(ctx *ResourceContext, resourceList []string) (allowResourceList []string, err error) {
	allowResourceList = make([]string, 0, 0)
	denyList := make([]string, 0, 0)
	rdp := GetResourceDescParser()
	for _, resource := range resourceList {
		any, desc, err := rdp.ParseResourceDesc(resource)
		if err != nil {
			return nil, err
		}
		if !any {
			denyList = append(denyList, desc.ResourceId)
		} else {
			return allowResourceList, nil
		}
	}
	condition := dao.NewCondition()
	condition.Eq("AppId", ctx.AppId)
	if len(denyList) != 0 {
		condition.NIn("SerialId", denyList)
	}
	condition.Gte("status", constants.CLUSTER_GROUP_STATUS_CREATING)
	condition.Eq("Type", constants.CLUSTER_GROUP_TYPE_PRIVATE)
	where, args := condition.GetWhere()

	clusterGroups, err := service3.GetTableService().ListclusterGroupsBySqlWithArgs("SELECT * FROM ClusterGroup"+where, args...)
	if err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	for _, cg := range clusterGroups {
		allowResourceList = append(allowResourceList, cg.SerialId)
	}

	return allowResourceList, nil
}

func getResourceByAllowStrategy(ctx *ResourceContext, resourceList []string) (allowResourceList []string, err error) {
	allowResourceList = make([]string, 0, 0)
	rdp := GetResourceDescParser()
	for _, resource := range resourceList {
		any, desc, err := rdp.ParseResourceDesc(resource)
		if err != nil {
			return nil, err
		}
		if !any {
			allowResourceList = append(allowResourceList, desc.ResourceId)
		} else {
			//从数据库中获取
			condition := dao.NewCondition()
			condition.Eq("AppId", ctx.AppId)
			condition.Gte("status", constants.CLUSTER_GROUP_STATUS_CREATING)
			condition.Eq("Type", constants.CLUSTER_GROUP_TYPE_PRIVATE)
			where, args := condition.GetWhere()

			clusterGroups, err := service3.GetTableService().ListclusterGroupsBySqlWithArgs("SELECT * FROM ClusterGroup"+where, args...)
			if err != nil {
				return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
			}
			for _, cg := range clusterGroups {
				allowResourceList = append(allowResourceList, cg.SerialId)
			}
		}
	}
	return allowResourceList, nil
}

func (o *resourceManager) ParseTagList(ctx *ResourceContext, TagList []*model.TagKeyValue) (
	resourceList []string, err error) {
	const pageSize = 1000

	resourceList = make([]string, 0, 0)
	if len(TagList) == 0 {
		return
	}

	if o.Mock {
		for _, kv := range TagList {
			resourceList = append(resourceList, kv.Key)
		}
	}

	tagComponent := component.NewTagComponent()

	tagFilters := make([]*component.TagFilter, 0, len(TagList))
	for _, tag := range TagList {
		tagFilters = append(tagFilters, &component.TagFilter{
			TagKey: tag.Key,
			TagValue: func() []string {
				if len(tag.Value) > 0 {
					return []string{tag.Value}
				} else {
					return nil
				}
			}(),
		})
	}
	tagFilters, _ = tagComponent.CompactTagFilters(tagFilters)
	b, _ := json.Marshal(tagFilters)
	logging.Debugf("compact tagFilters,len %d, %s", len(tagFilters), string(b))

	fn := func(tagFilters []*component.TagFilter) interface{} {
		resourceList := make([]string, 0, 0)
		for i := 1; ; i++ {
			rspStr, err := tagComponent.GetResourcesByTagsUnion(ctx.Uin, ctx.Region,
				fmt.Sprintf("%d", i), fmt.Sprintf("%d", pageSize),
				RESOURCE_SERVICE_TYPE, RESOURCE_PREFIX_CLUSTER,
				tagFilters, time.Now().Unix(), map[string]string{})
			if err != nil {
				return err
			}
			rsp := &mc_tag.GetResourcesByTagsUnionResponse{}
			if err := json.Unmarshal([]byte(rspStr), rsp); err != nil {
				return errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
			}

			if len(rsp.Rows) == 0 {
				break
			}

			for _, row := range rsp.Rows {
				resourceList = append(resourceList, row.ResourceId)
			}
			if uint64((i-1)*pageSize+len(rsp.Rows)) >= rsp.Total {
				break
			}
		}
		return resourceList
	}

	// chan多一个缓冲区也没关系
	resourceListChn := make(chan interface{}, len(tagFilters)/component.MaxTagKeysGetResourcesByTagsUnion+1)
	wg := &sync.WaitGroup{}

	for s := 0; s < len(tagFilters); s += component.MaxTagKeysGetResourcesByTagsUnion {
		e := s + component.MaxTagKeysGetResourcesByTagsUnion
		if e > len(tagFilters) {
			e = len(tagFilters)
		}

		wg.Add(1)
		go func(tagFilters []*component.TagFilter) {
			resourceListChn <- fn(tagFilters)
			wg.Done()
		}(tagFilters[s:e])

		if e >= len(tagFilters) {
			break
		}
	}

	wg.Wait()
	close(resourceListChn)

	for result := range resourceListChn {
		if err, ok := result.(error); ok {
			return nil, err
		}
		resourceList = append(resourceList, result.([]string)...)
	}

	return resourceList, nil
}

func (o *resourceManager) ParseTagListForJob(ctx *ResourceContext, TagList []*model.TagKeyValue) (
	resourceList []string, err error) {
	const pageSize = 1000

	resourceList = make([]string, 0, 0)
	if len(TagList) == 0 {
		return
	}

	if o.Mock {
		for _, kv := range TagList {
			resourceList = append(resourceList, kv.Key)
		}
	}

	tagComponent := component.NewTagComponent()

	tagFilters := make([]*component.TagFilter, 0, len(TagList))
	for _, tag := range TagList {
		tagFilters = append(tagFilters, &component.TagFilter{
			TagKey: tag.Key,
			TagValue: func() []string {
				if len(tag.Value) > 0 {
					return []string{tag.Value}
				} else {
					return nil
				}
			}(),
		})
	}
	tagFilters, _ = tagComponent.CompactTagFilters(tagFilters)
	b, _ := json.Marshal(tagFilters)
	logging.Debugf("compact tagFilters,len %d, %s", len(tagFilters), string(b))

	fn := func(tagFilters []*component.TagFilter) interface{} {
		resourceList := make([]string, 0, 0)
		for i := 1; ; i++ {
			rspStr, err := tagComponent.GetResourcesByTagsUnion(ctx.Uin, ctx.Region,
				fmt.Sprintf("%d", i), fmt.Sprintf("%d", pageSize),
				RESOURCE_SERVICE_TYPE, RESOURCE_PREFIX_JOB,
				tagFilters, time.Now().Unix(), map[string]string{})
			if err != nil {
				return err
			}
			rsp := &mc_tag.GetResourcesByTagsUnionResponse{}
			if err := json.Unmarshal([]byte(rspStr), rsp); err != nil {
				return errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
			}

			if len(rsp.Rows) == 0 {
				break
			}

			for _, row := range rsp.Rows {
				resourceList = append(resourceList, row.ResourceId)
			}
			if uint64((i-1)*pageSize+len(rsp.Rows)) >= rsp.Total {
				break
			}
		}
		return resourceList
	}

	// chan多一个缓冲区也没关系
	resourceListChn := make(chan interface{}, len(tagFilters)/component.MaxTagKeysGetResourcesByTagsUnion+1)
	wg := &sync.WaitGroup{}

	for s := 0; s < len(tagFilters); s += component.MaxTagKeysGetResourcesByTagsUnion {
		e := s + component.MaxTagKeysGetResourcesByTagsUnion
		if e > len(tagFilters) {
			e = len(tagFilters)
		}

		wg.Add(1)
		go func(tagFilters []*component.TagFilter) {
			resourceListChn <- fn(tagFilters)
			wg.Done()
		}(tagFilters[s:e])

		if e >= len(tagFilters) {
			break
		}
	}

	wg.Wait()
	close(resourceListChn)

	for result := range resourceListChn {
		if err, ok := result.(error); ok {
			return nil, err
		}
		resourceList = append(resourceList, result.([]string)...)
	}

	return resourceList, nil
}

// 测试用
func (o *resourceManager) EnableMock() *resourceManager {
	o.Mock = true
	return o
}

func generateEventId() int64 {
	r1 := rand.New(rand.NewSource(time.Now().UnixNano()))
	return int64(r1.Intn(65533) + 1)
}
