package flow

import (
	"encoding/json"
	"fmt"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	"tencentcloud.com/tstream_galileo/src/main/credentials_provider/common/log"
	constants2 "tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	table3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cdb"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/tke"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config/configure_center"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
)

type ClusterFlowService struct {
	FlowService
	RequestId string
	Request   *flow.TaskExecRequest

	ClusterGroup   *table.ClusterGroup
	Cluster        *table.Cluster
	ConnectionType int

	Tke            *table2.Tke
	ClusterType    int
	ArchGeneration int

	Cdb *table3.Cdb

	Region  string
	NewZone string //共享集群母集群开新区

	ClusterGroupService *service.ClusterGroupService
	CurStatus           int

	returnParams map[string]string

	cc *configure_center.ConfigureCenter
}

func NewClusterFlowService(request *flow.TaskExecRequest) (s *ClusterFlowService, err error) {
	s = &ClusterFlowService{Request: request, returnParams: request.Params}

	requestId, _, err := flowService.GetFlowParamString(request.Params, constants2.FLOW_PARAM_REQUEST_ID, "")
	if err != nil {
		return
	}
	s.RequestId = requestId

	zone, _, err := flowService.GetFlowParamString(request.Params, constants2.FLOW_PARAM_ZONE, "")
	if err != nil {
		return
	}
	s.NewZone = zone

	taskStatus, err := s.GetTaskStatus(request)
	if err != nil {
		return
	}
	s.CurStatus = int(taskStatus)

	clusterGroupId, _, err := flowService.GetFlowParamInt(request.Params, constants2.FLOW_PARAM_CLUSTER_GROUP_ID, 0)
	if err != nil || clusterGroupId == 0 {
		log.Errorf("##please set ClusterGroupId in params, clusterGroupIdParam: %d", clusterGroupId)
		return s, fmt.Errorf("request: %s, clusterGroupId: %d is 0,  please set ClusterGroupId in params, err: %+v", requestId, clusterGroupId, err)
	}

	s.ClusterGroupService, err = service.NewClusterGroupService(clusterGroupId)
	if err != nil {
		return
	}

	s.ClusterGroup = s.ClusterGroupService.GetClusterGroup()
	clusterList, err := s.ClusterGroupService.GetClusterList()
	if err != nil {
		return
	}
	if len(clusterList) <= 0 {
		err = errorcode.InternalErrorCode.NewWithMsg(fmt.Sprintf("there are %d cluster", len(clusterList)))
		return
	}
	s.Cluster = clusterList[0]

	s.ConnectionType = constants2.CLUSTER_NET_CONNECTION_TYPE_ENI
	if s.ClusterGroup.NetEnvironmentType == 3 {
		s.ConnectionType = constants2.CLUSTER_NET_CONNECTION_TYPE_INNER
	}

	s.ClusterType, err = s.GetClusterType(s.Request.Params)
	if err != nil {
		return
	}
	s.Region = s.ClusterGroup.Region
	tkeList, err := s.ClusterGroupService.GetTkeList()
	if err != nil {
		return
	}
	if len(tkeList) > 1 {
		err = errorcode.InternalErrorCode.NewWithMsg(fmt.Sprintf("there are %d tke instances", len(tkeList)))
		return
	}
	if len(tkeList) == 1 {
		s.Tke = tkeList[0]
		s.ArchGeneration = s.Tke.ArchGeneration
		s.ClusterType = s.Tke.ClusterType
	} else { // maybe zero
		paramArchGeneration, _, err := flowService.GetFlowParamInt(request.Params, constants2.FLOW_PARAM_ARCH_GNERATION, 0)
		if err != nil {
			return s, err
		}
		s.ArchGeneration = int(paramArchGeneration)

		paramTkeType, _, err := flowService.GetFlowParamInt(request.Params, constants2.FLOW_PARAM_CLUSTER_TYPE, 0)
		if err != nil {
			return s, err
		}
		s.ClusterType = int(paramTkeType)
	}

	cdbList, err := service2.GetTableService().ListCdbByClusterId(s.Cluster.Id)
	if err != nil {
		return
	}
	if len(cdbList) > 1 {
		err = errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("there are %d cdb instances", len(cdbList)), nil)
		return
	}
	if len(cdbList) == 1 {
		s.Cdb = cdbList[0]
	}

	return
}

func (s *ClusterFlowService) RetryRsp(msg string) *flow.TaskExecResponse {
	return s.NewTaskExecResponseWithParamsAndStatus(flow.TASK_IN_PROCESS, s.CurStatus, 0.1, msg, s.returnParams)
}

func (s *ClusterFlowService) RetryRspWithErr(err error) *flow.TaskExecResponse {
	return s.RetryRsp(fmt.Sprintf("%v", err))
}

func (s *ClusterFlowService) DoneRsp(msg string) *flow.TaskExecResponse {
	if s.CurStatus == flow.TASK_STATUS_INIT {
		return s.NewTaskExecResponseWithParamsAndStatus(flow.TASK_IN_PROCESS, flow.TASK_STATUS_RUNNING, 0.1, msg,
			s.returnParams)
	}
	if s.CurStatus == flow.TASK_STATUS_RUNNING {
		return s.NewTaskExecResponseWithParamsAndStatus(flow.TASK_SUCCESS, flow.TASK_STATUS_SUCCESS, 1, msg,
			s.returnParams)
	}

	return s.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 1, msg,
		s.returnParams)
}

// 失败Rsp，后面将不会重试. 谨慎使用。 一般只有涉及到调用资源创建的云api，才会用这个rsp
func (s *ClusterFlowService) FailRsp(msg string) *flow.TaskExecResponse {
	return s.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 1, msg,
		s.returnParams)
}

func (s *ClusterFlowService) FailRspWithErr(err error) *flow.TaskExecResponse {
	return s.FailRsp(fmt.Sprintf("%v", err))
}

func (s *ClusterFlowService) SetReturnParam(key, value string) {
	s.returnParams[key] = value
}

func (s *ClusterFlowService) SetReturnParamUseJson(key string, value interface{}) {
	v, _ := json.Marshal(value)
	s.returnParams[key] = string(v)
}

func (s *ClusterFlowService) GetParamUseJson(key string, value interface{}) (exists bool, err error) {
	val, exists := s.Request.Params[key]
	if !exists {
		return
	}
	err = json.Unmarshal([]byte(val), value)
	err = errorcode.InternalErrorCode.NewWithErr(err)
	return
}

func (s *ClusterFlowService) GetParam(key string, defaultValue string) (val string, exists bool) {
	val, exists = s.Request.Params[key]
	if !exists {
		return defaultValue, false
	}
	return val, true
}

func (s *ClusterFlowService) GetParamInt(key string, defaultValue int64) (val int64, exists bool, err error) {
	sVal, exists := s.Request.Params[key]
	if !exists {
		return defaultValue, false, nil
	}
	val, err = strconv.ParseInt(sVal, 10, 64)
	if err != nil {
		return defaultValue, false, errorcode.InternalErrorCode.NewWithErr(err)
	}
	return val, true, nil
}

func (s *ClusterFlowService) CC() *configure_center.ConfigureCenter {
	if s.cc == nil {
		s.cc = configure_center.CC(s.ClusterGroup.Region)
	}

	return s.cc
}
