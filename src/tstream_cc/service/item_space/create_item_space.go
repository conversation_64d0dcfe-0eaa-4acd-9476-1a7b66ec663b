package item_space

import (
	"errors"
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service3 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/item_space"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/item_space"
	tableRole "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/role_auth"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/quota"
	"unicode"
)

/**
 * Created by <PERSON> on 2021/10/18 1:27 下午.
 * At tencent
 */

func DoCreateItemSpace(req *model.CreateItemSpaceReq) (string, string, interface{}) {

	// 0. Print RequestId
	logger.Debugf("%s: CreateItemSpace API called by AppId %d, ProjectId %d", req.RequestId, req.AppId, req.ProjectId)

	// 检查project是否存在
	if req.ProjectId > 0 {
		ExistItemSpaceTable, err := GetItemSpaceByProjectId(req.AppId, req.Region, req.ProjectId)
		if err != nil {
			logger.Errorf("%s: GetItemSpaceByProjectId error: %+v", req.RequestId, err)
			return controller.UnsupportedOperation, "GetItemSpaceByProjectId error", nil
		}
		if ExistItemSpaceTable != nil {
			err = SyncProjectUsers(ExistItemSpaceTable)
			if err != nil {
				logger.Errorf("%s: SyncProjectUsers error: %+v", req.RequestId, err)
				return controller.UnsupportedOperation, "SyncProjectUsers error", nil
			}
			return controller.OK, controller.NULL, &model.CreateItemSpaceRsp{WorkSpaceId: ExistItemSpaceTable.SerialId}
		}
	}

	// 命名规范
	err := service2.CheckNameValidity(req.WorkSpaceName)
	if err != nil {
		logger.Errorf("%s: Failed to CheckNameValidity, with error: %+v", req.RequestId, err)
		err := errorcode.InvalidParameter_InvalidItemSpaceName.ReplaceDesc(" Improper ItemSpcaeName ")
		return errorcode.GetCodeReturn(err, nil)
	}

	// 检测重名  Appid ,region ,name
	isPass, errCodeStr, msg := CheckNameExisted(req.AppId, req.WorkSpaceName, req.Region)
	if !isPass {
		logger.Errorf("%s: %s", req.RequestId, msg)
		return errCodeStr, msg, nil
	}

	// 检测资源超用
	itemSpaceTotalCount, err := GetItemSpaceTotalCount(req.AppId, req.Region)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}
	overLimit, msg, err := quota.NewQuota().OverLimit("", int64(req.AppId), quota.ItemSpace, int32(itemSpaceTotalCount))
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}
	if overLimit && req.ProjectId == 0 {
		errMsg := fmt.Sprintf("ItemSpace %s", msg)
		err = errorcode.LimitExceededCode_WorkSpaceLimitExceeded.ReplaceDesc(errMsg)
		return errorcode.GetCodeReturn(err, nil)
	}

	//绑定成员权限表并保存作业
	ItemSpaceTable, _ := BuildItemSpaceFromReq(req)
	RoleAuthoTableUin, _ := BuildRoleAuthoFromReq(req, req.Uin, constants.ROLE_AUTHO_PERMISSION_SUPER_OWNER)
	var RoleAuthoTableSubUin *tableRole.RoleAuth
	if req.Uin != req.SubAccountUin {
		RoleAuthoTableSubUin, _ = BuildRoleAuthoFromReq(req, req.SubAccountUin, constants.ROLE_AUTHO_PERMISSION_OWNER)
	}

	_, err = SaveTable(ItemSpaceTable, RoleAuthoTableUin, RoleAuthoTableSubUin)
	if err != nil {
		logger.Errorf("%s:  Save ItemSpace error: %+v", req.RequestId, err)
		return controller.InternalError, "Can Not Save ItemSpace ", nil
	}

	// nameSpace
	err = SyncProjectUsers(ItemSpaceTable)
	if err != nil {
		logger.Errorf("%s: SyncProjectUsers error: %+v", req.RequestId, err)
		return controller.UnsupportedOperation, "SyncProjectUsers error", nil
	}
	createItemSpaceRsp := &model.CreateItemSpaceRsp{WorkSpaceId: ItemSpaceTable.SerialId}
	return controller.OK, controller.NULL, createItemSpaceRsp

}

func SaveTable(spaceTable *table.ItemSpace, authoTable *tableRole.RoleAuth, authoTable2 *tableRole.RoleAuth) (ItemSpaceId int64, err error) {
	errorcode.DefaultDeferHandler(&err)
	txManager := service3.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		transcationSql, params := CheckNameExistedTranscationSql(spaceTable.AppId, spaceTable.Region, spaceTable.ItemSpaceName)
		_, data, errs := tx.Query(transcationSql, params)
		if errs != nil {
			return err
		} else if len(data) > 0 {
			return errors.New(fmt.Sprintf(" ItemSpaceName: %s Already Exist", spaceTable.ItemSpaceName))
		}
		ItemSpaceId = tx.SaveObject(spaceTable, "ItemSpace")
		cidUtil := &util.CidUtil{}
		serialId := cidUtil.EncodeId(ItemSpaceId, "space", "space", util.GetNowTimestamp(), 8)
		tx.ExecuteSqlWithArgs("UPDATE ItemSpace set serialId = ?  WHERE id = ? ", serialId, ItemSpaceId)
		spaceTable.SerialId = serialId
		spaceTable.Id = ItemSpaceId
		authoTable.ItemSpaceId = ItemSpaceId
		authoTable.ItemSpaceSerialId = serialId
		tx.SaveObject(authoTable, "RoleAuth")
		if authoTable2 != nil {
			authoTable2.ItemSpaceId = ItemSpaceId
			authoTable2.ItemSpaceSerialId = serialId
			tx.SaveObject(authoTable2, "RoleAuth")
		}
		return nil
	}).Close()
	return ItemSpaceId, err
}

func BuildRoleAuthoFromReq(req *model.CreateItemSpaceReq, uin string, Permission int64) (*tableRole.RoleAuth, error) {
	RoleAuth := &tableRole.RoleAuth{}
	RoleAuth.AppId = req.AppId
	RoleAuth.OwnerUin = req.Uin
	RoleAuth.CreatorUin = req.SubAccountUin
	RoleAuth.AuthSubAccountUin = uin
	RoleAuth.CreateTime = util.GetCurrentTime()
	RoleAuth.UpdateTime = RoleAuth.CreateTime
	RoleAuth.Permission = Permission
	RoleAuth.Status = constants.ROLE_AUTHO_STATUS_USEABLE
	return RoleAuth, nil
}

func BuildItemSpaceFromReq(req *model.CreateItemSpaceReq) (*table.ItemSpace, error) {
	ItemSpace := &table.ItemSpace{}
	ItemSpace.AppId = req.AppId
	ItemSpace.OwnerUin = req.Uin
	ItemSpace.CreatorUin = req.SubAccountUin
	ItemSpace.ItemSpaceName = req.WorkSpaceName
	ItemSpace.Region = req.Region
	ItemSpace.ProjectId = req.ProjectId
	ItemSpace.Description = req.Description
	ItemSpace.CreateTime = util.GetCurrentTime()
	ItemSpace.UpdateTime = ItemSpace.CreateTime
	ItemSpace.Status = constants.ITEM_SPACE_STATUS_USEABLE
	return ItemSpace, nil
}

func ListProjectItemSpace() (itemSpaces []*table.ItemSpace, err error) {
	itemSpaces = make([]*table.ItemSpace, 0)

	sql := "SELECT * FROM ItemSpace "
	cond := dao.NewCondition()

	cond.Ne("Status", constants.ITEM_SPACE_STATUS_DELETED)
	cond.Ne("ProjectId", 0)

	where, args := cond.GetWhere()
	sql += where

	txManager := service3.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return nil, err
	}

	for i := 0; i < len(data); i++ {
		ItemSpace := &table.ItemSpace{}
		err = util.ScanMapIntoStruct(ItemSpace, data[i])
		if err != nil {
			return nil, errorcode.InternalErrorCode_UnMarshalFailed.NewWithErr(err)
		}
		itemSpaces = append(itemSpaces, ItemSpace)
	}
	return itemSpaces, err
}

func GetItemSpaceBySpaceName(appId int64, region string, itemSpaceName string) (itemTable *table.ItemSpace, err error) {
	listItemSpacesQuery := model.ListItemSpaceQuery{
		ItemSpaceName: []string{itemSpaceName},
		AppId:         appId,
		Region:        region,
		Offset:        0,
		Limit:         -1,
	}

	if !IsLegitimate(region) {
		errMsg := fmt.Sprintf("Region is not legitimate")
		return nil, errors.New(errMsg)
	}

	items, err := ListItemSpaces(&listItemSpacesQuery)
	if err != nil {
		return nil, err
	}
	if len(items) >= 1 {
		return items[0], nil
	}
	return nil, nil
}

func GetItemSpaceByProjectId(appId int64, region string, ProjectId int64) (itemTable *table.ItemSpace, err error) {
	listItemSpacesQuery := model.ListItemSpaceQuery{
		ProjectId: ProjectId,
		AppId:     appId,
		Region:    region,
		Offset:    0,
		Limit:     -1,
	}

	if !IsLegitimate(region) {
		errMsg := fmt.Sprintf("Region is not legitimate")
		return nil, errors.New(errMsg)
	}

	items, err := ListItemSpaces(&listItemSpacesQuery)
	if err != nil {
		return nil, err
	}
	if len(items) > 1 {
		msg := fmt.Sprintf(" ProjectId: %d Already Exist, length is %d", ProjectId, len(items))
		return nil, errors.New(msg)
	} else if len(items) == 1 {
		return items[0], nil
	}
	return nil, nil
}

func CheckNameExisted(appId int64, ItemSpaceName string, region string) (isPass bool, errorCode string, msg string) {
	listItemSpacesQuery := model.ListItemSpaceQuery{
		ItemSpaceName: []string{ItemSpaceName},
		AppId:         appId,
		Region:        region,
		Offset:        0,
		Limit:         -1,
	}

	if !IsLegitimate(region) {
		errMsg := fmt.Sprintf("Region is not legitimate")
		return false, controller.InternalError, errMsg
	}

	items, err := ListItemSpaces(&listItemSpacesQuery)
	if err != nil {
		return false, controller.InternalError, err.Error()
	}
	if len(items) > 1 {
		msg := fmt.Sprintf(" ItemSpaceName: %s Already existed", ItemSpaceName)
		return false, controller.InternalError, msg
	} else if len(items) == 1 {
		msg := fmt.Sprintf("ItemSpaceName: `%s` has existed", ItemSpaceName)
		return false, controller.FailedOperation_DuplicatedSpaceName, msg
	}
	return true, "", ""
}

func GetItemSpaceTotalCount(appId int64, region string) (count int, err error) {
	listItemSpacesQuery := model.ListItemSpaceQuery{
		AppId:  appId,
		Region: region,
		Offset: 0,
		Limit:  -1,
	}
	items, err := ListItemSpaces(&listItemSpacesQuery)
	return len(items), nil
}

func ListItemSpaces(query *model.ListItemSpaceQuery) ([]*table.ItemSpace, error) {
	sql := "SELECT * FROM ItemSpace "
	cond := dao.NewCondition()

	cond.Ne("Status", constants.ITEM_SPACE_STATUS_DELETED)

	if len(query.ItemSpaceName) > 0 {
		ItemSpaceName := UniqueSliceString(query.ItemSpaceName)
		cond.In("ItemSpaceName", ItemSpaceName)
	}

	if len(query.ClusterGroupId) > 0 {
		ClusterGroupId := UniqueSliceInt64(query.ClusterGroupId)
		cond.In("ClusterGroupId", ClusterGroupId)
	}

	if query.Region != "" {
		cond.Eq("Region", query.Region)
	}

	if query.AppId != 0 {
		cond.Eq("Appid", query.AppId)
	}
	if query.ProjectId != 0 {
		cond.Eq("ProjectId", query.ProjectId)
	}

	where, args := cond.GetWhere()
	sql += where

	if query.Limit > 0 {
		sql += " LIMIT ?, ?"
		args = append(args, query.Offset)
		args = append(args, query.Limit)
	} else {
		sql += " LIMIT ?, ?"
		args = append(args, query.Offset)
		// 默认返回20条，临时设置为全部返回
		args = append(args, -1)
	}

	txManager := service3.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return nil, err
	}

	ItemSpaces := make([]*table.ItemSpace, 0)
	for i := 0; i < len(data); i++ {
		ItemSpace := &table.ItemSpace{}
		err = util.ScanMapIntoStruct(ItemSpace, data[i])
		if err != nil {
			return nil, errorcode.InternalErrorCode_UnMarshalFailed.NewWithErr(err)
		}
		ItemSpaces = append(ItemSpaces, ItemSpace)
	}

	return ItemSpaces, nil
}

func UniqueSliceString(slice []string) []string {
	if len(slice) == 0 {
		return slice
	}

	m := make(map[string]struct{}, len(slice))
	for _, s := range slice {
		m[s] = struct{}{}
	}
	slice = make([]string, 0, len(m))
	for s, _ := range m {
		slice = append(slice, s)
	}
	return slice
}

func UniqueSliceInt64(slice []int64) []int64 {
	if len(slice) == 0 {
		return slice
	}

	m := make(map[int64]struct{}, len(slice))
	for _, s := range slice {
		m[s] = struct{}{}
	}
	slice = make([]int64, 0, len(m))
	for s, _ := range m {
		slice = append(slice, s)
	}
	return slice
}

// 检查bucket和region的合法性
func IsLegitimate(str string) bool {
	for _, x := range []rune(str) {
		if !unicode.IsDigit(x) && !unicode.IsLetter(x) && x != '-' {
			return false
		}
	}
	return true
}
