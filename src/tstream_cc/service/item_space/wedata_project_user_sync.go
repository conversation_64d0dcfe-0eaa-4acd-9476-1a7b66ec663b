package item_space

import (
	"errors"
	"fmt"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service3 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/item_space"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/role_auth"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/role_auth"
	wedata2 "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/wedata"
)

func SyncProjectUsers(item *table.ItemSpace) (err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("SyncProjectUsers panic, item: %d, with errors:%+v", item.Id, errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	if item.ProjectId <= 0 {
		return nil
	}
	pRoles, err := DescribeProjectUsersAll(item.Region, item.OwnerUin, item.CreatorUin, item.ProjectId)
	if err != nil {
		return
	}
	roleAuths, err := role_auth.ListSpaceRoleAuth(item.AppId, item.SerialId)
	if err != nil {
		return
	}
	cUsers, mdUsers, mdSpace, err := calcSyncUsers(item, pRoles, roleAuths)

	txManager := service3.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		//modify space
		if mdSpace {
			args := make([]interface{}, 0, 0)
			sql := "UPDATE ItemSpace set CreatorUin = ?  WHERE Id = ?"
			args = append(args, item.CreatorUin)
			args = append(args, item.Id)
			rowsAffected, err := tx.ExecuteSqlWithArgs(sql, args...).RowsAffected()
			if err != nil {
				return err
			} else if rowsAffected != 1 {
				logger.Errorf("Logic error? RowsAffected not 1, but %d when update ItemSpace, value is %v", rowsAffected, item)
			}
		}
		//add RoleAuth
		for _, user := range cUsers {
			tx.SaveObject(user, "RoleAuth")
		}
		//modify or delete RoleAuth
		for _, user := range mdUsers {
			args := make([]interface{}, 0, 0)
			sql := "UPDATE RoleAuth set `CreatorUin` = ?, `AuthSubAccountUin` = ?, `Status` = ? , Permission = ? WHERE Id = ?"
			args = append(args, item.CreatorUin)
			args = append(args, user.AuthSubAccountUin)
			args = append(args, user.Status)
			args = append(args, user.Permission)
			args = append(args, user.Id)
			rowsAffected, err := tx.ExecuteSqlWithArgs(sql, args...).RowsAffected()
			if err != nil {
				return err
			} else if rowsAffected != 1 {
				logger.Errorf("Logic error? RowsAffected not 1, but %d when update RoleAuth, value is %v", rowsAffected, user)
			}
		}
		return nil
	}).Close()
	return err
}

func calcSyncUsers(
	itemSpace *table.ItemSpace,
	pRoles []*wedata2.ProjectUserRole,
	roleAuths []*table2.RoleAuth) (cUsers []*table2.RoleAuth, mdUsers []*table2.RoleAuth, mdSpace bool, err error) {
	cUsers = make([]*table2.RoleAuth, 0)
	mdUsers = make([]*table2.RoleAuth, 0)
	roleAuthMap := make(map[string]*table2.RoleAuth)
	for _, auth := range roleAuths {
		roleAuthMap[auth.AuthSubAccountUin] = auth
	}
	for _, pRole := range pRoles {
		if *pRole.Creator && *pRole.UserId != itemSpace.CreatorUin {
			itemSpace.CreatorUin = *pRole.UserId
			mdSpace = true
		}
		// creator just need to modify
		if *pRole.Creator {
			delete(roleAuthMap, *pRole.UserId)
			continue
		}
		roleId := getOceanusRoleForWedataRole(pRole)
		roleAuth, ok := roleAuthMap[*pRole.UserId]
		//in wedata, not oceanus
		if !ok {
			cRole := BuildRoleAuthFromItemSpace(itemSpace, *pRole.UserId, roleId)
			cUsers = append(cUsers, cRole)
			delete(roleAuthMap, *pRole.UserId)
			continue
		}
		//in wedata, in oceanus
		if roleId != roleAuth.Permission {
			roleAuth.Permission = roleId
			mdUsers = append(mdUsers, roleAuth)
			delete(roleAuthMap, *pRole.UserId)
			continue
		}
		// change Administrator CreatorUin
		if roleAuth.OwnerUin == roleAuth.AuthSubAccountUin && strings.HasPrefix(roleAuth.CreatorUin, "46") {
			mdUsers = append(mdUsers, roleAuth)
		}
		delete(roleAuthMap, *pRole.UserId)
	}
	//not wedata, in oceanus
	for _, auth := range roleAuthMap {
		// change Creator
		if auth.CreatorUin == auth.AuthSubAccountUin && strings.HasPrefix(auth.CreatorUin, "46") {
			auth.CreatorUin = itemSpace.CreatorUin
			auth.AuthSubAccountUin = itemSpace.CreatorUin
			if auth.OwnerUin == auth.AuthSubAccountUin { // creator has added where create workspace
				continue
			}
			mdUsers = append(mdUsers, auth)
			continue
		}

		if auth.Permission != constants.ROLE_AUTHO_STATUS_DISEABLE {
			auth.Permission = constants.ROLE_AUTHO_STATUS_DISEABLE
			mdUsers = append(mdUsers, auth)
		}
	}
	return
}

func BuildRoleAuthFromItemSpace(itemSpace *table.ItemSpace, AuthUin string, RoleId int64) *table2.RoleAuth {
	RoleAuth := &table2.RoleAuth{}
	RoleAuth.AppId = itemSpace.AppId
	RoleAuth.OwnerUin = itemSpace.OwnerUin
	RoleAuth.ItemSpaceId = itemSpace.Id
	RoleAuth.ItemSpaceSerialId = itemSpace.SerialId
	RoleAuth.CreatorUin = itemSpace.CreatorUin
	RoleAuth.AuthSubAccountUin = AuthUin
	RoleAuth.CreateTime = util.GetCurrentTime()
	RoleAuth.UpdateTime = RoleAuth.CreateTime
	RoleAuth.Permission = RoleId
	RoleAuth.Status = constants.ROLE_AUTHO_STATUS_USEABLE
	return RoleAuth
}

func getOceanusRoleForWedataRole(pRole *wedata2.ProjectUserRole) int64 {
	roleMap := make(map[string]string)
	for _, role := range pRole.Roles {
		roleMap[*role.Name] = *role.RoleId
	}
	_, ok := roleMap["ProjectManager"]
	if ok {
		return 1
	}
	_, ok = roleMap["DataEngineer"]
	if ok {
		return 2
	}
	return 3
}
