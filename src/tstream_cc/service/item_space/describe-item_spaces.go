package item_space

import (
	"sort"
	"strconv"
	"strings"
	"sync"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/billing"
	clusterModel "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/cluster"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/item_space"
	model2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/role_auth"
	billingTable "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/billing"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/item_space"
	table3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/role_auth"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service_cluster "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
)

/**
 * Created by Andy on 2021/11/24 3:41 下午.
 * At tencent
 */

type describeItemSpaceContext struct {
	req                   *model.DescribeItemSpacesAuthReq
	totalCount            int
	clusterGroups         []*table2.ClusterGroup
	clusterGroupSerialIds []string
	ItemSpaceClusters     map[int64][]*model.ClusterGroupSetItem
	RoleAuths             map[int64][]*model2.RoleAuth
	RoleAuthCount         map[int64]int64
	itemTables            []*table.ItemSpace
	itemSpaceIds          []int64
	AdministersIns        []string                         // 超管账号的SubUins
	AdministersInfo       map[string]*table3.Administrator // 超管账号的SubUins
	JobsCount             map[int64]int64
}

func DoDescribeItemSpaces(req *model.DescribeItemSpacesAuthReq) (string, string, interface{}) {
	context := &describeItemSpaceContext{req: req}
	return context.DoDescribeItemSpacesAuth()
}

func (c *describeItemSpaceContext) DoDescribeItemSpacesAuth() (string, string, interface{}) {
	if c.req.Limit == 0 {
		c.req.Limit = 20
	}
	c.clusterGroupSerialIds = make([]string, 0)

	// 获取超管账号的Subuin
	err := c.GetAdminstors()
	if err != nil {
		logger.Errorf("%s: Failed to Administer, with error: %+v", c.req.RequestId, err)
		return controller.InternalError, err.Error(), nil
	}
	// 获取ItemSpace 根据不同账号的权限不同
	totalCount, itemTables, err := c.GetItemSpaces()
	if err != nil {
		logger.Errorf("%s: Failed to DescribeItemSpace, with error: %+v", c.req.RequestId, err)
		return controller.InternalError, err.Error(), nil
	}

	c.itemTables = itemTables
	c.itemSpaceIds = make([]int64, 0, len(itemTables))
	for _, itemTable := range itemTables {
		c.itemSpaceIds = append(c.itemSpaceIds, itemTable.Id)
	}

	// 根据ItemSpaceId 查找 CLusterGroup
	err = c.findClusterByItemSpace()
	if err != nil {
		logger.Errorf("%s: Failed to find ClusterByItemSpace, with error: %+v", c.req.RequestId, err)
		return controller.InternalError, err.Error(), nil
	}
	//根据ItemSpaceId 查找 空间用户和超管用户
	err = c.findRoleAuthoBySpaceId()
	if err != nil {
		logger.Errorf("%s: Failed to find RoleAuthoBySpaceId, with error: %+v", c.req.RequestId, err)
		return controller.InternalError, err.Error(), nil
	}
	// 查找空间作业数量
	err = c.findJobsCount()
	if err != nil {
		logger.Errorf("%s: Failed to find Jobs, with error: %+v", c.req.RequestId, err)
		return controller.InternalError, err.Error(), nil
	}

	ItemSpaces := &model.DescribeItemSpacesAuthRsp{TotalCount: totalCount, WorkSpaceSetItem: c.BuildDescribeItemSpacesAuthRsp()}
	return controller.OK, controller.NULL, ItemSpaces
}

func (c *describeItemSpaceContext) GetItemSpaces() (totalCount int, itemTables []*table.ItemSpace, err error) {
	nameFilters := GetItemNameFiltersFromReq(c.req)
	contain, _ := service2.Contain(c.req.SubAccountUin, c.AdministersIns)
	//  查找超管用户
	if contain {
		return GetItemSpacesSubuin(c.req, c.req.Uin, nameFilters)
	}
	return GetItemSpacesSubuin(c.req, c.req.SubAccountUin, nameFilters)
}

func GetItemSpacesSubuin(req *model.DescribeItemSpacesAuthReq, AuthSubAccountUin string, nameFilter []string) (totalCount int, itemTables []*table.ItemSpace, err error) {
	defer errorcode.DefaultDeferHandler(&err)
	args := make([]interface{}, 0)
	args = append(args, req.AppId)
	args = append(args, req.Region)
	args = append(args, constants.ITEM_SPACE_STATUS_USEABLE)
	var sql_1 string
	if len(nameFilter) > 0 {
		condition, params := getOrCondition(nameFilter, "ItemSpaceName")
		sql_1 = "SELECT * from ItemSpace WHERE Appid = ? AND Region = ? AND `Status` = ?" + condition +
			"AND Id IN (SELECT ItemSpaceId  from RoleAuth WHERE AuthSubAccountUin = ? And `Status` = ? )"
		for _, param := range params {
			args = append(args, param)
		}
	} else {
		sql_1 = "SELECT * from ItemSpace WHERE Appid = ? AND Region = ? AND `Status` = ? AND" +
			" Id IN (SELECT ItemSpaceId  from RoleAuth WHERE AuthSubAccountUin = ? And `Status` = ? )"
	}
	sql := sql_1
	logger.Debug("====")
	logger.Debug(sql_1)

	args = append(args, AuthSubAccountUin)
	args = append(args, constants.ROLE_AUTHO_STATUS_USEABLE)

	if req.OrderType == 0 {
		req.OrderType = 1
	}

	switch req.OrderType {
	case constants.ITEM_SPACE_ORDERBY_CTREATETIME_DESC:
		sql += " ORDER BY CreateTime DESC "
	case constants.ITEM_SPACE_ORDERBY_CTREATETIME_ASC:
		sql += " ORDER BY CreateTime ASC "
	case constants.ITEM_SPACE_ORDERBY_STATUS_DESC:
		sql += " ORDER BY Status DESC "
	case constants.ITEM_SPACE_ORDERBY_STATUS_ASC:
		sql += " ORDER BY Status ASC "
	default:
		sql += " ORDER BY CreateTime DESC "
	}

	if req.Limit > 0 {
		sql += " LIMIT ?, ?"
		args = append(args, req.Offset)
		args = append(args, req.Limit)
	} else {
		sql += " LIMIT ?, ?"
		args = append(args, req.Offset)
		args = append(args, -1)
	}

	txManager := service.GetTxManager()
	for i, arg := range args {
		logger.Debug("arg %d: %v", i, arg)
	}
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)

	if err != nil {
		logger.Errorf("Failed to execute the sql:%s, with args:%+v, with errors:%+v", sql, args, err)
		return 0, nil, err
	}

	t := make([]*table.ItemSpace, 0)
	for i := 0; i < len(data); i++ {
		itemTable := &table.ItemSpace{}
		err = util.ScanMapIntoStruct(itemTable, data[i])
		if err != nil {
			logger.Errorf("Failed to convert bytes into ItemSpace, with errors:%+v", err)
			return 0, nil, errorcode.InternalErrorCode_UnMarshalFailed.NewWithErr(err)
		}
		t = append(t, itemTable)
	}

	totalCount, err = GetItemSpacesTotalCount(req, AuthSubAccountUin, nameFilter)
	if err != nil {
		return 0, nil, err
	}
	return totalCount, t, nil
}

func GetItemSpacesTotalCount(req *model.DescribeItemSpacesAuthReq, AuthSubAccountUin string, nameFilter []string) (totalCount int, err error) {
	defer errorcode.DefaultDeferHandler(&err)
	args := make([]interface{}, 0)
	args = append(args, req.AppId)
	args = append(args, req.Region)
	args = append(args, constants.ITEM_SPACE_STATUS_USEABLE)
	var sql_1 string
	if len(nameFilter) > 0 {
		condition, params := getOrCondition(nameFilter, "ItemSpaceName")
		sql_1 = "SELECT * from ItemSpace WHERE Appid = ? AND Region = ? AND `Status` = ?" + condition +
			"AND Id IN (SELECT ItemSpaceId  from RoleAuth WHERE AuthSubAccountUin = ? And `Status` = ? )"
		for _, param := range params {
			args = append(args, param)
		}
	} else {
		sql_1 = "SELECT * from ItemSpace WHERE Appid = ? AND Region = ? AND `Status` = ? AND" +
			" Id IN (SELECT ItemSpaceId  from RoleAuth WHERE AuthSubAccountUin = ? And `Status` = ? )"
	}
	sql := sql_1
	args = append(args, AuthSubAccountUin)
	args = append(args, constants.ROLE_AUTHO_STATUS_USEABLE)

	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)

	if err != nil {
		logger.Errorf("Failed to execute the sql:%s, with args:%+v, with errors:%+v", sql, args, err)
		return 0, err
	}

	return len(data), nil
}

func getOrCondition(filter []string, fileName string) (string, []string) {
	where := " AND ("
	//cond := dao.NewCondition()
	params := make([]string, 0, 0)
	for i := 0; i < len(filter); i++ {
		if i == 0 {
			pass, search := processWildcard(filter[i])
			if pass {
				where += fileName + " like ? escape '/'"
				params = append(params, "%"+search+"%")
			} else {
				where += fileName + " like ? "
				params = append(params, "%"+filter[i]+"%")
			}
		} else {
			pass, search := processWildcard(filter[i])
			if pass {
				where += " OR " + fileName + " like ? escape '/'"
				params = append(params, "%"+search+"%")
			} else {
				where += " OR " + fileName + " like ? "
				params = append(params, "%"+filter[i]+"%")
			}
		}
	}
	return where + ") ", params
}

func processWildcard(search string) (contains bool, result string) {
	ok := strings.Contains(search, "_")
	if ok {
		search = strings.ReplaceAll(search, "_", "/_")
		contains = true
	}
	ok = strings.Contains(search, "%")
	if ok {
		search = strings.ReplaceAll(search, "%", "/%")
		contains = true
	}
	return contains, search
}

func GetItemNameFiltersFromReq(req *model.DescribeItemSpacesAuthReq) []string {
	for _, filter := range req.Filters {
		if (filter.Name == "WorkSpaceName") && len(filter.Values) > 0 {
			return filter.Values
		}
	}

	return nil
}

// 根据itemSpaceId 查找 Clusters
func (c *describeItemSpaceContext) findClusterByItemSpace() error {

	c.ItemSpaceClusters = make(map[int64][]*model.ClusterGroupSetItem, 0)
	// 授权资源中抽取子账号空间授权的集群--ItemSpaceId 是授权的 itemSpaceID
	// 注意 clusterGroupIds 这里指的是 SerialId
	for _, itemSpaceId := range c.itemSpaceIds {
		clusterGroups, err := service2.ExtractSubUinAuthoClusterGroups(c.clusterGroupSerialIds, itemSpaceId)
		if err != nil {
			logger.Errorf("%s: ExtractSubUinAuthoClusters : Obtain  clusterGroups that has permissions   error: %+v", c.req.RequestId, err)
			return err
		}
		billingResourceMap, err := service_cluster.GetClusterGroupBillingInfo(clusterGroups)
		if err != nil {
			return err
		}
		c.ItemSpaceClusters[itemSpaceId] = make([]*model.ClusterGroupSetItem, 0)
		err = c.BuildClusterSetItems(clusterGroups, itemSpaceId, billingResourceMap)
		if err != nil {
			return err
		}
	}
	return nil
}

func (c *describeItemSpaceContext) findRoleAuthoBySpaceId() error {
	c.RoleAuths = make(map[int64][]*model2.RoleAuth, 0)
	c.RoleAuthCount = make(map[int64]int64, 0)
	for _, itemSpaceId := range c.itemSpaceIds {
		roleAuth, err := c.RoleAuthoFindByItemSpaceId(itemSpaceId)
		// 超管状态的变更以及超管账户补充
		if err != nil {
			return err
		}
		c.RoleAuths[itemSpaceId] = roleAuth
		c.RoleAuthCount[itemSpaceId] = int64(len(roleAuth))
	}
	return nil
}

func (c *describeItemSpaceContext) BuildDescribeItemSpacesAuthRsp() []*model.ItemSpaceSetItem {
	rsp := make([]*model.ItemSpaceSetItem, 0)
	for _, itemSpace := range c.itemTables {
		ItemSpaceSetItem := &model.ItemSpaceSetItem{}
		ItemSpaceSetItem.SerialId = itemSpace.SerialId
		ItemSpaceSetItem.ProjectId = itemSpace.ProjectId
		ItemSpaceSetItem.ProjectIdStr = strconv.FormatInt(itemSpace.ProjectId, 10)
		ItemSpaceSetItem.AppId = itemSpace.AppId
		ItemSpaceSetItem.OwnerUin = itemSpace.OwnerUin
		ItemSpaceSetItem.CreatorUin = itemSpace.CreatorUin
		ItemSpaceSetItem.WorkSpaceName = itemSpace.ItemSpaceName
		ItemSpaceSetItem.Region = itemSpace.Region
		ItemSpaceSetItem.CreateTime = itemSpace.CreateTime
		ItemSpaceSetItem.UpdateTime = itemSpace.UpdateTime
		ItemSpaceSetItem.Status = itemSpace.Status
		ItemSpaceSetItem.Description = itemSpace.Description
		ItemSpaceSetItem.ClusterGroupSetItem = c.ItemSpaceClusters[itemSpace.Id]
		ItemSpaceSetItem.RoleAuth = c.RoleAuths[itemSpace.Id]
		ItemSpaceSetItem.RoleAuthCount = c.RoleAuthCount[itemSpace.Id]
		ItemSpaceSetItem.JobsCount = c.JobsCount[itemSpace.Id]
		ItemSpaceSetItem.WorkSpaceId = ItemSpaceSetItem.SerialId
		rsp = append(rsp, ItemSpaceSetItem)
	}
	return rsp
}

func (c *describeItemSpaceContext) RoleAuthoFindByItemSpaceId(itemSpaceId int64) (table []*model2.RoleAuth, err error) {
	errorcode.DefaultDeferHandler(&err)
	sql := "SELECT * from RoleAuth WHERE ItemSpaceId = ? AND `Status` != ? "
	args := make([]interface{}, 0)
	args = append(args, itemSpaceId)
	args = append(args, constants.ROLE_AUTHO_STATUS_DISEABLE)

	txManager := service.GetTxManager()
	_, datas, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to execute the sql:%s, with args:%+v, with errors:%+v", sql, args, err)
		return nil, err
	}

	arr := make([]string, 0)
	t := make([]*model2.RoleAuth, 0)
	for _, data := range datas {
		roleAuth := &model2.RoleAuth{}
		roleAuth.WorkSpaceSerialId = string(data["ItemSpaceSerialId"])
		roleAuth.WorkSpaceId, _ = strconv.ParseInt(string(data["ItemSpaceId"]), 10, 64)
		err = util.ScanMapIntoStruct(roleAuth, data)
		if err != nil {
			return nil, errorcode.InternalErrorCode_UnMarshalFailed.NewWithErr(err)
		}
		if ok, _ := service2.Contain(roleAuth.AuthSubAccountUin, c.AdministersIns); ok {
			roleAuth.Status = constants.ROLE_AUTHO_PERMISSION_SUPER_OWNER
		}
		arr = append(arr, roleAuth.AuthSubAccountUin)
		t = append(t, roleAuth)
	}

	for _, value := range c.AdministersIns {
		if ok, _ := service2.Contain(value, arr); !ok {
			role := c.buildAdministor(value)
			t = append(t, role)
		}
	}
	return t, nil
}

// GetAdminstors SELECT * from Administrator  WHERE AppId =?
func (c *describeItemSpaceContext) GetAdminstors() (err error) {
	errorcode.DefaultDeferHandler(&err)
	sql := "SELECT * from Administrator  WHERE AppId = ? AND OwnerUin = ? AND `Status` != ?"
	args := make([]interface{}, 0)
	args = append(args, c.req.AppId)
	args = append(args, c.req.Uin)
	args = append(args, constants.ROLE_AUTHO_STATUS_DISEABLE)

	txManager := service.GetTxManager()
	_, datas, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to execute the sql:%s, with args:%+v, with errors:%+v", sql, args, err)
		return err
	}

	t := make([]string, 0)
	t1 := make([]*table3.Administrator, 0)
	c.AdministersInfo = make(map[string]*table3.Administrator)
	for _, data := range datas {
		admin := &table3.Administrator{}
		err = util.ScanMapIntoStruct(admin, data)
		if err != nil {
			return errorcode.InternalErrorCode_UnMarshalFailed.NewWithErr(err)
		}
		t = append(t, admin.AuthSubAccountUin)
		t1 = append(t1, admin)
		c.AdministersInfo[admin.AuthSubAccountUin] = admin
	}
	c.AdministersIns = t

	return err
}

func (c *describeItemSpaceContext) BuildClusterSetItems(clusterGroups []*table2.ClusterGroup, itemSpaceId int64, billingResourceMap map[string]*billingTable.BillingResource) (err error) {
	clusterSetItemChn := make(chan interface{}, len(clusterGroups))
	wg := sync.WaitGroup{}
	wg.Add(len(clusterGroups))
	for i := 0; i < len(clusterGroups); i++ {
		clusterGroup := clusterGroups[i]
		fn := func(clusterGroup *table2.ClusterGroup) interface{} {
			var memRatio int8 = 4
			clusters, err := service_cluster.ListClusters(clusterGroup.Id)
			if err != nil {
				logger.Errorf("Failed to list clusters with clusterGroupId %d", clusterGroup.Id)
				return err
			}
			if len(clusters) > 0 {
				memRatio = clusters[0].MemRatio
			}
			totalClusterRunningCpu, totalClusterRunningMem, err := service2.GetClusterRunningCpuAndMem(clusterGroup.SerialId, clusterGroup.AppId, clusterGroup.Region, memRatio)
			if err != nil {
				return err
			}
			totalCpu := float32(clusterGroup.CuNum)
			totalMem := float32(clusterGroup.CuNum) * float32(memRatio)

			var clusterRunningCpu float32 = 0.00
			var clusterRunningMem float32 = 0.00
			var clusterRunningCu float32 = 0.00
			var eksRunningCu float32 = 0.00
			var eksRunningCpu float32 = 0.00
			var eksRunningMem float32 = 0.00
			var freeCu float32 = 0
			var freeMem float32 = 0
			var freeCpu float32 = 0
			// 非eks
			if clusterGroup.CuNum != 0 {
				if totalClusterRunningCpu >= totalCpu {
					clusterRunningCpu = totalCpu
					eksRunningCpu = totalClusterRunningCpu - totalCpu
					clusterRunningCu = float32(clusterGroup.CuNum)
					freeCu = 0
				} else {
					freeCpu = totalCpu - totalClusterRunningCpu
					clusterRunningCpu = totalClusterRunningCpu
				}
				if totalClusterRunningMem >= totalMem {
					clusterRunningMem = totalMem
					eksRunningMem = totalClusterRunningMem - totalMem
					clusterRunningCu = float32(clusterGroup.CuNum)
					freeCu = 0
				} else {
					freeMem = totalMem - totalClusterRunningMem
					clusterRunningMem = totalClusterRunningMem
				}
				if freeCpu > 0 {
					freeCu = service2.GetFreeCuNumFromCpuMem(freeCpu, freeMem, memRatio)
				}
				eksRunningCu = service2.GetCuNumFromCpuMem(eksRunningCpu, eksRunningMem, memRatio)
				clusterRunningCu = service2.GetCuNumFromCpuMem(clusterRunningCpu, clusterRunningMem, memRatio)
			} else {
				clusterRunningCu = service2.GetCuNumFromCpuMem(totalClusterRunningCpu, totalClusterRunningMem, memRatio)
			}

			clusterPayMode, err := GetPayModeWithClusterGroup(clusterGroup, billingResourceMap)
			if err != nil {
				return err
			}
			clusterSetItem := c.buildClusterSetItemFromClusterGroup(clusterGroup, freeCu, clusterRunningCu, clusterPayMode)
			clusterSetItem.RunningCpu = service2.GetFloat2Dot(clusterRunningCpu)
			clusterSetItem.RunningMem = service2.GetFloat2Dot(clusterRunningMem)
			clusterSetItem.TotalCpu = totalCpu
			clusterSetItem.TotalMem = totalMem

			v, ok := billingResourceMap[clusterGroup.SerialId]
			/**
			 * 走了计费的 外部集群
			 */
			if ok {
				clusterSetItem.BillingResourceMode = v.BillingResourceMode
			}

			count, subCg, err := service2.ListClusterGroupByParentSerialId(clusterGroup.SerialId)
			if err != nil {
				return err
			}
			if count == 1 {
				if eksRunningCu > float32(subCg.CuNum) {
					eksRunningCu = float32(subCg.CuNum)
				}
				if eksRunningCpu > float32(subCg.CuNum) {
					eksRunningCpu = float32(subCg.CuNum)
				}
				if eksRunningMem > float32(subCg.CuNum)*float32(memRatio) {
					eksRunningMem = float32(subCg.CuNum) * float32(memRatio)
				}
				clusterSetItem.SubEks = &clusterModel.SubEks{
					SerialId:   subCg.SerialId,
					CuNum:      subCg.CuNum,
					Status:     subCg.Status,
					StatusDesc: GetStatusDescByStatus(subCg.Status),
					RunningCu:  eksRunningCu,
					RunningCpu: service2.GetFloat2Dot(eksRunningCpu),
					RunningMem: service2.GetFloat2Dot(eksRunningMem),
					TotalCpu:   float32(subCg.CuNum),
					TotalMem:   float32(subCg.CuNum) * float32(memRatio),
				}
			}

			return clusterSetItem
		}

		go func() {
			clusterSetItemChn <- fn(clusterGroup)
			wg.Done()
		}()
	}

	wg.Wait()
	close(clusterSetItemChn)

	for result := range clusterSetItemChn {
		if err, ok := result.(error); ok {
			return err
		} else if clusterSetItem, ok := result.(*model.ClusterGroupSetItem); ok {
			c.ItemSpaceClusters[itemSpaceId] = append(c.ItemSpaceClusters[itemSpaceId], clusterSetItem)
		}
	}

	sort.Slice(c.ItemSpaceClusters[itemSpaceId], func(i, j int) bool {
		return c.ItemSpaceClusters[itemSpaceId][i].CreateTime > c.ItemSpaceClusters[itemSpaceId][j].CreateTime
	})
	return nil
}

func GetPayModeWithClusterGroup(clusterGroup *table2.ClusterGroup, billingResourceMap map[string]*billingTable.BillingResource) (int, error) {
	v, ok := billingResourceMap[clusterGroup.SerialId]
	/**
	 * 走了计费的 外部集群
	 */
	if ok {
		return v.PayMode, nil
	} else {
		/**
		 * 内部客户区分 后付费和 预付费
		 */
		isEks, err := service_cluster.IsEks(clusterGroup.Id)
		if err != nil {
			return -1, err
		}
		if isEks {
			return billing.PayModePost, nil
		} else {
			return billing.PayModePrepaid, nil
		}
	}
}

func (c *describeItemSpaceContext) buildClusterSetItemFromClusterGroup(clusterGroup *table2.ClusterGroup, freeCu float32, clusterRunningCu float32, clusterPayMode int) *model.ClusterGroupSetItem {
	clusterSetItem := &model.ClusterGroupSetItem{}
	clusterSetItem.ClusterId = clusterGroup.SerialId
	clusterSetItem.Name = clusterGroup.Name
	clusterSetItem.Region = clusterGroup.Region
	clusterSetItem.Zone = clusterGroup.Zone
	clusterSetItem.AppId = clusterGroup.AppId
	clusterSetItem.OwnerUin = clusterGroup.OwnerUin
	clusterSetItem.CreatorUin = clusterGroup.CreatorUin
	clusterSetItem.CuNum = clusterGroup.CuNum
	clusterSetItem.CuMem = clusterGroup.CuMem
	clusterSetItem.Status = clusterGroup.Status
	clusterSetItem.StatusDesc = GetStatusDescByStatus(clusterGroup.Status)
	clusterSetItem.CreateTime = service2.SwitchDefaultTime(clusterGroup.CreateTime)
	clusterSetItem.UpdateTime = service2.SwitchDefaultTime(clusterGroup.UpdateTime)
	// 如果集群处于正在运行和初始化状态，集群详情中的时间返回-，如果是删除状态，则返回删除时间
	switch clusterGroup.Status {
	case constants.CLUSTER_GROUP_STATUS_CREATING, constants.CLUSTER_GROUP_STATUS_RUNNING:
		clusterSetItem.UpdateTime = "-"
	}
	clusterSetItem.Remark = clusterGroup.Remark
	clusterSetItem.NetEnvironmentType = clusterGroup.NetEnvironmentType
	clusterSetItem.FreeCuNum = int16(freeCu)
	clusterSetItem.FreeCu = freeCu
	clusterSetItem.RunningCu = clusterRunningCu
	clusterSetItem.PayMode = clusterPayMode
	return clusterSetItem
}

func (c *describeItemSpaceContext) buildAdministor(value string) *model2.RoleAuth {
	role := &model2.RoleAuth{}
	administrator := c.AdministersInfo[value]
	role.AppId = administrator.AppId
	role.OwnerUin = administrator.OwnerUin
	role.CreatorUin = administrator.CreatorUin
	role.AuthSubAccountUin = administrator.AuthSubAccountUin
	role.CreateTime = administrator.CreateTime
	role.UpdateTime = administrator.UpdateTime
	role.Status = constants.ROLE_AUTHO_PERMISSION_SUPER_OWNER
	return role
}

func (c *describeItemSpaceContext) findJobsCount() error {
	c.JobsCount = make(map[int64]int64, 0)
	for _, itemSpaceId := range c.itemSpaceIds {
		count, err := GetJobsTotalCount(itemSpaceId)
		if err != nil {
			return err
		}
		c.JobsCount[itemSpaceId] = int64(count)
	}
	return nil
}

func GetStatusDescByStatus(status int8) string {
	switch status {
	case constants.CLUSTER_GROUP_STATUS_CREATING:
		return "creating" // 创建中
	case constants.CLUSTER_GROUP_STATUS_RUNNING:
		return "running" // 运行中
	case constants.CLUSTER_GROUP_STATUS_SCALE_PROGRESS:
		return "scaling"
	case constants.CLUSTER_GROUP_STATUS_ISOLATED:
		return "isolated"
	case constants.CLUSTER_GROUP_STATUS_SCALEDOWN_PROGRESS:
		return "scale downing"

	case constants.CLUSTER_GROUP_STATUS_UPGRADE:
		return "upgrade"

	case constants.CLUSTER_GROUP_STATUS_RECOVERING:
		return "recovering"

	default:
		return "unknown"
	}
}

func GetJobsTotalCount(itemSpaceId int64) (count int, err error) {
	errorcode.DefaultDeferHandler(&err)
	sql := "SELECT j.* FROM Job j"
	cond := dao.NewCondition()
	cond.Ne("j.Status", constants.JOB_STATUS_DELETE)
	cond.Eq("j.ItemSpaceId", itemSpaceId)
	where, args := cond.GetWhere()
	sql += where

	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Exception occurs when Get job count from db, with errors:%+v", err)
		return 0, err
	}

	return len(data), nil
}
