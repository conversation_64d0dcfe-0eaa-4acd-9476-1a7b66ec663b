package item_space

import (
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/profile"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	test "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/test"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	wedata "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/wedata"
)

func DescribeItemUsersAll(req *test.DescribeProjectUsersReq) (string, string, interface{}) {
	var OwnerUin = req.ProjectOwnerUin
	var CreateUin = req.ProjectOwnerUin
	ProjectId, err := strconv.ParseInt(req.ProjectId, 10, 64)
	if err != nil {
		logger.Errorf("%d:ProjectId can not to int64 error: %+v", req.ProjectId, err)
		return controller.UnsupportedOperation, "ProjectId can not to int64 error", nil
	}
	if req.ProjectOwnerUin == "" {
		itemTable, err := GetItemSpaceByProjectId(0, req.Region, ProjectId)
		if err != nil {
			logger.Errorf("%s: GetItemSpaceByProjectId error: %+v", req.RequestId, err)
			return controller.UnsupportedOperation, "GetItemSpaceByProjectId error", nil
		}
		if itemTable == nil {
			return controller.InternalError, "Project is empty", nil
		}
		OwnerUin = itemTable.OwnerUin
		CreateUin = itemTable.CreatorUin
	}

	pRoles, err := DescribeProjectUsersAll(req.Region, OwnerUin, CreateUin, ProjectId)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}
	return controller.OK, "", test.DescribeProjectUsersResp{PRoles: pRoles}
}

func DescribeProjectUsersAll(Region, OwnerUin, CreatorUin string, ProjectId int64) (pRoles []*wedata.ProjectUserRole, err error) {
	secretId, secretKey, token, pass, err := service.StsAssumeRole(OwnerUin, CreatorUin, Region)
	if err != nil {
		return nil, err
	}
	if !pass {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode_StsAssumeRoleNoAccess, "StsAssumeRole not pass", nil)
	}
	credential := common.NewTokenCredential(secretId, secretKey, token)
	request := wedata.NewDescribeProjectUsersAllRequest()
	StrProjectId := strconv.FormatInt(ProjectId, 10)
	request.ProjectId = &StrProjectId

	prof := profile.NewClientProfile()
	// 上海自动驾驶云专区走内网
	if Region == constants.AP_SHANGHAI_ADC {
		prof.HttpProfile.Endpoint = "wedata.internal.tencentcloudapi.com"
	}

	client, err := wedata.NewClient(credential, Region, prof)
	if err != nil {
		logger.Errorf("describeProjectUsersAll -> wedata.NewClient with error :[%v]", err)
		return nil, err
	}
	response, err := client.DescribeProjectUsersAll(request)
	if err != nil {
		logger.Errorf("describeProjectUsersAll -> DescribeProjectUsersAll with error :[%v]", err)
		return nil, err
	}
	return response.Response.Data, err
}

func DescribeProject(Region, OwnerUin, CreatorUin string, ProjectId int64) (project *wedata.Project, err error) {
	secretId, secretKey, token, pass, err := service.StsAssumeRole(OwnerUin, CreatorUin, Region)
	if err != nil {
		return nil, err
	}
	if !pass {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode_StsAssumeRoleNoAccess, "StsAssumeRole not pass", nil)
	}
	credential := common.NewTokenCredential(secretId, secretKey, token)
	request := wedata.NewDescribeProjectRequest()
	StrProjectId := strconv.FormatInt(ProjectId, 10)
	request.ProjectId = &StrProjectId

	prof := profile.NewClientProfile()
	// 上海自动驾驶云专区走内网
	if Region == constants.AP_SHANGHAI_ADC {
		prof.HttpProfile.Endpoint = "wedata.internal.tencentcloudapi.com"
	}

	client, err := wedata.NewClient(credential, Region, prof)
	if err != nil {
		logger.Errorf("describeProjectUsersAll -> wedata.NewClient with error :[%v]", err)
		return nil, err
	}
	response, err := client.DescribeProject(request)
	if err != nil {
		logger.Errorf("describeProject by ProjectId: %s -> DescribeProject with error :[%v]", ProjectId, err)
		return nil, err
	}
	return response.Response.Data, err
}
