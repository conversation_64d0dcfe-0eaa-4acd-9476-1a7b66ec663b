package item_space

import (
	"errors"
	"fmt"
	"reflect"
	"sort"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service3 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/item_space"
	model2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/item_space"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
)

/**
 * Created by <PERSON> on 2021/12/16 3:16 下午.
 * At tencent
 */

func DoModifyCorrelation(req *model.ModifyCorrelationReq) (string, string, interface{}) {
	logger.Debugf("%s: DoModifyCorrelation API called by AppId %d", req.RequestId, req.AppId)

	// 限制检测
	overLimit, msg, err := overLimit(req.AppId, constants.CLUSTERBOUNDSPACE, int64(len(req.ItemClusters)), constants.CLUSTERBOUNDSPACENUM)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}
	if overLimit {
		errMsg := fmt.Sprintf("ItemSpace %s", msg)
		err = errorcode.LimitExceededCode.ReplaceDesc(errMsg)
		return errorcode.GetCodeReturn(err, nil)
	}

	// 绑定
	txManager := service3.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		// tx.SaveObject(itemSpacesClusters, "ItemSpacesClusters")

		return nil
	}).Close()
	return controller.OK, controller.NULL, nil
}

// DoCreateCorrelation 绑定空间和集群
func DoCreateCorrelation(req *model.CreateCorrelationReq) (string, string, interface{}) {

	// 0. Print RequestId
	logger.Debugf("%s: DoCreateCorrelation API called by AppId %d", req.RequestId, req.AppId)

	rsp := &model.CreateCorrelationRsp{Create: false}
	item, err := service2.ItemSpaceIdToSerialId(req.WorkSpaceId, constants.ITEM_SPACE_STATUS_USEABLE, req.AppId)
	if err != nil {
		logger.Errorf("%s: Query  ItemSpaces error: %+v", req.RequestId, err)
		return controller.InternalError, err.Error(), rsp
	}
	// 检测空间和集群是否绑定
	correlations, err := QueryCorrelation([]string{req.WorkSpaceId}, req.ClusterGroupSerialId, false, req.AppId)
	if err != nil {
		logger.Errorf("%s: Query  ItemSpacesClusters error: %+v", req.RequestId, err)
		return controller.InternalError, err.Error(), rsp
	}

	// 限额检测
	exist := make([]string, 0)
	var countNum = 0
	for _, c := range correlations {
		if c.Status == constants.ITEM_SPACE_CLUSTERGROUPS_STATUS_USEABLE {
			countNum += 1
		}
		exist = append(exist, c.ClusterGroupSerialId)
	}
	overLimit, msg, err := overLimit(req.AppId, constants.CLUSTERBOUNDSPACE, int64(countNum), constants.CLUSTERBOUNDSPACENUM)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}
	if overLimit {
		errMsg := fmt.Sprintf("ItemSpace %s", msg)
		err = errorcode.LimitExceededCode_ClusterAssociatedWorkSpaceLimitExceeded.ReplaceDesc(errMsg)
		return errorcode.GetCodeReturn(err, nil)
	}

	for _, correlation := range correlations {
		// 更新关系绑定
		if correlation.Status == constants.ITEM_SPACE_CLUSTERGROUPS__DELETED {
			err := updateItemSpacesClusters(correlation.ClusterGroupId, item.Id, constants.ITEM_SPACE_CLUSTERGROUPS_STATUS_USEABLE)
			if err != nil {
				logger.Errorf("%s: Update  ItemSpacesClusters  error: %+v", req.RequestId, err)
				return controller.InternalError, err.Error(), rsp
			}

		}
	}
	for _, serialId := range req.ClusterGroupSerialId {

		if ok, _ := Contain(serialId, exist); ok {
			continue
		}

		// 查找 ClusterGroupId
		var statusList = []int{
			constants.CLUSTER_GROUP_STATUS_CREATING,
			constants.CLUSTER_GROUP_STATUS_RUNNING,
			constants.CLUSTER_GROUP_STATUS_INIT_PROGRESS,
			constants.CLUSTER_GROUP_STATUS_SCALE_PROGRESS,
			constants.CLUSTER_GROUP_STATUS_ISOLATED,
			constants.CLUSTER_GROUP_STATUS_SCALEDOWN_PROGRESS,
			constants.CLUSTER_GROUP_STATUS_UPGRADE,
			constants.CLUSTER_GROUP_STATUS_DELETED,
			constants.CLUSTER_GROUP_STATUS_DELETING,
			constants.CLUSTER_GROUP_STATUS_RECOVERING,
		}
		clusterGroup, err := service2.ListClusterGroups(&service2.ListClusterGroupsParam{
			AppId:        int32(req.AppId),
			Regions:      nil,
			Zone:         "",
			ClusterType:  []int8{constants.CLUSTER_GROUP_TYPE_PRIVATE},
			ClusterNames: nil,
			IsVagueNames: false,
			SerialIds:    []string{serialId},
			Offset:       0,
			Limit:        0,
			OrderType:    0,
			StatusList:   statusList,
		})
		if err != nil {
			logger.Errorf("%s: Query  ClusterGroup  error: %+v", req.RequestId, err)
			return controller.InternalError, err.Error(), rsp
		}

		if len(clusterGroup) == 0 {
			err := errorcode.InvalidParameterCode.ReplaceDesc("Can not find this Cluster")
			return errorcode.GetCodeReturn(err, rsp)
		}

		clusters, err := BuildItemSpacesClusters(req.AppId, req.Region, clusterGroup[0].Id, item.Id)
		err = SaveTableItemSpacesClusters(clusters)
		if err != nil {
			logger.Errorf("%s: Save  ItemSpacesClusters  error: %+v", req.RequestId, err)
			return controller.InternalError, err.Error(), rsp
		}
	}

	rsp.Create = true
	return controller.OK, controller.NULL, rsp

}

// Contain 包含判断
func Contain(obj interface{}, target interface{}) (bool, error) {
	targetValue := reflect.ValueOf(target)
	switch reflect.TypeOf(target).Kind() {
	case reflect.Slice, reflect.Array:
		for i := 0; i < targetValue.Len(); i++ {
			if targetValue.Index(i).Interface() == obj {
				return true, nil
			}
		}
	case reflect.Map:
		if targetValue.MapIndex(reflect.ValueOf(obj)).IsValid() {
			return true, nil
		}
	}

	return false, errors.New("not in array")
}

// DoDescribeCorrelation 展示空间 集群关系列表
func DoDescribeCorrelation(req *model.DescribeCorrelationReq) (string, string, interface{}) {
	// 0. Print RequestId
	logger.Debugf("%s: DoDescribeCorrelation API called by AppId %d", req.RequestId, req.AppId)
	rsp := &model.DescribeCorrelationRsp{}
	// 检测关联是否绑定
	correlation, err := QueryCorrelation([]string{req.WorkSpaceId}, req.ClusterGroupSerialId, true, req.AppId)
	if err != nil {
		logger.Errorf("%s: queryCorrelation Check error: %+v", req.RequestId, err)
		return controller.InternalError, err.Error(), rsp
	}
	if correlation != nil {
		sort.Slice(correlation, func(i, j int) bool {
			return correlation[i].WorkSpaceName > correlation[j].WorkSpaceName
		})
		rsp.ItemSpacesClusters = correlation
	}
	return controller.OK, controller.NULL, rsp
}

// DoDescribeUnboundItemSpaces 查询集群未绑定的空间
func DoDescribeUnboundItemSpaces(req *model.DescribeUnboundItemSpacesReq) (string, string, interface{}) {
	logger.Debugf("%s: DoDescribeUnboundClusters API called by AppId %d", req.RequestId, req.AppId)
	rsp := &model.DescribeUnboundItemSpacesRsp{}
	rsp.WorkSpacesUnBound = make([]*table.UnBoundItemSpaces, 0)
	correlation, err := queryUnCorrelationItemSpaces(req)
	if err != nil {
		logger.Errorf("%s: queryCorrelation Check error: %+v", req.RequestId, err)
		return controller.InternalError, err.Error(), rsp
	}
	if correlation != nil {
		sort.Slice(correlation, func(i, j int) bool {
			return correlation[i].WorkSpaceName > correlation[j].WorkSpaceName
		})
		rsp.WorkSpacesUnBound = correlation
	}
	return controller.OK, controller.NULL, rsp
}

// DoDeleteCorrelation 解除空间和集群绑定
func DoDeleteCorrelation(req *model.DeleteCorrelationReq) (string, string, interface{}) {
	rsp := &model.DeleteCorrelationRsp{Delete: false, UnprocessedJobsNum: 0}

	item, err := service2.ItemSpaceIdToSerialId(req.WorkSpaceId, constants.ITEM_SPACE_STATUS_USEABLE, req.AppId)
	if err != nil {
		logger.Errorf("%s: Query  ItemSpaces error: %+v", req.RequestId, err)
		return controller.InternalError, err.Error(), rsp
	}

	pass, unprocessedJobs, err := FindJobsbyClusterGroupSerialId(req, item.Id)
	if err != nil {
		logger.Errorf("%s: DoDeleteCorrelation QueryJobs  error: %+v", req.RequestId, err)
		return controller.InternalError, err.Error(), rsp
	}

	if !pass {
		rsp.UnprocessedJobsNum = int64(unprocessedJobs)
		return controller.OK, controller.NULL, rsp
	}

	// 查找 ClusterGroupId
	// 查找 ClusterGroupId
	var statusList = []int{
		constants.CLUSTER_GROUP_STATUS_CREATING,
		constants.CLUSTER_GROUP_STATUS_RUNNING,
		constants.CLUSTER_GROUP_STATUS_INIT_PROGRESS,
		constants.CLUSTER_GROUP_STATUS_SCALE_PROGRESS,
		constants.CLUSTER_GROUP_STATUS_ISOLATED,
		constants.CLUSTER_GROUP_STATUS_UPGRADE,
		constants.CLUSTER_GROUP_STATUS_SCALEDOWN_PROGRESS,
		constants.CLUSTER_GROUP_STATUS_DELETED,
		constants.CLUSTER_GROUP_STATUS_DELETING,
		constants.CLUSTER_GROUP_STATUS_RECOVERING,
	}

	clusterGroup, err := service2.ListClusterGroups(&service2.ListClusterGroupsParam{
		AppId:        int32(req.AppId),
		Regions:      nil,
		Zone:         "",
		ClusterType:  []int8{constants.CLUSTER_GROUP_TYPE_PRIVATE},
		ClusterNames: nil,
		IsVagueNames: false,
		SerialIds:    []string{req.ClusterGroupSerialId},
		Offset:       0,
		Limit:        0,
		OrderType:    0,
		StatusList:   statusList,
	})
	if err != nil {
		logger.Errorf("%s: Query  ClusterGroup  error: %+v", req.RequestId, err)
		return controller.InternalError, err.Error(), rsp
	}

	if len(clusterGroup) != 1 {
		err := errorcode.InternalErrorCode.ReplaceDesc("Logic error find error result on  clusterGroup by SerialId")
		return errorcode.GetCodeReturn(err, rsp)
	}

	err = updateItemSpacesClusters(clusterGroup[0].Id, item.Id, constants.ITEM_SPACE_CLUSTERGROUPS__DELETED)
	if err != nil {
		logger.Errorf("%s: Update  ItemSpacesClusters  error: %+v", req.RequestId, err)
		return controller.InternalError, err.Error(), rsp
	}
	rsp.Delete = true
	return controller.OK, controller.NULL, rsp
}

func QueryCorrelation(itemSpaceIds []string, clusterGroupSerialId []string, isUseable bool, appId int64) (tables []*table.ItemSpaceClusterItem, err error) {
	errorcode.DefaultDeferHandler(&err)
	sql := " SELECT a.`Status`,a.ClusterGroupId, b.SerialId as ClusterGroupSerialId,b.Name as ClusterName," +
		"c.SerialId as  WorkItemSpaceId ,c.ItemSpaceName as WorkSpaceName  FROM ItemSpacesClusters a INNER JOIN ClusterGroup b" +
		" ON a.ClusterGroupId = b.Id INNER JOIN ItemSpace c on c.Id = a.ItemSpaceId "

	cond := dao.NewCondition()
	if len(itemSpaceIds) > 0 {
		cond.In("c.SerialId", itemSpaceIds)
	}

	if len(clusterGroupSerialId) > 0 {
		cond.In("b.SerialId", clusterGroupSerialId)
	}

	if isUseable {
		cond.Ne("a.Status", constants.ITEM_SPACE_CLUSTERGROUPS__DELETED)
	}

	if appId > 0 {
		cond.Eq("a.AppId", appId)
	}

	cond.Ne("c.Status", constants.ITEM_SPACE_STATUS_DELETED)

	where, args := cond.GetWhere()
	sql += where

	txManager := service3.GetTxManager()
	_, datas, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to execute the sql:%s, with args:%+v, with errors:%+v", sql, args, err)
		return tables, err
	}
	if len(datas) == 0 {
		return tables, nil
	}

	t := make([]*table.ItemSpaceClusterItem, 0)
	for _, data := range datas {
		table := &table.ItemSpaceClusterItem{}
		err = util.ScanMapIntoStruct(table, data)
		if err != nil {
			return nil, errorcode.InternalErrorCode.NewWithErr(err)
		}
		t = append(t, table)
	}
	return t, nil
}

func queryUnCorrelationItemSpaces(req *model.DescribeUnboundItemSpacesReq) (tables []*table.UnBoundItemSpaces, err error) {
	errorcode.DefaultDeferHandler(&err)
	sql := " SELECT SerialId  as WorkSpaceId ,ItemSpaceName as WorkSpaceName FROM ItemSpace WHERE id not in (SELECT a.ItemSpaceId from  ItemSpacesClusters a INNER JOIN ClusterGroup b on a.ClusterGroupId = b.Id" +
		" WHERE b.SerialId = ? AND b.AppId = ? AND b.Region = ? AND a.`Status` != ? ) AND AppId = ?  AND Region = ? AND `Status` != ?"
	args := make([]interface{}, 0, 0)
	args = append(args, req.ClusterGroupSerialId)
	args = append(args, req.AppId)
	args = append(args, req.Region)
	args = append(args, constants.ITEM_SPACE_CLUSTERGROUPS__DELETED)
	args = append(args, req.AppId)
	args = append(args, req.Region)
	args = append(args, constants.ITEM_SPACE_STATUS_DELETED)

	txManager := service3.GetTxManager()
	_, datas, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to execute the sql:%s, with args:%+v, with errors:%+v", sql, args, err)
		return tables, err
	}
	if len(datas) == 0 {
		return tables, nil
	}

	t := make([]*table.UnBoundItemSpaces, 0)
	for _, data := range datas {
		table := &table.UnBoundItemSpaces{}
		err = util.ScanMapIntoStruct(table, data)
		if err != nil {
			return nil, errorcode.InternalErrorCode.NewWithErr(err)
		}
		t = append(t, table)
	}
	return t, nil
}

func BuildItemSpacesClusters(appid int64, region string, clusterGroupId int64, itemSpaceId int64) (*table.ItemSpacesClusters, error) {
	itemSpacesClusters := &table.ItemSpacesClusters{}
	itemSpacesClusters.AppId = appid
	itemSpacesClusters.Region = region
	itemSpacesClusters.ClusterGroupId = clusterGroupId
	itemSpacesClusters.ItemSpaceId = itemSpaceId
	itemSpacesClusters.CreateTime = util.GetCurrentTime()
	itemSpacesClusters.UpdateTime = itemSpacesClusters.CreateTime
	itemSpacesClusters.Status = constants.ITEM_SPACE_CLUSTERGROUPS_STATUS_USEABLE
	return itemSpacesClusters, nil
}

func SaveTableItemSpacesClusters(itemSpacesClusters *table.ItemSpacesClusters) (err error) {
	errorcode.DefaultDeferHandler(&err)
	txManager := service3.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.SaveObject(itemSpacesClusters, "ItemSpacesClusters")
		return nil
	}).Close()
	return err
}

func updateItemSpacesClusters(clusterGroupId int64, itemSpaceId int64, stat int8) (err error) {
	errorcode.DefaultDeferHandler(&err)
	sql := " UPDATE ItemSpacesClusters SET  `Status` = ? WHERE ItemSpaceId = ? AND ClusterGroupId = ?"

	args := make([]interface{}, 0)
	args = append(args, stat)
	args = append(args, itemSpaceId)
	args = append(args, clusterGroupId)

	txManager := service3.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		rowsAffected, err := tx.ExecuteSqlWithArgs(sql, args...).RowsAffected()
		if err != nil {
			return err
		} else if rowsAffected != 1 {
			return errors.New(fmt.Sprintf("Logic error? RowsAffected not 1, but %d when update ItemSpace", rowsAffected))
		}
		return nil
	}).Close()
	return nil
}

func FindJobsbyClusterGroupSerialId(req *model.DeleteCorrelationReq, itemSpacId int64) (pass bool, unprocessedJobs int, err error) {
	// 支持传入空字符串, 以忽略 region 过滤条件
	var regions []string
	if len(req.Region) == 0 {
		regions = nil
	} else {
		regions = []string{req.Region}
	}
	var statList = []int64{
		constants.JOB_STATUS_CREATE,
		constants.JOB_STATUS_INITIALIZED,
		constants.JOB_STATUS_PROGRESS,
		constants.JOB_STATUS_RUNNING,
		constants.JOB_STATUS_STOPPED,
		constants.JOB_STATUS_PAUSED,
		constants.JOB_STATUS_CONCERNING,
		constants.JOB_STATUS_FINISHED,
	}

	listJobQuery := model2.ListJobQuery{
		AppId:           int32(req.AppId),
		Regions:         regions,
		ClusterGroupIds: []string{req.ClusterGroupSerialId},
		ItemSpaceIds:    []int64{itemSpacId},
		Status:          statList,
		Offset:          0,
		Limit:           -1,
	}
	jobs, err := service2.ListJobs(&listJobQuery)
	if err != nil {
		return false, 0, err
	} else if len(jobs) >= 1 {
		return false, len(jobs), nil
	} else {
		return true, 0, nil
	}
}

func getQuota(AppId int64, typeKey string) (upper int64, err error) {
	cond := dao.NewCondition()
	cond.Eq("AppId", AppId)
	cond.Eq("ConfigurationKey", typeKey)
	where, args := cond.GetWhere()
	sql := "SELECT * FROM Quota " + where

	count, data, err := service3.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return
	}
	if count == 0 {
		return
	}

	tmp := &cquota{}
	err = util.ScanMapIntoStruct(tmp, data[0])
	if err != nil {
		err = errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		return
	}

	return tmp.ConfigurationValue, nil
}

type cquota struct {
	Id                 int64
	AppId              int64
	ConfigurationKey   string
	ConfigurationValue int64
}

func overLimit(AppId int64, typeKey string, currentCount int64, defaultQuota int64) (ok bool, msg string, err error) {
	if currentCount < defaultQuota {
		return false, "", nil
	}
	appIdQuota, err := getQuota(AppId, typeKey)
	if err != nil {
		return true, "", err
	}
	if currentCount > appIdQuota {
		return true, fmt.Sprintf("count [%d] over limit (%d)", currentCount, appIdQuota), nil
	}
	return false, "", nil
}
