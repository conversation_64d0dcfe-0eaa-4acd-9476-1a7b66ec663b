package item_space

import (
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/item_space"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
)

/**
 * Created by <PERSON> on 2021/11/15 2:13 下午.
 * At tencent
 */
func DoModifyItemSpace(req *model.ModifyItemSpaceReq) (string, string, interface{}) {
	// 0. Print RequestId
	logger.Infof("%s: CreateItemSpace API called by AppId %d", req.RequestId, req.AppId)

	// 1. 查找命名空间
	ItemSpaceTable, err := GetItemSpaceByItemSpaceId(req.WorkSpaceId, req.AppId)
	if err != nil {
		logger.Errorf("%s: GetItemSpaceByItemSpaceId error: %+v", req.RequestId, err)
		return controller.UnsupportedOperation, "GetItemSpaceByItemSpaceId error", nil
	}

	changeItem := &ModifyItem{}

	// 2.修改名称
	if len(req.WorkSpaceName) > 0 && req.WorkSpaceName != ItemSpaceTable.ItemSpaceName {

		// 命名规范
		err := service2.CheckNameValidity(req.WorkSpaceName)
		if err != nil {
			logger.Errorf("%s: Failed to CheckNameValidity, with error: %+v", req.RequestId, err)
			return controller.InvalidParameterValue_ItemSpaceName, err.Error(), nil
		}

		// 检测重名 &&
		isPass, errCodeStr, msg := CheckNameExisted(req.AppId, req.WorkSpaceName, ItemSpaceTable.Region)
		if !isPass {
			logger.Errorf("%s: %s", req.RequestId, msg)
			return errCodeStr, msg, nil
		}

		ItemSpaceTable.ItemSpaceName = req.WorkSpaceName
		changeItem.Name = true
	}

	// 3.修改 Descriptiion
	ItemSpaceTable.Description = req.Description
	changeItem.Description = true

	// 4.修改
	if !changeItem.Name && !changeItem.Description {
		return controller.OK, "Nothing To Change", nil
	}

	err = UpdateItemSpaceTable(changeItem, ItemSpaceTable)
	if err != nil {
		logger.Errorf("%s: Failed to Update ItemSpace, with error: %+v", req.RequestId, err)
		return controller.InternalError, err.Error(), nil
	}
	return controller.OK, controller.NULL, nil
}
