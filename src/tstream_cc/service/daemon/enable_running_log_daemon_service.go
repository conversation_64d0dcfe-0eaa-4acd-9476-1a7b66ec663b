package daemon

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	v1 "git.woa.com/tke/logconfig/pkg/apis/cls.cloud.tencent.com/v1"
	logconfig "git.woa.com/tke/logconfig/pkg/generated/clientset/versioned"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/log"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cls"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
)

const (
	WAITING_TIMES = 10
)

var (
	runningLogDaemon *EnableRunningLogDaemon
)

type EnableRunningLogRequest struct {
	RequestId string
	Uin       string
	Region    string
	LogSetId  string
	TopicId   string
	Clusters  []*log.TkeCluster
	CrdConf   *log.JobRunningLogConfCrd
}

func (o *EnableRunningLogRequest) String() string {
	if b, err := json.Marshal(o); err != nil {
		return ""
	} else {
		return string(b)
	}
}

type EnableRunningLogDaemon struct {
	RequestQueue chan *EnableRunningLogRequest
}

func (o *EnableRunningLogDaemon) disposeCluster(secretId, secretKey, token string, request *EnableRunningLogRequest, cluster *log.TkeCluster, client *logconfig.Clientset, append bool, lcTpl string) (err error) {
	k8sService := k8s.GetK8sService()

	newLc := &v1.LogConfig{}
	data := &struct {
		Name           string
		BeginningRegex string
		TopicId        string
	}{
		Name:           request.CrdConf.Crd.Name,
		BeginningRegex: request.CrdConf.Crd.BeginningRegex,
		TopicId:        request.TopicId,
	}

	if err := k8sService.RenderResourceConfig(lcTpl, data, newLc); err != nil {
		return err
	}
	if append {
		//newLc.Spec.ClsDetail.Mode = "append"
	}
	if _, err = k8sService.ApplyLogConfig(client, newLc); err != nil {
		return err
	}

	return o.waitLcTakeEffect(secretId, secretKey, token, request, cluster, client, true)
}

func (o *EnableRunningLogDaemon) deleteLc(client *logconfig.Clientset, crdConf *log.JobRunningLogConfCrd) (deleted bool, err error) {
	lcConfig := client.ClsV1().LogConfigs()

	if err := lcConfig.Delete(context.TODO(), crdConf.Crd.Name, metav1.DeleteOptions{}); err != nil {
		if !errors.IsNotFound(err) {
			return false, err
		}
		return false, nil
	} else {
		return true, nil
	}
}

func (o *EnableRunningLogDaemon) waitLcTakeEffect(secretId, secretKey, token string, request *EnableRunningLogRequest, cluster *log.TkeCluster, client *logconfig.Clientset, add bool) (err error) {
	requestId := request.RequestId

	check := func(machieGroups []*log.MachineGroup, groupName string) bool {
		if len(machieGroups) == 0 {
			return !add
		}
		for _, g := range machieGroups {
			if g.GroupName == groupName {
				return add
			}
		}
		return !add
	}

	// add 取值：
	//  - false: 等待机器组已删除, tke的crd实现有个bug，删除一个lc即可清空topic所有的机器组
	//    这里的判断兼容了后续crd修复bug的情况
	for i := 0; i < WAITING_TIMES; i++ {
		//machineGroups, err := cls.ListTopicMachineGroups(secretId, secretKey, token, cluster.Region, request.TopicId)
		adapter := &cls.CloudApiAdapter{}
		machineGroups, err := adapter.ListTopicMachineGroups(secretId, secretKey, token, cluster.Region, request.TopicId)
		if err != nil {
			logger.Errorf("[%s] %v ListTopicMachineGroups error %v", requestId, cluster.UniqClusterId, err)
			continue
		}
		b, _ := json.Marshal(machineGroups)
		logger.Infof("[%s] find machine groups %s for topic %s, cluster %s", requestId, string(b), request.TopicId, cluster.UniqClusterId)

		if check(machineGroups.MachineGroups, cluster.UniqClusterId) {
			logger.Infof("[%s] wait success for cluster %s, add = %v", requestId, cluster.UniqClusterId, add)
			return nil
		}
		logger.Infof("[%s] wait not complete, sleep...", requestId)
		time.Sleep(time.Second)
	}
	msg := fmt.Sprintf("[%s] wait fail for cluster %s after %d times, add = %v", requestId, cluster.UniqClusterId, WAITING_TIMES, add)
	logger.Infof(msg)
	return errorcode.NewStackError(errorcode.InternalErrorCode, msg, nil)
}

func (o *EnableRunningLogDaemon) disposeWithResult(request *EnableRunningLogRequest) (result *log.ModifyRunningLogShipperRsp) {
	clusters := request.Clusters
	result = &log.ModifyRunningLogShipperRsp{ClusterResults: make([]*log.ClusterResult, 0, len(clusters))}

	requestId := request.RequestId
	logger.Infof("[%s] dispose %v", requestId, request)

	if len(request.Clusters) == 0 {
		logger.Infof("[%s] empty clusters to dispose", requestId)
		return
	}
	region := request.Region
	for _, cluster := range clusters {
		result.ClusterResults = append(result.ClusterResults, &log.ClusterResult{
			ClusterId:    cluster.UniqClusterId,
			Success:      false,
			ErrorMessage: fmt.Sprintf("[%s] request appended", requestId),
		})
	}

	lcTpl, err := config.GetRainbowConfiguration(constants.CONF_RAINBOW_GROUP_TKE, constants.CONF_RAINBOW_KEY_OCEANUS_RUNNINGLOG_LC)
	if err != nil {
		errMsg := fmt.Sprintf("[%s] fail to get lc config template %v", requestId, err)
		logger.Errorf(errMsg)
		result.ClusterResults[0].ErrorMessage = errMsg // 设置一个即可
		return
	}

	secretId, secretKey, token, pass, err := service3.StsAssumeRole(request.Uin, request.Uin, region)
	if err != nil {
		errMsg := fmt.Sprintf("[%s] StsAssumeRole error %v", requestId, err)
		logger.Errorf(errMsg)
		result.ClusterResults[0].ErrorMessage = errMsg
		return
	}
	if !pass {
		errMsg := fmt.Sprintf("[%s] StsAssumeRole pass %v", requestId, pass)
		logger.Errorf(errMsg)
		result.ClusterResults[0].ErrorMessage = errMsg
		return
	}

	clients := make([]*logconfig.Clientset, 0, len(clusters))
	k8sService := k8s.GetK8sService()

	// 创建k8s集群client
	for i, cluster := range clusters {
		if len(cluster.KubeConfig) == 0 {
			errMsg := fmt.Sprintf("[%s] empty kubeConfig for cluster %v", requestId, cluster.UniqClusterId)
			logger.Errorf(errMsg)
			result.ClusterResults[i].ErrorMessage = errMsg

			clients = append(clients, nil)
			continue
		}
		if client, err := k8sService.NewLogConfigClient([]byte(cluster.KubeConfig)); err != nil {
			errMsg := fmt.Sprintf("[%s] %v NewLogConfigClient error %v", requestId, cluster.UniqClusterId, err)
			logger.Errorf(errMsg)
			result.ClusterResults[i].ErrorMessage = errMsg

			clients = append(clients, nil)
		} else {
			clients = append(clients, client)
		}
	}

	// 删除已存在的crd实例lc
	for i, cluster := range clusters {
		client := clients[i]
		if client == nil {
			continue
		}
		logger.Infof("[%s] delete lc for cluster %v", requestId, cluster.UniqClusterId)
		deleted, err := o.deleteLc(client, request.CrdConf)
		if err != nil {
			logger.Errorf("[%s] %v deleteLc error %v", requestId, cluster.UniqClusterId, err)
			continue
		}

		logger.Infof("[%s] delete lc success for cluster %s, deleted %v", requestId, cluster.UniqClusterId, deleted)
	}
	// tke对删除lc的处理只是关闭采集，topic关联的机器组并没有变化，删除之
	err = cls.ModifyTopicMachineGroups(secretId, secretKey, token, region, request.TopicId, nil)
	if err != nil {
		errMsg := fmt.Sprintf("[%s] ModifyTopicMachineGroups error %v", requestId, err)
		logger.Errorf(errMsg)
		result.ClusterResults[0].ErrorMessage = errMsg
		return
	}

	// 创建新的crd实例lc
	appendMode := false
	for i, cluster := range clusters {
		client := clients[i]
		if client == nil {
			continue
		}
		if err := o.disposeCluster(secretId, secretKey, token, request, cluster, client, appendMode, lcTpl); err != nil {
			errMsg := fmt.Sprintf("[%s] dispose cluster failed %s, %v", requestId, cluster.UniqClusterId, err)
			logger.Errorf(errMsg)
			result.ClusterResults[i].ErrorMessage = errMsg
		} else {
			appendMode = true
			logger.Infof("[%s] dispose cluster successfully %s", requestId, cluster.UniqClusterId)
			result.ClusterResults[i].ErrorMessage = ""
			result.ClusterResults[i].Success = true
		}
	}
	return
}

func (o *EnableRunningLogDaemon) dispose(request *EnableRunningLogRequest) {
	result := o.disposeWithResult(request)
	b, _ := json.Marshal(result)

	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.ExecuteSqlWithArgs("update JobRunningLogTopic set Remark=? where OwnerUin=? and Region=? and LogSetId=? and LogTopicId=?",
			string(b), request.Uin, request.Region, request.LogSetId, request.TopicId)
		return nil
	}).Close()
}

func (o *EnableRunningLogDaemon) Start() {
	logger.Infof("EnableRunningLogDaemon starting...")

	go func() {
		for request := range o.RequestQueue {
			go o.dispose(request)
		}

		logger.Infof("EnableRunningLogDaemon stopped")
	}()
}

func (o *EnableRunningLogDaemon) AppendTask(request *EnableRunningLogRequest) {
	if request == nil {
		return
	}
	o.RequestQueue <- request
}

func (o *EnableRunningLogDaemon) Stop() {
	logger.Infof("try to stop EnableRunningLogDaemon")
	close(o.RequestQueue)
}

func NewEnableRunningLogDaemon() *EnableRunningLogDaemon {
	return &EnableRunningLogDaemon{
		RequestQueue: make(chan *EnableRunningLogRequest, 10),
	}
}

func GetEnableRunningLogDaemon() *EnableRunningLogDaemon {
	if runningLogDaemon == nil {
		runningLogDaemon = NewEnableRunningLogDaemon()
	}
	return runningLogDaemon
}
