package service

import (
	"context"
	"fmt"
	"github.com/tencentyun/cos-go-sdk-v5"
	"net/http"
	"runtime/debug"
	"sort"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service2 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	service4 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/watchdog"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	clusterService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	cosHandler "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cos/handler"
	"time"
)

func DoDeleteJobSavepoint(req *model.DeleteJobSavepointReq) (string, string, *model.DeleteJobSavepointRsp) {

	if len(req.SerialIds) < 1 {
		msg := fmt.Sprintf("%s: InvalidParameterValue.SerialIds. No SerialIds specified", req.RequestId)
		logger.Error(msg)
		return controller.InvalidParameterValue, msg, nil
	}
	// 2. 对作业列表去重，如果size大于10，返回错误
	if len(req.SerialIds) > constants.OPERATION_BATCH_SIZE_UPPER_LIMIT {
		errMsg := fmt.Sprintf("At least Delete %d Savepoints one time", constants.OPERATION_BATCH_SIZE_UPPER_LIMIT)
		logger.Errorf("%s: Failed to delete jobs. %s", req.RequestId, errMsg)
		return controller.UnsupportedOperation, errMsg, nil
	}

	// 1. 检查作业是否存在
	job, err := service.WhetherJobExists(int32(req.AppId), req.Region, req.JobId)
	if err != nil {
		msg := fmt.Sprintf("%s Failed to list job ,err:%s", req.RequestId, err.Error())
		logger.Error(err)
		return controller.ResourceNotFound_JobId, msg, &model.DeleteJobSavepointRsp{}
	}

	// 鉴权
	err = auth.InnerAuthById(req.WorkSpaceId, req.IsSupOwner, job.ItemSpaceId, req.AppId, req.SubAccountUin, req.Action, false)
	if err != nil {
		logger.Errorf("%s: User  uin %s is NOT authenticated, refuse to serve", req.RequestId, req.Uin)
		errCode := errorcode.GetCode(err)
		return errCode.GetCodeStr(), errCode.GetCodeDesc(), nil
	}

	recordTypes := make([]int8, 0)
	recordTypes = append(recordTypes, constants.RECORD_TYPE_SAVEPOINT)
	recordTypes = append(recordTypes, constants.RECORD_TYPE_CANCEL_WITH_SAVEPOINT)
	savepoints, err := service2.GetSavepointsByJobId(job.Id, 0, 0, recordTypes, req.SerialIds)
	if err != nil {
		logger.Errorf("%s: GetSavepointsByJobId error, %v", req.RequestId, err)
		errCode := errorcode.GetCode(err)
		return errCode.GetCodeStr(), errCode.GetCodeDesc(), nil
	}
	if len(savepoints) < 1 {
		logger.Debugf("no savepoints")
		return controller.OK, controller.NULL, &model.DeleteJobSavepointRsp{}
	}

	cluster, err := clusterService.GetClusterByJobId(job.SerialId)
	if err != nil {
		logger.Errorf("%s: Failed to GetClusterByJobId, with jobId:%d, with errors: %+v", req.RequestId, req.JobId, err)
	}

	delCOSObjectHandlerIns := &cosHandler.DelCosObjectHandler{}
	tmpSecretId, tmpSecretKey, token, pass, err := service.StsAssumeRole(req.Uin, req.SubAccountUin, req.Region)
	if err != nil {
		logger.Errorf("DoDeleteJobSavepoint StsAssumeRoleWithSubAccountUin with error %v", err)
		return controller.UnsupportedOperation, "DeleteCOSFile StsAssumeRoleWithSubAccountUin error", nil
	}
	if !pass {
		logger.Errorf("DeleteCOSFile StsAssumeRoleWithSubAccountUin not pass with param uin %s, subUin %s, region %s ", req.Uin, req.SubAccountUin, req.Region)
		return controller.UnsupportedOperation, "DeleteCOSFile StsAssumeRoleWithSubAccountUin not pass", nil
	}
	savepointIds := make([]int64, 0)
	for _, savepoint := range savepoints {
		if savepoint.Status != constants.SAVEPOINT_STATUS_ACTIVE {
			logger.Warningf("requestId : %s, jobId %s, savepoint %s status %d is not valid, skip",
				req.RequestId, req.JobId, savepoint.SerialId, savepoint.Status)
			continue
		}
		if savepoint.Path == "" {
			logger.Warningf("requestId : %s, jobId %s, savepoint %s path %s is not valid, skip",
				req.RequestId, req.JobId, savepoint.SerialId, savepoint.Path)
			continue
		}

		savepointPrefix := fmt.Sprintf("cosn://%s/%d/%s/%s", cluster.DefaultCOSBucket, job.AppId, job.CreatorUin, job.SerialId)
		if !strings.Contains(savepoint.Path, savepointPrefix) {
			logger.Warningf("requestId : %s, jobId %s, savepoint %s path %s is not valid, skip",
				req.RequestId, req.JobId, savepoint.SerialId, savepoint.Path)
			updateSavepointStatus(savepoint.Id)
			continue
		}
		if !strings.Contains(savepoint.Path, "flink-savepoints") {
			logger.Warningf("requestId : %s, jobId %s, savepoint %s path is not valid, do not contains flink-savepoints, skip",
				req.RequestId, req.JobId, savepoint.SerialId)
			updateSavepointStatus(savepoint.Id)
			continue
		}
		path := GetSavepointPath(savepoint.Path, cluster.DefaultCOSBucket)
		logger.Debugf("GetSavepointPath(savepoint.Path, cluster.DefaultCOSBucket) %s %s %s", savepoint.Path, cluster.DefaultCOSBucket, path)
		request := cosHandler.BuildDelCosObjectReq(cluster.DefaultCOSBucket, req.Region, path, tmpSecretId, tmpSecretKey, token)
		delCOSObjectHandlerIns.Process(request)
		savepointIds = append(savepointIds, savepoint.Id)
		updateSavepointStatus(savepoint.Id)
	}
	return controller.OK, controller.NULL, &model.DeleteJobSavepointRsp{}
}

// updateSavepointStatus 删除Savepoint后更新Savepoint表中status状态
func updateSavepointStatus(id int64) {
	txManager := service4.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.ExecuteSqlWithArgs("UPDATE Savepoint set status = ?  WHERE id = ? ", constants.SAVEPOINT_STATUS_EXPIRED, id)
		return nil
	}).Close()
}

// cosn://autotest-gz-bucket-1257058945/1257058945/100006386216/cql-7lhu9v3e/1/flink-savepoints/savepoint-000000-85bd8268b9fa
func GetSavepointPath(fullPath string, defaultCOSBucket string) string {
	if !strings.Contains(fullPath, defaultCOSBucket) {
		return fullPath
	}
	return strings.Split(fullPath, defaultCOSBucket)[1][1:]
}

func DoDescribeJobSavepoint(req *model.DescribeJobSavepointReq) (string, string, *model.DescribeJobSavepointRsp) {
	// 1. 检查作业是否存在
	job, err := service.WhetherJobExists(int32(req.AppId), req.Region, req.JobId)
	if err != nil {
		msg := fmt.Sprintf("%s Failed to list job ,err:%s", req.RequestId, err.Error())
		logger.Error(err)
		return controller.ResourceNotFound_JobId, msg, &model.DescribeJobSavepointRsp{}
	}

	// 鉴权
	err = auth.InnerAuthById(req.WorkSpaceId, req.IsSupOwner, job.ItemSpaceId, req.AppId, req.SubAccountUin, req.Action, false)
	if err != nil {
		logger.Errorf("%s: User  uin %s is NOT authenticated, refuse to serve", req.RequestId, req.Uin)
		errCode := errorcode.GetCode(err)
		return errCode.GetCodeStr(), errCode.GetCodeDesc(), nil
	}

	//2.根据JobId返回Savepoint表
	savepoints, err := service2.GetSavepointsByJobId(job.Id, req.Limit, req.Offset, req.RecordTypes, nil)
	//logger.Info("Find the Savepoint")
	savepointLists := make([]*model.SavepointList, 0, 0)
	runningSavepointLists := make([]*model.SavepointList, 0, 0)
	JobInstanceIds := make([]int64, 0, 0)

	cs := make(chan string, len(savepoints))
	for _, v := range savepoints {
		var savepointList model.SavepointList
		savepointList.Status = v.Status
		savepointList.SerialId = v.SerialId
		savepointList.CreateTime = util.LocalTimeToTimestamp(v.CreateTime)
		savepointList.UpdateTime = util.LocalTimeToTimestamp(v.UpdateTime)
		savepointList.Path = v.Path
		savepointList.Size = v.Size
		savepointList.RecordType = v.RecordType
		savepointList.JobRuntimeId = v.JobRuntimeId
		savepointList.Description = v.Description
		savepointList.Timeout = v.Timeout
		savepointList.TimeConsuming = getTimeConsuming(v.Status, v.CreateTime, v.UpdateTime, v.Timeout)
		JobInstanceId := v.JobRuntimeId

		if v.Status == constants.SAVEPOINT_STATUS_IN_PROGRESS { //正在运行中的Savepoint
			if savepointList.TimeConsuming > savepointList.Timeout && v.RecordType != constants.RECORD_TYPE_CHECKPOINT { //超时的兜底操作
				_ = service2.UpdateTimeoutSavepoint(v.Id)
			}
			runningSavepointLists = append(runningSavepointLists, &savepointList)
		}

		go checkPathStatus(cs, &savepointList, req)

		JobInstanceIds = append(JobInstanceIds, JobInstanceId)
		savepointLists = append(savepointLists, &savepointList)
	}

	for len(cs) < len(savepoints) {
		time.Sleep(100 * time.Millisecond)
		logger.Infof("check path status cs: %v , savepoints: %v", len(cs), len(savepoints))
	}

	VersionIdList, err := service.GetJobInstanceByJobInstanceIds(JobInstanceIds)
	if err != nil {
		logger.Error(err)
	}
	totalNumber, err := service2.GetTotalSavepointsByJobId(job.Id, req.RecordTypes)
	if err != nil {
		logger.Error(err)
	}
	versionMap := make(map[int64]int64)
	for _, versionId := range VersionIdList {
		versionMap[versionId.Id] = versionId.VersionId
	}

	savepointListToShow := make([]*model.SavepointList, 0, 0)
	for _, v := range savepointLists {
		v.VersionId = versionMap[v.JobRuntimeId]
		savepointListToShow = append(savepointListToShow, v)
	}
	for _, v := range runningSavepointLists {
		v.VersionId = versionMap[v.JobRuntimeId]
	}

	describeJobSavepointRsp := &model.DescribeJobSavepointRsp{}
	describeJobSavepointRsp.Savepoint = getSort(savepointListToShow)
	describeJobSavepointRsp.RunningSavepoint = runningSavepointLists
	describeJobSavepointRsp.RunningTotalNumber = int64(len(runningSavepointLists))
	describeJobSavepointRsp.TotalNumber = int64(totalNumber)
	return controller.OK, controller.NULL, describeJobSavepointRsp
}

func checkPathStatus(c chan string, savepoint *model.SavepointList, req *model.DescribeJobSavepointReq) {
	//当发生异常时将状态置为不可用,并输出错误信息
	defer func() {
		c <- savepoint.SerialId
		if err := recover(); err != nil {
			logger.Errorf("exception when checkPathStatus! status is in progress SerialId:%v, err:%v, stack:%v",
				savepoint.SerialId, err, debug.Stack())
			savepoint.PathStatus = constants.SAVEPOINT_PATH_STATUS_UNAVAILABLE
			return
		}
	}()

	if savepoint.Status == constants.SAVEPOINT_STATUS_IN_PROGRESS || savepoint.Status == constants.SAVEPOINT_STATUS_EXPIRED {
		logger.Errorf("Failed to check savepoint! status is in progress SerialId:%v", savepoint.SerialId)
		savepoint.PathStatus = constants.SAVEPOINT_PATH_STATUS_UNAVAILABLE
		return
	}
	params, err := service.NewSavepointParam(savepoint.Path, req.Region)
	if err != nil {
		logger.Errorf("Failed to getSavepointParam with path %s err:%v", savepoint.Path, err)
		savepoint.PathStatus = constants.SAVEPOINT_PATH_STATUS_UNAVAILABLE
		return
	}
	client := initCosClient(params, req)
	if client == nil {
		logger.Errorf("Failed to create cos client!")
		savepoint.PathStatus = constants.SAVEPOINT_PATH_STATUS_UNAVAILABLE
		return
	}

	rsp, err := client.Object.Head(context.Background(), params.Path+constants.META_DATA_PATH, nil)
	if err != nil {
		logger.Errorf("Failed to check savepoint! cos return err:%v", err)
		if rsp == nil || rsp.StatusCode == 404 || rsp.StatusCode == 403 {
			//do nothing
		}
		savepoint.PathStatus = constants.SAVEPOINT_PATH_STATUS_UNAVAILABLE
		return
	}
	savepoint.PathStatus = constants.SAVEPOINT_PATH_STATUS_AVAILABLE
	return
}

func initCosClient(params *watchdog.SavepointParam, req *model.DescribeJobSavepointReq) *cos.Client {
	if nil == params {
		return nil
	}
	secretId, secretKey, token, err := service.GetResourceSecretIdAndKey(req.Uin, req.SubAccountUin, req.Region)
	if err != nil {
		logger.Errorf("Failed to check savepoint! getResourceSecretIdAndKey err:%v", err)
		return nil
	}

	url, err := cos.NewBucketURL(params.Bucket, params.Region, true)
	if err != nil {
		logger.Errorf("Failed to check savepoint! NewBucketURL err:%v", err)
		return nil
	}
	client := cos.NewClient(&cos.BaseURL{
		BucketURL: url,
	}, &http.Client{
		Transport: &cos.AuthorizationTransport{
			SecretID:     secretId,
			SecretKey:    secretKey,
			SessionToken: token,
		},
	})
	logger.Infof("init cos client, jobId: %s", req.JobId)
	return client
}

func getTimeConsuming(status int8, createTime string, updateTime string, timeout int64) int64 {
	if status == constants.SAVEPOINT_STATUS_IN_PROGRESS {
		return util.SubDate(createTime, util.GetCurrentTime())
	} else if status == constants.SAVEPOINT_STATUS_TIMEOUT {
		return timeout
	} else {
		t := util.SubDate(createTime, updateTime)
		if t <= 0 {
			t++
		}
		return t
	}
}

func getSort(wordFrequencies []*model.SavepointList) []*model.SavepointList {
	pl := make(PairList, len(wordFrequencies))
	i := 0
	for _, v := range wordFrequencies {
		pl[i] = v
		i++
	}
	//从小到大排序
	//sort.Sort(pl)
	//从大到小排序
	sort.Sort(sort.Reverse(pl))
	return pl
}

type PairList []*model.SavepointList

func (p PairList) Len() int { return len(p) }
func (p PairList) Less(i, j int) bool {
	if p[i].CreateTime == p[j].CreateTime {
		return p[i].SerialId < p[j].SerialId
	}
	return p[i].CreateTime < p[j].CreateTime
}
func (p PairList) Swap(i, j int) { p[i], p[j] = p[j], p[i] }
