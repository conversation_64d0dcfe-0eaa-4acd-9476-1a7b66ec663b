package service

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"

	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job"
	sql_model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/sql"
	service1 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/sql"
)

func DoJobsRunPreCheck(ctx context.Context, req *model.JobsRunPreCheckReq) (*model.JobsRunPreCheckRsp, error) {
	if req == nil {
		logger.Errorf("request param is not valid, req is nil.")
		return nil, errorcode.InternalErrorCode.New()
	}
	if req.WorkSpaceId == "" {
		logger.Errorf("request param is not valid, req.WorkSpaceId is nil.")
		return nil, errorcode.InvalidParameterValueCode.New()
	}
	itemSpaceId, err := auth.ItemSpaceTransfer(req.WorkSpaceId, req.AppId, req.RequestId)
	if err != nil {
		logger.Errorf("%s: User owner uin %s is NOT authenticated, refuse to serve", req.RequestId, req.Uin)
		return nil, err
	}

	//鉴权
	for _, value := range req.RunPreCheckJobItems {
		_, err := auth.InnerAuthJob(req.WorkSpaceId, req.IsSupOwner, value.JobId, req.AppId, req.SubAccountUin, req.Region, req.Action)
		if err != nil {
			logger.Errorf("%s: User  uin %s is NOT authenticated, refuse to serve", req.RequestId, req.Uin)
			return nil, err
		}
	}

	return jobsRunPreCheck(ctx, req, itemSpaceId)
}

func jobsRunPreCheck(ctx context.Context, req *model.JobsRunPreCheckReq, itemSpaceId int64) (*model.JobsRunPreCheckRsp, error) {
	logger.Infof("requestId : %s  jobs run pre check begin, reg.AppId : %d", req.RequestId, req.AppId)
	items := req.RunPreCheckJobItems
	jobRunPreCheckResults := make([]model.JobRunPreCheckResult, 0)
	for _, item := range items {
		jobId := item.JobId
		job, err := service.GetJobBySerialId(req.AppId, jobId)
		if err != nil {
			logger.Errorf("%s : get job by serial id %s with error %v", req.RequestId, jobId, err)
			return nil, err
		}
		rspItem := model.JobRunPreCheckResult{}
		jobConfigVersion := item.JobConfigVersion
		if jobConfigVersion == 0 || jobConfigVersion == -1 {
			// 优先用线上配置，没有则用最近一次配置
			var configId int64
			switch {
			case job.PublishedJobConfigId > 0:
				configId = job.PublishedJobConfigId
			case job.LatestJobConfigId > 0:
				configId = job.LatestJobConfigId
			}
			if configId > 0 {
				jobConfig, err := service.GetJobConfigById(configId)
				if err != nil {
					logger.Errorf("%s : get jobConfig by id %d with error %v", req.RequestId, configId, err)
					return nil, err
				}
				jobConfigVersion = int64(jobConfig.VersionId)
			}
			if jobConfigVersion > 0 {
				hasNewSysConnector, checkSysConnectorErr := checkJobSysConnectorHasNew(ctx, jobId, jobConfigVersion)
				if checkSysConnectorErr != nil {
					logger.Errorf("check sys connector error: %+v", checkSysConnectorErr)
					return nil, checkSysConnectorErr
				}
				rspItem.HasNewSystemConnector = hasNewSysConnector
			}
		}
		checkJobRefResourceDeleteFlag, checkJobRefResourceIsDeleteErr := checkJobRefResourceIsDelete(ctx, jobId, jobConfigVersion)
		if checkJobRefResourceIsDeleteErr != nil {
			logger.Errorf("check job ref resource is delete error: %+v", checkJobRefResourceIsDeleteErr)
			return nil, checkJobRefResourceIsDeleteErr
		}
		rspItem.JobId = jobId
		rspItem.JobConfigVersion = jobConfigVersion
		rspItem.RefResourceIsDelete = checkJobRefResourceDeleteFlag
		if job.Type == constants.JOB_TYPE_SQL {
			clusterGroup, err := service2.ListClusterGroupById(job.ClusterGroupId)
			if err != nil {
				logger.Errorf("%s : get clusterGroup by  id %d with error %v", req.RequestId, job.ClusterGroupId, err)
				return nil, err
			}
			jobConfig, err := service.GetJobConfig(item.JobId, item.JobConfigVersion)
			if err != nil {
				logger.Errorf("%s : get jobConfig by jobId:$s and jobConfigVersion %d with error %v",
					req.RequestId, item.JobId, item.JobConfigVersion, err)
				return nil, err
			}
			flinkVersion := service1.GetFlinkVersion(job, jobConfig)
			sqlService := sql.NewParseSqlService(req.RequestId, req.Uin, req.AppId, req.Region, flinkVersion, clusterGroup.SerialId, job.ItemSpaceId)
			decryptSqlCode, err := util.AesDecrypt(jobConfig.SqlCode, constants.AES_ENCRYPT_KEY)
			if err != nil {
				logger.Errorf("ReplaceCustomTimestampWithAES return error %v", err)
				return nil, err
			}
			parseSqlRsp, err := sqlService.ParseSql(ctx, base64.StdEncoding.EncodeToString([]byte(decryptSqlCode)), req.Region, 0)
			if err != nil {
				logger.Errorf("%s: parse sql with error %v", req.RequestId, err)
				return nil, err
			}
			marshal, _ := json.Marshal(parseSqlRsp)
			logger.Infof("parseSql result preRun check: %s", string(marshal))
			// 根据 sql-parser 结果，判断是否存在 kafka source
			containsKafkaSource, kafkaStartupMode, err := checkKafkaSource(parseSqlRsp)
			if err != nil {
				logger.Errorf("%s: checkKafkaSource with error %v", req.RequestId, err)
				return nil, err
			}
			rspItem.ContainsKafkaSource = containsKafkaSource
			rspItem.KafkaStartupMode = kafkaStartupMode
		}
		jobRunPreCheckResults = append(jobRunPreCheckResults, rspItem)
	}
	jobsRunPreCheckRsp := &model.JobsRunPreCheckRsp{}
	jobsRunPreCheckRsp.JobRunPreCheckResults = jobRunPreCheckResults
	logger.Infof("requestId : %s jobs run pre check end", req.RequestId)

	return jobsRunPreCheckRsp, nil
}

// checkKafkaSource 当包含了完整的kafka source，并且 'scan.startup.mode' = 'timestamp'，
// 并且 'scan.startup.timestamp-millis'= '#{CUSTOM_TIMESTAMP}', 此时才可以选择自定义时间，否则以 sqlCode 为主
func checkKafkaSource(parseSqlRsp *sql_model.ParseSqlRsp) (containsKafkaSource bool, kafkaStartupMode int64, err error) {
	if parseSqlRsp == nil {
		logger.Errorf("parseSql result is empty ")
		return false, kafkaStartupMode, errors.New("parseSqlRsp is empty")
	}
	kafkaProperties := make(map[string]string)
	kafkaSourceProperties := make(map[string]string)
	// type为kafka，就是source表，不需要sql Parse的判断（因为SqlParse有些source表解析不出来）
	for _, table := range parseSqlRsp.MetaRefs.TemporaryTables {
		if table.TableType == sql.KafkaTableType {
			kafkaProperties[table.Name] = table.Properties
			containsKafkaSource = true
			kafkaSourceProperties[table.Name] = table.Properties
		}
	}
	for _, sourceTable := range parseSqlRsp.MetaRefs.SourceTables {
		if value, ok := kafkaProperties[sourceTable]; ok {
			containsKafkaSource = true
			kafkaSourceProperties[sourceTable] = value
		}
	}
	logger.Debugf("kafkaSourceProperties: %v", kafkaSourceProperties)
	kafkaSourceStartupMode := make(map[string]int64)
	for sourceTable, propertiesStr := range kafkaSourceProperties {
		if propertiesStr == "" {
			continue
		}
		properties := make(map[string]string)
		err = json.Unmarshal([]byte(propertiesStr), &properties)
		if err != nil {
			logger.Errorf("sql properties parse error %v", err)
			return false, kafkaStartupMode, err
		}
		var isTimestampScanMod, containsCustomTimestamp bool
		// var isSpecificOffsetMod, containsSpecificOffset bool
		var isCustomScanMod bool
		for k, v := range properties {
			if k == sql.KafkaScanModeKey {
				if v == sql.KafkaScanModeTimestampValue {
					isTimestampScanMod = true
				}
				// if v == sql.KafkaCanModeSpecificOffsetValue {
				// 	isSpecificOffsetMod = true
				// }
				if v == sql.CustomScanMod {
					isCustomScanMod = true
				}
			}
			if k == sql.CustomTimestampFieldKey && v == sql.CustomTimestamp {
				containsCustomTimestamp = true
			}
			// if k == sql.SpecificOffsetFieldKey && v == sql.SpecificOffset {
			// 	containsSpecificOffset = true
			// }
		}
		if isTimestampScanMod && containsCustomTimestamp {
			kafkaStartupMode = model.KafkaStartupModeTimestamp
		}
		// if isSpecificOffsetMod && containsSpecificOffset {
		// 	kafkaStartupMode = model.KafkaStartupModeSpecificOffsets
		// }
		if isCustomScanMod {
			kafkaStartupMode = model.KafkaStartupModeCustom
		}
		kafkaSourceStartupMode[sourceTable] = kafkaStartupMode
	}
	logger.Debugf("kafkaSourceStartupMode %v", kafkaSourceStartupMode)
	if len(kafkaSourceStartupMode) > 0 && !isMapValuesEqual(kafkaSourceStartupMode) {
		// 当sql中包含多个kafka source, 并且使用自定义启动方式不一致, 返回错误, 暂不支持
		msg := "invalid custom const in multi kafka source"
		logger.Errorf(msg)
		return containsKafkaSource, kafkaStartupMode, errors.New(msg)
	}
	return containsKafkaSource && kafkaStartupMode > 0, kafkaStartupMode, nil
}

func isMapValuesEqual(m map[string]int64) bool {
	var firstVal int64
	first := true
	for _, v := range m {
		if v <= 0 {
			continue
		}
		if first {
			firstVal = v
			first = false
		} else {
			if v != firstVal {
				return false
			}
		}
	}
	return true
}

/*
*
检查作业引用的依赖是否被删除
*/
func checkJobRefResourceIsDelete(ctx context.Context, jobSerialId string, jobConfigVersion int64) (bool, error) {
	cond := dao.NewCondition()
	cond.Eq("Job.SerialId", jobSerialId)
	cond.Eq("JobConfig.VersionId", jobConfigVersion)
	cond.Eq("ResourceConfig.status", -2)
	where, args := cond.GetWhere()
	sql := "select " +
		"ResourceConfig.VersionId as resourceConfigVersionId," +
		"ResourceConfig.Status as resourceConfigStatus " +
		"from JobConfig " +
		"inner join ResourceRef on JobConfig.Id = ResourceRef.JobConfigId " +
		"inner join Resource on ResourceRef.ResourceId = Resource.Id " +
		"inner join Job on Job.Id = JobConfig.JobId " +
		"inner join ResourceConfig on ResourceConfig.ResourceId = Resource.Id and ResourceConfig.VersionId = ResourceRef.VersionId "
	sql += where
	_, data, err := service.GetTxManager().GetQueryTemplate().DoQueryWithContext(ctx, sql, args)
	if err != nil {
		return false, err
	}
	return len(data) > 0, nil
}

/*
*
检查作业内置connector是否有新版本
*/
func checkJobSysConnectorHasNew(ctx context.Context, jobSerialId string, jobConfigVersion int64) (bool, error) {
	cond := dao.NewCondition()
	cond.Eq("Job.SerialId", jobSerialId)
	cond.Eq("JobConfig.VersionId", jobConfigVersion)
	cond.Eq("Resource.SystemProvide", 1)
	where, args := cond.GetWhere()
	sql := "select " +
		"Job.SerialId as jobSerialId," +
		"JobConfig.Id as jobConfigId," +
		"JobConfig.VersionId as jobConfigVersionId," +
		"ResourceRef.Id as resourceRefId," +
		"ResourceRef.ResourceId as resourceId," +
		"ResourceRef.VersionId as resourceConfigVersionId," +
		"Resource.ResourceName as resourceName," +
		"Resource.SystemProvide as systemProvide " +
		"from JobConfig " +
		"inner join ResourceRef on JobConfig.Id = ResourceRef.JobConfigId " +
		"inner join Resource on ResourceRef.ResourceId = Resource.Id " +
		"inner join Job on Job.Id = JobConfig.JobId "
	sql += where
	_, data, err := service.GetTxManager().GetQueryTemplate().DoQueryWithContext(ctx, sql, args)
	if err != nil {
		return false, err
	}
	// 一个job关联多个不同的内置connector
	jobResourceRefItemArr := make([]*JobResourceRefItem, 0)
	resourceIdArr := make([]int, 0)
	for _, d := range data {
		tmp := &JobResourceRefItem{}
		err = util.ScanMapIntoStruct(tmp, d)
		if err != nil {
			return false, errorcode.InternalErrorCode.NewWithErr(err)
		}
		jobResourceRefItemArr = append(jobResourceRefItemArr, tmp)
		resourceIdArr = append(resourceIdArr, tmp.resourceId)
	}

	// 根据资源ID查询资源版本信息
	resourceMaxVersionMap, err := getResourceMaxVersionMap(resourceIdArr)
	logger.Infof("checkJobSysConnectorHasNew resourceMaxVersionMap = %s , jobSerialId = %s , jobConfigVersion = %s ", resourceMaxVersionMap, jobSerialId, jobConfigVersion)
	// 只要有一个connector 有新版本就返回true
	for _, d := range jobResourceRefItemArr {
		if d.resourceConfigVersionId < resourceMaxVersionMap[d.resourceId] {
			return true, nil
		}
	}
	return false, nil
}

func getResourceMaxVersionMap(resourceIdArr []int) (map[int]int, error) {
	cond := dao.NewCondition()
	cond.In("ResourceId", resourceIdArr)
	where, args := cond.GetWhere()
	sql := "select ResourceId,VersionId from ResourceConfig"
	sql += where
	_, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return nil, err
	}
	resourceMaxVersionMap := make(map[int]int)
	for _, d := range data {
		tmp := &ResourceConfigItem{}
		err = util.ScanMapIntoStruct(tmp, d)
		if err != nil {
			return nil, errorcode.InternalErrorCode.NewWithErr(err)
		}
		if v, ok := resourceMaxVersionMap[tmp.resourceId]; ok {
			if v < tmp.versionId {
				resourceMaxVersionMap[tmp.resourceId] = tmp.versionId
			}
		} else {
			resourceMaxVersionMap[tmp.resourceId] = tmp.versionId
		}
	}
	return resourceMaxVersionMap, nil
}

type JobResourceRefItem struct {
	jobSerialId             string
	jobConfigId             int
	jobConfigVersionId      int
	resourceRefId           int
	resourceId              int
	resourceConfigVersionId int
	resourceName            string
	systemProvide           int
}

type ResourceConfigItem struct {
	resourceId int
	versionId  int
}
