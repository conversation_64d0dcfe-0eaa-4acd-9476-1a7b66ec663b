package service

import (
	"encoding/json"
	"fmt"
	"tencentcloud.com/tstream_galileo/src/component"
	service1 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	v20210820 "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/wedata"

	"github.com/juju/errors"
	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	user2 "tencentcloud.com/tstream_galileo/src/tstream_cc/controller/mc/user"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/user"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cmq"
	service4 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/draft"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/quota"
	tag "tencentcloud.com/tstream_galileo/src/tstream_cc/service/tag"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/white_list"
)

/**
 *
 * <AUTHOR>
 * @time 2019/4/27
 * @version 0.4.0
 * @since 0.4.0
 * @see
 */
func DoCreateJob(req *model.CreateJobReq) (string, string, *model.CreateJobRsp) {
	// 0. Print RequestId
	logger.Debugf("%s: CreateJob API called by AppId %d", req.RequestId, req.AppId)

	pass, msg, camErr := cam(req)
	if camErr != nil {
		return controller.InternalError, msg, nil
	}
	if !pass {
		return v20210820.AUTHFAILURE_UNAUTHORIZEDOPERATION, msg, nil
	}

	if !service2.IsTceEnv() {
		// 0. 检查用户是否已实名认证
		userAuthReq := &user.IsUserAuthenticatedReq{
			OwnerUin: req.Uin,
		}
		retCode, retInfo, userAuthResp := (&user2.IsUserAuthenticatedController{}).Process(userAuthReq, 0)
		if retCode != controller.SUCCESS {
			logger.Errorf("%s: Failed to check if user is authenticated or not, with error message %s", req.RequestId, retInfo)
			return controller.InternalError, retInfo, nil
		}
		if userAuthResp.(*user.IsUserAuthenticatedResp).IsAuthenticated == 0 {
			logger.Errorf("%s: User owner uin %s is NOT authenticated, refuse to serve", req.RequestId, req.Uin)
			return controller.FailedOperation_UserNotAuthenticated, "Your account must be authenticated to use our service, please visit https://console.cloud.tencent.com/developer/auth", nil
		}
	}

	// 鉴权
	itemSpcId, err2 := auth.InnerCreateAuth(req.WorkSpaceId, int64(req.AppId), req.Region)
	if err2 != nil {
		logger.Errorf("%s: User  uin %s is NOT authenticated, refuse to serve", req.RequestId, req.Uin)
		errCode := errorcode.GetCode(err2)
		return errCode.GetCodeStr(), errCode.GetCodeDesc(), nil
	}

	// 检查标签是否合法
	tags, getTagErr := tag.GetTagService().ValidateTags(req.Uin, req.Tags)
	if getTagErr != nil {
		logger.Errorf("%s: validateTags, with error: %+v", req.RequestId, getTagErr)
		msg := fmt.Sprintf("%s: validateTags, with error, tags: %+v", req.RequestId, req.Tags)
		return controller.InvalidParameterValue, msg, nil
	}

	// 1. 检查 JobName
	err := service2.CheckNameValidityV2(req.Name, 100)
	if err != nil {
		logger.Errorf("%s: Failed to CheckNameValidity, with error: %+v", req.RequestId, err)
		return controller.InvalidParameterValue_JobName, err.Error(), nil
	}

	// 2. 共享集群支持SQL（ClusterType=1, JobType=1），独享集群支持JAR（ClusterType=2, JobType=2）和 SQL(ClusterType=2, JobType=1) 和 ETL (ClusterType=2, JobType=3)
	if req.ClusterType == constants.CLUSTER_GROUP_TYPE_SHARED && req.JobType != constants.JOB_TYPE_SQL {
		msg := fmt.Sprintf("%s: Unsupported composite type: ClusterType=%d, JobType=%d", req.RequestId, req.ClusterType, req.JobType)
		logger.Error(msg)
		return controller.InvalidParameterValue_ClusterType_JobType, msg, nil
	}

	// 3. 作业名称是否已存在
	isPass, errCodeStr, msg := service2.CheckJobNameExisted(req.AppId, req.Region, req.Name, itemSpcId)
	if !isPass {
		logger.Errorf("%s: %s", req.RequestId, msg)
		return errCodeStr, msg, nil
	}
	// 4. 如果是独享集群，检查clusterId是否为空
	if req.ClusterType == constants.CLUSTER_GROUP_TYPE_PRIVATE && req.ClusterId == "" {
		msg := fmt.Sprintf("%s: ClusterId should not be null", req.RequestId)
		logger.Error(msg)
		return controller.InvalidParameterValue_ClusterId, "ClusterId should not be null", nil
	}

	//// 4.5 集群绑定鉴权
	//err2 = auth.CheckItemSpaceCluster(itemSpcId, []string{req.ClusterId})
	//if err2 != nil {
	//	logger.Errorf("%s: User  uin %s is NOT authenticated, refuse to serve", req.RequestId, req.Uin)
	//	errCode := errorcode.GetCode(err2)
	//	return errCode.GetCodeStr(), errCode.GetCodeDesc(), nil
	//}

	// 5. 作业总数量是否超过配额
	jobTotalCount, err := GetJobsTotalCount(req.AppId, "", nil, nil, nil, nil, nil, nil, nil)
	if err != nil {
		logger.Errorf("%s: Failed to create job. Exception occurs when invoke GetJobsTotalCount, error: %+v", req.RequestId, err)
		return controller.InternalError, controller.NULL, nil
	}
	overLimit, msg, err := quota.NewQuota().OverLimit("", int64(req.AppId), quota.Job, int32(jobTotalCount))

	if err != nil {
		logger.Errorf("%s: Failed to create job. Exception occurs when invoke quota.NewQuota().OverLimit, error: %+v", req.RequestId, err)
		return controller.InternalError, controller.NULL, nil
	}
	if overLimit {
		errMsg := fmt.Sprintf("job %s", msg)
		logger.Errorf("%s: Failed to create job for AppID %d. %s", req.RequestId, req.AppId, errMsg)
		return controller.LimitExceeded, errMsg, nil
	}

	// 6. 如果是共享集群，检查 CUMem 是否符合规格
	if req.ClusterType == constants.CLUSTER_GROUP_TYPE_SHARED {
		if req.CuMem == 0 {
			req.CuMem = constants.CU_MEM_4GB
		} else if req.CuMem != constants.CU_MEM_4GB {
			msg := fmt.Sprintf("%s: Share Cluster only allow CuMem=4", req.RequestId)
			logger.Error(msg)
			return controller.InvalidParameterValue_CuMem, "Share Cluster only allow CuMem=4", nil
		}
	}

	// 8. 以上条件都满足，作业数量是否超过配额

	// 9. 获取ClusterGroup
	var clusterGroup *table2.ClusterGroup
	if req.ClusterType == constants.CLUSTER_GROUP_TYPE_SHARED {
		clusterGroups, err := ListShareClusterGroups(req.Region)
		if err != nil {
			logger.Errorf("%s: Failed to find ListShareClusterGroups, err: %+v", req.RequestId, err)
			return controller.InternalError, controller.NULL, nil
		}
		clusterGroup = clusterGroups[0] // 当前共享集群只有一个ClusterGroup
	} else {
		clusterGroup, err = ListPrivateClusterGroups(req.AppId, req.Region, req.ClusterId)
		if err != nil {
			logger.Errorf("%s: Failed to find ListPrivateClusterGroups, err: %+v", req.RequestId, err)
			return controller.InternalError, controller.NULL, nil
		}
		if clusterGroup == nil {
			msg := fmt.Sprintf("%s: ResourceNotFound.Cluster, appId: %d, region: %s, clusterId: %s", req.RequestId, req.AppId, req.Region, req.ClusterId)
			logger.Error(msg)
			return controller.ResourceNotFound_ClusterId, msg, nil
		}
	}

	// 10. 检查集群是否在运行状态
	// 2.6 迭代，新增集群扩容， 扩容中的集群可以新建作业
	if clusterGroup.Status != constants.CLUSTER_GROUP_STATUS_RUNNING &&
		clusterGroup.Status != constants.CLUSTER_GROUP_STATUS_SCALE_PROGRESS &&
		clusterGroup.Status != constants.CLUSTER_GROUP_STATUS_UPGRADE &&
		clusterGroup.Status != constants.CLUSTER_GROUP_STATUS_SCALEDOWN_PROGRESS {
		msg := fmt.Sprintf("%s: ResourceUnavailable.Cluster, cluster is not running, clusterId: %s", req.RequestId, req.ClusterId)
		logger.Errorf(msg)
		return controller.ResourceUnavailable, msg, nil
	}

	// 11. 如果是独享集群，检查 ClusterId 及 CuMem 规格是否匹配
	if req.ClusterType == constants.CLUSTER_GROUP_TYPE_PRIVATE {
		if req.CuMem == 0 {
			req.CuMem = clusterGroup.CuMem // 默认情况，作业内存规格与购买的独享集群保持一致
		} else if req.CuMem != clusterGroup.CuMem {
			whiteListUser, err := service3.WhetherInCuMemWhiteList(req.AppId, req.SubAccountUin)
			if err != nil {
				logger.Errorf("%s: Failed to decide whether user with appId[%d] is in CuMem WhiteList", req.RequestId, req.AppId)
				return controller.InternalError, controller.NULL, nil
			}
			if !whiteListUser { // 如果不在白名单中
				msg := fmt.Sprintf("Cluster: %s only support CuMem=%d, but specified %d", req.ClusterId, clusterGroup.CuMem, req.CuMem)
				logger.Errorf("%s: NOT_IN_WHITELIST AppId: %d, ClusterId: %s, %s", req.RequestId, req.AppId, req.ClusterId, msg)
				return controller.InvalidParameterValue_CuMem, msg, nil
			}
		}
	}

	// 12. 根据ClusterGroup获取Clusters
	cluster, err := service.SelectActiveCluster(req.SubAccountUin, clusterGroup.Id, 0, req.JobType)
	if err != nil {
		logger.Errorf("%s: Failed to SelectActiveCluster, err: %+v", req.RequestId, err)
		return controller.InternalError, controller.NULL, nil
	}

	if cluster == nil {
		logger.Errorf("%s: No cluster found for ClusterGroupId: %d", req.RequestId, clusterGroup.Id)
		return controller.InternalError, controller.NULL, nil
	}

	if req.FlinkVersion == "" {
		req.FlinkVersion = cluster.FlinkVersion
	}
	if !service.ClusterSupportFlinkVersion(cluster, req.FlinkVersion) {
		return controller.InvalidParameterValue, fmt.Sprintf("Cluster not support flink version %s",
			req.FlinkVersion), nil
	}

	// 14. 根据请求参数及集群信息构建Job实体
	job, err := BuildJobEntityFromReq(clusterGroup, cluster, req, itemSpcId)
	if err != nil {
		msg := fmt.Sprintf("%s: Failed to build JobEntity from req, err: %v", req.RequestId, err)
		logger.Error(msg)
		return controller.InternalError, controller.NULL, nil
	}

	// 15. 保存作业,初始化作业的运行次数		TODO 保存作业时将对应的ClusterGroup和Cluster减去相关的资源用量
	//openJobDefaultAlarm := req.OpenJobDefaultAlarm
	req.OpenJobDefaultAlarm = 0 // 不在这里保存，默认是开启的，但是要根据后面是否成功,先设置false,0表示不开启，存量作业查出来都是0.
	serialId, id, err := CreateJob(job)
	if err != nil {
		msg := fmt.Sprintf("%s: Failed to save job into db, job: %+v, error: %+v", req.RequestId, job, err)
		logger.Error(msg)
		return controller.InternalError, controller.NULL, nil
	}

	logCollect, err := initLogCollect(clusterGroup.Id)
	if err != nil {
		logger.Errorf("%s: Failed to initLogCollect, with errors:%+v", req.RequestId, err)
		return controller.InternalError, controller.NULL, nil
	}

	err = service4.DoCreateDraftConfigByDefault(id, req.SubAccountUin, job.Type, logCollect, cluster)
	if err != nil {
		logger.Errorf("%s: Failed to create draft config, with errors:%+v", req.RequestId, err)
		return controller.InternalError, controller.NULL, nil
	}
	createJobRsp := &model.CreateJobRsp{JobId: serialId, RequestId: req.RequestId}

	msg = fmt.Sprintf("%s: CreateJob succeeded for job: %+v", req.RequestId, job)
	logger.Info(msg)

	// 16. 作业绑定标签
	bindToTagErr := tag.JobBindToTag(req.RequestId, job.OwnerUin, job.Region, tags, serialId)
	if bindToTagErr != nil {
		logger.Errorf("%s: Failed to JobBindToTag, with errors:%+v", req.RequestId, bindToTagErr)
	}

	// 17. 往cmq发送消息,绑定默认告警策略
	err = cmq.SendMsgToBarad(serialId, int64(req.AppId), req.Uin, req.Region, cmq.MSG_OP_CREATE)
	if err != nil {
		// 无需返回错误
		logger.Errorf("%s send message to barad with error %v", req.RequestId, err)
	}

	return controller.OK, controller.NULL, createJobRsp
}

func ListPrivateClusterGroups(appId int32, region string, serialId string) (*table2.ClusterGroup, error) {
	serialIds := make([]string, 0)
	serialIds = append(serialIds, serialId)
	clusterGroups, err := service2.ListClusterGroups(&service2.ListClusterGroupsParam{
		AppId:        appId,
		Regions:      []string{region},
		Zone:         "",
		ClusterType:  []int8{constants.CLUSTER_GROUP_TYPE_PRIVATE},
		ClusterNames: nil,
		IsVagueNames: false,
		SerialIds:    serialIds,
		Offset:       0,
		Limit:        0,
		OrderType:    0,
		StatusList:   nil,
	})
	if err != nil {
		return nil, err
	} else if len(clusterGroups) == 0 {
		return nil, nil
	} else if len(clusterGroups) > 1 {
		return nil, errors.New(fmt.Sprintf("Logic error? More than one ClusterGroup found "+
			"in Region: `%s` for appId: %d, serialId: %s", region, appId, serialId))
	}
	return clusterGroups[0], nil
}

func ListShareClusterGroups(region string) ([]*table2.ClusterGroup, error) {
	clusterGroups, err := service2.ListClusterGroups(&service2.ListClusterGroupsParam{
		AppId:        0,
		Regions:      []string{region},
		Zone:         "",
		ClusterType:  []int8{constants.CLUSTER_GROUP_TYPE_SHARED},
		ClusterNames: nil,
		IsVagueNames: false,
		SerialIds:    nil,
		Offset:       0,
		Limit:        0,
		OrderType:    0,
		StatusList:   nil,
	})
	if err != nil {
		return nil, err
	} else if len(clusterGroups) == 0 {
		return nil, errors.New(fmt.Sprintf("No Share Cluster found in Region: `%s`", region))
	}

	return clusterGroups, nil
}

func BuildJobEntityFromReq(clusterGroup *table2.ClusterGroup, cluster *table2.Cluster, req *model.CreateJobReq, itemSpcId int64) (*table.Job, error) {
	job := &table.Job{}
	job.Name = req.Name
	job.AppId = req.AppId
	job.OwnerUin = req.Uin
	job.CreatorUin = req.SubAccountUin
	job.Region = req.Region
	job.Zone = clusterGroup.Zone // Fixme 暂时先填入ClusterGroup可用区，如果以后涉及跨可用区迁移，则填入cluster.Zone
	job.Status = constants.JOB_STATUS_CREATE
	job.Type = req.JobType
	job.ClusterGroupId = clusterGroup.Id
	job.ClusterId = cluster.Id
	if req.CuMem <= 0 {
		job.CuMem = constants.CU_MEM_4GB
	} else {
		job.CuMem = req.CuMem
	}
	if cluster.MemRatio != 0 {
		job.CuMem = cluster.MemRatio
	}

	job.Remark = req.Remark
	job.LastOpResult = constants.OK_OP_RESULT_FOR_JOB
	job.TotalRunMillis = 0
	job.PublishedJobConfigId = constants.INDEX_WITHOUT_JOB_CONFIGS_FOR_JOB
	job.LatestJobConfigId = constants.INDEX_WITHOUT_JOB_CONFIGS_FOR_JOB
	job.LatestJobConfigVersionId = constants.VERSION_ID_WITHOUT_CONFIGS_FOR_JOB
	job.LastPublishedJobConfigId = -1
	if req.FolderId == "" {
		job.FolderId = "root"
	} else {
		job.FolderId = req.FolderId
	}
	job.CreateTime = util.GetCurrentTime()
	job.FlinkVersion = req.FlinkVersion
	job.ItemSpaceId = itemSpcId
	job.Description = req.Description
	job.ManageType = constants.MANAGE_TYPE_INTERNAL
	if req.ManageType != "" {
		job.ManageType = req.ManageType
	}

	if req.FlinkJobType > 0 {
		job.FlinkJobType = req.FlinkJobType
	}

	return job, nil
}

// 初始草稿的日志采集状态只返回0,1两种，与CreateJobConfig保持一致
func initLogCollect(clusterGroupId int64) (logCollect int, err error) {
	cgService, err := service.NewClusterGroupService(clusterGroupId)
	if err != nil {
		return
	}

	_, topicId, historyCluster, err := cgService.GetClsInfo()
	if err != nil {
		return
	}
	// 新集群默认开启
	if !historyCluster {
		return constants.JobLogCollectEnabled, nil
	}

	// 历史集群未开启地域日志采集
	if len(topicId) == 0 {
		return constants.JobLogCollectDisabled, nil
	}
	// 地域日志集存在，还需检查crd，若crd已删除的处理，表明客户已提工单处理，进入历史集群未开启日志采集状态
	if cluster, err := cgService.GetActiveCluster(); err != nil {
		return 0, err
	} else if exist, err := service.LogConfigCrdExists(cluster); err != nil {
		return 0, err
	} else if !exist {
		return constants.JobLogCollectDisabled, nil
	}

	return constants.JobLogCollectEnabled, nil
}

func cam(req *model.CreateJobReq) (bool, string, error) {
	// CAM鉴权，强打标签
	authComponent := component.NewAuthComponent()
	isDev := service1.GetConfStringValue("scsDevEnv")
	var uin string
	if isDev == "true" {
		uin = service1.GetConfStringValue("scsDevEnvCamUin")
	} else {
		uin = req.Uin
	}
	resource := fmt.Sprintf("qcs::oceanus:%s:uin/%s:job/*", req.Region, uin)
	condition := make(map[string]interface{})
	if len(req.Tags) > 0 {
		var tagStrings []string
		for _, t := range req.Tags {
			tagString := t.TagKey + "&" + t.TagValue
			tagStrings = append(tagStrings, tagString)
		}
		condition["qcs:request_tag"] = tagStrings
	}
	var dat map[string]interface{}
	authToken := ""
	if req.CamContext == "" {
		return true, "", nil
	}
	if err := json.Unmarshal([]byte(req.CamContext), &dat); err == nil {
		authToken = dat["q-token"].(string)
	} else {
		msg := fmt.Sprintf("%s: Failed to unmarshal CamContext", req.RequestId)
		logger.Errorf(msg)
		return false, msg, err
	}
	permission, msg, err1 := authComponent.SigAndAuth("", "", "", "", req.RequestId, authToken, []string{resource}, condition)
	if err1 != nil {
		logger.Errorf("%s: Failed to SigAndAuth, with error message %s", req.RequestId, msg)
		return false, msg, err1
	}
	if !permission {
		logger.Errorf("%s: Failed to SigAndAuth, with error message %s", req.RequestId, msg)
		return false, msg, nil
	}
	return true, "", nil
}
