package service

import (
	"errors"
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service3 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job_config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service4 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/white_list"
)

func DoScaleInnerJobs(req *model.ScaleInnerJobsReq) (string, string, *model.ScaleInnerJobsRsp) {
	// 0. Print RequestId
	logger.Infof("%s: ScaleInnerJobs API called by AppId %d on jobs %+v", req.RequestId, req.AppId, req.ScaleInnerJobDescriptions)

	// 1、检查请求是否为空
	if len(req.ScaleInnerJobDescriptions) == 0 {
		errMsg := fmt.Sprintf("InvalidParameterValue.JobIds. No JobIds specified")
		logger.Errorf("%s: Failed to scale inner jobs. %s", req.RequestId, errMsg)
		return controller.InvalidParameterValue, errMsg, nil
	}

	// 2. 如果size大于20，返回错误
	if len(req.ScaleInnerJobDescriptions) > constants.OPERATION_BATCH_SIZE_UPPER_LIMIT {
		errMsg := fmt.Sprintf("At least Scale %d Jobs one time", constants.OPERATION_BATCH_SIZE_UPPER_LIMIT)
		logger.Errorf("%s: Failed to scale inner jobs. %s", req.RequestId, errMsg)
		return controller.UnsupportedOperation, errMsg, nil
	}

	// 3、构建runJobs请求
	runJobsReq := InitializeRunJobsReq(req)
	runJobsReq.RunJobDescriptions = make([]model.RunJobDescription, len(req.ScaleInnerJobDescriptions))
	job2RunJobDesp := map[*table.Job]model.RunJobDescription{}
	jobSerialId2ScaleJobDesp := map[string]model.ScaleInnerJobDescription{}
	for i := 0; i < len(req.ScaleInnerJobDescriptions); i++ {
		desp := req.ScaleInnerJobDescriptions[i]
		errCode, errMsg, job, runJobDesp := GetJobAndBuildRunJobDesp(req, desp)
		if errCode != controller.OK {
			errMsg := fmt.Sprintf("get job and build runJobDesp for job[%s] failed with error: %s", desp.JobId, errMsg)
			logger.Errorf("%s: Failed to scale jobs. %s", req.RequestId, errMsg)
			return errCode, errMsg, nil
		}
		job2RunJobDesp[job] = runJobDesp
		jobSerialId2ScaleJobDesp[job.SerialId] = desp
		runJobsReq.RunJobDescriptions[i] = runJobDesp
	}

	err := DoBatchScaleRun(job2RunJobDesp, jobSerialId2ScaleJobDesp, runJobsReq)
	if err != nil {
		logger.Errorf("%s: Failed to DoBatchScaleRun: %+v", req.RequestId, err)
		return controller.InternalError, err.Error(), nil
	}
	return controller.OK, controller.NULL, &model.ScaleInnerJobsRsp{RequestId: req.RequestId}
}

func DoBatchScaleRun(job2RunJobDesp map[*table.Job]model.RunJobDescription,
	jobSerialId2ScaleJobDesp map[string]model.ScaleInnerJobDescription, runJobsReq *model.RunJobsReq) (err error) {
	logger.Infof("%s: Started DoBatchScaleRun", runJobsReq.RequestId)
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("%s: Scale batch of jobs panic ,for job2ScaleJobDesps:%+v, errors:%+v", runJobsReq.RequestId, jobSerialId2ScaleJobDesp, errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()

	txManager := service3.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		startJobDesps := make([]model.RunJobDescription, 0)
		for job, runJobsDesp := range job2RunJobDesp {
			scaleJobDesp := jobSerialId2ScaleJobDesp[job.SerialId]
			// 1、update job config's defaultParallelism
			if scaleJobDesp.ScaledParallelism != 0 {
				_, err := UpdateJobConfigDefaultParallelism(job.Id, int64(job.PublishedJobConfigId), scaleJobDesp.ScaledParallelism, tx)
				if err != nil {
					errMsg := fmt.Sprintf("Failed to update job config's defaultParallelism, with job serialId:%s, with jobConfigId:%d, with errors:%+v", job.SerialId, job.PublishedJobConfigId, err)
					logger.Errorf("%s: DoBatchScaleRun failed, error: %s", runJobsReq.RequestId, errMsg)
					return errors.New(errMsg)
				}
			}

			// 2、update job's cuMem
			if scaleJobDesp.ScaledParallelismMem != 0 {
				_, err := updateJobCuMem(job, scaleJobDesp.ScaledParallelismMem, tx)
				if err != nil {
					errMsg := fmt.Sprintf("Failed to update job's CuMem, with job SerialId: %s, with errors:%+v", job.SerialId, err)
					logger.Errorf("%s: DoBatchScaleRun failed, error: %s", runJobsReq.RequestId, errMsg)
					return errors.New(errMsg)
				}
			}
			startJobDesps = append(startJobDesps, runJobsDesp)
		}
		// 批量启动作业
		err = DoStartJobs(runJobsReq, startJobDesps, true, true, jobSerialId2ScaleJobDesp, tx)
		if err != nil {
			logger.Errorf("%s: Failed to do start jobs, with startJobDesps, with errors:%+v", runJobsReq.RequestId, startJobDesps, err)
			return err
		}
		return nil
	}).Close()
	return nil
}

func InitializeRunJobsReq(scaleInnerJobsReq *model.ScaleInnerJobsReq) *model.RunJobsReq {
	runJobsReq := &model.RunJobsReq{}
	runJobsReq.AppId = scaleInnerJobsReq.AppId
	runJobsReq.Uin = scaleInnerJobsReq.Uin
	runJobsReq.SubAccountUin = scaleInnerJobsReq.SubAccountUin
	runJobsReq.Region = scaleInnerJobsReq.Region
	runJobsReq.RequestId = scaleInnerJobsReq.RequestId
	return runJobsReq
}

func WhetherJobCanBeScaled(job *table.Job, description model.ScaleInnerJobDescription) (bool, string, error) {
	// 只允许运行、停止和暂停状态的作业可以被扩缩容
	if job.Status != constants.JOB_STATUS_RUNNING && job.Status != constants.JOB_STATUS_STOPPED && job.Status != constants.JOB_STATUS_PAUSED && job.Status != constants.JOB_STATUS_FINISHED {
		msg := fmt.Sprintf("Job[%s] in status `%d`, not allowed to be scaled.", job.SerialId, job.Status)
		return false, msg, nil
	} else if job.Type == constants.JOB_TYPE_SQL {
		isSharedSqlJob, err := service4.IsSharedClusterGroup(job.ClusterGroupId)
		if err != nil {
			errMsg := fmt.Sprintf("Check isSharedSqlJob failed, error: %v", err)
			return false, constants.NULL, errors.New(errMsg)
		}
		// 共享集群sql作业只允许扩缩容CuNum 不允许扩缩容CuMem
		if isSharedSqlJob && description.ScaledParallelismMem != 0 {
			msg := fmt.Sprintf("You are not allowed to scale parallelism memory for a Sql Job[%s]`", job.SerialId)
			return false, msg, nil
		}
	}
	return true, constants.NULL, nil
}

func GetJobAndBuildRunJobDesp(scaleInnerJobsReq *model.ScaleInnerJobsReq, scaleJobDesp model.ScaleInnerJobDescription) (errCode string, errMsg string, job *table.Job, runJobDesp model.RunJobDescription) {
	runJobDesp = model.RunJobDescription{}
	// 1、先检查作业是否存在
	job, err := service.WhetherJobExists(scaleInnerJobsReq.AppId, scaleInnerJobsReq.Region, scaleJobDesp.JobId)
	if err != nil {
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), nil, runJobDesp
	}
	// 2、获取作业的publishedJobConfig
	publishedJobConfig, err := GetLatestPublishedJobConfig(job)
	if err != nil {
		errMsg := fmt.Sprintf(" Failed to find publishedJobConfig for job[%s] with error: %s", job.SerialId, err.Error())
		return controller.InternalError, errMsg, nil, runJobDesp
	}
	// 3、检查作业是否可以被扩缩容
	jobCanBeScaled, msg, err := WhetherJobCanBeScaled(job, scaleJobDesp)
	if err != nil {
		errMsg := fmt.Sprintf("WhetherJobCanBeScaled failed, error: %+v", err)
		return controller.InternalError, errMsg, nil, runJobDesp
	}
	if !jobCanBeScaled {
		errMsg := fmt.Sprintf("Job[%s] is not allowed to be scaled because %s", job.SerialId, msg)
		return controller.UnsupportedOperation, errMsg, nil, runJobDesp
	}
	// 4、 检查运行状态的作业的restartAllowed和RestartType参数
	if job.Status == constants.JOB_STATUS_RUNNING {
		if !scaleJobDesp.RestartAllowed {
			errMsg := fmt.Sprintf("You must set 'RestartAllowed' to 'true' to scale a running job %s", job.SerialId)
			return controller.InvalidParameterValue, errMsg, nil, runJobDesp
		} else if scaleJobDesp.RestartType != constants.JOB_SCALE_RESTART_DIRECT && scaleJobDesp.RestartType != constants.JOB_SCALE_RESTART_WITH_SAVEPOINT {
			errMsg := fmt.Sprintf("You must set 'RestartType'to '1'(stop and run) or '2'(pause and run) to scale a running job %s", job.SerialId)
			return controller.InvalidParameterValue, errMsg, nil, runJobDesp
		}
	}

	// 5、 检查作业并行度和CuMem参数
	if scaleJobDesp.ScaledParallelism == 0 && scaleJobDesp.ScaledParallelismMem == 0 {
		errMsg := fmt.Sprintf("At least one of the two parameters( ScaledParallelism or ScaledParallelismMem) must be provided to scale job %s", scaleJobDesp.JobId)
		return controller.InvalidParameterValue, errMsg, nil, runJobDesp
	}

	// 6、 如果是需要扩缩容CuMem，需要检查是否在白名单内CuMemWhiteList
	if scaleJobDesp.ScaledParallelismMem != 0 {
		isInCuMemWhiteList, err := service2.WhetherInCuMemWhiteList(scaleInnerJobsReq.AppId, scaleInnerJobsReq.SubAccountUin)
		if err != nil {
			errMsg := fmt.Sprintf("WhetherInCuMemWhiteList(appId[%d], subAccountUin[%s]) error: %+v", scaleInnerJobsReq.AppId, scaleInnerJobsReq.SubAccountUin, err)
			return controller.InternalError, errMsg, nil, runJobDesp
		}
		if !isInCuMemWhiteList {
			errMsg := fmt.Sprintf("User(AppId: %d, subaccountUin: %s) is not allowed to scale CuMem of job %s", scaleInnerJobsReq.AppId, scaleInnerJobsReq.SubAccountUin, scaleJobDesp.JobId)
			return controller.UnsupportedOperation, errMsg, nil, runJobDesp
		}

	}
	runJobDesp.JobId = scaleJobDesp.JobId
	runJobDesp.StartMode = scaleJobDesp.StartMode
	runJobDesp.JobConfigVersion = int64(publishedJobConfig.VersionId)
	if job.Status == constants.JOB_STATUS_RUNNING {
		if scaleJobDesp.RestartType == constants.JOB_SCALE_RESTART_DIRECT {
			runJobDesp.RunType = constants.JOB_RUN_TYPE_RESTART
		} else {
			runJobDesp.RunType = constants.JOB_RUN_TYPE_RESTART_WITH_SAVEPOINT
		}
	} else if job.Status == constants.JOB_STATUS_STOPPED {
		runJobDesp.RunType = constants.JOB_RUN_TYPE_RUN
	} else if job.Status == constants.JOB_STATUS_FINISHED {
		runJobDesp.RunType = constants.JOB_RUN_TYPE_RUN
	} else {
		runJobDesp.RunType = constants.JOB_RUN_TYPE_RESUME
	}
	return controller.OK, "", job, runJobDesp
}

func GetLatestPublishedJobConfig(job *table.Job) (*table2.JobConfig, error) {
	jobConfig := &table2.JobConfig{}
	sql := "SELECT * FROM JobConfig WHERE Id = ?"
	args := make([]interface{}, 0)
	args = append(args, job.PublishedJobConfigId)
	txManager := service3.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		errMsg := fmt.Sprintf("Exception occurs when query JobConfig from db, with errors:%+v", err)
		return nil, errors.New(errMsg)
	}
	if len(data) == 0 {
		errMsg := fmt.Sprintf("Can not find any publishedJobConfig, with job[%s]", job.SerialId)
		return nil, errors.New(errMsg)
	} else if len(data) > 1 {
		errMsg := fmt.Sprintf("Logical error, find more than one publishedJobConfigs, with job[%s]", job.SerialId)
		return nil, errors.New(errMsg)
	}
	err = util.ScanMapIntoStruct(jobConfig, data[0])
	if err != nil {
		errMsg := fmt.Sprintf("Failed to convert bytes into table.jobConfig, with errors:%+v", err)
		return nil, errors.New(errMsg)
	}
	return jobConfig, nil
}

func UpdateJobConfigDefaultParallelism(jobId int64, jobConfigId int64, defaultParallelism int16, tx *dao.Transaction) (int64, error) {
	sql := "UPDATE JobConfig set DefaultParallelism=? where jobId =? AND Id =?"
	args := make([]interface{}, 0)
	args = append(args, defaultParallelism)
	args = append(args, jobId)
	args = append(args, jobConfigId)
	result := tx.ExecuteSql(sql, args)
	return result.RowsAffected()
}

func updateJobCuMem(job *table.Job, cuMem int8, tx *dao.Transaction) (int64, error) {
	sql := "UPDATE Job set CuMem=? where Id =?"
	args := make([]interface{}, 0)
	args = append(args, cuMem)
	args = append(args, job.Id)
	result := tx.ExecuteSql(sql, args)
	return result.RowsAffected()
}
