package service

import (
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config/configure_center"

	tag "tencentcloud.com/tstream_galileo/src/tstream_cc/service/tag"

	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service5 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/k8s"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job"
	table3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job_config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config"
	service7 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/etl_parser"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job_config"
	service4 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
)

/**
 *
 * <AUTHOR>
 * @time 2019/4/27
 * @version 0.4.0
 * @since 0.4.0
 * @see
 */
func DoDescribeJobs(req *model.DescribeJobsReq) (string, string, *model.DescribeJobsRsp) {
	requestId := req.RequestId

	// 鉴权 (子账号，只能看见自己空间内绑定的集群，超管可以看到所有)
	itemSpcIds, err := auth.ExtractSubUinAuthItemSpaceIdSet(req.WorkSpaceId, req.IsSupOwner, req.AppId, req.SubAccountUin, req.Uin, req.Region)
	if err != nil {
		logger.Errorf("%s: ExtractSubUinAuthClustersSet : Obtain  clusterGroups that has permissions   error: %+v", req.RequestId, err)
		// 如果没有有权限的空间，返回空列表
		return controller.OK, controller.NULL, &model.DescribeJobsRsp{
			RequestId:  req.RequestId,
			TotalCount: 0,
			JobSet:     make([]*model.JobSetItem, 0),
		}
	}

	// 1. 检查JobIds和Filters
	code, msg := CheckJobIdsAndFilters(req)
	if code != controller.OK {
		logger.Errorf("%s: Failed to describe jobs (CheckJobIdsAndFilters), code: %s, msg: %s", requestId, code, msg)
		return code, msg, nil
	}

	var jobs []*table.Job
	var totalCount int
	if len(req.JobIds) > 0 { // 2. 如果指定了JobIds，Fixme，对JobIds进行去重
		code, msg, totalCount, jobs = GetJobsByJobIds(req, nil, itemSpcIds)
		if code != controller.OK {
			if code != controller.ResourceNotFound_JobId { // 找不到 JobID 的报错已经在 GetJobsByJobsId 里面打印了
				logger.Errorf("%s: Failed to describe jobs (GetJobsByJobIds), code: %s, msg: %s", requestId, code, msg)
			}
			return code, msg, nil
		}
	} else if req.ConnectorOptions != "" { // 3. 查询使用内置connector的作业
		jobs, err = GetJobsByConnectorOptions(req)
		if err != nil {
			logger.Errorf("Failed to describe jobs (GetJobsByConnectorOptions), error: %+v", err)
			return controller.InternalError, "Failed to describe jobs (GetJobsByConnectorOptions)", nil
		}
	} else if len(req.Filters) > 0 { // 3. 如果指定了Filters
		code, msg, totalCount, jobs = GetJobsByFilters(req, nil, itemSpcIds)
		if code != controller.OK {
			logger.Errorf("%s: Failed to describe jobs (GetJobsByFilters), code: %s, msg: %s", requestId, code, msg)
			return code, msg, nil
		}
	} else { // 4. 既没指定JobIds，也没指定Filters
		listJobQuery := model.ListJobQuery{
			AppId:           int32(req.AppId),
			Regions:         []string{req.Region},
			ItemSpaceIds:    itemSpcIds,
			IsVagueNames:    false,
			ClusterGroupIds: nil,
			Offset:          req.Offset,
			Limit:           req.Limit,
			OrderByList:     req.OrderByList,
		}
		jobs, err = service.ListJobs(&listJobQuery)
		if err != nil {
			logger.Errorf("%s: Failed to describe jobs, error: %+v", requestId, err)
			return controller.InternalError, controller.NULL, nil
		}
		totalCount, err = GetJobsTotalCount(int32(req.AppId), req.Region, nil, nil, nil, nil, nil, itemSpcIds, nil)
		if err != nil {
			logger.Errorf("%s: Failed to Get jobs total count, error: %+v", requestId, err)
			return controller.InternalError, controller.NULL, nil
		}
	}
	jobSet, err := BuildJobSetFromJobEntity(req, jobs)
	if err != nil {
		logger.Errorf("%s: Failed to describe jobs %s when Building, error: %+v", requestId, strings.Join(req.JobIds, ","), err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), nil
	}
	if extraResultContains(req.ExtraResult, constants.JOB_EVENT_INFO_KEY) {
		logger.Infof("%s: result contains %s", req.RequestId, constants.JOB_EVENT_INFO_KEY)
		types := []string{
			constants.EVENT_ENTITY_TYPE_JOB_FAIL_OR_RECOVER,
			constants.EVENT_ENTITY_TYPE_CHECKPOINT_FAIL_OR_RECOVER}
		err := renderJobEvents(jobs, jobSet, types, time.Now().Add(-24*time.Hour))
		if err != nil {
			// 事件信息渲染失败, 不影响整体接口响应, 无需返回 error code
			logger.Errorf("%s render job events with error %v", req.RequestId, err)
		}
	}

	describeJobsRsp := &model.DescribeJobsRsp{RequestId: req.RequestId, TotalCount: totalCount, JobSet: jobSet}

	return controller.OK, controller.NULL, describeJobsRsp
}

func extraResultContains(extraResult []string, target string) bool {
	for _, v := range extraResult {
		if v != "" && target != "" && v == target {
			return true
		}
	}
	return false
}

func renderJobEvents(jobs []*table.Job, jobSet []*model.JobSetItem, types []string, createTime time.Time) error {
	// 1. 过滤出运行中的作业
	jobRunningOrderIdDic := make(map[string]int64, 0)
	for _, j := range jobs {
		if j.Status == constants.JOB_STATUS_RUNNING {
			jobRunningOrderIdDic[j.SerialId] = j.JobRunningOrderId
		}
	}
	if len(jobRunningOrderIdDic) <= 0 {
		return nil
	}
	// 2. 查询事件信息
	results, err := queryEventCountByJobIdsAndRunningOrderIds(jobRunningOrderIdDic, types, createTime)
	if err != nil {
		logger.Errorf("queryEventCountByJobIdsAndRunningOrderIds execute failed , args: jobRunningOrderIdDic:%+v", jobRunningOrderIdDic)
		return err
	}
	// 3. 将事件信息渲染到 rsp 中
	for _, jobSetItem := range jobSet {
		if jobSetItem == nil {
			// 上游过来的jobSetItem可能为空
			continue
		}
		var eventCount int64 = 0
		for _, result := range results {
			if strings.HasPrefix(result.JobIdConcatRunningOrderId, jobSetItem.JobId) {
				eventCount += result.Count
			}
		}
		jobSetItem.EventInfo = &model.JobEventInfo{
			ErrorEventTotal: eventCount,
		}
		if eventCount > 0 && jobSetItem.LastOpResult == "" {
			jobSetItem.LastOpResult = fmt.Sprintf("当前作业实例在一天内产生 %d 次作业失败异常, 请关注", eventCount)
		}
	}
	return nil
}

func queryEventCountByJobIdAndRunningOrderId(jobId string, runningOrderId int64, resultC chan map[string]int64) (err error) {
	sql := "SELECT count(*) as result FROM EventAlert WHERE jobId=? AND runningOrderIdOnTrigger=? AND (type =? OR type =? OR type =?)"
	args := make([]interface{}, 0)
	args = append(args, jobId)
	args = append(args, runningOrderId)
	args = append(args, constants.EVENT_ENTITY_TYPE_JOB_FAIL_OR_RECOVER)
	args = append(args, constants.EVENT_ENTITY_TYPE_CHECKPOINT_FAIL_OR_RECOVER)
	args = append(args, constants.EVENT_ENTITY_TYPE_JOB_EXCEPTIONS)
	_, data, err := service5.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to execute the sql:%s, with args:%+v, with errors:%+v", sql, args, err)
		return err
	}
	if len(data) == 0 {
		logger.Errorf("Failed to query the cluster count, because query size is 0")
		return errors.New("EventAlert count query size is 0")
	}
	count, err := strconv.ParseInt(string(data[0]["result"]), 10, 64)
	if err != nil {
		logger.Errorf("sql %s query result error %v", err)
		return err
	}
	result := make(map[string]int64, 0)
	result[jobId] = count
	resultC <- result
	return nil
}

type JobEventCount struct {
	JobIdConcatRunningOrderId string
	Type                      string
	Count                     int64
}

func queryEventCountByJobIdsAndRunningOrderIds(jobIdAndRunningOrderIds map[string]int64, types []string, createTime time.Time) ([]*JobEventCount, error) {
	// SELECT  jobIdRunningOrderIdOnTrigger as JobIdConcatRunningOrderId , type as Type, count(*) as Count FROM EventAlert  where jobIdRunningOrderIdOnTrigger in (?, ?, ?) and type in (?, ?, ?) and createTime>? group by jobIdRunningOrderIdOnTrigger, type
	sql := "SELECT  jobIdRunningOrderIdOnTrigger as JobIdConcatRunningOrderId , type as Type, count(*) as Count FROM EventAlert "
	args := make([]interface{}, 0)
	jobIdAndRunningOrderIdParams := make([]string, 0)
	for jobId, runningOrderId := range jobIdAndRunningOrderIds {
		jobIdAndRunningOrderIdParams = append(jobIdAndRunningOrderIdParams, fmt.Sprintf("%s_%d", jobId, runningOrderId))
	}
	cond := dao.NewCondition()
	cond.In("jobIdRunningOrderIdOnTrigger", jobIdAndRunningOrderIdParams)
	cond.In("type", types)
	if !createTime.IsZero() {
		cond.Gt("createTime", createTime)
	}
	where, args := cond.GetWhere()
	sql += where
	sql += " group by jobIdRunningOrderIdOnTrigger, type "
	txManager := service5.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to execute the sql:%s, with args:%+v, with errors:%+v", sql, args, err)
		return nil, err
	}
	t := make([]*JobEventCount, 0)
	for i := 0; i < len(data); i++ {
		item := &JobEventCount{}
		err = util.ScanMapIntoStruct(item, data[i])
		if err != nil {
			return nil, errorcode.InternalErrorCode.NewWithErr(err)
		}
		t = append(t, item)
	}
	return t, nil
}

func CheckJobIdsAndFilters(req *model.DescribeJobsReq) (string, string) {
	if len(req.JobIds) > 0 && len(req.Filters) > 0 {
		return controller.InvalidParameterValue, "JobIds and Filters can't be specified both"
	}
	if len(req.JobIds) > 100 {
		return controller.InvalidParameterValue, "JobIds should not contain more than 100 elements"
	}

	if len(req.Filters) > 7 {
		return controller.InvalidParameterValue, "Filters should not contain more than 7 elements"
	}

	for i := 0; i < len(req.Filters); i++ {
		filter := req.Filters[i]
		if filter.Name != "Name" && filter.Name != "Status" && filter.Name != "ClusterId" &&
			filter.Name != "JobId" && filter.Name != "ClusterName" && filter.Name != "Type" && filter.Name != "Tags" {
			return controller.InvalidParameterValue, "Unknown filter.name, can only be one of `Name` or `Status` or `ClusterId` or `JobId` or `ClusterName` or `Type` or `Tags`"
		}
		if len(req.Filters[i].Values) > 7 {
			return controller.InvalidParameterValue, "Filter.Values should not contain more than 7 elements"
		}
	}
	if len(req.OrderByList) > 0 {
		for _, orderBy := range req.OrderByList {
			if orderBy.Field != constants.JOB_ORDER_BY_CURRENTRUNMILLIS &&
				orderBy.Field != constants.JOB_ORDER_BY_RUNNINGCU &&
				orderBy.Field != constants.JOB_ORDER_BY_RUNNINGCPU &&
				orderBy.Field != constants.JOB_ORDER_BY_RUNNINGMEM {
				return controller.InvalidParameterValue, "only RunningCu or CurrentRunMillis or RunningCpu or RunningMem can order."
			}
			if !strings.EqualFold(orderBy.Type, constants.ASC) &&
				!strings.EqualFold(orderBy.Type, constants.DESC) {
				return controller.InvalidParameterValue, "only asc or desc support."
			}
		}
		// 判断状态是不是运行中筛选
		if len(req.Filters) < 1 {
			return controller.InvalidParameterValue, "when order by RunningCu or CurrentRunMillis, must filter status with running."
		}
		statusRunning := false
		for i := 0; i < len(req.Filters); i++ {
			filter := req.Filters[i]
			if filter.Name == "Status" {
				for _, value := range filter.Values {
					if value == "4" {
						statusRunning = true
						break
					}
				}
			}
		}
		if !statusRunning {
			return controller.InvalidParameterValue, "when order by RunningCu or CurrentRunMillis or RunningCpu or RunningMem, must filter status with running."
		}
	}

	return controller.OK, controller.NULL
}

// 此接口调用量非常大，优化逻辑
func GetJobsByJobIds(req *model.DescribeJobsReq, clusterGroupIds []string, itemSpcIds []int64) (string, string, int, []*table.Job) {
	listJobQuery := model.ListJobQuery{
		AppId:           int32(req.AppId),
		Regions:         []string{req.Region},
		SerialIds:       req.JobIds,
		ItemSpaceIds:    itemSpcIds,
		IsVagueNames:    false,
		ClusterGroupIds: clusterGroupIds,
		Offset:          req.Offset,
		Limit:           req.Limit,
	}
	jobCandidates, err := service.ListJobs(&listJobQuery)
	if err != nil {
		logger.Errorf("Failed to ListJobs, error:%+v", err)
		return controller.InternalError, err.Error(), 0, nil
	}

	if req.Offset == 0 && len(req.JobIds) <= req.Limit {
		return controller.OK, controller.NULL, len(jobCandidates), jobCandidates
	}

	//can remove, contain by up logic
	if len(req.JobIds) == len(jobCandidates) {
		return controller.OK, controller.NULL, len(jobCandidates), jobCandidates
	}

	totalCount, err := GetJobsTotalCount(int32(req.AppId), req.Region, req.JobIds, nil, nil, clusterGroupIds, nil, itemSpcIds, nil)
	if err != nil {
		logger.Errorf("Failed to Get jobs total count, error: %+v", err)
		return controller.InternalError, controller.NULL, 0, nil
	}

	return controller.OK, controller.NULL, totalCount, jobCandidates
}

func GetJobsByConnectorOptions(req *model.DescribeJobsReq) ([]*table.Job, error) {
	jobs := make([]*table.Job, 0)
	if len(req.ConnectorOptions) == 0 {
		logger.Warningf("req.ConnectorOptions is empty")
		return jobs, nil
	}
	sql := "select * from Job where SerialId in (select distinct(j.SerialId) from SystemConnector sc " +
		" join Resource r on r.ResourceId = sc.ResourceId " +
		" join ResourceRef rr on rr.ResourceId = r.Id " +
		" join JobConfig jc on jc.Id = rr.JobConfigId " +
		" join Job j on j.Id = jc.JobId " +
		" join ClusterGroup cg on cg.Id = j.ClusterGroupId " +
		" where sc.Options like ? and r.Status = 1 and sc.Status = 1 and jc.Status != -2 " +
		" and j.Status != -2 "
	args := make([]interface{}, 0)
	args = append(args, "%"+req.ConnectorOptions+"%")
	for i := 0; i < len(req.Filters); i++ {
		if req.Filters[i].Name == "ClusterId" {
			clusterGroupIds := req.Filters[i].Values
			if len(clusterGroupIds) < 1 {
				break
			}
			sql += " and cg.SerialId in (  "
			for j := 0; j < len(clusterGroupIds)-1; j++ {
				sql += "?, "
				args = append(args, clusterGroupIds[j])
			}
			sql += "?)"
			args = append(args, clusterGroupIds[len(clusterGroupIds)-1])
			break
		}
	}
	sql += ") order by Id desc"
	logger.Infof("GetJobsByConnectorOptions sql:%s", sql)

	txManager := service5.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to GetJobsByConnectorOptions, error:%+v", err)
		return nil, err
	}

	for i := 0; i < len(data); i++ {
		job := &table.Job{}
		err = util.ScanMapIntoStruct(job, data[i])
		if err != nil {
			return nil, errorcode.InternalErrorCode.NewWithErr(err)
		}
		jobs = append(jobs, job)
	}

	return jobs, nil
}

func GetJobsByFilters(req *model.DescribeJobsReq, clusterGroupIds []string, itemSpcIds []int64) (string, string, int, []*table.Job) {
	jobs := make([]*table.Job, 0)

	var nameFilters []string
	var statusFilters []int64
	var typeFilters []int64
	var clusterIdFilters []string
	var serialIdFilters []string
	var clusterNameFilters []string
	var err error
	for i := 0; i < len(req.Filters); i++ {
		if req.Filters[i].Name == "Name" {
			nameFilters = req.Filters[i].Values
		} else if req.Filters[i].Name == "Status" {
			statusFilters, err = GetStatusIntByStatusStr(req.Filters[i].Values)
			if err != nil {
				logger.Errorf("Failed to GetStatusIntByStatusStr, error:%+v", err)
				return controller.InternalError, err.Error(), 0, nil
			}
		} else if req.Filters[i].Name == "Type" {
			typeFilters, err = GetStatusIntByStatusStr(req.Filters[i].Values)
			if err != nil {
				logger.Errorf("Failed to GetStatusIntByStatusStr, error:%+v", err)
				return controller.InternalError, err.Error(), 0, nil
			}
		} else if req.Filters[i].Name == "ClusterId" {
			// 只保留最后一个过滤器
			clusterIdFilters = nil

			// clusterGroupIds为空，表示所有集群
			if len(clusterGroupIds) > 0 {
				for _, id := range clusterGroupIds {
					if service.InSliceString(id, req.Filters[i].Values) {
						clusterIdFilters = append(clusterIdFilters, id)
					}
				}
			} else {
				clusterIdFilters = req.Filters[i].Values
			}

			if len(clusterIdFilters) == 0 {
				// 设置一个假的cluster serial id，得到想要的空结果
				clusterIdFilters = []string{constants.OCEANUS_FAKE_CLUSTER_SERIAL_ID}
			}
		} else if req.Filters[i].Name == "JobId" {
			serialIdFilters = req.Filters[i].Values
		} else if req.Filters[i].Name == "ClusterName" {
			clusterNameFilters = req.Filters[i].Values
		} else if req.Filters[i].Name == "Tags" {
			// 按标签过滤
			filterJobIdsByTags, empty, filterJobsByTagsErr := tag.FilterJobsByTags(req)
			if filterJobsByTagsErr != nil {
				logger.Errorf("reqId:%s Failed to FilterJobsByTags, error:%+v", req.RequestId, filterJobsByTagsErr)
				// 在有tags 过滤的情况下，没有符合条件的job 则给一个假job serial id,得到空结果
				serialIdFilters = []string{constants.OCEANUS_FAKE_JOB_SERIAL_ID}
			} else {
				if !empty {
					serialIdFilters = filterJobIdsByTags
				} else {
					// 在有tags 过滤的情况下，没有符合条件的job 则给一个假job serial id,得到空结果
					serialIdFilters = []string{constants.OCEANUS_FAKE_JOB_SERIAL_ID}
				}
			}
		}
	}

	listJobQuery := model.ListJobQuery{
		AppId:             int32(req.AppId),
		Regions:           []string{req.Region},
		SerialIds:         serialIdFilters,
		ItemSpaceIds:      itemSpcIds,
		Names:             nameFilters,
		IsVagueNames:      true,
		Status:            statusFilters,
		Type:              typeFilters,
		ClusterGroupIds:   clusterIdFilters,
		Offset:            req.Offset,
		Limit:             req.Limit,
		ClusterGroupNames: clusterNameFilters,
		OrderByList:       req.OrderByList,
	}
	jobCandidates, err := service.ListJobs(&listJobQuery)
	if err != nil {
		logger.Errorf("Failed to ListJobs, error:%+v", err)
		return controller.InternalError, err.Error(), 0, nil
	}

	for i := 0; i < len(jobCandidates); i++ {
		jobs = append(jobs, jobCandidates[i])
	}

	totalCount, err := GetJobsTotalCount(int32(req.AppId), req.Region, serialIdFilters, nameFilters, statusFilters, clusterIdFilters, clusterNameFilters, itemSpcIds, typeFilters)
	if err != nil {
		logger.Errorf("Failed to Get jobs total count, error: %+v", err)
		return controller.InternalError, controller.NULL, 0, nil
	}

	return controller.OK, controller.NULL, totalCount, jobs
}

type GetClusterGroup func(clusterGroupId int64) (*table2.ClusterGroup, error)

func getJobPubLishedJobConfig(jobs []*table.Job) (map[int64]*table3.JobConfig, error) {
	jobConfigMap := make(map[int64]*table3.JobConfig)
	if len(jobs) == 0 {
		return jobConfigMap, nil
	}
	jobConfigIds := make([]int64, 0, 0)
	for _, job := range jobs {
		jobConfigIds = append(jobConfigIds, job.PublishedJobConfigId)
	}
	jobConfigs, err := service3.ListJobConfigsByIds(jobConfigIds)
	if err != nil {
		return nil, err
	}
	for _, config := range jobConfigs {
		jobConfigMap[config.JobId] = config
	}
	return jobConfigMap, nil
}

func BuildSimpleJobSet(jobs []*table.Job) (jobSet []*model.JobSetItem, err error) {
	jobSet = make([]*model.JobSetItem, 0)
	if len(jobs) == 0 {
		return jobSet, nil
	}
	for _, job := range jobs {
		jobSetItem := &model.JobSetItem{}
		jobSetItem.Id = job.Id
		jobSetItem.JobId = job.SerialId
		jobSetItem.Region = job.Region
		jobSetItem.Zone = job.Zone
		jobSetItem.AppId = job.AppId
		jobSetItem.OwnerUin = job.OwnerUin
		jobSetItem.CreatorUin = job.CreatorUin
		jobSetItem.Name = job.Name
		jobSetItem.JobType = job.Type
		jobSetItem.FlinkVersion = job.FlinkVersion
		jobSet = append(jobSet, jobSetItem)
	}
	return jobSet, nil
}

func BuildJobSetFromJobEntity(req *model.DescribeJobsReq, jobs []*table.Job) (jobSet []*model.JobSetItem, err error) {
	jobSet = make([]*model.JobSetItem, 0)

	clusterGroupMap := make(map[int64]*table2.ClusterGroup)
	getClusterGroup := func(clusterGroupId int64) (*table2.ClusterGroup, error) {
		// return service2.ListClusterGroupById(clusterGroupId)
		if v, ok := clusterGroupMap[clusterGroupId]; ok {
			return v, nil
		}
		clusterGroup, err := service2.ListClusterGroupById(clusterGroupId)
		if err != nil {
			return nil, err
		}
		clusterGroupMap[clusterGroupId] = clusterGroup
		return clusterGroup, nil
	}
	for _, job := range jobs {
		if _, err := getClusterGroup(job.ClusterGroupId); err != nil {
			return nil, err
		}
	}
	jobConfigMap, err := getJobPubLishedJobConfig(jobs)
	if err != nil {
		return nil, fmt.Errorf("getJobPubLishedJobConfig error")
	}

	// find jobsItemSpaceId
	names, err := GetJobItemSpaceName(jobs)
	if err != nil {
		return nil, fmt.Errorf("getJobItemSpaceName error")
	}

	if len(req.OrderByList) == 0 {
		ch := make(chan *model.JobSetItem, len(jobs))
		waitGroup := &sync.WaitGroup{}
		waitGroup.Add(len(jobs))
		for _, job := range jobs {
			go func(job *table.Job) {
				defer waitGroup.Done()
				jobSetItem, e := buildJobSet(job, getClusterGroup, jobConfigMap, names)
				if e != nil {
					err = e
					ch <- nil
				} else {
					ch <- jobSetItem
				}
			}(job)
		}
		waitGroup.Wait()
		close(ch)
		if err != nil {
			return nil, err
		}
		for j := range ch {
			if j == nil {
				return nil, fmt.Errorf("cause happen")
			}
			jobSet = append(jobSet, j)
		}

		sort.Slice(jobSet, func(i, j int) bool {
			return jobSet[i].Id > jobSet[j].Id
		})
	} else {
		for _, job := range jobs {
			jobSetItem, err := buildJobSet(job, getClusterGroup, jobConfigMap, names)
			if err != nil {
				return nil, fmt.Errorf("buildJobSet error")
			}
			jobSet = append(jobSet, jobSetItem)
		}
	}

	// 标签信息
	tag.SetJobTagMapToJobSet(req.RequestId, req.Uin, req.Region, jobSet)

	return jobSet, nil
}

type ItemSpaceName struct {
	Id            int64
	ItemSpaceName string
	SerialId      string
}

func GetJobItemSpaceName(jobs []*table.Job) (itemMap map[int64]*ItemSpaceName, err error) {
	if len(jobs) == 0 {
		return
	}
	ids := make([]int64, 0)
	for _, job := range jobs {
		ids = append(ids, job.Id)
	}

	sql := "SELECT b.Id,a.ItemSpaceName,a.SerialId from ItemSpace a INNER JOIN Job b ON a.Id = b.ItemSpaceId "
	cond := dao.NewCondition()
	cond.In("b.Id", ids)
	where, args := cond.GetWhere()
	sql += where

	txManager := service5.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to execute the sql:%s, with args:%+v, with errors:%+v", sql, args, err)
		return nil, err
	}
	t := make(map[int64]*ItemSpaceName, 0)
	for i := 0; i < len(data); i++ {
		j := &ItemSpaceName{}
		err = util.ScanMapIntoStruct(j, data[i])
		if err != nil {
			return nil, errorcode.InternalErrorCode_UnMarshalFailed.NewWithErr(err)
		}
		t[j.Id] = j
	}
	return t, nil

}

func buildJobSet(job *table.Job, fn GetClusterGroup, jobConfigMap map[int64]*table3.JobConfig, itemMap map[int64]*ItemSpaceName) (*model.JobSetItem, error) {
	jobSetItem := &model.JobSetItem{}

	jobSetItem.Id = job.Id
	jobSetItem.JobId = job.SerialId
	jobSetItem.Region = job.Region
	jobSetItem.Zone = job.Zone
	jobSetItem.AppId = job.AppId
	jobSetItem.OwnerUin = job.OwnerUin
	jobSetItem.CreatorUin = job.CreatorUin
	jobSetItem.Name = job.Name
	// 这里的job可能是不同用户创建的，如果都去请求第三方接口判断是否开启默认告警，可能会导致接口超时。
	jobSetItem.OpenJobDefaultAlarm = job.OpenJobDefaultAlarm
	jobSetItem.JobType = job.Type
	jobSetItem.FlinkVersion = job.FlinkVersion
	if len(itemMap) > 0 {
		if job, ok := itemMap[job.Id]; ok {
			jobSetItem.WorkSpaceId = itemMap[job.Id].SerialId
			jobSetItem.WorkSpaceName = itemMap[job.Id].ItemSpaceName
		}
	}

	clusterName, err := GetClusterName(fn, job.ClusterGroupId)
	if err != nil {
		return nil, err
	}
	clusterSerialId, err := GetClusterSerialId(fn, job.ClusterGroupId)
	if err != nil {
		return nil, err
	}
	jobSetItem.ClusterName = clusterName
	jobSetItem.ClusterId = clusterSerialId

	jobSetItem.CuMem = job.CuMem
	jobSetItem.LatestJobConfigVersion = job.LatestJobConfigVersionId
	var (
		jmSpec float32
		tmSpec float32
	)
	if jobConfig, ok := jobConfigMap[job.Id]; !ok {
		logger.Debugf("No job config found for job, with job serialId:%s", job.SerialId)
		jobSetItem.PublishedJobConfigVersion = -1
	} else {
		jobSetItem.PublishedJobConfigVersion = jobConfig.VersionId
		jmSpec = jobConfig.JmCuSpec
		tmSpec = jobConfig.TmCuSpec
	}
	jobSetItem.Status = job.Status
	jobSetItem.RunningCu = jmSpec*float32(job.JmRunningCuNum) + tmSpec*float32(job.TmRunningCuNum)
	if job.RunningCu > 0 {
		jobSetItem.RunningCu = job.RunningCu
	}
	jobSetItem.RunningCuNum = int16(jobSetItem.RunningCu)
	jobSetItem.RunningCpu = service.GetFloat2Dot(jobSetItem.RunningCu)
	jobSetItem.RunningMem = service.GetFloat2Dot(jobSetItem.RunningCu * float32(job.CuMem))
	if job.RunningCpu != 0 && job.RunningMem != 0 {
		jobSetItem.RunningCpu = service.GetFloat2Dot(job.RunningCpu)
		jobSetItem.RunningMem = service.GetFloat2Dot(job.RunningMem)
		jobSetItem.RunningCu = service.GetCuNumFromCpuMem(job.RunningCpu, job.RunningMem, job.CuMem)
		jobSetItem.RunningCuNum = int16(jobSetItem.RunningCu)
	}
	if job.Status != constants.JOB_STATUS_RUNNING { // 如果作业不是运行中的状态，将RunningCuNum置为0
		jobSetItem.RunningCu = 0.0
		jobSetItem.RunningCuNum = 0
		jobSetItem.RunningCpu = 0
		jobSetItem.RunningMem = 0
	}
	jobSetItem.StatusDesc = GetStatusDescByStatus(job.Status)
	jobSetItem.CreateTime = service.SwitchDefaultTime(job.CreateTime)
	jobSetItem.StartTime = service.SwitchDefaultTime(job.StartTime)
	jobSetItem.StopTime = service.SwitchDefaultTime(job.StopTime)
	jobSetItem.UpdateTime = service.SwitchDefaultTime(job.UpdateTime)
	currentRunMillis := int64(0)
	if job.Status == constants.JOB_STATUS_RUNNING { // 如果作业正在运行，计算当前运行时间
		if job.CreateTime == "0000-00-00 00:00:00" {
			job.CreateTime = util.GetCurrentTime()
			logger.Errorf("Job createTime is 0000-00-00 00:00:00, set job createTime to currentTime, please check the program logic ", job.CreateTime)
		}
		currentRunMillis, err = service.GetCurrentRunMillis(job.StartTime)
		if err != nil {
			logger.Errorf("get job: %d %s startTime %s error: %v", job.Id, job.SerialId, job.StartTime, err)
		}
	}
	// 如果作业是停止或者暂停状态，将 startTime 设置为 ""
	switch job.Status {
	case constants.JOB_STATUS_PAUSED, constants.JOB_STATUS_STOPPED, constants.JOB_STATUS_FINISHED:
		jobSetItem.StartTime = ""
	}
	jobSetItem.CurrentRunMillis = currentRunMillis
	jobSetItem.TotalRunMillis = job.TotalRunMillis + currentRunMillis
	jobSetItem.Remark = job.Remark
	jobSetItem.Description = job.Description
	jobSetItem.LastOpResult = checkJobLastOpResult(job.LastOpResult)

	if cluster, err := service2.GetClusterByClusterId(job.ClusterId); err != nil {
		return nil, err
	} else {
		jobSetItem.WebUIUrl, err = GetJobWebUIURL(job, cluster, clusterSerialId)
		if err != nil {
			return nil, err
		}
		jobSetItem.SchedulerType = int64(cluster.SchedulerType)
	}
	clusterStatus, err := GetClusterStatus(fn, job.ClusterGroupId)
	if err != nil {
		return nil, err
	}
	jobSetItem.ClusterStatus = clusterStatus

	scalingType, err := checkJobIsScaling(job.SerialId)
	if err != nil {
		return nil, err
	}
	if err != nil {
		return nil, err
	}
	jobSetItem.ScalingType = scalingType
	jobSetItem.ProgressDesc = job.ProgressDesc
	jobSetItem.RunningCpu = service.GetFloat2Dot(jobSetItem.RunningCpu)
	jobSetItem.RunningMem = service.GetFloat2Dot(jobSetItem.RunningMem)
	jobSetItem.ContinueAlarm = job.ContinueAlarm
	return jobSetItem, nil
}

func checkJobLastOpResult(lastOpResult string) (result string) {
	arr := make([]string, 0)
	err := json.Unmarshal([]byte(lastOpResult), &arr)
	if err != nil {
		if strings.Contains(lastOpResult, constants.DEFAULT_JOB_ERROR_INFO_FROM_CA) {
			result = fmt.Sprintf("[\"%s\"]", constants.DEFAULT_JOB_ERROR_INFO_CN)
		} else if len(lastOpResult) > 0 {

			// 获取中文原因与事件特征的映射关系
			eventFeatureJsonStr, err := config.GetRainbowConfiguration("JobKubernetesEventMappings", "kubernetes_event_feature.csv")
			if err != nil {
				logger.Errorf("Failed to get k8s_event_feature.csv from Rainbow because %+v. ", err)
				result = fmt.Sprintf("[\"%s\", \"%s\"]", constants.DEFAULT_JOB_ERROR_INFO_CN_DETAIL, lastOpResult)
			}

			eventFeature := make(map[string]string, 0)
			err = json.Unmarshal([]byte(eventFeatureJsonStr), &eventFeature)
			if err != nil {
				logger.Errorf("Failed to Unmarshal event feature because %+v. ", err)
				result = fmt.Sprintf("[\"%s\", \"%s\"]", constants.DEFAULT_JOB_ERROR_INFO_CN_DETAIL, lastOpResult)
			}
			result = fmt.Sprintf("[\"%s\", \"%s\"]", eventFeature[constants.DEFAULT_JOB_ERROR_INFO_FROM_CA], lastOpResult)
		}
	} else {
		result = lastOpResult
	}
	return
}

func GetJobWebUIURL(job *table.Job, cluster *table2.Cluster, clusterSerialId string) (string, error) {
	// 运行中和操作中的任务需要返回webUIUrl
	if job.Status == constants.JOB_STATUS_RUNNING || job.Status == constants.JOB_STATUS_PROGRESS {
		// 实例状态为运行中
		jis, err := service.ListJobInstances(job.Id, constants.JOB_INSTANCE_STATUS_RUNNING)
		if err != nil || len(jis) != 1 || cluster.WebUIPrefix == "" {
			return "", nil
		}

		var webUIPrefix string
		if cluster.CrossTenantEniMode == constants.EnableStaticMode {
			if cluster.WebUIType == constants.PublicType {
				domain := configure_center.CC(job.Region).GetFlinkUiDomain()
				webUIPrefix = "https://" + job.Region + "." + domain + "/" + clusterSerialId + "/"
			} else {
				webUIPrefix = cluster.WebUIPrefix + clusterSerialId + "/"
			}
		} else {
			webUIPrefix = cluster.WebUIPrefix
		}
		dbUrl := webUIPrefix + jis[0].ApplicationId + "/?defaultToken=" + strconv.Itoa(GetPasswordSetStatus(cluster))
		realURL, err := service2.GetRealURL(cluster, dbUrl)
		if err != nil {
			return "", err
		}
		return realURL, nil
	}
	return "", nil
}

//func GetJobWebUIURLPrefix(job *table.Job, cluster *table2.Cluster, clusterSerialId string) (string, error) {
//	// 运行中和操作中的任务需要返回webUIUrl
//	if job.Status == constants.JOB_STATUS_RUNNING || job.Status == constants.JOB_STATUS_PROGRESS {
//		// 实例状态为运行中
//		jis, err := service.ListJobInstances(job.Id, constants.JOB_INSTANCE_STATUS_RUNNING)
//		if err != nil || len(jis) != 1 || cluster.WebUIPrefix == "" {
//			return "", nil
//		}
//
//		var webUIPrefix string
//		if cluster.CrossTenantEniMode == constants.EnableStaticMode {
//			if cluster.WebUIType == constants.PublicType {
//				domain := configure_center.CC(job.Region).GetFlinkUiDomain()
//				webUIPrefix = "https://" + job.Region + "." + domain + "/" + clusterSerialId + "/"
//			} else {
//				webUIPrefix = cluster.WebUIPrefix + clusterSerialId + "/"
//			}
//		} else {
//			webUIPrefix = cluster.WebUIPrefix
//		}
//		dbUrl := webUIPrefix + jis[0].ApplicationId + "/"
//		return dbUrl, nil
//	}
//	return "", nil
//}

// 是否是设置了密码 0: 已经设置密码，1: 默认密码
func GetPasswordSetStatus(cluster *table2.Cluster) int {
	tableService := service4.GetTableService()
	if authInfo, err := tableService.ListFlinkUiAuthInfo(cluster.ClusterGroupId); err != nil {
		return k8s.K8S_FLINK_WEBUI_PASSWD_SET
	} else if authInfo != nil {
		if err := authInfo.DecodePassword(); err != nil {
			return k8s.K8S_FLINK_WEBUI_PASSWD_SET
		} else if authInfo.Password == k8s.K8S_FLINK_WEBUI_PREFIX_PASSWD {
			return k8s.K8S_FLINK_WEBUI_PASSWD_NOTSET
		}
	}
	return k8s.K8S_FLINK_WEBUI_PASSWD_SET
}

func GetClusterName(fn GetClusterGroup, clusterGroupId int64) (string, error) {
	clusterGroup, err := fn(clusterGroupId)
	if err != nil {
		return "", err
	}

	if clusterGroup.Type == constants.CLUSTER_GROUP_TYPE_SHARED {
		return constants.DOUBLE_BAR, nil
	} else {
		return clusterGroup.Name, nil
	}
}

func GetClusterSerialId(fn GetClusterGroup, clusterGroupId int64) (string, error) {
	clusterGroup, err := fn(clusterGroupId)
	if err != nil {
		return "", err
	}

	if clusterGroup.Type == constants.CLUSTER_GROUP_TYPE_SHARED {
		return constants.DOUBLE_BAR, nil
	} else {
		return clusterGroup.SerialId, nil
	}
}

func GetClusterStatus(fn GetClusterGroup, clusterGroupId int64) (int8, error) {
	clusterGroup, err := fn(clusterGroupId)
	if err != nil {
		return 0, err
	}

	return clusterGroup.Status, nil
}

func GetStatusDescByStatus(status int8) string {
	switch status {
	case constants.JOB_STATUS_CREATE:
		return "create"
	case constants.JOB_STATUS_INITIALIZED:
		return "initialized" // 未发布
	case constants.JOB_STATUS_PROGRESS:
		return "progress" // 操作中
	case constants.JOB_STATUS_RUNNING:
		return "running" // 运行中
	case constants.JOB_STATUS_STOPPED:
		return "stopped" // 停止
	case constants.JOB_STATUS_FINISHED:
		return "finished" // 停止
	case constants.JOB_STATUS_PAUSED:
		return "paused" // 暂停
	case constants.JOB_STATUS_CONCERNING:
		return "concerning" // 故障
	default:
		return "unknown"
	}
}

func GetStatusByStatusDesc(statusDesc string) int8 {
	statusDesc = strings.ToLower(statusDesc)
	switch statusDesc {
	case "create":
		return constants.JOB_STATUS_CREATE
	case "initialized":
		return constants.JOB_STATUS_INITIALIZED
	case "progress":
		return constants.JOB_STATUS_PROGRESS
	case "running":
		return constants.JOB_STATUS_RUNNING
	case "stopped":
		return constants.JOB_STATUS_STOPPED
	case "finished":
		return constants.JOB_STATUS_FINISHED
	case "paused":
		return constants.JOB_STATUS_PAUSED
	case "concerning":
		return constants.JOB_STATUS_CONCERNING
	default:
		return 0
	}
}

func GetStatusIntByStatusStr(statusStr []string) ([]int64, error) {
	statusInts := make([]int64, 0)
	for i := 0; i < len(statusStr); i++ {
		statusInt, err := strconv.ParseInt(statusStr[i], 10, 64)
		if err != nil {
			return statusInts, err
		}
		statusInts = append(statusInts, statusInt)
	}

	return statusInts, nil
}

func DescribeFlinkSql(describeFlinkSqlReq *model.DescribeFlinkSqlReq) (err error, flinkSqlRsp *model.FlinkSqlRsp) {

	flinkSqlRsp = &model.FlinkSqlRsp{}
	flinkSqlRsp.SqlCode = constants.EMPTY

	req := &model.DescribeJobsReq{}
	req.AppId = describeFlinkSqlReq.AppId
	req.Region = describeFlinkSqlReq.Region
	req.JobIds = []string{describeFlinkSqlReq.JobId}

	listJobQuery := model.ListJobQuery{
		AppId:        int32(req.AppId),
		Regions:      []string{req.Region},
		SerialIds:    []string{describeFlinkSqlReq.JobId},
		IsVagueNames: false,
		Offset:       0,
		Limit:        0,
	}

	if describeFlinkSqlReq.JobConfigVersion == 0 {
		describeFlinkSqlReq.JobConfigVersion = -1
	}

	// 获取Job的自增Id
	jobs, err := service.ListJobs(&listJobQuery)
	if jobs == nil || len(jobs) != 1 {
		logger.Errorf(fmt.Sprintf("Invalid JobId: %s, jobs: %v", describeFlinkSqlReq.JobId, jobs))
		return errorcode.InvalidParameterValueCode.New(), nil
	}

	// 鉴权
	err = auth.InnerAuthById(describeFlinkSqlReq.WorkSpaceId, describeFlinkSqlReq.IsSupOwner, jobs[0].ItemSpaceId, describeFlinkSqlReq.AppId, describeFlinkSqlReq.SubAccountUin, describeFlinkSqlReq.Action, false)
	if err != nil {
		logger.Errorf("%s: User  uin %s is NOT authenticated, refuse to serve", req.RequestId, req.Uin)
		return err, nil
	}

	jobConfigs, err := service3.ListJobConfigs(jobs[0].Id, []int64{}, []int64{describeFlinkSqlReq.JobConfigVersion}, map[string][]string{})
	if err != nil || len(jobConfigs) != 1 {
		logger.Errorf(fmt.Sprintf("Params: %v,jobConfigs: %v", describeFlinkSqlReq, jobConfigs))
		return errorcode.InternalErrorCode_JobConfigNotFound.ReplaceDesc("Describe flink sql failed."), nil
	}

	programArgs := jobConfigs[0].ProgramArgs

	m := map[string]interface{}{}
	if err := json.Unmarshal([]byte(programArgs), &m); err != nil {
		logger.Errorf("ProgramArgs format error. must be json: %v, %s", programArgs, err.Error())
		return errorcode.FlinkSqlErrorCode_DescribeFailed.New(), flinkSqlRsp
	}

	if jsonIn, ok := m["EtlJobCanvas"]; ok {
		jsonStr := jsonIn.(string)
		jsonTransformation := service7.JsonTransformation{
			AppId: req.AppId,
		}
		// 1. sql转换
		sqlTransformed, err := jsonTransformation.Transform(jsonStr)
		logger.Debugf("Generated sql:%s", sqlTransformed)
		if err != nil {
			logger.Errorf("%s: Failed to convert JSON to SQL, %s", req.RequestId, err.Error())
			return errorcode.FlinkSqlErrorCode_DescribeFailed.New(), flinkSqlRsp
		}
		flinkSqlRsp.SqlCode = sqlTransformed
	}

	return nil, flinkSqlRsp
}

func checkJobIsScaling(JobSerialId string) (int8, error) {
	txManager := service5.GetTxManager()
	sql := "Select * from JobScaleRule where JobId = ? and Status = 1 "
	args := make([]interface{}, 0, 0)
	args = append(args, JobSerialId)
	cnt, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return constants.SCALING_TYPE_CLOSE, err
	}
	if cnt == 0 {
		return constants.SCALING_TYPE_CLOSE, nil
	}
	rule := &table3.JobScaleRule{}
	err = util.ScanMapIntoStruct(rule, data[0]) //将db查询出来的data强转成JobScaleRule对象
	if err != nil {
		logger.Errorf("%s Failed to convert bytes into table.JobScaleRule, with errors:%+v", data[0], err)
		return 0, errorcode.NewStackError(errorcode.InternalErrorCode_UnMarshalFailed, "", err)
	}
	if rule.RuleName == constants.SCALE_RULES_AUTO_SCALE_TIME_BASED {
		return constants.SCALING_TYPE_TIME_BASED, nil
	} else if rule.RuleName == constants.SCALE_RULES_AUTO_SCALE_BASIC {
		return constants.SCALING_TYPE_BASIC, nil
	} else {
		logger.Errorf("checkJobIsScaling has unknown scale type")
		return constants.SCALING_TYPE_CLOSE, errors.New("Unkown scale type")
	}
}
