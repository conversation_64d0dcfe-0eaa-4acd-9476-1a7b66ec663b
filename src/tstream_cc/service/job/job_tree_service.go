package service

import (
	"context"
	"encoding/hex"
	"encoding/json"
	"strconv"
	"sync"

	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/concurrent"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logging "tencentcloud.com/tstream_galileo/src/common/logger"
	logger "tencentcloud.com/tstream_galileo/src/main/credentials_provider/common/log"
	"tencentcloud.com/tstream_galileo/src/repo/db"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job"
	clustertable "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/job/jobtree"
)

var defaultService = NewJobTreeService()

// JobTreeService 作业树service
type JobTreeService struct {
}

// NewJobTreeService new job tree service
func NewJobTreeService() *JobTreeService {
	return &JobTreeService{}
}

// GetJobTreeService get job tree service
func GetJobTreeService() *JobTreeService {
	return defaultService
}

// Describe 分页查询, 支持各种条件筛选过滤
// 分页显示逻辑: https://doc.weixin.qq.com/flowchart/f4_ANQAkwaDACcCNq5UtOSpqTRaw6gOi?scode=AJEAIQdfAAoxusFHvMANQAkwaDACc
// 兼容历史逻辑的字段映射: https://doc.weixin.qq.com/sheet/e3_ACYAPgawAFQCN5qJ0MDNBSRCBAQQv?scode=AJEAIQdfAAoN3qK9HQACYAPgawAFQ&tab=BB08J2
// 普通场景下, 实际上目录数量不会很多, 主要是作业数量可能会很多。
// 假设极端情况下: 1000个目录, 10w个作业。
// 1. 在数据库里对作业分类统计(group by), 只返回每个目录对应的数量(历史页面也是要显示子目录的作业数量)
// 2. 拉取目录数据, 构建目录树
// 3. 如果当前目录层级需要返回的目录数量少于拉取数量, 在数据库里分页获取当前目录层级的作业列表。
// 4. 基于3, 对分页后的作业列表拉取数据填充。
func (s *JobTreeService) Describe(ctx context.Context,
	req *model.DescribeTreeJobsReq) (*model.DescribeTreeJobsRsp, error) {
	// 参数检查
	if err := s.checkDescribeRequest(req); err != nil {
		return nil, err
	}
	// 鉴权 (子账号，只能看见自己空间内绑定的集群，超管可以看到所有)
	spaceIdList, err := auth.ExtractSubUinAuthItemSpaceIdSet(req.WorkSpaceId, req.IsSupOwner,
		req.AppId, req.SubAccountUin, req.Uin, req.Region)
	if err != nil {
		logging.Errorf("call ExtractSubUinAuthItemSpaceIdSet failed, %+v", err)
		ec := errorcode.GetCode(err)
		if ec == errorcode.InternalErrorCode {
			return nil, errorcode.NewCodeErrorf(errorcode.FailedOperationCode, ec.GetCodeDesc())
		}
		return nil, err
	}
	if len(spaceIdList) == 0 {
		return nil, errorcode.NewCodeErrorf(errorcode.NoPrivilege, "has no privilege")
	}
	spaceId := spaceIdList[0]
	options, match, err := s.prepare(ctx, req, spaceId)
	if err != nil {
		return nil, err
	}
	if !match {
		return &model.DescribeTreeJobsRsp{
			RequestId: req.RequestId,
			HasMore:   false,
		}, nil
	}

	// 异常任务筛选需特殊处理
	exceptJobSet, filterAndNotMatch, err := s.prepareExceptionType(ctx, req, spaceId, options)
	if err != nil {
		return nil, err
	}
	if filterAndNotMatch {
		logging.Infof("not match exception type")
		return &model.DescribeTreeJobsRsp{
			RequestId: req.RequestId,
			HasMore:   false,
		}, nil
	}
	// 关键字要特殊处理
	sqlKeywordResult, filterAndNotMatch, err := s.prepareSqlKeyword(ctx, req, spaceId, options)
	if err != nil {
		return nil, err
	}
	if filterAndNotMatch {
		// 说明没有匹配到关键字
		logging.Infof("not match sql keyword")
		return &model.DescribeTreeJobsRsp{
			RequestId: req.RequestId,
			HasMore:   false,
		}, nil
	}

	jobCategorizer := jobtree.GetCategorizer(options.Category)
	if jobCategorizer == nil {
		return nil, errorcode.NewCodeErrorf(errorcode.UnsupportedOperationCode,
			"not supported category: %s", options.Category)
	}
	logging.Infof("page options: %s", options)
	pageResult, err := jobCategorizer.Page(ctx, options)
	if err != nil {
		return nil, err
	}
	rsp := s.convertToResponse(req.RequestId, pageResult)
	// 填充异常字段
	s.fillExceptionInfo(rsp, exceptJobSet)
	// 填充sqlcode
	s.fillDecodeSqlCode(rsp, sqlKeywordResult)
	// 拉取作业额外参数并填充详情
	s.fillJobDetail(ctx, rsp, pageResult)
	return rsp, nil
}

// prepare 如果返回false, 则表示不符合过滤条件, 直接回空包
func (s *JobTreeService) prepare(ctx context.Context, req *model.DescribeTreeJobsReq,
	spaceId int64) (*jobtree.DescribeOptions, bool, error) {
	options, err := s.convertToDescribeOptions(req)
	if err != nil {
		return nil, false, err
	}
	options.SpaceId = spaceId
	handleList := []func(ctx context.Context, req *model.DescribeTreeJobsReq,
		spaceId int64, options *jobtree.DescribeOptions) (bool, error){
		s.prepareCluster,
	}
	for _, handle := range handleList {
		ok, err := handle(ctx, req, spaceId, options)
		if err != nil {
			return nil, false, err
		}
		if !ok {
			return nil, false, nil
		}
	}
	return options, true, nil
}

// prepareExceptionType 返回异常作业合集和是否不符合条件, 如果返回不符合条件,则不用处理
func (s *JobTreeService) prepareExceptionType(ctx context.Context, req *model.DescribeTreeJobsReq,
	spaceId int64, options *jobtree.DescribeOptions) (*exceptionJobSet, bool, error) {
	exceptionValues := s.findFilterValue(req, model.TreeFilterKeyExceptionType)
	if len(exceptionValues) == 0 {
		return nil, false, nil
	}
	exceptionType := exceptionValues[0]
	logging.Infof("filter exception type: %s", exceptionType)
	exceptJobSet, err := s.getExceptionJobs(ctx, req, exceptionType)
	if err != nil {
		return nil, false, err
	}
	if options.MatchOpts == nil {
		options.MatchOpts = &jobtree.MatchOptions{}
	}
	var eventJobIdList []string
	var alarmJobIdList []string
	for v := range exceptJobSet.EventJobIdSet {
		eventJobIdList = append(eventJobIdList, v)
	}
	for v := range exceptJobSet.AlarmJobIdSet {
		alarmJobIdList = append(alarmJobIdList, v)
	}
	switch exceptJobSet.Type {
	case model.ExceptionTypeNormal:
		// 正常的就是都排除掉
		options.MatchOpts.JobIdFilterList = append(options.MatchOpts.JobIdFilterList, eventJobIdList...)
		options.MatchOpts.JobIdFilterList = append(options.MatchOpts.JobIdFilterList, alarmJobIdList...)
	case model.ExceptionTypeEvent:
		if len(eventJobIdList) == 0 {
			return nil, true, nil
		}
		options.MatchOpts.JobIdList = append(options.MatchOpts.JobIdList, eventJobIdList...)
	case model.ExceptionTypeAlarm:
		if len(alarmJobIdList) == 0 {
			return nil, true, nil
		}
		options.MatchOpts.JobIdList = append(options.MatchOpts.JobIdList, alarmJobIdList...)
	}

	return exceptJobSet, false, nil
}

type sqlKeywordMatchResult struct {
	JobsWithSqlCode map[string]*table.JobInfoForSqlCode
	Keyword         string
}

// prepareSqlKeyword 只有匹配了才会返回keyword
func (s *JobTreeService) prepareSqlKeyword(ctx context.Context, req *model.DescribeTreeJobsReq,
	spaceId int64, options *jobtree.DescribeOptions) (*sqlKeywordMatchResult, bool, error) {
	sqlKeywords := s.findFilterValue(req, model.TreeFilterKeySqlKeyword)
	if len(sqlKeywords) == 0 {
		return nil, false, nil
	}
	keyword := sqlKeywords[0]
	logging.Infof("filter sql keywords: %s", keyword)
	result, err := db.NewJobClient().SearchJobBySqlKeyword(ctx, req.AppId, spaceId, keyword)
	if err != nil {
		return nil, false, errorcode.NewCodeErrorf(errorcode.InternalErrorCode, "serach sql keyword failed")
	}
	if len(result) == 0 {
		logging.Infof("not match any code, keyword: %s", keyword)
		return nil, true, nil
	}
	jobIdList := make([]string, 0, len(result))
	jobsWithSqlCode := make(map[string]*table.JobInfoForSqlCode)
	for _, job := range result {
		jobIdList = append(jobIdList, job.SerialId)
		jobsWithSqlCode[job.SerialId] = job
	}
	options.MatchOpts.JobIdList = append(options.MatchOpts.JobIdList, jobIdList...)
	return &sqlKeywordMatchResult{
		JobsWithSqlCode: jobsWithSqlCode,
		Keyword:         keyword,
	}, false, nil
}

func (s *JobTreeService) prepareCluster(ctx context.Context, req *model.DescribeTreeJobsReq,
	spaceId int64, options *jobtree.DescribeOptions) (bool, error) {
	clusterNames := s.findFilterValue(req, model.TreeFilterKeyClusterName)
	clusterIds := s.findFilterValue(req, model.TreeFilterKeyClusterId)
	var clusterId, clusterName string
	if len(clusterIds) > 0 {
		clusterId = clusterIds[0]
	}
	if len(clusterNames) > 0 {
		clusterName = clusterNames[0]
	}
	if clusterId == "" && clusterName == "" {
		return true, nil
	}
	logging.Infof("filter cluster id: %s, cluster name: %s", clusterId, clusterName)
	clusterGroupList, err := db.NewClusterClient().SearchSpaceClusterGroupList(ctx, spaceId, clusterId, clusterName)
	if err != nil {
		return false, errorcode.NewCodeErrorf(errorcode.InternalErrorCode, "serach cluster group failed")
	}
	if len(clusterGroupList) == 0 {
		// 没有匹配的集群
		logging.Infof("not found any cluster, cluster id: %s, cluster name: %s", clusterId, clusterName)
		return false, nil
	}
	for _, clusterGroup := range clusterGroupList {
		options.MatchOpts.ClusterGroupIdList = append(options.MatchOpts.ClusterGroupIdList, clusterGroup.Id)
	}
	return true, nil
}

func (s *JobTreeService) checkDescribeRequest(req *model.DescribeTreeJobsReq) error {
	if req.WorkSpaceId == "" {
		return errorcode.NewCodeErrorf(errorcode.MissingParameterCode, "WorkSpaceId is empty")
	}
	if req.PageSize <= 0 {
		return errorcode.NewCodeErrorf(errorcode.InvalidParameterCode, "PageSize <= 0")
	}
	return nil
}

func (s *JobTreeService) convertToDescribeOptions(req *model.DescribeTreeJobsReq) (*jobtree.DescribeOptions, error) {
	category, err := s.fetchCategory(req)
	if err != nil {
		return nil, err
	}
	attach, err := decodeTreePageAttach(req.PageAttach)
	if err != nil {
		// 解析失败不影响
		logging.Errorf("decodeTreePageAttach failed, %+v", err)
	}
	searchOpts := &jobtree.SearchOptions{}
	matchOpts := &jobtree.MatchOptions{}
	for _, filter := range req.Filters {
		key, ok := model.ParseTreeFilterKey(filter.Name)
		if !ok {
			return nil, errorcode.NewCodeErrorf(errorcode.InvalidParameterCode,
				"invalid filter: %s", filter.Name)
		}
		if len(filter.Values) == 0 {
			continue
		}
		switch key {
		case model.TreeFilterKeyJobName:
			searchOpts.JobName = filter.Values[0]
		case model.TreeFilterKeyJobId:
			searchOpts.JobId = filter.Values[0]
		case model.TreeFilterKeyJobType:
			n, _ := strconv.ParseInt(filter.Values[0], 10, 64)
			matchOpts.JobType = &n
		case model.TreeFilterKeyJobStatus:
			n, _ := strconv.ParseInt(filter.Values[0], 10, 64)
			matchOpts.JobStatus = &n
		case model.TreeFilterKeyZone:
			matchOpts.JobZone = filter.Values[0]
		case model.TreeFilterKeyFolderName:
			searchOpts.FolderName = filter.Values[0]
		}
	}

	return &jobtree.DescribeOptions{
		AppId:      req.AppId,
		Region:     req.Region,
		ParentId:   req.ParentId,
		Category:   category,
		SearchOpts: searchOpts,
		MatchOpts:  matchOpts,
		PageAttach: attach,
		PageSize:   int(req.PageSize),
	}, nil
}

func (s *JobTreeService) fetchCategory(req *model.DescribeTreeJobsReq) (model.TreeCategory, error) {
	if req.Category == "" {
		// 默认返回目录
		return model.TreeCategoryNoop, nil
	}
	category, ok := model.ParseTreeCategory(req.Category)
	if !ok {
		return "", errorcode.NewCodeErrorf(errorcode.InvalidParameterCode,
			"invalid category: %s", req.Category)
	}
	return category, nil
}

func (s *JobTreeService) convertToResponse(requestId string,
	pageResult *jobtree.PageResult) *model.DescribeTreeJobsRsp {
	if pageResult == nil {
		return &model.DescribeTreeJobsRsp{RequestId: requestId}
	}
	rsp := &model.DescribeTreeJobsRsp{
		RequestId: requestId,
		HasMore:   pageResult.HasMore,
	}
	if pageResult.Current != nil {
		rsp.ParentId = pageResult.Current.ParentId
		rsp.Id = pageResult.Current.Id
		rsp.Name = pageResult.Current.Name
		rsp.JobNum = pageResult.Current.JobNum
	}
	for _, job := range pageResult.JobList {
		rsp.JobSet = append(rsp.JobSet, s.convertJobSet(job))
	}
	attach, err := encodeTreePageAttach(pageResult.PageAttach)
	if err != nil {
		// 如果失败了, 则返回没有后面数据, 否则下次请求数据会错乱
		rsp.HasMore = false
		logging.Errorf("call encodeTreePageAttach failed, %+v", err)
	}
	rsp.PageAttach = attach
	if len(pageResult.Children) == 0 {
		return rsp
	}
	rsp.Children = make([]*model.DescribeTreeJobsRsp, 0, len(pageResult.Children))
	for _, child := range pageResult.Children {
		rsp.Children = append(rsp.Children, &model.DescribeTreeJobsRsp{
			ParentId: child.ParentId,
			Id:       child.Id,
			Name:     child.Name,
			JobNum:   child.JobNum,
		})
	}
	return rsp
}

func (s *JobTreeService) convertJobSet(job *table.Job) *model.TreeJobSets {
	return &model.TreeJobSets{
		JobId:      job.SerialId,
		Name:       job.Name,
		JobType:    job.Type,
		RunningCu:  job.RunningCu,
		Status:     job.Status,
		RunningCpu: job.RunningCpu,
		RunningMem: job.RunningMem,
		AppId:      int64(job.AppId),

		OwnerUin:       job.OwnerUin,
		CreatorUin:     job.CreatorUin,
		CreateTime:     job.CreateTime,
		StartTime:      job.StartTime,
		StopTime:       job.StopTime,
		UpdateTime:     job.UpdateTime,
		TotalRunMillis: job.TotalRunMillis,
	}
}

func (s *JobTreeService) fillJobDetail(ctx context.Context, rsp *model.DescribeTreeJobsRsp,
	pageResult *jobtree.PageResult) {
	if len(rsp.JobSet) == 0 {
		return
	}
	var lock sync.Mutex
	_ = concurrent.GoAndWait(
		// 获取集群信息
		func() error {
			idList := make([]int64, 0, len(rsp.JobSet))
			for _, job := range pageResult.JobList {
				if job.ClusterGroupId <= 0 {
					continue
				}
				idList = append(idList, job.ClusterGroupId)
			}
			if len(idList) == 0 {
				logging.Info("not found any cluster")
				return nil
			}
			clusterGroupMap, err := db.NewClusterClient().BatchQueryClusterGroupByIds(ctx, idList)
			if err != nil {
				logging.Errorf("call db.BatchQueryClusterGroupBySerialIds failed, %+v", err)
				// 拉失败只是少显示了集群名字, 但是不能影响整体返回
				return nil
			}
			jobClusterMap := make(map[string]*clustertable.ClusterGroup)
			for _, job := range pageResult.JobList {
				clusterGroup, ok := clusterGroupMap[job.ClusterGroupId]
				if !ok {
					continue
				}
				jobClusterMap[job.SerialId] = clusterGroup
			}
			lock.Lock()
			defer lock.Unlock()
			for _, job := range rsp.JobSet {
				clusterGroup, ok := jobClusterMap[job.JobId]
				if !ok {
					continue
				}
				job.ClusterId = clusterGroup.SerialId
				job.ClusterName = clusterGroup.Name
			}
			return nil
		},
		// 获取作业scale规则
		func() error {
			jobIdList := make([]string, 0, len(rsp.JobSet))
			for _, jobSet := range rsp.JobSet {
				jobIdList = append(jobIdList, jobSet.JobId)
			}
			ruleMap, err := db.NewJobClient().BatchQueryJobScaleRuleByJobIds(ctx, jobIdList)
			if err != nil {
				logging.Errorf("call db.BatchQueryJobScaleRuleByJobIds failed, %+v", err)
				// 拉失败不能影响整体返回
				return nil
			}
			lock.Lock()
			defer lock.Unlock()
			for _, job := range rsp.JobSet {
				rule, ok := ruleMap[job.JobId]
				if !ok {
					continue
				}
				switch rule.RuleName {
				case constants.SCALE_RULES_AUTO_SCALE_TIME_BASED:
					job.ScalingType = constants.SCALING_TYPE_TIME_BASED
				case constants.SCALE_RULES_AUTO_SCALE_BASIC:
					job.ScalingType = constants.SCALING_TYPE_BASIC
				default:
					logger.Errorf("has unknown scale type, rule name:%s", rule.RuleName)
					job.ScalingType = constants.SCALING_TYPE_CLOSE
				}
			}
			return nil
		},
	)
}

func (s *JobTreeService) fillExceptionInfo(rsp *model.DescribeTreeJobsRsp, exceptJobSet *exceptionJobSet) {
	if len(rsp.JobSet) == 0 {
		return
	}
	if exceptJobSet == nil {
		return
	}
	for _, job := range rsp.JobSet {
		if exceptJobSet.EventJobIdSet != nil {
			if _, ok := exceptJobSet.EventJobIdSet[job.JobId]; ok {
				job.IsEvent = true
			}
		}
		if exceptJobSet.AlarmJobIdSet != nil {
			if _, ok := exceptJobSet.AlarmJobIdSet[job.JobId]; ok {
				job.IsAlarm = true
			}
		}
	}
}

func (s *JobTreeService) fillDecodeSqlCode(rsp *model.DescribeTreeJobsRsp, sqlKeywordResult *sqlKeywordMatchResult) {
	if len(rsp.JobSet) == 0 {
		return
	}
	if sqlKeywordResult == nil {
		return
	}
	if len(sqlKeywordResult.JobsWithSqlCode) == 0 {
		return
	}
	for _, job := range rsp.JobSet {
		jobWithSqlCode, ok := sqlKeywordResult.JobsWithSqlCode[job.JobId]
		if !ok {
			continue
		}
		job.DecodeSqlCode = extractContextAroundKeyword(jobWithSqlCode.DecodeSqlCode, []string{sqlKeywordResult.Keyword})
	}
}

func (s *JobTreeService) findFilterValue(req *model.DescribeTreeJobsReq, key model.TreeFilterKey) []string {
	for _, filter := range req.Filters {
		fk, ok := model.ParseTreeFilterKey(filter.Name)
		if !ok {
			continue
		}
		if fk == key {
			return filter.Values
		}
	}
	return nil
}

func (s *JobTreeService) getExceptionJobs(ctx context.Context, req *model.DescribeTreeJobsReq,
	exceptionType string) (*exceptionJobSet, error) {

	describeReq := &model.DescribeJobsExceptionReq{
		RequestBase: req.RequestBase,
		WorkSpaceId: req.WorkSpaceId,
	}
	describeRsp, err := describeJobsExceptionHandle(ctx, describeReq)
	if err != nil {
		return nil, err
	}
	if describeRsp == nil {
		return &exceptionJobSet{Type: exceptionType}, nil
	}
	exceptJobSet := &exceptionJobSet{
		Type:          exceptionType,
		EventJobIdSet: make(map[string]struct{}),
		AlarmJobIdSet: make(map[string]struct{}),
	}
	for _, info := range describeRsp.JobExceptions {
		if info.IsEvent {
			exceptJobSet.EventJobIdSet[info.JobId] = struct{}{}
		}
		if info.IsAlarm {
			exceptJobSet.AlarmJobIdSet[info.JobId] = struct{}{}
		}
	}
	return exceptJobSet, nil
}

var describeJobsExceptionHandle = func(ctx context.Context,
	req *model.DescribeJobsExceptionReq) (*model.DescribeJobsExceptionRsp, error) {
	// 异常这里和历史保持一致, 后面要优化需要找云监控的对齐方案
	code, codeDesc, rsp := DoDescribeJobsException(req)
	if code != controller.OK {
		return nil, errorcode.NewCodeErrorf(errorcode.InternalErrorCode, code, codeDesc)
	}
	return rsp, nil
}

// exceptionJobSet 异常任务信息
// type = ExceptionTypeAlarm 只要填充AlarmJobIdSet
// type = ExceptionTypeEvent 只要填充EventJobIdSet
// type = ExceptionTypeNormal 两个都要填充
type exceptionJobSet struct {
	Type          string // 参考
	AlarmJobIdSet map[string]struct{}
	EventJobIdSet map[string]struct{}
}

func encodeTreePageAttach(attach *jobtree.PageAttach) (string, error) {
	if attach == nil {
		return "", nil
	}
	buf, err := json.Marshal(attach)
	if err != nil {
		return "", err
	}
	return hex.EncodeToString(buf), nil
}

func decodeTreePageAttach(str string) (*jobtree.PageAttach, error) {
	if str == "" {
		return nil, nil
	}
	buf, err := hex.DecodeString(str)
	if err != nil {
		return nil, err
	}
	attach := new(jobtree.PageAttach)
	err = json.Unmarshal(buf, attach)
	return attach, err
}
