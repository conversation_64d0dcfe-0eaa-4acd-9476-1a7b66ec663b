package service

import (
	"errors"
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service5 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/watchdog"
)

/*
*

	通知 Galileo 作业正常运行 (Job: PROGRESS -> RUNNING; JobInstance: DEPLOYING -> RUNNING)
*/
func DoNotifyJobRunning(req *watchdog.NotifyJobReq) (status int64, msg string, resp *watchdog.NotifyJobResp, err error) {
	logger.Infof("%s: DoNotifyJobRunning API called for job id %d with %s", req.RequestId, req.JobId, req.ApplicationId)

	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Failed to notify job running, errors: %+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()

	txManager := service5.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		job, err := getJobById(req.JobId)
		if err != nil {
			logger.Errorf("%s: Failed to getRunningJobById for job id %d because %+v", req.RequestId, req.JobId, err)
			return err
		}
		if job.ManageType != constants.MANAGE_TYPE_EXTERNAL {
			return nil
		}
		// 更新 jobInstance info
		sql := "UPDATE JobInstance SET FlinkJobId=?, ApplicationId=?, FlinkJobPlan=? WHERE Id=?"
		tx.ExecuteSqlWithArgs(sql, req.FlinkJobId, req.ApplicationId, req.FlinkJobPlan, req.JobRuntimeId)
		return nil
	}).Close()
	logger.Infof("%s: DoNotifyJobRunning successful for job id %d", req.RequestId, req.JobId)
	return controller.SUCCESS, controller.NULL, &watchdog.NotifyJobResp{IsSucc: controller.OK}, nil
}

func getJobById(jobId int64) (*table.Job, error) {
	sql := "SELECT * FROM Job WHERE Id=? "
	args := make([]interface{}, 0)
	args = append(args, jobId)

	txManager := service5.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("exception occurs when query Job from db, with errors:%+v", err)
		return nil, err
	}

	job := &table.Job{}

	if len(data) == 0 {
		return nil, errors.New(fmt.Sprintf("cannot find any running job with id %d", jobId))
	}

	if len(data) != 1 {
		return nil, errors.New(fmt.Sprintf("Why?? Number of running jobs with id %d for status RUNNING is not 1 but %d", jobId, len(data)))
	}

	err = util.ScanMapIntoStruct(job, data[0])
	if err != nil {
		logger.Errorf("failed to convert bytes into table.Job, with errors:%+v", err)
		return nil, err
	}
	return job, nil
}
