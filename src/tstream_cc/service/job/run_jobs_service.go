package service

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"strconv"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service2 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model"
	model1 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/bucket"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job"
	model2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_config"
	cluster "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster_master"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job_config"
	table3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job_instance"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/watchdog"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service5 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/bucket"
	service6 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cos"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job_config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
	service4 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource"
	sql2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/sql"
	tableService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/tke"
	service7 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/variable"
)

/**
 *
 * <AUTHOR>
 * @time 2019/4/27
 * @version 0.4.0
 * @since 0.4.0
 * @see
 */
// assumeRole用户检验YUNAPI请求是否需要鉴权
// assumeRole 当运营平台运行时，需要调整assumeRole等于false 跳过校验鉴权
func DoRunJobs(req *model.RunJobsReq, assumeRole bool) (errCode string, errMsg string, rsp *model.RunJobRsp) {
	// 0. Print RequestId
	logger.Debugf("%s: RunJobs API called by AppId %d on jobs %+v", req.RequestId, req.AppId, req.RunJobDescriptions)
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("%s: Run batch of jobs panic, for runJobDescriptions:%+v, errors:%+v", req.RequestId, req.RunJobDescriptions, errs)
			if err, ok := errs.(error); ok {
				code := errorcode.GetCode(err)
				errCode = code.GetCodeStr()
				errMsg = fmt.Sprintf("%+v", err)
				logger.Errorf("%s: Run batch of jobs panic, errCode: %s errMsg: %s", req.RequestId, errCode, errMsg)
			} else {
				errCode = controller.InternalError
				errMsg = fmt.Sprintf("%+v", errs)
			}
			rsp = nil
		}
	}()
	// 1. 如果作业列表是空，返回错误
	if len(req.RunJobDescriptions) == 0 {
		msg := fmt.Sprintf("InvalidParameterValue.JobIds. No JobIds specified")
		logger.Error(req.RequestId + ": " + msg)
		return controller.InvalidParameterValue, msg, nil
	}

	//鉴权
	for _, value := range req.RunJobDescriptions {
		_, err := auth.InnerAuthJob(req.WorkSpaceId, req.IsSupOwner, value.JobId, int64(req.AppId), req.SubAccountUin, req.Region, req.Action)
		if err != nil {
			logger.Errorf("%s: User  uin %s is NOT authenticated, refuse to serve", req.RequestId, req.Uin)
			errCode := errorcode.GetCode(err)
			return errCode.GetCodeStr(), errCode.GetCodeDesc(), nil
		}
	}

	// 2. 对作业列表去重，如果size大于20，返回错误
	if len(req.RunJobDescriptions) > constants.OPERATION_BATCH_SIZE_UPPER_LIMIT {
		errMsg := fmt.Sprintf("At least Run %d Jobs one time", constants.OPERATION_BATCH_SIZE_UPPER_LIMIT)
		logger.Errorf("%s: Failed to run jobs. %s", req.RequestId, errMsg)
		return controller.UnsupportedOperation, errMsg, nil
	}

	// 3. 逐个检查作业是否存在以及是否允许启动或者恢复
	startJobDesps := make([]model.RunJobDescription, 0)
	for i := 0; i < len(req.RunJobDescriptions); i++ {
		desp := req.RunJobDescriptions[i]
		startJobDesps = append(startJobDesps, desp)
	}
	txManager := service2.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		err := DoStartJobs(req, startJobDesps, assumeRole, false, nil, tx)
		if err != nil {
			logger.Errorf("%s: Failed to DoStartJobs: %+v", req.RequestId, err)
		}
		return err
	}).Close()

	logger.Debugf("%s: Finished DoRunJobs", req.RequestId)
	return controller.OK, controller.NULL, &model.RunJobRsp{RequestId: req.RequestId}
}

func DoStartJobs(req *model.RunJobsReq, startJobDesps []model.RunJobDescription, assumeRole bool, isScaleJob bool,
	jobSerialId2ScaleJobDesp map[string]model.ScaleInnerJobDescription, tx *dao.Transaction) error {

	rjd2JobTable := map[model.RunJobDescription]*table.Job{}

	// 3. 逐个检查作业是否存在以及是否允许启动或者恢复
	type ResourceInfo struct {
		JobNeedCpu      float32
		JobNeedMem      float32
		ClusterMaxCu    float32
		TotalRunningCpu float32
		TotalRunningMem float32
		isEks           bool
		cluster         *cluster.Cluster
	}
	isInnerUser, err := service.IsInnerUser(req.AppId, req.Uin, req.SubAccountUin)
	if err != nil {
		logger.Errorf("%s: IsInnerUser error: %+v", req.RequestId, err)
		return err
	}

	resourceSumMap := map[int64]*ResourceInfo{}
	cgJobMap := make(map[string]*cluster.ClusterGroup)
	for i := 0; i < len(startJobDesps); i++ {
		runJobDescription := startJobDesps[i]
		// 3.1 检查作业是否存在
		job, err := service.WhetherJobExists(req.AppId, req.Region, runJobDescription.JobId)
		if err != nil {
			logger.Errorf("%s", err)
			return err
		}
		jobClusterGroup, err := service6.GetClusterGroupByClusterId(job.ClusterId)
		if err != nil {
			logger.Errorf("%s: ListClustersByClusterGroupId error: %+v", req.RequestId, err)
			return err
		}

		jobCluster, err := service6.GetActiveClusterByClusterGroupId(jobClusterGroup.Id)
		if err != nil {
			logger.Errorf("Failed to GetActiveClusterByClusterGroupId for %d because %+v", jobClusterGroup.Id, err)
			return err
		}
		// 选择“优先使用最新快照运行”时，所选批量运行作业如果存在可使用的快照，则会使用最新的快照运行（按时间），如果没有可用的快照，则不使用快照直接运行
		if runJobDescription.RunType == constants.JOB_RUN_TYPE_RUN_WITH_DEFAULT_SAVEPOINT {
			savepoints, err := service2.GetLatestActiveSavepointsByJobId(job.Id)
			var savepoint *watchdog.SavepointEntity
			for _, s := range savepoints {
				valid := savepointPathValid(s.Path, job, jobCluster)
				if valid {
					savepoint = s
					break
				}
			}
			if err != nil || savepoint == nil {
				logger.Debugf("get latest savepoint failed, err:#v", err)
			} else {
				runJobDescription.SavepointId = savepoint.SerialId
				runJobDescription.SavepointPath = savepoint.Path
			}
			runJobDescription.RunType = constants.JOB_RUN_TYPE_RUN
		}

		// 3.2 处理SQL作业的StartMode
		if job.Type == constants.JOB_TYPE_SQL || job.Type == constants.JOB_TYPE_ETL {
			if runJobDescription.RunType == constants.JOB_RUN_TYPE_RUN || runJobDescription.RunType == constants.JOB_RUN_TYPE_RESTART { // 运行和重启时, 允许选择时间点
				// 3.2.1 如果是启动/重启(非恢复)SQL作业，检查StartMode是否合规
				err := service.CheckStartMode(runJobDescription.StartMode)
				if err != nil {
					logger.Errorf("%s: Failed to run jobs, illegal StartMode: %s, error: %v", req.RequestId, runJobDescription.StartMode, err)
					return err
				}
			} else if runJobDescription.RunType == constants.JOB_RUN_TYPE_RESUME || runJobDescription.RunType == constants.JOB_RUN_TYPE_RESTART_WITH_SAVEPOINT {
				// 3.2.2 如果是恢复SQL作业，不能选择时间点, 所以填充StartMode为LATEST以防止Runner解析报错
				runJobDescription.StartMode = constants.START_MODE_LATEST
			} else {
				// 避免新增 StartMode 以后落入默认分支
				logger.Errorf("Unsupported StartMode: %s", runJobDescription.StartMode)
				return errors.New("unsupported StartMode")
			}
		}
		if job.Status == constants.JOB_STATUS_PROGRESS {
			msg := fmt.Sprintf("%s: Failed to publish job in progress status: JobId: %s, AppID: %d", req.RequestId, job.SerialId, job.AppId)
			logger.Errorf(req.RequestId + ":" + msg)
			return errors.New(msg)
		}
		//JobConfigVersion为0时，代表不传参数，此时查询job最新版本
		if runJobDescription.JobConfigVersion == 0 {
			runJobDescription.JobConfigVersion = int64(job.LatestJobConfigVersionId)
		}

		// 3.3 获取job运行所需要的jobConfig配置
		jobConfigs, err := service3.ListJobConfigs(job.Id, []int64{}, []int64{runJobDescription.JobConfigVersion}, nil)
		if err != nil {
			return err
		}
		if len(jobConfigs) == 0 {
			return errors.New(fmt.Sprintf("Job Config not found, with job Id:%s, with version:%d", job.SerialId, runJobDescription.JobConfigVersion))
		}
		jobConfig := jobConfigs[0]
		jobConfigResourceReady, err := service4.CheckJobConfigResourceRefReady(jobConfig.Id)
		if err != nil {
			return err
		}
		if !jobConfigResourceReady {
			return errors.New(fmt.Sprintf("Job ResourceConfig not ready, with job Id:%s, with job conf version:%d",
				job.SerialId, runJobDescription.JobConfigVersion))
		}

		// 如果是扩缩容作业 需要更新作业配置的并行度和作业CuMem
		if isScaleJob {
			scaleJobDesp := jobSerialId2ScaleJobDesp[job.SerialId]
			if scaleJobDesp.ScaledParallelism != 0 {
				jobConfig.DefaultParallelism = scaleJobDesp.ScaledParallelism
			}
			if scaleJobDesp.ScaledParallelismMem != 0 {
				job.CuMem = scaleJobDesp.ScaledParallelismMem
			}
		}

		// 3.4 检查配置是否正确
		err = service3.CheckCompositeParameters(job, jobConfig.SqlCode, jobConfig.EntrypointClass)
		if err != nil {
			return err
		}
		// 3.5 检查 RunType 是否合法
		if !WhetherLegalRunType(runJobDescription.RunType) {
			errMsg := fmt.Sprintf("Job[%s] in RunType `%d`, not supported", runJobDescription.JobId, runJobDescription.RunType)
			logger.Errorf("%s: Job[%s] in RunType `%d`, not supported", req.RequestId, runJobDescription.JobId, runJobDescription.RunType)
			return errors.New(errMsg)
		}

		// 3.5 检查作业 COS Bucket 设置及是否存在
		cosBucket, err := service3.GetRealCOSBucketFromJobConfig(jobConfig)
		if err != nil {
			logger.Errorf("%s: Failed to GetRealCOSBucketFromJobConfig because %+v", req.RequestId, err)
			return err
		}
		err = cos.CheckWhetherCOSBucketExists(req.Uin, req.SubAccountUin, req.Region, cosBucket)
		if err != nil {
			return err
		}

		// 3.6 检查作业能否被启动或者恢复或者重启
		if !WhetherJobCanBeRun(job, runJobDescription.RunType) {
			msg := fmt.Sprintf("RunType is not compatible with job [%s] in status `%d`. Only stopped jobs can be run, and only paused jobs can be resumed.", job.SerialId, job.Status)
			logger.Error(req.RequestId + ": " + msg)
			return errorcode.NewStackError(errorcode.InvalidParameterCode, msg, nil)
		}

		// 3.7 如果是重启作业,则需要考虑重新分配桶
		if isRunTypeRestart(&runJobDescription) {
			// 给要启动运行的作业创建 COS 桶
			err = service5.AssignJobBucket(CreateJobBucketRefReqWithOutBucketOnRestart(req, job.SerialId))
			if err != nil {
				logger.Errorf("%s: Failed to publish job, %s", req.RequestId, err.Error())
				return err
			}
		}

		// 3.8 如果切换集群，我们得考虑切换集群需不需要重新分配bucket
		if job.Status != constants.JOB_STATUS_PAUSED {
			job, err := SwitchActiveCluster(job)
			if err != nil {
				logger.Errorf("%s: Failed to SwitchActiveCluster because %+v", req.RequestId, err)
				return err
			}
			err = service5.AssignJobBucket(CreateJobBucketRefReqWithJob(job))
			if err != nil {
				logger.Errorf("%s: Failed to AssignJobBucket because %+v", req.RequestId, err)
				return err
			}
			rjd2JobTable[runJobDescription] = job
		} else {
			rjd2JobTable[runJobDescription] = job
		}

		cgJobMap[job.SerialId] = jobClusterGroup

		// 检查集群是否状态正常（有可能已被隔离, 不允许启动新作业）
		if jobClusterGroup.Status != constants.CLUSTER_GROUP_STATUS_RUNNING &&
			jobClusterGroup.Status != constants.CLUSTER_GROUP_STATUS_SCALE_PROGRESS {
			logger.Errorf("%s: Job cluster group %s is NOT in RUNNING or SCALING status but %d",
				jobClusterGroup.SerialId, jobClusterGroup.Status)
			return errorcode.NewStackError(errorcode.InvalidParameter_InvalidClusterId,
				"Cluster is in abnormal status", nil)
		}

		// 跳过对内部用户的检查
		if !isInnerUser {
			// len(jobClusterGroup) 必须大于 0
			jobNeedCpu, jobNeedMem := getJobNeedCpuMem(jobConfig, jobCluster.MemRatio)
			if isRunTypeRestart(&runJobDescription) {

				//jobCurCu, err := service8.GetTableService().GetJobRunningCu(job.SerialId)
				// 普通配置计算，高级配置直接不计算所需资源
				if jobConfig.ExpertModeConfiguration == "{}" || jobConfig.ExpertModeConfiguration == "" {
					jc, jm, tc, tm, err := service.GetJobRunningCPUAndMem(job)
					runningCpu := jc + tc
					runningMem := jm + tm
					if err != nil {
						return err
					}
					jobNeedCpu = jobNeedCpu - runningCpu
					jobNeedMem = jobNeedMem - runningMem
				}
			}

			// 获取这个作业的集群的runningCU
			totalRunningCpu, totalRunningMem, err := service.GetClusterRunningCpuAndMem(jobClusterGroup.SerialId, job.AppId, job.Region, jobCluster.MemRatio)
			if err != nil {
				logger.Errorf("%s: GetRunningCU error: %+v", req.RequestId, err)
				return err
			}

			if item, ok := resourceSumMap[job.ClusterId]; ok {
				item.JobNeedCpu += jobNeedCpu
				item.JobNeedMem += jobNeedMem
				item.TotalRunningCpu = totalRunningCpu
				item.TotalRunningMem = totalRunningMem
			} else {
				// 目前一个集群组只存在一个集群，所以集群组的CU数量和集群的CU数量是一致的，以后一个集群组存在多个集群，这里会存在着问题，整个页面的资源选择都会有问题
				// 这里只是记录
				cluster, err := service6.SelectActiveCluster(job.CreatorUin, job.ClusterGroupId, job.ClusterId, job.Type)
				if err != nil {
					logger.Errorf("%s: SelectActiveCluster error: %+v", req.RequestId, err)
					return err
				}
				isEks, err := service6.IsEks(cluster.ClusterGroupId)
				if err != nil {
					logger.Errorf("%s: 判断集群是不是按量付费失败, ClusterGroupId %d error: %+v", req.RequestId, cluster.ClusterGroupId, err)
					return err
				}
				resourceSumMap[job.ClusterId] = &ResourceInfo{
					TotalRunningCpu: totalRunningCpu,
					TotalRunningMem: totalRunningMem,
					JobNeedCpu:      jobNeedCpu,
					JobNeedMem:      jobNeedMem,
					ClusterMaxCu:    service.GetClusterTotalCU(jobClusterGroup),
					isEks:           isEks,
					cluster:         cluster,
				}
			}
		}
	}

	inWhite := auth.IsInWhiteList(int64(req.AppId), constants.WHITE_LIST_START_JOB_NO_CHECK)

	// 如果是非内部用户，则检查批量运行的作业所需要的默认并行度和集群当前的并行度是否满足
	if !(isInnerUser || inWhite) {

		for _, sumItem := range resourceSumMap {
			// eks 跳过检查
			if sumItem.isEks {
				continue
			}
			// 该集群作业的默认并行度 > (该集群的整体CU - 正在运行的CU)
			clusterFreeCpu := sumItem.ClusterMaxCu - sumItem.TotalRunningCpu
			clusterFreeMem := sumItem.ClusterMaxCu*float32(sumItem.cluster.MemRatio) - sumItem.TotalRunningMem
			// 超用太多之后，这里会是负数，缩容可能不会执行。
			if clusterFreeCpu < 0 {
				clusterFreeCpu = 0
			}
			if clusterFreeMem < 0 {
				clusterFreeMem = 0

			}
			if sumItem.JobNeedCpu > clusterFreeCpu+constants.FLOAT_TOLERATE || sumItem.JobNeedMem > clusterFreeMem+constants.FLOAT_TOLERATE {
				logger.Warningf("clusterFreeCpu(%f) < JobNeedCpu(%f) ... clusterFreeMem(%f) < JobNeedMem(%f)",
					clusterFreeCpu, sumItem.JobNeedCpu, clusterFreeMem, sumItem.JobNeedMem)
				errorMsg := fmt.Sprintf("Insufficient cluster resources, required Cpu %f > cluster FreeCu %f or required Mem %f > cluster FreeMem %f",
					sumItem.JobNeedCpu, clusterFreeCpu, sumItem.JobNeedMem, clusterFreeMem)
				logger.Errorf(errorMsg)
				return errorcode.ResourceInsufficientCode_Cu.ReplaceDesc(errorMsg)
			}
		}

	}

	// 4. 批量启动/恢复作业
	err = DoBatchRun(rjd2JobTable, req, isScaleJob, jobSerialId2ScaleJobDesp, tx, cgJobMap)
	if err != nil {
		logger.Errorf("%s: Failed to run batch of jobs, error: %+v", req.RequestId, err)
		return err
	}
	logger.Debugf("%s: RunJobs completed for AppId %d on jobs %+v", req.RequestId, req.AppId, req.RunJobDescriptions)
	return nil
}

func getJobNeedCpuMem(jobConfig *table2.JobConfig, memRatio int8) (float32, float32) {
	numberOfSlots := GetNumberOfTaskSlots(jobConfig)
	parallelism := float32(math.Ceil(float64(jobConfig.DefaultParallelism) / float64(numberOfSlots)))
	if jobConfig.ExpertModeConfiguration != "{}" && jobConfig.ExpertModeConfiguration != "" {
		return 0, 0
	}
	if jobConfig.JobManagerCpu != 0 {
		jmCpu := jobConfig.JobManagerCpu + jobConfig.TaskManagerCpu*parallelism
		jmMem := jobConfig.JobManagerMem + jobConfig.TaskManagerMem*parallelism
		return jmCpu, jmMem

	} else {
		jmNums := constants.DEFAULT_JM_RUNNING_CU * jobConfig.JmCuSpec
		tmNums := jobConfig.TmCuSpec * parallelism
		return jmNums + tmNums, (jmNums + tmNums) * float32(memRatio)
	}

}

func GetNumberOfTaskSlots(jobConfig *table2.JobConfig) (numberOfSlots int) {
	numberOfSlots = 1
	if len(jobConfig.Properties) > 0 {
		properties := make([]*model3.Property, 0)

		err := json.Unmarshal([]byte(jobConfig.Properties), &properties)
		if err != nil {
			logger.Errorf("jobConfig cannot Unmarshal to Property, error %v", err)
			return
		}

		for _, property := range properties {
			if property.Key == constants.NUMBER_OF_TASK_SLOTS_KEY {
				numberOfTaskSlots, err := strconv.Atoi(property.Value)
				if err != nil {
					logger.Errorf("value of properties.numberOfTaskSlots invalid: " + property.Value)
					return
				}
				numberOfSlots = numberOfTaskSlots
				return
			}
		}
	}
	return
}

func CreateJobBucketRefReqWithJob(job *table.Job) *model1.CreateJobBucketRefReq {
	policyReq := &model1.CreateJobBucketRefReq{}
	policyReq.AppId = job.AppId
	policyReq.Region = job.Region
	policyReq.CreatorUin = job.OwnerUin
	policyReq.JobId = job.SerialId
	return policyReq
}

func WhetherJobCanBeRun(job *table.Job, runType int8) bool {
	if (job.Status == constants.JOB_STATUS_PAUSED && (runType == constants.JOB_RUN_TYPE_RESUME || runType == constants.JOB_RUN_TYPE_RESTART ||
		runType == constants.JOB_RUN_TYPE_RESTART_WITH_SAVEPOINT || runType == constants.JOB_RUN_TYPE_RUN)) ||
		(job.Status == constants.JOB_STATUS_FINISHED && (runType == constants.JOB_RUN_TYPE_RUN || runType == constants.JOB_RUN_TYPE_RESTART)) ||
		(job.Status == constants.JOB_STATUS_STOPPED && (runType == constants.JOB_RUN_TYPE_RUN || runType == constants.JOB_RUN_TYPE_RESTART)) ||
		(job.Status == constants.JOB_STATUS_RUNNING && (runType == constants.JOB_RUN_TYPE_RESTART || runType == constants.JOB_RUN_TYPE_RESTART_WITH_SAVEPOINT)) ||
		(job.Status == constants.JOB_STATUS_INITIALIZED && runType == constants.JOB_RUN_TYPE_RUN) {
		return true
	} else {
		return false
	}
}

func DoBatchRun(req map[model.RunJobDescription]*table.Job, runJobReq *model.RunJobsReq, isScaleJob bool,
	jobSerialId2ScaleJobDesp map[string]model.ScaleInnerJobDescription, tx *dao.Transaction, cgJobMap map[string]*cluster.ClusterGroup) (err error) {
	logger.Debugf("%s: Started DoBatchRun", runJobReq.RequestId)

	for rjd, job := range req {
		var jobInstance *table3.JobInstance
		var oldJobRuntimeId int64
		oldJobRuntimeId = -1

		// 1. 更新作业的状态，以及JobConfig的状态
		jobConfigs, err := service3.ListJobConfigs(job.Id, []int64{}, []int64{rjd.JobConfigVersion}, nil)
		if err != nil {
			return err
		}
		if len(jobConfigs) == 0 {
			return errors.New(fmt.Sprintf("Job Config not found, with job Id:%s, with version:%d", job.SerialId, rjd.JobConfigVersion))
		}
		jobConfig := jobConfigs[0]
		if isScaleJob {
			if jobSerialId2ScaleJobDesp[job.SerialId].ScaledParallelism != 0 {
				jobConfig.DefaultParallelism = jobSerialId2ScaleJobDesp[job.SerialId].ScaledParallelism
			}
		}
		flinkVersion := service.GetFlinkVersion(job, jobConfig)
		if job.PublishedJobConfigId != jobConfig.Id {
			publishedJobConfigId := jobConfig.Id
			lastPublishedJobConfigId := job.PublishedJobConfigId
			// 1.1 更新作业 PublishedJobConfigId，LastPublishedJobConfigId
			_, err = service.UpdateJob(job.Id, publishedJobConfigId, lastPublishedJobConfigId, tx, flinkVersion) // 注意这里会把作业改为 PROGRESS 状态
			if err != nil {
				logger.Errorf("%s: Failed to update job, with job id: %d, with errors:%+v", runJobReq.RequestId, job.Id, err)
				return err
			}
			job.PublishedJobConfigId = jobConfig.Id

			// 更新jobConfigSqlcode
			if job.Type == constants.JOB_TYPE_SQL {
				err = service3.UpdateJobConfigSqlCode(context.Background(), job.Id, jobConfig.Id, jobConfig.SqlCode)
				if err != nil {
					logger.Errorf("%s: Failed to update jobConfigSqlcode, with job id: %d, with errors:%+v", runJobReq.RequestId, job.Id, err)
					return err
				}
			}
		}

		// 更新JobConfig状态
		_, err = service.UpdateJobConfig(job.Id, int64(jobConfig.VersionId), 0, constants.JOB_CONFIG_STATUS_ON_PUBLISHED, tx)
		if err != nil {
			logger.Errorf("%s: Failed to update job config, with jobConfig version:%d, with job id:%d, with errors:%+v", runJobReq.RequestId, jobConfig.VersionId, job.Id, err)
			return err
		}
		var customVariableCtx = &sql2.HandleCustomConstCtx{}
		if job.Type == constants.JOB_TYPE_SQL {
			customVariableCtx, err = handleCustomConst(runJobReq, job, cgJobMap, jobConfig, rjd, tx)
			if err != nil {
				logger.Errorf("%s:handleCustomConst has return error %v", err)
				return err
			}

			// 检查是否还在使用历史变量，如果是则更新为最新的变量关系
			err := handleVariableReference(job, tx)
			if err != nil {
				logger.Errorf("handleVariableReference has return error %v", err)
				return err
			}
		}

		// 对 JobConfig 的 Properties 做增强: 如果用户未设置 pipeline.max-parallelism: 2048 则默认填充
		jobConfig.Properties, err = enhanceJobConfigProperties(jobConfig.Properties)
		var jobRunningOrderId int64
		if err != nil {
			return err
		}
		jobConfig.Properties, err = checkJobConfigJobGraph(jobConfig.Properties, job, jobConfig, rjd.IsScale)
		if err != nil {
			return err
		}

		// add cos params for all 1.16 jobs
		if _, support := constants.CosParamFlinkVersion[flinkVersion]; support {
			jobConfig.Properties, err = addCOSParams(jobConfig.Properties, runJobReq.AppId, runJobReq.Region)
			if err != nil {
				return err
			}
		}

		// 2. 生成对应的启动命令
		if rjd.RunType == constants.JOB_RUN_TYPE_RESTART_WITH_SAVEPOINT || rjd.RunType == constants.JOB_RUN_TYPE_RESTART {
			oldJobRuntimeId, jobInstance, jobRunningOrderId, err = DoRestartJobDelegator(job, runJobReq, &rjd, jobConfig, tx)
		} else {
			jobInstance, jobRunningOrderId, err = DoStartJobDelegator(job, runJobReq, &rjd, jobConfig, tx)
		}
		if err != nil {
			logger.Errorf("%s: Failed to do run-job delegator, with errors:%+v", runJobReq.RequestId, err)
			return err
		}
		if jobRunningOrderId <= 0 {
			logger.Errorf("jobRunningOrderId is invalid value %d for %+v", jobRunningOrderId, runJobReq)
			return errors.New("jobRunningOrderId is invalid")
		}

		var metadata = ""
		var newSqlCode = jobConfig.SqlCode
		var excludeRefs = make([]*model2.ResourceRefItem, 0)
		if job.Type == constants.JOB_TYPE_SQL {
			// replace custom variable
			if rjd.CustomTimestamp > 0 || rjd.KafkaScanMode != "" || rjd.KafkaSpecificOffset != "" {
				logger.Infof("start replace custom variable,1 build context %v", customVariableCtx)
				replacedVariableSqlCodeStr, err := sql2.NewCustomConstHandler(customVariableCtx).ReplaceCustomConst()
				logger.Infof("start replace custom variable,2 replacedVariableSqlCodeStr %s", replacedVariableSqlCodeStr)
				if err != nil {
					logger.Errorf("handler custom const has return error %v", err)
					return err
				}
				jobConfig.SqlCode = replacedVariableSqlCodeStr
			}
			systemConf := sql2.GetSystemVariables(job.SerialId, cgJobMap[job.SerialId].SerialId)
			/*jobConfig.SqlCode, err = sql2.HandleSystemVariablesWithAes(systemConf, jobConfig.SqlCode)
			logger.Errorf("111 jobConfig.SqlCode: %s", jobConfig.SqlCode)
			if err != nil {
				logger.Errorf("handler SystemVariables has return error %v", err)
				return err
			}*/

			metadata, _, excludeRefs, newSqlCode, err = sql2.BuildMetadata4Command(context.Background(), jobConfig.ProgramArgs, jobConfig.SqlCode,
				runJobReq.Uin, int64(runJobReq.AppId), runJobReq.Region, flinkVersion, job.ItemSpaceId, true, systemConf)
			if err != nil {
				logger.Errorf("%s: Failed to build metadata for command, with errors:%+v", runJobReq.RequestId, err)
				return err
			}
			jobConfig.SqlCode = newSqlCode
		}
		// 3. 保存Command
		command, err := BuildRunCommandForClusterMaster(oldJobRuntimeId, jobInstance, job, jobConfig,
			runJobReq.RequestId, metadata, excludeRefs, jobRunningOrderId, rjd)
		if err != nil {
			logger.Errorf("%s: Failed to build run command, with errors:%+v", runJobReq.RequestId, err)
			return err
		}
		tx.SaveObject(command, "Command")

		// 4. 推送command到cluster-master
		archGeneration, err := tableService.GetTableService().GetTkeArchGeneration(command.ClusterId)
		if archGeneration >= constants.TKE_ARCH_GENERATION_V3 {
			notifyClusterSchedulerPullCommands(job, runJobReq.AppId, runJobReq.RequestId, command, flinkVersion)
		}

		// 5. 增加job的运行次数
		err = IncJobRunningOrderId(job.Id, tx)
		if err != nil {
			logger.Errorf("%s: Failed to IncJobRunningOrderId, with errors:%+v", runJobReq.RequestId, err)
			return err
		}
		//
		//// 扩缩容这里不置0，会导致扩容一直修改算子资源判断失败
		//sql := "UPDATE Job SET RunningCpu=0,RunningMem=0 WHERE Id=?" // 作业停止后需要把 RunningCuNum 设置为 0
		//tx.ExecuteSqlWithArgs(sql, job.Id)

		// 6. 修改作业的期望状态
		err = UpdateJobExpectedStatus(job.Id, constants.JOB_STATUS_RUNNING, tx)
		if err != nil {
			logger.Errorf("%s: Failed to UpdateJobExpectedStatus to %d, with errors:%+v", runJobReq.RequestId, constants.JOB_STATUS_RUNNING, err)
		}
	}

	logger.Debugf("%s: Finished DoBatchRun, command saved", runJobReq.RequestId)
	return nil
}

func handleCustomConst(
	runJobReq *model.RunJobsReq, job *table.Job,
	cgJobMap map[string]*cluster.ClusterGroup,
	jobConfig *table2.JobConfig,
	rjd model.RunJobDescription,
	tx *dao.Transaction) (*sql2.HandleCustomConstCtx, error) {
	flinkVersion := service.GetFlinkVersion(job, jobConfig)
	parseSqlService := sql2.NewParseSqlService(runJobReq.RequestId, runJobReq.Uin, int64(runJobReq.AppId), runJobReq.Region,
		flinkVersion, cgJobMap[job.SerialId].SerialId, job.ItemSpaceId)
	decrypt, err := util.AesDecrypt(jobConfig.SqlCode, constants.AES_ENCRYPT_KEY)
	if err != nil {
		logger.Errorf("reqId: %s,AesDecrypt sql with error %v", runJobReq.RequestId, err)
		return nil, err
	}
	parseSqlRsp, err := parseSqlService.ParseSql(context.Background(), base64.StdEncoding.EncodeToString([]byte(decrypt)), runJobReq.Region, 13)
	if err != nil {
		logger.Errorf("reqId: %s,parse sql with error %v", runJobReq.RequestId, err)
		return nil, err
	}
	customVariableCtx := sql2.NewHandleCustomVariableCtx().
		WithSqlCode(jobConfig.SqlCode).
		WithAes(true).
		WithCustomConstAndVals(map[string]interface{}{
			sql2.CustomTimestamp: rjd.CustomTimestamp,
			sql2.CustomScanMod:   rjd.KafkaScanMode,
			// sql2.SpecificOffset:  rjd.KafkaSpecificOffset,
		}).
		WithMetaTableEntry(parseSqlRsp.MetaRefs.TemporaryTables).
		WithNewVal(rjd.CustomTimestamp)
	containsCustomConstRst, err := sql2.NewCustomConstHandler(customVariableCtx).ContainsCustomConst()
	logger.Infof("%s: runJob containsCustomConst %v", runJobReq.RequestId, containsCustomConstRst)
	if err != nil {
		logger.Errorf("%s: Failed to call ContainsCustomConst, with jobConfig version:%d, with job id:%d, with errors:%+v", runJobReq.RequestId, jobConfig.VersionId, job.Id, err)
		return nil, err
	}
	// 如果启动参数带有自定义常量，则更新 jobConfig 中的 properties
	properties := jobConfig.Properties
	constPropertyMappingCopy := make(map[string]string)
	if containsCustomConstRst[sql2.CustomTimestamp] {
		constPropertyMappingCopy[sql2.JobConfigPropertyCustomTimestampKey] = strconv.FormatInt(rjd.CustomTimestamp, 10)
	}
	if containsCustomConstRst[sql2.CustomScanMod] {
		constPropertyMappingCopy[sql2.JobConfigPropertyCustomScanModKey] = rjd.KafkaScanMode
	}
	// if containsCustomConstRst[sql2.SpecificOffset] {
	// 	constPropertyMappingCopy[sql2.JobConfigPropertySpecificOffsetKey] = rjd.KafkaSpecificOffset
	// }
	for k, v := range constPropertyMappingCopy {
		properties, err = addCustomConstToJobConfigProperties(properties, k, v)
		if err != nil {
			logger.Errorf("%s: Failed to call addCustomConstToJobConfigProperties, with error %v", err)
			return nil, err
		}
		logger.Infof("%s: runJob addCustomConstToJobConfigProperties %s", runJobReq.RequestId, properties)
	}
	if properties != jobConfig.Properties {
		cnt, err := service.UpdateJobConfigProperties(job.Id, int64(jobConfig.VersionId), jobConfig.Id, properties, tx)
		if err != nil {
			logger.Errorf("%s: Failed to UpdateJobConfigProperties, with error %v", err)
			return nil, err
		}
		jobConfig.Properties = properties
		logger.Infof("%s: runJob UpdateJobConfigProperties %d", runJobReq.RequestId, cnt)
	}
	return customVariableCtx, nil
}

func WhetherLegalRunType(status int8) bool {
	switch status {
	case constants.JOB_RUN_TYPE_RESUME, constants.JOB_RUN_TYPE_RUN, constants.JOB_RUN_TYPE_RESTART, constants.JOB_RUN_TYPE_RESTART_WITH_SAVEPOINT:
		return true
	default:
		return false
	}
}

func BuildRunCommandForClusterMaster(oldJobRuntimeId int64, jobInstance *table3.JobInstance, job *table.Job, jobConfig *table2.JobConfig, requestId string, metadata string, excludeRefs []*model2.ResourceRefItem, jobRunningOrderId int64, rjd model.RunJobDescription) (*cluster_master.Command, error) {
	// 保险起见需要在重启时做个检查, 因为遇到过 oldJobRuntimeId 在重重检查下, 仍然是 -1 的情况
	if oldJobRuntimeId == -1 && (rjd.RunType == constants.JOB_RUN_TYPE_RESTART || rjd.RunType == constants.JOB_RUN_TYPE_RESTART_WITH_SAVEPOINT) {
		logger.Errorf("%s: Logic error? oldJobRuntimeId cannot be -1 for job %s", requestId, job.SerialId)
		return nil, errors.New("oldJobRuntimeId cannot be -1")
	}

	command := InitializeCommandForClusterMaster(job, constants.JOB_STATUS_RUNNING)
	refs, err := service4.GetResourceRefByJobConfigId(jobConfig.Id)
	logger.Infof("%s: GetResourceRefByJobConfigId refs: %s", requestId, refs)
	logger.Infof("%s: excludeRefs excludeRefs: %s", requestId, excludeRefs)
	if err != nil {
		return nil, err
	}
	// 过滤内置Connector [ProgramArgs.ExcludeConnector]
	if excludeRefs != nil && len(excludeRefs) > 0 {
		excludes := make(map[string]string)
		for _, ref := range excludeRefs {
			excludes[ref.ResourceId] = ref.ResourceId
		}
		for i, ref := range refs {
			if _, ok := excludes[ref.ResourceId]; ok {
				refs = append(refs[:i], refs[i+1:]...)
			}
		}
	}
	isRestart := false
	isResume := false
	isRestartWithSavepoint := false
	if rjd.RunType == constants.JOB_RUN_TYPE_RESUME || rjd.RunType == constants.JOB_RUN_TYPE_RESTART_WITH_SAVEPOINT {
		isResume = true
	}
	if rjd.RunType == constants.JOB_RUN_TYPE_RESTART || rjd.RunType == constants.JOB_RUN_TYPE_RESTART_WITH_SAVEPOINT {
		isRestart = true
		command.Action = constants.CLUSTER_MASTER_COMMAND_ACTION_RESTART_JOB
		if rjd.RunType == constants.JOB_RUN_TYPE_RESTART_WITH_SAVEPOINT {
			err = UpdateSavepointBeforeCancelWithSavepoint(nil, job)
			if err != nil {
				return nil, err
			}
			isRestartWithSavepoint = true
		}
	} else {
		command.Action = constants.CLUSTER_MASTER_COMMAND_ACTION_RUN_JOB
	}
	logger.Infof("%s: GetResourceRefByJobConfigId BuildRunJobParams refs: %s", requestId, refs)
	runJobParams, err := BuildRunJobParams(oldJobRuntimeId, jobInstance, job, jobConfig, rjd.StartMode,
		isRestart, isResume, isRestartWithSavepoint, "", refs, requestId, metadata, jobRunningOrderId, rjd.SavepointId, rjd.SavepointPath, rjd.IsScale)

	if err != nil {
		return nil, err
	}
	command.Params = runJobParams
	command.RunningOrderId = jobRunningOrderId

	return command, nil
}

func DoStartJobDelegator(job *table.Job, runJobReq *model.RunJobsReq, desp *model.RunJobDescription,
	jobConfig *table2.JobConfig, tx *dao.Transaction) (jobInstance *table3.JobInstance, jobRunningOrderId int64, err error) {
	// 1. Update Job status to "Progress"
	progressDesc := constants.RUN_JOB_PROGRESS_DESC
	if desp.SavepointPath != "" {
		progressDesc = constants.RUN_WITH_SAVEPOINT_JOB_PROGRESS_DESC
	}
	sql := "UPDATE Job SET Status=?, ProgressDesc=?, LastOpResult=? WHERE SerialId=? AND Status=?"
	result := tx.ExecuteSqlWithArgs(sql, constants.JOB_STATUS_PROGRESS, progressDesc, constants.OK_OP_RESULT_FOR_JOB, desp.JobId, job.Status)
	affectedRows, err := result.RowsAffected()
	if err != nil {
		logger.Errorf("%s: Failed to Update Job[%s] to `Progress` Status, with errors: %+v", runJobReq.RequestId, desp.JobId, err)
		return nil, -1, err
	}
	if affectedRows != 1 {
		logger.Errorf("%s: Failed to update job status to `Progress` because affected rows number is NOT 1 but %d", affectedRows)
		return nil, -1, errors.New("cannot update job status, maybe due to multiple same RunJobs requests at the same time")
	}

	err1 := CleanHistoryJobData(job, runJobReq.RequestId, tx)
	if err1 != nil {
		logger.Warningf("%s:run job %s CleanHistoryJobData error: %+v", runJobReq.RequestId, job.SerialId, err1)
	}
	// 2. 创建JobInstance
	jobInstance, err = BuildJobInstanceEntity(job.PublishedJobConfigId, job, jobConfig)
	if err != nil {
		logger.Errorf("%s: Failed to build job instance, with errors:%+v", runJobReq.RequestId, err)
		return nil, -1, err
	}
	jobRuntimeId := tx.SaveObject(jobInstance, "JobInstance")
	jobInstance.Id = jobRuntimeId
	return jobInstance, jobInstance.RunningOrderId, nil
}

func CleanHistoryJobData(job *table.Job, requestId string, tx *dao.Transaction) error {
	if err := MakeJobInstanceHistory(job.Id, tx); err != nil {
		logger.Errorf("%s: Failed to Make JobInstance[%s] to `History` Status, with errors: %+v", requestId, job.SerialId, err)
		return err
	}
	// 删除历史deployment
	jobClusterGroup, err := service6.GetClusterGroupByClusterId(job.ClusterId)
	if err != nil {
		logger.Errorf("%s: GetClusterGroupByClusterId error: %+v", requestId, err)
		return err
	}
	jobCluster, err := service6.GetClusterByClusterId(job.ClusterId)
	if err != nil {
		logger.Errorf("%s: GetClusterByJobId error: %+v", requestId, err)
		return err
	}
	ns := service6.GetDefaultNamespace(jobClusterGroup)
	k8sClient, err := tke.GetTkeService().KubernetesClientsetFromCluster(fmt.Sprintf("CleanHistoryJobData-%s", job.SerialId), jobCluster)
	if err != nil {
		return nil
	}
	logger.Infof("force delete deployments: LabelSelector jobSerialId==%s", job.SerialId)
	_, err = k8s.NewK8sService().DeleteDeployments(k8sClient, ns, metav1.ListOptions{
		LabelSelector: fmt.Sprintf("jobSerialId==%s", job.SerialId),
	})
	if err != nil {
		logger.Warningf("%s: failed to force delete deployments: LabelSelector jobSerialId==%s, with errors: %+v", requestId, job.SerialId, err)
	}
	return nil
}

func DoRestartJobDelegator(job *table.Job, runJobReq *model.RunJobsReq, req *model.RunJobDescription,
	jobConfig *table2.JobConfig, tx *dao.Transaction) (oldJobRuntimeId int64, jobInstance *table3.JobInstance, jobRunningOrderId int64, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("%s: Add PublishJob records to db panic ,for job:%+v, req:%+v, errors:%+v", runJobReq.RequestId, job, req, errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()

	// 1. Update Job status to "Progress"
	sql := "UPDATE Job SET Status=?, ProgressDesc=?, LastOpResult=? WHERE SerialId=? AND Status=?"
	result := tx.ExecuteSqlWithArgs(sql, constants.JOB_STATUS_PROGRESS, constants.RUN_WITH_SAVEPOINT_JOB_PROGRESS_DESC, constants.OK_OP_RESULT_FOR_JOB, job.SerialId, job.Status)
	affectedRows, err := result.RowsAffected()
	if err != nil {
		logger.Errorf("%s: Failed to Update Job[%s] to `Progress` Status, with errors: %+v", runJobReq.RequestId, job.SerialId, err)
		return -1, nil, -1, err
	}
	if affectedRows != 1 {
		logger.Errorf("%s: Failed to update job status to `Progress` because affected rows number is %d", affectedRows)
		return -1, nil, -1, errors.New("cannot update job status, maybe due to multiple same RunJobs requests at the same time")
	}

	if jobConfig == nil {
		jobConfigs, err := service3.ListJobConfigs(job.Id, []int64{}, []int64{req.JobConfigVersion}, nil)
		if err != nil {
			logger.Errorf("%s: Failed to get job config entity, errors: %+v", runJobReq.RequestId, err)
			return -1, nil, -1, err
		}
		if len(jobConfigs) == 0 {
			return -1, nil, -1, errors.New("query size is empty")
		}
		jobConfig = jobConfigs[0]
	}
	newLatestJobConfigId := jobConfig.Id
	// 1. 如果作业处于运行状态，则获取旧的JobRuntimeId
	oldJobRuntimeId, err = GetOldJobRuntimeId(job.Id, job.Status)
	if err != nil {
		return -1, nil, -1, err
	}
	// 2. 如果作业处于运行状态，则更新旧JobInstance的状态
	if job.Status == constants.JOB_STATUS_RUNNING { // Fixme 此操作与按量计费有关，谨慎
		err = MakeOldJobInstanceHistory(job.Id, oldJobRuntimeId, tx)
		if err != nil {
			return -1, nil, -1, err
		}
	}

	// 3. 创建新的JobInstance
	jobInstance, err = BuildJobInstanceEntity(newLatestJobConfigId, job, jobConfig)
	if err != nil {
		return -1, nil, -1, err
	}
	jobRuntimeId := tx.SaveObject(jobInstance, "JobInstance")

	jobInstance.Id = jobRuntimeId

	// 4. 创建资源引用
	if job.Type == constants.JOB_TYPE_JAR {
		createJobConfigReq, err := buildJobConfigReqOnRestart(runJobReq, req, jobConfig)
		if err != nil {
			return -1, nil, -1, err
		}
		success, err := service4.DoCreateResourceRefs(createJobConfigReq, newLatestJobConfigId)
		if err != nil {
			logger.Errorf("%s: DoPublishJob -> Failed to create Resource Refs, error: %+v", runJobReq.RequestId, err)
			return -1, nil, -1, err
		}

		if !success {
			logger.Errorf("%s: DoPublishJob -> Failed to create Resource Refs, SUCCESS: %+v", runJobReq.RequestId, success)
			return -1, nil, -1, err
		}
	}

	job.LatestJobConfigId = newLatestJobConfigId

	return oldJobRuntimeId, jobInstance, jobInstance.RunningOrderId, nil
}

func CreateJobBucketRefReqWithOutBucketOnRestart(req *model.RunJobsReq, jobId string) *model1.CreateJobBucketRefReq {
	policyReq := &model1.CreateJobBucketRefReq{}
	policyReq.AppId = req.AppId
	policyReq.Region = req.Region
	policyReq.CreatorUin = req.Uin
	policyReq.JobId = jobId
	return policyReq
}

func buildJobConfigReqOnRestart(req *model.RunJobsReq, desp *model.RunJobDescription, jobConfig *table2.JobConfig) (createJobConfigReq *model2.CreateJobConfigReq, err error) {
	createJobConfigReq = &model2.CreateJobConfigReq{}

	createJobConfigReq.Region = req.Region
	createJobConfigReq.AppId = req.AppId
	createJobConfigReq.SubAccountUin = req.SubAccountUin
	createJobConfigReq.Uin = req.Uin
	createJobConfigReq.RequestId = req.RequestId
	createJobConfigReq.Version = req.Version

	createJobConfigReq.JobId = desp.JobId
	createJobConfigReq.EntrypointClass = jobConfig.EntrypointClass
	createJobConfigReq.ProgramArgs = jobConfig.ProgramArgs
	createJobConfigReq.Remark = jobConfig.Remark
	resourceRefs, err := service4.GetResourceRefByJobConfigId(jobConfig.Id)
	if err != nil {
		return nil, err
	}
	createJobConfigReq.ResourceRefs = resourceRefs

	return createJobConfigReq, nil
}

func isRunTypeRestart(rjd *model.RunJobDescription) bool {
	return rjd.RunType == constants.JOB_RUN_TYPE_RESTART_WITH_SAVEPOINT || rjd.RunType == constants.JOB_RUN_TYPE_RESTART
}

func checkJobConfigJobGraph(properties string, job *table.Job, jobConfig *table2.JobConfig, isScale bool) (string, error) {
	dynamicProperties := make([]*model3.Property, 0)
	err := json.Unmarshal([]byte(properties), &dynamicProperties)
	if err != nil {
		// 防御式编程, 理论上不会格式错乱
		return properties, errorcode.NewStackError(errorcode.InternalErrorCode, "JobConfig Properties JSON unmarshal error", err)
	}
	// recover 和 普通启动作业的话 isScale是false，就可以用参数控制
	for _, property := range dynamicProperties {
		if !isScale && property.Key == constants.START_USE_JOB_GRAPH && property.Value == "false" {
			// 不使用job graph 启动作业
			return properties, nil
		}
	}
	if job.Type == constants.JOB_TYPE_JAR && auth.IsInWhiteList(int64(job.AppId), constants.WHITE_LIST_SCALE_VERTEX_PARALLELISM) {
		if jobConfig.AutoScaleJobGraph != "" {
			//s, err := base64.StdEncoding.DecodeString(graph.JobGraph)
			//if err != nil {
			//	logger.Errorf("decode job graph failed:%s",err)
			//	return "", err
			//}
			s := base64.StdEncoding.EncodeToString([]byte(jobConfig.AutoScaleJobGraph))
			logger.Debugf("AutoScale - jobgraph encode %s", s)
			dynamicProperties = append(dynamicProperties, &model3.Property{
				Key:   constants.JOB_START_JOB_GRAPH,
				Value: s,
			})
			//decodeString, err := base64.StdEncoding.DecodeString(graph.JobGraph)
			//if err != nil {
			//	return properties, errorcode.NewStackError(errorcode.InternalErrorCode, "JobConfig Graph decode error", err)
			//}
			//graph := &table2.FlinkJobVertice{}
			//err = json.Unmarshal(decodeString, graph)
			//parallelism := getJobGraphMaxParallelism(graph.Vertices)
			//logger.Debugf("Update job config parallelism by jobgraph")
			//jobConfig.DefaultParallelism = int16(parallelism)
			//txManager := service2.GetTxManager()
			//txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
			//	_, err := updateJobConfigDefaultParallelism(job.Id, jobConfig.Id, int16(parallelism), tx)
			//	if err != nil {
			//		return err
			//	}
			//	return nil
			//}).Close()
		}
	}
	enhancedDynamicProperties, err := json.Marshal(dynamicProperties)
	if err != nil {
		// 防御式编程, 理论上不会格式错乱
		return properties, errorcode.NewStackError(errorcode.InternalErrorCode, "JobConfig Properties JSON marshal error", err)
	}

	return string(enhancedDynamicProperties), nil
}

// 对 JobConfig 的 Properties 做一些动态增强, 例如补全用户未设置的必选值 "pipeline.max-parallelism"
func enhanceJobConfigProperties(properties string) (string, error) {
	if properties == "" {
		return service3.GetDefaultMaxParallelismKvPair(), nil
	}

	foundUserDefinedMaxParallelism := false
	dynamicProperties := make([]*model3.Property, 0)
	err := json.Unmarshal([]byte(properties), &dynamicProperties)
	if err != nil {
		// 防御式编程, 理论上不会格式错乱
		return properties, errorcode.NewStackError(errorcode.InternalErrorCode, "JobConfig Properties JSON unmarshal error", err)
	}
	for _, property := range dynamicProperties {
		if property.Key == constants.MAX_PARALLELISM_KEY {
			foundUserDefinedMaxParallelism = true
		}
	}
	if !foundUserDefinedMaxParallelism {
		dynamicProperties = append(dynamicProperties, &model3.Property{
			Key:   constants.MAX_PARALLELISM_KEY,
			Value: constants.MAX_PARALLELISM_DEFAULT_VALUE,
		})
		enhancedDynamicProperties, err := json.Marshal(dynamicProperties)
		if err != nil {
			// 防御式编程, 理论上不会格式错乱
			return properties, errorcode.NewStackError(errorcode.InternalErrorCode, "JobConfig Properties JSON marshal error", err)
		}
		return string(enhancedDynamicProperties), nil
	}

	return properties, err
}

func addCustomConstToJobConfigProperties(properties string, key, val string) (string, error) {
	if properties == "" || key == "" || val == "" {
		return properties, nil
	}
	dynamicProperties := make([]*model3.Property, 0)
	err := json.Unmarshal([]byte(properties), &dynamicProperties)
	if err != nil {
		return properties, errorcode.NewStackError(errorcode.InternalErrorCode, "JobConfig Properties JSON unmarshal error", err)
	}
	foundCustomConst := false
	for _, property := range dynamicProperties {
		if property.Key == key {
			foundCustomConst = true
			property.Value = val
		}
	}
	if !foundCustomConst {
		dynamicProperties = append(dynamicProperties, &model3.Property{
			Key:   key,
			Value: val,
		})
	}
	enhancedDynamicProperties, err := json.Marshal(dynamicProperties)
	if err != nil {
		return properties, errorcode.NewStackError(errorcode.InternalErrorCode, "JobConfig Properties JSON marshal error", err)
	}
	return string(enhancedDynamicProperties), nil
}

func addCOSParams(props string, appId int32, region string) (string, error) {
	properties := make([]*model3.Property, 0)
	if props != "" {
		err := json.Unmarshal([]byte(props), &properties)
		if err != nil {
			logger.Errorf("properties cannot Unmarshal to Property")
			return "", err
		}
	}

	cosProperties := service.GetCOSNFlinkConf(region, appId, properties)
	properties = append(properties, cosProperties...)

	marshalBytes, err := json.Marshal(properties)
	if err != nil {
		logger.Errorf("properties cannot Marshal to json")
		return "", err
	}
	return string(marshalBytes), nil
}

func handleVariableReference(job *table.Job, tx *dao.Transaction) error {
	references, err := service7.GetVariableReference(job.PublishedJobConfigId, job.ItemSpaceId)
	if err != nil {
		logger.Errorf("Failed to GetVariableReference, with error %v", err)
		return err
	}
	for _, reference := range references {
		variable, err := service7.GetVariable(reference.VariableId, "", "", job.OwnerUin, int64(job.AppId), job.Region, job.ItemSpaceId, -1)
		if err != nil {
			logger.Errorf("Failed to GetVariable, with error %v", err)
			return err
		}
		// 状态为历史状态则表示变量有修改，需要替换为最新的变量引用
		if variable.Status == constants.VARIABLE_STATUS_HISTORY {
			newestVariable, err := service7.GetVariable(-1, "", variable.SerialId, job.OwnerUin, int64(job.AppId), job.Region, job.ItemSpaceId, constants.VARIABLE_STATUS_ACTIVE)
			if err != nil {
				logger.Errorf("Failed to GetVariable, with error %v", err)
				return err
			}
			reference.VariableId = newestVariable.Id
			_, err = service7.UpdateVariableReference(reference, tx)
			if err != nil {
				logger.Errorf("Failed to UpdateVariableReference, with error %v", err)
				return err
			}
		}
	}
	return nil
}
