package jobtree

import (
	"context"
	"fmt"
	"sort"
	"strconv"

	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logging "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/repo/db"
)

// clusterCategrizer 集群分类器
type clusterCategrizer struct {
}

// Page 按照集群分类
// 集群分页只有二级, 一级是集群, 二级只有作业
func (c *clusterCategrizer) Page(ctx context.Context, options *DescribeOptions) (*PageResult, error) {
	if options.ParentId == "" {
		return c.pageClusterLayer(ctx, options)
	}
	return c.pageJobLayer(ctx, options)
}

func (c *clusterCategrizer) pageClusterLayer(ctx context.Context, options *DescribeOptions) (*PageResult, error) {
	ok, err := c.filterByFolderName(ctx, options)
	if err != nil {
		return nil, err
	}
	if !ok {
		// 指定了搜索目录并且没有搜索匹配的目录
		logging.Info("not search folder result, return empty page")
		return emptyPageResult(options.ParentId), nil
	}

	cond, _ := convertToJobCondition(options)
	countResultList, err := db.NewJobClient().CountJobGroupByClusterGroupId(ctx, cond)
	if err != nil {
		return nil, errorcode.NewCodeErrorf(errorcode.InternalErrorCode, err.Error())
	}
	if len(countResultList) == 0 {
		logging.Info("not result, return empty page")
		return emptyPageResult(options.ParentId), nil
	}
	// 根据id拉取集群信息组装
	var clusterGroupIdList []int64
	for _, cr := range countResultList {
		id, err := strconv.ParseInt(cr.CountName, 10, 64)
		if err != nil {
			logging.Errorf("parse cluster id failed, %+v", err)
			return nil, errorcode.NewCodeErrorf(errorcode.InternalErrorCode, err.Error())
		}
		clusterGroupIdList = append(clusterGroupIdList, id)
	}
	clusterGroupMap, err := db.NewClusterClient().BatchQueryClusterGroupByIds(ctx, clusterGroupIdList)
	if err != nil {
		return nil, errorcode.NewCodeErrorf(errorcode.InternalErrorCode, err.Error())
	}
	if len(clusterGroupMap) == 0 {
		logging.Info("not found any cluster")
		return emptyPageResult(options.ParentId), nil
	}

	parent := &Node{
		Id:   "root",
		Name: "作业目录",
	}
	for _, countResult := range countResultList {
		parent.JobNum += countResult.Count
	}
	createTimeMap := make(map[string]string)
	children := make([]*Node, 0, len(countResultList))
	for _, cr := range countResultList {
		id, _ := strconv.ParseInt(cr.CountName, 10, 64)
		clusterGroup, ok := clusterGroupMap[id]
		if !ok {
			continue
		}
		node := &Node{
			Id:       clusterGroup.SerialId,
			Name:     clusterGroup.Name,
			ParentId: parent.Id,
			JobNum:   cr.Count,
		}
		children = append(children, node)
		createTimeMap[node.Id] = clusterGroup.CreateTime
	}
	// 按照创建时间降序
	sort.Slice(children, func(i, j int) bool {
		return createTimeMap[children[i].Id] > createTimeMap[children[j].Id]
	})
	if options.PageAttach != nil && options.PageAttach.LastId != "" {
		idx := -1
		for i, node := range children {
			if node.Id == options.PageAttach.LastId {
				idx = i
				break
			}
		}
		if idx == len(children)-1 {
			return &PageResult{Current: parent, PageAttach: options.PageAttach, HasMore: false}, nil
		}
		if idx >= 0 {
			children = children[idx+1:]
		}
	}
	hasMore := false
	if len(children) > options.PageSize {
		children = children[:options.PageSize]
		hasMore = true
	}
	return &PageResult{
		Current:  parent,
		Children: children,
		HasMore:  hasMore,
		PageAttach: &PageAttach{
			LastType: typeFolder,
			LastId:   children[len(children)-1].Id,
		},
	}, nil
}

func (c *clusterCategrizer) pageJobLayer(ctx context.Context, options *DescribeOptions) (*PageResult, error) {
	clusterGroupMap, err := db.NewClusterClient().BatchQueryClusterGroupBySerialIds(ctx, []string{options.ParentId})
	if err != nil {
		return nil, errorcode.NewCodeErrorf(errorcode.InternalErrorCode, "batch query cluster group failed, %+v", err)
	}
	clusterGroup, ok := clusterGroupMap[options.ParentId]
	if !ok {
		logging.Infof("not found cluster, parent id %s", options.ParentId)
		return emptyPageResult(options.ParentId), nil
	}
	cond, _ := convertToJobCondition(options)
	cond.Eq("ClusterGroupId", clusterGroup.Id)
	// 先拉计数
	countResultList, err := db.NewJobClient().CountJobGroupByClusterGroupId(ctx, cond)
	if err != nil {
		return nil, errorcode.NewCodeErrorf(errorcode.InternalErrorCode, err.Error())
	}
	if len(countResultList) == 0 {
		logging.Info("not result, return empty page")
		return emptyPageResult(options.ParentId), nil
	}
	current := &Node{
		Id:     options.ParentId,
		Name:   clusterGroup.Name,
		JobNum: countResultList[0].Count,
	}

	lastId := int64(-1)
	if options.PageAttach != nil && options.PageAttach.LastId != "" {
		// 上一次的作业id
		lastId, _ = strconv.ParseInt(options.PageAttach.LastId, 10, 64)
	}
	// 拉取作业, 这里为了简单判断是否还有更多作业, 额外拉取1条数据即可
	jobList, err := db.NewJobClient().PageJobSortById(ctx, lastId, options.PageSize+1, cond)
	if err != nil {
		return nil, errorcode.NewCodeErrorf(errorcode.InternalErrorCode, "list job failed")
	}
	if len(jobList) == 0 {
		return &PageResult{
			Current:    current,
			HasMore:    false,
			PageAttach: options.PageAttach,
		}, nil
	}
	hasMore := false
	if len(jobList) > options.PageSize {
		jobList = jobList[:options.PageSize]
		hasMore = true
	}

	return &PageResult{
		Current: current,
		JobList: jobList,
		HasMore: hasMore,
		PageAttach: &PageAttach{
			LastType: typeJob,
			LastId:   fmt.Sprintf("%d", jobList[len(jobList)-1].Id),
		},
	}, nil
}

func (c *clusterCategrizer) filterByFolderName(ctx context.Context, options *DescribeOptions) (bool, error) {
	if options.SearchOpts == nil {
		return true, nil
	}
	if options.SearchOpts.FolderName == "" {
		return true, nil
	}
	clusterGroupList, err := db.NewClusterClient().SearchSpaceClusterGroupList(ctx, options.SpaceId,
		"", options.SearchOpts.FolderName)
	if err != nil {
		return false, errorcode.NewCodeErrorf(errorcode.InternalErrorCode, "serach cluster group failed")
	}
	if len(clusterGroupList) == 0 {
		// 没有匹配的集群
		logging.Infof("not found any cluster, cluster name: %s", options.SearchOpts.FolderName)
		return false, nil
	}
	for _, clusterGroup := range clusterGroupList {
		options.MatchOpts.ClusterGroupIdList = append(options.MatchOpts.ClusterGroupIdList, clusterGroup.Id)
	}
	return true, nil
}
