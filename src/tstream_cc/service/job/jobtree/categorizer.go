package jobtree

import (
	"context"

	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job"
)

var categorizerMap = map[model.TreeCategory]Categorizer{
	model.TreeCategoryFolderName:  &folderCategorizer{},
	model.TreeCategoryClusterName: &clusterCategrizer{},
	model.TreeCategoryCreateTime:  &timeCategorizer{},
	model.TreeCategoryNoop:        &noopCategorizer{},
}

type Categorizer interface {
	// Page 分页, specifyJobIdList为可以选参数, 用来指定固定的作业id列表
	Page(ctx context.Context, options *DescribeOptions) (*PageResult, error)
}

// GetCategorizer get categorizer
func GetCategorizer(category model.TreeCategory) Categorizer {
	return categorizerMap[category]
}
