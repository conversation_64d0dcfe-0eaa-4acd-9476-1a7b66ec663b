package jobtree

import (
	"encoding/json"
	"fmt"

	"tencentcloud.com/tstream_galileo/src/common/dao"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job"
)

const (
	typeFolder = "folder"
	typeJob    = "job"
)

// PageAttach tree page attach
type PageAttach struct {
	LastId   string // 如果是作业, 则这里存储的是作业里db的id, 不是SerialId
	LastType string // 参考上面的typeFolder, typeJob
}

// DescribeOptions 查询参数
type DescribeOptions struct {
	AppId      int64
	Region     string
	SpaceId    int64
	ParentId   string
	Category   model.TreeCategory
	SearchOpts *SearchOptions
	MatchOpts  *MatchOptions
	PageAttach *PageAttach
	PageSize   int
}

// String for format
func (options *DescribeOptions) String() string {
	buf, err := json.Marshal(options)
	if err != nil {
		return fmt.Sprintf("invalid options: %+v", err)
	}
	return string(buf)
}

// SearchOptions 搜索参数, 模糊匹配
type SearchOptions struct {
	JobName string
	JobId   string
	// FolderName 此目录搜索非作业目录，而是分类对应的目录名称
	// 比如集群分类:集群名称, 目录分类:作业目录名称, 时间分类:时间目录名称
	// 所以每个分类单独处理
	FolderName string
}

// MatchOptions 过滤参数, 完全匹配
type MatchOptions struct {
	JobType            *int64 // 根据是否为空指针判断是否设置值
	JobStatus          *int64 // 根据是否为空指针判断是否设置值
	JobZone            string
	JobIdList          []string
	JobIdFilterList    []string // 需要过滤的jobIdList
	ClusterGroupIdList []int64
	FolderIdList       []string
}

// PageResult 分页结果
type PageResult struct {
	Current    *Node
	JobList    []*table.Job
	Children   []*Node
	PageAttach *PageAttach
	HasMore    bool // 是否还有更多数据
}

func emptyPageResult(parentId string) *PageResult {
	return &PageResult{
		Current: &Node{
			Id:     parentId,
			JobNum: 0,
		},
		HasMore: false,
	}
}

// Node 节点信息
type Node struct {
	Id                    string
	Name                  string
	ParentId              string
	JobNum                int64 // 表示当前目录 + 子目录的作业数量
	JobNumWithoutChildren int64 // 表示当前目录不包含子目录的作业数量
}

// convertToJobCondition 返回条件集合和是否有搜索筛选条件 (不包含parentId, parentId在不同分类里有特殊含义)
func convertToJobCondition(options *DescribeOptions) (*dao.Condition, bool) {
	cond := dao.NewCondition()
	cond.Eq("AppId", options.AppId) // AppId是索引放在开头
	cond.Eq("Region", options.Region)
	cond.Eq("ItemSpaceId", options.SpaceId)
	hasSearch := fillSearchOptions(cond, options.SearchOpts)
	hasMatch := fillMatchOptions(cond, options.MatchOpts)
	return cond, hasSearch || hasMatch
}

func fillSearchOptions(cond *dao.Condition, searchOpts *SearchOptions) bool {
	if searchOpts == nil {
		return false
	}
	hasCond := false
	if searchOpts.JobName != "" {
		hasCond = true
		cond.Like("Name", "%"+searchOpts.JobName+"%")
	}
	if searchOpts.JobId != "" {
		hasCond = true
		cond.Like("SerialId", "%"+searchOpts.JobId+"%")
	}
	return hasCond
}

func fillMatchOptions(cond *dao.Condition, matchOpts *MatchOptions) bool {
	if matchOpts == nil {
		return false
	}
	hasCond := false
	if matchOpts.JobType != nil {
		hasCond = true
		cond.Eq("`Type`", *matchOpts.JobType)
	}
	if matchOpts.JobStatus != nil {
		hasCond = true
		cond.Eq("Status", *matchOpts.JobStatus)
	}
	if matchOpts.JobZone != "" {
		hasCond = true
		cond.Eq("Zone", matchOpts.JobZone)
	}
	if len(matchOpts.JobIdList) > 0 {
		hasCond = true
		cond.In("SerialId", matchOpts.JobIdList)
	}
	if len(matchOpts.JobIdFilterList) > 0 {
		hasCond = true
		cond.NIn("SerialId", matchOpts.JobIdFilterList)
	}
	if len(matchOpts.ClusterGroupIdList) > 0 {
		hasCond = true
		cond.In("ClusterGroupId", matchOpts.ClusterGroupIdList)
	}
	if len(matchOpts.FolderIdList) > 0 {
		hasCond = true
		cond.In("FolderId", matchOpts.FolderIdList)
	}
	return hasCond
}
