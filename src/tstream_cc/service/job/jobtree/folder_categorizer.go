package jobtree

import (
	"context"
	"fmt"
	"strconv"

	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logging "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/repo/db"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job"
)

// folderCategorizer 目录分类器
type folderCategorizer struct {
}

// Page 按照目录分类
// 注意: 如果没有搜索或者筛选条件, 即使是空目录也要显示
func (c *folderCategorizer) Page(ctx context.Context, options *DescribeOptions) (*PageResult, error) {
	ok, err := c.filterByFolderName(ctx, options)
	if err != nil {
		return nil, err
	}
	if !ok {
		// 指定了搜索目录并且没有搜索匹配的目录
		logging.Info("not search folder result, return empty page")
		return emptyPageResult(options.ParentId), nil
	}
	// 先获取计数, 然后分页
	cond, hasSearchOrMatch := convertToJobCondition(options)
	countResultList, err := db.NewJobClient().CountJobGroupByFolderId(ctx, cond)
	if err != nil {
		return nil, errorcode.NewCodeErrorf(errorcode.InternalErrorCode, "count job failed")
	}
	if len(countResultList) == 0 && hasSearchOrMatch {
		// 有过滤和筛选之后, 没有计数则返回
		logging.Info("not result, return empty page")
		return emptyPageResult(options.ParentId), nil
	}
	// 这里要拉取所有作业目录是因为计算作业数量的时候, 子目录的作业数量也要计算在父目录上
	folderList, err := c.listAllFolder(ctx, options)
	if err != nil {
		return nil, err
	}

	countResultMap := db.TransCountResultsToMap(countResultList)
	parent, allChildren := c.fetchAllChildren(folderList, options.ParentId, countResultMap)
	if hasSearchOrMatch {
		// 如果有搜索和筛选, 需要过滤作业数量为0的节点
		allChildren = c.filterFolderWithJob(allChildren)
	}
	// 先对目录分页, 然后再对作业分页
	children, hasMoreFolder := c.pageFolder(allChildren, options.PageAttach, options.PageSize)
	// 如果目录已经刚好等于请求数量, 则直接返回
	if len(children) == options.PageSize {
		hasMore := hasMoreFolder
		if !hasMore {
			// 目录没有了, 还要判断是否还有作业
			hasMore = parent.JobNumWithoutChildren > 0
		}
		return &PageResult{
			Current:  parent,
			Children: children,
			PageAttach: &PageAttach{
				LastType: typeFolder,
				LastId:   children[len(children)-1].Id,
			},
			HasMore: hasMore,
		}, nil
	}

	if options.MatchOpts != nil && len(options.MatchOpts.FolderIdList) > 0 {
		// 如果搜索了目录id/目录名字(也就是指定了目录id, 如果当前层级不在指定的目录内, 则不返回作业)
		match := false
		for _, id := range options.MatchOpts.FolderIdList {
			if parent.Id == id {
				match = true
				break
			}
		}
		if !match {
			pageResult := &PageResult{
				Current:  parent,
				Children: children,
				PageAttach: &PageAttach{
					LastType: typeFolder,
				},
				HasMore: false,
			}
			if len(children) > 0 {
				pageResult.PageAttach.LastId = children[len(children)-1].Id
			}
			logging.Infof("not match folder, not page job, id:%s", parent.Id)
			return pageResult, nil
		}
	}
	remainingSize := options.PageSize - len(children)
	logging.Infof("page job, remaining size: %d", remainingSize)
	jobList, hasMoreJob, err := c.pageJob(ctx, options, remainingSize)
	if err != nil {
		return nil, err
	}
	pageResult := &PageResult{
		Current:  parent,
		Children: children,
		JobList:  jobList,
		HasMore:  hasMoreJob,
		PageAttach: &PageAttach{
			LastType: typeJob,
		},
	}
	if len(jobList) > 0 {
		pageResult.PageAttach.LastId = fmt.Sprintf("%d", jobList[len(jobList)-1].Id)
	}
	return pageResult, nil
}

func (c *folderCategorizer) listAllFolder(ctx context.Context, options *DescribeOptions) ([]*Node, error) {
	rootNode := &Node{
		Id:   "root",
		Name: "作业目录",
	}
	treeDataList, err := db.NewJobClient().ListJobTree(ctx, options.AppId, options.SpaceId)
	if err != nil {
		return nil, errorcode.NewCodeErrorf(errorcode.InternalErrorCode, "list job tree failed")
	}
	if len(treeDataList) == 0 {
		// 要返回默认的root
		return []*Node{rootNode}, nil
	}
	nodes := make([]*Node, 0, len(treeDataList)+1)
	for _, treeData := range treeDataList {
		node := &Node{
			Id:       treeData.FolderId,
			Name:     treeData.FolderName,
			ParentId: treeData.ParentId,
		}
		nodes = append(nodes, node)
	}
	nodes = append(nodes, rootNode)
	return nodes, nil
}

func (c *folderCategorizer) fetchAllChildren(nodes []*Node, parentId string,
	countResultMap map[string]int64) (*Node, []*Node) {
	if parentId == "" {
		parentId = "root"
	}
	nodeMap := make(map[string]*Node)
	for _, node := range nodes {
		nodeMap[node.Id] = node
	}
	var parentNode *Node
	var targetChildren []*Node
	for _, node := range nodes {
		if node.Id == parentId {
			parentNode = node
		}
		if node.ParentId == parentId {
			targetChildren = append(targetChildren, node)
		}
		// 计算作业数量
		node.JobNumWithoutChildren = countResultMap[node.Id]
		node.JobNum += node.JobNumWithoutChildren
		curParentId := node.ParentId
		// 所有的父节点都加上, 注意加上的是当前目录独立拥有的作业数量
		for {
			curParent, ok := nodeMap[curParentId]
			if !ok {
				break
			}
			curParent.JobNum += node.JobNumWithoutChildren
			if curParent.ParentId == "" {
				break
			}
			curParentId = curParent.ParentId
		}
	}
	return parentNode, targetChildren
}

func (c *folderCategorizer) filterFolderWithJob(folderList []*Node) []*Node {
	var targetFolderList []*Node
	for _, node := range folderList {
		if node.JobNum == 0 {
			continue
		}
		targetFolderList = append(targetFolderList, node)
	}
	return targetFolderList
}

// pageFolder 目录是内存分页
// 返回分页后的列表和是否还有更多
func (c *folderCategorizer) pageFolder(allChildren []*Node, pageAttach *PageAttach, pageSize int) ([]*Node, bool) {
	if len(allChildren) == 0 {
		return nil, false
	}
	if pageAttach != nil && pageAttach.LastType == typeJob {
		// 上次分页拉取最后是作业, 则直接返回即可
		return nil, false
	}
	if pageAttach != nil && pageAttach.LastId != "" {
		idx := -1
		for i, node := range allChildren {
			if node.Id == pageAttach.LastId {
				idx = i
				break
			}
		}
		if idx == len(allChildren)-1 {
			// 上次拉取已经是最后一个了, 返回没有
			return nil, false
		}
		if idx >= 0 {
			allChildren = allChildren[idx+1:]
		}
	}
	if pageSize >= len(allChildren) {
		return allChildren, false
	}
	return allChildren[:pageSize], true
}

func (c *folderCategorizer) pageJob(ctx context.Context, options *DescribeOptions,
	pageSize int) ([]*table.Job, bool, error) {
	cond, _ := convertToJobCondition(options)
	// 这里要加上parentId过滤, 因为页面是一层层拉的
	parentId := options.ParentId
	if parentId == "" {
		parentId = "root"
	}
	cond.Eq("FolderId", parentId)

	lastId := int64(-1)
	if options.PageAttach != nil && options.PageAttach.LastType == typeJob && options.PageAttach.LastId != "" {
		lastId, _ = strconv.ParseInt(options.PageAttach.LastId, 10, 64)
	}
	// 拉取作业, 这里为了简单判断是否还有更多作业, 额外拉取1条数据即可
	jobList, err := db.NewJobClient().PageJobSortById(ctx, lastId, pageSize+1, cond)
	if err != nil {
		return nil, false, errorcode.NewCodeErrorf(errorcode.InternalErrorCode, "list job failed")
	}
	if len(jobList) == 0 {
		return nil, false, nil
	}
	hasMore := false
	if len(jobList) > pageSize {
		jobList = jobList[:pageSize]
		hasMore = true
	}
	return jobList, hasMore, nil
}

func (c *folderCategorizer) filterByFolderName(ctx context.Context, options *DescribeOptions) (bool, error) {
	if options.SearchOpts == nil {
		return true, nil
	}
	if options.SearchOpts.FolderName == "" {
		return true, nil
	}
	folderList, err := db.NewJobClient().SearchJobFolder(ctx, options.AppId, options.SpaceId, options.SearchOpts.FolderName)
	if err != nil {
		return false, errorcode.NewCodeErrorf(errorcode.InternalErrorCode, "serach folder list failed")
	}
	if len(folderList) == 0 {
		// 没有匹配的目录
		logging.Infof("not found any folder, folder name: %s", options.SearchOpts.FolderName)
		return false, nil
	}
	for _, folder := range folderList {
		options.MatchOpts.FolderIdList = append(options.MatchOpts.FolderIdList, folder.FolderId)
	}
	return true, nil
}
