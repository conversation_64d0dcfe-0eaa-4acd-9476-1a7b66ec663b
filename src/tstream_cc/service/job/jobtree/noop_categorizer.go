package jobtree

import (
	"context"
	"fmt"
	"strconv"

	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logging "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/repo/db"
)

// noopCategorizer 无分类的分页操作器
type noopCategorizer struct {
}

func (c *noopCategorizer) Page(ctx context.Context, options *DescribeOptions) (*PageResult, error) {
	// 先获取计数, 然后分页
	cond, hasSearchOrMatch := convertToJobCondition(options)
	total, err := db.NewJobClient().CountJob(ctx, cond)
	if err != nil {
		return nil, errorcode.NewCodeErrorf(errorcode.InternalErrorCode, "count job failed")
	}
	if total <= 0 && hasSearchOrMatch {
		// 有过滤和筛选之后, 没有计数则返回
		logging.Info("not result, return empty page")
		return emptyPageResult(options.ParentId), nil
	}
	lastId := int64(-1)
	if options.PageAttach != nil && options.PageAttach.LastId != "" {
		lastId, _ = strconv.ParseInt(options.PageAttach.LastId, 10, 64)
	}
	// 拉取作业, 这里为了简单判断是否还有更多作业, 额外拉取1条数据即可
	jobList, err := db.NewJobClient().PageJobSortById(ctx, lastId, options.PageSize+1, cond)
	if err != nil {
		return nil, errorcode.NewCodeErrorf(errorcode.InternalErrorCode, "list job failed")
	}
	hasMore := false
	if len(jobList) > options.PageSize {
		jobList = jobList[:options.PageSize]
		hasMore = true
	}
	pageResult := &PageResult{
		Current: &Node{
			Id:                    "root",
			Name:                  "作业目录",
			JobNum:                total,
			JobNumWithoutChildren: total,
		},
		JobList: jobList,
		PageAttach: &PageAttach{
			LastType: typeJob,
		},
		HasMore: hasMore,
	}
	if len(jobList) > 0 {
		pageResult.PageAttach.LastId = fmt.Sprintf("%d", jobList[len(jobList)-1].Id)
	}
	return pageResult, nil
}
