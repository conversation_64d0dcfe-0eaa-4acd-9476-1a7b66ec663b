package jobtree

import (
	"context"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logging "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/repo/db"
)

// timeCategorizer 时间分类器
type timeCategorizer struct {
}

// Page 时间分页只有二级, 一级是天数, 二级只有作业
func (c *timeCategorizer) Page(ctx context.Context, options *DescribeOptions) (*PageResult, error) {
	if options.ParentId == "" {
		return c.pageTimeLayer(ctx, options)
	}
	return c.pageJobLayer(ctx, options)
}

func (c *timeCategorizer) pageTimeLayer(ctx context.Context, options *DescribeOptions) (*PageResult, error) {
	cond, _ := convertToJobCondition(options)
	countResultList, err := db.NewJobClient().CountJobGroupByDay(ctx, cond)
	if err != nil {
		return nil, errorcode.NewCodeErrorf(errorcode.InternalErrorCode, err.Error())
	}
	countResultList = c.filterByFolderName(ctx, options, countResultList)
	if len(countResultList) == 0 {
		logging.Info("not result, return empty page")
		return emptyPageResult(options.ParentId), nil
	}
	parent := &Node{
		Id:   "root",
		Name: "作业目录",
	}
	for _, countResult := range countResultList {
		parent.JobNum += countResult.Count
	}

	children := make([]*Node, 0, len(countResultList))
	for _, cr := range countResultList {
		node := &Node{
			Id:       cr.CountName,
			Name:     cr.CountName,
			ParentId: parent.Id,
			JobNum:   cr.Count,
		}
		children = append(children, node)
	}
	sort.Slice(children, func(i, j int) bool {
		return children[i].Name > children[j].Name
	})
	if options.PageAttach != nil && options.PageAttach.LastId != "" {
		idx := -1
		for i, node := range children {
			if node.Id == options.PageAttach.LastId {
				idx = i
				break
			}
		}
		if idx == len(children)-1 {
			return &PageResult{Current: parent, PageAttach: options.PageAttach, HasMore: false}, nil
		}
		if idx >= 0 {
			children = children[idx+1:]
		}
	}
	hasMore := false
	if len(children) > options.PageSize {
		children = children[:options.PageSize]
		hasMore = true
	}
	return &PageResult{
		Current:  parent,
		Children: children,
		HasMore:  hasMore,
		PageAttach: &PageAttach{
			LastType: typeFolder,
			LastId:   children[len(children)-1].Id,
		},
	}, nil
}

func (c *timeCategorizer) pageJobLayer(ctx context.Context, options *DescribeOptions) (*PageResult, error) {
	cond, _ := convertToJobCondition(options)
	// 格式是2025-01-01
	lastTime, err := time.ParseInLocation(time.DateOnly, options.ParentId, time.Local)
	if err != nil {
		logging.Error("parse time failed, parentId:%s, %+v", options.ParentId, err)
		return nil, errorcode.NewCodeErrorf(errorcode.InvalidParameterCode,
			"parse time failed, parentId:%s, %s", options.ParentId, err)
	}
	cond.Gte("CreateTime", lastTime.Format(time.DateTime))
	cond.Lt("CreateTime", lastTime.Add(24*time.Hour).Format(time.DateTime))

	// 先拉计数
	countResultList, err := db.NewJobClient().CountJobGroupByDay(ctx, cond)
	if err != nil {
		return nil, errorcode.NewCodeErrorf(errorcode.InternalErrorCode, err.Error())
	}
	if len(countResultList) == 0 {
		return emptyPageResult(options.ParentId), nil
	}
	current := &Node{
		Id:     options.ParentId,
		Name:   options.ParentId,
		JobNum: countResultList[0].Count,
	}

	lastId := int64(-1)
	if options.PageAttach != nil && options.PageAttach.LastId != "" {
		// 上一次的作业id
		lastId, _ = strconv.ParseInt(options.PageAttach.LastId, 10, 64)
	}
	// 拉取作业, 这里为了简单判断是否还有更多作业, 额外拉取1条数据即可
	jobList, err := db.NewJobClient().PageJobSortById(ctx, lastId, options.PageSize+1, cond)
	if err != nil {
		return nil, errorcode.NewCodeErrorf(errorcode.InternalErrorCode, "list job failed")
	}
	if len(jobList) == 0 {
		return &PageResult{
			Current:    current,
			HasMore:    false,
			PageAttach: options.PageAttach,
		}, nil
	}
	hasMore := false
	if len(jobList) > options.PageSize {
		jobList = jobList[:options.PageSize]
		hasMore = true
	}

	return &PageResult{
		Current: current,
		JobList: jobList,
		HasMore: hasMore,
		PageAttach: &PageAttach{
			LastType: typeJob,
			LastId:   fmt.Sprintf("%d", jobList[len(jobList)-1].Id),
		},
	}, nil
}

func (c *timeCategorizer) filterByFolderName(ctx context.Context, options *DescribeOptions,
	countResultList []*db.CountResult) []*db.CountResult {
	if len(countResultList) == 0 {
		return countResultList
	}
	if options.SearchOpts == nil || options.SearchOpts.FolderName == "" {
		return countResultList
	}
	var filteredResult []*db.CountResult
	for _, v := range countResultList {
		if strings.Contains(v.CountName, options.SearchOpts.FolderName) {
			filteredResult = append(filteredResult, v)
		}
	}
	logging.Infof("filterd by folder name, folder name:%+vv", options.SearchOpts.FolderName)
	return filteredResult
}
