package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"runtime/debug"
	"strconv"
	"strings"
	"time"

	"github.com/tencentyun/cos-go-sdk-v5"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	model3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model"

	error1 "k8s.io/apimachinery/pkg/api/errors"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service1 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	service2 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/barad"
	model2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_config"
	model4 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cluster_master_protocol"
	table4 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster_master"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job"
	table3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job_config"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job_instance"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/watchdog"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service5 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/barad"
	service6 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job_config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
	service4 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource"
	service7 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/sql"
	sql2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/sql"
)

/*
*

	Watchdog 作业恢复流程
*/
func DoRecoverJob(req *watchdog.RecoverJobReq) (status int64, msg string, resp *watchdog.RecoverJobResp, err error) {
	logger.Infof("%s: RecoverJob API called for job id %d with req %v", req.RequestId, req.JobId, req)

	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("%s: Failed to recover job %d, errors: %+v", req.RequestId, req.JobId, errs)
			err = errors.New(fmt.Sprintf("%s", errs))
		}
	}()

	txManager := service2.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {

		// 1. 根据 JobId 获取作业
		var job *table.Job
		if req.JobCurrentStatus == constants.JOB_STATUS_STOPPED {
			job, err = getJobById(req.JobId)
			if err != nil {
				logger.Errorf("%s: Failed to getJobById for job id %d because %+v", req.RequestId, req.JobId, err)
				return err
			}
			if job.Status != constants.JOB_STATUS_STOPPED && job.Status != constants.JOB_STATUS_PAUSED {
				logger.Errorf("%s: Job current status is not STOPPED, recover failed", req.RequestId)
				return err
			}
		} else {
			job, err = getRunningJobById(req.JobId)
			if err != nil {
				logger.Errorf("%s: Failed to getRunningJobById for job id %d because %+v", req.RequestId, req.JobId, err)
				return err
			}
		}
		eventAlertReq := &barad.JobAlertReq{
			JobId:     job.SerialId,
			Message:   "Job failed unexpectedly, and recovered by Watchdog.",
			EventName: constants.BARAD_EVENT_ALERT_NAME_JOB_FAIL,
			Status:    constants.BARAD_EVENT_ALERT_ON,
		}
		retCode, retMsg, err := service5.ProcessEventAlert(eventAlertReq)
		if retCode != 0 {
			logger.Warningf("Failed to send alert to barad, retCode %d, retMsg %s, with error: %+v", retCode, retMsg, err)
		}
		// 2. 获取当前运行时间
		if job.StartTime == "" || job.StartTime == "0000-00-00 00:00:00" {
			job.StartTime = util.GetCurrentTime()
		}
		curRuntime, err := service.GetCurrentRunMillis(job.StartTime)
		if err != nil {
			logger.Errorf("%s: Failed to GetCurrentRunMillis for job id %d because %+v", req.RequestId, req.JobId, err)
			return err
		}

		// 3. 选择当前为 Active 的集群（根据实际运行的集群, 更新 Cluster 字段）
		job, err = SwitchActiveCluster(job)
		if err != nil {
			logger.Errorf("%s: Failed to SwitchActiveCluster for job id %d because %+v", req.RequestId, req.JobId, err)
			return err
		}

		// 4. 查询作业实例
		var jobInstance *table2.JobInstance
		if req.JobCurrentStatus == constants.JOB_STATUS_STOPPED {
			// 当作业为停止状态时, 找上一个作业实例
			jobInstance, err = GetLastJobInstanceByJobId(job.Id)
			if err != nil {
				logger.Errorf("%s: Failed to GetLastJobInstanceByJobId for job id %d because %+v", req.RequestId, req.JobId, err)
				return err
			}
		} else {
			// 检验 JobId 是否为正在运行的实例
			jobInstance, err = GetRunningJobInstanceByJobId(job.Id)
			if err != nil {
				logger.Errorf("%s: Failed to GetRunningJobInstanceByJobId for job id %d because %+v", req.RequestId, req.JobId, err)
				return err
			}
		}

		// 4.1 检查jobconfig 是否包含禁用 AutoRecover 配置
		jobConfig, err2 := service2.GetJobConfigById(jobInstance.JobConfigId)
		if err2 != nil {
			logger.Errorf("%s: Failed to GetJobById for job id %d because %+v", req.RequestId, jobInstance.JobConfigId, err)
			return err
		}
		if jobConfig.AutoRecover == constants.AutoRecoverDisable {
			// 如果配置禁用 AutoRecover
			logger.Infof("%s : job %d AutoRecover = -1, no need to recover", req.RequestId, req.JobId)
			// return nil
			return errors.New("no need recover")
		}

		// 5 检验 YARN Application ID, 防止重复发送命令
		if req.ApplicationId != "" {
			if jobInstance.ApplicationId != req.ApplicationId {
				logger.Errorf("%s: Recorded running & provided YARN application do not match: %s, %s", req.RequestId, jobInstance.ApplicationId, req.ApplicationId)
				return errors.New(fmt.Sprintf("Recorded running YARN application is %s, however the provided is %s, do not match", jobInstance.ApplicationId, req.ApplicationId))
			}
		} else {
			logger.Warningf("%s: No applicationId is provided in the recoverJob request, because the Watchdog version is too old", req.RequestId) // Impossible to reach here
		}

		found, _ := CheckRunningDeploymentByJobSerialId(job, jobInstance)
		if found {
			logger.Errorf("%s: Check running deployment before recovering job, old job deployment found in k8s client, skip to recover job!", req.RequestId)
			return errors.New(fmt.Sprintf("Old job deployment found in k8s client, deployment name: %s-%d, no need to recover!", job.SerialId, jobInstance.Id))
		}
		// 5.1 确定合适的快照进行恢复
		err = selectLatestValidSavepoint(req, job, jobConfig, jobInstance)
		if err != nil {
			logger.Errorf("%s cannot select latest savepoint for job:%s , error %v", req.RequestId, job.SerialId, err)
			return err
		}

		// 6. 更新作业状态为 History
		sql := "UPDATE Job SET Status=?, ProgressDesc=?, TotalRunMillis=?, TmRunningCuNum=0, JmRunningCuNum=0, RunningCpu=0, RunningMem=0, LastOpResult='' WHERE Id=?" // 作业停止后需要把 RunningCuNum 设置为 0
		rowsAffected, err := tx.ExecuteSqlWithArgs(sql, constants.JOB_STATUS_PROGRESS, constants.RECOVER_JOB_PROGRESS_DESC, job.TotalRunMillis+curRuntime, req.JobId).RowsAffected()
		if err != nil {
			logger.Warningf("%s: Failed to update Job table because of error %+v", req.RequestId, err)
			return err
		} else if rowsAffected != 1 {
			logger.Warningf("%s: Failed to update Job table when update Job table, because rowsAffected=%d", req.RequestId, rowsAffected)
			return errors.New(fmt.Sprintf("Failed to update Job table when update Job table, because rowsAffected=%d", rowsAffected))
		}

		// 7. 获取旧的JobRuntimeId 和 running job config id
		// oldJobRuntimeId, err := GetOldJobRuntimeId(req.JobId, constants.JOB_STATUS_RUNNING)
		// oldJobRuntimeId, jobConfigId, err := GetOldJobInstanceIdAndJobConfigId(job.Id, constants.JOB_STATUS_RUNNING)
		// if err != nil {
		// 	logger.Warningf("%s: Failed to GetOldJobRuntimeId because of error %+v", req.RequestId, err)
		// 	return err
		// } else if oldJobRuntimeId == -1 {
		// 	logger.Warningf("%s: Failed to update Job table when update Job table, because oldJobRuntimeId is -1", req.RequestId)
		// 	return errors.New(fmt.Sprintf("Failed to get old JobRuntimeId when recover job, oldJobRuntimeId=-1"))
		// } else if jobConfigId == -1 {
		// 	logger.Errorf("%s: Failed to get running job config id by job id [%d], jobConfigId is -1", req.RequestId, job.Id)
		// 	return fmt.Errorf("Failed to get running job config id by job id [%d], jobConfigId is -1", job.Id)
		// }
		oldJobRuntimeId, jobConfigId := jobInstance.Id, jobInstance.JobConfigId

		// 8. 更新旧JobInstance的状态
		err = MakeOldJobInstanceHistory(req.JobId, oldJobRuntimeId, tx)
		if err != nil {
			logger.Warningf("%s: Failed to MakeOldJobInstanceHistory because of error %+v", req.RequestId, err)
			return err
		}

		// 9. 创建并初始化新JobInstance
		newJobInstance, err := BuildJobInstanceEntity(jobConfigId, job, nil)
		if err != nil {
			return err
		}
		jobRuntimeId := tx.SaveObject(newJobInstance, "JobInstance")

		newJobInstance.Id = jobRuntimeId

		// 10. 创建并保存Command
		command, err := BuildRecoverCommandForClusterMaster(oldJobRuntimeId, newJobInstance, job, req.CheckpointPath, newJobInstance.RunningOrderId, req.RequestId)
		if err != nil {
			logger.Warningf("%s: Failed to BuildRecoverCommandForClusterMaster because of error %+v", req.RequestId, err)
			return err
		}
		tx.SaveObject(command, "Command")

		// 11. 增加job的运行次数
		err = IncJobRunningOrderId(job.Id, tx)
		if err != nil {
			logger.Errorf("%s: Failed to IncJobRunningOrderId, with errors:%+v", req.RequestId, err)
			return err
		}

		return nil
	}).Close()

	logger.Infof("%s: RecoverJob successful for job id %d", req.RequestId, req.JobId)
	return controller.SUCCESS, controller.NULL, &watchdog.RecoverJobResp{IsSucc: controller.OK}, nil
}

func BuildRecoverCommandForClusterMaster(oldJobRuntimeId int64, jobInstance *table2.JobInstance, job *table.Job, externalizedCheckpointPath string, jobRunningOrderId int64, requestId string) (*cluster_master.Command, error) {
	jobConfigIds := make([]int64, 0)
	jobConfigIds = append(jobConfigIds, jobInstance.JobConfigId)
	jobConfigs, err := service3.ListJobConfigs(job.Id, jobConfigIds, []int64{}, nil)
	if err != nil {
		return nil, err
	}
	jobConfig := jobConfigs[0]

	command := InitializeCommandForClusterMaster(job, constants.JOB_STATUS_RUNNING)
	command.Action = constants.CLUSTER_MASTER_COMMAND_ACTION_RESTART_JOB
	command.RunningOrderId = jobRunningOrderId

	refs, err := service4.GetResourceRefByJobConfigId(jobConfig.Id)
	if err != nil {
		return nil, err
	}
	var metadata = ""
	var newSqlCode = jobConfig.SqlCode
	var excludeRefs []*model2.ResourceRefItem
	if job.Type == constants.JOB_TYPE_SQL {
		constsAndVals := make(map[string]interface{})
		for constk, constProperty := range sql2.CostJobPropertyMaping {
			val, err := getCustomConstFromJobConfigProperties(jobConfig.Properties, constProperty)
			if err != nil {
				logger.Errorf("getCustomConstFromJobConfigProperties with error %v", err)
				return nil, err
			}
			if val == "" {
				continue
			}
			if constk == sql2.CustomTimestamp {
				intVal, err := strconv.ParseInt(val, 10, 64)
				if err != nil {
					logger.Errorf("properties cannot parse to int, recover has return error %v", err)
					return nil, err
				}
				constsAndVals[sql2.CustomTimestamp] = intVal
			}
			if constk == sql2.CustomScanMod {
				constsAndVals[sql2.CustomScanMod] = val
			}
		}
		if len(constsAndVals) > 0 {
			customVariableCtx := sql2.NewHandleCustomVariableCtx().
				WithSqlCode(jobConfig.SqlCode).
				WithAes(true).
				WithCustomConstAndVals(constsAndVals)
			replacedVariableSqlCodeStr, err := sql2.NewCustomConstHandler(customVariableCtx).ReplaceCustomConst()
			logger.Infof("recover getCustomConstFromJobConfigProperties  replacedVariableSqlCodeStr %s", replacedVariableSqlCodeStr)
			if err != nil {
				logger.Errorf("handler custom const has return error %v", err)
				return nil, err
			}
			jobConfig.SqlCode = replacedVariableSqlCodeStr
		}
		flinkVersion := service.GetFlinkVersion(job, jobConfig)

		jobClusterGroup, err := service6.GetClusterGroupByClusterId(job.ClusterId)
		if err != nil {
			logger.Errorf("%s: GetClusterGroupByClusterId error: %+v", job.ClusterId, err)
			return nil, err
		}
		systemConf := service7.GetSystemVariables(job.SerialId, jobClusterGroup.SerialId)
		/*jobConfig.SqlCode, err = service7.HandleSystemVariablesWithAes(systemConf, jobConfig.SqlCode)
		if err != nil {
			logger.Errorf("handler SystemVariables has return error %v", err)
			return nil, err
		}*/

		metadata, _, excludeRefs, newSqlCode, err = service7.BuildMetadata4Command(context.Background(), jobConfig.ProgramArgs, jobConfig.SqlCode,
			job.OwnerUin, int64(job.AppId), job.Region, flinkVersion, job.ItemSpaceId, true, systemConf)
		if err != nil {
			logger.Errorf("%s: Failed to build metadata for command, with errors:%+v", requestId, err)
			return nil, err
		}
		jobConfig.SqlCode = newSqlCode
		// 过滤内置Connector [ProgramArgs.ExcludeConnectors]
		if excludeRefs != nil && len(excludeRefs) > 0 {
			excludes := make(map[string]string)
			for _, ref := range excludeRefs {
				excludes[ref.ResourceId] = ref.ResourceId
			}
			for i, ref := range refs {
				if _, ok := excludes[ref.ResourceId]; ok {
					refs = append(refs[:i], refs[i+1:]...)
				}
			}
		}
	}
	// 作业恢复，不会使用StartMode，传空即可
	runJobParams, err := BuildRunJobParams(oldJobRuntimeId, jobInstance, job, jobConfig, constants.START_MODE_LATEST,
		true, false, false, externalizedCheckpointPath, refs, requestId, metadata, jobRunningOrderId, "", "", false)

	if err != nil {
		return nil, err
	}
	command.Params = runJobParams

	return command, nil
}

// ================= Helper 方法 =================

func getRunningJobById(jobId int64) (*table.Job, error) {
	sql := "SELECT * FROM Job WHERE Id=? AND Status=?"
	args := make([]interface{}, 0)
	args = append(args, jobId)
	args = append(args, constants.JOB_STATUS_RUNNING)

	txManager := service2.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("exception occurs when query Job from db, with errors:%+v", err)
		return nil, err
	}

	job := &table.Job{}

	if len(data) == 0 {
		return nil, errors.New(fmt.Sprintf("cannot find any running job with id %d", jobId))
	}

	if len(data) != 1 {
		return nil, errors.New(fmt.Sprintf("Why?? Number of running jobs with id %d for status RUNNING is not 1 but %d", jobId, len(data)))
	}

	err = util.ScanMapIntoStruct(job, data[0])
	if err != nil {
		logger.Errorf("failed to convert bytes into table.Job, with errors:%+v", err)
		return nil, err
	}

	return job, nil
}

// True 代表找到了同名的 deployment，不能继续恢复。False 代表没有找到，可以继续恢复
func CheckRunningDeploymentByJobSerialId(job *table.Job, jobInstance *table2.JobInstance) (bool, error) {
	clusterGroupService, err := service6.NewClusterGroupService(job.ClusterGroupId)
	ClusterGroup := clusterGroupService.GetClusterGroup()
	cluster, err := clusterGroupService.GetActiveCluster()
	if err != nil {
		return false, err
	}
	// 创建 k8s client
	k8sService := k8s.GetK8sService()
	kubConfig := []byte(cluster.KubeConfig)
	client, err := k8sService.NewClient(kubConfig)
	if err != nil {
		logger.Error("Check running deployment before recovering job, failed to create k8s client!", err)
		return false, nil
	}

	namespace := service6.GetDefaultNamespace(ClusterGroup)
	intf := client.AppsV1().Deployments(namespace)
	// 在 default namespace 中获取 deployment, 名称为 job serial id - job instance id
	name := job.SerialId + "-" + strconv.FormatInt(jobInstance.Id, 10)
	jobDeployment, err := intf.Get(context.TODO(), name, v1.GetOptions{})
	if err != nil {
		if !error1.IsNotFound(err) {
			logger.Errorf("Check running deployment before recovering job, Failed to Get deployment %s in namespace %s", name, namespace)
			return false, nil
		}
		logger.Errorf("Check running deployment before recovering job, Deployment %s not found in namespace %s", name, namespace)
		return false, nil
	}
	logger.Warningf("Check running deployment before recovering job, Deployment %s found in namespace %s", name, namespace)
	b, _ := json.Marshal(jobDeployment)
	logger.Warningf("Check running deployment before recovering job, GetDeploymentAppsV1 %s", string(b))
	return true, nil
}

func GetRunningJobInstanceByJobId(jobId int64) (*table2.JobInstance, error) {
	sql := "SELECT * FROM JobInstance WHERE JobId=? AND Status=? ORDER BY CreateTime DESC LIMIT 1"
	args := make([]interface{}, 0)
	args = append(args, jobId)
	args = append(args, constants.JOB_INSTANCE_STATUS_RUNNING)

	txManager := service2.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Exception occurs when query JobInstance from db, with errors:%+v", err)
		return nil, err
	}

	jobInstance := &table2.JobInstance{}

	if len(data) != 1 {
		return nil, errors.New(fmt.Sprintf("Number of running job instances with job id %d is not 1 but %d", jobId, len(data)))
	}

	err = util.ScanMapIntoStruct(jobInstance, data[0])
	if err != nil {
		logger.Errorf("Failed to convert bytes into table.JobInstance, with errors:%+v", err)
		return nil, err
	}

	return jobInstance, nil
}

func GetLastJobInstanceByJobId(jobId int64) (*table2.JobInstance, error) {
	sql := "SELECT * FROM JobInstance WHERE JobId=? ORDER BY CreateTime DESC LIMIT 1"
	args := make([]interface{}, 0)
	args = append(args, jobId)

	txManager := service2.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Exception occurs when query JobInstance from db, with errors:%+v", err)
		return nil, err
	}

	jobInstance := &table2.JobInstance{}

	if len(data) != 1 {
		return nil, errors.New(fmt.Sprintf("Number of running job instances with job id %d is not 1 but %d", jobId, len(data)))
	}

	err = util.ScanMapIntoStruct(jobInstance, data[0])
	if err != nil {
		logger.Errorf("Failed to convert bytes into table.JobInstance, with errors:%+v", err)
		return nil, err
	}

	return jobInstance, nil
}

func getCustomConstFromJobConfigProperties(properties string, key string) (string, error) {
	if properties == "" || key == "" {
		return "", nil
	}
	dynamicProperties := make([]*model3.Property, 0)
	err := json.Unmarshal([]byte(properties), &dynamicProperties)
	if err != nil {
		return properties, errorcode.NewStackError(errorcode.InternalErrorCode_UnMarshalFailed, "JobConfig Properties JSON unmarshal error", err)
	}
	for _, property := range dynamicProperties {
		if property.Key == key {
			return property.Value, nil
		}
	}
	return "", err
}

// selectLatestValidSavepoint
// 选择快照的时间范围: 从当前作业实例启动开始, 到recover 之前;
// 作业重新拉起选择的快照在此时间范围内, 按照时间倒序, 依次向前查找可用的快照, ( 需要优化快照可用性检测逻辑, 保证检测通过的快照能正常恢复作业 )
// 若在此时间内, 无可用快照, 则选择当前最新作业实例启动时的快照, 如果最新实例启动没有快照, 则无快照拉起作业
func selectLatestValidSavepoint(req *watchdog.RecoverJobReq, job *table.Job, jobConfig *table3.JobConfig, jobIns *table2.JobInstance) error {
	logger.Debugf("selectLatestValidSavepoint for job %s, jobIns %d", job.SerialId, jobIns.Id)
	// 先移除wd选择的快照, 这里的快照并为检测可用性
	req.CheckpointPath = ""
	savepoints, err := service2.GetActiveSavepointsByJobId(job.Id, jobIns.Id, 0, 200)
	if err != nil {
		logger.Errorf("%s selectLatestValidSavepoint cannot get savepoints by jobId %d", req.RequestId, job.Id)
		return err
	}
	cluster, err := service6.GetClusterByClusterId(job.ClusterId)
	if err != nil {
		logger.Errorf("%s selectLatestValidSavepoint canot found cluster by id %d", req.RequestId, job.ClusterId)
		return err
	}
	finalSavepointPath := ""
	var savepointOnDb *watchdog.SavepointEntity
	for _, item := range savepoints {
		if savepointPathValid(item.Path, job, cluster) {
			finalSavepointPath = item.Path
			savepointOnDb = item
			break
		}
	}
	// 扫描cos快照目录，找到一个最新的快照，与数据库中的快照对比，如果更新，则用cos上的快照
	chkPathOnCso, err := findLatestCheckpointOnCos(job, jobIns, cluster)
	if err != nil {
		logger.Errorf("%s:findLatestCheckpointOnCos with error %v", req.RequestId, err)
		saveEvent(job, "findLatestCheckpointOnCosErr", err.Error())
	}
	logger.Infof("%s: selectLatestValidSavepoint findLatestCheckpointOnCos result: %v", req.RequestId, chkPathOnCso)
	if err == nil && chkPathOnCso != nil {
		if savepointOnDb == nil {
			// 当数据库中没找到可用快照时，cos 中扫出来的就是最新的
			logger.Infof("%s: chkPathOnCso %s is the latest one", req.RequestId, chkPathOnCso.Path)
			finalSavepointPath = chkPathOnCso.Path
		} else {
			savepointOnDbUpdateTime, err := time.ParseInLocation("2006-01-02 15:04:05", savepointOnDb.UpdateTime, time.Local)
			if err != nil {
				logger.Errorf("%s:findLatestCheckpointOnCos with error %v", req.RequestId, err)
				return err
			}
			logger.Infof("%s: selectLatestValidSavepoint savepointOnDb result: %v, savepointOnDbUpdateTime:%v", req.RequestId, savepointOnDb, savepointOnDbUpdateTime)
			if chkPathOnCso.LastModifyTime.After(savepointOnDbUpdateTime) {
				logger.Infof("%s: chkPathOnCso %s is the latest one", req.RequestId, chkPathOnCso.Path)
				finalSavepointPath = chkPathOnCso.Path
			}
		}
	}

	if finalSavepointPath != "" {
		logger.Infof("%s selectLatestValidSavepoint savepoint %s is valid", req.RequestId, finalSavepointPath)
		req.CheckpointPath = finalSavepointPath
		return nil
	}
	// 当数据库中没有合适的快照时, 确认上一次作业启动使用的快照是否可用
	command, err := GetLatestCommandByJob(job.Id, jobIns.RunningOrderId)
	if err != nil {
		logger.Errorf("%s selectLatestValidSavepoint with error %v", req.RequestId, err)
		return err
	}
	runJobParam := &model4.RunJobParams{}
	err = json.Unmarshal([]byte(command.Params), runJobParam)
	if err != nil {
		logger.Errorf("%s selectLatestValidSavepoint with error %v", req.RequestId, err)
		return err
	}
	if savepointPathValid(runJobParam.ExternalizedCheckpointPath, job, cluster) {
		logger.Infof("%s selectLatestValidSavepoint runJobParam.ExternalizedCheckpointPath is valid for command %d", req.RequestId, command.Id)
		req.CheckpointPath = runJobParam.ExternalizedCheckpointPath
		return nil
	}
	if savepointPathValid(runJobParam.SavepointPath, job, cluster) {
		logger.Infof("%s selectLatestValidSavepoint runJobParam.SavepointPath is valid for command %d", req.RequestId, command.Id)
		req.CheckpointPath = runJobParam.SavepointPath
		return nil
	}
	// 无可用快照时，选择上一个实例的快照
	logger.Infof("%s selectLatestValidSavepoint cannot find valid savepopint ", req.RequestId)
	if len(runJobParam.ExternalizedCheckpointPath) > 0 {
		req.CheckpointPath = runJobParam.ExternalizedCheckpointPath
		return nil
	}
	if len(runJobParam.SavepointPath) > 0 {
		req.CheckpointPath = runJobParam.SavepointPath
		return nil
	}
	logger.Debugf("selectLatestValidSavepoint for job %s, jobIns %d, result : %s", job.SerialId, jobIns.Id, req.CheckpointPath)
	return nil
}

func savepointPathValid(path string, job *table.Job, cluster *table4.Cluster) bool {
	if path == "" {
		return false
	}
	params, err := service.NewSavepointParam(path, job.Region)
	if err != nil {
		logger.Errorf("failed to get savepoint params for path %s, error : %v", path, err)
		return false
	}
	secretId, secretKey, token, err := service.GetResourceSecretIdAndKey(job.OwnerUin, job.CreatorUin, job.Region)
	if err != nil {
		logger.Errorf("Failed to check savepoint! getResourceSecretIdAndKey err:%v", err)
		return false
	}

	url, err := cos.NewBucketURL(cluster.DefaultCOSBucket, job.Region, true)
	if err != nil {
		logger.Errorf("Failed to check savepoint! NewBucketURL err:%v", err)
		return false
	}
	client := cos.NewClient(&cos.BaseURL{
		BucketURL: url,
	}, &http.Client{
		Transport: &cos.AuthorizationTransport{
			SecretID:     secretId,
			SecretKey:    secretKey,
			SessionToken: token,
		},
	})
	client.Conf.RetryOpt.Count = 5
	rsp, err := client.Object.Head(context.Background(), params.Path+constants.META_DATA_PATH, nil)
	if err != nil {
		logger.Errorf("Failed to check savepoint! cos return err:%v", err)
		return false
	}
	if rsp == nil || rsp.StatusCode == 404 || rsp.StatusCode == 403 {
		return false
	}
	if rsp.StatusCode == 200 {
		return true
	}
	return false
}

func GetLatestCommandByJob(jobId, runningOrderId int64) (command *cluster_master.Command, err error) {
	sql := "SELECT * FROM Command WHERE JobId=? AND RunningOrderId=? AND (Action = ? or Action = ? ) ORDER BY CreateTime DESC limit 1 "
	cnt, rowsData, err := service1.GetTxManager().GetQueryTemplate().DoQueryWithArgs(sql, jobId, runningOrderId, constants.CLUSTER_MASTER_COMMAND_ACTION_RUN_JOB, constants.CLUSTER_MASTER_COMMAND_ACTION_RESTART_JOB)
	if err != nil {
		return nil, err
	} else if cnt == 0 {
		return nil, errors.New(fmt.Sprintf("No Command found for jobId: %d", jobId))
	} else if cnt > 1 {
		return nil, errors.New(fmt.Sprintf("Logic error? more than 1 Command found for jobId: %d", jobId))
	}
	command = &cluster_master.Command{}
	err = util.ScanMapIntoStruct(command, rowsData[0])

	return command, err
}

func findLatestCheckpointOnCos(job *table.Job, jobIns *table2.JobInstance, cluster *table4.Cluster) (chkPath *checkPointDir, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Failed to findLatestCheckpointOnCos job %d, errors: %+v,stack: %v", job.SerialId, errs, debug.Stack())
			err = errors.New(fmt.Sprintf("%s", errs))
		}
	}()
	client, err := getCosClient(job, cluster)
	if err != nil {
		logger.Errorf("findLatestCheckpointOnCos:Failed to get cos client err:%v", err)
		return chkPath, err
	}

	cpPrefix := fmt.Sprintf("%d/%s/%s/%d/flink-checkpoints/", job.AppId, job.CreatorUin, job.SerialId, jobIns.RunningOrderId)
	if len(jobIns.FlinkJobId) > 0 {
		cpPrefix = fmt.Sprintf("%s%s/", cpPrefix, jobIns.FlinkJobId)
	} else {
		cpPrefixes, _, rsp, err := listBucketPrefixes(client, cpPrefix, "/", 100, "")
		if err != nil || rsp == nil || rsp.StatusCode != 200 {
			logger.Errorf("findLatestCheckpointOnCos:Failed to Get checkpoint dir! cos return err:%v", err)
			return chkPath, err
		}

		if len(cpPrefixes) == 0 {
			logger.Warningf("findLatestCheckpointOnCos:No checkpoint directories found")
			return chkPath, nil
		} else if len(cpPrefixes) > 1 {
			errmsg := fmt.Sprintf("findLatestCheckpointOnCos: [%s] checkpoint directories more than 1", cpPrefix)
			logger.Errorf(errmsg)
			return chkPath, errors.New(errmsg)
		}
		// 取第一个子目录 1257058945/100006386216/cql-27he7ezx/4/flink-checkpoints/0000000062c0456b0000000000000000/
		cpPrefix = cpPrefixes[0]
	}
	logger.Infof("findLatestCheckpointOnCos: cpPrefix %s", cpPrefix)
	checkpointDir, err := findLatestCheckpointDir(client, cpPrefix)
	if err != nil {
		logger.Errorf("findLatestCheckpointOnCos:Failed to find latest checkpoint! err:%v", err)
		return chkPath, err
	}
	stateBucket := cluster.DefaultCOSBucket
	if cluster.StateCOSBucket != "" {
		stateBucket = cluster.StateCOSBucket
	}
	if checkpointDir != nil {
		checkpointDir.Path = "cosn://" + stateBucket + "/" + strings.TrimRight(checkpointDir.Path, "/")
	}
	return checkpointDir, nil
}

func findLatestCheckpointDir(client *cos.Client, prefix string) (*checkPointDir, error) {

	marker := ""
	var maxChkNum int64 = -1
	var maxChkPath string

	for {
		prefixes, result, rsp, err := listBucketPrefixes(client, prefix, "/", 200, marker)
		if err != nil || rsp == nil || rsp.StatusCode != 200 {
			logger.Errorf("findLatestCheckpointDir:Failed to Get chk dir! cos return err:%v", err)
			return nil, err
		}
		for _, chkPath := range prefixes {
			parts := strings.Split(strings.TrimRight(chkPath, "/"), "/")
			if len(parts) > 0 && strings.HasPrefix(parts[len(parts)-1], "chk-") {
				numStr := strings.TrimPrefix(parts[len(parts)-1], "chk-")
				if chkNum, err := strconv.ParseInt(numStr, 10, 64); err == nil {
					if chkNum > maxChkNum {
						maxChkNum = chkNum
						maxChkPath = chkPath
					}
				}
			}
		}
		if !result.IsTruncated {
			break
		}
		marker = result.NextMarker
	}

	if maxChkPath == "" {
		return nil, fmt.Errorf("no checkpoint found in directory for prefix %s", prefix)
	}

	return validateAndGetCheckpointInfo(client, maxChkPath)
}

type checkPointDir struct {
	Path           string
	LastModifyTime time.Time
}

func getCosClient(job *table.Job, cluster *table4.Cluster) (*cos.Client, error) {
	secretId, secretKey, token, err := service.GetResourceSecretIdAndKey(job.OwnerUin, job.CreatorUin, job.Region)
	if err != nil {
		logger.Errorf("Failed to getResourceSecretIdAndKey err:%v", err)
		return nil, err
	}
	url, err := cos.NewBucketURL(cluster.DefaultCOSBucket, job.Region, true)
	if err != nil {
		logger.Errorf("Failed to NewBucketURL err:%v", err)
		return nil, err
	}
	client := cos.NewClient(&cos.BaseURL{
		BucketURL: url,
	}, &http.Client{
		Transport: &cos.AuthorizationTransport{
			SecretID:     secretId,
			SecretKey:    secretKey,
			SessionToken: token,
		},
	})
	client.Conf.RetryOpt.Count = 5
	return client, nil
}

func listBucketPrefixes(client *cos.Client, prefix, delimiter string, maxKeys int, marker string) ([]string, *cos.BucketGetResult, *cos.Response, error) {
	result, rsp, err := client.Bucket.Get(context.Background(), &cos.BucketGetOptions{
		Prefix:    prefix,
		Delimiter: delimiter,
		MaxKeys:   maxKeys,
		Marker:    marker,
	})
	if err != nil {
		if result != nil && len(result.CommonPrefixes) > 0 {
			logger.Warningf("listBucketPrefixes: cos return err but result not empty, err: %v", err)
			return result.CommonPrefixes, result, rsp, nil
		}
		logger.Errorf("listBucketPrefixes:Failed to Get bucket prefixes! cos return err:%v", err)
		return nil, result, rsp, err
	}
	return result.CommonPrefixes, result, rsp, nil
}

func validateAndGetCheckpointInfo(client *cos.Client, path string) (*checkPointDir, error) {
	headRsp, err := client.Object.Head(context.Background(), path+constants.META_DATA_PATH, nil)
	if err != nil {
		if headRsp != nil && headRsp.StatusCode == 200 {
			logger.Warningf("validateAndGetCheckpointInfo: cos return err but headRsp.StatusCode==200, err: %v", err)
			// 继续处理 此处的 error 可能是 cosclient 重试机制导致
		} else {
			logger.Errorf("validateAndGetCheckpointInfo:Failed to validate checkpoint! cos return err:%v", err)
			return nil, err
		}
	}
	if headRsp == nil || headRsp.StatusCode != 200 {
		return nil, errors.New("validateAndGetCheckpointInfo:COS HEAD response invalid or not found")
	}
	t, err := time.Parse(time.RFC1123, headRsp.Header.Get("Last-Modified"))
	if err != nil {
		logger.Errorf("validateAndGetCheckpointInfo:parsing time %s with error %v", headRsp.Header.Get("Last-Modified"), err)
		return nil, err
	}
	return &checkPointDir{
		Path:           path,
		LastModifyTime: t,
	}, nil
}

func saveEvent(job *table.Job, eventName, eventValue string) {
	event := &table4.ClusterEvent{
		ClusterId:  job.ClusterGroupId,
		JobId:      job.Id,
		CreateTime: util.GetCurrentTime(),
		UpdateTime: util.GetCurrentTime(),
		EventName:  eventName,
		EventValue: eventValue,
	}
	service1.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.SaveObject(event, "ClusterEvent")
		return nil
	}).Close()
}
