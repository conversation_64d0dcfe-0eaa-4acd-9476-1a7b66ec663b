package service_test

import (
	"fmt"
	"github.com/satori/go.uuid"
	"testing"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource"
)

// go test -v ./src/tstream_cc/service/job/create_job_service_test.go ./src/tstream_cc/service/job/create_job_service.go ./src/tstream_cc/service/job/job_service.go ./src/tstream_cc/service/job/stop_jobs_service.go -o ./bin/test_create_job
// ./bin/test_create_job
func init()  {
}


func TestDeleteJob(t *testing.T) {
	service2.InitTestDB(service2.WALLYDB)

	isOk, msg1, rsp := service.DoDeleteJobs(&model.DeleteJobsReq{
		AppId: **********,
		Uin: "************",
		SubAccountUin: "************",
		RequestId: "mini_" + uuid.NewV4().String(),
		Region: "ap-guangzhou",
		Version: "1",
		JobIds: []string{ "cql-m2udydiw"},
	})

	if isOk == controller.OK {
		t.Logf("delete job success %+v", rsp)
		println(fmt.Sprintf("delete job success %+v", rsp))
	} else {
		t.Errorf("delete job failed %s msg1 %s", isOk, msg1)
	}
}

