package service

import (
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/auth"
	commandc "tencentcloud.com/tstream_galileo/src/common/command"
	"tencentcloud.com/tstream_galileo/src/common/configure"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service4 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cluster_admin_protocol"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job_config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	common "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service1 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	service7 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job_config"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource"
	"time"
)

const (
	ConfItemTimeout                            = "triggerSavepoint.timeout"
	SAVEPOINT_COMMAND_ACTION_TRIGGER_SAVEPOINT = 1
	SavepointInterfaceName                     = "qcloud.clusteradmin.command.savepoint"
	CHECKPOINT_TIMEOUT                         = "execution.checkpointing.timeout"
)

func getTimeout() int64 {
	var timeout = configure.GetConfInt64Value(constants.GALILEO_CONF_FILE, ConfItemTimeout, 11)
	return timeout
}

func DoTriggerJobSavepoint(req *model.TriggerJobSavepointReq) (string, string, *model.TriggerJobSavepointRsp) {
	// 1. 检查作业是否存在
	job, err := service.WhetherJobExists(int32(req.AppId), req.Region, req.JobId)
	if err != nil {
		msg := fmt.Sprintf("%s Failed to list job ,err:%s", req.RequestId, err.Error())
		logger.Errorf(msg)
		return controller.InternalError, msg, &model.TriggerJobSavepointRsp{SavepointTrigger: false}
	}

	// 鉴权
	err = auth.InnerAuthById(req.WorkSpaceId, req.IsSupOwner, job.ItemSpaceId, req.AppId, req.SubAccountUin, req.Action, false)
	if err != nil {
		logger.Errorf("%s: User  uin %s is NOT authenticated, refuse to serve", req.RequestId, req.Uin)
		errCode := errorcode.GetCode(err)
		return errCode.GetCodeStr(), errCode.GetCodeDesc(), nil
	}

	//2.组装sendData&SavepointEntity
	//2.1 jobRuntimeId
	jobInstance, err := GetRunningJobInstance(job.Id)
	if err != nil {
		msg := fmt.Sprintf("%s Failed to get jobInstance ,err:%s", req.RequestId, err.Error())
		logger.Errorf(msg)
		return controller.InternalError, msg, &model.TriggerJobSavepointRsp{SavepointTrigger: false}
	}
	jobRuntimeId := jobInstance.Id
	jobRunningOrderId := jobInstance.RunningOrderId
	jobConfigIds := make([]int64, 0)
	jobConfigIds = append(jobConfigIds, job.PublishedJobConfigId)
	jobConfigs, err := service7.ListJobConfigs(job.Id, jobConfigIds, []int64{}, nil)
	if err != nil {
		msg := fmt.Sprintf("%s Failed to get jobConfigs ,err:%s", req.RequestId, err.Error())
		logger.Errorf(msg)
		return controller.InternalError, msg, &model.TriggerJobSavepointRsp{SavepointTrigger: false}
	}
	jobConfig := jobConfigs[0]

	//2.2 savepointDirectory
	cosBucket, err := service7.GetRealCOSBucketFromJobConfig(jobConfig)
	savepointDirectory, err := service2.GetSavepointDirectory(job, cosBucket, jobRunningOrderId)

	//2.3 timeout(从高级参数：execution.checkpointing.timeout 中获取)
	timeout, err := GetClientTimeout(jobConfig)
	if err != nil {
		logger.Info(err)
		timeout = constants.SAVEPOINT_DEFAULT_TIMEOUT
	}
	savepointEntity := BuildSavepointEntity(job.Id, jobRuntimeId, constants.SAVEPOINT_STATUS_IN_PROGRESS, constants.RECORD_TYPE_SAVEPOINT, 0, timeout, "", util.TimestampToLocalTime(time.Now().Unix()),
		util.TimestampToLocalTime(time.Now().Unix()), req.Description, job.ClusterId, job.ItemSpaceId)

	clusterGroup, err := service1.GetClusterGroupByClusterId(job.ClusterId)
	if err != nil {
		msg := fmt.Sprintf("%s Failed to GetClusterGroupByClusterId ,err:%s", req.RequestId, err.Error())
		logger.Errorf(msg)
		return controller.InternalError, msg, &model.TriggerJobSavepointRsp{SavepointTrigger: false}
	}

	para := &model.TriggerJobSavepointCAPara{
		Params: model.TriggerJobSavepointParams{
			FlinkVersion:       common.GetFlinkVersion(job, jobConfig),
			SavepointDirectory: savepointDirectory,
			Timeout:            timeout,
			SchedulerType:      "2",
			DynamicProperties:  "",
			Description:        req.Description,
		},
		Action:          SAVEPOINT_COMMAND_ACTION_TRIGGER_SAVEPOINT,
		JobId:           job.Id,
		JobType:         job.Type,
		AppId:           req.AppId,
		ClusterId:       job.ClusterId,
		JobRuntimeId:    jobRuntimeId,
		ClusterSerialId: clusterGroup.SerialId,
	}
	caReq := cluster_admin_protocol.NewClusterAdminReq(req.RequestId, SavepointInterfaceName, para)
	sendData, err := json.Marshal(caReq)
	if err != nil {
		msg := fmt.Sprintf("%s Error converting SendData", req.RequestId)
		logger.Error(msg)
		logger.Error(err)
		return controller.InternalError, msg, &model.TriggerJobSavepointRsp{SavepointTrigger: false}
	}
	//3.创建CRD
	commandService, err := commandc.NewCommandService(job.ClusterId)
	if err != nil || commandService == nil {
		msg := fmt.Sprintf("%s Error can't get the commandService from job.ClusterId %d", req.RequestId, job.ClusterId)
		logger.Error(msg)
		logger.Error(err)
		return controller.InternalError, msg, &model.TriggerJobSavepointRsp{SavepointTrigger: false}
	}
	url, err := commandc.GetCAOrCSUrl(commandService.Cluster)
	if err != nil {
		msg := fmt.Sprintf("%s Error can't get the GetCAOrCSUrl %d", req.RequestId, job.ClusterId)
		logger.Error(msg)
		logger.Error(err)
		return controller.InternalError, msg, &model.TriggerJobSavepointRsp{SavepointTrigger: false}
	}
	rsp, err := commandService.DoRequest(&commandc.CommandRequest{
		ReqId:    req.RequestId,
		Url:      url,
		SendData: string(sendData),
		Uin:      req.Uin,
		Apikey:   "triggerJobSavepoint",
		Timeout:  getTimeout(),
		Callback: "",
	})
	if err != nil {
		msg := fmt.Sprintf("%s Error commandService.DoRequest", req.RequestId)
		logger.Error(msg)
		logger.Error(err)
		return controller.InternalError, msg, &model.TriggerJobSavepointRsp{SavepointTrigger: false}
	}
	//4.定义数据结构，返回结果
	caRsp := &model.TriggerJobSavepointCARsp{}
	err = json.Unmarshal([]byte(rsp), caRsp)
	if err != nil {
		msg := fmt.Sprintf("error parsing response: ret: %s \t err: %v", rsp, err)
		logger.Errorf(msg)
		logger.Error(err)
		return controller.InternalError, msg, &model.TriggerJobSavepointRsp{SavepointTrigger: false}
	}
	res := &model.TriggerJobSavepointRsp{}
	if caRsp.Data.ReturnCode == 0 { //成功
		//往Savepoint表中保存对应数据
		txManager := service4.GetTxManager()
		txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
			id := tx.SaveObject(savepointEntity, "Savepoint")
			cidUtil := &util.CidUtil{}
			serialId := cidUtil.EncodeId(id, "svp", "svp", util.GetNowTimestamp(), 8)
			tx.ExecuteSqlWithArgs("UPDATE Savepoint set serialId = ?  WHERE id = ? ", serialId, id)
			res.SavepointTrigger = true
			res.SavepointId = serialId
			return nil
		}).Close()
	} else {
		res.SavepointTrigger = false
		res.ErrorMsg = caRsp.Data.Params.ErrorMsg
		res.FinalSavepointPath = caRsp.Data.Params.FinalSavepointPath
		return controller.InternalError, res.ErrorMsg, res
	}

	return controller.OK, controller.NULL, res
}

func GetClientTimeout(jobConfig *table.JobConfig) (int64, error) {
	//把时间提取出来
	props := make([]*model2.Property, 0)
	if len(jobConfig.Properties) != 0 {
		err := json.Unmarshal([]byte(jobConfig.Properties), &props)
		if err != nil {
			return 0, errorcode.InternalErrorCode.NewWithErr(err)
		}
	}
	strTimeout := ""
	for _, prop := range props {
		if prop.Key == CHECKPOINT_TIMEOUT {
			strTimeout = prop.Value
			break
		}
	}
	if strTimeout == "" {
		logger.Info("No timeout is set in advanced parameters")
		return -1, errors.New("No timeout is set in advanced parameters")
	}
	// 去除空格
	strTimeout = strings.Replace(strTimeout, " ", "", -1)
	//strTimeout（string）->TimeoutDuration（time.Duration）
	TimeoutDuration := &Duration{}
	err := errors.New("")
	TimeoutDuration.Duration, err = common.ParseFlinkDuration(strTimeout)
	if err != nil {
		logger.Error(err)
		return -1, err
	}
	return int64(TimeoutDuration.Duration.Seconds()), nil
}

type Duration struct {
	time.Duration
}

func BuildSavepointEntity(jobId int64, jobRuntimeId int64, status int8, recordType int8, size int64, timeout int64, path string,
	createTime string, updateTime string, description string, clusterId int64, itemSpaceId int64) *model.SavepointEntity {
	savepointEntity := &model.SavepointEntity{}
	savepointEntity.JobId = jobId
	savepointEntity.JobRuntimeId = jobRuntimeId
	savepointEntity.Status = status         //正在运行中
	savepointEntity.RecordType = recordType //手动触发Savepoint
	savepointEntity.Size = size
	savepointEntity.Timeout = timeout
	savepointEntity.Path = path
	savepointEntity.CreateTime = createTime
	savepointEntity.UpdateTime = updateTime
	savepointEntity.Description = description
	savepointEntity.ClusterId = clusterId
	savepointEntity.ItemSpaceId = itemSpaceId
	return savepointEntity
}
