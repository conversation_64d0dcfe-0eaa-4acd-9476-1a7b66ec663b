package service

import (
	"errors"
	"fmt"
	cam2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cam"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/monitor"

	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service2 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
)

/**
 *
 * <AUTHOR>
 * @time 2019/4/27
 * @version 0.4.0
 * @since 0.4.0
 * @see
 */
func DoModifyJob(req *model.ModifyJobReq) (string, string, *model.ModifyJobRsp) {
	logger.Infof("%s: ModifyJob API is called by %d", req.RequestId, req.AppId)
	logger.Infof("%s: ModifyJob request: %+v", req.RequestId, req)

	// 1. 检查作业是否存在
	job, err := service.WhetherJobExists(req.AppId, req.Region, req.JobId)
	if err != nil {
		errCode := errorcode.GetCode(err)
		return errCode.GetCodeStr(), errCode.GetCodeDesc(), nil
	}
	// 鉴权
	err = auth.InnerAuthById(req.WorkSpaceId, req.IsSupOwner, job.ItemSpaceId, int64(req.AppId), req.SubAccountUin, req.Action, false)
	if err != nil {
		logger.Errorf("%s: User  uin %s is NOT authenticated, refuse to serve", req.RequestId, req.Uin)
		errCode := errorcode.GetCode(err)
		return errCode.GetCodeStr(), errCode.GetCodeDesc(), nil
	}

	switch {
	case req.Name != "":
		// 检查新名称是否合规
		err := service.CheckNameValidityV2(req.Name, 100)
		if err != nil {
			logger.Errorf("%s: Failed to CheckNameValidity, with error: %+v", req.RequestId, err)
			return controller.InvalidParameterValue_JobName, err.Error(), nil
		}
		// 检查新作业名称是否存在
		isPass, errCodeStr, msg := service.CheckJobNameExisted(req.AppId, req.Region, req.Name, job.ItemSpaceId)
		if !isPass {
			logger.Error(msg)
			return errCodeStr, msg, nil
		}
		err = UpdateJobName(job.Id, req.Name)
		if err != nil {
			logger.Errorf("%s: Failed to Update JobName, with error: %+v", req.RequestId, err)
			return controller.InternalError, err.Error(), nil
		}
	case req.ContinueAlarm != 0 && req.ContinueAlarm != job.ContinueAlarm:
		err := UpdateContinueAlarm(job.Id, req.ContinueAlarm)
		if err != nil {
			logger.Errorf("%s: Failed to Update ContinueAlarm, with error: %+v", req.RequestId, err)
			return controller.InternalError, err.Error(), nil
		}
	case req.Description != "" || req.Description == "" && job.Description != "":
		err := UpdateDescription(job.Id, req.Description)
		if err != nil {
			logger.Errorf("%s: Failed to Update Description, with error: %+v", req.RequestId, err)
			return controller.InternalError, err.Error(), nil
		}
	case req.Remark != "":
		err := UpdateRemark(job.Id, req.Remark)
		if err != nil {
			logger.Errorf("%s: Failed to Update Remark, with error: %+v", req.RequestId, err)
			return controller.InternalError, err.Error(), nil
		}
	case req.TargetFolderId != "":
		err := UpdateFolderId(job.Id, req.TargetFolderId)
		if err != nil {
			logger.Errorf("%s: Failed to Update FolderId, with error: %+v", req.RequestId, err)
			return controller.InternalError, err.Error(), nil
		}
	default:
		logger.Errorf("%s: Unknown state for request: %+v", req.RequestId, req)
		return controller.UnsupportedOperation, "Unknown state", nil
	}

	return controller.OK, controller.NULL, &model.ModifyJobRsp{RequestId: req.RequestId}
}

func CheckInputCombinations(req *model.ModifyJobReq) bool {
	//if (req.Name != "" && req.Remark == "") ||
	//	(req.Remark != "" && req.Name == "" || (req.Name == "" && req.Remark == "")) {
	//	return true
	//} else {
	//	return false
	//}

	ret := 0

	switch {
	case req.Name != "":
		ret += 1
	}
	switch {
	case req.Remark != "":
		ret += 1
	}
	switch {
	case req.TargetFolderId != "":
		ret += 1
	}

	if ret == 0 || ret == 1 {
		return true
	} else {
		return false
	}

}

func UpdateJobName(jobId int64, newName string) (err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Update JobName panic, with errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()

	txManager := service2.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		rowsAffected, err := tx.ExecuteSqlWithArgs("UPDATE Job SET Name=? WHERE Id=?", newName, jobId).RowsAffected()
		if err != nil {
			return err
		} else if rowsAffected != 1 {
			return errors.New(fmt.Sprintf("Logic error? RowsAffected not 1, but %d when update jobname", rowsAffected))
		}

		return nil
	}).Close()

	return nil
}

func UpdateRemark(jobId int64, newRemark string) (err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Update Job Remark panic, with errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()

	txManager := service2.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		rowsAffected, err := tx.ExecuteSqlWithArgs("UPDATE Job SET Remark=? WHERE Id=?", newRemark, jobId).RowsAffected()
		if err != nil {
			return err
		} else if rowsAffected != 1 {
			return errors.New(fmt.Sprintf("Logic error? RowsAffected not 1, but %d when update remark", rowsAffected))
		}

		return nil
	}).Close()

	return nil
}

func UpdateDescription(jobId int64, newDescription string) (err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Update Job Description panic, with errors:%+v", errs)
			err = fmt.Errorf("%+v", errs)
		}
	}()

	txManager := service2.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		rowsAffected, err := tx.ExecuteSqlWithArgs("UPDATE Job SET Description = ? WHERE Id = ?", newDescription, jobId).RowsAffected()
		if err != nil {
			return err
		} else if rowsAffected != 1 {
			return fmt.Errorf("logic error? RowsAffected not 1, but %d when update description", rowsAffected)
		}

		return nil
	}).Close()

	return nil
}

func UpdateContinueAlarm(jobId int64, continueAlarm int) (err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Update Job ContinueAlarm panic, with errors:%+v", errs)
			err = fmt.Errorf("%+v", errs)
		}
	}()

	txManager := service2.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		rowsAffected, err := tx.ExecuteSqlWithArgs("UPDATE Job SET ContinueAlarm = ? WHERE Id = ?", continueAlarm, jobId).RowsAffected()
		if err != nil {
			return err
		} else if rowsAffected != 1 {
			return fmt.Errorf("logic error? RowsAffected not 1, but %d when update continueAlarm", rowsAffected)
		}
		return nil
	}).Close()
	return nil
}

func UpdateFolderId(jobId int64, TargetFolderId string) (err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Update Job FolderId panic, with errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()

	txManager := service2.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		rowsAffected, err := tx.ExecuteSqlWithArgs("UPDATE Job SET FolderId = ?, UpdateTime = ? WHERE Id = ?", TargetFolderId, util.GetCurrentTime(), jobId).RowsAffected()
		if err != nil {
			return err
		} else if rowsAffected != 1 {
			return errors.New(fmt.Sprintf("Logic error? RowsAffected not 1, but %d when update FolderId", rowsAffected))
		}

		return nil
	}).Close()

	return nil
}

func UpdateScalingMode(jobId int64, scalingMode int) (err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Update Job ScalingMode panic, with errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()

	txManager := service2.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		rowsAffected, err := tx.ExecuteSqlWithArgs("UPDATE Job SET ScalingMode = ?, UpdateTime = ? WHERE Id = ?", scalingMode, util.GetCurrentTime(), jobId).RowsAffected()
		if err != nil {
			return err
		} else if rowsAffected != 1 {
			return errors.New(fmt.Sprintf("Logic error? RowsAffected not 1, but %d when update FolderId", rowsAffected))
		}

		return nil
	}).Close()

	return nil
}

func DoModifyJobDefaultAlarm(req *model.ModifyJobDefaultAlarmReq) (string, string, *model.ModifyJobDefaultAlarmRsp) {
	// 1. 检查作业是否存在
	job, err := service.WhetherJobExists(int32(req.AppId), req.Region, req.JobSerialId)
	if err != nil {
		errCode := errorcode.GetCode(err)
		return errCode.GetCodeStr(), errCode.GetCodeDesc(), nil
	}
	// 鉴权
	err = auth.InnerAuthById(req.WorkSpaceSerialId, req.IsSupOwner, job.ItemSpaceId, int64(req.AppId), req.SubAccountUin, req.Action, false)
	if err != nil {
		logger.Errorf("%s: User  uin %s is NOT authenticated, refuse to serve", req.RequestId, req.Uin)
		errCode := errorcode.GetCode(err)
		return errCode.GetCodeStr(), errCode.GetCodeDesc(), nil
	}
	email, err := cam2.GetUserEmail(job.OwnerUin, job.CreatorUin, job.Region)
	if err != nil {
		errCode := errorcode.GetCode(err)
		return errCode.GetCodeStr(), errCode.GetCodeDesc(), nil
	}
	if email == "" {
		logger.Debugf("user not bind email!!!")
		err = errors.New(fmt.Sprintf("User %s not bind email", job.CreatorUin))
		errCode := errorcode.GetCode(err)
		return errCode.GetCodeStr(), errCode.GetCodeDesc(), nil
	}

	// 获取用户创建人的告警策略
	policyId, err := monitor.GetJobDefaultMonitorAlarmPolicyId(job.OwnerUin, job.CreatorUin, req.Region, req.RequestId)
	if err != nil {
		logger.Errorf("%s: Failed to get JobDefaultMonitorAlarmPolicyId, with errors:%+v", req.RequestId, err)
		errCode := errorcode.GetCode(err)
		return errCode.GetCodeStr(), errCode.GetCodeDesc(), nil
	}
	if req.OpenJobDefaultAlarm == 1 {
		err := monitor.JobBindMonitorPolicy(job.OwnerUin, job.CreatorUin, req.Region, policyId, req.JobSerialId)
		if err != nil {
			logger.Errorf("%s: JobBindMonitorPolicy uin %s is NOT authenticated, refuse to serve，with err：%s", req.RequestId, req.Uin, err)
			errCode := errorcode.GetCode(err)
			return errCode.GetCodeStr(), errCode.GetCodeDesc(), nil
		}
	} else if req.OpenJobDefaultAlarm == 0 {
		err := monitor.JobUnBindMonitorPolicy(job.OwnerUin, job.CreatorUin, req.Region, policyId, req.JobSerialId)
		if err != nil {
			logger.Errorf("%s: JobUnBindMonitorPolicy uin %s is NOT authenticated, refuse to serve，with err：%s", req.RequestId, req.Uin, err)
			errCode := errorcode.GetCode(err)
			return errCode.GetCodeStr(), errCode.GetCodeDesc(), nil
		}
	}
	err = ModifyJobDefaultAlarm(req.JobSerialId, req.OpenJobDefaultAlarm)
	if err != nil {
		logger.Errorf("%s: ModifyJobDefaultAlarm uin %s is NOT authenticated, refuse to serve", req.RequestId, req.Uin)
		errCode := errorcode.GetCode(err)
		return errCode.GetCodeStr(), errCode.GetCodeDesc(), nil
	}
	return controller.OK, controller.NULL, &model.ModifyJobDefaultAlarmRsp{RequestId: req.RequestId}
}
