package service

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	model3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model"
	model1 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/resource_config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cmq"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/item_space"
	service6 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"

	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service1 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	clusterModel "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/cluster"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job"
	model2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/resource"
	model4 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/role_auth"
	model5 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/role_permission"
	clusterTableModel "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	jobModel "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job"
	jobConfigTableModel "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job_config"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/metadata"
	resourceModel "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/resource"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/variable"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/watchdog"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	clusterService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	jobConfigService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job_config"
	metadataService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/metadata"
	resourceService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource"
	service4 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource_config"
	service5 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/role_auth"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/role_permission"
	variableService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/variable"
)

func DoCopyJobs(req *model.CopyJobsReq) (string, string, *model.CopyJobsRsp) {

	if len(req.JobItems) < 1 {
		msg := fmt.Sprintf("%s: InvalidParameterValue.JobItems. No JobItems specified", req.RequestId)
		logger.Error(req.RequestId + ": " + msg)
		return controller.InvalidParameterValue, msg, nil
	}

	if len(req.JobItems) > constants.OPERATION_BATCH_SIZE_UPPER_LIMIT {
		errMsg := fmt.Sprintf("At least copy %d Jobs one time", constants.OPERATION_BATCH_SIZE_UPPER_LIMIT)
		logger.Errorf("%s: Failed to copy jobs. %s", req.RequestId, errMsg)
		return controller.UnsupportedOperation, errMsg, nil
	}

	if req.TargetWorkSpaceId != "" {
		authReq := &model4.DescribeItemSpaceAuthUsersReq{
			RequestBase: req.RequestBase,
			WorkSpaceId: req.TargetWorkSpaceId,
		}
		authOk, errMsg, authRsp := service5.DoDescribeItemSpaceUsers(authReq)
		if authOk != controller.OK {
			return authOk, errMsg, nil
		}
		rsp := authRsp.(*model4.DescribeItemSpaceAuthUsersRsp)
		roleAuths := rsp.RoleAuths

		var found bool
	OuterLoop:
		for _, roleAuth := range roleAuths {
			if roleAuth.AuthSubAccountUin == req.SubAccountUin && roleAuth.WorkSpaceSerialId == req.TargetWorkSpaceId {
				if roleAuth.Permission <= constants.ROLE_AUTHO_PERMISSION_DEVELOPER { // 0-超级管理员, 1-空间管理员, 2-开发者
					found = true
					break
				}
				if roleAuth.Permission == 3 { // 访客不具备写类型权限，所以不进行后续查询
					return controller.InternalError, "current role is guest, has no copyJob permission", nil
				}
				// 自定义角色, 判断是否有 28-复制作业 的权限
				roleSql := "select * from Role where id = ?"
				_, data, err := service1.GetTxManager().GetQueryTemplate().DoQueryWithArgs(roleSql, roleAuth.Permission)
				if err != nil {
					logger.Errorf("failed to query role by roleAuth.Permission %d", roleAuth.Permission)
					return controller.InternalError, "failed to copyJob, please try again later", nil
				}
				roleId := string(data[0]["SerialId"])
				logger.Infof("current roleId [%s] of copyJobs", roleId)
				roleReq := &model5.DescribeRolesReq{
					RequestBase: req.RequestBase,
					RoleId:      roleId,
				}
				roleOk, errMsg, roleRsp := service3.DescribeRoles(roleReq)
				if roleOk != controller.OK {
					return roleOk, errMsg, nil
				}
				for _, role := range roleRsp.Roles {
					for _, permissionId := range role.PermissionIdList {
						if permissionId == 28 { // 28-复制作业
							found = true
							break OuterLoop
						}
					}
				}
			}
		}
		if !found {
			msg := fmt.Sprintf("The user role space permission is limited, Please ask the space administrator for permission")
			return controller.InternalError, msg, nil
		}
	} else {
		req.TargetWorkSpaceId = req.WorkSpaceId
	}

	jobIdList := make([]string, 0, 0)
	clusterIdJobsMap := make(map[string][]model.CopyJobItem)
	for _, copyJobItem := range req.JobItems {
		itemTable, error := item_space.GetItemSpaceByItemSpaceId(req.TargetWorkSpaceId, req.AppId)
		if error != nil {
			logger.Errorf("%s: GetItemSpaceByItemSpaceId error: %+v", req.RequestId, error)
			return controller.UnsupportedOperation, "GetItemSpaceByItemSpaceId error", nil
		}
		isPass, errCodeStr, msg := service.CheckJobNameExisted(int32(req.AppId), req.Region, copyJobItem.TargetName, itemTable.Id)
		if !isPass {
			logger.Errorf("%s: %s", req.RequestId, msg)
			return errCodeStr, msg, nil
		}

		if clusterIdJobsMap[copyJobItem.TargetClusterId] == nil {
			subJobItems := make([]model.CopyJobItem, 0, 0)
			subJobItems = append(subJobItems, copyJobItem)
			clusterIdJobsMap[copyJobItem.TargetClusterId] = subJobItems
		} else {
			clusterIdJobsMap[copyJobItem.TargetClusterId] = append(clusterIdJobsMap[copyJobItem.TargetClusterId], copyJobItem)
		}

		// 鉴权
		_, err := auth.InnerAuthJob(req.WorkSpaceId, req.IsSupOwner, copyJobItem.SourceId, req.AppId, req.SubAccountUin, req.Region, req.Action)
		if err != nil {
			logger.Errorf("%s: User  uin %s is NOT authenticated, refuse to serve", req.RequestId, req.Uin)
			errCode := errorcode.GetCode(err)
			return errCode.GetCodeStr(), errCode.GetCodeDesc(), nil
		}

		jobIdList = append(jobIdList, copyJobItem.SourceId)
	}

	// 查询作业
	listJobQuery := model.ListJobQuery{
		AppId:        0,
		SerialIds:    jobIdList,
		IsVagueNames: false,
		Offset:       0,
		Limit:        0,
	}
	allJobs, err := service.ListJobs(&listJobQuery)
	if err != nil {
		logger.Errorf("%s DoCopyJobs Failed to ListJobs, error:%+v", req.RequestId, err)
		return controller.InternalError, err.Error(), nil
	}
	jobIdJobMap := make(map[string]*jobModel.Job)
	for _, job := range allJobs {
		jobIdJobMap[job.SerialId] = job
	}

	respones := &model.CopyJobsRsp{
		CopyJobsResults: make([]model.CopyJobResult, 0, len(req.JobItems)),
		SuccessCount:    0,
		FailCount:       0,
	}

	for clusterId, copyJobItems := range clusterIdJobsMap {
		code, msg, clusterGroup, cluster := checkCopyJobs(clusterId, req)
		if code != controller.OK {
			for _, copyJobItem := range copyJobItems {
				buildResult(nil, constants.PARAMSERR, msg, copyJobItem, respones, clusterGroup, nil)
			}
			continue
		}
		supportFineGrainedResource, err := clusterService.SupportFineGrainedResource(cluster)
		if err != nil {
			msg := fmt.Sprintf("%s Failed to SupportFineGrainedResource ,err:%s", req.RequestId, err.Error())
			for _, copyJobItem := range copyJobItems {
				buildResult(nil, constants.PARAMSERR, msg, copyJobItem, respones, clusterGroup, nil)
			}
			continue
		}

		tkeClusterType := -1
		if cluster.SchedulerType == constants.CLUSTER_SCHEDULER_TYPE_TKE {
			tkeClusterType, err = service6.GetTableService().GetTkeClusterType(cluster.Id)
			if err != nil {
				msg := fmt.Sprintf("%s Failed to GetTkeClusterType ,err:%s", req.RequestId, err.Error())
				for _, copyJobItem := range copyJobItems {
					buildResult(nil, constants.PARAMSERR, msg, copyJobItem, respones, clusterGroup, nil)
				}
				continue
			}
		}

		supportEtl, err := clusterService.CheckContainerVersionSupport(clusterId, &clusterService.FeatureCondition{
			ComponentName:  constants.ClusterScheduler,
			ImageVersion:   clusterService.ClusterSchedulerVersion,
			CreateTime:     "",
			CIndex:         0,
			CheckFunc:      nil,
			ArchGeneration: constants.TKE_ARCH_GENERATION_V2,
		})
		if !supportEtl {
			supportEtl, err = clusterService.CheckContainerVersionSupport(clusterId, &clusterService.FeatureCondition{
				ComponentName:  constants.ClusterAdmin,
				ImageVersion:   clusterService.EtlMinClusterAdminVersion,
				CreateTime:     "",
				CIndex:         0,
				CheckFunc:      nil,
				ArchGeneration: constants.TKE_ARCH_GENERATION_V2,
			})
			if err != nil {
				msg := fmt.Sprintf("%s Failed to supportEtl ,err:%s", req.RequestId, err.Error())
				for _, copyJobItem := range copyJobItems {
					buildResult(nil, constants.PARAMSERR, msg, copyJobItem, respones, clusterGroup, nil)
				}
				continue
			}
		}
		if err != nil {
			msg := fmt.Sprintf("%s Failed to supportEtl ,err:%s", req.RequestId, err.Error())
			for _, copyJobItem := range copyJobItems {
				buildResult(nil, constants.PARAMSERR, msg, copyJobItem, respones, clusterGroup, nil)
			}
			continue
		}
		for _, copyJobItem := range copyJobItems {
			copyJobTransaction(req, jobIdJobMap, copyJobItem, respones, clusterGroup, cluster, supportFineGrainedResource, supportEtl, tkeClusterType)
		}

		// 复制的作业也算新建作业,需要发送消息到barad, 与默认告警策略绑定
		for _, nJob := range respones.CopyJobsResults {
			err = cmq.SendMsgToBarad(nJob.TargetJobId, int64(req.AppId), req.Uin, req.Region, cmq.MSG_OP_CREATE)
			if err != nil {
				// 无需返回错误
				logger.Errorf("%s send message to barad with error %v", req.RequestId, err)
			}
		}

	}
	return controller.OK, controller.NULL, respones
}

func copyJobTransaction(req *model.CopyJobsReq, jobIdJobMap map[string]*jobModel.Job,
	copyJobItem model.CopyJobItem, respones *model.CopyJobsRsp,
	clusterGroup *clusterTableModel.ClusterGroup,
	cluster *clusterTableModel.Cluster,
	supportFineGrainedResource bool, supportEtl bool, tkeClusterType int) {
	// 查询作业
	job := jobIdJobMap[copyJobItem.SourceId]
	if job == nil {
		buildResult(nil, constants.PARAMSERR, "The job does not exist", copyJobItem, respones, clusterGroup, nil)
		return
	}
	//etl 支持
	if job.Type == constants.JOB_TYPE_ETL && !supportEtl {
		buildResult(nil, constants.PARAMSERR, "The target cluster does not support ETL jobs", copyJobItem, respones, clusterGroup, job)
		return
	}
	// 作业是否属于请求者
	if job.OwnerUin != req.Uin || int64(job.AppId) != req.AppId {
		msg := fmt.Sprintf("job.OwnerUin is %s , AppId is %d , but request: OwnerUin is %s, AppId is %d, userinfo invalid",
			job.OwnerUin, job.AppId, req.Uin, req.AppId)
		logger.Errorf(msg)
		buildResult(nil, constants.PARAMSERR, msg, copyJobItem, respones, clusterGroup, job)
		return
	}

	if job.Region != req.Region {
		msg := fmt.Sprintf("job.Region is %s, but request: Region is %s, region invalid",
			job.Region, req.Region)
		logger.Errorf(msg)
		buildResult(nil, constants.PARAMSERR, msg, copyJobItem, respones, clusterGroup, job)
		return
	}

	// 如果目标集群支持作业版本， 则直接使用，如果不支持，那就使用集群的版本，先把内容copy过去，用户再做修改
	flinkVersion := job.FlinkVersion
	if !strings.Contains(cluster.SupportedFlinkVersion, job.FlinkVersion) {
		flinkVersion = cluster.FlinkVersion
	}
	// 处理作业名称过长, 截取以后加时间
	suffix := time.Now().Format("2006-01-02_15_04_05")
	jobName := fmt.Sprintf("%s_%s", job.Name, suffix)
	suffixLen := len(suffix) + 5 //稍微放大一点点长度
	if len(job.Name)+suffixLen > constants.JOB_NAME_MAX_LENGTH {
		jobName = fmt.Sprintf("%s_%s", job.Name[1:len(job.Name)-suffixLen], suffix)
	}
	if copyJobItem.TargetName != "" {
		jobName = copyJobItem.TargetName
	}

	folderId := job.FolderId
	if copyJobItem.TargetFolderId != "" {
		folderId = copyJobItem.TargetFolderId
	}

	jobConfigs, err := jobConfigService.ListJobConfigs(job.Id, nil, []int64{}, nil)
	if err != nil {
		msg := fmt.Sprintf("Failed to get job config ,with serialId:%s, name:%s, appId:%d, with errors:%+v",
			job.SerialId, job.Name, req.AppId, err)
		buildResult(nil, constants.PARAMSERR, msg, copyJobItem, respones, clusterGroup, job)
		return
	}
	draftJobConfigs, err := jobConfigService.ListJobConfigs(job.Id, []int64{}, []int64{constants.JOB_CONFIG_DRAFT_VERSION}, nil)
	if err != nil {
		msg := fmt.Sprintf("Failed to get job config ,with serialId:%s, name:%s, appId:%d, with errors:%+v",
			job.SerialId, job.Name, req.AppId, err)
		buildResult(nil, constants.PARAMSERR, msg, copyJobItem, respones, clusterGroup, job)
		return
	}
	if len(draftJobConfigs) > 0 {
		jobConfigs = append(jobConfigs, draftJobConfigs[0])
	}

	if len(jobConfigs) == 0 {
		msg := fmt.Sprintf("The job: %s do not has state job config", job.Name)
		buildResult(nil, constants.PARAMSERR, msg, copyJobItem, respones, clusterGroup, job)
		return
	}

	jobConfigIds := make([]int64, 0, len(jobConfigs))
	for _, jobConfig := range jobConfigs {
		// 如果目标集群不支持细粒度，直接把新作业修改为 1.0
		if !supportFineGrainedResource {
			jobConfig.TmCuSpec = 1.0
			jobConfig.JmCuSpec = 1.0
		}
		//过滤仅TKE支持的参数
		if tkeClusterType != constants.K8S_CLUSTER_TYPE_TKE {
			jobConfig.Properties, err = filterJobConfigProperties(jobConfig.Properties)
			if err != nil {
				msg := fmt.Sprintf("The job: %s filterJobConfigProperties failed, with errors:%+v", job.Name, err)
				buildResult(nil, constants.PARAMSERR, msg, copyJobItem, respones, clusterGroup, job)
				return
			}
		}
		if jobConfig.COSBucket != "" {
			jobConfig.COSBucket = cluster.DefaultCOSBucket
		}
		jobConfigIds = append(jobConfigIds, jobConfig.Id)
	}
	// JobMetaTableRef
	jobMetaTableRefs, err := metadataService.ListJobMetaTableRefs(job.Id)
	if err != nil {
		msg := fmt.Sprintf("The job: %s listJobMetaTableRefs failed, with errors:%+v", job.Name, err)
		buildResult(nil, constants.PARAMSERR, msg, copyJobItem, respones, clusterGroup, job)
		return
	}

	// ResourceRef
	refList, err := resourceService.ListResourceRef(nil, jobConfigIds, nil, nil)
	if err != nil {
		msg := fmt.Sprintf("The job: %s ListResourceRef failed, with errors:%+v", job.Name, err)
		buildResult(nil, constants.PARAMSERR, msg, copyJobItem, respones, clusterGroup, job)
		return
	}

	variableRefs, err := variableService.ListJobVariableRefs(job.Id)
	if err != nil {
		msg := fmt.Sprintf("The job: %s ListJobVariableRefs failed, with errors:%+v", job.Name, err)
		buildResult(nil, constants.PARAMSERR, msg, copyJobItem, respones, clusterGroup, job)
		return
	}
	lineage, err := metadataService.GetMetaTableLineage(job.Id)
	if err != nil {
		msg := fmt.Sprintf("The job: %s GetMetaTableLineage failed, with errors:%+v", job.Name, err)
		buildResult(nil, constants.PARAMSERR, msg, copyJobItem, respones, clusterGroup, job)
		return
	}
	//收集符合版本的ResourceId
	resourceIds := make([]int64, 0, 0)
	for _, ref := range refList {
		resourceIds = append(resourceIds, ref.ResourceId)
	}

	systemResourceIdsExclude := make(map[int64]*resourceModel.Resource)
	// 如果flink版本发生了变化，则移除系统connector的依赖
	if flinkVersion != job.FlinkVersion {
		//根据ResourceId去获取Resource与Resource最新的版本
		systemResources, err := resourceService.ListResources(0, job.Region, resourceIds, nil,
			nil, nil, nil, nil, true, -1, -1, constants.RESOURCE_GROUP_ENABLED, constants.SYSTEM_PROVIDED, nil)
		if err != nil {
			msg := fmt.Sprintf("The job: %s ListResources failed, with errors:%+v", job.Name, err)
			buildResult(nil, constants.PARAMSERR, msg, copyJobItem, respones, clusterGroup, job)
			return
		}
		for _, systemResource := range systemResources {
			systemResourceIdsExclude[systemResource.Id] = systemResource
		}
	}

	defer func() {
		if err := recover(); err != nil {
			msg := fmt.Sprintf("copy job %s panic,  errors:%s", copyJobItem.SourceName, err.(error).Error())
			buildResult(nil, constants.PARAMSERR, msg, copyJobItem, respones, clusterGroup, job)
			return
		}
	}()
	// 事务
	newJob := &jobModel.Job{}
	service1.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		// 保存Job
		createJobReq := &model.CreateJobReq{
			AppId:         int32(req.AppId),
			Uin:           req.Uin,
			SubAccountUin: req.SubAccountUin,
			RequestId:     req.RequestId,
			Region:        req.Region,
			Name:          jobName,
			JobType:       job.Type,
			CuMem:         job.CuMem,
			Remark:        job.Remark,
			FolderId:      folderId,
			FlinkVersion:  flinkVersion,
		}
		//设置新作业的ItemSpaceId
		itemTable, err := item_space.GetItemSpaceByItemSpaceId(req.TargetWorkSpaceId, req.AppId)
		if err != nil {
			logger.Errorf("%s: GetItemSpaceByItemSpaceId error: %+v", req.RequestId, err)
			return err
		}
		newJob, err = BuildJobEntityFromReq(clusterGroup, cluster, createJobReq, itemTable.Id)

		// 如果原作业的状态不是 未初始化，副本就设置为停止。因为前端如果是未初始化，就不请求版本管理列表
		if job.Status != constants.JOB_STATUS_CREATE {
			newJob.Status = constants.JOB_STATUS_STOPPED
		}
		newJobId := tx.SaveObject(newJob, "Job")
		cidUtil := &util.CidUtil{}
		serialId := cidUtil.EncodeId(newJobId, "cql", "cql", util.GetNowTimestamp(), 8)
		tx.ExecuteSqlWithArgs("UPDATE Job set serialId = ?  WHERE id = ? ", serialId, newJobId)
		newJob.SerialId = serialId
		// 保存新旧jobConfigId关系
		jobConfigIdMap := make(map[int64]int64)
		// 保存jobConfigs
		for _, jobConfig := range jobConfigs {
			newJobConfig := buildJobConfig(req, jobConfig, cluster, newJobId, job)
			newJobConfigId := tx.SaveObject(newJobConfig, "JobConfig")
			jobConfigIdMap[jobConfig.Id] = newJobConfigId
		}

		// 设置 PublishedJobConfigId LatestJobConfigId LatestJobConfigVersionId LastPublishedJobConfigId ，不然前端版本递增有问题
		if job.Status != constants.JOB_STATUS_CREATE {
			tx.ExecuteSqlWithArgs(
				"UPDATE Job SET PublishedJobConfigId=?, LatestJobConfigId=?, LatestJobConfigVersionId=?, LastPublishedJobConfigId=? WHERE Id=?",
				jobConfigIdMap[job.PublishedJobConfigId], jobConfigIdMap[job.LatestJobConfigId],
				job.LatestJobConfigVersionId, jobConfigIdMap[job.LastPublishedJobConfigId], newJobId)
		}

		// 保存JobMetaTableRef
		for _, jobMetaTableRef := range jobMetaTableRefs {
			newJobMetaTableRef := &table.JobMetaTableRef{
				JobId:       newJobId,
				JobConfigId: jobConfigIdMap[jobMetaTableRef.JobConfigId],
				MetaTableId: jobMetaTableRef.MetaTableId,
				CreateTime:  util.GetCurrentTime(),
			}
			tx.SaveObject(newJobMetaTableRef, "JobMetaTableRef")
		}
		resourceMap := make(map[int64]*resourceModel.ResourceRef)
		sourceResourceMap := make(map[int64]*resourceModel.ResourceRef)
		copiedResourceIds := make(map[int64]bool)
		// 保存ResourceRef
		for _, ref := range refList {
			sourceResourceMap[ref.ResourceId] = ref
			// 如果flink版本有变化，而且是系统connector，不复制
			if systemResourceIdsExclude[ref.ResourceId] != nil {
				continue
			}
			resourceRef := &resourceModel.ResourceRef{}

			//如果是跨空间迁移，需要拷贝resource和resourceConfig
			if req.TargetWorkSpaceId != req.WorkSpaceId && !copiedResourceIds[ref.ResourceId] {
				copiedResourceIds[ref.ResourceId] = true
				resources, err := service2.GetResourceToMapByIds([]int64{ref.ResourceId})
				if err != nil {
					logger.Errorf("%s: getResourceToMapByIds error: %+v", req.RequestId, err)
					return err
				}
				copiedResource, exists := resources[ref.ResourceId]
				if !exists {
					msg := fmt.Sprintf("%s: could not find Resource By Id %d", req.RequestId, ref.ResourceId)
					logger.Errorf(msg)
					return errorcode.FailedOperationCode.NewWithMsg(msg)
				}
				var resourceLoc *model1.ResourceLocation
				copiedResourceConfig, err := service4.GetResourceConfigByResourceId(ref.ResourceId)
				if err != nil {
					logger.Errorf("%s: failed to query ResourceConfig. error: %+v", req.RequestId, err)
					return err
				}
				err = json.Unmarshal([]byte(copiedResourceConfig.ResourceLoc), &resourceLoc)
				if err != nil {
					logger.Errorf("%s: json unmarshal failed error: %+v", req.RequestId, err)
					return err
				}
				createResourceReq := &model2.CreateResourceReq{
					RequestBase:          req.RequestBase,
					Name:                 copiedResource.ResourceName,
					ResourceType:         copiedResource.ResourceType,
					Remark:               copiedResource.Remark,
					ResourceFrom:         copiedResource.ResourceFrom,
					FolderId:             constants.FOLDER_ROOT_ID,
					ResourceLoc:          resourceLoc,
					ResourceConfigRemark: copiedResourceConfig.Remark,
				}
				newResId, _, _, versionId, err := service4.SaveNewResource(createResourceReq, itemTable.Id)
				if err != nil {
					logger.Errorf("%s: failed to save new resource error: %+v", req.RequestId, err)
					return err
				}
				resourceRef.VersionId = versionId
				resourceRef.ResourceId = newResId
				resourceMap[ref.ResourceId] = resourceRef
			} else if copiedResourceIds[ref.ResourceId] {
				savedResourceRef, ok := resourceMap[ref.ResourceId]
				if !ok {
					msg := fmt.Sprintf("%s: current resourceRef lose, resourceId %d ", req.RequestId, ref.ResourceId)
					logger.Errorf(msg)
					return errorcode.InternalErrorCode.NewWithMsg(msg)
				}
				//已经处理的ref和当前ref的versionId如果不同，那么有必要生成新的resourceConfig
				if sourceResourceMap[ref.ResourceId].VersionId != ref.VersionId {
					resources, err := service2.GetResourceToMapByIds([]int64{ref.ResourceId})
					if err != nil {
						logger.Errorf("%s: getResourceToMapByIds error: %+v", req.RequestId, err)
						return err
					}
					resource, ok := resources[ref.ResourceId]
					if !ok {
						msg := fmt.Sprintf("%s: could not find Resource By Id %d", req.RequestId, ref.ResourceId)
						logger.Errorf(msg)
						return errorcode.InternalErrorCode.NewWithMsg(msg)
					}
					copiedResourceConfig, err := service4.GetResourceConfigByResourceId(ref.ResourceId)
					if err != nil {
						logger.Errorf("%s: failed to query ResourceConfig error: %+v", req.RequestId, err)
						return err
					}
					var resourceLoc *model1.ResourceLocation
					err = json.Unmarshal([]byte(copiedResourceConfig.ResourceLoc), &resourceLoc)
					if err != nil {
						logger.Errorf("%s: json unmarshal failed error: %+v", req.RequestId, err)
						return err
					}
					createResourceConfigReq := &model1.CreateResourceConfigReq{}
					createResourceConfigReq.AppId = req.AppId
					createResourceConfigReq.Uin = req.Uin
					createResourceConfigReq.SubAccountUin = req.SubAccountUin
					createResourceConfigReq.Region = req.Region
					createResourceConfigReq.RequestId = req.RequestId
					createResourceConfigReq.Version = req.Version
					createResourceConfigReq.ResourceId = resource.ResourceId
					createResourceConfigReq.ResourceLoc = resourceLoc
					createResourceConfigReq.Remark = copiedResourceConfig.Remark
					_, maxVersion, err := service2.SaveNewResourceConfig(createResourceConfigReq, resource)
					if err != nil {
						logger.Errorf("%s: save new ResourceConfig error: %+v", req.RequestId, err)
						return err
					}
					resourceRef.VersionId = maxVersion
				} else {
					resourceRef.VersionId = savedResourceRef.VersionId
				}
				resourceRef.ResourceId = savedResourceRef.ResourceId
			}
			//同空间下保留原本的ResourceId和VersionId
			if req.TargetWorkSpaceId == req.WorkSpaceId {
				resourceRef.ResourceId = ref.ResourceId
				resourceRef.VersionId = ref.VersionId
			}
			resourceRef.Status = ref.Status
			resourceRef.UsageType = ref.UsageType
			resourceRef.CreateTime = util.GetCurrentTime()
			resourceRef.JobConfigId = jobConfigIdMap[ref.JobConfigId]
			tx.SaveObject(resourceRef, "ResourceRef")
		}

		// 同步作业快照
		oldJobSavepoint, err := service1.GetActiveSavePointByJobId(job.Id)
		if err != nil {
			logger.Errorf("GetActiveSavePointByJobId with error: %v", err)
			return err
		}
		for _, svp := range oldJobSavepoint {
			newSvp := &watchdog.SavepointEntity{}
			newSvp.ClusterId = newJob.ClusterId
			newSvp.JobId = newJobId
			newSvp.JobRuntimeId = svp.JobRuntimeId
			newSvp.ItemSpaceId = newJob.ItemSpaceId
			newSvp.Status = svp.Status
			newSvp.Path = svp.Path
			newSvp.CreateTime = svp.CreateTime
			newSvp.UpdateTime = svp.UpdateTime
			newSvp.RecordType = svp.RecordType
			newSvp.Size = svp.Size
			newSvp.Timeout = svp.Timeout
			newSvp.Description = svp.Description
			svpId := tx.SaveObject(newSvp, "Savepoint")
			cidUtil := &util.CidUtil{}
			serialId := cidUtil.EncodeId(svpId, "svp", "svp", util.GetNowTimestamp(), 8)
			tx.ExecuteSqlWithArgs("UPDATE Savepoint set serialId = ?  WHERE id = ? ", serialId, svpId)
		}
		// 保存variableRefs
		for _, variableRef := range variableRefs {
			newVariableRef := &table2.VariableReference{
				VariableId:    variableRef.VariableId,
				ComponentId:   jobConfigIdMap[variableRef.ComponentId],
				ComponentType: variableRef.ComponentType,
				Status:        variableRef.Status,
				CreateTime:    util.GetCurrentTime(),
				UpdateTime:    util.GetCurrentTime(),
			}
			tx.SaveObject(newVariableRef, "VariableReference")
		}
		// 保存lineage
		for _, tableLineage := range lineage {
			newTableLineage := &table.MetaTableLineage{
				JobConfigId: jobConfigIdMap[tableLineage.JobConfigId],
				SourceId:    tableLineage.SourceId,
				SinkId:      tableLineage.SinkId,
				SourceRef:   tableLineage.SourceRef,
				SinkRef:     tableLineage.SinkRef,
				SourceType:  tableLineage.SourceType,
				Status:      tableLineage.Status,
				Region:      tableLineage.Region,
				Uin:         tableLineage.Uin,
				SubUin:      tableLineage.SubUin,
				AppId:       req.AppId,
				CreateTime:  util.GetCurrentTime(),
				UpdateTime:  util.GetCurrentTime(),
			}
			tx.SaveObject(newTableLineage, "MetaTableLineage")
		}
		buildResult(newJob, constants.SUCCESS, controller.OK, copyJobItem, respones, clusterGroup, job)
		return err
	}).Close()

	// 绑定告警给复制的用户
}

func filterJobConfigProperties(properties string) (string, error) {
	if properties == "" {
		return properties, nil
	}

	dynamicProperties := make([]*model3.Property, 0)
	newDynamicProperties := make([]*model3.Property, 0)
	err := json.Unmarshal([]byte(properties), &dynamicProperties)
	if err != nil {
		// 防御式编程, 理论上不会格式错乱
		return properties, errorcode.NewStackError(errorcode.InternalErrorCode, "JobConfig Properties JSON unmarshal error", err)
	}
	for _, property := range dynamicProperties {
		if _, exist := constants.WhiteListFlinkConfigurationForTke[strings.TrimSpace(property.Key)]; exist {
			continue
		}
		newDynamicProperties = append(newDynamicProperties, property)
	}
	newProperties, err := json.Marshal(newDynamicProperties)
	if err != nil {
		// 防御式编程, 理论上不会格式错乱
		return properties, errorcode.NewStackError(errorcode.InternalErrorCode, "JobConfig Properties JSON marshal error", err)
	}
	return string(newProperties), err
}

func buildJobConfig(req *model.CopyJobsReq, jobConfig *jobConfigTableModel.JobConfig,
	cluster *clusterTableModel.Cluster, newJobId int64, sourceJob *jobModel.Job) jobConfigTableModel.JobConfig {
	newJobConfig := jobConfigTableModel.JobConfig{
		CreatorUin:              req.Uin,
		JobId:                   newJobId,
		VersionId:               jobConfig.VersionId,
		EntrypointClass:         jobConfig.EntrypointClass,
		ProgramArgs:             jobConfig.ProgramArgs,
		CheckpointInterval:      jobConfig.CheckpointInterval,
		SqlCode:                 jobConfig.SqlCode,
		Status:                  constants.JOB_CONFIG_STATUS_UN_PUBLISHED,
		Remark:                  jobConfig.Remark,
		CreateTime:              util.GetCurrentTime(),
		MaxParallelism:          jobConfig.MaxParallelism,
		DefaultParallelism:      jobConfig.DefaultParallelism,
		Properties:              jobConfig.Properties,
		COSBucket:               jobConfig.COSBucket,
		JmCuSpec:                jobConfig.JmCuSpec,
		TmCuSpec:                jobConfig.TmCuSpec,
		PythonVersion:           jobConfig.PythonVersion,
		CheckpointRetainedNum:   jobConfig.CheckpointRetainedNum,
		ExpertModeConfiguration: jobConfig.ExpertModeConfiguration,
		TraceModeConfiguration:  jobConfig.TraceModeConfiguration,
		ClazzLevels:             jobConfig.ClazzLevels,
		JobGraph:                jobConfig.JobGraph,
		UseOldSysConnector:      jobConfig.UseOldSysConnector,
		LogLevel:                jobConfig.LogLevel,
		AutoRecover:             jobConfig.AutoRecover,
		FlinkVersion:            jobConfig.FlinkVersion,
		JobManagerCpu:           jobConfig.JobManagerCpu,
		JobManagerMem:           jobConfig.JobManagerMem,
		TaskManagerCpu:          jobConfig.TaskManagerCpu,
		TaskManagerMem:          jobConfig.TaskManagerMem,
	}

	// https://tapd.woa.com/tapd_fe/20358692/bug/detail/1020358692135441379
	if sourceJob.CuMem == constants.CU_MEM_2GB && sourceJob.CuMem < cluster.MemRatio {
		if newJobConfig.JobManagerMem > 0 && newJobConfig.JobManagerMem < 1 {
			newJobConfig.JobManagerMem = 1
		}
		if newJobConfig.TaskManagerMem > 0 && newJobConfig.TaskManagerMem < 1 {
			newJobConfig.TaskManagerMem = 1
		}
	}

	// 如果是同一个集群， 就把原作业的日志配置直接复制过来
	if sourceJob.ClusterId == cluster.Id {
		newJobConfig.ClsLogsetId = jobConfig.ClsLogsetId
		newJobConfig.ClsTopicId = jobConfig.ClsTopicId
		newJobConfig.LogCollect = jobConfig.LogCollect
		newJobConfig.EsServerlessIndex = jobConfig.EsServerlessIndex
		newJobConfig.EsServerlessSpace = jobConfig.EsServerlessSpace
		return newJobConfig
	}
	newJobConfig.LogCollect = constants.JobLogCollectDisabled
	// 判断原来作业开启是否.
	if jobConfig.LogCollect != constants.JobLogCollectDisabled {
		defaultLogCollectConf := &clusterModel.DefaultLogCollectConf{}
		if cluster.DefaultLogCollectConf != "" { // 集群的日志默认采集配置不为空，此时创建作业草稿的日志集和日志主题应该为默认配置
			err := json.Unmarshal([]byte(cluster.DefaultLogCollectConf), defaultLogCollectConf)
			if err == nil {
				newJobConfig.ClsLogsetId = defaultLogCollectConf.Conf.ClsLogsetId
				newJobConfig.ClsTopicId = defaultLogCollectConf.Conf.ClsTopicId
				newJobConfig.EsServerlessIndex = defaultLogCollectConf.Conf.EsServerlessIndex
				newJobConfig.EsServerlessSpace = defaultLogCollectConf.Conf.EsServerlessSpace
				newJobConfig.LogCollect = constants.JobLogCollectEnabled
			}
		}
	}
	return newJobConfig
}

func buildResult(newJob *jobModel.Job, result int64, msg string, copyJobItem model.CopyJobItem,
	respones *model.CopyJobsRsp, clusterGroup *clusterTableModel.ClusterGroup, sourceJob *jobModel.Job) {
	logger.Errorf(msg)
	if result == constants.PARAMSERR {
		respones.FailCount = respones.FailCount + 1
	} else {
		respones.SuccessCount = respones.SuccessCount + 1
	}
	copyJobsResult := model.CopyJobResult{
		JobId:   copyJobItem.SourceId,
		Result:  result,
		Message: msg,
		JobName: copyJobItem.SourceName,
		JobType: copyJobItem.JobType,
	}
	if sourceJob != nil {
		copyJobsResult.JobName = sourceJob.Name
	}
	if clusterGroup != nil {
		copyJobsResult.ClusterName = clusterGroup.Name
		copyJobsResult.ClusterId = clusterGroup.SerialId
	}
	if newJob != nil {
		copyJobsResult.TargetJobId = newJob.SerialId
		copyJobsResult.TargetJobName = newJob.Name
	}
	respones.CopyJobsResults = append(respones.CopyJobsResults, copyJobsResult)
}

func checkCopyJobs(clusterId string, req *model.CopyJobsReq) (string, string, *clusterTableModel.ClusterGroup, *clusterTableModel.Cluster) {
	// 集群是否存在
	clusterGroup, err := clusterService.ListClusterGroupBySerialId(clusterId)
	if err != nil {
		logger.Errorf("Failed to get cluster group by serial id %s because %+v", clusterId, err)
		return controller.InternalError, "Failed to get cluster group", nil, nil
	}
	cluster, err := clusterService.GetActiveClusterByClusterGroupId(clusterGroup.Id)
	if err != nil {
		logger.Errorf("Failed to get cluster by cluster group id for %d because %+v", clusterGroup.Id, err)
		return controller.InternalError, "Failed to get cluster group", clusterGroup, nil
	}

	if clusterGroup.Region != req.Region {
		msg := fmt.Sprintf("clusterGroup.Region is %s, but request: Region is %s, region invalid",
			clusterGroup.Region, req.Region)
		logger.Errorf(msg)
		return controller.InternalError, msg, clusterGroup, cluster
	}
	// 集群是否属于请求者
	if clusterGroup.OwnerUin != req.Uin || int64(clusterGroup.AppId) != req.AppId {
		msg := fmt.Sprintf("clusterGroup.OwnerUin is %s , AppId is %d , but request: OwnerUin is %s, AppId is %d, userinfo invalid",
			clusterGroup.OwnerUin, clusterGroup.AppId, req.Uin, req.AppId)
		logger.Errorf(msg)
		return controller.InternalError, msg, clusterGroup, cluster
	}
	// 集群是不是删除了
	if clusterGroup.Status == constants.CLUSTER_GROUP_STATUS_DELETED ||
		clusterGroup.Status == constants.CLUSTER_GROUP_STATUS_DELETING {
		msg := fmt.Sprintf("cluster: %s is deleted or deleting, can not copy jobs.", clusterGroup.Name)
		logger.Errorf(msg)
		return controller.InternalError, msg, clusterGroup, cluster
	}
	return controller.OK, controller.NULL, clusterGroup, cluster
}
