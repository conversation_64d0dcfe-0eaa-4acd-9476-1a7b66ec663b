package service

import (
	"github.com/juju/errors"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	table1 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job_config"
	common "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
)

/* ================= Watchdog 专用 ================= */

type QueryJobInstance struct {
	// 来自 JobInstance 表
	JobInstanceId      int64
	JobId              int64
	JobConfigId        int64
	TmRunningCuNum     int16
	DefaultParallelism int64
	CuMem              int8
	JobInstanceStatus  int8
	FlinkJobId         string
	ApplicationId      string
	CreateTime         string
	UpdateTime         string
	StartTime          string
	StopTime           string

	// 来自 Job 表
	JobStatus      int8
	SerialId       string
	Name           string
	AppId          int32
	OwnerUin       string
	CreatorUin     string
	Region         string
	Zone           string
	Type           int8
	ClusterGroupId int64
	ClusterId      int64
	FlinkVersion   string

	// 来自 Cluster 表
	SchedulerType int32
}

// 根据 ClusterID 和 Status 来筛选作业 用于 Watchdog 监控集群状态)
func QueryJobBy(clusterId int64, jobStatus int32, jobInstanceStatus int32, serialId string) (t []*QueryJobInstance, err error) {
	if clusterId <= 0 && jobStatus == 0 && jobInstanceStatus == 0 && serialId == "" {
		return nil, errors.New("Please provide at least one valid argument")
	}

	sql := "SELECT * FROM (" +
		"SELECT ji.Id AS JobInstanceId, ji.JobId, ji.JobConfigId, ji.TmRunningCuNum, ji.DefaultParallelism, ji.CuMem, " +
		"ji.Status AS JobInstanceStatus, ji.FlinkJobId, ji.ApplicationId, ji.CreateTime, ji.UpdateTime, ji.StartTime, " +
		"ji.StopTime, j.Status AS JobStatus, j.SerialId, j.Name, j.AppId, j.OwnerUin, j.CreatorUin, j.Region, j.Zone, j.Type, " +
		"j.ClusterGroupId, j.ClusterId, j.FlinkVersion, cl.SchedulerType" +
		" FROM JobInstance ji, Job j, Cluster cl WHERE ji.JobId = j.Id AND j.ClusterId = cl.Id"
	args := make([]interface{}, 0)

	if clusterId > 0 {
		sql += " AND j.ClusterId = ?"
		args = append(args, clusterId)
	}

	if jobStatus != 0 {
		sql += " AND j.Status = ?"
		args = append(args, jobStatus)
	} else {
		sql += " AND j.Status <> " + strconv.Itoa(constants.JOB_STATUS_DELETE)
	}

	if jobInstanceStatus != 0 {
		sql += " AND ji.Status = ?"
		args = append(args, jobInstanceStatus)
	} else {
		sql += " AND ji.Status <> " + strconv.Itoa(constants.JOB_INSTANCE_STATUS_HISTORY)
	}

	if serialId != "" {
		sql += " AND j.SerialId = ?"
		args = append(args, serialId)
	}

	sql += " ORDER BY ji.CreateTime DESC ) AS r GROUP BY r.JobId" // 同一个作业只返回最新的

	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Error("Failed to query with sql:", sql, ", errors:", err.Error())
		return t, err
	}

	t = make([]*QueryJobInstance, len(data))
	for i := 0; i < len(data); i++ {
		temp := &QueryJobInstance{}
		_ = util.ScanMapIntoStruct(temp, data[i])
		t[i] = temp
	}
	return t, nil
}

func QueryJobWithAutoTuning(clusterId int64, jobStatus int32, jobInstanceStatus int32, serialId string, jobName string,
	manageType string, clusterSerialId string, flinkJobType int8) (t []*QueryJobInstanceWithAutoTuning, err error) {
	if clusterId <= 0 && jobStatus == 0 && jobInstanceStatus == 0 && serialId == "" && jobName == "" {
		return nil, errors.New("Please provide at least one valid argument")
	}

	clusterIds := make([]int64, 0)
	if clusterId > 0 {
		sqlCluster := "SELECT ClusterId as Id FROM Tke WHERE InstanceId = (SELECT InstanceId FROM Tke WHERE ClusterId  = ? ) and Status=1 "
		argsCluster := make([]interface{}, 0)
		argsCluster = append(argsCluster, clusterId)

		txManager := service.GetTxManager()
		_, dataClusters, err := txManager.GetQueryTemplate().DoQuery(sqlCluster, argsCluster)
		if err != nil {
			logger.Error("Failed to query with sql:", sqlCluster, ", errors:", err.Error())
			return t, err
		}
		for _, ref := range dataClusters {
			clusterItem := &table1.Cluster{}
			err = util.ScanMapIntoStruct(clusterItem, ref)
			if err != nil {
				continue
			}
			clusterIds = append(clusterIds, clusterItem.Id)
		}
	}

	sql := "SELECT * FROM (" +
		"SELECT ji.Id AS JobInstanceId, ji.JobId, ji.JobConfigId, ji.TmRunningCuNum, ji.DefaultParallelism, ji.CuMem, " +
		"ji.Status AS JobInstanceStatus, ji.FlinkJobId, ji.ApplicationId, ji.CreateTime, ji.UpdateTime, ji.StartTime, " +
		"ji.StopTime, j.Status AS JobStatus, j.SerialId, j.Name, j.AppId, j.OwnerUin, j.CreatorUin, j.Region,  IF(NULLIF(ji.Zone, '') IS NULL, j.Zone, ji.Zone) as Zone, j.Type,  j.ManageType, j.FlinkJobType, " +
		"j.ClusterGroupId, j.ClusterId, j.FlinkVersion, cl.SchedulerType, cg.SerialId as ClusterGroupSerialId, cg.ResourceType " +
		" FROM JobInstance ji, Job j, Cluster cl, ClusterGroup cg WHERE ji.JobId = j.Id AND j.ClusterId = cl.Id AND cl.ClusterGroupId = cg.Id"
	args := make([]interface{}, 0)

	if jobStatus != 0 {
		sql += " AND j.Status = ?"
		args = append(args, jobStatus)
	} else {
		sql += " AND j.Status <> " + strconv.Itoa(constants.JOB_STATUS_DELETE)
	}

	if jobInstanceStatus != 0 {
		sql += " AND ji.Status = ?"
		args = append(args, jobInstanceStatus)
	} else {
		sql += " AND IF(j.ManageType = 'internal', ji.Status <> " + strconv.Itoa(constants.JOB_INSTANCE_STATUS_HISTORY) + ", TRUE)"
	}

	if serialId != "" {
		sql += " AND j.SerialId = ?"
		args = append(args, serialId)
	}

	if jobName != "" {
		sql += " AND j.Name = ? "
		args = append(args, jobName)
	}

	if manageType != "" {
		sql += "  AND j.ManageType = ? "
		args = append(args, manageType)
	}

	if len(clusterIds) > 0 {
		clusterIds := UniqueSliceInt64(clusterIds)
		sql += " AND cl.Id IN("
		for i := 1; i < len(clusterIds); i++ {
			sql += "?,"
			args = append(args, clusterIds[i])
		}
		sql += "?)"
		args = append(args, clusterIds[0])
		clusterId = 0
	}

	if clusterId > 0 {
		sql += " AND j.ClusterId = ?"
		args = append(args, clusterId)
	}

	if clusterSerialId != "" {
		sql += "  AND cg.SerialId = ? "
		args = append(args, clusterSerialId)
	}

	if flinkJobType > 0 {
		sql += "  AND j.FlinkJobType = ? "
		args = append(args, flinkJobType)
	}

	sql += " ORDER BY ji.CreateTime DESC ) AS r GROUP BY r.JobId " // 同一个作业只返回最新的

	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Error("Failed to query with sql:", sql, ", errors:", err.Error())
		return t, err
	}

	t = make([]*QueryJobInstanceWithAutoTuning, len(data))
	for i := 0; i < len(data); i++ {
		temp := &QueryJobInstanceWithAutoTuning{}
		_ = util.ScanMapIntoStruct(temp, data[i])
		// 新增自动扩缩容配置信息
		_, status, config := QueryAutoTuningInfos(temp)
		temp.AutoTuningStatus = status
		temp.AutoTuningConfig = config
		// 收集job运行资源
		job, err := getJobById(temp.JobId)
		if err != nil {
			logger.Errorf("Failed to get job info by id:%s", temp.JobId)
			return nil, err
		}
		jc, jm, tc, tm, err := common.GetJobRunningCPUAndMem(job)
		if err != nil {
			return nil, err
		}
		temp.JmRunningMem = jm
		temp.JmRunningCpu = jc
		temp.TmRunningMem = tm
		temp.TmRunningCpu = tc
		t[i] = temp
	}

	return t, nil
}

func QueryAutoTuningInfos(instance *QueryJobInstanceWithAutoTuning) (err error, status int8, config string) {
	//这里不可以
	jobScaleRules, err := queryJobScaleRules(instance.SerialId, int64(instance.AppId), constants.SCALE_RULES_STATUS_ACTIVE, "")
	if err != nil {
		return nil, constants.SCALE_RULES_STATUS_INACTIVE, ""
	}
	if len(jobScaleRules) == 0 {
		return nil, constants.SCALE_RULES_STATUS_INACTIVE, ""
	}
	if len(jobScaleRules) > 1 {
		logger.Errorf("Get more than one jobScaleRules for job %s!", instance.SerialId)
		return nil, constants.SCALE_RULES_STATUS_INACTIVE, ""
	}
	jobScaleRule := jobScaleRules[0]
	status = jobScaleRule.Status
	config = jobScaleRule.Configuration
	return nil, status, config
}

func queryJobScaleRules(jobId string, appId int64, status int8, requestId string) ([]*table.JobScaleRule, error) {
	sql := "SELECT * FROM JobScaleRule  "
	cond := dao.NewCondition()
	if jobId != "" {
		cond.Eq("JobId", jobId)
	}
	if appId != 0 {
		cond.Eq("AppId", appId)
	}
	if status != 0 {
		cond.Eq("Status", status)
	}
	cond.Ne("Status", constants.SCALE_RULES_STATUS_DELETE)
	// 只检查自动扩缩容规则
	cond.Eq("RuleName", constants.SCALE_RULES_AUTO_SCALE_BASIC)
	where, args := cond.GetWhere()
	sql += where

	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("%s Exception occurs when query JobScaleRule from db, with errors:%+v", requestId, err)
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	tableRules := make([]*table.JobScaleRule, 0) //声明tableRules为JobScaleRule数组对象
	for i := 0; i < len(data); i++ {
		rule := &table.JobScaleRule{}               //声明rule为JobScaleRule对象
		err = util.ScanMapIntoStruct(rule, data[i]) //将db查询出来的data强转成JobScaleRule对象
		if err != nil {
			logger.Errorf("%s Failed to convert bytes into table.JobScaleRule, with errors:%+v", requestId, err)
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode_UnMarshalFailed, "", err)
		}
		tableRules = append(tableRules, rule)
	}
	return tableRules, nil
}

// SetJobConfigVal 把 jobConfig 中的 AutoRecover 设置到 QueryJobInstanceWithAutoTuning 中
func SetJobConfigVal(jobInfo []*QueryJobInstanceWithAutoTuning) error {
	logger.Infof("QueryJob4 %s", util.GetCurrentTime())
	jobConfigIds := make([]int64, len(jobInfo))
	for _, info := range jobInfo {
		jobConfigIds = append(jobConfigIds, info.JobConfigId)
	}
	logger.Infof("QueryJob5 %s", util.GetCurrentTime())
	// 查询作业配置
	jobConfigs, err := common.GetJobConfigByIds(jobConfigIds)
	if err != nil {
		return err
	}
	logger.Infof("QueryJob6 %s", util.GetCurrentTime())
	jobConfigMap := make(map[int64]*table.JobConfig)
	for _, v := range jobConfigs {
		jobConfigMap[v.Id] = v
	}
	logger.Infof("QueryJob7 %s", util.GetCurrentTime())
	// 把字段设置到QueryJobInstanceWithAutoTuning中
	for _, info := range jobInfo {
		info.AutoRecover = jobConfigMap[info.JobConfigId].AutoRecover
		info.Properties = jobConfigMap[info.JobConfigId].Properties
	}
	logger.Infof("QueryJob8 %s", util.GetCurrentTime())
	return nil
}

type QueryJobInstanceWithAutoTuning struct {
	// 来自 JobInstance 表
	JobInstanceId      int64
	JobId              int64
	JobConfigId        int64
	TmRunningCuNum     int16
	DefaultParallelism int64
	CuMem              int8
	JobInstanceStatus  int8
	FlinkJobId         string
	ApplicationId      string
	CreateTime         string
	UpdateTime         string
	StartTime          string
	StopTime           string

	// 来自 Jo
	//b 表
	JobStatus      int8
	SerialId       string
	Name           string
	AppId          int32
	OwnerUin       string
	CreatorUin     string
	Region         string
	Zone           string
	Type           int8
	ClusterGroupId int64
	ClusterId      int64
	FlinkVersion   string
	ManageType     string
	FlinkJobType   int32

	// 来自 Cluster 表
	SchedulerType int32

	// 来自 ClustergGroup 表
	ClusterGroupSerialId string
	ResourceType         int32

	// 来自 job_scale_rule
	AutoTuningStatus int8
	AutoTuningConfig string

	// 来自jobconfig表
	AutoRecover int // Oceanus 平台恢复作业开关 1:开启 -1: 关闭

	// 来自计算
	JmRunningCpu float32
	JmRunningMem float32
	TmRunningCpu float32
	TmRunningMem float32
	Properties   string
}
