package service

import (
	"errors"
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service2 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster_master"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job_instance"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/watchdog"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
)

func ChangeJobState(req *watchdog.ChangeJobStateReq) (status int64, msg string, resp *watchdog.ChangeJobStateRsp, err error) {
	logger.Infof("%s: ChangeJobState API called for job id %d state %s", req.RequestId, req.JobId,
		getJobStateInfo(req.State))

	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Failed to ChangeJobState, errors: %+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()

	rsp := &watchdog.ChangeJobStateRsp{IsSucc: controller.NULL}

	txManager := service2.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		job, err := service2.GetJobById(req.JobId)
		if err != nil {
			logger.Errorf("%s: Failed to GetJobById for job id %d because %+v", req.RequestId, req.JobId, err)
			return err
		}

		logInfo := fmt.Sprintf(":%s: job [%d] [%s]", req.RequestId, job.Id, job.SerialId)

		jobInstanceList, err := service.ListJobInstancesNotInStatus(job.Id, constants.JOB_INSTANCE_STATUS_HISTORY)
		if err != nil {
			return err
		} else if len(jobInstanceList) != 1 {
			logger.Infof("%s: Failed to get JobRuntimeId when updateJobInstanceState, "+
				"jobInstanceList len %d not 1", logInfo, len(jobInstanceList))
			return nil
		}
		jobInstance := jobInstanceList[0]

		if jobInstance.Id != req.JobInstanceId {
			logger.Warningf("%s: current jobInstanceId[%d] not equal req jobInstanceId[%d], "+
				"ingore this request", jobInstance.Id, req.JobInstanceId)
			return nil
		}

		logger.Infof("%s ChangeJobState API called for job id %d state %s", logInfo, req.JobId,
			getJobStateInfo(req.State))
		newState, err := getFinallyState(req, job)
		if err != nil {
			logger.Errorf("%s getFinallyState err:%s", logInfo, err)
			return err
		}

		logger.Infof("%s try to change current status %s to %s", logInfo, getJobStateInfo(job.Status),
			getJobStateInfo(newState))

		if job.Status == newState {
			logger.Infof("%s current status %s, not need to change to %s", logInfo, getJobStateInfo(job.Status),
				getJobStateInfo(newState))
			return nil
		}

		if err = isJobCanTransitionState(job.Status, newState, jobInstance); err != nil {
			logger.Errorf("%s current status %s can't change to %s, err: %+v", logInfo, job.Id,
				getJobStateInfo(job.Status), getJobStateInfo(newState), err)
			return nil
		}

		if err = updateJob(tx, job, newState); err != nil {
			logger.Errorf("%s updateJob, err: %+v", logInfo, err)
			return err
		}

		if err = updateJobInstanceState(tx, job.Id, newState); err != nil {
			logger.Errorf("%s job id %d updateJobInstanceState err:%+v", logInfo, job.Id, err)
			return err
		}

		rsp.IsSucc = controller.OK
		return nil
	}).Close()

	logger.Infof("%s ChangeJobState done for job id %d", req.RequestId, req.JobId)

	return controller.SUCCESS, controller.NULL, rsp, nil
}

func getFinallyState(req *watchdog.ChangeJobStateReq, job *table.Job) (int8, error) {
	if req.State != constants.JOB_STATUS_STOPPED && req.State != constants.JOB_STATUS_FINISHED {
		return req.State, nil
	}

	txManager := service2.GetTxManager()
	sql := "SELECT * FROM Command where jobId=? order by Id desc limit 1"
	args := make([]interface{}, 0)
	args = append(args, job.Id)
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to get commands, with errors:%+v", err)
		return 0, err
	}
	commands := make([]*cluster_master.Command, 0)
	for i := 0; i < len(data); i++ {
		command := &cluster_master.Command{}
		err = util.ScanMapIntoStruct(command, data[i])
		if err != nil {
			logger.Errorf("Failed to convert bytes into Command, with errors:%+v", err)
			return 0, err
		}
		commands = append(commands, command)
	}
	if len(commands) != 1 {
		errMsg := fmt.Sprintf("recv stop state, not command exists")
		return 0, errors.New(errMsg)
	}

	// FIXME
	// watchdog 只能区分 集群STOP， 无法区分是 STOP 还是PAUSE
	// 而 command.Action 只有 CLUSTER_MASTER_COMMAND_ACTION_CANCEL_JOB, 而没有 CLUSTER_MASTER_COMMAND_ACTION_CANCEL_JOB_WITH_SAVEPOINT
	// 对于CLUSTER_MASTER_COMMAND_ACTION_CANCEL_JOB， 只能通过 JobNextStatus 来区分， 是STOP 还是 PAUSE
	if commands[0].Action == constants.CLUSTER_MASTER_COMMAND_ACTION_CANCEL_JOB {
		return commands[0].JobNextStatus, nil
	}

	return constants.JOB_STATUS_STOPPED, nil
}

var jobStateTransMap = map[int8]map[int8]struct{}{
	constants.JOB_STATUS_RUNNING: {
		constants.JOB_STATUS_RUNNING:  {},
		constants.JOB_STATUS_STOPPED:  {},
		constants.JOB_STATUS_FINISHED: {},
	},
	constants.JOB_STATUS_PROGRESS: {
		constants.JOB_STATUS_RUNNING:  {}, // 作业启动成功
		constants.JOB_STATUS_STOPPED:  {}, // 启动失败。
		constants.JOB_STATUS_PAUSED:   {},
		constants.JOB_STATUS_FINISHED: {},
	},
}

func isJobCanTransitionState(fromState, toState int8, jobInstance *table2.JobInstance) error {
	find := false
	if v, ok := jobStateTransMap[fromState]; ok {
		_, find = v[toState]
	}
	if !find {
		return errors.New(fmt.Sprintf("current state %s can't trans to %s", getJobStateInfo(fromState),
			getJobStateInfo(toState)))
	}
	if toState == constants.JOB_STATUS_RUNNING {
		if jobInstance.Status == constants.JOB_INSTANCE_STATUS_CREATE || jobInstance.Status == constants.
			JOB_INSTANCE_STATUS_DEPLOYING {
			return nil
		}
		return errors.New(fmt.Sprintf("jobInstanceState %s, not allow to change job state to %s",
			getJobInstanceStateInfo(jobInstance.Status), getJobStateInfo(toState)))
	}
	return nil
}

func updateJob(tx *dao.Transaction, job *table.Job, newJobState int8) error {
	switch newJobState {
	case constants.JOB_STATUS_RUNNING:
		if job.Status != constants.JOB_STATUS_RUNNING {
			sql := "UPDATE Job SET Status=?, StartTime=? WHERE Id=?"
			tx.ExecuteSqlWithArgs(sql, newJobState, util.GetCurrentTime(), job.Id)
		}
	case constants.JOB_STATUS_STOPPED, constants.JOB_STATUS_PAUSED, constants.JOB_STATUS_FINISHED:
		curRuntime, err := service.GetCurrentRunMillis(job.StartTime)
		if err != nil {
			logger.Errorf("Failed to GetCurrentRunMillis for job, error: %+v", err)
			return err
		}
		sql := "UPDATE Job SET Status=?, StopTime=?, TotalRunMillis=?, TmRunningCuNum=0, RunningCpu=0, RunningMem=0 WHERE Id=?"
		tx.ExecuteSqlWithArgs(sql, newJobState, util.GetCurrentTime(), job.TotalRunMillis+curRuntime, job.Id)
	}

	// 这里不能用 UpdateObject, 因为 StopTime 可能为 '0000-00-00 00:00:00' 导致up失败
	// tx.UpdateObject(job, job.Id, "Job")
	return nil
}

func updateJobInstanceState(tx *dao.Transaction, jobId int64, newJobState int8) error {
	jobInstanceList, err := service.ListJobInstancesNotInStatus(jobId, constants.JOB_INSTANCE_STATUS_HISTORY)
	if err != nil {
		return err
	} else if len(jobInstanceList) != 1 {
		return errors.New(fmt.Sprintf("Failed to get JobRuntimeId when updateJobInstanceState, "+
			"jobInstanceList len %d not 1", len(jobInstanceList)))
	}
	jobRuntimeId := jobInstanceList[0].Id

	switch newJobState {
	case constants.JOB_STATUS_RUNNING:
		sql := "UPDATE JobInstance SET Status=?, StartTime=?, UpdateTime=? WHERE Id=?" // 注意设置的是 RunningCuNum
		tx.ExecuteSqlWithArgs(sql, constants.JOB_INSTANCE_STATUS_RUNNING, util.GetCurrentTime(),
			util.GetCurrentTime(), jobRuntimeId)
	case constants.JOB_STATUS_STOPPED, constants.JOB_STATUS_PAUSED:
		sql := "UPDATE JobInstance SET Status=?, StopTime=? WHERE Id=? "
		tx.ExecuteSqlWithArgs(sql, constants.JOB_INSTANCE_STATUS_HISTORY, util.GetCurrentTime(), jobRuntimeId)
	}
	return nil
}

var jobStateInfo = map[int8]string{
	constants.JOB_STATUS_CREATE:      "CREATE",
	constants.JOB_STATUS_INITIALIZED: "INITIALIZED",
	constants.JOB_STATUS_PROGRESS:    "PROGRESS",
	constants.JOB_STATUS_RUNNING:     "RUNNING",
	constants.JOB_STATUS_STOPPED:     "STOPPED",
	constants.JOB_STATUS_FINISHED:    "FINISHED",
	constants.JOB_STATUS_PAUSED:      "PAUSED",
	constants.JOB_STATUS_CONCERNING:  "CONCERNING",
	constants.JOB_STATUS_DELETE:      "DELETE",
}

// FIXME 是否可以放到其他模块?
func getJobStateInfo(jobState int8) string {
	info, ok := jobStateInfo[jobState]
	if !ok {
		info = "UNKONW"
	}
	return fmt.Sprintf("%d (%s)", jobState, info)
}

var jobInstanceStateInfo = map[int8]string{
	constants.JOB_INSTANCE_STATUS_CREATE:    "CREATE",
	constants.JOB_INSTANCE_STATUS_DEPLOYING: "DEPLOYING",
	constants.JOB_INSTANCE_STATUS_HISTORY:   "HISTORY",
	constants.JOB_INSTANCE_STATUS_RUNNING:   "RUNNING",
}

func getJobInstanceStateInfo(state int8) string {
	info, ok := jobInstanceStateInfo[state]
	if !ok {
		info = "UNKONW"
	}
	return fmt.Sprintf("%d (%s)", state, info)
}
