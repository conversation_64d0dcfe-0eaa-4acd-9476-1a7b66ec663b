package service

import (
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service2 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/watchdog"
	service5 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	cosService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cos/handler"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/quota"
)

func UpdateSavepoint(req *watchdog.UpdateSavepointReq) (status int64, msg string, resp *watchdog.UpdateSavepointRsp, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Failed to UpdataSavepoint, errors: %+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()

	rsp := &watchdog.UpdateSavepointRsp{IsSucc: controller.NULL}
	savepoint := &watchdog.SavepointEntity{}
	count := 1
	logger.Debug("Update Savepoint from WatchDog！req.JobId: ", req.JobId, ", req.Status: ", req.Status, ", req.RecordType: ", req.RecordType)
	// 默认保留一份 checkpoint
	checkpointNumRetained := constants.CHECKPOINTS_NUM_RETAINED_VALUE
	savepointNumRetained := constants.SAVEPOINTS_NUM_RETAINED_VALUE

	// 根据任务 jobId 查找对应的高级参数配置
	job, _ := service2.GetJobById(req.JobId)
	jobConfig, _ := service2.GetJobConfigById(job.PublishedJobConfigId)
	jsonObj := make([]*model3.Property, 0, 0)
	json.Unmarshal([]byte(jobConfig.Properties), &jsonObj)

	// 根据任务 jobId 查找对应的高级参数配置的 checkpoint 保存数量,否则取默认值
	for i := range jsonObj {
		if jsonObj[i].Key == constants.CHECKPOINTS_NUM_RETAINED_KEY {
			checkpointNum, _ := strconv.ParseInt(string(jsonObj[i].Value), 10, 64)
			if checkpointNumRetained <= 0 {
				return controller.SYSERR, controller.NULL, rsp, nil
			}
			checkpointNumRetained = (int)(checkpointNum)
		} else if jsonObj[i].Key == constants.SAVEPOINTS_NUM_RETAINED_KEY {
			savepointNum, _ := strconv.ParseInt(string(jsonObj[i].Value), 10, 64)
			if savepointNumRetained <= 0 {
				return controller.SYSERR, controller.NULL, rsp, nil
			}
			savepointNumRetained = (int)(savepointNum)
		}
	}

	maxSavepointNumForAppId, err := quota.NewQuota().GetQuota("", int64(job.AppId), quota.Savepoint)
	if err == nil && maxSavepointNumForAppId > 0 {
		savepointNumRetained = int(maxSavepointNumForAppId)
	}

	if jobConfig.CheckpointRetainedNum != 0 {
		checkpointNumRetained = int(jobConfig.CheckpointRetainedNum)
	}

	if req.RecordType == constants.RECORD_TYPE_CHECKPOINT {
		// checkpoint 全部当做新数据保存,后续检查数量是否超过限制进行删除
		count = 0
		savepoint = getNewCheckpoint(req, savepoint)

	} else {
		// 兼容外部yarn作业通过flink webui 触发sp 没有操作中的savepoint
		// 根据 type 来判断更新 savepoint 还是 cancel-with-savepoint
		savepoint, err = updateInProgressSavepoint(req, req.RecordType)
		if job.FlinkJobType != constants.FLINK_JOB_TYPE_YARN {
			if err != nil {
				logger.Error("Failed to update Savepoint from WatchDog! Error occurred!"+
					"req.JobId: ", req.JobId, ", req.Status: ", req.Status, ", req.RecordType: ", req.RecordType, err)
				return controller.SYSERR, err.Error(), rsp, nil
			}
			if savepoint == nil {
				logger.Error("Failed to update Savepoint from WatchDog！No in-progress savepoint found! "+
					"req.JobId: ", req.JobId, ", req.Status: ", req.Status, ", req.RecordType: ", req.RecordType)
				return controller.SYSERR, "Failed to update Savepoint from WatchDog！No in-progress savepoint found! ", rsp, nil
			}
			logger.Debug("Update in-progress Savepoint！Savepoint record: savepoint.JobId: ", savepoint.JobId,
				"savepoint.Id: ", savepoint.Id, ", savepoint.RecordType: ", savepoint.RecordType)
			savepoint = getUpdateSavepoint(req, savepoint)
		} else {
			if err != nil || savepoint == nil {
				count = 0
				savepoint = getNewSavepoint(req, savepoint)
			} else {
				savepoint = getUpdateSavepoint(req, savepoint)
			}

		}
	}

	toBeCleanSavepoints := make([]*watchdog.SavepointEntity, 0, 0)
	txManager := service2.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		if count == 0 {
			id := tx.SaveObject(savepoint, "Savepoint")
			cidUtil := &util.CidUtil{}
			serialId := cidUtil.EncodeId(id, "svp", "svp", util.GetNowTimestamp(), 8)
			tx.ExecuteSqlWithArgs("UPDATE Savepoint set serialId = ?  WHERE id = ? ", serialId, id)
		} else {
			tx.UpdateObject(savepoint, savepoint.Id, "Savepoint")
		}

		// 检查当前 checkpoint 数量是否超出限制,超出限制则删除更新时间最老的数据
		if req.RecordType == constants.RECORD_TYPE_CHECKPOINT {
			logger.Debug("Clean Checkpoint！Savepoint record: savepoint.JobId: ", req.JobId,
				", savepoint.RecordType: ", req.RecordType, ", checkpointNumRetained: ", checkpointNumRetained)
			deleteOldCheckpoint(req, checkpointNumRetained, tx)
		}
		logger.Debug("Clean Savepoint！Savepoint record: savepoint.JobId: ", savepoint.JobId,
			", savepointNumRetained: ", savepointNumRetained)
		toBeCleanSavepoints = deleteOldSavepoint(req, savepointNumRetained, toBeCleanSavepoints, tx)

		rsp.IsSucc = controller.OK
		return nil
	}).Close()

	deleteOldSavepointCos(req.JobId, toBeCleanSavepoints)
	return controller.SUCCESS, controller.NULL, rsp, nil
}

func deleteOldCheckpoint(req *watchdog.UpdateSavepointReq, numRetained int, tx *dao.Transaction) {
	count, ids, _ := tx.QueryWithArgs("SELECT id FROM Savepoint WHERE JobId = ?  AND RecordType = ? "+
		"AND Status !=3 order by (case when Status=1 then 1 ELSE 4 END), UpdateTime desc ", req.JobId, req.RecordType)
	if count > numRetained {
		// 删除多余的数据,
		sql := "DELETE from Savepoint WHERE id in ( "
		args := make([]interface{}, 0, 0)
		palceHolder := make([]string, 0, 0)
		for i := count; i > numRetained; i-- {
			id, _ := strconv.ParseInt(string(ids[i-1]["id"]), 10, 64)
			args = append(args, id)
			palceHolder = append(palceHolder, "?")
		}
		sql = sql + strings.Join(palceHolder, ",") + ")"
		argsJosn, _ := json.Marshal(args)
		logger.Debug("delete checkpoint from jobId: ", req.JobId, ", count: ", count, ", "+
			"checkpointNumRetained: ", numRetained, ", sql: ",
			sql, ", args: "+string(argsJosn))
		tx.ExecuteSql(sql, args)
	}
}

func deleteOldSavepoint(req *watchdog.UpdateSavepointReq, numRetained int, toBeCleanSavepoints []*watchdog.SavepointEntity, tx *dao.Transaction) []*watchdog.SavepointEntity {
	count, data, _ := tx.QueryWithArgs("SELECT * FROM Savepoint WHERE JobId = ?  AND (RecordType = ? OR RecordType = ?) "+
		"AND Status !=3 order by (case when Status=1 then 1  ELSE 4 END), UpdateTime desc ", req.JobId, constants.RECORD_TYPE_SAVEPOINT, constants.RECORD_TYPE_CANCEL_WITH_SAVEPOINT)

	if count > numRetained {
		// 删除多余的数据,
		sql := "DELETE from Savepoint WHERE id in ( "
		args := make([]interface{}, 0, 0)
		palceHolder := make([]string, 0, 0)
		for i := count; i > numRetained; i-- {
			temp := &watchdog.SavepointEntity{}
			_ = util.ScanMapIntoStruct(temp, data[i-1])
			id := temp.Id

			args = append(args, id)
			palceHolder = append(palceHolder, "?")
			if len(temp.Path) <= 0 {
				continue
			}
			toBeCleanSavepoints = append(toBeCleanSavepoints, temp)
		}
		sql = sql + strings.Join(palceHolder, ",") + ")"
		argsJosn, _ := json.Marshal(args)
		logger.Debug("delete savepoint from jobId: ", req.JobId, ", count: ", count, ", "+
			"savepointNumRetained: ", numRetained, ", sql: ",
			sql, ", args: "+string(argsJosn))
		tx.ExecuteSql(sql, args)
	}
	return toBeCleanSavepoints
}

func deleteOldSavepointCos(jobId int64, toBeCleanSavepoints []*watchdog.SavepointEntity) {
	if len(toBeCleanSavepoints) == 0 {
		return
	}

	cond := dao.NewCondition()
	cond.Eq("j.Id", jobId)
	where, args := cond.GetWhere()
	sqlJobCluster := "select j.OwnerUin, j.CreatorUin, j.Region, j.ClusterId, c.DefaultCOSBucket, c.StateCOSBucket from Job j join Cluster c on j.ClusterId = c.Id " + where
	jobClusterEntity := &watchdog.JobClusterEntity{}
	service2.GetTxManager().GetQueryTemplate().GetObjectBySql(sqlJobCluster, jobClusterEntity, args...)

	tmpSecretId, tmpSecretKey, token, pass, err := service5.StsAssumeRole(jobClusterEntity.OwnerUin, jobClusterEntity.CreatorUin, jobClusterEntity.Region)
	if err != nil {
		logger.Errorf("DeleteCOSFile StsAssumeRoleWithSubAccountUin with error %v", err)
		return
	}
	if !pass {
		logger.Errorf("DeleteCOSFile StsAssumeRoleWithSubAccountUin not pass with param uin %s, subUin %s, region %s ", jobClusterEntity.OwnerUin, jobClusterEntity.OwnerUin, jobClusterEntity.Region)
		return
	}
	for _, savepoint := range toBeCleanSavepoints {

		params, err := service5.NewSavepointParam(savepoint.Path, jobClusterEntity.Region)
		if err != nil {
			logger.Errorf("DeleteCOSFile NewSavepointParam with error %v", err)
			continue
		}

		stateCOSBucket := jobClusterEntity.DefaultCOSBucket
		if len(jobClusterEntity.StateCOSBucket) >0 {
			stateCOSBucket = jobClusterEntity.StateCOSBucket
		}
		logger.Debug("delete cos: ", "stateCOSBucket:"+stateCOSBucket+" jobClusterEntity.Region:"+jobClusterEntity.Region+" params.Path:"+params.Path)
		request := cosService.BuildDelCosObjectReq(stateCOSBucket, jobClusterEntity.Region, params.Path, tmpSecretId, tmpSecretKey, token)
		cosService.GetHandler().AppendTask(request)
	}

}

func updateInProgressSavepoint(req *watchdog.UpdateSavepointReq, recordType int8) (entity *watchdog.SavepointEntity, err error) {
	count, savepoints, err := service2.GetInProgressSavepointByJobIdAndRecordType(req.JobId, req.RecordType)
	if err != nil {
		return nil, err
	}
	// 首先获取 savepoint, 获取不到再尝试获取 cancel-with-savepoint，用以兼容新集群旧任务的情况.
	if count == 0 {
		if recordType == constants.RECORD_TYPE_SAVEPOINT {
			logger.Warning("No in-progress Savepoint found for Job : ", req.JobId, ", req.RecordType:",
				req.RecordType, "req.Status: ", req.Status, "try to find cancel-with-savepoint record")
			count2, cancelWithSavepointList, err := service2.GetInProgressSavepointByJobIdAndRecordType(req.JobId, constants.RECORD_TYPE_CANCEL_WITH_SAVEPOINT)
			if count2 == 0 {
				logger.Warning("No in-progress cancel-with-savepoint found for Job : ", req.JobId, ", req.RecordType:",
					req.RecordType, "req.Status: ", req.Status, "try to find cancel-with-savepoint record")
				err = errors.New(fmt.Sprintf("No in-progress Cancel-with-Savepoint found!"))
				return nil, err
			} else {
				return cancelWithSavepointList[0], nil
			}
		} else {
			logger.Warning("No in-progress cancel-with-savepoint found for Job : ", req.JobId, ", req.RecordType:",
				req.RecordType, "req.Status: ", req.Status)
			err = errors.New(fmt.Sprintf("No in-progress cancel-with-savepoint found!"))
			return nil, err
		}
	} else {
		return savepoints[0], nil
	}
}

func getUpdateSavepoint(req *watchdog.UpdateSavepointReq, savepointEntity *watchdog.SavepointEntity) *watchdog.SavepointEntity {
	newSavepointEntity := &watchdog.SavepointEntity{
		Id:           savepointEntity.Id,
		JobId:        req.JobId,
		SerialId:     savepointEntity.SerialId,
		JobRuntimeId: req.JobRuntimeId,
		Status:       req.Status,
		RecordType:   savepointEntity.RecordType,
		Size:         req.Size,
		Timeout:      savepointEntity.Timeout,
		Path:         req.Path,
		CreateTime:   util.NanoTimestampToLocalTime(req.TriggerTime),
		UpdateTime:   util.NanoTimestampToLocalTime(req.LastAckTime),
		Description:  savepointEntity.Description,
		ClusterId:    req.ClusterId,
		ItemSpaceId:  savepointEntity.ItemSpaceId,
	}
	return newSavepointEntity
}

func getNewCheckpoint(req *watchdog.UpdateSavepointReq, savepointEntity *watchdog.SavepointEntity) *watchdog.SavepointEntity {
	newSavepointEntity := &watchdog.SavepointEntity{
		JobId:        req.JobId,
		JobRuntimeId: req.JobRuntimeId,
		Status:       req.Status,
		RecordType:   req.RecordType,
		Size:         req.Size,
		Timeout:      savepointEntity.Timeout,
		Path:         req.Path,
		CreateTime:   util.NanoTimestampToLocalTime(req.TriggerTime),
		UpdateTime:   util.NanoTimestampToLocalTime(req.LastAckTime),
		Description:  "作业运行时自动生成的快照",
		ClusterId:    req.ClusterId,
	}
	return newSavepointEntity
}

func getNewSavepoint(req *watchdog.UpdateSavepointReq, savepointEntity *watchdog.SavepointEntity) *watchdog.SavepointEntity {
	newSavepointEntity := &watchdog.SavepointEntity{
		JobId:        req.JobId,
		JobRuntimeId: req.JobRuntimeId,
		Status:       req.Status,
		RecordType:   req.RecordType,
		Size:         req.Size,
		Path:         req.Path,
		CreateTime:   util.NanoTimestampToLocalTime(req.TriggerTime),
		UpdateTime:   util.NanoTimestampToLocalTime(req.LastAckTime),
		Description:  "作业触发生成的快照",
		ClusterId:    req.ClusterId,
	}
	if savepointEntity != nil {
		newSavepointEntity.Timeout = savepointEntity.Timeout
	}
	return newSavepointEntity
}
