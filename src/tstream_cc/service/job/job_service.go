package service

import (
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"math/rand"
	"strconv"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/common/uuid"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	service8 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model"
	model3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model"
	model2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cluster_master_protocol"
	table4 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster_master"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job"
	table5 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job_config"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job_instance"
	table1 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/resource_config"
	common "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service6 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config/configure_center"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config/image_registry"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/configuration_center"
	service7 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job_config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
	service4 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
	service5 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/white_list"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
	"time"
)

/**
 *
 * <AUTHOR>
 * @time 2019/4/27
 * @version 0.4.0
 * @since 0.4.0
 * @see
 */
func CreateJob(job *table.Job) (serialId string, id int64, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Add Job to db panic ,for job:%+v, errors:%+v", job, errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()

	txManager := service.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		id = tx.SaveObject(job, "Job")
		cidUtil := &util.CidUtil{}
		serialId = cidUtil.EncodeId(id, "cql", "cql", util.GetNowTimestamp(), 8)
		tx.ExecuteSqlWithArgs("UPDATE Job set serialId = ?  WHERE id = ? ", serialId, id)

		return nil
	}).Close()

	return serialId, id, nil
}

func ModifyJobDefaultAlarm(jobSerialId string, openJobDefaultAlarm int8) (err error) {
	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		args := make([]interface{}, 0)
		args = append(args, openJobDefaultAlarm)
		args = append(args, jobSerialId)
		tx.ExecuteSql("UPDATE Job SET OpenJobDefaultAlarm=? WHERE SerialId=?", args)
		return err
	}).Close()
	return err
}

func GetJobsTotalCount(appId int32, region string, serialIds []string, names []string, status []int64, clusterGroupIds []string, clusterGroupNames []string, itemSpaceIds []int64, jobTypes []int64) (int, error) {
	sql := "SELECT j.* FROM Job j INNER JOIN ClusterGroup cg ON j.clusterGroupId=cg.Id"

	cond := dao.NewCondition()
	cond.Ne("j.Status", constants.JOB_STATUS_DELETE)

	if appId > 0 {
		cond.Eq("j.AppId", appId)
	}
	if region != "" {
		cond.Eq("j.Region", region)
	}
	if len(serialIds) > 0 {
		cond.In("j.SerialId", serialIds)
	}
	if len(names) > 0 {
		likeCond := dao.NewCondition().EnableOr()
		for i := 0; i < len(names); i++ {
			likeCond.Like("j.Name", "%"+names[i]+"%")
		}
		cond.Condition(likeCond)
	}
	if len(status) > 0 {
		cond.In("j.Status", status)
	}
	if len(jobTypes) > 0 {
		cond.In("j.Type", jobTypes)
	}
	if len(clusterGroupIds) > 0 {
		exclusiveClusterGroupIds, hasShareCluster := common.ClassifyClusterGroupIds(clusterGroupIds)
		if hasShareCluster && len(exclusiveClusterGroupIds) > 0 { // 若同时过滤独享集群及共享集群作业
			cond.Condition(dao.NewCondition().
				EnableOr().
				Eq("cg.Type", constants.CLUSTER_GROUP_TYPE_SHARED).
				In("cg.SerialId", exclusiveClusterGroupIds))
		} else if hasShareCluster { // 若仅仅过滤共享集群作业
			cond.Eq("cg.Type", constants.CLUSTER_GROUP_TYPE_SHARED)
		} else if !hasShareCluster { // 若仅仅过滤独享集群作业
			cond.In("cg.SerialId", exclusiveClusterGroupIds)
		}
	}

	if len(itemSpaceIds) > 1 || (len(itemSpaceIds) == 1 && itemSpaceIds[0] != 0) {
		itemSpaceIds := UniqueSliceInt64(itemSpaceIds)
		cond.In("j.ItemSpaceId", itemSpaceIds)
	}

	if len(clusterGroupNames) > 0 {
		likeCond := dao.NewCondition().EnableOr()
		for i := 0; i < len(clusterGroupNames); i++ {
			likeCond.Like("cg.Name", "%"+clusterGroupNames[i]+"%")
		}
		cond.Condition(likeCond)
	}

	where, args := cond.GetWhere()
	sql += where

	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Exception occurs when Get job count from db, with errors:%+v", err)
		return 0, err
	}

	return len(data), nil
}

func BuildJobInstanceEntity(newLatestJobConfigId int64, job *table.Job, newJobConfig *table5.JobConfig) (*table2.JobInstance, error) {
	jobInstance := &table2.JobInstance{}
	jobInstance.JobId = job.Id
	jobInstance.JobConfigId = newLatestJobConfigId

	if newJobConfig == nil {
		jobConfigs, err := service7.ListJobConfigs(job.Id, []int64{newLatestJobConfigId}, []int64{}, nil)
		if err != nil {
			logger.Errorf("Failed to get job config, with errors:%+v", err)
			return nil, err
		}
		if len(jobConfigs) == 0 {
			logger.Errorf("Failed to get job config, with result is empty.")
			return nil, errors.New("job config query result is empty")
		}
		newJobConfig = jobConfigs[0]
	}

	jobInstance.DefaultParallelism = newJobConfig.DefaultParallelism
	jobInstance.CuMem = job.CuMem
	jobInstance.Status = constants.JOB_INSTANCE_STATUS_CREATE
	jobInstance.CreateTime = util.GetCurrentTime()
	jobInstance.RunningOrderId = job.JobRunningOrderId + 1
	jobInstance.UpdateTime = util.GetCurrentTime()

	// JobInstance 设置 真正运行的Zone
	clusterGroup, err := service6.GetClusterGroupByClusterId(job.ClusterId)
	if err != nil {
		logger.Errorf("Failed to GetClusterGroupByClusterId(%d) because %+v", job.ClusterId, err)
		return nil, err
	}
	cluster, err := service6.GetClusterByJobId(job.SerialId)
	if err != nil {
		logger.Errorf("Failed to GetClusterByJobId(%d) because %+v", job.SerialId, err)
		return nil, err
	}
	jobInstance.Zone = GetJobInstanceZone(clusterGroup, newJobConfig, cluster)
	logger.Infof("recover job GetJobInstanceZone %s", jobInstance.Zone)

	return jobInstance, nil
}

/*
*
根据JobEntity初始化Command
*/
func InitializeCommandForClusterMaster(job *table.Job, nextJobStatus int8) *cluster_master.Command {
	command := &cluster_master.Command{}

	command.ClusterGroupId = job.ClusterGroupId
	command.ClusterId = job.ClusterId
	command.AppId = job.AppId
	command.OwnerUin = job.OwnerUin
	command.CreatorUin = job.CreatorUin
	command.Region = job.Region
	command.Zone = job.Zone
	command.JobId = job.Id
	command.JobName = job.Name
	command.JobSerialId = job.SerialId
	command.JobConfigId = job.PublishedJobConfigId
	command.JobType = job.Type
	command.JobRegressStatus = job.Status
	command.JobNextStatus = nextJobStatus
	command.Status = constants.CLUSTER_MASTER_COMMAND_CREATE
	command.CreateTime = util.GetCurrentTime()

	return command
}

func setLocalizationResources(runJobParams *cluster_master_protocol.RunJobParams,
	entryPointClass, programArgs string, job *table.Job, jobRuntimeId int64,
	resourceRefs []*model2.ResourceRefItem, resourceConfigItem *model2.ResourceConfigItem) (err error) {
	runJobParams.EntrypointClass = entryPointClass
	workspace, err := service3.GetScsWorkspace()
	if err != nil {
		logger.Errorf("Failed to GetScsWorkspace errors:%+v", err)
		return err
	}
	localizationDir := fmt.Sprintf("%s/%d/%s/runtime/%s/%d", workspace, job.AppId, job.CreatorUin, job.SerialId, jobRuntimeId)
	runJobParams.ShipFiles = fmt.Sprintf("%s/shipFiles", localizationDir)
	runJobParams.ProgramArgs = programArgs

	// 设置资源本地化的context
	logger.Infof("#setLocalizationResources, resourceRefs: %+v", resourceRefs)
	contexts, dependencies, err := service4.BuildLocalizationContextsV2(job, resourceRefs, resourceConfigItem)
	if err != nil {
		logger.Errorf("Failed to BuildLocalizationContexts resourceRefs: %+v errors:%+v", resourceRefs, err)
		return err
	}

	for _, c := range contexts {
		if c.UsageType == constants.RESOURCE_REF_USAGE_TYPE_MAIN {
			logger.Debugf("find main jar file:%s", c.Path)
			fileItem := strings.Split(c.Path, "/")
			jarName := fileItem[len(fileItem)-1]
			runJobParams.JarFilePath = fmt.Sprintf("%s/%s", localizationDir, jarName)
		}
		c.TargetBucket = runJobParams.COSBucket
		c.TargetRegion = job.Region
	}
	contextsBytes, err := json.Marshal(contexts)
	if err != nil {
		logger.Errorf("Failed to json marshal struct: %+v, errors:%+v", contexts, err)
		return err
	}
	dependenciesBytes, err := json.Marshal(dependencies)
	if err != nil {
		logger.Errorf("Failed to json marshal struct: %+v, errors:%+v", dependencies, err)
	}

	runJobParams.LocalizationResources = string(contextsBytes)
	runJobParams.UserDependencyFiles = string(dependenciesBytes)
	return
}

func BuildRunJobParams(oldJobRuntimeId int64, jobInstance *table2.JobInstance, job *table.Job, jobConfig *table5.JobConfig,
	startMode string, isRestart bool, isResume bool, isRestartWithSavepoint bool,
	externalizedCheckpointPath string, resourceRefs []*model2.ResourceRefItem, requestId string, metadata string, jobRunningOrderId int64,
	savepointSerialId string, savepointPath string, isScale bool) (string, error) {

	jobRuntimeId := jobInstance.Id

	// 对 JobConfig 的 Properties 做增强: 如果用户未设置 pipeline.max-parallelism: 2048 则默认填充
	var enhanceJobConfigErr error
	jobConfig.Properties, enhanceJobConfigErr = enhanceJobConfigProperties(jobConfig.Properties)
	if enhanceJobConfigErr != nil {
		logger.Errorf("enhanceJobConfigProperties return error %+v", enhanceJobConfigErr)
		return controller.NULL, enhanceJobConfigErr
	}
	jobConfig.Properties, enhanceJobConfigErr = checkJobConfigJobGraph(jobConfig.Properties, job, jobConfig, isScale)
	if enhanceJobConfigErr != nil {
		logger.Errorf("checkJobConfigJobGraph enhanceJobConfigProperties return error %+v", enhanceJobConfigErr)
		return controller.NULL, enhanceJobConfigErr
	}

	runJobParams := &cluster_master_protocol.RunJobParams{}
	runJobParams.RequestId = requestId
	runJobParams.OldJobRuntimeId = oldJobRuntimeId
	runJobParams.JobRuntimeId = jobRuntimeId
	runJobParams.JobRuntimeZone = jobInstance.Zone
	runJobParams.JobRunningOrderId = job.JobRunningOrderId + 1
	runJobParams.ExternalizedCheckpointPath = externalizedCheckpointPath // Watchdog 专用
	runJobParams.FlinkVersion = common.GetFlinkVersion(job, jobConfig)
	//使用Savepoint启动作业时获取savepointPath

	if savepointPath != "" {
		runJobParams.SavepointPath = savepointPath
	} else {
		if savepointSerialId != "" {
			savepointEntity, err := service8.GetSavepointPathBySerialId(job.Id, savepointSerialId)
			if err != nil {
				logger.Errorf("Failed to GetSavepointPathBySerialId for %s", savepointSerialId)
				return controller.NULL, err
			} else {
				runJobParams.SavepointPath = savepointEntity.Path
			}
		} else {
			logger.Debugf("Begin to run job %s without selected savepoint", job.SerialId)
		}
	}
	logger.Debugf("Begin to run job %s with selected savepoint %s", job.SerialId, runJobParams.SavepointPath)

	cosBucket, err := service7.GetRealCOSBucketFromJobConfig(jobConfig)
	if err != nil {
		logger.Errorf("Failed to GetRealCOSBucketFromJobConfig for %s (JobConfig %d) because %+v", job.SerialId, jobConfig.Id, err)
		return controller.NULL, err
	}
	logger.Debugf("Storage COS bucket for Job %s (JobConfig %d) is %s", job.SerialId, jobConfig.Id, cosBucket)
	runJobParams.COSBucket = cosBucket

	stateCOSBucket, err := service7.GetStateCOSBucketFromJobConfig(jobConfig)
	if err != nil {
		logger.Errorf("Failed to GetStateCOSBucketFromJobConfig for %s (JobConfig %d) because %+v", job.SerialId, jobConfig.Id, err)
		return controller.NULL, err
	}
	logger.Debugf("Storage stateCOSBucket for Job %s (JobConfig %d) is %s", job.SerialId, jobConfig.Id, stateCOSBucket)
	runJobParams.StateCOSBucket = stateCOSBucket

	savepointDirectory, err := service4.GetSavepointDirectory(job, stateCOSBucket, jobRunningOrderId)
	if err != nil {
		logger.Warningf("Failed to get savepointDirectory for job because %+v", err)
		return controller.NULL, err
	}
	runJobParams.SavepointDirectory = savepointDirectory

	if job.Type == constants.JOB_TYPE_SQL || job.Type == constants.JOB_TYPE_ETL {
		resourceConfigItem := &model2.ResourceConfigItem{}
		if job.Type == constants.JOB_TYPE_SQL {
			err, resourceConfigItem = service4.TranslateSqlToCos(job, jobConfig.SqlCode, constants.SQL_SUFFIX)
			if err != nil {
				logger.Warningf("Failed to translateSqlToCos for job because %+v", err)
				return controller.NULL, err
			}
			logger.Infof("translateSqlToCos resourceItem: %+v", resourceConfigItem)
			if resourceConfigItem != nil {
				sqlCheckRef := &table1.SqlCheckRef{
					SqlCheckId: uuid.NewRandom().String(),
					ResourceId: resourceConfigItem.Id,
					CreateTime: util.GetCurrentTime(),
					UpdateTime: util.GetCurrentTime(),
					Type:       constants.RESOURCE_TYPE_DEPENDENCY,
					Version:    constants.META_TABLE_VERSION_DEFAULT,
					Status:     constants.REGION_STATUS_ACTIVE,
				}
				err = service4.SaveSqlCheckRef(sqlCheckRef)
				if err != nil {
					return controller.NULL, err
				}
			}
		}
		if len(resourceRefs) > 0 || (resourceConfigItem != nil && resourceConfigItem.ResourceLoc != nil) {
			setLocalizationResources(runJobParams, jobConfig.EntrypointClass, jobConfig.ProgramArgs, job, jobRuntimeId, resourceRefs, resourceConfigItem)
		} else {
			runJobParams.LocalizationResources = "[]"
		}
		ec, jfp, _, err := GetSQLEntrypointEtc(job.Type)
		if err != nil {
			return controller.NULL, err
		}
		cluster, err := service6.GetClusterByClusterId(job.ClusterId)
		if err != nil {
			logger.Errorf("[%s] BuildRunJobParams -> GetClusterByClusterId(%s) error:%+v",
				requestId, job.ClusterId, err)
			return controller.NULL, err
		}
		clusterGroup, err := service6.GetClusterGroupByClusterId(job.ClusterId)
		if err != nil {
			logger.Errorf("Failed to GetClusterGroupByClusterId(%s) because %+v", requestId, err)
			return controller.NULL, err
		}
		jobClusterConfig := make(map[string]string, 0)
		if err := updateClusterDimensionProperties(job, cluster, jobClusterConfig, jobConfig, job.AppId, runJobParams.JobRuntimeZone, clusterGroup); err != nil {
			logger.Errorf("[%s] ignore ClusterConfig for cluster %d(%s) %v", requestId, cluster.Id, cluster.UniqClusterId, err)
		}
		jobImageVersion, assignedImage := jobClusterConfig["kubernetes.container.image"]
		if cluster.SchedulerType == constants.CLUSTER_SCHEDULER_TYPE_EMR || (assignedImage && !isNewFlinkVersion(jobImageVersion)) {
			ec = "com.tencent.cloud.tstream.flink.sql.StreamingSqlJob"
			logger.Debugf("[%s] BuildRunJobParams -> to lagency SQL Entrypoint (%s)",
				requestId, job.ClusterId)
		}

		runJobParams.EntrypointClass = ec
		runJobParams.JarFilePath = jfp

		programArgs, err := BuildSQLJobProgramArgs(job, jobConfig, jobConfig.CheckpointInterval, startMode,
			jobConfig.SqlCode, jobConfig.MaxParallelism, requestId, metadata, resourceConfigItem)

		if err != nil {
			return controller.NULL, err
		}
		runJobParams.ProgramArgs = programArgs

		err = GenerateDynamicProperties(runJobParams, job, jobConfig, runJobParams.StateCOSBucket, jobRunningOrderId)
		if err != nil {
			logger.Warningf("%s: Failed to setDynamicProperties because %+v", requestId, err)
		}
	} else {
		innerUserTransitions, err := service5.GetInnerUserTransitions(job.AppId, job.OwnerUin, job.CreatorUin)
		if err != nil {
			logger.Errorf("Failed to list InnerUserTransitions, error:%+v", err)
			return controller.NULL, err
		}
		// 兼容智能监控和用户提交jar的逻辑
		// 1.当智能监控的作业存在资源引用时，使用引用资源
		// 2.不存在资源引用时，使用本体资源
		if len(innerUserTransitions) > 0 {
			if len(resourceRefs) > 0 {
				logger.Infof("set inner user cos Localization resources appId: %d", job.AppId)
				setLocalizationResources(runJobParams, jobConfig.EntrypointClass, jobConfig.ProgramArgs, job, jobRuntimeId, resourceRefs, nil)
				// ！注意，这里为什么不去设置setDynamicProperties
				// 因为智能监控的cos集群的配置是写到配置里面的，和vpc环境下的独占集群稍微有区别
				// GenerateDynamicProperties(runJobParams, job, jobConfig.Id)
			} else {
				logger.Debugf("set inner user local Localization resources appId: %d", job.AppId)
				innerUserTransition := innerUserTransitions[0]
				if jobConfig.EntrypointClass != "" {
					runJobParams.EntrypointClass = jobConfig.EntrypointClass
				} else {
					logger.Warning("entryPointClass is null, set default entrypointClass %s", innerUserTransition.EntrypointClass)
					runJobParams.EntrypointClass = innerUserTransition.EntrypointClass
				}
				runJobParams.EntrypointClass = innerUserTransition.EntrypointClass
				runJobParams.JarFilePath = innerUserTransition.JarFilePath
				runJobParams.ShipFiles = innerUserTransition.ShipFiles
				runJobParams.ProgramArgs = jobConfig.ProgramArgs
				runJobParams.LocalizationResources = "[]"
			}
		} else {
			logger.Infof("set user cos Localization resources and dynamic properties appId: %d", job.AppId)
			setLocalizationResources(runJobParams, jobConfig.EntrypointClass, jobConfig.ProgramArgs, job, jobRuntimeId,
				resourceRefs, nil)
			GenerateDynamicProperties(runJobParams, job, jobConfig, runJobParams.StateCOSBucket, jobRunningOrderId)
		}
	}
	runJobParams.CuMem = job.CuMem
	runJobParams.MaxParallelism = jobConfig.MaxParallelism
	runJobParams.DefaultParallelism = jobConfig.DefaultParallelism

	runJobParams.IsRestart = isRestart
	runJobParams.IsRestartWithSavepoint = isRestartWithSavepoint
	runJobParams.IsResume = isResume

	enableTKE, err := service6.ClusterIsEnableTKE(job.ClusterId)
	if err != nil {
		logger.Errorf("Failed to list cluster schedule type by clusterId %d errors:%+v", job.ClusterId, err)
		return controller.NULL, err
	}

	// 用于CA通过这个字段获取不同的 ClusterClient，代码在  oceanus-common 的 ClusterClientFactory
	if enableTKE {
		runJobParams.SchedulerType = constants.SCHEDULER_TYPE_K8S
	} else {
		runJobParams.SchedulerType = constants.SCHEDULER_TYPE_YARN
	}

	runJobParamsBytes, err := json.Marshal(runJobParams)
	if err != nil {
		logger.Errorf("Failed to json marshal struct: %+v, errors:%+v", runJobParamsBytes, err)
		return controller.NULL, err
	}

	return string(runJobParamsBytes), nil
}

func updateFlinkCommonProperties(
	clusterGroup *table4.ClusterGroup,
	jobConfig *table5.JobConfig,
	runJobParams *cluster_master_protocol.RunJobParams,
	properties map[string]string,
	jobRunningOrderId int64,
) (err error) {
	requestId := runJobParams.RequestId

	commonConfMap, err := service3.GetConfigurationByType(constants.CONFIGURATION_TYPE_FLINK_COMMON)
	if err != nil {
		logger.Errorf("[%s] GetDynamicProperties -> GetConfigurationByType error:%+v", requestId, err)
		return
	}
	// 增加其他元数据信息到 DynamicProperties (Prometheus 等 Reporter 需要)
	properties["cluster.group.serial-id"] = clusterGroup.SerialId
	properties["job.version-id"] = strconv.Itoa(int(jobConfig.VersionId))
	properties["job.running-order-id"] = strconv.FormatInt(jobRunningOrderId, 10)
	properties["job.runtime-id"] = service.Itoa(runJobParams.JobRuntimeId)
	// FLINK UI默认打开火焰图
	properties[constants.REST_FLAMEGRAPH_ENABLED] = "true"
	properties["web.cancel.enable"] = "false"
	updateProperties(properties, commonConfMap)
	return
}

func updateFlinkOnTkeProperties(
	clusterGroup *table4.ClusterGroup,
	cluster *table4.Cluster,
	runJobParams *cluster_master_protocol.RunJobParams,
	job *table.Job,
	jobConfig *table5.JobConfig,
	properties map[string]string,
	stateCosBucket string,
) (err error) {
	if cluster.SchedulerType != constants.CLUSTER_SCHEDULER_TYPE_TKE {
		return
	}

	requestId := runJobParams.RequestId
	jobConfigId := jobConfig.Id

	properties[constants.FLINK_K8S_CLUSTER_ID] = fmt.Sprintf("%s-%d", job.SerialId, runJobParams.JobRuntimeId)

	// todo 设置后续需要的作业labels
	labels := make([]string, 0)
	labels = append(labels, fmt.Sprintf("%s:%d", "appId", job.AppId))
	labels = append(labels, fmt.Sprintf("%s:%s", "ownerUin", job.OwnerUin))
	labels = append(labels, fmt.Sprintf("%s:%s", "creatorUin", job.CreatorUin))
	labels = append(labels, fmt.Sprintf("%s:%d", "jConfId", jobConfigId))
	labels = append(labels, fmt.Sprintf("%s:%s", "requestId", runJobParams.RequestId))
	labels = append(labels, fmt.Sprintf("%s:%s", "jobSerialId", job.SerialId))
	labels = append(labels, fmt.Sprintf("%s:%d", "jobRunningOrderId", runJobParams.JobRunningOrderId))
	bucket := cluster.DefaultCOSBucket
	if cluster.LogCOSBucket != "" {
       bucket = cluster.LogCOSBucket
	}
	labels = append(labels, fmt.Sprintf("%s:%s", "bucket", bucket))
	labels = append(labels, fmt.Sprintf("%s:%s", "region", clusterGroup.Region))
	labels = append(labels, fmt.Sprintf("%s:%s", "clusterSerialId", clusterGroup.SerialId))
	if JobEnableDumpCollect(jobConfig) {
		labels = append(labels, fmt.Sprintf("%s:%d", "dumpCollectEnable", 1))
	}
	if jobConfig.ClsLogsetId != "" && jobConfig.ClsTopicId != "" && jobConfig.LogCollect == constants.JobLogCollectEnabled {
		labels = append(labels, fmt.Sprintf("%s:%d", "logCollectEnable", 1))
		labels = append(labels, fmt.Sprintf("%s:%s", "logCollectType", constants.JobLogCollectOnClsKey))
		labels = append(labels, fmt.Sprintf("logCollect:%s", jobConfig.ClsTopicId))
	} else if jobConfig.COSBucket != "" && jobConfig.LogCollect == constants.JobLogCollectEnabledOnCos {
		labels = append(labels, fmt.Sprintf("%s:%d", "logCollectEnable", 1))
		labels = append(labels, fmt.Sprintf("%s:%s", "logCollectType", constants.JobLogCollectOnCosKey))
		labels = append(labels, fmt.Sprintf("logCollect:%d", constants.JobLogCollectEnabledOnCos))
	} else if jobConfig.EsServerlessIndex != "" && jobConfig.LogCollect == constants.JobLogCollectEnabledOnES {
		labels = append(labels, fmt.Sprintf("%s:%d", "logCollectEnable", 1))
		labels = append(labels, fmt.Sprintf("%s:%s", "logCollectType", constants.JobLogCollectOnEsKey))
		labels = append(labels, fmt.Sprintf("logCollect:%s", jobConfig.EsServerlessIndex))
	} else {
		labels = append(labels, fmt.Sprintf("logCollect:%d", jobConfig.LogCollect))
	}

	properties[constants.FLINK_K8S_JM_LABELS] = strings.Join(labels, ",")
	properties[constants.FLINK_K8S_TM_LABELS] = strings.Join(labels, ",")

	// 查询公共的TKE集群Flink配置参数
	tkeConfMap, err := service3.GetConfigurationByType(constants.CONFIGURATION_TYPE_FLINK_TKE)
	if err != nil {
		logger.Errorf("[%s] GetDynamicProperties -> GetConfigurationByType(%d) error:%+v",
			requestId, constants.CONFIGURATION_TYPE_FLINK_TKE, err)
		return err
	}

	err = replaceFlinkImage(job, jobConfig, tkeConfMap)
	if err != nil {
		return err
	}

	if value, ok := tkeConfMap[constants.FLINK_K8S_REST_SERVICE_ANNOTATIONS]; ok {
		// 生成kubernetes.rest-service.annotations
		// 例子：service.kubernetes.io/qcloud-loadbalancer-internal-subnetid:subnet-xxx
		if cluster.SubnetId == "" {
			errMsg := fmt.Sprintf("cluster %d subnetId is empty", cluster.Id)
			logger.Errorf("[%s] GetDynamicProperties -> GetClusterByClusterId %s", requestId, errMsg)
			return errorcode.InternalErrorCode_SubnetNotFound.ReplaceDesc(errMsg)
		}
		tkeConfMap[constants.FLINK_K8S_REST_SERVICE_ANNOTATIONS] = fmt.Sprintf("%s:%s", value, cluster.SubnetId)
	}

	baseMemory := float32(4096)
	archGeneration, err := service2.GetTableService().GetTkeArchGeneration(cluster.Id)
	if err != nil {
		return err
	}
	if archGeneration >= constants.TKE_ARCH_GENERATION_V2 {
		baseMemory = 3900
		isInWhiteList := auth.IsInWhiteList(int64(clusterGroup.AppId), constants.WHITE_LIST_OPEN_RESOURCE_LIMIT_FACTOR)
		if isInWhiteList {
			limitFactor := 1.05 // 975/1024
			if _, ok := properties["kubernetes.jobmanager.memory.limit-factor"]; !ok {
				properties["kubernetes.jobmanager.memory.limit-factor"] = fmt.Sprintf("%f", limitFactor)
			}
			if _, ok := properties["kubernetes.taskmanager.memory.limit-factor"]; !ok {
				properties["kubernetes.taskmanager.memory.limit-factor"] = fmt.Sprintf("%f", limitFactor)
			}
		}
		if jobConfig.TmCuSpec == 0.25 || (jobConfig.TaskManagerMem > 0 && jobConfig.TaskManagerMem <= 1.5) {
			if _, ok := properties["taskmanager.memory.framework.off-heap.size"]; !ok {
				properties["taskmanager.memory.framework.off-heap.size"] = "120m"
			}
		}

		incCpuLimitForSmallCu, whitelistVal := auth.WhiteListValue(int64(clusterGroup.AppId), constants.WHITE_LIST_INC_CPULIMIT_FOR_SMALLCU)
		if incCpuLimitForSmallCu && whitelistVal.Param != "" {
			var result map[string]map[string]float64
			err := json.Unmarshal([]byte(whitelistVal.Param), &result)
			if err != nil {
				logger.Errorf("parse WHITE_LIST_INC_CPULIMIT_FOR_SMALLCU Param with error %v", err)
			}
			enhanceLimitFactor := func(key, prefix string) {
				if len(result) <= 0 {
					return
				}
				for property, value := range result[key] {
					if _, ok := properties[property]; !ok && strings.HasPrefix(property, prefix) {
						properties[property] = fmt.Sprintf("%f", value)
					}
				}
			}
			if (jobConfig.TmCuSpec > 0 && jobConfig.TmCuSpec < 0.5) || (jobConfig.TaskManagerCpu > 0 && jobConfig.TaskManagerCpu < 0.5) {
				enhanceLimitFactor("0.25cu", "kubernetes.taskmanager")
			} else if (jobConfig.TmCuSpec > 0 && jobConfig.TmCuSpec < 1) || (jobConfig.TaskManagerCpu > 0 && jobConfig.TaskManagerCpu < 1) {
				enhanceLimitFactor("0.5cu", "kubernetes.taskmanager")
			}
			if (jobConfig.JmCuSpec > 0 && jobConfig.JmCuSpec < 0.5) || (jobConfig.JobManagerCpu > 0 && jobConfig.JobManagerCpu < 0.5) {
				enhanceLimitFactor("0.25cu", "kubernetes.jobmanager")
			} else if (jobConfig.JmCuSpec > 0 && jobConfig.JmCuSpec < 1) || (jobConfig.JobManagerCpu > 0 && jobConfig.JobManagerCpu < 1) {
				enhanceLimitFactor("0.5cu", "kubernetes.jobmanager")
			}
		}
	}

	// 1:2 机型设置内存
	/*
		 * taskmanager.memory.jvm-overhead.min: 50m
		 * taskmanager.memory.jvm-metaspace.size: 128m
		* taskmanager.memory.managed.fraction: 0.1
		* taskmanager.memory.framework.off-heap.size: 80m
		* jobmanager.memory.jvm-overhead.min: 50m
		* jobmanager.memory.jvm-metaspace.size: 128m
		* jobmanager.memory.off-heap.size: 80m
	*/
	updateMemRatio2Properties(cluster, jobConfig, properties)

	updateProperties(properties, tkeConfMap)
	replaceLogLevel(properties, jobConfig.LogLevel)

	var baseMemRatio float32 = 1
	if cluster.MemRatio != constants.CVM_DEFAULT_MEMRATIO && cluster.MemRatio != 0 {
		baseMemRatio = float32(cluster.MemRatio) / float32(constants.CVM_DEFAULT_MEMRATIO)
	}
	logger.Debugf("baseMemRatio: %.2f", baseMemRatio)

	jobManagerCpu := jobConfig.JmCuSpec
	jobManagerMem := jobConfig.JmCuSpec * baseMemory * baseMemRatio
	taskManagerCpu := jobConfig.TmCuSpec
	taskManagerMem := jobConfig.TmCuSpec * baseMemory * baseMemRatio
	if jobConfig.JobManagerCpu > 0 {
		jobManagerCpu = jobConfig.JobManagerCpu
		jobManagerMem = jobConfig.JobManagerMem * (baseMemory / 4)
		taskManagerCpu = jobConfig.TaskManagerCpu
		taskManagerMem = jobConfig.TaskManagerMem * (baseMemory / 4)
	}

	properties["kubernetes.jobmanager.cpu"] = fmt.Sprintf("%f", jobManagerCpu)
	properties["jobmanager.memory.process.size"] = fmt.Sprintf("%dm", int(jobManagerMem))
	properties["kubernetes.taskmanager.cpu"] = fmt.Sprintf("%f", taskManagerCpu)
	properties["taskmanager.memory.process.size"] = fmt.Sprintf("%dm", int(taskManagerMem))
	if clusterGroup.AgentSerialId != "" {
		logger.Debugf(fmt.Sprintf("####  ClusterId %d, kubernetes.namespace ", cluster.Id))
		properties[constants.FLINK_K8S_NAMESPACE] = service6.GetDefaultNamespace(clusterGroup)
		properties[constants.FLINK_K8S_INIT_RESOURCE_ENABLE] = "true"
		// zk auth
		properties["high-availability.zookeeper.client.acl"] = "creator"
		properties["high-availability.zookeeper.path.root"] = clusterGroup.ZkRootPath

		// 设置 dnspolicy
		k8sService := k8s.GetK8sService()
		clientSet, err := k8sService.NewClient([]byte(cluster.KubeConfig))
		if err != nil {
			logger.Errorf("Failed to  NewClient[K8s] [ClusterId %s] ,err %v", clusterGroup.SerialId, err)
		} else {
			ns := fmt.Sprintf("oceanus-%s", clusterGroup.SerialId)
			service, err := k8sService.GetService(clientSet, ns, "kube-dns")
			if err != nil {
				logger.Errorf("get core dns service from %s error: %+v", ns, err)
			} else {
				properties[constants.FLINK_K8S_DNS_POLICY_NAMESERVERS] = service.Spec.ClusterIP
			}
		}

		label := ""
		labelMap := make(map[string]string)

		// 设置 node-selector
		if clusterGroup.ResourceType == constants.RESOURCE_TYPE_PRIVATE {
			labelMap = map[string]string{
				constants.TKE_CVM_NODE_ZONE:          clusterGroup.Zone,
				constants.TKE_CVM_NODE_APPID:         strconv.FormatInt(int64(clusterGroup.AppId), 10),
				constants.TKE_CVM_NODE_RESOURCE_TYPE: constants.TKE_CVM_NODE_RESOURCE_TYPE_PRIVATE,
			}
		} else {
			zone := runJobParams.JobRuntimeZone
			labelMap[constants.TKE_CVM_NODE_ZONE] = zone
			labelMap[constants.TKE_CVM_NODE_RESOURCE_TYPE] = constants.TKE_CVM_NODE_RESOURCE_TYPE_SHARE

			logger.Debugf("Job %s choose zone %s", job.SerialId, zone)
			// 设置 zk 地址
			properties[constants.FLINK_K8S_ZOOKEEPER_QUORUM] =
				deploy.GeneratePortConcatenatedIpString(
					deploy.GetNsZooKeeperServers(constants.ZooKeeperComponentReplicaNumber, zone), strconv.Itoa(constants.ZooKeeperPort))

		}
		for key, value := range labelMap {
			label += fmt.Sprintf("%s:%s,", key, value)
		}
		label = label[:len(label)-1]
		properties[constants.FLINK_K8S_JM_NODE_SELECTOR] = label
		properties[constants.FLINK_K8S_TM_NODE_SELECTOR] = label
	}
	return
}

func JobEnableDumpCollect(jobConfig *table5.JobConfig) bool {
	properties := make([]*model3.Property, 0)
	err := json.Unmarshal([]byte(jobConfig.Properties), &properties)
	if err != nil {
		logger.Errorf("jobConfig cannot Unmarshal to Property, error %v", err)
		return false
	}
	for _, item := range properties {
		if item.Key == constants.DIAGNOSIS_DATA_COLLECT_ENABLED_KEY && item.Value == constants.DIAGNOSIS_DATA_COLLECT_ENABLED_VALUE {
			return true
		}
	}
	return false
}

func updateMemRatio2Properties(cluster *table4.Cluster, jobConfig *table5.JobConfig, properties map[string]string) {
	if cluster.MemRatio == constants.CVM_MEMRATIO_2 {
		if jobConfig.TmCuSpec == 0.25 || (jobConfig.TaskManagerMem > 0 && jobConfig.TaskManagerMem <= 0.5) {
			if _, ok := properties["taskmanager.memory.jvm-overhead.min"]; !ok {
				properties["taskmanager.memory.jvm-overhead.min"] = "50m"
			}
			if _, ok := properties["taskmanager.memory.jvm-metaspace.size"]; !ok {
				properties["taskmanager.memory.jvm-metaspace.size"] = "128m"
			}
			if _, ok := properties["taskmanager.memory.managed.fraction"]; !ok {
				properties["taskmanager.memory.managed.fraction"] = "0.1"
			}
			properties["taskmanager.memory.framework.off-heap.size"] = "80m"
		}
		if jobConfig.JmCuSpec == 0.25 || (jobConfig.JobManagerMem > 0 && jobConfig.JobManagerMem <= 0.5) {
			if _, ok := properties["jobmanager.memory.jvm-overhead.min"]; !ok {
				properties["jobmanager.memory.jvm-overhead.min"] = "50m"
			}
			if _, ok := properties["jobmanager.memory.jvm-metaspace.size"]; !ok {
				properties["jobmanager.memory.jvm-metaspace.size"] = "128m"
			}
			properties["taskmanager.memory.framework.off-heap.size"] = "80m"
		}
		if jobConfig.TmCuSpec == 0.5 || (jobConfig.TaskManagerMem > 0 && jobConfig.TaskManagerMem <= 1) {
			if _, ok := properties["taskmanager.memory.jvm-overhead.min"]; !ok {
				properties["taskmanager.memory.jvm-overhead.min"] = "90m"
			}
			if _, ok := properties["taskmanager.memory.jvm-metaspace.size"]; !ok {
				properties["taskmanager.memory.jvm-metaspace.size"] = "128m"
			}
		}
		if jobConfig.JmCuSpec == 0.5 || (jobConfig.JobManagerMem > 0 && jobConfig.JobManagerMem <= 1) {
			if _, ok := properties["jobmanager.memory.jvm-metaspace.size"]; !ok {
				properties["jobmanager.memory.jvm-metaspace.size"] = "128m"
			}
		}
	}
}

// 计算独享集群、共享集群子集群
func GetJobInstanceZone(clusterGroup *table4.ClusterGroup, jobConfig *table5.JobConfig, cluster *table4.Cluster) (zone string) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("GetJobInstanceZone panic, with errors:%+v, stack %s", errs, util.Gostack())
		}
	}()
	zone = clusterGroup.Zone

	serialId := clusterGroup.SerialId
	if clusterGroup.AgentSerialId != "" && clusterGroup.ResourceType == constants.RESOURCE_TYPE_SHARE {
		serialId = clusterGroup.AgentSerialId
	}

	agentGroupService, err := service6.NewClusterGroupServiceBySerialId(serialId)
	if err != nil {
		logger.Errorf("clusterGroupService, err: %+v", err)
		return
	}
	agentGroup := agentGroupService.GetClusterGroup()

	supportedZones, _ := agentGroup.GetSupportedZones()
	zone = supportedZones[rand.Intn(len(supportedZones))]

	if agentGroup.NetEnvironmentType != constants.NETWORK_ENV_CLOUD_VPC {
		return
	}

	agentTke, err := agentGroupService.GetTke()
	if err != nil {
		logger.Errorf("GetTke, err: %+v", err)
		return
	}
	if agentTke.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		return
	}

	matchZoneSet, supportedZones, lackCuMap, err := calcJobRunningZone(agentGroup, jobConfig, cluster)
	if err != nil {
		zone = supportedZones[rand.Intn(len(supportedZones))]
		logger.Warningf("calcJobRunningZone err: %s", err.Error())
		return
	}

	b, _ := json.Marshal(matchZoneSet)
	logger.Debugf("matchZoneSet: %s", string(b))
	b, _ = json.Marshal(supportedZones)
	logger.Debugf("supportedZones: %s", string(b))
	b, _ = json.Marshal(lackCuMap)
	logger.Debugf("lackCuMap: %s", string(b))

	if len(matchZoneSet) <= 0 {
		matchZoneSet = supportedZones
	}
	if len(matchZoneSet) > 0 {
		zone = matchZoneSet[rand.Intn(len(matchZoneSet))]
		// 跨可用区部署，检查可用区，优先选择主可用区
		if clusterGroup.DeploymentMode == constants.DeploymentModeMultiple {
			err, multipleAvailabilityZone := getMultipleAvailabilityZone(clusterGroup, cluster, matchZoneSet)
			if err != nil {
				logger.Warningf("getMultipleAvailabilityZone err: %s", err.Error())
			}
			if multipleAvailabilityZone != "" {
				zone = multipleAvailabilityZone
			}
		}
	}

	lackCvm := lackCuMap[zone]
	if lackCvm <= 0.0 {
		return
	}
	logger.Debugf("%s, lackCvm count: %d", clusterGroup.SerialId, lackCvm)
	logger.Infof("###Trigger_AddCvm run job with clusterGroup %s", clusterGroup.SerialId)
	AddCvm(clusterGroup, zone, lackCvm)
	return zone
}

func AddCvm(clusterGroup *table4.ClusterGroup, zone string, lackCvm int) (added bool) {
	if clusterGroup.AgentSerialId != "" && clusterGroup.ResourceType == constants.RESOURCE_TYPE_SHARE {
		return
	}
	uc, err := clusterGroup.GetUniformConfig()
	if err != nil {
		logger.Errorf("GetUniformConfig, err: %+v", err)
		return
	}
	if uc.DisableRunBuySwitch {
		logger.Infof("DisableRunBuySwitch, disable: %+v", uc.DisableRunBuySwitch)
		return
	}

	agentGroupService, err := service6.NewClusterGroupServiceBySerialId(clusterGroup.SerialId)
	if err != nil {
		logger.Errorf("clusterGroupService, err: %+v", err)
		return
	}
	agentGroup := agentGroupService.GetClusterGroup()

	if agentGroup.NetEnvironmentType != constants.NETWORK_ENV_CLOUD_VPC {
		return
	}

	agentCluster, err := agentGroupService.GetCluster()
	if err != nil {
		logger.Errorf("GetCluster, err: %+v", err)
		return
	}

	agentTke, err := agentGroupService.GetTke()
	if err != nil {
		logger.Errorf("GetTke, err: %+v", err)
		return
	}
	if agentTke.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		return
	}

	action := constants.FLOW_PARAM_FLOW_ACTION_CREATE_CVM
	docId := fmt.Sprintf("%s_%s", clusterGroup.SerialId, action)

	sql, args := dao.NewQueryBuilder("SELECT tp.paramvalue FROM taskflow tf LEFT JOIN taskflowparams tp ON tf.id = tp.flowid").
		WhereEq("tf.processname", constants.FLOW_OCEANUS_UPGRADE_WORKER).
		WhereEq("tf.docId", docId).
		WhereIn("tf.status", []int{0, 1}).
		WhereEq("tp.paramkey", constants.FlowParamNeedCvmCount).
		Build()

	flowCountSlice, err := service2.FetchStringSlice(sql, args)
	if err != nil {
		logger.Errorf("FetchStringSlice, err: %+v", err)
		return
	}
	flowCount := 0
	for _, cs := range flowCountSlice {
		c, err := strconv.Atoi(cs)
		if err != nil {
			logger.Errorf("FetchStringSlice, err: %+v", err)
			return
		}
		flowCount += c
	}
	logger.Infof("%s, FlowCount: %d", clusterGroup.SerialId, flowCount)
	//最大可以购买的数量
	ci, err := clusterGroup.GetUniformCalcInfo()
	if err != nil {
		logger.Errorf("GetUniformCalcInfo, err: %+v", err)
		return
	}
	if ci.CalcInfo != nil && ci.CalcInfo.MaxAddWorkNum > 0 && lackCvm > ci.CalcInfo.MaxAddWorkNum {
		lackCvm = ci.CalcInfo.MaxAddWorkNum
	}
	if flowCount >= lackCvm {
		return true
	}

	//单次最大购买数量
	maxLeakCvm := 5
	if uc.MaxBuyCount > 0 {
		maxLeakCvm = uc.MaxBuyCount
	}
	maxLeakCvm += int(clusterGroup.CuNum) / 100
	maxLeakCvm -= flowCount
	if lackCvm > maxLeakCvm {
		lackCvm = maxLeakCvm
	}
	logger.Infof("%s, maxLeakCvm: %d, LackCvm: %d", clusterGroup.SerialId, maxLeakCvm, lackCvm)
	if lackCvm <= 0 {
		return true
	}

	flowId, err := flow.CreateFlow(constants.FLOW_OCEANUS_UPGRADE_WORKER, docId, 0, map[string]string{
		constants.FLOW_PARAM_REQUEST_ID:       fmt.Sprintf("%s", docId),
		constants.FLOW_PARAM_CLUSTER_GROUP_ID: fmt.Sprintf("%d", clusterGroup.Id),
		constants.FLOW_PARAM_CLUSTER_ID:       fmt.Sprintf("%d", agentCluster.Id),
		constants.FLOW_PARAM_CLUSTER_TYPE:     fmt.Sprintf("%d", agentTke.ClusterType),
		constants.FLOW_PARAM_FLOW_ACTION:      fmt.Sprintf("%s", action),
		constants.FLOW_PARAM_ZONE:             fmt.Sprintf("%s", zone),
		constants.FlowParamNeedCvmCount:       fmt.Sprintf("%d", lackCvm),
	}, nil)
	if err != nil {
		err = fmt.Errorf("[%s] createFlow %s fail %v", docId, constants.FLOW_OCEANUS_UPGRADE_WORKER, err)
		return
	}
	logger.Info("[%s] createFlow %s success, flowId %d", docId, constants.FLOW_OCEANUS_UPGRADE_WORKER, flowId)
	return true
}

func calcStandardPodSpec(cpu, memory float32, memRatio int8) float32 {
	var lowerBound float32 = 0.25
	var upperBound float32 = 7.0
	var step float32 = 0.05

	target := common.GetCuNumFromCpuMem(cpu, memory, memRatio)

	// 计算提供的数字在范围内第几步
	diff := target - lowerBound
	steps := int(diff / step)
	var closestValue float32
	if diff == float32(steps)*step {
		closestValue = lowerBound + step*float32(steps)
	} else {
		closestValue = lowerBound + step*float32(steps+1)
	}

	// 确保结果在范围内
	if closestValue > upperBound {
		closestValue = upperBound
	}
	logger.Debugf("target: %f, lowerBound: %f, upperBound: %f, step: %f, steps: %d, closestValue: %f\n", target, lowerBound, upperBound, step, steps, closestValue)
	return closestValue
}

func getZkNameSpace(clusterGroup *table4.ClusterGroup, zone string) string {
	if clusterGroup.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM {
		return zone
	}
	return constants.OCEANUS_NAMESPACE
}

// 跨AZ部署，检查可用区可用性，优先选择主可用区，其次是备可用区
// 如果报错 忽略
func getMultipleAvailabilityZone(clusterGroup *table4.ClusterGroup, cluster *table4.Cluster, supportedZones []string) (error, string) {
	rst := ""
	if clusterGroup.DeploymentMode != constants.DeploymentModeMultiple {
		return errors.New("getMultipleAvailabilityZone DeploymentMode is not multiple, ignore"), ""
	}
	k8sService := k8s.GetK8sService()
	k8sClient, err := k8sService.NewClient([]byte(cluster.KubeConfig))
	if err != nil {
		logger.Errorf("getMultipleAvailabilityZone NewClient, err: %+v", err)
		return err, ""
	}
	availabilityZones := make([]string, 0)
	for _, zone := range supportedZones {
		ready, err := k8sService.CheckZoneReady(k8sClient, getZkNameSpace(clusterGroup, zone))
		if err != nil {
			// 如果连接tke报错，默认不做过滤
			logger.Warningf("getMultipleAvailabilityZone CheckZoneReady, err: %+v", err)
			availabilityZones = append(availabilityZones, zone)
			continue
		}
		if ready {
			availabilityZones = append(availabilityZones, zone)
			continue
		}
	}
	if len(availabilityZones) < 1 {
		// 如果校验失败，默认不做过滤，防止误判
		return errors.New("getMultipleAvailabilityZone availabilityZones is null, ignore"), ""
	}
	for _, availabilityZone := range availabilityZones {
		rst = availabilityZone
		if availabilityZone == clusterGroup.Zone {
			// 主可用区直接返回
			break
		}
	}
	logger.Infof("getMultipleAvailabilityZone, zone is : %s", rst)
	return nil, rst
}

func calcJobRunningZone(agentGroup *table4.ClusterGroup, jobConfig *table5.JobConfig, cluster *table4.Cluster) (
	matchZoneSet, supportedZones []string, lackCuMap map[string]int, err error) {

	supportedZones, _ = agentGroup.GetSupportedZones()
	uniformConfig, err := agentGroup.GetUniformConfig()
	if err != nil {
		return
	}
	info, err := agentGroup.GetUniformCalcInfo()
	if err != nil {
		return
	}
	//去掉已经block的区
	blockedZoneMap := make(map[string]struct{})
	for _, zone := range uniformConfig.BlockedZones {
		blockedZoneMap[zone] = struct{}{}
		delete(info.ZoneInfoMap, zone)
	}
	tmpZones := make([]string, 0)
	for _, zone := range supportedZones {
		if _, ok := blockedZoneMap[zone]; !ok {
			tmpZones = append(tmpZones, zone)
		}
	}

	supportedZones = tmpZones

	if uniformConfig.DisableChooseZone {
		return
	}

	calcTimeStr := info.CalcTime
	if calcTimeStr == "0000-00-00 00:00:00" || len(calcTimeStr) <= 0 {
		return matchZoneSet, supportedZones, lackCuMap, fmt.Errorf("buyTime is %s", calcTimeStr)
	}

	loc, _ := time.LoadLocation("Local")
	buyTime, err := time.ParseInLocation("2006-01-02 15:04:05", calcTimeStr, loc)
	if err != nil {
		return
	}

	var delay int64 = 10
	if uniformConfig.DelayMinute > 0 {
		delay = uniformConfig.DelayMinute
	}
	tenMinuteBefore := time.Now().Add(-time.Duration(delay) * time.Minute)

	if buyTime.Before(tenMinuteBefore) {
		return matchZoneSet, supportedZones, lackCuMap, fmt.Errorf("buyTime is before %d minute now, buyTime %s", delay, buyTime)
	}

	matchZoneSet = make([]string, 0)
	lackCuMap = make(map[string]int)
	numberOfSlots := GetNumberOfTaskSlots(jobConfig)
	jmSpec := jobConfig.JmCuSpec
	tmSpec := jobConfig.TmCuSpec

	jobManagerCpu := jobConfig.JobManagerCpu
	jobManagerMem := jobConfig.JobManagerMem
	taskManagerCpu := jobConfig.TaskManagerCpu
	taskManagerMem := jobConfig.TaskManagerMem
	if jobManagerCpu > 0 {
		jmSpec = calcStandardPodSpec(jobManagerCpu, jobManagerMem, cluster.MemRatio)
		tmSpec = calcStandardPodSpec(taskManagerCpu, taskManagerMem, cluster.MemRatio)
	}
	tmCount := int64(math.Ceil(float64(jobConfig.DefaultParallelism) / float64(numberOfSlots)))
	for _, zone := range supportedZones {
		zoneInfo, ok := info.ZoneInfoMap[zone]
		if !ok {
			continue
		}
		logger.Infof("###calcJobRunningZone jmSpec %f tmSpec %f tmCount %d zoneInfo %+v", jmSpec, tmSpec, tmCount, zoneInfo)
		//先确保JM资源充足
		var jmCount int64 = 1
		jmNum := zoneInfo.NumOfCuMap[fmt.Sprintf("%f", jmSpec)]
		tmNum := zoneInfo.NumOfCuMap[fmt.Sprintf("%f", tmSpec)]
		if jmNum < jmCount { //缺少jm
			tmLack := 0
			if tmNum < tmCount {
				tmLack = int(tmCount - tmNum)
			}
			lackCuMap[zone] = calcCvm(jmSpec, tmSpec, tmLack)
			continue
		}
		//JM和TM一起计算
		if jmSpec >= tmSpec {
			jmCount = int64(jmSpec / tmSpec)
			if jmSpec > float32(jmCount)*tmSpec {
				jmCount += 1
			}
			jmSpec = tmSpec
		}
		if tmNum >= tmCount+jmCount {
			matchZoneSet = append(matchZoneSet, zone)
		} else {
			tmLack := int(tmCount + jmCount - tmNum)
			lackCuMap[zone] = calcCvm(jmSpec, tmSpec, tmLack-1)
		}
	}
	return matchZoneSet, supportedZones, lackCuMap, nil
}

func calcCvm(jmCuSpec, tmCuSpec float32, tmCount int) int {
	cuPerCvm := float32(7)
	jmCvmCount := 1
	if tmCount <= 0 {
		return jmCvmCount
	}

	tmCount = tmCount - int((cuPerCvm-jmCuSpec)/tmCuSpec)
	perCvmCount := int(cuPerCvm / tmCuSpec)
	tmCvmCount := tmCount / perCvmCount
	if tmCount > tmCvmCount*perCvmCount {
		tmCvmCount += 1
	}
	return jmCvmCount + tmCvmCount
}

const (
	FLINK_1_11_ENV string = "kubernetes.container-start-command-template"
	FLINK_JM_ENV   string = "containerized.master.env.JVM_ARGS"
	FLINK_TM_ENV   string = "containerized.taskmanager.env.JVM_ARGS"
)

// replaceLogLevel 替换真实作业的 LogLevel
func replaceLogLevel(properties map[string]string, logLevel string) {
	if logLevel == "" {
		logger.Debugf("logLevel is empty, use default.")
		return
	}
	logLevelKey := "-Dlog.level"
	// flink1.11 容器启动参数 JVM ENV 需要特殊兼容
	properties[FLINK_1_11_ENV] = getNewProperty(logLevelKey, logLevel, properties[FLINK_1_11_ENV])
	// flinkTM 容器启动参数 JVM ENV
	properties[FLINK_TM_ENV] = getNewProperty(logLevelKey, logLevel, properties[FLINK_TM_ENV])
	// flinkJM 容器启动参数 JVM ENV
	properties[FLINK_JM_ENV] = getNewProperty(logLevelKey, logLevel, properties[FLINK_JM_ENV])
}

// getNewProperty
func getNewProperty(logLevelKey string, logLevel string, originPropertyVal string) string {
	if strings.Contains(originPropertyVal, logLevelKey) {
		propertiesArray := strings.Split(originPropertyVal, " ")
		for i, v := range propertiesArray {
			if strings.HasPrefix(v, logLevelKey) {
				propertiesArray[i] = fmt.Sprintf("%s=%s", logLevelKey, logLevel)
			}
		}
		return strings.Join(propertiesArray, " ")
	}
	return originPropertyVal
}

func updateFlinkOnEmrProperties(
	requestId string,
	cluster *table4.Cluster,
	properties map[string]string,
) (err error) {
	if cluster.SchedulerType != constants.CLUSTER_SCHEDULER_TYPE_EMR {
		return
	}

	// 查询公共的YARN集群Flink配置参数
	yarnConfMap, err := service3.GetConfigurationByType(constants.CONFIGURATION_TYPE_FLINK_YARN)
	if err != nil {
		logger.Errorf("[%s] GetDynamicProperties -> GetConfigurationByType(%d) error:%+v",
			requestId, constants.CONFIGURATION_TYPE_FLINK_YARN, err)
		return err
	}
	updateProperties(properties, yarnConfMap)
	return
}

// 提供镜像版本号比较;
// 0 : a , b 的迭代版本号相等
// 1 : a 的迭代版本号大于 b
// -1 : a 的迭代版本号小于
// v1 := "flink-1.13-9.20-20241119"  v2 := "flink-1.16-9.20"
func CompareFlinkImageVersion(a string, b string) int {
	aVersions := parseFlinkVersion(a)
	bVersions := parseFlinkVersion(b)

	for i := range aVersions {
		if aVersions[i] < bVersions[i] {
			return -1
		} else if aVersions[i] > bVersions[i] {
			return 1
		}
	}
	return 0
}

func parseFlinkVersion(version string) []int {
	s := strings.Split(version, "-")
	v := strings.Split(s[2], ".")
	versions := []int{0, 0, 0}
	versions[0], _ = strconv.Atoi(v[0])
	if len(v) >= 2 {
		versions[1], _ = strconv.Atoi(v[1])
	}
	if len(v) >= 3 {
		versions[2], _ = strconv.Atoi(v[2])
	}
	return versions
}

func isSupportLongSql(flinkVersion string) bool {
	defer func() {
		if err := recover(); err != nil {
			logger.Warningf("isSupportLongSql (%s) error: %v", flinkVersion, err)
			return
		}
	}()
	fvs := strings.Split(flinkVersion, ":")
	if len(fvs) != 2 {
		return false
	}
	supportLongSqlFlinkVersion := "flink-1.16-9.20"
	return CompareFlinkImageVersion(fvs[1], supportLongSqlFlinkVersion) >= 0
}

func isNewFlinkVersion(flinkVersion string) bool {
	defer func() {
		if err := recover(); err != nil {
			logger.Warningf("isNewFlinkVersion (%s) error: %v", flinkVersion, err)
			return
		}
	}()
	fvs := strings.Split(flinkVersion, ":")
	if len(fvs) != 2 {
		return false
	}
	mvs := strings.Split(fvs[1], "-")
	if len(mvs) < 3 {
		return false
	}
	vo := strings.Split(mvs[2], ".")
	vn := [3]string{"5", "2", "0"}
	for i, ivo := range vo {
		if i > 2 {
			break
		}
		ivn := vn[i]
		iivo, err := strconv.Atoi(ivo)
		if err != nil {
			logger.Warningf("checkNewFlinkVersion (%s) error: %v", flinkVersion, err)
			return false
		}
		iivn, _ := strconv.Atoi(ivn)
		if iivo < iivn {
			return false
		}
		if iivo > iivn {
			return true
		}
	}
	return true
}

func updateClusterDimensionProperties(
	job *table.Job,
	cluster *table4.Cluster,
	properties map[string]string,
	jobConfig *table5.JobConfig,
	appId int32,
	jobRuntimeZone string,
	clusterGroup *table4.ClusterGroup) (err error) {
	clusterConfig, err := cluster.GetClusterConfig()
	if err != nil {
		logger.Errorf("job %s get cluster config error: %v", job.SerialId, err)
		return errorcode.InternalErrorCode.NewWithErr(err)
	}
	// 跨AZ部署 替换cluster config里面的annotation
	if clusterGroup.DeploymentMode == constants.DeploymentModeMultiple {
		clusterConfig = ReplaceSubnet(job.SerialId, cluster, clusterGroup, jobRuntimeZone, clusterConfig)
	}
	// 弹性集群作业支持设置pod 磁盘大小
	SetEksPodCbsSize(job, appId, cluster, clusterConfig)
	/**
	EKS 根据 jobconfig 的 JmCuSpec 和 TmCuSpec 的粒度动态设置 eks.tke.cloud.tencent.com/cpu-type  默认为 amd  细粒度 intel,amd
	*/
	err = ReplaceEksCpuType(cluster, jobConfig.JmCuSpec, jobConfig.TmCuSpec, jobConfig.JobManagerCpu, jobConfig.TaskManagerCpu, clusterConfig)
	if err != nil {
		return errorcode.InternalErrorCode.NewWithErr(err)
	}

	/**
	针对不同flink 版本的配置。 格式如下。
	{
	  "Flink-1.11" : {
	    "kubernetes.container.image" : "ccr.ccs.tencentyun.com\/oceanus\/flink-k8s:flink-1.11-4.0.6",
	    "OtherConfigKey" : "otherValue"
	  },
	  "kubernetes.jobmanager.annotations" : "'tke.cloud.tencent.com/cross-tenant-eni-enable:true','tke.cloud.tencent.com\/networks:tke-bridge,tke-direct-eni,tke-route'",
	  "kubernetes.taskmanager.annotations" : "'tke.cloud.tencent.com/cross-tenant-eni-enable:true','tke.cloud.tencent.com\/networks:tke-bridge,tke-direct-eni,tke-route'",
	  "kubernetes.container.image" : "ccr.ccs.tencentyun.com\/oceanus\/flink-k8s:flink-1.11-4.0.6",
	  "Flink-1.13" : {
	    "kubernetes.container.image" : "ccr.ccs.tencentyun.com\/oceanus\/flink-k8s:flink-1.11-4.0.6",
	    "OtherConfigKey" : "otherValue"
	  },
	  "Flink-1.13-Python-3.7" : {
	    "kubernetes.container.image" : "ccr.ccs.tencentyun.com\/oceanus\/flink-k8s:flink-1.11-4.0.6_pyflink",
	    "OtherConfigKey" : "otherValue"
	  }
	}
	*/
	flinkVersion := common.GetFlinkVersion(job, jobConfig)
	if job.Type == constants.JOB_TYPE_PYFLINK {
		flinkVersion = fmt.Sprintf("%s-%s", flinkVersion, jobConfig.PythonVersion)
	}
	for k, v := range clusterConfig {
		switch value := v.(type) {
		case string:
			properties[k] = value
		case map[string]interface{}:
			if k != flinkVersion {
				break
			}
			for fk, fv := range value {
				switch fValue := fv.(type) {
				case string:
					properties[fk] = fValue
				}
			}
		}
	}
	return
}

func ReplaceSubnet(jobSerialId string, cluster *table4.Cluster,
	clusterGroup *table4.ClusterGroup, jobRuntimeZone string, clusterConfig map[string]interface{}) map[string]interface{} {
	if clusterGroup.DeploymentMode != constants.DeploymentModeMultiple {
		return clusterConfig
	}
	if clusterGroup.Zone != jobRuntimeZone {
		logger.Warningf("job %s jobRuntimeZone %s not equal to cluster group %s zone %s, will replace subnet annotation of cluster config", jobSerialId, jobRuntimeZone, clusterGroup.SerialId, clusterGroup.Zone)
		zoneSubnets, err := cluster.GetSupportedZoneSubnets()
		if err != nil {
			logger.Errorf("job %s get supported zone subnets error: %v", jobSerialId, err)
		} else {
			if subnet, ok := zoneSubnets[jobRuntimeZone]; ok {
				groupService, err := service6.NewClusterGroupService(clusterGroup.Id)
				if err != nil {
					logger.Errorf("job %s new cluster group service error: %v", jobSerialId, err)
				} else {
					clusterVpc, err := groupService.GetGroupPeerVpc()
					tmpClusterConfig := make(map[string]interface{})
					newClusterConfig := strings.Replace(cluster.ClusterConfig, clusterVpc.SubnetId, subnet, -1)
					err = json.Unmarshal([]byte(newClusterConfig), &tmpClusterConfig)
					if err != nil {
						logger.Errorf("job %s unmarshal new cluster config error: %v", jobSerialId, err)
					} else {
						logger.Infof("job %s replace cluster config annotation from %s to %s", jobSerialId, cluster.ClusterConfig, newClusterConfig)
						return tmpClusterConfig
					}
				}
			} else {
				logger.Warningf("job %s jobRuntimeZone %s not in cluster group %s supported zone subnets %v, will not replace subnet annotation of cluster config", jobSerialId, jobRuntimeZone, clusterGroup.SerialId, zoneSubnets)
			}
		}
	}
	return clusterConfig
}

func SetEksPodCbsSize(job *table.Job, appId int32,
	cluster *table4.Cluster,
	clusterConfig map[string]interface{}) {
	isEks, _ := service6.IsEks(cluster.ClusterGroupId)
	if !isEks {
		return
	}
	if ok, w := auth.WhiteListValue(int64(appId), constants.WHITE_LIST_EKS_POD_CBS_SIZE); ok {
		strJobSerialIdCbsSizeMap := w.GetStrVal()
		if strJobSerialIdCbsSizeMap == "" {
			logger.Errorf("appid %d SetEksPodCbsSize whitelist param is blank...", appId)
			return
		}
		// {"seraiId1": 40, "seraiId2": 80}
		jobSerialIdCbsSizeMap := make(map[string]int8)
		err := json.Unmarshal([]byte(strJobSerialIdCbsSizeMap), &jobSerialIdCbsSizeMap)
		if err != nil {
			logger.Errorf("SetEksPodCbsSize Unmarshal to (map[string]int8 error with %s", strJobSerialIdCbsSizeMap)
			return
		}
		if cbsSize, ok := jobSerialIdCbsSizeMap[job.SerialId]; ok {
			if jobAnnotations, ok := clusterConfig[constants.KUBERNETES_JOBMANAGER_ANNOTATIONS]; ok {
				clusterConfig[constants.KUBERNETES_JOBMANAGER_ANNOTATIONS] = addCbsSize(jobAnnotations, cbsSize)
				logger.Infof("SetEksPodCbsSize jobAnnotations newStr %s", clusterConfig[constants.KUBERNETES_JOBMANAGER_ANNOTATIONS])
			}
			if taskAnnotations, ok := clusterConfig[constants.KUBERNETES_TASKMANAGER_ANNOTATIONS]; ok {
				clusterConfig[constants.KUBERNETES_TASKMANAGER_ANNOTATIONS] = addCbsSize(taskAnnotations, cbsSize)
				logger.Infof("SetEksPodCbsSize taskAnnotations newStr %s", clusterConfig[constants.KUBERNETES_TASKMANAGER_ANNOTATIONS])
			}
		}
	}
}

func addCbsSize(annotations interface{}, cbsSize int8) string {
	if annotations == nil || annotations.(string) == "" {
		return annotations.(string)
	}
	annotationMap := make(map[string]string)
	err := json.Unmarshal([]byte(annotations.(string)), &annotationMap)
	if err != nil {
		logger.Errorf("addCbsSize Unmarshal annotations to (map[string]string error with %s, use old format", annotations.(string))
		// 老格式
		return fmt.Sprintf("%s,'%s:%s'", annotations.(string),
			constants.EKS_POD_CBS_SIZE, fmt.Sprintf("%d", cbsSize))
	} else {
		annotationMap[constants.EKS_POD_CBS_SIZE] = fmt.Sprintf("%d", cbsSize)
		newAnnotations, _ := json.Marshal(annotationMap)
		return string(newAnnotations)
	}
}

func ReplaceEksCpuType(cluster *table4.Cluster,
	jmCuSpec float32,
	tmCuSpec float32,
	jobManagerCpu float32,
	taskManagerCpu float32,
	clusterConfig map[string]interface{}) error {
	isEks, _ := service6.IsEks(cluster.ClusterGroupId)
	if isEks {
		if jmCuSpec == constants.FINEGRAINEDRESOURCE_025 ||
			jmCuSpec == constants.FINEGRAINEDRESOURCE_05 ||
			(jobManagerCpu > 0 && jobManagerCpu < 1) {
			kja := clusterConfig[constants.KUBERNETES_JOBMANAGER_ANNOTATIONS]
			if kja != nil {
				newStr := repalceCpuType(kja)
				clusterConfig[constants.KUBERNETES_JOBMANAGER_ANNOTATIONS] = newStr
			}
		}
		if tmCuSpec == constants.FINEGRAINEDRESOURCE_025 ||
			tmCuSpec == constants.FINEGRAINEDRESOURCE_05 ||
			(taskManagerCpu > 0 && taskManagerCpu < 1) {
			kta := clusterConfig[constants.KUBERNETES_TASKMANAGER_ANNOTATIONS]
			if kta != nil {
				newStr := repalceCpuType(kta)
				clusterConfig[constants.KUBERNETES_TASKMANAGER_ANNOTATIONS] = newStr
			}
		}
	}
	return nil
}

func repalceCpuType(annotations interface{}) string {
	if annotations == nil || annotations.(string) == "" {
		return annotations.(string)
	}
	annotationMap := make(map[string]string)
	err := json.Unmarshal([]byte(annotations.(string)), &annotationMap)
	if err != nil {
		logger.Errorf("repalceCpuType Unmarshal annotations to (map[string]string error with %s, use old format", annotations.(string))
		// 老格式
		temp := []string{}
		for _, value := range strings.Split(annotations.(string), "','") {
			kv := strings.Split(value, ":")
			if len(kv) == 2 {
				// eks.tke.cloud.tencent.com/cpu-type 在中间或者在开头
				if kv[0] == constants.EKS_CPU_TYPE_KEY || kv[0] == "'"+constants.EKS_CPU_TYPE_KEY {
					// eks.tke.cloud.tencent.com/cpu-type 在最后面
					if kv[1][len(kv[1])-1:] == "'" {
						temp = append(temp, kv[0]+":"+constants.EKS_FINEGRAINEDRESOURCE_CPU_TYPE+"'")
					} else {
						temp = append(temp, kv[0]+":"+constants.EKS_FINEGRAINEDRESOURCE_CPU_TYPE)
					}
				} else {
					temp = append(temp, value)
				}
			} else {
				temp = append(temp, value)
			}
		}
		return strings.Join(temp, "','")
	} else {
		annotationMap[constants.EKS_CPU_TYPE_KEY] = constants.EKS_FINEGRAINEDRESOURCE_CPU_TYPE
		newAnnotations, _ := json.Marshal(annotationMap)
		return string(newAnnotations)
	}
}

func updateJobCheckpointConfigs(
	job *table.Job,
	jobConfig *table5.JobConfig,
	properties map[string]string,
) (err error) {
	if _, ok := properties[constants.CHECKPOINT_RETENTION_KEY]; !ok && jobConfig.CheckpointRetainedNum > 0 {
		properties[constants.CHECKPOINT_RETENTION_KEY] = constants.CHECKPOINT_RETENTION_ON_SUCCESS
		properties[constants.CHECKPOINT_RETENTION_NUM] = fmt.Sprintf("%d", jobConfig.CheckpointRetainedNum)
	}
	if _, ok := properties[constants.EXECUTION_CHECKPOINTING_INTERVAL]; !ok && jobConfig.CheckpointInterval > 0 {
		properties[constants.EXECUTION_CHECKPOINTING_INTERVAL] = fmt.Sprintf("%ds", jobConfig.CheckpointInterval)
	}
	if _, ok := properties[constants.CHECKPOINT_TIMEOUT]; !ok && jobConfig.CheckpointTimeoutSecond > 0 {
		properties[constants.CHECKPOINT_TIMEOUT] = fmt.Sprintf("%ds", jobConfig.CheckpointTimeoutSecond)
	}
	return nil
}

func updateJobTraceModeProperties(
	requestId string,
	jobConfig *table5.JobConfig,
	properties map[string]string,
) (err error) {
	if jobConfig.TraceModeConfiguration == "" || jobConfig.TraceModeConfiguration == "{}" {
		return nil
	}
	conf := &model2.TraceModeConfiguration{}
	err = json.Unmarshal([]byte(jobConfig.TraceModeConfiguration), conf)
	if err != nil {
		return errorcode.InvalidParameterValueCode.NewWithErr(err)
	}
	properties["trace.enabled"] = "true"
	properties["env.java.opts.taskmanager"] = "-javaagent:/opt/flink/pipeline-jars/oceanus-trace.jar"
	properties["trace.sample.rate"] = conf.Rate
	properties["trace.specific.operator"] = conf.Operator

	return nil
}

func updateJobExpertModeProperties(
	requestId string,
	jobConfig *table5.JobConfig,
	properties map[string]string,
	supportedFeatures []string,
	jobType int8,
) (err error) {
	// 没开启的这里都是空和{}
	if jobConfig.ExpertModeConfiguration == "" || jobConfig.ExpertModeConfiguration == "{}" {
		return nil
	}
	// jar作业没有configuration
	if jobType == constants.JOB_TYPE_JAR {
		properties["cluster.fine-grained-resource-management.enabled"] = "true"
		properties["slotmanager.resource-allocation.strategy"] = "DYNAMIC"
		return nil
	}
	conf := &model2.ExpertModeConfiguration{}
	err = json.Unmarshal([]byte(jobConfig.ExpertModeConfiguration), conf)
	if err != nil {
		return errorcode.InvalidParameterValueCode.NewWithErr(err)
	}
	for _, nc := range conf.NodeConfig {
		if nc.SlotSharingGroup != "default" && nc.SlotSharingGroup != "" {
			// Flink-conf.yaml 需要设置 cluster.fine-grained-resource-management.enabled: true
			// 这个参数是集群启动给ResourceManager用的.
			// 所以需要在 启动命令里面加上这个参数
			properties["cluster.fine-grained-resource-management.enabled"] = "true"
			properties["slotmanager.resource-allocation.strategy"] = "DYNAMIC"
			return nil
			// 新版本的只有default也能执行高级模式
		} else if nc.SlotSharingGroup != "" && getTMFineGrainedDifferentialByFeature(supportedFeatures) {
			properties["cluster.fine-grained-resource-management.enabled"] = "true"
			properties["slotmanager.resource-allocation.strategy"] = "DYNAMIC"
			return nil
		}
	}

	return nil
}

func getTMFineGrainedDifferentialByFeature(clusterFeature []string) bool {
	if len(clusterFeature) == 0 {
		return false
	}
	for _, v := range clusterFeature {
		// 如果集群支持日志级别特性，默认日志级别为 INFO
		if constants.TMFineGrainedDifferential == v {
			return true
		}
	}
	return false
}

func updateJobDimensionProperties(
	jobConfig *table5.JobConfig,
	properties map[string]string,
) (err error) {
	if len(jobConfig.Properties) == 0 {
		return
	}
	props := make([]*model.Property, 0)
	if err := json.Unmarshal([]byte(jobConfig.Properties), &props); err != nil {
		return errorcode.InternalErrorCode_UnMarshalFailed.NewWithErr(err)
	}
	propsMap := map[string]string{}
	for _, kv := range props {
		propsMap[kv.Key] = kv.Value
	}
	updateProperties(properties, propsMap)
	return
}

func updateCloudVpcProperties(
	requestId string,
	clusterGroup *table4.ClusterGroup,
	cluster *table4.Cluster,
	jobConfig *table5.JobConfig,
	properties map[string]string,
) (err error) {
	if clusterGroup.NetEnvironmentType != constants.NETWORK_ENV_CLOUD_VPC {
		return
	}

	// 对于现网 VPC 环境（外部用户）的集群，高级参数需要特殊处理
	// 根据本地域的部署情况, 设置内置的 Metric Reporter
	var builtInReporters string
	if IsCurrentRegionEnabledDetailMetricReporter(clusterGroup.Region) {
		builtInReporters = constants.BuiltInMetricsReporters
	} else {
		builtInReporters = constants.BuiltInMetricsReportersWithoutDetailMetric
	}

	// 如果用户有自定义 Reporter, 还需要和上述 Reporter 列表合并
	userDefinedMetricsReporters := properties["metrics.reporters"]
	if userDefinedMetricsReporters != "" {
		properties["metrics.reporters"] = builtInReporters + "," + userDefinedMetricsReporters
	} else {
		properties["metrics.reporters"] = builtInReporters
	}
	// EKS集群预留系统内存
	isEks, _ := service6.IsEks(clusterGroup.Id)
	if isEks {
		if jobConfig.JmCuSpec == 1 || (jobConfig.JobManagerMem > 0 && jobConfig.JobManagerMem <= float32(cluster.MemRatio)) {
			jobmanagerJvmOverhead := properties["jobmanager.memory.jvm-overhead.fraction"]
			if jobmanagerJvmOverhead == "" {
				properties["jobmanager.memory.jvm-overhead.fraction"] = "0.15"
			}
		}
		if jobConfig.TmCuSpec == 1 || (jobConfig.TaskManagerMem > 0 && jobConfig.TaskManagerMem <= float32(cluster.MemRatio)) {
			taskmanagerJvmOverhead := properties["taskmanager.memory.jvm-overhead.fraction"]
			if taskmanagerJvmOverhead == "" {
				properties["taskmanager.memory.jvm-overhead.fraction"] = "0.15"
			}
		}
	}

	if err := updateFlinkConfOverride(requestId, clusterGroup, jobConfig, properties); err != nil {
		// 不影响流程, 只在日志里报错即可
		logger.Errorf("%s, Failed to update global Flink configurations %+v", requestId, err)
	}

	return
}

func updateFlinkConfOverride(
	requestId string,
	clusterGroup *table4.ClusterGroup,
	jobConfig *table5.JobConfig,
	properties map[string]string,
) (err error) {
	// 谨慎地强制覆盖一些配置（避免历史集群配置不统一造成的问题) 。注意 YARN 和 TKE 集群都会受到影响，请勿滥用
	group := constants.ConfRainbowGroupCommon
	workerSpec, err := service3.GetConfigurationValueWithPreferred(
		constants.CONF_KEY_TKE_WORKER_SPEC, clusterGroup.Zone)
	if err != nil {
		return
	}
	cvmConfList, err := service2.GetTableService().ListCvmSaleConfByInstanceType(workerSpec, 0, 1)
	if err != nil {
		return
	}
	if len(cvmConfList) == 0 {
		msg := fmt.Sprintf("cvm spec %s not found in table CvmSaleConf", workerSpec)
		err = errorcode.InternalErrorCode.NewWithInfo(msg, nil)
		return
	}
	// 判断是不是eks 集群
	isEks, isEksErr := service6.IsEks(clusterGroup.Id)
	logger.Infof("req: %s updateFlinkConfOverride clusterGroup.SerialId:%s isEks: %t isEksErr: %+v", requestId, clusterGroup.SerialId, isEks, isEksErr)
	if err != nil {
		return
	}
	globalFlinkConfigMap := make(map[string]string, 0)

	tmCuSpec := jobConfig.TmCuSpec
	if jobConfig.TaskManagerCpu > 0 {
		tmCuSpec = jobConfig.TaskManagerCpu
	}

	activeProcessorCountData := &struct {
		ActiveProcessorCount int
	}{
		ActiveProcessorCount: int(float32(cvmConfList[0].Cpu) / tmCuSpec),
	}
	if isEks {
		eksActiveData, getEksActiveDataErr := GetEKSActiveProcessorCountWithTmCuSpec(requestId, tmCuSpec)
		if getEksActiveDataErr != nil {
			return
		}
		activeProcessorCountData = &struct {
			ActiveProcessorCount int
		}{
			ActiveProcessorCount: eksActiveData,
		}
	}
	logger.Infof("req: %s updateFlinkConfOverride clusterGroup.SerialId:%s isEks: %t activeProcessorCountData: %+v", requestId, clusterGroup.SerialId, isEks, activeProcessorCountData)
	err = config.DecodeK8sObjectFromRainbowConfigTemplate(
		group, constants.ConfRainbowFlinkConfOverride,
		activeProcessorCountData,
		&globalFlinkConfigMap)
	if err != nil {
		return
	}

	updateProperties(properties, globalFlinkConfigMap)

	return
}

func GetEKSActiveProcessorCountWithTmCuSpec(requestId string, tmCuSpec float32) (activeProcessorCount int, err error) {
	// eks 标准规格，若为非标准规格，则自动向上转换成标准规格 小于2 按2来算
	// 参考： https://cloud.tencent.com/document/product/457/74015
	group := constants.ConfRainbowGroupCommon
	eksNormalCpuArray := make([]float32, 0)
	if err = config.DecodeK8sObjectFromRainbowConfig(
		group, constants.ConfRainbowEksNormalCpuSpecifications, &eksNormalCpuArray); err != nil {
		return 0, err
	}
	logger.Infof("reqId:%s ,GetEKSActiveProcessorCountWithTmCuSpec eksNormalCpuArray : %+v", requestId, eksNormalCpuArray)
	// 向上转换为标准规格 计算后返回
	var currentEksNormalCpu float32
	currentEksNormalCpu = 0
	for _, eksNormalCpu := range eksNormalCpuArray {
		if eksNormalCpu >= tmCuSpec {
			// 找到第一个符合条件的就返回
			currentEksNormalCpu = eksNormalCpu
			break
		}
	}
	return int(currentEksNormalCpu / tmCuSpec), nil
}

func GenerateDynamicProperties(runJobParams *cluster_master_protocol.RunJobParams, job *table.Job,
	jobConfig *table5.JobConfig, stateCosBucket string, jobRunningOrderId int64) (err error) {
	requestId := runJobParams.RequestId
	clusterGroup, err := service6.GetClusterGroupByClusterId(job.ClusterId)
	if err != nil {
		logger.Errorf("Failed to GetClusterGroupByClusterId(%s) because %+v", requestId, err)
		return err
	}
	cluster, err := service6.GetClusterByClusterId(job.ClusterId)
	if err != nil {
		logger.Errorf("[%s] GetDynamicProperties -> GetClusterByClusterId(%s) error:%+v",
			requestId, job.ClusterId, err)
		return err
	}

	// 获取关于状态的DynamicProperties
	properties, err := service4.GetStateDynamicProperties(job, stateCosBucket, jobRunningOrderId)
	if err != nil {
		logger.Errorf("[%s] setDynamicProperties -> GetStateDynamicProperties errors:%+v", requestId, err)
		return err
	}

	if err = updateFlinkCommonProperties(clusterGroup, jobConfig, runJobParams, properties, jobRunningOrderId); err != nil {
		logger.Errorf("[%s] GetDynamicProperties -> GetConfigurationByType error:%+v", requestId, err)
		return err
	}

	if err = updateFlinkOnTkeProperties(clusterGroup, cluster, runJobParams, job, jobConfig,
		properties, stateCosBucket); err != nil {
		return
	}

	if err = updateFlinkOnEmrProperties(requestId, cluster, properties); err != nil {
		return
	}

	if err = updateCloudVpcProperties(requestId, clusterGroup, cluster, jobConfig, properties); err != nil {
		return
	}

	// 集群维度配置
	if err := updateClusterDimensionProperties(job, cluster, properties, jobConfig, job.AppId, runJobParams.JobRuntimeZone, clusterGroup); err != nil {
		logger.Errorf("[%s] ignore ClusterConfig for cluster %d(%s) %v", requestId, cluster.Id, cluster.UniqClusterId, err)
	}

	if err = updateJobTraceModeProperties(requestId, jobConfig, properties); err != nil {
		return err
	}

	if err = updateJobCheckpointConfigs(job, jobConfig, properties); err != nil {
		return err
	}

	// 作业维度配置
	if err := updateJobDimensionProperties(jobConfig, properties); err != nil {
		logger.Errorf("[%s] ignore JobConfig.Properties for cluster %d(%s) %v",
			requestId, cluster.Id, cluster.UniqClusterId, err)
	}

	supportedFeatures, err := cluster.GetSupportedFeatures()
	if err != nil {
		logger.Errorf("[%s] GetDynamicProperties -> GetSupportedFeatures error:%+v", requestId, err)
	}
	if err = updateJobExpertModeProperties(requestId, jobConfig, properties, supportedFeatures, job.Type); err != nil {
		return err
	}

	if clusterGroup.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && clusterGroup.AgentSerialId != "" {
		// 如果是共享子集群,则需要移除 flink.kubernetes.diagnosis-volume-mounted 和 flink.kubernetes.diagnosis-collection-enabled
		if val, ok := properties[constants.DIAGNOSIS_DATA_VOLUME_MOUNTED_EKY]; ok && val == constants.DIAGNOSIS_DATA_VOLUME_MOUNTED_VALUE {
			delete(properties, constants.DIAGNOSIS_DATA_VOLUME_MOUNTED_EKY)
		}
		if val, ok := properties[constants.DIAGNOSIS_DATA_COLLECT_ENABLED_KEY]; ok && val == constants.DIAGNOSIS_DATA_COLLECT_ENABLED_VALUE {
			delete(properties, constants.DIAGNOSIS_DATA_COLLECT_ENABLED_KEY)
		}
	}
	// >= Flink-1.16 cos 高级参数
	addCosParam(job, properties, jobConfig)

	properties[constants.OceanusFeatureParamKey] = cluster.SupportedFeatures

	// yunti集群启动作业的时候如果配置了这个参数就自动加上这个参数
	value, existed := properties[constants.KUBERNETES_HOSTNETWORK_ENABLED]
	if existed && value == "true" {
		properties[constants.TASKMANAGER_NETWORK_BINDADDRESS_SKIP_LOCALHOST] = strconv.FormatBool(true)
	}

	propertiesBytes, _ := json.Marshal(properties)
	runJobParams.DynamicProperties = string(propertiesBytes)
	return err
}

func addCosParam(job *table.Job, properties map[string]string, jobConfig *table5.JobConfig) {
	flinkVersion := common.GetFlinkVersion(job, jobConfig)
	if _, support := constants.CosParamFlinkVersion[flinkVersion]; support {
		properties["fs.AbstractFileSystem.cosn.impl"] = "org.apache.hadoop.fs.CosN"
		properties["fs.cosn.impl"] = "org.apache.hadoop.fs.CosFileSystem"
		properties["fs.cosn.credentials.provider"] = "org.apache.flink.fs.cos.OceanusCOSCredentialsProvider"
		properties["fs.cosn.bucket.region"] = job.Region
		properties["fs.cosn.trsf.fs.AbstractFileSystem.ofs.impl"] = "com.qcloud.chdfs.fs.CHDFSDelegateFSAdapter"
		properties["fs.cosn.trsf.fs.ofs.impl"] = "com.qcloud.chdfs.fs.CHDFSHadoopFileSystemAdapter"
		properties["fs.cosn.trsf.fs.ofs.tmp.cache.dir"] = "/tmp/chdfs/"
		if GetConfigValueFromJobConfigProperties(jobConfig, constants.OCEANUS_COS_PARAM_FS_COSN_CROSS_ACCOUNT_ACCESS_KEY) !=
			constants.OCEANUS_COS_PARAM_FS_COSN_CROSS_ACCOUNT_ACCESS_ENABLE {
			logger.Infof("Enable oceanus.fs.cosn.cross.account.access,job %s", job.SerialId)
			properties["fs.cosn.userinfo.appid"] = strconv.FormatInt(int64(job.AppId), 10)
			properties["fs.cosn.trsf.fs.ofs.user.appid"] = strconv.FormatInt(int64(job.AppId), 10)
		}
		properties["fs.cosn.trsf.fs.ofs.bucket.region"] = job.Region
		properties["fs.cosn.trsf.fs.ofs.upload.flush.flag"] = "true"
	}
}

func updateProperties(properties map[string]string, confMap map[string]string) map[string]string {
	for k, v := range confMap {
		properties[k] = v
	}
	return properties
}

func GetSQLEntrypointEtc(jobType int8) (string, string, string, error) {
	entrypointClass, err := service3.GetEntrypointClass(jobType)
	if err != nil {
		return controller.NULL, controller.NULL, controller.NULL, err
	}

	jarFilePath, err := service3.GetJarFilePath(jobType)
	if err != nil {
		return controller.NULL, controller.NULL, controller.NULL, err
	}

	shipFiles, err := service3.GetShipFiles(jobType)
	if err != nil {
		return controller.NULL, controller.NULL, controller.NULL, err
	}

	return entrypointClass, jarFilePath, shipFiles, nil
}

func GetConfigValueFromJobConfigProperties(jobConfig *table5.JobConfig, key string) (value string) {
	properties := make(map[string]string, 0)
	if err := updateJobDimensionProperties(jobConfig, properties); err != nil {
		logger.Errorf("ignore JobConfig.Properties for job %d", jobConfig.JobId, err)
		return ""
	}
	for k, v := range properties {
		if k == key {
			return v
		}
	}
	return ""
}

func BuildSQLJobProgramArgs(job *table.Job, jobConfig *table5.JobConfig, checkpointInterval int64, startMode string,
	encodedSqlCode string, maxParallelism int16, requestId string, metadata string, resourceConfigItem *model2.ResourceConfigItem) (string, error) {

	cluster, err := service6.GetClusterByClusterId(job.ClusterId)
	if err != nil {
		logger.Errorf("Failed to GetClusterByClusterId, error: %+v", err)
		return controller.NULL, err
	}

	var regions []string
	var statusList []int
	regions = append(regions, job.Region)
	statusList = append(statusList, constants.CLUSTER_GROUP_STATUS_RUNNING, constants.CLUSTER_GROUP_STATUS_UPGRADE,
		constants.CLUSTER_GROUP_STATUS_SCALE_PROGRESS, constants.CLUSTER_GROUP_STATUS_SCALEDOWN_PROGRESS)

	// 先找到这个 AppID 在这个地区和可用区下的所有运行中的集群 (数目不会很多)
	matchingClusterGroups, err := common.ListClusterGroups(&common.ListClusterGroupsParam{
		AppId:        0,
		Regions:      regions,
		Zone:         job.Zone,
		ClusterType:  []int8{constants.CLUSTER_GROUP_TYPE_PRIVATE},
		ClusterNames: nil,
		IsVagueNames: false,
		SerialIds:    nil,
		Offset:       0,
		Limit:        0,
		OrderType:    0,
		StatusList:   statusList,
	})
	if err != nil {
		logger.Errorf("Failed to ListClusterGroups for %d in %s (%s), error %+v", job.AppId, job.Region, job.SerialId, err)
		return controller.NULL, err
	}

	// 从查到的 ClusterGroup 列表里, 寻找与作业 ClusterGroupId 一致的
	var jobClusterGroup *table4.ClusterGroup
	for _, clusterGroup := range matchingClusterGroups {
		if clusterGroup.Id == job.ClusterGroupId {
			jobClusterGroup = clusterGroup
		}
	}
	if len(matchingClusterGroups) == 0 || jobClusterGroup == nil {
		msg := fmt.Sprintf("job %s not associated with any cluster group(s)", job.SerialId)
		logger.Errorf("Logic error? %s", msg)
		return controller.NULL, errors.New(msg)
	}

	// 共享集群 SQL 需要附加特定的 Config
	var sysConfigs string
	if jobClusterGroup.Type == constants.CLUSTER_GROUP_TYPE_SHARED {
		sysConfigs, err = BuildSystemConfig(job.AppId, job.Region, startMode, cluster.VpcId, cluster.SubnetId)
		if err != nil {
			msg := fmt.Sprintf("Failed to build System Configs (--otherConfig), often because of missing item in ConfigurationCenter table: " + err.Error())
			logger.Error(msg)
			return controller.NULL, errors.New(msg)
		}
	} else { // 独享集群 SQL + JAR 不需要特定的 Config
		sysConfigs = "{}"
	}

	// Flink Qcloud Streaming 运行时所需的其他参数
	var programArgs string
	programArgs += "--encodedJobName "
	programArgs += " " + encodeJobName(job, requestId) + " "
	if resourceConfigItem != nil && len(resourceConfigItem.ResourceName) > 0 {
		programArgs += " --sqlFilePath "
		programArgs += constants.RESOURCE_DEPENDENCY_DEFAULT_PATH + "/" + resourceConfigItem.ResourceName
		encodedSqlCode = ""
	}
	programArgs += " --encodedSqlCode "
	programArgs += encodedSqlCode
	programArgs += " --maxParallelism "
	programArgs += strconv.Itoa(int(maxParallelism))
	programArgs += " --checkpoint "
	programArgs += strconv.FormatInt(checkpointInterval, 10)
	programArgs += " --encodedOtherConfig "
	programArgs += base64.StdEncoding.EncodeToString([]byte(sysConfigs))
	if metadata != "" {
		programArgs += " --metadata "
		programArgs += base64.StdEncoding.EncodeToString([]byte(metadata))
	}
	// 从 sql 高级参数中提取 execution.checkpointing.timeout
	if GetConfigValueFromJobConfigProperties(jobConfig, constants.CHECKPOINT_TIMEOUT) != "" {
		checkpointTimeoutStr := GetConfigValueFromJobConfigProperties(jobConfig, constants.CHECKPOINT_TIMEOUT)
		duration, err := common.ParseFlinkDuration(strings.Replace(checkpointTimeoutStr, " ", "", -1))
		if err != nil {
			logger.Error("checkpointTimeoutStr %s parsed with error %v", checkpointTimeoutStr, err)
		} else {
			programArgs += " --checkpointTimeout "
			programArgs += fmt.Sprintf("%d", int64(duration.Seconds()))
		}
	} else if jobConfig.CheckpointTimeoutSecond > 0 {
		programArgs += " --checkpointTimeout "
		programArgs += fmt.Sprintf("%d", jobConfig.CheckpointTimeoutSecond)
	}
	// 从 sql 高级参数中提取 checkpoint retention policy
	if GetConfigValueFromJobConfigProperties(jobConfig, constants.CHECKPOINT_RETENTION_KEY) != "" {
		programArgs += " --checkpointRetentionPolicy "
		programArgs += GetConfigValueFromJobConfigProperties(jobConfig, constants.CHECKPOINT_RETENTION_KEY)
	} else if jobConfig.CheckpointRetainedNum != 0 {
		programArgs += " --checkpointRetentionPolicy "
		programArgs += "RETAIN_ON_SUCCESS"
	}
	//从sql高级参数中提取restart-strategy.fixed-delay.attempts
	if GetConfigValueFromJobConfigProperties(jobConfig, "restart-strategy.fixed-delay.attempts") != "" {
		programArgs += " --restartTimes "
		programArgs += GetConfigValueFromJobConfigProperties(jobConfig, "restart-strategy.fixed-delay.attempts")
	}
	if GetConfigValueFromJobConfigProperties(jobConfig, "execution.runtime-mode") != "" {
		programArgs += " --executionRuntimeMode "
		programArgs += GetConfigValueFromJobConfigProperties(jobConfig, "execution.runtime-mode")
	}
	expertModeConfigurationResourceName := jobConfig.ExpertModeConfigurationResourceName
	if len(expertModeConfigurationResourceName) > 0 {
		programArgs += " --expertModeConfigurationFile "
		programArgs += constants.RESOURCE_DEPENDENCY_DEFAULT_PATH + "/" + expertModeConfigurationResourceName
	} else if conf := service6.ConvertExpertModeConfiguration(jobConfig.ExpertModeConfiguration); conf != nil {
		programArgs += " --expertModeConfiguration "
		programArgs += base64.StdEncoding.EncodeToString(conf)
	}

	return programArgs, nil
}

func encodeJobName(job *table.Job, requestId string) string {
	jobName := fmt.Sprintf("%d %s %s %s", job.AppId, job.SerialId, job.Name, requestId)
	return base64.StdEncoding.EncodeToString([]byte(jobName))
}

func BuildSystemConfig(appId int32, region string, startMode string, vpcId string, subnetId string) (string, error) {
	systemConfigs := map[string]interface{}{}

	scsUserAppId, err := service3.GetScsUserAppId()
	if err != nil {
		return controller.NULL, err
	}
	scsUserUin, err := service3.GetScsUserUin()
	if err != nil {
		return controller.NULL, err
	}
	accountUrl, err := service3.GetAccountUrl(region)
	if err != nil {
		return controller.NULL, err
	}
	netcenterUrl, err := service3.GetNetcenterUrl(region, vpcId, subnetId)
	if err != nil {
		return controller.NULL, err
	}

	// Ckafka
	systemConfigs["ckafka.appId"] = appId
	ckafkaUrl, err := service3.GetCkafkaUrl(region)
	if err != nil {
		return controller.NULL, err
	}
	systemConfigs["ckafka.ckafkaUrl"] = ckafkaUrl
	platformUrl, err := service3.GetPlatformUrl(region)
	if err != nil {
		return controller.NULL, err
	}
	systemConfigs["ckafka.platformUrl"] = platformUrl
	systemConfigs["ckafka.scsUserAppId"] = scsUserAppId
	systemConfigs["ckafka.scsUserUin"] = scsUserUin
	systemConfigs["ckafka.accountUrl"] = accountUrl
	systemConfigs["ckafka.startMode"] = startMode
	systemConfigs["ckafka.netcenterUrl"] = netcenterUrl
	systemConfigs["ckafka.vpcId"] = vpcId
	systemConfigs["ckafka.subnetId"] = subnetId

	// CDP
	systemConfigs["cdp.appId"] = appId
	cdpUrl, err := service3.GetCdpUrl(region)
	if err != nil {
		return controller.NULL, err
	}
	systemConfigs["cdp.cdpUrl"] = cdpUrl
	cdpInnerUserKey, err := service3.GetCdpInnerUserKey()
	if err != nil {
		return controller.NULL, err
	}
	systemConfigs["cdp.innerUserKey"] = cdpInnerUserKey
	systemConfigs["cdp.startMode"] = startMode

	// MySQL & CDB (MySQL 的别名, 兼容旧语法)
	systemConfigs["mysql.appId"] = appId
	systemConfigs["cdb.appId"] = appId
	mysqlUrl, err := service3.GetCdbUrlByRegionAndType(region, "mysql")
	if err != nil {
		return controller.NULL, err
	}
	systemConfigs["mysql.cdbUrl"] = mysqlUrl
	systemConfigs["cdb.cdbUrl"] = mysqlUrl
	systemConfigs["mysql.netcenterUrl"] = netcenterUrl
	systemConfigs["cdb.netcenterUrl"] = netcenterUrl
	systemConfigs["mysql.vpcId"] = vpcId
	systemConfigs["mysql.subnetId"] = subnetId
	systemConfigs["cdb.vpcId"] = vpcId
	systemConfigs["cdb.subnetId"] = subnetId

	// PostgreSQL
	systemConfigs["postgresql.appId"] = appId
	pgUrl, err := service3.GetCdbUrlByRegionAndType(region, "postgresql")
	if err != nil {
		return controller.NULL, err
	}
	systemConfigs["postgresql.cdbUrl"] = pgUrl
	systemConfigs["postgresql.netcenterUrl"] = netcenterUrl
	systemConfigs["postgresql.vpcId"] = vpcId
	systemConfigs["postgresql.subnetId"] = subnetId

	// Snova
	scsUserUniqVpcId, err := service3.GetScsUserUniqVpcId()
	if err != nil {
		return controller.NULL, err
	}
	scsUserUniqSubnetId, err := service3.GetScsUserUniqSubnetId()
	if err != nil {
		return controller.NULL, err
	}

	systemConfigs["snova.appId"] = appId
	snovaUrl, err := service3.GetCdbUrlByRegionAndType(region, "snova")
	if err != nil {
		return controller.NULL, err
	}
	systemConfigs["snova.accountUrl"] = accountUrl
	systemConfigs["snova.cdbUrl"] = snovaUrl
	systemConfigs["snova.uniqVpcId"] = scsUserUniqVpcId
	systemConfigs["snova.uniqSubnetId"] = scsUserUniqSubnetId

	data, err := json.Marshal(systemConfigs)
	if err != nil {
		return controller.NULL, err
	}

	return string(data), nil
}

func getJobCurrentCosBucket(job *table.Job) (cosBucket string, err error) {
	jobConfigIds := make([]int64, 0)
	jobConfigIds = append(jobConfigIds, job.PublishedJobConfigId)
	jobConfigs, err := service7.ListJobConfigs(job.Id, jobConfigIds, []int64{}, nil)
	if err != nil {
		return "", err
	}
	jobConfig := jobConfigs[0]
	return service7.GetStateCOSBucketFromJobConfig(jobConfig)
}

func GetJobCurrentJobConfig(job *table.Job) (cosBucket *table5.JobConfig, err error) {
	jobConfigIds := make([]int64, 0)
	jobConfigIds = append(jobConfigIds, job.PublishedJobConfigId)
	jobConfigs, err := service7.ListJobConfigs(job.Id, jobConfigIds, []int64{}, nil)
	if err != nil {
		return nil, err
	}
	jobConfig := jobConfigs[0]
	return jobConfig, nil
}

func BuildStopJobParams(job *table.Job, stopType int8, requestId string) (string, error) {
	var jobInstance *table2.JobInstance
	var err error
	if job.Status == constants.JOB_STATUS_RUNNING {
		jobInstance, err = GetRunningJobInstanceByJobId(job.Id)
	} else {
		jobInstance, err = GetProgressJobInstanceByJobId(job.Id)
	}

	if err != nil {
		logger.Error(fmt.Sprintf("Failed to Get Running JobInstance, error:%+v", err))
		return "", err
	}
	jobConfig, err := GetJobCurrentJobConfig(job)
	if err != nil {
		logger.Error(fmt.Sprintf("Failed to Get JobConfig, error:%+v", err))
		return "", err
	}
	stopJobParams := &cluster_master_protocol.StopJobParams{}
	stopJobParams.FlinkVersion = common.GetFlinkVersion(job, jobConfig)
	stopJobParams.JobRuntimeId = jobInstance.Id
	stopJobParams.RequestId = requestId
	if stopType == constants.JOB_STOP_TYPE_STOP || stopType == constants.JOB_STOP_TYPE_SUCCEEDED {
		stopJobParams.IsPause = "false"
	} else {
		stopJobParams.IsPause = "true"
		stateCosBucket, err := getJobCurrentCosBucket(job)
		if err != nil {
			return "", err
		}
		savepointDirectory, err := service4.GetSavepointDirectory(job, stateCosBucket, jobInstance.RunningOrderId)
		if err != nil {
			logger.Error(fmt.Sprintf("Failed to get savepoint directory %s, error:%+v", job.SerialId, err))
			return "", err
		}
		stopJobParams.SavepointDirectory = savepointDirectory
	}

	isTKE, err := service6.ClusterIsEnableTKE(job.ClusterId)
	if err != nil {
		logger.Errorf("Failed to list cluster schedule type by clusterId %d errors:%+v", job.ClusterId, err)
		return controller.NULL, err
	}

	// 如果是TKE集群，增加DynamicProperties
	if isTKE {
		stopJobParams.SchedulerType = constants.SCHEDULER_TYPE_K8S
		properties := make(map[string]string, 0)
		// TKE需要指定集群ID
		properties[constants.FLINK_K8S_CLUSTER_ID] = fmt.Sprintf("%s-%d", job.SerialId, stopJobParams.JobRuntimeId)

		// 这里可能会有多余的-D参数，可以先忽略
		tkeConfMap, err := service3.GetConfigurationByType(constants.CONFIGURATION_TYPE_FLINK_TKE)
		if err != nil {
			logger.Errorf("GetDynamicProperties -> GetConfigurationByType(%d) error:%+v", constants.CONFIGURATION_TYPE_FLINK_TKE, err)
			return controller.NULL, err
		}

		updateProperties(properties, tkeConfMap)
		dynamicPropertiesBytes, _ := json.Marshal(properties)
		stopJobParams.DynamicProperties = string(dynamicPropertiesBytes)
	} else {
		stopJobParams.SchedulerType = constants.CLUSTER_SCHEDULER_TYPE_EMR
		yarnConfMap, err := service3.GetConfigurationByType(constants.CONFIGURATION_TYPE_FLINK_YARN)
		if err != nil {
			logger.Errorf("GetDynamicProperties -> GetConfigurationByType(%d) error:%+v", constants.CONFIGURATION_TYPE_FLINK_YARN, err)
			return controller.NULL, err
		}
		dynamicPropertiesBytes, _ := json.Marshal(yarnConfMap)
		stopJobParams.DynamicProperties = string(dynamicPropertiesBytes)
	}

	stopJobParamsBytes, err := json.Marshal(stopJobParams)
	if err != nil {
		logger.Errorf("Failed to json marshal struct: %+v, errors:%+v", stopJobParamsBytes, err)
		return controller.NULL, err
	}

	return string(stopJobParamsBytes), nil
}

// 从七彩石获取flink不同版本的镜像
func replaceFlinkImage(job *table.Job, jobConfig *table5.JobConfig, kv map[string]string) (err error) {

	if job.Region == "" {
		errMsg := fmt.Sprintf("job name=%s 's Region is nil in DB, please check it", job.Name)
		logger.Errorf(errMsg)
		return errorcode.InvalidParameterValueCode.NewWithInfo(errMsg, nil)
	}
	cc := image_registry.New(job.Region)

	flinkVersion := common.GetFlinkVersion(job, jobConfig)
	if flinkVersion == "" {
		errMsg := fmt.Sprintf("job name=%s 's FlinkVersion is nil in DB, please check it", job.Name)
		logger.Errorf(errMsg)
		return errorcode.InvalidParameterValueCode.NewWithInfo(errMsg, nil)
	}

	kubernetesContainerImage := ""
	if job.Type == constants.JOB_TYPE_PYFLINK {
		kubernetesContainerImage, err = cc.PyFlink(flinkVersion, jobConfig.PythonVersion)
	} else {
		kubernetesContainerImage, err = cc.Flink(flinkVersion)
	}
	if err != nil {
		errMsg := fmt.Sprintf("get flink tke image from rainbox error， do not replace "+
			"Configure_Center version[%s]，job's region [%s], flink version is [%s] error:%+v",
			kv[constants.FLINK_K8S_CONTAINER_IMAGE], job.Region, flinkVersion, err)
		logger.Errorf(errMsg)
		return errorcode.InvalidParameterValueCode.NewWithInfo(errMsg, nil)
	}
	if kubernetesContainerImage != "" {
		logger.Debugf("get flink tke image from rainbox successfully，"+
			"replace Configure_Center[%s]，job's region [%s], flink version [%s] with [%s]",
			kv[constants.FLINK_K8S_CONTAINER_IMAGE], job.Region, flinkVersion, kubernetesContainerImage)
		kv[constants.FLINK_K8S_CONTAINER_IMAGE] = kubernetesContainerImage
	}
	return nil
}

func SwitchActiveCluster(job *table.Job) (newJob *table.Job, err error) {
	cluster, err := service6.SelectActiveCluster(job.CreatorUin, job.ClusterGroupId, job.ClusterId, job.Type)
	if err != nil {
		logger.Errorf("SelectActiveCluster CreatorUin %s ClusterGroupId %d ClusterId %d error:%+v",
			job.CreatorUin, job.ClusterGroupId, job.ClusterId, err)
		return
	}

	job.ClusterId = cluster.Id
	UpdateJobClusterId(job.Id, job.ClusterId)
	newJob = job
	return
}

func UpdateJobClusterId(jobId int64, clusterId int64) (err error) {
	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		args := make([]interface{}, 0)
		args = append(args, clusterId)
		args = append(args, jobId)
		tx.ExecuteSql("UPDATE Job SET clusterId=? WHERE Id=?", args)
		return err
	}).Close()
	return err
}

func GetOldJobRuntimeId(jobId int64, jobStatus int8) (int64, error) {
	if jobStatus == constants.JOB_STATUS_RUNNING {
		oldJobRuntime, err := GetRunningJobInstance(jobId)
		if err != nil {
			return -1, err
		} else {
			return oldJobRuntime.Id, nil
		}
	} else {
		return -1, nil
	}
}

// GetOldJobInstanceIdAndJobConfigId 获取运行中作业实例ID，以及运行中作业实例的 jobConfigId
func GetOldJobInstanceIdAndJobConfigId(jobId int64, jobStatus int8) (jobInstanceId int64, jobConfigId int64, err error) {
	if jobStatus == constants.JOB_STATUS_RUNNING {
		oldJobRunningInstans, err := GetRunningJobInstance(jobId)
		if err != nil {
			return -1, -1, err
		} else {
			return oldJobRunningInstans.Id, oldJobRunningInstans.JobConfigId, nil
		}
	} else {
		return -1, -1, nil
	}
}

func MakeJobInstanceHistory(jobId int64, tx *dao.Transaction) error {
	sql := "UPDATE JobInstance SET Status=?, StopTime=? WHERE JobId=? AND Status IN (?, ?, ?)"
	args := make([]interface{}, 0)
	args = append(args, constants.JOB_INSTANCE_STATUS_HISTORY)
	args = append(args, util.GetCurrentTime())
	args = append(args, jobId)
	args = append(args, constants.JOB_INSTANCE_STATUS_RUNNING)
	args = append(args, constants.JOB_INSTANCE_STATUS_CREATE)
	args = append(args, constants.JOB_INSTANCE_STATUS_DEPLOYING)
	_, err := tx.ExecuteSql(sql, args).RowsAffected()
	if err != nil {
		return err
	}
	return nil
}

func UpdateJobInstanceParallelism(jobId int64, defaultParallelism int) error {
	txManager := service.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		sql := "UPDATE JobInstance SET DefaultParallelism = ? WHERE JobId=? AND Status IN (?)"
		args := make([]interface{}, 0)
		args = append(args, defaultParallelism)
		args = append(args, jobId)
		args = append(args, constants.JOB_INSTANCE_STATUS_RUNNING)
		_, err := tx.ExecuteSql(sql, args).RowsAffected()
		if err != nil {
			return err
		}
		return nil
	}).Close()
	return nil
}

func UpdateJobInstanceJobPlan(jobId int64, jobPlan string) error {
	txManager := service.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		sql := "UPDATE JobInstance SET FlinkJobPlan = ? WHERE JobId=? AND Status IN (?)"
		args := make([]interface{}, 0)
		args = append(args, jobPlan)
		args = append(args, jobId)
		args = append(args, constants.JOB_INSTANCE_STATUS_RUNNING)
		_, err := tx.ExecuteSql(sql, args).RowsAffected()
		if err != nil {
			return err
		}
		return nil
	}).Close()
	return nil
}

func MakeOldJobInstanceHistory(jobId int64, jobRuntimeId int64, tx *dao.Transaction) error {
	sql := "UPDATE JobInstance SET Status=?, StopTime=? WHERE Id=? AND JobId=?"
	args := make([]interface{}, 0)
	args = append(args, constants.JOB_INSTANCE_STATUS_HISTORY)
	args = append(args, util.GetCurrentTime())
	args = append(args, jobRuntimeId)
	args = append(args, jobId)
	rowsAffected, err := tx.ExecuteSql(sql, args).RowsAffected()
	if err != nil {
		return err
	} else if rowsAffected != 1 {
		return errors.New("error occurs when make old JobInstance history")
	}
	return nil
}

func GetJobInstanceRunningDuration(jobIds []string) (t map[string][]int64, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("GetJobInstanceRunningDuration panic, errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	jobInstanceMap, err := common.ListJobInstancesBySerialIds(jobIds)
	if err != nil {
		logger.Errorf("Failed to get job instances, with errors:%+v", err)
		return map[string][]int64{}, err
	}
	t = map[string][]int64{}
	timeLayout := "2006-01-02 15:04:05"
	loc, _ := time.LoadLocation("Local")
	for jobId, jobInstanceInfos := range jobInstanceMap {
		runningDuration := int64(0)
		totalRunningDuration := int64(0)
		instanceStartTime := int64(0)
		lastestJobInstanceInfo := jobInstanceInfos[0]
		if lastestJobInstanceInfo == nil {
			logger.Warningf("No job instance is running, with job id : %d", jobId)
			runningDurationInfo := make([]int64, 0)
			runningDurationInfo = append(runningDurationInfo, runningDuration)
			runningDurationInfo = append(runningDurationInfo, totalRunningDuration)
			runningDurationInfo = append(runningDurationInfo, instanceStartTime)
			t[jobId] = runningDurationInfo
			continue
		}
		if lastestJobInstanceInfo.Status != constants.JOB_INSTANCE_STATUS_RUNNING {
			runningDuration = 0
		} else {
			tmpInstanceCreateTime, _ := time.ParseInLocation(timeLayout, lastestJobInstanceInfo.StartTime, loc)
			if lastestJobInstanceInfo.StartTime == "0000-00-00 00:00:00" {
				runningDurationInfo := make([]int64, 0)
				runningDurationInfo = append(runningDurationInfo, runningDuration)
				runningDurationInfo = append(runningDurationInfo, totalRunningDuration)
				runningDurationInfo = append(runningDurationInfo, instanceStartTime)
				t[jobId] = runningDurationInfo
				continue
			} else {
				startTimeStamp := tmpInstanceCreateTime.Unix()
				instanceStartTime = startTimeStamp
				runningDuration = util.GetNowTimestamp()/1000 - startTimeStamp
			}
		}
		for i := 0; i < len(jobInstanceInfos); i++ {
			tmpInstanceCreateTime, _ := time.ParseInLocation(timeLayout, jobInstanceInfos[i].StartTime, loc)
			startTimeStamp := tmpInstanceCreateTime.Unix()
			if jobInstanceInfos[i].StartTime == "0000-00-00 00:00:00" {
				totalRunningDuration += 0
			} else {
				if jobInstanceInfos[i].StopTime == "" || jobInstanceInfos[i].StopTime == "0000-00-00 00:00:00" {
					currentTimestamp := time.Now().Unix()
					tmpDuration := currentTimestamp - startTimeStamp
					totalRunningDuration += tmpDuration
				} else {
					tmpInstanceStopTime, _ := time.ParseInLocation(timeLayout, jobInstanceInfos[i].StopTime, loc)
					endTimestamp := tmpInstanceStopTime.Unix()
					tmpDuration := endTimestamp - startTimeStamp
					totalRunningDuration += tmpDuration
				}
			}
		}
		runningDurationInfo := make([]int64, 0)
		runningDurationInfo = append(runningDurationInfo, runningDuration)
		runningDurationInfo = append(runningDurationInfo, totalRunningDuration)
		runningDurationInfo = append(runningDurationInfo, instanceStartTime)
		t[jobId] = runningDurationInfo
	}
	return t, nil
}

func GetLatestJobInstanceBySerialId(jobSerialId string) (t *table2.JobInstance, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("GetJobInstance panic , errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()

	sql := "select * from JobInstance where JobId in( select Id from Job where SerialId=? ) order by (CreateTime) desc limit 1"
	args := make([]interface{}, 0)
	args = append(args, jobSerialId)

	_, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Error("Failed to query with sql: ", sql, ", errors: ", err.Error())
		return nil, err
	}
	if len(data) == 0 {
		return nil, nil
	}
	if len(data) > 1 {
		logger.Error("logic error, not only one JobInstance query sql: ", sql)
		return nil, errors.New("login error, not only one JobInstance")
	}

	result := &table2.JobInstance{}
	_ = util.ScanMapIntoStruct(result, data[0])
	return result, nil
}

func GetJobInstance(id int64) (t *table2.JobInstance, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("GetJobInstance panic ,errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()

	sql := "SELECT * FROM JobInstance WHERE id=?"
	args := make([]interface{}, 0)
	args = append(args, id)

	_, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Error("Failed to query with sql:", sql, ", errors:", err.Error())
		return &table2.JobInstance{}, err
	}

	if len(data) == 0 {
		logger.Error("not find job Instance: ", id)
		return &table2.JobInstance{}, errors.New("not find Job Instance")
	}

	if len(data) > 1 {
		logger.Error("logic error, not only one JobInstance query sql:", sql)
		return &table2.JobInstance{}, errors.New("logic error, not only one JobInstance")
	}

	result := &table2.JobInstance{}
	_ = util.ScanMapIntoStruct(result, data[0])

	return result, nil
}

func GetLatestJobInstanceByJobId(jobId int64) (t *table2.JobInstance, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("GetJobInstance panic , errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()

	sql := "select * from JobInstance where JobId = ? order by (CreateTime) desc limit 1"
	args := make([]interface{}, 0)
	args = append(args, jobId)

	_, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Error("Failed to query with sql: ", sql, ", errors: ", err.Error())
		return nil, err
	}
	if len(data) == 0 {
		return nil, nil
	}
	if len(data) > 1 {
		logger.Error("logic error, not only one JobInstance query sql: ", sql)
		return nil, errors.New("login error, not only one JobInstance")
	}

	result := &table2.JobInstance{}
	_ = util.ScanMapIntoStruct(result, data[0])
	return result, nil
}

func IncJobRunningOrderId(jobId int64, tx *dao.Transaction) error {
	sql := "UPDATE Job SET JobRunningOrderId = JobRunningOrderId+1 WHERE Id=?"
	args := make([]interface{}, 0)
	args = append(args, jobId)
	rowsAffected, err := tx.ExecuteSql(sql, args).RowsAffected()
	if err != nil {
		return err
	} else if rowsAffected != 1 {
		return errors.New(fmt.Sprintf("Failed to update Job, rowsAffected=%d", rowsAffected))
	}
	return nil
}

func UpdateJobExpectedStatus(jobId int64, expectedStatus int, tx *dao.Transaction) error {
	sql := "UPDATE Job SET ExpectedStatus = ? WHERE Id=?"
	args := make([]interface{}, 0)
	args = append(args, expectedStatus)
	args = append(args, jobId)
	rowsAffected, err := tx.ExecuteSql(sql, args).RowsAffected()
	if err != nil {
		logger.Errorf("sql %s execute with error %v", sql, err)
		return err
	} else if rowsAffected != 1 {
		return errors.New(fmt.Sprintf("Failed to update Job, rowsAffected=%d", rowsAffected))
	}
	return nil
}

func GetUnExpectedStatusJobs() (jobs []*table.Job, err error) {
	sql := "SELECT * from Job WHERE (ExpectedStatus=? AND Status=?) OR (ExpectedStatus=? AND Status=?) OR (ExpectedStatus=? AND Status=?)"
	args := make([]interface{}, 0)
	args = append(args, constants.JOB_STATUS_RUNNING)
	args = append(args, constants.JOB_STATUS_STOPPED)

	args = append(args, constants.JOB_STATUS_STOPPED)
	args = append(args, constants.JOB_STATUS_RUNNING)

	args = append(args, constants.JOB_STATUS_RUNNING)
	args = append(args, constants.JOB_STATUS_PAUSED)
	_, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Error("Failed to query with sql: ", sql, ", errors: ", err.Error())
		return nil, err
	}
	jobs = make([]*table.Job, 0, len(data))
	if len(data) == 0 {
		return jobs, nil
	}
	for _, d := range data {
		job := &table.Job{}
		err = util.ScanMapIntoStruct(job, d)
		if err != nil {
			logger.Errorf("GetUnExpectedStatusJobs with error %v", err)
			return jobs, nil
		}
		jobs = append(jobs, job)
	}
	return jobs, nil
}

func GetJobByClusterGroupId(clusterGroupId int64, status int) (jobSet []*table.Job, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, "GetJobByClusterGroupId")
	sql := "SELECT * FROM Job WHERE ClusterGroupId = ? AND Status = ?"

	// 这里采用事务， 是为了 上层也需要把这个查询放在事务 里面这种场景
	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		var data map[int]map[string][]byte
		_, data, err = tx.QueryWithArgs(sql, clusterGroupId, status)
		if err != nil {
			return errorcode.NewStackError(errorcode.InternalErrorCode_DoSqlFailed, sql, err)
		}

		jobSet = make([]*table.Job, 0, len(data))
		for _, d := range data {
			job := &table.Job{}
			err = util.ScanMapIntoStruct(job, d)
			if err != nil {
				return errorcode.NewStackError(errorcode.InternalErrorCode_UnMarshalFailed, "ScanMapIntoStruct", err)
			}
			jobSet = append(jobSet, job)
		}

		return nil
	}).Close()
	return
}

func IsCurrentRegionEnabledDetailMetricReporter(region string) bool {
	regionMapping, err := configure_center.CC(region).FlowCC().MetricProviderCC().SupportRegion()
	if err != nil {
		logger.Errorf("can't deserialize region mapping")
		return false
	}
	_, ok := regionMapping[region]
	return ok
}
