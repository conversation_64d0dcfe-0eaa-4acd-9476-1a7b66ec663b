package service

import (
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/international"
	"time"

	"github.com/emirpasic/gods/sets/treeset"
	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service2 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/alert"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/billing"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job_instance"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config"
)

/**
 * 从 EventAlert 表、JobInstance 表获取近期作业的运行事件, 展示在前端的事件面板
 */
func DoDescribeJobEvents(req *model.DescribeJobEventsReq) (resp *model.DescribeJobEventsRsp, err error) {
	// TODO: 注意错误时 error 的产生, 以及性能影响

	//鉴权
	_, err = auth.InnerAuthJob(req.WorkSpaceId, req.IsSupOwner, req.JobId, req.AppId, req.SubAccountUin, req.Region, req.Action)
	if err != nil {
		logger.Errorf("%s: User owner uin %s is NOT authenticated, refuse to serve", req.RequestId, req.Uin)
		return
	}

	// 0. Print RequestId
	logger.Infof("%s: DoDescribeJobEvents API called by AppId %d", req.RequestId, req.AppId)

	// 1. 检查参数合法性
	err = checkParameterValidity(req)
	if err != nil {
		return nil, err
	}
	startTime := time.Unix(int64(req.StartTimestamp), 0).Format("2006-01-02 15:04:05")
	endTime := time.Unix(int64(req.EndTimestamp), 0).Format("2006-01-02 15:04:05")

	// 2.a. 如果请求里没有传入 RunningOrderIds, 则只做查询并返回所有符合条件的运行实例 ID（倒序排列）
	if len(req.RunningOrderIds) == 0 {
		// 新建一个 TreeMap 用于去重和排序
		runningOrderIds := treeset.NewWith(func(a, b interface{}) int {
			return compareRunningOrderIds(a.(uint64), b.(uint64))
		})

		// 查询 JobInstance 表在 (StartTimestamp、EndTimestamp) 期间的所有这个作业的 RunningOrderIds
		err = findRunningOrderIdsInJobInstance(req.JobId, startTime, endTime, runningOrderIds)
		if err != nil {
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}

		// 查询 EventAlert 表在 (StartTimestamp、EndTimestamp) 期间的所有这个作业 Event 列表, 找出 Trigger 和 Recovery 的 RunningOrderIds
		err = findRunningOrderIdsInEventAlert(req.JobId, startTime, endTime, runningOrderIds)
		if err != nil {
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}

		logger.Debugf("DescribeJobEvents response %+v", runningOrderIds.Values())
		runningIds := runningOrderIds.Values()
		int64RunningID := make([]int64, len(runningIds))

		job, err := service.WhetherJobExists(0, req.Region, req.JobId)
		if err != nil {
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "GetJob Error", err)
		}
		logger.Infof("find job %+v", job)

		for i, id := range runningIds {
			number := id.(uint64)
			int64RunningID[i] = int64(number)
		}

		jobInstances, err := service.ListJobInstanceByRunningOrderId(job.Id, int64RunningID)
		if err != nil {
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "GetJobInstance Error", err)
		}
		logger.Infof("find jobInstances total: %+v", len(jobInstances))

		jobConfigID := make(map[int64]int64, len(jobInstances))
		for _, jobInstance := range jobInstances {
			jobConfigID[jobInstance.RunningOrderId] = jobInstance.JobConfigId
		}
		logger.Infof("find jobConfigID : %+v", jobConfigID)

		ids := make([]int64, 0, len(jobConfigID))
		for _, id := range jobConfigID {
			ids = append(ids, id)
		}

		if len(ids) < 1 {
			return &model.DescribeJobEventsRsp{
				Events:          make([]model.JobEvent, 0),
				TotalCount:      0,
				RunningOrderIds: []interface{}{},
			}, nil
		}

		jobConfigEntries, err := service.GetJobConfigByIds(ids)
		if err != nil {
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "GetjobConfigByIDs Error", err)
		}
		logger.Infof("find jobConfigEntries total: %+v", len(jobConfigEntries))

		versions := make(map[int64]int16, len(jobConfigEntries))
		for _, jobConfigEntry := range jobConfigEntries {
			versions[jobConfigEntry.Id] = jobConfigEntry.VersionId
		}
		logger.Infof("find versions of jobConfigID : %+v", versions)

		matchVersions := make([]int16, 0, len(int64RunningID))
		for _, id := range int64RunningID {
			if version, ok := versions[jobConfigID[id]]; ok {
				matchVersions = append(matchVersions, version)
			} else {
				return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "Match Version Error",
					fmt.Errorf("cannot find version for job config id %d", jobConfigID[id]))
			}
		}
		logger.Infof("find matchVersions : %+v", matchVersions)
		return &model.DescribeJobEventsRsp{
			RunningOrderIds: runningOrderIds.Values(),
			Versions:        matchVersions,
		}, nil
	}

	// 2.b. 根据 SerialID、时间范围 (StartTimestamp、EndTimestamp)、类型 Types、RunningOrderId 列表，来返回所有符合条件的 Events
	// 如果没有传入 Type, 则放入所有类型 [1,2,3,4]
	if len(req.Types) == 0 {
		req.Types = append(req.Types,
			constants.EVENT_ENTITY_TYPE_JOB_START_OR_STOP,
			constants.EVENT_ENTITY_TYPE_JOB_FAIL_OR_RECOVER,
			constants.EVENT_ENTITY_TYPE_CHECKPOINT_FAIL_OR_RECOVER,
			constants.EVENT_ENTITY_TYPE_JOB_EXCEPTIONS,
		)
	}
	logger.Debugf("international - Language: %+v", req.Language)

	// 按时间戳倒序排序的 TreeMap
	jobEvents := make([]model.JobEvent, 0)
	events, err := extractEventsFromJobInstance(req, startTime, endTime)
	if err != nil {
		return nil, err
	}
	jobEvents = append(jobEvents, events...)

	events, err = extractEventsFromEventAlert(req, startTime, endTime)
	if err != nil {
		return nil, err
	}
	jobEvents = append(jobEvents, events...)

	sort.SliceStable(jobEvents, func(i, j int) bool {
		return jobEvents[i].Timestamp > jobEvents[j].Timestamp
	})

	// 统计接口耗时
	logger.Infof("%s: DoDescribeJobEvents API finished for AppId %d", req.RequestId, req.AppId)

	return &model.DescribeJobEventsRsp{
		Events:          jobEvents,
		TotalCount:      len(jobEvents),
		RunningOrderIds: []interface{}{},
	}, nil
}

// 获取类型 = 2、3、4 (作业运行失败、快照失败、运行时异常）的事件, 按条件查询 EventAlert 表, 遍历加入事件（>1 的可以合并）
func extractEventsFromEventAlert(req *model.DescribeJobEventsReq, startTime string, endTime string) ([]model.JobEvent, error) {
	events := make([]model.JobEvent, 0)

	// TODO: 注意错误时 error 的产生, 以及性能影响. 注意所有代码的 panic
	// TODO: 可扩展性

	// 只处理类型非 1 的 types
	typesToProcess := make([]string, 0)

	for _, item := range req.Types {
		if item != constants.EVENT_ENTITY_TYPE_JOB_START_OR_STOP {
			typesToProcess = append(typesToProcess, item)
		}
	}

	if len(typesToProcess) == 0 {
		return events, nil
	}

	sql := "SELECT * FROM EventAlert WHERE jobId = ? " +
		"AND (createTime BETWEEN ? AND ?) AND type IN (" + generateSQLPlaceholders(len(typesToProcess)) +
		") AND runningOrderIdOnTrigger IN (" + generateSQLPlaceholders(len(req.RunningOrderIds)) + ")" +
		" UNION " +
		"SELECT * FROM EventAlert WHERE jobId = ? " +
		"AND (recoverTime BETWEEN ? AND ?) AND type IN (" + generateSQLPlaceholders(len(typesToProcess)) +
		") AND runningOrderIdOnRecovery IN (" + generateSQLPlaceholders(len(req.RunningOrderIds)) + ")"

	args := make([]interface{}, 0)
	args = append(args, req.JobId)
	args = append(args, startTime)
	args = append(args, endTime)
	for _, elem := range typesToProcess {
		args = append(args, elem)
	}
	for _, elem := range req.RunningOrderIds {
		args = append(args, elem)
	}

	args = append(args, req.JobId)
	args = append(args, startTime)
	args = append(args, endTime)
	for _, elem := range typesToProcess {
		args = append(args, elem)
	}
	for _, elem := range req.RunningOrderIds {
		args = append(args, elem)
	}

	txManager := service2.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		_, data, err := tx.Query(sql, args)
		if err != nil {
			logger.Errorf("Query sql %s, args: %+v, error: %+v", sql, args, err)
			return err
		}

		logger.Debugf("SQL: %s, args: %+v, result length: %d", sql, args, len(data))

		for i := 0; i < len(data); i++ {
			eventEntity := alert.EventAlertEntity{}
			_ = util.ScanMapIntoStruct(&eventEntity, data[i])

			// 处理触发事件：针对有状态可恢复的事件类型（2、3）以及无需恢复的事件类型（4）
			if withinTimeRange(eventEntity.CreateTime, startTime, endTime) &&
				eventRunningOrderIdIn(eventEntity.RunningOrderIdOnTrigger, req.RunningOrderIds) {

				createTime, err := time.ParseInLocation(billing.StandardTimestampFormatString, eventEntity.CreateTime, time.Local)
				if err != nil {
					logger.Errorf("Failed to parse create timestamp %s because %+v. Skip this event",
						eventEntity.CreateTime, err)
				} else {
					var jobEvent *model.JobEvent
					if eventEntity.Type == constants.EVENT_ENTITY_TYPE_JOB_FAIL_OR_RECOVER {
						des := "发生作业失败"
						mess := eventEntity.Message
						solutionlink := international.TranslateStaticEntries(req.Language, eventEntity.Name+constants.EVENTALERT_SOLUTION_SUFFIX)
						if err != nil {
							logger.Errorf("Failed to translate 作业启动 to %s because %+v", req.Language, err)
							return err
						}

						jobEvent = &model.JobEvent{
							Type:           constants.EVENT_TYPE_JOB_FAIL_TRIGGER,
							Description:    des,
							Timestamp:      createTime.Unix(),
							RunningOrderId: int64(eventEntity.RunningOrderIdOnTrigger),
							Message:        mess,
							SolutionLink:   solutionlink,
						}
					} else if eventEntity.Type == constants.EVENT_ENTITY_TYPE_CHECKPOINT_FAIL_OR_RECOVER {
						des := "发生快照失败"
						mess := eventEntity.Message
						solutionlink := international.TranslateStaticEntries(req.Language, eventEntity.Name+constants.EVENTALERT_SOLUTION_SUFFIX)
						if err != nil {
							logger.Errorf("Failed to translate 作业启动 to %s because %+v", req.Language, err)
							return err
						}
						jobEvent = &model.JobEvent{
							Type:           constants.EVENT_TYPE_CHECKPOINT_FAIL_TRIGGER,
							Description:    des,
							Timestamp:      createTime.Unix(),
							RunningOrderId: int64(eventEntity.RunningOrderIdOnTrigger),
							Message:        mess,
							SolutionLink:   solutionlink,
						}
					} else if eventEntity.Type == constants.EVENT_ENTITY_TYPE_JOB_EXCEPTIONS {
						jobEvent, err = getEventFieldsFromRainbow(eventEntity, createTime.Unix())
						if err != nil {
							logger.Errorf("Failed to get event for %+v from rainbow because %+v", eventEntity, err)
						}
						solutionlink := international.TranslateStaticEntries(req.Language, eventEntity.Name+constants.EVENTALERT_SOLUTION_SUFFIX)
						jobEvent.SolutionLink = solutionlink
					} else {
						logger.Errorf("Unknown recoverable barad event type %s, skip", eventEntity.Type)
					}

					if jobEvent != nil {
						logger.Debugf("Add event %+v", jobEvent)
						events = append(events, *jobEvent)
					}
				}
			}

			// 处理恢复事件：只针对有状态可恢复的事件类型（2、3）
			if withinTimeRange(eventEntity.RecoverTime, startTime, endTime) &&
				eventRunningOrderIdIn(eventEntity.RunningOrderIdOnRecovery, req.RunningOrderIds) {

				recoverTime, err := time.ParseInLocation(billing.StandardTimestampFormatString, eventEntity.RecoverTime, time.Local)
				if err != nil {
					logger.Errorf("Failed to parse recover timestamp %s because %+v. Skip this event",
						eventEntity.RecoverTime, err)
				} else {
					var jobEvent *model.JobEvent
					if eventEntity.Type == constants.EVENT_ENTITY_TYPE_JOB_FAIL_OR_RECOVER {
						des := "作业失败已恢复"
						jobEvent = &model.JobEvent{
							Type:           constants.EVENT_TYPE_JOB_FAIL_RECOVER,
							Description:    des,
							Timestamp:      recoverTime.Unix(),
							RunningOrderId: int64(eventEntity.RunningOrderIdOnRecovery),
						}
					} else if eventEntity.Type == constants.EVENT_ENTITY_TYPE_CHECKPOINT_FAIL_OR_RECOVER {
						des := "快照失败已恢复"
						jobEvent = &model.JobEvent{
							Type:           constants.EVENT_TYPE_CHECKPOINT_FAIL_RECOVER,
							Description:    des,
							Timestamp:      recoverTime.Unix(),
							RunningOrderId: int64(eventEntity.RunningOrderIdOnRecovery),
						}
					} else {
						logger.Errorf("Unknown recoverable barad event type %s, skip", eventEntity.Type)
					}

					if jobEvent != nil {
						events = append(events, *jobEvent)
					}
				}
			}
		}
		return nil
	}).Close()

	return events, nil
}

// 检查事件的 RunningOrderId 是否在请求过滤条件中
// EventAlert 表的一条事件可能包含多个不同 RunningOrderId, UNION 时会都查到, 因此需要再次筛选
func eventRunningOrderIdIn(runningOrderId int, ids []uint64) bool {
	for _, id := range ids {
		if runningOrderId == int(id) {
			return true
		}
	}

	return false
}

// 按个数生成 SQL 参数中的 ? 占位符
func generateSQLPlaceholders(numQuestionMarks int) string {
	builder := strings.Builder{}
	for i := 0; i < numQuestionMarks; i++ {
		builder.WriteString("?")
		if i != numQuestionMarks-1 {
			builder.WriteString(",")
		}
	}
	return builder.String()
}

// 根据事件类型, 访问七彩石, 补充中文描述, 以及解决方案链接
// FIXME: 校验和增加 SolutionLink
// TODO: 增加新事件以后的录入文档流程 iWiki
func getEventFieldsFromRainbow(entity alert.EventAlertEntity, createTime int64) (*model.JobEvent, error) {
	value, err := config.GetRainbowConfiguration("Event", "alert_event_mapping.json")
	if err != nil {
		logger.Errorf("Failed to get alert_event_mapping.json from Event in Rainbow because %+v", err)
		return nil, err
	}

	alertMappings := make(map[string]alert.AlertMapping)
	err = json.Unmarshal([]byte(value), &alertMappings)
	if err != nil {
		logger.Errorf("Failed to unmarshal alert_event_mapping.json from Event in Rainbow because %+v", err)
		return nil, err
	}

	if alertMapping, ok := alertMappings[entity.Name]; ok {
		logger.Debugf("Event alert mapping from rainbow for event %s is %+v", entity.Name, alertMapping)
		return &model.JobEvent{
			Type:           alertMapping.Type,
			Description:    alertMapping.Description,
			Timestamp:      createTime,
			RunningOrderId: int64(entity.RunningOrderIdOnTrigger),
			Message:        entity.Message,
			SolutionLink:   alertMapping.SolutionLink,
		}, nil
	} else {
		return nil, errors.New("cannot find event mapping in rainbow for event " + entity.Name)
	}
}

// 从 JobInstance 表提取符合条件的作业启动 & 停止事件
// 获取类型 = 1 (启动/停止) 的事件: 按条件查询 JobInstance 表, 遍历加入事件
func extractEventsFromJobInstance(req *model.DescribeJobEventsReq, startTime string, endTime string) ([]model.JobEvent, error) {
	events := make([]model.JobEvent, 0)

	// 只处理类型为 1 的 types
	needContinue := false
	for _, item := range req.Types {
		if item == constants.EVENT_ENTITY_TYPE_JOB_START_OR_STOP {
			needContinue = true
		}
	}
	if !needContinue {
		return events, nil
	}

	// 寻找给定时间范围内启动或停止的该作业实例
	sql := "SELECT JobInstance.* FROM JobInstance, Job WHERE JobInstance.JobId = Job.Id " +
		"AND Job.SerialId = ? AND ((JobInstance.CreateTime BETWEEN ? AND ?) OR (JobInstance.StopTime BETWEEN ? AND ?)) " +
		"AND JobInstance.RunningOrderId IN (" + generateSQLPlaceholders(len(req.RunningOrderIds)) + ") " +
		"ORDER BY JobInstance.CreateTime DESC" // 必须倒序排列, 这样新的 JobInstance 的启动事件一定会在旧的 JobInstance 的停止事件前, 且稳定排序后相对顺序不变

	args := make([]interface{}, 0)
	args = append(args, req.JobId)
	args = append(args, startTime)
	args = append(args, endTime)
	args = append(args, startTime)
	args = append(args, endTime)
	for _, elem := range req.RunningOrderIds {
		args = append(args, elem)
	}

	txManager := service2.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		_, data, err := tx.Query(sql, args)
		if err != nil {
			logger.Errorf("Query sql %s, args: %+v, error: %+v", sql, args, err)
			return err
		}

		logger.Debugf("SQL: %s, args: %+v, result length: %d", sql, args, len(data))

		for i := 0; i < len(data); i++ {
			jobInstance := table2.JobInstance{}
			_ = util.ScanMapIntoStruct(&jobInstance, data[i])

			// 如果启动时间在范围内, 则生成作业启动事件
			if withinTimeRange(jobInstance.CreateTime, startTime, endTime) {
				launchTime, err := time.ParseInLocation(billing.StandardTimestampFormatString, jobInstance.CreateTime, time.Local)
				if err != nil {
					logger.Errorf("Failed to parse create timestamp %s because %+v. Skip this event",
						jobInstance.CreateTime, err)
				} else {
					jobConfig, err := service2.GetJobConfigById(jobInstance.JobConfigId)
					if err != nil {
						logger.Errorf("Failed to get job config by id %d because %+v", jobInstance.JobConfigId, err)
					} else {
						des := "作业启动"
						version := "作业版本:"
						//logger.Debugf("international - Language: %+v", req.Language)
						//if req.AccountArea == "1" {
						//	des = international.Translate(req.Language, des)
						//	version = international.Translate(req.Language, version)
						//}
						jobEvent := model.JobEvent{
							Type:           constants.EVENT_TYPE_JOB_START,
							Description:    des,
							Timestamp:      launchTime.Unix(),
							RunningOrderId: jobInstance.RunningOrderId,
							Message:        version + " V" + strconv.Itoa(int(jobConfig.VersionId)),
						}

						events = append(events, jobEvent)
						logger.Debugf("Add EVENT_JOB_START %+v", jobEvent)
					}
				}
			}

			// 如果停止时间在范围内, 则生成作业停止事件
			if withinTimeRange(jobInstance.StopTime, startTime, endTime) {
				stopTime, err := time.ParseInLocation(billing.StandardTimestampFormatString, jobInstance.StopTime, time.Local)
				if err != nil {
					logger.Errorf("Failed to parse stop timestamp %s because %+v. Skip this event",
						jobInstance.StopTime, err)
				} else {
					des := "作业停止"
					//if req.AccountArea == "1" {
					//	des = international.Translate(req.Language, des)
					//}
					jobEvent := model.JobEvent{
						Type:           constants.EVENT_TYPE_JOB_STOP,
						Description:    des,
						Timestamp:      stopTime.Unix(),
						RunningOrderId: jobInstance.RunningOrderId,
					}
					events = append(events, jobEvent)
					logger.Debugf("Add EVENT_JOB_STOP %+v", jobEvent)
				}
			}
		}
		return nil
	}).Close()

	return events, nil
}

func findRunningOrderIdsInEventAlert(jobId string, startTime string, endTime string, runningOrderIds *treeset.Set) error {
	sql := "SELECT DISTINCT runningOrderIdOnTrigger AS RunningOrderId FROM EventAlert WHERE jobId = ? " +
		"AND (createTime BETWEEN ? AND ?)" +
		" UNION " +
		"SELECT DISTINCT runningOrderIdOnRecovery AS RunningOrderId FROM EventAlert WHERE jobId = ? " +
		"AND (recoverTime BETWEEN ? AND ?)"

	args := make([]interface{}, 0)
	args = append(args, jobId)
	args = append(args, startTime)
	args = append(args, endTime)
	args = append(args, jobId)
	args = append(args, startTime)
	args = append(args, endTime)

	txManager := service2.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		_, data, err := tx.Query(sql, args)
		if err != nil {
			logger.Errorf("Query sql %s, args: %+v, error: %+v", sql, args, err)
			return err
		}

		logger.Debugf("SQL: %s, args: %+v, result length: %d", sql, args, len(data))

		for i := 0; i < len(data); i++ {
			runningOrderIdString := string(data[i]["RunningOrderId"])
			if runningOrderIdString == "" || runningOrderIdString == "0" {
				logger.Debugf("Illegal runningOrderIdString %s", runningOrderIdString)
				continue
			}
			runningOrderId, err := strconv.Atoi(runningOrderIdString)
			if err != nil {
				logger.Errorf("Failed to convert RunningOrderId %s to int because %+v", runningOrderIdString, err)
				return err
			}
			logger.Debugf("Found new runningOrderId %d in EventAlert, add to runningOrderIds", runningOrderId)
			runningOrderIds.Add(uint64(runningOrderId))
		}
		return nil
	}).Close()

	return nil
}

func findRunningOrderIdsInJobInstance(jobId string, startTime string, endTime string, runningOrderIds *treeset.Set) error {
	sql := "SELECT DISTINCT RunningOrderId FROM JobInstance, Job WHERE JobInstance.JobId = Job.Id " +
		"AND Job.SerialId = ? AND ((JobInstance.CreateTime BETWEEN ? AND ?) OR (JobInstance.StopTime BETWEEN ? AND ?))"

	args := make([]interface{}, 0)
	args = append(args, jobId)
	args = append(args, startTime)
	args = append(args, endTime)
	args = append(args, startTime)
	args = append(args, endTime)

	txManager := service2.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		_, data, err := tx.Query(sql, args)
		if err != nil {
			logger.Errorf("Query sql %s, args: %+v, error: %+v", sql, args, err)
			return err
		}

		logger.Debugf("SQL: %s, args: %+v, result length: %d", sql, args, len(data))

		for i := 0; i < len(data); i++ {
			runningOrderIdString := string(data[i]["RunningOrderId"])
			if runningOrderIdString == "" || runningOrderIdString == "0" {
				logger.Debugf("Illegal runningOrderIdString %s", runningOrderIdString)
				continue
			}
			runningOrderId, err := strconv.Atoi(runningOrderIdString)
			if err != nil {
				logger.Errorf("Failed to convert RunningOrderId %s to int because %+v", runningOrderIdString, err)
				return err
			}
			logger.Debugf("Found new runningOrderId %d in JobInstance, add to runningOrderIds", runningOrderId)
			runningOrderIds.Add(uint64(runningOrderId))
		}
		return nil
	}).Close()

	return nil
}

func checkParameterValidity(req *model.DescribeJobEventsReq) error {
	// 例如作业 JobId 是不是他自己的
	job, err := GetJobBySerialId(req.JobId)
	if err != nil {
		return errorcode.NewStackError(errorcode.InvalidParameterValue_JobIdValueError, "", err)
	}
	if int64(job.AppId) != req.AppId {
		return errorcode.NewStackError(errorcode.InvalidParameter_InvalidAppId, "", nil)
	}

	// 时间范围区间是否大于 0, 是否超过七天, 距今是否差距太大（最多看过去 90 天的）
	currentTime := time.Now()
	startTime := time.Unix(int64(req.StartTimestamp), 0)
	endTime := time.Unix(int64(req.EndTimestamp), 0)
	if startTime.After(currentTime) || endTime.Before(startTime) ||
		endTime.Sub(startTime) > 7*24*time.Hour || currentTime.Sub(startTime) > 90*24*time.Hour {
		logger.Errorf("Invalid start time %+v ~ end time %+v", startTime, endTime)
		return errorcode.NewStackError(errorcode.InvalidParameterValue_Timestamp, "", nil)
	}

	return nil
}

// 判断某个时间戳是否在指定范围内
func withinTimeRange(someTime string, startTime string, endTime string) bool {
	if someTime == "0000-00-00 00:00:00" {
		return false
	}

	someTimeVal, err := time.ParseInLocation(billing.StandardTimestampFormatString, someTime, time.Local)
	if err != nil {
		logger.Errorf("Failed to parse time string %s because %+v", someTime, err)
		return false
	}

	startTimeVal, err := time.ParseInLocation(billing.StandardTimestampFormatString, startTime, time.Local)
	if err != nil {
		logger.Errorf("Failed to parse time string %s because %+v", startTime, err)
		return false
	}

	endTimeVal, err := time.ParseInLocation(billing.StandardTimestampFormatString, endTime, time.Local)
	if err != nil {
		logger.Errorf("Failed to parse time string %s because %+v", endTime, err)
		return false
	}

	return (someTimeVal.Equal(startTimeVal) || someTimeVal.After(startTimeVal)) && someTimeVal.Before(endTimeVal)
}

// 倒序比较 RunningOrderId
func compareRunningOrderIds(a uint64, b uint64) int {
	if a > b {
		return -1
	} else if a < b {
		return 1
	} else {
		return 0
	}
}

func GetJobBySerialId(serialId string) (*table.Job, error) {
	sql := "SELECT * FROM Job WHERE SerialId=? AND Status<>?"
	args := make([]interface{}, 0)
	args = append(args, serialId)
	args = append(args, constants.JOB_STATUS_DELETE)

	txManager := service2.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Exception occurs when query JobInstance from db, with errors:%+v", err)
		return nil, err
	}

	job := &table.Job{}

	if len(data) != 1 {
		return nil, errors.New(fmt.Sprintf("Number of job with id %d is not 1 but %d", job.Id, len(data)))
	}

	err = util.ScanMapIntoStruct(job, data[0])
	if err != nil {
		logger.Errorf("Failed to convert bytes into table.Job, with errors:%+v", err)
		return nil, err
	}

	return job, nil
}
