package service

import (
	"encoding/hex"
	"sort"
	"strconv"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service2 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job"
	table1 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job"
	table3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job_config"
	commonSerivce "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service4 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/configuration_center"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job_config"
)

type Tree struct {
	FolderName string
	ParentId   string
	FoldId     string
}

/*
*
@Auther: felixwluo（罗稳）
*/
func DoDescribeTreeJobs(req *model.DescribeTreeJobsReq) (string, string, *model.DescribeTreeJobsRsp) {
	requestId := req.RequestId

	code, msg := CheckFilters(req)
	if code != controller.OK {
		logger.Errorf("%s: Failed to describe tree jobs (CheckFilters), code: %s, msg: %s", requestId, code, msg)
		return code, msg, nil
	}

	// 鉴权 (子账号，只能看见自己空间内绑定的集群，超管可以看到所有)
	itemSpcIds, err := auth.ExtractSubUinAuthItemSpaceIdSet(req.WorkSpaceId, req.IsSupOwner, req.AppId, req.SubAccountUin, req.Uin, req.Region)
	if err != nil {
		logger.Errorf("%s: ExtractSubUinAuthClustersSet : Obtain  clusterGroups that has permissions   error: %+v", req.RequestId, err)
		errCode := errorcode.GetCode(err)
		if errCode == errorcode.InternalErrorCode {
			errCode = errorcode.FailedOperationCode
		}
		return errCode.GetCodeStr(), errCode.GetCodeDesc(), nil
	}

	sqlKeyword := getSqlKeywordWithReq(req)

	// 制作一个默认根节点
	parentNode, err := buildInitTree(req, itemSpcIds, sqlKeyword)
	if err != nil {
		return errorcode.FailedOperationCode.GetCodeStr(), controller.NULL, nil
	}

	var ranTreesJobs []*model.DescribeTreeJobsRsp

	ranTrees, err := makeRanTree(req, itemSpcIds, sqlKeyword)
	if err != nil {
		return errorcode.FailedOperationCode.GetCodeStr(), controller.NULL, nil
	}
	ranTrees = append(ranTrees, parentNode)
	ranTreesJobs = makeTree(ranTrees, parentNode)

	return controller.OK, controller.NULL, ranTreesJobs[len(ranTreesJobs)-1]

}

func CheckFilters(req *model.DescribeTreeJobsReq) (string, string) {
	for i := 0; i < len(req.Filters); i++ {
		filter := req.Filters[i]
		if filter.Name != "Zone" && filter.Name != "JobType" && filter.Name != "JobStatus" && filter.Name != constants.SQL_KEYWORD {
			return controller.InvalidParameterValue, "Unknown filter.name, can only be one of `Zone` or `JobType` or `JobStatus`"
		}
		if len(req.Filters[i].Values) > 6 {
			return controller.InvalidParameterValue, "Filter.Values should not contain more than 6 elements"
		}
	}

	return controller.OK, controller.NULL
}

// 做随机树
func makeRanTree(req *model.DescribeTreeJobsReq, itemSpaceId []int64, sqlKeyword []string) (trees []*model.DescribeTreeJobsRsp, err error) {
	appId := int32(req.AppId)
	txManager := service2.GetTxManager()
	cond := dao.NewCondition()

	sql := "select t.* from Tree t"
	if appId > 0 {
		cond.Eq("t.AppId", appId)
	}
	if len(req.Region) > 0 {
		cond.Eq("t.Region", req.Region)
	}
	if len(itemSpaceId) > 0 {
		itemSpaceId := UniqueSliceInt64(itemSpaceId)
		cond.In("t.ItemSpaceId", itemSpaceId)
	}

	where, args := cond.GetWhere()
	sql += where
	sql += " ORDER BY t.CreateTime DESC "

	_, foldersData, err := txManager.GetQueryTemplate().DoQuery(sql, args)

	if err != nil {
		logger.Errorf("%s: Failed to describe_tree_jobs when Building,error: %+v", req.RequestId, err)
		return nil, err
	}

	var ts []Tree

	var sortKeys []int
	for k, _ := range foldersData {
		sortKeys = append(sortKeys, k)
	}
	sort.Ints(sortKeys)
	for _, k := range sortKeys {
		tree := Tree{
			FolderName: string(foldersData[k]["FolderName"]),
			ParentId:   string(foldersData[k]["ParentId"]),
			FoldId:     string(foldersData[k]["FolderId"]),
		}
		ts = append(ts, tree)
	}

	foldMap := make(map[string]Tree)
	for _, tf := range ts {
		foldMap[tf.FoldId] = tf
	}

	listJobQuery := model.ListJobQuery{
		AppId:        int32(req.AppId),
		Regions:      []string{req.Region},
		ItemSpaceIds: itemSpaceId,
	}
	var jobs []*table.JobInfoForSqlCode
	if sqlKeyword == nil || len(sqlKeyword) == 0 {
		jobs, err = ListJob(&listJobQuery)
		if err != nil {
			logger.Errorf("Failed to listJobQuery:%#v, error:%+v", listJobQuery, err)
			return nil, err
		}
	} else {
		jobs, err = ListJobByKeywords(&listJobQuery, sqlKeyword)
		if err != nil {
			logger.Errorf("Failed to listJobQuery:%#v,  sqlKeyword: %#v, error:%+v", listJobQuery, sqlKeyword, err)
			return nil, err
		}
	}

	jobConfigIds := make([]int64, 0, 0)
	jobFoldMap := make(map[string][]table.JobInfoForSqlCode)
	jobMap := make(map[int64]table.JobInfoForSqlCode)

	clusterIds := make([]int64, 0, 0)

	sortJobFoldMap := make([]string, 0)
	for _, j := range jobs {

		if jobFoldMap[j.FolderId] != nil {
			jobFoldMap[j.FolderId] = append(jobFoldMap[j.FolderId], *j)
		} else {
			jobFoldMap[j.FolderId] = []table.JobInfoForSqlCode{*j}
		}
		jobMap[j.Id] = *j
		jobConfigIds = append(jobConfigIds, j.PublishedJobConfigId)
		if !commonSerivce.IsContainStr(sortJobFoldMap, j.FolderId) {
			sortJobFoldMap = append(sortJobFoldMap, j.FolderId)
		}
		if ok, _ := commonSerivce.Contain(j.ClusterId, clusterIds); !ok {
			clusterIds = append(clusterIds, j.ClusterId)
		}
	}

	clusters, err := ListClustersByIds(clusterIds)
	if err != nil {
		logger.Errorf("Failed to get ListClustersByIds, with errors:%+v", err)
		return nil, err
	}
	clusterIdMap := make(map[int64]*table1.Cluster)
	for _, cluster := range clusters {
		clusterIdMap[cluster.Id] = cluster
	}

	jobIdClusterMap := make(map[int64]*table1.Cluster)
	for _, j := range jobs {
		if cluster, ok := clusterIdMap[j.ClusterId]; ok {
			jobIdClusterMap[j.Id] = cluster
		}
	}

	jobConfigs, err := service3.ListJobConfigsByIds(jobConfigIds)
	if err != nil {
		return nil, err
	}
	jobConfigMap := make(map[int64]*table3.JobConfig)

	for _, config := range jobConfigs {
		jobConfigMap[config.JobId] = config
	}

	includeFolderIdsMap := make(map[string]string)

	// 获取所有job的运行info
	jobss := make([]*table.Job, 0, 0)
	for _, j := range jobs {
		jobss = append(jobss, convertJobInfoForSqlCodeToJob(j))
	}
	jobRunningInfos, err := commonSerivce.GetClusterEachJobCpuAndMem(jobss)
	if err != nil {
		logger.Errorf("Failed to get cluster different job cpu and mem, with error:%+v", err)
		return nil, err
	}

	for _, folderId := range sortJobFoldMap {
		js := jobFoldMap[folderId]
		includeFolderIdsMap[folderId] = "1"
		folder := foldMap[folderId]
		parentId := folder.ParentId
		folderName := folder.FolderName

		var (
			tmRunningCuNum int16
			jmRunningCuNum int8
			jmSpec         float32
			tmSpec         float32
		)

		jobSets := []*model.TreeJobSets{}

		for _, jss := range js {
			job := &model.TreeJobSets{}
			id := jss.Id
			job.JobId = jss.SerialId
			scalingType, err := checkJobIsScaling(jss.SerialId)
			if err != nil {
				return nil, err
			}
			job.ScalingType = scalingType
			if jobConfig, ok := jobConfigMap[id]; !ok {
				logger.Debugf("No job config found for job, with job serialId:%s", job.JobId)
			} else {
				//jmc, jmm, tmc, tmm, err := commonSerivce.GetJobRunningCPUAndMem(&jss)
				//if err != nil {
				//	logger.Errorf("Failed to get job running cpu and mem, with error:%+v", err)
				//	return nil, err
				//}
				jmSpec = jobConfig.JmCuSpec
				tmSpec = jobConfig.TmCuSpec
			}

			job.Name = jss.Name
			job.JobType = jss.Type
			tmRunningCuNum = jss.TmRunningCuNum
			jmRunningCuNum = jss.JmRunningCuNum
			_, ok := jobRunningInfos[jss.Id]
			if ok {
				job.RunningCpu = jobRunningInfos[jss.Id].TaskManagerCpu + jobRunningInfos[jss.Id].JobManagerCpu
				job.RunningMem = jobRunningInfos[jss.Id].TaskManagerMem + jobRunningInfos[jss.Id].JobManagerMem
			}
			job.RunningCu = jmSpec*float32(jmRunningCuNum) + tmSpec*float32(tmRunningCuNum)
			//if jmCpu != 0 {
			//	job.RunningCpu = jmCpu*float32(jmRunningCuNum) + tmCpu*float32(tmRunningCuNum)
			//	job.RunningMem = jmMem*float32(jmRunningCuNum) + tmMem*float32(tmRunningCuNum)
			//} else {
			//	job.RunningCpu = job.RunningCu
			//	if cluster, ok := jobIdClusterMap[id]; ok {
			//		job.RunningMem = job.RunningCu * float32(cluster.MemRatio)
			//	} else {
			//		job.RunningMem = job.RunningCu * float32(constants.CU_MEM_4GB)
			//	}
			//}
			job.RunningCpu = commonSerivce.GetFloat2Dot(job.RunningCpu)
			job.RunningMem = commonSerivce.GetFloat2Dot(job.RunningMem)

			job.Status = jss.Status
			job.DecodeSqlCode = jss.DecodeSqlCode
			jobSets = append(jobSets, job)
		}

		tree := &model.DescribeTreeJobsRsp{
			ParentId:  parentId,
			Id:        folderId,
			Name:      folderName,
			JobSet:    jobSets,
			RequestId: req.RequestId,
		}
		trees = append(trees, tree)
	}

	for _, notFoundJob := range ts {
		if includeFolderIdsMap[notFoundJob.FoldId] == "" {
			tree := &model.DescribeTreeJobsRsp{
				ParentId:  notFoundJob.ParentId,
				Id:        notFoundJob.FoldId,
				Name:      notFoundJob.FolderName,
				JobSet:    []*model.TreeJobSets{},
				RequestId: req.RequestId,
			}
			trees = append(trees, tree)
		}
	}
	return trees, err
}

func convertJobInfoForSqlCodeToJob(jobInfoForSqlCode *table.JobInfoForSqlCode) *table.Job {
	job := &table.Job{}
	job.Id = jobInfoForSqlCode.Id
	job.SerialId = jobInfoForSqlCode.SerialId
	job.Name = jobInfoForSqlCode.Name
	job.AppId = jobInfoForSqlCode.AppId
	job.OwnerUin = jobInfoForSqlCode.OwnerUin
	job.CreatorUin = jobInfoForSqlCode.CreatorUin
	job.Region = jobInfoForSqlCode.Region
	job.Zone = jobInfoForSqlCode.Zone
	job.Status = jobInfoForSqlCode.Status
	job.Type = jobInfoForSqlCode.Type
	job.ClusterGroupId = jobInfoForSqlCode.ClusterGroupId
	job.ClusterId = jobInfoForSqlCode.ClusterId
	job.TmRunningCuNum = jobInfoForSqlCode.TmRunningCuNum
	job.JmRunningCuNum = jobInfoForSqlCode.JmRunningCuNum
	job.CuMem = jobInfoForSqlCode.CuMem
	job.Remark = jobInfoForSqlCode.Remark
	job.LastOpResult = jobInfoForSqlCode.LastOpResult
	job.CreateTime = jobInfoForSqlCode.CreateTime
	job.UpdateTime = jobInfoForSqlCode.UpdateTime
	job.StartTime = jobInfoForSqlCode.StartTime
	job.StopTime = jobInfoForSqlCode.StopTime
	job.TotalRunMillis = jobInfoForSqlCode.TotalRunMillis
	job.PublishedJobConfigId = jobInfoForSqlCode.PublishedJobConfigId
	job.LatestJobConfigId = jobInfoForSqlCode.LatestJobConfigId
	job.LatestJobConfigVersionId = jobInfoForSqlCode.LatestJobConfigVersionId
	job.LastPublishedJobConfigId = jobInfoForSqlCode.LastPublishedJobConfigId
	job.JobRunningOrderId = jobInfoForSqlCode.JobRunningOrderId
	job.FolderId = jobInfoForSqlCode.FolderId
	job.LastScaleTime = jobInfoForSqlCode.LastScaleTime
	job.FlinkVersion = jobInfoForSqlCode.FlinkVersion
	job.ItemSpaceId = jobInfoForSqlCode.ItemSpaceId
	job.Description = jobInfoForSqlCode.Description
	job.RunningCu = jobInfoForSqlCode.RunningCu
	job.ManageType = jobInfoForSqlCode.ManageType
	job.FlinkJobType = jobInfoForSqlCode.FlinkJobType
	job.RunningCpu = jobInfoForSqlCode.RunningCpu
	job.RunningMem = jobInfoForSqlCode.RunningMem
	job.ProgressDesc = jobInfoForSqlCode.ProgressDesc
	return job
}

func ListClustersByIds(clusterIds []int64) (clusters []*table1.Cluster, err error) {
	clusters = make([]*table1.Cluster, 0)
	if len(clusterIds) < 1 {
		return clusters, nil
	}
	var sql = "SELECT * FROM Cluster"
	cond := dao.NewCondition()
	if len(clusterIds) > 0 {
		cond.In("Id", clusterIds)
	}

	where, args := cond.GetWhere()
	sql += where

	txManager := service2.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return clusters, err
	}

	for i := 0; i < len(data); i++ {
		cluster := &table1.Cluster{}
		err = util.ScanMapIntoStruct(cluster, data[i])
		if err != nil {
			return clusters, errorcode.InternalErrorCode_UnMarshalFailed.NewWithErr(err)
		}
		clusters = append(clusters, cluster)
	}

	return clusters, nil
}

func ListJobByKeywords(query *model.ListJobQuery, sqlKeyword []string) ([]*table.JobInfoForSqlCode, error) {

	sql := "SELECT j.*, HEX(jcs.DecodeSqlCode) as DecodeSqlCode FROM Job j join JobConfigSqlCode jcs on j.Id = jcs.JobId "
	cond := dao.NewCondition()

	cond.Ne("j.Status", constants.JOB_STATUS_DELETE)

	if query.AppId > 0 {
		cond.Eq("j.AppId", query.AppId)
	}

	if len(query.Regions) > 0 {
		cond.In("j.Region", query.Regions)
	}

	if len(query.ItemSpaceIds) > 1 || (len(query.ItemSpaceIds) == 1 && query.ItemSpaceIds[0] != 0) {
		ItemSpaceIds := UniqueSliceInt64(query.ItemSpaceIds)
		cond.In("j.ItemSpaceId", ItemSpaceIds)
	}

	likeCond := dao.NewCondition().EnableOr()
	for i := 0; i < len(sqlKeyword); i++ {
		likeCond.Like("jcs.DecodeSqlCode", "%"+sqlKeyword[i]+"%")
	}
	cond.Condition(likeCond)

	where, args := cond.GetWhere()
	sql += where

	sql += " ORDER BY j.CreateTime DESC "

	txManager := service2.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return nil, err
	}

	logger.Infof("###ListJobByKeywords")
	jobs := make([]*table.JobInfoForSqlCode, 0)
	for i := 0; i < len(data); i++ {
		job := &table.JobInfoForSqlCode{}
		err = util.ScanMapIntoStruct(job, data[i])
		if err != nil {
			return nil, errorcode.FailedOperationCode.NewWithErr(err)
		}
		sqlCode := extractContextAroundKeyword(job.DecodeSqlCode, sqlKeyword)
		job.DecodeSqlCode = sqlCode
		jobs = append(jobs, job)
	}
	return jobs, nil
}

func ListJob(query *model.ListJobQuery) ([]*table.JobInfoForSqlCode, error) {

	sql := "SELECT j.* FROM Job j"
	cond := dao.NewCondition()

	cond.Ne("j.Status", constants.JOB_STATUS_DELETE)

	if query.AppId > 0 {
		cond.Eq("j.AppId", query.AppId)
	}

	if len(query.Regions) > 0 {
		cond.In("j.Region", query.Regions)
	}

	if len(query.ItemSpaceIds) > 1 || (len(query.ItemSpaceIds) == 1 && query.ItemSpaceIds[0] != 0) {
		ItemSpaceIds := UniqueSliceInt64(query.ItemSpaceIds)
		cond.In("j.ItemSpaceId", ItemSpaceIds)
	}

	where, args := cond.GetWhere()
	sql += where

	sql += " ORDER BY j.CreateTime DESC "

	txManager := service2.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return nil, err
	}

	jobs := make([]*table.JobInfoForSqlCode, 0)
	for i := 0; i < len(data); i++ {
		job := &table.JobInfoForSqlCode{}
		err = util.ScanMapIntoStruct(job, data[i])
		if err != nil {
			return nil, errorcode.FailedOperationCode.NewWithErr(err)
		}
		jobs = append(jobs, job)
	}

	return jobs, nil
}

func extractContextAroundKeyword(sqlCodeRaw string, keywords []string) string {
	if keywords == nil || len(keywords) == 0 || len(sqlCodeRaw) == 0 {
		return ""
	}
	rawBytes, err := hex.DecodeString(sqlCodeRaw)
	if err != nil {
		logger.Errorf("Failed to hex.DecodeString, with errors:%+v", err)
		return ""
	}
	sqlCode := string(rawBytes)
	keyword := keywords[0]
	index := strings.Index(sqlCode, keyword)
	if index == -1 {
		return ""
	}

	sqlKeywordTrimCharacterLengthStr, err := service4.GetConfigurationValue(constants.SQL_KEYWORD_TRIM_CHARACTER_LENGTH, "12")
	if err != nil {
		logger.Warningf("Failed to get SQL_KEYWORD_TRIM_CHARACTER_LENGTH from configuration center, with errors:%+v", err)
		sqlKeywordTrimCharacterLengthStr = "10"
	}
	sqlKeywordTrimCharacterLength, err := strconv.Atoi(sqlKeywordTrimCharacterLengthStr)
	if err != nil {
		logger.Warningf("Failed transform %#v to int , with errors:%+v", sqlKeywordTrimCharacterLengthStr, err)
		sqlKeywordTrimCharacterLength = 12
	}
	start := index - sqlKeywordTrimCharacterLength
	end := index + len(keyword) + sqlKeywordTrimCharacterLength

	// 处理起始位置小于0的情况
	if start < 0 {
		start = 0
	}
	// 处理结束位置超出字符串长度的情况
	if end > len(sqlCode) {
		end = len(sqlCode)
	}
	extractContext := sqlCode[start:end]
	return extractContext
}

func makeTree(ranNode []*model.DescribeTreeJobsRsp, parentNode *model.DescribeTreeJobsRsp) (trees []*model.DescribeTreeJobsRsp) {
	children, _ := haveChildren(ranNode, parentNode)
	if children != nil {
		parentNode.Children = append(parentNode.Children, children[0:]...)
		for _, v := range children {
			_, has := haveChildren(ranNode, v)
			if has {
				makeTree(ranNode, v)
			}
		}
	}
	return ranNode
}

func haveChildren(ranNode []*model.DescribeTreeJobsRsp, parentNode *model.DescribeTreeJobsRsp) (children []*model.DescribeTreeJobsRsp, yes bool) {
	for _, v := range ranNode {
		if v.ParentId == parentNode.Id {
			children = append(children, v)
		}
	}
	if children != nil {
		yes = true
	}
	return
}

func getSqlKeywordWithReq(req *model.DescribeTreeJobsReq) (sqlKeyword []string) {
	if len(req.Filters) == 0 {
		return nil
	}
	keywords := make([]string, 0)
	for _, filter := range req.Filters {
		if filter.Name == constants.SQL_KEYWORD {
			keywords = filter.Values
			break
		}
	}
	if len(keywords) == 0 {
		return nil
	}
	return keywords
}

// 制作默认初始化节点
func buildInitTree(req *model.DescribeTreeJobsReq, itemSpaceId []int64, sqlKeyword []string) (initNode *model.DescribeTreeJobsRsp, err error) {
	appId := int32(req.AppId)

	txManager := service2.GetTxManager()
	sql := "SELECT j.Id as Id, j.PublishedJobConfigId as PublishedJobConfigId, j.SerialId as JobId ,j.Name,j.Type as JobType,j.TmRunningCuNum,j.JmRunningCuNum,j.Status,j.CuMem as CuMem, '' as DecodeSqlCode FROM Job AS j"
	cond := dao.NewCondition()
	if sqlKeyword != nil && len(sqlKeyword) > 0 {
		logger.Infof(" getSqlKeywordWithReq, sqlKeyword : %#v", sqlKeyword)
		sql = "SELECT j.Id as Id, j.PublishedJobConfigId as PublishedJobConfigId, j.SerialId as JobId ,j.Name,j.Type as JobType," +
			"j.TmRunningCuNum,j.JmRunningCuNum,j.Status, HEX(jcs.DecodeSqlCode) as DecodeSqlCode FROM Job AS j join JobConfigSqlCode jcs on j.Id = jcs.JobId "
		likeCond := dao.NewCondition().EnableOr()
		for i := 0; i < len(sqlKeyword); i++ {
			likeCond.Like("jcs.DecodeSqlCode", "%"+sqlKeyword[i]+"%")
		}
		cond.Condition(likeCond)
	}

	if appId > 0 {
		cond.Eq("j.AppId", appId)
	}
	if len(req.Region) > 0 {
		cond.Eq("j.Region", req.Region)
	}

	if len(itemSpaceId) > 0 {
		itemSpaceId := UniqueSliceInt64(itemSpaceId)
		cond.In("j.ItemSpaceId", itemSpaceId)
	}

	cond.Eq("j.FolderID", "root")
	cond.Ne("j.Status", -2)

	where, args := cond.GetWhere()
	sql += where
	sql += " GROUP BY j.Id, j.PublishedJobConfigId, j.SerialId, j.Name,j.Type,j.TmRunningCuNum,j.JmRunningCuNum,j.Status ORDER BY j.CreateTime DESC"

	_, dataJob, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("%s: Failed to describe_tree_jobs when Building,error: %+v", req.RequestId, err)
		return nil, err
	}

	if len(dataJob) < 1 {
		initNode = &model.DescribeTreeJobsRsp{
			ParentId:  "",
			Id:        "root",
			Name:      "作业列表",
			JobSet:    make([]*model.TreeJobSets, 0),
			RequestId: req.RequestId,
		}
		return
	}

	clusterIds := make([]int64, 0, 0)

	jobConfigIds := make([]int64, 0, 0)
	for p := 0; p < len(dataJob); p++ {
		pjc, _ := strconv.ParseInt(string(dataJob[p]["PublishedJobConfigId"]), 10, 64)
		jobConfigIds = append(jobConfigIds, pjc)
		clusterId, _ := strconv.ParseInt(string(dataJob[p]["ClusterId"]), 10, 64)
		if ok, _ := commonSerivce.Contain(clusterId, clusterIds); !ok {
			clusterIds = append(clusterIds, clusterId)
		}
	}

	clusters, err := ListClustersByIds(clusterIds)
	if err != nil {
		logger.Errorf("Failed to get ListClustersByIds, with errors:%+v", err)
		return nil, err
	}
	clusterIdMap := make(map[int64]*table1.Cluster)
	for _, cluster := range clusters {
		clusterIdMap[cluster.Id] = cluster
	}

	jobIdClusterMap := make(map[int64]*table1.Cluster)
	for p := 0; p < len(dataJob); p++ {
		clusterId, _ := strconv.ParseInt(string(dataJob[p]["ClusterId"]), 10, 64)
		if cluster, ok := clusterIdMap[clusterId]; ok {
			id, _ := strconv.ParseInt(string(dataJob[p]["Id"]), 10, 64)
			jobIdClusterMap[id] = cluster
		}
	}

	jobConfigs, err := service3.ListJobConfigsByIds(jobConfigIds)
	if err != nil {
		return nil, err
	}
	jobConfigMap := make(map[int64]*table3.JobConfig)
	for _, config := range jobConfigs {
		jobConfigMap[config.JobId] = config
	}

	jobs := make([]*model.TreeJobSets, 0)
	for i := 0; i < len(dataJob); i++ {
		var (
			tmRunningCuNum int8
			jmRunningCuNum int8
			jmSpec         float32
			tmSpec         float32
			jmCpu          float32
			jmMem          float32
			tmCpu          float32
			tmMem          float32
		)

		job := &model.TreeJobSets{}
		id, _ := strconv.ParseInt(string(dataJob[i]["Id"]), 10, 8)
		job.JobId = string(dataJob[i]["JobId"])

		if jobConfig, ok := jobConfigMap[id]; !ok {
			logger.Debugf("No job config found for job, with job serialId:%s", job.JobId)
		} else {
			publishedJobConfigId, err := strconv.ParseInt(string(dataJob[i]["PublishedJobConfigId"]), 10, 64)
			if err != nil {
				logger.Errorf("Failed to parse PublishedJobConfigId, with error:%+v", err)
				return nil, err
			}
			cuMem, err := strconv.ParseInt(string(dataJob[i]["CuMem"]), 10, 8)
			if err != nil {
				logger.Errorf("Failed to parse CuMem, with error:%+v", err)
				return nil, err
			}
			job1 := &table.Job{
				Id:                   id,
				CuMem:                int8(cuMem),
				PublishedJobConfigId: publishedJobConfigId,
			}
			jmc, jmm, tmc, tmm, err := commonSerivce.GetJobRunningCPUAndMem(job1)
			if err != nil {
				logger.Errorf("Failed to get job running cpu and mem, with error:%+v", err)
				return nil, err
			}
			jmSpec = jobConfig.JmCuSpec
			tmSpec = jobConfig.TmCuSpec
			jmCpu = jmc
			jmMem = jmm
			tmCpu = tmc
			tmMem = tmm
		}

		job.Name = string(dataJob[i]["Name"])
		jobType, _ := strconv.ParseInt(string(dataJob[i]["JobType"]), 10, 8)
		job.JobType = int8(jobType)
		job.RunningCpu = jmCpu + tmCpu
		job.RunningMem = jmMem + tmMem
		tmRunningCuNumStr, _ := strconv.ParseInt(string(dataJob[i]["TmRunningCuNum"]), 10, 8)
		tmRunningCuNum = int8(tmRunningCuNumStr)

		jmRunningCuNumStr, _ := strconv.ParseInt(string(dataJob[i]["JmRunningCuNum"]), 10, 8)
		jmRunningCuNum = int8(jmRunningCuNumStr)

		job.RunningCu = jmSpec*float32(jmRunningCuNum) + tmSpec*float32(tmRunningCuNum)

		//if jmCpu != 0 {
		//	job.RunningCpu = jmCpu*float32(jmRunningCuNum) + tmCpu*float32(tmRunningCuNum)
		//	job.RunningMem = jmMem*float32(jmRunningCuNum) + tmMem*float32(tmRunningCuNum)
		//} else {
		//	job.RunningCpu = job.RunningCu
		//	if cluster, ok := jobIdClusterMap[id]; ok {
		//		job.RunningMem = job.RunningCu * float32(cluster.MemRatio)
		//	} else {
		//		job.RunningMem = job.RunningCu * float32(constants.CU_MEM_4GB)
		//	}
		//}
		job.RunningCpu = commonSerivce.GetFloat2Dot(job.RunningCpu)
		job.RunningMem = commonSerivce.GetFloat2Dot(job.RunningMem)

		status, _ := strconv.ParseInt(string(dataJob[i]["Status"]), 10, 8)
		job.Status = int8(status)

		job.DecodeSqlCode = extractContextAroundKeyword(string(dataJob[i]["DecodeSqlCode"]), sqlKeyword)
		jobs = append(jobs, job)
	}

	initNode = &model.DescribeTreeJobsRsp{
		ParentId:  "",
		Id:        "root",
		Name:      "作业列表",
		JobSet:    jobs,
		RequestId: req.RequestId,
	}
	return

}

func UniqueSliceInt64(slice []int64) []int64 {
	if len(slice) == 0 {
		return slice
	}

	m := make(map[int64]struct{}, len(slice))
	for _, s := range slice {
		m[s] = struct{}{}
	}
	slice = make([]int64, 0, len(m))
	for s, _ := range m {
		slice = append(slice, s)
	}
	return slice
}
