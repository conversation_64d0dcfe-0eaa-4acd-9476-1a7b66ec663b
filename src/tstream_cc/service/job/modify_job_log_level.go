package service

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/log"
	cluster "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	service2 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	clusterService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
)

// ArchieYao created at 2022/4/7 5:17 PM

const Flink111Log4jConf = "log4j.properties"
const DefaultLog4jConf = "log4j-console.properties"

func ModifyJobLogLevelBatch(req *model.ModifyJobLogLevelReq) (*model.ModifyJobLogLevelRsp, error) {
	if len(req.JobIds) <= 0 {
		return DoModifyJobLogLevel(req)
	}
	if len(req.JobId) > 0 {
		req.JobIds = append(req.JobIds, req.JobId)
	}
	modifyFailedJobs := make([]*model.ModifyFailedJob, 0)
	for _, jobId := range req.JobIds {
		req.JobId = jobId
		_, err := DoModifyJobLogLevel(req)
		if err != nil {
			code := errorcode.GetCode(err)
			modifyFailedJobs = append(modifyFailedJobs, &model.ModifyFailedJob{
				JobId:  jobId,
				Reason: code.GetCodeStr() + " " + code.GetCodeDesc(),
			})
		}
	}
	return &model.ModifyJobLogLevelRsp{RequestId: req.RequestId, ModifyFailedJobs: modifyFailedJobs}, nil
}

// DoModifyJobLogLevel 修改作业日志等级
func DoModifyJobLogLevel(req *model.ModifyJobLogLevelReq) (*model.ModifyJobLogLevelRsp, error) {
	// 检查作业是否存在
	job, err := service.WhetherJobExists(int32(req.AppId), req.Region, req.JobId)
	if err != nil {
		logger.Errorf("%s: job [%s] not exists in appId [%s]", req.RequestId, req.JobId, req.AppId)
		return nil, err
	}
	// 鉴权
	err = auth.InnerAuthById(req.WorkSpaceId, req.IsSupOwner, job.ItemSpaceId, int64(req.AppId), req.SubAccountUin, req.Action, false)
	if err != nil {
		logger.Errorf("%s: User  uin %s is NOT authenticated, refuse to serve", req.RequestId, req.Uin)
		return nil, err
	}
	if constants.JOB_STATUS_RUNNING != job.Status {
		// 当作业处于运行中时，才可以修改日志级别
		logger.Errorf("%s: job [%s] is not running", req.RequestId, req.JobId)
		return nil, errorcode.InvalidParameter_JobStatusError.New()
	}
	if !jobLogLevelValid(req.LogLevel) && (req.IsModifyClazzLevel && !jobClaszzLogLevelValid(req.ClazzLevels)) {
		msg := fmt.Sprintf("%s : logLevel [%s] not valid or ClazzLevels [%+v] not valid", req.RequestId, req.LogLevel, req.ClazzLevels)
		logger.Errorf(msg)
		return nil, errorcode.InvalidParameter_JobLogLevelParamError.ReplaceDesc(msg)
	}
	req.FlinkVersion = job.FlinkVersion
	if !flinkVersionValid(req.FlinkVersion) {
		logger.Errorf("%s : flink-version [%s] not valid", req.RequestId, req.FlinkVersion)
		return nil, errorcode.InvalidParameter_JobLogLevelParamError.ReplaceDesc("flink version not valid.")
	}
	cluster, err := clusterService.GetClusterByJobId(req.JobId)
	if err != nil {
		logger.Errorf("%s: GetClusterByJobId with error %v", req.RequestId, err)
		return nil, err
	}
	// 检测 cluster 是否支持修改日志级别
	if !strings.Contains(cluster.SupportedFeatures, constants.CLUSTER_LOG_LEVEL_FEATURE) {
		logger.Errorf("cluster %s not support %s", cluster.UniqClusterId, constants.CLUSTER_LOG_LEVEL_FEATURE)
		return nil, errorcode.InvalidParameter_ClusterNotSupportError.ReplaceDesc("cluster not support " + constants.CLUSTER_LOG_LEVEL_FEATURE)
	}
	client, err := k8s.GetK8sService().NewClient([]byte(cluster.KubeConfig))
	if err != nil {
		logger.Errorf("%s: k8s.newClient with error %v", req.RequestId, err)
		return nil, err
	}
	result, err := ModifyConfigMap(req, client, cluster)
	if err == nil && result {
		logger.Infof("%s: update target configmap successfully!", req.RequestId)
		// 更新完configmap之后，把 jobConfig 中的 LogLevel 改为 req 中的 LogLevel
		err2 := updateJobConfigLogLevel(req, job)
		if err2 != nil {
			return nil, err2
		}
	} else {
		return nil, err
	}
	return &model.ModifyJobLogLevelRsp{RequestId: req.RequestId}, nil
}

// updateJobConfigLogLevel 更新作业配置日志级别
func updateJobConfigLogLevel(req *model.ModifyJobLogLevelReq, job *table.Job) error {
	jobInstance, err := GetRunningJobInstanceByJobId(job.Id)
	if err != nil {
		logger.Errorf("%s update jobconfig loglevel for GetRunningJobInstanceByJobId with error %v", req.RequestId, err)
		return err
	}
	jobConfigs, err2 := service.GetJobConfigByIds([]int64{jobInstance.JobConfigId})
	if err2 != nil {
		logger.Errorf("%s update jobconfig loglevel for GetJobConfigByIds with error %v", req.RequestId, err2)
		return err2
	}
	err3 := doUpdate(jobConfigs[0].Id, req.LogLevel)
	if err3 != nil {
		logger.Errorf("%s update jobconfig loglevel for execute update with error %v", req.RequestId, err3)
		return err3
	}
	if req.IsModifyClazzLevel {
		strClazzLevels := constants.CLASS_LOG_LEVEL_DEFALUT_VALUE
		if len(req.ClazzLevels) > 0 {
			bytesClazzLevels, err := json.Marshal(req.ClazzLevels)
			if err != nil {
				errMsg := fmt.Sprintf("Failed to json marshal ClazzLevels struct: %+v, errors:%+v", req.ClazzLevels, err)
				logger.Errorf(errMsg)
				return errorcode.InvalidParameterValueCode.ReplaceDesc(errMsg)
			}
			strClazzLevels = string(bytesClazzLevels)
		}
		err3 = doUpdateClazzLevel(jobConfigs[0].Id, strClazzLevels)
		if err3 != nil {
			logger.Errorf("%s update jobconfig loglevel for execute update with error %v", req.RequestId, err3)
			return err3
		}
	}
	logger.Infof("%s updateJobConfigLogLevel successfully", req.RequestId)
	return nil
}

func doUpdateClazzLevel(jobConfigId int64, clazzLevels string) (err error) {
	service2.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		result := tx.ExecuteSqlWithArgs("UPDATE JobConfig SET ClazzLevels=? WHERE Id=?", clazzLevels, jobConfigId)
		_, err2 := result.RowsAffected()
		return err2
	}).Close()
	return err
}

func doUpdate(jobConfigId int64, logLevel string) (err error) {
	service2.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		result := tx.ExecuteSqlWithArgs("UPDATE JobConfig SET LogLevel=? WHERE Id=?", logLevel, jobConfigId)
		_, err2 := result.RowsAffected()
		return err2
	}).Close()
	return err
}

// ModifyConfigMap 更新configmap
func ModifyConfigMap(req *model.ModifyJobLogLevelReq, client *kubernetes.Clientset, cluster *cluster.Cluster) (result bool, err error) {
	jobNamespace := "default"
	if cluster != nil {
		clusterGroup, err := clusterService.GetClusterGroupByClusterId(cluster.Id)
		if err != nil {
			logger.Errorf("%s modifyJobLogLevel with error %v", req.RequestId, err)
		}
		if clusterGroup.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && clusterGroup.AgentSerialId != "" {
			// 如果是共享集群子集群
			jobNamespace = clusterGroup.SerialId
		}
	}
	configMapList, err := client.CoreV1().ConfigMaps(jobNamespace).List(context.TODO(), metav1.ListOptions{
		LabelSelector: fmt.Sprintf("type==flink-native-kubernetes"),
	})
	if err != nil {
		logger.Errorf("%s: get configMap with error %v", req.RequestId, err)
		return false, err
	}
	var targetConfigMap *v1.ConfigMap
	for i, item := range configMapList.Items {
		labelAppVal := item.Labels["app"]
		if strings.HasPrefix(labelAppVal, req.JobId) && strings.HasPrefix(item.Name, fmt.Sprintf("flink-config-%s", req.JobId)) {
			targetConfigMap = &configMapList.Items[i]
			break
		}
	}
	if targetConfigMap == nil {
		logger.Warningf("%s: cannot find target configmap by [%v]", req.RequestId, req)
		return result, nil
	}
	log4jConfKey, err := GetProperLog4jConfKey(req.FlinkVersion)
	if err != nil {
		logger.Errorf("%s: GetProperLog4jConfKey with error %v", req.RequestId, err)
		return false, err
	}
	log4j2Properties := targetConfigMap.Data[log4jConfKey]
	properties := strings.Split(log4j2Properties, "\n")
	if jobLogLevelValid(req.LogLevel) {
		renderLevel(req.LogLevel, properties)
	}
	if req.IsModifyClazzLevel {
		properties = RenderClazzLevel(req.ClazzLevels, properties)
	}
	newLog4j2Properties := strings.Join(properties, "\n")
	targetConfigMap.Data[log4jConfKey] = newLog4j2Properties
	_, err = client.CoreV1().ConfigMaps(jobNamespace).Update(context.TODO(), targetConfigMap, metav1.UpdateOptions{})
	if err != nil {
		logger.Errorf("%s: update configmap with error %v", req.RequestId, err)
		return result, err
	} else {
		result = true
	}
	return result, nil
}

func matchClazzLevel(property string) bool {
	nameRe := regexp.MustCompile(`logger.(\d+).name`)
	levelRe := regexp.MustCompile(`logger.(\d+).level`)
	// 匹配字符串
	namMatchs := nameRe.FindStringSubmatch(property)
	levelMatchs := levelRe.FindStringSubmatch(property)
	return len(namMatchs) > 0 || len(levelMatchs) > 0
}

func RenderClazzLevel(clazzLevels []*log.ClazzLevel, properties []string) []string {
	// 首先清空历史
	newLog4j2Properties := make([]string, 0, 0)
	for _, property := range properties {
		if matchClazzLevel(property) {
			// remove
			continue
		}
		newLog4j2Properties = append(newLog4j2Properties, property)
	}
	if len(clazzLevels) > 0 {
		for index, clazzLevel := range clazzLevels {
			if clazzLevel == nil {
				continue
			}
			newLog4j2Properties = append(newLog4j2Properties, fmt.Sprintf("logger.%d.name=%s", index, clazzLevel.Clazz))
			newLog4j2Properties = append(newLog4j2Properties, fmt.Sprintf("logger.%d.level=%s", index, clazzLevel.Level))
		}
	}
	return newLog4j2Properties

}

func renderLevel(logLevel string, properties []string) {
	for i, property := range properties {
		if strings.HasPrefix(strings.TrimSpace(property), "rootLogger.level") {
			properties[i] = fmt.Sprintf("rootLogger.level = %s", logLevel)
		}
		if strings.HasPrefix(strings.TrimSpace(property), "logger.flink.level") {
			properties[i] = fmt.Sprintf("logger.flink.level = %s", logLevel)
		}
		if strings.HasPrefix(strings.TrimSpace(property), "logger.akka.level") {
			properties[i] = fmt.Sprintf("logger.akka.level = %s", logLevel)
		}
		if strings.HasPrefix(strings.TrimSpace(property), "logger.kafka.level") {
			properties[i] = fmt.Sprintf("logger.kafka.level = %s", logLevel)
		}
		if strings.HasPrefix(strings.TrimSpace(property), "logger.hadoop.level") {
			properties[i] = fmt.Sprintf("logger.hadoop.level = %s", logLevel)
		}
		if strings.HasPrefix(strings.TrimSpace(property), "logger.zookeeper.level") {
			properties[i] = fmt.Sprintf("logger.zookeeper.level = %s", logLevel)
		}
		if strings.HasPrefix(strings.TrimSpace(property), "logger.clusteradmin.level") {
			properties[i] = fmt.Sprintf("logger.clusteradmin.level = %s", logLevel)
		}
	}
}

// GetProperLog4jConfKey 兼容不同版本的flink
func GetProperLog4jConfKey(flinkVersion string) (string, error) {
	if flinkVersion == "flink-1.11" || flinkVersion == "Flink-1.11" {
		return Flink111Log4jConf, nil
	}
	split := strings.Split(flinkVersion, "-")
	version, err := strconv.ParseFloat(split[1], 32)
	if err != nil {
		logger.Errorf("GetProperLog4jConfKey with error %v", err)
		return "", errorcode.InvalidParameter_FlinkVersionError.New()
	}
	if version > 1.11 {
		return DefaultLog4jConf, nil
	} else {
		return "", errorcode.InvalidParameter_FlinkVersionError.New()
	}
}

// jobLogLevelValid 检测logLevel参数合法性
func jobLogLevelValid(logLevel string) bool {
	if logLevel == constants.LOG_LEVEL_DEBUG ||
		logLevel == constants.LOG_LEVEL_INFO ||
		logLevel == constants.LOG_LEVEL_WARN ||
		logLevel == constants.LOG_LEVEL_ERROR ||
		logLevel == constants.LOG_LEVEL_TRACE {
		return true
	}
	return false
}

// jobLogLevelValid 检测logLevel参数合法性
func jobClaszzLogLevelValid(clazzLevels []*log.ClazzLevel) bool {
	// 清空设置
	if len(clazzLevels) < 1 {
		return true
	}
	for _, clazzLevel := range clazzLevels {
		if !jobLogLevelValid(clazzLevel.Level) || clazzLevel.Clazz == "" {
			return false
		}
	}
	return true
}

// flinkVersionValid 检测 flink-version 合法性，目前支持大于 flink-1.11 的版本
func flinkVersionValid(flinkVersion string) bool {
	if flinkVersion == "" {
		return false
	}
	if !strings.HasPrefix(flinkVersion, "flink-") && !strings.HasPrefix(flinkVersion, "Flink-") {
		return false
	}
	split := strings.Split(flinkVersion, "-")
	version, err := strconv.ParseFloat(split[1], 32)
	if err != nil {
		logger.Errorf("GetProperLog4jConfKey with error %v", err)
		return false
	}
	if version < 1.11 {
		return false
	}
	return true
}
