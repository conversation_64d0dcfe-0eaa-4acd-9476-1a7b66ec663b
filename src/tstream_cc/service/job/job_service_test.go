package service

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"testing"

	service1 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource"
	// service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
)

func TestCalcStandardPodSpec(t *testing.T) {
	//fmt.Println(calcStandardPodSpec(3.3, 16, 4))
	str, _ := base64.StdEncoding.DecodeString("eyJNZXRhZGF0YSI6eyJSZWZlcmVuY2VUYWJsZXMiOltdLCJWYXJpYWJsZXMiOltdfX0=")
	fmt.Println(string(str))
}
func TestSetEksPodCbsSize(t *testing.T) {

	strJobSerialIdCbsSizeMap := "{\"job1\": 40}"
	jobSerialIdCbsSizeMap := make(map[string]int8)
	err := json.Unmarshal([]byte(strJobSerialIdCbsSizeMap), &jobSerialIdCbsSizeMap)
	if err != nil {
		return
	}
	if cbsSize, ok := jobSerialIdCbsSizeMap["job1"]; ok {
		jobAnnotations := "'tke.cloud.tencent.com/cross-tenant-eni-enable:true','tke.cloud.tencent.com/networks:tke-bridge,tke-direct-eni,tke-route','eks.tke.cloud.tencent.com/cpu-type:amd,intel','eks.tke.cloud.tencent.com/security-group-id:sg-gost19xn'"
		newStr :=
			fmt.Sprintf("%s,'%s:%s'", jobAnnotations,
				constants.EKS_POD_CBS_SIZE, fmt.Sprintf("%d", cbsSize))
		fmt.Println(newStr)
	}
}

func TestBase64(t *testing.T) {
	fmt.Println(base64.StdEncoding.EncodeToString([]byte("select * from datagen_source_table limit 1000")))
	//str, _ := base64.StdEncoding.DecodeString("U0VUICBwaXBlbGluZS5vcGVyYXRvci1jaGFpbmluZyA9IGZhbHNlOwotLSBMb2dnZXIgU2luayDlj6/ku6XlsIbovpPlh7rmlbDmja7miZPljbDliLAgVGFza01hbmFnZXIg55qE5pel5b+X5LitICAKLS0gT2NlYW51cyDlubPlj7Dlt7LlhoXnva4gCi0tIERhdGFnZW4gQ29ubmVjdG9yIOWPr+S7pemaj+acuueUn+aIkOS4gOS6m+aVsOaNrueUqOS6jua1i+ivlQotLSDlj4Lop4EgaHR0cHM6Ly9jbG91ZC50ZW5jZW50LmNvbS9kb2N1bWVudC9wcm9kdWN0Lzg0OS81ODcxMwpDUkVBVEUgVEFCTEUgZGF0YWdlbl9zb3VyY2VfdGFibGUgKCAKICAgIGlkIElOVCwgCiAgICBuYW1lIFNUUklORyAKKSBXSVRIICggCiAgICAnY29ubmVjdG9yJyA9ICdkYXRhZ2VuJywKICAgICdyb3dzLXBlci1zZWNvbmQnPScxJyAgLS0g5q+P56eS5Lqn55Sf55qE5pWw5o2u5p2h5pWwCik7CgoKaW5zZXJ0IGludG8gYG5ld19teXNxbF8xMjE3YC5gZGVmYXVsdGAuYHRlc3QwODIzYAoKc2VsZWN0ICogZnJvbSBkYXRhZ2VuX3NvdXJjZV90YWJsZTsK")
	//fmt.Println(string(str))
	//str1 := "NDlUBesNvMyOGPGRVusELQZj0P+mGy8ph6HAwsklYtHwlXH30nQLmzr9s/OuabEsCaLN/BIX9huHZrnCLlvxvqH9nwbbndacuNdw/Qt/gyKokSqfaoNOb16nQIR1+dwlVGuxZlBm0ef0qQF5o1j9Y5pScEmwtFMf8AF577b7Dw/Pmr4HJxglAl7jj2uGYRmpCJRWfkz0iOTDOD9PlvGON0hOv1VSqSWYVx/LRiTOrREmdGU889+fIeCVEqyq9AMKWUTznMNNe0XEwJYIguO/lhk1oDUU1tT82xON662pPXUhKMUS2Gb9k2c8mgMVos6MfPnnDY8nhdhJK6Nt3cW69WYD8WBP3+wIyVE1NvVHWMhFs6xieVN/9G7X+gegmxTUUQMF7/HOxUwIS7EwrJQLoL6PgqI8nrCauvxt7OcTun9NdErC03hiwd3dMHakiYTfKwobHyGBNuK4wNnlCOS5aFFbTopcDqS1UmOtDC5Kd20dezZeEIcXSCKCdGU2XMgsW5RQUYu8S+/IeHwsLi1twVH57+ATzrAfus8qxRbzqZo="
	//decryptSqlCode, err1 := util.AesDecrypt(str1, constants.AES_ENCRYPT_KEY)
	//fmt.Println(decryptSqlCode)
	//fmt.Println(err1)
	//
	//sss, _ := base64.StdEncoding.DecodeString("LS0gRGF0YWdlbiBDb25uZWN0b3Ig5Y+v5Lul6ZqP5py655Sf5oiQ5LiA5Lqb5pWw5o2u55So5LqO5rWL6K+VCi0tIOWPguingSBodHRwczovL2Nsb3VkLnRlbmNlbnQuY29tL2RvY3VtZW50L3Byb2R1Y3QvODQ5LzU4NzEzCkNSRUFURSBUQUJMRSBkYXRhZ2VuX3NvdXJjZV90YWJsZSAoIAogICAgaWQgSU5ULCAKICAgIG5hbWUgU1RSSU5HIAopIFdJVEggKCAKICAgICdjb25uZWN0b3InID0gJ2RhdGFnZW4nLAogICAgJ3Jvd3MtcGVyLXNlY29uZCc9JzEnLCAgLS0g5q+P56eS5Lqn55Sf55qE5pWw5o2u5p2h5pWwCiAgICAnZmllbGRzLmlkLmtpbmQnPSdzZXF1ZW5jZScsCiAgICAnZmllbGRzLmlkLnN0YXJ0Jz0nMScsCiAgICAnZmllbGRzLmlkLmVuZCc9JzIwJwogICAgCik7CgotLSDovpPlhaXliLAgQmxhY2tob2xlIFNpbmsg55qE5pWw5o2uLCDkvJrooqvlhajpg6jkuKLlvIPjgILov5nkuKogU2luayDpgILlkIjlgZrmgKfog73mtYvor5XjgIIKLS0g5Y+C6KeBIGh0dHBzOi8vY2kuYXBhY2hlLm9yZy9wcm9qZWN0cy9mbGluay9mbGluay1kb2NzLXJlbGVhc2UtMS4xMS96aC9kZXYvdGFibGUvY29ubmVjdG9ycy9ibGFja2hvbGUuaHRtbApDUkVBVEUgVEFCTEUgYmxhY2tob2xlX3NpbmsgKCAKICAgIGlkIElOVCwgCiAgICBuYW1lIFNUUklORyAKKSBXSVRIICggCiAgICAnY29ubmVjdG9yJyA9ICdibGFja2hvbGUnCik7CgppbnNlcnQgaW50byBibGFja2hvbGVfc2luayBzZWxlY3QgKiBmcm9tIGRhdGFnZW5fc291cmNlX3RhYmxlOwo=")
	//
	//fmt.Println(string(sss))
	//
	//sss1, _ := base64.StdEncoding.DecodeString("c2VsZWN0ICogZnJvbSBkYXRhZ2VuX3NvdXJjZV90YWJsZQ==")
	//
	//fmt.Println(string(sss1))
	//
	//sss2, _ := base64.StdEncoding.DecodeString("Q1JFQVRFIFRBQkxFIGRhdGFnZW5fc291cmNlX3RhYmxlKGlkIGludCkgd2l0aCAoJ2Nvbm5lY3Rvcic9J2RhdGFnZW4nKQ==")
	//
	//fmt.Println(string(sss2))

}
func TestReplaceEksCpuType(t *testing.T) {

	service1.InitTestDB(service1.WALLYDB)

	str := "abcdefg"

	fmt.Println(str[:3])
	fmt.Println(str[len(str)-1:])

	clusterConfig := make(map[string]interface{})
	cc := "{\"kubernetes.jobmanager.annotations\":\"'eks.tke.cloud.tencent.com/security-group-id:sg-gost19xn','eks.tke.cloud.tencent.com/cpu-type:amd','tke.cloud.tencent.com/cross-tenant-eni-enable:true','tke.cloud.tencent.com/networks:tke-bridge,tke-direct-eni,tke-route'\",\"kubernetes.taskmanager.annotations\":\"'eks.tke.cloud.tencent.com/cpu-type:amd','eks.tke.cloud.tencent.com/security-group-id:sg-gost19xn','tke.cloud.tencent.com/cross-tenant-eni-enable:true','tke.cloud.tencent.com/networks:tke-bridge,tke-direct-eni,tke-route'\"}"
	json.Unmarshal([]byte(cc), &clusterConfig)

	//err := service.ReplaceEksCpuType(3, 0.25, 0.5, clusterConfig)

	//t.Errorf("modify job failed %v", err)
}

func Test_replaceLogLevel(t *testing.T) {
	type args struct {
		properties map[string]string
		logLevel   string
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
		{
			name: "test-case-1",
			args: args{
				properties: getTestCase1Properties(),
				logLevel:   "",
			},
		},
		{
			name: "test-case-2",
			args: args{
				properties: getTestCase1Properties(),
				logLevel:   "DEBUG",
			},
		},
		{
			name: "test-case-3",
			args: args{
				properties: getTestCase2Properties(),
				logLevel:   "ERROR",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			replaceLogLevel(tt.args.properties, tt.args.logLevel)
			t.Logf("test case %s : result : %v", tt.name, tt.args.properties)
		})
	}
}

//func Test_replaceClazzLevel(t *testing.T) {
//	type args struct {
//		properties map[string]string
//		logLevel   string
//	}
//	tests := []struct {
//		name string
//		args args
//	}{
//		// TODO: Add test cases.
//		{
//			name: "test-case-1",
//			args: args{
//				properties: getTestCase1Properties(),
//				logLevel:   "",
//			},
//		},
//		{
//			name: "test-case-2",
//			args: args{
//				properties: getTestCase1Properties(),
//				logLevel:   "DEBUG",
//			},
//		},
//		{
//			name: "test-case-3",
//			args: args{
//				properties: getTestCase2Properties(),
//				logLevel:   "ERROR",
//			},
//		},
//	}
//	clazzLevels := make([]*log.ClazzLevel, 0, 0)
//	clazzLevels = append(clazzLevels, &log.ClazzLevel{
//		Clazz: "cc1.cc1",
//		Level: "Warn",
//	})
//	clazzLevels = append(clazzLevels, &log.ClazzLevel{
//		Clazz: "cc2.cc1",
//		Level: "Warn",
//	})
//	bytesClazzLevels, _ := json.Marshal(clazzLevels)
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			//replaceClazzLevel(tt.args.properties, string(bytesClazzLevels))
//			t.Logf("test case %s : result : %v", tt.name, tt.args.properties)
//		})
//	}
//}

func getTestCase1Properties() map[string]string {
	m := make(map[string]string)
	m["kubernetes.container-start-command-template"] = "%java%  -Duser.timezone=GMT+08 %classpath% %jvmmem% %jvmopts% %logging% %class% %args%"
	m["containerized.taskmanager.env.JVM_ARGS"] = "-Duser.timezone=GMT+08  -Dlog.level=INFO"
	m["containerized.master.env.JVM_ARGS"] = "-Duser.timezone=GMT+08 -Dlog.level=INFO"
	return m
}

func getTestCase2Properties() map[string]string {
	m := make(map[string]string)
	m["kubernetes.container-start-command-template"] = "%java%  -Duser.timezone=GMT+08 -Dlog.level=INFO %classpath% %jvmmem% %jvmopts% %logging% %class% %args%"
	m["containerized.taskmanager.env.JVM_ARGS"] = "-Duser.timezone=GMT+08  -Dlog.level=INFO"
	m["containerized.master.env.JVM_ARGS"] = "-Duser.timezone=GMT+08 -Dlog.level=INFO"
	return m
}
