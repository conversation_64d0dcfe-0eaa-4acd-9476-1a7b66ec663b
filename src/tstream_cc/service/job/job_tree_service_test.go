package service

import (
	"context"
	"encoding/json"
	"os"
	"sync/atomic"
	"testing"
	"time"

	sqle "github.com/dolthub/go-mysql-server"
	"github.com/dolthub/go-mysql-server/memory"
	sqlsvr "github.com/dolthub/go-mysql-server/server"
	"github.com/dolthub/go-mysql-server/sql"
	"github.com/stretchr/testify/assert"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job"
	treetable "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/tree"
)

var timeCountForTest atomic.Int64

func TestJobTreeService_Describe(t *testing.T) {
	testCases := []struct {
		title   string
		prepare func(t *testing.T)
		do      func(t *testing.T)
	}{
		{
			title: "[分类:目录] [目录:0] [作业:0] - 无数据",
			do: func(t *testing.T) {
				req := createDescribeTreeJobsReqForTest(10)
				_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{})
			},
		},
		{
			title: "[分类:目录] [目录:0] [作业:4] - 纯作业翻页",
			prepare: func(t *testing.T) {
				addJobsForTest([]*table.Job{
					{
						FolderId: "root",
						SerialId: "cql-0",
					},
					{
						FolderId: "root",
						SerialId: "cql-1",
						Status:   constants.JOB_STATUS_DELETE,
					},
					{
						FolderId: "root",
						SerialId: "cql-2",
					},
					{
						FolderId: "root",
						SerialId: "cql-3",
					},
					{
						FolderId: "root",
						SerialId: "cql-4",
					},
				})
			},
			do: func(t *testing.T) {
				{
					// 测试每次拉取5条
					req := createDescribeTreeJobsReqForTest(5)
					rsp := assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobIdSet: []string{"cql-4", "cql-3", "cql-2", "cql-0"},
						JobNum:   4,
					})
					req.PageAttach = rsp.PageAttach
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobNum: 4,
					})
				}
				{
					// 测试每次拉取4条
					req := createDescribeTreeJobsReqForTest(4)
					rsp := assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobIdSet: []string{"cql-4", "cql-3", "cql-2", "cql-0"},
						JobNum:   4,
					})
					req.PageAttach = rsp.PageAttach
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobNum: 4,
					})
				}
				{
					// 测试每次拉取3条
					req := createDescribeTreeJobsReqForTest(3)
					rsp := assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobIdSet: []string{"cql-4", "cql-3", "cql-2"},
						JobNum:   4,
						HasMore:  true,
					})
					req.PageAttach = rsp.PageAttach
					rsp = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobIdSet: []string{"cql-0"},
						JobNum:   4,
					})
					req.PageAttach = rsp.PageAttach
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobNum: 4,
					})
				}
				{
					// 测试每次拉取2条
					req := createDescribeTreeJobsReqForTest(2)
					rsp := assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobIdSet: []string{"cql-4", "cql-3"},
						JobNum:   4,
						HasMore:  true,
					})
					req.PageAttach = rsp.PageAttach
					rsp = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobIdSet: []string{"cql-2", "cql-0"},
						JobNum:   4,
					})
					req.PageAttach = rsp.PageAttach
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobNum: 4,
					})
				}
			},
		},
		{
			title: "[分类:目录] [目录:4] [作业:0] - 纯目录翻页",
			prepare: func(t *testing.T) {
				addFoldersForTest([]*treetable.Tree{
					{
						FolderId: "folder-0",
						ParentId: "root",
					},
					{
						FolderId: "folder-1",
						ParentId: "root",
					},
					{
						FolderId: "folder-2",
						ParentId: "root",
					},
					{
						FolderId: "folder-3",
						ParentId: "root",
					},
				})
			},
			do: func(t *testing.T) {
				{
					// 测试每次拉取5条
					req := createDescribeTreeJobsReqForTest(5)
					rsp := assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						FolderIdSet: []string{"folder-3", "folder-2", "folder-1", "folder-0"},
					})
					req.PageAttach = rsp.PageAttach
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{})
				}
				{
					// 测试每次拉取4条
					req := createDescribeTreeJobsReqForTest(4)
					rsp := assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						FolderIdSet: []string{"folder-3", "folder-2", "folder-1", "folder-0"},
					})
					req.PageAttach = rsp.PageAttach
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{})
				}
				{
					// 测试每次拉取3条
					req := createDescribeTreeJobsReqForTest(3)
					rsp := assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						FolderIdSet: []string{"folder-3", "folder-2", "folder-1"},
						HasMore:     true,
					})
					req.PageAttach = rsp.PageAttach
					rsp = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						FolderIdSet: []string{"folder-0"},
					})
					req.PageAttach = rsp.PageAttach
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{})
				}
				{
					// 测试每次拉取2条
					req := createDescribeTreeJobsReqForTest(2)
					rsp := assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						FolderIdSet: []string{"folder-3", "folder-2"},
						HasMore:     true,
					})
					req.PageAttach = rsp.PageAttach
					rsp = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						FolderIdSet: []string{"folder-1", "folder-0"},
					})
					req.PageAttach = rsp.PageAttach
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{})
				}
			},
		},
		{
			title: "[分类:目录] [目录:4] [作业:4] - 目录+作业翻页",
			prepare: func(t *testing.T) {
				addFoldersForTest([]*treetable.Tree{
					{
						FolderId: "folder-0",
						ParentId: "root",
					},
					{
						FolderId: "folder-1",
						ParentId: "root",
					},
					{
						FolderId: "folder-2",
						ParentId: "root",
					},
					{
						FolderId: "folder-3",
						ParentId: "root",
					},
				})
				addJobsForTest([]*table.Job{
					{
						FolderId: "root",
						SerialId: "cql-0",
					},
					{
						FolderId: "root",
						SerialId: "cql-1",
					},
					{
						FolderId: "root",
						SerialId: "cql-2",
					},
					{
						FolderId: "root",
						SerialId: "cql-3",
					},
				})
			},
			do: func(t *testing.T) {
				{
					// 测试每次拉取8条
					req := createDescribeTreeJobsReqForTest(8)
					rsp := assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						FolderIdSet: []string{"folder-3", "folder-2", "folder-1", "folder-0"},
						JobIdSet:    []string{"cql-3", "cql-2", "cql-1", "cql-0"},
						JobNum:      4,
					})
					req.PageAttach = rsp.PageAttach
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{JobNum: 4})
				}
				{
					// 测试每次拉取6条
					req := createDescribeTreeJobsReqForTest(6)
					rsp := assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						FolderIdSet: []string{"folder-3", "folder-2", "folder-1", "folder-0"},
						JobIdSet:    []string{"cql-3", "cql-2"},
						JobNum:      4,
						HasMore:     true,
					})
					req.PageAttach = rsp.PageAttach
					rsp = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobIdSet: []string{"cql-1", "cql-0"},
						JobNum:   4,
					})
					req.PageAttach = rsp.PageAttach
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{JobNum: 4})
				}
				{
					// 测试每次拉取4条
					req := createDescribeTreeJobsReqForTest(4)
					rsp := assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						FolderIdSet: []string{"folder-3", "folder-2", "folder-1", "folder-0"},
						JobNum:      4,
						HasMore:     true,
					})
					req.PageAttach = rsp.PageAttach
					rsp = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobIdSet: []string{"cql-3", "cql-2", "cql-1", "cql-0"},
						JobNum:   4,
					})
					req.PageAttach = rsp.PageAttach
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{JobNum: 4})
				}
				{
					// 测试每次拉取3条
					req := createDescribeTreeJobsReqForTest(3)
					rsp := assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						FolderIdSet: []string{"folder-3", "folder-2", "folder-1"},
						JobNum:      4,
						HasMore:     true,
					})
					req.PageAttach = rsp.PageAttach
					rsp = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						FolderIdSet: []string{"folder-0"},
						JobIdSet:    []string{"cql-3", "cql-2"},
						JobNum:      4,
						HasMore:     true,
					})
					req.PageAttach = rsp.PageAttach
					rsp = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobIdSet: []string{"cql-1", "cql-0"},
						JobNum:   4,
					})
					req.PageAttach = rsp.PageAttach
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{JobNum: 4})
				}
			},
		},

		{
			title: "[分类:目录] [目录:1/0-3] [作业:1/0-2/1-1/0-0-2/0-1-1] - 分层拉取",
			prepare: func(t *testing.T) {
				addFoldersForTest([]*treetable.Tree{
					{
						FolderId: "folder-0",
						ParentId: "root",
					},
					{
						FolderId: "folder-0-0",
						ParentId: "folder-0",
					},
					{
						FolderId: "folder-0-1",
						ParentId: "folder-0",
					},
					{
						FolderId: "folder-0-2",
						ParentId: "folder-0",
					},
				})
				addJobsForTest([]*table.Job{
					{
						FolderId: "root",
						SerialId: "cql-0",
					},
					{
						FolderId: "folder-0",
						SerialId: "cql-0-0",
					},
					{
						FolderId: "folder-0",
						SerialId: "cql-0-1",
					},
					{
						FolderId: "folder-1",
						SerialId: "cql-1-0",
					},
					{
						FolderId: "folder-0-0",
						SerialId: "cql-0-0-0",
					},
					{
						FolderId: "folder-0-0",
						SerialId: "cql-0-0-1",
					},
					{
						FolderId: "folder-0-0",
						SerialId: "cql-0-0-2",
					},
					{
						FolderId: "folder-0-1",
						SerialId: "cql-0-1-0",
					},
				})
			},
			do: func(t *testing.T) {
				{
					// 拉取第一层级: root
					req := createDescribeTreeJobsReqForTest(2)
					rsp := assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						FolderIdSet: []string{"folder-0"},
						JobIdSet:    []string{"cql-0"},
						JobNum:      7,
					})
					req.PageAttach = rsp.PageAttach
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{JobNum: 7})
				}
				{
					// 拉取第二层: folder-0
					req := createDescribeTreeJobsReqForTest(2)
					req.ParentId = "folder-0"
					rsp := assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						FolderIdSet: []string{"folder-0-2", "folder-0-1"},
						JobNum:      6,
						HasMore:     true,
					})
					req.PageAttach = rsp.PageAttach
					rsp = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						FolderIdSet: []string{"folder-0-0"},
						JobIdSet:    []string{"cql-0-1"},
						JobNum:      6,
						HasMore:     true,
					})
					req.PageAttach = rsp.PageAttach
					rsp = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobIdSet: []string{"cql-0-0"},
						JobNum:   6,
					})
					req.PageAttach = rsp.PageAttach
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{JobNum: 6})
				}
				{
					// 拉取第三层: folder-0-1
					req := createDescribeTreeJobsReqForTest(2)
					req.ParentId = "folder-0-1"
					rsp := assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobIdSet: []string{"cql-0-1-0"},
						JobNum:   1,
					})
					req.PageAttach = rsp.PageAttach
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{JobNum: 1})
				}
			},
		},
		{
			title: "[分类:目录] [目录:3/0-3] [作业:3/0-2/1-1/0-0-2/0-1-1] [搜索:JobId] - 有数据:'-0-0',无数据:'no-job'",
			prepare: func(t *testing.T) {
				addFoldersForTest([]*treetable.Tree{
					{
						FolderId: "folder-0",
						ParentId: "root",
					},
					{
						FolderId: "folder-1",
						ParentId: "root",
					},
					{
						FolderId: "folder-2",
						ParentId: "root",
					},
					{
						FolderId: "folder-0-0",
						ParentId: "folder-0",
					},
					{
						FolderId: "folder-0-1",
						ParentId: "folder-0",
					},
					{
						FolderId: "folder-0-2",
						ParentId: "folder-0",
					},
				})
				addJobsForTest([]*table.Job{
					{
						FolderId: "root",
						SerialId: "cql-0",
					},
					{
						FolderId: "root",
						SerialId: "cql-1",
					},
					{
						FolderId: "root",
						SerialId: "cql-2",
					},
					{
						FolderId: "folder-0",
						SerialId: "cql-0-0",
					},
					{
						FolderId: "folder-0",
						SerialId: "cql-0-1",
					},
					{
						FolderId: "folder-1",
						SerialId: "cql-1-0",
					},
					{
						FolderId: "folder-0-0",
						SerialId: "cql-0-0-0",
					},
					{
						FolderId: "folder-0-0",
						SerialId: "cql-0-0-1",
					},
					{
						FolderId: "folder-0-0",
						SerialId: "cql-0-0-2",
					},
					{
						FolderId: "folder-0-1",
						SerialId: "cql-0-1-0",
					},
				})
			},
			do: func(t *testing.T) {
				filters := []struct {
					Name   string
					Values []string
				}{{string(model.TreeFilterKeyJobId), []string{"-0-0"}}}
				{
					// 拉取第一层级: root
					req := createDescribeTreeJobsReqForTest(2)
					req.Filters = filters
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						FolderIdSet: []string{"folder-0"},
						JobNum:      4,
					})
				}
				{
					// 拉取第二层: folder-0
					req := createDescribeTreeJobsReqForTest(2)
					req.Filters = filters
					req.ParentId = "folder-0"
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						FolderIdSet: []string{"folder-0-0"},
						JobIdSet:    []string{"cql-0-0"},
						JobNum:      4,
					})
				}
				{
					// 拉取第三层: folder-0-0
					req := createDescribeTreeJobsReqForTest(2)
					req.Filters = filters
					req.ParentId = "folder-0-0"
					rsp := assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobIdSet: []string{"cql-0-0-2", "cql-0-0-1"},
						JobNum:   3,
						HasMore:  true,
					})
					req.PageAttach = rsp.PageAttach
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobIdSet: []string{"cql-0-0-0"},
						JobNum:   3,
					})
				}
				{
					// 搜索 no-job: 无数据情况
					req := createDescribeTreeJobsReqForTest(2)
					req.Filters = []struct {
						Name   string
						Values []string
					}{{string(model.TreeFilterKeyJobId), []string{"no-job"}}}
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{})
				}
			},
		},
		{
			title: "[分类:目录] [目录:1/0-2] [作业:3/0-2/1-1/0-0-1] [搜索:JobName] - 有数据:'oceanus',无数据:'no-job'",
			prepare: func(t *testing.T) {
				addFoldersForTest([]*treetable.Tree{
					{
						FolderId: "folder-0",
						ParentId: "root",
					},
					{
						FolderId: "folder-1",
						ParentId: "root",
					},
					{
						FolderId: "folder-0-0",
						ParentId: "folder-0",
					},
					{
						FolderId: "folder-0-1",
						ParentId: "folder-0",
					},
				})
				addJobsForTest([]*table.Job{
					{
						FolderId: "root",
						SerialId: "cql-0",
					},
					{
						FolderId: "root",
						SerialId: "cql-1",
						Name:     "x-oceanus-job-0",
					},
					{
						FolderId: "root",
						SerialId: "cql-2",
						Name:     "x-oceanus-job-1",
					},
					{
						FolderId: "folder-0",
						SerialId: "cql-0-0",
					},
					{
						FolderId: "folder-0",
						SerialId: "cql-0-1",
					},
					{
						FolderId: "folder-1",
						SerialId: "cql-1-0",
						Name:     "x-oceanus-job-2",
					},
					{
						FolderId: "folder-0-0",
						SerialId: "cql-0-0-0",
						Name:     "x-oceanus-job-3",
					},
				})
			},
			do: func(t *testing.T) {
				{
					// 拉取第一层级: root
					req := createDescribeTreeJobsReqWithFilterForTest(model.TreeFilterKeyJobName, "oceanus", 2)
					rsp := assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						FolderIdSet: []string{"folder-1", "folder-0"},
						JobNum:      4,
						HasMore:     true,
					})
					req.PageAttach = rsp.PageAttach
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobIdSet: []string{"cql-2", "cql-1"},
						JobNum:   4,
					})
				}
				{
					// 拉取第二层: folder-1
					req := createDescribeTreeJobsReqWithFilterForTest(model.TreeFilterKeyJobName, "oceanus", 2)
					req.ParentId = "folder-1"
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobIdSet: []string{"cql-1-0"},
						JobNum:   1,
					})
				}
				{
					// 拉取第二层: folder-0
					req := createDescribeTreeJobsReqWithFilterForTest(model.TreeFilterKeyJobName, "oceanus", 2)
					req.ParentId = "folder-0"
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						FolderIdSet: []string{"folder-0-0"},
						JobNum:      1,
					})
				}
				{
					// 拉取第三层: folder-0-0
					req := createDescribeTreeJobsReqWithFilterForTest(model.TreeFilterKeyJobName, "oceanus", 2)
					req.ParentId = "folder-0-0"
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobIdSet: []string{"cql-0-0-0"},
						JobNum:   1,
					})
				}
				{
					// 搜索 no-job: 无数据情况
					req := createDescribeTreeJobsReqWithFilterForTest(model.TreeFilterKeyJobName, "no-job", 2)
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{})
				}
			},
		},
		{
			title: "[分类:目录] [目录:2] [作业:2/0-3] [搜索:ClusterName] - 有数据:'集群1',无数据:'集群99'",
			prepare: func(t *testing.T) {
				addFoldersForTest([]*treetable.Tree{
					{
						FolderId: "folder-0",
						ParentId: "root",
					},
					{
						FolderId: "folder-1",
						ParentId: "root",
					},
				})
				addJobsForTest([]*table.Job{
					{
						FolderId: "root",
						SerialId: "cql-0",
					},
					{
						FolderId:       "root",
						SerialId:       "cql-1",
						ClusterGroupId: 2, // 集群1
					},
					{
						FolderId:       "folder-0",
						SerialId:       "cql-0-0",
						ClusterGroupId: 2, // 集群1
					},
					{
						FolderId: "folder-0",
						SerialId: "cql-0-1",
					},
					{
						FolderId:       "folder-0",
						SerialId:       "cql-0-2",
						ClusterGroupId: 2, // 集群1
					},
				})
			},
			do: func(t *testing.T) {
				{
					// 拉取第一层级: root
					req := createDescribeTreeJobsReqWithFilterForTest(model.TreeFilterKeyClusterName, "集群1", 2)
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						FolderIdSet: []string{"folder-0"},
						JobIdSet:    []string{"cql-1"},
						JobNum:      3,
					})
				}
				{
					// 拉取第二层: folder-0
					req := createDescribeTreeJobsReqWithFilterForTest(model.TreeFilterKeyClusterName, "集群1", 2)
					req.ParentId = "folder-0"
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobIdSet: []string{"cql-0-2", "cql-0-0"},
						JobNum:   2,
					})
				}
				{
					req := createDescribeTreeJobsReqWithFilterForTest(model.TreeFilterKeyClusterName, "集群99", 2)
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{})
				}
			},
		},
		{
			title: "[分类:目录] [目录:2] [作业:2/0-3] [搜索:ClusterId] - 有数据:'100',无数据'999'",
			prepare: func(t *testing.T) {
				addFoldersForTest([]*treetable.Tree{
					{
						FolderId: "folder-0",
						ParentId: "root",
					},
					{
						FolderId: "folder-1",
						ParentId: "root",
					},
				})
				addJobsForTest([]*table.Job{
					{
						FolderId: "root",
						SerialId: "cql-0",
					},
					{
						FolderId:       "root",
						SerialId:       "cql-1",
						ClusterGroupId: 2, // 集群1
					},
					{
						FolderId:       "folder-0",
						SerialId:       "cql-0-0",
						ClusterGroupId: 2, // 集群1
					},
					{
						FolderId: "folder-0",
						SerialId: "cql-0-1",
					},
					{
						FolderId:       "folder-0",
						SerialId:       "cql-0-2",
						ClusterGroupId: 2, // 集群1
					},
				})
			},
			do: func(t *testing.T) {
				{
					// 拉取第一层: root
					req := createDescribeTreeJobsReqWithFilterForTest(model.TreeFilterKeyClusterId, "100", 2)
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						FolderIdSet: []string{"folder-0"},
						JobIdSet:    []string{"cql-0"},
						JobNum:      2,
					})

				}
				{
					// 拉取第二层: folder-0
					req := createDescribeTreeJobsReqWithFilterForTest(model.TreeFilterKeyClusterId, "100", 2)
					req.ParentId = "folder-0"
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobIdSet: []string{"cql-0-1"},
						JobNum:   1,
					})
				}
				{
					// no data
					req := createDescribeTreeJobsReqWithFilterForTest(model.TreeFilterKeyClusterId, "999", 2)
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{})
				}

			},
		},
		{
			title: "[分类:目录] [目录:3/2-1] [作业:1/0-1/2-1/1-0-2] [搜索:FolderName] - 有数据:'oceanus',无数据:'目录2025'",
			prepare: func(t *testing.T) {
				addFoldersForTest([]*treetable.Tree{
					{
						FolderId: "folder-0",
						ParentId: "root",
					},
					{
						FolderId: "folder-1",
						ParentId: "root",
					},
					{
						FolderId:   "folder-2",
						FolderName: "x-oceanus-0",
						ParentId:   "root",
					},
					{
						FolderId:   "folder-1-0",
						FolderName: "x-oceanus-1",
						ParentId:   "folder-1",
					},
				})
				addJobsForTest([]*table.Job{
					{
						FolderId: "root",
						SerialId: "cql-0",
					},
					{
						FolderId: "folder-2",
						SerialId: "cql-2-0",
					},
					{
						FolderId: "folder-1",
						SerialId: "cql-1-0",
					},
					{
						FolderId: "folder-1-0",
						SerialId: "cql-1-0-0",
					},
					{
						FolderId: "folder-1-0",
						SerialId: "cql-1-0-1",
					},
				})
			},
			do: func(t *testing.T) {
				{
					// 拉取第一层级: root
					req := createDescribeTreeJobsReqWithFilterForTest(model.TreeFilterKeyFolderName, "oceanus", 2)
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						FolderIdSet: []string{"folder-2", "folder-1"},
						JobNum:      3,
					})
				}
				{
					// 拉取第二层: folder-2
					req := createDescribeTreeJobsReqWithFilterForTest(model.TreeFilterKeyFolderName, "oceanus", 2)
					req.ParentId = "folder-2"
					req.PageAttach = ""
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobIdSet: []string{"cql-2-0"},
						JobNum:   1,
					})
				}
				{

					// 拉取第二层: folder-1
					req := createDescribeTreeJobsReqWithFilterForTest(model.TreeFilterKeyFolderName, "oceanus", 2)
					req.ParentId = "folder-1"
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						FolderIdSet: []string{"folder-1-0"},
						JobNum:      2,
					})
				}
				{
					// 拉取第三层: folder-1-0
					req := createDescribeTreeJobsReqWithFilterForTest(model.TreeFilterKeyFolderName, "oceanus", 2)
					req.ParentId = "folder-1-0"
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobIdSet: []string{"cql-1-0-1", "cql-1-0-0"},
						JobNum:   2,
					})
				}
				{
					// 搜索 no-job: 无数据情况
					req := createDescribeTreeJobsReqWithFilterForTest(model.TreeFilterKeyFolderName, "目录2025", 2)
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{})
				}
			},
		},
		{
			title: "[分类:目录] [目录:0] [作业:3] [搜索:SqlKeyword] - 有数据:'ICEBERG',无数据:'XXX'",
			prepare: func(t *testing.T) {
				addJobsForTest([]*table.Job{
					{
						FolderId: "root",
						SerialId: "cql-0",
					},
					{
						FolderId: "root",
						SerialId: "cql-1",
					},
					{
						FolderId: "root",
						SerialId: "cql-2",
					},
				})
				service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
					_, data, _ := tx.Query("SELECT * FROM Job WHERE SerialId = ?", []interface{}{"cql-1"})
					job := &table.Job{}
					_ = util.ScanMapIntoStruct(job, data[0])

					_ = tx.ExecuteSql("INSERT INTO JobConfigSqlCode (`JobId`, `JobConfigId`, `DecodeSqlCode`) VALUES (?,?,?)",
						[]interface{}{job.Id, job.PublishedJobConfigId, "CREATE TABLE FLOW(NAME STRING) USING ICEBERG; INSERT INTO FLOW VALUES('123');"})
					return nil
				}).Close()
			},
			do: func(t *testing.T) {
				{
					req := createDescribeTreeJobsReqWithFilterForTest(model.TreeFilterKeySqlKeyword, "ICEBERG", 2)
					rsp := assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobIdSet: []string{"cql-1"},
						JobNum:   1,
					})
					t.Logf("decode sql code:%s", rsp.JobSet[0].DecodeSqlCode)
					assert.Equal(t, extractContextAroundKeyword("CREATE TABLE FLOW(NAME STRING) USING ICEBERG; INSERT INTO FLOW VALUES('123');", []string{"ICEBERG"}),
						rsp.JobSet[0].DecodeSqlCode)
				}
				{
					req := createDescribeTreeJobsReqWithFilterForTest(model.TreeFilterKeySqlKeyword, "XXX", 2)
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{})
				}
			},
		},
		{
			title: "[分类:目录] [目录:1] [作业:2/0-3] [筛选:JobType] - 有数据:'2',无数据:'4'",
			prepare: func(t *testing.T) {
				addFoldersForTest([]*treetable.Tree{
					{
						FolderId: "folder-0",
						ParentId: "root",
					},
				})
				addJobsForTest([]*table.Job{
					{
						FolderId: "root",
						SerialId: "cql-0",
					},
					{
						FolderId: "root",
						SerialId: "cql-1",
						Type:     2,
					},
					{
						FolderId: "folder-0",
						SerialId: "cql-0-0",
						Type:     2,
					},
					{
						FolderId: "folder-0",
						SerialId: "cql-0-1",
					},
					{
						FolderId: "folder-0",
						SerialId: "cql-0-2",
						Type:     2,
					},
				})
			},
			do: func(t *testing.T) {
				{
					// 拉取第一层: root
					req := createDescribeTreeJobsReqWithFilterForTest(model.TreeFilterKeyJobType, "2", 2)
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						FolderIdSet: []string{"folder-0"},
						JobIdSet:    []string{"cql-1"},
						JobNum:      3,
					})
				}
				{
					req := createDescribeTreeJobsReqWithFilterForTest(model.TreeFilterKeyJobType, "2", 2)
					req.ParentId = "folder-0"
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobIdSet: []string{"cql-0-2", "cql-0-0"},
						JobNum:   2,
					})
				}
				{
					req := createDescribeTreeJobsReqWithFilterForTest(model.TreeFilterKeyJobType, "4", 2)
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{})
				}
			},
		},
		{
			title: "[分类:目录] [目录:1] [作业:2/0-3] [筛选:JobStatus] - 有数据:'4',无数据:'3'",
			prepare: func(t *testing.T) {
				addFoldersForTest([]*treetable.Tree{
					{
						FolderId: "folder-0",
						ParentId: "root",
					},
				})
				addJobsForTest([]*table.Job{
					{
						FolderId: "root",
						SerialId: "cql-0",
						Status:   4,
					},
					{
						FolderId: "root",
						SerialId: "cql-1",
						Status:   2, // 未发布
					},
					{
						FolderId: "folder-0",
						SerialId: "cql-0-0",
					},
					{
						FolderId: "folder-0",
						SerialId: "cql-0-1",
						Status:   4,
					},
					{
						FolderId: "folder-0",
						SerialId: "cql-0-2",
					},
				})
			},
			do: func(t *testing.T) {
				{
					// 拉取第一层: root
					req := createDescribeTreeJobsReqWithFilterForTest(model.TreeFilterKeyJobStatus, "4", 2)
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						FolderIdSet: []string{"folder-0"},
						JobIdSet:    []string{"cql-0"},
						JobNum:      2,
					})
				}
				{
					// 拉取第二层: folder-0
					req := createDescribeTreeJobsReqWithFilterForTest(model.TreeFilterKeyJobStatus, "4", 2)
					req.ParentId = "folder-0"
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobIdSet: []string{"cql-0-1"},
						JobNum:   1,
					})
				}
				{
					// no data
					req := createDescribeTreeJobsReqWithFilterForTest(model.TreeFilterKeyJobStatus, "3", 2)
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{})
				}
			},
		},
		{
			title: "[分类:目录] [目录:1] [作业:2/0-3] [筛选:Zone] - 有数据:'ap-guangzhou-2',无数据:'ap-beijing-0' ",
			prepare: func(t *testing.T) {
				addFoldersForTest([]*treetable.Tree{
					{
						FolderId: "folder-0",
						ParentId: "root",
					},
				})
				addJobsForTest([]*table.Job{
					{
						FolderId: "root",
						SerialId: "cql-0",
						Zone:     "ap-guangzhou-2",
					},
					{
						FolderId: "root",
						SerialId: "cql-1",
					},
					{
						FolderId: "folder-0",
						SerialId: "cql-0-0",
					},
					{
						FolderId: "folder-0",
						SerialId: "cql-0-1",
					},
					{
						FolderId: "folder-0",
						SerialId: "cql-0-2",
						Zone:     "ap-guangzhou-2",
					},
				})
			},
			do: func(t *testing.T) {
				{
					// 拉取第一层: root
					req := createDescribeTreeJobsReqWithFilterForTest(model.TreeFilterKeyZone, "ap-guangzhou-2", 2)
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						FolderIdSet: []string{"folder-0"},
						JobIdSet:    []string{"cql-0"},
						JobNum:      2,
					})
				}
				{
					// 拉取第二层: folder-0
					req := createDescribeTreeJobsReqWithFilterForTest(model.TreeFilterKeyZone, "ap-guangzhou-2", 2)
					req.ParentId = "folder-0"
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobIdSet: []string{"cql-0-2"},
						JobNum:   1,
					})
				}
				{
					// no data
					req := createDescribeTreeJobsReqWithFilterForTest(model.TreeFilterKeyZone, "ap-beijing-0", 2)
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{})
				}
			},
		},
		{
			title: "[分类:目录] [目录:1] [作业:2/0-3] [筛选:ExceptionType]",
			prepare: func(t *testing.T) {
				addFoldersForTest([]*treetable.Tree{
					{
						FolderId: "folder-0",
						ParentId: "root",
					},
				})
				addJobsForTest([]*table.Job{
					{
						FolderId: "root",
						SerialId: "cql-0",
					},
					{
						FolderId: "root",
						SerialId: "cql-1",
					},
					{
						FolderId: "folder-0",
						SerialId: "cql-0-0",
					},
					{
						FolderId: "folder-0",
						SerialId: "cql-0-1",
					},
					{
						FolderId: "folder-0",
						SerialId: "cql-0-2",
					},
				})
			},
			do: func(t *testing.T) {
				var originHandle = describeJobsExceptionHandle
				defer func() {
					describeJobsExceptionHandle = originHandle
				}()
				describeJobsExceptionHandle = func(ctx context.Context,
					req *model.DescribeJobsExceptionReq) (*model.DescribeJobsExceptionRsp, error) {
					return nil, nil
				}
				{
					// no data
					req := createDescribeTreeJobsReqWithFilterForTest(model.TreeFilterKeyExceptionType, model.ExceptionTypeAlarm, 2)
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{})

					req = createDescribeTreeJobsReqWithFilterForTest(model.TreeFilterKeyExceptionType, model.ExceptionTypeEvent, 2)
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{})
				}
				describeJobsExceptionHandle = func(ctx context.Context,
					req *model.DescribeJobsExceptionReq) (*model.DescribeJobsExceptionRsp, error) {
					return &model.DescribeJobsExceptionRsp{
						JobExceptions: []*model.JobException{
							{false, "", true, "cql-0"},
							{true, "", false, "cql-0-1"},
							{true, "", false, "cql-0-2"},
						},
					}, nil
				}
				{
					// 筛选: alarm
					// 拉取第一层: root
					req := createDescribeTreeJobsReqWithFilterForTest(model.TreeFilterKeyExceptionType, model.ExceptionTypeAlarm, 2)
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						FolderIdSet: []string{"folder-0"},
						JobNum:      2,
					})
				}
				{
					// 筛选: alarm
					// 拉取第二层: folder-0
					req := createDescribeTreeJobsReqWithFilterForTest(model.TreeFilterKeyExceptionType, model.ExceptionTypeAlarm, 2)
					req.ParentId = "folder-0"
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobIdSet: []string{"cql-0-2", "cql-0-1"},
						JobNum:   2,
					})
				}
				{
					// 筛选: event
					req := createDescribeTreeJobsReqWithFilterForTest(model.TreeFilterKeyExceptionType, model.ExceptionTypeEvent, 2)
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobIdSet: []string{"cql-0"},
						JobNum:   1,
					})
				}
				{
					// 筛选: normal
					// 拉取第一层: root
					req := createDescribeTreeJobsReqWithFilterForTest(model.TreeFilterKeyExceptionType, model.ExceptionTypeNormal, 2)
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobIdSet:    []string{"cql-1"},
						FolderIdSet: []string{"folder-0"},
						JobNum:      2,
					})
				}
				{
					// 筛选: normal
					// 拉取第二层: folder-0
					req := createDescribeTreeJobsReqWithFilterForTest(model.TreeFilterKeyExceptionType, model.ExceptionTypeNormal, 2)
					req.ParentId = "folder-0"
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobIdSet: []string{"cql-0-0"},
						JobNum:   1,
					})
				}
			},
		},
		{
			title: "[分类:时间] [目录:1] [作业:1/0-3] - 翻页",
			prepare: func(t *testing.T) {
				addFoldersForTest([]*treetable.Tree{
					{
						FolderId: "folder-0",
						ParentId: "root",
					},
				})
				addJobsForTest([]*table.Job{
					{
						FolderId:   "root",
						SerialId:   "cql-0",
						CreateTime: "2025-07-02 15:00:03",
					},
					{
						FolderId:   "folder-0",
						SerialId:   "cql-0-0",
						CreateTime: "2025-07-02 19:00:03",
					},
					{
						FolderId:   "folder-0",
						SerialId:   "cql-0-1",
						CreateTime: "2025-06-11 09:23:03",
					},
					{
						FolderId:   "folder-0",
						SerialId:   "cql-0-2",
						CreateTime: "2025-07-03 10:10:03",
					},
				})
			},
			do: func(t *testing.T) {
				// 第一层级是时间天
				{
					// 拉取3条
					req := createDescribeTreeJobsReqWithCategoryForTest(model.TreeCategoryCreateTime, 3)
					rsp := assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						FolderIdSet: []string{"2025-07-03", "2025-07-02", "2025-06-11"},
						JobNum:      4,
					})
					req.PageAttach = rsp.PageAttach
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{JobNum: 4})
				}
				{
					// 拉取2条
					req := createDescribeTreeJobsReqWithCategoryForTest(model.TreeCategoryCreateTime, 2)
					rsp := assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						FolderIdSet: []string{"2025-07-03", "2025-07-02"},
						JobNum:      4,
						HasMore:     true,
					})
					req.PageAttach = rsp.PageAttach
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						FolderIdSet: []string{"2025-06-11"},
						JobNum:      4,
					})
				}
				// 第二层级是作业
				{
					// 拉取2条
					req := createDescribeTreeJobsReqWithCategoryForTest(model.TreeCategoryCreateTime, 2)
					req.ParentId = "2025-07-02"
					rsp := assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobIdSet: []string{"cql-0-0", "cql-0"},
						JobNum:   2,
					})
					req.PageAttach = rsp.PageAttach
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{JobNum: 2})
				}
				{
					// 拉取1条
					req := createDescribeTreeJobsReqWithCategoryForTest(model.TreeCategoryCreateTime, 1)
					req.ParentId = "2025-07-02"
					rsp := assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobIdSet: []string{"cql-0-0"},
						JobNum:   2,
						HasMore:  true,
					})
					req.PageAttach = rsp.PageAttach
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobIdSet: []string{"cql-0"},
						JobNum:   2,
					})
				}
			},
		},
		{
			title: "[分类:集群] [目录:0] [作业:3] - 翻页",
			prepare: func(t *testing.T) {
				addFoldersForTest([]*treetable.Tree{
					{
						FolderId: "folder-0",
						ParentId: "root",
					},
				})
				addJobsForTest([]*table.Job{
					{
						FolderId:       "root",
						SerialId:       "cql-0",
						ClusterGroupId: int64(2),
					},
					{
						FolderId:       "root",
						SerialId:       "cql-0-0",
						ClusterGroupId: int64(1),
					},
					{
						FolderId:       "folder-0",
						SerialId:       "cql-0-1",
						ClusterGroupId: int64(2),
					},
				})
			},
			do: func(t *testing.T) {
				// 第一层级是集群
				{
					// 拉取3条
					req := createDescribeTreeJobsReqWithCategoryForTest(model.TreeCategoryClusterName, 3)
					rsp := assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						FolderIdSet: []string{"cluster-101", "cluster-100"},
						JobNum:      3,
					})
					req.PageAttach = rsp.PageAttach
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{JobNum: 3})
				}
				{
					// 拉取1条
					req := createDescribeTreeJobsReqWithCategoryForTest(model.TreeCategoryClusterName, 1)
					rsp := assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						FolderIdSet: []string{"cluster-101"},
						JobNum:      3,
						HasMore:     true,
					})
					req.PageAttach = rsp.PageAttach
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						FolderIdSet: []string{"cluster-100"},
						JobNum:      3,
					})
				}
				// 第二层级是作业
				{
					req := createDescribeTreeJobsReqWithCategoryForTest(model.TreeCategoryClusterName, 2)
					req.ParentId = "cluster-101"
					rsp := assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobIdSet: []string{"cql-0-1", "cql-0"},
						JobNum:   2,
					})
					req.PageAttach = rsp.PageAttach
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{JobNum: 2})
				}
				{
					req := createDescribeTreeJobsReqWithCategoryForTest(model.TreeCategoryClusterName, 1)
					req.ParentId = "cluster-101"
					rsp := assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobIdSet: []string{"cql-0-1"},
						JobNum:   2,
						HasMore:  true,
					})
					req.PageAttach = rsp.PageAttach
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobIdSet: []string{"cql-0"},
						JobNum:   2,
					})
				}
				// 搜索不存在的集群
				{
					req := createDescribeTreeJobsReqWithFilterForTest(model.TreeFilterKeyClusterName, "集群999", 2)
					req.Category = string(model.TreeCategoryClusterName)
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{})
				}
			},
		},
		{
			title: "[分类:无] [目录:2] [作业:3/0-1] - 作业翻页",
			prepare: func(t *testing.T) {
				addFoldersForTest([]*treetable.Tree{
					{
						FolderId: "folder-0",
						ParentId: "root",
					},
					{
						FolderId: "folder-1",
						ParentId: "root",
					},
				})
				addJobsForTest([]*table.Job{
					{
						FolderId: "root",
						SerialId: "cql-0",
					},
					{
						FolderId: "root",
						SerialId: "cql-1",
					},
					{
						FolderId: "root",
						SerialId: "cql-2",
					},
					{
						FolderId: "folder-0",
						SerialId: "cql-3",
					},
				})
			},
			do: func(t *testing.T) {
				{
					// 测试每次拉取5条
					req := createDescribeTreeJobsReqWithCategoryForTest(model.TreeCategoryNoop, 5)
					rsp := assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobIdSet: []string{"cql-3", "cql-2", "cql-1", "cql-0"},
						JobNum:   4,
						HasMore:  false,
					})
					req.PageAttach = rsp.PageAttach
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{JobNum: 4})
				}
				{
					// 测试每次拉取4条
					req := createDescribeTreeJobsReqWithCategoryForTest(model.TreeCategoryNoop, 4)
					rsp := assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobIdSet: []string{"cql-3", "cql-2", "cql-1", "cql-0"},
						JobNum:   4,
						HasMore:  false,
					})
					req.PageAttach = rsp.PageAttach
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{JobNum: 4})
				}
				{
					// 测试每次拉取3条
					req := createDescribeTreeJobsReqWithCategoryForTest(model.TreeCategoryNoop, 3)
					rsp := assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobIdSet: []string{"cql-3", "cql-2", "cql-1"},
						JobNum:   4,
						HasMore:  true,
					})
					req.PageAttach = rsp.PageAttach
					rsp = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobIdSet: []string{"cql-0"},
						JobNum:   4,
						HasMore:  false,
					})
				}
			},
		},
		{
			title: "[分类:无] [目录:2] [作业:3/0-2/1-1] [搜索:JobName] - 有数据:'oceanus',无数据:'no-job'",
			prepare: func(t *testing.T) {
				addFoldersForTest([]*treetable.Tree{
					{
						FolderId: "folder-0",
						ParentId: "root",
					},
					{
						FolderId: "folder-1",
						ParentId: "root",
					},
				})
				addJobsForTest([]*table.Job{
					{
						FolderId: "root",
						SerialId: "cql-0",
					},
					{
						FolderId: "root",
						SerialId: "cql-1",
						Name:     "x-oceanus-job-0",
					},
					{
						FolderId: "root",
						SerialId: "cql-2",
						Name:     "x-oceanus-job-1",
					},
					{
						FolderId: "folder-0",
						SerialId: "cql-0-0",
					},
					{
						FolderId: "folder-0",
						SerialId: "cql-0-1",
					},
					{
						FolderId: "folder-1",
						SerialId: "cql-1-0",
						Name:     "x-oceanus-job-2",
					},
				})
			},
			do: func(t *testing.T) {
				{
					// 搜索有作业
					req := createDescribeTreeJobsReqWithFilterForTest(model.TreeFilterKeyJobName, "oceanus", 2)
					req.Category = string(model.TreeCategoryNoop)
					rsp := assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobIdSet: []string{"cql-1-0", "cql-2"},
						JobNum:   3,
						HasMore:  true,
					})
					req.PageAttach = rsp.PageAttach
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{
						JobIdSet: []string{"cql-1"},
						JobNum:   3,
					})
				}
				{
					// 搜索 no-job: 无数据情况
					req := createDescribeTreeJobsReqWithFilterForTest(model.TreeFilterKeyJobName, "no-job", 2)
					req.Category = string(model.TreeCategoryNoop)
					_ = assertSimpleDescribeTreeJobsRsp(t, req, &testSimpleDescribeTreeJobsRsp{})
				}
			},
		},
	}
	for _, tc := range testCases {
		if tc.do == nil {
			t.Logf("无效测试: %s", tc.title)
			continue
		}
		t.Logf("开始测试: %s", tc.title)
		if tc.prepare != nil {
			tc.prepare(t)
		}
		tc.do(t)
		cleanDataForTest()
		t.Logf("结束测试: %s", tc.title)
		t.Logf("=====================================")
	}
}

func TestMain(m *testing.M) {
	db := memory.NewDatabase("online_galileo")
	db.BaseDatabase.EnablePrimaryKeyIndexes()
	pro := memory.NewDBProvider(db)
	engine := sqle.NewDefault(pro)
	config := sqlsvr.Config{
		Protocol: "tcp",
		Address:  "localhost:3306",
	}
	svr, err := sqlsvr.NewServer(config, engine, sql.NewContext, memory.NewSessionBuilder(pro), nil)
	if err != nil {
		panic(err)
	}
	go func() {
		if err = svr.Start(); err != nil {
			panic(err)
		}
	}()
	timeCountForTest.Store(0)
	defer func() {
		_ = svr.Close()
	}()
	time.Sleep(2 * time.Second)
	prepareDBForTest()
	code := m.Run()
	os.Exit(code)
}

func prepareDBForTest() {
	dbUrl := "root:root@tcp(localhost:3306)/online_galileo?charset=utf8"
	tx, err := dao.NewDataSourceTransactionManager(dbUrl)
	if err != nil {
		panic(err)
	}
	service.SetTxManager(tx)

	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		// prepare db
		_ = tx.ExecuteSql("CREATE TABLE IF NOT EXISTS `ItemSpace`\n(\n    `Id`            bigint(20)                      NOT NULL AUTO_INCREMENT,\n    `SerialId`      varchar(60) COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '例如 spc-XXX',\n    `AppId`         bigint(20)                      NOT NULL,\n    `OwnerUin`      varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '创建空间的主账号uin',\n    `CreatorUin`    varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '创建空间的子账号uin(如果是主账号创建,就是主账号uin)',\n    `ItemSpaceName` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '项目空间名称',\n    `Region`        varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '地域',\n    `UpdateTime`    timestamp                       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n    `CreateTime`    timestamp                       NOT NULL DEFAULT CURRENT_TIMESTAMP,\n    `Status`        tinyint(4)                      NOT NULL COMMENT '空间状态 1 未初始化 2 可用  -1 已删除',\n    `Description`   varchar(256) COLLATE utf8mb4_bin         DEFAULT NULL COMMENT '空间描述',\n    PRIMARY KEY (`Id`) USING BTREE\n    ) ENGINE = InnoDB\n    AUTO_INCREMENT = 1\n    DEFAULT CHARSET = utf8mb4\n    COLLATE = utf8mb4_bin\n    ROW_FORMAT = DYNAMIC;", nil)
		_ = tx.ExecuteSql("CREATE TABLE IF NOT EXISTS `Job`\n(\n    `Id`                       bigint(20)                      NOT NULL AUTO_INCREMENT,\n    `SerialId`                 varchar(12) COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '例如 cql-bu8nydsy',\n    `AppId`                    bigint(20)                      NOT NULL,\n    `OwnerUin`                 varchar(20) COLLATE utf8mb4_bin NOT NULL,\n    `CreatorUin`               varchar(20) COLLATE utf8mb4_bin NOT NULL,\n    `Region`                   varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou',\n    `Zone`                     varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou-3',\n    `Name`                     varchar(100) COLLATE utf8mb4_bin         DEFAULT NULL,\n    `Status`                   tinyint(4)                      NOT NULL COMMENT '作业状态，0：未初始化，1：未发布，2：操作中，3：运行中，4：停止，5：暂停，-1：故障，-2：删除',\n    `Type`                     tinyint(4)                      NOT NULL COMMENT '作业类型，0：SQL，1：JAR',\n    `ClusterGroupId`           smallint(20)                    NOT NULL,\n    `ClusterId`                smallint(20)                    NOT NULL,\n    `ItemSpaceId`              bigint(20)                      NOT NULL DEFAULT '0' COMMENT '项目空间ID',\n    `TmRunningCuNum`           int(20)                                  DEFAULT NULL,\n    `CuMem`                    tinyint(4)                      NOT NULL COMMENT 'CU内存规格类型，单位GB，只支持2,4,,8,16',\n    `LastOpResult`             mediumtext COLLATE utf8mb4_bin COMMENT '作业调度结果',\n    `Remark`                   varchar(100) COLLATE utf8mb4_bin         DEFAULT NULL COMMENT '备注',\n    `CreateTime`               timestamp                       NOT NULL DEFAULT CURRENT_TIMESTAMP,\n    `UpdateTime`               timestamp                       NULL     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n    `StartTime`                timestamp                       NOT NULL DEFAULT '0000-00-00 00:00:00',\n    `StopTime`                 timestamp                       NOT NULL DEFAULT '0000-00-00 00:00:00',\n    `TotalRunMillis`           bigint(20)                               DEFAULT '0' COMMENT '作业累计运行时间',\n    `PublishedJobConfigId`     bigint(20)                               DEFAULT '0',\n    `LatestJobConfigId`        bigint(20)                               DEFAULT '0',\n    `LatestJobConfigVersionId` bigint(20)                               DEFAULT '0',\n    `JmRunningCuNum`           int(11)                                  DEFAULT '0',\n    `LastPublishedJobConfigId` bigint(20)                               DEFAULT '-1',\n    `JobRunningOrderId`        bigint(20)                               DEFAULT '0' COMMENT '该作业总共运行了多少次，包含重启，此值等于JobInstance的数量',\n    `FolderId`                 varchar(15) COLLATE utf8mb4_bin          DEFAULT 'root' COMMENT '例如 folder-bu8nydsy',\n    `FlinkVersion`             varchar(255) COLLATE utf8mb4_bin         DEFAULT '',\n    `LastScaleTime`            timestamp                       NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '上一次完成扩缩容的时间',\n    PRIMARY KEY (`Id`) USING BTREE,\n    UNIQUE KEY `SerialId` (`SerialId`) USING BTREE,\n    KEY `index_0` (`AppId`, `Status`),\n    KEY `index_1` (`Status`, `ClusterId`)\n    ) ENGINE = InnoDB\n    AUTO_INCREMENT = 1\n    DEFAULT CHARSET = utf8mb4\n    COLLATE = utf8mb4_bin\n    ROW_FORMAT = DYNAMIC;", nil)
		_ = tx.ExecuteSql("CREATE TABLE IF NOT EXISTS `ItemSpacesClusters`\n(\n    `Id`             bigint(20)                      NOT NULL AUTO_INCREMENT,\n    `AppId`          bigint(20)                      NOT NULL,\n    `ItemSpaceId`    bigint(20)                      NOT NULL,\n    `Region`         varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '地域',\n    `ClusterGroupId` bigint(20)                      NOT NULL COMMENT '集群组ID',\n    `UpdateTime`     timestamp                       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n    `CreateTime`     timestamp                       NOT NULL DEFAULT CURRENT_TIMESTAMP,\n    `Status`         tinyint(4)                      NOT NULL COMMENT '状态: 2 启用 1 停用',\n    PRIMARY KEY (`Id`) USING BTREE,\n    KEY `ItemSpaceId` (`ItemSpaceId`)\n    ) ENGINE = InnoDB\n    AUTO_INCREMENT = 1\n    DEFAULT CHARSET = utf8mb4\n    COLLATE = utf8mb4_bin\n    ROW_FORMAT = DYNAMIC;", nil)
		_ = tx.ExecuteSql("CREATE TABLE IF NOT EXISTS `ClusterGroup`\n(\n    `Id`                 bigint(20)                      NOT NULL AUTO_INCREMENT,\n    `SerialId`           varchar(16) COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '例如 cluster-bu8nydsy',\n    `Name`               varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '集群名',\n    `AppId`              bigint(20)                      NOT NULL,\n    `OwnerUin`           varchar(20) COLLATE utf8mb4_bin NOT NULL,\n    `CreatorUin`         varchar(20) COLLATE utf8mb4_bin NOT NULL,\n    `Region`             varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou',\n    `Zone`               varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou-3',\n    `Type`               tinyint(4)                      NOT NULL COMMENT '模式，1：共享集群，2：独占集群',\n    `Status`             tinyint(4)                      NOT NULL COMMENT '集群状态，1：创建中，2：运行中',\n    `CuNum`              int(20)                         NOT NULL COMMENT 'CU数量',\n    `UsedCuNum`          int(20)                         NOT NULL COMMENT '已使用的CU数量',\n    `CuMem`              tinyint(4)                      NOT NULL COMMENT 'CU内存规格类型，单位GB，只支持2,4,,8,16',\n    `Remark`             varchar(100) COLLATE utf8mb4_bin         DEFAULT NULL COMMENT '备注',\n    `CreateTime`         timestamp                       NOT NULL DEFAULT CURRENT_TIMESTAMP,\n    `UpdateTime`         timestamp                       NULL     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n    `StopTime`           timestamp                       NOT NULL DEFAULT '0000-00-00 00:00:00',\n    `NetEnvironmentType` tinyint(4)                      NOT NULL COMMENT '网络环境，1：VPC，2：云支撑 3：VPCX 4: 私有云',\n    PRIMARY KEY (`Id`) USING BTREE,\n    UNIQUE KEY `SerialId` (`SerialId`) USING BTREE\n    ) ENGINE = InnoDB\n    AUTO_INCREMENT = 1\n    DEFAULT CHARSET = utf8mb4\n    COLLATE = utf8mb4_bin\n    ROW_FORMAT = DYNAMIC;", nil)
		_ = tx.ExecuteSql("CREATE TABLE IF NOT EXISTS `Tree`\n(\n    `Id`          bigint(20)                      NOT NULL AUTO_INCREMENT,\n    `ItemSpaceId` bigint(20)                      NOT NULL DEFAULT '0' COMMENT '项目空间ID',\n    `FolderId`    varchar(15) COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '例如 folder-bu8nydsy',\n    `FolderName`  varchar(50) CHARACTER SET utf8  NOT NULL COMMENT '文件夹名',\n    `ParentId`    varchar(15) COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '例如 folder-bu8nyd75',\n    `AppId`       bigint(20)                      NOT NULL,\n    `Region`      varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou',\n    `CreatorUin`  varchar(20) COLLATE utf8mb4_bin NOT NULL,\n    `OwnerUin`    varchar(20) COLLATE utf8mb4_bin NOT NULL,\n    `CreateTime`  timestamp                       NOT NULL DEFAULT CURRENT_TIMESTAMP,\n    `UpdateTime`  timestamp                       NULL     DEFAULT CURRENT_TIMESTAMP,\n    PRIMARY KEY (`Id`) USING BTREE,\n    UNIQUE KEY `FolderId` (`FolderId`) USING BTREE,\n    UNIQUE KEY `FolderName_ParentId` (`FolderName`, `ParentId`, `AppId`, `Region`, `ItemSpaceId`)\n    ) ENGINE = InnoDB\n    AUTO_INCREMENT = 1\n    DEFAULT CHARSET = utf8mb4\n    COLLATE = utf8mb4_bin\n    ROW_FORMAT = DYNAMIC;", nil)
		_ = tx.ExecuteSql("CREATE TABLE IF NOT EXISTS `JobScaleRule`\n(\n    `Id`             bigint(20)                      NOT NULL AUTO_INCREMENT,\n    `SerialId`       varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '例如 rule-ck9scadk',\n    `JobId`          varchar(12) COLLATE utf8mb4_bin NOT NULL COMMENT '例如 cql-bu8nydsy',\n    `AppId`          bigint(20)                      NOT NULL,\n    `OwnerUin`       varchar(20) COLLATE utf8mb4_bin NOT NULL,\n    `CreatorUin`     varchar(20) COLLATE utf8mb4_bin NOT NULL,\n    `Region`         varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou',\n    `Zone`           varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou-3',\n    `RuleName`       varchar(50) CHARACTER SET utf8  NOT NULL COMMENT '规则名',\n    `Status`         tinyint(4)                      NOT NULL COMMENT '规则状态，1：启用，-1：停用，-2：删除',\n    `ConditionRatio` smallint(10)                             DEFAULT NULL COMMENT '0-100取值百分比，如果tm中高于阈值CPU或低于阈值cpu占比高于这个值，就会触发扩缩容',\n    `Threshold`      bigint(20)                      NOT NULL COMMENT '阈值，如果是cpu 则是一个0-100的值 表示使用率，如果是kafka offset 则是offset之间的差值',\n    `DurationTime`   smallint(10)                    NOT NULL COMMENT '持续多久时间才进行扩缩容，单位分钟',\n    `Step`           varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '如果结尾是%，则是按当前并行度的百分数进行扩缩容，否则按固定数字进行扩缩容',\n    `ReachLimit`     smallint(10)                    NOT NULL COMMENT '扩容最高的并行度',\n    `CreateTime`     timestamp                       NOT NULL DEFAULT CURRENT_TIMESTAMP,\n    `UpdateTime`     timestamp                       NULL     DEFAULT CURRENT_TIMESTAMP,\n    `Properties`     mediumtext COLLATE utf8mb4_bin,\n    `Configuration`  mediumtext COLLATE utf8mb4_bin COMMENT '规则配置字段',\n    PRIMARY KEY (`Id`) USING BTREE\n    ) ENGINE = InnoDB\n    AUTO_INCREMENT = 1\n    DEFAULT CHARSET = utf8mb4\n    COLLATE = utf8mb4_bin\n    ROW_FORMAT = DYNAMIC;", nil)
		_ = tx.ExecuteSql("CREATE TABLE `JobConfigSqlCode` ( `Id` bigint(20) NOT NULL AUTO_INCREMENT, `JobId` bigint(20) NOT NULL, `JobConfigId` bigint(20) NOT NULL, `DecodeSqlCode` mediumtext COLLATE utf8mb4_bin, `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP, `UpdateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP, `Status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0: 使用中，-1：已删除', PRIMARY KEY (`Id`), UNIQUE KEY `index_sql` (`JobId`) ) ENGINE=InnoDB AUTO_INCREMENT=922 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC", nil)
		return nil
	}).Close()

	prepareDataForTest()
}

func prepareDataForTest() {
	// 通用的准备数据逻辑(无需清理)
	// 准备公共数据, 正常的工作空间和集群数据
	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		// space
		_ = tx.ExecuteSql("INSERT INTO `ItemSpace` (`SerialId`, `AppId`, `OwnerUin`, `CreatorUin`, `ItemSpaceName`, `Region`, `Status`) VALUES (?,?,?,?,?,?,?)",
			[]interface{}{"spc-000", 1, "10000", "10000", "测试工作空间000", "ap-guangzhou", 2})
		// cluster group
		_ = tx.ExecuteSql("INSERT INTO `ClusterGroup` (`SerialId`, `Name`, `AppId`, `OwnerUin`, `CreatorUin`, `Region`, `Zone`, `Type`, `Status`, `CuNum`, `UsedCuNum`, `CuMem`, `NetEnvironmentType`,`CreateTime`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)",
			[]interface{}{"cluster-100", "测试集群0", 1, "10000", "10000", "ap-guangzhou", "ap-guangzhou-1", 2, 2, 16, 8, 32, 1, getTimeForTest()})
		_ = tx.ExecuteSql("INSERT INTO `ItemSpacesClusters` (`AppId`, `ItemSpaceId`, `Region`, `ClusterGroupId`, `Status`) VALUES (?,?,?,?,?)",
			[]interface{}{1, 1, "ap-guangzhou", 1, 2})
		_ = tx.ExecuteSql("INSERT INTO `ItemSpacesClusters` (`AppId`, `ItemSpaceId`, `Region`, `ClusterGroupId`, `Status`) VALUES (?,?,?,?,?)",
			[]interface{}{1, 1, "ap-guangzhou", 2, 2})
		_ = tx.ExecuteSql("INSERT INTO `ClusterGroup` (`SerialId`, `Name`, `AppId`, `OwnerUin`, `CreatorUin`, `Region`, `Zone`, `Type`, `Status`, `CuNum`, `UsedCuNum`, `CuMem`, `NetEnvironmentType`,`CreateTime`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)",
			[]interface{}{"cluster-101", "测试集群1", 1, "10000", "10000", "ap-guangzhou", "ap-guangzhou-1", 2, 2, 16, 8, 32, 1, getTimeForTest()})
		// default job scale rule
		_ = tx.ExecuteSql("INSERT INTO `JobScaleRule` (`SerialId`, `JobId`, `AppId`, `OwnerUin`, `CreatorUin`, `Region`, `Zone`, `RuleName`, `Status`, `ConditionRatio`, `Threshold`, `DurationTime`, `Step`, `ReachLimit`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)",
			[]interface{}{"rule-200", "cql-0", 1, "10000", "10000", "ap-guangzhou", "ap-guangzhou-1", constants.SCALE_RULES_AUTO_SCALE_BASIC, 1, 99, 70, 30, "50%", 1})
		_ = tx.ExecuteSql("INSERT INTO `JobScaleRule` (`SerialId`, `JobId`, `AppId`, `OwnerUin`, `CreatorUin`, `Region`, `Zone`, `RuleName`, `Status`, `ConditionRatio`, `Threshold`, `DurationTime`, `Step`, `ReachLimit`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)",
			[]interface{}{"rule-201", "cql-1", 1, "10000", "10000", "ap-guangzhou", "ap-guangzhou-1", constants.SCALE_RULES_AUTO_SCALE_TIME_BASED, 1, 99, 70, 30, "50%", 1})
		return nil
	}).Close()
}

func cleanDataForTest() {
	// 通用的清理逻辑
	// 清理作业表和目录表
	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		_ = tx.ExecuteSql("DELETE FROM Tree", nil)
		_ = tx.ExecuteSql("DELETE FROM Job", nil)
		return nil
	}).Close()
}

func createDescribeTreeJobsReqForTest(pageSize int64) *model.DescribeTreeJobsReq {
	return &model.DescribeTreeJobsReq{
		RequestBase: apiv3.RequestBase{
			Region:        "ap-guangzhou",
			RequestId:     "x-request-id-000",
			AppId:         1,
			Uin:           "10000",
			SubAccountUin: "10001",
			IsSupOwner:    0,
		},
		Category:    string(model.TreeCategoryFolderName),
		WorkSpaceId: "spc-000",
		PageSize:    pageSize,
	}
}

func createDescribeTreeJobsReqWithFilterForTest(key model.TreeFilterKey, value string, pageSize int64) *model.DescribeTreeJobsReq {
	req := createDescribeTreeJobsReqForTest(pageSize)
	req.Filters = []struct {
		Name   string
		Values []string
	}{{string(key), []string{value}}}
	return req
}

func createDescribeTreeJobsReqWithCategoryForTest(category model.TreeCategory, pageSize int64) *model.DescribeTreeJobsReq {
	req := createDescribeTreeJobsReqForTest(pageSize)
	req.Category = string(category)
	return req
}

// addJobsForTest 增加作业 为了减少不需要测试的字段填充, 只支持以下字段, 其他是固定的, 后面有需求可以改
// `FolderId`,`SerialId`, `AppId`, `Region`, `Zone`, `Name`, `Status`, `Type`, `ClusterGroupId`, `ItemSpaceId`, `CreateTime`
func addJobsForTest(jobs []*table.Job) {
	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		// job
		for _, job := range jobs {
			appId := job.AppId
			if appId == 0 {
				appId = 1
			}
			region := job.Region
			if region == "" {
				region = "ap-guangzhou"
			}
			zone := job.Zone
			if zone == "" {
				zone = "ap-guangzhou-1"
			}
			clusterGroupId := job.ClusterGroupId
			if clusterGroupId == 0 {
				clusterGroupId = 1
			}
			spaceId := job.ItemSpaceId
			if spaceId == 0 {
				spaceId = 1
			}
			name := job.Name
			if name == "" {
				name = "作业-" + job.SerialId
			}
			jobStatus := job.Status
			if jobStatus == 0 {
				jobStatus = constants.JOB_STATUS_CREATE
			}
			jobType := job.Type
			if jobType == 0 {
				jobType = constants.JOB_TYPE_SQL
			}
			createTime := job.CreateTime
			if createTime == "" {
				createTime = getTimeForTest()
			}
			// job
			_ = tx.ExecuteSql("INSERT INTO `Job` (`FolderId`,`SerialId`, `AppId`, `OwnerUin`, `CreatorUin`, `Region`, `Zone`, `Name`, `Status`, `Type`, `ClusterGroupId`, `ClusterId`, `ItemSpaceId`, `TmRunningCuNum`, `CuMem`, `CreateTime`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)",
				[]interface{}{job.FolderId, job.SerialId, appId, "10000",
					"10000", region, zone, name, jobStatus, jobType, clusterGroupId, 0, spaceId, 4, 8, createTime})
		}
		return nil
	}).Close()
}

// addFoldersForTest 增加目录 为了减少不需要测试的字段填充, 只支持以下字段, 其他是固定的, 后面有需求可以改
// `ItemSpaceId`, `FolderId`, `FolderName`, `ParentId`, `AppId`, `Region`
func addFoldersForTest(folders []*treetable.Tree) {
	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		for _, folder := range folders {
			// tree
			spaceId := folder.ItemSpaceId
			if spaceId == 0 {
				spaceId = 1
			}
			appId := folder.AppId
			if appId == 0 {
				appId = 1
			}
			region := folder.Region
			if region == "" {
				region = "ap-guangzhou"
			}
			name := folder.FolderName
			if name == "" {
				name = "目录-" + folder.FolderId
			}
			_ = tx.ExecuteSql("INSERT INTO `Tree` (`ItemSpaceId`, `FolderId`, `FolderName`, `ParentId`, `AppId`, `Region`, `CreatorUin`, `OwnerUin`, `CreateTime`) VALUES (?,?,?,?,?,?,?,?,?)",
				[]interface{}{spaceId, folder.FolderId, name, folder.ParentId, appId, region, "10000", "10000", getTimeForTest()})
		}
		return nil
	}).Close()
}

func assertSimpleDescribeTreeJobsRsp(t *testing.T, req *model.DescribeTreeJobsReq,
	expected *testSimpleDescribeTreeJobsRsp) *model.DescribeTreeJobsRsp {
	jobTreeService := new(JobTreeService)
	rsp, err := jobTreeService.Describe(context.Background(), req)
	assert.NoError(t, err)
	// for debug
	// printDescribeTreeJobsRspForTest(t, rsp)
	assert.Equal(t, expected, convertToTestSimpleDescribeTreeJobsRsp(rsp))
	return rsp
}

func printDescribeTreeJobsRspForTest(t *testing.T, rsp *model.DescribeTreeJobsRsp) {
	if rsp == nil {
		t.Logf("rsp is nil")
		return
	}
	rspBuf, _ := json.MarshalIndent(rsp, "", "\t")
	t.Logf("rsp: %s", string(rspBuf))
}

type testSimpleDescribeTreeJobsRsp struct {
	FolderIdSet []string
	JobIdSet    []string
	JobNum      int64
	HasMore     bool
}

func convertToTestSimpleDescribeTreeJobsRsp(rsp *model.DescribeTreeJobsRsp) *testSimpleDescribeTreeJobsRsp {
	if rsp == nil {
		return nil
	}
	testRsp := &testSimpleDescribeTreeJobsRsp{
		JobNum:  rsp.JobNum,
		HasMore: rsp.HasMore,
	}
	for _, child := range rsp.Children {
		testRsp.FolderIdSet = append(testRsp.FolderIdSet, child.Id)
	}
	for _, job := range rsp.JobSet {
		testRsp.JobIdSet = append(testRsp.JobIdSet, job.JobId)
	}
	return testRsp
}

// 为了避免单测时间都撞在一起
func getTimeForTest() string {
	c := timeCountForTest.Add(1)
	return time.Now().Add(time.Duration(c) * time.Second).Format(time.DateTime)
}
