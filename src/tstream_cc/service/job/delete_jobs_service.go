package service

import (
	"errors"
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service2 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cmq"
	service5 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/metadata"
	service4 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/tag"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/variable"
)

/**
 *
 * <AUTHOR>
 * @time 2019/4/27
 * @version 0.4.0
 * @since 0.4.0
 * @see
 */
func DoDeleteJobs(req *model.DeleteJobsReq) (string, string, *model.DeleteJobsRsp) {
	// 0. Print RequestId
	logger.Debugf("%s: DeleteJobs API called by AppId %d", req.RequestId, req.AppId)

	// 1. 如果作业列表是空，返回错误
	if len(req.JobIds) == 0 {
		msg := fmt.Sprintf("%s: InvalidParameterValue.JobIds. No JobIds specified", req.RequestId)
		logger.Error(msg)
		return controller.InvalidParameterValue, msg, nil
	}

	// 2. 对作业列表去重，如果size大于10，返回错误
	if len(req.JobIds) > constants.OPERATION_BATCH_SIZE_UPPER_LIMIT {
		errMsg := fmt.Sprintf("At least Delete %d Jobs one time", constants.OPERATION_BATCH_SIZE_UPPER_LIMIT)
		logger.Errorf("%s: Failed to delete jobs. %s", req.RequestId, errMsg)
		return controller.UnsupportedOperation, errMsg, nil
	}

	// 3. 逐个检查作业是否存在以及是否允许删除
	jobs := make([]*table.Job, 0)
	for i := 0; i < len(req.JobIds); i++ {
		job, err := service.WhetherJobExists(req.AppId, req.Region, req.JobIds[i])
		if err != nil {
			errCode := errorcode.GetCode(err)
			return errCode.GetCodeStr(), errCode.GetCodeDesc(), nil
		}
		// 鉴权
		err = auth.InnerAuthById(req.WorkSpaceId, req.IsSupOwner, job.ItemSpaceId, int64(req.AppId), req.SubAccountUin, req.Action, false)
		if err != nil {
			logger.Errorf("%s: User  uin %s is NOT authenticated, refuse to serve", req.RequestId, req.Uin)
			errCode := errorcode.GetCode(err)
			return errCode.GetCodeStr(), errCode.GetCodeDesc(), nil
		}
		if !WhetherJobCanBeDeleted(job) {
			msg := fmt.Sprintf("%s: Job[%s] in status `%d`, not allowed to be deleted.", req.RequestId, job.SerialId, job.Status)
			logger.Error(msg)
			return controller.ResourceInUse, msg, nil
		}
		jobs = append(jobs, job)
	}

	// 4. 批量删除作业
	err := DoBatchDelete(req.AppId, req.JobIds)
	if err != nil {
		logger.Errorf("%s: Failed to delete batch of jobs, error: %+v", req.RequestId, err)
		return errorcode.InternalErrorCode_JobDeleteFailed.GetCodeStr(), controller.NULL, nil
	}

	success, err := service4.DoDeleteResourceRefs(req.JobIds)
	if err != nil {
		logger.Errorf("%s: Failed to delete ResourceRef of jobs, error: %+v", req.RequestId, err)
		return errorcode.InternalErrorCode_ResourceRefDeleteFailed.GetCodeStr(), controller.NULL, nil
	}

	if !success {
		logger.Errorf("%s: Failed to delete ResourceRef of jobs, success: %+v", req.RequestId, success)
		return errorcode.InternalErrorCode_ResourceRefDeleteFailed.GetCodeStr(), controller.NULL, nil
	}

	// 5. 删除Job的元数据引用信息 21.06.18 andylhuang
	refs, err := service5.DeleteMetaRefs(req.JobIds)
	if err != nil {
		logger.Errorf("%s: Failed to delete JobMetaTableReference of jobs, error: %+v", req.RequestId, err)
		return errorcode.InternalErrorCode_MetaRefDeleteFailed.GetCodeStr(), controller.NULL, nil
	}

	if !refs {
		logger.Errorf("%s: Failed to delete JobMetaTableReference of jobs, success: %+v", req.RequestId, success)
		return errorcode.InternalErrorCode_MetaRefDeleteFailed.GetCodeStr(), controller.NULL, nil
	}

	variableRefs, err := variable.DeleteVariableRefsBySerialId(req.JobIds)
	if err != nil {
		logger.Errorf("%s: Failed to delete VariableRef of jobs, error: %+v", req.RequestId, err)
		return errorcode.InternalErrorCode_VariableRefDeleteFailed.GetCodeStr(), controller.NULL, nil
	}

	if !variableRefs {
		logger.Errorf("%s: Failed to delete VariableRef of jobs, success: %+v", req.RequestId, success)
		return errorcode.InternalErrorCode_VariableRefDeleteFailed.GetCodeStr(), controller.NULL, nil
	}

	logger.Debugf("%s: DeleteJobs succeeded for jobs %+v", req.RequestId, req.JobIds)
	metaLineage, err := service5.DeleteMetaTableLineageBySerialId(req.JobIds)
	if err != nil {
		logger.Errorf("%s: Failed to delete MetaTableLineage of jobs, error: %+v", req.RequestId, err)
		return errorcode.InternalErrorCode_MetaLineageDeleteFailed.GetCodeStr(), controller.NULL, nil
	}

	if !metaLineage {
		logger.Errorf("%s: Failed to delete MetaTableLineage of jobs, success: %+v", req.RequestId, success)
		return errorcode.InternalErrorCode_MetaLineageDeleteFailed.GetCodeStr(), controller.NULL, nil
	}

	// 6. 为作业解绑标签
	unBindToTagErr := tag.JobUnBindToTagWithJobSerialIds(req.RequestId, req.JobIds)
	if unBindToTagErr != nil {
		logger.Errorf("%s: Failed to JobUnBindToTagWithJobSerialIds of jobs, error: %+v", req.RequestId, unBindToTagErr)
	}

	// 7. 与默认告警策略解绑
	for _, serialId := range req.JobIds {
		err = cmq.SendMsgToBarad(serialId, int64(req.AppId), req.Uin, req.Region, cmq.MSG_OP_DELETE)
		if err != nil {
			// 无需返回错误
			logger.Errorf("%s send message to barad with error %v", req.RequestId, err)
		}
	}

	// 8.解绑作业默认告警

	return controller.OK, controller.NULL, &model.DeleteJobsRsp{}
}

func WhetherJobCanBeDeleted(job *table.Job) bool {
	jobStatus := job.Status
	if jobStatus != constants.JOB_STATUS_RUNNING && jobStatus != constants.JOB_STATUS_PROGRESS {
		return true
	}
	return false
}

func DoBatchDelete(appId int32, jobIds []string) (err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Delete batch of jobs panic, for jobIds:%+v, errors:%+v", jobIds, errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()

	txManager := service2.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		for i := 0; i < len(jobIds); i++ {
			args := make([]interface{}, 0)
			args = append(args, jobIds[i])
			args = append(args, appId)
			sql := "SELECT * FROM Job WHERE SerialId=? AND AppId=?"

			_, data, err := tx.Query(sql, args)
			if err != nil {
				logger.Errorf("DoBatchDelete sql %s, args: %+v, error: %+v", sql, args, err)
				return err
			}

			if len(data) != 1 {
				logger.Errorf("DoBatchDelete sql: %s, args: %+v cnt(%d) != 1", sql, args, len(data))
				err = fmt.Errorf("DoBatchDelete found job error, cnt(%d) != 1", len(data))
				return err
			}

			job := &table.Job{}
			util.ScanMapIntoStruct(job, data[0])

			sql = "UPDATE Job SET Status=? WHERE AppId=? AND SerialId=?"
			result := tx.ExecuteSqlWithArgs(sql, constants.JOB_STATUS_DELETE, appId, jobIds[i])
			rowsAffected, err := result.RowsAffected()
			if err != nil {
				logger.Errorf("Failed to Update Job[%s] to `Delete` Status, with errors: %+v", jobIds[i], err)
				return err
			} else if rowsAffected != 1 {
				msg := fmt.Sprintf("Failed to Update Job[%s] to `Delete` Status, rowsAffected != 1, actual: %d", jobIds[i], rowsAffected)
				logger.Error(msg)
				return errors.New(msg)
			}

			args = make([]interface{}, 0)
			args = append(args, constants.JOB_CONFIG_STATUS_DELETE)
			args = append(args, job.Id)

			tx.ExecuteSql("UPDATE JobConfig SET Status=? WHERE JobId=? ", args)

			sql = "delete from JobConfigSqlCode WHERE JobId = ?"

			tx.ExecuteSqlWithArgs(sql, job.Id)
		}

		return err
	}).Close()

	return err
}
