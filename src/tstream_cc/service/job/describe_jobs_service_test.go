package service_test

import (
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource"
	"testing"
)

func init() {

}

func TestGetJobsByConnectorOptions(t *testing.T) {
	service2.InitTestDB(service2.WALLYDB)
	req := &model.DescribeJobsReq{}
	req.AppId = 1257058945
	req.Region = "ap-chengdu"
	req.ConnectorOptions = "setats"
	req.Filters = make([]struct {
		Name   string
		Values []string
	}, 1)
	req.Filters[0].Name = "ClusterId"
	req.Filters[0].Values = make([]string, 1)
	req.Filters[0].Values[0] = "cluster-pgel2z70"

	service.GetJobsByConnectorOptions(req)
}
