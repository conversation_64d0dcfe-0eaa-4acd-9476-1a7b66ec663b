package etl

import (
	"encoding/json"
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/command"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/etl"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cluster_admin_protocol"
	model2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/etl"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service1 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
)

func DescribeConnSubItemSchema(req *model.DescribeConnSubItemSchemaReq, evenId int64) (*model.DescribeConnSubItemSchemaRsp, error) {
	// 1. 检查作业是否存在
	job, err := service.WhetherJobExists(int32(req.AppId), req.Region, req.JobId)
	if err != nil {
		logger.Errorf("%s Failed to list job ,err:%s", req.RequestId, err.Error())
		return nil, err
	}

	// 鉴权
	err = auth.InnerAuthById(req.WorkSpaceId, req.IsSupOwner, job.ItemSpaceId, int64(req.AppId), req.SubAccountUin, req.Action, false)
	if err != nil {
		logger.Errorf("%s: User  uin %s is NOT authenticated, refuse to serve", req.RequestId, req.Uin)
		return nil, err
	}
	// 2、查询Connection
	// 读取数据库
	cond := dao.NewCondition()
	cond.Eq("SerialId", req.ConnId)
	cond.Eq("Status", model2.CONNECTION_STATUS_NORMAL)
	cond.Eq("AppId", req.AppId)
	cond.Eq("Region", req.Region)
	conns, err := ListConnections(cond, -1, -1)
	if err != nil {
		logger.Errorf("%s Failed to query connection resource ,err:%s", req.RequestId, err.Error())
		return nil, err
	}
	if len(conns) == 0 {
		return nil, errorcode.ConnectionErrorCode_DescribeFailed.ReplaceDesc(" Can not find connection resource connId " + req.ConnId)
	}
	connection := conns[0]
	configuration := GetConnectorConfiguration(connection.Type)
	mappings := make(map[string]string)
	err = json.Unmarshal([]byte(req.Properties), &mappings)
	if err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}
	connectorProps := configuration.Mapper(mappings, connection)

	err = CheckPropertySafe("", connectorProps, nil)
	if err != nil {
		logger.Errorf("CheckProperties error: %+v", err)
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}

	bytes, err := json.Marshal(connectorProps)
	if err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}

	clusterGroup, err := service1.GetClusterGroupByClusterId(job.ClusterId)
	if err != nil {
		logger.Errorf("%s Failed to GetClusterGroupByClusterId ,err:%s", req.RequestId, err.Error())
		return nil, err
	}

	// 3.构建访问ClusterAdmin的请求
	para := &model.DescribeTablePara{
		Params: model.Param{
			JobId:      req.JobId,
			RequestId:  req.RequestId,
			Properties: string(bytes),
			State:      0,
		},
		Action:          constants.ETL_COMMAND_ACTION_DESCRIBE_TABLE_SCHEMA,
		JobType:         constants.JOB_TYPE_ETL,
		AppId:           req.AppId,
		ClusterSerialId: clusterGroup.SerialId,
	}

	caReq := cluster_admin_protocol.NewClusterAdminReq(req.RequestId, InterfaceName, para)
	sendData, err := json.Marshal(caReq)
	if err != nil {
		logger.Errorf("%s Error converting SendData", req.RequestId)
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}
	commandService, err := command.NewCommandService(job.ClusterId)
	if err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}
	//4. 发起CA请求
	url, err := command.GetCAOrCSUrl(commandService.Cluster)
	if err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}
	rsp, err := commandService.DoRequest(&command.CommandRequest{
		ReqId:    req.RequestId,
		Url:      url,
		SendData: string(sendData),
		Uin:      req.Uin,
		Apikey:   ApiKey,
		Timeout:  getTimeout(),
		Callback: "",
	})
	if err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}
	// 5. 处理返回结果
	caRes := &model.DescribeTableClusterAdminRsp{}
	err = json.Unmarshal([]byte(rsp), caRes)
	if err != nil {
		logger.Error("error parsing response: ret:", rsp, "\t err:", err)
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}
	if err = commandService.HandlerException(caRes.Data.ReturnCode, req.RequestId, caRes.Data.ReturnMsg); err != nil {
		return nil, err
	}
	res := &model.DescribeConnSubItemSchemaRsp{
		caRes.Data.Params.Schema.PrimaryKeyColumns,
		caRes.Data.Params.Schema.Columns,
	}
	return res, nil
}

const (
	CONNECTOR_MYSQL          = "jdbc-mysql"
	CONNECTOR_POSTGRESQL     = "jdbc-postgresql"
	CONNECTOR_ELASTICSEARCH6 = "elasticsearch-6"
	CONNECTOR_ELASTICSEARCH7 = "elasticsearch-7"
	CONNECTOR_CLICKHOUSE     = "clickhouse"
	CONNECTOR_DORIS          = "doris"
)

type ConnectorConfiguration struct {
	Type   string
	Mapper func(properties map[string]string, connection *model2.BaseConnection) map[string]string
}

type OptionMapping struct {
	Key   string
	Value string
}

func GetConnectorConfiguration(connType int) ConnectorConfiguration {
	switch connType {
	case MYSQL:
		return ConnectorConfiguration{
			Type: CONNECTOR_MYSQL,
			Mapper: func(properties map[string]string, connection *model2.BaseConnection) map[string]string {
				connProperties := make(map[string]interface{})
				_ = json.Unmarshal([]byte(connection.Properties), &connProperties)
				ip := connProperties["Ip"]
				port := connProperties["Port"]
				database := properties["database"]
				tableName := properties["tableName"]
				fmtUrl := "**************************************************************************************************************************"
				url := fmt.Sprintf(fmtUrl, ip, fmt.Sprint(port), database)
				properties["connector"] = CONNECTOR_MYSQL
				properties["url"] = url
				properties["table-name"] = tableName
				properties["username"] = connection.Username
				properties["password"] = connection.Password
				return properties
			}}
	case PostgreSQL:
		return ConnectorConfiguration{
			Type: CONNECTOR_POSTGRESQL,
			Mapper: func(properties map[string]string, connection *model2.BaseConnection) map[string]string {
				connProperties := make(map[string]interface{})
				_ = json.Unmarshal([]byte(connection.Properties), &connProperties)
				ip := connProperties["Ip"]
				port := connProperties["Port"]
				database := properties["database"]
				schemaName := properties["schemaName"]
				tableName := properties["tableName"]
				fmtUrl := "*******************************************"
				url := fmt.Sprintf(fmtUrl, ip, fmt.Sprint(port), database, schemaName)
				properties["connector"] = CONNECTOR_POSTGRESQL
				properties["url"] = url
				properties["schema-name"] = schemaName
				properties["table-name"] = tableName
				properties["username"] = connection.Username
				properties["password"] = connection.Password
				return properties
			}}
	case ClickHouse:
		return ConnectorConfiguration{
			Type: CONNECTOR_CLICKHOUSE,
			Mapper: func(properties map[string]string, connection *model2.BaseConnection) map[string]string {
				connProperties := make(map[string]interface{})
				_ = json.Unmarshal([]byte(connection.Properties), &connProperties)
				ip := connProperties["Ip"]
				port := connProperties["Port"]
				database := properties["database"]
				tableName := properties["tableName"]
				fmtUrl := "clickhouse://%s:%s"
				url := fmt.Sprintf(fmtUrl, ip, fmt.Sprint(port))
				properties["connector"] = CONNECTOR_CLICKHOUSE
				properties["url"] = url
				properties["database-name"] = database
				properties["table-name"] = tableName
				properties["username"] = connection.Username
				properties["password"] = connection.Password
				return properties
			}}
	case Elasticsearch6:
		return ConnectorConfiguration{
			Type: CONNECTOR_ELASTICSEARCH6,
			Mapper: func(properties map[string]string, connection *model2.BaseConnection) map[string]string {
				connProperties := make(map[string]string)
				_ = json.Unmarshal([]byte(connection.Properties), &connProperties)
				hosts := connProperties["Hosts"]
				index := properties["indexName"]
				typeName := properties["typeName"]
				properties["connector"] = CONNECTOR_ELASTICSEARCH6
				properties["hosts"] = hosts
				properties["index"] = index
				properties["document-type"] = typeName
				properties["username"] = connection.Username
				properties["password"] = connection.Password
				return properties
			}}
	case Elasticsearch7:
		return ConnectorConfiguration{
			Type: CONNECTOR_ELASTICSEARCH7,
			Mapper: func(properties map[string]string, connection *model2.BaseConnection) map[string]string {
				connProperties := make(map[string]string)
				_ = json.Unmarshal([]byte(connection.Properties), &connProperties)
				hosts := connProperties["Hosts"]
				index := properties["indexName"]
				properties["connector"] = CONNECTOR_ELASTICSEARCH7
				properties["hosts"] = hosts
				properties["index"] = index
				properties["username"] = connection.Username
				properties["password"] = connection.Password
				return properties
			}}

	case Doris:
		return ConnectorConfiguration{
			Type: CONNECTOR_DORIS,
			Mapper: func(properties map[string]string, connection *model2.BaseConnection) map[string]string {
				connProperties := make(map[string]interface{})
				_ = json.Unmarshal([]byte(connection.Properties), &connProperties)
				database := properties["database"]
				tableName := properties["tableName"]
				fmtUrl := "jdbc:mysql://%s:%s/%s"
				fmtHttpUrl := "http://%s:%s/api/%s/%s/_schema"
				hosts := connProperties["Hosts"]
				port := connProperties["QueryPort"]
				httpPort := connProperties["HttpPort"]
				url := fmt.Sprintf(fmtUrl, hosts, fmt.Sprint(port), database)
				httpUrl := fmt.Sprintf(fmtHttpUrl, hosts, fmt.Sprint(httpPort), database, tableName)
				properties["url"] = url
				properties["http-url"] = httpUrl
				properties["connector"] = CONNECTOR_DORIS
				properties["username"] = connection.Username
				properties["password"] = connection.Password
				properties["database-name"] = database
				properties["table-name"] = tableName
				return properties
			}}
	}
	return ConnectorConfiguration{Type: constants.EMPTY}
}
