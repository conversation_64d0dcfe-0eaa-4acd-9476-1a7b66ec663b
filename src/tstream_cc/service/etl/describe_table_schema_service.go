package etl

import (
	"encoding/json"
	"tencentcloud.com/tstream_galileo/src/common/command"
	"tencentcloud.com/tstream_galileo/src/common/configure"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/etl"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cluster_admin_protocol"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service1 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
)

const (
	ConfItemTimeout = "describeTableSchema.timeout"
	InterfaceName   = "qcloud.clusteradmin.command.etl"
	ApiKey          = "DescribeTableSchema"
)

func getTimeout() int64 {
	var timeout = configure.GetConfInt64Value(constants.GALILEO_CONF_FILE, ConfItemTimeout, 11)
	return timeout
}

func DoDescribeTableSchema(req *model.DescribeTableSchemaReq, evenId int64) (*model.DescribeTableSchemaRsp, error) {
	// 1. 检查作业是否存在
	job, err := service.WhetherJobExists(int32(req.AppId), req.Region, req.JobId)
	if err != nil {
		logger.Errorf("%s Failed to list job ,err:%s", req.RequestId, err.Error())
		return nil, err
	}

	clusterGroup, err := service1.GetClusterGroupByClusterId(job.ClusterId)
	if err != nil {
		logger.Errorf("%s Failed to GetClusterGroupByClusterId ,err:%s", req.RequestId, err.Error())
		return nil, err
	}

	err = CheckPropertySafe(req.Properties, nil, nil)
	if err != nil {
		logger.Errorf("CheckProperties error: %+v", err)
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}

	// 2.构建访问ClusterAdmin的请求
	para := &model.DescribeTablePara{
		Params: model.Param{
			JobId:      req.JobId,
			RequestId:  req.RequestId,
			Properties: req.Properties,
			State:      0,
		},
		Action:          constants.ETL_COMMAND_ACTION_DESCRIBE_TABLE_SCHEMA,
		JobType:         constants.JOB_TYPE_ETL,
		AppId:           req.AppId,
		ClusterSerialId: clusterGroup.SerialId,
	}
	caReq := cluster_admin_protocol.NewClusterAdminReq(req.RequestId, InterfaceName, para)
	sendData, err := json.Marshal(caReq)
	if err != nil {
		logger.Errorf("%s Error converting SendData", req.RequestId)
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}
	service, err := command.NewCommandService(job.ClusterId)
	if err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}
	url, err := command.GetCAOrCSUrl(service.Cluster)
	if err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}
	//3. 发起请求
	rsp, err := service.DoRequest(&command.CommandRequest{
		ReqId:    req.RequestId,
		Url:      url,
		SendData: string(sendData),
		Uin:      req.Uin,
		Apikey:   ApiKey,
		Timeout:  getTimeout(),
		Callback: "",
	})
	if err != nil {
		return nil, err
	}
	// 4. 处理返回结果
	caRes := &model.DescribeTableClusterAdminRsp{}
	err = json.Unmarshal([]byte(rsp), caRes)
	if err != nil {
		logger.Error("error parsing response: ret:", rsp, "\t err:", err)
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}
	if err = service.HandlerException(caRes.Data.ReturnCode, req.RequestId, caRes.Data.ReturnMsg); err != nil {
		return nil, err
	}
	res := &model.DescribeTableSchemaRsp{}
	res.PrimaryKeyColumns = caRes.Data.Params.Schema.PrimaryKeyColumns
	res.Columns = caRes.Data.Params.Schema.Columns
	return res, nil
}
