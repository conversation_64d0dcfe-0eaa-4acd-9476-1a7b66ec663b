package etl

import (
	"encoding/json"
	"fmt"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/etl"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource"
	"testing"
)

func TestConvertJson(t *testing.T) {
	service2.InitTestDB(service2.WALLYDB)

	jobName := "test19"
	sourceConn := ""
	sinkConn := ""
	sql := ""
	res, err := DoConvertNew(sql, sourceConn, sinkConn)
	if err != nil {
		fmt.Printf("error")
	} else {
		txManager := service.GetTxManager()
		txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
			tx.ExecuteSqlWithArgs("insert into NewTest1(JobName,ProgramArgs) values (?,?)", jobName, res)
			return nil
		}).Close()
	}

}

func DoConvertNew(orgString string, sourceConn string, sinkConn string) (string, error) {

	orgJson := &model.EtlJob{}
	if err := json.Unmarshal([]byte(orgString), &orgJson); err != nil {
		return "", fmt.Errorf("orgJson unmarshal error")
	}

	// 转化三个 Node
	sourceNode, sourceName, err := ConvertSourceNodeNew(orgJson, sourceConn)
	if err != nil {
		return "", fmt.Errorf("SourceNode Convert Error")
	}
	calculatorNode, calculatorName, err := ConvertCalculatorNodeNew(orgJson, sourceNode)
	if err != nil {
		return "", fmt.Errorf("CalculatorNode Convert Error")
	}
	sinkNode, sinkName, err := ConvertSinkNodeNew(orgJson, calculatorNode, sinkConn)
	if err != nil {
		return "", fmt.Errorf("SinkNode Convert Error")
	}

	// 组装 Edge
	edges := ConvertEdges(sourceName, calculatorName, sinkName)

	// 组装 Node
	nodes := make([]*NewNode, 0)
	nodes = append(nodes, sourceNode, calculatorNode, sinkNode)

	// 组装最终Canvas
	newJson := &NewCanvas{}
	newJson.Edges = edges
	newJson.Nodes = nodes
	resJson, _ := json.Marshal(&newJson)
	Stringjson := string(resJson)

	// 组装最终 EtlJobCanvas
	newEtlJobCanvas := &NewEtlJobCanvas{}
	newEtlJobCanvas.EtlJobCanvas = Stringjson

	res, _ := json.Marshal(&newEtlJobCanvas)

	return string(res), nil
}

func ConvertSourceNodeNew(orgJson *model.EtlJob, sourceConn string) (*NewNode, string, error) {

	sourcePro := orgJson.EtlJob.Sources[0].Properties
	sourceSchema := orgJson.EtlJob.Sources[0].Schema

	id := strings.ReplaceAll(sourcePro["_connector"], "-", "_") + "_source" + "_0"
	name := strings.ReplaceAll(sourcePro["_connector"], "-", "_")
	// 转化 tables
	tables := ConvertTables(sourcePro)
	// 转化 inputShema/outputSchema
	inputSchema := ConvertSchemaNew(&sourceSchema, id, id, SOURCE_SCHEMA_INPUT)
	outputSchema := ConvertSchemaNew(&sourceSchema, id, id, SOURCE_SCHEMA_OUTPUT)

	// 组装  data
	advanced := make([]*NewProp, 0)
	advancedEach := &NewProp{
		Key:   "scan.incremental.snapshot.enabled",
		Value: "false",
	}
	advanced = append(advanced, advancedEach)
	data := &NewData{
		Type:         "source",
		Name:         name,
		InputSchema:  inputSchema,
		OutputSchema: outputSchema,
		ConnId:       sourceConn,
		Advanced:     advanced,
		Tables:       tables,
		Props:        nil,
	}

	// 组装  uiData
	uiData := ConvertUiDataNew(data, sourcePro, "source", sourceConn)

	// 组装  node
	m := &NewNode{}
	m.Id = id
	m.Type = "html"
	m.X = 160
	m.Y = 170
	m.Properties.Data = data
	m.Properties.Uidata = uiData

	return m, id, nil
}

func ConvertCalculatorNodeNew(orgJson *model.EtlJob, sourceNode *NewNode) (*NewNode, string, error) {
	// 转换 Transformer/outputSchema
	sinkPro := orgJson.EtlJob.Sinks[0].Properties
	orgJsonSinkCol := orgJson.EtlJob.Sinks[0].Schema.Columns
	orgJsonSourceCol := orgJson.EtlJob.Sources[0].Schema.Columns
	transformer := make(map[string]map[string]interface{})
	columnFormula := make(map[string]interface{})
	for k, v := range orgJsonSinkCol {
		each := make(map[string]string)
		each["name"] = v.Name
		each["type"] = v.DataType
		each["etlType"] = v.DataType
		each["from"] = orgJsonSourceCol[k].Name
		each["fromTable"] = sourceNode.Id
		each["opType"] = "default"
		columnFormula[v.Name] = each
	}
	transformer["columnFormula"] = columnFormula

	id := "calculator_operator_0"
	outputSchema := ConvertSchemaNew(&orgJson.EtlJob.Sinks[0].Schema, id, sourceNode.Id, CALCULATOR_SCHEMA_OUTPUT)
	// 组装 data
	data := &NewData{
		Type:         "operator",
		Name:         "calculator",
		Transformer:  transformer,
		InputSchema:  sourceNode.Properties.Data.InputSchema,
		OutputSchema: outputSchema,
	}

	// 组装  uiData
	uiData := ConvertUiDataNew(data, sinkPro, "calculator", "")

	// 组装  node
	m := &NewNode{}
	m.Id = id
	m.Type = "html"
	m.X = 390
	m.Y = 170
	m.Properties.Data = data
	m.Properties.Uidata = uiData

	return m, id, nil
}

func ConvertSinkNodeNew(orgJson *model.EtlJob, sourceNode *NewNode, sinkConn string) (*NewNode, string, error) {
	sinkPro := orgJson.EtlJob.Sinks[0].Properties
	id := strings.ReplaceAll(sinkPro["_connector"], "-", "_") + "_sink" + "_0"
	name := strings.ReplaceAll(sinkPro["_connector"], "-", "_")
	if name == "elasticsearch_6" || name == "elasticsearch_7" {
		name = "elasticsearch"
	}
	tables := ConvertTables(sinkPro)
	outputSchema := ConvertSchemaNew(&orgJson.EtlJob.Sinks[0].Schema, id, sourceNode.Id, SINK_SCHEMA_OUTPUT)
	// 组装 data
	data := &NewData{
		Type:         "sink",
		Name:         name,
		Tables:       tables,
		ConnId:       sinkConn,
		Props:        nil,
		Advanced:     nil,
		InputSchema:  sourceNode.Properties.Data.OutputSchema,
		OutputSchema: outputSchema,
	}

	// 组装  uiData
	uiData := ConvertUiDataNew(data, sinkPro, "sink", sinkConn)

	// 组装  node
	m := &NewNode{}
	m.Id = id
	m.Type = "html"
	m.X = 670
	m.Y = 170
	m.Properties.Data = data
	m.Properties.Uidata = uiData

	return m, id, nil
}

func ConvertTables(properties map[string]string) [][]string {
	// 转换 tables
	tables := make([][]string, 0)
	table := make([]string, 0)
	switch {
	case properties["_connector"] == "clickhouse" || properties["_connector"] == "mysql-cdc":
		databaseName := properties["database-name"]
		tableName := properties["table-name"]
		table = append(table, databaseName, tableName)
	case properties["_connector"] == "mysql-jdbc":
		tableName := properties["table-name"]
		databaseName := strings.Split(properties["url"], "/")[3]
		table = append(table, databaseName, tableName)
	case properties["_connector"] == "postgres-jdbc":
		tableName := properties["table-name"]
		j := strings.Split(properties["url"], "/")[3]
		databaseName := strings.Split(j, "?")[0]
		table = append(table, databaseName, tableName)
	case properties["_connector"] == "elasticsearch-6" || properties["_connector"] == "elasticsearch-7":
		typeName := properties["_indexType"]
		index := properties["index"]
		table = append(table, typeName, index)
	}
	tables = append(tables, table)
	fmt.Println(tables)
	return tables
}

func ConvertSchemaNew(sourceSchema *model.Schema, tableName string, fromtableName string, schemaType int) map[string]*NewTableSchema {
	outputSchema := make(map[string]*NewTableSchema)
	schema := &NewTableSchema{}
	columns := make(map[string]*NewTableColumn)
	for _, column := range sourceSchema.Columns {
		if column.Name == "" || column.DataType == "" {
			continue
		}
		eachColumn := &NewTableColumn{}
		eachColumn.Name = column.Name
		eachColumn.Type = column.DataType
		switch {
		case schemaType == SOURCE_SCHEMA_OUTPUT:
			eachColumn.EtlType = column.DataType
		case schemaType == CALCULATOR_SCHEMA_OUTPUT || schemaType == SINK_SCHEMA_OUTPUT:
			eachColumn.EtlType = column.DataType
			eachColumn.From = column.Name
			eachColumn.FromTable = fromtableName
			if schemaType == CALCULATOR_SCHEMA_OUTPUT {
				eachColumn.OpType = "default"
			}
		}
		columns[eachColumn.Name] = eachColumn
	}
	schema.TableName = tableName
	schema.Columns = columns
	if sourceSchema.PrimaryKeyColumns != "" {
		schema.PrimaryKey = sourceSchema.PrimaryKeyColumns
	}
	outputSchema[tableName] = schema
	return outputSchema
}

func ConvertUiDataNew(data *NewData, properties map[string]string, convertType string, conn string) *Uidata {
	formData := make(map[string]interface{})
	var key string
	var nodeType string
	var dbType string
	var configStatus string
	var isSilentMode bool

	if convertType == "source" {
		if properties["_connector"] == "mysql-cdc" {
			key = "SOURCE_MYSQL"
			nodeType = "source"
			dbType = "Mysql"
			configStatus = "CONFIG_IS_SUCCESS"
			isSilentMode = true
			// 转化 props
			props := make(map[string]string)
			props["timeZone"] = "Asia/Shanghai"
			props["serverId"] = "5015"
			props["skipOp"] = "u"
			// 转化 advanced
			advanced := make([]*NewProp, 0)
			advancedEach := &NewProp{
				Key:   "scan.incremental.snapshot.enabled",
				Value: "false",
			}
			advanced = append(advanced, advancedEach)

			nodeName := "mysql-source"
			connId := conn

			// 组装 formData
			formData["node_name"] = nodeName
			formData["connId"] = connId
			formData["props"] = props
			formData["advanced"] = advanced
			formData["tables"] = data.Tables
		}
	} else if convertType == "sink" {
		if properties["_connector"] == "mysql-jdbc" {
			key = "SINK_MYSQL"
			nodeType = "sink"
			dbType = "Mysql"
			configStatus = "CONFIG_IS_SUCCESS"
			isSilentMode = true
			// 转化 props
			props := make(map[string]string)
			props["timeZone"] = "Asia/Shanghai"
			props["bufferFlushMaxRows"] = "100"

			connId := conn
			nodeName := "mysql-sink"
			isEs6 := false

			// 组装 formData
			formData["node_name"] = nodeName
			formData["connId"] = connId
			formData["props"] = props
			formData["tables"] = data.Tables
			formData["isEs6"] = isEs6
		}
		if properties["_connector"] == "elasticsearch-6" || properties["_connector"] == "elasticsearch-7" {
			key = "SINK_ELASTICSEARCH"
			nodeType = "sink"
			dbType = "ElasticSearch"
			configStatus = "CONFIG_IS_SUCCESS"
			isSilentMode = true
			// 转化 props
			props := make(map[string]string)
			props["flushMaxActions"] = "200"
			props["failureHandler"] = "ignore"

			connId := conn
			nodeName := "ElasticSearch"
			var isEs6 bool
			if properties["_connector"] == "elasticsearch-7" {
				isEs6 = false
			} else {
				isEs6 = true
			}
			documentType := data.Tables[0][0]
			index := data.Tables[0][1]

			// 组装 formData
			formData["node_name"] = nodeName
			formData["connId"] = connId
			formData["props"] = props
			formData["tables"] = data.Tables
			formData["isEs6"] = isEs6
			formData["document-type"] = documentType
			formData["index"] = index
		}
		if properties["_connector"] == "clickhouse" {
			key = "SINK_CLICKHOUSE"
			nodeType = "sink"
			dbType = "ClickHouse"
			configStatus = "CONFIG_IS_SUCCESS"
			isSilentMode = true
			// 转化 props
			props := make(map[string]string)
			props["batchSize"] = "1000"
			props["collapsingField"] = "Sign"

			connId := conn
			nodeName := "ClickHouse"
			isEs6 := false

			// 组装 formData
			formData["node_name"] = nodeName
			formData["connId"] = connId
			formData["props"] = props
			formData["tables"] = data.Tables
			formData["isEs6"] = isEs6
		}
	} else {
		key = "FORMAT_WASH"
		nodeType = "operator"
		dbType = "Wash"
		configStatus = "CONFIG_IS_SUCCESS"
		isSilentMode = true
		// 转化 formData
		formData = nil
	}

	uiData := &Uidata{
		Key:          key,
		NodeType:     nodeType,
		DbType:       dbType,
		ConfigStatus: configStatus,
		IsSilentMode: isSilentMode,
		FormData:     formData,
	}
	return uiData
}

func ConvertEdges(sourceName string, calculatorName string, sinkName string) []*NewEdge {
	edges := make([]*NewEdge, 0)
	// edge-1
	startPoint1 := make(map[string]int)
	endPoint1 := make(map[string]int)
	pointsList1 := make([]map[string]int, 0)
	startPoint1["x"] = 219
	startPoint1["y"] = 170
	endPoint1["x"] = 331
	endPoint1["y"] = 170
	for i := 1; i <= 4; i++ {
		pointsListEach := make(map[string]int)
		var x int
		var y int
		switch i {
		case 1:
			x = 219
			y = 170
		case 2:
			x = 320
			y = 170
		case 3:
			x = 230
			y = 170
		case 4:
			x = 331
			y = 170
		}
		pointsListEach["x"] = x
		pointsListEach["y"] = y
		pointsList1 = append(pointsList1, pointsListEach)
	}
	edg1 := &NewEdge{
		Id:           "f8cea020-d289-45a1-acf3-098ea30bd3d1",
		Type:         "custom-edge",
		SourceNodeId: sourceName,
		TargetNodeId: calculatorName,
		StartPoint:   startPoint1,
		EndPoint:     endPoint1,
		Properties:   nil,
		PointsList:   pointsList1,
	}

	// edge-2
	startPoint2 := make(map[string]int)
	endPoint2 := make(map[string]int)
	pointsList2 := make([]map[string]int, 0)
	startPoint2["x"] = 449
	startPoint2["y"] = 170
	endPoint2["x"] = 611
	endPoint2["y"] = 170
	for i := 1; i <= 4; i++ {
		pointsListEach := make(map[string]int)
		var x int
		var y int
		switch i {
		case 1:
			x = 449
			y = 170
		case 2:
			x = 550
			y = 170
		case 3:
			x = 510
			y = 170
		case 4:
			x = 611
			y = 170
		}
		pointsListEach["x"] = x
		pointsListEach["y"] = y
		pointsList2 = append(pointsList2, pointsListEach)
	}
	edg2 := &NewEdge{
		Id:           "ca1031e1-80c9-40e1-9882-48d13d335333",
		Type:         "custom-edge",
		SourceNodeId: calculatorName,
		TargetNodeId: sinkName,
		StartPoint:   startPoint2,
		EndPoint:     endPoint2,
		Properties:   nil,
		PointsList:   pointsList2,
	}
	edges = append(edges, edg1, edg2)
	return edges
}

const (
	SOURCE_SCHEMA_INPUT      = 1
	SOURCE_SCHEMA_OUTPUT     = 2
	CALCULATOR_SCHEMA_INPUT  = 3
	CALCULATOR_SCHEMA_OUTPUT = 4
	SINK_SCHEMA_INPUT        = 5
	SINK_SCHEMA_OUTPUT       = 6
)

type NewEtlJobCanvas struct {
	EtlJobCanvas string `json:"EtlJobCanvas"`
}

type NewCanvas struct {
	Nodes []*NewNode `json:"nodes"`
	Edges []*NewEdge `json:"edges"`
}

type NewNode struct {
	Id         string `json:"id"`
	Type       string `json:"type"`
	X          int    `json:"x"`
	Y          int    `json:"y"`
	Properties struct {
		Uidata *Uidata  `json:"uiData"`
		Data   *NewData `json:"data"`
	} `json:"properties"`
}

type Uidata struct {
	Key          string                 `json:"key"`
	NodeType     string                 `json:"nodeType"`
	DbType       string                 `json:"dbType"`
	ConfigStatus string                 `json:"configStatus"`
	IsSilentMode bool                   `json:"isSilentMode"`
	FormData     map[string]interface{} `json:"formData"`
}

type NewData struct {
	Type         string                            `json:"type"`
	Name         string                            `json:"name"`
	Transformer  map[string]map[string]interface{} `json:"transformer"`
	InputSchema  map[string]*NewTableSchema        `json:"inputSchema"`
	OutputSchema map[string]*NewTableSchema        `json:"outputSchema"`
	ConnId       string                            `json:"connId"`
	Advanced     []*NewProp                        `json:"advanced"`
	Tables       [][]string                        `json:"tables"`
	Props        map[string]string                 `json:"props"`
}

type NewProp struct {
	Key   string
	Value string
}

type NewTableSchema struct {
	Columns    map[string]*NewTableColumn `json:"columns"`
	PrimaryKey string                     `json:"primaryKey"`
	TableName  string                     `json:"tableName"`
}

type NewTableColumn struct {
	Name      string `json:"name"`
	Type      string `json:"type"`
	EtlType   string `json:"etlType"`
	From      string `json:"from"`
	FromTable string `json:"fromTable"`
	OpType    string `json:"opType"`
}

type NewEdge struct {
	Id           string           `json:"id"`
	Type         string           `json:"type"`
	TargetNodeId string           `json:"targetNodeId"`
	SourceNodeId string           `json:"sourceNodeId"`
	StartPoint   map[string]int   `json:"startPoint"`
	EndPoint     map[string]int   `json:"endPoint"`
	Properties   map[string]int   `json:"properties"`
	PointsList   []map[string]int `json:"pointsList"`
}
