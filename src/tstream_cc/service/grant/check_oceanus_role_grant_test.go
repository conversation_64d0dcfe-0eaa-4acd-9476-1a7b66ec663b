package service_test

import (
	"fmt"
	"testing"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/grant"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/grant"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource"
)

func TestDoCheckOceanusRoleGrant(t *testing.T) {
	service2.InitTestDB(service2.WALLYDB)
	req := &model.CheckOceanusRoleGrantReq{
		AppId:              **********,
		Uin:                "************",
		SubAccountUin:      "************",
		Region:             "ap-guangzhou",
	}

	isOk, msg1, rsp := service.DoCheckOceanusRoleGrant(req)

	if isOk == controller.OK {
		t.Logf("check oceanus role success %+v", rsp)
		println(fmt.Sprintf("check oceanus role success %+v", rsp))
	} else {
		t.<PERSON><PERSON><PERSON>("create job failed isOk %s msg1 %s", isOk, msg1)
	}
}


