package service

import (
	"tencentcloud.com/tstream_galileo/src/common/auth"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/grant"
)

type CheckRoleAuthorizationService struct {
}

func (o *CheckRoleAuthorizationService) CheckRoleAuthorization(
	request *model.CheckRoleAuthorizationRequest) (response interface{}, err error) {

	//鉴权
	_, err = auth.PreCheckAuth(request.Uin, request.SubAccountUin, request.AppId, request.OceanusAction, request.WorkSpaceId)
	if err != nil {
		logger.Errorf("%s: User  uin %s is NOT authenticated, refuse to serve", request.Uin)
		return nil, err
	}

	return &model.DescribeAccountAuthorizationResponse{}, nil
}
