package etl_parser

import (
	"fmt"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
)

type PostgreSQLJdbcParser struct {
	ParserBase
}

func (t *PostgreSQLJdbcParser) Parse(version string, appId int64) (string, error) {
	if t.Node == nil {
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("you should setNode before parse"), nil)
	}
	sqlCode, err := t.CreateTableByOutputSchema()
	if err != nil {
		return "", err
	}
	sqlCode += "\n ) WITH ( \n"
	options, err := t.GetOptions()
	if err != nil {
		return "", err
	}
	options["connector"] = "jdbc"
	conn, prop, err := t.GetConnection(appId)
	if err != nil {
		return "", err
	}
	if conn == nil {
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("connection is nil,id: %s", t.Node.Properties.Data.ConnId), nil)
	}
	nodeTables := t.GetTables()
	if len(nodeTables) == 0 {
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("len of tables for %s is 0: %s", t.GetNode().Properties.Data.Name, t.GetNode().Id), nil)
	}
	database := nodeTables[0][0]
	schemaName := nodeTables[0][1]
	tableName := nodeTables[0][2]
	timeZone, ok := t.Node.Properties.Data.Props["timeZone"]
	if !ok {
		return "", errorcode.NewStackError(errorcode.InvalidParameterCode, fmt.Sprintf("option timeZone is required,node id : %s", t.Node.Id), nil)
	}
	url := fmt.Sprintf("****************************************************************************************", prop["Ip"], prop["Port"], database, schemaName, timeZone)
	delete(t.Node.Properties.Data.Props, "timeZone")
	for _, p := range t.Node.Properties.Data.Advanced {
		if strings.HasPrefix(p.Key, constants.JDBC_URL_PREFIX) {
			url += fmt.Sprintf("&%s=%s", p.Key[len(constants.JDBC_URL_PREFIX):], p.Value)
			p.Key = constants.SKIP_PREFIX + p.Key
		}
	}
	options["url"] = url
	options["table-name"] = tableName
	options["username"] = conn.Username
	options["password"] = conn.Password
	props := make([]string, 0, 0)
	for k, v := range options {
		props = append(props, fmt.Sprintf("\t '%s' = '%s'", k, v))
	}
	sqlCode += strings.Join(props, ", \n")
	sqlCode += "\n ); \n"
	sql, err := t.CreateInsertSQLByOutputSchema()
	if err != nil {
		return "", err
	}
	sqlCode += sql
	return sqlCode, nil
}
