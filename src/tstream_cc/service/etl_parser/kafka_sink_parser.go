package etl_parser

import (
	"fmt"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
)

type KafkaSinkParser struct {
	ParserBase
}

const (
	UPSERT_KAFKA_Connector = "upsert"
	KAFKA_Connector        = "append"
)

func (t *KafkaSinkParser) Parse(version string, appId int64) (string, error) {
	if t.Node == nil {
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("you should setNode before parse"), nil)
	}
	sqlCode, err := t.CreateTableByOutputSchema()
	if err != nil {
		return "", err
	}
	sqlCode += "\n ) WITH ( \n"
	options, err := t.GetOptions()
	if err != nil {
		return "", err
	}
	conn, prop, err := t.GetConnection(appId)
	//upsert-kafka
	if t.GetNode().Properties.Data.WriteType == UPSERT_KAFKA_Connector {
		options["connector"] = "upsert-kafka"
		//kafka
	} else if t.GetNode().Properties.Data.WriteType == KAFKA_Connector {
		options["connector"] = "kafka"
	} else {
		return "", errorcode.NewStackError(errorcode.InvalidParameterCode, fmt.Sprintf("Unsupport connection type: %d", conn.Type), nil)
	}
	if err != nil {
		return "", err
	}
	if conn == nil {
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("connection is nil,id: %s", t.Node.Properties.Data.ConnId), nil)
	}
	nodeTables := t.GetTables()
	if len(nodeTables) == 0 {
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("len of tables for %s is 0: %s", t.GetNode().Properties.Data.Name, t.GetNode().Id), nil)
	}
	topic := nodeTables[0][0]
	bootstrapServer := fmt.Sprintf("%s:%v", prop["Ip"], prop["Port"])
	options["properties.bootstrap.servers"] = bootstrapServer
	options["topic"] = topic
	props := make([]string, 0, 0)
	for k, v := range options {
		props = append(props, fmt.Sprintf("\t '%s' = '%s'", k, v))
	}
	sqlCode += strings.Join(props, ", \n")
	sqlCode += "\n ); \n"
	sql, err := t.CreateInsertSQLByOutputSchema()
	if err != nil {
		return "", err
	}
	sqlCode += sql
	return sqlCode, nil
}
