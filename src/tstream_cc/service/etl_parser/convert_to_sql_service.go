package etl_parser

import (
	"encoding/json"
	"fmt"
	"sort"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/etl"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/etl"
)

type NodeParser interface {
	Parse(version string, appId int64) (string, error)
	SetNext(parser NodeParser)
	GetNext() NodeParser
	GetNode() *model.Node
	SetNode(node *model.Node)
}

type ParserBase struct {
	Options map[string]*Option
	Next    NodeParser
	Node    *model.Node
}

func (t *ParserBase) SetNode(node *model.Node) {
	t.Node = node
}

func (t *ParserBase) GetNode() *model.Node {
	return t.Node
}

func (t *ParserBase) SetNext(parser NodeParser) {
	t.Next = parser
}

func (t *ParserBase) GetNext() NodeParser {
	return t.Next
}

func (t *ParserBase) GetOutputSchema() *model.TableSchema {
	id := t.GetNode().Id
	outputSchema := t.GetNode().Properties.Data.OutputSchema
	if schema, ok := outputSchema[id]; ok == true {
		return schema
	}
	return nil
}

//目前暂时只支持一个输入
func (t *ParserBase) GetSingleInputSchema() *model.TableSchema {
	id := t.GetNode().Id
	inputSchema := t.GetNode().Properties.Data.InputSchema
	for k, v := range inputSchema {
		if k != id {
			return v
		}
	}
	return nil
}

func (t *ParserBase) GetOutputColumns() []*model.TableColumn {
	id := t.GetNode().Id
	outputSchema := t.GetNode().Properties.Data.OutputSchema
	result := make([]*model.TableColumn, 0, 0)
	if schema, ok := outputSchema[id]; ok == true {
		for _, v := range schema.Columns {
			result = append(result, v)
		}
	}
	return result
}

func (t *ParserBase) GetTables() [][]string {
	tables := t.GetNode().Properties.Data.Tables
	if tables != nil && len(tables) != 0 {
		return tables
	}
	return make([][]string, 0, 0)
}

func (t *ParserBase) GetConnection(appId int64) (*model.BaseConnection, map[string]interface{}, error) {
	if t.GetNode().Properties.Data.ConnId != "" {

		return etl.GetConnectionById(t.GetNode().Properties.Data.ConnId, appId)
	}
	return nil, nil, nil
}

func (t *ParserBase) CreateTableByOutputSchema() (string, error) {
	schema := t.GetOutputSchema()
	if schema == nil {
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("output schema is nil: %s", t.GetNode().Id), nil)
	}
	columns := t.GetOutputColumns()
	if len(columns) == 0 {
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("len of output column is 0: %s", t.GetNode().Id), nil)
	}
	sort.Slice(columns, func(i, j int) bool {
		return columns[i].Index < columns[j].Index
	})
	sqlCode := fmt.Sprintf("CREATE TABLE `%s` ( \n ", schema.TableName)
	for i, column := range columns {
		sqlCode += fmt.Sprintf("\t `%s` %s", column.Name, column.EtlType)
		if i != len(columns)-1 {
			sqlCode += ", \n"
		}
	}
	if schema.PrimaryKey != "" {
		sqlCode += fmt.Sprintf(", \n\t PRIMARY KEY (`%s`) NOT ENFORCED", strings.Join(strings.Split(schema.PrimaryKey, ","), "`,`"))
	}
	return sqlCode, nil
}

func (t *ParserBase) CreateInsertSQLByOutputSchema() (string, error) {
	schema := t.GetOutputSchema()
	if schema == nil {
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("output schema is nil: %s", t.GetNode().Id), nil)
	}
	columns := t.GetOutputColumns()
	if len(columns) == 0 {
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("len of output column is 0: %s", t.GetNode().Id), nil)
	}
	sort.Slice(columns, func(i, j int) bool {
		return columns[i].Index < columns[j].Index
	})
	inputSchema := t.GetSingleInputSchema()
	tmpSink := make([]string, 0)
	tmpFrom := make([]string, 0)
	table := inputSchema.TableName
	for _, column := range columns {
		tmpSink = append(tmpSink, fmt.Sprintf("`%s`", column.Name))
		tmpFrom = append(tmpFrom, fmt.Sprintf("`%s`.`%s`", column.FromTable, column.From))
	}
	sqlCode := fmt.Sprintf("INSERT INTO `%s` (%s) SELECT %s FROM `%s`; \n", schema.TableName, strings.Join(tmpSink, ","), strings.Join(tmpFrom, ","), table)
	return sqlCode, nil
}

func (t *ParserBase) GetOptions() (map[string]string, error) {
	props := t.GetNode().Properties.Data.Props
	result := make(map[string]string)
	if props == nil {
		return result, nil
	}
	for _, o := range t.Options {
		if o.AddedTo {
			result[strings.TrimSpace(o.Key)] = strings.TrimSpace(o.Value)
			continue
		}
		v, ok := props[o.Key]
		if !ok && o.Required {
			return result, errorcode.NewStackError(errorcode.InvalidParameterCode, fmt.Sprintf("option %s is required,node id : %s", o.Key, t.GetNode().Id), nil)
		} else if !ok {
			logger.Infof("option config %s is missing", o.Key)
			continue
		}
		if v == "" {
			continue
		}
		var configKey string
		if o.ConfigKey != "" {
			configKey = o.ConfigKey
		} else {
			configKey = o.Key
		}
		result[strings.TrimSpace(configKey)] = strings.TrimSpace(v)
	}
	otherProps := t.GetNode().Properties.Data.Advanced
	for _, p := range otherProps {
		if strings.HasPrefix(p.Key, constants.SKIP_PREFIX) {
			logger.Infof("skip option config %s", p.Key)
			continue
		}
		result[strings.TrimSpace(p.Key)] = strings.TrimSpace(p.Value)
	}
	return result, nil
}

type Option struct {
	Key       string //json key
	Value     string
	ConfigKey string //真正的key
	Required  bool   //必填项
	AddedTo   bool   //需要增加到with参数
}

var ParserMap = map[string]func() NodeParser{
	"mysql_cdc_source": func() NodeParser {
		return &MySQLCDCParser{
			ParserBase: ParserBase{
				Options: map[string]*Option{
					"timeZone": {
						ConfigKey: "server-time-zone",
						Required:  true,
						Key:       "timeZone",
					},
					"serverId": {
						ConfigKey: "server-id",
						Required:  true,
						Key:       "serverId",
					},
					"skipOp": {
						ConfigKey: "debezium.skipped.operations",
						Required:  false,
						Key:       "skipOp",
					},
				},
			},
		}
	},
	"mysql_jdbc_sink": func() NodeParser {
		return &MySQLJdbcParser{
			ParserBase: ParserBase{
				Options: map[string]*Option{},
			},
		}
	},
	"pg_jdbc_sink": func() NodeParser {
		return &PostgreSQLJdbcParser{
			ParserBase: ParserBase{
				Options: map[string]*Option{},
			},
		}
	},
	"clickhouse_sink": func() NodeParser {
		return &ClickHouseSinkParser{
			ParserBase: ParserBase{
				Options: map[string]*Option{
					"batchSize": {
						ConfigKey: "sink.batch-size",
						Required:  false,
						Key:       "batchSize",
					},
					"collapsingField": {
						ConfigKey: "table.collapsing.field",
						Required:  true,
						Key:       "collapsingField",
					},
				},
			},
		}
	},
	"elasticsearch_sink": func() NodeParser {
		return &ElasticsearchSinkParser{
			ParserBase: ParserBase{
				Options: map[string]*Option{
					"flushMaxActions": {
						ConfigKey: "sink.bulk-flush.max-actions",
						Required:  false,
						Key:       "flushMaxActions",
					},
					"failureHandler": {
						ConfigKey: "failure-handler",
						Required:  false,
						Key:       "failureHandler",
					},
				},
			},
		}
	},
	"doris_sink": func() NodeParser {
		return &DorisSinkParser{
			ParserBase: ParserBase{
				Options: map[string]*Option{
					"batchSize": {
						ConfigKey: "sink.batch.size",
						Required:  false,
						Key:       "batchSize",
					},
				},
			},
		}
	},
	"elasticsearch_source": func() NodeParser {
		return &ESSourceParser{
			ParserBase: ParserBase{
				Options: map[string]*Option{
					"format": {
						ConfigKey: "format",
						Required:  false,
						Key:       "format",
					},
					"batchSize": {
						ConfigKey: "batchSize",
						Required:  false,
						Key:       "batchSize",
					},
					"keepScrollAliveSecs": {
						ConfigKey: "keepScrollAliveSecs",
						Required:  false,
						Key:       "keepScrollAliveSecs",
					},
				},
			},
		}
	},
	"kafka_sink": func() NodeParser {
		return &KafkaSinkParser{
			ParserBase: ParserBase{
				Options: map[string]*Option{
					"format": {
						ConfigKey: "format",
						Required:  false,
						Key:       "format",
					},
					"valueFormat": {
						ConfigKey: "value.format",
						Required:  false,
						Key:       "valueFormat",
					},
					"keyFormat": {
						ConfigKey: "key.format",
						Required:  false,
						Key:       "keyFormat",
					},
				},
			},
		}
	},
	"calculator_operator": func() NodeParser {
		return &OpCalculatorParser{
			ParserBase: ParserBase{},
		}
	},
}

type JsonTransformation struct {
	FlinkVersion string
	AppId        int64
}

func (t *JsonTransformation) getParser(node *model.Node) NodeParser {
	key := strings.ToLower(node.Properties.Data.Name) + "_" + strings.ToLower(node.Properties.Data.Type)
	fn, ok := ParserMap[key]
	if !ok {
		logger.Errorf("unknown node name:%s", key)
		return nil
	}
	parser := fn()
	parser.SetNode(node)
	return parser
}

func (t *JsonTransformation) getNext(cur NodeParser, m *model.Canvas) NodeParser {
	for _, e := range m.Edges {
		if e.SourceNodeId == cur.GetNode().Id {
			for _, node := range m.Nodes {
				if node.Id == e.TargetNodeId {
					next := t.getParser(node)
					if next != nil {
						return next
					}
				}
			}
		}
	}
	return nil
}

func (t *JsonTransformation) Transform(jsonStr string) (string, error) {
	if t.AppId == 0 {
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, "AppId is required", nil)
	}
	m := &model.Canvas{}
	if err := json.Unmarshal([]byte(jsonStr), m); err != nil {
		logger.Errorf("Fail to parse canvas json: %s", jsonStr)
		return "", err
	}
	ready := make([]NodeParser, 0)
	result := ""
	for _, node := range m.Nodes {
		if node.Properties.Data.Type == constants.ETL_CANVAS_NODE_TYPE_SOURCE {
			var head NodeParser
			head = t.getParser(node)
			if head == nil {
				continue
			}
			sql, err := head.Parse(t.FlinkVersion, t.AppId)
			if err != nil {
				return result, err
			}
			result += fmt.Sprintf("%s \n", sql)
			cur := head
			for {
				target := t.getNext(cur, m)
				if target == nil {
					break
				}
				cur.SetNext(target)
				cur = target
			}
			ready = append(ready, head)
		}
	}

	alreadyParsed := make(map[string]string, 0)
	//获取Source数组
	for _, p := range ready {
		//获取Source下游
		cur := p.GetNext()
		for {
			//如果下游是
			if cur == nil {
				break
			}
			if _, ok := alreadyParsed[cur.GetNode().Id]; ok {
				cur = p.GetNext()
				continue
			}
			sql, err := cur.Parse(t.FlinkVersion, t.AppId)
			if err != nil {
				return result, err
			}
			result += fmt.Sprintf("%s \n", sql)
			alreadyParsed[cur.GetNode().Id] = "1"
			cur = cur.GetNext()
		}
	}
	return result, nil
}

func (t *JsonTransformation) GetConnectorsList(jsonStr string) ([]string, error) {
	if t.AppId == 0 {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "AppId is required", nil)
	}
	m := &model.Canvas{}
	if err := json.Unmarshal([]byte(jsonStr), m); err != nil {
		logger.Errorf("Fail to parse canvas json: %s", jsonStr)
		return nil, err
	}
	connectorsList := make([]string, 0)
	for _, node := range m.Nodes {
		if node.Properties.Data.Type == constants.ETL_CANVAS_NODE_TYPE_SOURCE || node.Properties.Data.Type == constants.ETL_CANVAS_NODE_TYPE_SINK {
			if node.Properties.Data.Name == "elasticsearch" {
				if node.Properties.Data.Type == constants.ETL_CANVAS_NODE_TYPE_SOURCE {
					connectorsList = append(connectorsList, "es_source")
				} else {
					if node.Properties.Data.ConnId != "" {
						conn, _, err := etl.GetConnectionById(node.Properties.Data.ConnId, t.AppId)
						if err != nil {
							return nil, err
						}
						var connectorName string
						if conn.Type == 4 {
							connectorName = "elasticsearch_6"
						}
						if conn.Type == 5 {
							connectorName = "elasticsearch_7"
						}
						connectorsList = append(connectorsList, connectorName)
					}
				}
			} else {
				connectorsList = append(connectorsList, node.Properties.Data.Name)
			}
		}
	}

	// 去重
	res := etl.RemoveRepByLoop(connectorsList)

	return res, nil
}
