package etl_parser

import (
	"fmt"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
)

type DorisSinkParser struct {
	ParserBase
}

func (t *DorisSinkParser) Parse(version string, appId int64) (string, error) {
	sqlCode, err := t.CreateTableByOutputSchema()
	if err != nil {
		return "", err
	}
	sqlCode += "\n ) WITH ( \n"
	conn, prop, err := t.GetConnection(appId)
	if err != nil {
		return "", err
	}
	if conn == nil {
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("connection is nil,id: %s", t.GetNode().Properties.Data.ConnId), nil)
	}

	options, err := t.GetOptions()
	if err != nil {
		return "", err
	}
	options["connector"] = "doris"
	options["fenodes"] = fmt.Sprintf("%s:%v", prop["Hosts"], prop["HttpPort"])
	if conn.Username != "" {
		options["username"] = conn.Username
	}
	if conn.Password != "" {
		options["password"] = conn.Password
	}
	if t.Node.Properties.Data.SyncType == constants.ETL_SYNC_TYPE_DATABASE {
		options["database.dynamic.identifier.field"] = "databaseIdentifier"
		options["table.dynamic.identifier.field"] = "tableIdentifier"
		options["value.from.field"] = "evolution"
		options["table.identifier"] = "*"
	} else {
		nodeTables := t.GetTables()
		if len(nodeTables) == 0 {
			return "", errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("len of tables for %s is 0: %s", t.GetNode().Properties.Data.Name, t.GetNode().Id), nil)
		}
		options["table.identifier"] = fmt.Sprintf("%s.%s", nodeTables[0][0], nodeTables[0][1])
	}

	props := make([]string, 0, 0)
	for k, v := range options {
		props = append(props, fmt.Sprintf("\t '%s' = '%s'", k, v))
	}
	sqlCode += strings.Join(props, ", \n")
	sqlCode += "\n ); \n"
	sql, err := t.CreateInsertSQLByOutputSchema()
	if err != nil {
		return "", err
	}
	sqlCode += sql
	return sqlCode, nil
}
