package barad

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	http2 "net/http"
	"strconv"
	"strings"
	"time"

	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/item_space"

	"git.code.oa.com/polaris/polaris-go/api"
	"git.code.oa.com/polaris/polaris-go/pkg/model"
	"github.com/google/uuid"
	"github.com/juju/errors"
	"tencentcloud.com/tstream_galileo/src/common/configure"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/component"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/alert"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/barad"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/billing"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job_instance"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service4 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/http"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/polaris"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/region"
	tag "tencentcloud.com/tstream_galileo/src/tstream_cc/service/tag"
)

// 根据事件的类型、状态，判断是否需要保存到数据库的 EventAlert 表并发送给云监控
// 如果有重复告警则直接忽略
func ProcessEventAlert(reqData *barad.JobAlertReq) (retCode int64, retMsg string, err error) {

	// 查询数据库, 消息去重, 保存事件
	exists, err := createOrUpdateEventToDatabase(reqData)
	if err != nil {
		logger.Errorf("Failed to createOrUpdateEventToDatabase because %+v", err)
		return constants.SYSERR,
			"Failed to check event in EventAlert database due to errors: " + err.Error() + ", will NOT send alarm",
			err
	}
	if exists {
		return constants.SUCCESS, "Event already exists in database or job not in running status, no need to resend to Barad / EventBridge", nil
	}

	eventReq := buildEventAlertRequest(reqData.JobId, reqData.EventName, reqData.Status)
	if eventReq == nil {
		return constants.SYSERR, "Failed to build event request", errors.New("Failed to build event request.")
	}
	if reqData != nil {
		eventReq.Message = reqData.Message
	}

	// // TODO: 云监控事件中心完全下线后, 这里 Type 2（快照失败）、Type 3（作业失败）事件的 if 的代码判断可以去掉, 统一走下面的 EventBridge 流程
	// if reqData.Type == constants.EventNameToTypeMapping[constants.BARAD_EVENT_ALERT_NAME_JOB_FAIL] ||
	// 	reqData.Type == constants.EventNameToTypeMapping[constants.BARAD_EVENT_ALERT_NAME_CHECKPOINT_FAIL] {
	// 	logger.Debugf("Event %+v will be sent to Barad", reqData)
	// 	return sendAlertEventToBarad(eventReq)
	// }

	// 自定义事件不上报总线
	if reqData.Type == "5" {
		return constants.SUCCESS, "ok", nil
	}

	// 从七彩石获取事件信息, 补全 EventID 等
	eventInfo, err := getEventInfoFromRainbow(reqData.EventName)
	if err != nil {
		return constants.SYSERR, "Failed to get event info from Rainbow", err
	}

	// 如果是其他异常事件, 则统一走新版的 EventBridge 流程
	logger.Debugf("Event %+v will be sent to EventBridge", reqData)
	return sendAlertEventToEventBridge(eventReq, eventInfo)
}

func getEventInfoFromRainbow(eventName string) (*alert.AlertMapping, error) {
	value, err := config.GetRainbowConfiguration("Event", "alert_event_mapping.json")
	if err != nil {
		logger.Errorf("Failed to get alert_event_mapping.json from Event in Rainbow because %+v", err)
		return nil, err
	}

	alertMappings := make(map[string]alert.AlertMapping)
	err = json.Unmarshal([]byte(value), &alertMappings)
	if err != nil {
		logger.Errorf("Failed to unmarshal alert_event_mapping.json from Event in Rainbow because %+v", err)
		return nil, err
	}

	if alertMapping, ok := alertMappings[eventName]; ok {
		logger.Debugf("Event alert mapping from rainbow for event %s is %+v", eventName, alertMapping)
		return &alertMapping, nil
	} else {
		return nil, errors.New("cannot find event mapping in rainbow for event " + eventName)
	}
}

// 事件上报到 EventBridge
func sendAlertEventToEventBridge(eventReq *alert.EventAlertRequest, eventInfo *alert.AlertMapping) (int64, string, error) {
	// 0. 根据七彩石返回值, 填充必须字段
	eventReq.EventId = eventInfo.EventId

	// 1. 判断环境类型, 如果是联调和测试环境, 选择重庆; 如果是线上的, 选择广州
	polarisServiceName, polarisNamespace := getEventBridgePolarisServiceAndNamespace()

	// 2. 从北极星获取该环境最新的 EventBridge 服务地址
	eventBridgeUrl, err := getEventBridgeUrlFromPolaris(polarisServiceName, polarisNamespace)
	if err != nil {
		return constants.SYSERR, "Failed to getEventBridgeUrlFromPolaris", err
	}

	requestId := uuid.Must(uuid.NewUUID()).String()

	logger.Debugf("[%s]: Retrieved report URL %s from Polaris service %s (%s)",
		requestId,
		eventBridgeUrl,
		polarisServiceName,
		polarisNamespace,
	)

	uploadEventRequest, err := NewPutEventRequest(eventReq, requestId)
	if err != nil {
		logger.Errorf("Failed to NewPutEventRequest because %+v", err)
		return constants.SYSERR, "Failed to NewPutEventRequest", err
	}

	uploadEventRequestBytes, err := json.Marshal(uploadEventRequest)
	if err != nil {
		logger.Errorf("Failed to marshal uploadEventRequest because %+v", err)
		return constants.SYSERR, "Failed to marshal uploadEventRequest", err
	}
	uploadEventRequestString := string(uploadEventRequestBytes)

	logger.Debugf("[%s] Going to send %s to EventBridge at %s", requestId, uploadEventRequestString, eventBridgeUrl)

	// 4. 发送请求
	httpClient := http.NewHttpClient(5*time.Second, 8*time.Second) // 统一上报到广州区, 可能产生跨境流量, 因此超时时间需要大一些
	httpRsp, err := httpClient.Post(eventBridgeUrl, "application/json", strings.NewReader(uploadEventRequestString))
	if err != nil {
		logger.Errorf("Failed to send http post to EventBridge because %+v", err)
		return constants.SYSERR, "Failed to send http post to EventBridge", err
	}
	defer httpRsp.Body.Close()

	// 5. 检查响应, 决定是否容错
	return handleEventBridgeError(httpRsp, requestId)
}

// EventBridge 接入文档 https://km.woa.com/group/41191/articles/show/488943
func NewPutEventRequest(eventReq *alert.EventAlertRequest, eventId string) (alert.PutEventRequest, error) {

	eventDataStr, err := json.Marshal(eventReq)
	if err != nil {
		logger.Errorf("Failed to marshal event data because %+v", err)
		return alert.PutEventRequest{}, err
	}

	data := string(eventDataStr)

	return alert.PutEventRequest{
		AppId: eventReq.NumericAppId,
		Uin:   eventReq.OwnerUin,
		EventList: []alert.EventRequest{
			{
				Specversion:     "1.0",
				Id:              eventId,
				Time:            util.GetNowTimestamp(),
				Region:          eventReq.Region,
				Type:            generateTypeFromOceanusEvent(eventReq.EventName),
				Source:          "oceanus.cloud.tencent",
				Subject:         eventReq.InstanceId,
				DataContentType: "application/json;charset=utf-8",
				Status:          getEventStatusField(eventReq),
				Data:            data,
			},
		},
		EventBusId: "eb-" + eventReq.AppId + "-default",
	}, nil
}

// 只有作业失败和快照失败事件是带状态的, 其他事件只需要返回 "-" 即可
func getEventStatusField(req *alert.EventAlertRequest) string {
	if req.EventName == constants.BARAD_EVENT_ALERT_NAME_CHECKPOINT_FAIL ||
		req.EventName == constants.BARAD_EVENT_ALERT_NAME_JOB_FAIL {
		return strconv.Itoa(req.Status)
	}

	return "-"
}

// 处理 EventBridge 返回的错误
func handleEventBridgeError(httpRsp *http2.Response, requestId string) (int64, string, error) {
	logger.Debugf("[%s] Received HTTP response from EventBridge with status %s", requestId, httpRsp.Status)

	if httpRsp.StatusCode != 200 {
		logger.Errorf("[%s] HTTP status code from EventBridge is not 200 but %s", requestId, httpRsp.Status)
		return constants.SYSERR,
			"Unexpected HTTP status code from EventBridge",
			errors.New("Unexpected HTTP status code")
	}
	var bytes []byte
	bytes, err := ioutil.ReadAll(httpRsp.Body)
	if err != nil {
		logger.Errorf("[%s] Failed to ReadAll from response body because %+v", requestId, err)
		return constants.SYSERR, "Failed to ReadAll from response body", err
	}

	logger.Debugf("[%s] EventBridge response: %s", requestId, string(bytes))

	response := &alert.Response{}
	err = json.Unmarshal(bytes, response)
	if err != nil {
		logger.Errorf("[%s] Failed to parse response from EventBridge %+v", requestId, err)
		return constants.SYSERR, "Failed to parse response from EB", err
	}

	if response.Error != nil {
		// TODO: 区分 EventBridge 错误码, 制定重试策略
		logger.Errorf("[%s] EventBridge returned error code %s with message %s",
			requestId, response.Error.Code, response.Error.Message)
		return constants.PARAMSERR, "EventBridge returned with error", errors.New(response.Error.Code)
	}

	return constants.SUCCESS, "ok", nil
}

func getEventBridgeUrlFromPolaris(polarisServiceName string, polarisNamespace string) (string, error) {
	polarisService, err := polaris.GetPolarisService()
	if err != nil {
		logger.Warningf("Failed to get polaris service because %+v", err)
		return "", err
	}
	req := &api.GetOneInstanceRequest{
		GetOneInstanceRequest: model.GetOneInstanceRequest{
			Service:   polarisServiceName,
			Namespace: polarisNamespace,
		},
	}
	resp, err := polarisService.ConsumerApi.GetOneInstance(req)
	if err != nil {
		logger.Warningf("Failed to get EventBridge instance from Polaris because %+v", err)
		return "", err

	}
	if len(resp.GetInstances()) != 1 {
		logger.Errorf("Cannot get valid Polaris address(es) for EventBridge, because address count is %d",
			len(resp.GetInstances()),
		)
		return "", errors.New("Invalid number of Polaris addresses")
	}

	//goland:noinspection HttpUrlsUsage
	eventBridgeUrl := fmt.Sprintf("http://%s:%d/openapi/putEvents",
		resp.GetInstances()[0].GetHost(),
		resp.GetInstances()[0].GetPort(),
	)
	return eventBridgeUrl, nil
}

// 将下划线形式的 oceanus_event_name 转为 EventBridge 所需的 oceanus:ErrorEvent:OceanusEventName
func generateTypeFromOceanusEvent(eventName string) string {
	return "oceanus:ErrorEvent:" + snakeCaseToCamelCase(eventName)
}

func snakeCaseToCamelCase(inputUnderScoreStr string) (camelCase string) {
	var builder strings.Builder

	shouldToUpper := true
	for _, v := range inputUnderScoreStr {
		if shouldToUpper {
			builder.WriteString(strings.ToUpper(string(v)))
			shouldToUpper = false
		} else {
			if v == '_' {
				shouldToUpper = true
			} else {
				builder.WriteString(string(v))
			}
		}

	}
	return builder.String()
}

// 根据当前环境, 获取 EventBridge 在 Polaris 的服务和命名空间
func getEventBridgePolarisServiceAndNamespace() (polarisServiceName string, polarisNamespace string) {
	// 线上或测试环境都统一上报到广州区
	logger.Debugf("Using EventBridge guangzhou online or test Environment")
	polarisServiceName = "Eb.ap-guangzhou-1.EventBridge.AccessHttp"
	polarisNamespace = "Production"
	return
}

func createOrUpdateEventToDatabase(req *barad.JobAlertReq) (exists bool, err error) {
	if req.EventName == constants.BARAD_EVENT_ALERT_NAME_JOB_STOPPED {
		// 作业停止事件, 可以跳过入库
		return false, nil
	}
	// 获取作业基本信息
	job, err := service2.FindJobBySerialID(req.JobId)
	if err != nil {
		logger.Errorf("Failed to find job, with jobId:%s, with errors:%+v", req.JobId, err)
		return false, err
	}

	// Galileo 数据库 Job 和 JobInstance 必须处于运行状态, 才接受事件推送（只看数据库, 不管作业实际是否已经崩溃）
	jobRunningInstance, err := GetValidJobInstanceBySerialId(req.JobId)
	if err != nil {
		logger.Warningf("Cannot find a running job instance for %s, will not send or save alert", req.JobId)
		return true, nil // 作业未在运行, 事件无需保存, 此时通过返回 exists=true 可以跳出处理逻辑, 避免 Watchdog 报错
	}
	// 当存在jobInstance的时候（cls日志异常事件），比较是否是当前实例id，以免误报,无需保存，通过返回 exists=true 可以跳出处理逻辑, 避免 Watchdog 报错
	if req.JobInstanceId != 0 && jobRunningInstance.Id != req.JobInstanceId {
		logger.Warningf("Job:%s jobRunningInstance is not event's  req JobInstanceId: %d, running JobInstanceId: %d ", req.JobId, req.JobInstanceId, jobRunningInstance.Id)
		return true, nil
	}

	if isRecoverableEvent(req.EventName) {
		// 处理可恢复事件（目前只有 oceanus_job_fail 和 oceanus_checkpoint_fail 两种）
		return processRecoverableEvent(req, job, jobRunningInstance)
	} else {
		// 处理仅告警, 无需恢复的事件
		return processAlertOnlyEvent(req, job, jobRunningInstance)
	}
}

func processRecoverableEvent(req *barad.JobAlertReq, job *table.Job, jobInstance *table2.JobInstance) (exists bool, err error) {
	txManager := service.GetTxManager()
	alertIsOn, err := whetherAlertIsOnInDatabase(req)
	if err != nil {
		return false, err
	}

	logger.Debugf("alertIsOn %t and req.Status %d", alertIsOn, req.Status)

	// 两个变量, 一共四种状态组合
	if !alertIsOn && req.Status == constants.BARAD_EVENT_ALERT_ON {
		// 如果数据库未触发, 而事件状态是触发, 说明新发生了事件, 需要记录到数据库
		event := createNewEventAlertEntity(req, job, jobInstance)
		txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
			tx.SaveObject(event, "EventAlert")
			return nil
		}).Close()

		return false, nil
	} else if !alertIsOn && req.Status == constants.BARAD_EVENT_ALERT_OFF {
		logger.Debugf("Alert is already off, nothing to do")
		// 如果数据库未触发, 事件状态也是恢复, 说明是重复告警, 直接忽略
		return true, nil
	} else if alertIsOn && req.Status == constants.BARAD_EVENT_ALERT_ON {
		logger.Debugf("Alert is already on, nothing to do")
		// 如果数据库已触发, 事件状态也是触发, 说明是重复告警, 直接忽略
		return true, nil
	} else if alertIsOn && req.Status == constants.BARAD_EVENT_ALERT_OFF {
		// 如果数据库已触发, 事件状态是恢复, 则更新数据库, 并发送恢复事件
		logger.Debugf("Turn off existing event to database")
		err = turnOffAlertEventInDatabase(req, jobInstance)
		if err != nil {
			return false, err
		}
		return false, nil
	} else {
		logger.Errorf("Logic error? Not valid combination for isAlertOn %t and status %d", alertIsOn, req.Status)
		return false, errors.New("Illegal event status")
	}
}

func processAlertOnlyEvent(req *barad.JobAlertReq, job *table.Job, jobInstance *table2.JobInstance) (exists bool, err error) {
	if req.EventName == constants.BARAD_EVENT_ALERT_NAME_JOB_STOPPED {
		// 作业停止事件 入库位置在finish_commands_service.go, 由clusterMaster回调时入库
		return false, nil
	}
	if req.Important == false { // 如果不是重要消息, 则 1 小时内只允许告警一次
		// 寻找过去 1 小时内当前作业的同类事件
		latestEvent, err := getLatestEventAlertInDatabase(req)
		if err != nil {
			return false, err
		}
		if latestEvent != nil {
			logger.Debugf("Found latest alert-only event %+v", latestEvent)
			lastEventTime, err := time.ParseInLocation(billing.StandardTimestampFormatString, latestEvent.CreateTime, time.Local)
			if err != nil {
				return false, errorcode.NewStackError(errorcode.InternalErrorCode_ParseTimeFailed, "Failed to parse latestEvent", err)
			}

			// 如果同一个运行实例 (RunningOrderId), 一小时内多次触发
			if time.Now().Sub(lastEventTime) < 1*time.Hour &&
				int64(latestEvent.RunningOrderIdOnTrigger) == jobInstance.RunningOrderId {
				logger.Debugf("Found latest event %s for job %s with runningOrderId %d within one hour, skip",
					req.EventName,
					req.JobId,
					jobInstance.RunningOrderId)
				return true, nil
			}

			logger.Debugf("Event %+v is NOT within one hour, continue", latestEvent)
		}
	}

	event := createNewEventAlertEntity(req, job, jobInstance)
	txManager := service.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.SaveObject(event, "EventAlert")
		return nil
	}).Close()

	return false, nil
}

// 云监控事件中心录入的可恢复事件（有触发和恢复两种状态）
func isRecoverableEvent(eventName string) bool {
	return eventName == constants.BARAD_EVENT_ALERT_NAME_JOB_FAIL ||
		eventName == constants.BARAD_EVENT_ALERT_NAME_CHECKPOINT_FAIL
}

// 对于可恢复事件, 因为有状态, 所以不会出现多个重复的未恢复条目
func whetherAlertIsOnInDatabase(req *barad.JobAlertReq) (bool, error) {
	sql := "select * from EventAlert where jobId=? and isAlertOn=? and name=?"
	args := make([]interface{}, 0)
	args = append(args, req.JobId)
	args = append(args, constants.BARAD_EVENT_ALERT_ON)
	args = append(args, req.EventName)

	txManager := service.GetTxManager()
	count, _, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return false, err
	} else if count == 0 {
		return false, nil
	}

	return true, nil
}

// 对于无需恢复的事件, 因为没有状态, 所以有可能出现多个重复条目
func getLatestEventAlertInDatabase(req *barad.JobAlertReq) (*alert.EventAlertEntity, error) {
	sql := "SELECT * FROM EventAlert WHERE jobId=? AND isAlertOn=? AND name=? ORDER BY CreateTime DESC LIMIT 1"
	args := make([]interface{}, 0)
	args = append(args, req.JobId)
	args = append(args, constants.BARAD_EVENT_ALERT_ON)
	args = append(args, req.EventName)

	txManager := service.GetTxManager()
	count, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return nil, err
	} else if count == 0 {
		return nil, nil
	}

	event := &alert.EventAlertEntity{}
	_ = util.ScanMapIntoStruct(event, data[0])
	return event, nil
}

func turnOffAlertEventInDatabase(req *barad.JobAlertReq, jobInstance *table2.JobInstance) (err error) {
	currentTimeDate := util.GetCurrentTime()
	// 注意: status 之前一直是 1. 现在改为和 isAlertOn 表示同样的含义. 后续可以考虑合并两个字段
	sql := "update EventAlert set status = ?, isAlertOn = ?, recoverTime = ?, runningOrderIdOnRecovery = ? " +
		"where jobId = ? and isAlertOn = ? and name = ?"
	args := make([]interface{}, 0)
	args = append(args, constants.BARAD_EVENT_ALERT_OFF)
	args = append(args, constants.BARAD_EVENT_ALERT_OFF)
	args = append(args, currentTimeDate)
	args = append(args, jobInstance.RunningOrderId)
	args = append(args, req.JobId)
	args = append(args, constants.BARAD_EVENT_ALERT_ON)
	args = append(args, req.EventName)

	txManager := service.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		result := tx.ExecuteSql(sql, args)
		rowAffected, err := result.RowsAffected()
		if err != nil {
			logger.Errorf("Failed to execute the update , with sql:%s, with args:%+v", sql, args)
			return err
		}
		if rowAffected <= 0 {
			logger.Errorf("Failed to update event alert, affected rows count is 0, with sql:%s", sql)
			return errors.New("Failed to update event alert")
		}
		return nil
	}).Close()
	return nil
}

// isAlertOn 1 为告警， 0 为恢复
func buildEventAlertRequest(jobId string, eventName string, alertStatus int) (req *alert.EventAlertRequest) {
	job, err := service2.FindJobBySerialID(jobId)
	if err != nil {
		logger.Errorf("Failed to find job, with jobId:%s, with errors:%+v", jobId, err)
		return nil
	}
	additionalMsgs := make([]map[string]interface{}, 0)
	jobTags := make([]map[string]interface{}, 0)
	msgAppId := map[string]interface{}{}
	msgClusterId := map[string]interface{}{}
	msgSerialId := map[string]interface{}{}
	msgItemSpace := map[string]interface{}{}

	msgAppId["key"] = "userAppId"
	msgAppId["value"] = strconv.FormatInt(int64(job.AppId), 10)
	msgClusterId["key"] = "clusterId"
	clusterGroup, err := service4.GetClusterGroupByClusterId(job.ClusterId)
	if err != nil {
		logger.Errorf("Failed to GetClusterGroupByClusterId because %+v", err)
		return nil
	}
	msgClusterId["value"] = clusterGroup.SerialId
	msgSerialId["key"] = "jobName"
	msgSerialId["value"] = job.Name
	additionalMsgs = append(additionalMsgs, msgAppId)
	additionalMsgs = append(additionalMsgs, msgClusterId)
	additionalMsgs = append(additionalMsgs, msgSerialId)

	tags, err := tag.GetTagService().GetResourceTagsForJob(job.OwnerUin, job.Region, []string{jobId})
	if err != nil {
		logger.Warningf("Failed to GetResourceTagsForJob for error: %+v", err)
	}
	if err == nil && len(tags) == 0 {
		logger.Warningf("Alert: no tags were found for job %s", jobId)
	}

	if err == nil && (len(tags)) > 0 {
		for _, tag := range tags {
			msgTag := map[string]interface{}{}
			msgTag["key"] = tag.TagKey
			msgTag["value"] = tag.TagValue
			jobTags = append(jobTags, msgTag)
		}
	}

	dimensions := make([]map[string]interface{}, 0)
	dimension := map[string]interface{}{}
	dimension["key"] = "tjob_id"
	dimension["value"] = job.SerialId
	dimensions = append(dimensions, dimension)

	itemSpace, err := item_space.GetItemSpaceByItemId(job.ItemSpaceId)
	if err != nil {
		logger.Errorf("Failed to GetItemSpaceByItemId because %+v", err)
		return nil
	}
	msgItemSpace["key"] = "itemSpace"
	msgItemSpace["value"] = itemSpace.ItemSpaceName
	additionalMsgs = append(additionalMsgs, msgItemSpace)

	eventAlertJobs, err := GetJobs(job.AppId, job.SerialId, "")
	if err != nil {
		logger.Errorf("Failed to get eventAlertJobs")
	}
	if len(eventAlertJobs) == 1 {
		msgJobUrl := map[string]interface{}{}
		msgJobUrl["key"] = "jobUrl"
		msgJobUrl["value"] = eventAlertJobs[0].Url
		additionalMsgs = append(additionalMsgs, msgJobUrl)
	}
	currentTime := util.GetNowTimestamp()
	req = &alert.EventAlertRequest{
		OccurTime:     int(currentTime) / 1000,
		Region:        job.Region,
		ProductName:   constants.BARAD_EVENT_ALERT_PRODUCT_NAME,
		EventName:     eventName,
		Status:        alertStatus,
		AppId:         strconv.FormatInt(int64(job.AppId), 10),
		NumericAppId:  int64(job.AppId),
		OwnerUin:      job.OwnerUin,
		InstanceId:    jobId,
		FolderId:      job.FolderId,
		CreatorUin:    job.CreatorUin,
		ClusterId:     clusterGroup.SerialId,
		WorkSpaceId:   itemSpace.SerialId,
		Dimensions:    dimensions,
		AdditionalMsg: additionalMsgs,
		JobTags:       jobTags,
	}
	return req
}

// 初始化一个新的 EventAlert 告警事件对象
func createNewEventAlertEntity(req *barad.JobAlertReq, job *table.Job, jobInstance *table2.JobInstance) *alert.EventAlertEntity {
	logger.Debugf("Create new event entity for req %+v, job %+v, jobInstance %+v", req, job, jobInstance)

	return &alert.EventAlertEntity{
		AppId:                        job.AppId,
		Region:                       job.Region,
		Name:                         req.EventName,
		Type:                         req.Type,
		RunningOrderIdOnTrigger:      int(jobInstance.RunningOrderId),
		RunningOrderIdOnRecovery:     0,
		Status:                       constants.BARAD_EVENT_ALERT_ON,
		Message:                      req.Message,
		ClusterId:                    int32(job.ClusterId),
		JobId:                        req.JobId,
		JobName:                      job.Name,
		IsAlertOn:                    constants.BARAD_EVENT_ALERT_ON,
		CreateTime:                   util.GetCurrentTime(),
		RecoverTime:                  "0000-00-00 00:00:00",
		JobIdRunningOrderIdOnTrigger: fmt.Sprintf("%s_%d", req.JobId, jobInstance.RunningOrderId),
	}
}

// Deprecated: 云监控事件中心下线以后这个逻辑就不用了
func sendAlertEventToBarad(eventReq *alert.EventAlertRequest) (retCode int64, retMsg string, err error) {
	legacyRequest := &alert.LegacyBaradEventAlertRequest{
		OccurTime:     eventReq.OccurTime,
		Region:        eventReq.Region,
		ProductName:   eventReq.ProductName,
		EventName:     eventReq.EventName,
		Status:        eventReq.Status,
		AppId:         eventReq.AppId,
		InstanceId:    eventReq.InstanceId,
		FolderId:      eventReq.FolderId,
		CreatorUin:    eventReq.CreatorUin,
		ClusterId:     eventReq.ClusterId,
		WorkSpaceId:   eventReq.WorkSpaceId,
		Dimensions:    eventReq.Dimensions,
		AdditionalMsg: eventReq.AdditionalMsg,
	}

	for i := 5; i > 0; i-- {
		logger.Debugf("Going to send to barad: %+v", legacyRequest)
		retCode, retMsg, err = sendAlertToBarad(legacyRequest)
		logger.Debugf("Sent to barad: with retCode %d, retMsg %v", retCode, retMsg)
		if retCode == 0 || strings.Contains(retMsg, "event unregistered") {
			break
		}
		logger.Warningf("Failed to call sendAlertToBarad due to retCode %d, retMsg %s, error %+v, retry count %d",
			retCode, retMsg, err, i)
		time.Sleep(1 * time.Second)
	}

	return retCode, retMsg, err
}

// Deprecated: 云监控事件中心下线以后这个逻辑就不用了
func sendAlertToBarad(req *alert.LegacyBaradEventAlertRequest) (retCode int64, retMsg string, err error) {

	// 4. 通过HttpClient来发送告警到 barad
	alertUrl := configure.GetConfStringValue("component.properties", "barad.alert.report.url")
	if alertUrl == "" {
		return -1, "", errors.New("alert url is empty.")
	}
	regionId, err := service.GetRegionIdByName(req.Region)
	if err != nil {
		return -1, err.Error(), err
	}
	baradReporter := component.NewBaradReporter(regionId)
	reqMap, err := util.ScanStructIntoMap(req)
	if err != nil {
		return -1, err.Error(), err
	}
	regionName := reqMap["region"].(string)
	alertRegion, err := service3.GetRegionShortName(regionName)
	if err != nil {
		return -1, err.Error(), err
	}
	reqMap["region"] = alertRegion

	retData, err := baradReporter.SendPostRequest(alertUrl, reqMap)
	if err != nil {
		return -1, retData, err
	}
	return 0, retData, nil
}

// Deprecated: 仅非常古老的 YARN 集群会调用
func GetEventAlerts(clusterId int32, isAlertOn int) ([]*alert.EventAlertEntity, error) {
	sql := "select * from EventAlert where isAlertOn=? and clusterId=?"
	args := make([]interface{}, 0)
	args = append(args, isAlertOn)
	args = append(args, clusterId)

	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return nil, err
	}
	eventAlerts := make([]*alert.EventAlertEntity, 0)
	for i := 0; i < len(data); i++ {
		eventAlert := &alert.EventAlertEntity{}
		err = util.ScanMapIntoStruct(eventAlert, data[i])
		if err != nil {
			return nil, err
		}
		eventAlerts = append(eventAlerts, eventAlert)
	}
	return eventAlerts, nil
}
