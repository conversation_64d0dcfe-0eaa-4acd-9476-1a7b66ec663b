package service_test

import (
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/resource_config"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource_config"
	"testing"
)

func init() {
	service2.InitTestDB(service2.WALLYDB)

}

func Test_DoDescribeResourceConfigs(t *testing.T) {

	req := &model.DescribeResourceConfigsReq{
		RequestBase: apiv3.RequestBase{
			AppId:         **********,
			Uin:           "***********",
			SubAccountUin: "***********",
			Region:        "ap-guangzhou",
		},
	}
	service.DoDescribeResourceConfigs(req)
}
