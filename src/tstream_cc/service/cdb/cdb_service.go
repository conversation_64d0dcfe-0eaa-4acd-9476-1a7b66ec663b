package cdb

import (
	"encoding/json"
	"errors"
	"fmt"
	cdb "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/cdb/v20170320"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common"
	"math/rand"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	converter "tencentcloud.com/tstream_galileo/src/common/errcode_converter"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	service2 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cdb"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cdb"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config/flowCC"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/qcloud"
	service8 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
	"time"
)

var (
	cdbService *CdbService
)

type CdbService struct {
}

func (this *CdbService) getCdbApiUrlV3() string {
	// 内部接口可买所有地域的cdb
	return model.CDB_API_URL_V3_INNER
}

func (this *CdbService) getCdbApiDomain() string {
	return model.CDB_API_DOMAIN_INTERNAL
}

func (this *CdbService) NewDefaultCreateDBInstanceRequest(netEnvironmentType int8, InternalTkeClusterConfig *flowCC.InternalTkeClusterConfig) *cdb.CreateDBInstanceRequest {
	req := cdb.NewCreateDBInstanceRequest()
	req.SetDomain(this.getCdbApiDomain())

	engineVersion := model.CDB_API_ENGINE_VERSION
	req.EngineVersion = &engineVersion

	instanceRole := model.CDB_API_INSTANCE_RO
	req.InstanceRole = &instanceRole

	autoRenewFlag := int64(1)
	req.AutoRenewFlag = &autoRenewFlag

	goodsNum := int64(1)
	req.GoodsNum = &goodsNum

	Period := int64(1)
	req.Period = &Period

	if netEnvironmentType == constants.NETWORK_ENV_INNER_VPC {
		tagKeyProduct := InternalTkeClusterConfig.TagKeyProduct
		tagValueProduct := InternalTkeClusterConfig.TagValueProduct

		tagKeyDepartment := InternalTkeClusterConfig.TagKeyDepartment
		tagValueDepartment := InternalTkeClusterConfig.TagValueDepartment

		req.ResourceTags = []*cdb.TagInfo{
			{
				TagKey: &tagKeyProduct,
				TagValue: []*string{
					&tagValueProduct,
				},
			},
			{
				TagKey: &tagKeyDepartment,
				TagValue: []*string{
					&tagValueDepartment,
				},
			},
		}
	}
	return req
}

// 这个生成的eventId是和业务无关的，或者说是可以不重入的
func (this *CdbService) geneEventIdServiceIndependent() int {
	r1 := rand.New(rand.NewSource(time.Now().UnixNano()))
	return r1.Intn(65533) + 1
}

func (this *CdbService) CreateDBInstanceRaw(uin, subAccountUin, region string, req *model.CreateDBInstanceRequest) (
	code int, retmsg string, instanceId string, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("CreateDBInstanceRaw error"))

	// 构造业务数据
	// 必传参数
	req.Region = region
	req.EngineVersion = model.CDB_API_ENGINE_VERSION
	req.InstanceRole = model.CDB_API_INSTANCE_RO
	req.AutoRenewFlag = 1
	req.GoodsNum = 1
	req.Version = "2017-03-20"

	// 可选参数
	// 不传port/passwd/paramlist，防止申请的时候就初始化
	apiParaMap := map[string]string{}
	if b, err := json.Marshal(req); err != nil {
		errMsg := fmt.Sprintf("CreateDBInstance: json.Marshal err: %v", err)
		return model.CDB_API_NORMAL_ERROR, "", "", converter.GenSecCommonErrorCode(converter.CDB_ERROR, converter.ERROR_CODE_51002, converter.INTERNAL_ERROR_MESSAGE, errors.New(errMsg))
	} else if err := json.Unmarshal(b, &apiParaMap); err != nil {
		errMsg := fmt.Sprintf("CreateDBInstance: json.Unmarshal err: %v", err)
		return model.CDB_API_NORMAL_ERROR, "", "", converter.GenSecCommonErrorCode(converter.CDB_ERROR, converter.ERROR_CODE_51002, converter.INTERNAL_ERROR_MESSAGE, errors.New(errMsg))
	}

	apiService := qcloud.GetQcloudApiService()
	rsp, err := apiService.SendApiHttps(uin, subAccountUin, this.getCdbApiUrlV3(),
		model.CDB_API_GEN_CDB_INSTANCE_PACKAGE_ACTION_V3, this.geneEventIdServiceIndependent(), apiParaMap)
	if nil != err {
		errMsg := fmt.Sprintf("CreateDBInstance: SendApiHttps err: %v", err)
		return model.CDB_API_NORMAL_ERROR, "", "", converter.GenSecCommonErrorCode(converter.CDB_ERROR, converter.ERROR_CODE_51002, converter.INTERNAL_ERROR_MESSAGE, errors.New(errMsg))
	}
	type Response struct {
		Reply *model.CreateHourCDBRspV3 `json:"Response"`
	}
	result := &Response{}

	if err := json.Unmarshal([]byte(rsp), result); err != nil {
		errMsg := fmt.Sprintf("CreateDBInstance: Json Unmarshal err: %v", err)
		return model.CDB_API_NORMAL_ERROR, "", "", converter.GenCommonErrorCode(converter.INTERNAL_ERROR_MESSAGE, errors.New(errMsg))
	}

	if result.Reply.Error != nil {
		return model.CDB_API_NORMAL_ERROR, "", "", converter.GenSecCommonErrorCode(converter.CDB_ERROR, converter.ERROR_CODE_51002, fmt.Sprintf("%s %s", result.Reply.Error.Code, result.Reply.Error.Message), nil)
	}

	return 0, "success", result.Reply.InstanceIds[0], nil
}

func (this *CdbService) CreateDBInstance(secretId, secretKey, token, region string, req *cdb.CreateDBInstanceRequest) (
	instanceId string, dealId string, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("CreateDBInstance error"))

	prof := qcloud.NewClientProfile()
	credential := common.NewTokenCredential(secretId, secretKey, token)
	client, err := cdb.NewClient(credential, region, prof)
	if err != nil {
		errMsg := fmt.Sprintf("CreateDBInstance: NewClient err: %v", err)
		return "", "", converter.GenCommonErrorCode(converter.INTERNAL_ERROR_MESSAGE, errors.New(errMsg))
	}

	logger.Infof("CreateDBInstance request: %s", req.ToJsonString())
	response, err := client.CreateDBInstance(req)
	if err != nil {
		errMsg := fmt.Sprintf("CreateDBInstance err: %v", err)
		return "", "", converter.GenCommonErrorCode(converter.INTERNAL_ERROR_MESSAGE, errors.New(errMsg))
	}
	logger.Infof("CreateDBInstance response: %s", response.ToJsonString())
	if len(response.Response.InstanceIds) > 0 {
		instanceId = *response.Response.InstanceIds[0]
	}

	if len(response.Response.DealIds) > 0 {
		dealId = *response.Response.DealIds[0]
	}

	return instanceId, dealId, nil
}

func (this *CdbService) CreateDBInstanceWithScsAccountByNetEnvironmentType(netEnvironmentType int8, region string, req *cdb.CreateDBInstanceRequest) (
	instanceId string, dealId string, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("CreateDBInstanceWithScsAccount error"))

	secretId, secretKey, err := service.GetSecretIdAndKeyByNetworkEnvType(netEnvironmentType)
	if err != nil {
		errMsg := fmt.Sprintf("CreateDBInstance: GetSecretIdAndKeyOfScs err: %v", err)
		return "", "", converter.GenCommonErrorCode(converter.INTERNAL_ERROR_MESSAGE, errors.New(errMsg))
	}

	return this.CreateDBInstance(secretId, secretKey, "", region, req)
}

func (this *CdbService) UpgradeDBInstance(secretId, secretKey, token, region string, req *cdb.UpgradeDBInstanceRequest) (
	code int, retmsg string, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("UpgradeDBInstance error"))

	prof := qcloud.NewClientProfile()
	credential := common.NewTokenCredential(secretId, secretKey, token)
	client, err := cdb.NewClient(credential, region, prof)
	if err != nil {
		errMsg := fmt.Sprintf("UpgradeDBInstance: NewClient err: %v", err)
		return model.CDB_API_NORMAL_ERROR, "", converter.GenCommonErrorCode(converter.INTERNAL_ERROR_MESSAGE, errors.New(errMsg))
	}

	logger.Infof("UpgradeDBInstance request: %s", req.ToJsonString())
	response, err := client.UpgradeDBInstance(req)
	if err != nil {
		errMsg := fmt.Sprintf("UpgradeDBInstance err: %v", err)
		return model.CDB_API_NORMAL_ERROR, "", converter.GenCommonErrorCode(converter.INTERNAL_ERROR_MESSAGE, errors.New(errMsg))
	}
	logger.Infof("UpgradeDBInstance response: %s", response.ToJsonString())
	return 0, "success", nil
}

func (this *CdbService) UpgradeDBInstanceWithScsAccountByNetworkEnvType(netEnvironmentType int8, region string, req *cdb.UpgradeDBInstanceRequest) (
	code int, retmsg string, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("CreateDBInstanceWithScsAccount error"))

	secretId, secretKey, err := service.GetSecretIdAndKeyByNetworkEnvType(netEnvironmentType)
	if err != nil {
		errMsg := fmt.Sprintf("UpgradeDBInstance: GetSecretIdAndKeyOfScs err: %v", err)
		return model.CDB_API_NORMAL_ERROR, "", converter.GenCommonErrorCode(converter.INTERNAL_ERROR_MESSAGE, errors.New(errMsg))
	}

	return this.UpgradeDBInstance(secretId, secretKey, "", region, req)
}

func (this *CdbService) UpgradeDBInstanceWithScsAccount(region string, req *cdb.UpgradeDBInstanceRequest) (
	code int, retmsg string, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("CreateDBInstanceWithScsAccount error"))

	secretId, secretKey, err := service.GetSecretIdAndKeyOfScs()
	if err != nil {
		errMsg := fmt.Sprintf("UpgradeDBInstance: GetSecretIdAndKeyOfScs err: %v", err)
		return model.CDB_API_NORMAL_ERROR, "", converter.GenCommonErrorCode(converter.INTERNAL_ERROR_MESSAGE, errors.New(errMsg))
	}

	return this.UpgradeDBInstance(secretId, secretKey, "", region, req)
}

func (this *CdbService) NewDefaultUpgradeDBInstanceRequestBuilder() *UpgradeDBInstanceRequestBuilder {
	return NewUpgradeDBInstanceRequestBuilder().WithDomain(model.CDB_API_DOMAIN_INTERNAL)
}

func (this *CdbService) InitDBInstancesRaw(uin, subAccountUin, region string, cdbInstanceID string, password string) (code int, retmsg string, err error) {
	if len(uin) <= 0 || len(region) <= 0 || len(cdbInstanceID) <= 0 || len(password) <= 0 {
		errMsg := fmt.Sprintf("uin:%s, region:%s, cdbInstanceId:%s, password %s", uin, region, cdbInstanceID, password)
		return model.CDB_API_NORMAL_ERROR, "", converter.GenSecCommonErrorCode(converter.CDB_ERROR, converter.ERROR_CODE_51003, converter.INTERNAL_ERROR_MESSAGE, errors.New(errMsg))
	}
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("InitDBInstancesRaw error"))

	Params := map[string]string{
		"InstanceIds.0": cdbInstanceID,
		"NewPassword":   password,
		"Region":        region,
		"Version":       "2017-03-20",
	}
	apiService := qcloud.GetQcloudApiService()
	rsp, err := apiService.SendApiHttps(uin, subAccountUin, this.getCdbApiUrlV3(), model.CDB_API_INITIATE_CDB_INSTANCE_V3,
		this.geneEventIdServiceIndependent(), Params)
	if nil != err {
		errMsg := fmt.Sprintf("InitializeCDBInstance: SendApiHttps err: %v", err)
		return model.CDB_API_NORMAL_ERROR, "", converter.GenSecCommonErrorCode(converter.CDB_ERROR, converter.ERROR_CODE_51003, converter.INTERNAL_ERROR_MESSAGE, errors.New(errMsg))
	}

	type Response struct {
		Reply *model.BaseCdbResp `json:"Response"`
	}
	result := &Response{}
	if err := json.Unmarshal([]byte(rsp), result); err != nil {
		errMsg := fmt.Sprintf("InitializeCDBInstance: Json Unmarshal err: %v", err)
		return model.CDB_API_NORMAL_ERROR, "", converter.GenCommonErrorCode(converter.INTERNAL_ERROR_MESSAGE, errors.New(errMsg))
	}

	if result.Reply.Error != nil {
		return model.CDB_API_NORMAL_ERROR, "", converter.GenSecCommonErrorCode(converter.CDB_ERROR, converter.ERROR_CODE_51003, fmt.Sprintf("%s %s", result.Reply.Error.Code, result.Reply.Error.Message), nil)
	}

	return 0, "success", nil
}

func (this *CdbService) InitDBInstances(secretId, secretKey, token, region string, cdbInstanceID string, password string) (code int, retmsg string, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("InitDBInstances error"))

	if len(region) <= 0 || len(cdbInstanceID) <= 0 || len(password) <= 0 {
		errMsg := fmt.Sprintf("region:%s, cdbInstanceId:%s, password %s", region, cdbInstanceID, password)
		return model.CDB_API_NORMAL_ERROR, "", converter.GenSecCommonErrorCode(converter.CDB_ERROR, converter.ERROR_CODE_51003, converter.INTERNAL_ERROR_MESSAGE, errors.New(errMsg))
	}

	request := cdb.NewInitDBInstancesRequest()
	request.SetDomain(this.getCdbApiDomain())
	request.InstanceIds = []*string{&cdbInstanceID}
	request.NewPassword = &password

	prof := qcloud.NewClientProfile()
	credential := common.NewTokenCredential(secretId, secretKey, token)
	client, err := cdb.NewClient(credential, region, prof)
	if err != nil {
		errMsg := fmt.Sprintf("InitializeCDBInstance: NewClient err: %v", err)
		return model.CDB_API_NORMAL_ERROR, "", converter.GenSecCommonErrorCode(converter.CDB_ERROR, converter.ERROR_CODE_51003, converter.INTERNAL_ERROR_MESSAGE, errors.New(errMsg))
	}
	logger.Infof("InitDBInstances request: %s", request.ToJsonString())
	response, err := client.InitDBInstances(request)
	if err != nil {
		errMsg := fmt.Sprintf("InitializeCDBInstance: InitDBInstances err: %v", err)
		return model.CDB_API_NORMAL_ERROR, "", converter.GenSecCommonErrorCode(converter.CDB_ERROR, converter.ERROR_CODE_51003, converter.INTERNAL_ERROR_MESSAGE, errors.New(errMsg))
	}
	logger.Infof("InitDBInstances response: %s", response.ToJsonString())
	return 0, "success", nil
}

func (this *CdbService) InitDBInstancesWithScsAccountByNetEnvironmentType(netEnvironmentType int8, region string, cdbInstanceID string, password string) (code int, retmsg string, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("InitDBInstancesWithScsAccount error"))

	secretId, secretKey, err := service.GetSecretIdAndKeyByNetworkEnvType(netEnvironmentType)
	if err != nil {
		errMsg := fmt.Sprintf("InitializeCDBInstance: GetSecretIdAndKeyOfScs err: %v", err)
		return model.CDB_API_NORMAL_ERROR, "", converter.GenSecCommonErrorCode(converter.CDB_ERROR, converter.ERROR_CODE_51003, converter.INTERNAL_ERROR_MESSAGE, errors.New(errMsg))
	}

	return this.InitDBInstances(secretId, secretKey, "", region, cdbInstanceID, password)
}

func (this *CdbService) InitDBInstancesWithScsAccount(region string, cdbInstanceID string, password string) (code int, retmsg string, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("InitDBInstancesWithScsAccount error"))

	secretId, secretKey, err := service.GetSecretIdAndKeyOfScs()
	if err != nil {
		errMsg := fmt.Sprintf("InitializeCDBInstance: GetSecretIdAndKeyOfScs err: %v", err)
		return model.CDB_API_NORMAL_ERROR, "", converter.GenSecCommonErrorCode(converter.CDB_ERROR, converter.ERROR_CODE_51003, converter.INTERNAL_ERROR_MESSAGE, errors.New(errMsg))
	}

	return this.InitDBInstances(secretId, secretKey, "", region, cdbInstanceID, password)
}

func (this *CdbService) InitDBInstancesStsAssumeRole(uin, subAccountUin, region string, cdbInstanceID string, password string) (code int, retmsg string, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("InitDBInstancesStsAssumeRole error"))

	secretId, secretKey, token, pass, err := service.StsAssumeRole(uin, subAccountUin, region)
	if err != nil {
		errMsg := fmt.Sprintf("InitDBInstancesStsAssumeRole: StsAssumeRole err: %v", err)
		return model.CDB_API_NORMAL_ERROR, "", converter.GenSecCommonErrorCode(converter.CDB_ERROR, converter.ERROR_CODE_51003, converter.INTERNAL_ERROR_MESSAGE, errors.New(errMsg))
	}

	if !pass {
		errMsg := fmt.Sprintf("InitDBInstancesStsAssumeRole: StsAssumeRole not pass")
		return model.CDB_API_NORMAL_ERROR, "", converter.GenSecCommonErrorCode(converter.CDB_ERROR, converter.ERROR_CODE_51003, converter.INTERNAL_ERROR_MESSAGE, errors.New(errMsg))
	}

	return this.InitDBInstances(secretId, secretKey, token, region, cdbInstanceID, password)
}

func (this *CdbService) DescribeDBInstancesRaw(uin, subAccountUin, region string, insIds, lanIps []string, statuses []int,
	reqMap map[string]string) (totalCnt int, cdbList []*model.CdbInstanceInfoV3, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, "DescribeDBInstancesRaw panic error")

	if reqMap == nil {
		reqMap = map[string]string{}
	}

	// 构造业务数据
	// 必传参数
	apiParaMap := make(map[string]string)
	apiParaMap["Region"] = region
	// CDB新接口 新增公共请求参数
	apiParaMap["Version"] = "2017-03-20"
	// 请求输入参数
	if insIds != nil {
		for i, insId := range insIds {
			apiParaMap["InstanceIds."+strconv.Itoa(i)] = insId
		}
	}
	if lanIps != nil {
		for i, lanIp := range lanIps {
			apiParaMap["Vips."+strconv.Itoa(i)] = lanIp
		}
	}
	if statuses != nil {
		for i, status := range statuses {
			apiParaMap["Status."+strconv.Itoa(i)] = strconv.Itoa(status)
		}
	}
	if v, ok := reqMap["ProjectId"]; ok {
		apiParaMap["ProjectId"] = v
	}
	if v, ok := reqMap["Offset"]; ok {
		apiParaMap["Offset"] = v
	}
	if v, ok := reqMap["Limit"]; ok {
		apiParaMap["Limit"] = v
	}

	apiService := qcloud.GetQcloudApiService()
	rsp, err := apiService.SendApiHttps(uin, subAccountUin, this.getCdbApiUrlV3(), model.CDB_API_GET_CDB_INSTANCE_ACTION_V3,
		this.geneEventIdServiceIndependent(), apiParaMap)
	if err != nil {
		if stackCodedErr, ok := err.(errorcode.StackCodedError); ok {
			return totalCnt, cdbList, stackCodedErr
		}
		errMsg := fmt.Sprintf("GetCdbInstanceInfo: SendApiHttps err: %v", err)
		return totalCnt, cdbList, converter.GenSecCommonErrorCode(converter.CDB_ERROR,
			converter.ERROR_CODE_51000, converter.INTERNAL_ERROR_MESSAGE, errors.New(errMsg))
	}

	// result := &GetCdbInstanceInfoRsp{}
	type Response struct {
		Reply *model.GetCdbInstanceInfoRspV3 `json:"Response"`
	}
	result := &Response{}

	if err := json.Unmarshal([]byte(rsp), result); err != nil {
		errMsg := fmt.Sprintf("DescribeCVMInstancesStatus: Json Unmarshal err: %v", err)
		return totalCnt, cdbList, converter.GenCommonErrorCode(converter.INTERNAL_ERROR_MESSAGE, errors.New(errMsg))
	}
	if result.Reply.Error != nil {
		return totalCnt, cdbList, converter.GenSecCommonErrorCode(converter.CDB_ERROR, converter.ERROR_CODE_51000,
			fmt.Sprintf("%s %s", result.Reply.Error.Code, result.Reply.Error.Message), nil)
	}

	return result.Reply.TotalCount, result.Reply.Items, nil
}

func (this *CdbService) NewDefaultDescribeDBInstancesRequestBuilder() *DescribeDBInstancesRequestBuilder {
	return NewDescribeDBInstancesRequestBuilder().WithDomain(model.CDB_API_DOMAIN_INTERNAL)
}

func (this *CdbService) DescribeDBInstances(secretId, secretKey, token, region string, request *cdb.DescribeDBInstancesRequest) (totalCnt int, cdbList []*cdb.InstanceInfo, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, "DescribeDBInstances panic error")

	prof := qcloud.NewClientProfile()
	credential := common.NewTokenCredential(secretId, secretKey, token)
	client, err := cdb.NewClient(credential, region, prof)
	if err != nil {
		return totalCnt, cdbList, converter.GenCommonErrorCode(converter.INTERNAL_ERROR_MESSAGE, err)
	}
	logger.Infof("DescribeDBInstances request: %s", request.ToJsonString())
	if response, err := client.DescribeDBInstances(request); err != nil {
		return totalCnt, cdbList, converter.GenCommonErrorCode(converter.INTERNAL_ERROR_MESSAGE, err)
	} else {
		logger.Infof("DescribeDBInstances response: %s", response.ToJsonString())
		return int(*response.Response.TotalCount), response.Response.Items, nil
	}
}

func (this *CdbService) DescribeDBInstancesWithScsAccountByNetEnvironmentType(netEnvironmentType int8, region string, request *cdb.DescribeDBInstancesRequest) (totalCnt int, cdbList []*cdb.InstanceInfo, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, "DescribeDBInstancesWithScsAccount panic error")

	secretId, secretKey, err := service.GetSecretIdAndKeyByNetworkEnvType(netEnvironmentType)
	if err != nil {
		return totalCnt, cdbList, converter.GenCommonErrorCode(converter.INTERNAL_ERROR_MESSAGE, err)
	}

	return this.DescribeDBInstances(secretId, secretKey, "", region, request)
}

func (this *CdbService) DescribeDBInstancesWithScsAccount(region string, request *cdb.DescribeDBInstancesRequest) (totalCnt int, cdbList []*cdb.InstanceInfo, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, "DescribeDBInstancesWithScsAccount panic error")

	secretId, secretKey, err := service.GetSecretIdAndKeyOfScs()
	if err != nil {
		return totalCnt, cdbList, converter.GenCommonErrorCode(converter.INTERNAL_ERROR_MESSAGE, err)
	}

	return this.DescribeDBInstances(secretId, secretKey, "", region, request)
}

func (this *CdbService) DescribeDBInstancesStsAssumeRole(uin, subAccountUin, region string, request *cdb.DescribeDBInstancesRequest) (totalCnt int, cdbList []*cdb.InstanceInfo, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, "DescribeDBInstancesStsAssumeRole panic error")

	secretId, secretKey, token, pass, err := service.StsAssumeRole(uin, subAccountUin, region)
	if err != nil {
		return totalCnt, cdbList, converter.GenCommonErrorCode(converter.INTERNAL_ERROR_MESSAGE, err)
	}

	if !pass {
		errMsg := fmt.Sprintf("InitDBInstancesStsAssumeRole: StsAssumeRole not pass")
		return totalCnt, cdbList, converter.GenCommonErrorCode(converter.INTERNAL_ERROR_MESSAGE, errors.New(errMsg))
	}

	return this.DescribeDBInstances(secretId, secretKey, token, region, request)
}

func (this *CdbService) DescribeDBRoutes(
	secretId string,
	secretKey string,
	region string,
	instanceId string) (response *cdb.DescribeDBRoutesResponse, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, "DescribeDBRoutes panic error")

	// 初始化客户端
	prof := qcloud.NewClientProfile()
	credential := common.NewCredential(secretId, secretKey)
	client, err := cdb.NewClient(credential, region, prof)
	if err != nil {
		return nil, converter.GenCommonErrorCode(converter.INTERNAL_ERROR_MESSAGE, err)
	}

	// 构造请求
	request := cdb.NewDescribeDBRoutesRequest()
	request.InstanceId = &instanceId
	request.SetDomain("cdb.internal.tencentcloudapi.com")

	logger.Infof("DescribeDBRoutesRequest request: %s", request.ToJsonString())
	if response, err := client.DescribeDBRoutes(request); err != nil {
		return response, converter.GenCommonErrorCode(converter.INTERNAL_ERROR_MESSAGE, err)
	} else {
		logger.Infof("DescribeDBRoutesRequest response: %s", response.ToJsonString())
		return response, nil
	}
}

func (o *CdbService) DescribeDBRoutesWithScsAccount(
	region string,
	instanceId string) (response *cdb.DescribeDBRoutesResponse, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, "DescribeDBRoutesWithScsAccount panic error")

	secretId, secretKey, err := service.GetSecretIdAndKeyOfScs()
	if err != nil {
		errMsg := fmt.Sprintf("DescribeDBRoutesWithScsAccount: GetSecretIdAndKeyOfScs err: %v", err)
		return nil, converter.GenCommonErrorCode(converter.INTERNAL_ERROR_MESSAGE, errors.New(errMsg))
	}
	return o.DescribeDBRoutes(secretId, secretKey, region, instanceId)
}

func (this *CdbService) NewDefaultIsolateDBInstanceRequestBuilder() *IsolateDBInstanceRequestBuilder {
	return NewIsolateDBInstanceRequestBuilder().WithDomain(model.CDB_API_DOMAIN_INTERNAL)
}

func (this *CdbService) IsolateDBInstance(secretId, secretKey, token, region string,
	request *cdb.IsolateDBInstanceRequest) (sucess bool, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, "DescribeDBInstances panic error")

	prof := qcloud.NewClientProfile()
	credential := common.NewTokenCredential(secretId, secretKey, token)
	client, err := cdb.NewClient(credential, region, prof)
	if err != nil {
		return false, converter.GenCommonErrorCode(converter.INTERNAL_ERROR_MESSAGE, err)
	}
	logger.Infof("DescribeDBInstances request: %s", request.ToJsonString())
	if response, err := client.IsolateDBInstance(request); err != nil {
		return false, converter.GenCommonErrorCode(converter.INTERNAL_ERROR_MESSAGE, err)
	} else {
		logger.Infof("DescribeDBInstances response: %s", response.ToJsonString())
		return true, nil
	}
}

func (this *CdbService) IsolateDBInstanceWithScsAccountByNetEnvironmentType(netEnvironmentType int8, region string,
	request *cdb.IsolateDBInstanceRequest) (success bool, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("TerminateInstanceWithScsAccount error"))

	secretId, secretKey, err := service.GetSecretIdAndKeyByNetworkEnvType(netEnvironmentType)
	if err != nil {
		return false, converter.GenCommonErrorCode("", err)
	}
	return this.IsolateDBInstance(secretId, secretKey, "", region, request)
}

func (this *CdbService) IsolateDBInstanceWithScsAccount(region string,
	request *cdb.IsolateDBInstanceRequest) (success bool, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("TerminateInstanceWithScsAccount error"))

	secretId, secretKey, err := service.GetSecretIdAndKeyOfScs()
	if err != nil {
		return false, converter.GenCommonErrorCode("", err)
	}
	return this.IsolateDBInstance(secretId, secretKey, "", region, request)
}

/*
*
获取本集群 CDB 列表的唯一实例
如果找不到或者多于一个, 则直接 panic, 请注意捕获
*/
func (this *CdbService) MustGetCdbInstanceForClusterId(clusterId int64) *table2.Cdb {
	cdbInstanceList, err := service8.GetTableService().ListCdbByClusterId(clusterId)
	if err != nil {
		logger.Errorf("Failed to ListCdbByClusterId for %d because %+v", clusterId, err)
		panic(errorcode.NewStackError(errorcode.InternalErrorCode, "", err))
	}
	if len(cdbInstanceList) != 1 {
		logger.Errorf("Expect only 1 CDB instance for cluster id %d, found %d", clusterId, len(cdbInstanceList))
		panic(errorcode.NewStackError(errorcode.InternalErrorCode, "CDB instance number is not 1", nil))
	}
	return cdbInstanceList[0]
}

func (this *CdbService) IsInstanceIsolate(instance *cdb.InstanceInfo) bool {
	return *instance.Status == model.CDB_STATUS_ISOLATING || *instance.Status == model.CDB_STATUS_ISOLATED
}

func (this *CdbService) IsInstanceRunning(instance *cdb.InstanceInfo) bool {
	return *instance.Status == model.CDB_STATUS_RUNNING
}

func (this *CdbService) UpdateInstanceInfo(id, memory, volume int64) {
	service2.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.ExecuteSqlWithArgs("update Cdb set Memory=? and Volume=? where Id=?", memory, volume, id)
		return nil
	}).Close()
}

func (this *CdbService) UpgradeDBInstanceResourceByNetworkEnvType(netEnvironmentType int8, region, instanceId string, Memory, Volume int64) error {
	cdbInstance, err := this.DescribeDBInstanceByNetworkEnvType(netEnvironmentType, region, instanceId)
	if err != nil {
		return err
	}

	if !this.IsInstanceRunning(cdbInstance) {
		msg := fmt.Sprintf("CDB instance is not running, %s", instanceId)
		return errors.New(msg)
	}

	if *cdbInstance.Memory == Memory && *cdbInstance.Volume == Volume {
		return nil
	}

	upgradeReq := this.NewDefaultUpgradeDBInstanceRequestBuilder().
		WithInstance(instanceId).
		WithMemoryVolume(Memory, Volume).
		Build()

	_, _, err = this.UpgradeDBInstanceWithScsAccountByNetworkEnvType(netEnvironmentType, region, upgradeReq)
	return err
}

func (this *CdbService) UpgradeDBInstanceResource(region, instanceId string, Memory, Volume int64) error {
	cdbInstance, err := this.DescribeDBInstance(region, instanceId)
	if err != nil {
		return err
	}

	if !this.IsInstanceRunning(cdbInstance) {
		msg := fmt.Sprintf("CDB instance is not running, %s", instanceId)
		return errors.New(msg)
	}

	if *cdbInstance.Memory == Memory && *cdbInstance.Volume == Volume {
		return nil
	}

	upgradeReq := this.NewDefaultUpgradeDBInstanceRequestBuilder().
		WithInstance(instanceId).
		WithMemoryVolume(Memory, Volume).
		Build()

	_, _, err = this.UpgradeDBInstanceWithScsAccount(region, upgradeReq)
	return err
}

func (this *CdbService) DescribeDBInstanceByNetworkEnvType(netEnvironmentType int8, region, instanceId string) (*cdb.InstanceInfo, error) {
	describeReq := this.NewDefaultDescribeDBInstancesRequestBuilder().WithInstancesIds([]string{instanceId}).Build()
	totalCount, cdbSet, err := this.DescribeDBInstancesWithScsAccountByNetEnvironmentType(netEnvironmentType, region, describeReq)
	if totalCount > 1 || err != nil {
		msg := fmt.Sprintf("DescribeDBInstances returned multiple instances or err, %s", instanceId)
		return nil, errors.New(msg)
	} else if totalCount == 0 || len(cdbSet) == 0 {
		msg := fmt.Sprintf("CDB instance does not exist, return success instead, %s", instanceId)
		return nil, errors.New(msg)
	}
	return cdbSet[0], nil
}

func (this *CdbService) DescribeDBInstance(region, instanceId string) (*cdb.InstanceInfo, error) {
	describeReq := this.NewDefaultDescribeDBInstancesRequestBuilder().WithInstancesIds([]string{instanceId}).Build()
	totalCount, cdbSet, err := this.DescribeDBInstancesWithScsAccount(region, describeReq)
	if totalCount > 1 || err != nil {
		msg := fmt.Sprintf("DescribeDBInstances returned multiple instances or err, %s", instanceId)
		return nil, errors.New(msg)
	} else if totalCount == 0 || len(cdbSet) == 0 {
		msg := fmt.Sprintf("CDB instance does not exist, return success instead, %s", instanceId)
		return nil, errors.New(msg)
	}
	return cdbSet[0], nil
}

func NewCdbService() *CdbService {
	return &CdbService{}
}

func GetCdbService() *CdbService {
	if cdbService == nil {
		cdbService = NewCdbService()
	}
	return cdbService
}
