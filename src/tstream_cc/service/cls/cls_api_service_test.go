package cls

import (
	"encoding/json"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/log"
	"testing"
	"time"
)

func TestListLogSetsWithAuth(t *testing.T) {
	logSets, err := ListLogSetsWithAuth(*fTestSecretId, *fTestSecretKey, *fTestToken, *fTestRegion)
	if err == nil {
		b, _ := json.MarshalIndent(logSets, "", "  ")
		t.Logf("list log set success:\n%s", string(b))
	} else {
		t.Error(err)
	}
}

func TestGetLogSetWithAuth(t *testing.T) {
	logSet, err := GetLogSetWithAuth(*fTestSecretId, *fTestSecretKey, *fTestToken, *fTestRegion, *fTestLogSetId)
	if err == nil {
		b, _ := json.MarshalIndent(logSet, "", "  ")
		t.Logf("get log set success:\n%s", string(b))
	} else {
		t.Error(err)
	}
}

func TestListLogTopicsWithAuth(t *testing.T) {
	if rsp, err := ListLogTopicsWithAuth(*fTestSecretId, *fTestSecretKey, *fTestToken, *fTestRegion,
		*fTestLogSetId); err != nil {
		t.Error(err)
	} else {
		b, _ := json.MarshalIndent(rsp, "", "  ")
		t.Logf("%s", string(b))
	}
}

func TestGetLogTopicWithAuth(t *testing.T) {
	if rsp, err := GetLogTopicWithAuth(*fTestSecretId, *fTestSecretKey, *fTestToken, *fTestRegion,
		*fTestTopicIds); err != nil {
		t.Error(err)
	} else {
		b, _ := json.MarshalIndent(rsp, "", "  ")
		t.Logf("%s", string(b))
	}
}

func TestSearchLogWithAuth(t *testing.T) {
	req := &log.SearchLogReq{
		LogSetId:  *fTestLogSetId,
		TopicIds:  *fTestTopicIds,
		StartTime: time.Unix(*fTestStartTime, 0).Format("2006-01-02 15:04:05.000"),
		EndTime:   time.Unix(*fTestEndTime, 1).Format("2006-01-02 15:04:05.000"),
		Query:     *fTestQuery,
		Limit:     int32(*fTestLimit),
		Context:   *fTestContext,
		Sort:      *fTestSort,
	}

	if rsp, err := SearchLogWithAuth(*fTestSecretId, *fTestSecretKey, *fTestToken, *fTestRegion, req); err != nil {
		t.Error(err)
	} else {
		b, _ := json.MarshalIndent(rsp, "", "  ")
		t.Logf("%s", string(b))
	}
}

func TestGetIndexWithAuth(t *testing.T) {
	if rsp, err := GetIndexWithAuth(*fTestSecretId, *fTestSecretKey, *fTestToken, *fTestRegion,
		*fTestTopicIds); err != nil {
		t.Error(err)
	} else {
		b, _ := json.MarshalIndent(rsp, "", "  ")
		t.Logf("%s", string(b))
	}
}

func TestModifyIndexWithAuth(t *testing.T) {
	if err := ModifyIndexWithAuth(*fTestSecretId, *fTestSecretKey, *fTestToken, *fTestRegion,
		&log.ClsIndexConf{
			TopicId:   *fTestTopicIds,
			Effective: true,
			Rule: &log.ClsIndexRule{
				FullText: &log.ClsFullTextIndexRule{
					CaseSensitive: false,
					Tokenizer:     "!@#%^&*()-_=\"', <>/?|\\;:\n\t\r[]{}",
				},
				KeyValue: &log.ClsKeyValueIndexRule{
					CaseSensitive: true,
					Keys: []string{
						"pod_label_jobSerialId",
						"pod_label_jobRunningOrderId",
						"pod_name",
					},
					Types: []string{
						"text",
						"long",
						"text",
					},
					Tokenizers: []string{
						"",
						"",
						"",
					},
					SqlFlags: []bool{
						true,
						true,
						true,
					},
				},
			},
		}); err != nil {
		t.Error(err)
	} else {
		t.Logf("success")
	}
}

func TestCreateMachineGroup(t *testing.T) {
	if err := CreateMachineGroup(*fTestSecretId, *fTestSecretKey, *fTestToken, *fTestRegion, &log.MachineGroup{
		GroupId:   "",
		GroupName: *fTestClsGroupName,
		Type:      "label",
		Labels:    []string{*fTestClsGroupName},
	}); err != nil {
		t.Error(err)
	} else {
		t.Logf("success")
	}
}

func TestGetMachineGroup(t *testing.T) {
	if rsp, err := GetMachineGroup(*fTestSecretId, *fTestSecretKey, *fTestToken, *fTestRegion,
		*fTestClsGroupId); err != nil {
		t.Error(err)
	} else {
		b, _ := json.MarshalIndent(rsp, "", "  ")
		t.Logf("%s", string(b))
	}
}

func TestListTopicMachineGroups(t *testing.T) {
	if rsp, err := ListTopicMachineGroups(*fTestSecretId, *fTestSecretKey, *fTestToken, *fTestRegion, *fTestTopicIds); err != nil {
		t.Error(err)
	} else {
		b, _ := json.MarshalIndent(rsp, "", "  ")
		t.Logf("bind machine groups %d, %s", len(rsp.MachineGroups), string(b))
	}
}

func TestModifyTopicMachineGroups(t *testing.T) {
	groups := make([]string, 0, 1)
	if len(*fTestClsGroupId) > 0 {
		groups = append(groups, *fTestClsGroupId)
	}
	if err := ModifyTopicMachineGroups(*fTestSecretId, *fTestSecretKey, *fTestToken, *fTestRegion, *fTestTopicIds, groups); err != nil {
		t.Error(err)
	} else {
		t.Logf("success")
	}
}

func TestGetLogTopic(t *testing.T) {
	rsp, err := GetLogTopic(*fTestUin, *fTestSubUin, *fTestRegion, *fTestTopicIds)
	if err != nil {
		t.Fatal(err)
	}
	b, _ := json.MarshalIndent(rsp, "", "  ")
	t.Logf("%s", string(b))
}

func TestIsTopicBelongToLogSet(t *testing.T) {
	err := TopicBelongToLogSet(*fTestUin, *fTestSubUin, *fTestRegion, *fTestLogSetId, *fTestTopicIds)
	t.Log(err)
}
