package cls

import (
	"crypto/hmac"
	"crypto/sha1"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net"
	"net/http"
	"net/url"
	"sort"
	"strings"
	"sync"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/log"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
	"time"
)

func calSha1sum(msg string) string {
	h := sha1.New()
	h.Write([]byte(msg))
	return fmt.Sprintf("%x", h.Sum(nil))
}

func calSha1HMACDigest(key, msg string) string {
	h := hmac.New(sha1.New, []byte(key))
	h.Write([]byte(msg))
	return fmt.Sprintf("%x", h.Sum(nil))
}

func signature(secretID, secretKey, method, path string, params, headers url.Values, expire int64) string {
	var signedHeaderList []string
	var signedParameterList []string
	hs := url.Values{}
	for key, values := range headers {
		for _, value := range values {
			var lowerKey = strings.ToLower(key)
			if lowerKey == "content-type" || lowerKey == "content-md5" || lowerKey == "host" || lowerKey[0] == 'x' {
				hs.Add(lowerKey, value)
				signedHeaderList = append(signedHeaderList, lowerKey)
			}
		}
	}
	var formatHeaders = hs.Encode()
	sort.Strings(signedHeaderList)
	ps := url.Values{}
	for key, values := range params {
		for _, value := range values {
			var lowerKey = strings.ToLower(key)
			ps.Add(lowerKey, value)
			signedParameterList = append(signedParameterList, lowerKey)
		}
	}
	var formatParameters = ps.Encode()
	sort.Strings(signedParameterList)
	var formatString = fmt.Sprintf("%s\n%s\n%s\n%s\n", strings.ToLower(method), path, formatParameters, formatHeaders)
	var signTime = fmt.Sprintf("%d;%d", time.Now().Unix()-60, time.Now().Unix()+expire)
	//signTime = "1510109254;1510109314"
	var stringToSign = fmt.Sprintf("sha1\n%s\n%s\n", signTime, calSha1sum(formatString))
	var signKey = calSha1HMACDigest(secretKey, signTime)
	var signature = calSha1HMACDigest(signKey, stringToSign)
	return strings.Join([]string{
		"q-sign-algorithm=sha1",
		"q-ak=" + secretID,
		"q-sign-time=" + signTime,
		"q-key-time=" + signTime,
		"q-header-list=" + strings.Join(signedHeaderList, ";"),
		"q-url-param-list=" + strings.Join(signedParameterList, ";"),
		"q-signature=" + signature,
	}, "&")
}

func handleStatusCode(rsp *http.Response) error {
	// https://cloud.tencent.com/document/product/614/12402
	if rsp.StatusCode == 200 {
		logger.Debugf("rsp header : %v", rsp.Header)
		return nil
	}

	logger.Errorf("response header: %v", rsp.Header)
	logger.Errorf("response request: %v", rsp.Request)
	bodyData, err := ioutil.ReadAll(rsp.Body)
	if err != nil {
		return errorcode.InternalErrorCode.NewWithErr(err)
	}

	msg := fmt.Sprintf("CLS failed with statuCode: %d, CLS-RequestId: %s, body: %s",
		rsp.StatusCode, rsp.Header.Get("X-Cls-Requestid"), string(bodyData),
	)

	logger.Warningf("%s", msg)

	if rsp.StatusCode == 404 {
		return errorcode.ResourceNotFoundCode.NewWithInfo(msg, nil)
	} else if rsp.StatusCode == 400 {
		return errorcode.InvalidParameter_IllegalSearchKeyword.NewWithInfo("Incorrect search parameter. Please check whether it contains special characters that are not allowed.", nil)
	} else if rsp.StatusCode == 403 {
		return errorcode.InvalidParameter_CLSNotEnabled.NewWithInfo("Cloud Log Service is not activated. Please access https://console.cloud.tencent.com/cls to activate it.", nil)
	}
	return errorcode.InternalError_CLS.NewWithInfo(msg, nil)
}

func sendClsRequestByUrl(url, method, body, tmpSecretId, tmpSecretKey, token string) ([]byte, error) {
	logger.Debugf("%s %s with body: %s", method, url, body)
	request, err := http.NewRequest(method, url, strings.NewReader(body))
	if err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}
	auth := signature(tmpSecretId, tmpSecretKey, method, request.URL.Path, nil, nil, 300)
	request.Header.Set("Authorization", auth)
	if len(token) > 0 {
		request.Header.Set("x-cls-token", token)
	}
	request.Header.Set("Content-Type", "application/json")

	dialTimeout := time.Duration(5)
	timeout := time.Duration(5)
	client := &http.Client{
		Transport: &http.Transport{
			DialContext: (&net.Dialer{
				Timeout:   dialTimeout * time.Second,
				KeepAlive: dialTimeout * time.Second,
			}).DialContext,
			TLSHandshakeTimeout:   timeout * time.Second,
			ResponseHeaderTimeout: timeout * time.Second,
			ExpectContinueTimeout: timeout * time.Second,
			DisableKeepAlives:     true,
		},
	}

	resp, err := client.Do(request)
	if err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}
	defer resp.Body.Close()

	bodyBytes, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}

	if err = handleStatusCode(resp); err != nil {
		return nil, err
	}
	logger.Debug(resp.StatusCode, string(bodyBytes))
	return bodyBytes, nil
}

// 永久授权@token填写空字符串
func ListLogSetsWithAuth(secretId, secretKey, token, region string) ([]*log.LogSetDescription, error) {
	clsUrl := fmt.Sprintf("https://%s.cls.tencentyun.com/logsets", region)

	bodyBytes, err := sendClsRequestByUrl(clsUrl, "GET", "", secretId, secretKey, token)
	if err != nil {
		return nil, err
	}

	logSetsRsp := &log.ClsListLogSetsRsp{}
	err = json.Unmarshal(bodyBytes, logSetsRsp)
	if err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}
	return logSetsRsp.LogSets, nil
}

//func GetLogSet(uin, subAccountUin, region, logsetId string) (logSet *log.LogSetDescription, err error) {
//	sid, sKey, token, _, err := service.StsAssumeRole(uin, subAccountUin, region)
//	if err != nil {
//		return nil, errorcode.NewStackError(errorcode.AuthFailure_UnauthorizedOperation,
//			"Failed to get credential token", err)
//	}
//	adapter := &CloudApiAdapter{}
//	return adapter.GetLogSetWithAuth(sid, sKey, token, region, logsetId)
//	//return GetLogSetWithAuth(sid, sKey, token, region, logsetId)
//}

// 如果某个logSetIds 失败了， logSet 仍然会有成功的logSet数据，由调用方自己判断，要不要使用
func GetLogSetMulti(uin, subAccountUin, region string, logSetIds []string) (logSet []*log.LogSetDescription,
	err error) {
	logger.Debugf("Getting StsAssumeRole for UIN %s, SUB_ACCOUNT_UIN %s, REGION %s", uin, subAccountUin, region)
	sid, sKey, token, _, err := service.StsAssumeRole(uin, subAccountUin, region)
	if err != nil {
		return nil, errorcode.NewStackError(errorcode.AuthFailure_UnauthorizedOperation,
			"Failed to get credential token", err)
	}

	logSetIds = service.UniqueSliceString(logSetIds)
	wg := sync.WaitGroup{}
	wg.Add(len(logSetIds))
	result := make(chan interface{}, len(logSetIds))
	for _, it := range logSetIds {
		go func(logSet string) {
			//r, err := GetLogSetWithAuth(sid, sKey, token, region, logSet)
			adapter := &CloudApiAdapter{}
			r, err := adapter.GetLogSetWithAuth(sid, sKey, token, region, logSet, uin, subAccountUin)
			if err != nil {
				result <- err
			} else {
				result <- r
			}
			wg.Done()
		}(it)
	}

	wg.Wait()
	close(result)
	logSet = make([]*log.LogSetDescription, 0)
	for it := range result {
		if tmpErr, ok := it.(error); ok {
			err = tmpErr
		} else {
			tmp, _ := it.(*log.LogSetDescription)
			logSet = append(logSet, tmp)
		}
	}
	return logSet, err
}

// 永久授权@token填写空字符串
func GetLogSetWithAuth(secretId, secretKey, token, region, logsetId string) (*log.LogSetDescription, error) {
	clsUrl := fmt.Sprintf("https://%s.cls.tencentyun.com/logset?logset_id=%s", region, logsetId)

	bodyBytes, err := sendClsRequestByUrl(clsUrl, "GET", "", secretId, secretKey, token)
	if err != nil {
		logger.Errorf("GetLogSetWithAuth: %v", err)
		return nil, err
	}

	desc := &log.LogSetDescription{}
	err = json.Unmarshal(bodyBytes, desc)
	return desc, errorcode.InternalErrorCode.NewWithErr(err)
}

func ListLogTopicsWithAuth(secretId, secretKey, token, region, logSetId string) (*log.ClsListTopicRsp, error) {
	clsUrl := fmt.Sprintf("https://%s.cls.tencentyun.com/topics?logset_id=%s", region, logSetId)

	bodyBytes, err := sendClsRequestByUrl(clsUrl, "GET", "", secretId, secretKey, token)
	if err != nil {
		logger.Errorf("ListLogTopicsWithAuth: %v", err)
		return nil, err
	}

	rsp := &log.ClsListTopicRsp{}
	logger.Debugf("CLS response %+v", string(bodyBytes))
	err = json.Unmarshal(bodyBytes, rsp)
	if err != nil {
		logger.Errorf("ListLogTopicsWithAuth: error: %v", err)
		return nil, err
	}
	return rsp, nil
}

func GetLogTopic(uin, subAccountUin, region, topicId string) (*log.ClsTopicDescription, error) {
	sid, sKey, token, _, err := service.StsAssumeRole(uin, subAccountUin, region)
	if err != nil {
		logger.Errorf("GetTokenCredential error: %+v", err)
		return nil, errorcode.NewStackError(errorcode.AuthFailure_UnauthorizedOperation,
			"Failed to get credential token", err)
	}
	//return GetLogTopicWithAuth(sid, sKey, token, region, topicId)
	adapter := &CloudApiAdapter{}
	return adapter.GetLogTopicWithAuth(sid, sKey, token, region, topicId, uin, subAccountUin)
}

func GetLogTopicMulti(uin, subAccountUin, region string, topicIds []string) (topicSet []*log.ClsTopicDescription,
	err error) {
	sid, sKey, token, _, err := service.StsAssumeRole(uin, subAccountUin, region)
	if err != nil {
		return nil, errorcode.NewStackError(errorcode.AuthFailure_UnauthorizedOperation,
			"Failed to get credential token", err)
	}

	topicIds = service.UniqueSliceString(topicIds)
	wg := sync.WaitGroup{}
	wg.Add(len(topicIds))
	result := make(chan interface{}, len(topicIds))
	for _, it := range topicIds {
		go func(topicIds string) {
			//r, err := GetLogTopicWithAuth(sid, sKey, token, region, topicIds)
			adapter := &CloudApiAdapter{}
			r, err := adapter.GetLogTopicWithAuth(sid, sKey, token, region, topicIds, uin, subAccountUin)
			if err != nil {
				result <- err
			} else {
				result <- r
			}
			wg.Done()
		}(it)
	}

	wg.Wait()
	close(result)
	topicSet = make([]*log.ClsTopicDescription, 0)
	for it := range result {
		if tmpErr, ok := it.(error); ok {
			err = tmpErr
		} else {
			tmp, _ := it.(*log.ClsTopicDescription)
			topicSet = append(topicSet, tmp)
		}
	}
	return topicSet, err
}

func GetLogTopicWithAuth(secretId, secretKey, token, region, topicId string) (*log.ClsTopicDescription, error) {
	clsUrl := fmt.Sprintf("https://%s.cls.tencentyun.com/topic?topic_id=%s", region, topicId)

	bodyBytes, err := sendClsRequestByUrl(clsUrl, "GET", "", secretId, secretKey, token)
	if err != nil {
		logger.Errorf("GetLogTopicWithAuth: %v", err)
		return nil, err
	}

	rsp := &log.ClsTopicDescription{}
	err = json.Unmarshal(bodyBytes, rsp)
	return rsp, errorcode.InternalErrorCode.NewWithErr(err)
}

func SearchLogWithAuth(secretId, secretKey, token, region string, req *log.SearchLogReq) (*log.SearchLogRsp, error) {
	clsUrl := fmt.Sprintf("https://%s.cls.tencentyun.com/searchlog?%s", region, req.QueryString())

	bodyBytes, err := sendClsRequestByUrl(clsUrl, "GET", "", secretId, secretKey, token)
	if err != nil {
		logger.Errorf("SearchLogWithAuth: %v, request url : %s", err, clsUrl)
		return nil, err
	}

	rsp := &log.SearchLogRsp{}
	err = json.Unmarshal(bodyBytes, rsp)
	return rsp, errorcode.InternalErrorCode.NewWithErr(err)
}

func GetIndexWithAuth(secretId, secretKey, token, region, topicId string) (*log.ClsIndexConf, error) {
	clsUrl := fmt.Sprintf("https://%s.cls.tencentyun.com/index?topic_id=%s", region, topicId)

	bodyBytes, err := sendClsRequestByUrl(clsUrl, "GET", "", secretId, secretKey, token)
	if err != nil {
		logger.Errorf("GetIndexWithAuth: %v", err)
		return nil, err
	}

	rsp := &log.ClsIndexConf{}
	err = json.Unmarshal(bodyBytes, rsp)
	if err != nil {
		logger.Errorf("GetIndexWithAuth: error: %v", err)
		return nil, err
	}
	return rsp, nil
}

func ModifyIndexWithAuth(secretId, secretKey, token, region string, req *log.ClsIndexConf) error {
	clsUrl := fmt.Sprintf("https://%s.cls.tencentyun.com/index", region)

	reqBody, err := json.Marshal(req)
	if err != nil {
		return errorcode.InternalErrorCode.NewWithErr(err)
	}

	_, err = sendClsRequestByUrl(clsUrl, "PUT", string(reqBody), secretId, secretKey, token)
	if err != nil {
		return err
	}
	return nil
}

func CreateMachineGroup(secretId, secretKey, token, region string, req *log.MachineGroup) error {
	clsUrl := fmt.Sprintf("https://%s.cls.tencentyun.com/machinegroup", region)

	reqBody, err := json.Marshal(req)
	if err != nil {
		logger.Errorf("CreateMachineGroup: %v", err)
		return err
	}

	_, err = sendClsRequestByUrl(clsUrl, "POST", string(reqBody), secretId, secretKey, token)
	if err != nil {
		logger.Errorf("CreateMachineGroup: %v", err)
		return err
	}
	return nil
}

func GetMachineGroup(secretId, secretKey, token, region, groupId string) (*log.MachineGroup, error) {
	clsUrl := fmt.Sprintf("https://%s.cls.tencentyun.com/machinegroup?group_id=%s", region, groupId)

	bodyBytes, err := sendClsRequestByUrl(clsUrl, "GET", "", secretId, secretKey, token)
	if err != nil {
		logger.Errorf("GetMachineGroup: %v", err)
		return nil, err
	}

	rsp := &log.MachineGroup{}
	err = json.Unmarshal(bodyBytes, rsp)
	if err != nil {
		logger.Errorf("GetMachineGroup: error: %v", err)
		return nil, err
	}
	return rsp, nil
}

func ListTopicMachineGroups(secretId, secretKey, token, region, topicId string) (machineGroups *log.MachineGroups, err error) {
	clsUrl := fmt.Sprintf("https://%s.cls.tencentyun.com/topic/machinegroup?topic_id=%s", region, topicId)

	bodyBytes, err := sendClsRequestByUrl(clsUrl, "GET", "", secretId, secretKey, token)
	if err != nil {
		logger.Errorf("ListTopicMachineGroups: %v", err)
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	machineGroups = &log.MachineGroups{}
	err = json.Unmarshal(bodyBytes, machineGroups)
	if err != nil {
		logger.Errorf("ListTopicMachineGroups: error: %v", err)
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	return machineGroups, nil
}

func ModifyTopicMachineGroups(secretId, secretKey, token, region, topicId string, machineGroupIds []string) (err error) {
	clsUrl := fmt.Sprintf("https://%s.cls.tencentyun.com/topic/machinegroup?topic_id=%s", region, topicId)

	if machineGroupIds == nil {
		machineGroupIds = []string{}
	}
	req := &log.MachineGroupsRequest{MachineGroups: machineGroupIds}
	data, err := json.Marshal(req)
	if err != nil {
		return errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	_, err = sendClsRequestByUrl(clsUrl, "PUT", string(data), secretId, secretKey, token)
	if err != nil {
		logger.Errorf("ModifyTopicMachineGroups: %v", err)
		return errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	return nil
}

func TopicBelongToLogSet(uin, subAccountUin, region, logSet, topicId string) (err error) {
	if topicId == "" {
		return errorcode.InvalidParameterValueCode.ReplaceDesc("CLSTopicId is empty")
	}
	//topic, err := GetLogTopic(uin, subAccountUin, region, topicId)
	adapter := &CloudApiAdapter{}
	topic, err := adapter.GetLogTopic(uin, subAccountUin, region, topicId)
	if err != nil {
		return err
	}
	if topic.LogSetId != logSet {
		msg := fmt.Sprintf("topic_id %s not belong to log_set %s", topicId, logSet)
		return errorcode.InvalidParameterValueCode.ReplaceDesc(msg)
	}
	return nil
}

func TopicCanBeUse(uin, subAccountUin, region, logSet, topicId string) (err error) {
	err = TopicBelongToLogSet(uin, subAccountUin, region, logSet, topicId)
	if err != nil {
		return
	}

	currentUseTopicIdSet, err := service3.GetTableService().ListUserUseClsTopicId(uin, region)
	if err != nil {
		return
	}

	if _, ok := currentUseTopicIdSet[topicId]; ok {
		msg := fmt.Sprintf("topicId %s has been used", topicId)
		return errorcode.InvalidParameterValueCode.ReplaceDesc(msg)
	}
	return nil
}
