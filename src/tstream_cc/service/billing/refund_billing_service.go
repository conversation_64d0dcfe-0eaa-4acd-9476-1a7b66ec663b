package billing

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/uuid"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	constants2 "tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	constants "tencentcloud.com/tstream_galileo/src/tstream_cc/model/billing"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/billing"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config"
	"time"
)

const BILLING_GROUP_KEY = "Billing"
const BILLING_URL = "BILLING_URL"
const BILLING_RETRY_COUNT = 10

var hasInitedMetricVariables = false
var billingUrl string

func InitBillingVariables() {
	if hasInitedMetricVariables {
		return
	}

	billingUrl = config.MustGetRainbowConfigurationWithRetry(BILLING_GROUP_KEY, BILLING_URL, BILLING_RETRY_COUNT)
	logger.Infof("Init metric variables billingUrl:%s", billingUrl)
	hasInitedMetricVariables = true
}

func MarkDestroy(billingOrder *billing.BillingOrder) (err error) {
	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		if err := markBillingResource(tx, constants.ResourceStatusIsolated, billingOrder.ResourceId, billingOrder.AppId); err != nil {
			return err
		}
		if err := markClusterGroup(tx, constants2.CLUSTER_GROUP_STATUS_DELETED, billingOrder.ClusterGroupId); err != nil {
			return err
		}
		return nil
	}).Close()
	return nil
}

func markClusterGroup(tx *dao.Transaction, status int, clusterGroupId int64) error {
	sql := "UPDATE ClusterGroup SET Status = ? WHERE Id = ?"
	tx.ExecuteSqlWithArgs(sql, status, clusterGroupId)
	return nil
}

func markBillingResource(tx *dao.Transaction, status int, resourceId string, appId int64) error {
	sql := "UPDATE BillingResource SET Status = ? WHERE ResourceId = ? AND AppId = ?"
	tx.ExecuteSqlWithArgs(sql, status, resourceId, appId)
	return nil
}

func ExecRefund(billingOrder *billing.BillingOrder) (string, error) {
	reqData := buildRequestParam(billingOrder)
	return sendRequest(reqData)
}

func buildRequestParam(billingOrder *billing.BillingOrder) string {
	reqData := make(map[string]interface{}, 0)
	reqData["version"] = "1.0"
	reqData["caller"] = "oceanus_galileo"
	reqData["componentName"] = "trade"
	reqData["eventId"] = strings.ReplaceAll(uuid.New(), "-", "")
	reqData["timestamp"] = time.Now().Unix()

	interfacePram := make(map[string]interface{}, 0)
	reqData["interface"] = interfacePram
	interfacePram["interfaceName"] = "save"

	innerParam := make(map[string]interface{}, 0)
	interfacePram["para"] = innerParam
	innerParam["msgType"] = "dealDeliveryCallback"

	msgContent := make(map[string]interface{}, 0)
	innerParam["msgContent"] = msgContent
	msgContent["type"] = "sp_oceanus_exclusive"
	msgContent["dealName"] = billingOrder.DealName
	msgContent["deliveryResult"] = 3

	resourceIds := make([]string, 0)
	resourceIds = append(resourceIds, billingOrder.ResourceId)
	msgContent["resourceIds"] = resourceIds

	resources := make([]map[string]string, 0)
	resource := make(map[string]string, 0)
	resource["resourceId"] = billingOrder.ResourceId
	resource["executionStartTime"] = billingOrder.CreateTime
	resource["executionEndTime"] = billingOrder.UpdateTime
	resource["resourceNewStartTime"] = billingOrder.CreateTime
	resource["resourceNewEndTime"] = billingOrder.UpdateTime
	resources = append(resources, resource)
	msgContent["resources"] = resources

	bytes, _ := json.Marshal(reqData)
	return string(bytes)
}

func sendRequest(requestData string) (string, error) {
	logger.Infof("To billing server, request: %s", requestData)

	req, err := http.NewRequest("POST", billingUrl, strings.NewReader(requestData))
	if err != nil {
		logger.Errorf("An error occurred while sending the message.  %+v", err)
		return "", err
	}

	req.Header.Set("Content-Type", "text/json;charset=utf-8")

	httpClient := &http.Client{}
	httpClient.Timeout = time.Duration(5) * time.Second
	resp, err := httpClient.Do(req)
	defer func() {
		if resp != nil {
			_ = resp.Body.Close()
		}
	}()
	if err != nil {
		logger.Errorf("An error occurred while sending the message.  %+v", err)
		return "", err
	}
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logger.Errorf("An error occurred while sending the message.  %+v", err)
		return "", err
	}

	respStr := string(body)
	resultMap := make(map[string]interface{}, 0)
	err = json.Unmarshal(body, &resultMap)
	if err != nil {
		logger.Infof("refund result from billing:  %+v", respStr)
		return "", err
	}

	returnCode := resultMap["returnCode"].(float64)
	returnMsg := resultMap["returnMsg"].(string)
	if returnCode != 0 || returnMsg != "OK" {
		return "", errorcode.InternalErrorCode.ReplaceDesc(fmt.Sprintf("Request billing server is failed, resp:  %s", respStr))
	}

	logger.Infof("refund result from billing:  %s", respStr)
	return respStr, nil
}
