package billing

import (
	"encoding/json"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	billing "tencentcloud.com/tstream_galileo/src/tstream_cc/model/billing"
	billingProtocol "tencentcloud.com/tstream_galileo/src/tstream_cc/model/billing"
	billingTable "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/billing"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
)

type BillingResourceService struct {
}

func NewBillingResourceService() *BillingResourceService {
	return &BillingResourceService{}
}

func (b *BillingResourceService) GetResourceByFlowId(flowId int64) (resources []*billingTable.BillingResource, err error) {
	sql := "SELECT * FROM BillingResource WHERE FlowId = ?"

	_, data, err := service.GetTxManager().GetQueryTemplate().DoQueryWithArgs(sql, flowId)
	if err != nil {
		err = errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		return
	}

	resources = make([]*billingTable.BillingResource, 0, len(data))
	for _, d := range data {
		resource := &billingTable.BillingResource{}
		err = util.ScanMapIntoStruct(resource, d)
		if err != nil {
			err = errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
			return
		}
		resources = append(resources, resource)
	}

	return
}

func (b *BillingResourceService) GetResourceById(resourceId string) (resource *billingTable.BillingResource,
	err error) {
	sql := "SELECT * FROM BillingResource WHERE ResourceId = ?"
	_, data, err := service.GetTxManager().GetQueryTemplate().DoQueryWithArgs(sql, resourceId)
	if err != nil {
		err = errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		return
	}

	if len(data) == 0 {
		return nil, errorcode.NewStackError(errorcode.ResourceNotFoundCode, "no BillingResource found by this resource id", nil)
	} else if len(data) > 1 {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "more than 1 BillingResource found by this resource id", nil)
	}

	resources := make([]*billingTable.BillingResource, 0, len(data))
	for _, d := range data {
		newRes := &billingTable.BillingResource{}
		err = util.ScanMapIntoStruct(newRes, d)
		if err != nil {
			err = errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
			return
		}
		resources = append(resources, newRes)
	}

	return resources[0], nil
}

func (b *BillingResourceService) GetPayAppIdsTotalCount(subProductCode string, region int,
	international int, payMode int) (totalCount int,
	err error) {
	selectSql := "SELECT COUNT(DISTINCT(AppId)) AS Count"
	return newBillingResourceDataBase(selectSql, subProductCode, region).
		WithInternational(international).
		WithPayMode(payMode).
		QueryCount()
}

/**
 * 后付费 需要推送 用量，根据appid纬度，详情查看 https://tcb.woa.com/magical-brush/docs/754674275
 * 这里查询不能有状态筛选，因为已经删除的集群 可能在上一个周期产生了费用
 */
func (b *BillingResourceService) GetSendResourceAppIds(startTime string, endTime string) (appIdMap map[int64][]*billingTable.BillingResource, err error) {
	selectSql := "select br.* from BillingResource  br join ClusterGroup cg on cg.SerialId  = br.ResourceId " +
		"where br.ResourceId in  (select ResourceId from BillingOrder where PayMode = 0 and DealName not like '%NoBilling_%') " +
		"and br.PayMode = 0 and br.Type = 1 and (cg.StopTime = ? or cg.StopTime > ?) and cg.StartTime < ? and cg.StartTime != ?"
	args := make([]interface{}, 0)
	args = append(args, billing.NotIsolatedTimestamp)
	args = append(args, startTime)
	args = append(args, endTime)
	args = append(args, billing.NotIsolatedTimestamp)
	_, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(selectSql, args)
	if err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	appIdMap = make(map[int64][]*billingTable.BillingResource, 0)
	for _, d := range data {
		resource := &billingTable.BillingResource{}
		err = util.ScanMapIntoStruct(resource, d)
		if err != nil {
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
		if appIdMap[resource.AppId] == nil || len(appIdMap[resource.AppId]) < 1 {
			rs := make([]*billingTable.BillingResource, 0)
			rs = append(rs, resource)
			appIdMap[resource.AppId] = rs
		} else {
			appIdMap[resource.AppId] = append(appIdMap[resource.AppId], resource)
		}
	}
	return appIdMap, nil
}

func (b *BillingResourceService) GetPayAppIds(subProductCode string, region, international int, pageNo,
	pageSize *int, payMode int) (appIds []int64, err error) {
	selectSql := "SELECT DISTINCT(AppId)"
	db := newBillingResourceDataBase(selectSql, subProductCode, region).
		WithInternational(international).
		WithPayMode(payMode).
		WithLimit(pageNo, pageSize)
	sql, args := db.Build()

	_, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	appIds = make([]int64, 0, len(data))
	for _, d := range data {
		v, _ := d["AppId"]
		appId, err := strconv.ParseInt(string(v), 10, 64)
		if err != nil {
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
		appIds = append(appIds, appId)
	}
	return appIds, nil
}

func (b *BillingResourceService) GetUserResourcesTotalCount(subProductCode string, region int,
	appId int64, payMode int) (totalCount int, err error) {
	selectSql := "SELECT COUNT(ResourceId) AS Count"
	return newBillingResourceDataBase(selectSql, subProductCode, region).
		WithAppId(appId).
		WithPayMode(payMode).
		QueryCount()
}

func (b *BillingResourceService) GetUserResources(subProductCode string, region int,
	appId int64, payMode int, pageNo, pageSize *int) (resourceSet []*billingTable.BillingResource, err error) {
	selectSql := "SELECT *"
	return newBillingResourceDataBase(selectSql, subProductCode, region).
		WithAppId(appId).
		WithPayMode(payMode).
		WithLimit(pageNo, pageSize).
		QueryBillingResource()
}

func (b *BillingResourceService) GetResources(subProductCode string, region int, appId int64,
	resourceIds []string) (resourceSet []*billingTable.BillingResource, err error) {
	selectSql := "SELECT *"
	return newBillingResourceDataBase(selectSql, subProductCode, region).
		WithAppId(appId).
		WithResourceIds(resourceIds).
		QueryBillingResource()
}

func (b *BillingResourceService) ToResourceDetailSet(resources []*billingTable.BillingResource) (
	resourceDetailSet []*billingProtocol.ResourceDetail, err error) {
	resourceDetailSet = make([]*billingProtocol.ResourceDetail, 0, len(resources))
	for _, resource := range resources {
		resourceDetail, err := b.toResourceDetail(resource)
		if err != nil {
			return resourceDetailSet, err
		}
		resourceDetailSet = append(resourceDetailSet, resourceDetail)
	}
	return resourceDetailSet, nil
}

func (b *BillingResourceService) toResourceDetail(resource *billingTable.BillingResource) (resourceDetail *billingProtocol.ResourceDetail,
	err error) {

	var clusterGroupSerialId string
	if resource.Type == constants.BILLINGRESOURCE_TYPE_CLUSTER {
		clusterGroupSerialId = resource.ResourceId
	} else if resource.Type == constants.BILLINGRESOURCE_TYPE_SETATS {
		_setats, err := service2.GetSetatsBySerialId(resource.ResourceId)
		if err != nil {
			logger.Errorf("Failed to get setats by resource id %s because %+v", resource.ResourceId, err)
			panic(errorcode.InternalErrorCode.ReplaceDesc("failed to find setats"))
		}
		clusterGroupSerialId = _setats.ClusterGroupSerialId
	} else {
		logger.Errorf("query resource type error")
		panic(errorcode.InternalErrorCode.ReplaceDesc("query billing resource type error"))
	}
	clusterGroup, err := service2.ListClusterGroupBySerialId(clusterGroupSerialId)
	if err != nil {
		logger.Errorf("Failed to find ClusterGroup by serial id %s because %+v", resource.ResourceId, err)
		panic(errorcode.NewStackError(errorcode.InternalErrorCode, "failed to find cluster group", nil))
	}
	resourceDetail = &billingProtocol.ResourceDetail{
		AppId:             resource.AppId,
		Uin:               resource.Uin,
		ResourceId:        resource.ResourceId,
		Region:            resource.Region,
		ZoneId:            resource.ZoneId,
		RenewFlag:         int8(resource.AutoRenewFlag),
		Status:            int8(resource.Status),
		IsolatedTimestamp: resource.IsolatedTimestamp,
		CreateTime:        resource.CreateTime,
		ExpireTime:        resource.ExpireTime,
		PayMode:           resource.PayMode,
		GoodsDetail:       nil,
		ClusterName:       clusterGroup.Name,
		Alias:             clusterGroup.Name,
		ClusterID:         resource.ResourceId,
		ComputeUnit:       strconv.Itoa(int(clusterGroup.CuNum)),
	}
	// 后付费设置 calcUnit
	if resource.PayMode == billingProtocol.PayModePost {
		resourceDetail.CalcUnit = billingProtocol.CalcUnit
	}
	resourceDetail.GoodsDetail, err = b.toResourceGoodsDetail(resource)

	return resourceDetail, err
}

func (b *BillingResourceService) toResourceGoodsDetail(resource *billingTable.BillingResource) (*billingProtocol.
	ResourceGoodsDetail, error) {
	detail := &billingProtocol.ResourceGoodsDetail{}
	err := json.Unmarshal([]byte(resource.GoodsDetail), detail)
	if err != nil {
		err = errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	if detail.ResourceId == "" {
		detail.ResourceId = resource.ResourceId
	}
	detail.CurDeadline = resource.ExpireTime
	if resource.BillingResourceMode == billingProtocol.ExclusiveSale && resource.AutoRenewFlag == 1 {
		detail.ResourceOperationMode = "keepRenew"
	}
	detail.BillingResourceMode = resource.BillingResourceMode
	detail.Duration = resource.Duration
	if resource.BillingResourceMode == billingProtocol.ExclusiveSale {
		detail.OceanusExclusiveComputeCu = 0
	}
	if detail.OceanusExclusiveComputeCu12 != 0 {
		detail.OceanusExclusiveComputeCu = 0
	}
	if detail.OceanusExclusiveComputeCu18 != 0 {
		detail.OceanusExclusiveComputeCu = 0
	}
	if detail.OceanusCloudBssd > 0 ||
		detail.OceanusCloudPremium > 0 ||
		detail.OceanusCloudHssd > 0 ||
		detail.OceanusCloudSsd > 0 {
		detail.OceanusExclusiveComputeCu = 0
	}
	if detail.OceanusExclusiveComputeMultipleCu14 != 0 {
		detail.OceanusExclusiveComputeCu = 0
	}
	return detail, err
}

// 兼容 计费 自动续费逻辑
func (b *BillingResourceService) toResourceGoodsDetailNew(resource *billingTable.BillingResource) (*billingProtocol.
	NewResourceGoodsDetail, error) {
	detail := &billingProtocol.NewResourceGoodsDetail{}
	err := json.Unmarshal([]byte(resource.GoodsDetail), detail)
	if err != nil {
		err = errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	if detail.ResourceId == "" {
		detail.ResourceId = resource.ResourceId
	}
	detail.CurDeadline = resource.ExpireTime
	// 管理费用
	if resource.IsNeedManageNode == constants.NEED_MANAGE_NODE {
		detail.ComputeCu = resource.ComputeCu
		if resource.ComputeCu >= billing.NO_MANAGE_CU {
			detail.OceanusExclusiveComputeCu = int32(resource.ComputeCu)
		} else {
			detail.OceanusExclusiveComputeCu = int32(resource.ComputeCu) + billing.MANAGE_CU
		}
	}
	if resource.BillingResourceMode == billingProtocol.ExclusiveSale && resource.AutoRenewFlag == 1 {
		detail.ResourceOperationMode = "keepRenew"
	}
	detail.BillingResourceMode = resource.BillingResourceMode
	detail.Duration = resource.Duration
	if resource.BillingResourceMode == billingProtocol.ExclusiveSale {
		detail.OceanusExclusiveComputeCu = 0
	}
	if detail.OceanusExclusiveComputeCu12 != 0 {
		detail.OceanusExclusiveComputeCu = 0
	}
	if detail.OceanusExclusiveComputeCu18 != 0 {
		detail.OceanusExclusiveComputeCu = 0
	}
	if detail.OceanusExclusiveComputeMultipleCu14 != 0 {
		detail.OceanusExclusiveComputeCu = 0
	}
	return detail, err
}

func (b *BillingResourceService) QueryDeadlineResourceTotalCount(req *billingProtocol.QueryDeadlineListReq) (
	totalCount int, err error) {
	selectSql := "SELECT COUNT(*) AS Count"
	return newBillingResourceDataBase(selectSql, req.Type, req.Region).
		WithAppId(req.AppId).
		WithResourceIds(req.ResourceIds).
		WithAutoRenewFlags(req.AutoRenewFlags).
		WithDeadlineStart(req.DeadlineStart).
		WithDeadlineEnd(req.DeadlineEnd).
		QueryCount()
}

func (b *BillingResourceService) QueryDeadlineResource(req *billingProtocol.QueryDeadlineListReq) (
	instanceSet []*billingProtocol.Instances, err error) {
	selectSql := "SELECT *"
	resources, err := newBillingResourceDataBase(selectSql, req.Type, req.Region).
		WithAppId(req.AppId).
		WithResourceIds(req.ResourceIds).
		WithAutoRenewFlags(req.AutoRenewFlags).
		WithDeadlineStart(req.DeadlineStart).
		WithDeadlineEnd(req.DeadlineEnd).
		WithOrderBy(req.OrderBy).
		WithSortBy(req.SortBy).
		WithLimit(req.PageNo, req.PageSize).
		QueryBillingResource()
	if err != nil {
		return
	}
	return b.toInstanceSet(resources)
}

func (b *BillingResourceService) toInstanceSet(resources []*billingTable.BillingResource) (
	instanceSet []*billingProtocol.
		Instances, err error) {
	instanceSet = make([]*billingProtocol.Instances, 0, len(resources))
	for _, resource := range resources {
		instance, err := b.toInstance(resource)
		if err != nil {
			return nil, err
		}
		instanceSet = append(instanceSet, instance)
	}
	return instanceSet, nil
}

func (b *BillingResourceService) toInstance(resource *billingTable.BillingResource) (instance *billingProtocol.
	Instances, err error) {
	clusterGroupService, err := service2.NewClusterGroupServiceBySerialId(resource.ResourceId)
	if err != nil {
		return nil, err
	}
	instance = &billingProtocol.Instances{
		Uin:               resource.Uin,
		AppId:             resource.AppId,
		ResourceId:        resource.ResourceId,
		Region:            resource.Region,
		ZoneId:            resource.ZoneId,
		AutoRenewFlag:     resource.AutoRenewFlag,
		Type:              resource.SubProductCode,
		Status:            resource.Status,
		ProjectId:         0,
		IsolatedTimestamp: resource.IsolatedTimestamp,
		ExpireTime:        resource.ExpireTime,
		DeadLine:          resource.ExpireTime,
		InstanceName:      clusterGroupService.GetClusterGroup().Name,
		PresentInfo:       nil,
		GoodsDetail:       nil,
	}

	instance.GoodsDetail, err = b.toResourceGoodsDetailNew(resource)
	return instance, err
}
