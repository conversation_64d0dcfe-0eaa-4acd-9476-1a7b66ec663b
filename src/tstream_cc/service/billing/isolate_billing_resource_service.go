package billing

import (
	"errors"
	"fmt"
	"math/rand"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/billing"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"
	billing2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/billing"
	billingTable "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/billing"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	regionService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/region"
	"time"
)

type IsolateBillingResourceService struct {
	eventId      int64
	req          *billing.IsolateResourceReq
	resource     *billingTable.BillingResource
	resourceList []*billingTable.BillingResource
}

func NewIsolateBillingResourceService(req *billing.IsolateResourceReq, eventId int64) *IsolateBillingResourceService {
	return &IsolateBillingResourceService{req: req, eventId: eventId}
}

/**
没有资源就返回空，后付费 隔离所有资源
*/
func (i *IsolateBillingResourceService) IsolateAllResources() (flowId int64, err error) {
	// 获取当前Appid是否触发过隔离
	flowRelations, err := flow.GetFlowRelationByAppId(i.req.AppId, constants.FLOW_OCEANUS_ISOLATE_CLUSTER, i.req.Region)
	if err != nil {
		return 0, err
	}
	if len(flowRelations) > 0 {
		return flowRelations[0].ParentId, nil
	}
	// 获取所有未删除的, 未隔离的, 所有地域
	if err = i.getAllResources(); err != nil {
		return 0, err
	}
	// 没有资源就返回空
	if len(i.resourceList) < 1 {
		return billing.NoResourceFlag, nil
	}
	flowRelationList := make([]*flow.FlowRelation, 0)
	/**
	在一个事务里面处理所有
	*/
	hasIsolateResource := true
	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		for _, resource := range i.resourceList {
			if resource.Status == billing.ResourceStatusIsolated {
				continue
			}
			clusterGroupService, err := service2.NewClusterGroupServiceBySerialId(resource.ResourceId)
			if err != nil {
				return err
			}
			// 不处理非运行的集群，目前处理不了
			if _, err = clusterGroupService.CanIsolate(); err != nil {
				continue
			}
			isEks, _ := service2.IsEks(clusterGroupService.GetClusterGroup().Id)
			clusterType := constants.K8S_CLUSTER_TYPE_TKE
			if isEks {
				clusterType = constants.K8S_CLUSTER_TYPE_EKS
			}

			if err = clusterGroupService.MarkIsolated(tx); err != nil {
				return err
			}
			cluster, err := clusterGroupService.GetActiveCluster()
			if err != nil {
				return err
			}
			docId := fmt.Sprintf("%s@%d", resource.ResourceId, i.eventId)
			flowId, err = flow.CreateFlow(constants.FLOW_OCEANUS_ISOLATE_CLUSTER, docId,
				0, map[string]string{
					constants.FLOW_PARAM_REQUEST_ID:       fmt.Sprintf("%d", i.eventId),
					constants.FLOW_PARAM_CLUSTER_GROUP_ID: fmt.Sprintf("%d", clusterGroupService.GetClusterGroup().Id),
					constants.FLOW_PARAM_CLUSTER_TYPE:     fmt.Sprintf("%d", clusterType),
					constants.FLOW_PARAM_CLUSTER_ID:       fmt.Sprintf("%d", cluster.Id),
				}, nil)
			if err != nil {
				return err
			}
			resource.Status = billing.ResourceStatusIsolated
			resource.FlowId = flowId
			resource.IsolatedTimestamp = time.Now().Format(billing.StandardTimestampFormatString)
			tx.UpdateObject(resource, resource.Id, "BillingResource")

			flowRelationList = append(flowRelationList, &flow.FlowRelation{
				ChildId:     flowId,
				Processname: constants.FLOW_OCEANUS_ISOLATE_CLUSTER,
				ResourceId:  resource.ResourceId,
				AppId:       i.req.AppId,
				Region:      i.req.Region,
			})
		}
		// 全都是隔离不了的集群
		if len(flowRelationList) < 1 {
			hasIsolateResource = false
			return nil
		}
		flowId, err = flow.CreateFlowRelation(constants.FLOW_OCEANUS_ISOLATE_CLUSTER, constants.FLOW_OCEANUS_ISOLATE_CLUSTER, 0, flowRelationList)
		if err != nil {
			return err
		}
		return nil
	}).Close()
	// 没有需要隔离的集群
	if !hasIsolateResource {
		return billing.NoResourceFlag, nil
	}
	return flowId, nil
}

func (i *IsolateBillingResourceService) Isolate() (flowId int64, err error) {
	if err = i.getResource(); err != nil {
		return 0, err
	}

	if i.resource.Status == billing.ResourceStatusIsolated {
		return i.resource.FlowId, nil
	}

	if i.resource.Status != billing.ResourceStatusNormal {
		msg := fmt.Sprintf("resource %s status %d not allow to destroy", i.resource.ResourceId, i.resource.Status)
		return 0, errorcode.NewStackError(errorcode.ResourceUnavailableCode, msg, nil)
	}
	return i.isolateResource()
}

func (i *IsolateBillingResourceService) getAllResources() error {
	selectSql := "SELECT *"
	resourceSet, err := newBillingResourceDataBase(selectSql, *i.req.SubProductCode, i.req.Region).
		WithAppId(i.req.AppId).
		WithPayMode(billing.PayModePost).
		QueryBillingResource()
	if err != nil {
		return err
	}
	// 无需要隔离的资源, 返回成功
	if len(resourceSet) == 0 {
		return nil
	}
	i.resourceList = resourceSet
	return nil
}

func (i *IsolateBillingResourceService) getResource() error {
	selectSql := "SELECT *"
	resourceSet, err := newBillingResourceDataBase(selectSql, *i.req.SubProductCode, i.req.Region).
		WithAppId(i.req.AppId).
		WithResourceIds([]string{i.req.ResourceId}).
		QueryBillingResource()
	if err != nil {
		return err
	}
	if len(resourceSet) == 0 {
		return errorcode.NewStackError(errorcode.ResourceNotFoundCode, i.req.ResourceId, nil)
	}
	i.resource = resourceSet[0]
	return nil
}

func deleteSetats(setatsSerialId string, isolateResourceReq *billing.IsolateResourceReq, eventId int64) (err error) {
	logger.Infof("isolate resource %s deleteSetats with setatsSerialId %s, isolateResourceReq %+v, eventId %d",
		isolateResourceReq.ResourceId, setatsSerialId, isolateResourceReq, eventId)
	isDev := service.GetConfStringValue("scsDevEnv")
	var environment int8 = 2
	if isDev == "true" {
		environment = 1
	}
	code, msg, rsp := DoBillingRefundPurchase(&billing2.PurchaseReq{
		RequestBase: apiv3.RequestBase{
			AppId:         isolateResourceReq.AppId,
			Uin:           isolateResourceReq.Uin,
			SubAccountUin: isolateResourceReq.OperateUin,
		},
		RegionId:       int32(isolateResourceReq.Region),
		ResourceIdList: []string{setatsSerialId},
		EventId:        int32(eventId),
		IsSetats:       1,
		Environment:    environment,
	})
	if code != controller.OK {
		logger.Errorf("deleteSetats %s with setatsSerialId %s failed, code is %s, msg is %s", isolateResourceReq.ResourceId, setatsSerialId, code, msg)
		return errors.New(msg)
	}
	logger.Infof("deleteSetats %s with setatsSerialId %s success, rsp is %+v", isolateResourceReq.ResourceId, setatsSerialId, rsp)
	return nil
}

func deleteSubEksCluster(group *table.ClusterGroup) (err error) {
	// 是否有sub eks cluster
	count, subEksClusterGroup, err := service2.ListClusterGroupByParentSerialId(group.SerialId)
	logger.Infof("ListClusterGroupByParentSerialId with %s count is %d, err is %+v ", group.SerialId, count, err)
	if count == 0 {
		return
	}
	regionId, err := regionService.GetRegionIdByName(subEksClusterGroup.Region)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to get Region id by Name: %s, with errors:%+v", subEksClusterGroup.Region, err)
		logger.Error(errMsg)
		return
	}
	modifyResourceStatusRequest := &billing.ModifyResourceStatusRequest{
		AppId:         int64(subEksClusterGroup.AppId),
		Uin:           subEksClusterGroup.OwnerUin,
		SubAccountUin: group.CreatorUin,
		Region:        int8(regionId),
		ResourceId:    subEksClusterGroup.SerialId,
		PayMode:       billing.PayModePost,
	}

	s := NewModifyResourceStatusService(modifyResourceStatusRequest, int64(rand.Intn(********)))
	err = s.ModifyResourceStatus()
	if err != nil {
		logger.Errorf("deleteSubEksCluster %s with parent cluster %s failed, err msg is %+v", subEksClusterGroup.SerialId, group.SerialId, err)
		return
	}
	return
}

func (i *IsolateBillingResourceService) isolateResource() (flowId int64, err error) {

	isCluster := 1
	if i.resource.Type == constants.BILLINGRESOURCE_TYPE_SETATS {
		isCluster = 0
	}
	var clusterGroupSerialId string
	if isCluster == 1 {
		clusterGroupSerialId = i.req.ResourceId
	} else {
		_setats, err := service2.GetSetatsBySerialId(i.req.ResourceId)
		if err != nil {
			logger.Errorf("Failed to get setats by resource id %s because %+v", i.req.ResourceId, err)
			panic(err)
		}
		clusterGroupSerialId = _setats.ClusterGroupSerialId
	}

	clusterGroupService, err := service2.NewClusterGroupServiceBySerialId(clusterGroupSerialId)
	if err != nil {
		return
	}

	if isCluster == 1 {
		if _, err = clusterGroupService.CanIsolate(); err != nil {
			return
		}
		// 发起删除子sub eks集群
		if clusterGroupService.GetClusterGroup().Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && clusterGroupService.GetClusterGroup().AgentSerialId != "" {
			err = deleteSubEksCluster(clusterGroupService.GetClusterGroup())
			if err != nil {
				return
			}
		}
		// 查询是否有setats
		count, _setats, err := service2.GetSetatsByClusterGroupSerialId(i.req.ResourceId)
		if err != nil {
			logger.Errorf("Failed to get setats by resource id %s because %+v", i.req.ResourceId, err)
			panic(err)
		}
		if count > 0 {
			if _setats.Status != constants.SETATS_ISOLATED && _setats.Status != constants.SETATS_DELETING {
				err = deleteSetats(_setats.SerialId, i.req, i.eventId)
				if err != nil {
					logger.Errorf("isolate cluster %s deleteSetats %s failed, err msg is %+v", clusterGroupService.GetClusterGroup().SerialId, _setats.SerialId, err)
					panic(err)
				}
			}
		}
	}

	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		if isCluster == 1 {
			if err = clusterGroupService.MarkIsolated(tx); err != nil {
				logger.Errorf("MarkIsolated %s failed, err msg is %+v", clusterGroupSerialId, err)
				return err
			}
		} else {
			if err = service2.SwitchSetatasStatusTo(clusterGroupSerialId, constants.SETATS_ISOLATED); err != nil {
				logger.Errorf("SwitchSetatasStatusTo %s to %s failed, err msg is %+v", clusterGroupSerialId, constants.SETATS_ISOLATED, err)
				return err
			}
		}

		isEks, _ := service2.IsEks(clusterGroupService.GetClusterGroup().Id)
		clusterType := constants.K8S_CLUSTER_TYPE_TKE
		if isEks {
			clusterType = constants.K8S_CLUSTER_TYPE_EKS
		}

		cluster, err := clusterGroupService.GetActiveCluster()
		if err != nil {
			msg := fmt.Sprintf("isolateResource -> GetActiveCluster error:%+v", err)
			logger.Error(msg)
			return err
		}

		docId := fmt.Sprintf("%s@%d", i.req.ResourceId, i.eventId)
		flowId, err = flow.CreateFlow(constants.FLOW_OCEANUS_ISOLATE_CLUSTER, docId,
			0, map[string]string{
				constants.FLOW_PARAM_REQUEST_ID:        fmt.Sprintf("%d", i.eventId),
				constants.FLOW_PARAM_CLUSTER_GROUP_ID:  fmt.Sprintf("%d", clusterGroupService.GetClusterGroup().Id),
				constants.FLOW_PARAM_CLUSTER_TYPE:      fmt.Sprintf("%d", clusterType),
				constants.FLOW_PARAM_CLUSTER_ID:        fmt.Sprintf("%d", cluster.Id),
				constants.FLOW_PARAM_SETATS_IS_CLUSTER: fmt.Sprintf("%d", isCluster),
			}, nil)

		i.resource.Status = billing.ResourceStatusIsolated
		i.resource.FlowId = flowId
		if i.req.RenewFlag != nil {
			i.resource.AutoRenewFlag = *i.req.RenewFlag
		}
		if i.req.NewDeadline != nil {
			i.resource.ExpireTime = *i.req.NewDeadline
		}
		i.resource.IsolatedTimestamp = time.Now().Format(billing.StandardTimestampFormatString)
		tx.UpdateObject(i.resource, i.resource.Id, "BillingResource")
		return nil
	}).Close()
	return
}
