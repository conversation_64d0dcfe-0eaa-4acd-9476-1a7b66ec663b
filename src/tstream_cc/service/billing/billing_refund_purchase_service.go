package billing

import (
	"encoding/json"
	"strconv"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	service1 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/billing"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/configuration_center"
	"time"
)

func DoBillingRefundPurchase(req *billing.PurchaseReq) (string, string, *billing.PurchaseRsp) {
	logger.Infof("DoBillingRefundPurchase API called by AppId %d", req.AppId)

	if req.IsSetats != 1 {
		err := checkResource(req.AppId, req.ResourceId, req.ResourceIdList...)
		if err != nil {
			return controller.LimitExceeded, "check resource error", nil
		}
	}
	res, _ := json.Marshal(ConstructRefundPuchaseReq(req))

	return PostToBilling(req.Environment, res)
}

func ConstructRefundPuchaseReq(req *billing.PurchaseReq) *billing.RefundPurchase {
	res := &billing.RefundPurchase{}
	res.Version = 1
	res.ComponentName = "qcbuy"
	res.User = "auto"
	res.EventId = req.EventId
	res.Timestamp = time.Now().UnixNano()

	interf := billing.Interfacer{}
	interf.InterfaceName = "qcloud.Deal.OnlineRefund"

	para := billing.Parar{}
	para.AppId = req.AppId
	para.Uin = req.SubAccountUin
	para.OwnerUin = req.Uin

	isDev := service1.GetConfStringValue("scsDevEnv")
	if isDev == "true" {
		// 测试环境设置 真实uin
		scsDevEnvNewCamUin, _ := service.GetConfigurationValue("scsDevEnvNewCamUin", "************")
		if scsDevEnvNewCamUin != "" {
			para.Uin = scsDevEnvNewCamUin
			para.OwnerUin = scsDevEnvNewCamUin
		}
		logger.Infof("ConstructRefundPuchaseReq uin is %s", para.Uin)
		scsDevEnvNewCamAppId, _ := service.GetConfigurationValue("scsDevEnvNewCamAppId", "*********")
		para.AppId, _ = strconv.ParseInt(scsDevEnvNewCamAppId, 10, 64)
		logger.Infof("ConstructRefundPuchaseReq appid is %d", para.AppId)
	}

	goodsInfo := billing.GoodsInfo{}
	oceanus := billing.Sp_oceanus_exclusive{}
	oceanus.PayMode = 1
	oceanus.DealName = req.DealName
	oceanus.RegionId = req.RegionId
	oceanus.ResourceIdList = req.ResourceIdList

	goodsInfo.Sp_oceanus_exclusive = &oceanus
	para.GoodsInfo = &goodsInfo
	interf.Para = &para
	res.Interface = &interf

	return res
}
