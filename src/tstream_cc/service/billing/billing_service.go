package billing

import (
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/billing"
	billing2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/billing"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/quota"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/region"
	roleAuthService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/role_auth"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
	"time"
)

var tableService = service3.TableService{}

/*
*

	根据 DealName 查找 BillingOrder 记录
*/
func GetBillingOrderByDealName(dealName string) (*billing2.BillingOrder, error) {
	// SELECT 一下，如果存在直接返回 如果不存在则返回 nil
	sql := "SELECT * FROM BillingOrder WHERE DealName=?"
	args := make([]interface{}, 0, 0)
	args = append(args, dealName)
	cnt, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("GetBillingOrderByDealName failed. SQL:%s, args:%+v, error:%+v", sql, args, err)
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "Failed to GetBillingOrderByDealName", err)
	}
	if cnt == 1 {
		billingOrder := &billing2.BillingOrder{}
		err = util.ScanMapIntoStruct(billingOrder, data[0])
		if err != nil {
			logger.Errorf("Failed to ScanMapIntoStruct because %+v", err)
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "Failed to ScanMapIntoStruct", err)
		} else {
			return billingOrder, nil
		}
	} else if cnt != 0 { // 返回条数不是 1, 不是 0, 而是其他条数, 不应该出现这样的问题
		logger.Infof("Expected %d but only %d returned", 1, cnt)
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "billing order count is not expected", nil)
	} else {
		return nil, nil // 没找到, 但也没有报错
	}
}

func GetBillingOrderByResourceId(resourceId string, regionId int) (*billing2.BillingOrder, error) {
	// SELECT 一下，如果存在直接返回 如果不存在则返回 nil
	sql := "SELECT * FROM BillingOrder WHERE ResourceId=? AND Region=? AND Status=? ORDER BY CreateTime DESC"
	args := make([]interface{}, 0, 0)
	args = append(args, resourceId)
	args = append(args, regionId)
	args = append(args, billing.OrderStatusPending)
	cnt, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("GetBillingOrderByResourceId failed. SQL:%s, args:%+v, error:%+v", sql, args, err)
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "Failed to GetBillingOrderByResourceId", err)
	}
	if cnt >= 1 {
		billingOrder := &billing2.BillingOrder{}
		err = util.ScanMapIntoStruct(billingOrder, data[0])
		if err != nil {
			logger.Errorf("Failed to ScanMapIntoStruct because %+v", err)
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "Failed to ScanMapIntoStruct", err)
		} else {
			return billingOrder, nil
		}
	} else { // 返回条数是 0, 不应该出现这样的问题
		logger.Infof("Expected %s but only %d returned", ">0", cnt)
		return nil, errorcode.NewStackError(errorcode.ResourceNotFoundCode, "billing order count is not expected", nil)
	}
}

// 检查基本参数, 多个 CheckXXX 计费接口共享
func CheckNecessaryArguments(regionId int, zoneId int, appId int32, payMode int8, operateUin string, uin string, clusterGroupType int8) (int64, string, int) {

	/**
	 * 只有管理员才能创建集群, 创建弹性子集群不校验
	 */
	if operateUin != uin && clusterGroupType != constants.CLUSTER_GROUP_TYPE_SUB_EKS {
		admin, err := roleAuthService.RoleAuthoAdministorCheck(int64(appId), operateUin)
		if err != nil || admin == nil || admin.Status != constants.ROLE_AUTHO_STATUS_USEABLE {
			logger.Errorf("### only admin can operate，RoleAuthoAdministorCheck fail with AppId(%d), OperateUin(%s) because %+v", appId, operateUin, err)
			return constants.SYSERR, "only admin can operate.", billing.StatusSystemError
		}
	}

	// 1 检查 Region 和 Zone 是否合法
	regionName, err := region.GetRegionNameById(regionId)
	if err != nil {
		logger.Errorf("Failed to get region name by id from database because %+v", err)
		return constants.SYSERR, "Failed to get region name by id", billing.StatusSystemError
	}
	zoneName, err := region.GetZoneById(zoneId)
	if err != nil {
		logger.Errorf("Failed to get zone name by id from database because %+v", err)
		return constants.SYSERR, "Failed to get zone name by id", billing.StatusSystemError
	}
	clusterType := constants.K8S_CLUSTER_TYPE_TKE
	if payMode == billing.PayModePost {
		clusterType = constants.K8S_CLUSTER_TYPE_EKS
	}
	err = region.ValidateRegionZones(appId, []string{regionName}, constants.NETWORK_ENV_CLOUD_VPC, zoneName, clusterType)
	if err != nil {
		logger.Errorf("Failed to invoke ValidateRegionZones, with error: %+v", err)
		return constants.SYSERR, "Failed to check whether region zones are valid or not", billing.StatusSystemError
	}
	// Galileo 的 ConfigurationCenter 配置了地域黑名单（有些地域不允许自动创建集群）
	inBlackList, err := tableService.InClusterAutoCreateBlackRegionList(regionName)
	if err != nil {
		logger.Errorf("Failed to invoke InClusterAutoCreateBlackRegionList, with error: %+v", err)
		return constants.SYSERR, "Failed to check whether region zones are valid or not", billing.StatusSystemError
	}
	if inBlackList {
		logger.Errorf("Region %s is in blacklist, not allowed to buy", regionName)
		return constants.SYSERR, "Region in blacklist", billing.StatusRegionInBlacklist
	}

	// 3 ==== 付费模式检查（必须为预付费 1）====
	// PayMode  付费模式，0 表示按需计费/后付费，1 表示预付费
	if payMode != billing.PayModePrepaid && payMode != billing.PayModePost {
		logger.Errorf("Unsupported PayMode %d, should be %d or %d only", payMode, billing.PayModePrepaid, billing.PayModePost)
		return constants.PARAMSERR, "Unsupported pay mode", billing.StatusUnsupportedPayMode
	}

	return constants.SUCCESS, "", billing.StatusSuccess
}

func CheckManageCu(oceanusExclusiveComputeCu int, computeCu int) (int64, string, int) {
	if oceanusExclusiveComputeCu != computeCu {
		// 如果计算如果大于等于 48CU，就是不合法的
		if computeCu >= billing.NO_MANAGE_CU {
			errMsg := fmt.Sprintf("computeCu is (%d) >= 48CU, oceanusExclusiveComputeCu(%d) must equal with it",
				computeCu, oceanusExclusiveComputeCu)
			logger.Errorf(errMsg)
			return constants.PARAMSERR, errMsg, billing.StatusInvalidCuNum
		}
		// 如果 传给计费的CU数 不等于 计算CU + 2CU 的管理CU，不合法
		if oceanusExclusiveComputeCu != (computeCu + billing.MANAGE_CU) {
			errMsg := fmt.Sprintf("oceanusExclusiveComputeCu(%d) not equal Compute unit(%d) + 2 CU",
				oceanusExclusiveComputeCu, computeCu)
			logger.Errorf(errMsg)
			return constants.PARAMSERR, errMsg, billing.StatusInvalidCuNum
		}
	} else {
		// 如果相等， computeCu 必须 >= 48CU
		if computeCu < billing.NO_MANAGE_CU {
			errMsg := fmt.Sprintf("computeCu is (%d) < 48CU, so the oceanusExclusiveComputeCu(%d) must be %d + 2",
				computeCu, oceanusExclusiveComputeCu, computeCu)
			logger.Errorf(errMsg)
			return constants.PARAMSERR, errMsg, billing.StatusInvalidCuNum
		}
	}
	return constants.SUCCESS, "", billing.StatusSuccess
}

// 检查共有的 GoodsDetail 参数
func CheckNecessaryGoodsDetail(
	appId int32,
	timeUnit string,
	timeSpan int,
	goodsNum int,
	pid int,
	autoRenewFlag int,
	productCode string,
	subProductCode string,
	cuNum int,
	payMode int8,
	isCluster bool,
) (int64, string, int) {

	// 后付费校验
	if payMode == billing.PayModePost {

		if cuNum != 1 {
			logger.Errorf("Failed to check CuNum because sv_oceanus_compute_exclusive_cu is not equal %d", 1)
			return constants.PARAMSERR, "询价计费项必须等于1", billing.StatusInvalidCuNum
		}

		if timeUnit != billing.TimeUnitSecond {
			logger.Errorf("Invalid time unit %s, only `s` is supported", timeUnit)
			return constants.PARAMSERR, "Unsupported time unit, only `s` is supported", billing.StatusUnsupportedTimeUnit
		}

		/**
		 * 后付费 7200 用于冻结
		 */
		if timeSpan != 7200 {
			logger.Errorf("Invalid time span %d. Should be 7200", timeSpan)
			return constants.PARAMSERR, "Invalid time span, Should be 7200", billing.StatusInvalidTimeSpan
		}
	}

	// 预计付费校验
	if payMode == billing.PayModePrepaid {
		// 1 TimeUnit 必须是 m（月份）
		if timeUnit != billing.TimeUnitMonth && timeUnit != billing.TimeUnitDay && timeUnit != billing.TimeUnitYear {
			logger.Errorf("Invalid time unit %s, only `m` or `d` is supported", timeUnit)
			return constants.PARAMSERR, "Unsupported time unit", billing.StatusUnsupportedTimeUnit
		}

		// 2 TimeSpan 校验，必须是整数 [1, 36] 个月, 或最长 1080 天
		if timeSpan < 1 ||
			(timeUnit == billing.TimeUnitMonth && timeSpan > billing.MaximumAllowedTimeSpanMonth) ||
			(timeUnit == billing.TimeUnitDay && timeSpan > billing.MaximumAllowedTimeSpanMonth*30) {
			logger.Errorf("Invalid time span %d. Should be 1 ~ %d months", timeSpan, billing.MaximumAllowedTimeSpanMonth)
			return constants.PARAMSERR, "Invalid time span", billing.StatusInvalidTimeSpan
		}
		if isCluster {
			// 8 检查 CU 个数设置（大于等于 12, 小于等于 300 即可）
			if cuNum < billing.MinimalAllowedComputeUnit {
				logger.Errorf("Failed to check CuNum because CU number is less than %d", billing.MinimalAllowedComputeUnit)
				return constants.PARAMSERR, "Invalid CU number (too small)", billing.StatusInvalidCuNum
			}

			overLimit, msg, err := quota.NewQuota().OverLimit("", int64(appId), quota.ClusterCu, int32(cuNum))
			if err != nil {
				logger.Errorf("Failed to get ClusterCU limit when invoking quota.NewQuota().OverLimit, "+
					"error: %+v", err)
				return constants.SYSERR, "error while getting CU limit", billing.StatusSystemError
			}
			if overLimit {
				logger.Errorf("CU number provided is larger than allowed upper CU limit for AppID %d: %s", appId, msg)
				return constants.PARAMSERR, "Invalid CU number (too large)", billing.StatusInvalidCuNum
			}
		}
		// 3 GoodsNum 单次最多购买多少个集群
		if goodsNum != 1 {
			logger.Errorf("Only 1 cluster is allowed to be bought each time")
			return constants.PARAMSERR, "Only 1 cluster is allowed each time", billing.StatusOnlyOneClusterIsAllowed
		}
	}

	// 4 Pid
	if pid != billing.OceanusExclusivePid {
		logger.Errorf("Invalid pid %d. Should be %d", pid, billing.OceanusExclusivePid)
		return constants.PARAMSERR, "Invalid pid", billing.StatusInvalidPid
	}

	// 5 AutoRenewFlag 续费方式：0（默认）正常续费续费 有超级预付费不停服特权会自动续费；1 自动续费；2 不管有什么特权都不会自动续费
	if autoRenewFlag < 0 || autoRenewFlag > 2 {
		logger.Errorf("Invalid AutoRenewFlag %d. Should be 0, 1, 2", autoRenewFlag)
		return constants.PARAMSERR, "Invalid AutoRenewFlag", billing.StatusInvalidOtherParams
	}

	// 6 ProductCode 固定值 p_oceanus
	if productCode != billing.OceanusProductCode {
		logger.Errorf("Invalid ProductCode %s. Should be %s", productCode, billing.OceanusProductCode)
		return constants.PARAMSERR, "Invalid ProductCode", billing.StatusInvalidOtherParams
	}

	// 7 SubProductCode 独享集群固定值 sp_oceanus_exclusive
	if subProductCode != billing.OceanusExclusiveSubProductCode {
		logger.Errorf("Invalid SubProductCode %s. Should be %s",
			subProductCode, billing.OceanusExclusiveSubProductCode)
		return constants.PARAMSERR, "Invalid SubProductCode", billing.StatusInvalidOtherParams
	}

	return constants.SUCCESS, "", billing.StatusSuccess
}

// 计算资源到期时间（增加 TimeSpan 指定的月数)
func CalculateExpireTime(fromTime time.Time, timeUnit string, timeSpan int) time.Time {
	if timeUnit != billing.TimeUnitMonth && timeUnit != billing.TimeUnitDay && timeUnit != billing.TimeUnitYear {
		panic("Invalid TimeUnit, should be `m` or `d` or `y` only")
	}

	if timeUnit == billing.TimeUnitMonth {
		return fromTime.AddDate(0, timeSpan, 0)
	} else if timeUnit == billing.TimeUnitDay {
		return fromTime.AddDate(0, 0, timeSpan)
	} else if timeUnit == billing.TimeUnitYear {
		return fromTime.AddDate(timeSpan, 0, 0)
	} else {
		panic(errorcode.NewStackError(errorcode.InternalErrorCode, "Unsupported TimeUnit", nil))
	}
}

func checkResource(appId int64, resourceId string, resourceIds ...string) (err error) {

	resources := make([]string, 0)

	if len(resourceId) > 0 {
		resources = append(resources, resourceId)
	}
	for _, ri := range resourceIds {
		resources = append(resources, ri)
	}

	for _, ri := range resources {
		if err = tableService.CheckClusterGroupExist(appId, ri); err != nil {
			return
		}
	}
	return nil
}
