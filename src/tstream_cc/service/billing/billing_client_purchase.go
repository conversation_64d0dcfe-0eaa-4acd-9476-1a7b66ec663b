package billing

import (
	"encoding/json"
	"io/ioutil"
	"net/http"
	"strings"

	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/billing"
)

func PostToBilling(env int8, res []byte) (string, string, *billing.PurchaseRsp) {
	url := ""
	if env == 1 /*测试环境*/ {
		url = "http://trade.itd.com/interfaces/interface.php"
		logger.Infof("（测试环境）")
	} else if env == 2 /*线上环境*/ {
		url = "http://trade.tencentyun.com/interfaces/interface.php"
		logger.Infof("（线上环境）")
	}
	resp, _ := http.NewRequest("POST", url, strings.NewReader(string(res)))
	logger.Infof("PostToBilling " + string(res) + " to " + url)

	resp.Header.Set("Host", "trade.itd.com")
	resp.Header.Set("content-type", "application/x-www-form-urlencoded")
	response, err := (&http.Client{}).Do(resp)
	if err != nil {
		logger.Errorf("POST connect fail")
		return controller.LimitExceeded, "POST connect fail", nil
	}
	defer response.Body.Close()
	body, _ := ioutil.ReadAll(response.Body)
	if len(body) != 0 {
		logger.Errorf("repsonse body : %s", string(body))
	}
	rsp := &billing.PurchaseRsp{}
	er := json.Unmarshal(body, rsp)
	if er != nil {
		logger.Errorf("response body json.Unmarshal fail")
		return controller.LimitExceeded, "Possible parameter input error", nil
	}
	if rsp.ReturnCode != 0 {
		logger.Errorf("fail")
		failrsp := &billing.PurchaseRspFail{}
		json.Unmarshal(body, &failrsp)
		msg := "purchase fail " + " SeqId " + failrsp.SeqId + " ErrorCode " + failrsp.ReturnMessage
		return controller.LimitExceeded, msg, nil
	}
	return controller.OK, controller.NULL, rsp
}
