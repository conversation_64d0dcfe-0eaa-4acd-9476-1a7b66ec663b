package billing

import (
	"encoding/json"
	"fmt"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/billing"
	"time"
)

func DoBillingModifyPurchase(req *billing.PurchaseReq) (string, string, *billing.PurchaseRsp) {
	logger.Infof("DoBillingModifyPurchase API called by AppId %d", req.AppId)

	err := checkResource(req.AppId, req.ResourceId, req.ResourceIdList...)
	if err != nil {
		return controller.LimitExceeded, "check resource error", nil
	}

	res, _ := json.Marshal(ConstructModifyPuchaseReq(req))

	return PostToBilling(req.Environment, res)
}

func ConstructModifyPuchaseReq(req *billing.PurchaseReq) *billing.ModifyPurchaseReq {
	res := &billing.ModifyPurchaseReq{}
	res.Version = 1
	res.ComponentName = "qcbuy"
	res.User = "auto"
	res.EventId = req.EventId
	res.Timestamp = time.Now().UnixNano()

	interf := billing.Interfacem{}
	interf.InterfaceName = "qcloud.Deal.generateDealsAndPay"

	para := billing.Param{}
	para.AppId = req.AppId
	para.Uin = req.SubAccountUin
	para.OwnerUin = req.Uin

	goods := make([]*billing.Goodsm, 0, 0)
	good := &billing.Goodsm{}
	good.GoodsCategoryId = 102115 //Oceanus 变配
	good.GoodsNum = req.GoodsNum
	good.PayMode = 1             //预付模式
	good.RegionId = req.RegionId //地区
	good.ZoneId = req.ZoneId
	good.ProjectId = 0

	detail := billing.GoodsDetailm{}
	newConfig := billing.ConfigDetail{}
	newConfig.ProductCode = "p_oceanus"
	newConfig.SubProductCode = "sp_oceanus_exclusive"
	newConfig.Pid = 1000510 //Oceanus 计费页面的pid
	newConfig.Sv_oceanus_compute_exclusive_cu = req.NewCU
	newConfig.ModifyMode = req.ModifyMode
	newConfig.TimeUnit = req.TimeUnit
	detail.NewConfig = &newConfig

	oldConfig := billing.ConfigDetail{}
	oldConfig.ProductCode = "p_oceanus"
	oldConfig.SubProductCode = "sp_oceanus_exclusive"
	oldConfig.Pid = 1000510 //Oceanus 计费页面的pid
	oldConfig.Sv_oceanus_compute_exclusive_cu = req.OldCU
	oldConfig.ModifyMode = req.ModifyMode
	oldConfig.TimeUnit = req.TimeUnit
	detail.OldConfig = &oldConfig

	productInfos := make([]*billing.ProductInfo, 0, 0)
	pro := billing.ProductInfo{}
	pro.Name = "计算 CU 数"
	pro.Value = fmt.Sprintf("%v", req.NewCU)
	productInfos = append(productInfos, &pro)

	detail.CurDeadline = req.CurDeadline //资源id到期时间
	detail.ResourceId = req.ResourceId   //资源id

	detail.ProductInfo = productInfos
	good.GoodsDetail = &detail
	goods = append(goods, good)
	para.Goods = goods
	interf.Para = &para
	res.Interface = &interf

	return res
}
