package billing

import (
	"strconv"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	billing2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/billing"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/billing"
)

type billingResourceDataBase struct {
	selectSql      string
	subProductCode string
	region         int
	appId          *int64
	pageNo         *int
	pageSize       *int
	payMode        *int

	autoRenewFlags []int8
	deadlineStart  *string // 到期起始时间（yyyy // -MM-dd hh:mm:ss)
	deadlineEnd    *string // 到期结束时间（yyyy-MM-dd hh:mm:ss）
	orderBy        *string
	sortBy         *string

	resourceIds []string

	includeDestroyStatus bool

	international *int

	where string
	args  []interface{}
}

func newBillingResourceDataBase(selectSql, subProductCode string, region int) *billingResourceDataBase {
	return &billingResourceDataBase{
		selectSql:            selectSql,
		subProductCode:       subProductCode,
		region:               region,
		includeDestroyStatus: false,
	}
}

func (b *billingResourceDataBase) WithDestroyStatus(includeDestroyStatus bool) *billingResourceDataBase {
	b.includeDestroyStatus = includeDestroyStatus
	return b
}

func (b *billingResourceDataBase) WithInternational(intl int) *billingResourceDataBase {
	b.international = &intl
	return b
}

func (b *billingResourceDataBase) WithPayMode(payMode int) *billingResourceDataBase {
	b.payMode = &payMode
	return b
}

func (b *billingResourceDataBase) WithAppId(appId int64) *billingResourceDataBase {
	b.appId = &appId
	return b
}

func (b *billingResourceDataBase) WithResourceIds(resourceIds []string) *billingResourceDataBase {
	b.resourceIds = resourceIds
	return b
}

func (b *billingResourceDataBase) WithLimit(pageNo, pageSize *int) *billingResourceDataBase {
	b.pageNo = pageNo
	b.pageSize = pageSize
	return b
}

func (b *billingResourceDataBase) WithAutoRenewFlags(autoRenewFlags []int8) *billingResourceDataBase {
	b.autoRenewFlags = autoRenewFlags
	return b
}

func (b *billingResourceDataBase) WithDeadlineStart(deadlineStart *string) *billingResourceDataBase {
	b.deadlineStart = deadlineStart
	return b
}

func (b *billingResourceDataBase) WithDeadlineEnd(deadlineEnd *string) *billingResourceDataBase {
	b.deadlineEnd = deadlineEnd
	return b
}

func (b *billingResourceDataBase) WithOrderBy(orderBy *string) *billingResourceDataBase {
	b.orderBy = orderBy
	return b
}

func (b *billingResourceDataBase) WithSortBy(sortBy *string) *billingResourceDataBase {
	b.sortBy = sortBy
	return b
}

func (b *billingResourceDataBase) QueryCount() (count int, err error) {
	sql, args := b.Build()
	_, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return 0, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	v, _ := data[0]["Count"]
	count, err = strconv.Atoi(string(v))
	if err != nil {
		return 0, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	return
}

func (b *billingResourceDataBase) QueryBillingResource() (resources []*billing.BillingResource, err error) {
	sql, args := b.Build()
	_, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	resources = make([]*billing.BillingResource, 0, len(data))
	for _, d := range data {
		resource := &billing.BillingResource{}
		err = util.ScanMapIntoStruct(resource, d)
		if err != nil {
			return nil, errorcode.NewStackError(errorcode.InternalErrorCode_UnMarshalFailed, "", err)
		}
		resources = append(resources, resource)
	}
	return
}

func (b *billingResourceDataBase) Build() (sql string, args []interface{}) {
	b.args = make([]interface{}, 0)
	b.buildCommonWhere()
	b.buildOptionalField()
	b.buildLimit()
	return b.selectSql + " FROM BillingResource " + b.where, b.args
}

func (b *billingResourceDataBase) buildOptionalField() {
	b.buildAppId()
	b.buildPayMode()
	b.buildResourceIds()
	b.buildAutoRenewFlags()
	b.buildDeadlineStart()
	b.buildDeadlineEnd()
	b.buildOrderBy()
	b.buildInternational()
}

func (b *billingResourceDataBase) buildInternational() {
	if b.international != nil {
		b.where += " AND International = ?"
		b.args = append(b.args, *b.international)
	}
}

func (b *billingResourceDataBase) buildAppId() {
	if b.appId != nil {
		b.where += " AND AppId = ?"
		b.args = append(b.args, *b.appId)
	}
}

func (b *billingResourceDataBase) buildPayMode() {
	if b.payMode != nil {
		b.where += " AND PayMode = ?"
		b.args = append(b.args, *b.payMode)
	}
}

func (b *billingResourceDataBase) buildResourceIds() {
	if len(b.resourceIds) == 0 {
		return
	}

	b.where += " AND ResourceId " + b.genInPlaceHolder(len(b.resourceIds))
	for _, resourceId := range b.resourceIds {
		b.args = append(b.args, resourceId)
	}
}

func (b *billingResourceDataBase) buildAutoRenewFlags() {
	if len(b.autoRenewFlags) == 0 {
		return
	}

	b.where += " AND AutoRenewFlag " + b.genInPlaceHolder(len(b.autoRenewFlags))
	for _, autoRenewFlag := range b.autoRenewFlags {
		b.args = append(b.args, autoRenewFlag)
	}
}

func (b *billingResourceDataBase) buildDeadlineStart() {
	if b.deadlineStart == nil {
		return
	}

	b.where += " AND ExpireTime >= ?"
	b.args = append(b.args, *b.deadlineStart)
}

func (b *billingResourceDataBase) buildDeadlineEnd() {
	if b.deadlineEnd == nil {
		return
	}

	b.where += " AND ExpireTime <= ?"
	b.args = append(b.args, *b.deadlineEnd)
}

func (b *billingResourceDataBase) genInPlaceHolder(count int) string {
	placeholder := make([]string, count)
	for i := range placeholder {
		placeholder[i] = "?"
	}
	return " IN(" + strings.Join(placeholder, ",") + ")"
}

func (b *billingResourceDataBase) buildOrderBy() {
	if b.orderBy == nil {
		return
	}
	b.where += " ORDER BY ExpireTime"

	if b.sortBy == nil {
		return
	}
	if *b.sortBy == "ASC" {
		b.where += " ASC"
	} else {
		b.where += " DESC"
	}
}

func (b *billingResourceDataBase) buildLimit() {
	if b.pageNo != nil && b.pageSize != nil && *b.pageSize != 0 {
		if b.orderBy == nil {
			b.where += " ORDER BY Id"
		}
		b.where += " LIMIT ?, ?"
		b.args = append(b.args, (*b.pageNo)*(*b.pageSize))
		b.args = append(b.args, *b.pageSize)
	}
}

func (b *billingResourceDataBase) buildCommonWhere() {
	b.where = "WHERE SubProductCode = ? AND Region = ?"
	b.args = make([]interface{}, 0)
	b.args = append(b.args, b.subProductCode)
	b.args = append(b.args, b.region)
	if !b.includeDestroyStatus {
		b.where += " AND Status != ?"
		b.args = append(b.args, billing2.ResourceStatusDestroyed)
	}
	return
}
