package billing

import (
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/billing"
	"testing"
)

func TestDestroyBillingResourceService_Destroy(t *testing.T) {
	req := &billing.DestroyResourceReq{
		AppId:      *fTestAppId,
		Uin:        "",
		OperateUin: "",
		Region:     *fTestRegion,
		ResourceId: *fTestResourceId,
	}

	flowId, err := NewDestroyBillingResourceService(req).Destroy()
	if err != nil {
		t.Fatal(err)
	}

	t.Log(flowId)
}
