package billing

import (
	"errors"
	"fmt"

	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	service3 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/billing"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/cluster"
	billingTable "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/billing"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cmq"
)

var billingResourceService = &BillingResourceService{}

type DestroyBillingResourceService struct {
	req      *billing.DestroyResourceReq
	resource *billingTable.BillingResource
}

func NewDestroyBillingResourceService(req *billing.DestroyResourceReq) *DestroyBillingResourceService {
	return &DestroyBillingResourceService{req: req}
}

// 后付费欠费销毁所有隔离的资源
func (d *DestroyBillingResourceService) DestroyAll() (flowId int64, err error) {

	// 获取当前Appid是否触发过销毁
	flowRelations, err := flow.GetFlowRelationByAppId(d.req.AppId, constants.FLOW_OCEANUS_DELETE_CLUSTER, d.req.Region)
	if err != nil {
		return 0, err
	}
	if len(flowRelations) > 0 {
		return flowRelations[0].ParentId, nil
	}

	var parentRelationId int64
	// 获取当前Appid隔离的资源
	flowRelations, err = flow.GetFlowRelationByAppId(d.req.AppId, constants.FLOW_OCEANUS_ISOLATE_CLUSTER, d.req.Region)
	if err != nil {
		return 0, err
	}
	// 没有需要隔离销毁的资源
	if len(flowRelations) < 1 {
		return billing.NoResourceFlag, nil
	}

	parentRelationId = flowRelations[0].ParentId

	resourceIdList := make([]string, 0)
	for _, flowRelation := range flowRelations {
		resourceIdList = append(resourceIdList, flowRelation.ResourceId)
	}

	// 没有资源就返回空
	if len(resourceIdList) < 1 {
		return billing.NoResourceFlag, nil
	}

	// 后付费生命周期欠费销毁
	isQN := d.req.ResourceId == ""

	flowRelationList := make([]*flow.FlowRelation, 0)
	hasIsolateResource := true

	// 同步接口需要放入一个事务, 避免中间状态
	service3.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		for _, resourceId := range resourceIdList {
			clusterGroup, err := service2.ListClusterGroupBySerialId(resourceId)
			if err != nil {
				logger.Errorf("## 后付费欠费销毁所有隔离的资源 Failed to get cluster group by serial id %s because %+v", resourceId, err)
				panic(err)
			}

			if clusterGroup.Status == constants.CLUSTER_GROUP_STATUS_DELETED ||
				clusterGroup.Status == constants.CLUSTER_GROUP_STATUS_DELETING {
				logger.Warningf("后付费欠费销毁所有隔离的资源, 集群 name: %s 处于删除中，不需要操作", clusterGroup.Name)
				continue
			}

			billingResource, err := billingResourceService.GetResourceById(resourceId)
			if err != nil {
				logger.Errorf("Failed to get billing resource by id %s because %+v", resourceId, err)
				panic(err)
			}

			// 如果资源已经销毁了， 直接返回给计费 流程id， 让计费去查询流程是否结束
			if billingResource.Status == billing.ResourceStatusDestroyed {
				logger.Warningf("后付费欠费销毁所有隔离的资源, 集群 name: %s 已销毁，不需要操作", clusterGroup.Name)
				continue
			}
			// 删除集群
			flowId, err := d.destroyResource(billingResource)
			if err != nil {
				panic(err)
			}

			flowRelationList = append(flowRelationList, &flow.FlowRelation{
				ChildId:     flowId,
				Processname: constants.FLOW_OCEANUS_DELETE_CLUSTER,
				ResourceId:  resourceId,
				AppId:       d.req.AppId,
				Region:      d.req.Region,
			})
		}
		// 没有资源就返回空
		if len(flowRelationList) < 1 {
			hasIsolateResource = false
			return nil
		}
		flowId, err = flow.CreateFlowRelation(constants.FLOW_OCEANUS_DELETE_CLUSTER, constants.FLOW_OCEANUS_DELETE_CLUSTER, 0, flowRelationList)
		if err != nil {
			return err
		}

		// 欠费销毁的话，需要把taskflow_relation里的隔离关系设置为结束 isFinished = 1
		if isQN && parentRelationId != 0 {
			sql := "update taskflow_relation set isFinished=1 where parentId = ?"
			args := make([]interface{}, 0)
			args = append(args, parentRelationId)
			tx.ExecuteSql(sql, args)
		}
		return nil
	}).Close()

	// 没有需要隔离的集群
	if !hasIsolateResource {
		return billing.NoResourceFlag, nil
	}
	return flowId, nil
}

func (d *DestroyBillingResourceService) Destroy() (flowId int64, err error) {
	if err = d.GetResource(); err != nil {
		return 0, err
	}

	// 如果资源已经销毁了， 直接返回给计费 流程id， 让计费去查询流程是否结束
	if d.resource.Status == billing.ResourceStatusDestroyed {
		return d.resource.FlowId, nil
	}

	// 后付费生命周期没有隔离，直接删除 没有隔离状态
	if d.resource.PayMode == billing.PayModePrepaid && d.resource.Status != billing.ResourceStatusIsolated {
		msg := fmt.Sprintf("resource %s status %d not allow to destroy", d.resource.ResourceId, d.resource.Status)
		return 0, errorcode.NewStackError(errorcode.ResourceUnavailableCode, msg, nil)
	}

	return d.destroyResource(d.resource)
}

func (d *DestroyBillingResourceService) GetResource() error {
	selectSql := "SELECT *"
	resourceSet, err := newBillingResourceDataBase(selectSql, *d.req.SubProductCode, d.req.Region).
		WithDestroyStatus(true).
		WithAppId(d.req.AppId).
		WithResourceIds([]string{d.req.ResourceId}).
		QueryBillingResource()
	if err != nil {
		return err
	}
	if len(resourceSet) == 0 {
		return errorcode.NewStackError(errorcode.ResourceNotFoundCode, d.req.ResourceId, nil)
	}
	d.resource = resourceSet[0]
	return nil
}

func (d *DestroyBillingResourceService) destroyResource(resource *billingTable.BillingResource) (flowId int64, err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, "destroyResource")

	isCluster := 1
	if d.resource.Type == constants.BILLINGRESOURCE_TYPE_SETATS {
		isCluster = 0
	}
	var clusterGroupSerialId string
	if isCluster == 1 {
		clusterGroupSerialId = d.req.ResourceId
	} else {
		_setats, err := service2.GetSetatsBySerialId(d.req.ResourceId)
		if err != nil {
			logger.Errorf("Failed to get setats by resource id %s because %+v", d.req.ResourceId, err)
			panic(err)
		}
		clusterGroupSerialId = _setats.ClusterGroupSerialId
	}

	clusterGroupService, err := service2.NewClusterGroupServiceBySerialId(clusterGroupSerialId)
	if err != nil {
		return
	}
	if isCluster == 1 {
		_, err = clusterGroupService.CanDelete()
		if err != nil {
			return
		}
	}

	deleteClusterGroupService, err := service2.NewDeleteClusterService(&model.DeleteClusterReq{
		RequestBase: apiv3.RequestBase{},
		ClusterId:   clusterGroupSerialId,
	})
	if err != nil {
		return
	}

	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		resource.Status = billing.ResourceStatusDestroyed
		if isCluster == 1 {
			resource.FlowId, err = deleteClusterGroupService.Delete()
			if err != nil {
				return err
			}
		} else {
			cluster, err := clusterGroupService.GetCluster()
			if err != nil {
				logger.Errorf("Failed to get cluster by resource id %s because %+v", d.req.ResourceId, err)
				return err
			}

			tx.ExecuteSqlWithArgs("update Setats set Status=? where ClusterGroupSerialId=? and Status != -2", constants.SETATS_DELETING, clusterGroupService.GetClusterGroup().SerialId)

			resource.FlowId, err = flow.CreateFlow(constants.FLOW_OCEANUS_DELETE_SETATS,
				fmt.Sprintf("%s@%s@%d", clusterGroupSerialId, d.req.ResourceId, d.req.EventId), 0, map[string]string{
					constants.FLOW_PARAM_CLUSTER_GROUP_ID: fmt.Sprintf("%d", clusterGroupService.GetClusterGroup().Id),
					constants.FLOW_PARAM_CLUSTER_ID:       fmt.Sprintf("%d", cluster.Id),
					constants.FLOW_PARAM_REQUEST_ID:       fmt.Sprintf("%d", d.req.EventId),
					constants.FLOW_PARAM_APPID:            fmt.Sprintf("%d", clusterGroupService.GetClusterGroup().AppId),
				}, nil)
		}

		flowId = resource.FlowId
		tx.UpdateObject(resource, resource.Id, "BillingResource")
		return nil
	}).Close()
	if isCluster == 1 {
		err = senMsgForDestroyCluster(resource)
		if err != nil {
			logger.Errorf("%s: senMsgForDestroyCluster with error %v", resource.ResourceId, err)
			return
		}
	}

	return
}

func senMsgForDestroyCluster(resource *billingTable.BillingResource) (err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("senMsgForDestroyCluster with error for cluster %s %v", err, resource.ResourceId)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	jobSet := make([]*table.Job, 0)
	sql := "SELECT j.* FROM Job j left join ClusterGroup cg on j.ClusterGroupId = cg.Id WHERE cg.SerialId = ?"
	args := make([]interface{}, 0, 0)
	args = append(args, resource.ResourceId)
	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("senMsgForDestroyCluster with error for cluster %s %v", err, resource.ResourceId)
		return err
	}
	for i := 0; i < len(data); i++ {
		job := &table.Job{}
		err = util.ScanMapIntoStruct(job, data[i])
		if err != nil {
			logger.Errorf("senMsgForDestroyCluster with error for cluster %s %v", err, resource.ResourceId)
			return err
		}
		jobSet = append(jobSet, job)
	}
	// 与默认告警策略解绑
	for _, job := range jobSet {
		err = cmq.SendMsgToBarad(job.SerialId, int64(job.AppId), job.OwnerUin, job.Region, cmq.MSG_OP_DELETE)
		if err != nil {
			// 无需返回错误
			logger.Errorf("%s send message to barad with error %v", resource.ResourceId, err)
		}
	}
	return nil
}
