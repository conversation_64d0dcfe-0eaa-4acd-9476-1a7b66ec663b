package metadata

import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logging "tencentcloud.com/tstream_galileo/src/common/logger"
	service2 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/metadata"
)

type DoDescribeMetaDatabasesService struct {
}

func (service *DoDescribeMetaDatabasesService) DoDescribeMetaDatabases(req *model.DescribeMetaDatabasesReq, evenId int64) (string, string, *model.DescribeMetaDatabasesRsp) {

	// 鉴权 (子账号，只能看见自己空间内绑定的集群，超管可以看到所有)
	itemSpcIds, err := auth.ExtractSubUinAuthItemSpaceIdSet(req.WorkSpaceId, req.IsSupOwner, req.AppId, req.SubAccountUin, req.Uin, req.Region)
	if err != nil {
		logging.Errorf("%s: ExtractSubUinAuthClustersSet : Obtain  clusterGroups that has permissions   error: %+v", req.RequestId, err)
		errCode := errorcode.GetCode(err)
		return errCode.GetCodeStr(), errCode.GetCodeDesc(), nil
	}

	rsp := &model.DescribeMetaDatabasesRsp{}
	cond := dao.NewCondition()
	if len(itemSpcIds) > 0 {
		cond.In("MetastoreDatabase.ItemSpaceId", itemSpcIds)
	}
	if len(req.DatabaseIds) < 1 {
		return controller.OK, controller.NULL, rsp
	}
	cond.In("MetastoreDatabase.Id", req.DatabaseIds)
	cond.Eq("MetastoreDatabase.AppId", req.AppId)
	cond.Ne("MetastoreDatabase.Status", constants.METADATA_CATALOG_STATUS_DELETE)
	where, args := cond.GetWhere()
	sql := "SELECT MetastoreCatalog.Id cId,MetastoreCatalog.Name cName,MetastoreCatalog.Type cType,MetastoreDatabase.Id dId,MetastoreDatabase.Name dName, " +
		" MetastoreDatabase.Uin dCreator, MetastoreDatabase.CreateTime dCreateTime, MetastoreDatabase.Comment dComment,case MetastoreDatabase.Name when '_db' then 0 else 1 end IsDefaultDatabase FROM  MetastoreDatabase  " +
		" JOIN MetastoreCatalog  ON MetastoreDatabase.CatalogId = MetastoreCatalog.Id " + where
	_, data, err := service2.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return controller.InternalError, err.Error(), rsp
	}
	rsp.DatabasesItem = make([]model.DatabasesItem, 0)
	for _, value := range data {
		databaseItem := model.DatabasesItem{}
		databaseItem.CatalogId, _ = strconv.ParseInt(string(value["cId"]), 10, 64)
		databaseItem.CatalogName = string(value["cName"])
		databaseItem.DatabaseId, _ = strconv.ParseInt(string(value["dId"]), 10, 64)
		databaseItem.DatabaseName = string(value["dName"])
		databaseItem.DatabaseDes = string(value["dComment"])
		databaseItem.Creator = string(value["dCreator"])
		databaseItem.CreateTime = string(value["dCreateTime"])
		databaseItem.IsDefaultCatalog, _ = strconv.Atoi(string(value["cType"]))
		databaseItem.IsDefaultDatabase, _ = strconv.Atoi(string(value["IsDefaultDatabase"]))
		rsp.DatabasesItem = append(rsp.DatabasesItem, databaseItem)
	}
	return controller.OK, controller.NULL, rsp
}
