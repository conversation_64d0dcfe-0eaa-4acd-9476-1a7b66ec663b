package metadata

import (
	"encoding/json"
	"errors"
	"fmt"
	"regexp"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	service2 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	metadata_table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/metadata"
)

/**
 *
 */
const (
	IDENT_EXTRACT_TYPE_MAPPING   = 1
	IDENT_EXTRACT_TYPE_UNMAPPING = 2
	// 逻辑表类型
	TABLE_TYPE_MYSQL_CDC    = "mysql-cdc"
	TABLE_TYPE_JDBC         = "jdbc"
	TABLE_TYPE_KAFKA        = "kafka"
	TABLE_TYPE_UPSERT_KAFKA = "upsert-kafka"
	// TABLE_TYPE_UPSERT_CMQ      =  "cmq"
	// TABLE_TYPE_UPSERT_RABBITMQ =  "rabbitmq"
	TABLE_TYPE_TDSQL_SUBSCRIBE = "tdsql-subscribe"
	TABLE_TYPE_REDIS           = "redis"
	TABLE_TYPE_MONGODB_CDC     = "mongodb-cdc"
	TABLE_TYPE_MONGODB         = "mongodb"
	TABLE_TYPE_POSTGRES_CDC    = "postgres-cdc"
	//TABLE_TYPE_HBASE_1_4        =  "hbase-1.4"
	//TABLE_TYPE_HBASE_2_2        =  "hbase-2.2"
	//TABLE_TYPE_DORIS            =  "doris"
	TABLE_TYPE_HIVE = "hive"
	//TABLE_TYPE_CLICKHOUSE       =  "clickhouse"
	//TABLE_TYPE_KUDU             =  "kudu"
	TABLE_TYPE_JDBCPG          = "jdbcPG"
	TABLE_TYPE_ELASTICSEARCH_6 = "elasticsearch-6"
	TABLE_TYPE_ELASTICSEARCH_7 = "elasticsearch-7"
	TABLE_TYPE_ELASTICSEARCH   = "elasticsearch" // es 旧写法
	TYPE_ELASTICSEARCH_SOURCE  = "es-source"

	TABLE_TYPE_FILESYSTEM = "filesystem"
	// 物理表类型
	IDENTIFIER_TYPE_MYSQL         = "mysql"
	IDENTIFIER_TYPE_KAFKA         = "kafka"
	IDENTIFIER_TYPE_TDSQL         = "tdsql"
	IDENTIFIER_TYPE_REDIS         = "redis"
	IDENTIFIER_TYPE_MONGODB       = "mongodb"
	IDENTIFIER_TYPE_POSTGRES      = "postgres"
	IDENTIFIER_TYPE_FILESYSTEM    = "filesystem"
	IDENTIFIER_TYPE_HIVE          = "hive"
	IDENTIFIER_TYPE_ELASTICSEARCH = "elasticsearch"
)

type MetaTableIdentifierService struct {
	FlinkVersion           string
	BuildInSystemConnector bool // 系统支持的connector
	extractorContext       *ExtractorContext
	IdentExtractType       int
}

type IdentifierExtractor interface {
	SetIdentifierType(context *ExtractorContext) (err error)
	ExtractStableOptions(context *ExtractorContext) (matched bool, stableOptions string, err error)
	ExtractUnstableOptions(context *ExtractorContext) (matched bool, unstableOptions string, err error)
}

// identifier extractor
var extractors = map[string]IdentifierExtractor{
	TABLE_TYPE_JDBC:            &JdbcExtractor{},
	TABLE_TYPE_MYSQL_CDC:       &MysqlCdcExtractor{},
	TABLE_TYPE_KAFKA:           &KafkaExtractor{},
	TABLE_TYPE_UPSERT_KAFKA:    &KafkaExtractor{},
	TABLE_TYPE_TDSQL_SUBSCRIBE: &TdsqlExtractor{},
	TABLE_TYPE_REDIS:           &RedisExtractor{},
	TABLE_TYPE_MONGODB_CDC:     &MongodbCdcExtractor{},
	TABLE_TYPE_MONGODB:         &MongodbExtractor{},
	TABLE_TYPE_POSTGRES_CDC:    &PostgresCdcExtractor{},
	TABLE_TYPE_HIVE:            &HiveExtractor{},
	TABLE_TYPE_JDBCPG:          &JdbcPgExtractor{},
	TABLE_TYPE_ELASTICSEARCH:   &ElasticsearchExtractor{},
	TYPE_ELASTICSEARCH_SOURCE:  &ElasticsearchSourceExtractor{},
}

func NewMetaTableIdentifierService(flinkVersion string, properties string,
	catalog string, database string, table string) (mis *MetaTableIdentifierService, err error) {
	refs, systemConnector, mismatched, err := GetSystemConnectorRef(properties, flinkVersion)
	if err != nil {
		logger.Error("Failed to  GetSystemConnectorRef :", properties, "\t err:", err)
		return nil, err
	}
	if mismatched || len(refs) == 0 || systemConnector == nil {
		return &MetaTableIdentifierService{
			FlinkVersion: flinkVersion,
			extractorContext: &ExtractorContext{
				StableOptions:   "",
				UnstableOptions: "",
				Properties:      properties,
				Catalog:         catalog,
				Database:        database,
				Table:           table,
			},
			BuildInSystemConnector: false,
			IdentExtractType:       IDENT_EXTRACT_TYPE_UNMAPPING,
		}, nil
	} else {
		return &MetaTableIdentifierService{
			FlinkVersion: flinkVersion,
			extractorContext: &ExtractorContext{
				StableOptions:   systemConnector.StableOptions,
				UnstableOptions: systemConnector.UnstableOptions,
				Properties:      properties,
				Catalog:         catalog,
				Database:        database,
				Table:           table,
			},
			BuildInSystemConnector: true,
			IdentExtractType:       systemConnector.IdentExtractType,
		}, nil
	}
}

type ExtractorContext struct {
	StableOptions   string // host,port
	UnstableOptions string // bootstrap.server
	Properties      string
	IdentifierType  string
	Catalog         string
	Database        string
	Table           string
}

// default extractor
type MappingExtractor struct {
}

func ExtractOptions(options string, context *ExtractorContext) (matched bool, stableOptions string, err error) {
	if options == "" {
		return matched, stableOptions, err
	}
	pros := make(map[string]string)
	err = json.Unmarshal([]byte(context.Properties), &pros)
	if err != nil {
		logger.Error("Failed to  parse Unmarshal properties :", context.Properties, "\t err:", err)
		return matched, stableOptions, err
	}
	var optionsArr = make([]string, 0)
	err = json.Unmarshal([]byte(options), &optionsArr)
	if err != nil {
		logger.Error("Failed to  parse Unmarshal stableOptions :", options, "\t err:", err)
		return matched, stableOptions, err
	}
	extractedOptions := make(map[string]string, 0)
	if len(optionsArr) > 0 {
		for _, key := range optionsArr {
			value, ok := pros[key]
			if ok {
				matched = true
				extractedOptions[key] = value
			} else {
				matched = false
			}
		}
	}
	if matched {
		stableOptionsBytes, err := json.Marshal(&extractedOptions) // ordered
		if err != nil {
			logger.Error("Failed to  marshal option :", extractedOptions, "\t err:", err)
			return matched, stableOptions, err
		}
		return matched, string(stableOptionsBytes), nil
	} else {
		// 如果未匹配上，则创建一个Identifier
		logger.Warningf("The table definition [%s] does not match options [%s] .", context.Properties, options)
	}
	return matched, stableOptions, nil
}

func (extractor *MappingExtractor) SetIdentifierType(context *ExtractorContext) (err error) {
	pros := make(map[string]string)
	err = json.Unmarshal([]byte(context.Properties), &pros)
	if err != nil {
		logger.Error("Failed to  parse Unmarshal properties :", context.Properties, "\t err:", err)
		return err
	}
	connector, ok := pros["connector"] // default : use connector type as identify type
	if !ok {
		connector = pros["connector.type"]
	}
	context.IdentifierType = connector
	return nil
}

func (extractor *MappingExtractor) ExtractStableOptions(context *ExtractorContext) (matched bool, stableOptions string, err error) {
	return ExtractOptions(context.StableOptions, context)
}

func (extractor *MappingExtractor) ExtractUnstableOptions(context *ExtractorContext) (matched bool, unstableOptions string, err error) {
	return ExtractOptions(context.UnstableOptions, context)
}

// UnMappingExtractor
type UnMappingExtractorExtractor struct {
}

func (extractor *UnMappingExtractorExtractor) SetIdentifierType(context *ExtractorContext) (err error) {
	mappingExtractor := MappingExtractor{}
	return mappingExtractor.SetIdentifierType(context)
}

func (extractor *UnMappingExtractorExtractor) ExtractStableOptions(context *ExtractorContext) (matched bool, stableOptions string, err error) {
	return false, "", nil
}

func (extractor *UnMappingExtractorExtractor) ExtractUnstableOptions(context *ExtractorContext) (matched bool, unstableOptions string, err error) {
	return false, "", nil
}

// [*] jdbc
// stable : hostname,port,database-name,table-name
type JdbcExtractor struct {
}

func (extractor *JdbcExtractor) SetIdentifierType(context *ExtractorContext) (err error) {
	pros := make(map[string]string)
	err = json.Unmarshal([]byte(context.Properties), &pros)
	if err != nil {
		return err
	}
	urlPatten := "^jdbc\\:(.*)\\://(.*)\\:(.*)/(.*)\\??.*$"
	urlRegx := regexp.MustCompile(urlPatten)
	url := pros["url"]
	if urlRegx.Match([]byte(url)) {
		urlMatched := urlRegx.FindStringSubmatch(url)
		if len(urlMatched) != 5 {
			return errors.New("Invalid jdbc url " + url)
		}
		context.IdentifierType = urlMatched[1]
	}
	return nil
}

func (extractor *JdbcExtractor) ExtractStableOptions(context *ExtractorContext) (matched bool, stableOptions string, err error) {

	pros := make(map[string]string)
	err = json.Unmarshal([]byte(context.Properties), &pros)
	if err != nil {
		return matched, stableOptions, err
	}
	extractedOptions := make(map[string]string, 0)
	url := pros["url"]
	urlPatten := "^jdbc\\:(.*)\\://(.*)\\:(.*?)/(.*)$"
	if strings.Contains(url, "?") {
		urlPatten = "^jdbc\\:(.*)\\://(.*)\\:(.*?)/(.*?)\\?.*$"
	}
	urlRegx := regexp.MustCompile(urlPatten)
	tableName := pros["table-name"]
	if urlRegx.Match([]byte(url)) {
		urlMatched := urlRegx.FindStringSubmatch(url)
		if len(urlMatched) != 5 {
			return matched, "", errors.New("Invalid jdbc url " + url)
		}
		host := urlMatched[2]
		port := urlMatched[3]
		databaseName := urlMatched[4]
		extractedOptions["hostname"] = host
		extractedOptions["port"] = port
		extractedOptions["database-name"] = databaseName
		matched = true
	}
	extractedOptions["table-name"] = tableName
	extractedOptionsBytes, err := json.Marshal(&extractedOptions)
	if err != nil {
		logger.Error("Failed to  marshal option :", extractedOptions, "\t err:", err)
		return matched, "", err
	}
	return matched, string(extractedOptionsBytes), nil
}

func (extractor *JdbcExtractor) ExtractUnstableOptions(context *ExtractorContext) (matched bool, stableOptions string, err error) {
	return ExtractOptions(context.UnstableOptions, context)
}

// [mysql] mysql cdc
// stable : hostname,port,database-name,table-name [ref -> jdbc:mysql]
type MysqlCdcExtractor struct {
}

func (extractor *MysqlCdcExtractor) SetIdentifierType(context *ExtractorContext) (err error) {
	context.IdentifierType = IDENTIFIER_TYPE_MYSQL
	return nil
}

func (extractor *MysqlCdcExtractor) ExtractStableOptions(context *ExtractorContext) (matched bool, stableOptions string, err error) {
	pros := make(map[string]string)
	err = json.Unmarshal([]byte(context.Properties), &pros)
	if err != nil {
		return matched, stableOptions, err
	}
	extractedOptions := make(map[string]string, 0)
	hostname := pros["hostname"]
	port := pros["port"]
	databaseName := pros["database-name"]
	tableName := pros["table-name"]
	extractedOptions["hostname"] = hostname
	extractedOptions["port"] = port
	extractedOptions["database-name"] = databaseName
	extractedOptions["table-name"] = tableName
	extractedOptionsBytes, err := json.Marshal(&extractedOptions)
	if err != nil {
		logger.Error("Failed to  marshal option :", extractedOptions, "\t err:", err)
		return matched, "", err
	}
	return true, string(extractedOptionsBytes), nil
}

func (extractor *MysqlCdcExtractor) ExtractUnstableOptions(context *ExtractorContext) (matched bool, stableOptions string, err error) {
	return ExtractOptions(context.UnstableOptions, context)
}

// [kafka] kafka|upsert-kafka
// stable: topic,properties.bootstrap.servers [default]
type KafkaExtractor struct {
}

func (extractor *KafkaExtractor) SetIdentifierType(context *ExtractorContext) (err error) {
	context.IdentifierType = IDENTIFIER_TYPE_KAFKA
	return nil
}

func (extractor *KafkaExtractor) ExtractStableOptions(context *ExtractorContext) (matched bool, stableOptions string, err error) {
	return ExtractOptions(context.StableOptions, context)
}

func (extractor *KafkaExtractor) ExtractUnstableOptions(context *ExtractorContext) (matched bool, unstableOptions string, err error) {
	return ExtractOptions(context.UnstableOptions, context)
}

// [cmq]cmq [full default]
// stable:hosts,queue

// [tdsql] tdsql-subscribe
// stable:topic,properties.bootstrap.servers
type TdsqlExtractor struct {
}

func (extractor *TdsqlExtractor) SetIdentifierType(context *ExtractorContext) (err error) {
	context.IdentifierType = IDENTIFIER_TYPE_TDSQL
	return nil
}

func (extractor *TdsqlExtractor) ExtractStableOptions(context *ExtractorContext) (matched bool, stableOptions string, err error) {
	return ExtractOptions(context.StableOptions, context)
}

func (extractor *TdsqlExtractor) ExtractUnstableOptions(context *ExtractorContext) (matched bool, unstableOptions string, err error) {
	return ExtractOptions(context.UnstableOptions, context)
}

// [redis]redis
// stable:nodes,database(default 0)

type RedisExtractor struct {
}

func (extractor *RedisExtractor) SetIdentifierType(context *ExtractorContext) (err error) {
	context.IdentifierType = IDENTIFIER_TYPE_REDIS
	return nil
}

func (redisExtractor *RedisExtractor) ExtractStableOptions(context *ExtractorContext) (matched bool, stableOptions string, err error) {
	pros := make(map[string]string)
	err = json.Unmarshal([]byte(context.Properties), &pros)
	if err != nil {
		return matched, stableOptions, err
	}
	extractedOptions := make(map[string]string, 0)
	nodes := pros["nodes"]
	database := pros["database"]
	if database == "" { // default 0
		database = "0"
	}
	extractedOptions["nodes"] = nodes
	extractedOptions["database"] = database
	extractedOptionsBytes, err := json.Marshal(&extractedOptions)
	if err != nil {
		logger.Error("Failed to  marshal option :", extractedOptions, "\t err:", err)
		return matched, "", err
	}
	return true, string(extractedOptionsBytes), nil
}

func (extractor *RedisExtractor) ExtractUnstableOptions(context *ExtractorContext) (matched bool, unstableOptions string, err error) {
	return ExtractOptions(context.UnstableOptions, context)
}

// [mongodb]mongodb-cdc
// stable:hosts,database,collection
type MongodbCdcExtractor struct {
}

func (extractor *MongodbCdcExtractor) SetIdentifierType(context *ExtractorContext) (err error) {
	context.IdentifierType = IDENTIFIER_TYPE_MONGODB
	return nil
}

func (extractor *MongodbCdcExtractor) ExtractStableOptions(context *ExtractorContext) (matched bool, stableOptions string, err error) {
	return ExtractOptions(context.StableOptions, context)
}

func (extractor *MongodbCdcExtractor) ExtractUnstableOptions(context *ExtractorContext) (matched bool, unstableOptions string, err error) {
	return ExtractOptions(context.UnstableOptions, context)
}

// [mongodb]mongodb  [full default]
// stable:uri,database,collection -> hosts,database,collection

type MongodbExtractor struct {
}

func (extractor *MongodbExtractor) SetIdentifierType(context *ExtractorContext) (err error) {
	context.IdentifierType = IDENTIFIER_TYPE_MONGODB
	return nil
}

func (extractor *MongodbExtractor) ExtractStableOptions(context *ExtractorContext) (matched bool, stableOptions string, err error) {
	pros := make(map[string]string)
	err = json.Unmarshal([]byte(context.Properties), &pros)
	if err != nil {
		return matched, stableOptions, err
	}
	extractedOptions := make(map[string]string, 0)
	url := pros["uri"]
	//urlPatten := "^mongodb\\:(.*)\\://(.*)\\:(.*?)/(.*)$"
	urlPatten := "^mongodb\\://(.*)@(.*)/(.*?)"
	if strings.Contains(url, "?") {
		//urlPatten = "^mongodb\\:(.*)\\://(.*)\\:(.*?)/(.*?)\\?.*$"
		urlPatten = "^mongodb\\://(.*)@(.*)/(.*?)\\?.*$"
	}
	urlRegx := regexp.MustCompile(urlPatten)
	database := pros["database"]
	collection := pros["collection"]
	if urlRegx.Match([]byte(url)) {
		urlMatched := urlRegx.FindStringSubmatch(url)
		if len(urlMatched) != 4 {
			return matched, "", errors.New("Invalid jdbc url " + url)
		}
		hosts := urlMatched[2]
		extractedOptions["hosts"] = hosts
	}
	extractedOptions["database"] = database
	extractedOptions["collection"] = collection
	extractedOptionsBytes, err := json.Marshal(&extractedOptions)
	if err != nil {
		logger.Error("Failed to  marshal option :", extractedOptions, "\t err:", err)
		return matched, "", err
	}
	return true, string(extractedOptionsBytes), nil
}

func (extractor *MongodbExtractor) ExtractUnstableOptions(context *ExtractorContext) (matched bool, unstableOptions string, err error) {
	return ExtractOptions(context.UnstableOptions, context)
}

// [postgres] postgres-cdc [ref -> jdbc:postgres]
type PostgresCdcExtractor struct {
}

func (extractor *PostgresCdcExtractor) SetIdentifierType(context *ExtractorContext) (err error) {
	context.IdentifierType = IDENTIFIER_TYPE_POSTGRES
	return nil
}

func (extractor *PostgresCdcExtractor) ExtractStableOptions(context *ExtractorContext) (matched bool, stableOptions string, err error) {
	return ExtractOptions(context.StableOptions, context)
}

func (extractor *PostgresCdcExtractor) ExtractUnstableOptions(context *ExtractorContext) (matched bool, unstableOptions string, err error) {
	return ExtractOptions(context.UnstableOptions, context)
}

// [hive]
// hive-database hive-table (hive_table) 取自DDL
type HiveExtractor struct {
}

func (extractor *HiveExtractor) SetIdentifierType(context *ExtractorContext) (err error) {
	context.IdentifierType = IDENTIFIER_TYPE_HIVE
	return nil
}

func (extractor *HiveExtractor) ExtractStableOptions(context *ExtractorContext) (matched bool, stableOptions string, err error) {
	matched, options, err := ExtractOptions(context.StableOptions, context)
	if err != nil {
		return matched, options, err
	}
	if matched && options != "" {
		pros := make(map[string]string)
		err = json.Unmarshal([]byte(options), &pros)
		if err != nil {
			return matched, stableOptions, err
		}
		pros["hive-table"] = context.Table
		stableOptionsBytes, err := json.Marshal(&pros) // ordered
		if err != nil {
			logger.Error("Failed to  marshal option :", pros, "\t err:", err)
			return matched, stableOptions, err
		}
		return matched, string(stableOptionsBytes), nil
	}
	return matched, stableOptions, nil
}

func (extractor *HiveExtractor) ExtractUnstableOptions(context *ExtractorContext) (matched bool, unstableOptions string, err error) {
	return ExtractOptions(context.UnstableOptions, context)
}

// [jdbcPG]
// url,table-name -> hostname,port,database-name,table-name
type JdbcPgExtractor struct {
}

func (extractor *JdbcPgExtractor) SetIdentifierType(context *ExtractorContext) (err error) {
	context.IdentifierType = IDENTIFIER_TYPE_POSTGRES
	return nil
}

func (extractor *JdbcPgExtractor) ExtractStableOptions(context *ExtractorContext) (matched bool, stableOptions string, err error) {
	jdbcExtractor := JdbcExtractor{}
	return jdbcExtractor.ExtractStableOptions(context)
}

func (extractor *JdbcPgExtractor) ExtractUnstableOptions(context *ExtractorContext) (matched bool, unstableOptions string, err error) {
	return ExtractOptions(context.UnstableOptions, context)
}

// elasticsearch （旧写法）
type ElasticsearchExtractor struct {
}

func (extractor *ElasticsearchExtractor) SetIdentifierType(context *ExtractorContext) (err error) {
	context.IdentifierType = IDENTIFIER_TYPE_ELASTICSEARCH
	return nil
}

func (extractor *ElasticsearchExtractor) ExtractStableOptions(context *ExtractorContext) (matched bool, stableOptions string, err error) {
	context.StableOptions = "[\"connector.hosts\",\"connector.index\"]"
	matched, options, err := ExtractOptions(context.StableOptions, context)
	if err != nil {
		return false, "", err
	}
	if matched && options != "" {
		pros := make(map[string]string)
		newPros := make(map[string]string)
		err = json.Unmarshal([]byte(options), &pros)
		if err != nil {
			return matched, stableOptions, err
		}
		newPros["hosts"] = pros["connector.hosts"]
		newPros["index"] = pros["connector.index"]
		stableOptionsBytes, err := json.Marshal(&newPros) // ordered
		if err != nil {
			logger.Error("Failed to  marshal option :", pros, "\t err:", err)
			return matched, stableOptions, err
		}
		return matched, string(stableOptionsBytes), nil
	}
	return false, "", nil
}

func (extractor *ElasticsearchExtractor) ExtractUnstableOptions(context *ExtractorContext) (matched bool, unstableOptions string, err error) {
	return ExtractOptions(context.UnstableOptions, context)
}

type ElasticsearchSourceExtractor struct {
}

func (extractor *ElasticsearchSourceExtractor) SetIdentifierType(context *ExtractorContext) (err error) {
	context.IdentifierType = IDENTIFIER_TYPE_ELASTICSEARCH
	return nil
}

func (extractor *ElasticsearchSourceExtractor) ExtractStableOptions(context *ExtractorContext) (matched bool, stableOptions string, err error) {

	context.StableOptions = "[\"endPoint\",\"port\",\"indexName\"]"
	matched, options, err := ExtractOptions(context.StableOptions, context)
	if err != nil {
		return false, "", err
	}
	if matched && options != "" {
		pros := make(map[string]string)
		newPros := make(map[string]string)
		err = json.Unmarshal([]byte(options), &pros)
		if err != nil {
			return matched, stableOptions, err
		}
		port := pros["port"]
		endPoint := pros["endPoint"]
		indexName := pros["indexName"]
		newPros["index"] = indexName
		newPros["hosts"] = "http://" + endPoint + ":" + port
		stableOptionsBytes, err := json.Marshal(&newPros) // ordered
		if err != nil {
			logger.Error("Failed to  marshal option :", pros, "\t err:", err)
			return matched, stableOptions, err
		}
		return matched, string(stableOptionsBytes), nil
	}
	return false, "", nil
}

func (extractor *ElasticsearchSourceExtractor) ExtractUnstableOptions(context *ExtractorContext) (matched bool, unstableOptions string, err error) {
	return ExtractOptions(context.UnstableOptions, context)
}

//  service function
func (ms *MetaTableIdentifierService) BuildMetaTableIdentifier(region string, appId int64, uin string, subUin string,
	itemSpaceId int64) (metaTableIdentifier *metadata_table.MetaTableIdentifier, err error) {
	// 1. get identifier extractor
	extractor, err := ms.GetIdentifierExtractor()
	if err != nil {
		logger.Error("Failed to get identifier extractor", "\t err:", err)
		return metaTableIdentifier, err
	}
	// 2、set identifier type
	err = extractor.SetIdentifierType(ms.extractorContext)
	if err != nil {
		logger.Error("Failed to set  identifier type", "\t err:", err)
		return metaTableIdentifier, err
	}
	// 3、extract stableOptions & unstableOptions
	stableOptionsMatched, stableOptions, err := extractor.ExtractStableOptions(ms.extractorContext)
	if err != nil {
		logger.Error("Failed to extract stable options", "\t err:", err)
		return metaTableIdentifier, err
	}
	// 4. find TableIdentifier by stableOptions & unstableOptions
	if stableOptionsMatched && stableOptions != "" {
		metaTableIdentifier, err = ms.GetMetaTableIdentifier(stableOptions, "", region, appId, uin, itemSpaceId)
		if err != nil {
			logger.Error("Failed to get meta table identifier", "\t err:", err)
			return metaTableIdentifier, err
		}
	}
	if metaTableIdentifier == nil {
		metaTableIdentifier = &metadata_table.MetaTableIdentifier{}
		metaTableIdentifier.UnstableOptions = ""
		metaTableIdentifier.StableOptions = stableOptions
		metaTableIdentifier.AppId = appId
		metaTableIdentifier.Uin = uin
		metaTableIdentifier.SubUin = subUin
		metaTableIdentifier.Type = ms.extractorContext.IdentifierType
		metaTableIdentifier.ItemSpaceId = itemSpaceId
		metaTableIdentifier.Region = region
		metaTableIdentifier.Status = constants.METADATA_CATALOG_STATUS_ACTIVE
		metaTableIdentifier.CreateTime = util.GetCurrentTime()
		metaTableIdentifier.UpdateTime = util.GetCurrentTime()
	}
	return metaTableIdentifier, nil
}

func (ms *MetaTableIdentifierService) GetIdentifierExtractor() (extractor IdentifierExtractor, err error) {
	if ms.IdentExtractType == IDENT_EXTRACT_TYPE_UNMAPPING {
		return &UnMappingExtractorExtractor{}, nil
	}
	tableType, err := GetConnectorType(ms.extractorContext.Properties)
	if err != nil {
		return nil, err
	}
	extractor, ok := extractors[tableType]
	if ok { //
		return extractor, nil
	} else if ms.BuildInSystemConnector {
		return &MappingExtractor{}, nil
	}
	return &UnMappingExtractorExtractor{}, nil
}

/**
 *  创建Meta Table时，匹配Identifier
 *  如果未匹配到,创建Meta Table时创建Identifier
 */
func (ms *MetaTableIdentifierService) GetMetaTableIdentifier(stableOptions string, unstableOptions string, region string, appId int64, uin string,
	itemSpaceId int64) (metaTableIdentifier *metadata_table.MetaTableIdentifier, err error) {

	metaTableIdentifier = &metadata_table.MetaTableIdentifier{}
	if ms.IdentExtractType == IDENT_EXTRACT_TYPE_UNMAPPING || stableOptions == "" {
		return metaTableIdentifier, nil // empty Identifier. Table与Identifier一一对应
	}
	// 根据stableOptions & unstableOptions 匹配 Identifier
	cond := dao.NewCondition()
	cond.Eq("StableOptions", stableOptions)
	cond.Eq("AppId", appId)
	cond.Eq("Uin", uin)
	cond.Eq("Region", region)
	cond.Eq("ItemSpaceId", itemSpaceId)
	cond.Eq("Status", constants.METADATA_CATALOG_STATUS_ACTIVE)

	where, args := cond.GetWhere()
	sql := "SELECT  * FROM MetaTableIdentifier  " + where
	count, data, err := service2.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return nil, err
	}
	if count == 0 {
		return nil, nil // not found
	}
	err = util.ScanMapIntoStruct(metaTableIdentifier, data[0])
	if err != nil {
		logger.Errorf("Failed to convert MetaTableIdentifier data bytes into struct, with errors:%+v", err)
		return nil, err
	}
	// TODO  match unstableOptions
	return metaTableIdentifier, nil
}

func GetMetaTableIdentifierByTableId(tableId int64, appId int64, uin string, region string,
	itemSpaceIds []int64) (count int, metaTableIdentifier *metadata_table.MetaTableIdentifier, err error) {

	cond := dao.NewCondition()
	cond.Eq("a.TableId", tableId)
	cond.Eq("b.AppId", appId)
	cond.Eq("b.Uin", uin)
	cond.Eq("b.Region", region)
	cond.In("b.ItemSpaceId", itemSpaceIds)
	cond.Eq("b.Status", constants.METADATA_CATALOG_STATUS_ACTIVE)

	where, args := cond.GetWhere()
	sql := "SELECT  * FROM MetaTableIdentifierRef a JOIN MetaTableIdentifier b on a.IdentifierId=b.Id " + where
	count, data, err := service2.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return count, nil, err
	}

	if count > 1 {
		return count, nil, errors.New(fmt.Sprintf("Found multi MetaTableIdentifier ,tableId: %d", tableId)) // multi found
	}
	metaTableIdentifier = &metadata_table.MetaTableIdentifier{}
	err = util.ScanMapIntoStruct(metaTableIdentifier, data[0])
	if err != nil {
		logger.Errorf("Failed to convert MetaTableIdentifier data bytes into struct, with errors:%+v", err)
		return count, nil, err
	}
	return count, metaTableIdentifier, nil
}

func GetMetaTableIdentifierRefs(identifierId int64) (tables []*metadata_table.MetaTableIdentifierRef, err error) {
	sql := "SELECT * FROM MetaTableIdentifierRef mi "
	cond := dao.NewCondition()
	cond.Eq("mi.Status", constants.METADATA_CATALOG_STATUS_ACTIVE)
	cond.Eq("mi.IdentifierId", identifierId)
	where, args := cond.GetWhere()
	sql += where

	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return nil, err
	}
	tables = make([]*metadata_table.MetaTableIdentifierRef, 0)
	for i := 0; i < len(data); i++ {
		metaTableIdentifierRef := &metadata_table.MetaTableIdentifierRef{}
		err = util.ScanMapIntoStruct(metaTableIdentifierRef, data[i])
		if err != nil {
			logger.Errorf("Failed to convert bytes into MetaTableIdentifierRef, with errors:%+v", err)
			return nil, errorcode.InternalErrorCode.New()
		}
		tables = append(tables, metaTableIdentifierRef)
	}
	return tables, nil
}

func SaveMetaTableIdentifier(metaTableIdentifier *metadata_table.MetaTableIdentifier) (id int64, err error) {

	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Add metaTableIdentifier to db panic ,for job:%+v, errors:%+v", metaTableIdentifier, errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	txManager := service.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		id = tx.SaveObject(metaTableIdentifier, "MetaTableIdentifier")
		cidUtil := &util.CidUtil{}
		serialId := cidUtil.EncodeId(id, "ident", "mide", util.GetNowTimestamp(), 8)
		tx.ExecuteSqlWithArgs("UPDATE MetaTableIdentifier set serialId = ?  WHERE id = ? ", serialId, id)
		return nil
	}).Close()

	return id, nil
}

func SaveIdentifierRefs(metaTableIdentifierRefs []*metadata_table.MetaTableIdentifierRef) (err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Add metaTableIdentifierRef to db panic ,for metaTableIdentifierRefs:%+v, errors:%+v", metaTableIdentifierRefs, errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	txManager := service.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		for _, metaTableIdentifierRef := range metaTableIdentifierRefs {

			tx.SaveObject(metaTableIdentifierRef, "MetaTableIdentifierRef")
		}
		return nil
	}).Close()
	return nil
}

func DeleteMetaTableIdentifierRefsByTableId(tableId int64) (err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Delete MetaTableIdentifierRef to db panic ,for Table :%+v, errors:%+v", tableId, errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()

	txManager := service.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		cond := dao.NewCondition()
		cond.Eq("TableId", tableId)
		where, args := cond.GetWhere()
		args2 := []interface{}{constants.METADATA_CATALOG_STATUS_DELETE}
		sql := "UPDATE MetaTableIdentifierRef SET Status = ? " + where
		tx.ExecuteSql(sql, append(args2, args...))
		return nil
	}).Close()
	return
}

func DeleteMetaTableIdentifierById(id int64) (err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Delete MetaTableIdentifier to db panic ,for TableIdentifier:%+v, errors:%+v", id, errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	txManager := service.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		cond := dao.NewCondition()
		cond.Eq("Id", id)
		where, args := cond.GetWhere()
		args2 := []interface{}{constants.METADATA_CATALOG_STATUS_DELETE}
		sql := "UPDATE MetaTableIdentifier SET Status = ? " + where
		tx.ExecuteSql(sql, append(args2, args...))
		return nil
	}).Close()
	return
}

func DeleteMetaTableLineageByJobConfigId(jobId int64, jobConfigVersions []int16) (err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Delete MetaTableLineage to db panic , errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	txManager := service.GetTxManager()

	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		args := make([]interface{}, 0)
		args = append(args, jobId)
		sql := " UPDATE MetaTableLineage SET Status = -2 WHERE JobConfigId IN (" +
			   " SELECT Id FROM JobConfig WHERE JobId = ? AND VersionId IN("
		for i := 0; i < len(jobConfigVersions); i++ {
			sql += " ? "
			args = append(args, jobConfigVersions[i])
			if i != len(jobConfigVersions)-1 {
				sql += ","
			}
		}
		sql += "))"
		tx.ExecuteSql(sql, args)
		return err
	}).Close()
	return
}

func DeleteMetaTableLineageBySerialId(serialIds []string) (success bool, err error) {
	txManager := service.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		args := make([]interface{}, 0)
		sql := "UPDATE MetaTableLineage SET Status = -2 WHERE JobConfigId IN (" +
			" SELECT Id FROM JobConfig jc WHERE JobId IN (" +
			" SELECT Id FROM Job j WHERE SerialId IN ("
		for i := 0; i < len(serialIds); i++ {
			sql += " ? "
			args = append(args, serialIds[i])
			if i != len(serialIds)-1 {
				sql += ","
			}
		}
		sql += ")))"
		tx.ExecuteSql(sql, args)
		success = true
		return err
	}).Close()
	return success, err
}
