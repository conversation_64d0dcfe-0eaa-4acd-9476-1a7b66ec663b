package metadata

import (
	"sort"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service2 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/metadata"
)

type DoDescribeMetaTablesService struct {
}

func (service *DoDescribeMetaTablesService) DoDescribeMetaTables(req *model.DescribeMetaTablesReq, evenId int64) (string, string, *model.DescribeMetaTablesRsp) {
	rsp := &model.DescribeMetaTablesRsp{}
	tableItemList := make([]*model.MetaTableItem, 0)
	var strCond string
	condTableCount := dao.NewCondition()
	condTableCount.Eq("MetastoreTable.Region", req.Region)
	condTableCount.Eq("MetastoreTable.Uin", req.Uin)
	condTableCount.Eq("MetastoreTable.AppId", req.AppId)
	condTableCount.Ne("MetastoreTable.Status", constants.METADATA_CATALOG_STATUS_DELETE)
	condTableCount.Ne("MetastoreDatabase.Status", constants.METADATA_CATALOG_STATUS_DELETE)
	condTableCount.Ne("MetastoreCatalog.Status", constants.METADATA_CATALOG_STATUS_DELETE)
	if len(req.SerialIds) > 0 {
		condTableCount.In("MetastoreTable.SerialId", req.SerialIds)
	}
	params := make([]interface{}, 0, 0)
	if req.Filters != nil && len(req.Filters) != 0 {
		for i := 0; i < len(req.Filters); i++ {
			filterKey := req.Filters[i].Name
			filterVals := req.Filters[i].Values
			if filterKey == "TableName" {
				for i := 0; i < len(filterVals); i++ {
					if i == 0 {
						strCond = " and (MetastoreTable.Name like ? "
						params = append(params, "%"+filterVals[i]+"%")
					} else {
						strCond += " or MetastoreTable.Name like ? "
						params = append(params, "%"+filterVals[i]+"%")
					}
					if i == len(filterVals)-1 {
						strCond += ")"
					}
				}
			}
		}
	}
	limit := req.Limit
	if limit == 0 {
		limit = 20
	}
	where, args := condTableCount.GetWhere()
	sqlTableCnt :=
		" select MetastoreTable.Id Id,MetastoreTable.SerialId SerialId,MetaTableIdentifier.SerialId IdentifierId,MetastoreTable.Name Name,MetastoreTable.TableType Type, " +
		" MetastoreTable.SubUin Creator,MetastoreTable.UpdateTime UpdateTime," +
		" (select count(distinct JobMetaTableRef.JobId) from JobMetaTableRef where MetastoreTable.Id=JobMetaTableRef.MetaTableId and JobMetaTableRef.status!=-1) JobCount ," +
		" MetastoreDatabase.Name DatabaseName,MetastoreCatalog.Name CatalogName from MetastoreTable " +
		" left join MetaTableIdentifierRef on MetastoreTable.Id = MetaTableIdentifierRef.TableId " +
		" left join MetaTableIdentifier on MetaTableIdentifierRef.IdentifierId = MetaTableIdentifier.Id " +
		" join MetastoreDatabase on MetastoreTable.DatabaseId = MetastoreDatabase.Id " +
		" join MetastoreCatalog on MetastoreDatabase.CatalogId = MetastoreCatalog.Id " +
		where + strCond + " order by MetastoreTable.UpdateTime desc,MetastoreTable.Id asc limit ?,? "
	for _, param := range params {
		args = append(args, param)
	}
	_, data, err := service2.GetTxManager().GetQueryTemplate().DoQuery(sqlTableCnt, append(args, req.Offset, limit))
	if err != nil {
		logging.Errorf("Failed to query Metadata , with errors:%+v", err)
		return controller.InternalError, err.Error(), rsp
	}
	for _, value := range data {
		metaTableItem := &model.MetaTableItem{}
		err = util.ScanMapIntoStruct(metaTableItem, value)
		if err != nil {
			logging.Errorf("Failed to query Metadata , with errors:%+v", err)
			return controller.InternalError, err.Error(), rsp
		}
		tableItemList = append(tableItemList, metaTableItem)
	}
	sort.Slice(tableItemList, func(i, j int) bool {
		return tableItemList[i].UpdateTime > tableItemList[j].UpdateTime
	})
	condTableCount = dao.NewCondition()
	condTableCount.Eq("MetastoreTable.Region", req.Region)
	condTableCount.Eq("MetastoreTable.Uin", req.Uin)
	condTableCount.Eq("MetastoreTable.AppId", req.AppId)
	condTableCount.Ne("MetastoreTable.Status", constants.METADATA_CATALOG_STATUS_DELETE)
	where, args = condTableCount.GetWhere()
	tableCountSql := "select count(id) as mtCount from MetastoreTable " + where + strCond
	_, data, err = service2.GetTxManager().GetQueryTemplate().DoQuery(tableCountSql, args)
	if err != nil {
		logging.Errorf("Failed to query Metadata , with errors:%+v", err)
		return controller.InternalError, err.Error(), rsp
	}
	tableCount, _ := strconv.ParseInt(string(data[0]["mtCount"]), 10, 64)
	rsp = &model.DescribeMetaTablesRsp{
		TotalCount:   tableCount,
		MetaTableSet: tableItemList,
	}
	return controller.OK, controller.NULL, rsp
}
