package metadata

import (
	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service2 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/metadata"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/metadata"
)

type DoDescribeMetaDatabaseService struct {
}

func (service *DoDescribeMetaDatabaseService) DoDescribeMetaDatabase(req *model.DescribeMetaDatabaseReq, evenId int64) (string, string, *model.DescribeMetaDatabaseRsp) {
	// 0623 增加区域条件
	metastoreDatabase, err := GetDatabaseByName(req)
	if err != nil {
		logging.Errorf("Failed to query meta table , with errors:%+v", err)
		return controller.InternalError, controller.NULL, nil
	}
	rsp := &model.DescribeMetaDatabaseRsp{
		Table: *metastoreDatabase,
	}
	return controller.OK, controller.NULL, rsp
}

func GetDatabaseByName(req *model.DescribeMetaDatabaseReq) (metastoreDatabase *table.MetastoreDatabase, err error) {
	cond := dao.NewCondition()
	cond.Eq("MetastoreCatalog.Name", req.Catalog)
	cond.Eq("MetastoreDatabase.Name", req.Database)
	cond.Eq("MetastoreDatabase.Region", req.Region)
	cond.Eq("MetastoreDatabase.Uin", req.Uin)
	cond.Eq("MetastoreDatabase.AppId", req.AppId)
	cond.Ne("MetastoreDatabase.Status", constants.METADATA_CATALOG_STATUS_DELETE)

	// 鉴权 (子账号，只能看见自己空间内绑定的集群，超管可以看到所有)
	itemSpcIds, err := auth.ExtractSubUinAuthItemSpaceIdSet(req.WorkSpaceId, req.IsSupOwner, req.AppId, req.SubAccountUin, req.Uin, req.Region)
	if err != nil {
		logging.Errorf("%s: ExtractSubUinAuthClustersSet : Obtain  clusterGroups that has permissions   error: %+v", req.RequestId, err)
		return
	}

	// 空间条件
	if len(itemSpcIds) > 0 {
		cond.In("MetastoreDatabase.ItemSpaceId", req.WorkSpaceId)
	}

	where, args := cond.GetWhere()
	sql := "SELECT * FROM   MetastoreDatabase  JOIN MetastoreCatalog  ON MetastoreDatabase.CatalogId = MetastoreCatalog.Id " + where
	count, data, err := service2.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return nil, err
	}
	if count <= 0 {
		return &table.MetastoreDatabase{}, nil
	}
	metastoreDatabase = &table.MetastoreDatabase{}
	err = util.ScanMapIntoStruct(metastoreDatabase, data[0])
	if err != nil {
		logging.Errorf("Failed to convert MetastoreDatabase data bytes into struct, with errors:%+v", err)
		return &table.MetastoreDatabase{}, err
	}
	return metastoreDatabase, nil
}
