package metadata

import (
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/metadata"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/metadata"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/quota"
)

type DoCreateMetaDatabaseService struct {
}

func (service *DoCreateMetaDatabaseService) DoCreateMetaDatabase(req *model.CreateMetaDatabaseReq, evenId int64) (string, string, *model.CreateMetaDatabaseRsp) {
	resp := &model.CreateMetaDatabaseRsp{}
	// MetastoreDatabase 增加 Region 字段
	metastoreDatabase := &metadata.MetastoreDatabase{
		CatalogId:  req.CatalogId,
		Name:       req.DatabaseName,
		Comment:    req.Comment,
		Region:     req.Region,
		SubUin:     req.SubAccountUin,
		Uin:        req.Uin,
		AppId:      req.AppId,
		CreateTime: util.GetCurrentTime(),
		UpdateTime: util.GetCurrentTime(),
		Status: constants.METADATA_CATALOG_STATUS_ACTIVE,
	}

	// 鉴权
	itemSpcId, err := auth.InnerCreateAuth(req.WorkSpaceId, req.AppId, req.Region)
	if err != nil {
		logger.Errorf("%s: User  uin %s is NOT authenticated, refuse to serve", req.RequestId, req.Uin)
		errCode := errorcode.GetCode(err)
		return errCode.GetCodeStr(), errCode.GetCodeDesc(), nil
	}
	metastoreDatabase.ItemSpaceId = itemSpcId

	// 1. 校验库名
	err = CheckNameValidity(req.DatabaseName, 100)
	if err != nil {
		logger.Errorf("Failed to invoke CheckNameValidity, with error: %+v", err)
		return controller.InvalidParameterValue_MetaDatabaseName, "Invalid meta database name", resp
	}
	// 2. 判断库是否已存在
	if DefaultDatabaseName == req.DatabaseName {
		msg := fmt.Sprintf("Meta Database %s already existed.", req.DatabaseName)
		logger.Warning(msg)
		return controller.InvalidParameterValue_MetaDatabaseExisted, msg, nil
	}
	// 0623 增加区域条件 + 空间条件
	existed, err := CheckMetaDatabaseExisted(req.CatalogId, req.DatabaseName, req.Uin, req.AppId, req.Region, itemSpcId)
	if err != nil {
		logger.Error("Failed to CheckMetaTableExisted :", req, "\t err:", err)
		return controller.InternalError, err.Error(), nil
	}
	if existed {
		msg := fmt.Sprintf("Meta Database %s already existed.", req.DatabaseName)
		logger.Warning(msg)
		return controller.InvalidParameterValue_MetaDatabaseExisted, msg, nil
	}
	// 3. 数据库总数量是否超过配额
	//+ 0623 增加区域条件
	databaseTotalCount, err := GetDatabasesTotalCountByAppId(req.AppId, req.Region)
	if err != nil {
		logger.Errorf("%s: Failed to create database. Exception occurs when invoke GetDatabasesTotalCountByAppId, error: %+v", req.RequestId, err)
		return controller.InternalError, controller.NULL, nil
	}
	overLimit, msg, err := quota.NewQuota().OverLimit("", int64(req.AppId), quota.MetaDatabase, int32(databaseTotalCount))
	if err != nil {
		logger.Errorf("%s: Failed to create database. Exception occurs when invoke quota.NewQuota().OverLimit, error: %+v", req.RequestId, err)
		return controller.InternalError, controller.NULL, nil
	}
	if overLimit {
		errMsg := fmt.Sprintf("database %s", msg)
		logger.Errorf("%s: Failed to create database for AppID %d. %s", req.RequestId, req.AppId, errMsg)
		return controller.LimitExceeded_MetaDatabase, errMsg, nil
	}

	// 4. 保存库
	id, err := SaveMetaDatabase(metastoreDatabase)
	if err != nil {
		logger.Error("Failed to save meta database :", metastoreDatabase, "\t err:", err)
		return controller.InternalError, controller.NULL, nil
	}
	resp.DatabaseId = id
	return controller.OK, controller.NULL, resp
}
