package metadata

import (
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/metadata"
)

type DoDeleteMetaCatalogService struct {
}

func (d *DoDeleteMetaCatalogService) DoDeleteMetaCatalogs(req *model.DeleteExternalMetaCatalogsReq, evenId int64) (string, string, *model.DeleteExternalMetaCatalogsRsp) {
	// 0. Print RequestId
	logger.Infof("%s: DeleteMetaCatalogs API called by SerialId %+s", req.RequestId, req.SerialIds)

	rsp := &model.DeleteExternalMetaCatalogsRsp{}

	//如果catalog列表是空，返回错误
	if len(req.SerialIds) == 0 {
		msg := fmt.Sprintf("%s: InvalidParameterValue.CatalogId. No SerialId specified", req.RequestId)
		logger.Error(msg)
		return errorcode.InvalidParameterValueCode.GetCodeStr(), errorcode.InvalidParameterValueCode.GetCodeDesc(), rsp
	}

	for _, serialId := range req.SerialIds {
		_, err := auth.InnerAuthMetaCatalog(req.WorkSpaceId, req.IsSupOwner, serialId, req.AppId, req.SubAccountUin, req.Region, req.Action)
		if err != nil {
			logger.Errorf("%s: User owner uin %s is NOT authenticated, refuse to serve", req.RequestId, req.Uin)
			errCode := errorcode.GetCode(err)
			return errCode.GetCodeStr(), errCode.GetCodeDesc(), rsp
		}
		err = DoMultiDelCatalog(serialId, req.AppId)
		if err != nil {
			logger.Errorf("%s: Failed To Delete Meta Catalog,  error: %+v", req.RequestId, err)
			return errorcode.FailedOperationCode_DeleteExternalCatalog.GetCodeStr(), errorcode.FailedOperationCode_DeleteExternalCatalog.GetCodeDesc(), rsp
		}
		rsp.SerialIds = req.SerialIds
	}
	return controller.OK, controller.NULL, rsp
}
