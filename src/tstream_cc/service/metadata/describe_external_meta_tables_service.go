package metadata

import (
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/auth"
	commandc "tencentcloud.com/tstream_galileo/src/common/command"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	logger "tencentcloud.com/tstream_galileo/src/main/credentials_provider/common/log"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	modelResource "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_config"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/metadata"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cluster_admin_protocol"
	sql_model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/sql"
	table4 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/metadata"
	service1 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
)

type DoDescribeMetaExternalTableService struct {
	Request                  *model.DescribeExternalMetaTablesReq
	MetaCatalogHiveResources []*model.MetaCatalogExternalResource
	ResourceRefs             []*modelResource.ResourceRefItem
	HiveVersion              string
	CatalogName              string
	DefaultDatabase          string
	ResourceLocs             []*sql_model.ResourceLocationForSqlServer
	HiveConfDir              string
	ClusterId                int64
	CatalogType              int64
	Cam                      *sql_model.Cam
	VpcReq                   map[string]interface{}
	MysqlCatalog             *model.MysqlCatalog
}

func (tms *DoDescribeMetaExternalTableService) DoDescribeMetaExternalTable(req *model.DescribeExternalMetaTablesReq, evenId int64) (rsp *model.DescribeExternalMetaTablesRsp, err error) {

	if tms.Request.CatalogId == "" {
		err = errorcode.InvalidParameterCode.ReplaceDesc("CatalogIds can not be null .")
		return
	}
	meta, err := GetMetaCatalogFormSerialId(tms.Request.CatalogId, tms.Request.AppId, tms.Request.Region)
	if err != nil {
		logger.Errorf(" InnerAuth query  ItemSpaces error: %+v", err)
		return
	}
	if meta == nil {
		err = errorcode.InvalidParameterCode.ReplaceDesc("Catalog not exists.")
		return
	}

	//Auth鉴权
	itemSpcIds, err := auth.ExtractSubUinAuthItemSpaceIdSet(req.WorkSpaceId, req.IsSupOwner, req.AppId, req.SubAccountUin, req.Uin, req.Region)
	if err != nil {
		logging.Errorf("%s: ExtractSubUinAuthItemSpaceIdSet : Obtain catalog that has permissions   error: %+v", req.RequestId, err)
		return rsp, err
	}
	metaExternalDatabaseService := DoDescribeMetaExternalDatabaseService{Request: &model.DescribeExternalMetaDatabasesReq{RequestBase: req.RequestBase, CatalogId: req.CatalogId,
		ClusterId: req.ClusterId, FlinkVersion: req.FlinkVersion}}

	// 查询资源
	err = metaExternalDatabaseService.getMetaCatalogResourceRef(itemSpcIds)
	if err != nil {
		logging.Errorf("Failed to query meta table , with errors:%+v", err)
		return rsp, err
	}

	// 查询 CatalogIds 相关资源
	err = metaExternalDatabaseService.getMetaRelatedInfo()
	if err != nil {
		return
	}

	if meta.Type == constants.METADATA_CATALOG_TYPE_HIVE {
		// CRD Build req
		err = metaExternalDatabaseService.buildQueryMetaHiveDatabaseReq()
		if err != nil {
			return
		}
	}

	if meta.Type == constants.METADATA_CATALOG_TYPE_MYSQL {
		// CRD Build req
		err = metaExternalDatabaseService.buildQueryMetaMysqlDatabaseReq()
		if err != nil {
			return
		}
	}

	tms.fillData(metaExternalDatabaseService)
	// query
	rsp, err = tms.crdQueryTable()
	if err != nil {
		return
	}
	return rsp, nil
}

func (tms *DoDescribeMetaExternalTableService) DoDescribeMetaExternalTableInternal(req *model.DescribeExternalMetaTablesReq, evenId int64) (rsp *model.DescribeExternalMetaTablesRsp, err error) {

	if tms.Request.CatalogId == "" {
		err = errorcode.InvalidParameterCode.ReplaceDesc("CatalogIds can not be null .")
		return
	}
	meta, err := GetMetaCatalogFormSerialId(tms.Request.CatalogId, tms.Request.AppId, tms.Request.Region)
	if err != nil {
		logger.Errorf(" InnerAuth query  ItemSpaces error: %+v", err)
		return
	}
	if meta == nil {
		err = errorcode.InvalidParameterCode.ReplaceDesc("Catalog not exists.")
		return
	}

	//Auth鉴权
	itemSpcIds, err := auth.ExtractSubUinAuthItemSpaceIdSet(req.WorkSpaceId, req.IsSupOwner, req.AppId, req.SubAccountUin, req.Uin, req.Region)
	if err != nil {
		logging.Errorf("%s: ExtractSubUinAuthItemSpaceIdSet : Obtain catalog that has permissions   error: %+v", req.RequestId, err)
		return rsp, err
	}
	metaExternalDatabaseService := DoDescribeMetaExternalDatabaseService{Request: &model.DescribeExternalMetaDatabasesReq{RequestBase: req.RequestBase, CatalogId: req.CatalogId,
		ClusterId: req.ClusterId, FlinkVersion: req.FlinkVersion}}

	// 查询资源
	err = metaExternalDatabaseService.getMetaCatalogResourceRef(itemSpcIds)
	if err != nil {
		logging.Errorf("Failed to query meta table , with errors:%+v", err)
		return rsp, err
	}

	// 查询 CatalogIds 相关资源
	err = metaExternalDatabaseService.getMetaRelatedInfo()
	if err != nil {
		return
	}
	if meta.Type == constants.METADATA_CATALOG_TYPE_HIVE {
		// CRD Build req
		err = metaExternalDatabaseService.buildQueryMetaHiveDatabaseReq()
		if err != nil {
			return
		}
	}
	if meta.Type == constants.METADATA_CATALOG_TYPE_MYSQL {
		// CRD Build req
		err = metaExternalDatabaseService.buildQueryMetaMysqlDatabaseReq()
		if err != nil {
			return
		}
	}
	tms.fillData(metaExternalDatabaseService)
	rsp = &model.DescribeExternalMetaTablesRsp{}
	if meta.Type == constants.METADATA_CATALOG_TYPE_HIVE {
        tms.filHiveRsp(rsp)
	} else if meta.Type == constants.METADATA_CATALOG_TYPE_MYSQL {
		tms.filMysqlRsp(rsp)
	}

	return rsp, nil
}

func (tms *DoDescribeMetaExternalTableService) filHiveRsp(rsp *model.DescribeExternalMetaTablesRsp) {
	table2s := make([]*model.Table2, 0)
	table2 := &model.Table2{}
	table2.TableName = tms.Request.TableName
	table2.TableType = "hive"
	table2.CatalogId = tms.Request.CatalogId
	table2.Catalog = "_dc"
	table2.Database = tms.Request.DatabaseName
	tableProperties := &model.TableProperties{
		Connector: "hive",
	}
	jsonData, _ := json.Marshal(tableProperties)
	table2.TableProperties = string(jsonData)
	table2s = append(table2s, table2)
	rsp.TablesSetItem = table2s
}

func (tms *DoDescribeMetaExternalTableService) filMysqlRsp(rsp *model.DescribeExternalMetaTablesRsp) {
	table2s := make([]*model.Table2, 0)
	table2 := &model.Table2{}
	table2.TableName = tms.Request.TableName
	table2.TableType = "jdbc"
	table2.CatalogId = tms.Request.CatalogId
	table2.Catalog = "_dc"
	table2.Database = tms.Request.DatabaseName
	tableProperties := &model.TableProperties{
		Connector: "jdbc",
		Password: tms.MysqlCatalog.Password,
		TableName: tms.Request.TableName,
		Username: tms.MysqlCatalog.Username,
		Url: fmt.Sprintf("jdbc:mysql://%s:%d/%s", tms.MysqlCatalog.Hostname, tms.MysqlCatalog.Port, tms.Request.DatabaseName),
	}
	jsonData, _ := json.Marshal(tableProperties)
	table2.TableProperties = string(jsonData)
	table2s = append(table2s, table2)
	rsp.TablesSetItem = table2s
}

func filRsp(rsp *model.DescribeExternalMetaTablesRsp, tables []*model.Table, catalogId string, catalog string) {
	table2s := make([]*model.Table2, 0)
	for _, table := range tables {
		table2 := &model.Table2{}
		table2.TableName = table.TableName
		table2.TableType = table.TableType
		table2.CatalogId = catalogId
		table2.Catalog = catalog
		table2.Database = table.Database
		TableSchema2s := make([]*model.TableSchema2, 0)
		for _, schema := range table.Columns {
			TableSchema2 := &model.TableSchema2{}
			TableSchema2.Name = schema.Name
			TableSchema2.DataType = schema.DataType
			TableSchema2s = append(TableSchema2s, TableSchema2)
		}
		table2.Columns = TableSchema2s
		table2.TableProperties = table.TableProperties
		table2s = append(table2s, table2)
	}
	rsp.TablesSetItem = table2s
}

func GetMetaCatalogFormSerialId(serialId string, appId int64, region string) (metadbs *table.MetastoreCatalog, err error) {

	args := make([]interface{}, 0, 0)
	args = append(args, serialId)
	args = append(args, appId)
	args = append(args, region)

	sql := "SELECT * from MetastoreCatalog WHERE SerialId = ? and AppId = ? and Region = ? "
	cnt, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)

	if cnt == 0 {
		return nil, nil
	}

	metadb := &table.MetastoreCatalog{}
	err = util.ScanMapIntoStruct(metadb, data[0])
	if err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}
	return metadb, nil
}

func (tms *DoDescribeMetaExternalTableService) crdQueryTable() (rsp *model.DescribeExternalMetaTablesRsp, err error) {
	logging.Infof("CheckSqlGrammar %v", tms.VpcReq)
	ssReq := &model.SqlServerDescribeTbsReq{
		SqlServerBaseReq: &model.SqlServerBaseReq{},
	}
	ssReq.Version = "1.0"
	ssReq.Callee = "DEV"
	ssReq.Password = "DEV"
	ssReq.Caller = "DEV"
	ssReq.EventID = 11
	ssReq.Timestamp = util.GetNowTimestamp()
	ssReq.RequestId = tms.Request.RequestId

	ssReq.Interface.Para.FlinkClientVersion = tms.VpcReq["flinkClientVersion"].(string)
	ssReq.Interface.Para.Cam = tms.VpcReq["cam"]
	ssReq.Interface.Para.ResoureceLocs = tms.VpcReq["resoureceLocs"]
	ssReq.Interface.Para.CatalogName = tms.VpcReq["catalogName"].(string)
	ssReq.Interface.Para.DefaultDatabase = tms.VpcReq["defaultDatabase"].(string)
	if _, exists := tms.VpcReq["hiveConfDir"]; exists {
		ssReq.Interface.Para.HiveConfDir = tms.VpcReq["hiveConfDir"].(string)
	}
	if _, exists := tms.VpcReq["hiveVersion"]; exists {
		ssReq.Interface.Para.HiveVersion = tms.VpcReq["hiveVersion"].(string)
	}
	ssReq.Interface.Para.Database = tms.VpcReq["database"].(string)
	ssReq.Interface.Para.Table = tms.VpcReq["table"].(string)

	if tms.CatalogType == constants.METADATA_CATALOG_TYPE_MYSQL {
		ssReq.Interface.Para.Username = tms.VpcReq["username"].(string)
		ssReq.Interface.Para.Password = tms.VpcReq["password"].(string)
		ssReq.Interface.Para.Hostname = tms.VpcReq["hostname"].(string)
		ssReq.Interface.Para.Port = tms.VpcReq["port"].(int)
	}

	byteSsReq, err := json.Marshal(ssReq)
	if err != nil {
		msg := fmt.Sprintf("%s Error converting SendData", tms.Request.RequestId)
		logging.Error(msg)
		return nil, errors.New(msg)
	}

	if tms.CatalogType == constants.METADATA_CATALOG_TYPE_MYSQL {
		stringSsReq, err := util.ObjectToString(ssReq)
		if err != nil {
			msg := fmt.Sprintf("%s Error converting SendData", tms.Request.RequestId)
			logging.Error(msg)
			return nil, errors.New(msg)
		}
		byteSsReq = []byte(stringSsReq)
	}

	clusterGroup, err := service1.GetClusterGroupByClusterId(tms.ClusterId)
	if err != nil {
		logging.Errorf("%d Failed to GetClusterGroupByClusterId ,err:%s", tms.ClusterId, err.Error())
		return nil, err
	}

	action := constants.METADATA_DESCRIBEHIVETBS_ACTION
	if tms.CatalogType == constants.METADATA_CATALOG_TYPE_MYSQL {
		action = constants.METADATA_DESCRIBEMYSQLTBS_ACTION
	}

	sendData, url, err := ConvertRequestSSToCS(byteSsReq, action, tms.Request.RequestId, tms.ClusterId, clusterGroup)
	if err != nil {
		return nil, err
	}

	logging.Info("===ClusterId")
	logging.Info(tms.ClusterId)
	commandService, err := commandc.NewCommandService(tms.ClusterId)
	if err != nil {
		return nil, err
	}
	ssRsp, err := commandService.DoRequest(&commandc.CommandRequest{
		ReqId:    tms.Request.RequestId,
		Url:      url,
		SendData: sendData,
		Uin:      tms.Request.Uin,
		Apikey:   "DescribeMetaTables",
		Timeout:  getTimeout(),
		Callback: "",
	})
	logging.Info("===")
	logging.Info(ssRsp)

	response, err := ConvertResponseSSToCS(ssRsp, tms.Request.RequestId, clusterGroup)
	if err != nil {
		logging.Errorf("Error call sql check %v", err)
		return nil, errors.New("Error call sql check. ")
	}

	vpcTbsRsp := &model.DescribeMetaExternalTableVpcRsp{}
	err = json.Unmarshal([]byte(response), vpcTbsRsp)
	if err != nil {
		logging.Errorf("Error parsing response: %v ret: %v", ssRsp, err)
		return nil, errors.New("Failed to parse sql server response. ")
	}

	if err = tms.handlerException(vpcTbsRsp.ReturnCode, tms.Request.RequestId, vpcTbsRsp.ReturnMsg); err != nil {
		return nil, err
	}

	rsp = &model.DescribeExternalMetaTablesRsp{}

	filRsp(rsp, vpcTbsRsp.Data.Tables, tms.Request.CatalogId, tms.CatalogName)
	return rsp, nil
}


func (tms *DoDescribeMetaExternalTableService) fillData(metaExternalDatabaseService DoDescribeMetaExternalDatabaseService) {
	tms.HiveVersion = metaExternalDatabaseService.HiveVersion
	tms.CatalogName = metaExternalDatabaseService.CatalogName
	tms.DefaultDatabase = metaExternalDatabaseService.DefaultDatabase
	tms.ResourceRefs = metaExternalDatabaseService.ResourceRefs
	tms.MetaCatalogHiveResources = metaExternalDatabaseService.MetaCatalogResources
	tms.HiveConfDir = metaExternalDatabaseService.HiveConfDir
	tms.ResourceLocs = metaExternalDatabaseService.ResourceLocs
	tms.Cam = metaExternalDatabaseService.Cam
	tms.ClusterId = metaExternalDatabaseService.ClusterId
	tms.CatalogType = metaExternalDatabaseService.CatalogType
	tms.VpcReq = metaExternalDatabaseService.VpcReq
	tms.VpcReq["database"] = tms.Request.DatabaseName
	tms.VpcReq["table"] = tms.Request.TableName
	tms.MysqlCatalog = metaExternalDatabaseService.MysqlCatalog
}

func (tms *DoDescribeMetaExternalTableService) handlerException(returnCode int64, reqId string, returnMsg string) error {
	if returnCode != 0 {
		msg := fmt.Sprintf("%s request done but return exception : %s", reqId, returnMsg)
		logging.Error(msg)
		switch returnCode {
		case -10005:
			return errorcode.InvalidParameter_MetaNetworkError.ReplaceDesc(returnMsg)
		case -10006:
			return errorcode.InvalidParameter_DatabaseNotExist.ReplaceDesc(returnMsg)
		default:
			return errorcode.InvalidParameterCode.ReplaceDesc(returnMsg)
		}
	}
	return nil
}

type ServerCSPara struct {
	Params          Params `json:"params"`
	ClusterId       int64  `json:"clusterId"`
	Action          int    `json:"action"`
	ClusterSerialId string `json:"clusterSerialId"`
}
type Params struct {
	CommonRequest string `json:"commonRequest"`
	RequestId     string `json:"requestId"`
}

type ServerCSRsp struct {
	cluster_admin_protocol.QaeResponse
	Data struct {
		cluster_admin_protocol.ClusterAdminResponse
		Params struct {
			CommonResponse string `json:"commonResponse"`
		} `json:"params"`
	} `json:"data"`
}

func ConvertResponseSSToCS(ssResponse string, requestId string, clusterGroup *table4.ClusterGroup) (string, error) {

	logging.Debugf("%s #####ConvertResponseSSToCS ssResponse %s ", requestId, ssResponse)
	if clusterGroup.AgentSerialId == "" {
		return ssResponse, nil
	} else {
		serverCSRsp := &ServerCSRsp{}
		err := json.Unmarshal([]byte(ssResponse), serverCSRsp)
		if err != nil {
			msg := fmt.Sprintf("%s ConvertResponseSSToCS Error parsing response: ret: %s, error: %v", requestId, ssResponse, err)
			logging.Errorf(msg)
			return "", err
		}
		logging.Debugf("%s #####ConvertResponseSSToCS ssResponse %v ", requestId, serverCSRsp)
		// 6. 返回结果
		// 6.1 CA端异常返回
		if serverCSRsp.Data.ReturnCode != 0 {
			logging.Errorf("%s call cs checksql failed, errMsg is %s", requestId, serverCSRsp.Data.ReturnMsg)
			return "", errorcode.FailedOperationCode.ReplaceDesc(serverCSRsp.Data.ReturnMsg)
		}
		return serverCSRsp.Data.Params.CommonResponse, nil
	}
}

func ConvertRequestSSToCS(ssReq []byte, action int, requestId string, clusterId int64, clusterGroup *table4.ClusterGroup) (string, string, error) {
	urlPath := constants.SQL_SERVER_PATH_MAP[action]
	logging.Debugf("%s #### urlPath: %s ", requestId, urlPath)

	if clusterGroup.AgentSerialId == "" {
		url := fmt.Sprintf("http://%s:%s%s", constants.SqlServerServiceName, strconv.Itoa(constants.SqlServerPort), urlPath)
		logging.Errorf("%s #### url: %s ", requestId, url)
		return string(ssReq), url, nil
	} else {

		// 共享资源池调用CS转发
		para := &ServerCSPara{
			Params: Params{
				CommonRequest: string(ssReq),
				RequestId:     requestId,
			},
			ClusterId:       clusterId,
			Action:          action,
			ClusterSerialId: clusterGroup.SerialId,
		}
		csReq := cluster_admin_protocol.NewClusterAdminReq(requestId, "qcloud.cs.sqlserver", para)
		logging.Debugf("%s #### csReq: %v ", requestId, csReq)
		data, err := json.Marshal(csReq)
		if err != nil {
			msg := fmt.Sprintf("%s Error converting SendData", requestId)
			logging.Error(msg)
			return "", "", errors.New(msg)
		}
		logging.Debugf("%s #### data: %s ", requestId, data)
		url, err := commandc.GetCAOrCSUrl(&table4.Cluster{
			Id: clusterId,
		})
		logging.Debugf("%s #### url: %s ", requestId, url)
		if err != nil {
			msg := fmt.Sprintf("Error commandService.GetCAOrCSUrl, error: %v", err)
			logging.Error(msg)
			return "", "", errors.New(msg)
		}
		return string(data), url, nil
	}
}
