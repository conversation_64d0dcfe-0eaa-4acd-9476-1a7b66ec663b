package service

import (
	"testing"
	"time"

	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_config"
	cluster "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	tableInstance "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job_instance"
	cosService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cos/handler"
)

func Test_buildJobDependencyPath(t *testing.T) {
	type args struct {
		jobInstance *tableInstance.JobInstance
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
		{
			name: "test-case-1",
			args: args{
				jobInstance: &tableInstance.JobInstance{
					ApplicationId: "cql-jhe31k4a-2867",
				},
			},
			want: "user/user_00/.flink/cql-jhe31k4a-2867",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := buildJobDependencyPath(tt.args.jobInstance); got != tt.want {
				t.Errorf("buildJobDependencyPath() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestDeleteCOSFile(t *testing.T) {
	type args struct {
		jobInstances []*tableInstance.JobInstance
		req          *model.DeleteJobConfigsReq
		cluster      *cluster.Cluster
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test-case-1",
			args: args{
				jobInstances: makeJobInstance(),
				req: &model.DeleteJobConfigsReq{
					Region: "ap-beijing",
				},
				cluster: &cluster.Cluster{
					DefaultCOSBucket: "kkingtest-1257058945",
				},
			},
			wantErr: false,
		},
	}
	cosService.GetHandler().Start()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := DeleteCOSFile(tt.args.jobInstances, tt.args.req, tt.args.cluster); (err != nil) != tt.wantErr {
				t.Errorf("DeleteCOSFile() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
	time.Sleep(time.Duration(2000))
}

func makeJobInstance() []*tableInstance.JobInstance {
	jobInstances := make([]*tableInstance.JobInstance, 0)
	jobInstances = append(jobInstances, &tableInstance.JobInstance{
		ApplicationId: "cql-1bkp40wu-2407457",
	})
	return jobInstances
}
