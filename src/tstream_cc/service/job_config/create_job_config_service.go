package service

import (
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"tencentcloud.com/tstream_galileo/src/common/dlocker"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/log"

	"github.com/juju/errors"
	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service2 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	model3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model"
	mode2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/bucket"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_config"
	sql3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/sql"
	sql_model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/sql"
	table3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job_config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/bucket"
	service5 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cos"
	service6 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/etl"
	service7 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/etl_parser"
	service_meta "tencentcloud.com/tstream_galileo/src/tstream_cc/service/metadata"
	service_metadata "tencentcloud.com/tstream_galileo/src/tstream_cc/service/metadata"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/quota"
	service4 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource"
	sql2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/sql"
	service8 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
	service_variable "tencentcloud.com/tstream_galileo/src/tstream_cc/service/variable"
)

func buildCheckSqlGrammarReq(req *model.CreateJobConfigReq, clusterId string, sqlCode string) *sql3.CheckSqlGrammarReq {
	chkReq := &sql3.CheckSqlGrammarReq{}
	chkReq.ResourceRefs = req.ResourceRefs
	chkReq.Region = req.Region
	chkReq.Version = req.Version
	chkReq.ClusterId = clusterId
	chkReq.AppId = int64(req.AppId)
	chkReq.JobId = req.JobId
	chkReq.RequestId = req.RequestId
	chkReq.SqlCode = sqlCode
	chkReq.SubAccountUin = req.SubAccountUin
	chkReq.Uin = req.Uin
	chkReq.IsSupOwner = req.IsSupOwner
	chkReq.Action = req.Action
	chkReq.WorkSpaceId = req.WorkSpaceId
	return chkReq
}

type createJobConfigContext struct {
	req                *model.CreateJobConfigReq
	job                *table.Job
	programElements    string
	checkpointInterval int64
	sqlCode            string
}

/**
 *
 * <AUTHOR>
 * @time 2019/4/29
 * @version 0.4.0
 * @since 0.4.0
 * @see
 */
func DoCreateJobConfig(req *model.CreateJobConfigReq) (rsp *model.CreateJobConfigRsp, err error) {

	// 0. 获取作业的作业的类型
	job, err := service.WhetherJobExists(req.AppId, req.Region, req.JobId)
	if err != nil {
		return nil, err
	}

	// 1. 新版 EtlJob 画布作业入参ProgramArgs的key为 EtlJobCanvas，并且为 base64 格式，这里将其转为Json
	req.ProgramArgs, err = ParseProgramArgsBase64ToJson(req.ProgramArgs, job.Type)
	if err != nil {
		return nil, err
	}

	ctx, err := checkCreateJobConfigReq(req)
	if err != nil {
		return nil, err
	}

	//  参数都验证ok后，检查作业配置数量是否超过配额
	err = checkQuota(ctx)
	if err != nil {
		return nil, err
	}

	// 鉴权
	err = auth.InnerAuthById(req.WorkSpaceId, req.IsSupOwner, ctx.job.ItemSpaceId, int64(req.AppId), req.SubAccountUin, req.Action, false)
	if err != nil {
		logger.Errorf("%s: User  uin %s is NOT authenticated, refuse to serve", req.RequestId, req.Uin)
		return nil, err
	}
	flinkVersion := ctx.job.FlinkVersion

	if req.FlinkVersion != "" {
		flinkVersion = req.FlinkVersion
	}
	req.FlinkVersion = flinkVersion

	// 6.自动匹配内置Connector
	var innerConnectorRefLocs = make([]*sql_model.ResourceLocationIntegral, 0)
	var tableLineage = &sql_model.DescribeTableLineageRspV1{Success: false}
	if ctx.job.Type == constants.JOB_TYPE_SQL {

		clusterGroup, err := service5.ListClusterGroupById(ctx.job.ClusterGroupId)
		if err != nil {
			logger.Errorf("Failed to get  ClusterGroup by id: %d, with errors: %+v", ctx.job.ClusterGroupId, err)
			return nil, err
		}

		sqlServer, err := sql2.NewSqlServerService(int64(req.AppId), clusterGroup.SerialId, req.RequestId, req.Uin, req.Region, flinkVersion)
		if err != nil {
			return nil, errorcode.FailedOperationCode_ParseSql.NewWithErr(err)
		}
		parseSqlService := sql2.NewParseSqlService(req.RequestId, req.Uin, int64(req.AppId), req.Region, flinkVersion, clusterGroup.SerialId, ctx.job.ItemSpaceId)
		deEncryptSql, err := DeEncodeAndDeEncryptSqlCode(ctx.sqlCode)
		if err != nil {
			return nil, err
		}
		base64Sql := base64.StdEncoding.EncodeToString([]byte(deEncryptSql))
		if sqlServer.FoundSatisfiedCluster { // 未部署SqlServer则不进行自动匹配
			// 解析SQL，获取表和元数据信息
			parseSqlRsp, err := parseSqlService.ParseSql(base64Sql, req.Region, 13)
			if err != nil {
				return nil, errorcode.FailedOperationCode_ParseSql.NewWithErr(err)
			}
			if !parseSqlRsp.Pass {
				return nil, errorcode.FailedOperationCode_ParseSql.ReplaceDesc(parseSqlRsp.ErrorMessage)
			}
			// 提取元数据表和ExternalCatalog
			metaTables, exCatalogs, err := parseSqlService.GetMetaTables(parseSqlRsp.MetaRefs, ctx.job.ItemSpaceId)
			if len(exCatalogs) > 0 { // 如果解析到ExternalCatalog,将其添加到作业参数中
				resultStr, err := AddExCatalogToProgramArgs(ctx.programElements, exCatalogs)
				if err != nil {
					return nil, errorcode.FailedOperationCode_ParseSql.NewWithErr(err)
				}
				ctx.programElements = resultStr
			}
			userDefinedConnectors := make([]string, 0)
			if req.ResourceRefs != nil || len(req.ResourceRefs) > 0 { //如果有选择程序包，判断是否有内置Connector，将内置的过滤
				locs, err := sql2.GetResourceLoc(req.ResourceRefs)
				if err != nil {
					logger.Errorf("%s: Failed to save GetResourceLoc, error: %+v", req.RequestId, err)
					return nil, errorcode.FailedOperationCode_ParseSql.NewWithErr(err)
				}
				analyzeUserJars, _ := sqlServer.AnalyzeUserJar(locs, req.JobId)
				if analyzeUserJars != nil {
					userDefinedConnectors = analyzeUserJars.UserDefinedConnectors
				}
			}
			// 从SQL解析结果中提取内置的Connector
			innerConnectorRefLocs, err = parseSqlService.GetResourceRefsFromMeta(metaTables, parseSqlRsp.MetaRefs, userDefinedConnectors, exCatalogs)
			if err != nil {
				logger.Errorf("%s: Failed to save GetResourceLoc, error: %+v", req.RequestId, err)
				return nil, errorcode.FailedOperationCode_ParseSql.NewWithErr(err)
			}
		}
		tableLineage, err = parseSqlService.DescribeTableLineage(base64Sql, req.Region, 15)
		if err != nil {
			logger.Errorf("%s: Failed to DescribeTableLineage, error: %+v", req.RequestId, err)
			return nil, errorcode.FailedOperationCode_ParseSql.NewWithErr(err)
		}

	}
	// 7. 保存作业配置
	jobConfigId, versionId, err := CreateJobConfig(ctx.job, req, ctx.sqlCode, ctx.checkpointInterval, ctx.programElements)
	if err != nil {
		return nil, err
	}
	//8.保存作业与元数据表的关联关系
	if ctx.job.Type == constants.JOB_TYPE_SQL {
		commands, _, _, _, err := sql2.BuildMetadata4Command(ctx.programElements, ctx.sqlCode, req.Uin, int64(req.AppId),
			req.Region, flinkVersion, ctx.job.ItemSpaceId, false, nil)
		err = service_meta.SaveJobMetaTableRef(commands, ctx.job.Id, jobConfigId, req.Uin, int64(req.AppId), req.Region, ctx.job.ItemSpaceId)
		if err != nil {
			logger.Errorf("%s: Failed to SaveJobMetaTableRef, error: %+v", req.RequestId, err)
			return nil, errorcode.FailedOperationCode.NewWithErr(err)
		}
		err = service_variable.SaveJobVariableRef(req.ProgramArgs, jobConfigId, req.Uin, int64(req.AppId), req.Region)
		if err != nil {
			logger.Errorf("%s: Failed to save VariableReference, error: %+v", req.RequestId, err)
			return nil, errorcode.FailedOperationCode.NewWithErr(err)
		}
	}
	if tableLineage.Success {
		err := service_metadata.SaveMetaTableLineage(tableLineage, ctx.job.Id, jobConfigId, req.Uin, req.SubAccountUin, int64(req.AppId), req.Region, ctx.job.ItemSpaceId)
		if err != nil {
			logger.Errorf("%s: Failed to save ineage(table, error: %+v", req.RequestId, err)
			return nil, errorcode.FailedOperationCode.NewWithErr(err)
		}
	}
	// 9. 创建资源引用
	// 9.1 程序包
	if req.ResourceRefs != nil || len(req.ResourceRefs) > 0 {
		success, err := service4.DoCreateResourceRefs(req, jobConfigId)
		if err != nil {
			logger.Errorf("%s: Failed to create Resource Refs, error: %+v", req.RequestId, err)
			return nil, errorcode.InternalErrorCode.NewWithErr(err)
		}

		if !success {
			logger.Errorf("%s: Failed to create Resource Refs, SUCCESS: %+v", req.RequestId, success)
			return nil, errorcode.InternalErrorCode.NewWithErr(err)
		}
	}
	// 9.2 自动关联Connector模式下，创建表(临时表、元数据表)资源引用
	if len(innerConnectorRefLocs) > 0 {
		resourceRefs := make([]*model.ResourceRefItem, 0)
		reqResourceSet := make(map[string]bool)
		if req.ResourceRefs != nil || len(req.ResourceRefs) > 0 {
			for _, ref := range req.ResourceRefs {
				reqResourceSet[ref.ResourceId] = true
			}
		}
		items := service_metadata.BuildResourceRefItems(innerConnectorRefLocs)
		for _, loc := range items {
			if _, ok := reqResourceSet[loc.ResourceId]; !ok {
				resourceRefs = append(resourceRefs, &model.ResourceRefItem{
					ResourceId: loc.ResourceId,
					Version:    loc.Version,
					Type:       loc.Type,
				})
			}
		}

		if req.UseOldSystemConnector == constants.UseOldSysConnector_ENABLE {
			replaceOldSystemConnector(ctx.job, resourceRefs)
		}
		req2SaveRefs := &model.CreateJobConfigReq{
			ResourceRefs: resourceRefs,
			AppId:        req.AppId,
			RequestId:    req.RequestId,
		}
		success, err := service4.DoCreateResourceRefs(req2SaveRefs, jobConfigId)
		if err != nil {
			logger.Errorf("%s: Failed to create Resource Refs, error: %+v", req.RequestId, err)
			return nil, err
		}

		if !success {
			logger.Errorf("%s: Failed to create Resource Refs, SUCCESS: %+v", req.RequestId, success)
			return nil, err
		}

	}

	if err = service3.AssignJobBucket(buildCreateJobBucketRefReqWithoutBucket(req)); err != nil {
		logger.Errorf("%s: Failed to AssignJobBucket error: %+v", req.RequestId, err)
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}

	createJobConfigRsp := &model.CreateJobConfigRsp{RequestId: req.RequestId, Version: versionId}

	return createJobConfigRsp, nil
}

func replaceOldSystemConnector(job *table.Job, resourceRefs []*model.ResourceRefItem) {
	// 获取旧版本作业配置ID
	var jobConfigId int64
	if job.PublishedJobConfigId != -1 {
		jobConfigId = job.PublishedJobConfigId
	} else if job.LatestJobConfigVersionId != -1 {
		jobConfigId = int64(job.LatestJobConfigVersionId)
	} else {
		return
	}

	logger.Infof("replaceOldSystemConnector jobConfigId: %d", jobConfigId)
	// 获取旧版本作业配置
	jobConfig, err := service2.GetJobConfigById(jobConfigId)
	if err != nil {
		logger.Errorf("Failed to get job config by id: %d, with errors: %+v", jobConfigId, err)
		return
	}

	// 获取旧版本资源引用
	oldRefs, err := service4.GetResourceRefByJobConfigId(jobConfig.Id)
	if err != nil {
		logger.Errorf("Failed to get resource ref by job config id: %d, with errors: %+v", jobConfig.Id, err)
		return
	}

	// 创建旧版本资源引用的映射，用于快速查找
	oldRefMap := make(map[string]int64)
	for _, ref := range oldRefs {
		oldRefMap[ref.ResourceId] = ref.Version
	}

	// 遍历新版本资源引用，如果找到匹配的旧版本，则替换版本号
	for i := range resourceRefs {
		if oldVersion, exists := oldRefMap[resourceRefs[i].ResourceId]; exists {
			resourceRefs[i].Version = oldVersion
		}
	}
}

func checkLogCollectParam(req *model.CreateJobConfigReq) error {
	if req.LogCollect && req.LogCollectType == constants.JobLogCollectTypeCLS && (req.ClsLogsetId == "" || req.ClsTopicId == "") {
		// 采集日志到CLS，但CLS相关信息为空
		return errorcode.InvalidParameter_JobConfigLogCollectParamError.ReplaceDesc("Create job config error, ClsLogsetId or ClsTopicId not valid.")
	} else if req.LogCollect && req.LogCollectType == constants.JobLogCollectTypeCOS && req.COSBucket == "" {
		// 采集日志到COS，但COS相关信息为空
		return errorcode.InvalidParameter_JobConfigLogCollectParamError.ReplaceDesc("Create job config error, bucket not valid.")
	} else {
		return nil
	}
}

func checkQuota(ctx *createJobConfigContext) error {
	jobConfigCount, err := GetJobConfigCount(ctx.job.Id)
	if err != nil {
		return err
	}
	overLimit, msg, err := quota.NewQuota().OverLimit("", int64(ctx.req.AppId), quota.JobConfig, int32(jobConfigCount))
	if err != nil {
		return err
	}
	if overLimit {
		if ctx.req.AutoDelete == 0 {
			errMsg := fmt.Sprintf("Job [%s] jobConfig %s", ctx.req.JobId, msg)
			logger.Warningf("%s: Job config exceed the limit: %d, with job id: %d", ctx.req.RequestId,
				constants.JOB_CONFIG_LIMIT_PER_JOB, ctx.job.Id)
			return errorcode.LimitExceededCode.ReplaceDesc(errMsg)
		}
		ok, err := DeleteEarliestCanBeDelJobConfig(ctx.job)
		if err != nil {
			return errorcode.InternalErrorCode.New()
		}
		if !ok {
			return errorcode.LimitExceededCode.ReplaceDesc("not job config can't be delete")
		}
	}
	return nil
}

func checkCreateJobConfigReq(req *model.CreateJobConfigReq) (ctx *createJobConfigContext, err error) {
	ctx = &createJobConfigContext{req: req}
	if err = checkLogCollectParam(req); err != nil {
		logger.Errorf("checkLogCollectParam with error [%v]", err)
		return nil, err
	}
	// 1. 检查作业是否存在
	ctx.job, err = service.WhetherJobExists(req.AppId, req.Region, req.JobId)
	if err != nil {
		return nil, err
	}
	ctx.programElements, ctx.checkpointInterval, ctx.sqlCode, err = ExtractElementsFromProgramArgs(req.ProgramArgs, ctx.job.Type)
	if err != nil {
		logger.Errorf("%s: Failed to extractElements from programArgs, with errors: %+v", req.RequestId, err)
		return nil, err
	}

	//如果是ETL作业，那么需要进行额外的逻辑
	if ctx.job.Type == constants.JOB_TYPE_ETL {
		ctx.sqlCode, err = checkETLParams(req, ctx.job)
		if err != nil {
			return nil, err
		}
	}

	cluster, err := service5.GetActiveClusterByClusterGroupId(ctx.job.ClusterGroupId)
	if err != nil {
		return nil, err
	}

	tkeClusterType := -1
	if cluster.SchedulerType != constants.CLUSTER_SCHEDULER_TYPE_EMR {
		tkeClusterType, err = service8.GetTableService().GetTkeClusterType(cluster.Id)
		if err != nil {
			logger.Errorf("GetTkeClusterType failed for cluster id %s", cluster.Id)
			return nil, err
		}
	}

	err = CheckJobConfigProperties(tkeClusterType, int64(req.AppId), req.Properties, req.DefaultParallelism)
	if err != nil {
		return nil, err
	}

	// 如果是SQL类型Job，检查CheckpointInterval范围
	if ctx.job.Type == constants.JOB_TYPE_SQL || ctx.job.Type == constants.JOB_TYPE_ETL {
		if !auth.IsInWhiteList(int64(req.AppId), constants.WHITE_LIST_SUPPORT_LOW_INTERVAL_CHECK_POINT) {
			err = service.CheckCheckpointIntervalValid(ctx.checkpointInterval)
			if err != nil {
				logger.Errorf("%s: Failed to create JobConfig, %s", req.RequestId, err.Error())
				return nil, err
			}
		} else {
			if ctx.checkpointInterval < constants.WHITE_LIST_CHECKPOINT_INTERVAL_LOWER_LIMIT && ctx.checkpointInterval > 0 {
				err = errors.New(fmt.Sprintf("checkpointInterval must be greater than 1, but got %d", ctx.checkpointInterval))
				return nil, err
			}
		}
	}

	// 2. 根据JobType检查 JarFileLocation，0.4.0用不到，0.5.0再加检查逻辑
	// 2. 检查 引用资源信息
	isInnerUser, err := service.IsInnerUser(req.AppId, req.Uin, req.SubAccountUin)
	if err != nil {
		logger.Errorf("%s: IsInnerUser error: %+v", req.RequestId, err)
		return nil, err
	}
	// 智能监控用户，不检查资源引用情况，因为他们的资源都是在本地的
	if !isInnerUser {
		ok, mainResourceCount := CheckResourceRefs(ctx.job, req.ResourceRefs)
		if !ok {
			errMsg := fmt.Sprintf("main ResourceRefs count %d invalid", mainResourceCount)
			logger.Errorf("%s: %s", req.RequestId, errMsg)
			return nil, errorcode.InvalidParameterCode.ReplaceDesc(errMsg)
		}
	}

	// 3. 根据JobType检查 EntrypointClass
	err = CheckCompositeParameters(ctx.job, ctx.sqlCode, req.EntrypointClass)
	if err != nil {
		return nil, err
	}

	// 4. 检测 COS Bucket 是否存在且可读写（空值跳过） 同时校验集群是否是 TKE 集群
	// TODO: 目前受限于 ClusterAdmin 还未支持单作业设置 COSBucket, 该功能暂时无效
	if req.COSBucket != "" &&
		cluster.SchedulerType != constants.CLUSTER_SCHEDULER_TYPE_TKE {
		return nil, err
	}
	err = cos.CheckWhetherCOSBucketExists(req.Uin, req.SubAccountUin, req.Region, req.COSBucket)
	if err != nil {
		return nil, err
	}

	// 检查 es index status ready
	err = CheckEsIndexReady(req.LogCollectType, req.EsServerlessIndex, req.EsServerlessSpace, cluster)
	if err != nil {
		return nil, err
	}

	if req.JobManagerCpu == 0 {
		if ok, err := CheckPodSpecs(req.AppId, cluster, req.JobManagerSpec, req.TaskManagerSpec); err != nil {
			return nil, err
		} else if !ok {
			err = errorcode.InvalidParameterValueCode.ReplaceDesc(fmt.Sprintf(
				"PodSpec(%f,%f) not supported", req.JobManagerSpec, req.TaskManagerSpec))
			return nil, err
		}
	} else {
		if ok, err := CheckCpuMem(req.AppId, cluster, req.JobManagerCpu, req.JobManagerMem, req.TaskManagerCpu, req.TaskManagerMem); err != nil {
			return nil, err
		} else if !ok {
			err = errorcode.InvalidParameterValueCode.ReplaceDesc(fmt.Sprintf(
				"Pod Cpu Mem(%f, %f, %f, %f) not valid", req.JobManagerCpu, req.JobManagerMem, req.TaskManagerCpu, req.TaskManagerMem))
			return nil, err
		}
	}

	//sql作业的高级配置才需要检查configuration
	if req.ExpertModeOn && ctx.job.Type == constants.JOB_TYPE_SQL {
		if err = ValidateExpertModeConfiguration(req.ExpertModeConfiguration); err != nil {
			return ctx, err
		}
	}
	return ctx, err
}

func CheckEsIndexReady(logCollectType int8, esServerlessIndex string, esServerlessSpace string, cluster *table3.Cluster) (error error) {
	if logCollectType != constants.JobLogCollectTypeES {
		return nil
	}
	if esServerlessIndex == "" || esServerlessSpace == "" {
		return errors.New("EsServerlessIndex or EsServerlessSpace  is null, please check")
	}
	clusterLogconfig := cluster.LogConfig
	if clusterLogconfig == "" {
		return errors.New("Cluster.logConfig is null, please check")
	}
	esServelessLogConf := &log.ClusterEsServelessLogConf{}
	err := json.Unmarshal([]byte(clusterLogconfig), esServelessLogConf)
	if err != nil {
		logger.Errorf("Cluster.LogConfig format error %s", clusterLogconfig)
		return errors.New(fmt.Sprintf("Cluster.LogConfig format error %v", clusterLogconfig))
	}
	if !strings.Contains(clusterLogconfig, esServerlessIndex) || !strings.Contains(clusterLogconfig, esServerlessSpace) {
		logger.Errorf("req.EsServerlessIndex or req.EsServerlessSpace not belong to cluster.LogConfig")
		return errors.New(fmt.Sprintf("req.EsServerlessIndex:[%v] or req.EsServerlessSpace:[%v] not belong to cluster.LogConfig:[%v]", esServerlessIndex, esServerlessSpace, clusterLogconfig))
	}
	if esServelessLogConf.Status != log.ClusterLogconfDone {
		logger.Errorf("EsIndex status: %d, not ready", esServelessLogConf.Status)
		return errors.New(fmt.Sprintf("EsIndex info: %v, not ready.", clusterLogconfig))
	}

	return nil
}

func checkETLParams(req *model.CreateJobConfigReq, job *table.Job) (sqlCode string, err error) {

	// 兼容旧版 ETL 作业
	m := map[string]interface{}{}
	if err := json.Unmarshal([]byte(req.ProgramArgs), &m); err != nil {
		logger.Errorf("ProgramArgs format error. must be json")
		return "", err
	}
	var sql string
	if _, ok := m["EtlJob"]; ok {
		// 1. 将ProgramArgs转换为Sql
		etl, sqlTransformed, err := service6.ConvertSql(job, req.ProgramArgs)
		logger.Debugf("Generated sql:%s", sqlTransformed)
		if err != nil {
			logger.Errorf("%s: Failed to convert JSON to SQL, %s", req.RequestId, err.Error())
			return "", errorcode.InvalidParameterValueCode.NewWithErr(err)
		}

		// 2. 根据不同的connector添加不同的ResourceRefs
		err, refs := service6.CreateResourceRefForEtlJob(job, etl.EtlJob.Connectors, req.Region, "EtlJob")
		if err != nil {
			logger.Errorf("%s:Failed to Create Ref , %s", req.RequestId, err.Error())
			return "", errorcode.InternalErrorCode.NewWithErr(err)
		}
		sql = sqlTransformed
		req.ResourceRefs = refs
	}
	if jsonIn, ok := m["EtlJobCanvas"]; ok {
		jsonStr := jsonIn.(string)
		jsonTransformation := service7.JsonTransformation{
			AppId: int64(req.AppId),
		}
		// 1. sql转换
		sqlTransformed, err := jsonTransformation.Transform(jsonStr)
		logger.Debugf("Generated sql:%s", sqlTransformed)
		if err != nil {
			logger.Errorf("%s: Failed to convert JSON to SQL, %s", req.RequestId, err.Error())
			return "", errorcode.InvalidParameterValueCode.NewWithErr(err)
		}
		connectorList, err := jsonTransformation.GetConnectorsList(jsonStr)
		err, refs := service6.CreateResourceRefForEtlJob(job, connectorList, req.Region, "EtlJobCanvas")
		if err != nil {
			logger.Errorf("%s:Failed to Create Ref , %s", req.RequestId, err.Error())
			return "", errorcode.InternalErrorCode.NewWithErr(err)
		}
		sql = sqlTransformed
		req.ResourceRefs = refs
	}
	// 3. 加密sql为了之后的执行与保存
	sqlCode, err = EncryptAndEncodeSqlCode(sql)
	if err != nil {
		return "", err
	}
	// 4. ETL 作业的默认高级参数设置
	addDefaultConfiguration(&req.Properties)
	return sqlCode, nil
}

func buildCreateJobBucketRefReqWithoutBucket(req *model.CreateJobConfigReq) *mode2.CreateJobBucketRefReq {
	createReq := &mode2.CreateJobBucketRefReq{}
	createReq.JobId = req.JobId
	createReq.Region = req.Region
	createReq.CreatorUin = req.Uin
	createReq.AppId = req.AppId
	return createReq
}

func UpdateJobConfigSqlCode(jobId int64, jobConfigId int64, sqlCode string) (err error) {
	if len(sqlCode) == 0 {
		return nil
	}
	deEncryptSql, err := DeEncodeAndDeEncryptSqlCode(sqlCode)
	if err != nil {
		logger.Errorf("Failed to DeEncodeAndDeEncryptSqlCode sql: %s , %#v", sqlCode, err)
		return err
	}
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("%s: duplicate key update JobConfigSqlCode to db panic ,for job:%+v, errors:%+v", jobId, jobConfigId, errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()

	service2.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.ExecuteSqlWithArgs("insert into JobConfigSqlCode(`JobId`,`JobConfigId`,`DecodeSqlCode`) values(?,?,?) on duplicate key update `JobConfigId`=? , DecodeSqlCode = ?", jobId, jobConfigId, deEncryptSql, jobConfigId, deEncryptSql)
		return nil
	}).Close()
	return nil
}

func CreateJobConfig(job *table.Job, req *model.CreateJobConfigReq, sqlCode string, checkpointInterval int64, programElements string) (jobConfigId int64, versionId int16, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("%s: Add JobConfig to db panic ,for job:%+v, errors:%+v", req.RequestId, job, errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	bizId := fmt.Sprintf("optId-%s-%d", job.SerialId, job.LatestJobConfigVersionId)
	locker := dlocker.NewDlocker("createJobConfig", bizId, 10)
	err = locker.Lock()
	if err != nil {
		logger.Errorf("lock with createJobConfig-%s error, errors:%+v", bizId, err)
		return
	}
	defer func() {
		err := locker.UnLock()
		if err != nil {
			logger.Errorf("CreateJobConfig could not UnLock the lock. %+v", err)
		}
	}()
	// 重新查一次 LatestJobConfigVersionId，虽然加锁了，但是加锁之前查的LatestJobConfigVersionId
	newJob, err := service.WhetherJobExists(job.AppId, job.Region, job.SerialId)
	if err != nil {
		logger.Errorf("WhetherJobExists %s error, errors:%+v", req.JobId, err)
		return
	}
	newLatestJobConfigVersionId := newJob.LatestJobConfigVersionId + 1
	txManager := service2.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		// 1. 保存作业配置
		jobConfig, err := BuildJobConfigEntityFromReq(newLatestJobConfigVersionId, job.Id, req, sqlCode, checkpointInterval, programElements, job.Type)
		if err != nil {
			logger.Errorf("%s: Failed to BuildJobConfigEntityFromReq: %+v", req.RequestId, err)
			return err
		}
		jobConfigId = tx.SaveObject(jobConfig, "JobConfig")
		logger.Debugf("%s: Newly-saved JobConfigId: %d", req.RequestId, jobConfigId)

		// 2. 更新作业版本
		err = UpdateJobWhenCreateJobConfig(job.Id, job.Status, jobConfigId, newLatestJobConfigVersionId, tx, req.FlinkVersion)
		if err != nil {
			return err
		}
		// 3. 更新sqlCode
		err = UpdateJobConfigSqlCode(job.Id, jobConfigId, sqlCode)
		if err != nil {
			return err
		}

		return nil
	}).Close()

	return jobConfigId, newLatestJobConfigVersionId, nil
}

func BuildJobConfigEntityFromReq(lastVersionId int16, jobId int64, req *model.CreateJobConfigReq, sqlCode string, checkpointInterval int64, programElements string, jobType int8) (*table2.JobConfig, error) {
	jobConfig := &table2.JobConfig{}
	jobConfig.CreatorUin = req.SubAccountUin
	jobConfig.JobId = jobId
	jobConfig.VersionId = lastVersionId
	jobConfig.EntrypointClass = req.EntrypointClass
	jobConfig.CheckpointInterval = checkpointInterval
	jobConfig.SqlCode = sqlCode
	jobConfig.ClsLogsetId = req.ClsLogsetId
	jobConfig.ClsTopicId = req.ClsTopicId
	jobConfig.EsServerlessSpace = req.EsServerlessSpace
	jobConfig.EsServerlessIndex = req.EsServerlessIndex
	jobConfig.PythonVersion = req.PythonVersion
	jobConfig.LogLevel = req.LogLevel
	jobConfig.CheckpointRetainedNum = req.CheckpointRetainedNum
	jobConfig.UseOldSysConnector = req.UseOldSystemConnector
	if req.AutoRecover == 0 {
		// 设置默认值，如果请求参数没带，则使用默认参数
		req.AutoRecover = constants.AutoRecoverEnable
	}
	jobConfig.AutoRecover = req.AutoRecover
	jobConfig.ContinueAlarm = req.ContinueAlarm

	strClazzLevels := constants.CLASS_LOG_LEVEL_DEFALUT_VALUE

	if len(req.ClazzLevels) > 0 {
		bytesClazzLevels, err := json.Marshal(req.ClazzLevels)
		if err != nil {
			errMsg := fmt.Sprintf("Failed to json marshal ClazzLevels struct: %+v, errors:%+v", req.ClazzLevels, err)
			logger.Errorf(errMsg)
			return nil, errorcode.InvalidParameterValueCode.ReplaceDesc(errMsg)
		}
		strClazzLevels = string(bytesClazzLevels)
	}
	jobConfig.ClazzLevels = strClazzLevels

	maxParallelism, props, err := calculateMaxParallelism(req)
	if err != nil {
		return nil, err
	}

	jobConfig.MaxParallelism = maxParallelism
	jobConfig.Properties = props
	jobConfig.Status = constants.JOB_CONFIG_STATUS_UN_PUBLISHED
	jobConfig.FlinkVersion = req.FlinkVersion
	jobConfig.LibConfig = req.LibConfig

	if req.COSBucket != "" && req.LogCollectType != constants.JobLogCollectTypeCOS { // 目前不支持用户显式传入 COSBucket
		return nil, errorcode.InvalidParameter_JobConfigCosBucketParamError.ReplaceDesc(fmt.Sprintf("COSBucket is not supported yet in JobConfig creation"))
	} else { // 如果用户未传入 COSBucket 字段, 则继承自集群的 DefaultCOSBucket 设定
		job, err := service2.GetJobBySerialId(int64(req.AppId), req.JobId)
		if err != nil {
			logger.Errorf("Failed to GetJobBySerialId for %d and %s because %+v", req.AppId, req.JobId, err)
			return nil, err
		}
		cluster, err := service5.GetClusterByClusterId(job.ClusterId)
		if err != nil {
			logger.Errorf("Failed to GetClusterByClusterId for cluster id %d because %+v", job.ClusterId, err)
			return nil, err
		}
		jobConfig.COSBucket = cluster.DefaultCOSBucket
	}

	jobConfig.Remark = req.Remark
	jobConfig.CreateTime = util.GetCurrentTime()
	jobConfig.DefaultParallelism = req.DefaultParallelism
	if req.DefaultParallelism <= 0 { // DefaultParallelism 是可选参数, 如果用户没有传入则云 API 这里是 0
		jobConfig.DefaultParallelism = 1
	}
	if jobConfig.DefaultParallelism > jobConfig.MaxParallelism {
		return nil, errors.New(fmt.Sprintf("Job config's default parallelism %d shoud not be larger than maxParallelism %d",
			jobConfig.DefaultParallelism, jobConfig.MaxParallelism))
	}
	jobConfig.ProgramArgs = programElements
	if req.LogCollect && ((req.LogCollectType == constants.JobLogCollectTypeCLS && req.ClsLogsetId != "" && req.ClsTopicId != "") || req.LogCollectType == 0) { // LogCollectType=0 为了兼容历史接口
		jobConfig.LogCollect = constants.JobLogCollectEnabled
	} else if req.LogCollect && req.LogCollectType == constants.JobLogCollectTypeCOS && req.COSBucket != "" {
		jobConfig.LogCollect = constants.JobLogCollectEnabledOnCos
		properties, err := service.AddDiagnosisVolumeMounted2Properties(jobConfig.Properties)
		if err != nil {
			logger.Errorf("AddDiagnosisVolumeMounted2Properties with error [%v]", err)
			return nil, err
		}
		jobConfig.Properties = properties
	} else if req.LogCollect && req.LogCollectType == constants.JobLogCollectTypeES && req.EsServerlessIndex != "" && req.EsServerlessSpace != "" {
		jobConfig.LogCollect = constants.JobLogCollectEnabledOnES
	} else {
		jobConfig.LogCollect = constants.JobLogCollectDisabled
	}

	if req.TraceModeOn && req.TraceModeConfiguration != nil {
		bExpertModeConfiguration, err := json.Marshal(req.TraceModeConfiguration)
		if err != nil {
			return nil, errorcode.InvalidParameterValueCode.NewWithErr(err)
		}
		jobConfig.TraceModeConfiguration = string(bExpertModeConfiguration)
	} else {
		jobConfig.TraceModeConfiguration = "{}"
	}

	if req.JobGraph != nil {
		b, err := json.Marshal(req.JobGraph)
		if err != nil {
			return nil, errorcode.InvalidParameterValueCode.NewWithErr(err)
		}
		jobConfig.JobGraph = string(b)
	}

	if req.JobManagerSpec == 0 && req.TaskManagerSpec == 0 && req.JobManagerCpu == 0 &&
		req.TaskManagerCpu == 0 && req.JobManagerMem == 0 && req.TaskManagerMem == 0 {

		cluster, err := service5.GetClusterByJobId(req.JobId)
		if err != nil {
			logger.Errorf("fail to GetClusterByJobId with error [%v]", err)
			return nil, err
		}
		jobConfig.JobManagerCpu = 1
		jobConfig.JobManagerMem = float32(cluster.MemRatio)
		jobConfig.TaskManagerCpu = 1
		jobConfig.TaskManagerMem = float32(cluster.MemRatio)
	} else {
		jobConfig.JmCuSpec = req.JobManagerSpec
		jobConfig.TmCuSpec = req.TaskManagerSpec
		jobConfig.JobManagerCpu = req.JobManagerCpu
		jobConfig.JobManagerMem = req.JobManagerMem
		jobConfig.TaskManagerCpu = req.TaskManagerCpu
		jobConfig.TaskManagerMem = req.TaskManagerMem
	}

	if req.ExpertModeOn {
		if jobType == constants.JOB_TYPE_SQL {
			bExpertModeConfiguration, err := json.Marshal(req.ExpertModeConfiguration)
			if err != nil {
				return nil, errorcode.InvalidParameterValueCode.NewWithErr(err)
			}
			jobConfig.ExpertModeConfiguration = string(bExpertModeConfiguration)
		} else if jobType == constants.JOB_TYPE_JAR {
			jobConfig.ExpertModeConfiguration = "{\"msg\":\"高级jar作业\"}"
		} else {
			logger.Errorf("ExpertModeOn is only supported for SQL and JAR job type")
			return nil, errors.New("ExpertModeOn is only supported for SQL and JAR job type")
		}
	}

	return jobConfig, nil
}

func calculateMaxParallelism(req *model.CreateJobConfigReq) (maxParallelism int16, props string, err error) {
	// 获取作业的最大并行度信息, 如果用户设置了则按照用户设置的值, 否则取 2048
	maxParallelism, props, err = GetMaxParallelism(req.DefaultParallelism, req.Properties, false)
	if err != nil {
		return 0, "", err
	}

	job, err := service2.GetJobBySerialId(int64(req.AppId), req.JobId)
	if err != nil {
		return 0, "", err
	}
	clusterGroup, err := service5.GetClusterGroupByClusterId(job.ClusterId)
	if err != nil {
		return 0, "", err
	}

	// 共享集群不支持 Properties, 固定为 128
	if clusterGroup.Type == constants.CLUSTER_GROUP_TYPE_SHARED {
		return constants.DEFAULT_MAX_PARALLELISM_FOR_SHARED_CLUSTER, "", nil
	}

	// 独享集群按 GetMaxParallelism 逻辑
	return maxParallelism, props, nil
}

/*
*
Fixme trim EntrypointClass
*/
func CheckCompositeParameters(job *table.Job, sqlCode string, entrypointClass string) (err error) {
	if (job.Type == constants.JOB_TYPE_SQL || job.Type == constants.JOB_TYPE_ETL) && sqlCode == "" { // SQL作业指定了EntrypointClass
		msg := fmt.Sprintf("SQL job/ETL job should specify SqlCode, JobId: %s", job.SerialId)
		logger.Error(msg)
		return errorcode.InvalidParameterValueCode.ReplaceDesc(msg)
	} else if job.Type == constants.JOB_TYPE_JAR && entrypointClass == "" { // JAR作业必须指定EntrypointClass
		msg := fmt.Sprintf("JAR job must specify EntrypointClass, JobId: %s", job.SerialId)
		logger.Error(msg)
		return errorcode.InvalidParameterValueCode.ReplaceDesc(msg)
	}
	// pyflink 作业， entrypointClass 也可以为空。 所以这里不做检查

	return nil
}

func UpdateJobWhenCreateJobConfig(jobId int64, jobStatus int8, jobConfigId int64, newLatestJobConfigVersionId int16, tx *dao.Transaction, flinkVersion string) error {
	var result sql.Result
	if jobStatus == constants.JOB_STATUS_CREATE {
		result = tx.ExecuteSqlWithArgs("UPDATE Job SET LatestJobConfigId=?, LatestJobConfigVersionId=?, "+
			"Status=?, FlinkVersion=? WHERE Id=? AND Status=?", jobConfigId, newLatestJobConfigVersionId,
			constants.JOB_STATUS_INITIALIZED, flinkVersion, jobId, constants.JOB_STATUS_CREATE)
	} else {
		result = tx.ExecuteSqlWithArgs("UPDATE Job SET LatestJobConfigId=?, LatestJobConfigVersionId=?, FlinkVersion=? WHERE Id=?", jobConfigId, newLatestJobConfigVersionId, flinkVersion, jobId)
	}

	if rowAffected, err := result.RowsAffected(); err != nil {
		logger.Errorf("Failed to Update Job when CreateJobConfig, %+v", err)
		return err
	} else if rowAffected == 0 {
		return errors.New("Failed to Update Job when CreateJobConfig, rowAffected=0")
	}

	return nil
}

func GetMaxParallelism(defaultParallelism int16, props []*model3.Property, addMaxParallelismKvPairIfNotPresent bool) (int16, string, error) {
	// 根据用户提供的算子并行度, 计算一个默认的 MaxParallelism 值
	var maxParallelism = GetDefaultMaxParallelism()

	if len(props) == 0 {
		if addMaxParallelismKvPairIfNotPresent {
			return maxParallelism, GetDefaultMaxParallelismKvPair(), nil
		}
		return maxParallelism, "", nil
	} else {
		foundUserDefinedMaxParallelism := false
		for i := 0; i < len(props); i++ {
			prop := props[i]
			if prop.Key == constants.MAX_PARALLELISM_KEY {
				userProvidedMaxParallelism, err := strconv.ParseInt(prop.Value, 10, 64)

				if err != nil {
					return maxParallelism,
						"",
						errorcode.NewStackError(errorcode.InvalidParameter_IllegalMaxParallelism,
							"Invalid MaxParallelism:"+prop.Value,
							err)
				} else if userProvidedMaxParallelism < int64(defaultParallelism) {
					return maxParallelism,
						"",
						errorcode.NewStackError(errorcode.InvalidParameter_MaxParallelismTooSmall,
							"MaxParallelism too small:"+prop.Value,
							err)
				} else if userProvidedMaxParallelism > constants.UPPER_BOUND_MAX_PARALLELISM {
					return maxParallelism,
						"",
						errorcode.NewStackError(errorcode.InvalidParameter_MaxParallelismTooLarge,
							"MaxParallelism too large:"+prop.Value,
							err)
				}

				maxParallelism = int16(userProvidedMaxParallelism)
				foundUserDefinedMaxParallelism = true
				break
			}
		}

		if !foundUserDefinedMaxParallelism && addMaxParallelismKvPairIfNotPresent {
			props = append(props, &model3.Property{
				Key:   constants.MAX_PARALLELISM_KEY,
				Value: constants.MAX_PARALLELISM_DEFAULT_VALUE,
			})
		}

		propArrayBytes, err := json.Marshal(props)
		if err != nil {
			logger.Errorf("Failed to unmarshal JobConfig.Properties JSON because %+v", err)
			return maxParallelism, "", errorcode.InternalErrorCode.New()
		}
		return maxParallelism, string(propArrayBytes), nil
	}
}

// 固定返回 2048, 不再动态计算
func GetDefaultMaxParallelism() int16 {
	maxParallelismValue, err := strconv.Atoi(constants.MAX_PARALLELISM_DEFAULT_VALUE)
	if err != nil {
		panic(err) // 代码里保证不乱改的话, err 是不可能事件, 这里防御式编程
	}

	return int16(maxParallelismValue)
}

func GetDefaultMaxParallelismKvPair() string {
	return "[{\"Key\":\"pipeline.max-parallelism\",\"Value\":\"" + constants.MAX_PARALLELISM_DEFAULT_VALUE + "\"}]"
}

/*
* 新增etl 作业默认值
用户没有添加就加上，用户自定义了，按照用户定义的为准
ts210712 by andylhuang
*
*/
func addDefaultConfiguration(properties *[]*model3.Property) {
	requiredConfiguration := make(map[string]string, 0)

	for _, property := range *properties {
		key := property.Key
		if _, exist := constants.EtlDefaultConfiguration[strings.TrimSpace(key)]; exist {
			requiredConfiguration[strings.TrimSpace(key)] = property.Value
		}
	}

	if len(requiredConfiguration) < 2 {
		if _, exist := requiredConfiguration[constants.ETL_METRICS_LATENCY_INTERVAL_NAME]; !exist {
			*properties = append(*properties, &model3.Property{Key: constants.ETL_METRICS_LATENCY_INTERVAL_NAME,
				Value: constants.ETL_METRICS_LATENCY_INTERVAL_VALUE})
		}
		if _, exist := requiredConfiguration[constants.ETL_PIPELINE_OPERATOR_CHAINING_NAME]; !exist {
			*properties = append(*properties, &model3.Property{Key: constants.ETL_PIPELINE_OPERATOR_CHAINING_NAME,
				Value: constants.ETL_PIPELINE_OPERATOR_CHAINING_VALUE})
		}
	}
}
