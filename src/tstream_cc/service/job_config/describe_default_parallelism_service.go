package service

import (
	"fmt"
	v12 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"strings"
	"sync"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_config"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/configuration_center"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/time_profile"
)

type DescribeDefaultParallelismService struct {
	request     *model.DescribeDefaultParallelismReq
	ReservedCpu *resource.Quantity
}

type NodeInfoEx struct {
	model.NodeInfo
	CuCapacity int64
}

func (o *NodeInfoEx) ToNodeINfo() *model.NodeInfo {
	return &o.NodeInfo
}

func (o *DescribeDefaultParallelismService) DescribeDefaultParallelism() (
	rsp *model.DescribeDefaultParallelismRsp, err error) {
	req := o.request
	job, err := service.WhetherJobExists(int32(req.AppId), req.Region, req.JobId)
	if err != nil {
		return
	}

	if req.JobManagerSpec == 0.0 {
		req.JobManagerSpec = 1.0
	}
	if req.TaskManagerSpec == 0.0 {
		req.TaskManagerSpec = 1.0
	}

	clusterGroupService, err := service3.NewClusterGroupService(job.ClusterGroupId)
	if err != nil {
		return
	}

	cluster, err := clusterGroupService.GetActiveCluster()
	if err != nil {
		return
	}
	if support, err := service3.SupportFineGrainedResource(cluster); err != nil {
		return nil, err
	} else if !support {
		return o.getWithoutFineGrainedResource(clusterGroupService, job)
	}

	simpleCal, err := service2.GetConfigurationValue(constants.ConfKeyEnableSimpleDefaultParallelism, "true")
	if err != nil {
		return
	}
	if simpleCal == "true" {
		return o.simpleCalDefaultParallelism(clusterGroupService, job)
	}

	// for Tke
	nodeInfoList, err := o.GetNodeInfoList(cluster)
	if err != nil {
		return
	}

	maxParallelism, fragmentList, err := o.GetMaxParallelismAndFragmentList(nodeInfoList,
		int64(clusterGroupService.GetClusterGroup().CuNum))
	if err != nil {
		return
	}

	jc, jm, tc, tm, err := service.GetJobRunningCPUAndMem(job)
	if err != nil {
		return
	}
	runningCpu := jc + tc
	runningMem := jm + tm
	if err != nil {
		return
	}

	jobRunningCu := service.GetCuNumFromCpuMem(runningCpu, runningMem, cluster.MemRatio)

	return &model.DescribeDefaultParallelismRsp{
		MaxDefaultParallelism: maxParallelism,
		ClusterFreeCu: func(nodeInfoList []*NodeInfoEx) float32 {
			sum := float32(0.0)
			for _, node := range nodeInfoList {
				sum += node.FreeCu
			}
			return sum - jobRunningCu
		}(nodeInfoList),
		JobRunningCu:  jobRunningCu,
		JobRunningCpu: service.GetFloat2Dot(runningCpu),
		JobRunningMem: service.GetFloat2Dot(runningMem),
		FragmentList:  fragmentList,
		NodeList: func(from []*NodeInfoEx) (to []*model.NodeInfo) {
			to = make([]*model.NodeInfo, 0, len(from))
			for _, node := range from {
				to = append(to, node.ToNodeINfo())
			}
			return
		}(nodeInfoList),
	}, nil
}

func (o *DescribeDefaultParallelismService) simpleCalDefaultParallelism(
	clusterGroupService *service3.ClusterGroupService, job *table2.Job) (rsp *model.DescribeDefaultParallelismRsp, err error) {
	return o.getWithoutFineGrainedResource(clusterGroupService, job)
}

func (o *DescribeDefaultParallelismService) GetNodeInfoList(cluster *table.Cluster) (
	nodeInfoList []*NodeInfoEx, err error) {
	reservedCuPerNode, err := service2.GetConfigurationIntValueByKey(constants.CONF_KEY_RESERVED_CU_PER_WORKER_NODE)
	if err != nil {
		return
	}
	o.ReservedCpu = &resource.Quantity{}
	o.ReservedCpu.Set(reservedCuPerNode)

	k8sService := k8s.GetK8sService()
	kubConfig := []byte(cluster.KubeConfig)
	client, err := k8sService.NewClient(kubConfig)
	if err != nil {
		return nil, errorcode.InternalErrorCode_K8sClientCreateFailed.NewWithErr(err)
	}

	timeProfile := time_profile.NewTimeProfile(cluster.UniqClusterId)
	nodeList, err := k8sService.ListNode(client, v1.ListOptions{
		LabelSelector: fmt.Sprintf("%s==%s", constants.TKE_CVM_LABEL_KEY, constants.TKE_WORKER_NODE_LABEL_VAL),
	})
	logger.Debugf("ListNode cost: %s", timeProfile.Cost())
	if err != nil {
		return
	}

	nodeInfoList = make([]*NodeInfoEx, 0, len(nodeList.Items))
	const batchSize = 4
	wg := &sync.WaitGroup{}
	nodeInfoChn := make(chan interface{}, len(nodeList.Items))
	for i := 0; i < len(nodeList.Items); i += batchSize {
		end := i + batchSize
		if end > len(nodeList.Items) {
			end = len(nodeList.Items)
		}

		wg.Add(1)
		go func(i int) {
			timeProfile := time_profile.NewTimeProfile(cluster.UniqClusterId)
			defer func() {
				wg.Done()
				logger.Debugf("ListPod & parse cost: %s", timeProfile.Cost())
			}()

			client, err := k8sService.NewClient(kubConfig)
			if err != nil {
				nodeInfoChn <- errorcode.InternalErrorCode_K8sClientCreateFailed.NewWithErr(err)
				return
			}

			for _, node := range nodeList.Items[i:end] {
				podList, err := k8sService.ListPod(client, "default", v1.ListOptions{
					FieldSelector: fmt.Sprintf("spec.nodeName=%s", node.Name),
				})
				if err != nil {
					nodeInfoChn <- err
					break
				}
				if nodeInfo, err := o.parseNode(&node, podList); err != nil {
					nodeInfoChn <- err
				} else {
					nodeInfoChn <- nodeInfo
				}
			}
		}(i)
	}
	wg.Wait()
	close(nodeInfoChn)

	for nodeInfo := range nodeInfoChn {
		if err, yes := nodeInfo.(error); yes {
			return nil, err
		}
		nodeInfoList = append(nodeInfoList, nodeInfo.(*NodeInfoEx))
	}
	service.SortSliceByField(nodeInfoList, "FreeCu", true)
	return
}

func (o *DescribeDefaultParallelismService) parseNode(node *v12.Node, podList *v12.PodList) (
	nodeInfo *NodeInfoEx, err error) {
	logger.Debugf("node: %s, podNum: %d", node.Name, len(podList.Items))

	nodeInfo = &NodeInfoEx{
		NodeInfo: model.NodeInfo{
			IP:      node.Name,
			FreeCu:  0,
			FreeEni: 0,
		},
		CuCapacity: 0,
	}
	maxEni := int64(7)
	if qualtity, exist := node.Status.Capacity[constants.TkeDirectEniResourceName]; exist {
		maxEni = qualtity.Value()
	}
	nodeInfo.FreeEni = maxEni

	quantity := node.Status.Capacity["cpu"]
	quantity.Sub(*o.ReservedCpu)
	nodeInfo.CuCapacity = quantity.MilliValue() / 1000
	for _, pod := range podList.Items {
		if !strings.HasPrefix(pod.Name, "cql-") || strings.HasPrefix(pod.Name, o.request.JobId) {
			logger.Debugf("ignore non flink pod %s", pod.Name)
			continue
		}
		nodeInfo.FreeEni--
		podCpuLimit := pod.Spec.Containers[0].Resources.Limits["cpu"]
		quantity.Sub(podCpuLimit)
	}
	nodeInfo.FreeCu = float32(quantity.MilliValue()) / 1000.0

	return
}

func (o *DescribeDefaultParallelismService) GetMaxParallelismAndFragmentList(
	nodeInfoList []*NodeInfoEx,
	cuNum int64,
) (maxParallelism int64, fragmentList []*model.FragmentInfo, err error) {
	freeCuMap := make(map[float32]int64, 0)
	fragmentMap := make(map[float32]int64, 0)
	jmSpec := o.request.JobManagerSpec
	tmSpec := o.request.TaskManagerSpec

	cuCapacity := int64(0)
	for _, node := range nodeInfoList {
		cuCapacity += node.CuCapacity
	}
	logger.Debugf("cuCapacity %d, cuNum %d", cuCapacity, cuNum)

	// simulate tke scheduler, nodeInfoList is sorted in descend order by FreeCu
	for i := len(nodeInfoList) - 1; i >= 0; i-- {
		node := nodeInfoList[i]
		freeCu := node.FreeCu
		if jmSpec > 0 && freeCu >= jmSpec {
			freeCu -= jmSpec
			jmSpec = 0
		}
		if freeCu > 0 {
			freeCuMap[freeCu]++
		}
	}
	for freeCu, cnt := range freeCuMap {
		if freeCu >= tmSpec {
			parallelism := int64(freeCu / tmSpec)
			maxParallelism += parallelism * cnt
			freeCu -= float32(parallelism) * tmSpec
		}
		if freeCu > 0 {
			fragmentMap[freeCu] += cnt
		}
	}
	if cuNum < cuCapacity {
		// cut off more allocated CU
		cuCutOff := float32(cuCapacity - cuNum)
		tmpMap := fragmentMap
		fragmentMap = make(map[float32]int64, len(tmpMap))
		for cu, cnt := range tmpMap {
			if cuCutOff <= 0 {
				fragmentMap[cu] += cnt
				continue
			}
			tCnt := int64(cuCutOff / cu)
			if tCnt > cnt {
				tCnt = cnt
			}
			if tCnt <= 0 {
				cnt--
				if cnt > 0 {
					fragmentMap[cu] += cnt
				}
				cu -= cuCutOff
				if cu > 0 {
					fragmentMap[cu]++
				}
				cuCutOff = 0
			} else {
				cuCutOff -= float32(tCnt) * cu
				cnt -= tCnt
				if cnt > 0 {
					fragmentMap[cu] += cnt
				}
			}
		}
		// if fragments cannot compensate cut off CU, decrease parallelism
		if cuCutOff > 0 {
			p := int64(cuCutOff / tmSpec)
			cu := float32(p) * tmSpec
			if cu < cuCutOff {
				p++
				cu += tmSpec
			}
			cu -= cuCutOff
			maxParallelism -= p
			if maxParallelism < 0 {
				maxParallelism = 0
			}
			if cu > 0 {
				fragmentMap[cu]++
			}
		}
	}

	fragmentList = make([]*model.FragmentInfo, 0, len(fragmentMap))
	for cu, cnt := range fragmentMap {
		fragmentList = append(fragmentList, &model.FragmentInfo{
			Spec:  cu,
			Count: cnt,
		})
	}
	service.SortSliceByField(fragmentList, "Spec", false)

	return
}

func (o *DescribeDefaultParallelismService) getWithoutFineGrainedResource(
	clusterGroupService *service3.ClusterGroupService, job *table2.Job) (rsp *model.DescribeDefaultParallelismRsp, err error) {
	clusterGroup := clusterGroupService.GetClusterGroup()
	cluster, err := clusterGroupService.GetCluster()
	if err != nil {
		return nil, err
	}
	totalRunningCpu, totalRunningMem, err := service.GetClusterRunningCpuAndMem(clusterGroup.SerialId, clusterGroup.AppId, clusterGroup.Region, cluster.MemRatio)
	if err != nil {
		return
	}
	jc, jm, tc, tm, err := service.GetJobRunningCPUAndMem(job)
	if err != nil {
		return
	}
	runningCpu := jc + tc
	runningMem := jm + tm
	runningCu := service.GetCuNumFromCpuMem(totalRunningCpu, totalRunningMem, cluster.MemRatio)
	freeCu := service.GetClusterTotalCU(clusterGroup) - runningCu + runningCu
	maxParallelism := int64((freeCu - constants.DEFAULT_JM_RUNNING_CU*o.request.JobManagerSpec) /
		o.request.TaskManagerSpec)
	if maxParallelism < 0 {
		maxParallelism = 0
	}

	fragmentList := make([]*model.FragmentInfo, 0, 1)
	spec := freeCu - constants.DEFAULT_JM_RUNNING_CU*o.request.JobManagerSpec -
		float32(maxParallelism)*o.request.TaskManagerSpec
	if spec > 0 {
		fragmentList = append(fragmentList, &model.FragmentInfo{
			Spec:  spec,
			Count: 1,
		})
	}

	return &model.DescribeDefaultParallelismRsp{
		MaxDefaultParallelism: maxParallelism,
		ClusterFreeCu:         freeCu - runningCu,
		JobRunningCu:          runningCu,
		JobRunningCpu:         runningCpu,
		JobRunningMem:         runningMem,
		FragmentList:          fragmentList,
		NodeList:              nil,
	}, nil
}

func NewDescribeDefaultParallelismService(
	request *model.DescribeDefaultParallelismReq,
) *DescribeDefaultParallelismService {
	return &DescribeDefaultParallelismService{request: request}
}
