package service

import (
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	model2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_config"
	metadata_model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/metadata"
	table3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job_config"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config"
	service5 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/metadata"
)

/**
 *
 * <AUTHOR>
 * @time 2019/4/29
 * @version 0.4.0
 * @since 0.4.0
 * @see
 */
func ListJobConfigs(jobId int64, jobConfigsIds []int64, jobConfigVersions []int64, filters map[string][]string) ([]*table.JobConfig, error) {
	sql := "SELECT * FROM JobConfig WHERE status<>? AND jobId=?"
	args := make([]interface{}, 0)
	args = append(args, constants.JOB_CONFIG_STATUS_DELETE)
	args = append(args, jobId)

	if len(jobConfigsIds) > 0 {
		sql += " AND id IN ("
		for i := 1; i < len(jobConfigsIds); i++ {
			sql += "?, "
			args = append(args, jobConfigsIds[i])
		}
		sql += "?)"
		args = append(args, jobConfigsIds[len(jobConfigsIds)-1])
	}
	if len(jobConfigVersions) > 0 {
		jobConfigVersion := jobConfigVersions[0]
		if jobConfigVersion == constants.JOB_CONFIG_DRAFT_VERSION {
			sql += " AND versionId IN (?) "
			args = append(args, constants.JOB_CONFIG_DRAFT_VERSION)
		} else {
			sql += " AND versionId IN ("
			for i := 1; i < len(jobConfigVersions); i++ {
				sql += "?, "
				args = append(args, jobConfigVersions[i])
			}
			sql += "?)"
			args = append(args, jobConfigVersions[len(jobConfigVersions)-1])
		}
	} else {
		sql += " AND versionId > ? "
		args = append(args, constants.JOB_CONFIG_DRAFT_VERSION)
	}
	if filters != nil && len(filters) > 0 {
		for key, values := range filters {
			if key == "Status" {
				sql += " AND status IN ("
				for i := 0; i < len(values); i++ {
					sql += "?"
					args = append(args, values[i])
					if i != len(values)-1 {
						sql += ","
					}
				}
				sql += ")"
			}
		}
	}
	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return nil, err
	}
	jobConfigs := make([]*table.JobConfig, 0)
	for i := 0; i < len(data); i++ {
		jobConfig := &table.JobConfig{}
		err = util.ScanMapIntoStruct(jobConfig, data[i])
		if err != nil {
			logger.Errorf("Failed to convert bytes into table.jobConfig, with errors:%+v", err)
			return []*table.JobConfig{}, errorcode.InternalErrorCode.New()
		}
		jobConfigs = append(jobConfigs, jobConfig)
	}
	return jobConfigs, nil
}

func GetJobConfigCount(jobId int64) (int, error) {
	sql := "Select count(*) as jcCount from JobConfig where jobId=? and status <> ? "
	args := make([]interface{}, 0)
	args = append(args, jobId)
	args = append(args, constants.JOB_CONFIG_STATUS_DELETE)

	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to query job config count, with sql:%s, args:%+v", sql, args)
		return 0, err
	}
	jcCountStr := string(data[0]["jcCount"])
	jcCountInt, err := strconv.Atoi(jcCountStr)
	if err != nil {
		logger.Errorf("Failed to convert job config count from string to int, with errors:%+v", err)
		return 0, errorcode.InternalErrorCode.NewWithErr(err)
	}
	return jcCountInt, nil
}

func DeleteJobConfigs(jobId int64, jobConfigVersions []int16, uin string) error {
	sql := "Update JobConfig set Status = ?, DeletorUin = ? Where JobId = ? and VersionId In ("
	args := make([]interface{}, 0)
	args = append(args, constants.JOB_CONFIG_STATUS_DELETE)
	args = append(args, uin)
	args = append(args, jobId)
	for i := 0; i < len(jobConfigVersions); i++ {
		sql += " ? "
		args = append(args, jobConfigVersions[i])
		if i != len(jobConfigVersions)-1 {
			sql += ","
		}
	}
	sql += ")"

	txManager := service.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.ExecuteSql(sql, args)
		//删除 JobConfig时解除和 JobMetaTableRef的关系 210618
		sql1, args1 := service5.DeleteJobConfigMetaRefsSql(jobId, jobConfigVersions, uin)
		tx.ExecuteSql(sql1, args1)
		return nil
	}).Close()

	return nil
}

// QueryJobConfigs 查询删除的作业配置信息
func QueryJobConfigs(jobId int64, jobConfigVersions []int16) (jobConfigs []*table.JobConfig, err error) {
	jobConfigs = make([]*table.JobConfig, 0)
	sql := "Select * from JobConfig Where JobId = ? and VersionId In ("
	args := make([]interface{}, 0)
	args = append(args, jobId)
	for i := 0; i < len(jobConfigVersions); i++ {
		sql += " ? "
		args = append(args, jobConfigVersions[i])
		if i != len(jobConfigVersions)-1 {
			sql += ","
		}
	}
	sql += ")"
	txManager := service.GetTxManager()
	count, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Error("query JobConfigs with error %v", err)
		return jobConfigs, err
	}
	if count > 0 {
		for i := 0; i < len(data); i++ {
			jobConfig := &table.JobConfig{}
			err = util.ScanMapIntoStruct(jobConfig, data[i])
			if err != nil {
				logger.Errorf("Faild to convert bytes into job config, with errors:%+v")
				return jobConfigs, err
			}
			jobConfigs = append(jobConfigs, jobConfig)
		}
	}

	return jobConfigs, nil
}

func GetJobConfigsByJobSerialId(appId int32, jobSerialId string, status int, jobConfigId int64) (jobConfigs []*table.JobConfig, err error) {

	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Get job config panic, with errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	sql := "select a.* from JobConfig as a, Job as b where a.jobId=b.id and b.publishedJobConfigId=a.id "
	args := make([]interface{}, 0)
	if jobSerialId != "" {
		sql = fmt.Sprintf("%s and b.serialId = ? ", sql)
		args = append(args, jobSerialId)
	}
	if status != -1 {
		sql = fmt.Sprintf("%s and a.`status` = ? ", sql)
		args = append(args, status)
	}
	if jobConfigId != 0 {
		sql = fmt.Sprintf("%s and a.`id` = ? ", sql)
		args = append(args, jobConfigId)
	}
	sql = fmt.Sprintf("%s and a.`status` in (1, 128)", sql)
	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)

	if err != nil {
		logger.Errorf("Failed to query job config, with sql: %s, with appId:%d,with errors:%+v", sql, appId, err)
		return []*table.JobConfig{}, err
	}
	jobConfigs = make([]*table.JobConfig, 0)
	for i := 0; i < len(data); i++ {
		jobConfigMap := data[i]
		jobConfig := &table.JobConfig{}
		err = util.ScanMapIntoStruct(jobConfig, jobConfigMap)
		if err != nil {
			logger.Errorf("Faild to convert bytes into job config, with errors:%+v")
			return []*table.JobConfig{}, err
		}
		jobConfigs = append(jobConfigs, jobConfig)
	}
	return jobConfigs, nil
}

func GetPublishedJobConfig(appId int32, jobSerialId string) (jobConfigs *table.JobConfig, err error) {
	sql := "select a.* from JobConfig as a, Job as b where a.jobId=b.id and b.publishedJobConfigId=a.id "
	args := make([]interface{}, 0)
	if jobSerialId != "" {
		sql = fmt.Sprintf("%s and b.serialId = ? ", sql)
		args = append(args, jobSerialId)
	}
	sql = fmt.Sprintf("%s and a.`status` != -2", sql)
	txManager := service.GetTxManager()
	cnt, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to query job config, with sql: %s, with appId:%d,with errors:%+v", sql, appId, err)
		return nil, err
	}
	if cnt != 1 {
		logger.Errorf("Job: %s, can find Unexpected publiedjobs", jobSerialId)
		return nil, errorcode.NewStackError(errorcode.FailedOperationCode, fmt.Sprintf("Job: %s, need started a job", jobSerialId), nil)
	}
	jobConfig := &table.JobConfig{}
	err = util.ScanMapIntoStruct(jobConfig, data[0])
	if err != nil {
		logger.Errorf("Faild to convert bytes into job config, with errors:%+v")
		return nil, err
	}
	return jobConfig, nil
}

func EncryptAndEncodeSqlCode(sqlCode string) (string, error) {
	encryptSqlCode, err := util.AesEncrypt([]byte(sqlCode), constants.AES_ENCRYPT_KEY)
	if err != nil {
		return constants.NULL, errorcode.InvalidParameterValueCode.ReplaceDesc("SqlCode format error.")
	}

	return encryptSqlCode, nil
}

func DeEncodeAndDeEncryptSqlCode(encodeSqlCode string) (string, error) {
	decryptSqlCode, err := util.AesDecrypt(encodeSqlCode, constants.AES_ENCRYPT_KEY)
	if err != nil {
		return constants.NULL, errorcode.InternalErrorCode.NewWithErr(err)
	}

	return decryptSqlCode, nil
}

func ExtractElementsFromProgramArgs(programArgs string, jobType int8) (argsResult string, checkPointInterval int64, sqlCode string, err error) {
	if jobType == constants.JOB_TYPE_JAR || jobType == constants.JOB_TYPE_PYFLINK {
		return programArgs, 0, "", nil
	} else {
		if len(programArgs) == 0 {
			return programArgs, -1, "", nil
		} else {
			m := map[string]interface{}{}
			err = json.Unmarshal([]byte(programArgs), &m)
			if err != nil {
				return programArgs, -1, "", errorcode.InvalidParameterValueCode.ReplaceDesc(
					"ProgramArgs format error. must be json")
			}
			var checkPointInterval float64
			var sqlCode string
			if _, ok := m["CheckpointInterval"]; ok {

				switch value := m["CheckpointInterval"].(type) {
				case string:
					floatVal, err := strconv.ParseFloat(value, 64)
					if err != nil {
						logger.Errorf("CheckpointInterval:%+v, ParseFloat failed, err: %+v", value, err)
						return programArgs, -1, "", errorcode.InvalidParameterValueCode.ReplaceDesc(
							"CheckpointInterval ParseFloat failed")
					}
					checkPointInterval = floatVal
				case float64:
					checkPointInterval = value
				case int:
					checkPointInterval = float64(value)
				default:
					logger.Errorf("CheckpointInterval:%+v, is not string or float64 or int", value)
					return programArgs, -1, "", errorcode.InvalidParameterValueCode.ReplaceDesc(
						"CheckpointInterval is not string or float64 or int")

				}
				delete(m, "CheckpointInterval")
			} else {
				return programArgs, -1, "", errorcode.InvalidParameterValueCode.ReplaceDesc("checkpoint interval not found")
			}
			if _, ok := m["SqlCode"]; ok {
				sqlCode = m["SqlCode"].(string)
				sqlCodeBytes, err := base64.StdEncoding.DecodeString(sqlCode)
				if err != nil {
					return programArgs, -1, "", errorcode.InvalidParameterValueCode.ReplaceDesc(
						"SqlCode base64 decode error")
				}
				sqlCode = string(sqlCodeBytes)
				sqlCode, err = EncryptAndEncodeSqlCode(sqlCode)
				if err != nil {
					return programArgs, -1, "", err
				}
				delete(m, "SqlCode")
			} else {
				return programArgs, -1, "", errorcode.InvalidParameterValueCode.ReplaceDesc("SqlCode not found")
			}
			argsBytes, err := json.Marshal(m)
			if err != nil {
				return programArgs, -1, "", errorcode.InternalErrorCode.New()
			}
			return string(argsBytes), int64(checkPointInterval), sqlCode, nil
		}
	}
}

func ParseProgramArgsBase64ToJson(programArgs string, jobType int8) (string, error) {
	if len(programArgs) == 0 || jobType != constants.JOB_TYPE_ETL {
		return programArgs, nil
	} else {
		m := map[string]interface{}{}
		if err := json.Unmarshal([]byte(programArgs), &m); err != nil {
			logger.Errorf("ProgramArgs format error. must be json")
			return programArgs, err
		}
		orgProgramArgs := programArgs
		if _, ok := m["EtlJobCanvas"]; ok {
			paramBase64 := m["EtlJobCanvas"].(string)
			paramBytes, err := base64.StdEncoding.DecodeString(paramBase64)
			if err != nil {
				logger.Errorf("The parameter EtlJobCanvas in ProgramArgs decode err")
				return programArgs, err
			}
			m["EtlJobCanvas"] = string(paramBytes)
			resBytes, err := json.Marshal(m)
			if err != nil {
				return programArgs, err
			}
			orgProgramArgs = string(resBytes)
		}
		return orgProgramArgs, nil
	}
}

func ListJobConfigsByIds(jobConfigsIds []int64) ([]*table.JobConfig, error) {
	jobConfigs := make([]*table.JobConfig, 0)
	if len(jobConfigsIds) < 1 {
		logger.Errorf("ListJobConfigsByIds jobConfigIds is zero: %s", errorcode.InternalErrorCode.New().Stack())
		return jobConfigs, nil
	}
	sql := "SELECT * FROM JobConfig WHERE status<>? "
	args := make([]interface{}, 0)
	args = append(args, constants.JOB_CONFIG_STATUS_DELETE)
	sql += " AND id IN ("
	for i := 0; i < len(jobConfigsIds); i++ {
		sql += "?, "
		args = append(args, jobConfigsIds[i])
	}
	sql += "?)"
	args = append(args, jobConfigsIds[len(jobConfigsIds)-1])

	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Exception occurs when query Job from db, with errors:%+v", err)
		return nil, err
	}

	for i := 0; i < len(data); i++ {
		jobConfig := &table.JobConfig{}
		err = util.ScanMapIntoStruct(jobConfig, data[i])
		if err != nil {
			logger.Errorf("Failed to convert bytes into table.jobConfig, with errors:%+v", err)
			return []*table.JobConfig{}, err
		}
		jobConfigs = append(jobConfigs, jobConfig)
	}
	return jobConfigs, nil
}

func CheckResourceRefs(job *table2.Job, refs []*model.ResourceRefItem) (bool, int) {
	cnt := 0
	if job.ManageType == constants.MANAGE_TYPE_EXTERNAL {
		return true, cnt
	}

	for _, ref := range refs {
		if ref.Type == constants.RESOURCE_REF_USAGE_TYPE_MAIN {
			cnt++
		}
	}

	if job.Type == constants.JOB_TYPE_SQL || job.Type == constants.JOB_TYPE_ETL {
		return cnt == 0, cnt
	}
	return cnt == 1, cnt
}

// 获取当前该作业对应的 COSBucket 或集群默认的 COSBucket（一个 ClusterGroup 只有一个 Active 的 Cluster）
func GetRealCOSBucketFromJobConfig(jobConfig *table.JobConfig) (string, error) {
	if jobConfig == nil {
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, "Provided JobConfig is nil", nil)
	}

	if jobConfig.COSBucket != "" {
		return jobConfig.COSBucket, nil
	}

	job, err := service.GetJobById(jobConfig.JobId)
	if err != nil {
		logger.Errorf("Failed to get job by id for %d because %+v", jobConfig.JobId, err)
		return "", err
	}
	cluster, err := service2.GetActiveClusterByClusterGroupId(job.ClusterGroupId)
	if err != nil {
		logger.Errorf("Failed to get cluster by cluster group id for %d because %+v", job.ClusterGroupId, err)
		return "", err
	}
	if cluster.DefaultCOSBucket != "" {
		return cluster.DefaultCOSBucket, nil
	}

	return "", nil
}

func parseCheckpointInterval(config string, sec int64) (time.Duration, error) {
	// 去除首尾空格
	config = strings.TrimSpace(config)

	unitsList := []string{
		"day",
		"days",
		"h",
		"hour",
		"hours",
		"min",
		"minute",
		"minutes",
		"secs",
		"ms",
		"milli",
		"millis",
		"millisecond",
		"milliseconds",
		"µs",
		"micro",
		"micros",
		"microsecond",
		"microseconds",
		"ns",
		"nano",
		"nanos",
		"nanosecond",
		"nanoseconds",
		"s",
		"d",
		"second",
		"seconds",
		"sec",
	}

	// 解析带单位的配置
	units := map[string]time.Duration{
		"day":          24 * time.Hour,
		"days":         24 * time.Hour,
		"h":            time.Hour,
		"hour":         time.Hour,
		"hours":        time.Hour,
		"min":          time.Minute,
		"minute":       time.Minute,
		"minutes":      time.Minute,
		"secs":         time.Second,
		"ms":           time.Millisecond,
		"milli":        time.Millisecond,
		"millis":       time.Millisecond,
		"millisecond":  time.Millisecond,
		"milliseconds": time.Millisecond,
		"µs":           time.Microsecond,
		"micro":        time.Microsecond,
		"micros":       time.Microsecond,
		"microsecond":  time.Microsecond,
		"microseconds": time.Microsecond,
		"ns":           time.Nanosecond,
		"nano":         time.Nanosecond,
		"nanos":        time.Nanosecond,
		"nanosecond":   time.Nanosecond,
		"nanoseconds":  time.Nanosecond,
		"s":            time.Second,
		"d":            24 * time.Hour,
		"second":       time.Second,
		"seconds":      time.Second,
		"sec":          time.Second,
	}

	for _, unit := range unitsList {
		duration := units[unit]
		if strings.HasSuffix(config, unit) {
			valueStr := strings.TrimSuffix(config, unit)
			valueStr = strings.TrimSpace(valueStr)
			value, err := strconv.Atoi(valueStr)
			if err != nil {
				return 0, fmt.Errorf("execution.checkpointing.interval parse failed: %w", err)
			}
			dur := time.Duration(value) * duration
			if dur < time.Duration(sec)*time.Second {
				return 0, errors.New(fmt.Sprintf("execution.checkpointing.interval not allow below %d sec", sec))
			}
			return dur, nil
		}
	}

	// 如果没有找到单位，尝试将其解析为毫秒
	value, err := strconv.Atoi(config)
	if err != nil {
		return 0, errors.New("execution.checkpointing.interval unit need " +
			"DAYS: (d | day | days), HOURS: (h | hour | hours), MINUTES: (min | minute | minutes), SECONDS: (s | sec | secs | second | seconds), MILLISECONDS: (ms | milli | millis | millisecond | milliseconds), MICROSECONDS: (µs | micro | micros | microsecond | microseconds), NANOSECONDS: (ns | nano | nanos | nanosecond | nanoseconds)")
	}
	dur := time.Duration(value) * time.Millisecond
	if dur < time.Duration(sec)*time.Second {
		return 0, errors.New(fmt.Sprintf("execution.checkpointing.interval not allow below %d sec", sec))
	}
	return dur, nil
}

func CheckJobConfigProperties(tkeClusterType int, appId int64, properties []*model2.Property, defaultParallelism int16) (error error) {
	if len(properties) == 0 {
		return nil
	}

	// 校验是否包含不支持的参数
	unsupportedFlinkConf := make([]string, 0)

	isTke := tkeClusterType == constants.K8S_CLUSTER_TYPE_TKE

	inWhiteList, w := auth.WhiteListValue(appId, constants.WHITE_LIST_OPEN_FLINK_CONFIGURATION)

	for _, property := range properties {
		if property.Key == constants.EXECUTION_CHECKPOINTING_INTERVAL {
			if auth.IsInWhiteList(appId, constants.WHITE_LIST_SUPPORT_LOW_INTERVAL_CHECK_POINT) {
				_, err := parseCheckpointInterval(property.Value, constants.WHITE_LIST_CHECKPOINT_INTERVAL_LOWER_LIMIT)
				if err != nil {
					return err
				}
			} else {
				_, err := parseCheckpointInterval(property.Value, constants.CHECKPOINT_INTERVAL_LOWER_LIMIT)
				if err != nil {
					return err
				}
			}
		}

		if strings.Contains(property.Value, "/var/run") {
			msg := fmt.Sprintf("key: %s, value: %s", property.Key, property.Value)
			return errorcode.InvalidParameterCode_UnsupportedFlinkConf.ReplaceDesc(msg)
		}

		key := property.Key
		if inWhiteList {
			whiteListFlink, err := w.GetStringListVal()
			if err != nil {
				return err
			}
			if service3.InSliceString(key, whiteListFlink) {
				continue
			}
		}

		if _, exist := constants.WhiteListFlinkConfiguration[strings.TrimSpace(key)]; inWhiteList && exist {
			continue
		}
		if _, exist := constants.WhiteListFlinkConfigurationForTke[strings.TrimSpace(key)]; isTke && inWhiteList && exist {
			continue
		}
		if _, exist := constants.ProhibitedFlinkConfiguration[strings.TrimSpace(key)]; exist {
			unsupportedFlinkConf = append(unsupportedFlinkConf, key)
		}
	}

	if len(unsupportedFlinkConf) > 0 {
		return errorcode.InvalidParameterCode_UnsupportedFlinkConf.ReplaceDesc(
			strings.Join(unsupportedFlinkConf, ","))
	}

	// 如果用户设置了 pipeline.max-parallelism, 校验合法性
	_, _, err := GetMaxParallelism(defaultParallelism, properties, false)
	if err != nil {
		return err
	}

	return nil
}

func CheckCpuMem(appid int32, cluster *table3.Cluster, jmCpu, jmMem, tmCpu, tmMem float32) (ok bool, err error) {
	if jmCpu == 0 && jmMem == 0 && tmCpu == 0 && tmMem == 0 {
		return true, nil
	}
	if !auth.IsInWhiteList(int64(appid), constants.WHITE_LIST_OPEN_FINE_GRAINED) {
		if jmCpu < constants.FINEGRAINEDRESOURCE_05 || tmCpu < constants.FINEGRAINEDRESOURCE_05 {
			return false, nil
		}
		if jmMem < constants.FINEGRAINEDRESOURCE_05*float32(cluster.MemRatio) || tmMem < constants.FINEGRAINEDRESOURCE_05*float32(cluster.MemRatio) {
			return false, nil
		}
	}
	return true, nil
}

func CheckPodSpecs(appid int32, cluster *table3.Cluster, jmSpec, tmSpec float32) (ok bool, err error) {
	if jmSpec == 0.0 && tmSpec == 0.0 {
		return true, nil
	}

	supportedSpecs := &model.DescribePodSpecsRsp{
		JobManagerSpec:  []float32{1.0},
		TaskManagerSpec: []float32{1.0},
	}
	yes, err := service2.SupportFineGrainedResource(cluster)
	if err != nil {
		return
	}
	if yes {
		group := constants.ConfRainbowGroupCommon
		if err = config.DecodeK8sObjectFromRainbowConfig(group, constants.ConfRainbowKeyFineGrainedResourcePodSpecs,
			supportedSpecs); err != nil {
			return
		}
	}

	if auth.IsInWhiteList(int64(appid), constants.WHITE_LIST_OPEN_FINE_GRAINED) {
		var smallerCu float32 = 0.25
		supportedSpecs.JobManagerSpec = append(supportedSpecs.JobManagerSpec, smallerCu)
		supportedSpecs.TaskManagerSpec = append(supportedSpecs.TaskManagerSpec, smallerCu)
	}

	if auth.IsInWhiteList(int64(appid), constants.WHITE_LIST_OPEN_NON_STANDARD_POD_SPEC) {

		nonStandardPodSpecs := &model.DescribePodSpecsRsp{
			JobManagerSpec:  []float32{},
			TaskManagerSpec: []float32{},
		}

		group := constants.ConfRainbowGroupCommon
		if err = config.DecodeK8sObjectFromRainbowConfig(group, constants.ConfRainbowKeyNonStandardPodSpecs,
			nonStandardPodSpecs); err != nil {
			return
		}

		for _, spec := range nonStandardPodSpecs.JobManagerSpec {
			supportedSpecs.JobManagerSpec = append(supportedSpecs.JobManagerSpec, spec)
		}
		for _, spec := range nonStandardPodSpecs.TaskManagerSpec {
			supportedSpecs.TaskManagerSpec = append(supportedSpecs.TaskManagerSpec, spec)
		}

	}

	if jmSpec != 0.0 && !service3.InSliceFloat32(jmSpec, supportedSpecs.JobManagerSpec) {
		return false, nil
	}
	if tmSpec != 0.0 && !service3.InSliceFloat32(tmSpec, supportedSpecs.TaskManagerSpec) {
		return false, nil
	}
	return true, nil
}

func AddExCatalogToProgramArgs(programElements string, exCatalogs []*metadata_model.ExternalCatalogV1) (resultStr string, err error) {
	if programElements != "" && len(exCatalogs) > 0 {
		params := map[string]interface{}{}
		err := json.Unmarshal([]byte(programElements), &params)
		if err != nil {
			logger.Errorf("Failed to parse program params %s, with errors:%+v", programElements, err)
			return programElements, err
		}
		outerMetadatastr, ok := params["Metadata"] // outer (base64)
		if !ok {
			outerMetadatastr = "{}"
		} else {
			outerMetadataBytes, err := base64.StdEncoding.DecodeString(outerMetadatastr.(string))
			if err != nil {
				logger.Errorf("Failed to parse program params %s, with errors:%+v", programElements, err)
				return programElements, err
			}
			outerMetadatastr = string(outerMetadataBytes)
		}
		outerMetadataMap := map[string]interface{}{}
		err = json.Unmarshal([]byte(outerMetadatastr.(string)), &outerMetadataMap)
		if err != nil {
			logger.Errorf("Failed to parse metadata %s, with errors:%+v", outerMetadatastr, err)
			return programElements, err
		}
		catalogs, err := service5.BuildMetaCatalogs(exCatalogs)
		if err != nil {
			logger.Errorf("Failed to Build  meta catalog %s, with errors:%+v", exCatalogs, err)
			return programElements, err
		}
		catalogsBytes, err := json.Marshal(catalogs)
		if err != nil {
			logger.Errorf("Failed to Build  marshal catalog %s, with errors:%+v", catalogs, err)
			return programElements, err
		}
		innerMetadata, ok := outerMetadataMap["Metadata"] // inner
		if !ok {
			innerMetadata = make(map[string]string, 0)
		}
		innerMetadataMap := map[string]interface{}{}
		marshal, err := json.Marshal(innerMetadata)
		if err != nil {
			logger.Errorf("Failed to Build  marshal catalog %s, with errors:%+v", catalogs, err)
			return programElements, err
		}
		err = json.Unmarshal(marshal, &innerMetadataMap)
		if err != nil {
			logger.Errorf("Failed to parse metadata %s, with errors:%+v", innerMetadata, err)
			return programElements, err
		}
		catalogs2 := make([]*metadata_model.Catalog, 0)
		err = json.Unmarshal(catalogsBytes, &catalogs2)
		if err != nil {
			logger.Errorf("Failed to parse metadata %s, with errors:%+v", innerMetadata, err)
			return programElements, err
		}
		innerMetadataMap["Catalogs"] = catalogs2
		outerMetadataMap["Metadata"] = innerMetadataMap
		outerMetadataBytes, err := json.Marshal(outerMetadataMap)
		if err != nil {
			logger.Errorf("Failed to Build  marshal metadatas %s, with errors:%+v", params, err)
			return programElements, err
		}
		outerMetadataBytesBase64 := base64.StdEncoding.EncodeToString(outerMetadataBytes)
		params["Metadata"] = outerMetadataBytesBase64
		argsBytes, err := json.Marshal(params)
		if err != nil {
			logger.Errorf("Failed to Build  marshal params %s, with errors:%+v", params, err)
			return programElements, err
		}
		return string(argsBytes), nil
	} else {
		return programElements, nil
	}
}

// deprecated
func AttachTableVersionToProgramArgs(programElements string, uin string, appId int32, region string, itemSpaceId int64) (args string, err error) {
	params := map[string]interface{}{}
	err = json.Unmarshal([]byte(programElements), &params)
	if err != nil {
		logger.Errorf("Failed to parse program params %s, with errors:%+v", programElements, err)
		return programElements, err
	}
	metadatastr, ok := params["Metadata"] // outer (base64)
	if !ok {
		return programElements, nil
	} else {
		metadataBytes, err := base64.StdEncoding.DecodeString(metadatastr.(string))
		if err != nil {
			logger.Errorf("Failed to decode  metadata %s, with errors:%+v", programElements, err)
			return programElements, err
		}
		metadataV1 := &metadata_model.MetadataV1{}
		err = json.Unmarshal(metadataBytes, metadataV1)
		if err != nil {
			logger.Errorf("Failed to parse metadata %s, with errors:%+v", programElements, err)
			return programElements, err
		}
		for _, mtable := range metadataV1.Metadata.ReferenceTables {
			metaTable, err := service5.GetMetaTableByName(mtable.Catalog, mtable.Database, mtable.Table, uin, int64(appId), region, itemSpaceId)
			if err != nil {
				logger.Errorf("Failed to get meta table %s, with errors:%+v", programElements, err)
				return programElements, err
			}
			mtable.Version = metaTable.Version
		}
		metadataV1JsonBytes, err := json.Marshal(metadataV1)
		if err != nil {
			logger.Errorf("Failed to marshal  metadata %s, with errors:%+v", programElements, err)
			return programElements, err
		}
		params["Metadata"] = base64.StdEncoding.EncodeToString(metadataV1JsonBytes)
		argsBytes, err := json.Marshal(params)
		if err != nil {
			logger.Errorf("Failed to Build  marshal params %s, with errors:%+v", params, err)
			return programElements, err
		}
		return string(argsBytes), nil
	}
}

func ValidateExpertModeConfiguration(conf *model.ExpertModeConfiguration) error {
	if conf == nil {
		return errorcode.InvalidParameterValueCode.ReplaceDesc(
			"ExpertModeConfiguration can't be null when ExpertModeOn is true.")
	}
	// todo validate StateTTL format
	if err := validateNodeSlotSharingGroup(conf); err != nil {
		return err
	}

	for _, ssg := range conf.SlotSharingGroups {
		if err := validateSlotSharingGroupSpec(ssg); err != nil {
			return err
		}
	}

	return nil
}

func validateNodeSlotSharingGroup(conf *model.ExpertModeConfiguration) error {
	ssgNameSet := make(map[string]struct{})
	for _, ssg := range conf.SlotSharingGroups {
		if ssg.Name == "default" {
			if ssg.Spec.CPU == 0 {
				logger.Warningf("SlotSharingGroup default is not set.")
				ssg.Spec.CPU = 1
			}
			if ssg.Spec.HeapMemory == "" {
				logger.Warningf("SlotSharingGroup default is not set.")
				ssg.Spec.HeapMemory = "1500mb"
			}
			if ssg.Spec.ManagedMemory == "" {
				logger.Warningf("SlotSharingGroup default is not set.")
				ssg.Spec.ManagedMemory = "1500mb"
			}
			continue
		}
		_, ok := ssgNameSet[ssg.Name]
		if ok {
			return errorcode.InvalidParameterValueCode.ReplaceDesc(fmt.Sprintf("SlotSharingGroup Name %s duplicate",
				ssg.Name))
		}
		ssgNameSet[ssg.Name] = struct{}{}
	}

	for _, n := range conf.NodeConfig {
		if n.SlotSharingGroup == "" || n.SlotSharingGroup == "default" {
			continue
		}
		_, ok := ssgNameSet[n.SlotSharingGroup]
		if !ok {
			return errorcode.InvalidParameterValueCode.ReplaceDesc(fmt.Sprintf("SlotSharingGroup %s not found",
				n.SlotSharingGroup))
		}
	}
	return nil
}

func validateSlotSharingGroupSpec(slot *model.SlotSharingGroup) error {
	if slot.Name == "default" {
		return nil
	}

	if slot.Spec.CPU < 0 {
		return errorcode.InvalidParameterValueCode.ReplaceDesc(fmt.Sprintf("SlotSharingGroup %s CPU must great than 0",
			slot.Description))
	}

	if ok, msg := validateMemorySize(slot.Spec.HeapMemory); !ok {
		return errorcode.InvalidParameterValueCode.ReplaceDesc(fmt.Sprintf("%s. HeadpMemory %s", slot.Description, msg))
	}

	if slot.Spec.OffHeapMemory != "" {
		if ok, msg := validateMemorySize(slot.Spec.HeapMemory); !ok {
			return errorcode.InvalidParameterValueCode.ReplaceDesc(fmt.Sprintf("%s. HeadpMemory %s", slot.Description, msg))
		}
	}

	if slot.Spec.ManagedMemory != "" {
		if ok, msg := validateMemorySize(slot.Spec.HeapMemory); !ok {
			return errorcode.InvalidParameterValueCode.ReplaceDesc(fmt.Sprintf("%s. HeadpMemory %s", slot.Description, msg))
		}
	}
	return nil
}

func validateMemorySize(input string) (bool, string) {
	// Regular expression pattern to match the memory unit format
	pattern := `^\d+(b|bytes|k|kb|kibibytes|m|mb|mebibytes|g|gb|gibibytes|t|tb|tebibytes)?$`
	// Compile the pattern
	re, err := regexp.Compile(pattern)
	if err != nil {
		return false, "Error compiling regex"
	}
	// Match the input string against the pattern
	if re.MatchString(input) {
		return true, ""
	}
	return false, fmt.Sprintf("Invalid memory unit format: %s", input)
}
