package service

import (
	"fmt"
	model2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model"
	"testing"

	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job_config"
)

func TestDeleteJobConfigs(t *testing.T) {
	type args struct {
		jobId             int64
		jobConfigVersions []int16
		uin               string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := DeleteJobConfigs(tt.args.jobId, tt.args.jobConfigVersions, tt.args.uin); (err != nil) != tt.wantErr {
				t.Errorf("DeleteJobConfigs() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestQueryJobConfigs(t *testing.T) {
	init_txManager(t)
	type args struct {
		jobId             int64
		jobConfigVersions []int16
	}
	tests := []struct {
		name           string
		args           args
		wantJobConfigs []*table.JobConfig
		wantErr        bool
	}{
		// TODO: Add test cases.
		{
			name: "test-case-1",
			args: args{
				jobId:             1664,
				jobConfigVersions: []int16{2},
			},
			wantErr: false,
		},
		{
			name: "test-case-2",
			args: args{
				jobId:             4,
				jobConfigVersions: []int16{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19},
			},
			wantErr: false,
		},
	}
	txManager.Ping()
	if txManager == nil {
		fmt.Println("txManager nil")
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotJobConfigs, err := QueryJobConfigs(tt.args.jobId, tt.args.jobConfigVersions)
			if (err != nil) != tt.wantErr {
				t.Errorf("QueryJobConfigs() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			t.Logf("len jobCongis %d", len(gotJobConfigs))
			for _, jc := range gotJobConfigs {
				t.Logf("jobConfig %+v", jc)
			}
		})
	}

}

func Test_validateMemorySize(t *testing.T) {
	testCases := []struct {
		size     string
		expected bool
	}{
		{"100", true}, // 缺少单位仍然合法
		{"100b", true},
		{"100B", false},
		{"100k", true},
		{"100K", false},
		{"100m", true},
		{"100M", false},
		{"100g", true},
		{"100G", false},
		{"100t", true},
		{"100T", false},
		{"100", true},     // 缺少单位仍然合法
		{"100KB", false},  // 不支持的单位
		{"abc", false},    // 非法字符
		{"100b ", false},  // 额外的空格
		{" 100b", false},  // 额外的空格
		{"-100b", false},  // 负数
		{"100.5k", false}, // 浮点数
		{"1 00k", false},  // 空格
		{"1_00k", false},  // 下划线
		{"100kB", false},  // 不支持的单位
		{"100k ", false},  // 额外的空格
		{" 100k", false},  // 额外的空格
		{"100.5M", false}, // 浮点数
		{"1 00M", false},  // 空格
		{"1_00M", false},  // 下划线
		{"100MB", false},  // 不支持的单位
		{"100m ", false},  // 额外的空格
		{" 100m", false},  // 额外的空格
		{"1kk", false},
	}

	for _, tc := range testCases {
		result, _ := validateMemorySize(tc.size)
		if result != tc.expected {
			t.Errorf("Mismatched result for size: %s. Expected %v, got %v", tc.size, tc.expected, result)
		}
	}
}

func TestListJobConfigs(t *testing.T) {
	init_txManager(t)
	type args struct {
		jobId             int64
		jobConfigVersions []int16
	}
	tests := []struct {
		name           string
		args           args
		wantJobConfigs []*table.JobConfig
		wantErr        bool
	}{
		// TODO: Add test cases.
		{
			name: "test-case-1",
			args: args{
				jobId:             1,
				jobConfigVersions: []int16{2},
			},
			wantErr: false,
		},
		{
			name: "test-case-2",
			args: args{
				jobId:             1,
				jobConfigVersions: []int16{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19},
			},
			wantErr: false,
		},
	}
	txManager.Ping()
	if txManager == nil {
		fmt.Println("txManager nil")
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotJobConfigs, err := ListJobConfigs(tt.args.jobId, nil, nil, nil)
			if (err != nil) != tt.wantErr {
				t.Errorf("QueryJobConfigs() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			t.Logf("len jobCongis %d", len(gotJobConfigs))
			for _, jc := range gotJobConfigs {
				t.Logf("jobConfig %+v", jc)
			}
		})
	}

}
func TestName(t *testing.T) {
	s := "{\"CheckpointInterval\":600,\"Metadata\":\"eyJNZXRhZGF0YSI6eyJSZWZlcmVuY2VUYWJsZXMiOltdLCJWYXJpYWJsZXMiOlt7IkNhdGFsb2ciOiJfZGVmYXVsdCIsIkRhdGFiYXNlIjoiX2RlZmF1bHQiLCJUYWJsZSI6ImRhdGFnZW5fc291cmNlX3RhYmxlIiwiVHlwZSI6MiwiVmFyaWFibGVFbnRyaWVzIjpbeyJLZXkiOiJjb25uZWN0b3IiLCJQbGFjZWhvbGRlciI6InJvd3NfcGVyX3NlY29uZF92YXIiLCJWYWx1ZSI6ImRhdGFnZW4iLCJWYXJpYWJsZUlkIjoiIn0seyJLZXkiOiJyb3dzLXBlci1zZWNvbmQiLCJQbGFjZWhvbGRlciI6Inh4eHgxIiwiVmFsdWUiOiIyIiwiVmFyaWFibGVJZCI6IiJ9LHsiS2V5Ijoicm93cy1wZXItc2Vjb25kIiwiUGxhY2Vob2xkZXIiOiJ4eHh4MiIsIlZhbHVlIjoiMiIsIlZhcmlhYmxlSWQiOiIifV19XX19\\\",\\\"SqlCode\\\":\\\"Q1JFQVRFIFRBQkxFIGRhdGFnZW5fc291cmNlX3RhYmxlICggCiAgICBhZ2UgSU5ULCAKICAgIHNjb3JlIElOVCAKKSBXSVRIICgKJ3Jvd3MtcGVyLXNlY29uZCc9JyR7eHh4eDE6MjExMTF9JHt4eHh4MjoyMX0nLCdjb25uZWN0b3InID0gJyR7cm93c19wZXJfc2Vjb25kX3ZhcjpkYXRhZ2VufScKKTsKCgpDUkVBVEUgVEFCTEUgYERhdGFfT3V0cHV0YCAoIC0t5q2l6aqkIDIg77ya5Yib5bu65pWw5o2u57uT5p6c6KGo77yIU2lua++8iSBEYXRhX091dHB1dAogICAgYGF2Z19hZ2VgIEJJR0lOVCwKICAgIGBhdmdfc2NvcmVgIEJJR0lOVAopIFdJVEggKAogICAgJ2Nvbm5lY3RvcicgPSAnYmxhY2tob2xlJwopOwoKSU5TRVJUIElOVE8gYERhdGFfT3V0cHV0YCAgIC0t5q2l6aqkIDMg77yaIOWwhuaVsOaNrua6kOihqO+8iFNvdXJjZe+8iSBEYXRhX0ludHB1dCDkuK3nmoQgYWdlIOWSjCBzY29yZSDlj5blubPlnYfmlbDkuYvlkI7lrZjlgqjkuo7mlbDmja7nu5PmnpzooajvvIhTaW5r77yJIERhdGFfT3V0cHV0ClNFTEVDVCBBVkcoYWdlKSwgQVZHKHNjb3JlKSBGUk9NIGBkYXRhZ2VuX3NvdXJjZV90YWJsZWA7Cg==\"}"
	json, err := ParseProgramArgsBase64ToJson(s, 1)
	if err != nil {
		return
	}
	fmt.Println(json)
	args, checkpoint, sql, err := ExtractElementsFromProgramArgs(json, 1)
	fmt.Println(args)
	fmt.Println(checkpoint)
	fmt.Println(sql)
	if err != nil {
		return
	}
}

func TestCheckJobConfigProperties(t *testing.T) {
	type args struct {
		tkeClusterType     int
		appId              int64
		properties         []*model2.Property
		defaultParallelism int16
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test-case-1",
			args: args{
				tkeClusterType: 0,
				appId:          1257058945,
				properties: []*model2.Property{
					{
						Key:   "execution.checkpointing.interval",
						Value: "3000000 ms",
					},
				},
				defaultParallelism: 1,
			},
			wantErr: false,
		},
		// TODO: Add test cases.
		{
			name: "test-case-1",
			args: args{
				tkeClusterType: 0,
				appId:          1257058945,
				properties: []*model2.Property{
					{
						Key:   "execution.checkpointing.interval",
						Value: "3000000 ms",
					},
				},
				defaultParallelism: 1,
			},
			wantErr: false,
		}, // TODO: Add test cases.
		{
			name: "test-case-1",
			args: args{
				tkeClusterType: 0,
				appId:          1257058945,
				properties: []*model2.Property{
					{
						Key:   "execution.checkpointing.interval",
						Value: "3000000 ms",
					},
				},
				defaultParallelism: 1,
			},
			wantErr: false,
		}, // TODO: Add test cases.
		{
			name: "test-case-1",
			args: args{
				tkeClusterType: 0,
				appId:          1257058945,
				properties: []*model2.Property{
					{
						Key:   "execution.checkpointing.interval",
						Value: "3000000 ms",
					},
				},
				defaultParallelism: 1,
			},
			wantErr: false,
		}, // TODO: Add test cases.
		{
			name: "test-case-1",
			args: args{
				tkeClusterType: 0,
				appId:          1257058945,
				properties: []*model2.Property{
					{
						Key:   "execution.checkpointing.interval",
						Value: "3000000 ms",
					},
				},
				defaultParallelism: 1,
			},
			wantErr: false,
		}, // TODO: Add test cases.
		{
			name: "test-case-1",
			args: args{
				tkeClusterType: 0,
				appId:          1257058945,
				properties: []*model2.Property{
					{
						Key:   "execution.checkpointing.interval",
						Value: "3000000 ms",
					},
				},
				defaultParallelism: 1,
			},
			wantErr: false,
		}, // TODO: Add test cases.
		{
			name: "test-case-1",
			args: args{
				tkeClusterType: 0,
				appId:          1257058945,
				properties: []*model2.Property{
					{
						Key:   "execution.checkpointing.interval",
						Value: "3000000 ms",
					},
				},
				defaultParallelism: 1,
			},
			wantErr: false,
		},
		{
			name: "test-case-2",
			args: args{
				tkeClusterType: 0,
				appId:          1257058945,
				properties: []*model2.Property{
					{
						Key:   "execution.checkpointing.interval",
						Value: "10 s",
					},
				},
				defaultParallelism: 1,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := CheckJobConfigProperties(tt.args.tkeClusterType, tt.args.appId, tt.args.properties, tt.args.defaultParallelism); err != nil {
				t.Errorf("CheckJobConfigProperties() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
