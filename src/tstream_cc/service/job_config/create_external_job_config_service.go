package service

import (
	"context"
	"fmt"

	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	model1 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/item_space"
)

func DoCreateExternakJobConfig(req *model.CreateJobConfigReq) (rsp *model.CreateJobConfigRsp, err error) {
	listJobQuery := model1.ListJobQuery{
		SerialIds:    []string{req.JobId},
		IsVagueNames: false,
		Offset:       0,
		Limit:        0,
		ManageType:   constants.MANAGE_TYPE_EXTERNAL,
	}
	jobs, err := service.ListJobs(&listJobQuery)
	if err != nil {
		return nil, err
	} else if len(jobs) > 1 {
		return nil, errorcode.InternalErrorCode_UnexpectedRecordNums.NewWithInfo(req.JobName, nil)
	} else if len(jobs) == 0 {
		msg := fmt.Sprintf("JobNotFound: Job with jobName: %s", req.JobName)
		return nil, errorcode.ResourceNotFound_Job.ReplaceDesc(msg)
	}
	job := jobs[0]
	req.Uin = job.OwnerUin
	req.SubAccountUin = job.CreatorUin
	req.AppId = job.AppId
	req.Region = job.Region
	req.JobId = job.SerialId
	itemSpace, err := item_space.GetItemSpaceByItemId(job.ItemSpaceId)
	if err != nil {
		return nil, err
	}
	req.WorkSpaceId = itemSpace.SerialId
	return DoCreateJobConfig(context.Background(), req)
}
