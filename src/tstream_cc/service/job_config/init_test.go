package service

import (
	"flag"
	"io/ioutil"
	"os"
	"path"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource"
	"testing"

	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
)

var (
	fTestRegion = flag.String("test.region", "ap-nanjing",
		"e.g. ap-guangzhou ap-beijing ap-shanghai ap-chengdu")
	fTestAppid  = flag.Int64("test.appid", 1257058945, "")
	fTestJobId  = flag.String("test.jobid", "", "cql-xxx")
	fTestJmSpec = flag.Float64("test.jm-spec", 0.5, "0.5 1.0 2.0")
	fTestTmSpec = flag.Float64("test.tm-spec", 0.5, "0.5 1.0 2.0")

	fTestDbUrl = flag.String("test.db.url", "root:root@tcp(127.0.0.1:3306)/online_galileo?charset=utf8", "")

	fTestKubeConfig = flag.String("test.kube.config", path.Join(os.Getenv("HOME"), ".kube/config"), "")
)

var (
	txManager *dao.DataSourceTransactionManager

	kubeConfig []byte
)

func init() {
	//testing.Init()
	//flag.Parse()
	service2.InitTestDB(service2.WALLYDB)

}

func init_txManager(t *testing.T) {
	if txManager != nil {
		return
	}
	tx, err := dao.NewDataSourceTransactionManager(*fTestDbUrl)
	if err != nil {
		t.Fatal("darwin Process create DataSourceTransactionManager err", err)
	}
	service.SetTxManager(tx)
	txManager = tx
}

func init_kubConfig(t *testing.T) {
	if kubeConfig != nil {
		return
	}
	if env := os.Getenv("KUBECONFIG"); len(env) > 0 {
		fTestKubeConfig = &env
	}
	var err error
	kubeConfig, err = ioutil.ReadFile(*fTestKubeConfig)
	if err != nil {
		t.Fatalf("fail to read %s, %v", *fTestKubeConfig, err)
		return
	}
}
