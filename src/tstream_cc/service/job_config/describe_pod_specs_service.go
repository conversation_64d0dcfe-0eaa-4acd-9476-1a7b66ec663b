package service

import (
	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_config"
	common "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config"
)

type DescribePodSpecsService struct {
	request *model.DescribePodSpecsReq
}

func (o *DescribePodSpecsService) DescribePodSpecs() (rsp *model.DescribePodSpecsRsp, err error) {
	//0.安全检测，ClusterId是对应账号的
	err = common.CheckClusterIdWithAppId(o.request.ClusterId, o.request.AppId)
	if err != nil {
		return nil, err
	}
	clusterService, err := service.NewClusterGroupServiceBySerialId(o.request.ClusterId)
	if err != nil {
		return
	}
	cluster, err := clusterService.GetActiveCluster()
	if err != nil {
		return
	}
	if yes, err := service.SupportFineGrainedResource(cluster); err != nil {
		return nil, err
	} else if !yes {
		return &model.DescribePodSpecsRsp{
			JobManagerSpec:  []float32{1.0},
			TaskManagerSpec: []float32{1.0},
		}, nil
	}
	group := constants.ConfRainbowGroupCommon

	rsp = &model.DescribePodSpecsRsp{}
	if err = config.DecodeK8sObjectFromRainbowConfig(group, constants.ConfRainbowKeyFineGrainedResourcePodSpecs,
		rsp); err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}
	// 白名单开放 0.25 cu
	o.CheckOpenSmallerFineness(rsp)
	// 非标准cu
	if err = o.CheckNonStandardPodSpecs(rsp); err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}

	util.Float32s(rsp.JobManagerSpec)
	util.Float32s(rsp.TaskManagerSpec)

	return
}

func (o *DescribePodSpecsService) CheckOpenSmallerFineness(rsp *model.DescribePodSpecsRsp) {
	if auth.IsInWhiteList(o.request.AppId, constants.WHITE_LIST_OPEN_FINE_GRAINED) {
		JM := []float32{constants.FINEGRAINEDRESOURCE_025}
		Tm := []float32{constants.FINEGRAINEDRESOURCE_025}
		JM = append(JM, rsp.JobManagerSpec...)
		Tm = append(Tm, rsp.TaskManagerSpec...)
		rsp.JobManagerSpec = JM
		rsp.TaskManagerSpec = Tm
		return
	}
	return
}

func (o *DescribePodSpecsService) CheckNonStandardPodSpecs(rsp *model.DescribePodSpecsRsp) (err error) {
	if !auth.IsInWhiteList(o.request.AppId, constants.WHITE_LIST_OPEN_NON_STANDARD_POD_SPEC) {
		return nil
	}

	nonStandardPodSpecs := &model.DescribePodSpecsRsp{
		JobManagerSpec:  []float32{},
		TaskManagerSpec: []float32{},
	}

	group := constants.ConfRainbowGroupCommon
	if err = config.DecodeK8sObjectFromRainbowConfig(group, constants.ConfRainbowKeyNonStandardPodSpecs,
		nonStandardPodSpecs); err != nil {
		return
	}

	for _, spec := range nonStandardPodSpecs.JobManagerSpec {
		rsp.JobManagerSpec = append(rsp.JobManagerSpec, spec)
	}
	for _, spec := range nonStandardPodSpecs.TaskManagerSpec {
		rsp.TaskManagerSpec = append(rsp.TaskManagerSpec, spec)
	}

	return
}

func NewDescribePodSpecsService(req *model.DescribePodSpecsReq) *DescribePodSpecsService {
	return &DescribePodSpecsService{request: req}
}
