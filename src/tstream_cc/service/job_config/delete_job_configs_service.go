package service

import (
	"context"
	"fmt"

	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	service "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	clusterService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"

	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	jobModel "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_config"
	cluster "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job_config"
	tableInstance "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/job_instance"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	cosService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cos/handler"
	service5 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/metadata"

	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/variable"
)

func DoDeleteJobConfigs(req *model.DeleteJobConfigsReq) (string, string, *model.DeleteJobConfigsRsp) {
	//草稿配置不允许删除
	jobConfigVersions := req.JobConfigVersions
	for i := 0; i < len(jobConfigVersions); i++ {
		if jobConfigVersions[i] == constants.JOB_CONFIG_DRAFT_VERSION {
			logger.Warningf("%s: Can not delete the draft config...", req.RequestId)
			return controller.FailedOperation_DraftConfigCanNotDelete, controller.NULL, nil
		}
	}
	listJobQuery := jobModel.ListJobQuery{
		AppId:             req.AppId,
		SerialIds:         []string{req.JobId},
		JobIds:            []int64{},
		Names:             []string{},
		IsVagueNames:      false,
		Status:            []int64{},
		ClusterGroupIds:   []string{},
		Offset:            0,
		Limit:             20,
		ClusterGroupNames: nil,
	}
	jobs, err := service2.ListJobs(&listJobQuery)
	if err != nil {
		logger.Errorf("%s: Failed to get jobs, with job id:%s, with errors:%+v", req.RequestId, req.JobId, err)
		return controller.InternalError, controller.NULL, nil
	}
	if len(jobs) == 0 {
		logger.Errorf("%s: Failed to get jobs, with query result is empty.", req.RequestId)
		return controller.ResourceNotFound, controller.NULL, nil
	}
	job := jobs[0]

	// 鉴权
	err = auth.InnerAuthById(req.WorkSpaceId, req.IsSupOwner, job.ItemSpaceId, int64(req.AppId), req.SubAccountUin, req.Action, false)
	if err != nil {
		logger.Errorf("%s: User  uin %s is NOT authenticated, refuse to serve", req.RequestId, req.Uin)
		errCode := errorcode.GetCode(err)
		return errCode.GetCodeStr(), errCode.GetCodeDesc(), nil
	}

	//作业处于运行中，暂停中，且与对应的JobConfig有关联，则不允许删除
	if job.Status == constants.JOB_STATUS_PAUSED || job.Status == constants.JOB_STATUS_RUNNING {
		publishedJobConfigId := job.PublishedJobConfigId
		lastPublishedJobConfigId := job.LastPublishedJobConfigId
		relatedJobConfigs, err := ListJobConfigs(job.Id, []int64{publishedJobConfigId, lastPublishedJobConfigId}, []int64{}, nil)
		if err != nil {
			logger.Errorf("%s: Failed to query job configs, with jobIds: [%d, %d], with errors: %+v", req.RequestId, publishedJobConfigId, lastPublishedJobConfigId, err)
			return controller.InternalError, controller.NULL, nil
		}
		for _, jc := range relatedJobConfigs {
			logger.Errorf("relatedJobConfigVersions:%+v", jc)
		}
		for i := 0; i < len(req.JobConfigVersions); i++ {
			for j := 0; j < len(relatedJobConfigs); j++ {
				if relatedJobConfigs[j].VersionId == req.JobConfigVersions[i] {
					logger.Errorf("%s: Failed to delete job config version, it is related to some running/paused jobs", req.RequestId)
					return controller.FailedOperation_JobConfigOnPublish, fmt.Sprintf("Job Config %d related by Job: %s", publishedJobConfigId, job.SerialId), nil
				}
			}
		}
	}

	for _, version := range req.JobConfigVersions {
		req.JobConfigVersions = append(req.JobConfigVersions, -version*10)
	}

	err = DeleteJobConfigs(job.Id, req.JobConfigVersions, req.SubAccountUin)
	if err != nil {
		logger.Errorf("%s: Failed to delete job configs, with jobId:%d, with job config versions: %+v, with errors: %+v", req.RequestId, req.JobId, req.JobConfigVersions, err)
		return controller.InternalError, controller.NULL, nil
	}
	// 删除Job的变量引用关系
	variableRefs, err := variable.DeleteVariableRefs(job.Id, req.JobConfigVersions)
	if err != nil {
		logger.Errorf("%s: Failed to delete VariableReference of jobs, error: %+v", req.RequestId, err)
		return controller.InternalError, controller.NULL, nil
	}
	if !variableRefs {
		logger.Errorf("%s: Failed to delete VariableReference of jobs, success: %+v", req.RequestId, variableRefs)
		return controller.InternalError, controller.NULL, nil
	}
	err = service5.DeleteMetaTableLineageByJobConfigId(job.Id, req.JobConfigVersions)
	if err != nil {
		logger.Errorf("%s: Failed to delete meta table lineage of jobs, error: %+v", req.RequestId, err)
		return controller.InternalError, controller.NULL, nil
	}

	jobConfigs, err := QueryJobConfigs(job.Id, req.JobConfigVersions)
	if err != nil {
		logger.Errorf("%s: Failed to QueryJobConfigs, with jobId:%d, with job config versions: %+v, with errors: %+v", req.RequestId, req.JobId, req.JobConfigVersions, err)
		return controller.InternalError, controller.NULL, nil
	}
	jobConfigIds := make([]int64, 0)
	for _, jc := range jobConfigs {
		jobConfigIds = append(jobConfigIds, jc.Id)
	}
	instances, err := service.GetJobInstanceByJobConfigs(job.Id, jobConfigIds, constants.JOB_INSTANCE_STATUS_HISTORY)
	if err != nil {
		logger.Errorf("%s: Failed to GetJobInstanceByJobConfigs, with jobId:%d, with job config versions: %+v, with errors: %+v", req.RequestId, req.JobId, jobConfigIds, err)
		return controller.InternalError, controller.NULL, nil
	}
	clusterIns, err := clusterService.GetClusterByJobId(job.SerialId)
	if err != nil {
		logger.Errorf("%s: Failed to GetJobInstanceByJobConfigs, with jobId:%d, with job config versions: %+v, with errors: %+v", req.RequestId, req.JobId, jobConfigIds, err)
	}
	// 删除COS上传过的文件
	err = DeleteCOSFile(instances, req, clusterIns)
	if err != nil {
		logger.Errorf("%s: Failed to DeleteCOSFile, err %v", req.RequestId, err)
	}
	deleteJobConfigsRsp := &model.DeleteJobConfigsRsp{}
	return controller.OK, controller.NULL, deleteJobConfigsRsp
}

func DeleteEarliestCanBeDelJobConfig(ctx context.Context, job *table.Job) (ok bool, err error) {
	cond := dao.NewCondition()
	cond.Eq("JobId", job.Id)
	cond.NIn("Id", []int64{job.PublishedJobConfigId, job.LastPublishedJobConfigId})
	cond.Ne("VersionId", constants.JOB_CONFIG_DRAFT_VERSION)
	cond.Ne("Status", constants.JOB_CONFIG_STATUS_DELETE)
	where, args := cond.GetWhere()
	sql := "select * from JobConfig " + where + " order by VersionId limit 1"
	jobConfig := &table2.JobConfig{}
	service.GetTxManager().GetQueryTemplate().GetObjectBySqlWithContext(ctx, sql, jobConfig, args...)
	return true, DeleteJobConfigs(job.Id, []int16{jobConfig.VersionId}, "System")
}

// DeleteCOSFile 删除作业COS目录下的文件
func DeleteCOSFile(jobInstances []*tableInstance.JobInstance, req *model.DeleteJobConfigsReq, cluster *cluster.Cluster) (err error) {
	if len(jobInstances) > 0 {
		tmpSecretId, tmpSecretKey, token, pass, err := service2.StsAssumeRole(req.Uin, req.SubAccountUin, req.Region)
		// tmpSecretId, tmpSecretKey, token := "tmpSecretId", "tmpSecretKey", ""
		// pass := true
		if err != nil {
			logger.Errorf("DeleteCOSFile StsAssumeRoleWithSubAccountUin with error %v", err)
			return err
		}
		if !pass {
			logger.Errorf("DeleteCOSFile StsAssumeRoleWithSubAccountUin not pass with param uin %s, subUin %s, region %s ", req.Uin, req.SubAccountUin, req.Region)
			return fmt.Errorf("StsAssumeRoleWithSubAccountUin not pass with param uin %s, subUin %s, region %s ", req.Uin, req.SubAccountUin, req.Region)
		}
		for _, ins := range jobInstances {
			if len(ins.ApplicationId) <= 0 {
				continue
			}
			path := buildJobDependencyPath(ins)
			request := cosService.BuildDelCosObjectReq(cluster.DefaultCOSBucket, req.Region, path, tmpSecretId, tmpSecretKey, token)
			cosService.GetHandler().AppendTask(request)
		}
	}
	return nil
}

// buildJobDependencyPath 构建作业实例上传依赖COS目录
func buildJobDependencyPath(jobInstance *tableInstance.JobInstance) string {
	return fmt.Sprintf("user/user_00/.flink/%s", jobInstance.ApplicationId)
}
