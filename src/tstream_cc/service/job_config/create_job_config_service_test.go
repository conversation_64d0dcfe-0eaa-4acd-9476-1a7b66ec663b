package service

import (
	"fmt"
	"strings"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model"
	model3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model"
	model2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_config"
	table3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	"testing"
)

//func TestBuildJobConfigEntityFromReq(t *testing.T) {
//	BuildJobConfigEntityFromReq(1, 1, &model1.CreateJobConfigReq{}, "", 1, "")
//}

func TestAddSetatsDependency(t *testing.T) {
	var rr []*model2.ResourceRefItem
	fmt.Println(len(rr))
	refs := make([]*model2.ResourceRefItem, 0)
	refs = append(refs, &model2.ResourceRefItem{
		ResourceId: "aaa",
		Version:    1,
		Type:       1,
	})
	pps := make([]*model3.Property, 0)
	pps = append(pps, &model3.Property{Key: "a", Value: "88888"})
	pps = append(pps, &model3.Property{Key: constants.Engine_Type_Key, Value: constants.EngineTypeSetats})
	req := &model2.CreateJobConfigReq{
		JobId: "cql-6pe6d5ll",
		//ResourceRefs: refs,
		Properties: pps,
	}
	AddSetatsDependency(req)
	fmt.Printf("ResourceRefs: %+v", req.ResourceRefs)
}
func TestCheckEsIndexReady(t *testing.T) {
	type args struct {
		logCollectType    int8
		esServerlessIndex string
		esServerlessSpace string
		cluster           *table3.Cluster
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "test-1",
			args: args{
				logCollectType:    1,
				esServerlessIndex: "",
				esServerlessSpace: "",
				cluster: &table3.Cluster{
					LogConfig: "",
				},
			},
			wantErr: true,
		},
		{
			name: "test-2",
			args: args{
				logCollectType:    4,
				esServerlessIndex: "",
				esServerlessSpace: "",
				cluster: &table3.Cluster{
					LogConfig: "",
				},
			},
			wantErr: true,
		},
		{
			name: "test3",
			args: args{
				logCollectType:    4,
				esServerlessIndex: "",
				esServerlessSpace: "",
				cluster: &table3.Cluster{
					LogConfig: "{\"Status\":2,\"EsServerlessInfo\":[{\"IndexId\":\"index-fnc20sz0\",\"WorkspaceId\":\"space-ap4kwo9q\",\"DiDataCollectorIds\":[\"coll-3p3farsm-filebeat\"]}]}",
				},
			},
			wantErr: true,
		},
		{
			name: "test4",
			args: args{
				logCollectType:    4,
				esServerlessIndex: "index-fnc20sz0",
				esServerlessSpace: "space-ap4kwo9q",
				cluster: &table3.Cluster{
					LogConfig: "{\"Status\":1,\"EsServerlessInfo\":[{\"IndexId\":\"index-fnc20sz0\",\"WorkspaceId\":\"space-ap4kwo9q\",\"DiDataCollectorIds\":[\"coll-3p3farsm-filebeat\"]}]}",
				},
			},
			wantErr: true,
		},
		{
			name: "test5",
			args: args{
				logCollectType:    4,
				esServerlessIndex: "index-f2",
				esServerlessSpace: "space-a3",
				cluster: &table3.Cluster{
					LogConfig: "{\"Status\":2,\"EsServerlessInfo\":[{\"IndexId\":\"index-fnc20sz0\",\"WorkspaceId\":\"space-ap4kwo9q\",\"DiDataCollectorIds\":[\"coll-3p3farsm-filebeat\"]}]}",
				},
			},
			wantErr: true,
		},
		{
			name: "test6",
			args: args{
				logCollectType:    4,
				esServerlessIndex: "index-fnc20sz0",
				esServerlessSpace: "space-ap4kwo9q",
				cluster: &table3.Cluster{
					LogConfig: "{\"Status\":2,\"EsServerlessInfo\":[{\"IndexId\":\"index-fnc20sz0\",\"WorkspaceId\":\"space-ap4kwo9q\",\"DiDataCollectorIds\":[\"coll-3p3farsm-filebeat\"]}]}",
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := CheckEsIndexReady(tt.args.logCollectType, tt.args.esServerlessIndex, tt.args.esServerlessSpace, tt.args.cluster)
			if err != nil {
				fmt.Println(err.Error())
			}

		})
	}
}

func TestGetMaxParallelism(t *testing.T) {
	type args struct {
		defaultParallelism int16
		props              []*model3.Property
	}
	tests := []struct {
		name    string
		args    args
		want    int16
		wantErr bool
	}{
		{
			name: "Small-1",
			args: args{
				defaultParallelism: 1,
			},
			want: GetDefaultMaxParallelism(),
		},
		{
			name: "Small-2",
			args: args{
				defaultParallelism: 80,
			},
			want: GetDefaultMaxParallelism(),
		},
		{
			name: "Small-3",
			args: args{
				defaultParallelism: 100,
			},
			want: GetDefaultMaxParallelism(),
		},
		{
			name: "Large",
			args: args{
				defaultParallelism: 129,
			},
			want: GetDefaultMaxParallelism(),
		},
		{
			name: "VeryLarge",
			args: args{
				defaultParallelism: 2048,
			},
			want: GetDefaultMaxParallelism(),
		},
		{
			name: "User-Provided-Normal",
			args: args{
				defaultParallelism: 129,
				props: []*model3.Property{
					{
						Key:   "pipeline.max-parallelism",
						Value: "131",
					},
				},
			},
			want: 131,
		},
		{
			name: "User-Provided-Invalid-Less-Than-Default-Parallelism",
			args: args{
				defaultParallelism: 129,
				props: []*model3.Property{
					{
						Key:   "pipeline.max-parallelism",
						Value: "128",
					},
				},
			},
			want:    2048,
			wantErr: true,
		},
		{
			name: "User-Provided-Invalid-Too-Large-Max-Parallelism",
			args: args{
				defaultParallelism: 129,
				props: []*model3.Property{
					{
						Key:   "pipeline.max-parallelism",
						Value: "120000000008",
					},
				},
			},
			want:    2048,
			wantErr: true,
		},
		{
			name: "User-Provided-Invalid-Wrong-Max-Parallelism",
			args: args{
				defaultParallelism: 129,
				props: []*model3.Property{
					{
						Key:   "pipeline.max-parallelism",
						Value: "abcdefg",
					},
				},
			},
			want:    2048,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, props, err := GetMaxParallelism(tt.args.defaultParallelism, tt.args.props, false)
			t.Logf("Props: %s, Error: %+v", props, err)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetMaxParallelism() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetMaxParallelism() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestAppendStruct(t *testing.T) {
	type CreateJobConfigReq struct {
		AppId         int32
		Uin           string
		SubAccountUin string
		RequestId     string
		Region        string
		Version       string

		JobId              string `check:"nullable:false|strlen:[12, 12]"`
		EntrypointClass    string `check:"nullable:true|strlen:[0, 500]"`
		ProgramArgs        string `check:"nullable:true|strlen:[0, 100000]"`
		Properties         []*model.Property
		Remark             string `check:"nullable:true|strlen:[0, 100]"`
		DefaultParallelism int16  `check:"nullable:true|range:[1, 2048]"`
		COSBucket          string `check:"nullable:true|strlen:[0, 100]"`
		LogCollect         bool

		AutoDelete int32

		JobManagerSpec  float32
		TaskManagerSpec float32

		JobManagerCpu  float32
		JobManagerMem  float32
		TaskManagerCpu float32
		TaskManagerMem float32
	}

	pop1 := model.Property{Key: "a", Value: "88888"}
	pop2 := model.Property{Key: "b", Value: "true"}

	props := make([]*model.Property, 0)
	properties := append(props, &pop1, &pop2)

	test := CreateJobConfigReq{
		AppId:              6,
		Uin:                "test",
		JobId:              "job_test",
		EntrypointClass:    "com.java.sql.DriverManager",
		ProgramArgs:        "format json",
		Properties:         properties,
		Remark:             "test_remark",
		DefaultParallelism: 1,
		COSBucket:          "lll",
	}

	fmt.Printf("%+v", test)
	fmt.Printf("\n")

	requiredConfiguration := make(map[string]string, 0)

	for _, property := range properties {
		key := property.Key
		fmt.Printf("\n")
		fmt.Printf("%T", key)
		if _, exist := constants.EtlDefaultConfiguration[strings.TrimSpace(key)]; exist {
			requiredConfiguration[strings.TrimSpace(key)] = property.Value
		}
	}

	if len(requiredConfiguration) < 2 {
		p := &test.Properties
		if _, exist := requiredConfiguration[constants.ETL_METRICS_LATENCY_INTERVAL_NAME]; !exist {
			*p = append(*p, &model.Property{Key: constants.ETL_METRICS_LATENCY_INTERVAL_NAME, Value: constants.ETL_METRICS_LATENCY_INTERVAL_VALUE})
		}
		if _, exist := requiredConfiguration[constants.ETL_PIPELINE_OPERATOR_CHAINING_NAME]; !exist {
			*p = append(*p, &model.Property{Key: constants.ETL_PIPELINE_OPERATOR_CHAINING_NAME, Value: constants.ETL_PIPELINE_OPERATOR_CHAINING_VALUE})
		}
	}
	fmt.Println(constants.ETL_METRICS_LATENCY_INTERVAL_NAME)
	fmt.Println("=====================")
	fmt.Printf("%+v", test)
	fmt.Printf("\n")
	fmt.Printf("%+v", test.Properties)

	for _, item := range test.Properties {
		fmt.Printf("\n")
		fmt.Printf("%+v", item)
	}

}

func Test_checkCreateJobConfigReq(t *testing.T) {
	type args struct {
		req *model2.CreateJobConfigReq
	}
	tests := []struct {
		name    string
		args    args
		wantCtx *createJobConfigContext
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				req: &model2.CreateJobConfigReq{
					AppId:              1257058945,
					Region:             "ap-hongkong",
					JobId:              "cql-me9ae5ob",
					ProgramArgs:        `{"CheckpointInterval":10,"Metadata":"eyJNZXRhZGF0YSI6eyJSZWZlcmVuY2VUYWJsZXMiOltdLCJWYXJpYWJsZXMiOltdfX0=","SqlCode":"Q1JFQVRFIFRBQkxFIGRhdGFnZW5fc291cmNlX3RhYmxlICggCiAgICBhZ2UgSU5ULCAKICAgIHNjb3JlIElOVCAKKSBXSVRIICgKJ3Jvd3MtcGVyLXNlY29uZCc9JzEwMDAwMDAnLCdjb25uZWN0b3InID0gJ2RhdGFnZW4nCik7CgoKQ1JFQVRFIFRBQkxFIGBEYXRhX091dHB1dGAgKCAtLeatpemqpCAyIO+8muWIm+W7uuaVsOaNrue7k+aenOihqO+8iFNpbmvvvIkgRGF0YV9PdXRwdXQKICAgIGBhdmdfYWdlYCBCSUdJTlQsCiAgICBgYXZnX3Njb3JlYCBCSUdJTlQKKSBXSVRIICgKICAgICdjb25uZWN0b3InID0gJ2JsYWNraG9sZScKKTsKCklOU0VSVCBJTlRPIGBEYXRhX091dHB1dGAgICAtLeatpemqpCAzIO+8miDlsIbmlbDmja7mupDooajvvIhTb3VyY2XvvIkgRGF0YV9JbnRwdXQg5Lit55qEIGFnZSDlkowgc2NvcmUg5Y+W5bmz5Z2H5pWw5LmL5ZCO5a2Y5YKo5LqO5pWw5o2u57uT5p6c6KGo77yIU2lua++8iSBEYXRhX091dHB1dApTRUxFQ1QgQVZHKGFnZSksIEFWRyhzY29yZSkgRlJPTSBgZGF0YWdlbl9zb3VyY2VfdGFibGVgOwoKCgoKCg=="}`,
					JobManagerCpu:      1,
					JobManagerMem:      2,
					TaskManagerCpu:     1,
					TaskManagerMem:     2,
					DefaultParallelism: 1,
					LogLevel:           "INFO",
					LogCollect:         false,
					AutoRecover:        1,
					Properties: []*model.Property{
						{
							Key:   "pipeline.max-parallelism",
							Value: "2048",
						},
					},
					CheckpointRetainedNum: 1,
				},
			},
			wantErr: false,
		},
		{
			name: "test1",
			args: args{
				req: &model2.CreateJobConfigReq{
					AppId:              1257058945,
					Region:             "ap-hongkong",
					JobId:              "cql-me9ae5ob",
					ProgramArgs:        `{"CheckpointInterval":1,"Metadata":"eyJNZXRhZGF0YSI6eyJSZWZlcmVuY2VUYWJsZXMiOltdLCJWYXJpYWJsZXMiOltdfX0=","SqlCode":"Q1JFQVRFIFRBQkxFIGRhdGFnZW5fc291cmNlX3RhYmxlICggCiAgICBhZ2UgSU5ULCAKICAgIHNjb3JlIElOVCAKKSBXSVRIICgKJ3Jvd3MtcGVyLXNlY29uZCc9JzEwMDAwMDAnLCdjb25uZWN0b3InID0gJ2RhdGFnZW4nCik7CgoKQ1JFQVRFIFRBQkxFIGBEYXRhX091dHB1dGAgKCAtLeatpemqpCAyIO+8muWIm+W7uuaVsOaNrue7k+aenOihqO+8iFNpbmvvvIkgRGF0YV9PdXRwdXQKICAgIGBhdmdfYWdlYCBCSUdJTlQsCiAgICBgYXZnX3Njb3JlYCBCSUdJTlQKKSBXSVRIICgKICAgICdjb25uZWN0b3InID0gJ2JsYWNraG9sZScKKTsKCklOU0VSVCBJTlRPIGBEYXRhX091dHB1dGAgICAtLeatpemqpCAzIO+8miDlsIbmlbDmja7mupDooajvvIhTb3VyY2XvvIkgRGF0YV9JbnRwdXQg5Lit55qEIGFnZSDlkowgc2NvcmUg5Y+W5bmz5Z2H5pWw5LmL5ZCO5a2Y5YKo5LqO5pWw5o2u57uT5p6c6KGo77yIU2lua++8iSBEYXRhX091dHB1dApTRUxFQ1QgQVZHKGFnZSksIEFWRyhzY29yZSkgRlJPTSBgZGF0YWdlbl9zb3VyY2VfdGFibGVgOwoKCgoKCg=="}`,
					JobManagerCpu:      1,
					JobManagerMem:      2,
					TaskManagerCpu:     1,
					TaskManagerMem:     2,
					DefaultParallelism: 1,
					LogLevel:           "INFO",
					LogCollect:         false,
					AutoRecover:        1,
					Properties: []*model.Property{
						{
							Key:   "execution.checkpointing.interval",
							Value: "1 ms",
						},
					},
					CheckpointRetainedNum: 1,
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := checkCreateJobConfigReq(tt.args.req)
			if err != nil {
				t.Errorf("checkCreateJobConfigReq() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}
