package service

import (
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/bucket"
)

type BucketManager struct {

}

func NewBucketManager() *BucketManager {
	return &BucketManager{}
}

func (this *BucketManager) AssignBucket(buckets []*table.Bucket) *table.Bucket {
	for _, bucket := range buckets {
		if bucket.CurNum < bucket.MaxNum {
			return bucket
		}
	}
	logger.Errorf("can not assign bucket")
	return nil
}




