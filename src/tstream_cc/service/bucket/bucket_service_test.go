package service_test

import (
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/bucket"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/bucket"
	service1 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource"
	"testing"
)

//  go test -v ./src/tstream_cc/service/bucket/bucket_service_test.go ./src/tstream_cc/service/bucket/bucket_service.go ./src/tstream_cc/service/bucket/bucket_ref_service.go ./src/tstream_cc/service/bucket/bucket_manager.go -o ./bin/test_bucket_create
// ./bin/test_bucket_create
func init() {
	service1.InitTestDB(service1.WALLYDB)
}

func Test_DoCreateBucket(t *testing.T) {
	req := &model.CreateBucketReq{}
	req.Type = 1
	req.Region = "ap-guangzhou"
	req.BucketName = "seventest-1257058945"
	req.MaxNum = 2000

	service2.DoCreateBucket(req)
}
