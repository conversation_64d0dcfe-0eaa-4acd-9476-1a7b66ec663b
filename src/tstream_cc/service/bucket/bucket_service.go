package service

import (
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/bucket"
	model2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/bucket"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/bucket"
	table1 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/configuration_center"
	"unicode"
)

func GetBucketType(bucketType int8) int8 {
	switch bucketType {
	case 2:
		return constants.BUKCET_TYPE_STATUSDATA_EXCLUSIVE
	case 1:
		return constants.BUKCET_TYPE_STATUSDATA_SHARE
	case 3:
		return constants.BUKCET_TYPE_RESOURCEDATA
	case 4:
		return constants.BUKCET_TYPE_CLUSTERDATA
	default:
		return 3
	}
}

func ListBuckets(ids []int, bucketName, region string, status []int,
	types []int8, available bool) (bucketList []*table.Bucket, err error) {
	bucketList = make([]*table.Bucket, 0, 0)
	cond := dao.NewCondition()
	if len(ids) > 0 {
		cond.In("Id", ids)
	}
	if len(types) > 0 {
		cond.In("Type", types)
	}
	if bucketName != "" {
		cond.Eq("BucketName", bucketName)
	}
	if len(status) == 0 {
		cond.Eq("Status", constants.BUCKET_STATUS_ACTIVE)
	} else {
		cond.In("Status", status)
	}
	if region != "" {
		cond.Eq("Region", region)
	}

	where, args := cond.GetWhere()
	sql := "SELECT * FROM Bucket " + where
	if available {
		sql = sql + " AND CurNum < MaxNum"
	}
	_, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("ListBuckets sql: %s args:%+v error:%+v", sql, args, err)
		return bucketList, err
	}

	for _, v := range data {
		ref := &table.Bucket{}
		util.ScanMapIntoStruct(ref, v)
		bucketList = append(bucketList, ref)
	}

	return bucketList, err
}

/**
 * 检查该地域的集群类型是否开启COS
 *
 */
func WhetherEnableCOSByRegion(region string, clusterType int8) (enable bool, err error) {
	buckets, err := ListBuckets(
		nil, "", region,
		[]int{constants.BUCKET_STATUS_ACTIVE}, []int8{GetBucketType(clusterType)}, true)
	if err != nil {
		logger.Errorf("IsEnableCOSByRegion -> ListBuckets error: %+v", err)
		return enable, err
	}
	if len(buckets) > 0 {
		enable = true
		return enable, err
	}
	return enable, err
}

func AssignJobBucket(innerReq *model.CreateJobBucketRefReq) (err error) {
	//1. 先确认作业的集群是否开启了COS
	enable, err := WhetherEnableCOSByJobId(innerReq.JobId)
	if err != nil {
		logger.Errorf("WhetherEnableCOSByJobId %s, error: %+v", innerReq.JobId, err)
		return err
	}

	//2. 开启了COS, 找到之前分配的bucket
	if enable {
		//查看之前的作业是否已经分配了Bucket
		bucketName, err := FindBucketByJob(innerReq.JobId)
		if err != nil {
			logger.Errorf("Find Bucket By Job %s, Region: %s, error: %+v", innerReq.JobId, innerReq.Region, err)
			return err
		}
		if bucketName != "" {
			innerReq.BucketName = bucketName
			//保存任务桶 以及 对应的策略
			success, err := SaveJobBucketRefAndPolicy(innerReq)
			if err != nil {
				logger.Errorf("Failed to save bucket and update policy, error: %+v", err)
				return err
			}
			if !success {
				logger.Errorf("Failed to save bucket and update policy, success false")
				return err
			}
		} else {
			logger.Errorf("bucket name is null")
			return err
		}

	} else {
		logger.Infof("The cluster of job %s is not enabled for COS", innerReq.JobId)
		return err
	}

	return err
}

func ListClusterGroupById(clusterGroupId int64) (t *table1.ClusterGroup, err error) {
	sql := "SELECT * FROM ClusterGroup WHERE Id=?"
	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQueryWithArgs(sql, clusterGroupId)
	if err != nil {
		logger.Errorf("Failed to query ClusterGroups from db, with errors:%+v", err)
		return nil, err
	} else if len(data) != 1 {
		logger.Errorf("Logic error? Failed to query ClusterGroups from db, expected 1, actual numbers: %d", len(data))
		return nil, err
	}

	clusterGroup := &table1.ClusterGroup{}
	err = util.ScanMapIntoStruct(clusterGroup, data[0])
	if err != nil {
		logger.Errorf("Failed to get ScanMapIntoStruct, with errors:%+v", err)
		return nil, err
	}

	return clusterGroup, nil
}

func AssignClusterBucketV2(clusterGroup *table1.ClusterGroup, cluster *table1.Cluster) (err error) {

	//1. 先确认集群的地域是否开启了Bucket
	enable, err := WhetherEnableCOSByRegion(clusterGroup.Region, constants.BUKCET_TYPE_CLUSTERDATA)
	if err != nil {
		logger.Errorf("Failed to check WhetherEnableCOSByRegion, Region: %s clusterGroupType %d",
			clusterGroup.Region, clusterGroup.Type)
		return err
	}

	logger.Infof("WhetherEnableCOSByRegion: %+v cos by region %s", enable, clusterGroup.Region)

	if enable {
		// 1. 如果该地域已经开启cos, 给集群分配该集群的bucket
		bucket, err := FindAvailableClusterBucket(clusterGroup.Region, constants.BUKCET_TYPE_CLUSTERDATA)
		if err != nil {
			logger.Errorf("Failed to create Cluster, find available cluster bucket region %s clusterGroup %d error: %+v",
				clusterGroup.Region, clusterGroup.Type, err)
			return err
		}
		logger.Infof("AssignClusterBucket FindAvailableClusterBucket %+v", bucket)
		// 2. 如果改地域开启集群on cos的模式，则为这个CreatorUin创建对应访问cos的子账户，并设置对应目录的权限
		createReq := &model.CreateAccountAndUpdatePolicyReq{}
		createReq.AppId = clusterGroup.AppId
		createReq.Region = clusterGroup.Region
		createReq.CreatorUin = clusterGroup.CreatorUin
		createReq.BucketName = bucket.BucketName // 获取分配的bucket
		createReq.ClusterGroupId = clusterGroup.Id
		createReq.ClusterId = cluster.Id

		success, err := CreateAccountAndUpdatePolicy(createReq)
		if err != nil {
			logger.Errorf("Failed to create cluster %s account and update policy error: %s", clusterGroup.SerialId, err)
			return err
		}
		if !success {
			logger.Errorf("Failed to create Cluster, create user count success: %%+v", success)
			return err
		}
	}
	return err
}

func AssignClusterBucket(clusterGroupId int64, clusterId int64) (err error) {

	clusterGroup, err := ListClusterGroupById(clusterGroupId)
	if err != nil {
		logger.Errorf("AssignClusterBucket ListClusterGroupById clusterGroupId %d clusterId %d, error: %+v", clusterGroupId, err)
		return err
	}
	//1. 先确认集群的地域是否开启了Bucket
	enable, err := WhetherEnableCOSByRegion(clusterGroup.Region, constants.BUKCET_TYPE_CLUSTERDATA)
	if err != nil {
		logger.Errorf("Failed to check WhetherEnableCOSByRegion, Region: %s clusterGroupType %d",
			clusterGroup.Region, clusterGroup.Type)
		return err
	}

	logger.Infof("WhetherEnableCOSByRegion: %+v cos by region %s", enable, clusterGroup.Region)

	if enable {
		// 1. 如果该地域已经开启cos, 给集群分配该集群的bucket
		bucket, err := FindAvailableClusterBucket(clusterGroup.Region, constants.BUKCET_TYPE_CLUSTERDATA)
		if err != nil {
			logger.Errorf("Failed to create Cluster, find available cluster bucket region %s clusterGroup %d error: %+v",
				clusterGroup.Region, clusterGroup.Type, err)
			return err
		}
		logger.Infof("AssignClusterBucket FindAvailableClusterBucket %+v", bucket)
		// 2. 如果改地域开启集群on cos的模式，则为这个CreatorUin创建对应访问cos的子账户，并设置对应目录的权限
		createReq := &model.CreateAccountAndUpdatePolicyReq{}
		createReq.AppId = clusterGroup.AppId
		createReq.Region = clusterGroup.Region
		createReq.CreatorUin = clusterGroup.CreatorUin
		createReq.BucketName = bucket.BucketName // 获取分配的bucket
		createReq.ClusterGroupId = clusterGroupId
		createReq.ClusterId = clusterId

		success, err := CreateAccountAndUpdatePolicy(createReq)
		if err != nil {
			logger.Errorf("Failed to create cluster %s account and update policy error: %s", clusterGroup.SerialId, err)
			return err
		}
		if !success {
			logger.Errorf("Failed to create Cluster, create user count success: %%+v", success)
			return err
		}
	}
	return err
}

/**
 * 通过判断数据库的bucket状态来决定可用区是否启用
 */
func WhetherEnableCOSByJobId(jobId string) (enable bool, err error) {
	//1. 找到对应的Job
	job, err := service3.FindJobBySerialID(jobId)
	if err != nil {
		logger.Errorf("FindJobBySerialID JobId %s error: %+v", jobId, err)
		return enable, err
	}
	//2. 通过Job找到对应集群，并检查集群是否开启cos
	ref, err := FindBucketClusterRef(job.ClusterGroupId, job.ClusterId, constants.BUCKET_REF_STATUS_ACTIVE)
	if err != nil {
		logger.Errorf("FindBucketClusterRef error: %+v", err)
		return enable, err
	}
	//3. 找到引用
	if ref != nil {
		enable = true
		return enable, err
	}

	return enable, err
}

func FindAvailableClusterBucket(region string, clusterType int8) (bucket *table.Bucket, err error) {
	bucketList, err := ListBuckets(nil, "", region,
		[]int{constants.BUCKET_STATUS_ACTIVE}, []int8{GetBucketType(clusterType)}, true)
	if err != nil {
		logger.Errorf("FindAvailableClusterBucket ListBuckets error: %+v", err)
		return bucket, err
	}
	if len(bucketList) == 0 {
		err = fmt.Errorf("FindAvailableClusterBucket bucket not found region %s clusterType: %d", region, clusterType)
		return bucket, err
	}
	bucketManager := NewBucketManager()
	bucket = bucketManager.AssignBucket(bucketList)
	return bucket, err
}

func FindAvailableJobBucket(jobId string) (bucket *table.Bucket, err error) {

	job, err := service3.FindJobBySerialID(jobId)
	if err != nil {
		logger.Errorf("FindAvailableJobBucket FindJobBySerialID jobId %s error:%+v", jobId, err)
		return bucket, err
	}

	clusterGroup, err := ListClusterGroupById(job.ClusterGroupId)
	if err != nil {
		logger.Errorf("FindAvailableJobBucket jobId ListClusterGroupById %s error:%+v", jobId, err)
		return bucket, err
	}

	buckets, err := ListBuckets(nil, "", job.Region, []int{constants.BUCKET_STATUS_ACTIVE}, []int8{clusterGroup.Type}, true)
	if len(buckets) == 0 {
		logger.Errorf("FindAvailableBucket can not assign bucket region %s, ClusterType %d to jobId %s",
			job.Region, clusterGroup.Type, jobId)
		return nil, fmt.Errorf("can not assign bucket region %s, ClusterType %d to jobId %s",
			job.Region, clusterGroup.Type, jobId)
	}
	bucketManager := NewBucketManager()
	bucket = bucketManager.AssignBucket(buckets)

	return bucket, err
}

func IncBucketCurNum(tx *dao.Transaction, bucketName string) (err error) {
	args := make([]interface{}, 0, 0)
	args = append(args, bucketName)
	args = append(args, constants.BUCKET_STATUS_ACTIVE)
	sql := "UPDATE Bucket SET CurNum=CurNum+1 WHERE BucketName = ? AND Status = ?"
	tx.ExecuteSql(sql, args)
	return err
}

func DecBucketCurNum(tx *dao.Transaction, bucketName string) (err error) {
	args := make([]interface{}, 0, 0)
	args = append(args, bucketName)
	args = append(args, constants.BUCKET_STATUS_ACTIVE)
	sql := "UPDATE Bucket SET CurNum=CurNum-1 WHERE BucketName = ? AND Status = ?"
	tx.ExecuteSql(sql, args)
	return err
}

func GetResourceBucketName(req *model2.GetResourceBucketReq) (region, bucket string, err error) {
	bucketList, err := ListBuckets(nil, "",
		req.Region, []int{constants.BUCKET_STATUS_ACTIVE}, []int8{constants.BUKCET_TYPE_RESOURCEDATA}, false)
	if err != nil {
		logger.Errorf("GetResourceBucket error:%+v", err)
		return "", "", err
	}
	if len(bucketList) == 0 {
		if req.Region == constants.BUCKET_DEFAULT {
			return "", "", fmt.Errorf("bucketNotFound in Region %s", req.Region)
		}
		logger.Errorf("bucketNotFound in Region %s, use Default %s", req.Region, constants.BUCKET_DEFAULT)
		req.Region = constants.BUCKET_DEFAULT
		return GetResourceBucketName(req)
	} else {
		return req.Region, bucketList[0].BucketName, nil
	}
}

func GetResourceBucket(req *model2.GetResourceBucketReq) (rsp *model2.GetResourceBucketRsp, err error) {
	/*bucketList, err := ListBuckets(nil, "",
		req.Region, []int{constants.BUCKET_STATUS_ACTIVE}, []int8{constants.BUKCET_TYPE_RESOURCEDATA}, false)
	if err != nil {
		logger.Errorf("GetResourceBucket error:%+v", err)
		return nil, err
	}

	if len(bucketList) == 0 {
		if req.Region == constants.BUCKET_DEFAULT {
			return nil, fmt.Errorf("bucketNotFound in Region %s", req.Region)
		}
		logger.Errorf("bucketNotFound in Region %s, use Default %s", req.Region, constants.BUCKET_DEFAULT)
		req.Region = constants.BUCKET_DEFAULT
		return GetResourceBucket(req)
	}*/
	region, bucketName, err := GetResourceBucketName(req)
	if err != nil {
		return nil, err
	}
	rsp = &model2.GetResourceBucketRsp{}
	OwnerUin, err := service2.GetScsUserUin()
	if err != nil {
		return nil, err
	}
	rsp.OwnerUin = OwnerUin
	SecretId, SecretKey, err := service3.GetSecretIdAndKeyOfScs()
	if err != nil {
		return nil, err
	}
	rsp.SecretId = SecretId
	rsp.SecretKey = SecretKey
	rsp.Region = region
	rsp.BucketName = bucketName
	return rsp, err
}

func DoGetResourceBucket(req *model2.GetResourceBucketReq) (status string, message string, rsp *model2.GetResourceBucketRsp) {
	rsp, err := GetResourceBucket(req)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}
	return controller.OK, controller.NULL, rsp
}

func DoCreateBucket(req *model.CreateBucketReq) (status int64, message string, rsp *model.CreateBucketRsp) {
	success, err := CreateBucket(req)
	if err != nil {
		return controller.ERROR_CODE_CREATE_BUCKET_ERROR, err.Error(), &model.CreateBucketRsp{}
	}
	if success {
		return constants.SUCCESS, controller.NULL, &model.CreateBucketRsp{}
	} else {
		return controller.ERROR_CODE_CREATE_BUCKET_FAILED, fmt.Sprintf("create bucket %s error", req.BucketName), &model.CreateBucketRsp{}
	}

}

func DoDisableBucket(req *model.DisableBucketReq) (status int64, message string, rsp *model.DisableBucketReq) {
	success, err := DisableBucket(req)
	if err != nil {
		return controller.ERROR_CODE_DISABLE_BUCKET_ERROR, err.Error(), &model.DisableBucketReq{}
	}
	if success {
		return controller.SUCCESS, controller.NULL, &model.DisableBucketReq{}
	} else {
		return controller.ERROR_CODE_DISABLE_BUCKET_FAILED, fmt.Sprintf("disable bucket %s error", req.BucketName), &model.DisableBucketReq{}
	}
}

func DoModifyBucket(req *model.ModifyBucketReq) (status int64, message string, rsp *model.ModifyBucketRsp) {
	success, err := ModifyBucket(req)
	if err != nil {
		return controller.ERROR_CODE_MODIFY_BUCKET_ERROR, err.Error(), &model.ModifyBucketRsp{}
	}
	if success {
		return controller.SUCCESS, controller.NULL, &model.ModifyBucketRsp{}
	} else {
		return controller.ERROR_CODE_MODIFY_BUCKET_FAILED, fmt.Sprintf("modify bucket %s error", req.BucketName), &model.ModifyBucketRsp{}
	}
}

func ModifyBucket(req *model.ModifyBucketReq) (success bool, err error) {
	exists, err := BucketExists(req.BucketName)
	if err != nil {
		return success, err
	}
	if !exists {
		logger.Infof("The bucket %s not exists", req.BucketName)
		return success, fmt.Errorf("the bucket %s not exists", req.BucketName)
	}

	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		args := make([]interface{}, 0, 0)
		args = append(args, req.MaxNum)
		args = append(args, req.BucketName)
		tx.ExecuteSql("UPDATE Bucket SET MaxNum=? WHERE BucketName=?", args)
		success = true
		return err
	}).Close()
	return success, err
}

func BucketExists(bucketName string) (success bool, err error) {
	bucketList, err := ListBuckets(nil, bucketName, "", nil, nil, false)
	if err != nil {
		logger.Errorf("BucketExists bucket %s, error: %+v", bucketName, err)
		return success, err
	}
	if len(bucketList) > 0 {
		success = true
		return success, err
	}
	return success, err
}

func DisableBucket(req *model.DisableBucketReq) (success bool, err error) {
	//检查Bucket是否合法
	if !IsLegitimate(req.BucketName) {
		logger.Infof("The bucket %s is not legitimate", req.BucketName)
		return success, fmt.Errorf("the bucket %s not legitimate", req.BucketName)
	}
	exists, err := BucketExists(req.BucketName)
	if err != nil {
		return success, err
	}
	if !exists {
		logger.Infof("The bucket %s not exists", req.BucketName)
		return success, fmt.Errorf("the bucket %s not exists", req.BucketName)
	}
	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		args := make([]interface{}, 0, 0)
		args = append(args, constants.BUCKET_STATUS_DELETE)
		args = append(args, req.BucketName)
		tx.ExecuteSql("UPDATE Bucket SET Status=? WHERE BucketName=?", args)
		success = true
		return err
	}).Close()
	return success, err
}

/**
 * 创建时检查bucket，如果不存在则创建，存在则返回错误
 */
func CreateBucket(req *model.CreateBucketReq) (success bool, err error) {
	exists, err := BucketExists(req.BucketName)
	if err != nil {
		return success, err
	}

	if exists {
		logger.Infof("The bucket %s already exists", req.BucketName)
		return success, fmt.Errorf("the bucket %s already exists", req.BucketName)
	}
	logger.Infof("create bucket %s region %s type %d", req.BucketName, req.Region, GetBucketType(req.Type))
	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.SaveObject(buildBucket(req), "Bucket")
		success = true
		return err
	}).Close()
	return success, nil
}

func buildBucket(req *model.CreateBucketReq) *table.Bucket {
	bucket := &table.Bucket{}
	bucket.Region = req.Region
	bucket.Status = constants.BUCKET_STATUS_ACTIVE
	bucket.BucketName = req.BucketName
	bucket.Type = GetBucketType(req.Type)
	bucket.MaxNum = req.MaxNum
	return bucket
}

//检查bucket和region的合法性
func IsLegitimate(str string) bool {
	for _, x := range []rune(str) {
		if !unicode.IsDigit(x) && !unicode.IsLetter(x) && x != '-' {
			return false
		}
	}
	return true
}
