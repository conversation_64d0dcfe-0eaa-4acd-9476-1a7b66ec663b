package role_auth

import (
	"errors"
	"fmt"
	"sort"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service3 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/role_auth"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/role_auth"
	"time"
)

/**
 * Created by <PERSON> on 2021/12/10 11:26 上午.
 * At tencent
 */

func DoModifyAdministrator(req *model.ModifyAdministratorUserReq) (string, string, interface{}) {
	rsp := &model.ModifyAdministratorRsp{Auth: false}
	if len(req.ModifySubAccountUin) == 0 {
		err := updateAdministerSetDelete(req.ModifySubAccountUin, req)
		if err != nil {
			logger.Errorf("%s:  Save Administrator error: %+v", req.RequestId, err)
			err = errorcode.InternalErrorCode.ReplaceDesc("Can Not Update Administrator")
			return errorcode.GetCode(err).GetCodeStr(), errorcode.GetCode(err).GetCodeDesc(), rsp
		}
		rsp.Auth = true
		return controller.OK, controller.NULL, rsp
	}

	for _, subAccountUin := range req.ModifySubAccountUin {
		if req.Uin == subAccountUin {
			continue
		}

		administer, err := RoleAuthoAdministorCheck(req.AppId, subAccountUin)
		if err != nil {
			logger.Errorf("%s: RoleAuthoAdministorCheck error: %+v", req.RequestId, err)
			return controller.InternalError, err.Error(), rsp
		}

		if administer != nil {
			if administer.Status == constants.ROLE_AUTHO_STATUS_DISEABLE {
				err = updateAdminister(administer, constants.ROLE_AUTHO_STATUS_USEABLE, req.SynVersion, req.Uin)
				if err != nil {
					logger.Errorf("%s:  Update Administrator error: %+v", req.RequestId, err)
					err = errorcode.InternalErrorCode.ReplaceDesc("Can Not Update Administrator")
					return errorcode.GetCode(err).GetCodeStr(), errorcode.GetCode(err).GetCodeDesc(), rsp
				}
				continue
			} else {
				continue
			}
		}
		// 授权 新增权限
		admin, _ := BuildAdministerFromReq2(req, subAccountUin, req.SynVersion)
		err = SaveTableAdminister(admin)
		if err != nil {
			logger.Errorf("%s:  Save Administrator error: %+v", req.RequestId, err)
			err = errorcode.InternalErrorCode.ReplaceDesc("Can Not Save Administrator")
			return errorcode.GetCode(err).GetCodeStr(), errorcode.GetCode(err).GetCodeDesc(), rsp
		}
	}

	err := updateAdministerSetDelete(req.ModifySubAccountUin, req)
	if err != nil {
		logger.Errorf("%s:  Save Administrator error: %+v", req.RequestId, err)
		err = errorcode.InternalErrorCode.ReplaceDesc("Can Not Update Administrator")
		return errorcode.GetCode(err).GetCodeStr(), errorcode.GetCode(err).GetCodeDesc(), rsp
	}
	rsp.Auth = true
	return controller.OK, controller.NULL, rsp
}

// DoCreateAdministrator 创建超管用户
func DoCreateAdministrator(req *model.CreateAdministorUserReq) (string, string, interface{}) {
	logger.Debugf("%s: DoCreateAdministorUser API called by AppId %d", req.RequestId, req.AppId)
	rsp := &model.CreateAdministorUserRsp{Auth: false}

	if len(req.AddSubAccountUin) == 0 {
		err := errorcode.InvalidParameterCode.ReplaceDesc("AddSubAccountUin is null")
		logger.Errorf("%s:InvalidParameterCode AddSubAccountUin is null: %+v", req.RequestId, err)
		return controller.InvalidParameterValue, err.Error(), rsp
	}
	for _, subAccountUin := range req.AddSubAccountUin {
		if req.Uin == subAccountUin {
			continue
		}

		administer, err := RoleAuthoAdministorCheck(req.AppId, subAccountUin)
		if err != nil {
			logger.Errorf("%s: RoleAuthAdministerCheck error: %+v", req.RequestId, err)
			return controller.InternalError, err.Error(), rsp
		}

		if administer != nil {
			if administer.Status == constants.ROLE_AUTHO_STATUS_DISEABLE {
				err = updateAdminister(administer, constants.ROLE_AUTHO_STATUS_USEABLE, req.SynVersion, req.Uin)
				if err != nil {
					logger.Errorf("%s:  Update Administrator error: %+v", req.RequestId, err)
					err = errorcode.InternalErrorCode.ReplaceDesc("Can Not Update Administrator")
					return errorcode.GetCode(err).GetCodeStr(), errorcode.GetCode(err).GetCodeDesc(), rsp
				}
				continue
			} else {
				err = errors.New("Subuin has been authorized ")
				logger.Errorf("%s: Subuin has been authorized: %+v", req.RequestId, err)
				return controller.UnsupportedOperation, err.Error(), rsp
			}
		}
		// 授权 新增权限
		admin, _ := BuildAdministerFromReq(req, subAccountUin)
		err = SaveTableAdminister(admin)
		if err != nil {
			logger.Errorf("%s:  Save Administrator error: %+v", req.RequestId, err)
			err = errorcode.InternalErrorCode.ReplaceDesc("Can Not Save Administrator")
			return errorcode.GetCode(err).GetCodeStr(), errorcode.GetCode(err).GetCodeDesc(), rsp
		}
	}
	rsp.Auth = true
	return controller.OK, controller.NULL, rsp
}

// DoDeleteAdministrator 删除超管用户
func DoDeleteAdministrator(req *model.DeleteAdministorUserReq) (string, string, interface{}) {
	logger.Debugf("%s: DoDeleteAdministorUser API called by AppId %d", req.RequestId, req.AppId)
	rsp := &model.DeleteAdministorUserRsp{Delete: false}
	if len(req.DelSubAccountUin) == 0 {
		err := errorcode.InvalidParameterCode.ReplaceDesc("AddSubAccountUin is null")
		logger.Errorf("%s:InvalidParameterCode AddSubAccountUin is null: %+v", req.RequestId, err)
		return controller.InvalidParameterValue, err.Error(), rsp
	}

	for _, subAccountUin := range req.DelSubAccountUin {
		if req.Uin == subAccountUin {
			continue
		}

		administor, err := RoleAuthoAdministorCheck(req.AppId, subAccountUin)
		if err != nil {
			logger.Errorf("%s: RoleAuthoAdministorCheck error: %+v", req.RequestId, err)
			return controller.InternalError, err.Error(), rsp
		}

		if administor == nil || administor.Status == constants.ROLE_AUTHO_STATUS_DISEABLE {
			continue
		}

		err = updateAdminister(administor, constants.ROLE_AUTHO_STATUS_DISEABLE, req.SynVersion, req.Uin)
		if err != nil {
			logger.Errorf("%s:  Update Administrator error: %+v", req.RequestId, err)
			err = errorcode.InternalErrorCode.ReplaceDesc("Can Not Update Administrator")
			return errorcode.GetCode(err).GetCodeStr(), errorcode.GetCode(err).GetCodeDesc(), rsp
		}
	}
	rsp.Delete = true
	return controller.OK, controller.NULL, rsp
}

// DoCheckAdministrator 检测是否为超管用户
func DoCheckAdministrator(req *model.CheckAdministorUserReq) (string, string, interface{}) {
	logger.Debugf("%s: DoCheckAdministorUser API called by AppId %d", req.RequestId, req.AppId)
	rsp := &model.CheckAdministorUserRsp{IsAdministor: false}

	if req.Uin == req.CheckSubAccountUin {
		rsp.IsAdministor = true
		return controller.OK, controller.NULL, rsp
	}

	administor, err := RoleAuthoAdministorCheck(req.AppId, req.CheckSubAccountUin)
	if err != nil {
		logger.Errorf("%s: RoleAuthoAdministorCheck error: %+v", req.RequestId, err)
		return controller.InternalError, err.Error(), rsp
	}

	if administor == nil || administor.Status == constants.ROLE_AUTHO_STATUS_DISEABLE {
		return controller.OK, controller.NULL, rsp
	} else {
		rsp.IsAdministor = true
		return controller.OK, controller.NULL, rsp
	}
}

// DoDescribeAdministrators 超管用户列表
func DoDescribeAdministrators(req *model.DescribeAdministorsReq) (string, string, interface{}) {
	logger.Debugf("%s: DoDescribeAdministors API called by AppId %d", req.RequestId, req.AppId)
	rsp := &model.DescribeAdministorsRsp{}
	//intoMap, _ := ScanStructIntoMap(req.SubUins)
	administer, err := RoleAuthAdministerAuth(req.AppId, req.Uin)
	// 主账号为超管账号
	administer = append(administer, &model.Administrator{OwnerUin: req.Uin, CreatorUin: req.Uin, AuthSubAccountUin: req.Uin, Status: constants.ROLE_AUTHO_STATUS_USEABLE})
	if err != nil {
		logger.Errorf("%s: RoleAuthoAdministorCheck error: %+v", req.RequestId, err)
		return controller.InternalError, err.Error(), rsp
	}

	if len(administer) > 0 {
		sort.Slice(administer, func(i, j int) bool {
			return administer[i].CreateTime > administer[j].CreateTime
		})
	}
	rsp.Administrators = administer
	return controller.OK, controller.NULL, rsp
}

func updateAdminister(administer *table.Administrator, stat int64, version int64, uin string) (err error) {
	defer errorcode.DefaultDeferHandler(&err)
	args := make([]interface{}, 0, 0)
	sql := "UPDATE Administrator set `Status` = ? ,`Version` = ?,UpdateTime = ? WHERE AuthSubAccountUin = ? AND OwnerUin = ? "

	args = append(args, stat)
	args = append(args, version)
	args = append(args, util.GetCurrentTime())
	args = append(args, administer.AuthSubAccountUin)
	args = append(args, uin)
	txManager := service3.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		rowsAffected, err := tx.ExecuteSqlWithArgs(sql, args...).RowsAffected()
		if err != nil {
			return err
		} else if rowsAffected != 1 {
			return errors.New(fmt.Sprintf("Logic error? RowsAffected not 1, but %d when update RoleAuth", rowsAffected))
		}

		return nil
	}).Close()

	return nil
}

func updateAdministerSetDelete(administer []string, req *model.ModifyAdministratorUserReq) (err error) {
	defer errorcode.DefaultDeferHandler(&err)
	args := make([]interface{}, 0, 0)
	sql := "  UPDATE Administrator set `Status` = " + fmt.Sprint(constants.ROLE_AUTHO_STATUS_DISEABLE) + ", UpdateTime = '" + util.GetCurrentTime() + "'"
	cond := dao.NewCondition()
	if len(administer) > 0 {
		cond.NIn("AuthSubAccountUin", administer)
	}
	cond.Eq("AppId", req.AppId)
	cond.Eq("OwnerUin", req.Uin)
	where, args := cond.GetWhere()
	sql += where
	txManager := service3.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		_, err := tx.ExecuteSqlWithArgs(sql, args...).RowsAffected()
		if err != nil {
			return err
		}
		return nil
	}).Close()

	return nil
}

func SaveTableAdminister(administer *table.Administrator) (err error) {
	defer errorcode.DefaultDeferHandler(&err)
	txManager := service3.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.SaveObject(administer, "Administrator")
		return nil
	}).Close()
	return err
}

func BuildAdministerFromReq(req *model.CreateAdministorUserReq, subAccountUin string) (*table.Administrator, error) {
	administer := &table.Administrator{}
	administer.AppId = req.AppId
	administer.OwnerUin = req.Uin
	administer.CreatorUin = req.SubAccountUin
	administer.AuthSubAccountUin = subAccountUin
	administer.CreateTime = util.GetCurrentTime()
	administer.UpdateTime = administer.CreateTime
	administer.Status = constants.ROLE_AUTHO_STATUS_USEABLE
	return administer, nil
}

func BuildAdministerFromReq2(req *model.ModifyAdministratorUserReq, subAccountUin string, version int64) (*table.Administrator, error) {
	administer := &table.Administrator{}
	administer.AppId = req.AppId
	administer.OwnerUin = req.Uin
	administer.CreatorUin = req.SubAccountUin
	administer.AuthSubAccountUin = subAccountUin
	administer.CreateTime = util.GetCurrentTime()
	administer.UpdateTime = administer.CreateTime
	administer.Version = version
	administer.Status = constants.ROLE_AUTHO_STATUS_USEABLE
	return administer, nil
}

func RoleAuthoAdministorCheck(appId int64, subUin string) (tables *table.Administrator, err error) {
	errorcode.DefaultDeferHandler(&err)
	sql := "SELECT * from Administrator WHERE AppId = ?  AND AuthSubAccountUin = ?  "
	args := make([]interface{}, 0)
	args = append(args, appId)
	args = append(args, subUin)

	txManager := service3.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to execute the sql:%s, with args:%+v, with errors:%+v", sql, args, err)
		return nil, err
	}
	if len(data) == 0 {
		return nil, nil
	}
	if len(data) > 1 {
		logger.Errorf("Logic error ?  find more than one in RoleAuth by execute the sql:%s, with args:%+v, sql", args)
		err = errors.New("Logic error ? find more than one  in RoleAuth ")
		return nil, err
	}
	roleAuth := &table.Administrator{}
	err = util.ScanMapIntoStruct(roleAuth, data[0])
	if err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}
	return roleAuth, nil
}

func RoleAuthAdministerAuth(appId int64, uin string) (tables []*model.Administrator, err error) {
	errorcode.DefaultDeferHandler(&err)
	sql := "SELECT * from Administrator WHERE AppId = ?  AND OwnerUin = ?   AND `Status` = ? "
	args := make([]interface{}, 0)
	args = append(args, appId)
	args = append(args, uin)
	args = append(args, constants.ROLE_AUTHO_STATUS_USEABLE)

	txManager := service3.GetTxManager()
	_, datas, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to execute the sql:%s, with args:%+v, with errors:%+v", sql, args, err)
		return nil, err
	}

	t := make([]*model.Administrator, 0)
	if len(datas) == 0 {
		return t, nil
	}
	for _, data := range datas {
		admin := &model.Administrator{}
		err = util.ScanMapIntoStruct(admin, data)
		if err != nil {
			return nil, errorcode.InternalErrorCode.NewWithErr(err)
		}
		t = append(t, admin)
	}
	return t, nil
}

func BuildAndInsertAdminister(appid int64, subUin string, uin string) (administer *table.Administrator, err error) {
	administer = &table.Administrator{}
	administer.AppId = appid
	administer.OwnerUin = uin
	administer.CreatorUin = uin
	administer.AuthSubAccountUin = subUin
	administer.CreateTime = util.GetCurrentTime()
	administer.UpdateTime = administer.CreateTime
	administer.Version = time.Now().UnixNano()
	administer.Status = constants.ROLE_AUTHO_STATUS_USEABLE
	err = SaveTableAdminister(administer)
	if err != nil {
		logger.Errorf(" Save Administrator error: %+v", err)
		err = errorcode.InternalErrorCode.ReplaceDesc("Can Not Save Administrator")
		return nil, err
	}
	return administer, nil
}

func BuildAndUpdateAdminister(administer *table.Administrator, uin string) (err error) {
	err = updateAdminister(administer, constants.ROLE_AUTHO_STATUS_USEABLE, time.Now().UnixNano(), uin)
	if err != nil {
		logger.Errorf("Update Administrator error: %+v", err)
		err = errorcode.InternalErrorCode.ReplaceDesc("Can Not Update Administrator")
		return err
	}
	return nil
}
