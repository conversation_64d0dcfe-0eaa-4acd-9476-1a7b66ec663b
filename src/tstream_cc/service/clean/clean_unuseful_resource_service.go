package clean

import (
	"encoding/json"
	"fmt"
	clb "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	cvm2 "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/cvm/v20170312"
	tkeSDK "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/tke/v20180525"
	vpc2 "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/vpc/v20170312"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/clean"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/Cvm"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cbs"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cdb"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cvm"
	service4 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/tke"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/vpc"
	yuntiService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/yunti"
	"time"
)

func DeleteUnusefulCvm(req *clean.DeleteUnusefulCvmReq) (string, string, interface{}) {
	resp := clean.DeleteUnusefulCvmResp{CvmIds: req.CvmIds}

	if len(req.CvmIds) == 0 {
		return controller.OK, "", resp
	}

	if req.NetEnvironmentType == constants.NETWORK_ENV_INNER_VPC {
		err := yuntiService.TerminateInstances(fmt.Sprintf("CSIG流计算Oceanus集群%s销毁", "cluster-xxxxxxxx"), req.CvmIds, req.Region, "cluster-xxxxxxxx")
		if err != nil {
			return controller.InternalError, err.Error(), resp
		}
		return controller.OK, "", resp
	}

	err := cbs.GetCbsService(req.NetEnvironmentType, req.Region).TerminateCbsFromCVM(req.CvmIds)
	if err != nil {
		return controller.InternalError, err.Error(), resp
	}
	cvmService := cvm.GetCvmService()
	err = cvmService.TerminateInstancesWithScsAccount(req.Region, req.CvmIds)
	return controller.OK, "", resp
}

func CheckUnusefulCvm(req *clean.CheckUnusefulCvmReq) (string, string, interface{}) {
	resp := clean.CheckUnusefulCvmResp{}
	cvmService := cvm.GetCvmService()
	var instanceSet []*cvm2.Instance
	var err error

	if len(req.InstanceIds) == 0 {
		_, instanceSet, err = cvmService.DescribeAllInstancesWithScsAccount(req.NetEnvironmentType, req.Region)
	} else {
		instanceSet, err = cvmService.DescribeInstancesWithScsAccount(req.NetEnvironmentType, req.Region, req.InstanceIds)
	}
	if err != nil {
		return controller.InternalError, err.Error(), resp
	}
	if len(instanceSet) == 0 {
		return controller.OK, "", resp
	}

	vpcInstanceMap := make(map[string][]string)
	for _, instance := range instanceSet {
		if req.FilterExpired && *instance.RestrictState == "EXPIRED" {
			continue
		}
		if req.FilterNamePrefix != "" && strings.HasPrefix(*instance.InstanceName, req.FilterNamePrefix) {
			continue
		}

		vpcId := *instance.VirtualPrivateCloud.VpcId
		vpcInstanceMap[vpcId] = append(vpcInstanceMap[vpcId], *instance.InstanceId)
	}

	tkeService := tke.GetTkeService()

	for vpcId, instanceIdSet := range vpcInstanceMap {
		sql, args := dao.NewQueryBuilder("SELECT cg.* FROM ClusterGroup cg  LEFT JOIN Cluster c ON cg.Id = c.ClusterGroupId ").
			WhereNe("cg.Status", constants.CLUSTER_GROUP_STATUS_DELETED).
			WhereEq(" cg.AgentSerialId", "").
			WhereNe("cg.Type", constants.CLUSTER_GROUP_TYPE_SUB_EKS).
			WhereEq("c.VpcId", vpcId).
			Build()
		clusterGroups, err := service4.Fetch[table.ClusterGroup](sql, args)
		if err != nil {
			return controller.InternalError, err.Error(), resp
		}
		if len(clusterGroups) > 1 {
			ib, _ := json.Marshal(instanceIdSet)
			gb, _ := json.Marshal(clusterGroups)
			msg := fmt.Sprintf("vpc: %s, instanceIdSet: %s, clustergroups: %s", vpcId, string(ib), string(gb))
			return controller.InternalError, fmt.Sprintf("cluster group more than one, err: %s", msg), resp
		}
		if len(clusterGroups) == 0 {
			resp.UnusefulInstanceIds = append(resp.UnusefulInstanceIds, instanceIdSet...)
			continue
		}
		cg := clusterGroups[0]
		cgs, err := service2.NewClusterGroupServiceBySerialId(cg.SerialId)
		if err != nil {
			return controller.InternalError, err.Error(), resp
		}
		t, err := cgs.GetTke()
		if err != nil {
			return controller.InternalError, err.Error(), resp
		}
		time.Sleep(time.Millisecond * 100)
		_, instanceNodeSet, err := tkeService.DescribeClusterAllInstancesWithScsAccountByNetEnvironmentType(cg.NetEnvironmentType, cg.Region, t.InstanceId)
		if err != nil {
			return controller.InternalError, err.Error(), resp
		}
		instanceIdMap := make(map[string]*tkeSDK.Instance)
		for _, instance := range instanceNodeSet {
			instanceIdMap[*instance.InstanceId] = instance
		}
		for _, instanceId := range instanceIdSet {
			if _, ok := instanceIdMap[instanceId]; ok {
				continue
			}
			resp.UnusefulInstanceIds = append(resp.UnusefulInstanceIds, instanceId)
		}
	}
	sql, args := dao.NewQueryBuilder("SELECT * FROM PooledCvm ").
		WhereEq("Status", constants.CVM_POOL_STATUS_VALID).
		WhereIn("InstanceId", resp.UnusefulInstanceIds).
		Build()
	pooledCvmSet, err := service4.Fetch[table2.PooledCvm](sql, args)
	if err != nil {
		return controller.InternalError, err.Error(), resp
	}

	pooledIdMap := make(map[string]struct{})
	for _, pc := range pooledCvmSet {
		resp.PooledInstanceIds = append(resp.PooledInstanceIds, pc.InstanceId)
		pooledIdMap[pc.InstanceId] = struct{}{}
	}
	for _, instanceId := range resp.UnusefulInstanceIds {
		if _, ok := pooledIdMap[instanceId]; ok {
			continue
		}
		resp.LeakedInstanceIds = append(resp.LeakedInstanceIds, instanceId)
	}
	return controller.OK, "", resp
}

func ListAllCvm(req *clean.ListAllCvmReq) (string, string, interface{}) {
	resp := clean.ListAllCvmResp{}
	cvmService := cvm.GetCvmService()
	var instanceSet []*cvm2.Instance
	var err error

	if len(req.InstanceIds) == 0 {
		_, instanceSet, err = cvmService.DescribeAllInstancesWithScsAccount(req.NetEnvironmentType, req.Region)
	} else {
		instanceSet, err = cvmService.DescribeInstancesWithScsAccount(req.NetEnvironmentType, req.Region, req.InstanceIds)
	}
	if err != nil {
		return controller.InternalError, err.Error(), resp
	}
	resp.TotalCount = len(instanceSet)
	if req.ShowInstance {
		resp.InstanceSet = instanceSet
		return controller.OK, "", resp
	}
	cvmSet := make([]*clean.CvmItem, 0)
	for _, instance := range instanceSet {
		cvmItem := &clean.CvmItem{}
		b, _ := json.Marshal(instance)
		_ = json.Unmarshal(b, cvmItem)
		cvmSet = append(cvmSet, cvmItem)
	}
	resp.CvmSet = cvmSet
	return controller.OK, "", resp
}

func ListAllUnusefulVpc(req *clean.ListAllUnusefulVpcReq) (string, string, interface{}) {
	resultVpcSet := make([]*clean.VpcItem, 0)
	resultNotSureVpcSet := make([]*clean.VpcItem, 0)
	resultUnKnowVpcSet := make([]*clean.VpcItem, 0)
	regionSet, err := getRegionSet()
	if err != nil {
		msg := fmt.Sprintf("ListAllUnusefulVpc -> getRegionSet error:%+v", err)
		logger.Error(msg)
		return controller.InternalError, msg, nil
	}
	for _, region := range regionSet {
		logger.Errorf("ListAllUnusefulVpc region: %s", region)
	}

	vpcService := vpc.GetVpcService()
	var offset, limit int64
	offset = 0
	limit = 100
	for {
		describeReq := vpcService.NewDefaultDescribeVpcsRequestBuilder().WithOffsetLimit(offset, limit).Build()
		totalCount, vpcSet, err := vpcService.DescribeVpcsWithScsAccount(req.Region, describeReq)
		if err != nil {
			msg := fmt.Sprintf("ListAllUnusefulVpc -> DescribeVpcsWithScsAccount(%x) error:%+v", req.Region, err)
			logger.Error(msg)
			return controller.InternalError, msg, nil
		}

		unUseVpcSet := make([]*vpc2.Vpc, 0)
		notSureVpcSet := make([]*vpc2.Vpc, 0)
		unKnowVpcSet := make([]*vpc2.Vpc, 0)

		for _, vpc := range vpcSet {
			group, err := service2.ListClusterGroupBySerialId(*vpc.VpcName)
			if err != nil {
				logger.Errorf("ListClusterGroupBySerialId Failed, %s because %+v", *vpc.VpcName, err)
				group1, err := service2.ListClusterGroupByVpcId(*vpc.VpcId)
				if err != nil {
					logger.Errorf("ListClusterGroupByVpcId Failed, %s because %+v", *vpc.VpcId, err)
					unKnowVpcSet = append(unKnowVpcSet, vpc)
					continue
				}
				if group1.Status == -2 {
					notSureVpcSet = append(notSureVpcSet, vpc)
				}
				continue
			}
			if group.Status == -2 {
				unUseVpcSet = append(unUseVpcSet, vpc)
			}
		}

		unUseVpcItemSet, err := describeVpcItem(req.Region, unUseVpcSet)
		if err != nil {
			return controller.InternalError, err.Error(), nil
		}
		resultVpcSet = append(resultVpcSet, unUseVpcItemSet...)

		notSureVpcItemSet, err := describeVpcItem(req.Region, notSureVpcSet)
		if err != nil {
			return controller.InternalError, err.Error(), nil
		}
		resultNotSureVpcSet = append(resultNotSureVpcSet, notSureVpcItemSet...)

		unKnowVpcItemSet, err := describeVpcItem(req.Region, unKnowVpcSet)
		if err != nil {
			return controller.InternalError, err.Error(), nil
		}
		resultUnKnowVpcSet = append(resultUnKnowVpcSet, unKnowVpcItemSet...)

		if totalCount <= uint64(offset+limit) {
			break
		}
		offset += limit
	}
	return controller.OK, "", clean.ListAllUnusefulVpcResp{VpcSet: resultVpcSet, NotSureVpcSet: resultNotSureVpcSet, UnKnowVpcSet: resultUnKnowVpcSet}
}

func describeVpcItem(region string, vpcSet []*vpc2.Vpc) (vpcItemSet []*clean.VpcItem, err error) {
	if len(vpcSet) <= 0 {
		return
	}
	vpcIds := make([]*string, 0)
	for _, v := range vpcSet {
		vpcIds = append(vpcIds, v.VpcId)
	}
	vpcIdNameMap := make(map[string]string, 0)
	for _, v := range vpcSet {
		vpcIdNameMap[*v.VpcId] = *v.VpcName
	}
	vpcService := vpc.GetVpcService()
	describeVpcResourceRequest := vpcService.NewDefaultDescribeVpcResourceDashboardRequestBuilder().
		WithVpcIdPtr(vpcIds).Build()
	resourceSet, err := vpcService.DescribeVpcResourceDashboardWithScsAccount(region, describeVpcResourceRequest)
	if err != nil {
		msg := fmt.Sprintf("ListAllUnusefulVpc -> DescribeVpcResourceDashboardWithScsAccount(%x) error:%+v", region, err)
		logger.Error(msg)
		return
	}
	for _, resource := range resourceSet {
		vpcItem := &clean.VpcItem{Region: region, VpcId: *resource.VpcId, VpcName: vpcIdNameMap[*resource.VpcId]}
		vpcItem.CDBCount = *resource.CDB
		vpcItem.CvmCount = *resource.CVM
		vpcItem.EmrCount = *resource.Emr
		vpcItem.LBCount = *resource.LB
		if *resource.CVM > 0 {
			cvmSet, err := describeCvmByVpcId(region, *resource.VpcId)
			if err != nil {
				msg := fmt.Sprintf("ListAllUnusefulVpc -> describeCvmByVpcId(%x) error:%+v", region, err)
				logger.Error(msg)
				return vpcItemSet, err
			}
			vpcItem.CvmIds = cvmSet
		}
		vpcItemSet = append(vpcItemSet, vpcItem)
	}
	return vpcItemSet, err
}

func describeCvmByVpcId(region string, vcpId string) (cvmSet []*string, err error) {
	secretId, secretKey, err := service3.GetSecretIdAndKeyOfScs()
	if err != nil {
		return cvmSet, err
	}
	var offset, limit int64
	offset = 0
	limit = 100
	for {
		request := cvm2.NewDescribeInstancesRequest()
		request.Offset = &offset
		request.Limit = &limit
		FilterValues := make([]*string, 0)
		FilterValues = append(FilterValues, &vcpId)
		filters := make([]*cvm2.Filter, 0)
		filterName := "vpc-id"
		filter := &cvm2.Filter{Name: &filterName, Values: FilterValues}
		filters = append(filters, filter)
		request.Filters = filters

		instanceSet, totalCount, err := cvm.GetCvmService().DescribeInstances(secretId, secretKey, "", region, request)
		if err != nil {
			return cvmSet, err
		}
		for _, cvm := range instanceSet {
			cvmSet = append(cvmSet, cvm.InstanceId)
		}
		if *totalCount <= offset+limit {
			break
		}
		offset += limit
	}
	return cvmSet, err
}

func DeleteUnusefulClb(req *clean.DeleteUnusefulClbReq) (string, string, interface{}) {
	secretId, secretKey, err := service3.GetSecretIdAndKeyOfScs()
	if err != nil {
		return controller.InternalError, "ListAllUnusefulClb -> GetSecretIdAndKeyOfScs error", nil
	}
	client, err := clb.NewClientWithSecretId(secretId, secretKey, req.Region)
	if err != nil {
		logger.Errorf("NewClientWithSecretId err: %s", err.Error())
		msg := fmt.Sprintf("ListAllUnusefulClb -> NewClientWithSecretId error:%+v", err)
		logger.Error(msg)
		return controller.InternalError, "ListAllUnusefulClb -> NewClientWithSecretId error", nil
	}
	delClbReq := clb.NewDeleteLoadBalancerRequest()
	delClbReq.LoadBalancerIds = req.LoadBalancerIds
	_, err = client.DeleteLoadBalancer(delClbReq)
	if err != nil {
		msg := fmt.Sprintf("DeleteUnusefulClb -> DeleteLoadBalancer(%x) error:%+v", req.Region, err)
		logger.Error(msg)
		return controller.InternalError, msg, nil
	}
	return controller.OK, "", clean.DeleteUnusefulClbResp{LoadBalancerIds: req.LoadBalancerIds}
}

func ListAllUnusefulClb(req *clean.ListAllUnusefulClbReq) (string, string, interface{}) {
	secretId, secretKey, err := service3.GetSecretIdAndKeyOfScs()
	if err != nil {
		return controller.InternalError, "ListAllUnusefulClb -> GetSecretIdAndKeyOfScs error", nil
	}
	client, err := clb.NewClientWithSecretId(secretId, secretKey, req.Region)
	if err != nil {
		logger.Errorf("NewClientWithSecretId err: %s", err.Error())
		msg := fmt.Sprintf("ListAllUnusefulClb -> NewClientWithSecretId error:%+v", err)
		logger.Error(msg)
		return controller.InternalError, "ListAllUnusefulClb -> NewClientWithSecretId error", nil
	}
	tkeService := tke.GetTkeService()
	clbItemSet := make([]*clean.ClbItem, 0)
	JodDeleteClbSet := make([]*clean.ClbItem, 0)
	maybeClbItemSet := make([]*clean.ClbItem, 0)
	unknownClbItemSet := make([]*clean.ClbItem, 0)
	var offset, limit, interval int64
	offset = 0
	limit = 100
	interval = 0
	for {
		lbReq := clb.NewDescribeLoadBalancersRequest()
		lbReq.Offset = &offset
		lbReq.Limit = &limit
		lbRes, err := client.DescribeLoadBalancers(lbReq)
		if err != nil {
			msg := fmt.Sprintf("ListAllUnusefulClb -> DescribeLoadBalancers(%x) error:%+v", req.Region, err)
			logger.Error(msg)
			return controller.InternalError, msg, nil
		}
		totalCount := lbRes.Response.TotalCount
		for _, clb := range lbRes.Response.LoadBalancerSet {
			interval += 1
			if interval%20 == 0 {
				time.Sleep(time.Duration(1) * time.Second)
			}
			lbId := *clb.LoadBalancerId
			lbName := *clb.LoadBalancerName
			vpcId := *clb.VpcId
			logger.Infof("CLB Info: %s %s %s %s", lbId, lbName, vpcId)
			tke := ""
			jobId := ""
			if strings.HasPrefix(lbName, "ccs_") && strings.HasSuffix(lbName, "_oceanus-flink-webui") {
				tkeSuffix := strings.TrimPrefix(lbName, "ccs_")
				tke = strings.TrimSuffix(tkeSuffix, "_oceanus-flink-webui")
			}
			if strings.HasPrefix(lbName, "cls-") && strings.HasSuffix(lbName, "_default_ingress-nginx-ingress-control") {
				tke = strings.TrimSuffix(lbName, "_default_ingress-nginx-ingress-control")
			}
			if strings.HasPrefix(lbName, "cls-") && strings.HasSuffix(lbName, "-rest") {
				lbNameArr := strings.SplitN(lbName, "_default_", 2)
				tke = lbNameArr[0]
				jobArr := strings.Split(lbNameArr[1], "-")
				jobId = jobArr[0] + "-" + jobArr[1]
			}
			clbItem := &clean.ClbItem{Region: req.Region, ClbId: lbId, ClbName: lbName, VpcId: vpcId, Tke: tke, JobId: jobId}
			if tke == "" {
				unknownClbItemSet = append(unknownClbItemSet, clbItem)
				continue
			}
			totalCount, _, err := tkeService.DescribeClusters(
				secretId, secretKey, "", req.Region,
				tkeService.NewDefaultDescribeClustersRequestBuilder().WithClusterIds(tke).Build())
			if err != nil {
				msg := fmt.Sprintf("ListAllUnusefulClb -> DescribeClusters(%x) error:%+v", tke, err)
				logger.Error(msg)
				return controller.InternalError, msg, nil
			}
			if totalCount == 1 && jobId != "" {
				logger.Error("Job: %d", jobId)
				job, err := service3.WhetherJobExists(0, req.Region, jobId)
				if err != nil {
					msg := fmt.Sprintf("ListAllUnusefulClb -> WhetherJobExists(%x) error:%+v", jobId, err)
					logger.Error(msg)
					maybeClbItemSet = append(maybeClbItemSet, clbItem)
					continue
				}
				if job.Status == -2 {
					JodDeleteClbSet = append(JodDeleteClbSet, clbItem)
				} else if !(job.Status == 3 || job.Status == 4) {
					maybeClbItemSet = append(maybeClbItemSet, clbItem)
				}
			} else if totalCount == 1 {
				continue
			} else if totalCount == 0 {
				clbItemSet = append(clbItemSet, clbItem)
			} else {
				msg := fmt.Sprintf("ListAllUnusefulClb -> DescribeClusters(%x) length(%d)", tke, totalCount)
				logger.Error(msg)
				return controller.InternalError, msg, nil
			}
		}
		if *totalCount <= uint64(offset+limit) {
			break
		}
		offset += limit
	}
	return controller.OK, "", clean.ListAllUnusefulClbResp{TotalCount: len(clbItemSet), ClbSet: clbItemSet, JodDeleteClbSet: JodDeleteClbSet, UnknownClbSet: unknownClbItemSet, MaybeClbSet: maybeClbItemSet}
}

func ListAllUnusefulCdb(req *clean.ListAllUnusefulCdbReq) (string, string, interface{}) {
	cdbService := cdb.GetCdbService()
	cdbItemSet := make([]*clean.CdbItem, 0)
	unknownCdbItemSet := make([]*clean.CdbItem, 0)
	regionSet, err := getRegionSet()
	if err != nil {
		logger.Errorf("getRegionSet err: %s", err.Error())
		msg := fmt.Sprintf("ListAllUnusefulCdb -> getRegionSet error:%+v", err)
		logger.Error(msg)
		return controller.InternalError, msg, nil
	}
	for _, region := range regionSet {
		logger.Errorf("region: %s", region)
	}

	var offset, limit uint64
	offset = 0
	limit = 100
	for {
		describeReq := cdbService.NewDefaultDescribeDBInstancesRequestBuilder().WithOffsetLimit(offset, limit).Build()
		totalCount, cdbList, err := cdbService.DescribeDBInstancesWithScsAccount(req.Region, describeReq)
		if err != nil {
			msg := fmt.Sprintf("ListAllUnusefulCdb -> DescribeDBInstancesWithScsAccount(%x) error:%+v", req.Region, err)
			logger.Error(msg)
			return controller.InternalError, msg, nil
		}
		for _, cdb := range cdbList {
			logger.Infof("cdb id %s, name %s, status %d", *cdb.InstanceId, *cdb.InstanceName, *cdb.Status)
			if cdbService.IsInstanceIsolate(cdb) {
				continue
			}
			unused, err := CheckCdbUnuseful(*cdb.InstanceId)
			if err != nil {
				unknownCdbItemSet = append(unknownCdbItemSet, &clean.CdbItem{Region: req.Region, InstanceId: *cdb.InstanceId, InstanceName: *cdb.InstanceName})
				continue
			}
			if unused {
				cdbItemSet = append(cdbItemSet, &clean.CdbItem{Region: req.Region, InstanceId: *cdb.InstanceId, InstanceName: *cdb.InstanceName})
			}
		}
		if uint64(totalCount) <= offset+limit {
			break
		}
		offset += limit
	}
	return controller.OK, "", clean.ListAllUnusefulCdbResp{TotalCount: len(cdbItemSet), CdbSet: cdbItemSet, UnknownCdbSet: unknownCdbItemSet}
}

func IsolateUnusefulCdb(req *clean.IsolateUnusefulCdbReq) (string, string, interface{}) {
	unused, err := CheckCdbUnuseful(req.InstanceId)
	if err != nil {
		msg := fmt.Sprintf("IsolateUnusefulCdb -> CheckCdbUnuseful(%x) error:%+v", req.InstanceId, err)
		logger.Error(msg)
		return controller.InternalError, msg, nil
	}

	if unused {
		cdbService := cdb.GetCdbService()
		isolateReq := cdbService.NewDefaultIsolateDBInstanceRequestBuilder().WithInstance(req.InstanceId).Build()
		// 需要测试， 重复对同一个db调用 隔离， api 返回什么
		_, err = cdbService.IsolateDBInstanceWithScsAccount(req.Region, isolateReq)
		if err != nil {
			msg := fmt.Sprintf("IsolateUnusefulCdb -> IsolateDBInstanceWithScsAccount(%x) error:%+v", req.InstanceId, err)
			logger.Error(msg)
			return controller.InternalError, msg, nil
		}
	} else {
		msg := fmt.Sprintf("IsolateUnusefulCdb ->(%x) is useful", req.InstanceId)
		logger.Error(msg)
		return controller.InternalError, msg, nil
	}
	return controller.OK, "", clean.IsolateUnusefulCdbResp{InstanceId: req.InstanceId}
}

func CheckCdbUnuseful(InstanceId string) (unused bool, err error) {
	group, err := service2.ListClusterGroupByCdbId(InstanceId)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to get cluster group by cdb id %s because %+v", InstanceId, err)
		logger.Errorf(errMsg)
		return unused, err
	}
	if group.Status == -2 {
		return true, nil
	}
	return unused, err
}

func getRegionSet() (regionSet []string, err error) {
	sql := "SELECT DISTINCT(Region) FROM ClusterGroup"
	cond := dao.NewCondition()
	cond.Eq("Status", constants.CLUSTER_GROUP_STATUS_DELETED)
	where, args := cond.GetWhere()
	count, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql+where, args)
	if err != nil {
		logger.Infof("getRegionSet sql %s return %v", sql, err)
		return nil, err
	}
	regionSet = make([]string, 0, count)
	for _, d := range data {
		if v, ok := d["Region"]; ok {
			regionSet = append(regionSet, string(v))
		}
	}
	return regionSet, nil
}
