package service

import (
	"fmt"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/cluster"
	"testing"
)

func TestDescribeWebUIWhiteListService(t *testing.T) {
	req := &model.ModifyWebUIWhiteListReq{
		RequestBase: apiv3.RequestBase{
			Action:        "",
			Region:        *fTestRegion,
			Token:         "",
			Version:       "",
			Language:      "",
			Timestamp:     "",
			RequestId:     "",
			AppId:         *fTestAppid,
			Uin:           *fTestUin,
			SubAccountUin: *fTestSubAccountUin,
			ClientIp:      "",
			ApiModule:     "",
			RequestSource: "",
			CamContext:    "",
		},
		ClusterId: "aa",
	}

	svr := NewModifyWebUIWhiteListService(req)
	rsp, err := svr.ModifyWebUIWhiteList()
	fmt.Println(rsp)
	fmt.Println(err)
}

func TestParseEksIp(t *testing.T) {
	str := "{\"eniID\":\"eni-dat2gtio\",\"mac\":\"20:90:6F:40:07:82\",\"primaryIP\":\"***********\",\"subnetCIDR\":\"**********/24\",\"attachedInsID\":\"eks-c2eqwv46\"}"
	fmt.Println(ParseEksIp(str))

}
