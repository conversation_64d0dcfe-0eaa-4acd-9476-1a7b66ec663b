package service

import (
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/watchdog"
)

type DescribeClusterService struct {
}

func (this *DescribeClusterService) DescribeCluster(req *watchdog.DescribeClusterReq, eventId int64) (
	*watchdog.DescribeClusterRsp, error) {
	if cluster, err := GetClusterByClusterId(req.ClusterId); err != nil {
		logger.Errorf("[%d] GetClusterByClusterId err %v", eventId, err)
		return nil, err
	} else {
		clusterGroup, err := ListClusterGroupById(cluster.ClusterGroupId)
		if err != nil {
			logger.Errorf("[%d] ListClusterGroupById err %v", eventId, err)
			return nil, err
		}

		return &watchdog.DescribeClusterRsp{
			Id:                 cluster.Id,
			UniqClusterId:      cluster.UniqClusterId,
			ClusterGroupId:     cluster.ClusterGroupId,
			CreatorUin:         cluster.CreatorUin,
			Zone:               cluster.Zone,
			VpcId:              cluster.VpcId,
			SubnetId:           cluster.SubnetId,
			VpcCIDR:            cluster.VpcCIDR,
			RoleType:           cluster.RoleType,
			SchedulerType:      cluster.SchedulerType,
			CuNum:              cluster.CuNum,
			UsedCuNum:          cluster.UsedCuNum,
			CrossTenantEniMode: cluster.CrossTenantEniMode,
			ClusterSerialId:    clusterGroup.SerialId,
		}, nil
	}
}
