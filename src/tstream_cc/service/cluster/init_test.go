package service

import (
	"flag"
	"fmt"
	"os"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"testing"
)

var (
	fTestAppid         = flag.Int64("test.appid", **********, "")
	fTestUin           = flag.String("test.uin", "************", "")
	fTestSubAccountUin = flag.String("test.subaccount.uin", "************", "")
	fTestRegion        = flag.String("test.region", "ap-guangzhou",
		"e.g. ap-guangzhou ap-beijing ap-shanghai ap-chengdu")
	fTestUniqVpcId    = flag.String("test.uniq.vpc.id", "vpc-mjmrypy9", "流计算测试 VPC 1180200|172.28.0.0/16")
	fTestUniqSubnetId = flag.String("test.uniq.subnet.id", "subnet-0rewz4vk", "测试子网 812842")
	fTestZone         = flag.String("test.zone", "ap-guangzhou-4", "")
	fTestCuNum        = flag.Int64("test.cu.num", 8, "")
	fTestCuMem        = flag.Int64("test.cu.mem", 4, "")
	fTestTagKey       = flag.String("test.tag.key", "abcdefghij", "")
	fTestTagValue     = flag.String("test.tag.value", "aaa111", "")

	fTestDbUrl = flag.String("test.db.url", "root:root@tcp(127.0.0.1:3306)/online_galileo?charset=utf8", "")
)

func init() {
	testing.Init()
	flag.Parse()
	// 初始化数据库
	tx, err := dao.NewDataSourceTransactionManager(*fTestDbUrl)
	if err != nil {
		fmt.Println("darwin Process create DataSourceTransactionManager err", err)
		os.Exit(-1)
	}
	service.SetTxManager(tx)
	flow.SetTxManager(tx)
	flow.SetTaskCenterCallbackInfo(&flow.TaskCenterCallBackInfo{
		CallbackAddr:          "CallbackAddr",
		CompleteTaskInterface: "*fTestTaskCenterCompleteTaskInterface",
		AlarmTaskInterface:    "*fTestTaskCenterAlarmTaskInterface",
	})
}
