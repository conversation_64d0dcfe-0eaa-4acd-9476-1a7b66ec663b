package service

import (
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"fmt"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cluster"
)

func DoDeleteClusterGroup(req *model.DeleteClusterGroupReq) (int64, string, interface{}) {

	err := validityCheck(req)
	if err != nil {
		logger.Errorf("Failed to delete ClusterGroupId %s, validityCheck error: %s", req.ClusterGroupId, err)
		return controller.ERROR_CODE_DELETE_CLUSTER_ERROR, controller.NULL, nil
	}

	err = DeleteClusterGroup(req.ClusterGroupId)
	if err != nil {
		logger.Errorf("Failed to delete ClusterGroupId %s, DeleteClusterGroup error: %s", req.ClusterGroupId, err)
		return controller.ERROR_CODE_DELETE_CLUSTER_ERROR, controller.NULL, nil
	}

	return controller.SUCCESS, controller.NULL, &model.DeleteClusterGroupRsp{}
}

func validityCheck(req *model.DeleteClusterGroupReq) (err error) {

	group, err := ListClusterGroupById(req.ClusterGroupId)
	if err != nil {
		logger.Errorf("validityCheck -> ListClusterGroupById error: %+v", err)
		return err
	}

	if group == nil {
		logger.Warningf("validityCheck -> ListClusterGroupById %d clusterGroup is null", req.ClusterGroupId)
		return err
	}

	if group.AppId != req.AppId {
		msg := fmt.Sprintf("clustergroup appId(%d) != req.AppId(%d)", group.AppId, req.AppId)
		logger.Errorf(msg)
		err = fmt.Errorf(msg)
		return err
	}

	if group.Region != req.Region {
		msg := fmt.Sprintf("clustergroup Region(%s) != req.Region(%s)", group.Region, req.Region)
		logger.Errorf(msg)
		err = fmt.Errorf(msg)
		return err
	}


	sql := "SELECT * FROM Job where ClusterGroupId = ? AND Status != ?"
	args := make([]interface{}, 0)
	args = append(args, req.ClusterGroupId)
	args = append(args, constants.JOB_STATUS_DELETE)
	cnt, _, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)

	if err != nil {
		logger.Errorf("validityCheck -> ListJobs error: %+v", err)
		return err
	}

	logger.Infof("DoDeleteClusterGroup -> validityCheck -> jobList size: %d", cnt)

	if cnt > 0 {
		msg := fmt.Sprintf("failed to delete cluster. You should delete all cluster %s jobs", group.SerialId)
		err = fmt.Errorf(msg)
		logger.Errorf(msg)
		return err
	}

	logger.Infof("DoDeleteClusterGroup -> validityCheck -> success")

	return err
}
