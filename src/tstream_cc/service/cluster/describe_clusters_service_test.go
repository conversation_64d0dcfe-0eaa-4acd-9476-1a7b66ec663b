package service

import (
	"encoding/json"
	"fmt"
	"testing"

	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/cluster"
)

func TestDoDescribeClusters(t *testing.T) {
	filters := make([]struct {
		Name   string
		Values []string
	}, 0)
	filters = append(filters, struct {
		Name   string
		Values []string
	}{
		Name:   "PayMode",
		Values: []string{"0"},
	})
	filters = append(filters, struct {
		Name   string
		Values []string
	}{
		Name:   "DeploymentMode",
		Values: []string{"0"},
	})
	req := &model.DescribeClustersReq{
		RequestBase: apiv3.RequestBase{
			Action:        "",
			Region:        *fTestRegion,
			Token:         "",
			Version:       "",
			Language:      "",
			Timestamp:     "",
			RequestId:     "",
			AppId:         *fTestAppid,
			Uin:           *fTestUin,
			SubAccountUin: *fTestSubAccountUin,
			ClientIp:      "",
			ApiModule:     "",
			RequestSource: "",
			CamContext:    "",
			IsSupOwner:    1,
		},
		Filters:   filters,
		Offset:    0,
		Limit:     0,
		OrderType: 0,
	}

	rsp, err := DoDescribeClusters(req)
	if err != nil {
		t.Fatal(err)
	}

	b, _ := json.MarshalIndent(rsp, "", " ")
	t.Logf("%s", string(b))
}

func TestDoDescribeSupportZones(t *testing.T) {
	rsp, err := DoDescribeSupportZones(&model.DescribeSupportZonesReq{
		RequestBase: apiv3.RequestBase{
			Region: *fTestRegion,
		},
	})
	fmt.Println(rsp, err)
}
