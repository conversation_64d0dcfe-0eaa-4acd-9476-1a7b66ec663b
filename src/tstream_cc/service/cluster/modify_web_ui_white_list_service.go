package service

import (
	"context"
	"fmt"
	clb "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common"
	tencentCloudErr "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/errors"
	vpc "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/vpc/v20170312"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"strconv"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service4 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/cluster"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config/configure_center"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/qcloud"
	tke2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/tke"
	vpc2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/vpc"
)

const (
	SecurityGroupNameSuffix = "webui-clb"
	CidrBlocksMaxLength     = 50
	CidrBlocksEmpty         = "" // 放通所有ip
)

type ModifyWebUIWhiteListService struct {
	request    *model.ModifyWebUIWhiteListReq
	clbClient  *clb.Client
	k8sService *k8s.K8sService
	k8sClient  *kubernetes.Clientset

	clusterGroup *table.ClusterGroup
	cluster      *table.Cluster
}

func NewModifyWebUIWhiteListService(req *model.ModifyWebUIWhiteListReq) ModifyWebUIWhiteListService {
	return ModifyWebUIWhiteListService{
		request:    req,
		k8sService: k8s.NewK8sService(),
	}

}

func DefaultNamespace(cg *table.ClusterGroup) string {
	result := constants.DEFAULT_NAMESPACE
	if cg.AgentSerialId != "" {
		result = fmt.Sprintf("%s", cg.SerialId)
	}
	return result
}

func DefaultName(cg *table.ClusterGroup) string {
	result := constants.TKE_NGINX_INGRESS_RELEASE_NAME
	if cg.AgentSerialId != "" {
		result = constants.TKE_NGINX_INGRESS_RELEASE_NAME + "-" + cg.SerialId
	}
	return result
}

func (s *ModifyWebUIWhiteListService) SetServiceType(serviceType corev1.ServiceType) error {
	// 禁用公网
	name := fmt.Sprintf("%s-nginx-ingress-controller", DefaultName(s.clusterGroup))

	namespace := DefaultNamespace(s.clusterGroup)

	ingService, err := s.k8sClient.CoreV1().Services(namespace).Get(context.TODO(), name, metav1.GetOptions{})
	if err != nil {
		return err
	}
	// 已经处理
	if ingService.Spec.Type == serviceType {
		return nil
	}
	ingService.Spec.Type = serviceType
	logger.Debugf("ingService.Spec.Ports %+v", ingService.Spec.Ports)
	// ClusterIP需要移除NodePort
	if serviceType == corev1.ServiceTypeClusterIP {
		if len(ingService.Spec.Ports) == 1 {
			ingService.Spec.Ports[0].NodePort = 0
		}
		if len(ingService.Spec.Ports) == 2 {
			ingService.Spec.Ports[0].NodePort = 0
			ingService.Spec.Ports[1].NodePort = 0
		}
	}
	_, err = s.k8sClient.CoreV1().Services(namespace).Update(context.TODO(), ingService, metav1.UpdateOptions{})
	return err
}

func (s *ModifyWebUIWhiteListService) GetAnnotations(ttype int, annotations map[string]string) (map[string]string, error) {
	if len(annotations) == 0 {
		annotations = make(map[string]string)
	}
	existFunc := func(key string, mmap map[string]string) (exist bool) {
		_, exist = mmap[key]
		return exist
	}
	//兼容老集群没开启StaticMode=true
	if s.cluster.CrossTenantEniMode == constants.EnableStaticMode {
		if !existFunc("tke.cloud.tencent.com/cross-tenant-eni-enable", annotations) {
			annotations["tke.cloud.tencent.com/cross-tenant-eni-enable"] = "true"
		}
		if !existFunc("tke.cloud.tencent.com/networks", annotations) {
			annotations["tke.cloud.tencent.com/networks"] = "tke-bridge,tke-direct-eni,tke-route"
		}
		// 公网，绑定FlinkUi clb跨租户弹性网卡
		if ttype == constants.PublicType {
			flinkUiClb, err := GetFlinkUiCLB(s.clusterGroup.Region)
			if err != nil {
				return nil, err
			}
			crossTenantEniConfig := fmt.Sprintf("{\"AppId\":%s,\"Uin\":\"%s\",\"UniqVpcId\":\"%s\",\"SubnetId\":\"%s\"}",
				configure_center.CC(s.clusterGroup.Region).GetFlinkUiClbAppId(), configure_center.CC(s.clusterGroup.Region).GetFlinkUiClbUin(), flinkUiClb.VpcId, flinkUiClb.SubnetId)
			logger.Infof("crossTenantEniConfig is %s", crossTenantEniConfig)
			annotations["tke.cloud.tencent.com/cross-tenant-eni-config"] = crossTenantEniConfig
			return annotations, nil
		}

		// 内网，增加租户弹性网卡
		if ttype == constants.PrivateType {
			// 共享集群拿用户VPC ip
			if s.clusterGroup.AgentSerialId != "" {
				groupService, err := NewClusterGroupServiceByGroup(s.clusterGroup)
				if err != nil {
					return nil, err
				}

				clusterVpc, err := groupService.GetGroupPeerVpc()
				if err != nil {
					return nil, err
				}
				crossTenantEniConfig := fmt.Sprintf("{\"AppId\":%d,\"Uin\":\"%s\",\"UniqVpcId\":\"%s\",\"SubnetId\":\"%s\"}",
					s.clusterGroup.AppId, s.clusterGroup.OwnerUin, clusterVpc.VpcId, clusterVpc.SubnetId)
				logger.Infof("crossTenantEniConfig is %s", crossTenantEniConfig)
				annotations["tke.cloud.tencent.com/cross-tenant-eni-config"] = crossTenantEniConfig
				return annotations, nil
			} else {
				if existFunc("tke.cloud.tencent.com/cross-tenant-eni-config", annotations) {
					delete(annotations, "tke.cloud.tencent.com/cross-tenant-eni-config")
				}
			}
			return annotations, nil
		}
		return annotations, nil
	}

	// 公网，移除弹性网卡
	if ttype == constants.PublicType {
		if existFunc("tke.cloud.tencent.com/cross-tenant-eni-enable", annotations) {
			delete(annotations, "tke.cloud.tencent.com/cross-tenant-eni-enable")
		}
		if existFunc("tke.cloud.tencent.com/networks", annotations) {
			delete(annotations, "tke.cloud.tencent.com/networks")
		}
		if existFunc("tke.cloud.tencent.com/cross-tenant-eni-config", annotations) {
			delete(annotations, "tke.cloud.tencent.com/cross-tenant-eni-config")
		}
		return annotations, nil
	}

	// 内网，增加弹性网卡
	if ttype == constants.PrivateType {
		if !existFunc("tke.cloud.tencent.com/cross-tenant-eni-enable", annotations) {
			annotations["tke.cloud.tencent.com/cross-tenant-eni-enable"] = "true"
		}
		if !existFunc("tke.cloud.tencent.com/networks", annotations) {
			annotations["tke.cloud.tencent.com/networks"] = "tke-bridge,tke-direct-eni,tke-route"
		}
		if s.clusterGroup.AgentSerialId != "" {
			clusterGroupService, err := NewClusterGroupService(s.clusterGroup.Id)
			if err != nil {
				return nil, err
			}
			eniConfig, err := clusterGroupService.GetClusterEniParam(false)
			if err != nil {
				return nil, err
			}
			annotations["tke.cloud.tencent.com/cross-tenant-eni-config"] = eniConfig
		}
		return annotations, nil
	}
	return annotations, nil
}

/**
 * 设置ingress service type clusterIp
 * 设置ingress deployemnt 弹性网卡
 * 这里不能立马成功，通过DescribeWebUIWhiteList获取结果，设置最终状态
 */
func (s *ModifyWebUIWhiteListService) ModifyIngress(ttype int) (err error) {
	if s.cluster.CrossTenantEniMode == constants.EnableStaticMode {
		err = s.SetServiceType(corev1.ServiceTypeClusterIP)
	} else {
		if ttype == constants.PrivateType {
			err = s.SetServiceType(corev1.ServiceTypeClusterIP)
		}
		if ttype == constants.PublicType {
			err = s.SetServiceType(corev1.ServiceTypeLoadBalancer)
		}
	}

	if err != nil {
		return err
	}
	name := fmt.Sprintf("%s-nginx-ingress-controller", DefaultName(s.clusterGroup))

	namespace := DefaultNamespace(s.clusterGroup)

	deploy, err := s.k8sService.GetDeploymentAppsV1(s.k8sClient, namespace, name)
	if err != nil {
		logger.Errorf("Failed to GetDeploymentAppsV1,err: %v", err.Error())
		return err
	}
	if deploy == nil {
		return errorcode.FailedOperationCode_InvalidDNS.NewWithInfo("Deployment nginx-ingress-controller not installed.", nil)
	}
	if s.clusterGroup.NetEnvironmentType == constants.NETWORK_ENV_CLOUD_VPC {
		annotationList := deploy.Spec.Template.Annotations
		ll := len(annotationList)
		logger.Infof("before: %+v,  ll is %d", annotationList, ll)
		annotations, err := s.GetAnnotations(ttype, annotationList)
		if err != nil {
			return err
		}
		logger.Infof("after: %+v, ll is %d, len(annotations) is %d", annotations, ll, len(annotations))
		deploy.Spec.Template.Annotations = annotations
	}
	_, err = s.k8sService.ApplyDeploymentAppsV1(s.k8sClient, deploy)
	if err != nil {
		logger.Errorf("Failed to ApplyDeploymentAppsV1,err: %v", err.Error())
		return err
	}
	if s.cluster.CrossTenantEniMode == constants.EnableStaticMode && s.clusterGroup.NetEnvironmentType == constants.NETWORK_ENV_CLOUD_VPC {
		flinkVersionService := NewFlinkUiClbService(s.k8sClient, s.clusterGroup, s.cluster)
		if ttype == constants.PrivateType {
			err = flinkVersionService.DeleteFlinkUiRule()
			if err != nil {
				logger.Errorf("Failed to DeleteFlinkUiRule,err: %v", err.Error())
				return err
			}
		}
	}
	return nil
}

func (s *ModifyWebUIWhiteListService) ModifyWebUIWhiteList() (rsp *model.ModifyWebUIWhiteListRsp, err error) {
	rsp = &model.ModifyWebUIWhiteListRsp{RequestId: s.request.RequestId}

	s.clusterGroup, err = GetClusterGroupBySerialId(s.request.ClusterId)
	if err != nil {
		return nil, errorcode.FailedOperationCode.NewWithErr(err)
	}
	if int64(s.clusterGroup.AppId) != s.request.AppId || s.clusterGroup.OwnerUin != s.request.Uin {
		return nil, errorcode.FailedOperationCode.ReplaceDesc(fmt.Sprintf("The appid %d or uin %s has no operation permission.", s.request.AppId, s.request.Uin))
	}

	cluster, err := GetActiveClusterByClusterGroupId(s.clusterGroup.Id)
	s.cluster = cluster
	if err != nil {
		return nil, errorcode.FailedOperationCode.NewWithErr(err)
	}

	s.k8sClient, err = tke2.GetTkeService().KubernetesClientsetFromCluster("ModifyIngressServiceType", cluster)
	if err != nil {
		return nil, errorcode.FailedOperationCode.NewWithErr(err)
	}

	// 切换访问方式
	if (s.request.Type == constants.PrivateType && cluster.WebUIType == constants.PublicType) ||
		(s.request.Type == constants.PublicType && cluster.WebUIType == constants.PrivateType) {
		err := s.ModifyIngress(s.request.Type)
		if err != nil {
			return nil, errorcode.FailedOperationCode.NewWithErr(err)
		}

		// 设置webui地址
		service4.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
			tx.ExecuteSqlWithArgs("update Cluster set WebUIType=? where Id=?", s.request.Type, cluster.Id)
			return nil
		}).Close()

		return rsp, nil
	}
	//	新集群直接返回，不需要绑定lb
	if s.cluster.CrossTenantEniMode == constants.EnableStaticMode {
		return rsp, nil
	}

	// 如果是设置公网，且之前就是公网，就是改变安全组
	// 1. 查询ingress绑定的lb
	loadBalancerId, err := GetLoadBalancerId(s.request.ClusterId)
	if err != nil {
		logger.Errorf("[%s] Failed to get load balancer ,err: %v", s.request.RequestId, err.Error())
		return rsp, errorcode.FailedOperationCode.NewWithErr(err)
	}
	logger.Infof("[%s] Find ingress bound load balancer %s", s.request.RequestId, loadBalancerId)
	// 2. 检查clb是否绑定安全组
	secretId, secretKey, err := service3.GetSecretIdAndKeyByNetworkEnvType(s.clusterGroup.NetEnvironmentType)
	if err != nil {
		return rsp, err
	}
	prof := qcloud.NewClientProfile()
	credential := common.NewTokenCredential(secretId, secretKey, "")
	s.clbClient, err = clb.NewClient(credential, s.request.Region, prof)
	if err != nil {
		logger.Errorf("[%s] Failed to NewClient[clb] ,err: %v", s.request.RequestId, err.Error())
		return rsp, errorcode.FailedOperationCode.NewWithErr(err)
	}
	describeLoadBalancersReq := clb.NewDescribeLoadBalancersRequest()
	describeLoadBalancersReq.LoadBalancerIds = append(describeLoadBalancersReq.LoadBalancerIds, &loadBalancerId)
	describeLoadBalancersRsp, err := s.clbClient.DescribeLoadBalancers(describeLoadBalancersReq)
	if err != nil {
		logger.Errorf("[%s] Failed to DescribeLoadBalancers ,err: %v", s.request.RequestId, err.Error())
		return rsp, errorcode.FailedOperationCode.NewWithErr(err)
	}
	loadBalancerSet := describeLoadBalancersRsp.Response.LoadBalancerSet
	if len(loadBalancerSet) == 0 {
		return rsp, errorcode.FailedOperationCode.ReplaceDesc("loadBalancerSet is empty,load balancer id " + loadBalancerId)
	}
	loadBalancer := loadBalancerSet[0]
	var webSecurityGroupBound = len(loadBalancer.SecureGroups) > 0
	// 3.查询webUI安全组
	vpcService := vpc2.NewVpcService()
	describeSecurityGroupsReq := vpc.NewDescribeSecurityGroupsRequest()
	var securityGroups = make([]*vpc.SecurityGroup, 0)
	var webSecurityGroupFound = false
	var webSecurityGroup = ""
	if webSecurityGroupBound { // loadBalancer 已绑定安全组
		for _, securityGroup := range loadBalancer.SecureGroups {
			describeSecurityGroupsReq.SecurityGroupIds = append(describeSecurityGroupsReq.SecurityGroupIds, securityGroup)
		}
		securityGroups, err = vpcService.DescribeSecurityGroups(s.request.Region, s.clusterGroup.NetEnvironmentType, describeSecurityGroupsReq)
		if err != nil {
			logger.Errorf("[%s] Failed to DescribeSecurityGroups ,err: %v", s.request.RequestId, err.Error())
			return rsp, errorcode.FailedOperationCode.NewWithErr(err)
		}
		webSecurityGroup, webSecurityGroupFound = FindSecurityGroup(s.request.ClusterId, securityGroups)
		logger.Infof("[%s] Find flink ui security group : %t", s.request.RequestId, webSecurityGroupFound)
		if s.checkDelSecurityGroupPolicies() { // 删除安全策略，放通所有IP
			// 清空安区组策略，并解绑安全组
			err := s.unboundSecurityGroup(loadBalancerId, webSecurityGroup)
			if err != nil {
				logger.Errorf("[%s] Failed to unbound security group ,err: %v", s.request.RequestId, err.Error())
				return rsp, errorcode.FailedOperationCode.NewWithErr(err)
			}
			logger.Infof("[%s] Unbound security group %s success", s.request.RequestId, webSecurityGroup)
			return rsp, nil
		}
	} else {                                   // loadBalancer 未已绑定安全组
		if s.checkDelSecurityGroupPolicies() { // 删除安全策略，放通所有IP
			// 未绑定安全组，无需任何操作
			return rsp, nil
		}
		// 查询lb是否曾经绑定过lb
		describeSecurityGroupsReq = vpc.NewDescribeSecurityGroupsRequest()
		securityGroupNameFilter := "security-group-name"
		describeSecurityGroupsFilter := &vpc.Filter{
			Name:   &securityGroupNameFilter,
			Values: nil,
		}
		securityGroupName := GenSecurityGroupName(s.request.ClusterId)
		describeSecurityGroupsFilter.Values = append(describeSecurityGroupsFilter.Values, &securityGroupName)
		describeSecurityGroupsReq.Filters = append(describeSecurityGroupsReq.Filters, describeSecurityGroupsFilter)
		securityGroups, err = vpcService.DescribeSecurityGroups(s.request.Region, s.clusterGroup.NetEnvironmentType, describeSecurityGroupsReq)
		if err != nil {
			logger.Errorf("[%s] Failed to DescribeSecurityGroups ,err: %v", s.request.RequestId, err.Error())
			return rsp, errorcode.FailedOperationCode.NewWithErr(err)
		}
		webSecurityGroup, webSecurityGroupFound = FindSecurityGroup(s.request.ClusterId, securityGroups)
		logger.Infof("[%s] Find flink ui security group : %t", s.request.RequestId, webSecurityGroupFound)
	}
	// 4.创建安全组
	if !webSecurityGroupFound {
		vpcClient, err := vpc.NewClient(credential, s.request.Region, prof)
		if err != nil {
			logger.Errorf("[%s] Failed to NewClient[vpc] ,err: %v", s.request.RequestId, err.Error())
			return rsp, errorcode.FailedOperationCode.NewWithErr(err)
		}
		securityGroupDescription := "flink-ui-white-list"
		createSecurityGroupReq := vpc.NewCreateSecurityGroupRequest()
		var groupName = GenSecurityGroupName(s.request.ClusterId)
		createSecurityGroupReq.GroupName = &groupName
		createSecurityGroupReq.GroupDescription = &securityGroupDescription
		createSecurityGroupRsp, err := vpcClient.CreateSecurityGroup(createSecurityGroupReq)
		if err != nil {
			logger.Errorf("[%s] Failed to CreateSecurityGroup ,err: %v", s.request.RequestId, err.Error())
			return rsp, errorcode.FailedOperationCode.NewWithErr(err)
		}
		logger.Infof("[%s] Create security group: %s success.", s.request.RequestId, groupName)
		webSecurityGroup = *createSecurityGroupRsp.Response.SecurityGroup.SecurityGroupId
	}
	// 5. 配置安全组策略
	modifySecurityGroupPoliciesReq := vpc.NewModifySecurityGroupPoliciesRequest()
	modifySecurityGroupPoliciesReq.SecurityGroupId = &webSecurityGroup
	//  检查并生成安全组策略
	securityGroupPolicies, err := s.genSecurityGroupPolicies(s.request.CidrBlocks)
	if err != nil {
		logger.Errorf("[%s] Failed to generate security group policies, err %v", s.request.RequestId, err.Error())
		return rsp, errorcode.FailedOperationCode.NewWithErr(err)
	}
	modifySecurityGroupPoliciesReq.SecurityGroupPolicySet = securityGroupPolicies
	_, err = vpcService.ModifySecurityGroupPolicies(s.request.Region, s.clusterGroup.NetEnvironmentType, modifySecurityGroupPoliciesReq)
	if err != nil {
		logger.Errorf("[%s] Failed to ModifySecurityGroupPolicies, err %v", s.request.RequestId, err.Error())
		return rsp, s.wrapException4ModifySecurityGroupPolicies(err)
	}
	logger.Infof("[%s] Modify security group %s policies success.", s.request.RequestId, webSecurityGroup)
	// 6. clb 绑定安全组
	if !webSecurityGroupBound {
		setLoadBalancerSecurityGroupsReq := clb.NewSetLoadBalancerSecurityGroupsRequest()
		setLoadBalancerSecurityGroupsReq.LoadBalancerId = &loadBalancerId
		setLoadBalancerSecurityGroupsReq.SecurityGroups = append(setLoadBalancerSecurityGroupsReq.SecurityGroups, &webSecurityGroup)
		_, err := s.setLoadBalancerSecurityGroups(setLoadBalancerSecurityGroupsReq)
		if err != nil {
			logger.Errorf("[%s] Failed to SetLoadBalancerSecurityGroups, err %v", s.request.RequestId, err.Error())
			return rsp, errorcode.FailedOperationCode.NewWithErr(err)
		}
		logger.Infof("[%s] Set security group %s  to load balancer %s success.", s.request.RequestId, webSecurityGroup, loadBalancerId)
	}
	return rsp, nil
}

/**
 * ingress 格式：
 * 支持多个IP，IP之间以英文逗号分隔，格式可以是***********,***********/24，最多支持50个。禁止设置白名单为0.0.0.0。
 */
func (s *ModifyWebUIWhiteListService) genSecurityGroupPolicies(cidrBlocks string) (policies *vpc.SecurityGroupPolicySet, err error) {
	policies = &vpc.SecurityGroupPolicySet{}
	if cidrBlocks == "" { // 安全组设置为空，放通所有IP
		policies.Ingress = nil
		policies.Ingress = make([]*vpc.SecurityGroupPolicy, 0)
		return policies, nil
	}
	// cidr block 解析
	cidrs := strings.Split(cidrBlocks, ",")
	if len(cidrs) > CidrBlocksMaxLength { // 最多支持50个
		return policies, errorcode.InvalidParameterValueCode.ReplaceDesc("cidr blocks can not be more than " + strconv.Itoa(CidrBlocksMaxLength))
	}
	securityGroupPolicyProtocol := "TCP"
	securityGroupPolicyPort := "80,443"
	securityGroupPolicyAction := "ACCEPT"
	securityGroupPolicyModifyTime := util.GetCurrentTime()
	for _, cidr := range cidrs {
		var currCidr = cidr
		if strings.Contains(cidr, "0.0.0.0") {
			return policies, errorcode.InvalidParameterValueCode.ReplaceDesc("cidr block can not be set to 0.0.0.0")
		}
		policies.Ingress = append(policies.Ingress, &vpc.SecurityGroupPolicy{
			Protocol:   &securityGroupPolicyProtocol,
			Port:       &securityGroupPolicyPort,
			CidrBlock:  &currCidr,
			Action:     &securityGroupPolicyAction,
			ModifyTime: &securityGroupPolicyModifyTime,
		})
	}
	return policies, nil
}

func (s *ModifyWebUIWhiteListService) checkDelSecurityGroupPolicies() bool {
	return s.request.CidrBlocks == CidrBlocksEmpty
}

func (s *ModifyWebUIWhiteListService) unboundSecurityGroup(loadBalancerId string, securityGroupId string) error {
	vpcService := vpc2.NewVpcService()
	// 1. 查询安全策略
	describeSecurityGroupPoliciesReq := vpc.NewDescribeSecurityGroupPoliciesRequest()
	describeSecurityGroupPoliciesReq.SecurityGroupId = &securityGroupId
	describeSecurityGroupPoliciesRsp, err := vpcService.DescribeSecurityGroupPolicies(s.request.Region, s.clusterGroup.NetEnvironmentType, describeSecurityGroupPoliciesReq)
	if err != nil {
		logger.Errorf("[%s] Failed to DescribeSecurityGroupPolicies, err %v", s.request.RequestId, err.Error())
		return errorcode.FailedOperationCode.NewWithErr(err)
	}
	// 2. 删除全部安全策略
	deleteSecurityGroupPoliciesRequest := vpc.NewDeleteSecurityGroupPoliciesRequest()
	deleteSecurityGroupPoliciesRequest.SecurityGroupId = &securityGroupId
	deleteSecurityGroupPoliciesRequest.SecurityGroupPolicySet =
		&vpc.SecurityGroupPolicySet{
			Ingress: describeSecurityGroupPoliciesRsp.Ingress,
		}
	_, err = vpcService.DeleteSecurityGroupPolicies(s.request.Region, s.clusterGroup.NetEnvironmentType, deleteSecurityGroupPoliciesRequest)
	if err != nil {
		logger.Errorf("[%s] Failed to DeleteSecurityGroupPolicies, err %v", s.request.RequestId, err.Error())
		return errorcode.FailedOperationCode.NewWithErr(err)
	}
	// 3. 解绑安全组
	setLoadBalancerSecurityGroupsReq := clb.NewSetLoadBalancerSecurityGroupsRequest()
	setLoadBalancerSecurityGroupsReq.LoadBalancerId = &loadBalancerId
	_, err = s.setLoadBalancerSecurityGroups(setLoadBalancerSecurityGroupsReq)
	if err != nil {
		logger.Errorf("[%s] Failed to setLoadBalancerSecurityGroups, err %v", s.request.RequestId, err.Error())
		return errorcode.FailedOperationCode.NewWithErr(err)
	}
	return nil
}

func (s *ModifyWebUIWhiteListService) setLoadBalancerSecurityGroups(request *clb.SetLoadBalancerSecurityGroupsRequest) (
	rsp *clb.SetLoadBalancerSecurityGroupsResponse, err error) {
	rsp, err = s.clbClient.SetLoadBalancerSecurityGroups(request)
	if err != nil {
		logger.Errorf("[%s] Failed to SetLoadBalancerSecurityGroups, err %v", s.request.RequestId, err.Error())
		return rsp, errorcode.FailedOperationCode.NewWithErr(err)
	}
	return rsp, nil
}

func (s *ModifyWebUIWhiteListService) wrapException4ModifySecurityGroupPolicies(err error) error {

	if sdkError, ok := err.(*tencentCloudErr.TencentCloudSDKError); ok {
		if sdkError.GetCode() == "InvalidParameterValue" && strings.Contains(sdkError.Message, "SecurityGroupPolicySet.Ingress") {
			index := strings.Index(sdkError.Message, "值")
			if index > 0 {
				logger.Infof("[%s] Wrap InvalidParameterValueException for ModifySecurityGroupPolicies,origin error %v", s.request.RequestId, err)
				return errorcode.InvalidParameterValueCode.ReplaceDesc(sdkError.Message[index:])
			}
		}
	}
	return err
}

func GetLoadBalancerId(clusterId string) (loadBalancerId string, err error) {
	// 1. 查询ingress绑定的lb
	k8sService := k8s.GetK8sService()
	clusterGroup, err := GetClusterGroupBySerialId(clusterId)
	if err != nil {
		logger.Errorf("Failed to GetClusterGroupBySerialId [ClusterId %s] ,err %v", clusterId, err)
		return "", errorcode.FailedOperationCode.NewWithErr(err)
	}
	clusterGroupService, err := NewClusterGroupService(clusterGroup.Id)
	if err != nil {
		logger.Errorf("Failed to NewClusterGroupService [ClusterId %s] ,err %v", clusterId, err)
		return "", errorcode.FailedOperationCode.NewWithErr(err)
	}
	cluster, err := clusterGroupService.GetActiveCluster()
	if err != nil {
		logger.Errorf("Failed to GetActiveCluster [ClusterId %s] ,err %v", clusterId, err)
		return "", errorcode.FailedOperationCode.NewWithErr(err)
	}
	clientSet, err := k8sService.NewClient([]byte(cluster.KubeConfig))
	if err != nil {
		logger.Errorf("Failed to  NewClient[K8s] [ClusterId %s] ,err %v", clusterId, err)
		return "", errorcode.FailedOperationCode.NewWithErr(err)
	}

	namespace := DefaultNamespace(clusterGroup)
	name := fmt.Sprintf("%s-nginx-ingress-controller", DefaultName(clusterGroup))
	ingressSvc, err := k8sService.GetService(clientSet, namespace, name)
	if err != nil {
		logger.Errorf("Failed to [K8s]GetService [ClusterId %s] ,err %v", clusterId, err)
		return "", errorcode.FailedOperationCode.NewWithErr(err)
	}
	loadBalancerId, ok := ingressSvc.Annotations["service.kubernetes.io/loadbalance-id"]
	if !ok {
		logger.Errorf("Failed to Annotations[service.kubernetes.io/loadbalance-id] [ClusterId %s] ,err %v", clusterId, err)
		return "", errorcode.FailedOperationCode.NewWithMsg("Annotations[service.kubernetes.io/loadbalance-id] not found")
	}
	return loadBalancerId, nil
}

func GenSecurityGroupName(ClusterId string) string {
	return ClusterId + "-" + SecurityGroupNameSuffix
}

func FindSecurityGroup(clusterId string, securityGroups []*vpc.SecurityGroup) (securityGroupId string, webSecurityGroupFound bool) {
	targetSecurityGroupName := GenSecurityGroupName(clusterId)
	for _, securityGroup := range securityGroups {
		logger.Infof("Find security group %s", *securityGroup.SecurityGroupName)
		if *securityGroup.SecurityGroupName == targetSecurityGroupName {
			return *securityGroup.SecurityGroupId, true
		}
	}
	return "", false
}
