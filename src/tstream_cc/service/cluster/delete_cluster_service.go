package service

import (
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/cluster"
)

type DeleteClusterService struct {
	req          *model.DeleteClusterReq
	groupService *ClusterGroupService
}

func DeleteCluster(req *model.DeleteClusterReq) (err error) {
	deleteClusterService, err := NewDeleteClusterService(req)
	if err != nil {
		return err
	}

	_, err = deleteClusterService.validityReq()
	if err != nil {
		return err
	}

	_, err = deleteClusterService.groupService.CanDelete()
	if err != nil {
		return err
	}

	_, err = deleteClusterService.Delete()
	return err
}

func NewDeleteClusterService(req *model.DeleteClusterReq) (d *DeleteClusterService, err error) {
	d = &DeleteClusterService{req: req}
	d.groupService, err = NewClusterGroupServiceBySerialId(req.ClusterId)
	return d, err
}

func (d *DeleteClusterService) validityReq() (ok bool, err error) {
	group := d.groupService.GetClusterGroup()
	if int64(group.AppId) != d.req.AppId {
		return false, errorcode.NewStackError(errorcode.InvalidParameter_InvalidAppId, "AppID was not matched", nil)
	}

	if group.Region != d.req.Region {
		return false, errorcode.NewStackError(errorcode.InvalidParameter_InvalidRegion, "Region not matched", nil)
	}
	return true, nil
}

func (d *DeleteClusterService) deleteAutoDeployCluster() (flowId int64, err error) {
	clusterGroup := d.groupService.GetClusterGroup()
	docId := fmt.Sprintf("%s@%s", clusterGroup.SerialId, d.req.RequestId)

	isEks, _ := IsEks(clusterGroup.Id)
	clusterType := constants.K8S_CLUSTER_TYPE_TKE
	if isEks {
		clusterType = constants.K8S_CLUSTER_TYPE_EKS
	}

	cluster, err := d.groupService.GetCluster()
	if err != nil {
		return 0, err
	}

	if err = d.groupService.MarkDeleting(); err != nil {
		return 0, err
	}

	flowId, err = flow.CreateFlow(constants.FLOW_OCEANUS_DELETE_CLUSTER, docId,
		0, map[string]string{
			constants.FLOW_PARAM_REQUEST_ID:       d.req.RequestId,
			constants.FLOW_PARAM_CLUSTER_GROUP_ID: fmt.Sprintf("%d", clusterGroup.Id),
			constants.FLOW_PARAM_CLUSTER_TYPE:     fmt.Sprintf("%d", clusterType),
			constants.FLOW_PARAM_CLUSTER_ID:       fmt.Sprintf("%d", cluster.Id),
		}, nil)

	if err != nil {
		err = errorcode.NewStackError(errorcode.InternalErrorCode_CreateFlowFailed, "CreateFlow", err)
		return 0, err
	}

	logger.Infof("[%s] createFlow %s success, flowId %d", d.req.RequestId, constants.FLOW_OCEANUS_DELETE_CLUSTER, flowId)
	return flowId, err
}

// 删除接入计费前创建的历史集群
func (d *DeleteClusterService) deleteHistoryCluster() (flowId int64, err error) {
	return 0, d.groupService.MarkDeletedWithRemark("destroy me")
}

func (d *DeleteClusterService) Delete() (flowId int64, err error) {
	tkeList, err := d.groupService.GetTkeList()
	if err != nil {
		return
	}

	if len(tkeList) != 0 {
		return d.deleteAutoDeployCluster()
	}

	return d.deleteHistoryCluster()
}
