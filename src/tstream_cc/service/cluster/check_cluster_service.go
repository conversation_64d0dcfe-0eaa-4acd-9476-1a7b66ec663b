package service

import (
	"fmt"
	"strings"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	constants1 "tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/tke"
	tke2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/tke"
)

func CheckCluster(serialId string, checkRunning bool) (errorCode string, errorMsg string, cluster *table.Cluster, tke *table2.Tke) {
	clusterGroupService, err := NewClusterGroupServiceBySerialId(serialId)
	if err != nil {
		msg := fmt.Sprintf("CheckCluster -> NewClusterGroupServiceBySerialId(%x) error:%+v", serialId, err)
		logger.Error(msg)
		return controller.InternalError, msg, nil, nil
	}
	//check cluster
	cluster, err = clusterGroupService.GetActiveCluster()
	if err != nil {
		msg := fmt.Sprintf("CheckCluster -> GetActiveCluster(%s) error:%+v", serialId, err)
		logger.Error(msg)
		return controller.InternalError, msg, nil, nil
	}
	if cluster.RoleType != constants1.CLUSTER_ROLE_TYPE_ACTIVE {
		return controller.InvalidClusterState, "cluster role type not active", nil, nil
	}
	if cluster.SchedulerType != constants1.CLUSTER_SCHEDULER_TYPE_TKE {
		return controller.InvalidClusterState, "cluster scheduler type not tke", nil, nil
	}
	//if len(cluster.ClusterConfig) == 0 {
	//	return controller.InvalidClusterState, "cluster config is empty", nil
	//}
	if len(cluster.KubeConfig) == 0 {
		return controller.InvalidClusterState, "cluster kube config is empty", nil, nil
	}
	if len(cluster.DefaultCOSBucket) == 0 {
		return controller.InvalidClusterState, "cluster is not COS storage", nil, nil
	}
	if strings.Compare(cluster.FlinkVersion, constants1.FlinkVersion) < 0 {
		return controller.InvalidClusterState, "cluster Flink Version less than Flink-1.11", nil, nil
	}
	//check cluster group
	clusterGroup := clusterGroupService.GetClusterGroup()
	if clusterGroup.Status != constants1.CLUSTER_GROUP_STATUS_RUNNING &&
		clusterGroup.Status != constants1.CLUSTER_GROUP_STATUS_SCALE_PROGRESS &&
		clusterGroup.Status != constants1.CLUSTER_GROUP_STATUS_RECOVERING &&
		clusterGroup.Status != constants1.CLUSTER_GROUP_STATUS_UPGRADE &&
		clusterGroup.Status != constants1.CLUSTER_GROUP_STATUS_ISOLATED && clusterGroup.Status != constants1.CLUSTER_GROUP_STATUS_SCALEDOWN_PROGRESS {
		return controller.InvalidClusterGroupState, "cluster group status is not running or scale_progress or isolated or scaledown_progress", nil, nil

	}
	//if clusterGroup.NetEnvironmentType != constants1.NETWORK_ENV_CLOUD_VPC {
	//	return controller.InvalidClusterGroupState, "cluster group is not cloud vpc", nil
	//}
	//check tke
	tkeList, err := clusterGroupService.GetTkeList()
	if err != nil {
		msg := fmt.Sprintf("CheckCluster -> GetTkeList(%s) error:%+v", serialId, err)
		logger.Error(msg)
		return controller.InternalError, msg, nil, nil
	}
	if len(tkeList) != 1 {
		return controller.InternalError, fmt.Sprintf("tke length is %d, not 1", len(tkeList)), nil, nil
	}
	tke = tkeList[0]
	if tke.Status != constants1.TKE_STATUS_RUNNING {
		return controller.InvalidTkeState, "tke status is not running", nil, nil
	}
	if checkRunning {
		running, err := tke2.IsClusterRunning(clusterGroup, cluster, tke)
		if err != nil {
			msg := fmt.Sprintf("CheckCluster -> IsClusterRunning(%d) error:%+v", cluster.Id, err)
			logger.Error(msg)
			return controller.InternalError, msg, nil, nil
		}
		if !running {
			return controller.InvalidTkeState, "tke is not running", nil, nil
		}
	}
	if tke.WorkerStatus != constants1.K8S_CLUSTER_WITH_WORKER {
		return controller.InvalidTkeState, "tke work is not added", nil, nil
	}
	//check Emr
	emrInstances, err := clusterGroupService.GetEmrList()
	if err != nil {
		msg := fmt.Sprintf("DoUpdateTke -> ListEmrByClusterId(%d) error:%+v", cluster.Id, err)
		logger.Error(msg)
		return controller.InternalError, msg, nil, nil
	}
	if len(emrInstances) != 0 {
		return controller.InvalidClusterState, fmt.Sprintf("emr length is %d, not 0", len(tkeList)), nil, nil
	}
	return errorCode, errorMsg, cluster, tke
}
