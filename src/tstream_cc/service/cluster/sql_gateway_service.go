package service

import (
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"path/filepath"
	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model"
	model2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/cluster"
	model5 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_config"
	model4 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/resource_config"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	model3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/resource_config"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/bucket"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cos"
	"time"
)

const (
	SQL_GATEWAY_JARS_UPLOAD_PATH = "/user/user_00/.flink/%s/%d/systemShipFiles"
)

type SqlGatewayService struct {
	RequestId string
}

var SupportedFlinkVersion = map[string]struct{}{
	constants.FLINK_VERSION_1_17: {},
	constants.FLINK_VERSION_1_18: {},
}

type SqlGatewayResourceItem struct {
	Url  string `json:"url"`
	Path string `json:"path"`
}

func (o *SqlGatewayService) CreateSqlGateway(req *model2.CreateSqlGatewayReq) (rsp *model2.CreateSqlGatewayRsp, err error) {
	if !auth.IsInWhiteList(req.AppId, constants.WHITE_LIST_SQL_GATEWAY) {
		logger.Errorf("[%s] AppId %d has no operation permission to create sql gateway.", req.RequestId, req.AppId)
		return rsp, errorcode.FailedOperationCode.ReplaceDesc(fmt.Sprintf("[%s] has no operation permission to create sql gateway.", req.RequestId))
	}
	clusterGroup, err := GetClusterGroupBySerialId(req.ClusterId)
	if err != nil {
		logger.Errorf("[%s] Failed to  GetClusterGroupBySerialId,error: %+v", req.RequestId, err)
		return rsp, errorcode.FailedOperationCode.NewWithErr(err)
	}
	cluster, err := GetActiveClusterByClusterGroupId(clusterGroup.Id)
	if err != nil {
		logger.Errorf("[%s] Failed to  GetActiveClusterByClusterGroupId,error: %+v", req.RequestId, err)
		return rsp, errorcode.FailedOperationCode.NewWithErr(err)
	}
	if int64(clusterGroup.AppId) != req.AppId || clusterGroup.OwnerUin != req.Uin {
		return nil, errorcode.FailedOperationCode.ReplaceDesc(fmt.Sprintf("The appid %d or uin %s has no operation permission.", req.AppId, req.Uin))
	}
	//1. Check if the conditions for enabling are met.
	//1.1 Check if the flink version is supported.
	if _, ok := SupportedFlinkVersion[req.FlinkVersion]; !ok {
		msg := fmt.Sprintf("[%s] Unsupported flink version %s", req.RequestId, req.FlinkVersion)
		logger.Errorf(msg)
		return rsp, errorcode.FailedOperationCode.ReplaceDesc(msg)
	}
	// 1.2 Check if the cluster status is active
	if clusterGroup.Status != constants.CLUSTER_GROUP_STATUS_RUNNING {
		return rsp, errorcode.FailedOperationCode.ReplaceDesc(fmt.Sprintf("[%s] The cluster is no longer active.", req.RequestId))
	}
	// 1.3. Check if the cluster type is tke/eks
	isEks, _ := IsEks(cluster.ClusterGroupId)
	if cluster.SchedulerType != constants.CLUSTER_SCHEDULER_TYPE_TKE && !isEks {
		return rsp, errorcode.UnsupportedOperationCode.ReplaceDesc(fmt.Sprintf("[%s] The cluster %s type does not support this feature.", req.RequestId, req.ClusterId))
	}
	sqlGatewayExists, gateway, err := GetSqlGateway(req.AppId, cluster.Id, req.FlinkVersion)
	if err != nil {
		logger.Errorf("[%s] Failed to GetSqlGateway,error: %+v", req.RequestId, err)
		return rsp, errorcode.FailedOperationCode.NewWithErr(err)
	}
	// 1.4. Check if the sql gateway is stopped or deleted
	if sqlGatewayExists {
		if gateway.Status != constants.SqlGatewayStopped && gateway.Status != constants.SqlGatewayDeleted {
			return rsp, errorcode.FailedOperationCode.ReplaceDesc(fmt.Sprintf("[%s] The SqlGateway %s is not in the stopped state , cannot be started.", req.RequestId, gateway.SerialId))
		}
	}
	//2. generated resource url & resource ref
	gatewayRefs := make([]*table.SqlGatewayRef, 0)
	locs := make([]*model4.ResourceLocation, 0)
	for _, ref := range req.ResourceRefs {
		resourceConfig, err := GetResourceConfigByResourceRef(&model5.ResourceRefItem{
			ResourceId: ref.ResourceId,
			Type:       ref.Type,
			Version:    ref.Version,
		})
		if err != nil {
			logger.Errorf("[%s] Failed to GetResourceConfigByResourceRef,error: %+v", req.RequestId, err)
			return rsp, errorcode.FailedOperationCode.NewWithErr(err)
		}
		loc := &model4.ResourceLocation{}
		err = json.Unmarshal([]byte(resourceConfig.ResourceLoc), loc)
		if err != nil {
			logger.Errorf("[%s] Failed to Unmarshal,error: %+v", req.RequestId, err)
			return rsp, errorcode.FailedOperationCode.NewWithErr(err)
		}
		locs = append(locs, loc)
		gatewayRefs = append(gatewayRefs, &table.SqlGatewayRef{
			ResourceId: resourceConfig.ResourceId,
			VersionId:  ref.Version,
			Status:     constants.RESOURCE_REF_STATUS_ACTIVE,
			Type:       ref.Type,
			CreateTime: util.GetCurrentTime(),
			UpdateTime: util.GetCurrentTime(),
		})
	}
	// upload path : user/user_00/.flink/sgw-xxx/1695120670803/systemShipFiles
	generateSqlGatewayRefs := func(gatewaySerialId string, locs []*model4.ResourceLocation) (path string, refs string, err error) {
		sqlGatewayResourceItems := make([]*SqlGatewayResourceItem, 0)
		uploadPath := fmt.Sprintf(SQL_GATEWAY_JARS_UPLOAD_PATH, gatewaySerialId, time.Now().UnixNano()/1e6)
		for _, loc := range locs {
			inOceanus, err := service2.BucketExists(loc.Param.Bucket)
			if err != nil {
				logger.Errorf("[%s] Failed to  BucketExists,err: %+v", req.RequestId, err)
				panic(errorcode.NewStackError(errorcode.InternalErrorCode, "Failed to get GetSecretIdAndKeyOfCos", err))
			}
			var secretId, secretKey, token string
			var pass bool
			if inOceanus {
				secretId, secretKey, err = service3.GetSecretIdAndKeyOfCos()
				if err != nil {
					logger.Errorf("[%s] Failed to GetSecretIdAndKeyOfCos,err: %+v", req.RequestId, err)
					panic(errorcode.NewStackError(errorcode.InternalErrorCode, "Failed to get GetSecretIdAndKeyOfCos", err))
				}
			} else {
				secretId, secretKey, token, pass, err = service3.StsAssumeRole(req.Uin, req.SubAccountUin, req.Region)
				if err != nil {
					logger.Errorf("[%s] Failed to GetSecretIdAndKeyOfCos,err: %+v", req.RequestId, err)
					panic(errorcode.NewStackError(errorcode.InternalErrorCode, "Failed to get GetSecretIdAndKeyOfCos", err))
				}
				if !pass {
					logger.Errorf("[%s] No permission to access the user's bucket %s. ", req.RequestId, loc.Param.Bucket)
					panic(errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("[%s] No permission to access the user's bucket %s. ", req.RequestId, loc.Param.Bucket), err))
				}
			}
			cosClient := cos.NewCosClient(secretId, secretKey, token, loc.Param.Region, loc.Param.Bucket)
			url, err := cosClient.GetPresignedURL(loc.Param.Path, http.MethodGet, time.Hour)
			if err != nil {
				logger.Errorf("[%s] Failed to GetPresignedUrl,err: %+v", req.RequestId, err)
				return "", "", err
			}
			logger.Infof("[%s] Path %s GetPresignedURL %s", req.RequestId, loc.Param.Path, url)
			_, fileName := filepath.Split(loc.Param.Path)
			sqlGatewayResourceItems = append(sqlGatewayResourceItems, &SqlGatewayResourceItem{
				Url:  url,
				Path: uploadPath + "/" + fileName,
			})
		}
		marshaled, err := json.Marshal(sqlGatewayResourceItems)
		if err != nil {
			logger.Errorf("[%s] Failed to Marshal,err: %+v", req.RequestId, err)
			return "", "", err
		}
		encoded := base64.StdEncoding.EncodeToString(marshaled)
		return uploadPath, encoded, nil
	}
	var properties = "[]"
	if len(req.Properties) > 0 {
		propertiesData, err := json.Marshal(req.Properties)
		if err != nil {
			logger.Errorf("[%s] Failed to marshal properties,error: %+v", req.RequestId, err)
			return rsp, errorcode.FailedOperationCode.NewWithErr(err)
		}
		properties = string(propertiesData)
	}
	//3. Trigger the SqlGateway deployment flow.
	sqlGateway := &table.SqlGateway{
		ClusterId:    cluster.Id,
		FlinkVersion: req.FlinkVersion,
		Status:       constants.SqlGatewayCreating,
		CuSpec:       req.CuSpec,
		Properties:   properties,
		AppId:        req.AppId,
		OwnerUin:     req.Uin,
		CreatorUin:   req.SubAccountUin,
		Region:       req.Region,
		CreateTime:   util.GetCurrentTime(),
		UpdateTime:   util.GetCurrentTime(),
		Cpu:          req.Cpu,
		Mem:          req.Mem,
	}
	regionId, err := service.GetRegionIdByName(req.Region)
	if err != nil {
		logger.Errorf("[%s] Failed to GetRegionIdByName,error: %+v", req.RequestId, err)
		return rsp, errorcode.FailedOperationCode.NewWithErr(err)
	}

	var gatewaySerialId, path, refs string
	var gatewayId int64
	txManager := service.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		if sqlGatewayExists { // already exists,update config an restart
			gatewaySerialId = gateway.SerialId
			gatewayId = gateway.Id
			sqlGateway.SerialId = gateway.SerialId
			tx.UpdateObject(sqlGateway, gateway.Id, "SqlGateway")
			// delete sqlGatewayRefs
			deleteSqlGatewayRefs := "UPDATE SqlGatewayRef SET Status = ? WHERE SqlGatewayId = ? "
			tx.ExecuteSqlWithArgs(deleteSqlGatewayRefs, constants.RESOURCE_REF_STATUS_DELETE, gateway.Id)
		} else {
			id := tx.SaveObject(sqlGateway, "SqlGateway")
			cidUtil := &util.CidUtil{}
			serialId := cidUtil.EncodeId(id, "sgw", "gateway", util.GetNowTimestamp(), 8)
			gatewaySerialId = serialId
			gatewayId = id
			tx.ExecuteSqlWithArgs("UPDATE SqlGateway set SerialId = ?  WHERE Id = ? ", serialId, id)
		}
		if len(gatewayRefs) > 0 {
			path, refs, err = generateSqlGatewayRefs(gatewaySerialId, locs)
			if err != nil {
				logger.Errorf("[%s] Failed to generate SqlGatewayRefs,error: %+v", req.RequestId, err)
				return err
			}
			// save sqlGatewayRefs
			for _, gatewayRef := range gatewayRefs {
				gatewayRef.SqlGatewayId = gatewayId
				tx.SaveObject(gatewayRef, "SqlGatewayRef")
			}
		}

		flowId, err := flow.CreateFlow(constants.FLOW_OCEANUS_CREATE_SQL_GATEWAY,
			fmt.Sprintf("%s@%s", req.ClusterId, req.RequestId), regionId, map[string]string{
				constants.FLOW_PARAM_GATEWAY_FLINK_VERSION: req.FlinkVersion,
				constants.FLOW_PARAM_CLUSTER_GROUP_ID:      fmt.Sprintf("%d", clusterGroup.Id),
				constants.FLOW_PARAM_CLUSTER_ID:            fmt.Sprintf("%d", cluster.Id),
				constants.FLOW_PARAM_GATEWAY_SERIAL_ID:     gatewaySerialId,
				constants.FLOW_PARAM_REQUEST_ID:            req.RequestId,
				constants.FLOW_PARAM_CU_SPEC:               fmt.Sprintf("%f", req.CuSpec),
				constants.FLOW_PARAM_REGION:                req.Region,
				constants.FLOW_PARAM_APPID:                 fmt.Sprintf("%d", req.AppId),
				constants.FLOW_PARAM_REFS:                  refs,
				constants.FLOW_PARAM_REFS_PATH:             path,
				constants.FLOW_PARAM_PROPERTIES:            properties,
				constants.FLOW_PARAM_CPU_SPEC:              fmt.Sprintf("%f", req.Cpu),
				constants.FLOW_PARAM_MEM_SPEC:              fmt.Sprintf("%f", req.Mem),
			}, nil)
		if err != nil {
			logger.Errorf("[%s] Failed to CreateFlow,error: %+v", req.RequestId, err)
			return err
		}
		logger.Infof("[%s] Create flow %s success, flowId %d", req.RequestId, constants.FLOW_OCEANUS_CREATE_SQL_GATEWAY, flowId)
		return nil
	}).Close()
	rsp = &model2.CreateSqlGatewayRsp{
		SqlGatewayId: gatewaySerialId,
	}
	return rsp, nil
}

func (o *SqlGatewayService) StopSqlGateway(req *model2.StopSqlGatewayReq) (rsp *model2.StopSqlGatewayRsp, err error) {
	if !auth.IsInWhiteList(req.AppId, constants.WHITE_LIST_SQL_GATEWAY) {
		logger.Errorf("[%s] AppId %d has no operation permission to create sql gateway.", req.RequestId, req.AppId)
		return rsp, errorcode.FailedOperationCode.ReplaceDesc(fmt.Sprintf("[%s] has no operation permission to create sql gateway.", req.RequestId))
	}
	sqlGatewayExists, gateway, err := GetSqlGatewayBySerialId(req.SqlGatewayId)
	if err != nil {
		logger.Errorf("[%s] Failed to GetSqlGateway,error: %+v", req.RequestId, err)
		return rsp, errorcode.FailedOperationCode.NewWithErr(err)
	}
	if !sqlGatewayExists {
		return rsp, errorcode.InvalidParameterCode.ReplaceDesc(fmt.Sprintf("[%s] SqlGateway %s is not existed.", req.RequestId, req.SqlGatewayId))
	}
	regionId, err := service.GetRegionIdByName(req.Region)
	if err != nil {
		logger.Errorf("[%s] Failed to GetRegionIdByName error: %+v", req.RequestId, err)
		return rsp, errorcode.FailedOperationCode.NewWithErr(err)
	}
	clusterGroup, err := GetClusterGroupByClusterId(gateway.ClusterId)
	if err != nil {
		logger.Errorf("[%s] Failed to GetClusterGroupByClusterId error: %+v", req.RequestId, err)
		return rsp, errorcode.FailedOperationCode.NewWithErr(err)
	}
	if int64(clusterGroup.AppId) != req.AppId || clusterGroup.OwnerUin != req.Uin {
		return nil, errorcode.FailedOperationCode.ReplaceDesc(fmt.Sprintf("The appid %d or uin %s has no operation permission.", req.AppId, req.Uin))
	}
	txManager := service.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		err := SwitchSqlGatewayStatus(gateway.SerialId, constants.SqlGatewayStopping)
		if err != nil {
			logger.Errorf("[%s] Failed to SwitchSqlGatewayStatus error: %+v", req.RequestId, err)
			return err
		}
		if flowId, err := flow.CreateFlow(constants.FLOW_OCEANUS_STOP_SQL_GATEWAY,
			fmt.Sprintf("%s@%s", clusterGroup.SerialId, req.RequestId), regionId, map[string]string{
				constants.FLOW_PARAM_CLUSTER_GROUP_ID:  fmt.Sprintf("%d", clusterGroup.Id),
				constants.FLOW_PARAM_CLUSTER_ID:        fmt.Sprintf("%d", gateway.ClusterId),
				constants.FLOW_PARAM_GATEWAY_SERIAL_ID: gateway.SerialId,
				constants.FLOW_PARAM_REQUEST_ID:        req.RequestId,
				constants.FLOW_PARAM_APPID:             fmt.Sprintf("%d", req.AppId),
			}, nil); err != nil {
			logger.Error("flow.CreateFlow(constants.FLOW_OCEANUS_STOP_SQL_GATEWAY), errors:", err.Error())
			return err
		} else {
			logger.Infof("[%s] Create flow %s success, flowId %d", req.RequestId, constants.FLOW_OCEANUS_STOP_SQL_GATEWAY, flowId)
		}
		return nil
	}).Close()
	rsp = &model2.StopSqlGatewayRsp{}
	return rsp, nil
}

func SwitchSqlGatewayStatus(serialId string, targetStatus int) (err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Update SqlGateway %s failed , errors:%+v", serialId, errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	logger.Infof("Switch SqlGateway %s status to %d", serialId, targetStatus)
	txManager := service.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.ExecuteSqlWithArgs("UPDATE SqlGateway SET `Status` = ?  WHERE SerialId = ? ", targetStatus, serialId)
		return nil
	}).Close()
	return nil
}

func GetSqlGateway(appId int64, clusterId int64, flinkVersion string) (exists bool, sqlGateway *table.SqlGateway, err error) {
	cond := dao.NewCondition()
	cond.Eq("ClusterId", clusterId)
	cond.Eq("FlinkVersion", flinkVersion)
	cond.Eq("AppId", appId)
	where, args := cond.GetWhere()
	sql := " SELECT * FROM SqlGateway  " + where
	count, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if count == 0 {
		return false, nil, err
	}
	sqlGateway = &table.SqlGateway{}
	err = util.ScanMapIntoStruct(sqlGateway, data[0])
	if err != nil {
		logger.Errorf("Failed to GetSqlGateway,cluster %s, errors:%+v", clusterId, err)
		return false, nil, err
	}
	return true, sqlGateway, nil
}

func GetSqlGatewayBySerialId(serialId string) (exists bool, sqlGateway *table.SqlGateway, err error) {
	cond := dao.NewCondition()
	cond.Eq("SerialId", serialId)
	where, args := cond.GetWhere()
	sql := " SELECT * FROM SqlGateway  " + where
	count, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if count == 0 {
		return false, nil, err
	}
	sqlGateway = &table.SqlGateway{}
	err = util.ScanMapIntoStruct(sqlGateway, data[0])
	if err != nil {
		logger.Errorf("Failed to GetSqlGateway,serialId %s, errors:%+v", serialId, err)
		return false, nil, err
	}
	return true, sqlGateway, nil
}

func GetSqlGatewaysByClusterGroupId(clusterGroupId int64) (sqlGateways []*table.SqlGateway, err error) {
	clusters, err := ListClusters(clusterGroupId)
	if err != nil {
		return nil, err
	}
	if len(clusters) == 0 {
		return nil, errors.New(fmt.Sprintf("Not found cluster with clusterGroupId %d", clusterGroupId))
	}
	cluster := clusters[0]
	cond := dao.NewCondition()
	cond.Eq("ClusterId", cluster.Id)
	where, args := cond.GetWhere()
	sql := " SELECT * FROM SqlGateway " + where
	_, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return nil, err
	}
	sqlGateways = make([]*table.SqlGateway, 0)
	for i := 0; i < len(data); i++ {
		sqlGateway := &table.SqlGateway{}
		err = util.ScanMapIntoStruct(sqlGateway, data[i])
		if err != nil {
			return nil, err
		}
		sqlGateways = append(sqlGateways, sqlGateway)
	}
	return sqlGateways, nil
}

// To avoid circular references, it duplicated methods from the resource package.
func GetResourceConfigByResourceRef(v *model5.ResourceRefItem) (*model3.ResourceConfig, error) {
	resourceConfig := &model3.ResourceConfig{}
	args := make([]interface{}, 0, 0)
	sql := ""
	logger.Infof("GetResourceConfigByResourceRef GetResourceConfigByResourceRef ResourceRefItem : %s", v)
	if v.Version == constants.RESOURCE_REF_VERSION_ID_USE_LATEST {
		args = append(args, v.ResourceId)
		args = append(args, v.ResourceId)
		sql = "SELECT a.* FROM ResourceConfig as a, Resource as b WHERE a.ResourceId = b.Id And b.ResourceId=? " +
			" AND VersionId=(SELECT max(a.VersionId) FROM ResourceConfig as a, Resource as b WHERE a.ResourceId = b.Id AND b.ResourceId=?)"
	} else {
		args = append(args, v.ResourceId)
		args = append(args, v.Version)
		sql = "SELECT a.* FROM ResourceConfig as a, Resource as b WHERE a.ResourceId = b.Id AND b.ResourceId=? AND a.VersionId=?"
	}
	cnt, resourceConfigData, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("BuildLocalizationContexts -> DoQuery sql:%s args:%+v error:%+v", sql, args, err)
		return nil, err
	}
	if cnt != 1 {
		err = fmt.Errorf("BuildLocalizationContexts -> DoQuery sql:%s args:%+v resource config len(%d) != 1",
			sql, args, cnt)
		return nil, err
	}
	err = util.ScanMapIntoStruct(resourceConfig, resourceConfigData[0])
	if err != nil {
		logger.Errorf("BuildLocalizationContexts -> ScanMapIntoStruct error:%+v", err)
		return nil, err
	}
	return resourceConfig, nil
}

func GetSqlGatewayItemsByClusterId(clusterId int64, memRatio int8) (sqlGatewayItems []*model2.SqlGatewayItem, err error) {
	cond := dao.NewCondition()
	cond.Eq("ClusterId", clusterId)
	where, args := cond.GetWhere()
	sql := " SELECT * FROM SqlGateway " + where
	_, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return nil, err
	}
	sqlGatewayItems = make([]*model2.SqlGatewayItem, 0)
	for i := 0; i < len(data); i++ {
		sqlGateway := &table.SqlGateway{}
		err = util.ScanMapIntoStruct(sqlGateway, data[i])
		if err != nil {
			return nil, err
		}
		refItems, err := GetGatewayRefItems(sqlGateway.Id)
		if err != nil {
			return nil, err
		}
		properties := make([]*model.Property, 0)
		if sqlGateway.Properties != "" {
			err = json.Unmarshal([]byte(sqlGateway.Properties), &properties)
			if err != nil {
				return nil, err
			}
		}
		sqlGatewayItem := &model2.SqlGatewayItem{
			SerialId:     sqlGateway.SerialId,
			FlinkVersion: sqlGateway.FlinkVersion,
			Status:       sqlGateway.Status,
			CuSpec:       sqlGateway.CuSpec,
			CreatorUin:   sqlGateway.CreatorUin,
			CreateTime:   sqlGateway.CreateTime,
			UpdateTime:   sqlGateway.UpdateTime,
			ResourceRefs: refItems,
			Properties:   properties,
		}

		if sqlGateway.Cpu != 0 {
			sqlGatewayItem.Cpu = sqlGateway.Cpu
			sqlGatewayItem.Mem = sqlGateway.Mem
		} else {
			sqlGatewayItem.Cpu = sqlGateway.CuSpec
			sqlGatewayItem.Mem = sqlGateway.CuSpec * float32(memRatio)
		}

		sqlGatewayItems = append(sqlGatewayItems, sqlGatewayItem)
	}
	return sqlGatewayItems, nil
}

func GetGatewayRefItems(sqlGatewayId int64) (gatewayRefItems []*model2.GatewayRefItem, err error) {

	cond := dao.NewCondition()
	cond.Eq("a.SqlGatewayId", sqlGatewayId)
	cond.Eq("a.Status", constants.RESOURCE_STATUS_ACTIVE)
	where, args := cond.GetWhere()
	sql := " SELECT b.ResourceId,a.VersionId AS Version,a.Type,c.SerialId AS WorkspaceId FROM SqlGatewayRef a " +
		"JOIN Resource b ON a.ResourceId = b.Id JOIN ItemSpace c ON b.ItemSpaceId = c.Id " + where
	_, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return nil, err
	}
	gatewayRefItems = make([]*model2.GatewayRefItem, 0)
	for i := 0; i < len(data); i++ {
		gatewayRefItem := &model2.GatewayRefItem{}
		err = util.ScanMapIntoStruct(gatewayRefItem, data[i])
		if err != nil {
			return nil, err
		}
		gatewayRefItems = append(gatewayRefItems, gatewayRefItem)
	}
	return gatewayRefItems, nil
}
