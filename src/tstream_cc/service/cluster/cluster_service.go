package service

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	errors2 "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/dlocker"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/common/uuid"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	model1 "tencentcloud.com/tstream_galileo/src/tstream_cc/model"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/billing"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/cluster"
	model2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_config"
	table4 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/bucket"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	table3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	table7 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/tke"
	service6 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/bucket"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/configuration_center"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
	service5 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
)

/**
 *
 * <AUTHOR>
 * @time 2019/4/28
 * @version 0.4.0
 * @since 0.4.0
 * @see
 */
// FIXME: 功能与 region_service 的 GetRegionClusterCount 基本重复
func GetClusterGroupTotalCount(listClusterGroupsParam *service6.ListClusterGroupsParam) (int, error) {
	sql, args := service6.GetListClusterGroupsSql(listClusterGroupsParam)
	txManager := service.GetTxManager()
	count, _, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return 0, err
	}

	return count, nil
}

func IsSharedClusterGroup(clusterGroupId int64) (bool, error) {
	clusterGroup, err := ListClusterGroupById(clusterGroupId)
	if err != nil {
		logger.Errorf("IsSharedClusterGroup -> ListClusterGroupById(%d) error: %+v", clusterGroupId, err)
		return false, err
	}
	if clusterGroup.Type == constants.CLUSTER_GROUP_TYPE_SHARED {
		return true, nil
	} else {
		return false, nil
	}
}

func listClusterGroupBySql(sql string, args interface{}) (t *table.ClusterGroup, err error) {
	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQueryWithArgs(sql, args)
	if err != nil {
		return nil, err
	} else if len(data) != 1 {
		return nil, errorcode.ResourceNotFound_Cluster.New()
	}

	clusterGroup := &table.ClusterGroup{}
	err = util.ScanMapIntoStruct(clusterGroup, data[0])
	return clusterGroup, errorcode.InternalErrorCode.NewWithErr(err)
}

func ListClusterGroupByVpcId(vpcId string) (t *table.ClusterGroup, err error) {
	return listClusterGroupBySql("select cg.* from ClusterGroup cg left join Cluster c on cg.Id = c.ClusterGroupId where c.VpcId = ?", vpcId)
}

func ListClusterGroupByCdbId(cdbId string) (t *table.ClusterGroup, err error) {
	return listClusterGroupBySql("select cg.* from ClusterGroup cg left join Cluster c on cg.Id = c.ClusterGroupId left join Cdb d on d.ClusterId = c.Id where d.InstanceId = ?", cdbId)
}

func ListClusterGroupBySerialId(serialId string) (t *table.ClusterGroup, err error) {
	return listClusterGroupBySql("SELECT * FROM ClusterGroup WHERE SerialId=?", serialId)
}

func ListClusterGroupByParentSerialId(serialId string) (count int, t *table.ClusterGroup, err error) {
	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQueryWithArgs("SELECT * FROM ClusterGroup WHERE ParentSerialId=? and Status = 2", serialId)
	if err != nil {
		return 0, nil, err
	} else if len(data) == 1 {
		clusterGroup := &table.ClusterGroup{}
		err = util.ScanMapIntoStruct(clusterGroup, data[0])
		if err != nil {
			return 0, nil, err
		}
		return 1, clusterGroup, nil
	} else if len(data) == 0 {
		return 0, nil, nil
	} else {
		return len(data), nil, errorcode.FailedOperationCode.NewWithMsg(fmt.Sprintf("cluster %s exists more than 1 sub eks cluster", serialId))
	}
}

func ListClusterGroupByAgentSerialId(serialId string) (clusterGroupList []*table.ClusterGroup, err error) {
	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQueryWithArgs("SELECT * FROM ClusterGroup WHERE AgentSerialId=? and Status = 2", serialId)
	if err != nil {
		return nil, err
	}
	clusterGroupList = make([]*table.ClusterGroup, 0)
	for i := 0; i < len(data); i++ {
		c := &table.ClusterGroup{}
		err = util.ScanMapIntoStruct(c, data[i])
		if err != nil {
			return nil, errorcode.InternalErrorCode_UnMarshalFailed.NewWithErr(err)
		}
		clusterGroupList = append(clusterGroupList, c)
	}
	return clusterGroupList, nil
}

func ListClusterGroupBySerialIdV2(serialId string, appId int) (t *table.ClusterGroup, err error) {
	clusterGroup := &table.ClusterGroup{}
	args := make([]interface{}, 0)
	args = append(args, serialId)
	args = append(args, appId)
	sql := "SELECT * FROM ClusterGroup WHERE SerialId=? and AppId=?"
	logger.Infof("sql[%v], args :[%v]", sql, args)
	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to query Clusters from db, with errors:%+v", err)
		return clusterGroup, errorcode.NewStackError(errorcode.InternalErrorCode_DoSqlFailed, "cannot get cluster from DB", err)
	}
	if len(data) != 1 {
		err = fmt.Errorf("GetClusterByClusterId error, active cluster size is NOT 1 but len(%d)", len(data))
		return clusterGroup, errorcode.NewStackError(errorcode.ResourceNotFoundCode, "cluster size is not 1", err)
	}
	err = util.ScanMapIntoStruct(clusterGroup, data[0])
	if err != nil {
		logger.Errorf("data format err :[%v]", err)
	}
	return clusterGroup, nil
}

func ListClusterGroupById(clusterGroupId int64) (t *table.ClusterGroup, err error) {
	return listClusterGroupBySql("SELECT * FROM ClusterGroup WHERE Id=?", clusterGroupId)
}

func GetClusterByClusterId(clusterId int64) (cluster *table.Cluster, err error) {
	sql := "SELECT * FROM Cluster WHERE Id=?"
	args := make([]interface{}, 0)
	args = append(args, clusterId)

	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to query Clusters from db, with errors:%+v", err)
		return cluster, err
	}

	if len(data) != 1 {
		err = fmt.Errorf("GetClusterByClusterId error, cluster size is len(%d)", len(data))
		return cluster, err
	}

	cluster = &table.Cluster{}
	err = util.ScanMapIntoStruct(cluster, data[0])
	if err != nil {
		logger.Errorf("Failed to get ScanMapIntoStruct, with errors:%+v", err)
		return nil, err
	}
	return
}

func GetClusterByClusterGroupId(clusterGroupId int64) (cluster *table.Cluster, err error) {
	sql := "SELECT * FROM Cluster WHERE ClusterGroupId=?"
	args := make([]interface{}, 0)
	args = append(args, clusterGroupId)

	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to query Clusters by ClusterGroupId[%d] from db, error:%+v", clusterGroupId, err)
		return cluster, err
	}

	if len(data) != 1 {
		err = fmt.Errorf("GetClusterByClusterGroupId error, cluster size is len(%d)", len(data))
		return cluster, err
	}
	cluster = &table.Cluster{}
	err = util.ScanMapIntoStruct(cluster, data[0])
	if err != nil {
		logger.Errorf("Failed to get ScanMapIntoStruct GetClusterByClusterGroupId, with errors:%+v", err)
		return nil, err
	}
	return
}

func GetClusterByJobId(jobId string) (cluster *table.Cluster, err error) {
	sql := "SELECT * FROM Cluster WHERE Id=(SELECT ClusterId FROM Job WHERE SerialId=?)"
	args := make([]interface{}, 0)
	args = append(args, jobId)

	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to query Clusters from db, with errors:%+v", err)
		return cluster, err
	}

	if len(data) != 1 {
		err = fmt.Errorf("GetClusterByClusterId error, cluster size is len(%d)", len(data))
		return cluster, err
	}

	cluster = &table.Cluster{}
	err = util.ScanMapIntoStruct(cluster, data[0])
	if err != nil {
		logger.Errorf("Failed to get ScanMapIntoStruct, with errors:%+v", err)
		return nil, err
	}
	return
}

func GetClusterGroupByJobId(jobId string) (clusterGroup *table.ClusterGroup, err error) {
	sql := "SELECT * FROM ClusterGroup WHERE Id=(SELECT ClusterGroupId FROM Job WHERE SerialId=?)"
	args := make([]interface{}, 0)
	args = append(args, jobId)

	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to query Clusters from db, with errors:%+v", err)
		return clusterGroup, err
	}

	if len(data) != 1 {
		err = fmt.Errorf("GetClusterGroupByJobId error, cluster size is len(%d)", len(data))
		return clusterGroup, err
	}

	clusterGroup = &table.ClusterGroup{}
	err = util.ScanMapIntoStruct(clusterGroup, data[0])
	if err != nil {
		logger.Errorf("Failed to get ScanMapIntoStruct, with errors:%+v", err)
		return nil, err
	}
	return
}

func GetActiveClusterByClusterGroupId(clusterGroupId int64) (cluster *table.Cluster, err error) {
	sql := "SELECT * FROM Cluster WHERE ClusterGroupId=? AND RoleType=?"
	args := make([]interface{}, 0)
	args = append(args, clusterGroupId)
	args = append(args, constants.CLUSTER_ROLE_TYPE_ACTIVE)

	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to query Clusters from db, with errors:%+v", err)
		return cluster, errorcode.NewStackError(errorcode.InternalErrorCode_DoSqlFailed, "cannot get cluster from DB", err)
	}

	if len(data) != 1 {
		err = fmt.Errorf("GetClusterByClusterId error, active cluster size is NOT 1 but len(%d)", len(data))
		return cluster, errorcode.NewStackError(errorcode.ResourceNotFoundCode, "cluster size is not 1", err)
	}

	cluster = &table.Cluster{}
	err = util.ScanMapIntoStruct(cluster, data[0])
	if err != nil {
		logger.Errorf("Failed to get ScanMapIntoStruct, with errors:%+v", err)
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode_UnMarshalFailed, "cannot ScanMapIntoStruct", err)
	}
	return
}

func ListClusters(clusterGroupId int64) (t []*table.Cluster, err error) {
	sql := "SELECT * FROM Cluster WHERE clusterGroupId=?"
	args := make([]interface{}, 0)
	args = append(args, clusterGroupId)

	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return nil, err
	}
	clusters := make([]*table.Cluster, 0)
	for i := 0; i < len(data); i++ {
		cluster := &table.Cluster{}
		err = util.ScanMapIntoStruct(cluster, data[i])
		if err != nil {
			return nil, errorcode.InternalErrorCode.NewWithErr(err)
		}
		clusters = append(clusters, cluster)
	}

	return clusters, nil
}

func ClusterIsEnableTKE(clusterId int64) (enable bool, err error) {
	cluster, err := GetClusterByClusterId(clusterId)
	if err != nil {
		logger.Errorf("Failed to GetClusterByClusterId %d, with errors:%+v", clusterId, err)
		return enable, err
	}

	if cluster.SchedulerType == constants.CLUSTER_SCHEDULER_TYPE_TKE {
		enable = true
	}
	return
}

func ListClusterBySchedulerType(clusterGroupId int64, schedulerType int8) (t []*table.Cluster, err error) {

	clusterList, err := ListClusters(clusterGroupId)
	if err != nil {
		logger.Errorf("Failed to query Clusters from db, with errors:%+v", err)
		return nil, err
	}

	t = make([]*table.Cluster, 0)
	for _, c := range clusterList {
		if c.SchedulerType == schedulerType {
			t = append(t, c)
		}
	}
	return
}

func CreateCluster(requestId string, req *model.CreateClusterReq) (clusterGroup *table.ClusterGroup, cluster *table.Cluster, flowId int64, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("[%s] Add ClusterGroup/Cluster to db panic, errors:%+v, stack: %s", requestId, errs, util.Gostack())
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()

	txManager := service.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		params := make(map[string]string)
		params[constants.FLOW_PARAM_REQUEST_ID] = requestId

		clusterType, archGeneration := int(req.ClusterType), req.ArchGeneration
		clusterGroupId, clusterId := int64(0), int64(0)
		serialId := ""

		// 统一资源池母集群，开区
		if req.ClusterGroupType == constants.CLUSTER_GROUP_TYPE_UNIFORM && req.AgentSerialId != "" {
			cgs, err := NewClusterGroupServiceBySerialId(req.AgentSerialId)
			if err != nil {
				return err
			}
			clusterGroup = cgs.clusterGroup
			clusterGroupId = cgs.clusterGroup.Id
			serialId = cgs.clusterGroup.SerialId
			c, err := cgs.GetActiveCluster()
			if err != nil {
				return err
			}
			cluster = c
			clusterId = c.Id
			tke, err := cgs.GetTkeList()
			if err != nil {
				return err
			}
			if len(tke) != 1 {
				msg := fmt.Sprintf("CreateCluster tke lenght is not 1, but %d", len(tke))
				return errors.New(msg)
			}
			clusterType = tke[0].ClusterType
			archGeneration = tke[0].ArchGeneration

			params[constants.FLOW_PARAM_ZONE] = req.Zone
			// TODO 集群创建成功之后，再设置支持某个区域
		} else {
			clusterGroup, _ = BuildClusterGroupEntityFromReq(req)
			cluster, _ = BuildClusterEntityFromReq(req)
			peerVpcs, _ := BuildClusterGroupPeerVpcsFromReq(req)
			flinkUiAuthInfo, _ := BuildFlinkUiAuthInfo(req)
			// 创建统一集群的母集群
			if req.ClusterGroupType == constants.CLUSTER_GROUP_TYPE_UNIFORM && req.AgentSerialId == "" {
				clusterGroup.Type = constants.CLUSTER_GROUP_TYPE_UNIFORM
				// 母集群权限控制跟目录
				clusterGroup.ZkRootPath = "/"
				clusterGroup.ZkUser = uuid.New()
				zkPass, _ := util.AesEncrypt([]byte(uuid.New()), constants.AES_ENCRYPT_KEY)
				clusterGroup.ZkPass = zkPass
			}

			//混合计费
			if req.ClusterGroupType == constants.CLUSTER_GROUP_TYPE_SUB_EKS {
				clusterGroup.Type = constants.CLUSTER_GROUP_TYPE_SUB_EKS
			}

			// 创建独享集群
			if req.ClusterGroupType != constants.CLUSTER_GROUP_TYPE_UNIFORM && req.AgentSerialId == "" {

			}

			// 创建共享集群
			if req.ClusterGroupType != constants.CLUSTER_GROUP_TYPE_UNIFORM && req.AgentSerialId != "" {
				clusterGroup.AgentSerialId = req.AgentSerialId
				// zk acl
				clusterGroup.ZkRootPath = uuid.New()
				clusterGroup.ZkUser = uuid.New()
				zkPass, _ := util.AesEncrypt([]byte(uuid.New()), constants.AES_ENCRYPT_KEY)
				clusterGroup.ZkPass = zkPass
			}
			if req.ParentSerialId != "" {
				clusterGroup.ParentSerialId = req.ParentSerialId
			}

			var clusterGroupNameIsNull = false // 【点石需求】无需设置集群名称，自动将集群名称设为与集群ID相同
			if clusterGroup.Name == "" {
				clusterGroup.Name = "OceanusCluster" // db中Name字段不能为空，设置临时名称
				clusterGroupNameIsNull = true
			}
			deploymentMode := constants.DeploymentModeDefault
			if len(req.SlaveVpcDescriptions) > 0 {
				deploymentMode = constants.DeploymentModeMultiple
				for _, slaveVpcDescription := range req.SlaveVpcDescriptions {
					if err = clusterGroup.AddSupportedZone(slaveVpcDescription.Zone); err != nil {
						logger.Errorf("[%s] CreateCluster -> AddSupportedZone error: %+v", requestId, err)
						return err
					}
					if err = cluster.AddSupportedZoneSubnets(slaveVpcDescription.Zone, slaveVpcDescription.SubnetId); err != nil {
						logger.Errorf("[%s] CreateCluster -> AddSupportedZoneSubnets error: %+v", requestId, err)
						return err
					}
				}
			}
			clusterGroup.DeploymentMode = deploymentMode
			clusterGroupId = tx.SaveObject(clusterGroup, "ClusterGroup")
			clusterGroup.Id = clusterGroupId
			cluster.ClusterGroupId = clusterGroupId
			// 如果是内网环境则直接将peerVpc的网络信息作为TKE集群的网络信息，不需要额外走购买流程去购买VPC
			if req.NetEnvironmentType == constants.NETWORK_ENV_INNER_VPC && len(peerVpcs) == 1 {
				cluster.VpcId = peerVpcs[0].VpcId
				cluster.SubnetId = peerVpcs[0].SubnetId
			}
			clusterId = tx.SaveObject(cluster, "Cluster")
			cluster.Id = clusterId

			// add ClusterVersion
			clusterVersion, _ := BuildClusterVersion(clusterId, clusterGroup.Region)
			tx.SaveObject(clusterVersion, "ClusterVersion")

			for _, peerVpc := range peerVpcs {
				peerVpc.ClusterGroupId = clusterGroupId
				tx.SaveObject(peerVpc, "ClusterGroupPeerVpc")
			}
			// 如果开启cos，创建对应的集群信息
			err = service3.AssignClusterBucketV2(clusterGroup, cluster)
			if err != nil {
				logger.Errorf("[%s] CreateCluster -> AssignClusterBucketV2 error: %+v", requestId, err)
				return err
			}
			cidUtil := &util.CidUtil{}
			serialId = cidUtil.EncodeId(clusterGroupId, "cluster", "cluster", util.GetNowTimestamp(), 8)
			clusterGroup.SerialId = serialId
			if clusterGroupNameIsNull { // 设置ClusterGroupName为serialId
				tx.ExecuteSqlWithArgs("UPDATE ClusterGroup SET serialId = ?, name = ?  WHERE id = ?", serialId, serialId, clusterGroupId)
			} else {
				tx.ExecuteSqlWithArgs("UPDATE ClusterGroup SET serialId = ? WHERE id = ?", serialId, clusterGroupId)
			}
			// 存flink ui密码，这里不区分on yarn, on tke了，都存起来
			if flinkUiAuthInfo != nil {
				flinkUiAuthInfo.ClusterGroupId = clusterGroup.Id
				flinkUiAuthInfo.Id = tx.SaveObject(flinkUiAuthInfo, "FlinkUiAuthInfo")
			}
		}

		params[constants.FLOW_PARAM_CLUSTER_GROUP_ID] = fmt.Sprintf("%d", clusterGroupId)
		params[constants.FLOW_PARAM_CLUSTER_ID] = fmt.Sprintf("%d", clusterId)
		params[constants.FLOW_PARAM_CLUSTER_TYPE] = fmt.Sprintf("%d", clusterType)
		params[constants.FLOW_PARAM_ARCH_GNERATION] = fmt.Sprintf("%d", archGeneration)
		bt, _ := json.Marshal(req.Tags)
		params[constants.FLOW_PARAM_TAGS] = base64.StdEncoding.EncodeToString(bt)

		bp, _ := json.Marshal(params)
		clusterGroup.CreateParam = string(bp)
		tx.ExecuteSqlWithArgs("UPDATE ClusterGroup SET CreateParam = ? WHERE id = ?", clusterGroup.CreateParam, clusterGroup.Id)

		// 发起流程
		if blackRegion, err := service5.GetTableService().InClusterAutoCreateBlackRegionList(req.Region); err != nil {
			logger.Errorf("[%s] CreateCluster -> InClusterAutoCreateBlackRegionList error: %+v", requestId, err)
			return err
		} else if service6.IsTceEnv() {
			logger.Errorf("[%s] CreateCluster -> Skip auto create cluster")
		} else if !blackRegion {
			// 内网账号也支持自动化部署了
			regionId, err := service.GetRegionIdByName(req.Region)
			if err != nil {
				logger.Errorf("[%s] GetRegionIdByName error: %+v", requestId, err)
				return err
			}

			// 目前只有 VPC 网络(包括内网)环境可以自动化部署
			if flowId, err = flow.CreateFlow(constants.FLOW_OCEANUS_CREATE_CLUSTER,
				fmt.Sprintf("%s@%s", serialId, requestId), regionId, params, nil); err != nil {
				logger.Errorf("[%s] createFlow %s fail %v", requestId, constants.FLOW_OCEANUS_CREATE_CLUSTER, err)
				return err
			} else {
				logger.Infof("[%s] createFlow %s success, flowId %d", requestId, constants.FLOW_OCEANUS_CREATE_CLUSTER, flowId)
			}
		} else { // 账号在自动部署黑名单(自研上云), 或者网络环境非 VPC
			logger.Infof("[%s] in blackRegion(%v) or not in VPC environment, cluster create flow should be handled manually", requestId, clusterGroup.AppId, blackRegion)
		}
		return nil
	}).Close()

	return clusterGroup, cluster, flowId, nil
}

func CheckClusterExist(clusterId int64, creatorUin string) (exist bool, err error) {
	sql := "SELECT * FROM Cluster WHERE Id=? AND CreatorUin=?"
	args := make([]interface{}, 0)
	args = append(args, clusterId)
	args = append(args, creatorUin)

	cnt, _, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("CheckClusterExist sql:%s, args:%+v error:%+v", sql, args, err)
		return exist, err
	}
	if cnt > 0 {
		exist = true
		return exist, err
	}
	logger.Infof("CheckClusterExist clusterId %d false", clusterId)
	return exist, err
}

func ModifyClusterRoleType(clusterId int64, creatorUin string, roleType int) (err error) {
	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		args := make([]interface{}, 0)
		args = append(args, roleType)
		args = append(args, clusterId)
		args = append(args, creatorUin)
		tx.ExecuteSql("UPDATE Cluster SET RoleType=? WHERE Id=? AND CreatorUin=?", args)
		return nil
	}).Close()
	return err
}

func ModifyClusterDefaultLogCollectConf(clusterId int64, defaultLogCollectConf string) (err error) {
	args := make([]interface{}, 0)
	args = append(args, defaultLogCollectConf)
	args = append(args, clusterId)
	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.ExecuteSql("UPDATE Cluster SET DefaultLogCollectConf=? WHERE Id=? limit 1", args)
		return nil
	}).Close()
	logger.Infof("modify sql [%s], args [%v]", "UPDATE Cluster SET DefaultLogCollectConf=? WHERE Id=? limit 1", args)
	return err
}

func DeleteClusterGroup(ClusterGroupId int64) (err error) {
	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		// 删除ClusterGroup表的数据
		args := make([]interface{}, 0)
		args = append(args, constants.CLUSTER_GROUP_STATUS_DELETED)
		args = append(args, ClusterGroupId)
		tx.ExecuteSql("UPDATE ClusterGroup SET Status=? WHERE Id=?", args)

		// 删除Cluster表的数据
		args = make([]interface{}, 0)
		args = append(args, constants.CLUSTER_ROLE_TYPE_DECOMMISSIONED)
		args = append(args, ClusterGroupId)
		tx.ExecuteSql("UPDATE Cluster SET RoleType=? WHERE ClusterGroupId=?", args)

		// 删除ClusterVersion表的数据
		clusterList, err := ListClusters(ClusterGroupId)
		if err != nil {
			logger.Errorf("DeleteClusterGroup -> ListClusters error: %+v", err)
			return err
		}
		for _, cluster := range clusterList {
			args = make([]interface{}, 0)
			args = append(args, cluster.Id)
			tx.ExecuteSql("Delete from ClusterVersion WHERE ClusterId=?", args)
		}

		args = make([]interface{}, 0)
		args = append(args, ClusterGroupId)
		args = append(args, constants.BUCKET_REF_STATUS_ACTIVE)

		sql := "SELECT * FROM BucketClusterRef WHERE ClusterGroupId=? AND Status=?"
		cnt, data, err := tx.Query(sql, args)
		if err != nil {
			logger.Errorf("DeleteClusterGroup -> Query sql: %s args: %+v error: %+v", sql, args, err)
			return err
		}

		if cnt > 0 {
			// 删除BucketClusterRef表的数据
			args = make([]interface{}, 0)
			args = append(args, constants.BUCKET_REF_STATUS_DELETE)
			args = append(args, ClusterGroupId)
			tx.ExecuteSql("UPDATE BucketClusterRef SET Status=? WHERE ClusterGroupId=?", args)

			for _, v := range data {
				bcf := &table2.BucketClusterRef{}
				err = util.ScanMapIntoStruct(bcf, v)
				if err != nil {
					logger.Errorf("ScanMapIntoStruct error: %+v", err)
					return err
				}
				_ = service3.DecBucketCurNum(tx, bcf.BucketName)
			}
		}
		// todo 删除该集群组上的作业

		return err
	}).Close()
	return err
}

func AddCluster2ClusterGroup(cluster *table.Cluster) (clusterId int64, err error) {
	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		clusterId = tx.SaveObject(cluster, "Cluster")
		return nil
	}).Close()
	return clusterId, err
}

func GetClusterGroupByClusterId(clusterId int64) (clusterGroup *table.ClusterGroup, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Get cluster group panic, with errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	sql := "select a.* from ClusterGroup as a, Cluster as b where a.id = b.clusterGroupId and b.id=?"
	args := make([]interface{}, 0)
	args = append(args, clusterId)
	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to execute the sql:%s, with args:%+v, with errors:%+v", sql, args, err)
		return &table.ClusterGroup{}, err
	}
	if len(data) == 0 {
		logger.Errorf("Failed to query the cluster group, because the cluster group size is 0")
		return &table.ClusterGroup{}, errors.New("cluster group query size is 0")
	}
	clusterGroup = &table.ClusterGroup{}
	err = util.ScanMapIntoStruct(clusterGroup, data[0])
	if err != nil {
		logger.Errorf("Failed to convert bytes into cluster group, with errors:%+v", err)
		return &table.ClusterGroup{}, err
	}
	return clusterGroup, nil
}

func GetClusterGroupBySerialId(serialId string) (clusterGroup *table.ClusterGroup, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Get cluster group panic, with errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	sql := "select * from ClusterGroup where SerialId=?"
	args := make([]interface{}, 0)
	args = append(args, serialId)
	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to execute the sql:%s, with args:%+v, with errors:%+v", sql, args, err)
		return &table.ClusterGroup{}, err
	}
	if len(data) == 0 {
		logger.Errorf("Failed to query the cluster group, because the cluster group size is 0")
		return &table.ClusterGroup{}, errors.New("cluster group query size is 0")
	}
	clusterGroup = &table.ClusterGroup{}
	err = util.ScanMapIntoStruct(clusterGroup, data[0])
	if err != nil {
		logger.Errorf("Failed to convert bytes into cluster group, with errors:%+v", err)
		return &table.ClusterGroup{}, err
	}
	return clusterGroup, nil
}

func SelectActiveCluster(creatorUin string, clusterGroupId int64, clusterId int64, jobType int8) (newCluster *table3.Cluster, err error) {

	// 如果是共享集群，获取的是共享集群的UIN，如果是独享集群。获取的是用户的子账户UIN
	if jobType == constants.JOB_TYPE_SQL || jobType == constants.JOB_TYPE_ETL {
		creatorUin, err = service2.GetScsUserUin()
		if err != nil {
			logger.Errorf("SelectActiveCluster -> can not GetScsUserUin error: %+v", err)
			return nil, err
		}
	}

	// sql := "SELECT * FROM Cluster WHERE CreatorUin=? AND RoleType=? "
	sql := "SELECT * FROM Cluster WHERE RoleType=? "
	args := make([]interface{}, 0)
	// args = append(args, creatorUin)
	args = append(args, constants.CLUSTER_ROLE_TYPE_ACTIVE)

	if clusterGroupId > 0 {
		sql = sql + " AND ClusterGroupId=? "
		args = append(args, clusterGroupId)
	}
	_, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("SelectActiveCluster sql:%s args:%+v error: %+v", sql, args, err)
		return
	}

	sql = sql + " ORDER BY Id ASC"

	clusters := make([]*table3.Cluster, 0, 0)
	for _, v := range data {
		cluster := &table3.Cluster{}
		util.ScanMapIntoStruct(cluster, v)
		// 如果原来的集群还是active，返回原来集群
		if cluster.Id == clusterId {
			newCluster = cluster
			return
		}
		clusters = append(clusters, cluster)
	}

	// 目前选多个集群的第一个，以后再增集群的策略
	// 以后增加负载路由？
	if len(clusters) > 0 {
		newCluster = clusters[0]
		return
	} else {
		logger.Errorf("SelectActiveCluster can not found active cluster clusterGroupId %d clusterId %d",
			clusterGroupId, clusterId)
		logger.Errorf("SelectActiveCluster sql:%s args:%+v", sql, args)
		err = fmt.Errorf("can not found active cluster")
		return
	}
}

func GetClusterCountByCondition(region string, clusterId int64, status int, keyword string) (count int, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Get cluster count panic, with errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	sql := "select count(*) as result from Cluster as a, ClusterGroup as b where a.clusterGroupId = b.id and b.region= ? "
	args := make([]interface{}, 0)
	args = append(args, region)
	if clusterId != 0 {
		sql = sql + " and a.id = ? "
		args = append(args, clusterId)
	}
	if status != -1 {
		sql = sql + " and b.status = ? "
		args = append(args, status)
	}
	if keyword != "" {
		keyword = "%" + keyword + "%"
		sql = sql + " and b.name like ? "
		args = append(args, keyword)
	}
	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to execute the sql:%s, with args:%+v, with errors:%+v", sql, args, err)
		return -1, err
	}
	if len(data) == 0 {
		logger.Errorf("Failed to query the cluster count, because query size is 0")
		return -1, errors.New("cluster count query size is 0")
	}
	count, err = strconv.Atoi(string(data[0]["result"]))
	if err != nil {
		logger.Errorf("Failed to convert bytes into count, with errors:%+v", err)
		return -1, err
	}
	return count, nil

}

func SearchClusterInfos(region string, clusterId int64, status int, pageNo int, pageSize int, keyword string) (clusterInfos []*table.Cluster, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Get cluster infos panic, with errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	sql := "select a.* from Cluster as a, ClusterGroup as b where a.clusterGroupId = b.id and b.region = ? "
	args := make([]interface{}, 0)
	args = append(args, region)
	if clusterId != 0 {
		sql = sql + " and a.id = ? "
		args = append(args, clusterId)
	}
	if status != -1 {
		sql = sql + " and b.status = ? "
		args = append(args, status)
	}
	if keyword != "" {
		keyword = "%" + keyword + "%"
		sql = sql + " and (b.name like ? or a.id like ?) "
		args = append(args, keyword)
		args = append(args, keyword)
	}
	sql = sql + " limit ?,? "
	startIndx := (pageNo - 1) * pageSize
	args = append(args, startIndx)
	args = append(args, pageSize)
	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to execute the sql:%s, with args:%+v, with errors:%+v", sql, args, err)
		return []*table.Cluster{}, err
	}
	clusterInfos = make([]*table.Cluster, 0)
	for i := 0; i < len(data); i++ {
		clusterInfo := &table.Cluster{}
		err = util.ScanMapIntoStruct(clusterInfo, data[i])
		if err != nil {
			logger.Errorf("Failed to convert bytes into cluster, with errors:%+v", err)
			return []*table.Cluster{}, err
		}
		clusterInfos = append(clusterInfos, clusterInfo)
	}
	return clusterInfos, nil
}

func GetClusterId(regionId int, shareMode int) (clusterId int, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Get cluster id panic, with errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	txManager := service.GetTxManager()
	sql := "select a.id from Cluster as a, ClusterGroup as b where a.clusterGroupId = b.id "
	args := make([]interface{}, 0)
	if regionId != 0 {
		args = append(args, regionId)
		sql = sql + " and regionId = ? "
	}
	if shareMode != 0 {
		args = append(args, shareMode)
		sql = sql + " and type = ? "
	}
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to execute the sql: %s, with args:%+v, with errors;%+v", sql, args, err)
		return -1, err
	}
	if len(data) == 0 {
		logger.Errorf("Failed to get cluster id, because the query size is 0")
		return -1, errors.New("cluster id query size is 0")
	}
	clusterId, err = strconv.Atoi(string(data[0]["id"]))
	if err != nil {
		logger.Errorf("Failed to convert bytes into string, with errors:%+v", err)
		return -1, err
	}
	return clusterId, nil

}

func SwitchClusterGroupStatusTo(clusterGroupId int64, status int) (err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Get cluster id panic, with errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	txManager := service.GetTxManager()
	sql := "UPDATE ClusterGroup SET Status = ? WHERE Id = ?"
	args := make([]interface{}, 0)
	args = append(args, status)
	args = append(args, clusterGroupId)

	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		rs := tx.ExecuteSql(sql, args)
		_, err := rs.RowsAffected()
		if err != nil {
			logger.Errorf("Failed to update ClusterGroup status to RUNNING, with sql: %s, with args:%+v", sql, args)
			return err
		}
		return nil
	}).Close()

	return err
}

/**
 * 集群创建完成，更新 创建时间，更准确的记录集群的开始产生费用的时间
 */
func UpdateStartTime(clusterGroupId int64) error {
	sql := "UPDATE ClusterGroup SET StartTime = ? WHERE Id = ?"
	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.ExecuteSqlWithArgs(sql, util.GetCurrentTime(), clusterGroupId)
		return nil
	}).Close()
	return nil
}

func LogConfigCrdExists(cluster *table.Cluster) (exist bool, err error) {
	// on tke的才可能存在crd
	if cluster.SchedulerType != constants.CLUSTER_SCHEDULER_TYPE_TKE {
		return false, nil
	}

	crdConf, err := service5.GetTableService().GetLogConfigCrdConf()
	if err != nil {
		return false, err
	}

	lcClient, err := k8s.GetK8sService().NewLogConfigClient([]byte(cluster.KubeConfig))
	if err != nil {
		return false, errorcode.InternalErrorCode.NewWithInfo(
			fmt.Sprintf("clusterId:%d, tke:%s", cluster.Id, cluster.UniqClusterId), err)
	}
	lcConfig := lcClient.ClsV1().LogConfigs()
	if _, err = lcConfig.Get(context.TODO(), crdConf.Crd.Name, metav1.GetOptions{}); err != nil {
		if errors2.IsNotFound(err) {
			return false, nil
		}
		return false, errorcode.InternalErrorCode.NewWithErr(err)
	}
	return true, nil
}

func IsEks(clusterGroupId int64) (ret bool, err error) {
	// eks 默认支持
	clusterGroupService, err := NewClusterGroupService(clusterGroupId)
	if err != nil {
		return false, errorcode.InternalErrorCode.NewWithErr(err)
	}
	tkeList, err := clusterGroupService.GetTkeList()
	if err != nil {
		return false, errorcode.InternalErrorCode.NewWithErr(err)
	}
	if len(tkeList) == 1 {
		if tkeList[0].ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
			return true, nil
		}
	}
	return false, nil
}

func GetDefaultNamespace(cg *table.ClusterGroup) string {
	result := constants.DEFAULT_NAMESPACE
	if cg.AgentSerialId != "" {
		result = cg.SerialId
	}
	return result
}

func GetDefaultCredentialNamespace(cg *table.ClusterGroup) string {
	result := constants.OCEANUS_NAMESPACE
	if cg.AgentSerialId != "" {
		result = constants.OCEANUS_NAMESPACE + "-" + cg.SerialId
	}
	return result
}

func SupportFineGrainedResource(cluster *table.Cluster) (ret bool, err error) {
	if cluster.SchedulerType != constants.CLUSTER_SCHEDULER_TYPE_TKE {
		return false, nil
	}

	supportedFeatures, err := cluster.GetSupportedFeatures()
	if err != nil {
		return false, errorcode.InternalErrorCode.NewWithErr(err)
	}
	return service6.InSliceString(constants.FeatureFineGrainedResource, supportedFeatures), nil
}

func SupportInPlaceScale(cluster *table.Cluster) (bool, error) {
	supportedFeatures, err := cluster.GetSupportedFeatures()
	if err != nil {
		return false, errorcode.InternalErrorCode.NewWithErr(err)
	}
	return service6.InSliceString(constants.FeatureInPlaceScale, supportedFeatures), nil
}
func ClusterSupportFlinkVersion(cluster *table.Cluster, flinkVersion string) bool {
	return service6.InSliceString(flinkVersion, cluster.GetSupportedFlinkVersion())
}

func DecodeClusterExtendConfigDNS(config interface{}, dnsConfig *model.DescribeClusterDNSRsp) (err error) {
	b, _ := json.Marshal(config)
	if err = json.Unmarshal(b, dnsConfig); err != nil {
		return errorcode.FailedOperationCode_InvalidClusterExtendConfig.NewWithInfo(
			fmt.Sprintf("Invalid dns configuration."), err)
	}
	return
}

func SupportCustomizedDNS(cluster *table.Cluster) bool {
	return cluster.SchedulerType != constants.CLUSTER_SCHEDULER_TYPE_EMR
}

/* 共享集群模式下，包年包月下的按量付费集群，在作业启动停止的时候，判断当前集群的正在
 * 运行的cu数量是不是超过了
 * 包年包月的cu数，然后
 * 记录到EksSettle表
 * 包含子eks集群的包年包月集群，运行作业都是记录到包年包月下，eks集群只做推量用.
 */
func RecordEKSResource(clusterId int64, appId int32, region string, ownerUin string) error {
	clusterGroup, err := GetClusterGroupByClusterId(clusterId)
	if err != nil {
		logger.Errorf("finish command RecordEKSResource for GetClusterGroupByClusterId with %d error %v", clusterId, err)
		return err
	}

	cluster, err := GetActiveClusterByClusterGroupId(clusterGroup.Id)
	if err != nil {
		logger.Errorf("Failed to GetActiveClusterByClusterGroupId for %d because %+v", clusterGroup.Id, err)
		return err
	}
	// 是否有sub eks cluster
	count, subEksClusterGroup, err := ListClusterGroupByParentSerialId(clusterGroup.SerialId)
	if count != 1 {
		logger.Warningf("cluster %s has more than one sub eks cluster", clusterGroup.SerialId)
		return err
	}
	if subEksClusterGroup.Type != constants.CLUSTER_GROUP_TYPE_SUB_EKS {
		return err
	}

	// 加锁
	locker := dlocker.NewDlocker("RecordEKSResource", fmt.Sprintf("RecordEKSResource-%s-%d", subEksClusterGroup.SerialId, appId), 60)
	err = locker.Lock()
	if err != nil {
		logger.Errorf("sync lock for RecordEKSResource failed![ %s ]", err.Error())
		return err
	}
	defer func() {
		err := locker.UnLock()
		if err != nil {
			logger.Errorf("RecordEKSResource could not UnLock the lock. %+v", err)
		}
	}()

	totalRunningCpu, totalRunningMem, err := service6.GetClusterRunningCpuAndMem(clusterGroup.SerialId, appId, region, cluster.MemRatio)
	if err != nil {
		logger.Errorf("finish command RecordEKSResource for GetRunningCU with %s error %v", clusterGroup.SerialId, err)
		return err
	}
	clusterRunning := service6.GetCuNumFromCpuMem(totalRunningCpu, totalRunningMem, cluster.MemRatio)
	txManager := service.GetTxManager()
	if clusterRunning > float32(clusterGroup.CuNum) {
		// set stoptime, add new record
		eksSettle := table4.EksSettle{
			ResourceId:   subEksClusterGroup.SerialId,
			AppId:        int64(appId),
			Uid:          ownerUin,
			StartTime:    util.GetCurrentTime(),
			RunningCuNum: clusterRunning - float32(clusterGroup.CuNum),
		}
		if eksSettle.RunningCuNum > float32(subEksClusterGroup.CuNum) {
			eksSettle.RunningCuNum = float32(subEksClusterGroup.CuNum)
		}
		txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
			sql := "UPDATE EksSettle SET StopTime = ? WHERE ResourceId = ? and AppId = ? and StopTime = ?"
			tx.ExecuteSqlWithArgs(sql, util.GetCurrentTime(), subEksClusterGroup.SerialId, appId, billing.NotIsolatedTimestamp)
			tx.SaveObject(eksSettle, "EksSettle")
			return nil
		}).Close()
	}
	if clusterRunning <= float32(clusterGroup.CuNum) {
		// set stoptime
		txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
			sql := "UPDATE EksSettle SET StopTime = ? WHERE ResourceId = ? and AppId = ? and StopTime = ?"
			tx.ExecuteSqlWithArgs(sql, util.GetCurrentTime(), subEksClusterGroup.SerialId, appId, billing.NotIsolatedTimestamp)
			return nil
		}).Close()
	}
	return nil
}

func ListUserActiveClusterGroup(appId int64, region string) (clusterGroupList []*table.ClusterGroup, err error) {
	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQueryWithArgs("SELECT * FROM ClusterGroup WHERE AppId=? and Status = 2 and Region = ? and Type != ?", appId, region, constants.CLUSTER_GROUP_TYPE_SUB_EKS)
	if err != nil {
		return nil, err
	}
	clusterGroupList = make([]*table.ClusterGroup, 0)
	for i := 0; i < len(data); i++ {
		c := &table.ClusterGroup{}
		err = util.ScanMapIntoStruct(c, data[i])
		if err != nil {
			return nil, errorcode.InternalErrorCode.NewWithErr(err)
		}
		clusterGroupList = append(clusterGroupList, c)
	}
	return clusterGroupList, nil
}

func ListClustersByClusterGroupId(clusterGroupId []int64) (t []*table.Cluster, err error) {
	cond := dao.NewCondition()
	cond.In("ClusterGroupId", clusterGroupId)
	where, args := cond.GetWhere()
	sql := "SELECT * FROM Cluster" + where
	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return nil, err
	}
	clusters := make([]*table.Cluster, 0)
	for i := 0; i < len(data); i++ {
		cluster := &table.Cluster{}
		err = util.ScanMapIntoStruct(cluster, data[i])
		if err != nil {
			return nil, errorcode.InternalErrorCode.NewWithErr(err)
		}
		clusters = append(clusters, cluster)
	}

	return clusters, nil
}

func ModifyClusterConfig(clusterId int64, creatorUin string, clusterConfig string) (err error) {
	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		args := make([]interface{}, 0)
		args = append(args, clusterConfig)
		args = append(args, clusterId)
		args = append(args, creatorUin)
		tx.ExecuteSql("UPDATE Cluster SET ClusterConfig=? WHERE Id=? AND CreatorUin=?", args)
		return nil
	}).Close()
	return err
}

func ConvertExpertModeConfiguration(jsonConf string) []byte {
	if jsonConf == "" || jsonConf == "{}" {
		return nil
	}
	expertModeConfiguration := &model2.ExpertModeConfiguration{}
	err := json.Unmarshal([]byte(jsonConf), expertModeConfiguration)
	if err != nil {
		logger.Errorf("%s %v", jsonConf, err)
		return nil
	}

	for _, n := range expertModeConfiguration.NodeConfig {
		if n.StateTTL != "" {
			if n.Configuration == nil {
				n.Configuration = make([]*model1.Property, 0)
			}
			n.Configuration = append(n.Configuration, &model1.Property{
				Key:   "__table.exec.node.state.ttl__",
				Value: n.StateTTL,
			})
		}
	}
	rt, _ := json.Marshal(expertModeConfiguration)
	return rt
}

func ListInnerNetTkeClusterGroupConditions() (tkeConditions []*table7.ChangeTkeCvmCondition, err error) {
	sql := `SELECT 
   				 t.InstanceId AS TkeInstanceId, 
    			 cg.Region AS TkeRegion 
			FROM ClusterGroup cg
			LEFT JOIN Cluster c ON cg.Id = c.ClusterGroupId
			LEFT JOIN Tke t ON t.ClusterId = c.Id
			WHERE cg.Status = 2
			AND cg.NetEnvironmentType = ?
			AND c.SchedulerType = ?
			AND t.ClusterType = ? `

	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQueryWithArgs(sql, constants.NETWORK_ENV_INNER_VPC,
		constants.CLUSTER_SCHEDULER_TYPE_TKE, constants.K8S_CLUSTER_TYPE_TKE)
	if err != nil {
		logger.Errorf("ListInnerNetTkeClusterGroupConditions failed! because of :%+v", err)
		return nil, err
	}
	tkeConditions = make([]*table7.ChangeTkeCvmCondition, 0)
	logger.Infof("ListInnerNetTkeClusterGroupConditions data: %d", len(data))
	for i := 0; i < len(data); i++ {
		condition := &table7.ChangeTkeCvmCondition{}
		err = util.ScanMapIntoStruct(condition, data[i])
		if err != nil {
			logger.Errorf("ListInnerNetTkeClusterGroupConditions failed! because of :%+v", err)
			continue
		}
		logger.Infof("ListInnerNetTkeClusterGroupConditions ,TkeInstanceId is %s ,Region is %s data: %+v", condition.TkeInstanceId, condition.TkeRegion, condition)
		tkeConditions = append(tkeConditions, condition)
	}
	return
}
