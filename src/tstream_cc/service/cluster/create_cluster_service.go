package service

import (
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"

	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service2 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/k8s"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/login_settings"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	table4 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/password"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/yunti"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config/configure_center"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/configuration_center"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cos"
	service4 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
	tag2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/tag"
	yuntiService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/yunti"
)

/**
 *
 * <AUTHOR>
 * @time 2019/4/28
 * @version 0.4.0
 * @since 0.4.0
 * @see
 */

func DoCreateClusterInternal(req *model.CreateClusterReq) (code string, msg string, clusterGroup *table.ClusterGroup, cluster *table.Cluster, flowId int64) {
	requestId := req.RequestId

	requestBase := &apiv3.RequestBase{
		RequestId: req.RequestId,
		Region:    req.Region,
		Uin:       req.Uin,
	}
	CheckAccountReq := &yunti.CheckAccountReq{
		RequestBase: *requestBase,
	}
	CheckOwnerUinRsp, err := yuntiService.CheckOwnUin(CheckAccountReq)
	if err != nil {
		logger.Errorf("%s: Failed to SendRequest2YunTi for checkOwnUin, with error: %+v", requestId, err)
		return controller.InternalError, fmt.Sprintf("%v", err), nil, nil, 0
	}

	//如果是云梯账号，则修改网络环境类型
	if CheckOwnerUinRsp.Result.Total > 0 {
		req.NetEnvironmentType = constants.NETWORK_ENV_INNER_VPC
	}

	// 创建最新版本的集群
	if req.ArchGeneration == 0 {
		req.ArchGeneration = constants.TKE_ARCH_GENERATION_V6
	}

	// 调整集群创建类型
	err = checkClusterType(req)
	if err != nil {
		logger.Errorf("%s: Failed to choose Agent Cluster, with error: %+v", requestId, err)
		return controller.InternalError, fmt.Sprintf("%v", err), nil, nil, 0
	}

	// 检查 Flink UI 密码是否合法
	if req.LoginSettings != nil {
		if passwd, err := base64.StdEncoding.DecodeString(req.LoginSettings.Password); err != nil {
			logger.Errorf("%s: Failed to decode flink ui password, with error: %+v", requestId, err)
			return controller.InternalError, fmt.Sprintf("%v", err), nil, nil, 0
		} else if valid := util.IsValidPassword(string(passwd)); valid != true {
			logger.Errorf("%s: Flink UI password is not valid", requestId)
			return controller.InvalidParameterValue_Password, controller.NULL, nil, nil, 0
		}
	} else {
		req.LoginSettings = &login_settings.LoginSettings{Password: base64.StdEncoding.EncodeToString([]byte(k8s.K8S_FLINK_WEBUI_PREFIX_PASSWD))}
	}

	// 检查标签是否合法
	tags, err := tag2.GetTagService().ValidateTags(req.Uin, req.Tags)
	if err != nil {
		logger.Errorf("%s: validateTags, with error: %+v", requestId, err)
		code := errorcode.GetCode(err)
		return code.GetCodeStr(), code.GetCodeDesc(), nil, nil, 0
	}
	req.Tags = tags

	// 检查 VPC 和 COS 是否为空
	isNull, errCode, errMsg := isNullVPCAndCOS(req)
	if isNull {
		return errCode, errMsg, nil, nil, 0
	}

	if req.OrderOrigin == 0 {
		// 检查 COS Bucket 是否合法
		err = cos.CheckWhetherCOSBucketExists(req.Uin, req.SubAccountUin, req.Region, req.DefaultCOSBucket)
		if err != nil {
			errCode := errorcode.GetCode(err)
			return errCode.GetCodeStr(), errCode.GetCodeDesc(), nil, nil, 0
		}
		// 现网VPC集群（公有云） 需要校验CLS信息。 自研上云等，不需要此字段
		//if req.NetEnvironmentType == constants.NETWORK_ENV_CLOUD_VPC {
		//	err = cls.TopicCanBeUse(req.Uin, req.SubAccountUin, req.Region, req.CLSLogSet, req.CLSTopicId)
		//	if err != nil {
		//		errCode := errorcode.GetCode(err)
		//		return errCode.GetCodeStr(), errCode.GetCodeDesc(), nil, nil, 0
		//	}
		//}
	}

	clusterGroup, cluster, flowId, err = CreateCluster(requestId, req)
	if err != nil {
		logger.Errorf("Failed to create cluster because %+v", err)
		return controller.FailedOperation_CreateCluster, err.Error(), nil, nil, 0
	}

	/*
		// 1. 通过Region和集群类型判断该
		if err = service2.AssignClusterBucket(clusterGroupId, clusterId); err != nil {
			logger.Errorf("Failed to assign cluster bucket, with error: %+v", err)
			return controller.InternalError, controller.NULL, nil
		}
	*/

	return controller.OK, controller.NULL, clusterGroup, cluster, flowId
}

func checkClusterType(req *model.CreateClusterReq) error {
	//创建按量计费子集群
	if req.ClusterGroupType == constants.CLUSTER_GROUP_TYPE_SUB_EKS {
		return nil
	}
	//创建EKS集群
	if req.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		req.AgentSerialId = ""
		req.NetEniType = constants.CLUSTER_NET_ENI_POD
		return nil
	}
	tableService := service4.GetTableService()
	sql := "select * from ClusterGroup"
	var clusterGroup *table.ClusterGroup
	cond := dao.NewCondition().
		Eq("SerialId", req.AgentSerialId).
		Eq("Status", constants.CLUSTER_GROUP_STATUS_RUNNING).
		Eq("Type", constants.CLUSTER_GROUP_TYPE_UNIFORM)
	where, args := cond.GetWhere()

	if req.AgentSerialId != "" {
		clusterGroups, err := tableService.ListclusterGroupsBySqlWithArgs(
			sql+where, args...)
		if err != nil {
			return err
		}
		if len(clusterGroups) >= 1 {
			clusterGroup = clusterGroups[0]
		}
	}

	// 创建统一资源池母集群
	if req.ClusterGroupType == constants.CLUSTER_GROUP_TYPE_UNIFORM && req.AgentSerialId == "" {
		return nil
	}
	// 统一资源池母集群开新区
	if req.ClusterGroupType == constants.CLUSTER_GROUP_TYPE_UNIFORM && req.AgentSerialId != "" {
		if clusterGroup == nil {
			msg := fmt.Sprintf("can not find clustergroup, agent serial %s", req.AgentSerialId)
			return errors.New(msg)
		}

		if supportZone, err := clusterGroup.IsSupportZone(req.Zone); err != nil {
			return err
		} else if supportZone {
			msg := fmt.Sprintf("zone %s has support, agent serial %s", req.Zone, req.AgentSerialId)
			return errors.New(msg)
		}
		return nil
	}

	//创建独享集群
	req.ClusterGroupType = constants.CLUSTER_GROUP_TYPE_PRIVATE
	//内网创建独享集群
	if req.NetEnvironmentType == constants.NETWORK_ENV_INNER_VPC {
		req.AgentSerialId = ""
		req.NetEniType = constants.CLUSTER_NET_ENI_POD
		return nil
	}
	//创建公网独享集群
	if auth.IsInWhiteList(req.AppId, constants.WHITE_LIST_NODE_NETWORK) {
		req.NetEniType = constants.CLUSTER_NET_ENI_NODE
	} else if req.NetEniType != constants.CLUSTER_NET_ENI_NODE {
		req.NetEniType = constants.CLUSTER_NET_ENI_POD
	}
	//非1:4的机器，创建独享集群
	if req.MemRatio != constants.CVM_DEFAULT_MEMRATIO {
		req.AgentSerialId = ""
		return nil
	}
	//节点弹性网卡，创建独享集群
	if req.NetEniType == constants.CLUSTER_NET_ENI_NODE {
		req.AgentSerialId = ""
		return nil
	}
	//白名单，创建独享集群
	if auth.IsInWhiteList(req.AppId, constants.WHITE_LIST_CREATE_PRIVATE_CLUSTER) {
		req.AgentSerialId = ""
		return nil
	}
	//默认创建独享集群
	if ok, _ := auth.WhiteListValue(req.AppId, constants.WHITE_LIST_CREATE_IN_AGENT); !ok {
		req.AgentSerialId = ""
		return nil
	}

	// 创建共享集群
	req.NetEniType = constants.CLUSTER_NET_ENI_POD
	if clusterGroup == nil {
		cond = dao.NewCondition().
			Eq("Status", constants.CLUSTER_GROUP_STATUS_RUNNING).
			Eq("Type", constants.CLUSTER_GROUP_TYPE_UNIFORM).
			Eq("UniformOwnerUin", "")
		where, args = cond.GetWhere()
		where += " ORDER BY CreateTime DESC"
		clusterGroups, err := tableService.ListclusterGroupsBySqlWithArgs(
			sql+where, args...)
		if err != nil {
			return err
		}

		if len(clusterGroups) >= 1 {
			clusterGroup = clusterGroups[0]
		}
	}

	if clusterGroup == nil {
		req.AgentSerialId = ""
		return nil
	}

	req.AgentSerialId = clusterGroup.SerialId

	cluster, err := GetClusterByClusterGroupId(clusterGroup.Id)
	if err != nil {
		return err
	}
	archGeneration, err := tableService.GetTkeArchGeneration(cluster.Id)
	if err != nil {
		return err
	}

	if auth.IsInWhiteList(req.AppId, constants.WHITE_LIST_RESOURCE_TYPE_SHARE) ||
		req.Uin == clusterGroup.UniformOwnerUin {
		req.ResourceType = constants.RESOURCE_TYPE_SHARE
		req.ArchGeneration = archGeneration
		return nil
	}

	if supportZone, err := clusterGroup.IsSupportZone(req.Zone); err != nil {
		return err
	} else if supportZone {
		req.ArchGeneration = archGeneration
		return nil
	}

	//资源独享集群，没有开区，退回到独享集群
	req.AgentSerialId = ""
	return nil
}

func isNullVPCAndCOS(req *model.CreateClusterReq) (bool, string, string) {
	if len(req.VPCDescriptions) == 0 {
		return true, controller.InvalidParameterValue_VpcParameterEmpty, "InvalidParameterValue.VpcParameterEmpty"
	}

	if len(req.VPCDescriptions[0].VpcId) == 0 {
		return true, controller.InvalidParameterValue_VpcParameterEmpty, "InvalidParameterValue.VpcParameterEmpty"
	}

	if len(req.VPCDescriptions[0].SubnetId) == 0 {
		return true, controller.InvalidParameterValue_VpcParameterEmpty, "InvalidParameterValue.VpcParameterEmpty"
	}

	if len(req.DefaultCOSBucket) == 0 {
		return true, controller.InvalidParameterValue_CosParameterEmpty, "InvalidParameterValue.CosParameterEmpty"
	}

	return false, "", ""
}

func ValidateVpcSubnet(netEnvType int, vpcDescriptions []model.VpcDescription, uin string, subAccountUin string, region string) (err error) {

	if len(vpcDescriptions) == 0 {
		err = fmt.Errorf("VPCDescriptions len is 0")
		return err
	}

	for _, description := range vpcDescriptions {
		// 1.0 修复
		var sid, sKey, token string
		if netEnvType == constants.NETWORK_ENV_INNER_VPC {
			sid, sKey, err = service.GetSecretIdAndKeyOfInner()
		} else if description.AppId != 0 && description.OwnerUin != "" {
			//inlong集群， 调用wedata获取临时密钥 assumeRole(user_uin)
			sid, sKey, token, _, err = service.AccessWeDataRoleKey(region, description.OwnerUin, description.OwnerUin)
			//sid, sKey, token, _, err = service.StsAssumeRole(description.OwnerUin, "", region)
		} else if description.AppId == 0 && description.OwnerUin == "" {
			sid, sKey, token, _, err = service.StsAssumeRole(uin, subAccountUin, region)
		} else {
			err = fmt.Errorf("VPCDescriptions AppId or OwnerUin is Empty")
		}
		if err != nil {
			logger.Errorf("validateVpcSubnet GetTokenCredential error: %+v", err)
			return err
		}

		exits, err := service.CheckVpcSubnetExist(description.VpcId, description.SubnetId, region, sid, sKey, token)
		if err != nil {
			logger.Errorf("validateVpcSubnet CheckVpcSubnetExist error: %+v", err)
			return err
		}
		vpcMessage := fmt.Sprintf("vpcId %s subnet %s on appId %d uni %s",
			description.VpcId, description.SubnetId, description.AppId, description.OwnerUin)
		if !exits {
			err = fmt.Errorf("%s not exits", vpcMessage)
			return err
		}
		logger.Infof("find %s", vpcMessage)
	}

	return err
}

func BuildClusterGroupEntityFromReq(req *model.CreateClusterReq) (*table.ClusterGroup, error) {
	clusterGroup := &table.ClusterGroup{}
	clusterGroup.Name = req.Name
	clusterGroup.AppId = int32(req.AppId)
	clusterGroup.OwnerUin = req.Uin
	clusterGroup.CreatorUin = req.SubAccountUin
	clusterGroup.Region = req.Region
	clusterGroup.Zone = req.Zone
	clusterGroup.Type = constants.CLUSTER_GROUP_TYPE_PRIVATE
	clusterGroup.NetEnvironmentType = req.NetEnvironmentType
	clusterGroup.Status = constants.CLUSTER_GROUP_STATUS_CREATING
	clusterGroup.CuNum = req.CuNum
	clusterGroup.CuMem = req.CuMem
	clusterGroup.UsedCuNum = 0
	clusterGroup.Remark = req.Remark
	clusterGroup.CreateTime = util.GetCurrentTime()
	clusterGroup.UpdateTime = clusterGroup.CreateTime
	clusterGroup.StopTime = "0000-00-00 00:00:00"

	clusterGroup.NetEniType = int8(req.NetEniType)
	clusterGroup.ResourceType = req.ResourceType
	clusterGroup.UniformOwnerUin = req.UniformOwnerUin

	if req.MemRatio != clusterGroup.CuMem {
		clusterGroup.CuMem = req.MemRatio
	}
	return clusterGroup, nil
}

func BuildClusterVersion(clusterId int64, region string) (clusterVersion *table.ClusterVersion, err error) {
	clusterVersion = &table.ClusterVersion{}
	clusterVersion.ClusterId = clusterId
	clusterVersion.Version = configure_center.CC(region).LatestClusterVersion()
	clusterVersion.CreateTime = util.GetCurrentTime()
	clusterVersion.UpdateTime = clusterVersion.CreateTime
	return clusterVersion, nil
}

func BuildClusterEntityFromReq(req *model.CreateClusterReq) (cluster *table.Cluster, err error) {
	cluster = &table.Cluster{}
	cluster.CreatorUin = req.SubAccountUin
	cluster.Zone = req.Zone
	cluster.VpcId = constants.DOUBLE_BAR
	cluster.SubnetId = constants.DOUBLE_BAR
	cluster.VpcCIDR = constants.DOUBLE_BAR
	cluster.RoleType = constants.CLUSTER_ROLE_TYPE_ACTIVE
	cluster.SchedulerType = constants.CLUSTER_SCHEDULER_TYPE_TKE
	cluster.CuNum = req.CuNum
	cluster.UsedCuNum = 0
	cluster.CreateTime = util.GetCurrentTime()
	cluster.UpdateTime = cluster.CreateTime
	cluster.StopTime = "0000-00-00 00:00:00"
	cluster.FlinkVersion = configure_center.CC(req.Region).DefaultFlinkVersion()
	cluster.DefaultCOSBucket = req.DefaultCOSBucket
	cluster.ClsLogSet = req.CLSLogSet
	cluster.ClsTopicId = req.CLSTopicId

	cluster.StateCOSBucket = req.DefaultCOSBucket
	cluster.LogCOSBucket = req.DefaultCOSBucket
	// 独享集群
	isPrivateCluster := req.ClusterGroupType == constants.CLUSTER_GROUP_TYPE_PRIVATE && req.AgentSerialId == ""
	if req.ClusterType == constants.K8S_CLUSTER_TYPE_TKE &&
		req.NetEnvironmentType == constants.NETWORK_ENV_CLOUD_VPC &&
		isPrivateCluster {
		// 判断当前 appid 是否在黑名单中
		if ok, err := Log2EsBlacklistContainsAppid(req.RequestId, req.AppId); err != nil {
			// 黑名单加载失败时, Log2Es 特性不应该加上
			logger.Errorf("%s failed get Log2EsBlacklist", req.RequestId)
		} else if ok {
			// 当前 appid 在log2es黑名单中
			logger.Infof("%s Log2EsBlacklistContainsAppid %d", req.RequestId, req.AppId)
		} else {
			// 当前 appid 不在黑名单中, 增加 Log2Es 到 Cluster SupportFeature
			req.SupportedFeatures, err = AddLog2EsToSupportFeature(req.RequestId, req.Region, req.SupportedFeatures)
			if err != nil {
				logger.Errorf("%s failed addLog2EsToSupportFeature", req.RequestId)
			}
		}

	}
	req.SupportedFeatures = append(req.SupportedFeatures, constants.FeatureParseConnector)
	if ok, value := auth.WhiteListValue(req.AppId, constants.WHITE_LIST_REMOVE_CLUSTER_FEATURE); ok {
		tobeRemoved := make([]string, 0)
		err := json.Unmarshal([]byte(value.Param), &tobeRemoved)
		if err != nil {
			logger.Errorf("%s failed to Unmarshal WhiteListValue %v", req.RequestId, value)
		} else {
			req.SupportedFeatures = service.Difference(req.SupportedFeatures, tobeRemoved)
		}
	}
	b, err := json.Marshal(req.SupportedFeatures)
	if err != nil {
		logger.Errorf("%s failed to mashal supportFeature", req.RequestId)
		return cluster, err
	}
	cluster.SupportedFeatures = string(b)
	cluster.LogMode = 1 // 创建新集群默认使用新版logListener，后续支持COS模式，这里的值则为动态
	// 设置集群默认日志采集配置为COS EKS 集群无需填充改字段
	if req.ClusterType == constants.K8S_CLUSTER_TYPE_TKE {
		defaultClusterLogConf := &model.DefaultLogCollectConf{
			LogCollectType: constants.CLUSTER_DEFAULT_LOG_COLLECT_COS,
			Conf: &model.LogCollectConf{
				CosBucket: req.DefaultCOSBucket,
			},
		}
		jsonBytes, _ := json.Marshal(defaultClusterLogConf)
		cluster.DefaultLogCollectConf = string(jsonBytes)
	}
	cluster.SupportedFlinkVersion = configure_center.CC(req.Region).DefaultSupportFlinkVersion()

	cluster.MemRatio = req.MemRatio
	cluster.Cores = req.Cores
	if req.NetEnvironmentType == constants.NETWORK_ENV_CLOUD_VPC && req.Region != constants.AP_SHANGHAI_ADC {
		cluster.CrossTenantEniMode = constants.EnableStaticMode
	}
	return cluster, nil
}

// 判断appid 是否在 Log2Es 的黑名单内
func Log2EsBlacklistContainsAppid(reqId string, appid int64) (result bool, err error) {
	value, err := config.GetRainbowConfiguration("ConfigureCenter.Flow.Common", "esServerlessAppIdBlacklist")
	if err != nil {
		logger.Errorf("Failed to get ConfigureCenter.Flow.Common.esServerlessAppIdBlacklist from Rainbow because %+v. ", err)
		return result, err
	}
	logger.Infof("%s Log2EsBlacklistContainsAppid step1 rainbowConfig value %s", reqId, value)
	log2EsBlacklist := make([]int64, 0)
	err = json.Unmarshal([]byte(value), &log2EsBlacklist)
	if err != nil {
		logger.Errorf("%s failed to unmarshal log2EsBlacklist", reqId)
		return result, err
	}
	for _, appidItem := range log2EsBlacklist {
		if appidItem == appid {
			result = true
			logger.Infof("%s  step2 log2EsBlacklist contains appid %d", reqId, appid)
			break
		}
	}
	return result, nil
}

// cluster feature 增加 Log2Es 特性
func AddLog2EsToSupportFeature(reqId, region string, feature []string) (newFeature []string, err error) {
	value, err := config.GetRainbowConfiguration("ConfigureCenter.Flow.Common", "esServerlessSupportRegion")
	if err != nil {
		logger.Errorf("Failed to get ConfigureCenter.Flow.Common.esServerlessSupportRegion from Rainbow because %+v. ", err)
		value = "[]"
	}
	logger.Infof("%s step1 rainbowConfig value %s", reqId, value)
	esServerlessSupportRegionList := make([]string, 0)
	err = json.Unmarshal([]byte(value), &esServerlessSupportRegionList)
	if err != nil {
		logger.Errorf("%s failed to unmarshal esServerlessSupportRegionList", reqId)
		return feature, err
	}
	for _, regionItem := range esServerlessSupportRegionList {
		if regionItem == region {
			feature = append(feature, constants.LOG2ES)
			logger.Infof("%s step2 add %s  to Cluster SupportedFeatures", reqId, constants.LOG2ES)
		}
	}
	return feature, nil
}

func BuildClusterGroupPeerVpcsFromReq(req *model.CreateClusterReq) ([]*table.ClusterGroupPeerVpc, error) {
	peerVpcs := make([]*table.ClusterGroupPeerVpc, 0)

	//isDev := service2.GetConfStringValue("scsDevEnv")
	//if isDev == "true" {
	//	vpcs, err := buildClusterGroupPeerVpcsFromConf(req.Region)
	//	if err != nil {
	//		return peerVpcs, err
	//	}
	//	peerVpcs = append(peerVpcs, vpcs...)
	//	b, _ := json.Marshal(vpcs)
	//	logger.Infof("BuildClusterGroupPeerVpcsFromReq, isDev = true, peerVpcs = %s", string(b))
	//}

	if len(peerVpcs) != 0 {
		b, _ := json.Marshal(peerVpcs)
		logger.Infof("BuildClusterGroupPeerVpcsFromReq, len(peerVpcs) > 0, peerVpcs = %s", string(b))
		return peerVpcs, nil
	}

	for _, vpcDescription := range req.VPCDescriptions {
		peerVpc := &table.ClusterGroupPeerVpc{}
		peerVpc.VpcId = vpcDescription.VpcId
		peerVpc.SubnetId = vpcDescription.SubnetId
		peerVpc.Status = constants.PEER_VPC_STATUS_OK
		peerVpc.CreateTime = util.GetCurrentTime()
		peerVpc.UpdateTime = peerVpc.CreateTime
		peerVpc.AppId = vpcDescription.AppId
		peerVpc.OwnerUin = vpcDescription.OwnerUin
		peerVpcs = append(peerVpcs, peerVpc)
	}

	return peerVpcs, nil
}

func BuildFlinkUiAuthInfo(req *model.CreateClusterReq) (*table4.FlinkUiAuthInfo, error) {
	passwdBytes, err := base64.StdEncoding.DecodeString(req.LoginSettings.Password)
	if err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode_EncodeFailed, "", err)
	}
	authinfo := &table4.FlinkUiAuthInfo{
		ClusterGroupId: 0,
		User:           "admin",
		Password:       string(passwdBytes),
	}
	if err := authinfo.EncodePassword(); err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode_EncodeFailed, "", err)
	}
	return authinfo, nil
}

func buildClusterGroupPeerVpcsFromConf(region string) ([]*table.ClusterGroupPeerVpc, error) {
	peerVpcs := make([]*table.ClusterGroupPeerVpc, 0)

	var (
		vpcId    string
		subnetId string
		appIdStr string
		ownerUin string
		appId    int32
	)

	key := fmt.Sprintf("%s.%s", "scsDevEnvVpc", region)
	vpc, err := service3.GetConfigurationValue(key, "")
	if err != nil {
		return peerVpcs, err
	}
	logger.Infof("buildClusterGroupPeerVpcsFromConf, vpc = %s", vpc)
	if len(vpc) > 0 {
		vpcSlice := strings.Split(vpc, ":")
		if !(len(vpcSlice) == 2 || len(vpcSlice) == 4) {
			return peerVpcs, nil
		}
		vpcId, subnetId = vpcSlice[0], vpcSlice[1]
		if len(vpcSlice) == 4 {
			appIdStr, ownerUin = vpcSlice[2], vpcSlice[3]
		}
	} else {
		vpcId = service2.GetConfStringValue("scsDevEnvVpcId")
		subnetId = service2.GetConfStringValue("scsDevEnvSubnetId")
		appIdStr = service2.GetConfStringValue("scsDevEnvVpcAppId")
		ownerUin = service2.GetConfStringValue("scsDevEnvVpcOwnerUin")
	}
	if vpcId == "" || subnetId == "" {
		return peerVpcs, nil
	}
	if appIdStr == "" || ownerUin == "" {
		ownerUin = ""
	} else {
		appId64, err := strconv.ParseInt(appIdStr, 10, 32)
		if err != nil {
			return peerVpcs, err
		}
		appId = int32(appId64)
	}

	peerVpc := &table.ClusterGroupPeerVpc{}
	peerVpc.Status = constants.PEER_VPC_STATUS_OK
	peerVpc.VpcId = vpcId
	peerVpc.SubnetId = subnetId
	peerVpc.CreateTime = util.GetCurrentTime()
	peerVpc.UpdateTime = peerVpc.CreateTime
	peerVpc.AppId = appId
	peerVpc.OwnerUin = ownerUin
	peerVpcs = append(peerVpcs, peerVpc)
	return peerVpcs, nil
}
