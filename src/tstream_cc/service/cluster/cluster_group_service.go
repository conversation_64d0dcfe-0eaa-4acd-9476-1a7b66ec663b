package service

import (
	"fmt"
	"reflect"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	model3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cdb"
	model2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/emr"
	table4 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cdb"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	table3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/emr"
	table5 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/log"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/tke"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config/common_config"
	"time"
)

type ClusterGroupService struct {
	clusterGroup  *table.ClusterGroup
	cluster       []*table.Cluster
	tke           []*table2.Tke // 理论上自动部署后， 一个 Cluster 对应一个tke, 一个emr 一个cdb
	emr           []*table3.Emr
	cdb           []*table4.Cdb
	activeCluster *table.Cluster
}

func NewClusterGroupService(clusterGroupId int64) (clusterGroupService *ClusterGroupService, err error) {
	clusterGroupService = &ClusterGroupService{}
	clusterGroupService.clusterGroup, err = ListClusterGroupById(clusterGroupId)
	if err != nil {
		return nil, err
	}

	if clusterGroupService.clusterGroup == nil {
		return nil, errorcode.ResourceNotFound_Cluster.New()
	}
	return
}

func NewClusterGroupServiceByGroup(group *table.ClusterGroup) (clusterGroupService *ClusterGroupService, err error) {
	clusterGroupService = &ClusterGroupService{}
	clusterGroupService.clusterGroup = group

	if clusterGroupService.clusterGroup == nil {
		return nil, errorcode.ResourceNotFound_Cluster.New()
	}
	return
}

func NewClusterGroupServiceBySerialId(serialId string) (clusterGroupService *ClusterGroupService, err error) {
	clusterGroupService = &ClusterGroupService{}
	clusterGroupService.clusterGroup, err = ListClusterGroupBySerialId(serialId)
	if err != nil {
		return
	}

	if clusterGroupService.clusterGroup == nil {
		return nil, errorcode.ResourceNotFound_Cluster.ReplaceDesc(serialId)
	}
	return
}

func (c *ClusterGroupService) CanDelete() (can bool, err error) {
	if can, err = c.statusCanDelete(); err != nil {
		return
	}

	if can, err = c.typeCanDelete(); err != nil {
		return
	}

	return true, nil
}

func (c *ClusterGroupService) CanScale() (can bool, err error) {
	if c.clusterGroup.Status != constants.CLUSTER_GROUP_STATUS_RUNNING {
		msg := fmt.Sprintf("ClusterGroup status %d", c.clusterGroup.Status)
		return false, errorcode.NewStackError(errorcode.ResourceInUseCode_InstanceInProcess, msg, nil)
	}

	if can, err = c.typeCanScale(); err != nil {
		return
	}

	return c.IsAutoDeploy()
}

func (c *ClusterGroupService) CanUpgrade() (can bool, err error) {
	if c.clusterGroup.Status != constants.CLUSTER_GROUP_STATUS_RUNNING {
		msg := fmt.Sprintf("ClusterGroup status %d", c.clusterGroup.Status)
		return false, errorcode.NewStackError(errorcode.ResourceInUseCode_InstanceInProcess, msg, nil)
	}

	if can, err = c.typeCanScale(); err != nil {
		return
	}

	return c.IsAutoDeploy()
}

func (c *ClusterGroupService) CanIsolate() (can bool, err error) {
	if c.clusterGroup.Status != constants.CLUSTER_GROUP_STATUS_RUNNING {
		msg := fmt.Sprintf("ClusterGroup status %d", c.clusterGroup.Status)
		return false, errorcode.NewStackError(errorcode.ResourceInUseCode_InstanceInProcess, msg, nil)
	}

	if can, err = c.typeCanIsolate(); err != nil {
		return
	}

	return true, nil
}

func (c *ClusterGroupService) GetClusterGroup() *table.ClusterGroup {
	return c.clusterGroup
}

func (c *ClusterGroupService) GetClusterList() (cluster []*table.Cluster, err error) {
	if c.cluster != nil {
		return c.cluster, nil
	}

	defer func() {
		if err != nil {
			c.cluster = nil
		}
	}()

	c.cluster, err = ListClusters(c.clusterGroup.Id)
	if err != nil {
		err = errorcode.NewStackError(errorcode.InternalErrorCode, "ListClusters", err)
		return c.cluster, err
	}

	return c.cluster, err
}

func (c *ClusterGroupService) GetCluster() (cluster *table.Cluster, err error) {
	if c.activeCluster != nil {
		return c.activeCluster, nil
	}

	defer func() {
		if err != nil {
			c.activeCluster = nil
		}
	}()
	c.cluster, err = c.GetClusterList()
	if err != nil {
		return nil, err
	}
	var tmpC *table.Cluster
	for _, item := range c.cluster {
		if item.RoleType == constants.CLUSTER_ROLE_TYPE_ACTIVE {
			c.activeCluster = item
		}
		tmpC = item
	}

	if c.activeCluster == nil {
		c.activeCluster = tmpC
	}

	if c.activeCluster == nil {
		err = errorcode.NewStackError(errorcode.ResourceNotFoundCode, "", nil)
		return nil, err
	}
	return c.activeCluster, nil
}

func (c *ClusterGroupService) GetActiveCluster() (cluster *table.Cluster, err error) {
	if c.activeCluster != nil {
		return c.activeCluster, nil
	}

	defer func() {
		if err != nil {
			c.activeCluster = nil
		}
	}()
	c.cluster, err = c.GetClusterList()
	if err != nil {
		return nil, err
	}
	for _, item := range c.cluster {
		if item.RoleType == constants.CLUSTER_ROLE_TYPE_ACTIVE {
			c.activeCluster = item
		}
	}
	if c.activeCluster == nil {
		err = errorcode.NewStackError(errorcode.ResourceNotFoundCode, "", nil)
		return nil, err
	}
	return c.activeCluster, nil
}

func (c *ClusterGroupService) GetClusterEniParam(formatAnno bool) (eniConfig string, err error) {
	pv, err := c.GetGroupPeerVpc()
	if err != nil {
		return
	}
	if pv.AppId == 0 {
		pv.AppId = c.clusterGroup.AppId
	}
	if pv.OwnerUin == "" {
		pv.OwnerUin = c.clusterGroup.OwnerUin
	}
	if formatAnno {
		eniConfig = fmt.Sprintf("'{\"AppId\":%d,\"Uin\":\"%s\",\"UniqVpcId\":\"%s\",\"SubnetId\":\"%s\"}'",
			pv.AppId, pv.OwnerUin, pv.VpcId, pv.SubnetId)
	} else {
		eniConfig = fmt.Sprintf("{\"AppId\":%d,\"Uin\":\"%s\",\"UniqVpcId\":\"%s\",\"SubnetId\":\"%s\"}",
			pv.AppId, pv.OwnerUin, pv.VpcId, pv.SubnetId)
	}

	return
}

func (c *ClusterGroupService) GetGroupPeerVpc() (clusterGroupPeerVpc *table.ClusterGroupPeerVpc, err error) {
	sql := "SELECT * FROM ClusterGroupPeerVpc WHERE ClusterGroupId = ? AND Status = 1"
	args := make([]interface{}, 0)
	args = append(args, c.clusterGroup.Id)
	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("ClusterGroupId: %d,error msg: %s", clusterGroupPeerVpc.ClusterGroupId, err.Error())
		return nil, err
	}
	if len(data) != 1 {
		err = fmt.Errorf("GetGroupPeerVpc error, cluster size is len(%d)", len(data))
		logger.Errorf(err.Error())
		return nil, err
	}

	clusterGroupPeerVpc = &table.ClusterGroupPeerVpc{}
	err = util.ScanMapIntoStruct(clusterGroupPeerVpc, data[0])
	if err != nil {
		logger.Errorf("Failed to get ScanMapIntoStruct, with errors:%+v", err)
		return nil, err
	}
	return clusterGroupPeerVpc, nil
}

func (c *ClusterGroupService) getClusterIdList() ([]int64, error) {
	clusterIds := make([]int64, 0)
	clusterList, err := c.GetClusterList()
	if err != nil {
		return nil, err
	}
	for _, cluster := range clusterList {
		clusterIds = append(clusterIds, cluster.Id)
	}
	return clusterIds, nil
}

func (c *ClusterGroupService) getCloudResourceList(tableName string, resultType reflect.Type) (reflect.Value, error) {
	clusterIds, err := c.getClusterIdList()
	if err != nil {
		return reflect.Zero(resultType), err
	}
	cond := dao.NewCondition()
	cond.In("ClusterId", clusterIds)
	where, args := cond.GetWhere()
	sql := fmt.Sprintf("SELECT * from %s", tableName) + where
	_, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		err = errorcode.NewStackError(errorcode.InternalErrorCode, sql, err)
		return reflect.Zero(resultType), err
	}
	result := reflect.MakeSlice(resultType, 0, len(data))
	for _, d := range data {
		value := reflect.New(resultType.Elem().Elem())
		err = util.ScanMapIntoStruct(value.Interface(), d)
		result = reflect.Append(result, value)
		if err != nil {
			err = errorcode.NewStackError(errorcode.InternalErrorCode, "ScanMapIntoStruct Tke", err)
			return reflect.Zero(resultType), err
		}
	}
	return result, nil
}

func (c *ClusterGroupService) GetTke() (tke *table2.Tke, err error) {
	tkeList, err := c.GetTkeList()
	if err != nil {
		return nil, err
	}
	if len(tkeList) != 1 {
		msg := fmt.Sprintf("%s tke is length is not 1, but %d", c.clusterGroup.SerialId, len(tkeList))
		return nil, errorcode.InternalErrorCode.NewWithMsg(msg)
	}
	return tkeList[0], nil
}

func (c *ClusterGroupService) GetTkeList() (tke []*table2.Tke, err error) {
	if c.tke != nil {
		return c.tke, nil
	}
	tmp, err := c.getCloudResourceList("Tke", reflect.TypeOf(c.tke))
	if err != nil {
		return nil, err
	}
	c.tke = tmp.Interface().([]*table2.Tke)
	return c.tke, nil
}

func (c *ClusterGroupService) GetEmrList() (emr []*table3.Emr, err error) {
	if c.emr != nil {
		return c.emr, nil
	}
	tmp, err := c.getCloudResourceList("Emr", reflect.TypeOf(c.emr))
	if err != nil {
		return nil, err
	}
	c.emr = tmp.Interface().([]*table3.Emr)
	return c.emr, nil
}

func (c *ClusterGroupService) GetCdb() (cdb *table4.Cdb, err error) {
	cdbList, err := c.GetCdbList()
	if err != nil {
		return
	}
	if len(cdbList) != 1 {
		msg := fmt.Sprintf("%s tke is length is not 1, but %d", c.clusterGroup.SerialId, len(cdbList))
		return nil, errorcode.InternalErrorCode.NewWithMsg(msg)
	}

	return cdbList[0], nil
}

func (c *ClusterGroupService) GetCdbList() (cdb []*table4.Cdb, err error) {
	if c.cdb != nil {
		return c.cdb, nil
	}
	tmp, err := c.getCloudResourceList("Cdb", reflect.TypeOf(c.cdb))
	if err != nil {
		return nil, err
	}
	c.cdb = tmp.Interface().([]*table4.Cdb)
	rainbowOceanusPasswordEncodeKey, err := common_config.GetRainbowOceanusPasswordEncodeKey()
	if err != nil {
		logger.Errorf("Failed to get oceanus cluster password encode key in Rainbow")
		return nil, err
	}
	for _, db := range c.cdb {
		if len(db.Password) > 0 {
			if password, err := util.DecodePassword(db.Password, rainbowOceanusPasswordEncodeKey); err != nil {
				return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
			} else {
				db.Password = password
			}
		}
	}
	return c.cdb, nil
}

func (c *ClusterGroupService) IsAutoDeploy() (bool, error) {
	// 目前根据 Tke 表是否存在对应的数据来判断
	tkeList, err := c.GetTkeList()
	if len(tkeList) == 0 {
		err = errorcode.NewStackError(errorcode.UnsupportedOperationCode_Cluster,
			"not auto deploy cluster", nil)
		return false, err
	}
	return true, nil
}

func (c *ClusterGroupService) MarkDeleting() (err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, "MarkDeleting")

	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		if err = c.markStatus(tx, constants.CLUSTER_GROUP_STATUS_DELETING); err != nil {
			return err
		}
		if err = c.markClusterRoleType(tx, constants.CLUSTER_ROLE_TYPE_DECOMMISSIONING); err != nil {
			return err
		}
		return nil
	}).Close()
	return nil
}

func (c *ClusterGroupService) MarkDeleted() (err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, "MarkDeleted")

	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		if err = c.markStatus(tx, constants.CLUSTER_GROUP_STATUS_DELETED); err != nil {
			return err
		}

		if err = c.markStopTime(tx); err != nil {
			return err
		}

		if err = c.markClusterRoleType(tx, constants.CLUSTER_ROLE_TYPE_DECOMMISSIONED); err != nil {
			return err
		}

		if err = c.markRelatedCloudResourceDeleted(tx); err != nil {
			return err
		}

		if err = c.markClusterVersionDeleted(tx); err != nil {
			return err
		}

		if err = c.markRelatedJobDeleted(tx); err != nil {
			return err
		}
		return nil
	}).Close()
	return nil
}

func (c *ClusterGroupService) MarkDeletedWithRemark(remark string) (err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, "MarkDeletedWithRemark")
	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		// 保证在一个 事务里面执行
		sql := "UPDATE ClusterGroup SET Remark = ? WHERE Id = ?"
		tx.ExecuteSqlWithArgs(sql, remark, c.clusterGroup.Id)
		return c.MarkDeleted()
	}).Close()
	return nil
}

func (c *ClusterGroupService) MarkIsolated(tx *dao.Transaction) (err error) {
	if tx == nil {
		service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
			if err = c.markStatus(tx, constants.CLUSTER_GROUP_STATUS_DELETING); err != nil {
				return err
			}
			return nil
		}).Close()
		return nil
	}
	return c.markStatus(tx, constants.CLUSTER_GROUP_STATUS_ISOLATED)
}

func (c *ClusterGroupService) MarkUpgrade(tx *dao.Transaction) (err error) {
	updateTime := time.Now().Format("2006-01-02 15:04:05")
	if tx == nil {
		service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
			if err = c.markStatusAndUpdateTime(tx, constants.CLUSTER_GROUP_STATUS_UPGRADE, updateTime); err != nil {
				return err
			}
			return nil
		}).Close()
		return nil
	}
	return c.markStatusAndUpdateTime(tx, constants.CLUSTER_GROUP_STATUS_UPGRADE, updateTime)
}

func (c *ClusterGroupService) GetClsInfo() (logSet string, topicId string, historyCluster bool, err error) {
	cluster, err := c.GetActiveCluster()
	if err != nil {
		return "", "", false, err
	}
	if cluster.ClsTopicId != "" {
		return cluster.ClsLogSet, cluster.ClsTopicId, false, nil
	}

	cnt, data, err := service.GetTxManager().GetQueryTemplate().DoQueryWithArgs(
		"SELECT * FROM JobRunningLogTopic WHERE Status=1 AND OwnerUin=? AND Region=?", c.clusterGroup.OwnerUin,
		c.clusterGroup.Region)
	if err != nil {
		return "", "", true, err
	}
	if cnt == 0 {
		return "", "", true, nil
	}

	if cnt > 1 {
		return "", "", true, errorcode.InternalErrorCode.New()
	}

	inst := &table5.JobRunningLogTopic{}
	if err = util.ScanMapIntoStruct(inst, data[0]); err != nil {
		return "", "", true, errorcode.InternalErrorCode.New()
	}

	return inst.LogSetId, inst.LogTopicId, true, nil
}

func (c *ClusterGroupService) statusCanDelete() (can bool, err error) {
	if c.clusterGroup.Status == constants.CLUSTER_GROUP_STATUS_DELETED {
		return true, nil
	}

	// 目前只有运行中和隔离中的才能删除，其余状态的都不可以
	if c.clusterGroup.Status != constants.CLUSTER_GROUP_STATUS_RUNNING &&
		c.clusterGroup.Status != constants.CLUSTER_GROUP_STATUS_ISOLATED {
		msg := fmt.Sprintf("ClusterGroup status %d", c.clusterGroup.Status)
		return false, errorcode.NewStackError(errorcode.ResourceInUseCode_InstanceInProcess, msg, nil)
	}
	return true, nil
}

func (c *ClusterGroupService) typeCanDelete() (can bool, err error) {
	if c.clusterGroup.Type != constants.CLUSTER_GROUP_TYPE_PRIVATE && c.clusterGroup.Type != constants.CLUSTER_GROUP_TYPE_SUB_EKS {
		msg := fmt.Sprintf("Cluster group type %d", c.clusterGroup.Type)
		return false, errorcode.NewStackError(errorcode.UnsupportedOperationCode_ClusterCanNotDelete, msg, nil)
	}

	return true, nil
}

func (c *ClusterGroupService) typeCanScale() (can bool, err error) {
	if c.clusterGroup.Type != constants.CLUSTER_GROUP_TYPE_PRIVATE && c.clusterGroup.Type != constants.CLUSTER_GROUP_TYPE_SUB_EKS {
		msg := fmt.Sprintf("Cluster group type %d", c.clusterGroup.Type)
		return false, errorcode.NewStackError(errorcode.UnsupportedOperationCode_ClusterCanNotScale, msg, nil)
	}
	return true, nil
}

func (c *ClusterGroupService) typeCanIsolate() (can bool, err error) {
	if c.clusterGroup.Type != constants.CLUSTER_GROUP_TYPE_PRIVATE && c.clusterGroup.Type != constants.CLUSTER_GROUP_TYPE_SUB_EKS {
		msg := fmt.Sprintf("Cluster group type %d", c.clusterGroup.Type)
		return false, errorcode.NewStackError(errorcode.UnsupportedOperationCode_ClusterCanNotScale, msg, nil)
	}

	return true, nil
}

func (c *ClusterGroupService) markStatus(tx *dao.Transaction, status int) error {
	sql := "UPDATE ClusterGroup SET Status = ? WHERE Id = ?"
	tx.ExecuteSqlWithArgs(sql, status, c.clusterGroup.Id)
	return nil
}

func (c *ClusterGroupService) markStatusAndUpdateTime(tx *dao.Transaction, status int, updateTime string) error {
	sql := "UPDATE ClusterGroup SET Status = ? and UpdateTime = ? WHERE Id = ?"
	tx.ExecuteSqlWithArgs(sql, status, updateTime, c.clusterGroup.Id)
	return nil
}

/**
 * https://tcb.woa.com/magical-brush/docs/754674275
 * 推量延迟及补结算流程介绍： https://tcb.woa.com/magical-brush/docs/807014104
 * 后付费 需要推送用量账单， 更新这个stop time 是为了 查询用量的时候，可以筛选根据时间 过滤筛选已经停用集群
 * 为什么要根据时间呢， 因为不能根据状态，因为删除的集群，可能在上一个计费周期有消耗
 * 另外， 计费 小时结算 如果在下个月1号没有推送，就需要 走补结算流程了， 系统发送也没用了
 * 所以这里过滤只需要筛选出 停止时间 > 大于本月1号 就行了，如果小于本月1号， 推了也没用了。
 */
func (c *ClusterGroupService) markStopTime(tx *dao.Transaction) error {
	sql := "UPDATE ClusterGroup SET StopTime = ? WHERE Id = ?"
	tx.ExecuteSqlWithArgs(sql, util.GetCurrentTime(), c.clusterGroup.Id)
	return nil
}

func (c *ClusterGroupService) markClusterRoleType(tx *dao.Transaction, roleType int) error {
	sql := "UPDATE Cluster SET RoleType = ? WHERE ClusterGroupId = ?"
	tx.ExecuteSqlWithArgs(sql, roleType, c.clusterGroup.Id)
	return nil
}

func (c *ClusterGroupService) markClusterVersionDeleted(tx *dao.Transaction) (err error) {
	clusterIds, err := c.getClusterIdList()
	if err != nil {
		return
	}
	for _, clusterId := range clusterIds {
		args := make([]interface{}, 0)
		args = append(args, clusterId)
		tx.ExecuteSql("Delete from ClusterVersion WHERE ClusterId=?", args)
	}
	return nil
}

func (c *ClusterGroupService) markRelatedCloudResourceDeleted(tx *dao.Transaction) (err error) {
	clusterIds, err := c.getClusterIdList()
	if err != nil {
		return
	}
	cond := dao.NewCondition()
	cond.In("ClusterId", clusterIds)
	where, args := cond.GetWhere()

	sql := "UPDATE Tke SET Status = ?"
	tx.ExecuteSql(sql+where, append([]interface{}{constants.TKE_STATUS_DELETED}, args...))

	sql = "UPDATE Emr SET Status = ?"
	tx.ExecuteSql(sql+where, append([]interface{}{model2.EMR_STATUS_DELETED}, args...))

	sql = "UPDATE Cdb SET Status = ?"
	tx.ExecuteSql(sql+where, append([]interface{}{model3.CDB_STATUS_DELETED}, args...))
	return nil
}

// 这里不调用 deleteJobs 接口，是因为 deleteJob 只能删除非 运行状态的job
func (c *ClusterGroupService) markRelatedJobDeleted(tx *dao.Transaction) (err error) {
	sql := "UPDATE Job SET Status = ?, LastOpResult = ? WHERE ClusterGroupId = ?"
	tx.ExecuteSqlWithArgs(sql, constants.JOB_STATUS_DELETE, "Deleted because ClusterGroup Deleted", c.clusterGroup.Id)

	sql = "UPDATE JobInstance ji JOIN Job j ON ji.JobId = j.Id SET ji.Status = ? WHERE j.ClusterGroupId = ?"
	tx.ExecuteSqlWithArgs(sql, constants.JOB_INSTANCE_STATUS_HISTORY, c.clusterGroup.Id)

	sql = "UPDATE JobConfig jc JOIN Job j ON jc.JobId = j.Id SET jc.Status = ? , jc.DeletorUin = ? WHERE j.ClusterGroupId = ?"
	tx.ExecuteSqlWithArgs(sql, constants.JOB_CONFIG_STATUS_DELETE, "System", c.clusterGroup.Id)

	sql = "UPDATE ResourceRef r JOIN JobConfig jc on r.JobConfigId = jc.Id JOIN Job j ON jc.JobId = j.Id SET r.Status = ? WHERE j.ClusterGroupId = ?"

	tx.ExecuteSqlWithArgs(sql, constants.RESOURCE_REF_STATUS_DELETE, c.clusterGroup.Id)

	sql = "delete jcs from JobConfigSqlCode jcs JOIN Job j on jcs.JobId = j.Id WHERE j.ClusterGroupId = ?"

	tx.ExecuteSqlWithArgs(sql, c.clusterGroup.Id)

	return nil
}
