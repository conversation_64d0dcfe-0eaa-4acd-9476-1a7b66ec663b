package service

import (
	"encoding/json"
	"fmt"

	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	txService "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/log"
	model2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cls"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cos"
)

/**
 *
 * <AUTHOR>
 * @time 2019/4/28
 * @version 0.4.0
 * @since 0.4.0
 * @see
 */

// 为了解决import cycle
type LogModify interface {
	ApplyLogConfigsCrdInstance(ownerUin, subAccountUin, region, kubeConfig, topicId string) (err error)
}

type ModifyClusterService struct {
	clusterGroupService *ClusterGroupService
	req                 *model.ModifyClusterReq
	modifyLogService    LogModify
}

func NewModifyClusterService(req *model.ModifyClusterReq, modifyLogService LogModify) (s *ModifyClusterService) {
	return &ModifyClusterService{req: req, modifyLogService: modifyLogService}
}

func (m *ModifyClusterService) Modify() (rsp *model.ModifyClusterRsp, err error) {
	m.clusterGroupService, err = NewClusterGroupServiceBySerialId(m.req.ClusterId)
	if err != nil {
		return nil, err
	}
	if err = m.validReq(); err != nil {
		return nil, err
	}
	// 根据appID判断权限
	clusterGroup := m.clusterGroupService.GetClusterGroup()
	if clusterGroup.AppId != int32(m.req.AppId) {
		logger.Errorf("%s: User  uin %s is NOT authenticated, No modify permission for the current cluster", m.req.RequestId, m.req.Uin)
		return nil, errorcode.UnsupportedOperationCode.ReplaceDesc("No modify permission for the current cluster")
	}
	// 注意修改 COSBukect， 理论上应该还要修改这个集群的 core-site.xml,但目前没有做
	if err = m.doModify(); err != nil {
		return nil, err
	}

	return &model.ModifyClusterRsp{}, err
}

func (m *ModifyClusterService) validReq() (err error) {
	if m.req.ClusterName != "" {
		if err = service.CheckNameValidity(m.req.ClusterName); err != nil {
			return err
		}
	}
	if m.req.Remark != "" {
		if err = service.CheckNameValidity(m.req.Remark); err != nil {
			return err
		}
	}

	if err = m.validParamCOSBucket(); err != nil {
		return err
	}

	//if err = m.validParamCLS(); err != nil {
	//	return err
	//}
	return nil
}

func (m *ModifyClusterService) validParamCOSBucket() (err error) {
	if m.req.DefaultCOSBucket == "" {
		return nil
	}
	cluster, err := m.clusterGroupService.GetActiveCluster()
	if err != nil {
		return err
	}

	if cluster.SchedulerType != constants.CLUSTER_SCHEDULER_TYPE_TKE {
		return errorcode.UnsupportedOperationCode.ReplaceDesc("Only TKE, EKS cluster supports DefaultCOSBucket settings")
	}

	return cos.CheckWhetherCOSBucketExists(m.req.Uin, m.req.SubAccountUin, m.req.Region, m.req.DefaultCOSBucket)
}

func (m *ModifyClusterService) validParamCLS() (err error) {
	if m.req.CLSLogSet == "" {
		if m.req.CLSTopicId == "" {
			return nil
		}
		return errorcode.InvalidParameterValueCode.ReplaceDesc("CLSLogSet and CLSTopicId must been set both")
	}
	if m.req.CLSTopicId == "" {
		return errorcode.InvalidParameterValueCode.ReplaceDesc("CLSLogSet and CLSTopicId must been set both")
	}

	cluster, err := m.clusterGroupService.GetActiveCluster()
	if err != nil {
		return err
	}
	if cluster.SchedulerType != constants.CLUSTER_SCHEDULER_TYPE_TKE {
		return errorcode.UnsupportedOperationCode.ReplaceDesc("Only TKE, EKS cluster supports CLSTopicId settings")
	}

	_, topicId, historyCluster, err := m.clusterGroupService.GetClsInfo()
	if err != nil {
		return err
	}
	// 历史开启地域日志模式的集群， 不支持修改
	if historyCluster && len(topicId) > 0 {
		// 排除调升级过程中的集群
		exist, err := LogConfigCrdExists(cluster)
		if err != nil {
			return err
		}
		if exist {
			return errorcode.UnsupportedOperationCode_Cluster.ReplaceDesc("This Cluster not support change CLS Topic")
		}
	}

	// 新集群不允许修改
	if !historyCluster && topicId != "" {
		msg := fmt.Sprintf("CLSTopicId has been set to %s, can't change.", topicId)
		return errorcode.UnsupportedOperationCode_Cluster.ReplaceDesc(msg)
	}

	err = cls.TopicCanBeUse(m.req.Uin, m.req.SubAccountUin, m.req.Region, m.req.CLSLogSet, m.req.CLSTopicId)
	if err != nil {
		return err
	}
	return nil
}

func (m *ModifyClusterService) doModify() (err error) {
	defer errorcode.DefaultDeferHandler(&err)
	clusterGroup := m.clusterGroupService.GetClusterGroup()
	cluster, err := m.clusterGroupService.GetActiveCluster()
	if err != nil {
		return err
	}
	txService.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		if m.req.ClusterName != "" {
			clusterGroup.Name = m.req.ClusterName
		}
		if m.req.Remark != "" {
			clusterGroup.Remark = m.req.Remark
		}
		if m.req.DefaultCOSBucket != "" {
			cluster.DefaultCOSBucket = m.req.DefaultCOSBucket
		}
		//if m.req.CLSTopicId != "" {
		//	cluster.ClsLogSet = m.req.CLSLogSet
		//	cluster.ClsTopicId = m.req.CLSTopicId
		//	err = m.modifyLogService.ApplyLogConfigsCrdInstance(m.req.Uin, m.req.SubAccountUin, m.req.Region,
		//		cluster.KubeConfig, m.req.CLSTopicId)
		//	if err != nil {
		//		return err
		//	}
		//}
		tx.UpdateObject(clusterGroup, clusterGroup.Id, "ClusterGroup")
		tx.UpdateObject(cluster, cluster.Id, "Cluster")

		return nil
	}).Close()

	return nil
}

// 修改集群角色
func DoModifyClusterRoleType(req *model2.ModifyClusterReq) (int64, string, interface{}) {

	exist, err := CheckClusterExist(req.ClusterId, req.UserUin)
	if err != nil {
		return controller.SYSERR, controller.NULL, &model.ModifyClusterRsp{}
	}

	if !exist {
		return controller.PARAMSERR, controller.NULL, &model.ModifyClusterRsp{}
	}

	switch req.RoleType {
	case constants.CLUSTER_ROLE_TYPE_ACTIVE,
		constants.CLUSTER_ROLE_TYPE_STANDBY,
		constants.CLUSTER_ROLE_TYPE_DECOMMISSIONED,
		constants.CLUSTER_ROLE_TYPE_DECOMMISSIONING:
		logger.Infof("check cluster roleType pass")
	default:
		logger.Errorf("cluster roleType %d error, not support", req.RoleType)
		return controller.PARAMSERR, controller.NULL, &model.ModifyClusterRsp{}
	}

	err = ModifyClusterRoleType(req.ClusterId, req.UserUin, req.RoleType)
	if err != nil {
		return controller.SYSERR, controller.NULL, &model.ModifyClusterRsp{}
	}
	return controller.SUCCESS, controller.NULL, &model.ModifyClusterRsp{}
}

// DoModifyClusterDefaultLogCollectConf 修改集群默认日志配置
func DoModifyClusterDefaultLogCollectConf(req *model.ModifyClusterReq) (interface{}, error) {
	rsp := &model.ModifyClusterRsp{}
	clusterGroup, err := ListClusterGroupBySerialIdV2(req.ClusterId, req.AppId)
	if err != nil {
		return rsp, err
	}
	logger.Infof("clusterGroup [%v]", clusterGroup)
	clusterList, err := ListClusters(clusterGroup.Id)
	if err != nil {
		return rsp, err
	}
	for _, cluster := range clusterList {
		var defaultLogCollectConf = &model.DefaultLogCollectConf{LogCollectType: req.DefaultLogCollect}
		if req.DefaultLogCollect == constants.CLUSTER_DEFAULT_LOG_COLLECT_CLS {
			defaultLogCollectConf.Conf = &model.LogCollectConf{ClsLogsetId: req.CLSLogSet, ClsTopicId: req.CLSTopicId, CosBucket: ""}
		} else if req.DefaultLogCollect == constants.CLUSTER_DEFAULT_LOG_COLLECT_COS {
			if req.DefaultCOSBucket == "" || req.DefaultCOSBucket != cluster.DefaultCOSBucket {
				return rsp, errorcode.InvalidParameter_ModifyClusterDefaultLogCollectConfError.ReplaceDesc("Modify cluster default log collect failed. COS bucket not valid")
			}
			defaultLogCollectConf.Conf = &model.LogCollectConf{ClsLogsetId: "", ClsTopicId: "", CosBucket: req.DefaultCOSBucket}
		} else if req.DefaultLogCollect == constants.CLUSTER_DEFAULT_LOG_COLLECT_ES_SERVERLESS {
			// 判断此时的 index 是否绑定完成
			clusterLogconfig := cluster.LogConfig
			esServelessLogConf := &log.ClusterEsServelessLogConf{}
			err = json.Unmarshal([]byte(clusterLogconfig), esServelessLogConf)
			if err != nil {
				logger.Errorf("Cluster.LogConfig format error %s", clusterLogconfig)
				return rsp, errorcode.InvalidParameter_ModifyClusterDefaultLogCollectConfError.ReplaceDesc("Modify cluster default log collect failed. index not valid")
			}
			if esServelessLogConf.Status != log.ClusterLogconfDone {
				return rsp, errorcode.InvalidParameter_ModifyClusterDefaultLogCollectConfError.ReplaceDesc("Modify cluster default log collect failed. index diData status not ready")
			}
			defaultLogCollectConf.Conf = &model.LogCollectConf{EsServerlessIndex: req.EsServerlessIndex, EsServerlessSpace: req.EsServerlessSpace}
		} else {
			defaultLogCollectConf.Conf = &model.LogCollectConf{ClsLogsetId: "", ClsTopicId: "", CosBucket: ""}
		}
		dfStr, err := json.Marshal(defaultLogCollectConf)
		if err != nil {
			logger.Errorf("defaultLogCollectConf decode to jsonStr error")
			return rsp, err
		}
		err = ModifyClusterDefaultLogCollectConf(cluster.Id, string(dfStr))
		if err != nil {
			logger.Errorf("ModifyClusterDefaultLogCollectConf with error [%v]", err)
			return rsp, err
		}
	}
	return rsp, nil
}
