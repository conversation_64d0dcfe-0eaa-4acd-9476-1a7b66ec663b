package service

import (
	"fmt"
	cdb2 "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/cdb/v20170320"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common"
	sdkTke "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/tke/v20180525"
	"sync"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service2 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	model3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cdb"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/cluster"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	service4 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cdb"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config/common_config"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/configuration_center"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/tke"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/vpc"
)

// /////////////////////////////////////////////////////////////////////////////
type PrepareResource interface {
	ResourceReady(*model.CreateClusterInternalReq, *table.ClusterGroup, *table.Cluster) (bool, error)
	UserProvidedResource(req *model.CreateClusterInternalReq) bool
	ValidateResourceInCloudVpc(*model.CreateClusterInternalReq, *table.ClusterGroup, *table.Cluster) (interface{}, error)
	ValidateResourceInInnerVpc(*model.CreateClusterInternalReq, *table.ClusterGroup, *table.Cluster) (interface{}, error)
	RecordResource(interface{}, *model.CreateClusterInternalReq, *table.ClusterGroup, *table.Cluster, *dao.Transaction) error
}

type PrepareClusterVpc struct {
}

type PrepareCdb struct {
}

type PrepareTke struct {
}

// /////////////////////////////////////
func (o *PrepareClusterVpc) ResourceReady(req *model.CreateClusterInternalReq, clusterGroup *table.ClusterGroup, cluster *table.Cluster) (ready bool, err error) {
	if cluster.VpcId != constants.DOUBLE_BAR && cluster.SubnetId != constants.DOUBLE_BAR {
		return true, nil
	}

	return false, nil
}

func (o *PrepareClusterVpc) UserProvidedResource(req *model.CreateClusterInternalReq) bool {
	return req.ClusterVpc != nil && len(req.ClusterVpc.VpcId) > 0 && len(req.ClusterVpc.SubnetId) > 0
}

func (o *PrepareClusterVpc) ValidateResourceInCloudVpc(req *model.CreateClusterInternalReq, clusterGroup *table.ClusterGroup, cluster *table.Cluster) (instance interface{}, err error) {
	// 检查用户提供的资源是否有效
	vpcService := vpc.GetVpcService()
	vpcReq := vpcService.NewDefaultDescribeVpcsRequestBuilder().
		WithVpcIds([]string{req.ClusterVpc.VpcId}).
		Build()
	if _, _, err := vpcService.DescribeVpcsWithScsAccount(req.Region, vpcReq); err != nil {
		return nil, err
	}

	subnetReq := vpcService.NewDefaultDescribeSubnetsRequestBuilder().
		WithSubnetIds([]string{req.ClusterVpc.SubnetId}).
		Build()
	if _, _, err := vpcService.DescribeSubnetsWithScsAccount(req.Region, subnetReq); err != nil {
		return nil, err
	}
	return nil, nil
}

func (o *PrepareClusterVpc) ValidateResourceInInnerVpc(req *model.CreateClusterInternalReq, clusterGroup *table.ClusterGroup, cluster *table.Cluster) (instance interface{}, err error) {
	// 自研上云环境无需检查
	return nil, nil
}

func (o *PrepareClusterVpc) RecordResource(instance interface{}, req *model.CreateClusterInternalReq, clusterGroup *table.ClusterGroup, cluster *table.Cluster, tx *dao.Transaction) (err error) {
	tx.ExecuteSqlWithArgs("update Cluster set VpcId=?,SubnetId=? where Id=?",
		req.ClusterVpc.VpcId, req.ClusterVpc.SubnetId, cluster.Id)

	return nil
}

// /////////////////////////////////////
func (o *PrepareCdb) ResourceReady(req *model.CreateClusterInternalReq, clusterGroup *table.ClusterGroup, cluster *table.Cluster) (ready bool, err error) {
	cdbList, err := service.GetTableService().ListCdbByClusterId(cluster.Id)
	if err != nil {
		return false, err
	}
	if len(cdbList) > 1 {
		return false, errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("there are %d cdb instances", len(cdbList)), nil)
	}
	if len(cdbList) == 1 {
		return true, nil
	}

	return false, nil
}

func (o *PrepareCdb) UserProvidedResource(req *model.CreateClusterInternalReq) bool {
	return req.CdbInstance != nil && len(req.CdbInstance.InstanceId) > 0
}

func (o *PrepareCdb) ValidateResourceInCloudVpc(req *model.CreateClusterInternalReq, clusterGroup *table.ClusterGroup, cluster *table.Cluster) (instance interface{}, err error) {
	if len(req.CdbInstance.User) == 0 || len(req.CdbInstance.Password) == 0 {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "should provide user/password in CloudVPC for exist cdb instance", nil)
	}

	cdbService := cdb.GetCdbService()
	cdbReq := cdbService.NewDefaultDescribeDBInstancesRequestBuilder().
		WithInstancesIds([]string{req.CdbInstance.InstanceId}).
		Build()
	cnt, cdbList, err := cdbService.DescribeDBInstancesWithScsAccount(req.Region, cdbReq)
	if err != nil {
		return nil, err
	} else if cnt != 1 {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("found %d instances for instanceId %s", cnt, req.CdbInstance.InstanceId), nil)
	}

	if !(*cdbList[0].Status == model3.CDB_STATUS_RUNNING && *cdbList[0].TaskStatus == 0 && *cdbList[0].InitFlag == 1) {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("cdb is not in normal status %d, task status %d, initflag %d", *cdbList[0].Status, *cdbList[0].TaskStatus, *cdbList[0].InitFlag), nil)
	}

	return cdbList[0], nil
}

func (o *PrepareCdb) ValidateResourceInInnerVpc(req *model.CreateClusterInternalReq, clusterGroup *table.ClusterGroup, cluster *table.Cluster) (instance interface{}, err error) {
	cdbInst := req.CdbInstance
	if len(cdbInst.Vip) == 0 || cdbInst.Vport == 0 || len(cdbInst.User) == 0 || len(cdbInst.Password) == 0 {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("should provide Vip/Vport/User/Password in InnerVpc"), nil)
	}

	return nil, nil
}

func (o *PrepareCdb) RecordResource(instance interface{}, req *model.CreateClusterInternalReq, clusterGroup *table.ClusterGroup, cluster *table.Cluster, tx *dao.Transaction) (err error) {
	memory, err := service3.GetConfigurationIntValueByKey(constants.CONF_KEY_CDB_MEMORY_MB)
	if err != nil {
		return err
	}
	volume, err := service3.GetConfigurationIntValueByKey(constants.CONF_KEY_CDB_VOLUME_GB)
	if err != nil {
		return err
	}
	vip := req.CdbInstance.Vip
	vport := req.CdbInstance.Vport

	if instance != nil {
		inst := instance.(*cdb2.InstanceInfo)
		vip = *inst.Vip
		vport = int(*inst.Vport)
		memory = *inst.Memory
		volume = *inst.Volume
	}
	rainbowOceanusPasswordEncodeKey, err := common_config.GetRainbowOceanusPasswordEncodeKey()
	if err != nil {
		logger.Errorf("Failed to get oceanus cluster password encode key in Rainbow")
		return err
	}
	password, err := util.EncodePassword(req.CdbInstance.Password, rainbowOceanusPasswordEncodeKey)
	if err != nil {
		return errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	// 	Id	bigint(20) AI PK
	//	CreateTime	timestamp
	//	UpdateTime	timestamp
	//	ClusterId	bigint(20)
	//	InstanceId	varchar(16)
	//	InstanceName	varchar(32)
	//	Vip	varchar(32)
	//	Vport	int(11)
	//	User	varchar(32)
	//	Password	varchar(64)
	//	Memory	bigint(20)
	//	Volume	bigint(20)
	//	Status	int(11)
	tx.ExecuteSqlWithArgs("insert into Cdb(`ClusterId`,`InstanceId`,`InstanceName`,`Vip`,`Vport`, `User`,`Password`,`Memory`,`Volume`,`Status`) VALUES(?,?,?,?,?,?,?,?,?,?) on duplicate key update `InstanceName`=?",
		cluster.Id, req.CdbInstance.InstanceId, clusterGroup.SerialId, vip, vport, req.CdbInstance.User, password, memory, volume, model3.CDB_STATUS_RUNNING, clusterGroup.SerialId)

	return nil
}

// /////////////////////////////////////
func (o *PrepareTke) ResourceReady(req *model.CreateClusterInternalReq, clusterGroup *table.ClusterGroup, cluster *table.Cluster) (ready bool, err error) {
	tkeList, err := service.GetTableService().ListTkeByClusterId(cluster.Id)
	if err != nil {
		return false, err
	}
	if len(tkeList) > 1 {
		return false, errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("there are %d tke instances", len(tkeList)), nil)
	}
	// tke已创建，使用已有的即可
	if len(tkeList) == 1 {
		return true, nil
	}
	return false, nil
}

func (o *PrepareTke) UserProvidedResource(req *model.CreateClusterInternalReq) bool {
	return req.TkeInstance != nil && len(req.TkeInstance.InstanceId) > 0
}

func (o *PrepareTke) ValidateTke(req *model.CreateClusterInternalReq, clusterGroup *table.ClusterGroup, cluster *table.Cluster, secretId, secretKey string) (instance interface{}, err error) {
	tkeService := tke.GetTkeService()
	if totalCount, clusters, err := tkeService.DescribeClusters(secretId, secretKey, "",
		clusterGroup.Region, tkeService.NewDefaultDescribeClustersRequestBuilder().WithClusterIds(req.TkeInstance.InstanceId).Build()); err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	} else if totalCount != 1 {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("tke instance count %d for instanceId %s", totalCount, req.TkeInstance.InstanceId), nil)
	} else if *clusters[0].ClusterStatus != constants.TKE_STATUS_RUNNING_STR {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("tke cluster %s is in abnormal status %s", req.TkeInstance.InstanceId, *clusters[0].ClusterStatus), nil)
	} else {
		return clusters[0], nil
	}
}

func (o *PrepareTke) ValidateEks(req *model.CreateClusterInternalReq, clusterGroup *table.ClusterGroup, cluster *table.Cluster, secretId, secretKey string) (instance interface{}, err error) {
	// TODO: eks集群合法性检测，截至2020/10/14，eks云api尚未发布
	// https://tcloud-dev.oa.com/document/product/457/34042?!preview&!document=1
	tkeService := tke.GetTkeService()
	describeEKSClustersRequest := sdkTke.NewDescribeEKSClustersRequest()
	describeEKSClustersRequest.ClusterIds = common.StringPtrs([]string{req.TkeInstance.InstanceId})
	if totalCount, clusters, err := tkeService.DescribeEKSClusters(secretId, secretKey,
		clusterGroup.Region, describeEKSClustersRequest); err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	} else if totalCount != 1 {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("tke instance count %d for instanceId %s", totalCount, req.TkeInstance.InstanceId), nil)
	} else if *clusters[0].Status != constants.TKE_STATUS_RUNNING_STR && *clusters[0].Status != constants.EKS_STATUS_IDLING_STR {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("tke cluster %s is in abnormal status %s", req.TkeInstance.InstanceId, *clusters[0].Status), nil)
	} else {
		return clusters[0], nil
	}
}

func (o *PrepareTke) ValidateResourceInCloudVpc(req *model.CreateClusterInternalReq, clusterGroup *table.ClusterGroup, cluster *table.Cluster) (instance interface{}, err error) {
	secretId, secretKey, err := service4.GetSecretIdAndKeyOfScs()
	if err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	if req.TkeInstance.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		return o.ValidateEks(req, clusterGroup, cluster, secretId, secretKey)
	} else {
		return o.ValidateTke(req, clusterGroup, cluster, secretId, secretKey)
	}
}

func (o *PrepareTke) ValidateResourceInInnerVpc(req *model.CreateClusterInternalReq, clusterGroup *table.ClusterGroup, cluster *table.Cluster) (instance interface{}, err error) {
	secretId, secretKey, err := service4.GetSecretIdAndKeyOfInner()
	if err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	if req.TkeInstance.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		return o.ValidateEks(req, clusterGroup, cluster, secretId, secretKey)
	} else {
		return o.ValidateTke(req, clusterGroup, cluster, secretId, secretKey)
	}
}

func (o *PrepareTke) RecordResource(instance interface{}, req *model.CreateClusterInternalReq, clusterGroup *table.ClusterGroup, cluster *table.Cluster, tx *dao.Transaction) (err error) {
	instanceId := req.TkeInstance.InstanceId
	//	Id	bigint(20) AI PK
	//	CreateTime	timestamp
	//	UpdateTime	timestamp
	//	ClusterId	bigint(20)
	//	InstanceId	varchar(16)
	//	InstanceName	varchar(32)
	//	ClusterType	int(11)
	// 	WorkerStatus	int(11)
	//	Status	int(11)
	tx.ExecuteSqlWithArgs("insert into Tke(`ClusterId`,`InstanceId`,`InstanceName`,`ClusterType`, `WorkerStatus`, `Status`, `ArchGeneration`) values(?,?,?,?,?,?,?) on duplicate key update `InstanceName`=?", cluster.Id, instanceId, clusterGroup.SerialId, req.TkeInstance.ClusterType, constants.K8S_CLUSTER_WITH_WORKER, constants.TKE_STATUS_RUNNING, req.ArchGeneration, clusterGroup.SerialId)

	return nil
}

// /////////////////////////////////////////////////////////////////////////////
type CreateClusterInternalService struct {
}

func (o *CreateClusterInternalService) GenerateSupportedFeatures(ClusterType int8) []string {
	group := constants.ConfRainbowGroupCommon

	features := make([]string, 0)
	if err := config.DecodeK8sObjectFromRainbowConfig(
		group, constants.ConfRainbowKeyOceanusFullFeatures, &features); err != nil {
		panic(errorcode.InternalErrorCode_DecodeK8sObjectFailed.NewWithErr(err))
	}
	ret := make([]string, 0, len(features))
	for _, f := range features {
		// disable find-grained-resource feature if non-fine grained resource cluster
		if f == constants.FeatureFineGrainedResource && !(ClusterType == constants.K8S_CLUSTER_TYPE_EKS) {
			continue
		}
		ret = append(ret, f)
	}

	return ret
}

func (o *CreateClusterInternalService) prepareResourcesTransaction(req *model.CreateClusterInternalReq, clusterGroup *table.ClusterGroup, cluster *table.Cluster) (err error) {
	defer errorcode.DefaultDeferHandlerWithMess(&err, fmt.Sprintf("prepareResourcesIfAvailable error"))

	service2.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		return o.prepareResourcesIfAvailable(req, clusterGroup, cluster, tx)
	}).Close()

	return nil
}

func (o *CreateClusterInternalService) prepareResource(req *model.CreateClusterInternalReq, clusterGroup *table.ClusterGroup, cluster *table.Cluster, tx *dao.Transaction, impl PrepareResource) (err error) {
	// 1. 检查资源是否已申请，若已经申请，不再记录资源
	if ready, err := impl.ResourceReady(req, clusterGroup, cluster); err != nil {
		return err
	} else if ready {
		return nil
	}

	var (
		instance interface{}
	)

	if req.NetEnvironmentType == constants.NETWORK_ENV_CLOUD_VPC {
		// 现网VPC
		// 没有提供资源，交给创建流程申请。流程可重入：创建流程会处理资源已申请的情况
		if !impl.UserProvidedResource(req) {
			return nil
		}

		// 使用提供的资源，对资源有效性进行检查
		if instance, err = impl.ValidateResourceInCloudVpc(req, clusterGroup, cluster); err != nil {
			return err
		}
	} else {
		// 内网VPC，必须提供资源
		if !impl.UserProvidedResource(req) {
			return errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("InnerVpc: should provide vpc"), nil)
		}

		// 使用提供的资源，对资源有效性进行检查
		if instance, err = impl.ValidateResourceInInnerVpc(req, clusterGroup, cluster); err != nil {
			return err
		}
	}

	return impl.RecordResource(instance, req, clusterGroup, cluster, tx)
}

func (o *CreateClusterInternalService) prepareResourcesIfAvailable(req *model.CreateClusterInternalReq, clusterGroup *table.ClusterGroup, cluster *table.Cluster, tx *dao.Transaction) (err error) {
	prepareResources := []PrepareResource{&PrepareClusterVpc{}, &PrepareCdb{}, &PrepareTke{}}

	wg := sync.WaitGroup{}
	wg.Add(len(prepareResources))
	result := make(chan error, len(prepareResources))

	for _, impl := range prepareResources {
		impl := impl
		go func() {
			defer wg.Done()
			err := o.prepareResource(req, clusterGroup, cluster, tx, impl)
			result <- err
		}()
	}
	go func() {
		wg.Wait()
		close(result)
	}()

	for err := range result {
		if err != nil {
			return err
		}
	}
	return nil
}
