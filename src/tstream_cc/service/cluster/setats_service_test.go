package service

import (
	"encoding/json"
	"fmt"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/setats"
	"testing"
)

func TestGetSetatsInfoByClusterGroupSerialId(t *testing.T) {
	rst, err := GetSetatsInfoByClusterGroupSerialId("cluster-xxxx", 1)
	fmt.Println(rst)
	fmt.Println(err)
}

func TestListSetats(t *testing.T) {
	rst, err := ListSetats(&ListSetatsParam{
		AppId: **********,
		//ClusterGroupSerialIds: []string{"cluster-pgel2z70"},
	})
	fmt.Println(rst)
	fmt.Println(err)
}

func TestModifySetatsWarehouse(t *testing.T) {

	resourceRefs := make([]*setats.ResourceRef, 0)
	resourceRefs = append(resourceRefs, &setats.ResourceRef{
		ResourceId: "a",
		Version:    1,
		Type:       1,
		Status:     1,
	})
	resourceRefs = append(resourceRefs, &setats.ResourceRef{
		ResourceId: "b",
		Version:    1,
		Type:       1,
		Status:     1,
	})
	rst, _ := ModifySetatsWarehouse(&setats.ModifySetatsWarehouseReq{
		RequestBase: apiv3.RequestBase{
			Action:        "",
			Region:        "ap-chengdu",
			Token:         "",
			Version:       "",
			Language:      "",
			Timestamp:     "",
			RequestId:     "",
			AppId:         **********,
			Uin:           "************",
			SubAccountUin: "************",
			ClientIp:      "",
			ApiModule:     "",
			RequestSource: "",
			CamContext:    "",
			AccountArea:   "",
			IsSupOwner:    0,
		},
		ClusterGroupSerialId: "cluster-xxxx",
		SetatsSerialId:       "setats-jjbal8ip",
		Location:             "",
		CatalogType:          "hive",
		Uri:                  "thrift://************:7004,thrift://*************:7004",
		WarehouseUrl:         "hdfs://HDFS1018835/usr/setats",
		Authentication:       "kerberos",
		ResourceRefs:         resourceRefs,
	})
	b, _ := json.Marshal(rst)
	fmt.Println(string(b))
}

func TestDescribeSetatsConfiguration(t *testing.T) {
	rst, _ := DescribeSetatsConfiguration(&setats.DescribeSetatsConfigurationReq{
		RequestBase: apiv3.RequestBase{
			Action:        "",
			Region:        "",
			Token:         "",
			Version:       "",
			Language:      "",
			Timestamp:     "",
			RequestId:     "",
			AppId:         1,
			Uin:           "1",
			SubAccountUin: "",
			ClientIp:      "",
			ApiModule:     "",
			RequestSource: "",
			CamContext:    "",
			AccountArea:   "",
			IsSupOwner:    0,
		},
		ClusterGroupSerialId: "cluster-xxxx",
		SetatsSerialId:       "setats-xxxx",
	})
	b, _ := json.Marshal(rst)
	fmt.Println(string(b))
}

func TestModifySetatsConfiguration(t *testing.T) {
	paramList := make([]*setats.Param, 0)
	for i := 1; i < 3; i++ {
		paramList = append(paramList, &setats.Param{
			ParamName:         fmt.Sprintf("para%d", i),
			ParamValue:        fmt.Sprintf("xxxxfdfdfdsfsdf%d", i),
			ReferenceValue:    fmt.Sprintf("r%d", i),
			ModificationGuide: fmt.Sprintf("m%d", i),
			IsRestart:         1,
		})
	}
	rst, err := ModifySetatsConfiguration(&setats.ModifySetatsConfigurationReq{
		RequestBase: apiv3.RequestBase{
			Action:        "",
			Region:        "",
			Token:         "",
			Version:       "",
			Language:      "",
			Timestamp:     "",
			RequestId:     "",
			AppId:         1,
			Uin:           "1",
			SubAccountUin: "",
			ClientIp:      "",
			ApiModule:     "",
			RequestSource: "",
			CamContext:    "",
			AccountArea:   "",
			IsSupOwner:    0,
		},
		ClusterGroupSerialId: "cluster-xxxx",
		SetatsSerialId:       "setats-xxxx",
		ParamList:            paramList,
	})
	b, _ := json.Marshal(rst)
	fmt.Println(string(b))
	fmt.Println(err)
}
