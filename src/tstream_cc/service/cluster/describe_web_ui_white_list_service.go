package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	clb "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common"
	vpc "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/vpc/v20170312"
	"github.com/juju/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	service4 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/cluster"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/qcloud"
	tke2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/tke"
	vpc2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/vpc"
)

type DescribeWebUIWhiteListService struct {
	request      *model.DescribeWebUIWhiteListReq
	k8sService   *k8s.K8sService
	k8sClient    *kubernetes.Clientset
	clusterGroup *table.ClusterGroup
	cluster      *table.Cluster
}

func NewDescribeWebUIWhiteListService(req *model.DescribeWebUIWhiteListReq) DescribeWebUIWhiteListService {
	return DescribeWebUIWhiteListService{
		request:    req,
		k8sService: k8s.NewK8sService(),
	}
}

func NewDescribeWebUIWhiteListService1(req *model.DescribeWebUIWhiteListReq, k8sClient *kubernetes.Clientset, clusterGroup *table.ClusterGroup, cluster *table.Cluster) DescribeWebUIWhiteListService {
	return DescribeWebUIWhiteListService{
		request:      req,
		k8sService:   k8s.NewK8sService(),
		k8sClient:    k8sClient,
		clusterGroup: clusterGroup,
		cluster:      cluster,
	}
}

// 设置完ingress nginx LB，需要判断是否ready，然后设置密码
func (s *DescribeWebUIWhiteListService) GetPublic() (result bool, err error) {

	ready, err := s.Ready()
	if err != nil {
		return false, err
	}
	if !ready {
		return false, nil
	}
	controllerName := fmt.Sprintf("%s-nginx-ingress-controller", DefaultName(s.clusterGroup))
	// 设置WebUIPrefix为 https://${EXTERNAL-IP}/
	// NAME                                    TYPE           CLUSTER-IP      EXTERNAL-IP   PORT(S)                      AGE
	// ingress-nginx-ingress-controller        LoadBalancer   *************   ***********   80:31079/TCP,443:31060/TCP   18h
	namespace := DefaultNamespace(s.clusterGroup)
	ingService, err := s.k8sClient.CoreV1().Services(namespace).Get(context.TODO(), controllerName, metav1.GetOptions{})
	if err != nil {
		return false, errorcode.InternalErrorCode.NewWithErr(err)
	}

	if len(ingService.Status.LoadBalancer.Ingress) == 0 {
		return false, nil
	}
	webUIPrefix := fmt.Sprintf("https://%s/", ingService.Status.LoadBalancer.Ingress[0].IP)
	service4.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.ExecuteSqlWithArgs("update Cluster set WebUIPrefix=? where Id=?", webUIPrefix, s.cluster.Id)
		return nil
	}).Close()
	return true, nil
}

func (s *DescribeWebUIWhiteListService) GetPrivateIp(clusterGroupId int64) (ip string, err error) {
	ready, err := s.Ready()
	if err != nil {
		return "", err
	}
	if !ready {
		return "", nil
	}
	// 获取弹性网卡ip，设置webui地址.
	// 获取Deployment所关联的Pod信息
	namespace := DefaultNamespace(s.clusterGroup)
	pods, err := s.k8sClient.CoreV1().Pods(namespace).List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		return "", errorcode.FailedOperationCode.NewWithErr(err)
	}

	for _, pod := range pods.Items {
		if val, exists := pod.Labels["app"]; exists && val == "nginx-ingress" {
			logger.Debugf("old ingress pods is %+v", pod)
		} else if val, exists := pod.Labels["app.kubernetes.io/name"]; exists && strings.Contains(val, "nginx-ingress") {
			logger.Debugf("new ingress pods is %+v", pod)
		} else {
			continue
		}
		// 可以根据需要输出更多的Pod信息
		// 例如：pod.Status.Phase, pod.Status.StartTime, pod.Status.PodIP 等
		// 可以根据需要进一步操作Pod，例如获取日志、删除Pod等
		/**
		TKE tke.cloud.tencent.com/networks-status
		[{
			"name": "tke-bridge",
			"interface": "eth0",
			"ips": ["**********"],
			"mac": "fe:dd:b6:6b:f7:29",
			"default": true,
			"dns": {}
		}, {
			"name": "tke-direct-eni",
			"interface": "eth1",
			"ips": ["*************"],
			"mac": "20:90:6F:4F:ED:C5",
			"dns": {}
		}, {
			"name": "tke-route",
			"ips": ["*******"],
			"dns": {}
		}]

		EKS tke.cloud.tencent.com/attached-cross-tenant-eni

		{"eniID":"eni-dat2gtio","mac":"20:90:6F:40:07:82","primaryIP":"***********","subnetCIDR":"**********/24","attachedInsID":"eks-c2eqwv46"}
		*/
		name := fmt.Sprintf("%s-nginx-ingress-controller", DefaultName(s.clusterGroup))
		if !strings.Contains(pod.Name, name) {
			continue
		}

		isEks, err := IsEks(clusterGroupId)
		if err != nil {
			errMsg := fmt.Sprintf("get Cluster Type error %+v.", err)
			logger.Errorf(errMsg)
			return "", errorcode.UnsupportedOperationCode.ReplaceDesc(errMsg)
		}
		if !isEks {
			strNetworksStatus, exist := pod.Annotations["tke.cloud.tencent.com/networks-status"]
			if !exist {
				errMsg := fmt.Sprintf("Pod %s can't find tke.cloud.tencent.com/networks-status, please check.", pod.Name)
				logger.Errorf(errMsg)
				return "", errorcode.UnsupportedOperationCode.ReplaceDesc(errMsg)
			}
			logger.Debugf("tke.cloud.tencent.com/networks-status info is %s", strNetworksStatus)
			ip, err = ParseIp(strNetworksStatus, s.clusterGroup.NetEnvironmentType)
			if err != nil {
				return "", errorcode.FailedOperationCode.NewWithErr(err)
			}
			break
		} else {
			attachedCrossTenantEni, exist := pod.Annotations["tke.cloud.tencent.com/attached-cross-tenant-eni"]
			if !exist {
				errMsg := fmt.Sprintf("Pod %s can't find tke.cloud.tencent.com/attached-cross-tenant-eni, please check.", pod.Name)
				logger.Errorf(errMsg)
				return "", errorcode.UnsupportedOperationCode.ReplaceDesc(errMsg)
			}
			logger.Debugf("tke.cloud.tencent.com/attached-cross-tenant-eni info is %s", attachedCrossTenantEni)
			ip, err = ParseEksIp(attachedCrossTenantEni)
			if err != nil {
				return "", errorcode.FailedOperationCode.NewWithErr(err)
			}
			break
		}
	}
	return ip, nil
}

// 获取nginx-ingress-controller弹性网卡地址
func (s *DescribeWebUIWhiteListService) GetPrivate(clusterGroupId int64) (result bool, err error) {
	ip, err := s.GetPrivateIp(clusterGroupId)
	if err != nil {
		return false, err
	}
	logger.Info("GetPrivate ip is %s", ip)
	if ip == "" {
		return false, nil
	}

	// 设置webui地址
	webUIPrefix := fmt.Sprintf("https://%s/", ip)
	service4.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.ExecuteSqlWithArgs("update Cluster set WebUIPrefix=? where Id=?", webUIPrefix, s.cluster.Id)
		return nil
	}).Close()
	return true, nil
}

func (s *DescribeWebUIWhiteListService) GetCrossTenantEni(clusterGroupId int64) (result bool, err error) {
	ip, err := s.GetPrivateIp(clusterGroupId)
	if err != nil {
		logger.Errorf("GetPrivateIp error %+v", err)
		return false, err
	}
	if ip == "" {
		logger.Info("GetPrivateIp ip is empty")
		return false, nil
	}

	// 设置webui地址
	webUIPrefix := fmt.Sprintf("https://%s/", ip)
	service4.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.ExecuteSqlWithArgs("update Cluster set WebUIPrefix=? where Id=?", webUIPrefix, s.cluster.Id)
		return nil
	}).Close()
	return true, nil
}

func (s *DescribeWebUIWhiteListService) Ready() (bool, error) {
	namespace := DefaultNamespace(s.clusterGroup)
	name := fmt.Sprintf("%s-nginx-ingress-controller", DefaultName(s.clusterGroup))

	deploy, err := s.k8sService.GetDeploymentAppsV1(s.k8sClient, namespace, name)
	if err != nil {
		logger.Errorf("Ready DeploymentAppsV1Ready error, %+v", err)
		return false, err
	}

	ready, err := s.k8sService.DeploymentAppsV1Ready(s.k8sClient, deploy)
	if err != nil {
		logger.Errorf("Ready DeploymentAppsV1Ready error, %+v", err)
		return false, err
	}
	return ready, err
}

func ParseEksIp(attachedCrossTenantEni string) (string, error) {
	attachedCrossTenantEniList := make(map[string]interface{}, 0)
	err := json.Unmarshal([]byte(attachedCrossTenantEni), &attachedCrossTenantEniList)
	logger.Debugf("after Unmarshal  %+v", attachedCrossTenantEniList)
	if err != nil {
		logger.Errorf("json.Unmarshal([]byte(%s), map[string]interface{}) error, %+v", attachedCrossTenantEni, err)
		return "", errorcode.FailedOperationCode.NewWithErr(err)
	}
	if _, exist := attachedCrossTenantEniList["primaryIP"]; !exist {
		logger.Error("attachedCrossTenantEniList primaryIP does not exist")
		return "", errorcode.FailedOperationCode.NewWithMsg("attachedCrossTenantEniList primaryIP does not exist")
	}
	return attachedCrossTenantEniList["primaryIP"].(string), nil
}

func ParseIp(strNetworksStatus string, netEnvironmentType int8) (string, error) {
	ip := ""
	networksStatusList := make([]map[string]interface{}, 0, 0)
	err := json.Unmarshal([]byte(strNetworksStatus), &networksStatusList)
	logger.Debugf("after Unmarshal  %+v", networksStatusList)
	if err != nil {
		logger.Errorf("json.Unmarshal([]byte(%s), []map[string]interface{}) error, %+v", strNetworksStatus, err)
		return "", errorcode.FailedOperationCode.NewWithErr(err)
	}
	for _, networksStatus := range networksStatusList {
		logger.Debugf("networksStatus is %+v", networksStatus)
		if _, exist := networksStatus["name"]; !exist {
			continue
		}

		if netEnvironmentType == constants.NETWORK_ENV_CLOUD_VPC && networksStatus["name"] != "tke-direct-eni" {
			continue
		}
		if _, exist := networksStatus["ips"]; !exist {
			continue
		}
		ips := networksStatus["ips"]
		ipList, ok := ips.([]interface{})
		if !ok {
			continue
		}
		if len(ipList) < 1 {
			continue
		}
		ip = ipList[0].(string)
		break
	}
	return ip, nil
}

// DoSyncWebUIPrefix 同步WebUIPrefix
func (s *DescribeWebUIWhiteListService) DoSyncWebUIPrefix(cluster *table.Cluster) (result bool, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Error("[DoSyncWebUIPrefix] error:", errs)
			err = errors.Cause(fmt.Errorf("%+v", errs))
			return
		}
	}()
	rsp := true
	if cluster.WebUIType == constants.PrivateType {
		s.clusterGroup, err = GetClusterGroupBySerialId(s.request.ClusterId)
		if err != nil {
			return false, err
		}
		s.cluster = cluster
		s.k8sClient, err = tke2.GetTkeService().KubernetesClientsetFromCluster("DoSyncWebUIPrefix", s.cluster)
		if err != nil {
			return false, err
		}
		rsp, err = s.GetPrivate(s.clusterGroup.Id)
		if err != nil {
			return false, err
		}
	}

	if cluster.WebUIType == constants.PublicType {
		s.clusterGroup, err = GetClusterGroupBySerialId(s.request.ClusterId)
		if err != nil {
			return false, err
		}
		s.cluster = cluster
		s.k8sClient, err = tke2.GetTkeService().KubernetesClientsetFromCluster("DoSyncWebUIPrefix", s.cluster)
		if err != nil {
			return false, err
		}
		rsp, err = s.GetPublic()

		if s.cluster.CrossTenantEniMode == constants.EnableStaticMode && s.cluster.WebUIType == constants.PublicType {
			flinkVersionService := NewFlinkUiClbService(s.k8sClient, s.clusterGroup, s.cluster)
			err = flinkVersionService.BindFlinkUiClb()
			if err != nil {
				logger.Errorf("Failed to BindFlinkUiClb,err: %v", err.Error())
				return rsp, errorcode.FailedOperationCode.NewWithErr(err)
			}
		}

		if err != nil {
			return false, err
		}
	}
	return rsp, err
}

func (s *DescribeWebUIWhiteListService) DescribeWebUIWhiteList() (rsp *model.DescribeWebUIWhiteListRsp, err error) {

	defer func() {
		if errs := recover(); errs != nil {
			logger.Error("[DescribeWebUIWhiteList] error:", errs)
			err = errors.Cause(fmt.Errorf("%+v", errs))
			return
		}
	}()

	rsp = &model.DescribeWebUIWhiteListRsp{
		CidrBlocks: CidrBlocksEmpty,
	}

	s.clusterGroup, err = GetClusterGroupBySerialId(s.request.ClusterId)
	if err != nil {
		return rsp, errorcode.FailedOperationCode.NewWithErr(err)
	}
	if int64(s.clusterGroup.AppId) != s.request.AppId || s.clusterGroup.OwnerUin != s.request.Uin {
		return rsp, errorcode.FailedOperationCode.ReplaceDesc(fmt.Sprintf("The appid %d or uin %s has no operation permission.", s.request.AppId, s.request.Uin))
	}

	s.cluster, err = GetActiveClusterByClusterGroupId(s.clusterGroup.Id)
	if err != nil {
		return rsp, errorcode.FailedOperationCode.NewWithErr(err)
	}

	s.k8sClient, err = tke2.GetTkeService().KubernetesClientsetFromCluster("DescribeWebUIWhiteListService", s.cluster)
	if err != nil {
		return rsp, errorcode.FailedOperationCode.NewWithErr(err)
	}
	// 新集群都是内网ip，获取的是购买的flink clb vpc和ip或者用户vpc ip
	if s.cluster.CrossTenantEniMode == constants.EnableStaticMode {
		result, err := s.GetPrivate(s.clusterGroup.Id)
		if err != nil {
			return rsp, errorcode.FailedOperationCode.NewWithErr(err)
		}
		// 修改公网的时候不能及时获取ingress ip，只能查询的时候绑定
		if s.request.Type == constants.PublicType && s.cluster.WebUIType == constants.PublicType {
			flinkVersionService := NewFlinkUiClbService(s.k8sClient, s.clusterGroup, s.cluster)
			err = flinkVersionService.BindFlinkUiClb()
			if err != nil {
				logger.Errorf("Failed to BindFlinkUiClb,err: %v", err.Error())
				return rsp, errorcode.FailedOperationCode.NewWithErr(err)
			}
		}
		rsp.Result = result
		return rsp, nil
	}

	// 获取最新内网ip
	if s.request.Type == constants.PrivateType {
		if s.cluster.WebUIType == constants.PrivateType {
			result, err := s.GetPrivate(s.clusterGroup.Id)
			if err != nil {
				return rsp, errorcode.FailedOperationCode.NewWithErr(err)
			} else {
				rsp.Result = result
				return rsp, nil
			}
		} else {
			rsp.Result = true
			return rsp, nil
		}
	}
	// 获取最新公网ip
	if s.request.Type == constants.PublicType {
		if s.cluster.WebUIType == constants.PublicType {
			result, err := s.GetPublic()
			if err != nil {
				return rsp, errorcode.FailedOperationCode.NewWithErr(err)
			}
			if !result {
				rsp.Result = result
				return rsp, nil
			}
		}
	}

	rsp.Result = true
	// 外网获取安全组
	loadBalancerId, err := GetLoadBalancerId(s.request.ClusterId)
	if err != nil {
		logger.Errorf("[%s] Failed to get load balancer ,err: %v", s.request.RequestId, err.Error())
		return rsp, errorcode.FailedOperationCode.NewWithErr(err)
	}
	secretId, secretKey, err := service3.GetSecretIdAndKeyByNetworkEnvType(s.clusterGroup.NetEnvironmentType)
	if err != nil {
		return rsp, err
	}
	prof := qcloud.NewClientProfile()
	// 上海自动驾驶云专区走内网
	if s.request.Region == constants.AP_SHANGHAI_ADC {
		prof.HttpProfile.Endpoint = constants.ClbApiDomainInternal
	}
	credential := common.NewTokenCredential(secretId, secretKey, "")
	clbClient, err := clb.NewClient(credential, s.request.Region, prof)
	if err != nil {
		logger.Errorf("[%s] Failed to NewClient[clb] ,err: %v", s.request.RequestId, err.Error())
		return rsp, errorcode.FailedOperationCode.NewWithErr(err)
	}
	describeLoadBalancersReq := clb.NewDescribeLoadBalancersRequest()
	describeLoadBalancersReq.LoadBalancerIds = append(describeLoadBalancersReq.LoadBalancerIds, &loadBalancerId)
	describeLoadBalancersRsp, err := clbClient.DescribeLoadBalancers(describeLoadBalancersReq)
	if err != nil {
		logger.Errorf("[%s] Failed to DescribeLoadBalancers ,err: %v", s.request.RequestId, err.Error())
		return rsp, errorcode.FailedOperationCode.NewWithErr(err)
	}
	loadBalancerSet := describeLoadBalancersRsp.Response.LoadBalancerSet
	if len(loadBalancerSet) == 0 {
		return rsp, errorcode.FailedOperationCode.ReplaceDesc("loadBalancerSet is empty,load balancer id " + loadBalancerId)
	}
	loadBalancer := loadBalancerSet[0]
	var webSecurityGroupBound = len(loadBalancer.SecureGroups) > 0
	if webSecurityGroupBound {
		describeSecurityGroupsReq := vpc.NewDescribeSecurityGroupsRequest()
		for _, securityGroup := range loadBalancer.SecureGroups {
			describeSecurityGroupsReq.SecurityGroupIds = append(describeSecurityGroupsReq.SecurityGroupIds, securityGroup)
		}
		vpcService := vpc2.NewVpcService()
		securityGroups, err := vpcService.DescribeSecurityGroups(s.request.Region, s.clusterGroup.NetEnvironmentType, describeSecurityGroupsReq)
		if err != nil {
			logger.Errorf("[%s] Failed to DescribeSecurityGroups ,err: %v", s.request.RequestId, err.Error())
			return rsp, errorcode.FailedOperationCode.NewWithErr(err)
		}
		webSecurityGroup, webSecurityGroupFound := FindSecurityGroup(s.request.ClusterId, securityGroups)
		if webSecurityGroupFound {
			describeSecurityGroupPoliciesReq := vpc.NewDescribeSecurityGroupPoliciesRequest()
			describeSecurityGroupPoliciesReq.SecurityGroupId = &webSecurityGroup
			describeSecurityGroupPoliciesRsp, err := vpcService.DescribeSecurityGroupPolicies(s.request.Region, s.clusterGroup.NetEnvironmentType, describeSecurityGroupPoliciesReq)
			if err != nil {
				logger.Errorf("[%s] Failed to DescribeSecurityGroupPolicies ,err: %v", s.request.RequestId, err.Error())
				return rsp, errorcode.FailedOperationCode.NewWithErr(err)
			}
			if len(describeSecurityGroupPoliciesRsp.Ingress) == 0 {
				return rsp, nil
			}
			var cidrBlocks []string
			for _, ingress := range describeSecurityGroupPoliciesRsp.Ingress {
				cidrBlocks = append(cidrBlocks, *ingress.CidrBlock)
			}
			cidrBlocksStr := strings.Join(cidrBlocks, ",")
			rsp.CidrBlocks = cidrBlocksStr
		}
	}
	return rsp, nil
}
