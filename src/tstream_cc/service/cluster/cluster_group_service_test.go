package service

import (
	"encoding/json"
	"testing"
)

func getClusterGroupService(t *testing.T, serialid string) *ClusterGroupService {
	clusterGroupService, err := NewClusterGroupServiceBySerialId(serialid)
	if err != nil {
		t.Fatal(err)
	}
	return clusterGroupService
}

func getNormalClusterGroupService(t *testing.T) *ClusterGroupService {
	return getClusterGroupService(t, "cluster-2ozn4nan")
}

func getVpcXClusterGroupService(t *testing.T) *ClusterGroupService {
	return getClusterGroupService(t, "cluster-rgfpvkr5")
}

func getNotAutoDeployCluster(t *testing.T) *ClusterGroupService {
	return getClusterGroupService(t, "cluster-do5mj8uw")
}

func logObjectJson(t *testing.T, info string, o interface{}) {
	b, _ := json.MarshalIndent(o, "", "")
	t.Logf("%s %s", info, string(b))
}

func TestNewClusterGroupService(t *testing.T) {
	clusterGroupService, err := NewClusterGroupService(1094)
	if err != nil {
		t.Fatal(err)
	}

	if clusterGroupService != nil {
		t.Logf("cluster 1094 find")
	} else {
		t.Fatal()
	}

	clusterGroupService, err = NewClusterGroupService(-1)
	if clusterGroupService == nil {
		t.Logf("cluster -1 not find")
	} else {
		t.Fatal()
	}
}

func TestNewClusterGroupServiceBySerialId(t *testing.T) {
	clusterGroupService, err := NewClusterGroupServiceBySerialId("cluster-dguh6gcs")
	if err != nil {
		t.Fatal(err)
	}

	if clusterGroupService != nil {
		t.Logf("cluster 1094 find")
	} else {
		t.Fatal()
	}

	clusterGroupService, err = NewClusterGroupServiceBySerialId("cluster-not-exists")
	if err != nil {
		t.Log(err)
	} else {
		t.Fatal()
	}
}

func TestClusterGroupService_GetClusterGroup(t *testing.T) {
	clusterGroupService := getNormalClusterGroupService(t)
	logObjectJson(t, "cluster group: ", clusterGroupService.GetClusterGroup())
}

func TestClusterGroupService_GetClusterList(t *testing.T) {
	clusterGroupService := getNormalClusterGroupService(t)
	list, err := clusterGroupService.GetClusterList()
	if err != nil {
		t.Fatal(err)
	}
	logObjectJson(t, "cluster list: ", list)
}

func TestClusterGroupService_GetTkeList(t *testing.T) {
	clusterGroupService := getNormalClusterGroupService(t)

	list, err := clusterGroupService.GetTkeList()
	if err != nil {
		t.Fatal(err)
	}
	logObjectJson(t, "tke list: ", list)
}

func TestClusterGroupService_GetCdbList(t *testing.T) {
	clusterGroupService := getNormalClusterGroupService(t)

	list, err := clusterGroupService.GetCdbList()
	if err != nil {
		t.Fatal(err)
	}
	logObjectJson(t, "cdb list: ", list)
}

func TestClusterGroupService_GetEmrList(t *testing.T) {
	clusterGroupService := getNormalClusterGroupService(t)

	list, err := clusterGroupService.GetEmrList()
	if err != nil {
		t.Fatal(err)
	}
	logObjectJson(t, "emr list: ", list)
}

func TestClusterGroupService_CanDelete(t *testing.T) {
	clusterGroupService := getNormalClusterGroupService(t)

	can, err := clusterGroupService.CanDelete()
	if err != nil {
		t.Fatal(err)
	}
	if !can {
		t.Fatal("cluster can't delete")
	}

	vpcxClusterGroup := getVpcXClusterGroupService(t)
	can, err = vpcxClusterGroup.CanDelete()
	if err == nil && can {
		t.Fatal("should can delete")
	}
	t.Log(err)

	notAutoDeployCluster := getNotAutoDeployCluster(t)
	can, err = notAutoDeployCluster.CanDelete()
	if err == nil && can {
		t.Fatal("should can delete")
	}
	t.Log(err)
}

func TestClusterGroupService_MarkDeleting(t *testing.T) {
	clusterGroupService := getNormalClusterGroupService(t)
	err := clusterGroupService.MarkDeleting()
	if err != nil {
		t.Fatal(err)
	}
	tmp := getNormalClusterGroupService(t)
	logObjectJson(t, "new cluster group: ", tmp.GetClusterGroup())
	list, err := tmp.GetClusterList()
	if err != nil {
		t.Fatal(err)
	}
	logObjectJson(t, "new cluster list: ", list)
}

func TestClusterGroupService_MarkDeleted(t *testing.T) {
	clusterGroupService := getNormalClusterGroupService(t)
	err := clusterGroupService.MarkDeleted()
	if err != nil {
		t.Fatal(err)
	}

	tmp := getNormalClusterGroupService(t)
	logObjectJson(t, "new cluster group: ", tmp.GetClusterGroup())
	list, err := tmp.GetClusterList()
	if err != nil {
		t.Fatal(err)
	}
	logObjectJson(t, "new cluster list: ", list)

	tkeList, err := tmp.GetTkeList()
	if err != nil {
		t.Fatal(err)
	}
	logObjectJson(t, "new tke list: ", tkeList)

	emr, err := tmp.GetEmrList()
	if err != nil {
		t.Fatal(err)
	}
	logObjectJson(t, "new tke list: ", emr)

	cdb, err := tmp.GetCdbList()
	if err != nil {
		t.Fatal(err)
	}
	logObjectJson(t, "new tke list: ", cdb)
}
