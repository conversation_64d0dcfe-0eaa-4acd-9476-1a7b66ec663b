package service

import (
	"encoding/json"
	"fmt"
	commandc "tencentcloud.com/tstream_galileo/src/common/command"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/common/uuid"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/log"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cluster_admin_protocol"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
)

const (
	clusterSchedulerUrl = "http://cluster-scheduler-service.oceanus:9020/interface"
	forward_credential  = 1
	forwardCredential   = "qcloud.clustermaster.credential.forward"
)

func SendClusterCredentialRequest(req *model.SendClusterCredentialReq) (rep *model.SendClusterCredentialRsp, err error) {
	rep = &model.SendClusterCredentialRsp{}
	serialIds := make([]string, 0)
	if req.SerialId != "" {
		serialIds = append(serialIds, req.SerialId)
	}
	successSerialIds, errorSerialIds, err := SendClusterCredential(serialIds)
	return &model.SendClusterCredentialRsp{SerialIds: successSerialIds, ErrorSerialIds: errorSerialIds}, err
}

func SendClusterCredential(serialIds []string) (successSerialIds, errorSerialIds []*string, err error) {
	successSerialIds = make([]*string, 0)
	errorSerialIds = make([]*string, 0)

	credentialInfoList, err := DescribeV4ClusterInfoAndClsAuthInfo(serialIds)
	if err != nil {
		return
	}

	for _, ci := range credentialInfoList {
		requestId := uuid.NewRandom().String()
		serialId := ci.ClusterGroupSerialId
		if sErr := SendCredential(requestId, ci); sErr != nil {
			logger.Errorf("requestId %s, Fail send credential for %s to cluster-scheduler, %+v", requestId, serialId, sErr)
			errorSerialIds = append(errorSerialIds, &serialId)
			continue
		}
		successSerialIds = append(successSerialIds, &serialId)
	}
	return
}

func SendCredential(requestId string, credentialInfo *log.CredentialInfo) (err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("requestId: %s, %s: Send Credential panic, errors:%+v", requestId, credentialInfo.ClusterGroupSerialId, errs)
			if e, ok := errs.(error); ok {
				err = e
			} else {
				err = fmt.Errorf("error for %+v", errs)
			}
		}
	}()

	logger.Debugf("begin send SendCredential to %s", credentialInfo.ClusterGroupSerialId)

	commandService, err := commandc.NewCommandService(credentialInfo.ClusterId)
	if err != nil || commandService == nil {
		return fmt.Errorf("error can't get the commandService from clusterId %d", credentialInfo.ClusterId)
	}
	url := clusterSchedulerUrl
	zkPass := ""
	if credentialInfo.ZkPass != "" {
		zkPass, err = util.AesDecrypt(credentialInfo.ZkPass, constants.AES_ENCRYPT_KEY)
		if err != nil {
			logger.Errorf("error can't decode the zkPass, use error %+v", err)
			zkPass = credentialInfo.ZkPass
		}
	}
	para := &SendCommand2CsReq{
		Action:          forward_credential,
		ClusterId:       credentialInfo.ClusterId,
		ClusterSerialId: credentialInfo.ClusterGroupSerialId,
		Params: sendCredentialParam{
			RequestId:     requestId,
			Credential:    *credentialInfo.Credential,
			ClusterType:   credentialInfo.ClusterType,
			AgentSerialId: credentialInfo.AgentSerialId,
			ZkUser:        credentialInfo.ZkUser,
			ZkPass:        zkPass,
		},
	}
	caReq := cluster_admin_protocol.NewClusterAdminReq(requestId, forwardCredential, para)
	sendData, err := json.Marshal(caReq)
	rsp, err := commandService.DoRequest(&commandc.CommandRequest{
		ReqId:    requestId,
		Url:      url,
		SendData: string(sendData),
		Uin:      "",
		Apikey:   "sendCredential2ClusterScheduler",
		Timeout:  getTimeout(),
		Callback: "",
	})
	if err != nil {
		return err
	}
	csRsp := &SendCredential2CsRsp{}
	err = json.Unmarshal([]byte(rsp), csRsp)
	if err != nil {
		return err
	} else if csRsp.Data.ReturnCode != 0 {
		return fmt.Errorf("%v", csRsp)
	}
	notifySendCredentialSuccess(credentialInfo.ClusterGroupSerialId)
	return
}

func notifySendCredentialSuccess(serialId string) {
	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		sql, args := dao.NewUpdateBuilder("UPDATE ClusterGroup").
			Set("SendCredentialTime", util.GetCurrentTime()).
			WhereEq("SerialId", serialId).
			Limit(1).
			Build()

		service2.TExecute(tx, sql, args)
		return nil
	}).Close()
}

func DescribeV4ClusterInfoAndClsAuthInfo(serialIds []string) (credentialInfoList []*log.CredentialInfo, err error) {
	credentialInfoList, err = GetV4ClusterInfo(serialIds)
	if err != nil {
		return nil, err
	}
	secretId, secretKey, err := service3.GetSecretIdAndKey()
	if err != nil {
		logger.Errorf("DescribeClsAuthInfo GetSecretIdAndKey error: %+v", err)
		return nil, err
	}

	for _, credentialInfo := range credentialInfoList {

		policy := ""
		if credentialInfo.ClusterType == constants.CLUSTER_GROUP_TYPE_UNIFORM {
			policy, err = config.GetRainbowConfiguration("Common", "UniformAssumePolicy.json")
			if err != nil {
				logger.Error("GetRainbowConfiguration err :",
					credentialInfo.ClusterGroupSerialId, credentialInfo.OwnerUin, credentialInfo.Region, err)
				continue
			}
		} else {
			//policy, err = config.GetRainbowConfiguration("Common", "PrivateAssumePolicy.json")
			//if err != nil {
			//	logger.Error("GetRainbowConfiguration err :",
			//		credentialInfo.ClusterGroupSerialId, credentialInfo.OwnerUin, credentialInfo.Region, err)
			//	continue
			//}
		}

		tmpSecretId, tmpSecretKey, token, expiredTime, err := service3.StsAssumeRoleWithAuth(secretId, secretKey,
			credentialInfo.OwnerUin, credentialInfo.OwnerUin, credentialInfo.Region, constants.ASSUME_ROLE_EXPIRED_DURATION, policy)
		if err != nil {
			logger.Error("StsAssumeRoleWithAuth err :",
				credentialInfo.ClusterGroupSerialId, credentialInfo.OwnerUin, credentialInfo.Region, err)
			continue
		} else {
			credential := &log.Credential{
				SecretId:    tmpSecretId,
				SecretKey:   tmpSecretKey,
				Token:       token,
				ExpiredTime: expiredTime,
			}
			credentialInfo.Credential = credential
		}
	}

	return credentialInfoList, err
}

func GetV4ClusterInfo(serialIds []string) ([]*log.CredentialInfo, error) {
	sql := "select cg.ZkUser as ZkUser, cg.ZkPass as ZkPass, cg.AgentSerialId as AgentSerialId, cg.Type as ClusterType, cg.SerialId as ClusterGroupSerialId, cg.OwnerUin as OwnerUin, " +
		"cg.Region as Region, c.Id as ClusterId from ClusterGroup cg left join Cluster c on c.ClusterGroupId = cg.Id left join Tke t on t.ClusterId = c.Id "

	cond := dao.NewCondition().
		Gt("cg.Status", 1).
		Gte("t.ArchGeneration", 3)

	if len(serialIds) > 0 {
		cond.In("cg.SerialId", serialIds)
	}

	where, args := cond.GetWhere()

	sql = sql + where

	cnt, data, err := service.GetTxManager().GetQueryTemplate().DoQueryWithArgs(sql, args...)
	if err != nil {
		return nil, err
	}
	credentialInfoList := make([]*log.CredentialInfo, 0)
	for i := 0; i < cnt; i++ {
		credentialInfo := &log.CredentialInfo{}
		err = util.ScanMapIntoStruct(credentialInfo, data[i])
		if err != nil {
			return nil, errorcode.InternalErrorCode_UnMarshalFailed.NewWithErr(err)
		}
		credentialInfoList = append(credentialInfoList, credentialInfo)
	}
	logger.Info("current has %d clusters version >= 3", cnt)
	return credentialInfoList, nil
}

type SendCommand2CsReq struct {
	Action          int64               `json:"action"`
	ClusterId       int64               `json:"clusterId"`
	ClusterSerialId string              `json:"clusterSerialId"`
	Params          sendCredentialParam `json:"params"`
}

type sendCredentialParam struct {
	RequestId     string         `json:"requestId"`
	Credential    log.Credential `json:"credential"`
	ClusterType   int64          `json:"clusterType"`
	AgentSerialId string         `json:"agentSerialId"`
	ZkUser        string         `json:"zkUser"`
	ZkPass        string         `json:"zkPass"`
}

type SendCredential2CsRsp struct {
	cluster_admin_protocol.QaeResponse
	Data struct {
		cluster_admin_protocol.ClusterAdminResponse
	} `json:"data"`
}
