package service

import (
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/dlocker"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	model1 "tencentcloud.com/tstream_galileo/src/tstream_cc/model"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/cluster"
	model5 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/setats"
	setats2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/setats"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/tag"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config/common_config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource_auth"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
	tag2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/tag"
)

const (
	SetatsParamsKey         = "SetatsParams"
	SetatsGroup             = "ConfigureCenter.Flow.K8S.Setats"
	SetatsParamActive       = 1
	SetatsParamInActive     = -1
	SetatsWarehouseInActive = 0
	SetatsWarehouseStarting = 1
	SetatsWarehouseActive   = 2
)

func DescribeSetatsConfiguration(req *setats.DescribeSetatsConfigurationReq) (*setats.DescribeSetatsConfigurationRsp, error) {
	err := CheckSetatsParam(req.ClusterGroupSerialId, req.SetatsSerialId, req.AppId, req.Uin)
	if err != nil {
		logger.Errorf("Failed to check setats param, with errors:%+v", err)
		return &setats.DescribeSetatsConfigurationRsp{}, err
	}
	result := &setats.DescribeSetatsConfigurationRsp{}

	params, err := ListParamBySetatsSerialId(req.SetatsSerialId)
	if err != nil {
		logger.Errorf("Failed to list param by setats serial id:%s, with errors:%+v", req.SetatsSerialId, err)
		return &setats.DescribeSetatsConfigurationRsp{}, err
	}
	if len(params) == 0 {
		setatsParams := []*setats.Param{}
		// 如果没有参数，从七彩石获取
		// 检查是否存在不合法的参数
		err = config.DecodeK8sObjectFromRainbowConfig(SetatsGroup, SetatsParamsKey, &setatsParams)
		if err != nil {
			logger.Errorf("Failed to get setats params from rainbow config, with errors:%+v", err)
			return &setats.DescribeSetatsConfigurationRsp{}, err
		}
		result.ParamList = setatsParams
		return result, nil
	}
	for _, param := range params {
		setatsParam := &setats.Param{
			ParamName:         param.ParamName,
			ParamValue:        param.ParamValue,
			ReferenceValue:    param.ReferenceValue,
			ModificationGuide: param.ModificationGuide,
			IsRestart:         param.IsRestart,
		}
		result.ParamList = append(result.ParamList, setatsParam)
	}
	return result, nil
}

func ModifySetatsWarehouse(req *setats.ModifySetatsWarehouseReq) (*setats.ModifySetatsWarehouseRsp, error) {

	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("ModifySetatsWarehouse panic, with errors:%+v", errs)
			return
		}
	}()
	err := CheckSetatsParam(req.ClusterGroupSerialId, req.SetatsSerialId, req.AppId, req.Uin)
	if err != nil {
		logger.Errorf("Failed to check setats param, with errors:%+v", err)
		return &setats.ModifySetatsWarehouseRsp{}, err
	}
	clusterGroup, err := GetClusterGroupBySerialId(req.ClusterGroupSerialId)
	if err != nil {
		logger.Errorf("Failed to GetClusterGroupBySerialId for %s because %+v", req.ClusterGroupSerialId, err)
		return &setats.ModifySetatsWarehouseRsp{}, err
	}
	cluster, err := GetActiveClusterByClusterGroupId(clusterGroup.Id)
	if err != nil {
		logger.Errorf("Failed to GetActiveClusterByClusterGroupId for %d because %+v", clusterGroup.Id, err)
		return &setats.ModifySetatsWarehouseRsp{}, err
	}

	count, setatsWarehouse, err := GetSetatsWarehouseBySerialId(req.SetatsSerialId)
	if err != nil {
		logger.Errorf("Failed to GetSetatsWarehouseBySerialId for %s because %+v", req.SetatsSerialId, err)
		return &setats.ModifySetatsWarehouseRsp{}, err
	}

	// 7. 生成并检验 resource url & resource ref
	setatsRefs := make([]*setats2.SetatsRef, 0)
	for _, ref := range req.ResourceRefs {
		resourceConfig, err := GetResourceConfigByResourceRef(&model5.ResourceRefItem{
			ResourceId: ref.ResourceId,
			Type:       ref.Type,
			Version:    ref.Version,
		})
		if err != nil {
			logger.Errorf("[%s] ModifySetatsWarehouse Failed to GetResourceConfigByResourceRef,error: %+v", req.RequestId, err)
			return &setats.ModifySetatsWarehouseRsp{}, err
		}

		setatsRefs = append(setatsRefs, &setats2.SetatsRef{
			ResourceId: resourceConfig.ResourceId,
			Version:    ref.Version,
			Status:     constants.RESOURCE_REF_STATUS_ACTIVE,
			Type:       ref.Type,
			CreateTime: util.GetCurrentTime(),
			UpdateTime: util.GetCurrentTime(),
		})
	}

	newSetatsWarehouse := &setats2.SetatsWarehouse{
		SetatsSerialId:  req.SetatsSerialId,
		CatalogType:     req.CatalogType,
		Uri:             req.Uri,
		WarehouseUrl:    req.WarehouseUrl,
		Authentication:  req.Authentication,
		Status:          SetatsWarehouseInActive,
		Location:        req.Location,
		CreateTime:      util.GetCurrentTime(),
		HiveUri:         req.HiveUri,
		HiveCatalogType: req.HiveCatalogType,
	}
	properties := "[]"
	if req.Properties != nil && len(req.Properties) > 0 {
		_properties, err := json.Marshal(req.Properties)
		if err != nil {
			logger.Errorf("[%s] Failed to json.Marshal req.Properties(%+v),error: %+v", req.RequestId, req.Properties, err)
			return nil, err
		}
		properties = string(_properties)
	}
	newSetatsWarehouse.Properties = properties

	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {

		if count == 0 {
			tx.SaveObject(newSetatsWarehouse, "SetatsWarehouse")
		} else {
			newSetatsWarehouse.Id = setatsWarehouse.Id
			tx.UpdateObject(newSetatsWarehouse, newSetatsWarehouse.Id, "SetatsWarehouse")
			// delete clusterSessionRefs
			deleteClusterSessionRefs := "UPDATE SetatsRef SET Status = ? WHERE SetatsSerialId = ? "
			tx.ExecuteSqlWithArgs(deleteClusterSessionRefs, constants.RESOURCE_REF_STATUS_DELETE, newSetatsWarehouse.SetatsSerialId)
		}
		if len(setatsRefs) > 0 {
			// save clusterSessionRefs
			for _, setatsRef := range setatsRefs {
				setatsRef.SetatsSerialId = newSetatsWarehouse.SetatsSerialId
				tx.SaveObject(setatsRef, "SetatsRef")
			}
		}
		tx.ExecuteSqlWithArgs("update Setats set Status=? where ClusterGroupSerialId=? and Status != -2", constants.SETATS_WAREHOOUSE_NOT_INITING, clusterGroup.SerialId)
		tx.ExecuteSqlWithArgs("update SetatsWarehouse set Status=? where SetatsSerialId=?", SetatsWarehouseStarting, req.SetatsSerialId)

		flowId, err := flow.CreateFlow(constants.FLOW_OCEANUS_START_SETATS_WAREHOUSE,
			fmt.Sprintf("%s@%s", req.ClusterGroupSerialId, req.RequestId), 0, map[string]string{
				constants.FLOW_PARAM_CLUSTER_GROUP_ID: fmt.Sprintf("%d", clusterGroup.Id),
				constants.FLOW_PARAM_CLUSTER_ID:       fmt.Sprintf("%d", cluster.Id),
				constants.FLOW_PARAM_REQUEST_ID:       req.RequestId,
				constants.FLOW_PARAM_REGION:           req.Region,
				constants.FLOW_PARAM_APPID:            fmt.Sprintf("%d", req.AppId),
			}, nil)
		if err != nil {
			logger.Errorf("Failed to create flow for %s@%s because %+v", req.ClusterGroupSerialId, req.RequestId, err)
			return err
		}
		logger.Infof("Create flow %d for %s@%s", flowId, req.ClusterGroupSerialId, req.RequestId)
		return nil
	}).Close()

	return &setats.ModifySetatsWarehouseRsp{}, nil
}

func DeleteSetats(req *setats.DeleteSetatsReq) (*setats.DeleteSetatsRsp, error) {

	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("ScaleSetats panic, with errors:%+v", errs)
			return
		}
	}()

	clusterGroup, err := GetClusterGroupBySerialId(req.ClusterGroupSerialId)
	if err != nil {
		logger.Errorf("Failed to get cluster group by serial id:%s, with errors:%+v", req.ClusterGroupSerialId, err)
		return &setats.DeleteSetatsRsp{}, errorcode.FailedOperationCode.NewWithErr(err)
	}
	if int64(clusterGroup.AppId) != req.AppId || clusterGroup.OwnerUin != req.Uin {
		logger.Errorf("The appid %d or uin %s does not match request appid %d or uin %s.",
			clusterGroup.AppId, clusterGroup.OwnerUin, req.AppId, req.Uin)
		return &setats.DeleteSetatsRsp{}, errorcode.FailedOperationCode.ReplaceDesc(
			fmt.Sprintf("The appid %d or uin %s has no operation permission.", req.AppId, req.Uin))
	}
	if err != nil {
		logger.Errorf("Failed to check setats param, with errors:%+v", err)
		return &setats.DeleteSetatsRsp{}, err
	}

	cluster, err := GetActiveClusterByClusterGroupId(clusterGroup.Id)
	if err != nil {
		logger.Errorf("Failed to GetActiveClusterByClusterGroupId for %d because %+v", clusterGroup.Id, err)
		return &setats.DeleteSetatsRsp{}, err
	}
	SwitchSetatasStatusTo(clusterGroup.SerialId, constants.SETATS_DELETING)
	flow.CreateFlow(constants.FLOW_OCEANUS_DELETE_SETATS,
		fmt.Sprintf("%s@%s", req.ClusterGroupSerialId, req.RequestId), 0, map[string]string{
			constants.FLOW_PARAM_CLUSTER_GROUP_ID: fmt.Sprintf("%d", clusterGroup.Id),
			constants.FLOW_PARAM_CLUSTER_ID:       fmt.Sprintf("%d", cluster.Id),
			constants.FLOW_PARAM_REQUEST_ID:       req.RequestId,
			constants.FLOW_PARAM_APPID:            fmt.Sprintf("%d", req.AppId),
		}, nil)

	return &setats.DeleteSetatsRsp{}, nil
}

func ScaleSetats(req *setats.ScaleSetatsReq) (*setats.ScaleSetatsRsp, error) {

	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("ScaleSetats panic, with errors:%+v", errs)
			return
		}
	}()

	clusterGroup, err := GetClusterGroupBySerialId(req.ClusterGroupSerialId)
	if err != nil {
		logger.Errorf("Failed to get cluster group by serial id:%s, with errors:%+v", req.ClusterGroupSerialId, err)
		return &setats.ScaleSetatsRsp{}, errorcode.FailedOperationCode.NewWithErr(err)
	}
	if int64(clusterGroup.AppId) != req.AppId || clusterGroup.OwnerUin != req.Uin {
		logger.Errorf("The appid %d or uin %s does not match request appid %d or uin %s.",
			clusterGroup.AppId, clusterGroup.OwnerUin, req.AppId, req.Uin)
		return &setats.ScaleSetatsRsp{}, errorcode.FailedOperationCode.ReplaceDesc(
			fmt.Sprintf("The appid %d or uin %s has no operation permission.", req.AppId, req.Uin))
	}
	if err != nil {
		logger.Errorf("Failed to check setats param, with errors:%+v", err)
		return &setats.ScaleSetatsRsp{}, err
	}

	cluster, err := GetActiveClusterByClusterGroupId(clusterGroup.Id)
	if err != nil {
		logger.Errorf("Failed to GetActiveClusterByClusterGroupId for %d because %+v", clusterGroup.Id, err)
		return &setats.ScaleSetatsRsp{}, err
	}
	SwitchSetatasStatusTo(clusterGroup.SerialId, constants.SETATS_SCALE_PROGRESS)
	flow.CreateFlow(constants.FLOW_OCEANUS_SCALE_SETATS,
		fmt.Sprintf("%s@%s", req.ClusterGroupSerialId, req.RequestId), 0, map[string]string{
			constants.FLOW_PARAM_CLUSTER_GROUP_ID:                fmt.Sprintf("%d", clusterGroup.Id),
			constants.FLOW_PARAM_CLUSTER_ID:                      fmt.Sprintf("%d", cluster.Id),
			constants.FLOW_PARAM_REQUEST_ID:                      req.RequestId,
			constants.FLOW_PARAM_REGION:                          req.Region,
			constants.FLOW_PARAM_SETATS_SCALE_WORKER:             "1",
			constants.FLOW_PARAM_SETATS_SCALE_DISK:               "1",
			constants.FLOW_PARAM_APPID:                           fmt.Sprintf("%d", req.AppId),
			constants.FLOW_PARAM_SETATS_MASTER_DISK_SIZE:         fmt.Sprintf("%d", req.MasterDiskSize),
			constants.FLOW_PARAM_SETATS_WORKER_DISK_SIZE:         fmt.Sprintf("%d", req.WorkerDiskSize),
			constants.FLOW_PARAM_SETATS_WORKERDEFAULTPARALLELISM: fmt.Sprintf("%d", req.WorkerDefaultParallelism),
		}, nil)

	return &setats.ScaleSetatsRsp{}, nil
}

func GetBatchTask(batchTaskReq *setats.BatchTaskReq) (*setats2.BatchTask, error) {
	if batchTaskReq.JobUUID == "" && batchTaskReq.EngineJobId == "" {
		logger.Warningf("GetBatchTask batchTaskReq is invalid")
		return nil, errors.New("batchTaskReq is invalid")
	}
	sql := "select * from BatchTask where AppId=? "
	args := make([]interface{}, 0)
	args = append(args, batchTaskReq.AppId)
	if batchTaskReq.JobUUID != "" {
		sql += " and JobUUID=? "
		args = append(args, batchTaskReq.JobUUID)
	}
	if batchTaskReq.EngineJobId != "" {
		sql += " and EngineJobId=? "
		args = append(args, batchTaskReq.EngineJobId)
	}
	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to execute the sql:%s, with args:%+v, with errors:%+v", sql, args, err)
		return nil, err
	}
	if len(data) != 0 {
		batchTask := &setats2.BatchTask{}
		err = util.ScanMapIntoStruct(batchTask, data[0])
		if err != nil {
			logger.Errorf("Failed to convert bytes into batchTask, with errors:%+v", err)
			return nil, err
		}
		return batchTask, nil
	}
	return nil, nil
}

func GetLatestSessionId(batchTaskReq *setats.BatchTaskReq) (*setats2.BatchTask, error) {
	sql := "select * from BatchTask where AppId=? and Type=? and JobSerialId=? order by id desc limit 1"
	args := make([]interface{}, 0)
	args = append(args, batchTaskReq.AppId)
	args = append(args, constants.BatchTaskRunType)
	args = append(args, batchTaskReq.InstanceId)

	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to execute the sql:%s, with args:%+v, with errors:%+v", sql, args, err)
		return nil, err
	}
	if len(data) != 0 {
		batchTask := &setats2.BatchTask{}
		err = util.ScanMapIntoStruct(batchTask, data[0])
		if err != nil {
			logger.Errorf("Failed to convert bytes into batchTask, with errors:%+v", err)
			return nil, err
		}
		return batchTask, nil
	}
	return nil, nil
}

func CreateSetats(req *setats.CreateSetatsReq) (rst *setats.CreateSetatsRsp, err error) {

	rst = &setats.CreateSetatsRsp{}
	defer func() {
		if errs := recover(); errs != nil {
			err = errors.New(fmt.Sprintf("%+v", errs))
			logger.Errorf("CreateSetats panic, with errors:%+v", errs)
			return
		}
	}()

	clusterGroup, err := GetClusterGroupBySerialId(req.ClusterGroupSerialId)
	if err != nil {
		logger.Errorf("Failed to get cluster group by serial id:%s, with errors:%+v", req.ClusterGroupSerialId, err)
		return rst, errorcode.FailedOperationCode.NewWithErr(err)
	}
	if int64(clusterGroup.AppId) != req.AppId || clusterGroup.OwnerUin != req.Uin {
		logger.Errorf("The appid %d or uin %s does not match request appid %d or uin %s.",
			clusterGroup.AppId, clusterGroup.OwnerUin, req.AppId, req.Uin)
		return rst, errorcode.FailedOperationCode.ReplaceDesc(
			fmt.Sprintf("The appid %d or uin %s has no operation permission.", req.AppId, req.Uin))
	}
	if err != nil {
		logger.Errorf("Failed to check setats param, with errors:%+v", err)
		return rst, err
	}

	locker := dlocker.NewDlocker("CreateSetats", clusterGroup.SerialId, 8000)
	err = locker.Lock()
	if err != nil { // 加锁失败, 且没有 flowId, 就返回 flowId = 0 让计费重试
		logger.Warningf("Another CreateSetats process for %s has lock but not finished yet", clusterGroup.SerialId)
		return rst, errors.New(fmt.Sprintf("Another CreateSetats process for %s has lock but not finished yet", clusterGroup.SerialId))
	}
	defer locker.UnLock()

	// 是否已经有了 setats集群
	count, _, err := GetSetatsByClusterGroupSerialId(clusterGroup.SerialId)
	if err != nil {
		logger.Errorf("CreateSetats Failed to get setats by ClusterGroupSerialId id:%s, with errors:%+v", clusterGroup.SerialId, err)
		return rst, err
	}
	if count != 0 {
		logger.Errorf("CheckDisk cluster group %s has setats", clusterGroup.SerialId)
		return rst, errors.New(fmt.Sprintf("cluster group %s has setats", clusterGroup.SerialId))
	}

	cluster, err := GetActiveClusterByClusterGroupId(clusterGroup.Id)
	if err != nil {
		logger.Errorf("Failed to GetActiveClusterByClusterGroupId for %d because %+v", clusterGroup.Id, err)
		return rst, err
	}
	flowId := int64(0)
	setatsSerialId := ""
	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		_setats := &setats2.Setats{
			ClusterGroupSerialId:     req.ClusterGroupSerialId,
			AppId:                    req.AppId,
			OwnerUin:                 req.Uin,
			CreatorUin:               req.SubAccountUin,
			MasterCpu:                req.MasterCpu,
			MasterMem:                req.MasterMem,
			MasterDiskType:           req.MasterDiskType,
			MasterDiskSize:           req.MasterDiskSize,
			WorkerCpu:                req.WorkerCpu,
			WorkerMem:                req.WorkerMem,
			WorkerDiskType:           req.WorkerDiskType,
			WorkerDiskSize:           req.WorkerDiskSize,
			WorkerDefaultParallelism: req.WorkerDefaultParallelism,
			Region:                   req.Region,
			Zone:                     req.Zone,
			Status:                   constants.SETATS_INIT_PROGRESS,
			CreateTime:               util.GetCurrentTime(),
			UpdateTime:               util.GetCurrentTime(),
		}
		id := tx.SaveObject(_setats, "Setats")
		cidUtil := &util.CidUtil{}
		setatsSerialId = cidUtil.EncodeId(id, "setats", "Setats", util.GetNowTimestamp(), 8)
		tx.ExecuteSqlWithArgs("UPDATE Setats set SerialId = ?  WHERE Id = ? ", setatsSerialId, id)

		tags := ""
		if len(req.Tags) > 0 {
			bt, _ := json.Marshal(req.Tags)
			tags = base64.StdEncoding.EncodeToString(bt)
		}
		flowId, err = flow.CreateFlow(constants.FLOW_OCEANUS_CREATE_SETATS,
			fmt.Sprintf("%s@%s", req.ClusterGroupSerialId, req.RequestId), 0, map[string]string{
				constants.FLOW_PARAM_CLUSTER_GROUP_ID:                fmt.Sprintf("%d", clusterGroup.Id),
				constants.FLOW_PARAM_CLUSTER_ID:                      fmt.Sprintf("%d", cluster.Id),
				constants.FLOW_PARAM_REQUEST_ID:                      req.RequestId,
				constants.FLOW_PARAM_REGION:                          req.Region,
				constants.FLOW_PARAM_APPID:                           fmt.Sprintf("%d", req.AppId),
				constants.FLOW_PARAM_SETATS_MASTER_DISK_TYPE:         req.MasterDiskType,
				constants.FLOW_PARAM_SETATS_MASTER_DISK_SIZE:         fmt.Sprintf("%d", req.MasterDiskSize),
				constants.FLOW_PARAM_SETATS_WORKER_DISK_TYPE:         req.WorkerDiskType,
				constants.FLOW_PARAM_SETATS_WORKER_DISK_SIZE:         fmt.Sprintf("%d", req.WorkerDiskSize),
				constants.FLOW_PARAM_SETATS_WORKERDEFAULTPARALLELISM: fmt.Sprintf("%d", req.WorkerDefaultParallelism),
				constants.FLOW_PARAM_SETATS_MASTERCPU:                fmt.Sprintf("%f", req.MasterCpu),
				constants.FLOW_PARAM_SETATS_WORKERCPU:                fmt.Sprintf("%f", req.WorkerCpu),
				constants.FLOW_PARAM_TAGS:                            tags,
			}, nil)
		logger.Infof("Create flow %d for %s@%s", flowId, req.ClusterGroupSerialId, req.RequestId)
		if err != nil {
			logger.Errorf("Failed to create flow for %s@%s because %+v", req.ClusterGroupSerialId, req.RequestId, err)
			return err
		}
		return nil
	}).Close()

	return &setats.CreateSetatsRsp{FlowId: flowId, ClusterGroupId: clusterGroup.Id, ClusterId: cluster.Id, SetatsSerialId: setatsSerialId}, nil
}

func RestartSetats(req *setats.RestartSetatsReq) (*setats.RestartSetatsRsp, error) {
	err := CheckSetatsParam(req.ClusterGroupSerialId, req.SetatsSerialId, req.AppId, req.Uin)
	if err != nil {
		logger.Errorf("Failed to check setats param, with errors:%+v", err)
		return &setats.RestartSetatsRsp{}, err
	}

	clusterGroup, err := GetClusterGroupBySerialId(req.ClusterGroupSerialId)
	if err != nil {
		logger.Errorf("Failed to GetClusterGroupBySerialId for %s because %+v", req.ClusterGroupSerialId, err)
		return &setats.RestartSetatsRsp{}, err
	}
	cluster, err := GetActiveClusterByClusterGroupId(clusterGroup.Id)
	if err != nil {
		logger.Errorf("Failed to GetActiveClusterByClusterGroupId for %d because %+v", clusterGroup.Id, err)
		return &setats.RestartSetatsRsp{}, err
	}

	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.ExecuteSqlWithArgs("update Setats set Status=? where ClusterGroupSerialId=? and Status != -2", constants.SETATS_RESTARTING, clusterGroup.SerialId)
		_, err = flow.CreateFlow(constants.FLOW_OCEANUS_RESTART_SETATS,
			fmt.Sprintf("%s@%s", req.ClusterGroupSerialId, req.RequestId), 0, map[string]string{
				constants.FLOW_PARAM_CLUSTER_GROUP_ID: fmt.Sprintf("%d", clusterGroup.Id),
				constants.FLOW_PARAM_CLUSTER_ID:       fmt.Sprintf("%d", cluster.Id),
				constants.FLOW_PARAM_REQUEST_ID:       req.RequestId,
				constants.FLOW_PARAM_REGION:           req.Region,
				constants.FLOW_PARAM_APPID:            fmt.Sprintf("%d", req.AppId),
			}, nil)
		if err != nil {
			logger.Errorf("Failed to create flow oceanus_restart_setats for %s@%s because %+v", req.ClusterGroupSerialId, req.RequestId, err)
			return err
		}
		return nil
	}).Close()
	return &setats.RestartSetatsRsp{}, nil
}

func ModifySetatsConfiguration(req *setats.ModifySetatsConfigurationReq) (*setats.ModifySetatsConfigurationRsp, error) {

	err := CheckSetatsParam(req.ClusterGroupSerialId, req.SetatsSerialId, req.AppId, req.Uin)
	if err != nil {
		logger.Errorf("Failed to check setats param, with errors:%+v", err)
		return &setats.ModifySetatsConfigurationRsp{}, err
	}
	if len(req.ParamList) == 0 {
		logger.Warningf("The param list is empty, so do nothing.")
		return &setats.ModifySetatsConfigurationRsp{}, nil
	}
	setatsParams := []*setats2.SetatsParam{}
	// 检查是否存在不合法的参数
	err = config.DecodeK8sObjectFromRainbowConfig(SetatsGroup, SetatsParamsKey, &setatsParams)
	if err != nil {
		logger.Errorf("Failed to get setats params from rainbow config, with errors:%+v", err)
		return &setats.ModifySetatsConfigurationRsp{}, err
	}
	paraNames := make([]string, 0)
	for _, setatsParam := range setatsParams {
		paraNames = append(paraNames, setatsParam.ParamName)
	}
	for _, param := range req.ParamList {
		if ok, _ := service2.Contain(param.ParamName, paraNames); !ok {
			logger.Errorf("The param name %s is not in the setats params.", param.ParamName)
			return &setats.ModifySetatsConfigurationRsp{}, errorcode.FailedOperationCode.ReplaceDesc(
				fmt.Sprintf("The param name %s is not in the setats params.", param.ParamName))
		}
	}
	txManager := service.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		deleteSql := "update SetatsParam set Status = ? where SetatsSerialId = ?"
		tx.ExecuteSqlWithArgs(deleteSql, SetatsParamInActive, req.SetatsSerialId)
		sql := "insert into SetatsParam(SetatsSerialId, ParamName, ParamValue, ReferenceValue, ModificationGuide, IsRestart, Status) values "
		args := make([]interface{}, 0)
		for _, param := range req.ParamList {
			sql += "(?, ?, ?, ?, ?, ?, ?), "
			args = append(args, req.SetatsSerialId, param.ParamName, param.ParamValue, param.ReferenceValue, param.ModificationGuide, param.IsRestart, SetatsParamActive)
		}
		sql = strings.TrimRight(sql, ", ")
		logger.Infof("###insert into SetatsParam sql:%s, args:%+v", sql, args)
		tx.ExecuteSql(sql, args)
		return nil
	}).Close()
	return &setats.ModifySetatsConfigurationRsp{}, nil
}

func CheckSetatsParam(clusterGroupSerialId string, setatsSerialId string, appId int64, ownerUin string) error {
	clusterGroup, err := GetClusterGroupBySerialId(clusterGroupSerialId)
	if err != nil {
		logger.Errorf("Failed to get cluster group by serial id:%s, with errors:%+v", clusterGroupSerialId, err)
		return errorcode.FailedOperationCode.NewWithErr(err)
	}
	if int64(clusterGroup.AppId) != appId || clusterGroup.OwnerUin != ownerUin {
		logger.Errorf("The appid %d or uin %s does not match request appid %d or uin %s.",
			clusterGroup.AppId, clusterGroup.OwnerUin, appId, ownerUin)
		return errorcode.FailedOperationCode.ReplaceDesc(
			fmt.Sprintf("The appid %d or uin %s has no operation permission.", appId, ownerUin))
	}
	_, err = GetSetatsBySerialId(setatsSerialId)
	if err != nil {
		logger.Errorf("Failed to get setats by serial id:%s, with errors:%+v", setatsSerialId, err)
		return errorcode.FailedOperationCode.NewWithErr(err)
	}
	return nil
}

func ListSetatsRefBySetatsSerialId(setatsSerialId string) (result []*setats2.SetatsResourceInfo, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("ListSetatsRefBySetatsSerialId panic, with errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	sql := "select sr.*, r.ResourceId as ResourceSerialId from SetatsRef sr join Resource r on r.Id = sr.ResourceId where sr.SetatsSerialId=? and sr.Status = ?"
	args := make([]interface{}, 0)
	args = append(args, setatsSerialId)
	args = append(args, SetatsParamActive)
	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to execute the sql:%s, with args:%+v, with errors:%+v", sql, args, err)
		return nil, err
	}
	if len(data) == 0 {
		logger.Errorf("Failed to ListParamBySetatsSerialId because the setats size is 0")
		return result, nil
	}
	for _, obj := range data {
		setatsRef := &setats2.SetatsResourceInfo{}
		err = util.ScanMapIntoStruct(setatsRef, obj)
		if err != nil {
			logger.Errorf("Failed to convert bytes into setatsParam, with errors:%+v", err)
			return nil, err
		}
		result = append(result, setatsRef)
	}
	return result, nil
}

func ListParamBySetatsSerialId(setatsSerialId string) (result []*setats2.SetatsParam, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("ListParamBySetatsSerialId panic, with errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	sql := "select * from SetatsParam where SetatsSerialId=? and Status = ?"
	args := make([]interface{}, 0)
	args = append(args, setatsSerialId)
	args = append(args, SetatsParamActive)
	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to execute the sql:%s, with args:%+v, with errors:%+v", sql, args, err)
		return nil, err
	}
	if len(data) == 0 {
		logger.Errorf("Failed to ListParamBySetatsSerialId because the setats size is 0")
		return result, nil
	}
	for _, obj := range data {
		setatsParam := &setats2.SetatsParam{}
		err = util.ScanMapIntoStruct(setatsParam, obj)
		if err != nil {
			logger.Errorf("Failed to convert bytes into setatsParam, with errors:%+v", err)
			return nil, err
		}
		result = append(result, setatsParam)
	}
	return result, nil
}

func GetSetatsInfoByClusterGroupSerialId(clusterGroupSerialId string, clusterGroupId int64) (setatsInfo *model.SetatsInfo, err error) {
	setatsInfo = &model.SetatsInfo{}
	count, _setats, err := GetSetatsByClusterGroupSerialId(clusterGroupSerialId)
	if err != nil {
		logger.Errorf("Failed to get setats by clusterGroupSerialId id:%s, with errors:%+v", clusterGroupSerialId, err)
		return nil, err
	}
	if count == 0 {
		return nil, nil
	}
	setatsInfo.CreateTime = _setats.CreateTime
	billingResource, err := GetBillingResourceBySerialId(_setats.SerialId)
	if err != nil {
		logger.Errorf("Failed to get billing resource by serial id:%s, with errors:%+v", _setats.SerialId, err)
		return nil, err
	}
	setatsInfo.AutoRenewFlag = billingResource.AutoRenewFlag
	setatsInfo.ExpireTime = service2.SwitchDefaultTime(billingResource.ExpireTime)
	setatsInfo.SecondsUntilExpiry = calculateSecondsUntilExpiry(setatsInfo.ExpireTime)
	setatsInfo.IsolatedTime = billingResource.IsolatedTimestamp
	setatsInfo.ManagerUrl = _setats.ManagerUrl

	cluster, err := GetActiveClusterByClusterGroupId(clusterGroupId)
	if err != nil {
		logger.Errorf("GetSetatsInfoByClusterGroupSerialId Failed to get active cluster by clusterGroupId:%d, with errors:%+v", clusterGroupId, err)
		return nil, err
	}
	// 获取 setats hive metastore mysql 数据库信息
	cdbList, err := service3.GetTableService().ListCdbWithEncodeByClusterId(cluster.Id)
	if err != nil {
		logger.Errorf("GetSetatsInfoByClusterGroupSerialId Failed to get cdb by cluster id:%d, with errors:%+v", cluster.Id, err)
		return nil, err
	}
	if len(cdbList) > 1 {
		logger.Errorf("GetSetatsInfoByClusterGroupSerialId There are more than one cdb instances")
		err = errorcode.NewStackError(errorcode.InternalErrorCode_UnexpectedRecordNums, fmt.Sprintf("there are %d cdb instances", len(cdbList)), nil)
		return nil, err
	}

	if len(cdbList) == 1 {
		cdb := cdbList[0]
		setatsInfo.MetaUrl = fmt.Sprintf("%s:%d", cdb.Vip, cdb.Vport)
		setatsInfo.MetaUser = _setats.HiveMetastoreUser
		setatsInfo.MetaPass = _setats.HiveMetastorePass
	}

	tags, err := tag2.GetTagService().GetResourceTags(_setats.OwnerUin, _setats.Region, []string{_setats.SerialId}, resource_auth.RESOURCE_PREFIX_SETATS)
	if err != nil {
		logger.Errorf("Failed to get resource tags by setats serial id:%s, with errors:%+v", _setats.SerialId, err)
		return nil, err
	}
	if len(tags) > 0 {
		tagList := make([]*tag.Tag, 0)
		for _, t := range tags {
			tagList = append(tagList, &tag.Tag{
				TagKey:   t.TagKey,
				TagValue: t.TagValue,
			})
		}
		setatsInfo.Tags = tagList
	}

	setatsInfo.MasterInfo = &model.SetatsCvmInfo{
		Cpu: _setats.MasterCpu,
		Mem: _setats.MasterMem,
		Disk: &model.SetatsDisk{
			DiskType: _setats.MasterDiskType,
			DiskSize: _setats.MasterDiskSize,
		},
	}
	setatsInfo.WorkerInfo = &model.SetatsCvmInfo{
		Cpu: _setats.WorkerCpu,
		Mem: _setats.WorkerMem,
		Disk: &model.SetatsDisk{
			DiskType: _setats.WorkerDiskType,
			DiskSize: _setats.WorkerDiskSize,
		},
		DefaultParallelism: _setats.WorkerDefaultParallelism,
	}
	setatsInfo.SetatsSerialId = _setats.SerialId
	setatsInfo.Status = _setats.Status

	count, setatsWarehouse, err := GetSetatsWarehouseBySerialId(_setats.SerialId)
	if err != nil {
		logger.Errorf("Failed to GetSetatsWarehouseBySerialId by setats serial id:%s, with errors:%+v", _setats.SerialId, err)
		return nil, err
	}
	if count == 0 {
		if setatsInfo.Status == constants.SETATS_RUNNING {
			setatsInfo.Status = constants.SETATS_WAREHOOUSE_NOT_INIT
		}
		return setatsInfo, nil
	}
	resourceRefs, err := GetSetatsResourceRefs(_setats.SerialId)
	if err != nil {
		logger.Errorf("Failed to GetSetatsRefs by setats serial id:%s, with errors:%+v", _setats.SerialId, err)
		return nil, err
	}
	properties := make([]model1.Property, 0, 0)
	if setatsWarehouse.Properties != "" && setatsWarehouse.Properties != "[]" {
		err := json.Unmarshal([]byte(setatsWarehouse.Properties), &properties)
		if err != nil {
			logger.Errorf("Failed to unmarshal properties, with errors:%+v", err)
			return nil, err
		}
	}

	setatsInfo.Warehouse = &model.SetatsWarehouse{
		Status:          setatsWarehouse.Status,
		Location:        setatsWarehouse.Location,
		CatalogType:     setatsWarehouse.CatalogType,
		Uri:             setatsWarehouse.Uri,
		WarehouseUrl:    setatsWarehouse.WarehouseUrl,
		Authentication:  setatsWarehouse.Authentication,
		ResourceRefs:    resourceRefs,
		HiveUri:         setatsWarehouse.HiveUri,
		Properties:      properties,
		HiveCatalogType: setatsWarehouse.HiveCatalogType,
	}
	return setatsInfo, nil
}

func GetSetatsResourceRefs(setatsSerialId string) (resourceRefs []*setats.ResourceRef, err error) {

	cond := dao.NewCondition()
	cond.Eq("a.SetatsSerialId", setatsSerialId)
	cond.Eq("a.Status", constants.RESOURCE_STATUS_ACTIVE)
	cond.Eq("b.Status", constants.RESOURCE_STATUS_ACTIVE)
	where, args := cond.GetWhere()
	sql := " SELECT itemSpace.SerialId as WorkspaceId, b.ResourceName as Name, b.ResourceId, a.Version, a.Type, a.Status FROM SetatsRef a " +
		"JOIN Resource b ON a.ResourceId = b.Id JOIN ItemSpace itemSpace on itemSpace.Id = b.ItemSpaceId " + where
	_, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return nil, err
	}
	resourceRefs = make([]*setats.ResourceRef, 0)
	for i := 0; i < len(data); i++ {
		resourceRef := &setats.ResourceRef{}
		err = util.ScanMapIntoStruct(resourceRef, data[i])
		if err != nil {
			return nil, err
		}
		resourceRefs = append(resourceRefs, resourceRef)
	}
	return resourceRefs, nil
}

type ListSetatsParam struct {
	AppId                 int64
	ClusterGroupSerialIds []string
}

func ListSetats(listSetatsParam *ListSetatsParam) (setatsList []*setats2.Setats, err error) {
	var sql = "select Setats.* from Setats join SetatsWarehouse on Setats.SerialId = SetatsWarehouse.SetatsSerialId"
	cond := dao.NewCondition()

	if listSetatsParam.AppId > 0 {
		cond.Eq("Setats.AppId", listSetatsParam.AppId)
	}
	cond.Ne("SetatsWarehouse.Location", constants.SETATS_LOCATION_COS)
	cond.NIn("Setats.Status", []int{constants.CLUSTER_GROUP_STATUS_DELETED, constants.SETATS_DELETING, constants.SETATS_ISOLATED})

	if len(listSetatsParam.ClusterGroupSerialIds) > 0 {
		cond.In("Setats.ClusterGroupSerialId", listSetatsParam.ClusterGroupSerialIds)
	}

	where, args := cond.GetWhere()
	sql += where

	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to execute the sql:%s, with args:%+v, with errors:%+v", sql, args, err)
		return setatsList, err
	}
	setatsList = make([]*setats2.Setats, 0)
	for i := 0; i < len(data); i++ {
		_setats := &setats2.Setats{}
		err = util.ScanMapIntoStruct(_setats, data[i])
		if err != nil {
			logger.Errorf("Failed to convert bytes into setats, with errors:%+v", err)
			return setatsList, errorcode.InternalErrorCode_UnMarshalFailed.NewWithErr(err)
		}
		setatsList = append(setatsList, _setats)
	}

	return setatsList, nil
}

func GetSetatsByClusterGroupSerialId(clusterGroupSerialId string) (count int, setats *setats2.Setats, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Get setats panic, with errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	sql := "select * from Setats where ClusterGroupSerialId=? and Status != -2"
	args := make([]interface{}, 0)
	args = append(args, clusterGroupSerialId)
	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to execute the sql:%s, with args:%+v, with errors:%+v", sql, args, err)
		return 0, nil, err
	}
	if len(data) == 0 {
		logger.Errorf("Failed to query the setats, because the setats size is 0")
		return 0, nil, nil
	}
	setats = &setats2.Setats{}
	err = util.ScanMapIntoStruct(setats, data[0])
	if err != nil {
		logger.Errorf("Failed to convert bytes into setats, with errors:%+v", err)
		return 0, nil, err
	}
	return 1, setats, nil
}

func GetSetatsWarehouseBySerialId(setatsSerialId string) (count int, setatsWarehouse *setats2.SetatsWarehouse, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Get setatsWarehouse panic, with errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	sql := "select * from SetatsWarehouse where SetatsSerialId=?"
	args := make([]interface{}, 0)
	args = append(args, setatsSerialId)
	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to execute the sql:%s, with args:%+v, with errors:%+v", sql, args, err)
		return -1, nil, err
	}
	if len(data) == 0 {
		logger.Errorf("Failed to query the setatsWarehouse, because the setats size is 0")
		return 0, nil, nil
	}
	setatsWarehouse = &setats2.SetatsWarehouse{}
	err = util.ScanMapIntoStruct(setatsWarehouse, data[0])
	if err != nil {
		logger.Errorf("Failed to convert bytes into setatsWarehouse, with errors:%+v", err)
		return -1, nil, err
	}
	return 1, setatsWarehouse, nil
}

func GetSetatsBySerialId(setatsSerialId string) (setats *setats2.Setats, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Get setats panic, with errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	sql := "select * from Setats where SerialId=? and Status != -2"
	args := make([]interface{}, 0)
	args = append(args, setatsSerialId)
	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to execute the sql:%s, with args:%+v, with errors:%+v", sql, args, err)
		return &setats2.Setats{}, err
	}
	if len(data) == 0 {
		logger.Errorf("Failed to query the setats, because the setats size is 0")
		return &setats2.Setats{}, errors.New("setats query size is 0")
	}
	setats = &setats2.Setats{}
	err = util.ScanMapIntoStruct(setats, data[0])
	if err != nil {
		logger.Errorf("Failed to convert bytes into setats, with errors:%+v", err)
		return &setats2.Setats{}, err
	}
	return setats, nil
}

func ModifySetatasInfo(clusterGroupSerialId string, workerDefaultParallelism int, workerDiskSize int, masterDiskSize int) (err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("modifySetatasInfo to panic, with errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	sql := "update Setats set MasterDiskSize=?, WorkerDiskSize=?, WorkerDefaultParallelism=? where ClusterGroupSerialId=? and Status != -2"
	args := make([]interface{}, 0)
	args = append(args, masterDiskSize)
	args = append(args, workerDiskSize)
	args = append(args, workerDefaultParallelism)
	args = append(args, clusterGroupSerialId)
	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.ExecuteSql(sql, args)
		return nil
	}).Close()
	if err != nil {
		logger.Errorf("Failed to update setats status, with errors:%+v", err)
		return err
	}
	return nil
}

func ModifySetatasManagerUrl(clusterGroupSerialId string, managerUrl string) (err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("ModifySetatasManagerUrl to panic, with errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	sql := "update Setats set ManagerUrl=? where ClusterGroupSerialId=? and Status != -2 and ManagerUrl = ''"
	args := make([]interface{}, 0)
	args = append(args, managerUrl)
	args = append(args, clusterGroupSerialId)
	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.ExecuteSql(sql, args)
		return nil
	}).Close()
	if err != nil {
		logger.Errorf("Failed to update setats ManagerUrl, with errors:%+v", err)
		return err
	}
	return nil
}

func ModifySetatsWarehouseHiveUri(setatsSerialId string, hiveUri string) (err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("ModifySetatsWarehouseHiveUri to panic, with errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	sql := "update SetatsWarehouse set HiveUri=? where SetatsSerialId=? and HiveUri = ''"
	args := make([]interface{}, 0)
	args = append(args, hiveUri)
	args = append(args, setatsSerialId)
	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.ExecuteSql(sql, args)
		return nil
	}).Close()
	if err != nil {
		logger.Errorf("Failed to update setatsWarehouse HiveUri, with errors:%+v", err)
		return err
	}
	return nil
}

func SwitchSetatasStatusTo(clusterGroupSerialId string, status int8) (err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Switch setats status to panic, with errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	sql := "update Setats set Status=? where ClusterGroupSerialId=? and Status != -2"
	args := make([]interface{}, 0)
	args = append(args, status)
	args = append(args, clusterGroupSerialId)
	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.ExecuteSql(sql, args)
		return nil
	}).Close()
	if err != nil {
		logger.Errorf("Failed to update setats status, with errors:%+v", err)
		return err
	}
	return nil
}

func RecordHiveMetaInfoApp(setatsSerialId string) (err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("RecordHiveMetaInfo to panic, with errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	hiveMetastoreUser, err := config.GetRainbowConfiguration("ConfigureCenter.Flow.K8S.Setats", "hiveMetastoreUser")
	if err != nil {
		logger.Errorf("RecordHiveMetaInfoApp Failed to get hiveMetastoreUser, with errors:%+v", err)
		return err
	}
	hiveMetastorePass, err := config.GetRainbowConfiguration("ConfigureCenter.Flow.K8S.Setats", "hiveMetastorePass")
	if err != nil {
		logger.Errorf("RecordHiveMetaInfoApp Failed to get hiveMetastorePass, with errors:%+v", err)
		return err
	}
	rainbowOceanusPasswordEncodeKey, err := common_config.GetRainbowOceanusPasswordEncodeKey()
	if err != nil {
		logger.Errorf("RecordHiveMetaInfoApp Failed to get oceanus cluster password encode key in Rainbow")
		return err
	}
	password, err := util.EncodePassword(hiveMetastorePass, rainbowOceanusPasswordEncodeKey)
	sql := "update Setats set HiveMetastoreUser=?, HiveMetastorePass=? where SerialId=? and HiveMetastoreUser = '' and HiveMetastorePass = ''"
	args := make([]interface{}, 0)
	args = append(args, hiveMetastoreUser)
	args = append(args, password)
	args = append(args, setatsSerialId)
	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.ExecuteSql(sql, args)
		return nil
	}).Close()
	if err != nil {
		logger.Errorf("Failed to RecordHiveMetaInfo, with errors:%+v", err)
		return err
	}
	return nil
}

func SwitchSetatsWarehouseStatusTo(clusterGroupSerialId string, status int8) (err error) {
	defer func() {
		if errs := recover(); errs != nil {
			logger.Errorf("Switch SetatsWarehouse status to panic, with errors:%+v", errs)
			err = errors.New(fmt.Sprintf("%+v", errs))
		}
	}()
	count, setats, err := GetSetatsByClusterGroupSerialId(clusterGroupSerialId)
	if err != nil {
		logger.Errorf("Failed to get setats by clusterGroupSerialId id:%s, with errors:%+v", clusterGroupSerialId, err)
		return err
	}
	if count == 0 {
		logger.Errorf("Failed to get setats by clusterGroupSerialId id:%s, because the setats size is 0", clusterGroupSerialId)
		return errors.New("setats size is 0")
	}
	sql := "update SetatsWarehouse set Status=? where SetatsSerialId=?"
	args := make([]interface{}, 0)
	args = append(args, status)
	args = append(args, setats.SerialId)
	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.ExecuteSql(sql, args)
		return nil
	}).Close()
	if err != nil {
		logger.Errorf("Failed to update SetatsWarehouse status, with errors:%+v", err)
		return err
	}
	return nil
}
