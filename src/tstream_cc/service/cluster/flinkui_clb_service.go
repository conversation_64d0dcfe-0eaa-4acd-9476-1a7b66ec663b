package service

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"k8s.io/client-go/kubernetes"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/cluster"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/clb"
	table1 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	clbservice "tencentcloud.com/tstream_galileo/src/tstream_cc/service/clb"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config/configure_center"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
)

type FlinkUiClbService struct {
	flinkUiClb   *table.FlinkUiCLB
	k8sService   *k8s.K8sService
	k8sClient    *kubernetes.Clientset
	clusterGroup *table1.ClusterGroup
	cluster      *table1.Cluster
}

func NewFlinkUiClbService(k8sClient *kubernetes.Clientset, clusterGroup *table1.ClusterGroup, cluster *table1.Cluster) FlinkUiClbService {
	return FlinkUiClbService{
		k8sService:   k8s.NewK8sService(),
		k8sClient:    k8sClient,
		clusterGroup: clusterGroup,
		cluster:      cluster,
	}
}

func GetFlinkUiCLB(region string) (flinkUiClb *table.FlinkUiCLB, err error) {
	sql := "SELECT * FROM FlinkUiCLB WHERE Region = ?"
	args := make([]interface{}, 0)
	args = append(args, region)
	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("GetFlinkUiCLB flinkUiClbId: %s,error msg: %s", region, err.Error())
		return nil, err
	}
	if len(data) != 1 {
		err = fmt.Errorf("GetFlinkUiCLB error, FlinkUiCLB size is len(%d)", len(data))
		logger.Errorf(err.Error())
		return nil, err
	}

	flinkUiClb = &table.FlinkUiCLB{}
	err = util.ScanMapIntoStruct(flinkUiClb, data[0])
	if err != nil {
		logger.Errorf("GetFlinkUiCLB Failed to get ScanMapIntoStruct, with errors:%+v", err)
		return nil, err
	}
	return flinkUiClb, nil
}

func ListFlinkUiCLB() (flinkUiClb []*table.FlinkUiCLB, err error) {
	sql := "SELECT * FROM FlinkUiCLB"
	args := make([]interface{}, 0)
	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("ListFlinkUiCLB error, FlinkUiCLB size is len(%d)", len(data))
		return nil, err
	}
	flinkUiClb = make([]*table.FlinkUiCLB, 0)
	for i := 0; i < len(data); i++ {
		tmp := &table.FlinkUiCLB{}
		err := util.ScanMapIntoStruct(tmp, data[i])
		if err != nil {
			logger.Errorf("Failed to covert map into struct, with errors:%+v", err)
			return []*table.FlinkUiCLB{}, err
		}
		flinkUiClb = append(flinkUiClb, tmp)
	}
	return flinkUiClb, nil
}

func (f *FlinkUiClbService) CreateFlinkUiRule() error {
	if f.cluster.CrossTenantEniMode != constants.EnableStaticMode {
		return nil
	}
	domain := configure_center.CC(f.clusterGroup.Region).GetFlinkUiDomain()
	if domain == "" {
		return errors.New("domain is empty")
	}

	flinkUiClb, err := GetFlinkUiCLB(f.clusterGroup.Region)
	if err != nil {
		return err
	}

	exist, err := clbservice.CheckRuleExist(flinkUiClb.LoadBalancerId, "/"+f.clusterGroup.SerialId, f.clusterGroup.Region)
	if err != nil {
		return err
	}
	if exist {
		return nil
	}

	_, _, err = clbservice.CreateRule(&clbservice.Target{
		Region:         f.clusterGroup.Region,
		LoadBalancerId: flinkUiClb.LoadBalancerId,
		ListenerId:     flinkUiClb.ListenerId,
		Domain:         f.clusterGroup.Region + "." + domain,
		Url:            "/" + f.clusterGroup.SerialId,
	})

	return err
}

func (f *FlinkUiClbService) DeleteFlinkUiRule() error {
	if f.cluster.CrossTenantEniMode != constants.EnableStaticMode {
		return nil
	}

	domain := configure_center.CC(f.clusterGroup.Region).GetFlinkUiDomain()
	if domain == "" {
		return errors.New("domain is empty")
	}

	flinkUiClb, err := GetFlinkUiCLB(f.clusterGroup.Region)
	if err != nil {
		return err
	}

	_, _, err = clbservice.DeleteRule(&clbservice.Target{
		Region:         f.clusterGroup.Region,
		LoadBalancerId: flinkUiClb.LoadBalancerId,
		ListenerId:     flinkUiClb.ListenerId,
		Domain:         f.clusterGroup.Region + "." + domain,
		Url:            "/" + f.clusterGroup.SerialId,
	})

	return err
}

func (f *FlinkUiClbService) RegisterFlinkUiTarget() error {
	if f.cluster.CrossTenantEniMode != constants.EnableStaticMode {
		return nil
	}
	domain := configure_center.CC(f.clusterGroup.Region).GetFlinkUiDomain()
	if domain == "" {
		return errors.New("domain is empty")
	}
	describeWebUIWhiteListService := NewDescribeWebUIWhiteListService1(&model.DescribeWebUIWhiteListReq{
		ClusterId: f.clusterGroup.SerialId,
	}, f.k8sClient, f.clusterGroup, f.cluster)
	ip, err := describeWebUIWhiteListService.GetPrivateIp(f.clusterGroup.Id)
	if err != nil {
		return err
	}
	logger.Infof("RegisterFlinkUiTarget ip: %s", ip)
	if ip == "" {
		return errors.New("get cross-tenant-eni is null")
	}

	flinkUiClb, err := GetFlinkUiCLB(f.clusterGroup.Region)
	if err != nil {
		return err
	}

	// 添加重试机制，最多重试3次，每次间隔3秒
	const maxRetries = 3
	const retryInterval = 3 * time.Second

	target := &clbservice.Target{
		Region:         f.clusterGroup.Region,
		LoadBalancerId: flinkUiClb.LoadBalancerId,
		ListenerId:     flinkUiClb.ListenerId,
		Ip:             ip,
		Domain:         f.clusterGroup.Region + "." + domain,
		Url:            "/" + f.clusterGroup.SerialId,
	}

	var lastErr error
	for i := 0; i < maxRetries; i++ {
		code, msg, err := clbservice.RegisterFlinkUiTarget(target)
		if err == nil {
			logger.Infof("RegisterFlinkUiTarget success after %d attempts", i+1)
			return nil
		}

		lastErr = err
		logger.Warningf("RegisterFlinkUiTarget attempt %d failed: code=%d, msg=%s, err=%v", i+1, code, msg, err)

		// 如果不是最后一次重试，则等待一段时间后继续
		if i < maxRetries-1 {
			logger.Infof("Retrying RegisterFlinkUiTarget in %v...", retryInterval)
			time.Sleep(retryInterval)
		}
	}

	logger.Errorf("RegisterFlinkUiTarget failed after %d attempts, last error: %v", maxRetries, lastErr)
	return lastErr
}

func (f *FlinkUiClbService) DeregisterFlinkUiTarget() error {
	if f.cluster.CrossTenantEniMode != constants.EnableStaticMode {
		return nil
	}
	// 公网访问才需要解绑，内网访问不存在绑定关系，公网切换内网的时候也已经解绑
	if f.cluster.WebUIType == constants.PrivateType {
		return nil
	}

	describeWebUIWhiteListService := NewDescribeWebUIWhiteListService1(&model.DescribeWebUIWhiteListReq{
		ClusterId: f.clusterGroup.SerialId,
	}, f.k8sClient, f.clusterGroup, f.cluster)
	ip, err := describeWebUIWhiteListService.GetPrivateIp(f.clusterGroup.Id)
	if err != nil {
		return err
	}
	return f.DeregisterFlinkUiTargetByIp(ip)
}

func (f *FlinkUiClbService) DeregisterFlinkUiTargetByIp(ip string) error {
	if f.cluster.CrossTenantEniMode != constants.EnableStaticMode {
		return nil
	}
	// 公网访问才需要解绑，内网访问不存在绑定关系，公网切换内网的时候也已经解绑
	if f.cluster.WebUIType == constants.PrivateType {
		return nil
	}
	domain := configure_center.CC(f.clusterGroup.Region).GetFlinkUiDomain()
	if domain == "" {
		return errors.New("domain is empty")
	}

	logger.Infof("DeregisterFlinkUiTarget ip: %s", ip)
	if ip == "" {
		return errors.New("get cross-tenant-eni is null")
	}
	flinkUiClb, err := GetFlinkUiCLB(f.clusterGroup.Region)
	if err != nil {
		return err
	}

	// 解绑先查询是否存在，否则直接调用解绑会报错
	exist, err := clbservice.CheckTargetExist(flinkUiClb.LoadBalancerId, ip, f.clusterGroup.Region)
	if !exist {
		logger.Infof("target not exist, no need to deregister")
		return nil
	}

	_, _, err = clbservice.DeregisterFlinkUiTarget(&clbservice.Target{
		Region:         f.clusterGroup.Region,
		LoadBalancerId: flinkUiClb.LoadBalancerId,
		ListenerId:     flinkUiClb.ListenerId,
		Ip:             ip,
		Domain:         f.clusterGroup.Region + "." + domain,
		Url:            "/",
	})
	return err
}

func (f *FlinkUiClbService) BindFlinkUiClb() error {
	err := f.CreateFlinkUiRule()
	if err != nil && !strings.Contains(strings.ToLower(err.Error()), "duplicate url") {
		return err
	}

	// 获取IP和CLB信息
	describeWebUIWhiteListService := NewDescribeWebUIWhiteListService1(&model.DescribeWebUIWhiteListReq{
		ClusterId: f.clusterGroup.SerialId,
	}, f.k8sClient, f.clusterGroup, f.cluster)
	ip, err := describeWebUIWhiteListService.GetPrivateIp(f.clusterGroup.Id)
	if err != nil {
		return err
	}
	if ip == "" {
		return errors.New("get cross-tenant-eni is null")
	}

	flinkUiClb, err := GetFlinkUiCLB(f.clusterGroup.Region)
	if err != nil {
		return err
	}

	// 检查目标健康状态，只有不健康时才注册
	isHealthy, err := clbservice.CheckTargetHealth(flinkUiClb.LoadBalancerId, ip, f.clusterGroup.Region)
	if err != nil {
		return err
	}

	if !isHealthy {
		err = f.RegisterFlinkUiTarget()
		if err != nil {
			return err
		}
	}

	return nil
}
