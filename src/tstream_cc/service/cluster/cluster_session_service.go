package service

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"path/filepath"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/dlocker"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	model3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model"
	model_capi_cluster "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/cluster"
	model5 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_config"
	model4 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/resource_config"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cluster"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service_bucket "tencentcloud.com/tstream_galileo/src/tstream_cc/service/bucket"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config/configure_center"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cos"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/version"
	"time"
)

var (
	clusterSessionService *ClusterSessionService
)

const (
	SqlServerSupportSession          = "8.4"
	SqlServerSupportSessionFlink116  = "9.3.0"
	SqlServerSupportSessionFlink118  = "9.3.0"
	SqlServerSupportSessionFlink120  = "9.23.0"
	SESSION_CLUSTER_JARS_UPLOAD_PATH = "/user/user_00/.flink/%s/%d/systemShipFiles"
)

var SessionSupportFlinkVersion = map[string]struct{}{
	constants.FLINK_VERSION_1_13: {},
	constants.FLINK_VERSION_1_16: {},
	constants.FLINK_VERSION_1_18: {},
	constants.FLINK_VERSION_1_20: {},
}

type ClusterSessionResourceItem struct {
	Url  string `json:"url"`
	Path string `json:"path"`
}

type ClusterSessionService struct {
}

func (this *ClusterSessionService) DoStopSessionCluster(req *model.ClusterSessionBase) (string, string, *model_capi_cluster.ClusterSessionRspWrapper) {
	// 1. 校验权限，只有超级管理员可以停止session集群
	if 1 != req.IsSupOwner {
		logger.Warning("Illegal OPERATE: No permission,need administrator ,DoStopSessionCluster: %v", req)
		return "NO_PERMISSION_TO_OPERATE", "No permission, need administrator", nil
	}
	requestId := req.RequestId
	clusterGroup, err := GetClusterGroupBySerialId(req.ClusterGroupSerialId)
	if err != nil {
		logger.Errorf("Failed to GetClusterGroupBySerialId for %s because %+v", req.ClusterGroupSerialId, err)
		return controller.InvalidParameterValue, fmt.Sprintf("%v", err), nil
	}
	if clusterGroup.AppId != int32(req.AppId) || clusterGroup.OwnerUin != req.Uin {
		// 恶意请求行为
		logger.Errorf("clusterGroup.AppId: %d , req.AppId: %d, clusterGroup.OwnerUin: %s, req.Uin: %s", clusterGroup.AppId, req.AppId, clusterGroup.OwnerUin, req.Uin)
		return controller.InvalidParameterValue, "Illegal request", nil
	}
	cluster, err := GetActiveClusterByClusterGroupId(clusterGroup.Id)
	if err != nil {
		logger.Errorf("Failed to GetActiveClusterByClusterGroupId for %d because %+v", clusterGroup.Id, err)
		return controller.InvalidParameterValue, fmt.Sprintf("%v", err), nil
	}
	// 2. 校验集群是否为tke独享集群或eks集群,只有这种集群才可能有session集群
	isEks, _ := IsEks(cluster.ClusterGroupId)
	if cluster.SchedulerType != constants.CLUSTER_SCHEDULER_TYPE_TKE && !isEks {
		logger.Errorf("DoStopSessionCluster: not supported, no tke or eks cluster, req: %v", req)
		return controller.InvalidParameterValue, "Not supported, no tke or eks cluster", nil
	}
	if _, support := SessionSupportFlinkVersion[req.FlinkVersion]; !support ||
		!strings.Contains(cluster.SupportedFlinkVersion, req.FlinkVersion) {
		logger.Errorf("DoStopSessionCluster: not supported, illegal flink version, req: %s", req.FlinkVersion)
		return controller.InvalidParameterValue, "Not supported flink version", nil
	}
	ClusterSession, _ := service2.GetClusterSessionBySerialIdAndFlinkVersion(req.ClusterGroupSerialId, req.FlinkVersion)
	if ClusterSession == nil {
		logger.Errorf("ClusterGroup: %s, Session cluster is not exists", clusterGroup.SerialId)
		return controller.InvalidParameterValue, "Session cluster is not exists", nil
	}
	if ClusterSession.Status != constants.ClusterSessionRunning {
		logger.Errorf("ClusterGroup: %s, Session cluster is not running status, can not stop", clusterGroup.SerialId)
		return controller.InvalidParameterValue, "Session cluster is not running status, can not stop", nil
	}
	regionId, err := service.GetRegionIdByName(clusterGroup.Region)
	if err != nil {
		logger.Errorf("[%s] GetRegionIdByName error: %+v", requestId, err)
		regionId = 0
	}
	// 4. 加锁之后保存CusterSession集群信息以及创建开启Session集群的Flow
	//lock
	locker := dlocker.NewDlocker("oceanus-stop-session-cluster", fmt.Sprintf("cluster-session-%s", req.ClusterGroupSerialId), 3600)
	err = locker.Lock()
	if err != nil {
		msg := fmt.Sprintf("lock task failed![ %s ]", err.Error())
		logger.Error(msg)
		return controller.InternalError, msg, nil
	}
	defer locker.UnLock()
	// 开启事物保存ClusterSession信息
	txManager := service.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		ClusterSession.Status = constants.ClusterSessionStopping
		tx.UpdateObject(ClusterSession, ClusterSession.Id, "ClusterSession")
		if flowId, err := flow.CreateFlow(constants.FLOW_OCEANUS_STOP_SESSION_CLUSTER,
			fmt.Sprintf("%s@%s", req.ClusterGroupSerialId, requestId), regionId, map[string]string{
				constants.FLOW_PARAM_REQUEST_ID:             requestId,
				constants.FLOW_PARAM_APPID:                  fmt.Sprintf("%d", req.AppId),
				constants.FLOW_PARAM_OWNERUIN:               req.Uin,
				constants.FLOW_PARAM_CREATORUIN:             req.SubAccountUin,
				constants.FLOW_PARAM_FLINK_VERSION:          req.FlinkVersion,
				constants.FLOW_PARAM_REGION:                 req.Region,
				constants.FLOW_PARAM_CLUSTER_GROUP_ID:       fmt.Sprintf("%d", clusterGroup.Id),
				constants.FLOW_PARAM_CLUSTER_GROUP_SERIALID: clusterGroup.SerialId,
				constants.FLOW_PARAM_CLUSTER_ID:             fmt.Sprintf("%d", cluster.Id),
			}, nil); err != nil {
			logger.Error("flow.CreateFlow(constants.FLOW_OCEANUS_STOP_SESSION_CLUSTER), errors:", err.Error())
			return err
		} else {
			logger.Infof("[%s] createFlow %s success, flowId %d", requestId, constants.FLOW_OCEANUS_STOP_SESSION_CLUSTER, flowId)
		}
		return nil
	}).Close()
	rsp := buildClusterSessionItem(ClusterSession)
	return controller.OK, controller.NULL, rsp
}

func (this *ClusterSessionService) DoCreateSessionCluster(req *model.ClusterSessionReq) (string, string, *model_capi_cluster.ClusterSessionRspWrapper) {
	// 1. 校验权限，只有超级管理员可以开启session集群
	if 1 != req.IsSupOwner {
		logger.Warning("Illegal OPERATE: No permission,need administrator ,DoCreateSessionCluster: %v", req)
		return "NO_PERMISSION_TO_OPERATE", "No permission, need administrator", nil
	}
	if req.TaskManagerNum < 1 {
		errMsg := fmt.Sprintf("taskManagerNum[%d] must > 0", req.TaskManagerNum)
		logger.Errorf(errMsg)
		return controller.InvalidParameterValue, errMsg, nil
	}
	requestId := req.RequestId
	clusterGroup, err := GetClusterGroupBySerialId(req.ClusterGroupSerialId)
	if err != nil {
		logger.Errorf("Failed to GetClusterGroupBySerialId for %s because %+v", req.ClusterGroupSerialId, err)
		return controller.InvalidParameterValue, fmt.Sprintf("%v", err), nil
	}
	if clusterGroup.AppId != int32(req.AppId) || clusterGroup.OwnerUin != req.Uin {
		// 恶意请求的越权行为
		logger.Errorf("clusterGroup.AppId: %d , req.AppId: %d, clusterGroup.OwnerUin: %s, req.Uin: %s", clusterGroup.AppId, req.AppId, clusterGroup.OwnerUin, req.Uin)
		return controller.InvalidParameterValue, "Illegal request", nil
	}
	cluster, err := GetActiveClusterByClusterGroupId(clusterGroup.Id)
	if err != nil {
		logger.Errorf("Failed to GetActiveClusterByClusterGroupId for %d because %+v", clusterGroup.Id, err)
		return controller.InvalidParameterValue, fmt.Sprintf("%v", err), nil
	}
	// 2. 检查集群状态是否为运行中
	if clusterGroup.Status != constants.CLUSTER_GROUP_STATUS_RUNNING {
		return controller.FailedOperation_CreateCluster, "Cluster is not running status", nil
	}
	// 3. 校验集群是否为tke独享集群或eks集群
	isEks, _ := IsEks(cluster.ClusterGroupId)
	if cluster.SchedulerType != constants.CLUSTER_SCHEDULER_TYPE_TKE && !isEks {
		logger.Errorf("DoCreateSessionCluster: not supported, no tke or eks cluster, req: %v", req)
		return controller.InvalidParameterValue, "Not supported, no tke or eks cluster", nil
	}
	// 4. 参数校验(校验开启session集群支持的Flink版本)
	// 4.1 校验管控服务sqlserver版本是不是大于等于8.4.0,否则不允许开启session集群，因为无法执行调试作业
	isSupportSession, err := this.CheckSupportSession(cluster, req.FlinkVersion)
	if !isSupportSession {
		logger.Errorf("Please check the version of sqlserver which version is need to >= 8.4.0")
		return controller.UnsupportedOperation_Cluster, fmt.Sprintf("Cluster is not support %s session mode, "+
			"please contact us to upgrade control", req.FlinkVersion), nil
	}
	if err != nil {
		logger.Errorf("Failed to CheckSupportSession for %s because %+v", clusterGroup.SerialId, err)
		return controller.InvalidParameterValue, fmt.Sprintf("%v", err), nil
	}
	// 4.2 校验有没有设置被禁用的的参数，如果有则返回提示
	flinkSessionDisabledConfiguration, err := configure_center.CC(clusterGroup.Region).FlowCC().ClusterSessionCC().FlinkSessionDisabledConfiguration()
	if err != nil {
		logger.Errorf("Failed to get FlinkSessionDisabledConfiguration for %s because %+v", clusterGroup.Region, err)
		return controller.InvalidParameterValue, fmt.Sprintf("%v", err), nil
	}
	disabledConfigurations := make([]string, 0)
	err = json.Unmarshal([]byte(flinkSessionDisabledConfiguration), &disabledConfigurations)
	if err != nil {
		// 防御式编程, 理论上不会格式错乱
		return controller.InternalError, "FlinkSessionDisabledConfiguration JSON unmarshal error", nil
	}
	unsupportedFlinkConf := make([]string, 0)
	properties := req.Properties
	for _, property := range properties {
		key := property.Key
		if service2.ContainsString(disabledConfigurations, strings.TrimSpace(key)) {
			unsupportedFlinkConf = append(unsupportedFlinkConf, key)
		}
	}
	if len(unsupportedFlinkConf) > 0 {
		return controller.InvalidParameterValue, "ProhibitedFlinkConfiguration: " + strings.Join(unsupportedFlinkConf, ","), nil
	}

	// 5. 如果是tke集群则需要校验集群可用CU数是否满足条件(JmCuSpec + TmCuSpec * TmNum <= 独立集群可用Cu数)
	needCpu := req.JobManagerCuSpec + req.TaskManagerCuSpec*float32(req.TaskManagerNum)
	needMem := needCpu * float32(cluster.MemRatio)
	if req.JobManagerCpu != 0 {
		needCpu = req.JobManagerCpu + req.TaskManagerCpu*float32(req.TaskManagerNum)
		needMem = req.JobManagerMem + req.TaskManagerMem*float32(req.TaskManagerNum)
	}
	needCuNum := service2.GetCuNumFromCpuMem(needCpu, needMem, cluster.MemRatio)
	if cluster.SchedulerType == constants.CLUSTER_SCHEDULER_TYPE_TKE && !isEks {
		totalRunningCpu, totalRunningMem, err := service2.GetClusterRunningCpuAndMem(clusterGroup.SerialId, clusterGroup.AppId, clusterGroup.Region, cluster.MemRatio)
		if err != nil {
			logger.Errorf("Failed to GetRunningCU for %s because %+v", clusterGroup.SerialId, err)
			return controller.InvalidParameterValue, fmt.Sprintf("%v", err), nil
		}
		clusterTotalCu := service2.GetClusterTotalCU(clusterGroup)
		freeCpu := clusterTotalCu - totalRunningCpu
		freeMem := clusterTotalCu*float32(cluster.MemRatio) - totalRunningMem

		if needCpu > freeCpu || needMem > freeMem {
			logger.Errorf("ClusterGroup: %s, clusterTotalCu %f totalRunningCpu: %f, totalRunningMem: %f, NeedCpu: %f, NeedMem: %f",
				clusterGroup.SerialId, clusterTotalCu, totalRunningCpu, totalRunningMem, needCpu, needMem)
			return controller.ResourceInsufficient, "Resource is insufficient", nil
		}
	}
	// 6. 校验集群在ClusterSession中记录以及状态(没有记录\开启失败\停止状态才可以再次开启session集群)
	ClusterSession, _ := service2.GetClusterSessionBySerialIdAndFlinkVersion(req.ClusterGroupSerialId, req.FlinkVersion)
	if nil != ClusterSession && ClusterSession.Status != constants.ClusterSessionFailed && ClusterSession.Status != constants.ClusterSessionStopped {
		logger.Errorf("ClusterGroup: %s, Session cluster is operating", clusterGroup.SerialId)
		return controller.FailedOperation_CreateCluster, "Session cluster is operating", nil
	}
	regionId, err := service.GetRegionIdByName(clusterGroup.Region)
	if err != nil {
		logger.Errorf("[%s] GetRegionIdByName error: %+v", requestId, err)
		regionId = 0
	}

	rsp := &model_capi_cluster.ClusterSessionRspWrapper{}
	// 7. 生成并检验 resource url & resource ref
	clusterSessionRefs := make([]*table.ClusterSessionRef, 0)
	locs := make([]*model4.ResourceLocation, 0)
	for _, ref := range req.ResourceRefs {
		resourceConfig, err := GetResourceConfigByResourceRef(&model5.ResourceRefItem{
			ResourceId: ref.ResourceId,
			Type:       ref.Type,
			Version:    ref.Version,
		})

		if err != nil {
			logger.Errorf("[%s] Failed to GetResourceConfigByResourceRef,error: %+v", req.RequestId, err)
			return controller.InternalError, fmt.Sprintf("%v", err), nil
		}

		loc := &model4.ResourceLocation{}
		err = json.Unmarshal([]byte(resourceConfig.ResourceLoc), loc)
		if err != nil {
			logger.Errorf("[%s] Failed to Unmarshal,error: %+v", req.RequestId, err)
			return controller.InternalError, fmt.Sprintf("%v", err), nil
		}

		locs = append(locs, loc)
		clusterSessionRefs = append(clusterSessionRefs, &table.ClusterSessionRef{
			ResourceId: resourceConfig.ResourceId,
			VersionId:  ref.Version,
			Status:     constants.RESOURCE_REF_STATUS_ACTIVE,
			Type:       ref.Type,
			CreateTime: util.GetCurrentTime(),
			UpdateTime: util.GetCurrentTime(),
		})
	}
	// upload path : user/user_00/.flink/sgw-xxx/1695120670803/systemShipFiles
	generateSessionClusterRefs := func(clusterGroupSerialId string, locs []*model4.ResourceLocation) (path string, refs string, err error) {
		ClusterSessionResourceItems := make([]*ClusterSessionResourceItem, 0)
		uploadPath := fmt.Sprintf(SESSION_CLUSTER_JARS_UPLOAD_PATH, clusterGroupSerialId, time.Now().UnixNano()/1e6)
		for _, loc := range locs {
			inOceanus, err := service_bucket.BucketExists(loc.Param.Bucket)
			if err != nil {
				logger.Errorf("[%s] Failed to  BucketExists,err: %+v", req.RequestId, err)
				panic(errorcode.NewStackError(errorcode.InternalErrorCode_GetBucketFailed, "Failed to get GetSecretIdAndKeyOfCos", err))
			}
			var secretId, secretKey, token string
			var pass bool
			if inOceanus {
				secretId, secretKey, err = service2.GetSecretIdAndKeyOfCos()
				if err != nil {
					logger.Errorf("[%s] Failed to GetSecretIdAndKeyOfCos,err: %+v", req.RequestId, err)
					panic(errorcode.NewStackError(errorcode.InternalErrorCode_DoSqlFailed, "Failed to get GetSecretIdAndKeyOfCos", err))
				}
			} else {
				secretId, secretKey, token, pass, err = service2.StsAssumeRole(req.Uin, req.SubAccountUin, req.Region)
				if err != nil {
					logger.Errorf("[%s] Failed to GetSecretIdAndKeyOfCos,err: %+v", req.RequestId, err)
					panic(errorcode.NewStackError(errorcode.InternalErrorCode_GetStsAssumeRoleFailed, "Failed to get GetSecretIdAndKeyOfCos", err))
				}
				if !pass {
					logger.Errorf("[%s] No permission to access the user's bucket %s. ", req.RequestId, loc.Param.Bucket)
					panic(errorcode.NewStackError(errorcode.InternalErrorCode_StsAssumeRoleNoAccess, fmt.Sprintf("[%s] No permission to access the user's bucket %s. ", req.RequestId, loc.Param.Bucket), err))
				}
			}
			cosClient := cos.NewCosClient(secretId, secretKey, token, loc.Param.Region, loc.Param.Bucket)
			url, err := cosClient.GetPresignedURL(loc.Param.Path, http.MethodGet, time.Hour)
			if err != nil {
				logger.Errorf("[%s] Failed to GetPresignedUrl,err: %+v", req.RequestId, err)
				return "", "", err
			}
			logger.Infof("[%s] Path %s GetPresignedURL %s", req.RequestId, loc.Param.Path, url)
			_, fileName := filepath.Split(loc.Param.Path)
			ClusterSessionResourceItems = append(ClusterSessionResourceItems, &ClusterSessionResourceItem{
				Url:  url,
				Path: uploadPath + "/" + fileName,
			})
		}
		marshaled, err := json.Marshal(ClusterSessionResourceItems)
		if err != nil {
			logger.Errorf("[%s] Failed to Marshal,err: %+v", req.RequestId, err)
			return "", "", err
		}
		encoded := base64.StdEncoding.EncodeToString(marshaled)
		return uploadPath, encoded, nil
	}

	propArrayBytes, err := json.Marshal(req.Properties)
	if err != nil {
		logger.Errorf("Failed to unmarshal JobConfig.Properties JSON because %+v", err)
		return controller.InternalError, fmt.Sprintf("%v", err), nil
	}
	// 8. 加锁之后保存CusterSession集群信息以及创建开启Session集群的Flow
	//lock
	locker := dlocker.NewDlocker("oceanus-create-session-cluster", fmt.Sprintf("cluster-session-%s", req.ClusterGroupSerialId), 3600)
	err = locker.Lock()
	if err != nil {
		msg := fmt.Sprintf("lock task failed![ %s ]", err.Error())
		logger.Error(msg)
		return controller.InternalError, msg, nil
	}
	defer locker.UnLock()

	// 开启事物保存ClusterSession信息
	var path, refs string
	var clusterSessionId int64
	txManager := service.GetTxManager()
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		newClusterSession := &table.ClusterSession{
			ClusterGroupSerialId: req.ClusterGroupSerialId,
			AppId:                req.AppId,
			OwnerUin:             req.Uin,
			CreatorUin:           req.SubAccountUin,
			Region:               req.Region,
			Zone:                 req.Zone,
			Status:               constants.ClusterSessionCreating,
			CuNum:                needCuNum,
			FlinkVersion:         req.FlinkVersion,
			Properties:           string(propArrayBytes),
			JobManagerCuSpec:     req.JobManagerCuSpec,
			TaskManagerCuSpec:    req.TaskManagerCuSpec,
			TaskManagerNum:       req.TaskManagerNum,
			CreateTime:           util.GetCurrentTime(),
			UpdateTime:           util.GetCurrentTime(),
			JobManagerCpu:        req.JobManagerCpu,
			JobManagerMem:        req.JobManagerMem,
			TaskManagerCpu:       req.TaskManagerCpu,
			TaskManagerMem:       req.TaskManagerMem,
		}
		if ClusterSession == nil {
			// 集群没有Session记录则是第一次开启Session集群
			clusterSessionId = tx.SaveObject(newClusterSession, "ClusterSession")
		} else {
			newClusterSession.Id = ClusterSession.Id
			clusterSessionId = ClusterSession.Id
			tx.UpdateObject(newClusterSession, newClusterSession.Id, "ClusterSession")
			// delete clusterSessionRefs
			deleteClusterSessionRefs := "UPDATE ClusterSessionRef SET Status = ? WHERE SessionClusterId = ? "
			tx.ExecuteSqlWithArgs(deleteClusterSessionRefs, constants.RESOURCE_REF_STATUS_DELETE, ClusterSession.Id)
		}

		if len(clusterSessionRefs) > 0 {
			path, refs, err = generateSessionClusterRefs(req.ClusterGroupSerialId, locs)
			if err != nil {
				logger.Errorf("[%s] Failed to generate ClusterSessionRefs,error: %+v", req.RequestId, err)
				return err
			}
			// save clusterSessionRefs
			for _, clusterSessionRef := range clusterSessionRefs {
				clusterSessionRef.SessionClusterId = clusterSessionId
				tx.SaveObject(clusterSessionRef, "ClusterSessionRef")
			}
		}
		rsp = buildClusterSessionItem(newClusterSession)

		if flowId, err := flow.CreateFlow(constants.FLOW_OCEANUS_CREATE_SESSION_CLUSTER,
			fmt.Sprintf("%s@%s", req.ClusterGroupSerialId, requestId), regionId, map[string]string{
				constants.FLOW_PARAM_REQUEST_ID:             requestId,
				constants.FLOW_PARAM_APPID:                  fmt.Sprintf("%d", req.AppId),
				constants.FLOW_PARAM_OWNERUIN:               req.Uin,
				constants.FLOW_PARAM_CREATORUIN:             req.SubAccountUin,
				constants.FLOW_PARAM_FLINK_VERSION:          req.FlinkVersion,
				constants.FLOW_PARAM_JMCUSPEC:               fmt.Sprintf("%f", req.JobManagerCuSpec),
				constants.FLOW_PARAM_TMCUSPEC:               fmt.Sprintf("%f", req.TaskManagerCuSpec),
				constants.FLOW_PARAM_TMNUM:                  fmt.Sprintf("%d", req.TaskManagerNum),
				constants.FLOW_PARAM_REGION:                 req.Region,
				constants.FLOW_PARAM_PROPERTIES:             string(propArrayBytes),
				constants.FLOW_PARAM_CLUSTER_GROUP_ID:       fmt.Sprintf("%d", clusterGroup.Id),
				constants.FLOW_PARAM_CLUSTER_GROUP_SERIALID: clusterGroup.SerialId,
				constants.FLOW_PARAM_CLUSTER_ID:             fmt.Sprintf("%d", cluster.Id),
				constants.FLOW_PARAM_REFS:                   refs,
				constants.FLOW_PARAM_REFS_PATH:              path,
				constants.FLOW_PARAM_JMCPU:                  fmt.Sprintf("%f", req.JobManagerCpu),
				constants.FLOW_PARAM_JMMEM:                  fmt.Sprintf("%f", req.JobManagerMem),
				constants.FLOW_PARAM_TMCPU:                  fmt.Sprintf("%f", req.TaskManagerCpu),
				constants.FLOW_PARAM_TMMEM:                  fmt.Sprintf("%f", req.TaskManagerMem),
			}, nil); err != nil {
			logger.Error("flow.CreateFlow(constants.FLOW_OCEANUS_CREATE_SESSION_CLUSTER), errors:", err.Error())
			return err
		} else {
			logger.Infof("[%s] createFlow %s success, flowId %d", requestId, constants.FLOW_OCEANUS_CREATE_SESSION_CLUSTER, flowId)
		}
		return nil
	}).Close()
	return controller.OK, controller.NULL, rsp
}

/**
 * 检查是否支持Session集群(sqlserver版本>8.4.0)
 */
func (this *ClusterSessionService) CheckSupportSession(cluster *table.Cluster, flinkVersion string) (bool, error) {
	if !strings.Contains(cluster.SupportedFlinkVersion, flinkVersion) {
		logger.Errorf("DoCreateSessionCluster: not supported, flink version, req: %s", flinkVersion)
		return false, nil
	}

	tv, err := version.GetLastTkeVersion(cluster.Id, "sql-server", "app-container")
	if err != nil || tv == nil {
		return false, err
	}
	if flinkVersion == constants.FLINK_VERSION_1_13 && version.CompareImageVersion(tv.ContainerVersion, SqlServerSupportSession) >= 0 {
		logger.Debugf("resource %s container %s, version %s upper then %s", "sql-server", "app-container", tv.ContainerVersion, SqlServerSupportSession)
		return true, err
	}
	if flinkVersion == constants.FLINK_VERSION_1_16 && version.CompareImageVersion(tv.ContainerVersion,
		SqlServerSupportSessionFlink116) >= 0 {
		return true, nil
	}
	if flinkVersion == constants.FLINK_VERSION_1_18 && version.CompareImageVersion(tv.ContainerVersion,
		SqlServerSupportSessionFlink118) >= 0 {
		return true, nil
	}
	if flinkVersion == constants.FLINK_VERSION_1_20 && version.CompareImageVersion(tv.ContainerVersion,
		SqlServerSupportSessionFlink120) >= 0 {
		return true, nil
	}
	return false, nil
}

func NewClusterSessionService() *ClusterSessionService {
	return &ClusterSessionService{}
}

func GetClusterSessionService() *ClusterSessionService {
	if clusterSessionService == nil {
		clusterSessionService = NewClusterSessionService()
	}
	return clusterSessionService
}

func GetSessionClusterRefItems(SessionClusterId int64) (sessionClusterRefItems []*model_capi_cluster.SessionClusterRefItem, err error) {

	cond := dao.NewCondition()
	cond.Eq("a.SessionClusterId", SessionClusterId)
	cond.Eq("a.Status", constants.RESOURCE_STATUS_ACTIVE)
	where, args := cond.GetWhere()
	sql := " SELECT b.ResourceId,a.VersionId AS Version,a.Type,c.SerialId AS WorkspaceId FROM ClusterSessionRef a " +
		"JOIN Resource b ON a.ResourceId = b.Id JOIN ItemSpace c ON b.ItemSpaceId = c.Id " + where
	_, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return nil, err
	}
	sessionClusterRefItems = make([]*model_capi_cluster.SessionClusterRefItem, 0)
	for i := 0; i < len(data); i++ {
		sessionClusterRefItem := &model_capi_cluster.SessionClusterRefItem{}
		err = util.ScanMapIntoStruct(sessionClusterRefItem, data[i])
		if err != nil {
			return nil, err
		}
		sessionClusterRefItems = append(sessionClusterRefItems, sessionClusterRefItem)
	}
	return sessionClusterRefItems, nil
}

func buildClusterSessionItem(session *table.ClusterSession) *model_capi_cluster.ClusterSessionRspWrapper {
	props := make([]*model3.Property, 0)
	json.Unmarshal([]byte(session.Properties), &props)
	refItems, err := GetSessionClusterRefItems(session.Id)
	if err != nil {
		return nil
	}
	clusterSessionItem := &model_capi_cluster.ClusterSessionRsp{
		ClusterGroupSerialId: session.ClusterGroupSerialId,
		AppId:                session.AppId,
		OwnerUin:             session.OwnerUin,
		CreatorUin:           session.CreatorUin,
		Region:               session.Region,
		Zone:                 session.Zone,
		Status:               session.Status,
		CuNum:                session.CuNum,
		FlinkVersion:         session.FlinkVersion,
		Properties:           props,
		ResourceRefs:         refItems,
		JobManagerCuSpec:     session.JobManagerCuSpec,
		TaskManagerCuSpec:    session.TaskManagerCuSpec,
		TaskManagerNum:       session.TaskManagerNum,
		CreateTime:           session.CreateTime,
		UpdateTime:           session.UpdateTime,
		JobManagerCpu:        session.JobManagerCpu,
		JobManagerMem:        session.JobManagerMem,
		TaskManagerCpu:       session.TaskManagerCpu,
		TaskManagerMem:       session.TaskManagerMem,
	}

	return &model_capi_cluster.ClusterSessionRspWrapper{ClusterSession: clusterSessionItem}
}
