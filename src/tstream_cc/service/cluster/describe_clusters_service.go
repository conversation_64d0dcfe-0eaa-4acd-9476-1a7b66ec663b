package service

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/shopspring/decimal"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"sync"
	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	service4 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	model3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/billing"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/cluster"
	model_capi_cluster "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/k8s"
	billingTable "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/billing"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	table4 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	tableItemSpace "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/item_space"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/tke"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/tag"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cls"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config/configure_center"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource_auth"
	model2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource_auth/model"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
	tag2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/tag"
	"time"
)

/**
 *
 * <AUTHOR>
 * @time 2019/4/28
 * @version 0.4.0
 * @since 0.4.0
 * @see
 */

type describeClusterContext struct {
	req                   *model.DescribeClustersReq
	totalCount            int
	clusterGroups         []*table.ClusterGroup
	clusterGroupIds       []int64
	clusterGroupStringIds []string
	billingResourceMap    map[string]*billingTable.BillingResource
	billingOrdersMap      map[string][]*model.Order
	CCNsMap               map[int64][]*model.CCN
	activeCluster         map[int64]*table.Cluster
	tkeMap                map[int64]*table2.Tke //clusterId,tke
	clusterTag            map[string][]*tag.Tag
	CLSLogSetName         map[string]string
	CLSLogTopicName       map[string]string
	clusterSetItem        []*model.ClusterSetItem
	Correlations          map[int64][]*tableItemSpace.ItemSpaceClusterItem // clusterGroup 和 ItemSpace 的关系
}

func DoDescribeClusters(req *model.DescribeClustersReq) (rsp *model.DescribeClustersRsp, err error) {
	context := &describeClusterContext{req: req}
	return context.DescribeClusters()
}

func (d *describeClusterContext) DescribeClusters() (rsp *model.DescribeClustersRsp, err error) {
	clusterSet := make([]*model.ClusterSetItem, 0)
	defer errorcode.DefaultDeferHandler(&err)

	if err = d.validateReq(); err != nil {
		return nil, err
	}

	var empty bool
	d.req.ClusterIds, empty, err = d.getAccessibleClusters()
	if err != nil {
		return nil, err
	}
	if empty {
		return &model.DescribeClustersRsp{TotalCount: 0, ClusterSet: clusterSet}, nil
	}

	// 鉴权 (子账号，只能看见自己空间内绑定的集群，超管可以看到所有)
	authClusterIds, err, empty2, _ := auth.ExtractSubUinAuthClustersSet(d.req.WorkSpaceId, d.req.IsSupOwner, d.req.ClusterIds, d.req.AppId, d.req.SubAccountUin, d.req.Uin)
	if err != nil {
		logger.Errorf("%s: ExtractSubUinAuthClustersSet : Obtain  clusterGroups that has permissions   error: %+v", d.req.RequestId, err)
		return nil, err
	}
	if empty2 {
		return &model.DescribeClustersRsp{TotalCount: 0, ClusterSet: clusterSet}, nil
	}
	if authClusterIds != nil {
		d.req.ClusterIds = authClusterIds
	}
	if err = d.getClusterGroupFromDB(); err != nil {
		return nil, err
	}

	if len(d.clusterGroups) == 0 {
		return &model.DescribeClustersRsp{TotalCount: d.totalCount, ClusterSet: clusterSet}, nil
	}
	if err = d.getClusterGroupOtherInfo(); err != nil {
		return nil, err
	}
	if err = d.BuildClusterSetItems(); err != nil {
		return nil, err
	}

	describeClustersRsp := &model.DescribeClustersRsp{TotalCount: d.totalCount, ClusterSet: d.clusterSetItem}

	return describeClustersRsp, nil
}

func (d *describeClusterContext) getClusterGroupFromDB() (err error) {
	nameFilters := GetClusterNameFiltersFromReq(d.req)

	isFilterSetats := GetIsSetatsFiltersFromReq(d.req)
	if isFilterSetats {
		setatsList, err := ListSetats(&ListSetatsParam{
			AppId:                 d.req.AppId,
			ClusterGroupSerialIds: d.req.ClusterIds,
		})
		if err != nil {
			logger.Errorf("ListSetats err %+v", err)
			return err
		}
		if len(setatsList) < 1 {
			// 设置不存在的集群Id
			d.req.ClusterIds = []string{constants.OCEANUS_FAKE_CLUSTER_SERIAL_ID}
		} else {
			clusterIds := make([]string, 0, len(setatsList))
			for _, setats := range setatsList {
				clusterIds = append(clusterIds, setats.ClusterGroupSerialId)
			}
			d.req.ClusterIds = clusterIds
		}
		logger.Infof("### ListSetats clusterIds %+v", d.req.ClusterIds)
	}
	// 2. 查询独享集群
	d.clusterGroups, err = service2.ListClusterGroups(&service2.ListClusterGroupsParam{
		AppId:        int32(d.req.AppId),
		Regions:      []string{d.req.Region},
		Zone:         "",
		ClusterType:  []int8{constants.CLUSTER_GROUP_TYPE_PRIVATE, constants.CLUSTER_GROUP_TYPE_UNIFORM},
		ClusterNames: nameFilters,
		IsVagueNames: true,
		SerialIds:    d.req.ClusterIds,
		Offset:       d.req.Offset,
		Limit:        d.req.Limit,
		OrderType:    d.req.OrderType,
		StatusList:   nil,
	})
	if err != nil {
		return err
	}
	d.clusterGroupIds = make([]int64, 0, len(d.clusterGroups))
	d.clusterGroupStringIds = make([]string, 0, len(d.clusterGroups))
	for _, c := range d.clusterGroups {
		d.clusterGroupIds = append(d.clusterGroupIds, c.Id)
		d.clusterGroupStringIds = append(d.clusterGroupStringIds, c.SerialId)
	}

	// 3. 获取独享集群总数
	d.totalCount, err = GetClusterGroupTotalCount(int32(d.req.AppId), d.req.Region, "",
		constants.CLUSTER_GROUP_TYPE_PRIVATE,
		nameFilters, true, d.req.ClusterIds)
	if err != nil {
		return err
	}
	return nil
}

func (d *describeClusterContext) getClusterGroupOtherInfo() error {
	getInfoFuncs := []func() error{
		d.getBillingResourceInfo,
		d.getBillingOrders,
		d.getCCNS,
		d.getActiveCluster,
		d.getTke,
		d.getClusterTags,
		d.getClustersInItemSpace,
	}

	wg := sync.WaitGroup{}
	wg.Add(len(getInfoFuncs))
	funcResult := make(chan error, len(getInfoFuncs))
	for _, get := range getInfoFuncs {
		go func(f func() error) {
			funcResult <- f()
			wg.Done()
		}(get)
	}
	wg.Wait()
	close(funcResult)

	for err := range funcResult {
		if err != nil {
			return err
		}
	}
	return nil
}

func (d *describeClusterContext) getBillingResourceInfo() (err error) {
	d.billingResourceMap, err = GetClusterGroupBillingInfo(d.clusterGroups)
	return err
}

func (d *describeClusterContext) getBillingOrders() (err error) {
	d.billingOrdersMap, err = GetBillingOrders(d.clusterGroups)
	return err
}

func (d *describeClusterContext) getCCNS() (err error) {
	d.CCNsMap, err = ListCCNs(d.clusterGroupIds)
	return err
}

func (d *describeClusterContext) getActiveCluster() (err error) {
	d.activeCluster, err = ListActiveClusters(d.clusterGroupIds)
	if err != nil {
		return err
	}
	// if err = adjustClusterClsInfo(d.req.Uin, d.req.Region, d.activeCluster); err != nil {
	// 	return err
	// }
	// 上海驾驶云不调用，访问不通
	if d.req.Region == constants.AP_SHANGHAI_ADC {
		return nil
	}

	// 显示集群列表的时候， 不需要去获取CLS 的名字，只有详情页需要。 CLS 的接口有点慢，每次都需要1秒多
	if len(d.req.ClusterIds) == 1 {
		return d.getClusterCLSInfo()
	}
	return nil
}

func (d *describeClusterContext) getTke() (err error) {
	d.tkeMap, err = ListTke(d.clusterGroupIds)
	return err
}

func (d *describeClusterContext) getClusterTags() (err error) {
	tags, err := tag2.GetTagService().GetResourceTags(d.req.Uin, d.req.Region, d.clusterGroupStringIds, resource_auth.RESOURCE_PREFIX_CLUSTER)
	if err != nil {
		return err
	}
	d.clusterTag = make(map[string][]*tag.Tag, 0)
	for _, t := range tags {
		d.clusterTag[t.ResourceId] = append(d.clusterTag[t.ResourceId],
			&tag.Tag{
				TagKey:   t.TagKey,
				TagValue: t.TagValue,
			})
	}
	return nil
}

func (d *describeClusterContext) getClusterCLSInfo() (err error) {
	// 为了方便后续，获取集群列表也返回CLS 名字信息
	// getClusterCLSLogSetName getClusterCLSTopicName 仍然按照 批量的方式在做
	wg := sync.WaitGroup{}
	wg.Add(2)
	go func() {
		_ = d.getClusterCLSLogSetName()
		wg.Done()
	}()
	go func() {
		_ = d.getClusterCLSTopicName()
		wg.Done()
	}()
	wg.Wait()
	return nil
}

func (d *describeClusterContext) getClusterCLSLogSetName() (err error) {
	logSets := make([]string, 0)
	for _, it := range d.activeCluster {
		if it.ClsLogSet != "" {
			logSets = append(logSets, it.ClsLogSet)
		}
	}

	//logSetInfo, err := cls.GetLogSetMulti(d.req.Uin, d.req.SubAccountUin, d.req.Region, logSets)
	adapter := &cls.CloudApiAdapter{}
	logSetInfo, err := adapter.GetLogSetMulti(d.req.Uin, d.req.SubAccountUin, d.req.Region, logSets)
	if err != nil {
		// 只打印日志， 作为查问题用。
		logger.Warningf("%s: GetLogSetMulti err %+v", d.req.RequestId, err)
	}
	d.CLSLogSetName = make(map[string]string)
	for _, it := range logSetInfo {
		d.CLSLogSetName[it.LogSetId] = it.LogSetName
	}
	return nil
}

func (d *describeClusterContext) getClusterCLSTopicName() (err error) {
	topicSets := make([]string, 0)
	for _, it := range d.activeCluster {
		if it.ClsTopicId != "" {
			topicSets = append(topicSets, it.ClsTopicId)
		}
	}

	topicSetInfo, err := cls.GetLogTopicMulti(d.req.Uin, d.req.SubAccountUin, d.req.Region, topicSets)
	if err != nil {
		// 只打印日志， 作为查问题用。
		logger.Warningf("%s: GetLogTopicMulti err %+v", d.req.RequestId, err)
	}
	d.CLSLogTopicName = make(map[string]string)
	for _, it := range topicSetInfo {
		d.CLSLogTopicName[it.TopicId] = it.TopicName
	}
	return nil
}

func (d *describeClusterContext) buildClusterSetItemFromClusterGroup(clusterGroup *table.ClusterGroup, freeCu float32, clusterRunningCu float32) *model.ClusterSetItem {
	clusterSetItem := &model.ClusterSetItem{}

	clusterSetItem.ClusterId = clusterGroup.SerialId
	clusterSetItem.Name = clusterGroup.Name
	clusterSetItem.Region = clusterGroup.Region
	clusterSetItem.Zone = clusterGroup.Zone
	clusterSetItem.AppId = clusterGroup.AppId
	clusterSetItem.OwnerUin = clusterGroup.OwnerUin
	clusterSetItem.CreatorUin = clusterGroup.CreatorUin
	clusterSetItem.Type = clusterGroup.Type
	clusterSetItem.CuNum = clusterGroup.CuNum
	clusterSetItem.CuMem = clusterGroup.CuMem
	clusterSetItem.Status = clusterGroup.Status
	clusterSetItem.StatusDesc = GetStatusDescByStatus(clusterGroup.Status)
	clusterSetItem.CreateTime = service2.SwitchDefaultTime(clusterGroup.CreateTime)
	clusterSetItem.UpdateTime = service2.SwitchDefaultTime(clusterGroup.UpdateTime)
	// 如果集群处于正在运行和初始化状态，集群详情中的时间返回-，如果是删除状态，则返回删除时间
	switch clusterGroup.Status {
	case constants.CLUSTER_GROUP_STATUS_CREATING, constants.CLUSTER_GROUP_STATUS_RUNNING:
		clusterSetItem.UpdateTime = "-"
	}
	clusterSetItem.Remark = clusterGroup.Remark
	if ccns, ok := d.CCNsMap[clusterGroup.Id]; ok {
		clusterSetItem.CCNs = ccns
	} else {
		clusterSetItem.CCNs = []*model.CCN{}
	}
	if cluster, ok := d.activeCluster[clusterGroup.Id]; ok {
		clusterSetItem.MemRatio = cluster.MemRatio
		clusterSetItem.CrossTenantEniMode = cluster.CrossTenantEniMode
		clusterSetItem.WebUIType = cluster.WebUIType
		clusterSetItem.DefaultCOSBucket = cluster.DefaultCOSBucket
		clusterSetItem.Version = &model.Version{
			Flink:          cluster.FlinkVersion,
			SupportedFlink: cluster.GetSupportedFlinkVersion(),
		}
		clusterSetItem.CLSLogSet = cluster.ClsLogSet
		clusterSetItem.CLSTopicId = cluster.ClsTopicId
		clusterSetItem.DefaultLogCollectConf = cluster.DefaultLogCollectConf
		if v, ok := d.CLSLogSetName[cluster.ClsLogSet]; ok {
			clusterSetItem.CLSLogName = v
		}
		if v, ok := d.CLSLogTopicName[cluster.ClsTopicId]; ok {
			clusterSetItem.CLSTopicName = v
		}

		if t, ok := d.tkeMap[cluster.Id]; ok {
			clusterSetItem.ClusterType = t.ClusterType
			clusterSetItem.ArchGeneration = t.ArchGeneration
		}

		if clusterExtendConfig, err := cluster.GetClusterExtendConfig(); err == nil {
			if config, exist := clusterExtendConfig[constants.ClusterExtendConfigKeyDNS]; exist {
				dnsConfig := &model.DescribeClusterDNSRsp{}
				if err = DecodeClusterExtendConfigDNS(config, dnsConfig); err == nil && !dnsConfig.Emtpy() {
					clusterSetItem.CustomizedDNSEnabled = constants.CustomizedDNSEnabled
				}
			}
		}
		if !SupportCustomizedDNS(cluster) {
			clusterSetItem.CustomizedDNSEnabled = constants.CustomizedDNSNotSupported
		}
		// add sql gateways
		sqlGateways, err := GetSqlGatewayItemsByClusterId(cluster.Id, clusterSetItem.MemRatio)
		if err != nil {
			logger.Errorf("[%s] Failed to GetSqlGateways ,ClusterId %s ,because %+v", d.req.RequestId, clusterGroup.SerialId, err)
		}
		clusterSetItem.SqlGateways = sqlGateways

		// add Setats
		setatsInfo, err := GetSetatsInfoByClusterGroupSerialId(clusterGroup.SerialId, clusterGroup.Id)
		if err != nil {
			logger.Errorf("[%s] Failed to GetSetatsInfoByClusterGroupSerialId ,ClusterId %s ,because %+v", d.req.RequestId, clusterGroup.SerialId, err)
		} else {
			if setatsInfo != nil {
				setatsInfo.VpcId = cluster.VpcId
				setatsInfo.SubnetId = cluster.SubnetId
			}
			clusterSetItem.Setats = setatsInfo
		}

	}
	clusterSetItem.NetEnvironmentType = clusterGroup.NetEnvironmentType
	clusterSetItem.FreeCuNum = int16(freeCu)
	clusterSetItem.FreeCu = freeCu
	clusterRunningCu2, _ := decimal.NewFromFloat32(clusterRunningCu).Round(2).Float64()
	clusterSetItem.RunningCu = float32(clusterRunningCu2)
	clusterSetItem.AgentSerialId = clusterGroup.AgentSerialId
	clusterSetItem.ResourceType = int(clusterGroup.ResourceType)

	v, ok := d.billingResourceMap[clusterGroup.SerialId]

	/**
	 * 走了计费的 外部集群
	 */
	if ok {
		clusterSetItem.BillingResourceMode = v.BillingResourceMode
		clusterSetItem.PayMode = v.PayMode
		// 预付费
		if v.PayMode == billing.PayModePrepaid {
			clusterSetItem.IsolatedTime = service2.SwitchDefaultTime(v.IsolatedTimestamp)
			clusterSetItem.ExpireTime = service2.SwitchDefaultTime(v.ExpireTime)
			clusterSetItem.SecondsUntilExpiry = calculateSecondsUntilExpiry(clusterSetItem.ExpireTime)
			clusterSetItem.AutoRenewFlag = v.AutoRenewFlag
			clusterSetItem.IsNeedManageNode = v.IsNeedManageNode
			// 包销模式过期时间
			if v.BillingResourceMode == billing.ExclusiveSale {
				if v.Duration != "" {
					parts := strings.Split(v.Duration, billing.TimeUnitYear)
					strTimeSpan := parts[0]
					logger.Infof("exclusiveSale strTimeSpan is %s", strTimeSpan)
					timeSpan, err := strconv.ParseInt(strTimeSpan, 10, 64)
					if err == nil {
						logger.Infof("exclusiveSale timeSpan is %d", timeSpan)
						// 3. ExpireTime
						exclusiveSaleStartTime, err := time.ParseInLocation(billing.StandardTimestampFormatString, v.ExclusiveSaleStartTime, time.Local)
						if err == nil {
							expireTime := CalculateExpireTime(exclusiveSaleStartTime, billing.TimeUnitYear, int(timeSpan)).
								Format(billing.StandardTimestampFormatString)
							logger.Infof("exclusiveSale expireTime is %s", expireTime)
							clusterSetItem.ExpireTime = expireTime
							clusterSetItem.SecondsUntilExpiry = calculateSecondsUntilExpiry(expireTime)
						} else {
							logger.Errorf("Failed to parse exclusiveSaleStartTime %s because %+v", v.ExclusiveSaleStartTime, err)
						}
					} else {
						logger.Errorf("Failed to parse timeSpan because %+v", err)
					}

				}
			}
		}
		// 后付费，没有过期时间，只有隔离时间，隔离7天后，销毁
		if v.PayMode == billing.PayModePost {
			if v.IsolatedTimestamp == billing.NotIsolatedTimestamp {
				clusterSetItem.IsolatedTime = "-"
				clusterSetItem.ExpireTime = "-"
				clusterSetItem.SecondsUntilExpiry = "-"
				clusterSetItem.AutoRenewFlag = 0
			} else {
				_expireTime, err := time.ParseInLocation(billing.StandardTimestampFormatString, v.IsolatedTimestamp, time.Local)
				if err != nil {
					logger.Errorf("Failed to parse ExpireTime %s because %+v", v.IsolatedTimestamp, err)
					clusterSetItem.IsolatedTime = "-"
					clusterSetItem.ExpireTime = "-"
					clusterSetItem.SecondsUntilExpiry = "-"
					clusterSetItem.AutoRenewFlag = 0
				} else {
					// 隔离七天后销毁
					_expireTime = _expireTime.AddDate(0, 0, 7)
					clusterSetItem.IsolatedTime = service2.SwitchDefaultTime(v.IsolatedTimestamp)
					clusterSetItem.ExpireTime = service2.SwitchDefaultTime(_expireTime.Format(billing.StandardTimestampFormatString))
					clusterSetItem.SecondsUntilExpiry = calculateSecondsUntilExpiry(_expireTime.Format(billing.StandardTimestampFormatString))
					clusterSetItem.AutoRenewFlag = 0
				}
			}
		}
	} else {
		/**
		 * 内部客户区分 后付费和 预付费
		 */
		isEks, _ := IsEks(clusterGroup.Id)
		if isEks {
			clusterSetItem.PayMode = billing.PayModePost
		} else {
			clusterSetItem.PayMode = billing.PayModePrepaid
		}
		clusterSetItem.IsolatedTime = "-"
		clusterSetItem.ExpireTime = "-"
		clusterSetItem.SecondsUntilExpiry = "-"
		clusterSetItem.AutoRenewFlag = 0
		clusterSetItem.IsNeedManageNode = constants.NOT_NEED_MANAGE_NODE
	}

	if tag, ok := d.clusterTag[clusterGroup.SerialId]; ok {
		clusterSetItem.Tags = tag
	}

	if orders, ok := d.billingOrdersMap[clusterGroup.SerialId]; ok {
		clusterSetItem.Orders = orders
	}

	if items, ok := d.Correlations[clusterGroup.Id]; ok {
		clusterSetItem.Correlations = items
	} else {
		clusterSetItem.Correlations = []*tableItemSpace.ItemSpaceClusterItem{}
	}
	return clusterSetItem
}

// 计算资源到期时间（增加 TimeSpan 指定的月数)
func CalculateExpireTime(fromTime time.Time, timeUnit string, timeSpan int) time.Time {
	if timeUnit != billing.TimeUnitMonth && timeUnit != billing.TimeUnitDay && timeUnit != billing.TimeUnitYear {
		panic("Invalid TimeUnit, should be `m` or `d` or `y` only")
	}

	if timeUnit == billing.TimeUnitMonth {
		return fromTime.AddDate(0, timeSpan, 0)
	} else if timeUnit == billing.TimeUnitDay {
		return fromTime.AddDate(0, 0, timeSpan)
	} else if timeUnit == billing.TimeUnitYear {
		return fromTime.AddDate(timeSpan, 0, 0)
	} else {
		panic(errorcode.NewStackError(errorcode.InternalErrorCode, "Unsupported TimeUnit", nil))
	}
}

func (d *describeClusterContext) BuildClusterSetItems() (err error) {

	waitGroupLen := len(d.clusterGroups)
	wg := sync.WaitGroup{}
	wg.Add(waitGroupLen)
	clusterSetItemChn := make(chan interface{}, waitGroupLen)
	for i := 0; i < len(d.clusterGroups); i++ {
		clusterGroup := d.clusterGroups[i]
		fn := func(clusterGroup *table.ClusterGroup) interface{} {
			var memRatio int8 = constants.CU_MEM_4GB
			if cluster, ok := d.activeCluster[clusterGroup.Id]; ok {
				memRatio = cluster.MemRatio
			}

			totalClusterRunningCpu, totalClusterRunningMem, err :=
				service2.GetClusterRunningCpuAndMem(clusterGroup.SerialId, clusterGroup.AppId, clusterGroup.Region, memRatio)
			if err != nil {
				return err
			}
			totalCpu := float32(clusterGroup.CuNum)
			totalMem := float32(clusterGroup.CuNum) * float32(memRatio)

			var clusterRunningCpu float32 = 0.00
			var clusterRunningMem float32 = 0.00
			var clusterRunningCu float32 = 0.00
			var eksRunningCu float32 = 0.00
			var eksRunningCpu float32 = 0.00
			var eksRunningMem float32 = 0.00
			var freeCu float32 = 0
			var freeMem float32 = 0
			var freeCpu float32 = 0
			// 非eks
			if clusterGroup.CuNum != 0 {
				if totalClusterRunningCpu >= totalCpu {
					clusterRunningCpu = totalCpu
					eksRunningCpu = totalClusterRunningCpu - totalCpu
					clusterRunningCu = float32(clusterGroup.CuNum)
					freeCu = 0
				} else {
					freeCpu = totalCpu - totalClusterRunningCpu
					clusterRunningCpu = totalClusterRunningCpu
				}
				if totalClusterRunningMem >= totalMem {
					clusterRunningMem = totalMem
					eksRunningMem = totalClusterRunningMem - totalMem
					clusterRunningCu = float32(clusterGroup.CuNum)
					freeCu = 0
				} else {
					freeMem = totalMem - totalClusterRunningMem
					clusterRunningMem = totalClusterRunningMem
				}
				if freeCpu > 0 {
					freeCu = service2.GetFreeCuNumFromCpuMem(freeCpu, freeMem, memRatio)
				}
				eksRunningCu = service2.GetCuNumFromCpuMem(eksRunningCpu, eksRunningMem, memRatio)
				clusterRunningCu = service2.GetCuNumFromCpuMem(clusterRunningCpu, clusterRunningMem, memRatio)
			} else {
				clusterRunningCu = service2.GetCuNumFromCpuMem(totalClusterRunningCpu, totalClusterRunningMem, memRatio)
				clusterRunningCpu = totalClusterRunningCpu
				clusterRunningMem = totalClusterRunningMem
			}

			clusterSetItem := d.buildClusterSetItemFromClusterGroup(clusterGroup, freeCu, clusterRunningCu)
			clusterSetItem.RunningCpu = service2.GetFloat2Dot(clusterRunningCpu)
			clusterSetItem.RunningMem = service2.GetFloat2Dot(clusterRunningMem)
			clusterSetItem.TotalCpu = totalCpu
			clusterSetItem.TotalMem = totalMem

			// 查询ClusterGroup对应的session集群
			clusterSessions, err := GetClusterSessionsWithRefsBySerialId(clusterGroup.SerialId, clusterSetItem.MemRatio)
			if err != nil {
				return err
			}

			for i := 0; i < len(clusterSessions); i++ {
				webUIURL, err := GetClusterSessionWithRefsWebUIURL(clusterGroup.Id, clusterSessions[i])
				if err != nil {
					return err
				}
				clusterSessions[i].WebUIUrl = webUIURL
			}

			clusterSetItem.ClusterSessions = clusterSessions

			count, subCg, err := service2.ListClusterGroupByParentSerialId(clusterGroup.SerialId)
			if err != nil {
				return err
			}
			if count == 1 {
				if eksRunningCu > float32(subCg.CuNum) {
					eksRunningCu = float32(subCg.CuNum)
				}
				if eksRunningCpu > float32(subCg.CuNum) {
					eksRunningCpu = float32(subCg.CuNum)
				}
				if eksRunningMem > float32(subCg.CuNum)*float32(memRatio) {
					eksRunningMem = float32(subCg.CuNum) * float32(memRatio)
				}
				clusterSetItem.SubEks = &model.SubEks{
					SerialId:   subCg.SerialId,
					CuNum:      subCg.CuNum,
					Status:     subCg.Status,
					StatusDesc: GetStatusDescByStatus(subCg.Status),
					RunningCu:  eksRunningCu,
					RunningCpu: service2.GetFloat2Dot(eksRunningCpu),
					RunningMem: service2.GetFloat2Dot(eksRunningMem),
					TotalCpu:   float32(subCg.CuNum),
					TotalMem:   float32(subCg.CuNum) * float32(memRatio),
				}
			}

			return clusterSetItem
		}

		go func() {
			clusterSetItemChn <- fn(clusterGroup)
			wg.Done()
		}()
	}

	wg.Wait()
	close(clusterSetItemChn)

	for result := range clusterSetItemChn {
		if err, ok := result.(error); ok {
			return err
		} else if clusterSetItem, ok := result.(*model.ClusterSetItem); ok {
			d.clusterSetItem = append(d.clusterSetItem, clusterSetItem)
		}
	}

	sort.Slice(d.clusterSetItem, func(i, j int) bool {
		return d.clusterSetItem[i].CreateTime > d.clusterSetItem[j].CreateTime
	})
	return nil
}

func GetClusterSessionWithRefsWebUIURL(clusterGroupId int64, session *model_capi_cluster.ClusterSessionRsp) (string, error) {
	// 运行中的Session集群需要返回flinkui
	if session.Status == constants.ClusterSessionRunning {
		cluster, err := GetActiveClusterByClusterGroupId(clusterGroupId)
		if err != nil {
			return constants.EMPTY, err
		}
		noDotLowerFlinkVersion := service2.GetNoDotLowerFlinkVersion(session.FlinkVersion)
		clusterSessionAppName := fmt.Sprintf(constants.ClusterSessionAPPTemplate, session.ClusterGroupSerialId, noDotLowerFlinkVersion)
		var webUIPrefix string
		if cluster.CrossTenantEniMode == constants.EnableStaticMode {
			if cluster.WebUIType == constants.PublicType {
				domain := configure_center.CC(session.Region).GetFlinkUiDomain()
				webUIPrefix = "https://" + session.Region + "." + domain + "/" + session.ClusterGroupSerialId + "/"
			} else {
				webUIPrefix = cluster.WebUIPrefix + session.ClusterGroupSerialId + "/"
			}
		} else {
			webUIPrefix = cluster.WebUIPrefix
		}
		dbUrl := webUIPrefix + "/" + clusterSessionAppName + "/#/overview" + "/?defaultToken=" + strconv.Itoa(GetPasswordSetStatus(cluster))
		realURL, err := GetRealURL(cluster, dbUrl)
		if err != nil {
			return "", err
		}
		return realURL, nil
	}
	return constants.EMPTY, nil
}

func GetRealURL(cluster *table.Cluster, dbUrl string) (string, error) {
	features, err := cluster.GetSupportedFeatures()
	if err != nil {
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	for _, v := range features {
		if v == constants.FeatureFLinkUINoPassword {
			// 解析 URL
			webUiUrl, err := url.Parse(dbUrl)
			if err != nil {
				logger.Errorf("https webUiUrl parse error")
				return "", err
			}
			authInfo, err := service3.GetTableService().ListFlinkUiAuthInfo(cluster.ClusterGroupId)
			if err != nil {
				return "", err
			}
			err = authInfo.DecodePassword()
			if err != nil {
				return "", err
			}
			webUiUrl.User = url.UserPassword(k8s.K8S_FLINK_WEBUI_PREFIX_USER, authInfo.Password)
			newURL := webUiUrl.String()
			return newURL, nil
		}
	}

	return dbUrl, nil
}

func GetPasswordSetStatus(cluster *table.Cluster) int {
	tableService := service3.GetTableService()
	if authInfo, err := tableService.ListFlinkUiAuthInfo(cluster.ClusterGroupId); err != nil {
		return k8s.K8S_FLINK_WEBUI_PASSWD_SET
	} else if authInfo != nil {
		if err := authInfo.DecodePassword(); err != nil {
			return k8s.K8S_FLINK_WEBUI_PASSWD_SET
		} else if authInfo.Password == k8s.K8S_FLINK_WEBUI_PREFIX_PASSWD {
			return k8s.K8S_FLINK_WEBUI_PASSWD_NOTSET
		}
	}
	return k8s.K8S_FLINK_WEBUI_PASSWD_SET
}

func (d *describeClusterContext) validateReq() (err error) {
	// 1. 检查clusterIds是否合规 Fixme 检查ClusterIds和Filters是否合法
	if len(d.req.ClusterIds) > constants.OPERATION_BATCH_SIZE_UPPER_LIMIT {
		msg := fmt.Sprintf("Length of ClusterIds is %d, exceed upper limit %d", len(d.req.ClusterIds), constants.OPERATION_BATCH_SIZE_UPPER_LIMIT)
		return errorcode.InvalidParameterValue_ClusterIds.ReplaceDesc(msg)
	}

	switch d.req.OrderType {
	case constants.CLUSTER_INFO_ORDERBY_STATUS,
		constants.CLUSTER_INFO_ORDERBY_CTREATETIME_ASC,
		constants.CLUSTER_INFO_ORDERBY_CTREATETIME_DESC,
		constants.CLUSTER_INFO_ORDERBY_CTREATETIME:
		return err
	default:
		return errorcode.InvalidParameterValueCode.ReplaceDesc(fmt.Sprintf("not support OrderType %d", d.req.OrderType))
	}
}

func GetClusterNameFiltersFromReq(req *model.DescribeClustersReq) []string {
	for _, filter := range req.Filters {
		if filter.Name == "Name" && len(filter.Values) > 0 {
			return filter.Values
		}
	}

	return nil
}

func GetIsSetatsFiltersFromReq(req *model.DescribeClustersReq) bool {
	for _, filter := range req.Filters {
		if filter.Name == "IsSetats" {
			return true
		}
	}

	return false
}

func (d *describeClusterContext) getAccessibleClusters() (clusterGroupIds []string, empty bool, err error) {
	//mode:18 改造
	var dat map[string]interface{}
	token := ""
	if err := json.Unmarshal([]byte(d.req.CamContext), &dat); err == nil {
		token = dat["q-token"].(string)
	} else {
		logger.Info("Can't get the token from CamContext")
	}
	// 资源级权限管理
	clusterGroupIds, empty, err = resource_auth.GetResourceManager().GetAccessibleClusters(&resource_auth.ResourceContext{
		AppId:         d.req.AppId,
		Uin:           d.req.Uin,
		SubAccountUin: d.req.SubAccountUin,
		Region:        d.req.Region,
		Token:         token,
	}, false)
	if err != nil {
		return
	}
	if empty {
		return
	}

	if len(d.req.ClusterIds) == 0 {
		d.req.ClusterIds = clusterGroupIds
	} else if len(clusterGroupIds) > 0 {
		// 用户指定了集群，跟授权的集群做交集
		idMap := make(map[string]struct{}, len(clusterGroupIds))
		for _, id := range clusterGroupIds {
			idMap[id] = struct{}{}
		}
		inputIds := d.req.ClusterIds
		d.req.ClusterIds = make([]string, 0, len(inputIds))
		for _, id := range inputIds {
			if _, ok := idMap[id]; ok {
				d.req.ClusterIds = append(d.req.ClusterIds, id)
			}
		}
	}

	// 按标签过滤
	clusterGroupIds, empty, err = FilerClustersByTag(d.req)
	if err != nil {
		code := errorcode.GetCode(err)
		// 标签不合法，返回空列表
		if code.GetCodeStr() == errorcode.FailedOperationCode_GetTagValues.GetCodeStr() {
			return nil, true, nil
		}

		return
	}
	if empty {
		return
	}

	return
}

func (d *describeClusterContext) getClustersInItemSpace() (err error) {
	d.Correlations, err = service2.QueryCluserItemSpaceCorrelation(nil, d.req.ClusterIds, true)
	if err != nil {
		return err
	}
	return nil
}

func ListCCNs(clusterGroupIds []int64) (ccnsMap map[int64][]*model.CCN, err error) {
	placeholder, args, err := service2.GetSqlPlaceholder(clusterGroupIds)
	if err != nil {
		return nil, err
	}
	sql := "SELECT * FROM ClusterGroupPeerVpc WHERE ClusterGroupId in (" + placeholder + ") AND Status<>?"
	args = append(args, constants.PEER_VPC_STATUS_DELETE)
	_, datas, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return nil, err
	}

	ccnsMap = make(map[int64][]*model.CCN)
	for _, data := range datas {
		peerVpc := &table.ClusterGroupPeerVpc{}
		err = util.ScanMapIntoStruct(peerVpc, data)
		if err != nil {
			return nil, errorcode.InternalErrorCode.NewWithErr(err)
		}
		ccn := BuildCCNFromClusterGroupPeerVpc(peerVpc)
		ccnsMap[peerVpc.ClusterGroupId] = append(ccnsMap[peerVpc.ClusterGroupId], ccn)
	}
	return ccnsMap, nil
}

func ListActiveClusters(clusterGroupIds []int64) (t map[int64]*table.Cluster, err error) {
	placeholder, args, err := service2.GetSqlPlaceholder(clusterGroupIds)
	if err != nil {
		return nil, err
	}
	sql := "SELECT * FROM Cluster WHERE ClusterGroupId in (" + placeholder + ") AND RoleType=?"
	args = append(args, constants.CLUSTER_ROLE_TYPE_ACTIVE)

	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return nil, err
	}
	t = make(map[int64]*table.Cluster)
	for i := 0; i < len(data); i++ {
		cluster := &table.Cluster{}
		err = util.ScanMapIntoStruct(cluster, data[i])
		if err != nil {
			return nil, errorcode.InternalErrorCode.NewWithErr(err)
		}
		t[cluster.ClusterGroupId] = cluster
	}

	return t, nil
}

func ListTke(clusterGroupIds []int64) (t map[int64]*table2.Tke, err error) {
	placeholder, args, err := service2.GetSqlPlaceholder(clusterGroupIds)
	if err != nil {
		return nil, err
	}
	sql := "select t.* from ClusterGroup cg  " +
		"inner join Cluster c on cg.Id = c.clusterGroupId " +
		"inner join Tke t on t.ClusterId = c.Id " +
		"where cg.Id in (" + placeholder + ")"
	txManager := service.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return nil, err
	}
	t = make(map[int64]*table2.Tke)
	for i := 0; i < len(data); i++ {
		tke := &table2.Tke{}
		err = util.ScanMapIntoStruct(tke, data[i])
		if err != nil {
			return nil, errorcode.InternalErrorCode.NewWithErr(err)
		}
		t[tke.ClusterId] = tke
	}

	return t, nil
}

// 2.12 迭代（支持集群维度cls）之前， 用户的日志收集是按照地域维度的，所以集群的cls 信息为空
func adjustClusterClsInfo(uin, region string, clusters map[int64]*table.Cluster) (err error) {
	initRegionClsInfo := false
	regionClsLogSet := ""
	regionClsTopicId := ""
	for _, cluster := range clusters {
		if cluster.ClsTopicId == "" {
			if !initRegionClsInfo {
				inst, err := service3.GetTableService().GetJobRunningLogTopic(uin, region)
				if err != nil {
					return nil
				}
				if inst != nil {
					regionClsLogSet = inst.LogSetId
					regionClsTopicId = inst.LogTopicId
				}
				initRegionClsInfo = true
			}

			// 历史集群处理方案：https://iwiki.woa.com/pages/viewpage.action?pageId=537112625
			// 即使开启了地域运行日志，还需要检查crd，crd不存在，则是客户要求升级到作业级日志控制，进入历史集群未开设置日志集状态
			logger.Debugf("check history cluster %s, clusterGroupId %d", cluster.UniqClusterId, cluster.ClusterGroupId)
			if cluster.KubeConfig != "" {
				if exists, err := LogConfigCrdExists(cluster); err != nil {
					return err
				} else if exists {
					cluster.ClsLogSet = regionClsLogSet
					cluster.ClsTopicId = regionClsTopicId
				}
			}
		}
	}
	return nil
}

func BuildCCNFromClusterGroupPeerVpc(peerVpc *table.ClusterGroupPeerVpc) *model.CCN {
	ccn := &model.CCN{}
	ccn.VpcId = peerVpc.VpcId
	ccn.SubnetId = peerVpc.SubnetId
	ccn.CcnId = peerVpc.CcnId
	//ccn.AppId = peerVpc.AppId
	//ccn.OwnerUin = peerVpc.OwnerUin

	return ccn
}

func BuildOrderFromBillingOrder(billingOrder *billingTable.BillingOrder) *model.Order {
	order := &model.Order{}
	switch billingOrder.CategoryId {
	case billing.OceanusExclusiveCategoryIdCreate:
		order.Type = 1
	case billing.OceanusExclusiveCategoryIdRenew:
		order.Type = 2
	case billing.OceanusExclusiveCategoryIdModify:
		order.Type = 3
	}
	order.AutoRenewFlag = billingOrder.AutoRenewFlag
	order.OperateUin = billingOrder.OperateUin
	order.ComputeCu = billingOrder.ComputeCu
	order.OrderTime = billingOrder.CreateTime
	return order
}

func calculateSecondsUntilExpiry(expireTimeString string) string {
	if expireTimeString == "0000-00-00 00:00:00" {
		return "-"
	}

	expireTime, err := time.ParseInLocation(billing.StandardTimestampFormatString, expireTimeString, time.Local)
	if err != nil {
		logger.Errorf("Failed to parse ExpireTime %s because %+v", expireTimeString, err)
		return "-"
	}
	return service.Itoa(int64(expireTime.Sub(time.Now()).Seconds()))
}

func GetStatusDescByStatus(status int8) string {
	switch status {
	case constants.CLUSTER_GROUP_STATUS_CREATING:
		return "creating" // 创建中
	case constants.CLUSTER_GROUP_STATUS_RUNNING:
		return "running" // 运行中
	case constants.CLUSTER_GROUP_STATUS_SCALE_PROGRESS:
		return "scaling"
	case constants.CLUSTER_GROUP_STATUS_ISOLATED:
		return "isolated"
	case constants.CLUSTER_GROUP_STATUS_SCALEDOWN_PROGRESS:
		return "scale downing"

	case constants.CLUSTER_GROUP_STATUS_UPGRADE:
		return "upgrade"

	case constants.CLUSTER_GROUP_STATUS_RECOVERING:
		return "recovering"

	default:
		return "unknown"
	}
}

func getTags(req *model.DescribeClustersReq) (tags []*tag.Tag, err error) {
	if len(req.Filters) == 0 {
		return nil, nil
	}
	tagsKv := make([]string, 0)
	for _, filter := range req.Filters {
		if filter.Name == "Tags" {
			tagsKv = filter.Values
			break
		}
	}
	if len(tagsKv) == 0 {
		return nil, nil
	}
	tags = make([]*tag.Tag, 0, len(tagsKv))
	for _, kv := range tagsKv {
		arr := strings.SplitN(kv, ":", 2)

		tags = append(tags, &tag.Tag{
			TagKey: arr[0],
			TagValue: func() string {
				if len(arr) >= 2 {
					return arr[1]
				} else {
					return ""
				}
			}(),
		})
	}
	return tags, nil
}

func FilerClustersByTag(req *model.DescribeClustersReq) (clusterGroupIds []string, empty bool, err error) {
	// 校验标签合法性
	tags, err := getTags(req)
	if err != nil {
		return nil, true, err
	}

	tags, err = tag2.GetTagService().ValidateTags(req.Uin, tags)
	if err != nil {
		return nil, true, err
	}

	if len(tags) == 0 {
		return req.ClusterIds, false, nil
	}
	tagList := make([]*model2.TagKeyValue, 0, len(tags))
	for _, t := range tags {
		tagList = append(tagList, &model2.TagKeyValue{
			Key:   t.TagKey,
			Value: t.TagValue,
		})
	}

	clustersByTag, err := resource_auth.GetResourceManager().ParseTagList(&resource_auth.ResourceContext{
		AppId:         req.AppId,
		Uin:           req.Uin,
		SubAccountUin: req.SubAccountUin,
		Action:        "",
		ResourceIds:   nil,
		Ip:            "",
		Token:         "",
		Region:        req.Region,
	}, tagList)
	if err != nil {
		return nil, true, err
	}

	if len(req.ClusterIds) == 0 {
		return clustersByTag, len(clustersByTag) == 0, nil
	}
	clustersByTagMap := make(map[string]struct{}, len(clustersByTag))
	for _, id := range clustersByTag {
		clustersByTagMap[id] = struct{}{}
	}

	for _, id := range req.ClusterIds {
		if _, exist := clustersByTagMap[id]; exist {
			clusterGroupIds = append(clusterGroupIds, id)
		}
	}

	return clusterGroupIds, len(clusterGroupIds) == 0, nil
}

// clusterGroups 需要是同一个地域， 同一个appid的
func GetClusterGroupBillingInfo(clusterGroups []*table.ClusterGroup) (
	billingResourceMap map[string]*billingTable.BillingResource, err error) {
	resourceIdSet := make([]string, 0, len(clusterGroups))
	for _, clusterGroup := range clusterGroups {
		resourceIdSet = append(resourceIdSet, clusterGroup.SerialId)
	}

	resourceSet, err := getBillingResource(resourceIdSet)
	if err != nil {
		return nil, err
	}

	billingResourceMap = make(map[string]*billingTable.BillingResource)
	for _, resource := range resourceSet {
		billingResourceMap[resource.ResourceId] = resource
	}
	return billingResourceMap, nil
}

func GetBillingOrders(clusterGroups []*table.ClusterGroup) (
	billingResourceMap map[string][]*model.Order, err error) {
	resourceIdSet := make([]string, 0, len(clusterGroups))
	for _, clusterGroup := range clusterGroups {
		resourceIdSet = append(resourceIdSet, clusterGroup.SerialId)
	}

	resourceSet, err := getBillingOrder(resourceIdSet)
	if err != nil {
		return nil, err
	}

	billingResourceMap = make(map[string][]*model.Order)
	for _, resource := range resourceSet {
		order := BuildOrderFromBillingOrder(resource)
		billingResourceMap[resource.ResourceId] = append(billingResourceMap[resource.ResourceId], order)
	}
	return billingResourceMap, nil
}

// 不能直接用  BillingService， import cycle
func getBillingResource(resourceIdSet []string) (resourceSet []*billingTable.BillingResource, err error) {
	resourceSet = make([]*billingTable.BillingResource, 0)

	if len(resourceIdSet) == 0 {
		return
	}

	placeholder, args, err := service2.GetSqlPlaceholder(resourceIdSet)
	if err != nil {
		return nil, err
	}

	sql := "SELECT * FROM BillingResource WHERE ResourceId IN(" + placeholder + ")"
	_, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return
	}

	for _, d := range data {
		resource := &billingTable.BillingResource{}
		err = util.ScanMapIntoStruct(resource, d)
		if err != nil {
			return nil, errorcode.InternalErrorCode.NewWithErr(err)
		}
		resourceSet = append(resourceSet, resource)
	}
	return
}

func getBillingOrder(resourceIdSet []string) (resourceSet []*billingTable.BillingOrder, err error) {
	resourceSet = make([]*billingTable.BillingOrder, 0)

	if len(resourceIdSet) == 0 {
		return
	}

	placeholder, args, err := service2.GetSqlPlaceholder(resourceIdSet)
	if err != nil {
		return nil, err
	}

	sql := "SELECT * FROM BillingOrder WHERE ResourceId IN(" + placeholder + ")"
	_, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return
	}

	for _, d := range data {
		resource := &billingTable.BillingOrder{}
		err = util.ScanMapIntoStruct(resource, d)
		if err != nil {
			return nil, errorcode.InternalErrorCode.NewWithErr(err)
		}
		resourceSet = append(resourceSet, resource)
	}
	return
}

func GetBillingResourceBySerialId(resourceId string) (resource *billingTable.BillingResource, err error) {

	if resourceId == "" {
		return
	}
	args := make([]interface{}, 0)
	args = append(args, resourceId)

	sql := "SELECT * FROM BillingResource WHERE ResourceId = ?"
	_, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to execute the sql:%s, with args:%+v, with errors:%+v", sql, args, err)
		return nil, err
	}
	if len(data) == 0 {
		logger.Errorf("Failed to query BillingResource by cluster group, because the BillingResource size is 0!")
		return nil, errors.New("BillingResource query size is 0")
	}
	resource = &billingTable.BillingResource{}
	err = util.ScanMapIntoStruct(resource, data[0])
	if err != nil {
		logger.Errorf("Failed to convert bytes into cluster group, with errors:%+v", err)
		return nil, err
	}
	return resource, nil
}

func GetClusterSessionsWithRefsBySerialId(serialId string, memRatio int8) (clusterSession []*model_capi_cluster.ClusterSessionRsp, err error) {
	sql := "select * from ClusterSession  where  ClusterGroupSerialId = ?"
	args := make([]interface{}, 0)
	args = append(args, serialId)
	clusterSessionList := make([]*model_capi_cluster.ClusterSessionRsp, 0)
	txManager := service4.GetTxManager()
	_, data, err := txManager.GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		logger.Errorf("Failed to execute the sql:%s, with args:%+v, with errors:%+v", sql, args, err)
		return nil, err
	}
	for i := 0; i < len(data); i++ {
		clusterSession := &table4.ClusterSession{}
		err = util.ScanMapIntoStruct(clusterSession, data[i])
		if err != nil {
			return nil, err
		}
		refItems, err := GetSessionClusterRefItems(clusterSession.Id)
		if err != nil {
			return nil, err
		}
		properties := make([]*model3.Property, 0)
		if clusterSession.Properties != "" {
			err = json.Unmarshal([]byte(clusterSession.Properties), &properties)
			if err != nil {
				return nil, err
			}
		}
		clusterSessionItem := &model_capi_cluster.ClusterSessionRsp{
			ClusterGroupSerialId: clusterSession.ClusterGroupSerialId,
			AppId:                clusterSession.AppId,
			OwnerUin:             clusterSession.OwnerUin,
			CreatorUin:           clusterSession.CreatorUin,
			Region:               clusterSession.Region,
			Zone:                 clusterSession.Zone,
			Status:               clusterSession.Status,
			CuNum:                clusterSession.CuNum,
			FlinkVersion:         clusterSession.FlinkVersion,
			Properties:           properties,
			ResourceRefs:         refItems,
			JobManagerCuSpec:     clusterSession.JobManagerCuSpec,
			TaskManagerCuSpec:    clusterSession.TaskManagerCuSpec,
			TaskManagerNum:       clusterSession.TaskManagerNum,
			CreateTime:           clusterSession.CreateTime,
			UpdateTime:           clusterSession.UpdateTime,
		}
		if clusterSession.JobManagerCpu != 0 {
			clusterSessionItem.JobManagerCpu = clusterSession.JobManagerCpu
			clusterSessionItem.JobManagerMem = clusterSession.JobManagerMem
			clusterSessionItem.TaskManagerCpu = clusterSession.TaskManagerCpu
			clusterSessionItem.TaskManagerMem = clusterSession.TaskManagerMem
		} else {
			// 兼容历史数据
			clusterSessionItem.JobManagerCpu = clusterSession.JobManagerCuSpec
			clusterSessionItem.JobManagerMem = clusterSession.JobManagerCuSpec * float32(memRatio)
			clusterSessionItem.TaskManagerCpu = clusterSession.TaskManagerCuSpec
			clusterSessionItem.TaskManagerMem = clusterSession.TaskManagerCuSpec * float32(memRatio)
		}

		clusterSessionList = append(clusterSessionList, clusterSessionItem)
	}
	return clusterSessionList, nil
}
