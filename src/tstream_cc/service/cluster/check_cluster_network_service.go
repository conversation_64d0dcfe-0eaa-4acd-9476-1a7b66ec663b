package service

import (
	"encoding/json"
	"fmt"
	commandc "tencentcloud.com/tstream_galileo/src/common/command"
	"tencentcloud.com/tstream_galileo/src/common/configure"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cluster_admin_protocol"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/net"
)

const (
	ConfItemTimeout           = "triggerNetworkTest.timeout"
	CheckNetworkInterfacename = "qcloud.clusteradmin.command.network.test"
	NetworkTestAction         = 1
)

func getTimeout() int64 {
	var timeout = configure.GetConfInt64Value(constants.GALILEO_CONF_FILE, ConfItemTimeout, 20)
	return timeout
}

func DoTriggerCheckNetwork(req *model.CheckClusterNetworkReq) (error, *model.CheckClusterNetworkRsp) {
	// 1. 获取集群id
	clusterGroup, err := GetClusterGroupBySerialId(req.ClusterId)
	if err != nil {
		logger.Errorf("Failed to GetClusterGroupBySerialId for %s because %+v", req.ClusterId, err)
		return errorcode.InvalidParameter_InvalidClusterId.New(), nil
	}
	if int64(clusterGroup.AppId) != req.AppId {
		logger.Errorf("appId(%d) != req.AppId(%d)", clusterGroup.AppId, req.AppId)
		return errorcode.InvalidParameter_InvalidClusterId.New(), nil
	}
	cluster, err := GetActiveClusterByClusterGroupId(clusterGroup.Id)
	if err != nil {
		logger.Errorf("Failed to GetActiveClusterByClusterGroupId for %d because %+v", clusterGroup.Id, err)
		return errorcode.InvalidParameter_InvalidClusterId.New(), nil
	}

	// 2. 封装请求CA的参数
	para := &model.CheckClusterNetworkCAPara{
		Params: model.Params{
			Host:  req.Host,
			Ports: req.Ports,
		},
		Action:          NetworkTestAction,
		ClusterId:       cluster.Id,
		ClusterSerialId: clusterGroup.SerialId,
	}
	caReq := cluster_admin_protocol.NewClusterAdminReq(req.RequestId, CheckNetworkInterfacename, para)
	sendData, err := json.Marshal(caReq)
	if err != nil {
		msg := fmt.Sprintf("%s Error converting SendData, error: %v", req.RequestId, err)
		logger.Error(msg)
		return errorcode.InternalErrorCode_MarshalFailed.New(), nil
	}
	// 3. 创建CRD
	commandService, err := commandc.NewCommandService(cluster.Id)
	if err != nil || commandService == nil {
		msg := fmt.Sprintf("%s Error can't get the commandService from job.ClusterId %d, error: %v", req.RequestId, cluster.Id, err)
		logger.Error(msg)
		return errorcode.InternalErrorCode_CreateCommandServiceFailed.New(), nil
	}
	// 4. 请求CA获取测试结果
	url, err := commandc.GetCAOrCSUrl(cluster)
	if err != nil {
		msg := fmt.Sprintf("%s Error commandService.GetCAOrCSUrl, error: %v", req.RequestId, err)
		logger.Error(msg)
		return errorcode.InternalErrorCode_DoCommandRequestFailed.New(), nil
	}
	rsp, err := commandService.DoRequest(&commandc.CommandRequest{
		ReqId:    req.RequestId,
		Url:      url,
		SendData: string(sendData),
		Uin:      req.Uin,
		Apikey:   "triggerClusterNetworkTest",
		Timeout:  getTimeout(),
		Callback: "",
	})
	if err != nil {
		msg := fmt.Sprintf("%s Error commandService.DoRequest, error: %v", req.RequestId, err)
		logger.Error(msg)
		return errorcode.InternalErrorCode_GetCAOrCSRspFailed.New(), nil
	}
	// 5. 解析返回数据
	caRsp := &model.TriggerNetworkTestCARsp{}
	err = json.Unmarshal([]byte(rsp), caRsp)
	if err != nil {
		msg := fmt.Sprintf("Error parsing response: ret: %v, error: %v", rsp, err)
		logger.Errorf(msg)
		return errorcode.InternalErrorCode_UnMarshalFailed.New(), nil
	}
	// 6. 返回结果
	// 6.1 CA端异常返回
	if caRsp.Data.ReturnCode != 0 {
		return errorcode.FailedOperationCode.ReplaceDesc(caRsp.Data.ReturnMsg), nil
	}
	// 6.2 成功则构造返回结果
	res := &model.CheckClusterNetworkRsp{}
	res.Ping = caRsp.Data.Params.Ping
	res.Telnet = caRsp.Data.Params.Telnet

	return nil, res
}
