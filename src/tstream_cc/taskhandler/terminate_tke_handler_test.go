package taskHandler

import (
	"encoding/json"
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"testing"
	"time"
)

func TestTerminateCvmsHandler_CompleteTask(t *testing.T) {
	req := &flow.TaskExecRequest{
		Retrycount: 0,
		Processkey: "",
		Taskcode:   "",
		DocId:      "",
		FlowId:     "",
		TaskId:     "",
		Params: map[string]string{
			constants.FLOW_PARAM_REQUEST_ID:                  "test",
			constants.FLOW_PARAM_CLUSTER_GROUP_ID:            "1725",
			constants.FLOW_PARAM_CLUSTER_ID:                  fmt.Sprintf("%d", *fTestClusterId),
			constants.FLOW_PARAM_SCALE_CLUSTER_TARGET_CU_NUM: fmt.Sprintf("%d", *fTestClusterTargetCU),
		},
	}

	handler := &terminateCvmsHandler{}

	rsp := handler.CompleteTask(req)
	b, _ := json.MarshalIndent(rsp, "", "")
	t.Logf("first init: %s", string(b))
	if rsp.RetCode != flow.TASK_IN_PROCESS {
		t.Fatal()
	}

	for {
		time.Sleep(time.Second)
		req.Retrycount++
		req.Params = rsp.Params

		rsp = handler.CompleteTask(req)

		b, _ = json.MarshalIndent(rsp, "", "")
		t.Logf("running: %s", string(b))
		if rsp.RetCode == flow.TASK_SUCCESS {
			break
		}
	}

	t.Logf("scale tke sucess")
}
