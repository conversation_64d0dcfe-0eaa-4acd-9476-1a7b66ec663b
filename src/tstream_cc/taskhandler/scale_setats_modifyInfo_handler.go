package taskHandler

import (
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/uuid"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type ScaleSetatsModifyInfoHandler struct {
}

func (c ScaleSetatsModifyInfoHandler) Apps(ctx *deploy.Context) ([]apps.App, error) {
	requestId := fmt.Sprintf("ScaleSetatsModifyInfoHandler-%s-%s", uuid.NewRandom().String(), ctx.ClusterGroup.SerialId)
	logger.Infof("ScaleSetatsModifyInfoHandler Apps, requestId:%s", requestId)
	result := make([]apps.App, 0)
	result = append(result, &modifySetatasInfoApp{ctx: ctx, requestId: requestId})
	count, _setats, err := service.GetSetatsByClusterGroupSerialId(ctx.ClusterGroup.SerialId)
	if err != nil {
		logger.Errorf("ScaleSetatsModifyInfoHandler Failed to get setats by ClusterGroupSerialId id:%s, with errors:%+v", ctx.ClusterGroup.SerialId, err)
		return nil, errorcode.FailedOperationCode.NewWithErr(err)
	}
	if count == 0 {
		logger.Errorf("ScaleSetatsModifyInfoHandler Failed to get setats by ClusterGroupSerialId id:%s, with count 0", ctx.ClusterGroup.SerialId)
		return nil, errorcode.FailedOperationCode.NewWithMsg(fmt.Sprintf("setats size is 0"))
	}
	count, _, err = service.GetSetatsWarehouseBySerialId(_setats.SerialId)
	if err != nil {
		logger.Errorf("ScaleSetatsModifyInfoHandler Failed to GetSetatsWarehouseBySerialId by setats serial id:%s, with errors:%+v", _setats.SerialId, err)
		return nil, err
	}
	if count == 1 {
		logger.Infof("ScaleSetatsModifyInfoHandler need to scale setats")
		result = append(result, &setats{ctx: ctx, needManagerUrl: true})
	}
	return result, nil
}
