package taskHandler

import (
	"errors"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cdb"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type deleteCdbService struct {
}

func (s *deleteCdbService) Apps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	cg := ctx.ClusterGroup

	if cg.Type == constants.CLUSTER_GROUP_TYPE_SUB_EKS {
		return result, nil
	}

	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM {
		result = append(result, &deleteCdbApp{ctx: ctx})
	}

	// 独享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId == "" {
		result = append(result, &deleteCdbApp{ctx: ctx})
	}

	// 共享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId != "" {
	}

	return result, nil
}

type deleteCdbApp struct {
	ctx *deploy.Context
	apps.ApplyApp
}

func (c *deleteCdbApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	cdbList, err := c.ctx.ClusterGroupService.GetCdbList()
	if err != nil {
		return
	}

	clusterGroup := c.ctx.ClusterGroup

	region := clusterGroup.Region
	cdbService := cdb.GetCdbService()
	for _, instance := range cdbList {
		describeReq := cdbService.NewDefaultDescribeDBInstancesRequestBuilder().
			WithInstancesIds([]string{instance.InstanceId}).
			Build()
		totalCount, cdbSet, err := cdbService.DescribeDBInstancesWithScsAccountByNetEnvironmentType(clusterGroup.NetEnvironmentType, region, describeReq)
		if err != nil {
			return nil, err
		}
		if totalCount == 0 || len(cdbSet) == 0 {
			logger.Errorf("CDB instance does not exist, return success instead.", totalCount)
			continue
		}
		if totalCount > 1 {
			return nil, errors.New("DescribeDBInstances returned multiple instances")
		}

		if cdbService.IsInstanceIsolate(cdbSet[0]) {
			logger.Infof("cdb %s status %d, isolate done, skip it", instance.InstanceId)
			continue
		}

		req := cdbService.NewDefaultIsolateDBInstanceRequestBuilder().WithInstance(instance.InstanceId).Build()
		_, err = cdbService.IsolateDBInstanceWithScsAccountByNetEnvironmentType(clusterGroup.NetEnvironmentType, region, req)
		if err != nil {
			logger.Infof("IsolateDBInstance return err:%v", err)
			return nil, err
		}
	}

	return nil, nil
}
