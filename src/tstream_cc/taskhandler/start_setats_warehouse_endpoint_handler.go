package taskHandler

import (
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type StartSetatsWarehouseEndpointHandler struct {
}

func (c StartSetatsWarehouseEndpointHandler) Apps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	result = append(result, &switchSetatsStatusApp{ctx: ctx, status: constants.SETATS_RUNNING})
	result = append(result, &switchSetatsWarehouseStatusApp{ctx: ctx, status: service.SetatsWarehouseActive})
	return result, nil
}
