package taskHandler

import (
	"fmt"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/oceanus_controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	clusterService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config/image_registry"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type DeployTaskManagerService struct {
}

/**
 * 1. 添加flink-taskmanager-deployment
 */
func (s *DeployTaskManagerService) Apps(ctx *deploy.Context) ([]apps.App, error) {
	tmDeployment := s.getTmDeployment(ctx)
	return []apps.App{
		// taskmanager.deployment
		&clusterSessionTaskManager{ctx: ctx, tmDeployment: tmDeployment, namespace: tmDeployment.Namespace},
	}, nil
}

func (s *DeployTaskManagerService) getTmDeployment(ctx *deploy.Context) *oceanus_controller.ClusterSessionTmDeployment {
	// Session集群Flink版本(此参数决定启动Session集群时使用什么版本Flink镜像也作为Session集群JM和TM的Deployment的名称一部分)
	flinkVersion, _ := ctx.GetParam(constants.FLOW_PARAM_FLINK_VERSION, ctx.CC().DefaultFlinkVersion())
	// ClusterGroupSerialId
	serialId := ctx.ClusterGroup.SerialId
	// 获取TM的CU规格
	tmCuSpec, _ := ctx.GetParam(constants.FLOW_PARAM_TMCUSPEC, constants.FLOW_PARAM_DEFAULT_TMCUSPEC)
	tmNum, _ := ctx.GetParam(constants.FLOW_PARAM_TMNUM, constants.FLOW_PARAM_DEFAULT_TMNUM)
	// 根据CU规格计算TM的内存大小
	floatTmCuSpec, _ := strconv.ParseFloat(tmCuSpec, 32)
	taskmanagerMemory := service.GetMemoryByCuSpec(floatTmCuSpec, "Mi", ctx.Tke.ArchGeneration, ctx.Cluster.MemRatio)
	taskmanagerCpu := service.GetDeploymentCpu(floatTmCuSpec)
	// 新模式
	tmCpu, _ := ctx.GetParam(constants.FLOW_PARAM_TMCPU, "0")
	floatTmCpu, _ := strconv.ParseFloat(tmCpu, 32)
	if floatTmCpu != 0 {
		taskmanagerCpu = fmt.Sprintf("%f", floatTmCpu)
		tmMem, _ := ctx.GetParam(constants.FLOW_PARAM_TMMEM, "0")
		floatTmMem, _ := strconv.ParseFloat(tmMem, 32)
		taskmanagerMemory = service.GetMemoryByMemG(floatTmMem, "Mi", ctx.Tke.ArchGeneration)
	}
	// 生成JM和TM的公共标签
	appId, _, _ := ctx.GetParamInt(constants.FLOW_PARAM_APPID, 0)
	ownerUin, _ := ctx.GetParam(constants.FLOW_PARAM_OWNERUIN, constants.EMPTY)
	creatorUin, _ := ctx.GetParam(constants.FLOW_PARAM_CREATORUIN, constants.EMPTY)
	// 根据Flink版本获取对应版Flink镜像完整地址
	region, _ := ctx.GetParam(constants.FLOW_PARAM_REGION, "ap-guangzhou")
	flinkImage, err := image_registry.New(region).Flink(flinkVersion)
	if err != nil {
		errMsg := fmt.Sprintf("get flink tke image from rainbox error，"+
			"job's region [%s], flink version is [%s] error:%+v", region, flinkVersion, err)
		logger.Errorf(errMsg)
		panic(errorcode.NewStackError(errorcode.InternalErrorCode, "get flinkImage err", err))
	}
	// 要转为小写的，否则创建的时候tke端报错
	lowerFlinkVersion := service.GetNoDotLowerFlinkVersion(flinkVersion)
	namespace := clusterService.GetDefaultNamespace(ctx.ClusterGroup)
	credentialNamespace := clusterService.GetDefaultCredentialNamespace(ctx.ClusterGroup)
	// 构建JmDeployment的参数
	clusterSessiontmDeployment := &oceanus_controller.ClusterSessionTmDeployment{
		AppId:               appId,
		OwnerUin:            ownerUin,
		CreatorUin:          creatorUin,
		AppName:             fmt.Sprintf(constants.ClusterSessionAPPTemplate, serialId, lowerFlinkVersion),
		DeploymentName:      fmt.Sprintf(constants.ClusterSessionTMTemplate, serialId, lowerFlinkVersion),
		ContainerImage:      flinkImage,
		ImagePullPolicy:     constants.FLOW_PARAM_IMAGE_PULL_POLICY,
		CPU:                 taskmanagerCpu,
		Memory:              taskmanagerMemory,
		HadoopConfigName:    fmt.Sprintf(constants.ClusterSessionHadoopConfigTemplate, serialId, lowerFlinkVersion),
		FlinkConfigName:     fmt.Sprintf(constants.ClusterSessionFlinkConfigTemplate, serialId, lowerFlinkVersion),
		TmNum:               tmNum,
		Namespace:           namespace,
		CredentialNamespace: credentialNamespace,
	}
	clusterConfig, err := ctx.Cluster.GetClusterConfig()
	for k, v := range clusterConfig {
		switch value := v.(type) {
		case string:
			if k == constants.KUBERNETES_TASKMANAGER_ANNOTATIONS {
				annotations, err := ctx.GetPodAnnotations(true, true)
				if err != nil {
					panic(errorcode.NewStackError(errorcode.InternalErrorCode, "ctx.GetPodAnnotations err", err))
				}
				clusterSessiontmDeployment.CrossTenant = annotations
			}
		case map[string]interface{}:
			if k != flinkVersion {
				break
			}
			for fk, fv := range value {
				switch fValue := fv.(type) {
				case string:
					if constants.FLINK_K8S_CONTAINER_IMAGE == fk {
						clusterSessiontmDeployment.ContainerImage = fValue
					}
					if constants.FLINK_K8S_CONTAINER_IMAGE_PULL_POLICY == fk {
						clusterSessiontmDeployment.ImagePullPolicy = fValue
					}
				}
			}
		}
	}
	return clusterSessiontmDeployment
}

type clusterSessionTaskManager struct {
	ctx          *deploy.Context
	tmDeployment *oceanus_controller.ClusterSessionTmDeployment
	apps.Deployment
	namespace string
}

func (c clusterSessionTaskManager) Params() (interface{}, error) {
	return c.tmDeployment, nil
}

func (c clusterSessionTaskManager) Decode(params, into interface{}) (interface{}, error) {
	return c.ctx.FlowCC.ClusterSessionCC().FlinkSessionTmDeployment(params, into)
}
