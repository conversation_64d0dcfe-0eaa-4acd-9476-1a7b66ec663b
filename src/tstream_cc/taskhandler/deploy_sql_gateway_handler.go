package taskHandler

import (
	"encoding/json"
	"fmt"
	v1 "k8s.io/api/core/v1"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/oceanus_controller"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	clusterService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config/image_registry"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/tke"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type DeploySqlGatewayHandler struct {
	flinkVersion string
	namespace    string
}

const (
	SqlGatewayFlinkConfigTemplate    = "sql-gateway-flink-conf-%s"
	SqlGatewayHadoopConfigTemplate   = "sql-gateway-hadoop-config-%s"
	SqlGatewayDeploymentNameTemplate = "sql-gateway-%s" // Deployment&App Name
	SqlGatewayServiceNameTemplate    = "sql-gateway-%s-rest"
	SqlGatewayComponentName          = "sql-gateway"
	SqlGatewayLabelType              = "flink-standalone-kubernetes"
	SqlGatewayServicePort            = 8083
	SqlGatewayIdleTimeout            = "60 min"
	SqlGatewayIdleTimeoutKey         = "sql-gateway.session.idle-timeout"
	SqlGatewayNamespace              = "default"
)

func (s *DeploySqlGatewayHandler) Apps(ctx *deploy.Context) ([]apps.App, error) {
	namespace := clusterService.GetDefaultNamespace(ctx.ClusterGroup)
	s.namespace = namespace
	base, err := ctx.ParamBase(constants.K8S_KIND_STATEFULSET,
		namespace,
		fmt.Sprintf(SqlGatewayDeploymentNameTemplate, s.flinkVersion), true)
	if err != nil {
		panic(errorcode.NewStackError(errorcode.InternalErrorCode, "ctx.ParamBase err", err))
	}
	flinkVersion, _ := ctx.GetParam(constants.FLOW_PARAM_GATEWAY_FLINK_VERSION, ctx.CC().DefaultFlinkVersion())
	cpuSpec, _ := ctx.GetParam(constants.FLOW_PARAM_CU_SPEC, "1.0")
	floatCuSpec, _ := strconv.ParseFloat(cpuSpec, 32)
	memory := service2.GetMemoryByCuSpec(floatCuSpec, "Mi", ctx.Tke.ArchGeneration, ctx.Cluster.MemRatio)
	// 新模式
	cpu, _ := ctx.GetParam(constants.FLOW_PARAM_CPU_SPEC, "0")
	floatCpu, _ := strconv.ParseFloat(cpu, 32)
	if floatCpu != 0 {
		cpuSpec = cpu
		mem, _ := ctx.GetParam(constants.FLOW_PARAM_MEM_SPEC, "0")
		floatMemory, _ := strconv.ParseFloat(mem, 32)
		memory = service2.GetMemoryByMemG(floatMemory, "Mi", ctx.Tke.ArchGeneration)
	}

	appId, _, _ := ctx.GetParamInt(constants.FLOW_PARAM_APPID, 0)
	s.flinkVersion = service2.GetNoDotLowerFlinkVersion(flinkVersion) // format FlinkVersion
	refsPath, _ := ctx.GetParam(constants.FLOW_PARAM_REFS_PATH, constants.EMPTY)
	refs, _ := ctx.GetParam(constants.FLOW_PARAM_REFS, constants.EMPTY)
	dynamicPropertiesJson, _ := ctx.GetParam(constants.FLOW_PARAM_PROPERTIES, constants.EMPTY)
	annotations, err := ctx.GetPodAnnotations(true, true)
	if err != nil {
		panic(errorcode.NewStackError(errorcode.InternalErrorCode, "ctx.GetPodAnnotations err", err))
	}
	region, _ := ctx.GetParam(constants.FLOW_PARAM_REGION, "ap-guangzhou")
	flinkImage, err := image_registry.New(region).Flink(flinkVersion)
	if err != nil {
		panic(errorcode.NewStackError(errorcode.InternalErrorCode, "image_registry.New err", err))
	}
	properties := make([]*model.Property, 0)
	err = json.Unmarshal([]byte(dynamicPropertiesJson), &properties)
	if err != nil {
		panic(errorcode.NewStackError(errorcode.InternalErrorCode, "Unmarshal properties err", err))
	}
	dynamicProperties := make(map[string]string, 0)
	for _, property := range properties {
		dynamicProperties[property.Key] = property.Value
	}

	secret, err := ctx.GetOceanusIPSecret()
	if err != nil {
		return nil, err
	}

	return []apps.App{
		s.flinkConfMap(flinkVersion, ctx, refs, refsPath, dynamicProperties),
		s.hadoopConfigMap(ctx),
		&sqlGateway{ctx: ctx,
			sqlGatewayData: &oceanus_controller.SqlGatewayData{
				Base:             base,
				AppName:          fmt.Sprintf(SqlGatewayDeploymentNameTemplate, s.flinkVersion),
				AppId:            appId,
				DeploymentName:   fmt.Sprintf(SqlGatewayDeploymentNameTemplate, s.flinkVersion),
				CPU:              cpuSpec,
				Memory:           memory,
				CrossTenant:      annotations,
				ContainerImage:   flinkImage,
				HadoopConfigName: fmt.Sprintf(SqlGatewayHadoopConfigTemplate, s.flinkVersion),
				FlinkConfigName:  fmt.Sprintf(SqlGatewayFlinkConfigTemplate, s.flinkVersion),
				Namespace:        namespace,
			},
			flinkVersion: flinkVersion,
		},
		s.service(ctx, fmt.Sprintf(SqlGatewayServiceNameTemplate, s.flinkVersion), SqlGatewayServicePort, secret),
	}, nil
}

func (s *DeploySqlGatewayHandler) flinkConfMap(flinkVersion string, ctx *deploy.Context, refs string, refsPath string, dynamicProperties map[string]string) apps.App {
	sqlGatewayIdleTimeout := SqlGatewayIdleTimeout
	if len(dynamicProperties) > 0 {
		if timeOut, ok := dynamicProperties[SqlGatewayIdleTimeoutKey]; ok {
			sqlGatewayIdleTimeout = timeOut
			delete(dynamicProperties, SqlGatewayIdleTimeoutKey)
		}
	}

	// sql-gateway.endpoint.rest.address: 0.0.0.0
	// rest.address: cluster-6zz5ubam-session-flink-1-16-rest
	// flink 1.18 sql gateway 支持 flink 1.18 cluster session做为计算引擎
	if constants.Flinkversion118 == flinkVersion {
		dynamicProperties["sql-gateway.endpoint.rest.address"] = "0.0.0.0"
		dynamicProperties["rest.address"] = fmt.Sprintf("%s-session-%s-rest", ctx.ClusterGroup.SerialId, s.flinkVersion)
	}

	return apps.NewConfigMap(
		apps.CMWithName(fmt.Sprintf(SqlGatewayFlinkConfigTemplate, s.flinkVersion)),
		apps.CMWithNamespace(s.namespace),
		apps.CMAddData("flink-conf.yaml", func() (string, error) {
			return ctx.FlowCC.SqlGatewayCC().SqlGatewayFlinkConf(struct {
				SqlGatewayIdleTimeout string
				SqlGatewayJars        string
				SqlGatewayJarsPath    string
				DynamicProperties     map[string]string
				Namespace             string
			}{
				SqlGatewayIdleTimeout: sqlGatewayIdleTimeout,
				SqlGatewayJars:        refs,
				SqlGatewayJarsPath:    refsPath,
				DynamicProperties:     dynamicProperties,
				Namespace:             s.namespace,
			})
		}),
		apps.CMAddData("log4j-console.properties", ctx.FlowCC.ClusterAdminCC().FlinkLog4jV2),
	)
}

func (s *DeploySqlGatewayHandler) hadoopConfigMap(ctx *deploy.Context) apps.App {
	return apps.NewConfigMap(
		apps.CMWithName(fmt.Sprintf(SqlGatewayHadoopConfigTemplate, s.flinkVersion)),
		apps.CMWithNamespace(s.namespace),
		apps.CMAddData("core-site.xml", func() (string, error) {
			return ctx.FlowCC.CoreSiteXml(struct {
				CosBucket string
				Region    string
			}{CosBucket: ctx.Cluster.DefaultCOSBucket,
				Region: ctx.ClusterGroup.Region})
		}),
	)
}

func (s *DeploySqlGatewayHandler) service(ctx *deploy.Context, componentName string, port int32, secret *v1.Secret) apps.App {
	labels := tke.GetTkeService().ComponentLabels(componentName, ctx.ClusterGroup, ctx.Cluster)
	return apps.NewService(
		apps.ServiceWithName(componentName),
		apps.ServiceWithNamespace(s.namespace),
		apps.ServiceWithPort(port),
		apps.ServiceWithLabels(labels),
		apps.ServiceWithSelector(map[string]string{
			"app":       fmt.Sprintf(SqlGatewayDeploymentNameTemplate, s.flinkVersion),
			"component": SqlGatewayComponentName,
			"type":      SqlGatewayLabelType,
		}),
		apps.ServiceWithOwnerReference(tke.GetTkeService().SecretAsOwnerReference(secret)),
	)
}

type sqlGateway struct {
	ctx            *deploy.Context
	sqlGatewayData *oceanus_controller.SqlGatewayData
	apps.Deployment
	flinkVersion string
}

func (c *sqlGateway) Params() (interface{}, error) {
	return c.sqlGatewayData, nil
}

func (c *sqlGateway) Decode(params, info interface{}) (interface{}, error) {
	return c.ctx.FlowCC.SqlGatewayCC().FlinkSqlGatewayDeployment(params, info, c.flinkVersion)
}
