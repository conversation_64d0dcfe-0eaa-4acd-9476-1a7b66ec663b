package taskHandler

import (
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type isolateClusterEndPointService struct {
}

func (s *isolateClusterEndPointService) Apps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	cg := ctx.ClusterGroup

	isCluster, exists := ctx.GetParam(constants.FLOW_PARAM_SETATS_IS_CLUSTER, "1")
	if exists {
		if isCluster == "0" {
			return result, nil
		}
	}

	result = append(result, &switchClusterGroupStatusApp{ctx: ctx, status: constants.CLUSTER_GROUP_STATUS_ISOLATED})

	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM {

	}

	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId == "" {

	}

	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId != "" {

	}

	return result, nil
}
