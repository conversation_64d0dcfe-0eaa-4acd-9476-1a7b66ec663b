package taskHandler

import (
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	table4 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cdb"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/tke"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cdb"
	flow2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/flow"
)

type upgradeCDBHandler struct {
}

func (h *upgradeCDBHandler) HandleTaskStatusInit(requestId string, request *flow.TaskExecRequest) (resp *flow.TaskExecResponse) {
	flowService := flow2.GetFlowService()
	s, err := NewUpgradeCdbService(requestId, request)
	if err != nil {
		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_RUNNING, 0.1,
			fmt.Sprintf("%v", err), request.Params)
	}

	if err = s.init(); err != nil {
		return s.RetryRsp(fmt.Sprintf("%v", err))
	}

	return s.upgrade()
}

func (h *upgradeCDBHandler) HandleTaskStatusRunning(requestId string, request *flow.TaskExecRequest) (resp *flow.TaskExecResponse) {
	flowService := flow2.GetFlowService()
	s, err := NewUpgradeCdbService(requestId, request)
	if err != nil {
		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_RUNNING, 0.1,
			fmt.Sprintf("%v", err), request.Params)
	}

	if err = s.init(); err != nil {
		return s.RetryRsp(fmt.Sprintf("%v", err))
	}

	return s.checkUpgraded()
}

type upgradeCdbService struct {
	requestId string
	request   *flow.TaskExecRequest

	*flow2.ClusterFlowService

	targetMemory int64
	targetVolume int64

	*ApplyTKEHandler
	k8sInstance *table2.Tke
	cdb         *table4.Cdb
}

func NewUpgradeCdbService(requestId string, request *flow.TaskExecRequest) (s *upgradeCdbService, err error) {
	flowService, err := flow2.NewClusterFlowService(request)
	if err != nil {
		return nil, err
	}

	s = &upgradeCdbService{requestId: requestId, request: request, ClusterFlowService: flowService,
		ApplyTKEHandler: &ApplyTKEHandler{}}

	if err != nil {
		return nil, err
	}
	return
}

func (s *upgradeCdbService) init() (err error) {
	if err = s.getFromParam(); err != nil {
		return
	}

	tkeList, err := s.ClusterGroupService.GetTkeList()
	if err != nil {
		return
	}
	if len(tkeList) != 1 {
		return errorcode.InternalErrorCode.NewWithInfo(fmt.Sprintf("there are %d tke instances", len(tkeList)),
			nil)
	}
	s.k8sInstance = tkeList[0]

	running, err := IsClusterRunning(s.ClusterGroup, s.Cluster, s.k8sInstance)
	if err != nil {
		return err
	}
	if !running {
		return errorcode.InternalErrorCode.NewWithInfo(fmt.Sprintf("tke not running"), nil)
	}

	s.cdb, err = s.ClusterGroupService.GetCdb()
	if err != nil {
		return
	}
	return nil
}

func (s *upgradeCdbService) getFromParam() (err error) {
	var exist bool
	s.targetMemory, exist, err = s.GetFlowParamInt(s.request.Params, constants.FLOW_PARAM_UPGRADE_CDB_TARGET_MEMORY, 0)
	if err != nil {
		return
	}
	if !exist {
		return errorcode.InternalErrorCode.NewWithInfo(constants.FLOW_PARAM_UPGRADE_CDB_TARGET_MEMORY, nil)
	}
	s.targetVolume, exist, err = s.GetFlowParamInt(s.request.Params, constants.FLOW_PARAM_UPGRADE_CDB_TARGET_VOLUME, 0)
	if err != nil {
		return
	}
	if !exist {
		return errorcode.InternalErrorCode.NewWithInfo(constants.FLOW_PARAM_UPGRADE_CDB_TARGET_VOLUME, nil)
	}
	return
}

func (s *upgradeCdbService) upgrade() (rsp *flow.TaskExecResponse) {
	logger.Infof("%v", s.request)

	cdbService := cdb.GetCdbService()
	err := cdbService.UpgradeDBInstanceResource(s.ClusterGroup.Region, s.cdb.InstanceId, s.targetMemory, s.targetVolume)
	if err != nil {
		return s.RetryRspWithErr(err)
	}
	return s.DoneRsp("upgrade db down")
}

func (s *upgradeCdbService) checkUpgraded() (rsp *flow.TaskExecResponse) {
	cdbService := cdb.GetCdbService()

	cdbInstance, err := cdbService.DescribeDBInstance(s.ClusterGroup.Region, s.cdb.InstanceId)
	if err != nil {
		return s.RetryRspWithErr(err)
	}
	if !cdbService.IsInstanceRunning(cdbInstance) {
		return s.RetryRsp("wait cdb running")
	}

	cdbService.UpdateInstanceInfo(s.cdb.Id, *cdbInstance.Memory, *cdbInstance.Volume)

	return s.DoneRsp("check upgrade db down")
}
