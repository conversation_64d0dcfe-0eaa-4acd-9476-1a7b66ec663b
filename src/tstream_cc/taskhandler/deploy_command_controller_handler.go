package taskHandler

import (
	"fmt"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/oceanus_controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type DeployCommandControllerService struct {
}

func (s *DeployCommandControllerService) Apps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	cg := ctx.ClusterGroup

	// 共享集群 包年包月下的eks集群，不做任何处理
	if cg.Type == constants.CLUSTER_GROUP_TYPE_SUB_EKS {
		return result, nil
	}

	// 共享集群母集群，开新区
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone != "" {
		return result, nil
	}

	// 共享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId != "" {
		return result, nil
	}

	// 创建共享集群母集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone == "" {
	}

	// 独享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId == "" {
	}

	result = append(result, &commandControllerCrd{ctx: ctx})

	if ctx.Tke.ArchGeneration <= constants.TKE_ARCH_GENERATION_V2 {
		result = append(result, &commandControllerSts{ctx: ctx})
	}
	if ctx.Tke.ArchGeneration >= constants.TKE_ARCH_GENERATION_V3 {
		result = append(result, &commandControllerDep{ctx: ctx})
	}

	return result, nil
}

type commandControllerCrd struct {
	ctx *deploy.Context
	apps.Crd
}

func (c *commandControllerCrd) Params() (interface{}, error) {
	return nil, nil
}

func (c *commandControllerCrd) Decode(params, into interface{}) (interface{}, error) {
	return c.ctx.FlowCC.AppsCC().CommandControllerCrd(params, into)
}

type commandControllerSts struct {
	ctx *deploy.Context
	apps.StatefulSet
}

func (c *commandControllerSts) Params() (interface{}, error) {
	return getCommandControllerParams(c.ctx, constants.K8S_KIND_STATEFULSET)
}

func (c *commandControllerSts) Decode(params, into interface{}) (interface{}, error) {
	return c.ctx.FlowCC.AppsCC().CommandController(params, into)
}

type commandControllerDep struct {
	ctx *deploy.Context
	apps.Deployment
}

func (c *commandControllerDep) Params() (interface{}, error) {
	return getCommandControllerParams(c.ctx, constants.K8S_KIND_DEPLOYMENT)
}

func (c *commandControllerDep) Decode(params, into interface{}) (interface{}, error) {
	return c.ctx.FlowCC.AppsCC().CommandController(params, into)
}

func getCommandControllerParams(ctx *deploy.Context, workLoadKind string) (data *oceanus_controller.Base, err error) {
	imageName, exists := ctx.GetParam(constants.FlowParamCommandControllerImage, "")
	if !exists {
		imageName, err = ctx.CC().ImageRegistry().CommandController()
		if err != nil {
			return nil, err
		}
	}

	base, err := ctx.ParamBase(workLoadKind,
		constants.OCEANUS_NAMESPACE,
		constants.ComponentCommandController,
		false)
	if err != nil {
		return nil, err
	}

	if ctx.Tke.ArchGeneration >= constants.TKE_ARCH_GENERATION_V5 &&
		ctx.ClusterType == constants.K8S_CLUSTER_TYPE_TKE &&
		ctx.ClusterGroup.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && ctx.ClusterGroup.AgentSerialId == "" { //TKE 集群
		for key := range base.NodeSelector {
			delete(base.NodeSelector, key)
		}

		base.NodeSelector[constants.TKE_CVM_WORKER_LABEL_KEY] = fmt.Sprintf("worker")
		base.PriorityClassName = constants.TKE_PRIORITY_CLASS_NAME
	}

	base.AppImage = imageName
	base.ServiceAccountName = constants.SERVICE_ACCOUNT_OCEANUS

	return base, nil
}
