package taskHandler

import (
	"encoding/json"
	"errors"
	"fmt"

	cvm "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/cvm/v20170312"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cbs"
	cvm2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cvm"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/tke"
	yuntiService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/yunti"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type deleteCvmService struct {
}

func (s *deleteCvmService) Apps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	cg := ctx.ClusterGroup

	if cg.Type == constants.CLUSTER_GROUP_TYPE_SUB_EKS {
		return result, nil
	}

	// Eks集群不需要处理cvm
	if ctx.Tke.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		return result, nil
	}

	//共享集群 母集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM {
		result = append(result, &poolCvmApp{ctx: ctx, pcProvider: &deleteClusterPoolCvmProvider{}})
		result = append(result, &deleteCvmApp{ctx: ctx})
	}

	//独享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId == "" {
		result = append(result, &DeleteFlinkUiRuleApp{ctx: ctx})
		result = append(result, &poolCvmApp{ctx: ctx, pcProvider: &deleteClusterPoolCvmProvider{}})
		result = append(result, &deleteNodeEniApp{ctx: ctx})
		result = append(result, &deleteCvmApp{ctx: ctx})
	}

	//共享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId != "" {
		result = append(result, &DeleteFlinkUiRuleApp{ctx: ctx})
		if cg.ResourceType == constants.RESOURCE_TYPE_PRIVATE {
			//result = append(result, &uniformClusterIsolateCvmApp{ctx: ctx})
		}
	}
	return result, nil
}

type deleteCvmApp struct {
	ctx *deploy.Context
	apps.ApplyApp
}

func (c *deleteCvmApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {

	canDeleteIds, othersIds, err := getTkeCvm(c.ctx, cvm2.GetCvmService().InstanceCanTerminate)
	if err != nil {
		return
	}

	if len(othersIds) > 0 {
		b, _ := json.Marshal(othersIds)
		return nil, errors.New(fmt.Sprintf("can not delete cvms: %s", string(b)))
	}

	err = c.deleteCvm(canDeleteIds)
	if err != nil {
		logger.Errorf("delete cvm failed: %v", err)
		return nil, err
	}
	return
}

func getTkeCvm(ctx *deploy.Context, f func(*cvm.Instance) bool) (canDeleteIds []*string, dirtyIds []*string, err error) {
	canDeleteIds = make([]*string, 0)
	dirtyIds = make([]*string, 0)

	region := ctx.Region
	tkeInstance := ctx.Tke

	tkeService := tke.GetTkeService()
	cvmService := cvm2.GetCvmService()

	var offset, limit int64
	offset = 0
	limit = 100
	for {
		req := tkeService.NewDefaultDescribeClusterInstancesRequestBuilder().
			WithClusterId(tkeInstance.InstanceId).
			WithOffsetLimit(offset, limit).
			Build()
		totalCnt, instanceSet, err := tkeService.DescribeClusterInstancesWithScsAccountByNetEnvironmentType(ctx.ClusterGroup.NetEnvironmentType, region, req)
		if err != nil {
			logger.Infof("DescribeClusterInstancesWithScsAccount return err:%v", err)
			return canDeleteIds, dirtyIds, err
		}

		cvmInstanceIdSet := make([]*string, 0, len(instanceSet))
		for _, instance := range instanceSet {
			cvmInstanceIdSet = append(cvmInstanceIdSet, instance.InstanceId)
		}
		cvmInstanceSet, err := cvmService.DescribeInstancesWithScsAccount(ctx.ClusterGroup.NetEnvironmentType, region, cvmInstanceIdSet)
		if err != nil {
			logger.Infof("DescribeInstancesWithScsAccount return err:%v", err)
			return canDeleteIds, dirtyIds, err
		}

		for _, instance := range cvmInstanceSet {
			if f(instance) {
				canDeleteIds = append(canDeleteIds, instance.InstanceId)
			} else {
				dirtyIds = append(dirtyIds, instance.InstanceId)
			}
		}

		if totalCnt <= uint64(offset+limit) {
			break
		}
		offset += limit
	}
	return canDeleteIds, dirtyIds, nil
}

func (c *deleteCvmApp) deleteCvm(cvmInstances []*string) (err error) {
	if len(cvmInstances) == 0 {
		return nil
	}

	b, _ := json.Marshal(cvmInstances)
	logger.Infof("delete cvms: %s", string(b))

	cg := c.ctx.ClusterGroup
	region := cg.Region

	if cg.NetEnvironmentType == constants.NETWORK_ENV_INNER_VPC {
		return yuntiService.TerminateInstances(fmt.Sprintf("CSIG流计算Oceanus集群%s销毁", cg.SerialId), cvmInstances, region, cg.SerialId)
	}

	err = cbs.GetCbsService(cg.NetEnvironmentType, region).TerminateCbsFromCVM(cvmInstances)
	if err != nil {
		return err
	}
	cvmService := cvm2.GetCvmService()
	return cvmService.TerminateInstancesWithScsAccount(region, cvmInstances)
}
