package taskHandler

import (
	"encoding/json"
	"fmt"
	v1 "k8s.io/api/core/v1"
	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type DeployMetricProviderService struct {
}

func (s *DeployMetricProviderService) Apps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	cg := ctx.ClusterGroup

	allow := auth.IsInWhiteList(int64(cg.AppId), constants.WHITE_DEPLOY_METRIC_PROVIDER)
	if !allow {
		return result, nil
	}

	tkeCluster := ctx.Tke
	// 共享集群 包年包月下的eks集群/ 独享集群的eks集群，不做任何处理
	if cg.Type == constants.CLUSTER_GROUP_TYPE_SUB_EKS || tkeCluster.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		return result, nil
	}

	// 共享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId != "" {
		return result, nil
	}

	// 共享集群母集群，开新区
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone != "" {
		return result, nil
	}

	// 创建共享集群母集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone == "" {
		return result, nil
	}

	// 独享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId == "" {
	}

	if !service3.IsCurrentRegionEnabledDetailMetricReporter(ctx.ClusterGroup.Region) {
		return result, nil
	}

	secret, err := ctx.GetOceanusIPSecret()
	if err != nil {
		return result, err
	}

	result = append(result,
		s.configMap(ctx, secret),
		&metricsProvider{ctx: ctx},
		ctx.Service(constants.ComponentMetricProvider, constants.MetricProviderPort, secret))

	return result, nil
}

type metricsProvider struct {
	ctx *deploy.Context
	apps.StatefulSet
}

func (m *metricsProvider) Params() (interface{}, error) {
	image, err := m.ctx.CC().ImageRegistry().MetricProvider()
	if err != nil {
		return nil, err
	}

	base, err := m.ctx.ParamBase(constants.K8S_KIND_STATEFULSET,
		constants.OCEANUS_NAMESPACE,
		constants.ComponentMetricProvider, false)
	if err != nil {
		return nil, err
	}

	if m.ctx.Tke.ArchGeneration >= constants.TKE_ARCH_GENERATION_V5 {
		for key := range base.NodeSelector {
			delete(base.NodeSelector, key)
		}
		base.NodeSelector[constants.TKE_CVM_WORKER_LABEL_KEY] = fmt.Sprintf("worker")
	}

	base.AppImage = image
	base.ListenPort = constants.MetricProviderPort
	base.Env["PATH"] = "/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"
	base.Env["TKE_CLUSTER_ID"] = m.ctx.Tke.InstanceId
	base.Env["CLS_HOST"] = "ap-guangzhou.cls.tencentyun.com"

	return base, nil
}

func (m *metricsProvider) Decode(params, into interface{}) (interface{}, error) {
	return m.ctx.FlowCC.AppsCC().MetricProvider(params, into)
}

func (s *DeployMetricProviderService) configMap(ctx *deploy.Context, secret *v1.Secret) apps.App {
	return ctx.ConfigMap(
		secret,
		apps.CMWithName(constants.MetricProviderTelegrafConfigName),
		apps.CMAddData("telegraf.conf", getConfigForTelegrafConf(ctx.ClusterGroup.Region)))
}

// 这里，其实不是只有region 作为模板参数就够了。
func getConfigForTelegrafConf(region string) apps.CMDataValue {
	return func() (string, error) {
		// 从七彩石读取模板
		template := config.MustGetRainbowConfiguration(constants.RainbowGroupMetricProvider, constants.CONF_RAINBOW_KEY_TELEGRAF_TEMPLATE)

		regionMappingStr := config.MustGetRainbowConfiguration(constants.RainbowGroupMetricProvider, constants.CONF_RAINBOW_KEY_TELEGRAF_REGION_MAPPING)
		var regionMapping map[string]string
		if err := json.Unmarshal([]byte(regionMappingStr), &regionMapping); err != nil {
			logger.Errorf("can't deserialize region mapping")
			panic(errorcode.NewStackError(errorcode.InternalErrorCode, "", err))
		}

		params := struct {
			Interval                string
			FlushInterval           string
			LogFileRotationInterval string
			LogFileMaxSize          string
			LogFileMaxArchives      string
			Region                  string
			Namespace               string
			TaskNum                 string
			MaxRps                  string
			LogSampleRate           string
		}{
			Interval:                config.MustGetRainbowConfiguration(constants.RainbowGroupMetricProvider, constants.CONF_RAINBOW_KEY_TELEGRAF_INTERVAL),
			FlushInterval:           config.MustGetRainbowConfiguration(constants.RainbowGroupMetricProvider, constants.CONF_RAINBOW_KEY_TELEGRAF_FLUSH_INTERVAL),
			LogFileRotationInterval: config.MustGetRainbowConfiguration(constants.RainbowGroupMetricProvider, constants.CONF_RAINBOW_KEY_TELEGRAF_LOGFILE_ROTATION_INTERVAL),
			LogFileMaxSize:          config.MustGetRainbowConfiguration(constants.RainbowGroupMetricProvider, constants.CONF_RAINBOW_KEY_TELEGRAF_LOGFILE_ROTATION_MAX_SIZE),
			LogFileMaxArchives:      config.MustGetRainbowConfiguration(constants.RainbowGroupMetricProvider, constants.CONF_RAINBOW_KEY_TELEGRAF_LOGFILE_ROTATION_MAX_ARCHIVES),
			Region:                  "\"" + regionMapping[region] + "\"",
			Namespace:               config.MustGetRainbowConfiguration(constants.RainbowGroupMetricProvider, constants.CONF_RAINBOW_KEY_TELEGRAF_NAMESPACE),
			TaskNum:                 config.MustGetRainbowConfiguration(constants.RainbowGroupMetricProvider, constants.CONF_RAINBOW_KEY_TELEGRAF_OUTPUT_TASK_NUM),
			MaxRps:                  config.MustGetRainbowConfiguration(constants.RainbowGroupMetricProvider, constants.CONF_RAINBOW_KEY_TELEGRAF_OUTPUT_MAX_RPS),
			LogSampleRate:           config.MustGetRainbowConfiguration(constants.RainbowGroupMetricProvider, constants.CONF_RAINBOW_KEY_TELEGRAF_LOG_SAMPLE_RATE),
		}

		return config.MustRenderConfigFile(template, params), nil
	}
}
