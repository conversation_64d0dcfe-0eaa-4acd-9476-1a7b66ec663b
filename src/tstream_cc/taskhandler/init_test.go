package taskHandler

import (
	"flag"
	"fmt"
	"os"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/dlocker"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"testing"
)

var (
	fTestUin           = flag.String("test.uin", "************", "")
	fTestSubAccountUin = flag.String("test.subaccount.uin", "************", "")
	fTestRegion        = flag.String("test.region", "ap-nanjing",
		"e.g. ap-guangzhou ap-beijing ap-shanghai ap-chengdu")
	fTestUniqVpcId                    = flag.String("test.uniq.vpc.id", "vpc-dkj09k2d", "")
	fTestUniqSubnetId                 = flag.String("test.uniq.subnet.id", "subnet-q60aydeq", "")
	fTestZone                         = flag.String("test.zone", "ap-nanjing-1", "")
	fTestClusterCidr                  = flag.String("test.cluster.cidr", "10.0.64.0/20", "")
	fTestMaxNodePodNum                = flag.Uint64("test.max.node.pod.num", 32, "")
	fTestClusterId                    = flag.Int64("test.cluster.id", 1648, "")
	fTestClusterGroupId               = flag.String("test.clustergroup.id", "1660", "")
	fTestClusterSerialId              = flag.String("test.cluster.serial.id", "cluster-e8ovfofm", "")
	fTestClusterTargetCU              = flag.Int64("test.cluster.cu", 12, "")
	fTestOceanusTencentCloudApiDomain = flag.String("test.oceanus.tencentcloudapi.domain", "oceanus.dev.tencentcloudapi.com", "")
	fTestSecretId                     = flag.String("test.secret.id", "", "")
	fTestSecretKey                    = flag.String("test.secret.key", "", "")
	fTestSgId                         = flag.String("test.sgid", "sg-gost19xn", "")
	fTestGroup                        = flag.String("test.group", "Tke", "")
	fTestKey                          = flag.String("test.key", "", "")
	fTestEnableEni                    = flag.Bool("test.enable.eni", true, "")
	fTestIsEks                        = flag.Bool("test.is.eks", false, "")
	fTestSupportedFeatures            = flag.String("test.supported.features", `["FineGrainedResource"]`, "")
	fTestCvmCount                     = flag.Int64("test.cvm.count", 3, "")
	fTestCvmInstanceType              = flag.String("test.cvm.type", "S5.2XLARGE32", "")
	fTestIsWorker                     = flag.Bool("test.is.worker", true, "")

	fTestDbUrl = flag.String("test.db.url", "root:root@tcp(localhost:3306)/online_galileo?charset=utf8", "")
)

func init() {
	testing.Init()
	flag.Parse()
	tx, err := dao.NewDataSourceTransactionManager(*fTestDbUrl)
	if err != nil {
		fmt.Println("darwin Process create DataSourceTransactionManager err", err)
		os.Exit(-1)
	}
	service.SetTxManager(tx)
	dlocker.SetTxManager(tx)
}
