package taskHandler

import (
	"fmt"
	"runtime/debug"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	constants2 "tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	flow2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/flow"
)

type scaleTKEClusterHandler struct {
	taskStatusHandler taskStatusHandler
}

func NewScaleTKEClusterHandler(handler taskStatusHandler) *scaleTKEClusterHandler {
	return &scaleTKEClusterHandler{taskStatusHandler: handler}
}

func (this *scaleTKEClusterHandler) CompleteTask(request *flow.TaskExecRequest) (resp *flow.TaskExecResponse) {
	flowService := flow2.GetFlowService()

	params := request.Params
	requestId, _, _ := flowService.GetFlowParamString(params, constants2.FLOW_PARAM_REQUEST_ID, "")

	defer func() {
		if err := recover(); err != nil {
			logger.Infof("[%s] scaleTKEClusterHandler err %v", requestId, string(debug.Stack()))
			resp = flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1, fmt.Sprintf("%v", err), params)
		}
	}()

	taskStatusHandler := this.taskStatusHandler
	if taskStatusHandler == nil {
		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1, "taskStatusHandler is nil", params)
	}

	status, err := flowService.GetTaskStatus(request)
	if err != nil {
		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1, err.Error(), params)
	}

	switch status {
	case flow.TASK_STATUS_SUCCESS:
		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_SUCCESS, flow.TASK_STATUS_SUCCESS, 0.1, "ok", params)
	case flow.TASK_STATUS_RUNNING:
		return taskStatusHandler.HandleTaskStatusRunning(requestId, request)
	case flow.TASK_STATUS_INIT:
		return taskStatusHandler.HandleTaskStatusInit(requestId, request)
	case flow.TASK_STATUS_FAILED:
		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1, "", params)
	default:
		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1, "", params)
	}
}
