package taskHandler

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"runtime/debug"

	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/log"
	commonService "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	flow2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/flow"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/log"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/tke"
)

type ApplyEsServerlessFilebeatPatchHandler struct {
}

func (h *ApplyEsServerlessFilebeatPatchHandler) HandleTaskStatusInit(requestId string, request *flow.TaskExecRequest) (resp *flow.TaskExecResponse) {
	flowService := flow2.GetFlowService()
	defer func() {
		if err := recover(); err != nil {
			logger.Infof("[%s] err %v", requestId, string(debug.Stack()))
			resp = flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_IN_PROCESS, 0.1, fmt.Sprintf("%v", err), request.Params)
		}
	}()
	applyEsServerlessService, err := NewApplyEsServerlessPatchService(requestId, request)
	if err != nil {
		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_IN_PROCESS, 0.1,
			fmt.Sprintf("%v", err), request.Params)
	}
	return applyEsServerlessService.apply()
}

func (h *ApplyEsServerlessFilebeatPatchHandler) HandleTaskStatusRunning(requestId string, request *flow.TaskExecRequest) (resp *flow.TaskExecResponse) {
	flowService := flow2.GetFlowService()
	defer func() {
		if err := recover(); err != nil {
			logger.Infof("[%s] err %v", requestId, string(debug.Stack()))
			resp = flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_IN_PROCESS, 0.1, fmt.Sprintf("%v", err), request.Params)
		}
	}()
	applyEsServerlessService, err := NewApplyEsServerlessPatchService(requestId, request)
	if err != nil {
		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_IN_PROCESS, 0.1,
			fmt.Sprintf("%v", err), request.Params)
	}
	return applyEsServerlessService.checkReady()
}

type ApplyEsServerlessFilebeatPatchService struct {
	requestId string
	request   *flow.TaskExecRequest
	*flow2.ClusterFlowService
	filebeatNames []string
}

func NewApplyEsServerlessPatchService(reqId string, request *flow.TaskExecRequest) (*ApplyEsServerlessFilebeatPatchService, error) {
	clusterFlowService, err := flow2.NewClusterFlowService(request)
	if err != nil {
		logger.Errorf("NewApplyEsServerlessPatchService  has return error %v", err)
		return nil, err
	}
	indexId := request.Params[constants.FLOW_PARAM_ES_SERVERLESS_INDEXID]
	if indexId == "" {
		logger.Errorf("NewApplyEsServerlessPatchService, indexId is not valid")
		return nil, errors.New("NewApplyEsServerlessPatchService , indexId not valid")
	}
	logconfig := clusterFlowService.Cluster.LogConfig
	esServelessLogConf := &log.ClusterEsServelessLogConf{}
	err = json.Unmarshal([]byte(logconfig), esServelessLogConf)
	if err != nil {
		logger.Errorf("Cluster.LogConfig format error %s", logconfig)
		return nil, err
	}
	filebeats := make([]string, 0)
	for _, model := range esServelessLogConf.EsServerlessInfo {
		if model.IndexId == indexId {
			filebeats = model.DiDataCollectorIds
		}
	}
	result := &ApplyEsServerlessFilebeatPatchService{
		requestId:          reqId,
		request:            request,
		ClusterFlowService: clusterFlowService,
		filebeatNames:      filebeats,
	}
	return result, nil
}

func (s *ApplyEsServerlessFilebeatPatchService) apply() (rsp *flow.TaskExecResponse) {

	if s.ClusterGroup.NetEniType == constants.CLUSTER_NET_ENI_NODE {
		logger.Infof("%s: node eni plan no need enable eni", s.ClusterGroup.SerialId)
		return s.DoneRsp("update filebeat successfully")
	}

	filebeats := s.filebeatNames
	secretId, secretKey, err := commonService.GetSecretIdAndKeyByNetworkEnvType(s.ClusterGroup.NetEnvironmentType)
	if err != nil {
		logger.Errorf("GetSecretIdAndKeyByNetworkEnvType with error %v", err)
		return s.RetryRspWithErr(err)
	}
	aess := &service.ApplyEsServerlessService{
		SecretId:  secretId,
		SecretKey: secretKey,
		Token:     "",
		Region:    s.Region,
	}
	for _, v := range filebeats {
		enable, err := aess.FilebeatContainsEniIp(s.Cluster.KubeConfig, constants.FLOW_PARAM_ES_SERVERLESS_FILEBEAT_NAMESPACE, v)
		if err != nil {
			logger.Errorf("FilebeatContainsEniIp return error %v", err)
			return s.RetryRspWithErr(err)
		}
		if !enable {
			err = aess.UpdateFilebeat(s.Cluster.KubeConfig, constants.FLOW_PARAM_ES_SERVERLESS_FILEBEAT_NAMESPACE, v)
			if err != nil {
				logger.Errorf("update filebeat with error %v", err)
				return s.RetryRspWithErr(err)
			}
			logger.Infof("update filebeat %s successfully", v)
		}
		logger.Infof("%s is already enable eni", v)
	}
	return s.DoneRsp("update filebeat successfully")
}

func (s *ApplyEsServerlessFilebeatPatchService) checkReady() (rsp *flow.TaskExecResponse) {
	filebeats := s.filebeatNames
	clientSet, err := tke.GetTkeService().KubernetesClientsetFromCluster(s.request.DocId, s.Cluster)
	if err != nil {
		logger.Errorf("ApplyEsServerlessFilebeatPatchService#checkReady new kubernetes client has return error %v", err)
		return s.FailRspWithErr(err)
	}
	for _, v := range filebeats {
		ds, err := clientSet.AppsV1().DaemonSets(constants.FLOW_PARAM_ES_SERVERLESS_FILEBEAT_NAMESPACE).Get(context.TODO(), v, v1.GetOptions{})
		if err != nil {
			logger.Errorf("cannot get daemonset by name %s", v)
			return s.RetryRspWithErr(err)
		}
		if ds.Status.NumberUnavailable != 0 {
			return s.RetryRsp(fmt.Sprintf("filebeat %s not ready", v))
		}
		logger.Infof("filebeat %s apply successfully", v)
	}
	return s.DoneRsp("filebeat is ready")
}
