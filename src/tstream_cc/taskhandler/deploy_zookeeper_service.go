package taskHandler

import (
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	commandc "tencentcloud.com/tstream_galileo/src/common/command"
	"tencentcloud.com/tstream_galileo/src/common/configure"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/common/uuid"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cluster_admin_protocol"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/helm"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config/configure_center"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/helm"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/tke"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
	v20180525 "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke"
)

var (
	ValueTypeYaml = "yaml"
)

type DeployZookeeperService struct {
}

func (s *DeployZookeeperService) Apps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	cg := ctx.ClusterGroup

	// 共享集群 包年包月下的eks集群，不做任何处理
	if cg.Type == constants.CLUSTER_GROUP_TYPE_SUB_EKS {
		return result, nil
	}

	// 创建共享集群母集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone == "" {
		result = append(result, &createNamespaceApp{ctx: ctx, name: cg.Zone})
		result = append(result, &initZookeeperApp{ctx: ctx, zone: cg.Zone})
		result = append(result, &setRootAclForAdmin{ctx: ctx, zone: cg.Zone})
	}

	// 共享集群母集群，开新区
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone != "" {
		result = append(result, &createNamespaceApp{ctx: ctx, name: ctx.NewZone})
		result = append(result, &initZookeeperApp{ctx: ctx, zone: ctx.NewZone})
		result = append(result, &setRootAclForAdmin{ctx: ctx, zone: ctx.NewZone})
		//开新区要把所有子集群的 root path 创建一次
		clusterGroupList, err := service.ListClusterGroupByAgentSerialId(cg.SerialId)
		if err != nil {
			logger.Errorf("ListClusterGroupByAgentSerialId %s error %+v", cg.SerialId, err)
			return nil, err
		}
		for i := 0; i < len(clusterGroupList); i++ {
			result = append(result, &setClusterZookeeperPathAuth{subClusterGroup: clusterGroupList[i], zone: ctx.NewZone})
		}
	}

	// 独享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId == "" {
		result = append(result, &initZookeeperApp{ctx: ctx})
	}

	// 共享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId != "" {
		// TODO 创建子集群，要把集群在所有的zk的 root path 创建一次
		acs, err := service.NewClusterGroupServiceBySerialId(cg.AgentSerialId)
		if err != nil {
			logger.Errorf("setZookeeperAuth NewClusterGroupServiceBySerialId %s error %+v", cg.SerialId, err)
			return nil, err
		}
		supportedZones, err := acs.GetClusterGroup().GetSupportedZones()
		if err != nil {
			logger.Errorf("GetSupportedZones %s error %+v", cg.SerialId, err)
			return nil, err
		}
		for _, supportedZone := range supportedZones {
			// 创建子集群的zk path，以及设置独立的权限
			result = append(result, &setClusterZookeeperPathAuth{subClusterGroup: ctx.ClusterGroup, zone: supportedZone})
		}
	}

	// TODO zookeeper 配置权限

	return result, nil
}

type ZkAuth2ClusterManagerReq struct {
	Action          int64       `json:"action"`
	ClusterId       int64       `json:"clusterId"`
	ClusterSerialId string      `json:"clusterSerialId"`
	Params          zkAuthParam `json:"params"`
}

type zkAuthParam struct {
	RequestId string `json:"requestId"`
	ZkQuorum  string `json:"zkQuorum"`
	ZkUser    string `json:"zkUser"`
	ZkPass    string `json:"zkPass"`
	// setAcl
	AUser    string `json:"aUser"`
	APass    string `json:"aPass"`
	User     string `json:"user"`
	Pass     string `json:"pass"`
	RootPath string `json:"rootPath"`
}

type ZkAuth2ClusterManagerRsp struct {
	cluster_admin_protocol.QaeResponse
	Data struct {
		cluster_admin_protocol.ClusterAdminResponse
	} `json:"data"`
}
type setClusterZookeeperPathAuth struct {
	apps.ApplyApp
	zone            string
	subClusterGroup *table.ClusterGroup
}

func (c *setClusterZookeeperPathAuth) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	subCluster, err := service.GetActiveClusterByClusterGroupId(c.subClusterGroup.Id)
	if err != nil {
		logger.Errorf("setClusterZookeeperPathAuth GetActiveClusterByClusterGroupId clusterId %d, error %+v", c.subClusterGroup.SerialId, err)
		return nil, err
	}
	logger.Debugf("setClusterZookeeperPathAuth for %s", c.subClusterGroup.SerialId)
	commandService, err := commandc.NewCommandService(subCluster.Id)
	if err != nil || commandService == nil {
		logger.Errorf("setClusterZookeeperPathAuth can't get the commandService from clusterId %d", subCluster.Id)
		return nil, err
	}
	serviceUrl, err := commandc.GetCAOrCSUrl(subCluster)
	if err != nil {
		return nil, err
	}
	if c.subClusterGroup.ZkPass == "" || c.subClusterGroup.ZkUser == "" {
		logger.Errorf("setClusterZookeeperPathAuth cluster group %s zk info is null", c.subClusterGroup.SerialId)
		return nil, fmt.Errorf("setClusterZookeeperPathAuth cluster group %s zk info is null", c.subClusterGroup.SerialId)
	}
	// 解密
	zkPass, err := util.AesDecrypt(c.subClusterGroup.ZkPass, constants.AES_ENCRYPT_KEY)
	if err != nil {
		logger.Errorf("setClusterZookeeperPathAuth cluster group %s error can't decode the zkPass, use error %+v", c.subClusterGroup.SerialId, err)
		return nil, err
	}
	requestId := uuid.NewRandom().String()
	if c.subClusterGroup.AgentSerialId == "" {
		logger.Errorf("setClusterZookeeperPathAuth clusterGroup.AgentSerialId %s is null", c.subClusterGroup.SerialId)
		return nil, fmt.Errorf("setClusterZookeeperPathAuth clusterGroup.AgentSerialId %s is null", c.subClusterGroup.SerialId)
	}
	acs, err := service.NewClusterGroupServiceBySerialId(c.subClusterGroup.AgentSerialId)
	if err != nil {
		logger.Errorf("setClusterZookeeperPathAuth NewClusterGroupServiceBySerialId %s error %+v", c.subClusterGroup.SerialId, err)
		return nil, err
	}
	aUser := acs.GetClusterGroup().ZkUser
	aPass := acs.GetClusterGroup().ZkPass
	if aPass == "" || aUser == "" {
		logger.Errorf("setClusterZookeeperPathAuth agent cluster group %s zk info is null", acs.GetClusterGroup().SerialId)
		return nil, fmt.Errorf("setClusterZookeeperPathAuth agent cluster group %s zk info is null", acs.GetClusterGroup().SerialId)
	}
	// 解密
	aPass, err = util.AesDecrypt(acs.GetClusterGroup().ZkPass, constants.AES_ENCRYPT_KEY)
	if err != nil {
		logger.Errorf("setClusterZookeeperPathAuth agent cluster group %s error can't decode the zkPass, use error %+v", acs.GetClusterGroup().SerialId, err)
		return nil, err
	}
	param := zkAuthParam{
		RequestId: requestId,
		ZkQuorum:  deploy.GeneratePortConcatenatedIpString(deploy.GetNsZooKeeperServers(constants.ZooKeeperComponentReplicaNumber, c.zone), strconv.Itoa(constants.ZooKeeperPort)),
		AUser:     aUser,
		APass:     aPass,
		User:      c.subClusterGroup.ZkUser,
		Pass:      zkPass,
		RootPath:  c.subClusterGroup.ZkRootPath,
	}
	para := &ZkAuth2ClusterManagerReq{
		Action:          constants.ZKAUTH_SETACL_ACTION,
		ClusterId:       subCluster.Id,
		ClusterSerialId: c.subClusterGroup.SerialId,
		Params:          param,
	}
	caReq := cluster_admin_protocol.NewClusterAdminReq(requestId, constants.ZkAuthInterfaceName, para)
	sendData, err := json.Marshal(caReq)
	logger.Debugf("setClusterZookeeperPathAuth send request %s", c.subClusterGroup.SerialId)
	rsp, err := commandService.DoRequest(&commandc.CommandRequest{
		ReqId:    requestId,
		Url:      serviceUrl,
		SendData: string(sendData),
		Uin:      "",
		Apikey:   "setClusterZookeeperPathAuth",
		Timeout:  getTimeout(),
		Callback: "",
	})
	if err != nil {
		logger.Errorf("setZookeeperAuth send request error %+v", err)
		return nil, err
	}
	csRsp := &ZkAuth2ClusterManagerRsp{}
	err = json.Unmarshal([]byte(rsp), csRsp)
	if err != nil {
		logger.Errorf("setClusterZookeeperPathAuth Unmarshal ZkAuth2ClusterManagerRsp error %+v", err)
		return nil, err
	} else if csRsp.Data.ReturnCode != 0 {
		logger.Errorf("setClusterZookeeperPathAuth csRsp.Data.ReturnCode is not 0, err msg is %s", csRsp.Data.ReturnMsg)
		return nil, fmt.Errorf("%v", csRsp)
	}
	return nil, err
}

type setRootAclForAdmin struct {
	apps.ApplyApp
	ctx  *deploy.Context
	zone string
}

func (c *setRootAclForAdmin) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	clusterGroup := c.ctx.ClusterGroup
	cluster := c.ctx.Cluster

	logger.Debugf("setRootAclForAdmin for %s", clusterGroup.SerialId)
	commandService, err := commandc.NewCommandService(cluster.Id)
	if err != nil || commandService == nil {
		logger.Errorf("setRootAclForAdmin can't get the commandService from clusterId %d", c.ctx.Cluster.Id)
		return nil, err
	}
	serviceUrl, err := commandc.GetCAOrCSUrl(cluster)
	if err != nil {
		return nil, err
	}
	if clusterGroup.ZkPass == "" || clusterGroup.ZkUser == "" {
		logger.Errorf("setRootAclForAdmin cluster group %s zk info is null", clusterGroup.SerialId)
		return nil, fmt.Errorf("setRootAclForAdmin cluster group %s zk info is null", clusterGroup.SerialId)
	}
	// 解密
	zkPass, err := util.AesDecrypt(clusterGroup.ZkPass, constants.AES_ENCRYPT_KEY)
	if err != nil {
		logger.Errorf("setRootAclForAdmin cluster group %s error can't decode the zkPass, use error %+v", clusterGroup.SerialId, err)
		return nil, err
	}
	requestId := uuid.NewRandom().String()
	param := zkAuthParam{
		RequestId: requestId,
		ZkQuorum:  deploy.GeneratePortConcatenatedIpString(deploy.GetNsZooKeeperServers(constants.ZooKeeperComponentReplicaNumber, c.zone), strconv.Itoa(constants.ZooKeeperPort)),
		ZkUser:    clusterGroup.ZkUser,
		ZkPass:    zkPass,
	}

	para := &ZkAuth2ClusterManagerReq{
		Action:          constants.ZKAUTH_SETROOTACLFORADMIN_ACTION,
		ClusterId:       cluster.Id,
		ClusterSerialId: clusterGroup.SerialId,
		Params:          param,
	}
	caReq := cluster_admin_protocol.NewClusterAdminReq(requestId, constants.ZkAuthInterfaceName, para)
	sendData, err := json.Marshal(caReq)
	logger.Debugf("setRootAclForAdmin send request %s", clusterGroup.SerialId)
	rsp, err := commandService.DoRequest(&commandc.CommandRequest{
		ReqId:    requestId,
		Url:      serviceUrl,
		SendData: string(sendData),
		Uin:      "",
		Apikey:   "setRootAclForAdmin",
		Timeout:  getTimeout(),
		Callback: "",
	})
	if err != nil {
		logger.Errorf("setRootAclForAdmin send request error %+v", err)
		return nil, err
	}
	csRsp := &ZkAuth2ClusterManagerRsp{}
	err = json.Unmarshal([]byte(rsp), csRsp)
	if err != nil {
		logger.Errorf("setRootAclForAdmin Unmarshal ZkAuth2ClusterManagerRsp error %+v", err)
		return nil, err
	} else if csRsp.Data.ReturnCode != 0 {
		logger.Errorf("setRootAclForAdmin csRsp.Data.ReturnCode is not 0, err msg is %s", csRsp.Data.ReturnMsg)
		return nil, fmt.Errorf("%v", csRsp)
	}
	return nil, err
}

const CommandTimeOut = "command.timeout"

func getTimeout() int64 {
	var timeout = configure.GetConfInt64Value(constants.GALILEO_CONF_FILE, CommandTimeOut, 15)
	return timeout
}

type initZookeeperApp struct {
	apps.ApplyApp
	ctx  *deploy.Context
	zone string
}

func (c *initZookeeperApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	if c.ctx.Tke.ClusterType == constants.K8S_CLUSTER_TYPE_TKE {
		if c.ctx.Tke.ArchGeneration >= constants.TKE_ARCH_GENERATION_V6 {
			err = c.installNewZKOnTke()
		} else {
			err = c.installZKOnTke()
		}
	} else {
		err = c.installZKOnEks()
	}
	return nil, err
}

func (c *initZookeeperApp) Ready(client apps.Client, _ interface{}) (ready bool, err error) {
	ready, err = client.K8sService().StatefulSetAppsV1Ready(client.ClientSet(), c.ZKNamespace(), constants.ComponentZooKeeper)
	return
}

func (c *initZookeeperApp) ZKNamespace() string {
	result := constants.OCEANUS_NAMESPACE
	if c.zone != "" {
		result = c.zone
	}
	return result
}

func (c *initZookeeperApp) installZKOnEks() (err error) {
	clusterGroup := c.ctx.ClusterGroup
	cluster := c.ctx.Cluster

	helService := service2.GetHelmService()
	kubeConfig := []byte(cluster.KubeConfig)
	client, err := helService.NewClient(kubeConfig, c.ZKNamespace())
	if err != nil {
		return
	}

	res, err := helService.ListByName(client, c.ZKNamespace(), constants.ComponentZooKeeper)
	if err != nil {
		return
	}

	if len(res.Releases) > 0 {
		return
	}

	installOpts, err := c.getZooKeeperOnEKSInstallOptions(clusterGroup)
	if err != nil {
		return
	}
	status, err := helService.Install(client, c.ZKNamespace(), installOpts)
	if err != nil {
		return
	}
	logger.Infof("install eks zookeeper: %+v", status)

	return
}

func (c *initZookeeperApp) getZooKeeperOnEKSInstallOptions(clusterGroup *table.ClusterGroup) (*helm.InstallOptions, error) {
	cc := configure_center.CC(clusterGroup.Region).FlowCC().TkeCC()
	chartPath, err := cc.ZookeeperChartPath()
	if err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "Failed to get configuration from Rainbow", err)
	}
	clusterConfig, err := cc.ClusterConfig(clusterGroup)
	if err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "Failed to get configuration from Rainbow", err)
	}
	annotations := make(map[string]string)
	annotations[constants.EKS_CPU_TYPE_KEY] = clusterConfig.EksCpuType
	annotations[constants.EKS_SECURITY_GROUP_ID_KEY] = clusterConfig.SecurityGroup

	deployZooKeeperYaml, err := cc.EksZookeeperYaml(map[string]map[string]string{"Annotations": annotations})
	if err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "Failed to get configuration from Rainbow", err)
	}
	return &helm.InstallOptions{
		ChartPath:  chartPath,
		Name:       constants.ComponentZooKeeper,
		ValueBytes: []byte(deployZooKeeperYaml),
	}, nil
}

func (c *initZookeeperApp) installZKOnTke() (err error) {
	params := c.ctx.Request.Params
	clusterGroup := c.ctx.ClusterGroup
	tkeInstance := c.ctx.Tke
	// 调用 DescribeClusterReleaseDetails 检查ZK部署状态
	tkeDescribeClusterReleaseDetailsRequest := tke.GetTkeService().NewDefaultDescribeClusterReleaseDetailsRequestBuilder()
	tkeDescribeClusterReleaseDetailsRequest.
		WithNameSpace(c.ZKNamespace()).
		WithName(constants.ComponentZooKeeper).
		WithClusterId(tkeInstance.InstanceId)

	release, err := tke.GetTkeService().DescribeClusterReleaseDetailsWithScsAccount(clusterGroup, tkeDescribeClusterReleaseDetailsRequest.Build())
	if err != nil {
		return
	}

	// release.Status=nil 和 uninstalled 状态时，创建ZK应用
	if release.Status == nil || *release.Status == constants.TKE_HELM_STATUS_UNINSTALLED {

		tkeRequest := tke.GetTkeService().NewDefaultCreateClusterReleaseRequestBuilder()
		releaseValues, err := c.generateZooKeeperChartsValues(clusterGroup.Region, tkeInstance.ArchGeneration, clusterGroup.Type)
		if err != nil {
			return err
		}
		tkeRequest.
			WithClusterId(tkeInstance.InstanceId).
			WithNameSpace(c.ZKNamespace()).
			WithName(constants.ComponentZooKeeper).
			WithValues(releaseValues).
			WithChart(constants.ComponentZooKeeper).
			WithChartVersion(constants.ZooKeeperChartVersion)

		releaseInfo, err := tke.GetTkeService().CreateClusterReleaseWithScsAccount(clusterGroup, tkeRequest.Build())
		if err != nil {
			return err
		}

		if releaseInfo == nil || len(*releaseInfo.ID) <= 0 {
			return errors.New("CreateClusterReleaseWithScsAccount releaseId not find")
		}

		// 调用 createRelease 后设置流程参数便于出现 failed 状态后取消安装（ cancel 方法必须要应用ID，只有 createRelease 的 release 才有应用ID）
		params["zkReleaseId"] = *releaseInfo.ID
		return err
	} else if *release.Status == constants.TKE_HELM_STATUS_DEPLOYED {
		if invokeSource, ok := params[constants.FLOW_PARAM_INVOKE_SOURCE]; ok &&
			invokeSource == constants.FLOW_INVOKE_FROM_CREATE_COMPONETS {
			tkeRequest := tke.GetTkeService().NewDefaultUpgradeClusterReleaseRequestBuilder()
			releaseValues, err := c.generateZooKeeperChartsValues(clusterGroup.Region, tkeInstance.ArchGeneration, clusterGroup.Type)
			if err != nil {
				return err
			}
			tkeRequest.
				WithClusterId(tkeInstance.InstanceId).
				WithNameSpace(c.ZKNamespace()).
				WithName(constants.ComponentZooKeeper).
				WithValues(releaseValues).
				WithChart(constants.ComponentZooKeeper).
				WithChartVersion(constants.ZooKeeperChartVersion)

			_, err = tke.GetTkeService().UpgradeClusterReleaseWithScsAccount(clusterGroup, tkeRequest.Build())
			if err != nil {
				return errors.New("UpgradeClusterReleaseWithScsAccount releaseId not find")
			}
			params[constants.FLOW_PARAM_INVOKE_SOURCE] = fmt.Sprintf("%s_invoked", constants.FLOW_INVOKE_FROM_CREATE_COMPONETS)
		}
		return
	} else if *release.Status == constants.TKE_HELM_STATUS_PENDING_INSTALL {
		return
	} else if *release.Status == constants.TKE_HELM_STATUS_FAILED {
		releaseId := params["zkReleaseId"]
		if len(releaseId) <= 0 {
			return errors.New("CreateClusterReleaseWithScsAccount releaseId not find")
		}
		tkeCancelClusterReleaseRequest := tke.GetTkeService().NewDefaultCancelClusterReleaseRequestBuilder()
		tkeCancelClusterReleaseRequest.
			WithId(releaseId).
			WithClusterId(tkeInstance.InstanceId)
		pendingRelease, err := tke.GetTkeService().CancelClusterReleaseWithScsAccount(clusterGroup, tkeCancelClusterReleaseRequest.Build())
		if err != nil {
			return err
		}
		return errors.New(fmt.Sprintf("release status is failed, cancel it now, response: %+v", pendingRelease))
	} else {
		return errors.New(fmt.Sprintf("Invalid status, DescribeClusterReleaseDetails response: %+v", release))
	}
}

func (c *initZookeeperApp) installNewZKOnTke() (err error) {
	cluster := c.ctx.Cluster

	helService := service2.GetHelmService()
	kubeConfig := []byte(cluster.KubeConfig)
	client, err := helService.NewClient(kubeConfig, c.ZKNamespace())
	if err != nil {
		return
	}

	res, err := helService.ListByName(client, c.ZKNamespace(), constants.ComponentZooKeeper)
	if err != nil {
		return
	}

	if len(res.Releases) > 0 {
		return
	}
	helmInstallOptions, err := c.getZooKeeperInstallOptions()
	if err != nil {
		return err
	}
	status, err := helService.Install(client, c.ZKNamespace(), helmInstallOptions)
	if err != nil {
		return
	}
	logger.Infof("install zookeeper: %+v", status)

	return
}

func (c *initZookeeperApp) getZooKeeperInstallOptions() (*helm.InstallOptions, error) {
	cc := configure_center.CC(c.ctx.ClusterGroup.Region).FlowCC().TkeCC()
	chartPath, err := cc.TkeZookeeperChartPath()
	if err != nil {
		return nil, errors.New("failed to get configuration from Rainbow")
	}

	// 直接构造values参数
	values := map[string]interface{}{
		"replicaCount": 3,
		"minServerId":  1,
		"persistence": map[string]interface{}{
			"size": "10Gi",
		},
		"resources": map[string]interface{}{
			"limits": map[string]interface{}{
				"cpu":    "500m",
				"memory": "2200Mi",
			},
			"requests": map[string]interface{}{
				"cpu":    "500m",
				"memory": "2200Mi",
			},
		},
		"networkPolicy": map[string]interface{}{
			"enabled": false, // 禁用 NetworkPolicy 避免跨节点通信问题
		},
	}

	// 设置NodeSelector
	nodeSelector := make(map[string]string)
	if c.ctx.Tke.ClusterType == constants.K8S_CLUSTER_TYPE_TKE {
		if c.ctx.Tke.ArchGeneration >= constants.TKE_ARCH_GENERATION_V2 {
			nodeSelector[constants.TKE_CVM_ZK_LABEL_KEY] = constants.TKE_ZK_NODE_LABEL_VAL
			values["PriorityClassName"] = constants.TKE_PRIORITY_CLASS_NAME
		} else {
			nodeSelector[constants.TKE_CVM_LABEL_KEY] = constants.TKE_CONTROL_NODE_LABEL_VAL
		}
		if c.zone != "" {
			nodeSelector[constants.TKE_CVM_NODE_ZONE] = c.zone
		}
	}
	values["nodeSelector"] = nodeSelector
	return &helm.InstallOptions{
		ChartPath: chartPath,
		Name:      constants.ComponentZooKeeper,
		ValuesMap: values,
	}, nil
}

// 生成 TKE Charts 所需的 Values
func (c *initZookeeperApp) generateZooKeeperChartsValues(region string, archGeneration int, cgType int8) (*v20180525.ReleaseValues, error) {
	params := make(map[string]interface{})
	nodeSelector := make(map[string]string)
	params["NodeSelector"] = nodeSelector
	if archGeneration >= constants.TKE_ARCH_GENERATION_V2 {
		nodeSelector[constants.TKE_CVM_ZK_LABEL_KEY] = constants.TKE_ZK_NODE_LABEL_VAL
		params["PriorityClacssName"] = constants.TKE_PRIORITY_CLASS_NAME
	} else {
		nodeSelector[constants.TKE_CVM_LABEL_KEY] = constants.TKE_CONTROL_NODE_LABEL_VAL
	}

	if c.zone != "" {
		nodeSelector[constants.TKE_CVM_NODE_ZONE] = c.zone
	}

	deployZooKeeperYaml, err := configure_center.CC(region).FlowCC().TkeCC().ZookeeperYaml(params)
	if err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "Failed to get configuration from Rainbow", err)
	}

	values := &v20180525.ReleaseValues{
		RawOriginal: &deployZooKeeperYaml,
		ValuesType:  &ValueTypeYaml,
	}

	return values, nil
}

type createStorageClassApp struct {
	apps.StorageClass
	ctx               *deploy.Context
	volumeBindingMode string
}

func (c *createStorageClassApp) Params() (interface{}, error) {
	return struct {
		VolumeBindingMode string
	}{c.volumeBindingMode}, nil
}

func (c *createStorageClassApp) Decode(params, into interface{}) (interface{}, error) {
	return c.ctx.FlowCC.TkeCC().OceanusCC().StorageClass(params, into)
}
