package taskHandler

import (
	"fmt"
	sdkTke "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/tke/v20180525"
	v1 "k8s.io/api/apps/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"strings"
	log "tencentcloud.com/tstream_galileo/src/common/logger"
	logger "tencentcloud.com/tstream_galileo/src/main/credentials_provider/common/log"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/tke"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type deleteTkeService struct {
}

func (s *deleteTkeService) Apps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	cg := ctx.ClusterGroup

	if cg.Type == constants.CLUSTER_GROUP_TYPE_SUB_EKS {
		return result, nil
	}

	result = append(result, &deleteSessionApp{ctx: ctx})
	result = append(result, &deleteGatewaysApp{ctx: ctx})

	tkeCluster := ctx.Tke
	// eks集群不能级联删除了
	if tkeCluster.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		result = append(result, &deleteEksResources{ctx: ctx})
	}

	// 共享集群母集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM {
		result = append(result, &deleteTkeApp{ctx: ctx})
	}
	// 独享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId == "" {
		result = append(result, &deleteTkeApp{ctx: ctx})
	}
	// 共享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId != "" {
		// 删除ns
		result = append(result, &DeleteIngressApp{ctx: ctx})
		result = append(result, &deleteNamespaceApp{ctx: ctx})
	}

	return result, nil
}

type deleteResourceQuotaApp struct {
	apps.ResourceQuota
	ctx *deploy.Context
}

func (c *deleteResourceQuotaApp) Params() (interface{}, error) {

	acs, err := service.NewClusterGroupServiceBySerialId(c.ctx.ClusterGroup.ParentSerialId)
	if err != nil {
		return nil, err
	}
	cuNum := acs.GetClusterGroup().CuNum
	serialId := acs.GetClusterGroup().SerialId

	cpu := fmt.Sprintf("%d", cuNum)
	mem := fmt.Sprintf("%dGi", cuNum*constants.CVM_DEFAULT_MEMRATIO)
	return struct {
		Name           string
		Namespace      string
		RequestsCpu    string
		RequestsMemory string
		LimitsCpu      string
		LimitsMemory   string
	}{serialId,
		serialId,
		cpu,
		mem,
		cpu,
		mem}, nil
}

func (c *deleteResourceQuotaApp) Decode(params, into interface{}) (interface{}, error) {
	return c.ctx.FlowCC.TkeCC().ResourceQuotaCC().ResourceQuota(params, into)
}

type deleteEksResources struct {
	ctx *deploy.Context
	apps.ApplyApp
}

func (c *deleteEksResources) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	// 删除 jm
	_, err = k8s.GetK8sService().DeleteDeployments(c.ctx.ClientSet(), constants.DEFAULT_NAMESPACE,
		metav1.ListOptions{
			LabelSelector: "component==jobmanager",
		})
	if err != nil {
		logger.Errorf("Failed to DeleteDeployment component==jobmanager because %+v", err)
		return nil, err
	}
	// 删除tm
	_, err = k8s.GetK8sService().DeleteDeployments(c.ctx.ClientSet(), constants.DEFAULT_NAMESPACE,
		metav1.ListOptions{
			LabelSelector: "component==taskmanager",
		})
	if err != nil {
		logger.Errorf("Failed to DeleteDeployment component==taskmanager because %+v", err)
		return nil, err
	}
	// 删除sql-gateway
	_, err = k8s.GetK8sService().DeleteDeployments(c.ctx.ClientSet(), constants.DEFAULT_NAMESPACE,
		metav1.ListOptions{
			LabelSelector: "component==sql-gateway",
		})
	if err != nil {
		logger.Errorf("Failed to DeleteDeployment component==sql-gateway because %+v", err)
		return nil, err
	}
	// 删除 ingress-nginx-ingress-controller ingress-nginx-ingress-default-backend
	_, err = k8s.GetK8sService().DeleteDeployment(c.ctx.ClientSet(), &v1.Deployment{
		ObjectMeta: metav1.ObjectMeta{Namespace: constants.DEFAULT_NAMESPACE, Name: constants.TkeSvcIngressName}})
	if err != nil {
		logger.Errorf("Failed to DeleteDeployment %s because %+v", constants.TkeSvcIngressName, err)
		return nil, err
	}
	_, err = k8s.GetK8sService().DeleteDeployment(c.ctx.ClientSet(), &v1.Deployment{
		ObjectMeta: metav1.ObjectMeta{Namespace: constants.DEFAULT_NAMESPACE, Name: "ingress-nginx-ingress-default-backend"}})
	if err != nil {
		logger.Errorf("Failed to DeleteDeployment ingress-nginx-ingress-default-backend because %+v", err)
		return nil, err
	}

	// 直接删除oceanus ns
	err = k8s.DeleteNamespace(c.ctx.ClientSet(), constants.OCEANUS_NAMESPACE)
	if err != nil {
		log.Errorf("Failed to Delete namespace %s because %+v", constants.OCEANUS_NAMESPACE, err)
		return nil, err
	}
	return nil, nil
}

type deleteNamespaceApp struct {
	ctx *deploy.Context
	apps.ApplyApp
}

func (c *deleteNamespaceApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	clusterGroup := c.ctx.ClusterGroup
	flinkNamespace := clusterGroup.SerialId
	logger.Infof("Delete namespace %s", flinkNamespace)
	err = k8s.DeleteNamespace(c.ctx.ClientSet(), flinkNamespace)
	if err != nil {
		log.Errorf("Failed to Delete namespace %s because %+v", flinkNamespace, err)
		return nil, err
	}
	oceanusNamespace := fmt.Sprintf("oceanus-%s", clusterGroup.SerialId)
	logger.Infof("Delete namespace %s", oceanusNamespace)
	err = k8s.DeleteNamespace(c.ctx.ClientSet(), oceanusNamespace)
	if err != nil {
		log.Errorf("Failed to Delete namespace %s because %+v", oceanusNamespace, err)
		return nil, err
	}
	return nil, nil
}

type deleteSessionApp struct {
	ctx *deploy.Context
	apps.ApplyApp
}

func (c *deleteSessionApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	clusterGroup := c.ctx.ClusterGroup
	clusterSessions, err := service3.GetClusterSessionsBySerialId(clusterGroup.SerialId)
	if err != nil {
		return nil, err
	}

	for _, clusterSession := range clusterSessions {
		err = service3.SwitchClusterSessionStatusTo(clusterSession.Id, constants.ClusterSessionDeleted)
		if err != nil {
			log.Errorf("Failed to SwitchClusterSessionStatus because %+v", err)
			return nil, err
		}
	}
	return nil, nil
}

type deleteGatewaysApp struct {
	ctx *deploy.Context
	apps.ApplyApp
}

func (c *deleteGatewaysApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	clusterGroup := c.ctx.ClusterGroup
	sqlGateways, err := service.GetSqlGatewaysByClusterGroupId(clusterGroup.Id)
	if err != nil {
		return nil, err
	}
	for _, gateway := range sqlGateways {
		err = service.SwitchSqlGatewayStatus(gateway.SerialId, constants.SqlGatewayDeleted)
		if err != nil {
			log.Errorf("Failed to SwitchSqlGatewayStatus because %+v", err)
			return nil, err
		}
	}
	return nil, nil
}

type deleteTkeApp struct {
	ctx *deploy.Context
	apps.ApplyApp
}

func (c *deleteTkeApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	tkeCluster := c.ctx.Tke
	cg := c.ctx.ClusterGroup
	tkeService := tke.GetTkeService()

	if tkeCluster.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		req := sdkTke.NewDeleteEKSClusterRequest()
		req.ClusterId = &tkeCluster.InstanceId
		_, err = tkeService.DeleteEKSClusterWithScsAccountByNetEnvironmentType(cg.NetEnvironmentType, cg.Region, req)
	}
	if tkeCluster.ClusterType == constants.K8S_CLUSTER_TYPE_TKE {
		req := tkeService.NewDefaultDeleteClusterRequestBuilder().WithClusterId(tkeCluster.InstanceId).
			WithInstanceDeleteMode(constants.TKE_DELETE_CLUSTER_MODEL).Build()
		_, err = tkeService.DeleteClusterWithScsAccountByNetEnvironmentType(cg.NetEnvironmentType, cg.Region, req)
	}

	if err == nil || strings.Contains(err.Error(), "ClusterNotFound") || strings.Contains(err.Error(), "ResourceNotFound") {
		return nil, nil
	}

	return nil, err
}
