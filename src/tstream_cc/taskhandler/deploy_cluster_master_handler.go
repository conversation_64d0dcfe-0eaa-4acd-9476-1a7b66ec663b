package taskHandler

import (
	v1 "k8s.io/api/core/v1"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/oceanus_controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/region"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type DeployClusterMasterService struct {
}

func (s *DeployClusterMasterService) Apps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	cg := ctx.ClusterGroup

	// 共享集群 包年包月下的eks集群，不做任何处理
	if cg.Type == constants.CLUSTER_GROUP_TYPE_SUB_EKS {
		return result, nil
	}

	// 创建共享集群母集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone == "" {
	}

	// 共享集群母集群，开新区
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone != "" {
	}

	// 独享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId == "" {
		if ctx.Tke.ArchGeneration < constants.TKE_ARCH_GENERATION_V2 {
			secret, err := ctx.GetOceanusIPSecret()
			if err != nil {
				return result, err
			}
			result = append(result,
				s.clusterMasterConfigMap(ctx, secret),
				ctx.LogListenerConfigMap(false, secret),
				ctx.LogListenerMetaConfigMap(constants.ComponentClusterMaster, secret),
				&clusterMaster{ctx: ctx},
				ctx.Service(constants.ComponentClusterMaster, constants.ClusterMasterPort, secret))
		}
	}

	// 共享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId != "" {
	}
	return result, nil
}

type clusterMaster struct {
	ctx *deploy.Context
	apps.StatefulSet
}

func (c *clusterMaster) stsParams() (*oceanus_controller.Base, error) {
	appContainerImage, err := c.ctx.CC().ImageRegistry().ClusterMaster()
	if err != nil {
		return nil, err
	}
	logContainerImage, err := c.ctx.CC().ImageRegistry().LogListener()
	if err != nil {
		return nil, err
	}

	base, err := c.ctx.ParamBase(constants.K8S_KIND_STATEFULSET,
		constants.OCEANUS_NAMESPACE,
		constants.ComponentClusterMaster,
		false)
	if err != nil {
		return nil, err
	}

	base.AppImage = appContainerImage
	base.SidecarImage = logContainerImage
	return base, nil
}

func (c *clusterMaster) Params() (interface{}, error) {
	return c.stsParams()
}

func (c *clusterMaster) Decode(params, into interface{}) (interface{}, error) {
	return c.ctx.FlowCC.AppsCC().ClusterMaster(params, into)
}

func (c *DeployClusterMasterService) clusterMasterConfigMap(ctx *deploy.Context, secret *v1.Secret) apps.App {
	return ctx.ConfigMap(
		secret,
		apps.CMWithName(constants.ClusterMasterConfigmapName),
		apps.CMAddData("master-conf.yaml", c.getConfigForMasterConf(ctx)),
		apps.CMAddData("notify.properties", c.getConfigForNotifyProperties(ctx)),
		apps.CMAddData("log4j.properties", ctx.FlowCC.CommonLog4j),
		apps.CMAddData("application.conf", ctx.ConfUseCdbParams("cluster_master", ctx.FlowCC.ApplicationConf)))
}

func (c *DeployClusterMasterService) getConfigForMasterConf(ctx *deploy.Context) apps.CMDataValue {
	return func() (string, error) {
		nginxUsername, nginxPassword, url, err := ctx.FlowCC.NginxCC().Info()
		if err != nil {
			return "", err
		}
		regionId, err := region.GetRegionIdByName(ctx.ClusterGroup.Region)
		if err != nil {
			return "", err
		}
		params := struct {
			TaskCenterServiceUrl    string
			ClusterMasterServiceUrl string
			PublicServiceNginxUrl   string
			NginxUsername           string
			NginxPassword           string
			RegionId                int64
			ClusterGroupId          int64
			ClusterId               int64
		}{
			TaskCenterServiceUrl:    constants.TaskcenterServiceName,
			ClusterMasterServiceUrl: constants.ClusterMasterServiceName,
			PublicServiceNginxUrl:   url,
			NginxUsername:           nginxUsername,
			NginxPassword:           nginxPassword,
			RegionId:                int64(regionId),
			ClusterGroupId:          ctx.ClusterGroup.Id,
			ClusterId:               ctx.Cluster.Id,
		}
		return ctx.FlowCC.ClusterMasterCC().MasterConfYaml(params)
	}
}

func (s *DeployClusterMasterService) getConfigForNotifyProperties(ctx *deploy.Context) apps.CMDataValue {
	return func() (string, error) {
		url, err := ctx.FlowCC.NginxCC().Url()
		if err != nil {
			return "", err
		}
		// 替换模板参数
		params := struct {
			PublicServiceNginxUrl string
		}{
			PublicServiceNginxUrl: url,
		}
		return ctx.FlowCC.NotifyProperties(params)
	}
}
