package taskHandler

import (
	"encoding/json"
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config"
	"testing"
)

func TestDeploySetatsHiveMetastoreHandler(t *testing.T) {
	config.InitRainbowService(nil, nil, nil)

	handler := NewDeployHandler(&DeploySetatsHiveMetastoreHandler{})
	request := &flow.TaskExecRequest{
		Retrycount: 0,
		Processkey: "test1",
		Taskcode:   "test1",
		DocId:      "12222",
		FlowId:     "122",
		TaskId:     "1222",
		Params: map[string]string{
			constants.FLOW_PARAM_CLUSTER_GROUP_ID: fmt.Sprintf("%d", 39),
			constants.FLOW_PARAM_CLUSTER_ID:       fmt.Sprintf("%d", 1),
			constants.FLOW_PARAM_REQUEST_ID:       "test_hahaha11111",
		},
	}

	rsp := handler.CompleteTask(request)

	b, _ := json.MarshalIndent(rsp, "", "")

	if rsp.RetCode != flow.TASK_IN_PROCESS {
		t.Fatal(string(b))
	}

	t.Log(string(b))
}
