package taskHandler

import (
	"fmt"
	v1 "k8s.io/api/apps/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	clusterService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config/configure_center"
	flow2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/flow"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/tke"
)

type DeleteTaskManagerService struct {
}

func (d *DeleteTaskManagerService) CompleteTask(request *flow.TaskExecRequest) (resp *flow.TaskExecResponse) {
	flowService := flow2.GetFlowService()
	params := request.Params

	defer func() {
		if errs := recover(); errs != nil {
			resp = flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1,
				fmt.Sprintf("%v", errs), params)
		}
	}()

	var err error
	resp, err = d.handle(request)
	if err != nil {
		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1, fmt.Sprintf("%v", err), params)
	}
	return
}

func (d *DeleteTaskManagerService) handle(request *flow.TaskExecRequest) (resp *flow.TaskExecResponse, err error) {
	flowService := flow2.GetFlowService()
	// Session集群Flink版本(此参数决定启动Session集群时使用什么版本Flink镜像也作为Session集群JM和TM的Deployment的名称一部分)
	//flinkVersion, _ := .GetParam(constants.FLOW_PARAM_FLINK_VERSION, ctx.CC().DefaultFlinkVersion())
	params := request.Params
	region, exists, err := flowService.GetFlowParamString(params, constants.FLOW_PARAM_REGION, "ap-guangzhou")
	if err != nil {
		return nil, err
	}
	if !exists {
		panic("region not exists")
	}
	flinkVersion, exists, err := flowService.GetFlowParamString(params, constants.FLOW_PARAM_FLINK_VERSION, configure_center.CC(region).DefaultFlinkVersion())
	if err != nil {
		return nil, err
	}
	if !exists {
		panic("region not exists")
	}
	noDotLowerFlinkVersion := service.GetNoDotLowerFlinkVersion(flinkVersion)
	ClusterGroup, Cluster, err := flowService.GetClusterGroupAndCluster(request.Params)
	if err != nil {
		return nil, err
	}
	clientset, err := tke.GetTkeService().KubernetesClientsetFromCluster(request.DocId, Cluster)
	if err != nil {
		return nil, err
	}
	namespace := clusterService.GetDefaultNamespace(ClusterGroup)

	// 删除TmDeployment
	tmDeploymentName := fmt.Sprintf(constants.ClusterSessionTMTemplate, ClusterGroup.SerialId, noDotLowerFlinkVersion)
	_, err = k8s.GetK8sService().DeleteDeployment(clientset, &v1.Deployment{
		ObjectMeta: metav1.ObjectMeta{Namespace: namespace, Name: tmDeploymentName}})
	if err != nil {
		logger.Errorf("Failed to DeleteDeployment because %+v", err)
		return nil, err
	}
	return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_SUCCESS, flow.TASK_STATUS_SUCCESS, 0.1, "ok",
		params), nil
}
