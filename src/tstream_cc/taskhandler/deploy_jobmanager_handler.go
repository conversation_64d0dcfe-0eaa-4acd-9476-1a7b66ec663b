package taskHandler

import (
	"bytes"
	"encoding/json"
	"fmt"
	"k8s.io/apimachinery/pkg/util/yaml"
	"path"
	"strconv"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	model3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/oceanus_controller"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	clusterService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config/image_registry"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type DeployJobManagerService struct {
}

const (
	USER_DEPENDENCY_CONFIG_KEY              = "kubernetes.internal.session-cluster.kerberos.jars"
	KERBEROS_LOGIN_KEYTAB_KEY               = "security.kerberos.login.keytab"
	KERBEROS_LOGIN_CONF_KEY                 = "security.kerberos.login.conf"
	USER_DEPENDENCY_FILE_DOWNLOAD_DIR_VALUE = "/opt/flink/usrlib"
	KERBEROS_KRB5_CONF_KEY                  = "security.kerberos.krb5-conf.path"
	USER_CONFIG_KERBEROS_ENABLED_KEY_1      = "fs.hdfs.hadoop.security.authentication"
	USER_CONFIG_KERBEROS_ENABLED_KEY_2      = "flink.hadoop.hadoop.security.authentication"
	KUBERNETES_KERBEROS_ENABLED_KEY         = "kubernetes.internal.session-cluster.kerberos.enable"
)

/**
 * 1. 添加flink的configmap
 * 2. 添加hadoop的configmap
 * 3. 添加flink-jobmanager-deployment
 * 4. 添加flink-jobmanager-service
 */
func (s *DeployJobManagerService) Apps(ctx *deploy.Context) ([]apps.App, error) {
	// Session集群Flink版本(此参数决定启动Session集群时使用什么版本Flink镜像也作为Session集群JM和TM的Deployment的名称一部分)
	flinkVersion, _ := ctx.GetParam(constants.FLOW_PARAM_FLINK_VERSION, ctx.CC().DefaultFlinkVersion())

	refs, _ := ctx.GetParam(constants.FLOW_PARAM_REFS, constants.EMPTY)
	logger.Infof("in deploy_jobmanager_handler Apps, refs: %s", refs)

	// ClusterGroupSerialId
	serialId := ctx.ClusterGroup.SerialId
	// 集群默认FlinkConf(这些配置在启动session集群时不可被修改)
	flinkConfStr, jmDeployment, err := s.getFlinkConfStr(ctx, flinkVersion, serialId)
	if err != nil {
		panic(errorcode.NewStackError(errorcode.InternalErrorCode, "ctx.FlinkConfString err", err))
	}
	// flink配置转Map(用于添加flink配置)
	flinkConfMap := s.confToMap(flinkConfStr)
	// 添加开启session集群时自定义的高级参数(不能覆盖原来session集群的默认配置)
	properties, exists := ctx.GetParam(constants.FLOW_PARAM_PROPERTIES, constants.EMPTY)
	dynamicProperties := make([]*model3.Property, 0)

	if exists {
		foundUserDefinedMaxParallelism := false
		err := json.Unmarshal([]byte(properties), &dynamicProperties)
		if err != nil {
			// 防御式编程, 理论上不会格式错乱
			panic(errorcode.NewStackError(errorcode.InternalErrorCode, "JobConfig Properties JSON unmarshal error", err))
		}
		// 修改kerberos配置文件路径，指向容器中文件位置。
		dynamicProperties = updatePropertyForKerberos(dynamicProperties)
		for _, property := range dynamicProperties {
			if property.Key == constants.MAX_PARALLELISM_KEY {
				foundUserDefinedMaxParallelism = true
			}
			flinkConfMap[property.Key] = property.Value
		}
		if !foundUserDefinedMaxParallelism {
			flinkConfMap[constants.MAX_PARALLELISM_KEY] = constants.MAX_PARALLELISM_DEFAULT_VALUE
		}
	}
	// flink 1.18 for sql gateway
	if constants.Flinkversion118 == flinkVersion {
		flinkConfMap[constants.RESOURCE_ALLOCATION_STRATEGY] = "DEFAULT"
		flinkConfMap[constants.NUMBER_OF_TASK_SLOTS_KEY] = "20"
	}

	// 添加ClusterConfig中的配置
	clusterConfig, err := ctx.Cluster.GetClusterConfig()
	for k, v := range clusterConfig {
		switch value := v.(type) {
		case string:
			flinkConfMap[k] = value
			if k == constants.KUBERNETES_JOBMANAGER_ANNOTATIONS {
				annotations, err := ctx.GetPodAnnotations(true, true)
				if err != nil {
					panic(errorcode.NewStackError(errorcode.InternalErrorCode, "ctx.GetPodAnnotations err", err))
				}
				jmDeployment.CrossTenant = annotations
			}
		case map[string]interface{}:
			if k != flinkVersion {
				break
			}
			for fk, fv := range value {
				switch fValue := fv.(type) {
				case string:
					flinkConfMap[fk] = fValue
					if constants.FLINK_K8S_CONTAINER_IMAGE == fk {
						jmDeployment.ContainerImage = fValue
					}
					if constants.FLINK_K8S_CONTAINER_IMAGE_PULL_POLICY == fk {
						jmDeployment.ImagePullPolicy = fValue
					}
				}
			}
		}
	}

	// flink-1.16  之后, 内置了 cos filesystem; 所以创建session集群时候, 需要把cos的参数加上
	if flinkVersion != constants.FLINK_VERSION_1_13 {
		cosProperties := service2.GetCOSNFlinkConf(ctx.Region, ctx.ClusterGroup.AppId, dynamicProperties)
		for _, p := range cosProperties {
			flinkConfMap[p.Key] = p.Value
		}
	}

	if _, ok := flinkConfMap["taskmanager.memory.framework.off-heap.size"]; !ok {
		flinkConfMap["taskmanager.memory.framework.off-heap.size"] = "120m"
	}

	jmDeploymentConfig := &clusterSessionJobManager{ctx: ctx, jmDeployment: jmDeployment, namespace: jmDeployment.Namespace}

	flinkClusterSessionConfigMap := s.flinkClusterSessionConfigMap(flinkConfMap, refs)
	return []apps.App{
		// flink-config.configmap
		apps.NewConfigMap(
			// configmap的名称
			apps.CMWithName(fmt.Sprintf(constants.ClusterSessionFlinkConfigTemplate, serialId, service2.GetNoDotLowerFlinkVersion(flinkVersion))),
			apps.CMWithNamespace(jmDeployment.Namespace),
			// flink配置
			apps.CMAddData("flink-conf.yaml", flinkClusterSessionConfigMap),
			// flink日志配置
			apps.CMAddData("log4j-console.properties", ctx.FlowCC.ClusterAdminCC().FlinkLog4jV2),
		),
		// hadoop-config.configmap
		s.HadoopConfigMap(ctx, flinkVersion, serialId, jmDeployment.Namespace),
		// jobmanager.service
		apps.NewSessionClusterService(
			apps.SessionClusterServiceWithName(jmDeployment.AppName+"-rest"),
			apps.SessionClusterServiceWithNamespace(jmDeployment.Namespace),
			apps.SessionClusterServiceWithPorts([]apps.Port{
				{Name: "jobmanager-rpc", Port: 6123},
				{Name: "blobserver", Port: 6124},
				{Name: "rest", Port: 8081},
			}),
			apps.SessionClusterServiceWithLabels(s.JmServiceLabels(jmDeployment)),
			apps.SessionClusterServiceWithSelector(s.JmServiceLabels(jmDeployment)),
		),
		// jobmanager.deployment
		jmDeploymentConfig,
	}, nil
}

/**
 * 启动session集群需要的HadoopConfigMap
 */
func (s *DeployJobManagerService) HadoopConfigMap(ctx *deploy.Context, flinkVersion string, serialId string, namespace string) apps.App {
	vFunc := func() (string, error) {
		// 替换模板参数
		params := struct {
			CosBucket string
			Region    string
		}{
			CosBucket: ctx.Cluster.DefaultCOSBucket,
			Region:    ctx.ClusterGroup.Region,
		}
		coreSite, err := ctx.FlowCC.CoreSiteXml(params)
		if err != nil {
			return "", err
		}
		return coreSite, nil
	}
	return apps.NewConfigMap(
		// configmap的名称
		apps.CMWithName(fmt.Sprintf(constants.ClusterSessionHadoopConfigTemplate, serialId, service2.GetNoDotLowerFlinkVersion(flinkVersion))),
		apps.CMWithNamespace(namespace),
		apps.CMAddData("core-site.xml", vFunc),
	)
}

func (s *DeployJobManagerService) flinkClusterSessionConfigMap(flinkConfMap map[string]string, refs string) apps.CMDataValue {
	return func() (string, error) {
		resultConf := make([]string, 0)
		resultConf = append(resultConf, fmt.Sprintf("%s: %s", USER_DEPENDENCY_CONFIG_KEY, refs))
		for k, v := range flinkConfMap {
			resultConf = append(resultConf, fmt.Sprintf("%s: %s", k, v))
		}
		return strings.Join(resultConf, "\n"), nil
	}
}

func (s *DeployJobManagerService) getFlinkConfStr(ctx *deploy.Context, flinkVersion string, serialId string) (string, *oceanus_controller.ClusterSessionJmDeployment, error) {
	noDotLowerFlinkVersion := service2.GetNoDotLowerFlinkVersion(flinkVersion)
	// 获取JM的CU规格
	jmCuSpec, _ := ctx.GetParam(constants.FLOW_PARAM_JMCUSPEC, constants.FLOW_PARAM_DEFAULT_JMCUSPEC)
	// 根据CU规格计算JM的内存大小
	floatJmCuSpec, _ := strconv.ParseFloat(jmCuSpec, 32)
	jmMemory := service2.GetMemoryByCuSpec(floatJmCuSpec, "m", ctx.Tke.ArchGeneration, ctx.Cluster.MemRatio)
	jmMemoryMi := service2.GetMemoryByCuSpec(floatJmCuSpec, "Mi", ctx.Tke.ArchGeneration, ctx.Cluster.MemRatio)
	// 获取TM的CU规格
	tmCuSpec, _ := ctx.GetParam(constants.FLOW_PARAM_TMCUSPEC, constants.FLOW_PARAM_DEFAULT_TMCUSPEC)
	// 根据CU规格计算TM的内存大小
	floatTmCuSpec, _ := strconv.ParseFloat(tmCuSpec, 32)
	tmMemory := service2.GetMemoryByCuSpec(floatTmCuSpec, "m", ctx.Tke.ArchGeneration, ctx.Cluster.MemRatio)

	jmCpu, _ := ctx.GetParam(constants.FLOW_PARAM_JMCPU, "0")
	floatJmCpu, _ := strconv.ParseFloat(jmCpu, 32)
	// 新模式
	if floatJmCpu != 0 {
		floatJmCuSpec = floatJmCpu

		jmMem, _ := ctx.GetParam(constants.FLOW_PARAM_JMMEM, "0")
		floatJmMem, _ := strconv.ParseFloat(jmMem, 32)
		jmMemory = service2.GetMemoryByMemG(floatJmMem, "m", ctx.Tke.ArchGeneration)
		jmMemoryMi = service2.GetMemoryByMemG(floatJmMem, "Mi", ctx.Tke.ArchGeneration)

		tmCpu, _ := ctx.GetParam(constants.FLOW_PARAM_TMCPU, "0")
		floatTmCpu, _ := strconv.ParseFloat(tmCpu, 32)
		floatTmCuSpec = floatTmCpu

		tmMem, _ := ctx.GetParam(constants.FLOW_PARAM_TMMEM, "0")
		floatTmMem, _ := strconv.ParseFloat(tmMem, 32)
		tmMemory = service2.GetMemoryByMemG(floatTmMem, "m", ctx.Tke.ArchGeneration)
	}

	// 生成JM和TM的公共标签
	appId, _, _ := ctx.GetParamInt(constants.FLOW_PARAM_APPID, 0)
	ownerUin, _ := ctx.GetParam(constants.FLOW_PARAM_OWNERUIN, constants.EMPTY)
	creatorUin, _ := ctx.GetParam(constants.FLOW_PARAM_CREATORUIN, constants.EMPTY)
	jmLabels := make([]string, 0)
	jmLabels = append(jmLabels, fmt.Sprintf(constants.ClusterLabelFormat1, constants.FLOW_PARAM_APPID, appId))
	jmLabels = append(jmLabels, fmt.Sprintf(constants.ClusterLabelFormat2, constants.FLOW_PARAM_OWNERUIN, ownerUin))
	jmLabels = append(jmLabels, fmt.Sprintf(constants.ClusterLabelFormat2, constants.FLOW_PARAM_CREATORUIN, creatorUin))
	jmLabels = append(jmLabels, fmt.Sprintf(constants.ClusterLabelFormat2, constants.FLOW_PARAM_TYPE, constants.ClusterSessionType))
	jmLabels = append(jmLabels, fmt.Sprintf(constants.ClusterLabelFormat2, constants.FLOW_PARAM_APP, fmt.Sprintf(constants.ClusterSessionAPPTemplate, serialId, noDotLowerFlinkVersion)))

	tmLabels := make([]string, len(jmLabels))
	copy(tmLabels, jmLabels)
	jmLabels = append(jmLabels, fmt.Sprintf(constants.ClusterLabelFormat2, constants.ClusterLabelComponent, constants.ClusterJobmanager))
	tmLabels = append(tmLabels, fmt.Sprintf(constants.ClusterLabelFormat2, constants.ClusterLabelComponent, constants.ClusterTaskManager))
	// 根据Flink版本获取对应版Flink镜像完整地址
	region, _ := ctx.GetParam(constants.FLOW_PARAM_REGION, "ap-guangzhou")
	flinkImage, err := image_registry.New(region).Flink(flinkVersion)
	if err != nil {
		errMsg := fmt.Sprintf("get flink tke image from rainbox error，"+
			"job's region [%s], flink version is [%s] error:%+v", region, flinkVersion, err)
		logger.Errorf(errMsg)
		return constants.EMPTY, nil, errorcode.InvalidParameterValueCode.NewWithInfo(errMsg, err)
	}
	// Oceanus集群的子网id(非跨租户子网)
	clusterSubnetId := ctx.Cluster.SubnetId
	/**
	 * 处理掉tke端不允许的情况
	 * 1. ConfigMap中metadata.name (正则: [a-z0-9]([-a-z0-9]*[a-z0-9])?(\\\\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*)
	 * 2. Service中metadata.name (正则: [a-z]([-a-z0-9]*[a-z0-9])?)
	 * 3. Service中metadata.labels和spec.selector (正则: (([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])?)
	 */
	// 构建JmDeployment的参数
	namespace := clusterService.GetDefaultNamespace(ctx.ClusterGroup)
	credentialNamespace := clusterService.GetDefaultCredentialNamespace(ctx.ClusterGroup)
	clusterSessionJmDeployment := &oceanus_controller.ClusterSessionJmDeployment{
		AppId:               appId,
		OwnerUin:            ownerUin,
		CreatorUin:          creatorUin,
		AppName:             fmt.Sprintf(constants.ClusterSessionAPPTemplate, serialId, noDotLowerFlinkVersion),
		DeploymentName:      fmt.Sprintf(constants.ClusterSessionJMTemplate, serialId, noDotLowerFlinkVersion),
		ContainerImage:      flinkImage,
		ImagePullPolicy:     constants.FLOW_PARAM_IMAGE_PULL_POLICY,
		CPU:                 service2.GetDeploymentCpu(floatJmCuSpec),
		Memory:              jmMemoryMi,
		HadoopConfigName:    fmt.Sprintf(constants.ClusterSessionHadoopConfigTemplate, serialId, noDotLowerFlinkVersion),
		FlinkConfigName:     fmt.Sprintf(constants.ClusterSessionFlinkConfigTemplate, serialId, noDotLowerFlinkVersion),
		Namespace:           namespace,
		CredentialNamespace: credentialNamespace,
	}
	params := struct {
		JobManagerRpcAddress string
		CosBucket            string
		JmCuSpec             string
		JmMemory             string
		JmLabels             string
		TmCuSpec             string
		TmMemory             string
		TmLabels             string
		ContainerImage       string
		SubnetId             string
		ImagePullPolicy      string
		Namespace            string
		CredentialNamespace  string
	}{
		JobManagerRpcAddress: clusterSessionJmDeployment.AppName + "-rest",
		CosBucket:            ctx.Cluster.DefaultCOSBucket,
		JmCuSpec:             fmt.Sprintf("%f", floatJmCuSpec),
		JmMemory:             jmMemory,
		JmLabels:             strings.Join(jmLabels, ","),
		TmCuSpec:             fmt.Sprintf("%f", floatTmCuSpec),
		TmMemory:             tmMemory,
		TmLabels:             strings.Join(tmLabels, ","),
		ContainerImage:       flinkImage,
		SubnetId:             clusterSubnetId,
		ImagePullPolicy:      constants.FLOW_PARAM_IMAGE_PULL_POLICY,
		Namespace:            namespace,
		CredentialNamespace:  credentialNamespace,
	}
	flinkSessionConfigMap, err := ctx.FlowCC.ClusterSessionCC().FlinkSessionConf(params)
	if err != nil {
		errMsg := fmt.Sprintf("Get FlinkSessionConf error, error:%+v", err)
		logger.Errorf(errMsg)
		return constants.EMPTY, nil, errorcode.InvalidParameterValueCode.NewWithInfo(errMsg, err)
	}
	return flinkSessionConfigMap, clusterSessionJmDeployment, nil
}

// 修改keytab和krb5.conf两项配置，改为container内的绝对路径
func updatePropertyForKerberos(properties []*model3.Property) []*model3.Property {
	kerberosEnabled := false
	for _, property := range properties {
		if (property.Key == USER_CONFIG_KERBEROS_ENABLED_KEY_1 || property.Key == USER_CONFIG_KERBEROS_ENABLED_KEY_2) && property.Value == "kerberos" {
			kerberosEnabled = true
			properties = append(properties, &model3.Property{
				Key:   KUBERNETES_KERBEROS_ENABLED_KEY,
				Value: "true",
			})
			break
		}
	}

	hasKeytab := false
	hasKrb5Conf := false
	for index, property := range properties {
		if property.Key == KERBEROS_LOGIN_KEYTAB_KEY {
			properties[index] = &model3.Property{
				Key:   KERBEROS_LOGIN_KEYTAB_KEY,
				Value: path.Join(USER_DEPENDENCY_FILE_DOWNLOAD_DIR_VALUE, property.Value),
			}
			hasKeytab = true
		}
		if property.Key == KERBEROS_LOGIN_CONF_KEY || property.Key == KERBEROS_KRB5_CONF_KEY {
			properties[index] = &model3.Property{
				Key:   KERBEROS_KRB5_CONF_KEY,
				Value: path.Join(USER_DEPENDENCY_FILE_DOWNLOAD_DIR_VALUE, property.Value),
			}
			hasKrb5Conf = true
		}
	}
	logger.Infof("func updatePropertyForKerberos, kerberosEnabled: %s, hasKeytab: %s, hasKrb5Conf: %s", kerberosEnabled, hasKeytab, hasKrb5Conf)

	// 部分产品文档里没有提示用户添加fs.hdfs.hadoop.security.authentication: kerberos
	// 同时设置了keytab和krb5conf也认为开启kerberos，补上配置项
	if kerberosEnabled || (hasKeytab && hasKrb5Conf) {
		properties = append(properties, &model3.Property{
			Key:   KUBERNETES_KERBEROS_ENABLED_KEY,
			Value: "true",
		})
	}

	return properties
}

/**
 * 将FlinkConf str转换为Map
 */
func (s *DeployJobManagerService) confToMap(str string) map[string]string {
	dec := yaml.NewYAMLOrJSONDecoder(bytes.NewReader([]byte(str)), len(str))
	mapConf := map[string]string{}
	if err := dec.Decode(&mapConf); err != nil {
		panic(errorcode.NewStackError(errorcode.InternalErrorCode, "confToMap error", err))
	}
	return mapConf
}

func (s *DeployJobManagerService) JmServiceLabels(deployment *oceanus_controller.ClusterSessionJmDeployment) map[string]string {
	return map[string]string{
		constants.ClusterLabelComponent: constants.ClusterJobmanager,
		constants.FLOW_PARAM_TYPE:       constants.ClusterSessionType,
		constants.FLOW_PARAM_APP:        deployment.AppName,
	}
}

type clusterSessionJobManager struct {
	ctx          *deploy.Context
	jmDeployment *oceanus_controller.ClusterSessionJmDeployment
	apps.Deployment
	namespace string
	region    string
}

func (c *clusterSessionJobManager) Params() (interface{}, error) {
	return c.jmDeployment, nil
}

func (c *clusterSessionJobManager) Decode(params, info interface{}) (interface{}, error) {
	return c.ctx.FlowCC.ClusterSessionCC().FlinkSessionJmDeployment(params, info)
}
