package taskHandler

import (
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type scaleClusterEndPointService struct {
}

func (s *scaleClusterEndPointService) Apps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	cg := ctx.ClusterGroup

	result = append(result, &switchClusterGroupStatusApp{ctx: ctx, status: constants.CLUSTER_GROUP_STATUS_RUNNING})

	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM {

	}

	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId == "" {

	}

	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId != "" {

	}

	//内网环境下执行密码入库
	logger.Debugf("cg.NetEnvironmentType == constants.NETWORK_ENV_INNER_VPC: %+v scaleClusterEndPointService", cg.NetEnvironmentType == constants.NETWORK_ENV_INNER_VPC)
	if cg.NetEnvironmentType == constants.NETWORK_ENV_INNER_VPC {
		result = append(result, &updatePasswordApp{ctx: ctx})
	}

	return result, nil
}
