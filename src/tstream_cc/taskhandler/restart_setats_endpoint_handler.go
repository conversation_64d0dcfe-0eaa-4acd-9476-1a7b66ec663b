package taskHandler

import (
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type ReStartSetatsWarehouseEndpointHandler struct {
}

func (c ReStartSetatsWarehouseEndpointHandler) Apps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	result = append(result, &switchSetatsStatusApp{ctx: ctx, status: constants.SETATS_RUNNING})
	return result, nil
}
