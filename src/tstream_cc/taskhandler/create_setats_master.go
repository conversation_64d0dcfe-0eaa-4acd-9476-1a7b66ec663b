package taskHandler

import (
	"errors"
	"fmt"
	tke "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/tke/v20180525"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"sort"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/dlocker"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/apiv3"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/cost"
	cvm2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cvm"
	table3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/Cvm"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/configuration_center"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cvm"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
	cvmApi "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/cvm"
)

type CreateSetatsMaster struct {
}

func (c CreateSetatsMaster) Apps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	result = append(result, &addMasterApp{ctx: ctx})
	return result, nil
}

type addMasterApp struct {
	ctx *deploy.Context
	apps.ApplyReadyApp
}

func (c *addMasterApp) Ready(s apps.Client, v interface{}) (bool, error) {
	h := &ApplyTKEHandler{}
	if allNodeRunning, err := h.TryMakeAllNodesRunning(c.ctx); err != nil {
		msg := fmt.Sprintf("addMasterApp TryMakeAllNodesRunning Error, err %v", err)
		logger.Errorf(msg)
		return false, err
	} else if !allNodeRunning {
		msg := fmt.Sprintf("addMasterApp allNodeRunning is false")
		logger.Errorf(msg)
		return false, errors.New(msg)
	}
	return true, nil
}

func (c *addMasterApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	masterDiskType, _ := c.ctx.GetParam(constants.FLOW_PARAM_SETATS_MASTER_DISK_TYPE, constants.TKE_CVM_DISK_TYPE)
	strMasterDiskSize, _ := c.ctx.GetParam(constants.FLOW_PARAM_SETATS_MASTER_DISK_SIZE, fmt.Sprintf("%d", constants.TKE_WORKER_NODE_DISK_SIZE))
	strMasterCpu, _ := c.ctx.GetParam(constants.FLOW_PARAM_SETATS_MASTERCPU, "8")

	masterDiskSize, _ := strconv.Atoi(strMasterDiskSize)
	masterCpu, _ := strconv.ParseFloat(strMasterCpu, 32)
	labels := make(map[string]string)
	labels[constants.TKE_CVM_LABEL_KEY] = constants.SETATS_MANAGER_LABEL
	err = AddSetatsCvm("addMasterApp", c.ctx, masterCpu, int64(masterDiskSize), masterDiskType, 1, labels, 1)
	if err != nil {
		logger.Errorf("addMasterApp addSetatsCvm Error, err %v", err)
		return nil, err
	}
	return nil, nil
}

func AddSetatsCvm(taskCode string, ctx *deploy.Context, cpu float64, diskSize int64, diskType string,
	parallelism int64, setatsLabels map[string]string, expectWorkerCount int) error {
	// 先查询节点是否加完了
	k8sService := ctx.K8sService()
	workerList, err := k8sService.ListNode(ctx.ClientSet(), metav1.ListOptions{
		LabelSelector: labels.FormatLabels(setatsLabels),
	})
	if err != nil {
		msg := fmt.Sprintf("addSetatsCvm %s ListNode Error, err %v", taskCode, err)
		logger.Errorf(msg)
		return errorcode.InternalErrorCode.NewWithInfo(msg, err)
	}
	workerCount := len(workerList.Items)
	logger.Infof("addSetatsCvm ListNode workerCount with labels %s is %d", labels.FormatLabels(setatsLabels), workerCount)
	if workerCount >= expectWorkerCount {
		logger.Infof("addSetatsCvm tke workerCount %d, >= expectWorkerCount cnt %d, done", workerCount, expectWorkerCount)
		return nil
	}

	logger.Infof("addSetatsCvm cpu %d, mem %d, diskSize %d, diskType %s, parallelism %d", int64(cpu), int64(cpu*float64(ctx.Cluster.MemRatio)), diskSize, diskType, parallelism)
	cvmSaleConfList, err := service3.GetTableService().ListActiveCvmSaleConfWithCpuMemory(int64(cpu), int64(cpu*float64(ctx.Cluster.MemRatio)))
	logger.Infof("addMasterApp cvmSaleConfList len %d", len(cvmSaleConfList))
	if err != nil || len(cvmSaleConfList) == 0 {
		msg := fmt.Sprintf("addSetatsCvm GetWhiteListCvmSaleConf ListActiveCvmSaleConfWithCpuMemory with masterCpu: %f Error, len %d", cpu, len(cvmSaleConfList))
		logger.Errorf(msg)
		return errorcode.InternalErrorCode.NewWithInfo(msg, err)
	}
	defaultCvmSaleConf := cvmSaleConfList[0].InstanceType
	logger.Infof("addSetatsCvm defaultCvmSaleConf %s", defaultCvmSaleConf)

	instanceTypes := make([]string, 0)
	for _, cvmSaleConf := range cvmSaleConfList {
		instanceTypes = append(instanceTypes, cvmSaleConf.InstanceType)
		logger.Infof("addSetatsCvm cvmSaleConf %+v", cvmSaleConf)
	}

	sort.Slice(instanceTypes, func(i, j int) bool {
		return instanceTypes[i] > instanceTypes[j]
	})

	appid, err := service.GetScsUserAppId()
	if err != nil {
		msg := fmt.Sprintf("addSetatsCvm GetScsUserAppId Error, err %v", err)
		logger.Errorf(msg)
		return errorcode.InternalErrorCode.NewWithInfo(msg, err)
	}
	uin, err := service.GetScsUserUin()
	if err != nil {
		msg := fmt.Sprintf("addSetatsCvm GetScsUserUin Error, err %v", err)
		logger.Errorf(msg)
		return errorcode.InternalErrorCode.NewWithInfo(msg, err)
	}
	// 检查 库存
	cvmService := cvm.GetCvmService()
	cvmReq := cvmService.NewDefaultDescribeZoneInstanceConfigInfosRequestBuilder().
		WithFilter("instance-type", instanceTypes).
		WithFilter("zone", []string{ctx.ClusterGroup.Zone}).
		WithRequestRole(cvm2.CvmInternApiRequestRole).
		WithInternalInfo(ctx.ClusterGroup.Region, appid, uin, uin).
		BuildInternal()
	instanceTypeQuotaSet, err := cvmService.DescribeZoneInstanceConfigInfosInternal(ctx.ClusterGroup.Region, cvmReq)
	if err != nil {
		msg := fmt.Sprintf("addSetatsCvm DescribeZoneInstanceConfigInfosInternal Error, err %v", err)
		logger.Errorf(msg)
		return errorcode.InternalErrorCode.NewWithInfo(msg, err)
	}

	instanceTypeQuotaList := make([]*cvmApi.InstanceTypeQuotaItem, 0)
	for _, quota := range instanceTypeQuotaSet {
		if *quota.InstanceQuota < parallelism {
			logger.Infof("addSetatsCvm InstanceType %s quota %d < %d, skip", *quota.InstanceType, *quota.InstanceQuota, parallelism)
			continue
		}
		if *quota.Status != "SELL" {
			logger.Infof("addSetatsCvm InstanceType %s Status %s is not SELL, skip", *quota.InstanceType, *quota.Status)
			continue
		}
		instanceTypeQuotaList = append(instanceTypeQuotaList, quota)
	}
	if len(instanceTypeQuotaList) == 0 {
		msg := fmt.Sprintf("addSetatsCvm no cvm can use")
		logger.Errorf(msg)
		return errorcode.InternalErrorCode.NewWithInfo(msg, err)
	}
	sort.Slice(instanceTypeQuotaList, func(i, j int) bool {
		return *instanceTypeQuotaList[i].InstanceType > *instanceTypeQuotaList[j].InstanceType
	})
	defaultCvmSaleConf = *instanceTypeQuotaList[0].InstanceType
	logger.Infof("addSetatsCvm finally use InstanceType %s", defaultCvmSaleConf)

	retCode, errMsg, _ := BuyWorker(&cost.BuyWorkerReq{
		RequestBase: apiv3.RequestBase{
			RequestId:     fmt.Sprintf("addSetatsCvm_%s", ctx.ClusterGroup.SerialId),
			AppId:         int64(ctx.ClusterGroup.AppId),
			Uin:           ctx.ClusterGroup.OwnerUin,
			SubAccountUin: ctx.ClusterGroup.CreatorUin,
		},
		SerialId: ctx.ClusterGroup.SerialId,
		Count:    parallelism,
		DiskSize: diskSize,
		DiskType: diskType,
		Spec:     defaultCvmSaleConf,
		Force:    true,
		Labels:   setatsLabels,
		//Taints:   GetTaints(),
	}, taskCode, ctx.ClusterGroup.SerialId)
	if retCode != controller.OK {
		logger.Errorf("addMasterApp BuyWorker Error, retCode %d, errMsg %s", retCode, errMsg)
		return errorcode.InternalErrorCode.NewWithInfo(errMsg, err)
	}
	return nil
}

func GetTaints() []*tke.Taint {
	setatsTaintKey := "setats-taint-key"
	setatsTaintValue := "setats-taint-value"
	setatsTaintEffect := "NoSchedule"

	taints := make([]*tke.Taint, 0)
	taints = append(taints, &tke.Taint{
		Key:    &setatsTaintKey,
		Value:  &setatsTaintValue,
		Effect: &setatsTaintEffect,
	})
	return taints
}

func BuyWorker(req *cost.BuyWorkerReq, taskCode, clusterGroupSerialId string) (retCode string, errMsg string, response interface{}) {
	defer func() {
		if errs := recover(); errs != nil {
			msg := fmt.Sprintf("buy worker %s panic , for clusterGroup:%s, errors:%+v", req.RequestId, req.SerialId, errs)
			retCode = controller.InternalError
			errMsg = msg
		}
	}()
	locker := dlocker.NewDlocker(fmt.Sprintf("oceanus-buy-worker-%s-%s", taskCode, clusterGroupSerialId), req.SerialId, 8000)
	err := locker.Lock()
	if err != nil {
		logger.Infof("Another buy worker process for %s has lock but not finished yet", req.SerialId)
		return controller.InternalError, "Another  buy worker process instance is still going, not ready yet", nil
	}
	defer locker.UnLock()

	groupService, err := service2.NewClusterGroupServiceBySerialId(req.SerialId)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}

	clusterGroup := groupService.GetClusterGroup()

	cluster, err := groupService.GetActiveCluster()
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}
	tkeList, err := groupService.GetTkeList()
	if err != nil {
		msg := fmt.Sprintf("UpgradeController -> GetTkeList(%s) error:%+v", req.SerialId, err)
		logger.Error(msg)
		return controller.InternalError, msg, nil
	}
	if len(tkeList) != 1 {
		return controller.InternalError, fmt.Sprintf("tke length is %d, not 1", len(tkeList)), nil
	}
	k8sInstance := tkeList[0]

	if k8sInstance.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		return controller.OK, "", cost.DescribeCvmResp{Message: "Cluster is eks cluster"}
	}

	if clusterGroup.Type == constants.CLUSTER_GROUP_TYPE_SUB_EKS {
		return controller.OK, "", cost.DescribeCvmResp{Message: "Cluster is sub eks cluster"}
	}

	taskExecRequest := &flow.TaskExecRequest{
		Params: map[string]string{
			constants.FLOW_PARAM_REQUEST_ID:       fmt.Sprintf("%s_%s", req.SerialId, req.RequestId),
			constants.FLOW_PARAM_CLUSTER_GROUP_ID: fmt.Sprintf("%d", clusterGroup.Id),
			constants.FLOW_PARAM_CLUSTER_ID:       fmt.Sprintf("%d", cluster.Id),
			constants.FLOW_PARAM_CLUSTER_TYPE:     fmt.Sprintf("%d", k8sInstance.ClusterType),
		}}

	c, err := deploy.New(taskExecRequest)
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}
	h := &ApplyTKEHandler{}
	var workerConf *table3.CvmSaleConfig

	var forceSpec bool

	if req.Spec == "" {
		wn, cn, cvmConf, err := h.ComputeWorkerNodeAndCuNum(clusterGroup.CuNum, c)
		if err != nil {
			return controller.InternalError, err.Error(), nil
		}
		logger.Infof("CalcClusterNodeCount: %d, %d", cn, wn)
		workerConf, forceSpec, err = ChangeWorkSpec(clusterGroup, cvmConf)
		if err != nil {
			return controller.InternalError, err.Error(), nil
		}
	} else {
		forceSpec = true
		cvmConfList, err := service3.GetTableService().ListCvmSaleConfByInstanceType(req.Spec, 0, 1)
		if err != nil {
			return controller.InternalError, err.Error(), nil
		}
		if len(cvmConfList) <= 0 {
			return controller.InternalError, "can not find cvm conf", nil
		}
		workerConf = cvmConfList[0]
	}
	logger.Infof("buy worker spec: %s", workerConf.InstanceType)
	if workerConf.InstanceType != constants.TKE_WORKER_NODE_INSTANCE_TYPE && !req.Force {
		return controller.OK, "", cost.BuyWorkerResp{Count: 0}
	}

	if req.Count <= 0 {
		return controller.InternalError, "Request count is zone", nil
	}

	if req.DryBuy {
		return controller.OK, "", cost.BuyWorkerResp{Count: req.Count}
	}

	zone := req.Zone
	if zone == "" {
		zone = clusterGroup.Zone
	}
	if support, err := clusterGroup.IsSupportZone(zone); err != nil {
		return controller.InternalError, err.Error(), nil
	} else if !support {
		err = fmt.Errorf("cluster %s not support zone %s", clusterGroup.SerialId, zone)
		return controller.InternalError, err.Error(), nil
	}

	diskType, diskSize, err := clusterGroup.GetDiskInfo()
	if err != nil {
		return controller.InternalError, err.Error(), nil
	}

	if req.DiskType != "" {
		diskType = req.DiskType
	}
	if req.DiskSize > 0 {
		diskSize = req.DiskSize
	}

	label := h.GetWorkerLabel(clusterGroup, zone)

	if len(req.Labels) > 0 {
		label = req.Labels
	}

	osImg, err := config.GetRainbowConfiguration("ConfigureCenter.Flow.K8S.Setats", "CvmOsImg")
	if err != nil {
		logger.Warningf("GetRainbowConfiguration from ConfigureCenter.Flow.K8S.Setats.CvmOsImg :%+v", err)
		osImg = "img-1tmhysjj"
	}

	buyCount, err := h.CheckAndAddWorker(c,
		zone,
		req.Count,
		workerConf,
		forceSpec,
		label,
		diskSize,
		diskType,
		taskCode, req.Taints,
		osImg,
		constants.TKE_NODE_SETATSSYSTEM_DISK_SIZE)
	if err != nil {
		logger.Errorf("CheckAndAddWorker error:%+v", err)
		return controller.InternalError, err.Error(), nil
	}

	return controller.OK, "", cost.BuyWorkerResp{Count: buyCount}
}
