package taskHandler

import (
	"encoding/json"
	"errors"
	"fmt"
	billing3 "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/billing/v20180709"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	constants2 "tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cdb"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	model2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/tke"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cdb"
	service4 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config/common_config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config/configure_center"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type CreateCdbService struct {
}

func (s *CreateCdbService) Apps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	cg := ctx.ClusterGroup

	// 共享集群 包年包月下的eks集群，不做任何处理
	if cg.Type == constants2.CLUSTER_GROUP_TYPE_SUB_EKS {
		return result, nil
	}

	ccApp := createCdbApp{ctx: ctx}

	// 创建共享集群母集群
	if cg.Type == constants2.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone == "" {
		result = append(result, &ccApp)
	}

	// 共享集群母集群，开新区
	if cg.Type == constants2.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone != "" {
	}

	// 独享集群
	if cg.Type == constants2.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId == "" {
		result = append(result, &ccApp)
	}

	// 共享集群
	if cg.Type == constants2.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId != "" {
		result = append(result, &insertCdbApp{ctx: ctx, aSerialId: cg.AgentSerialId})
	}

	return result, nil
}

type createCdbApp struct {
	ctx *deploy.Context
	apps.ApplyReadyApp
}

func (c *createCdbApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	params := c.ctx.Request.Params
	cg := c.ctx.ClusterGroup
	// 数据库判断
	if c.ctx.Cdb != nil {
		params[constants2.FLOW_PARAM_CDB_INSTANCE_ID] = c.ctx.Cdb.InstanceId
	}

	instanceId, exist, err := c.ctx.GetFlowParamString(params, constants2.FLOW_PARAM_CDB_INSTANCE_ID, "")
	if err != nil {
		return
	}

	memory, err := model2.DefaultCdbMemoryMB(c.ctx.CC().FlowCC(), cg.Type, cg.CuNum, c.ctx.ClusterType)
	if err != nil {
		return
	}
	volume, err := model2.DefaultCdbVolumeGB(c.ctx.CC().FlowCC(), cg.Type, cg.CuNum, c.ctx.ClusterType)
	if err != nil {
		return
	}

	if !exist {
		// 创建
		instanceId, err = c.createCdb(memory, volume)
		if err != nil {
			return nil, err
		}

		if instanceId == "" {
			return nil, errors.New("instanceId is Empty")
		}

		params[constants2.FLOW_PARAM_CDB_INSTANCE_ID] = instanceId
	}

	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		sql := "insert into Cdb(`ClusterId`,`InstanceId`,`InstanceName`,`Memory`,`Volume`,`Status`) VALUES(?,?,?,?,?,?) on duplicate key update `InstanceName`=?"
		tx.ExecuteSqlWithArgs(sql, c.ctx.Cluster.Id, instanceId, cg.SerialId, memory, volume, model.CDB_STATUS_CREATING, cg.SerialId)
		return nil
	}).Close()
	return nil, nil
}

func (c *createCdbApp) Ready(_ apps.Client, _ interface{}) (_ bool, err error) {
	params := c.ctx.Request.Params

	cdbTable := c.ctx.Cdb

	if cdbTable == nil {
		panic(errors.New("there is no cdb instance find"))
	}

	cdbService := cdb.GetCdbService()

	clusterGroup, cluster, err := c.ctx.GetClusterGroupAndCluster(params)
	if err != nil {
		return false, err
	}

	cnt, cdbList, err := cdbService.DescribeDBInstancesWithScsAccountByNetEnvironmentType(
		clusterGroup.NetEnvironmentType,
		clusterGroup.Region,
		cdbService.NewDefaultDescribeDBInstancesRequestBuilder().
			WithInstancesIds([]string{cdbTable.InstanceId}).Build())
	if err != nil {
		return false, err
	}
	if cnt != 1 {
		return false, errors.New(fmt.Sprintf("instance cnt for %s is %d", cdbTable.InstanceId, cnt))
	}

	cdbInstance := cdbList[0]

	// cdb运行状态且无任务，即cdb就绪，cdb已经去掉init方法，后期不需要进行初始化
	if *cdbInstance.Status == model.CDB_STATUS_RUNNING && *cdbInstance.TaskStatus == 0 {
		//if *cdbInstance.InitFlag == 0 {
		//	if err := h.initDBInstances(clusterGroup, cluster, instanceId); err != nil {
		//		// 初始化接口失败可重试，即使是网络超时错误而实际上初始化成功，重试时因为有initFlag标志检查也不会出现重复初始化错误
		//		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_RUNNING, 0.1, fmt.Sprintf("%v", err), clsParams)
		//	}
		//	return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_IN_PROCESS, flow.TASK_STATUS_RUNNING, 0.1, "ok", clsParams)
		//}

		service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {

			tx.ExecuteSqlWithArgs("update Cdb set Status=?,Vip=?,Vport=? where ClusterId=? and InstanceId=?",
				model.CDB_STATUS_RUNNING, *cdbInstance.Vip, *cdbInstance.Vport, cluster.Id, cdbTable.InstanceId)

			// 若已记录密码（提供资源场景，秘密很可能不是默认密码），无需更新
			if len(cdbTable.User) > 0 && len(cdbTable.Password) > 0 {
				return nil
			}
			rainbowOceanusClusterPassword, err := common_config.GetRainbowOceanusClusterPassword()
			if err != nil {
				logger.Errorf("Failed to get oceanus cluster password in Rainbow")
				return errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
			}
			rainbowOceanusPasswordEncodeKey, err := common_config.GetRainbowOceanusPasswordEncodeKey()
			if err != nil {
				logger.Errorf("Failed to get oceanus cluster password encode key in Rainbow")
				return errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
			}
			password, err := util.EncodePassword(rainbowOceanusClusterPassword, rainbowOceanusPasswordEncodeKey)
			if err != nil {
				return errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
			}
			tx.ExecuteSqlWithArgs("update Cdb set User=?,Password=? where ClusterId=? and InstanceId=?",
				"root", password, cluster.Id, cdbTable.InstanceId)

			return nil
		}).Close()

		return true, nil
	} else {
		return false, nil
	}
}

func (c *createCdbApp) createCdb(memory, volume int64) (instanceId string, err error) {
	params := c.ctx.Request.Params

	tmpOrderId := "cdb_not_return_order_id"

	cdbService := cdb.GetCdbService()
	cg := c.ctx.ClusterGroup
	if total, cdbList, err := cdbService.DescribeDBInstancesWithScsAccountByNetEnvironmentType(cg.NetEnvironmentType, cg.Region,
		cdbService.NewDefaultDescribeDBInstancesRequestBuilder().WithInstancesNames([]string{cg.SerialId}).Build()); err != nil {
		return "", err
	} else if total == 1 {
		instanceId = *cdbList[0].InstanceId
		return instanceId, nil
	} else if total > 1 {
		err = fmt.Errorf("to many cdbs with name %s, count is %d", cg.SerialId, total)
		return "", err
	}

	prepaidPeriod, err := c.ctx.CC().FlowCC().PrepaidPeriod()
	if err != nil {
		return "", err
	}

	cdbOrderId, exist, err := c.ctx.GetFlowParamString(params, constants2.CDB_ORDER_ID, constants2.EMPTY)
	logger.Infof("GetFlowParamString cdbOrderId: %s", cdbOrderId)
	if err != nil {
		return "", err
	}

	if cg.NetEnvironmentType == constants2.NETWORK_ENV_CLOUD_VPC {
		if !exist {
			instanceId, cdbOrderId, err = c.createDBInstance(cg, c.ctx.Cluster, memory, volume, prepaidPeriod)
		}
		return
	}

	if !exist {
		// 内网账号购买CDB会报账号没有支付权限的错误且发起一个BPaaS审批流和一个订单号，这里需要特殊处理避免重试提交N个审批流从而产生大量的CDB实例
		instanceId, cdbOrderId, err = c.createDBInstance(cg, c.ctx.Cluster, memory, volume, prepaidPeriod)

		if err != nil {
			// 判断错误信息是否包含没有支付权限的错误信息
			errorMessage := err.Error()
			logger.Infof("createDBInstance Msg: %s", errorMessage)
			if strings.Contains(errorMessage, "no pay auth") || strings.Contains(errorMessage, "无法支付") {
				// 得到订单Id
				cdbOrderId = service3.ParseOrderIdFromErrorMsg(errorMessage)
				if cdbOrderId == "" {
					cdbOrderId = tmpOrderId
				}
				c.ctx.SetReturnParam(constants2.CDB_ORDER_ID, cdbOrderId)
			}
			if cdbOrderId == "" {
				panic("ParseOrderId: cdbOrderId is empty")
			}
			err = nil
		}
	}
	if instanceId != "" {
		return instanceId, err
	}

	if cdbOrderId == "" {
		panic("ParseOrderId: cdbOrderId is empty")
	}

	if cdbOrderId == tmpOrderId {
		return "", fmt.Errorf("wait cdb create")
	}

	// 根据订单ID去查询订单信息获得发货的CDB的实例ID，如果没有的话可能就是BPaaS审批流还没有完成要等待审批流完成才会生产CDB
	dealsByCondRequest := billing3.NewDescribeDealsByCondRequest()
	dealsByCondRequest.StartTime = common.StringPtr(util.GetOneYearAgoTime())
	dealsByCondRequest.EndTime = common.StringPtr(util.GetAfterOneDayTime())
	dealsByCondRequest.Limit = common.Int64Ptr(1)
	dealsByCondRequest.OrderId = &cdbOrderId

	dealsByCondResponse, err := service3.DescribeDealsByCond(cg.Region, cg.NetEnvironmentType, dealsByCondRequest)
	if err != nil {
		logger.Errorf("DescribeDealsByCond err: %+v", err)
		return "", err
	}
	// 这里需要注意的是不管是购买CDB的审批流被拒绝了还是审批通过之后CDB在生产中还没有发货成功都查不到订单信息，
	// 只能重试了(因此内网购买CDB的BPaaS审批流必须审批通过否则需要人工干预结束流程)
	if *dealsByCondResponse.Response.TotalCount != 1 {
		b, _ := json.Marshal(dealsByCondResponse.Response)
		return "", fmt.Errorf("error: Cannot query order info, : %s", string(b))
	}
	if *dealsByCondResponse.Response.TotalCount == 1 {
		if len(dealsByCondResponse.Response.Deals[0].ResourceId) != 1 {
			b, _ := json.Marshal(dealsByCondResponse.Response)
			return "", fmt.Errorf("ResourceId is not right: %s", string(b))
		}
		// 说明购买的CDB发货成功则获取CDB的实例ID
		instanceId = *dealsByCondResponse.Response.Deals[0].ResourceId[0]
		return instanceId, nil
	}
	return "", fmt.Errorf("deal response length is zero")
}

func (c *createCdbApp) createDBInstance(clusterGroup *table.ClusterGroup, cluster *table.Cluster, memory,
	volume, prepaidPeriod int64) (instanceId, dealId string, err error) {

	cdbService := cdb.GetCdbService()
	if total, cdbList, err := cdbService.DescribeDBInstancesWithScsAccountByNetEnvironmentType(clusterGroup.NetEnvironmentType, clusterGroup.Region,
		cdbService.NewDefaultDescribeDBInstancesRequestBuilder().WithInstancesNames([]string{clusterGroup.SerialId}).Build()); err != nil {
		return "", "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	} else if total == 1 {
		return *cdbList[0].InstanceId, "", nil
	} else if total > 1 {
		return "", "", errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("to many cdbs with name %s, count is %d", clusterGroup.SerialId, total), nil)
	}

	rainbowOceanusClusterPassword, getPasswordError := common_config.GetRainbowOceanusClusterPassword()
	if getPasswordError != nil {
		return "", "", errorcode.NewStackError(errorcode.InternalErrorCode, "", getPasswordError)
	}
	internalClusterConfig, err := configure_center.CC(clusterGroup.Region).FlowCC().TkeCC().InternalClusterConfig()
	if err != nil {
		return "", "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	request := cdbService.NewDefaultCreateDBInstanceRequest(clusterGroup.NetEnvironmentType, internalClusterConfig)
	request.Period = &prepaidPeriod
	request.UniqVpcId = &cluster.VpcId
	request.UniqSubnetId = &cluster.SubnetId
	request.Memory = &memory
	request.Volume = &volume
	request.Zone = &clusterGroup.Zone
	request.InstanceName = &clusterGroup.SerialId
	request.Password = &rainbowOceanusClusterPassword

	instanceId, dealId, err = cdbService.CreateDBInstanceWithScsAccountByNetEnvironmentType(clusterGroup.NetEnvironmentType, clusterGroup.Region, request)
	return
}

func (c *createCdbApp) initDBInstances(clusterGroup *table.ClusterGroup, cluster *table.Cluster, instanceId string) error {
	rainbowOceanusClusterPassword, getPasswordError := common_config.GetRainbowOceanusClusterPassword()
	if getPasswordError != nil {
		return errorcode.NewStackError(errorcode.InternalErrorCode, "", getPasswordError)
	}
	if _, _, err := cdb.GetCdbService().InitDBInstancesWithScsAccountByNetEnvironmentType(clusterGroup.NetEnvironmentType, clusterGroup.Region, instanceId, rainbowOceanusClusterPassword); err != nil {
		return errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	} else {
		return nil
	}
}

type insertCdbApp struct {
	apps.ReadyApp
	ctx *deploy.Context

	aSerialId string
}

func (c *insertCdbApp) Ready(_ apps.Client, _ interface{}) (_ bool, err error) {
	if c.ctx.Cdb != nil {
		return true, nil
	}

	acg, err := service4.NewClusterGroupServiceBySerialId(c.aSerialId)
	if err != nil {
		return false, err
	}
	cdbTable, err := acg.GetCdb()
	if err != nil {
		return false, err
	}

	rainbowOceanusClusterPassword, err := common_config.GetRainbowOceanusClusterPassword()
	if err != nil {
		return false, err
	}
	password, err := util.EncodePassword(rainbowOceanusClusterPassword, cdbTable.Password)

	cdbTable.Id = 0
	cdbTable.ClusterId = c.ctx.Cluster.Id
	cdbTable.InstanceName = c.ctx.ClusterGroup.SerialId
	cdbTable.Password = password

	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {

		tx.SaveObject(cdbTable, "Cdb")
		return nil
	}).Close()
	return false, nil
}
