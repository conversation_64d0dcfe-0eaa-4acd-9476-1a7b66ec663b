package deploy

import (
	"k8s.io/api/apps/v1"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
)

type serviceInfo struct {
	name string
	port int32
}

type StsOptions struct {
	sts        *v1.StatefulSet
	resource   *constants.Resource
	images     []string
	logCollect bool
	startupLog bool

	service *serviceInfo
}

type AssignStsOption func(o *StsOptions)

func NewStsOptions(opts ...AssignStsOption) StsOptions {
	var options StsOptions
	for _, opt := range opts {
		opt(&options)
	}
	if options.sts == nil {
		options.sts = &v1.StatefulSet{}
	}
	if options.resource == nil {
		options.resource = &constants.Resource{}
	}
	return options
}

func StsWithSts(sts *v1.StatefulSet) AssignStsOption {
	return func(o *StsOptions) {
		o.sts = sts
	}
}

func StsWithResource(resource *constants.Resource) AssignStsOption {
	return func(o *StsOptions) {
		o.resource = resource
	}
}

func StsWithImages(image ...string) AssignStsOption {
	return func(o *StsOptions) {
		o.images = image
	}
}

func StsWithLogCollect() AssignStsOption {
	return func(o *StsOptions) {
		o.logCollect = true
	}
}

func StsWithStartupLog() AssignStsOption {
	return func(o *StsOptions) {
		o.startupLog = true
	}
}

func StsWithService(name string, port int32) AssignStsOption {
	return func(o *StsOptions) {
		o.service = &serviceInfo{
			name: name,
			port: port,
		}
	}
}
