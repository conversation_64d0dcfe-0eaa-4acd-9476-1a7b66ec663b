package deploy

import (
	"bytes"
	"encoding/json"
	"encoding/xml"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/auth"

	v1 "k8s.io/api/core/v1"
	"k8s.io/apiextensions-apiserver/pkg/client/clientset/clientset"
	"k8s.io/apimachinery/pkg/util/yaml"
	"k8s.io/client-go/kubernetes"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	"tencentcloud.com/tstream_galileo/src/common/uuid"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/oceanus_controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cdb"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config/flowCC"
	flow2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/flow"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/tke"
)

// Context 提供部署TKE服务的一些上下文信息
type Context struct {
	*flow2.ClusterFlowService
	FlowCC *flowCC.CC

	apps []apps.App

	//tmp cache
	clientSet   *kubernetes.Clientset
	clientSetEx *clientset.Clientset
}

func (c *Context) KubeConfig() []byte {
	if c.Cluster.KubeConfig == "" {
		panic("Cluster kube config is empty")
	}
	return []byte(c.Cluster.KubeConfig)
}

func (c *Context) K8sService() *k8s.K8sService {
	return k8s.GetK8sService()
}

func (c *Context) ClientSet() *kubernetes.Clientset {
	var err error
	if c.clientSet == nil {
		c.clientSet, err = k8s.GetK8sService().NewClient(c.KubeConfig())
	}
	if err != nil {
		panic(err)
	}
	return c.clientSet
}

func (c *Context) ClientSetEx() *clientset.Clientset {
	var err error
	if c.clientSetEx == nil {
		c.clientSetEx, err = k8s.GetK8sService().NewExtensionClient(c.KubeConfig())
	}
	if err != nil {
		panic(err)
	}
	return c.clientSetEx
}

func New(request *flow.TaskExecRequest) (c *Context, err error) {
	clusterFlowService, err := flow2.NewClusterFlowService(request)
	if err != nil {
		return nil, err
	}
	c = &Context{
		ClusterFlowService: clusterFlowService,
		apps:               make([]apps.App, 0),
	}
	c.FlowCC = c.CC().FlowCC()

	return c, nil
}

func (c *Context) GetOceanusIPSecret() (s *v1.Secret, err error) {
	_, s, err = tke.GetTkeService().GetOrCreateImagePullSecrets(c.Region, c.ClientSet())
	return
}

func (c *Context) GetNamespaceIPSecret(namespace string) (s *v1.Secret, err error) {
	_, s, err = tke.GetTkeService().GetOrCreateImagePullSecretsWithNamespace(c.Region, namespace, c.ClientSet())
	return
}

// ConfigMap 生成类型为ConfigMap的apps.App
func (c *Context) ConfigMap(secret *v1.Secret, option ...apps.AssignCMOption) apps.App {
	return c.ConfigMapBuilder(secret, option...).Build()
}

func (c *Context) ConfigMapBuilder(secret *v1.Secret, option ...apps.AssignCMOption) *apps.ConfigMapBuilder {
	owner := tke.GetTkeService().SecretAsOwnerReference(secret)
	option = append(option, apps.CMWithOwnerReference(owner))
	return apps.NewConfigMapBuilder(option...)
}

// ParamBase 一些通用的模板渲染参数
func (c *Context) ParamBase(workLoadKind, namespace, name string, isEni bool) (*oceanus_controller.Base, error) {
	annotations, err := c.GetPodAnnotations(isEni, true)
	if err != nil {
		return nil, err
	}
	nodeSelector := make(map[string]string)
	if c.ClusterType == constants.K8S_CLUSTER_TYPE_TKE {
		nodeSelector[constants.TKE_CVM_LABEL_KEY] = constants.TKE_CONTROL_NODE_LABEL_VAL
	}

	secret, err := c.GetNamespaceIPSecret(namespace)
	if err != nil {
		return nil, err
	}

	return &oceanus_controller.Base{
		IsEKs:          c.ClusterType == constants.K8S_CLUSTER_TYPE_EKS,
		ArchGeneration: c.Tke.ArchGeneration,
		ClusterGroupId: c.ClusterGroup.Id,
		ClusterId:      c.Cluster.Id,
		AppId:          c.ClusterGroup.AppId,
		OwnerUin:       c.ClusterGroup.OwnerUin,
		Region:         c.ClusterGroup.Region,

		WorkLoadKind: workLoadKind,
		Namespace:    namespace,
		Name:         name,
		OwnerRef:     &oceanus_controller.OwnerRef{Name: secret.Name, Uid: string(secret.UID)},
		Labels: map[string]string{
			"app":            name,
			"clusterGroupId": fmt.Sprintf("%d", c.ClusterGroup.Id),
			"clusterId":      fmt.Sprintf("%d", c.Cluster.Id),
			"ownerUin":       c.ClusterGroup.OwnerUin,
			"region":         c.ClusterGroup.Region,
		},

		Replicas:    1,
		ServiceName: fmt.Sprintf("%s-service", name),

		Annotations: annotations,

		// PriorityClassName
		ServiceAccountName: "default",
		HostAliases:        make(map[string]string),
		NodeSelector:       nodeSelector,

		// AppImage
		// ListenPort
		// SidecarImage
		Env: map[string]string{
			"REGION":          c.ClusterGroup.Region,
			"ARCH_GENERATION": fmt.Sprintf("%d", c.Tke.ArchGeneration),
		},
	}, nil
}

func (c *Context) GetPodAnnotations(isENI bool, formatAnno bool) (map[string]string, error) {
	annotations := make(map[string]string)

	if isENI && c.ConnectionType == constants.CLUSTER_NET_CONNECTION_TYPE_ENI && c.ClusterGroup.NetEniType == constants.CLUSTER_NET_ENI_POD {
		annotations = tke.GetTkeService().GenerateTkeEniAnnotation()
		if c.ClusterGroup.AgentSerialId != "" {
			eniConfig, err := c.ClusterGroupService.GetClusterEniParam(formatAnno)
			if err != nil {
				return nil, err
			}
			annotations["tke.cloud.tencent.com/cross-tenant-eni-config"] = eniConfig
		}
	}

	if c.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		cc, err := c.CC().FlowCC().TkeCC().ClusterConfig(c.ClusterGroup)
		if err != nil {
			return nil, err
		}
		annotations[constants.EKS_CPU_TYPE_KEY] = cc.EksCpuType
		annotations[constants.EKS_SECURITY_GROUP_ID_KEY] = cc.SecurityGroup
	}

	return annotations, nil
}

// Service Service.App, 默认使用TCP
func (c *Context) Service(componentName string, port int32, owner *v1.Secret) apps.App {
	return c.ServiceBuilder(componentName, port, owner).Build()
}

func (c *Context) ServiceBuilder(componentName string, port int32, owner *v1.Secret) *apps.ServiceBuilder {
	ownerReference := tke.GetTkeService().SecretAsOwnerReference(owner)
	labels := tke.GetTkeService().ComponentLabels(componentName, c.ClusterGroup, c.Cluster)
	return apps.NewServiceBuilder(
		apps.ServiceWithName(componentName+"-service"),
		apps.ServiceWithPort(port),
		apps.ServiceWithLabels(labels),
		apps.ServiceWithSelector(labels),
		apps.ServiceWithOwnerReference(ownerReference),
	)
}

func (c *Context) ServiceOption(componentLabelName string, componentServiceName string, port int32, targetPort int32, owner *v1.Secret) apps.App {
	ownerReference := tke.GetTkeService().SecretAsOwnerReference(owner)
	labels := tke.GetTkeService().ComponentLabels(componentLabelName, c.ClusterGroup, c.Cluster)
	return apps.NewServiceBuilder(
		apps.ServiceWithName(componentServiceName+"-service"),
		apps.ServiceWithPort(port),
		apps.ServiceWithTargetPort(targetPort),
		apps.ServiceWithLabels(labels),
		apps.ServiceWithSelector(labels),
		apps.ServiceWithOwnerReference(ownerReference),
	).Build()
}

// HadoopConfigMap 生成Hadoop需要的HadoopConfigMap  CA,WD中需要用到
func (c *Context) HadoopConfigMap(secret *v1.Secret) apps.App {
	return c.HadoopConfigMapBuilder(secret).Build()
}

func (c *Context) HadoopConfigMapBuilder(secret *v1.Secret) *apps.ConfigMapBuilder {
	vFunc := func() (string, error) {
		// 替换模板参数
		stateCosBucket := c.Cluster.DefaultCOSBucket
		if len(c.Cluster.StateCOSBucket) > 0 {
			stateCosBucket = c.Cluster.StateCOSBucket
		}
		params := struct {
			CosBucket string
			Region    string
		}{
			CosBucket: stateCosBucket,
			Region:    c.ClusterGroup.Region,
		}
		coreSite, err := c.FlowCC.CoreSiteXml(params)
		if err != nil {
			return "", err
		}
		return coreSite, nil
	}

	coreSiteKey := "core-site.xml"

	tFunc := func(client apps.Client, v interface{}) (result interface{}, err error) {
		cm := v.(*v1.ConfigMap)

		if ok, w := auth.WhiteListValue(int64(c.ClusterGroup.AppId), constants.WHITE_LIST_CORE_SITE); ok {
			cs, err := w.GetCoreSite(c.ClusterGroup.SerialId)
			if err != nil {
				return nil, err
			}
			if cs != "" {
				cm.Data[coreSiteKey], err = c.mergeCoreSite(cm.Data[coreSiteKey], cs)
				if err != nil {
					return nil, err
				}
			}
		}

		ocm, _ := c.K8sService().GetConfigMap(client.ClientSet(), cm.Namespace, cm.Name)
		if ocm == nil {
			return cm, nil
		}
		if ocm.Data[coreSiteKey] != "" {
			cm.Data[coreSiteKey], err = c.mergeCoreSite(cm.Data[coreSiteKey], ocm.Data[coreSiteKey])
			if err != nil {
				return nil, err
			}
		}
		return cm, nil
	}

	builder := c.ConfigMapBuilder(
		secret,
		apps.CMWithName(constants.HadoopConfigmapName),
		apps.CMAddData(coreSiteKey, vFunc))

	builder.WithTransform(tFunc)
	return builder
}

func (c *Context) mergeCoreSite(old, new string) (string, error) {
	type Property struct {
		Name  string `xml:"name"`
		Value string `xml:"value"`
	}
	type Configuration struct {
		XMLName    xml.Name   `xml:"configuration"`
		Properties []Property `xml:"property"`
	}

	oc := &Configuration{}
	err := xml.Unmarshal([]byte(old), oc)
	if err != nil {
		return "", err
	}
	nc := &Configuration{}
	err = xml.Unmarshal([]byte(new), nc)
	if err != nil {
		return "", err
	}
	pMap := make(map[string]string)
	for _, p := range oc.Properties {
		pMap[p.Name] = p.Value
	}
	for _, p := range nc.Properties {
		pMap[p.Name] = p.Value
	}
	pArr := make([]Property, 0)
	for k, v := range pMap {
		pArr = append(pArr, Property{k, v})
	}

	oc.Properties = pArr

	output, err := xml.MarshalIndent(oc, "", "    ")
	if err != nil {
		return "", err
	}

	processingInstruction := `<?xml-stylesheet type="text/xsl" href="configuration.xsl"?>` + "\n"

	return xml.Header + processingInstruction + string(output), nil
}

// LogListenerConfigMap 组件如果需要收集日志到我们自己的CLS中，则添加这个App
func (c *Context) LogListenerConfigMap(withStartupLog bool, secret *v1.Secret) apps.App {
	return c.LogListenerConfigMapBuilder(withStartupLog, secret).Build()
}

func (c *Context) LogListenerConfigMapBuilder(withStartupLog bool, secret *v1.Secret) *apps.ConfigMapBuilder {
	configMapName := constants.LoglistenerConfigmapName
	if withStartupLog {
		configMapName = constants.LoglistenerConfigmapNameWithStartupLog
	}
	vFunc := func() (string, error) {
		return c.getLogListenerConf(withStartupLog)
	}
	return c.ConfigMapBuilder(
		secret,
		apps.CMWithName(configMapName),
		apps.CMAddData("loglistener.conf", vFunc))
}

// LogListenerMetaConfigMap 组件如果需要收集日志到我们自己的CLS中，则添加这个App
// 并且会采用 tke.GetTkeService().ComponentLabels 生成上报到CLS的一些固定字段信息
func (c *Context) LogListenerMetaConfigMap(component string, secret *v1.Secret) apps.App {
	return c.LogListenerMetaConfigMapBuilder(component, secret).Build()
}

func (c *Context) LogListenerMetaConfigMapBuilder(component string, secret *v1.Secret) *apps.ConfigMapBuilder {
	meta := tke.GetTkeService().ComponentLabels(component, c.ClusterGroup, c.Cluster)
	vFunc := func() (string, error) {
		metadata := make(map[string]string, len(meta)+1)
		for k, v := range meta {
			metadata[fmt.Sprintf("pod_label_%s", k)] = v
		}
		metadata["pod_name"] = fmt.Sprintf("%s-0", component)
		b, _ := json.Marshal(metadata)
		return string(b), nil
	}
	return c.ConfigMapBuilder(
		secret,
		apps.CMWithName(fmt.Sprintf("%s-log-metadata", component)),
		apps.CMAddData(".controller.log.metadata", vFunc))
}

func (c *Context) getLogListenerConf(withStartupLog bool) (string, error) {
	// 替换模板参数
	id, err := c.FlowCC.StartupLogListenerSecretId()
	if err != nil {
		return "", nil
	}

	key, err := c.FlowCC.StartupLogListenerSecretKey()
	if err != nil {
		return "", nil
	}
	params := struct {
		RegionName                  string
		StartupLogListenerSecretId  string
		StartupLogListenerSecretKey string
		PodIP                       string
		RandomUUID                  string
		WithStartupLog              bool
	}{
		RegionName:                  c.ClusterGroup.Region,
		StartupLogListenerSecretId:  id,
		StartupLogListenerSecretKey: key,
		PodIP:                       "127.0.0.1", // 无意义, 但属于必填项
		RandomUUID:                  uuid.NewRandom().String(),
		WithStartupLog:              withStartupLog,
	}

	return c.FlowCC.LogListerner(params)
}

// CdbParamsForConf 集群CDB的一些通用参数，可用于模板渲染
func (c *Context) CdbParamsForConf(database string) (interface{}, error) {
	cdbInstance := cdb.GetCdbService().MustGetCdbInstanceForClusterId(c.Cluster.Id)
	params := struct {
		DatabaseURL      string
		DatabaseName     string
		DatabaseUser     string
		DatabasePassword string
	}{
		DatabaseURL:      fmt.Sprintf("%s:%d", cdbInstance.Vip, cdbInstance.Vport),
		DatabaseName:     database,
		DatabaseUser:     cdbInstance.User,
		DatabasePassword: cdbInstance.Password,
	}
	return params, nil
}

// ConfUseCdbParams 使用CdbParamsForConf拿到渲染参数，用confFunc渲染
func (c *Context) ConfUseCdbParams(database string, confFunc func(interface{}) (string, error)) apps.CMDataValue {
	return func() (string, error) {
		params, err := c.CdbParamsForConf(database)
		if err != nil {
			return "", err
		}
		return confFunc(params)
	}
}

// FlinkConf flink-conf.yaml的生成函数
func (c *Context) FlinkConf(zkReplicaNumber int) apps.CMDataValue {
	return func() (string, error) {
		// 替换模板参数
		stateCosBucket := c.Cluster.DefaultCOSBucket
		if len(c.Cluster.StateCOSBucket) > 0 {
			stateCosBucket = c.Cluster.StateCOSBucket
		}
		params := struct {
			UniqClusterId string
			ZooKeeperUrl  string
			CosBucket     string
		}{
			UniqClusterId: c.Cluster.UniqClusterId,
			ZooKeeperUrl:  GeneratePortConcatenatedIpString(GetZooKeeperServers(zkReplicaNumber), strconv.Itoa(constants.ZooKeeperPort)),
			CosBucket:     stateCosBucket,
		}

		flinkConf, err := c.FlowCC.ClusterAdminCC().FlinkConf(params)
		if err != nil {
			panic(err)
		}
		if len(c.Cluster.FlinkConfig) == 0 {
			return flinkConf, err
		}

		toMapConf := func(str string) map[string]string {
			dec := yaml.NewYAMLOrJSONDecoder(bytes.NewReader([]byte(str)), len(str))
			mapConf := map[string]string{}
			if err := dec.Decode(&mapConf); err != nil {
				panic(errorcode.NewStackError(errorcode.InternalErrorCode, "", err))
			}
			return mapConf
		}
		baseConf := toMapConf(flinkConf)
		overrideConf := toMapConf(c.Cluster.FlinkConfig)
		for k, v := range overrideConf {
			baseConf[k] = v
		}

		resultConf := make([]string, 0)
		for k, v := range baseConf {
			resultConf = append(resultConf, fmt.Sprintf("%s: %s", k, v))
		}
		sort.Sort(sort.StringSlice(resultConf))
		return strings.Join(resultConf, "\n"), nil
	}
}

func (c *Context) FlinkConfForUa(zkReplicaNumber int) apps.CMDataValue {
	return func() (string, error) {
		// 替换模板参数
		stateCosBucket := c.Cluster.DefaultCOSBucket
		if len(c.Cluster.StateCOSBucket) > 0 {
			stateCosBucket = c.Cluster.StateCOSBucket
		}
		params := struct {
			UniqClusterId string
			ZooKeeperUrl  string
			CosBucket     string
		}{
			UniqClusterId: c.Cluster.UniqClusterId,
			ZooKeeperUrl:  GeneratePortConcatenatedIpString(GetNsZooKeeperServers(zkReplicaNumber, c.Cluster.Zone), strconv.Itoa(constants.ZooKeeperPort)),
			CosBucket:     stateCosBucket,
		}

		flinkConf, err := c.FlowCC.ClusterAdminCC().FlinkConf(params)
		if err != nil {
			panic(err)
		}
		if len(c.Cluster.FlinkConfig) == 0 {
			return flinkConf, err
		}

		toMapConf := func(str string) map[string]string {
			dec := yaml.NewYAMLOrJSONDecoder(bytes.NewReader([]byte(str)), len(str))
			mapConf := map[string]string{}
			if err := dec.Decode(&mapConf); err != nil {
				panic(errorcode.NewStackError(errorcode.InternalErrorCode, "", err))
			}
			return mapConf
		}
		baseConf := toMapConf(flinkConf)
		overrideConf := toMapConf(c.Cluster.FlinkConfig)
		for k, v := range overrideConf {
			baseConf[k] = v
		}

		resultConf := make([]string, 0)
		for k, v := range baseConf {
			resultConf = append(resultConf, fmt.Sprintf("%s: %s", k, v))
		}
		sort.Sort(sort.StringSlice(resultConf))

		return strings.Join(resultConf, "\n"), nil
	}
}

// 返回 *************:2181,*************:2181,**************:2181 的形式
func GeneratePortConcatenatedIpString(ipArray []string, port string) string {
	return strings.Join(ipArray, ":"+port+",") + ":" + port
}

func GetZooKeeperServers(replicaNumber int) []string {
	result := make([]string, replicaNumber)

	for i := 0; i < replicaNumber; i++ {
		result[i] = fmt.Sprintf("zookeeper-%d.zookeeper-headless.oceanus.svc.cluster.local", i)
	}
	return result
}

func GetNsZooKeeperServers(replicaNumber int, zkNamespace string) []string {
	result := make([]string, replicaNumber)

	for i := 0; i < replicaNumber; i++ {
		result[i] = fmt.Sprintf("zookeeper-%d.zookeeper-headless.%s.svc.cluster.local", i, zkNamespace)
	}
	return result
}
