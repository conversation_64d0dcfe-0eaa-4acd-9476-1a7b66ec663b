package deploy

import (
	"fmt"
	appsV1 "k8s.io/api/apps/v1"
	coreV1 "k8s.io/api/core/v1"
	"reflect"
	"runtime/debug"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	flow2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/flow"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/version"
)

type Component interface {
	Apps(ctx *Context) ([]apps.App, error)
}

// Handler taskcenter task的处理
type Handler struct {
	c Component
}

func NewHandler(c Component) *Handler {
	return &Handler{c: c}
}

func (h *Handler) HandleTaskStatusInit(requestId string, request *flow.TaskExecRequest) (resp *flow.TaskExecResponse) {
	flowService := flow2.GetFlowService()

	defer func() {
		if err := recover(); err != nil {
			logger.Errorf("[%s] Deploy Task failed because: %v %s", requestId, err, string(debug.Stack()))
			resp = flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_INIT, 0.1, fmt.Sprintf("%v", err), request.Params)
		}
	}()
	ctx, err := New(request)
	if err != nil {
		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_INIT, 0.1,
			fmt.Sprintf("%v", err), request.Params)
	}

	appList, err := h.c.Apps(ctx)
	if err != nil {
		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_IN_PROCESS, flow.TASK_STATUS_INIT, 0.1,
			fmt.Sprintf("%v", err), request.Params)
	}
	for _, app := range appList {
		aType := reflect.TypeOf(app)
		logger.Infof("%s begin to invoke apply method", aType)
		t := app.AppType()
		params, err := app.Params()
		if err != nil {
			return ctx.RetryRspWithErr(err)
		}
		t, err = app.Decode(params, t)
		if err != nil {
			return ctx.RetryRspWithErr(err)
		}
		t, err = app.Transform(ctx, t)
		if err != nil {
			return ctx.RetryRspWithErr(err)
		}
		result, err := app.Apply(ctx, t)
		if err != nil {
			return ctx.RetryRspWithErr(err)
		}
		h.registryComponentVersion(ctx, result)
	}
	return ctx.DoneRsp("")
}

func (h *Handler) HandleTaskStatusRunning(requestId string, request *flow.TaskExecRequest) (resp *flow.TaskExecResponse) {
	flowService := flow2.GetFlowService()

	defer func() {
		if err := recover(); err != nil {
			logger.Errorf("[%s] Deploy Task failed because: %v", requestId, err)
			resp = flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_RUNNING, 0.1, fmt.Sprintf("%v",
				err), request.Params)
		}
	}()
	s, err := New(request)
	if err != nil {
		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_RUNNING, 0.1,
			fmt.Sprintf("%v", err), request.Params)
	}

	appList, err := h.c.Apps(s)
	if err != nil {
		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_IN_PROCESS, flow.TASK_STATUS_RUNNING, 0.1,
			fmt.Sprintf("%v", err), request.Params)
	}
	for _, app := range appList {
		aType := reflect.TypeOf(app)
		logger.Infof("%s begin to invoke ready method", aType)
		t := app.AppType()
		params, err := app.Params()
		if err != nil {
			return s.RetryRspWithErr(err)
		}
		t, err = app.Decode(params, t)
		if err != nil {
			return s.RetryRspWithErr(err)
		}
		ready, err := app.Ready(s, t)
		if err != nil {
			return s.RetryRspWithErr(err)
		}
		if !ready {
			// 如果没有ready则继续retry
			return s.RetryRsp(fmt.Sprintf("not ready: %s", strconv.FormatBool(ready)))
		}
	}
	return s.DoneRsp("")
}

func (h *Handler) registryComponentVersion(ctx *Context, template interface{}) {
	name := ""
	containers := make([]coreV1.Container, 0)
	switch v := template.(type) {
	case *appsV1.Deployment:
		name = v.Name
		containers = v.Spec.Template.Spec.Containers
		// 如果是开启session集群启动的Component则不记录到数据库
		if len(v.Spec.Selector.MatchLabels) > 0 && (v.Spec.Selector.MatchLabels[constants.ClusterLabelComponent] == constants.ClusterJobmanager ||
			v.Spec.Selector.MatchLabels[constants.ClusterLabelComponent] == constants.ClusterTaskManager) {
			return
		}
	case *appsV1.StatefulSet:
		name = v.Name
		containers = v.Spec.Template.Spec.Containers
	case *appsV1.DaemonSet:
		name = v.Name
		containers = v.Spec.Template.Spec.Containers
	default:

	}

	for _, c := range containers {
		v, _ := service.ImageToVersion(c.Image)
		version.InitTkeVersion(ctx.Cluster.Id, name, c.Name, v)
	}
}
