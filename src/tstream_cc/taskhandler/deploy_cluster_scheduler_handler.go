package taskHandler

import (
	"fmt"
	v1 "k8s.io/api/core/v1"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/oceanus_controller"
	clusterService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/region"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type DeployClusterSchedulerService struct {
}

func (cs *DeployClusterSchedulerService) Apps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	cg := ctx.ClusterGroup

	// 共享集群 包年包月下的eks集群，不做任何处理
	if cg.Type == constants.CLUSTER_GROUP_TYPE_SUB_EKS {
		return result, nil
	}

	if ctx.Tke.ArchGeneration < constants.TKE_ARCH_GENERATION_V2 {
		return result, nil
	}

	// 共享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId != "" {
		return result, nil
	}

	// 共享集群母集群，开新区
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone != "" {
		// TODO 开新区，可以增大replica
		return result, nil
	}

	// 创建共享集群母集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone == "" {
	}

	// 独享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId == "" {

	}

	secret, err := ctx.GetOceanusIPSecret()
	if err != nil {
		return result, err
	}

	result = append(result,
		ctx.HadoopConfigMap(secret),
		ctx.LogListenerConfigMap(true, secret),
		ctx.LogListenerMetaConfigMap(constants.ComponentClusterScheduler, secret),
		//cs.clusterSchedulerConfigMap(ctx, secret),
		ctx.Service(constants.ComponentClusterScheduler, constants.ClusterSchedulerPort, secret))

	if ctx.Tke.ArchGeneration <= constants.TKE_ARCH_GENERATION_V2 {
		result = append(result, &clusterSchedulerSts{ctx: ctx})
	}
	if ctx.Tke.ArchGeneration >= constants.TKE_ARCH_GENERATION_V3 {
		result = append(result, &clusterSchedulerDep{ctx: ctx})
	}

	if ctx.Tke.ArchGeneration <= constants.TKE_ARCH_GENERATION_V5 {
		result = append(result, cs.clusterSchedulerConfigMap(ctx, secret))
	}
	if ctx.Tke.ArchGeneration >= constants.TKE_ARCH_GENERATION_V6 {
		result = append(result, cs.clusterSchedulerConfigMapForMerge(ctx, secret))
		// 兼容watchdog service 保留
		result = append(result, ctx.ServiceOption(constants.ComponentClusterScheduler,
			constants.ComponentWatchdog, constants.WatchdogPort, constants.ClusterSchedulerPort, secret))
	}

	result = append(result, &sendCredentialApp{ctx: ctx})

	return result, nil
}

func (cs *DeployClusterSchedulerService) clusterSchedulerConfigMap(ctx *deploy.Context, secret *v1.Secret) apps.App {
	dbName, _ := ctx.GetParam(constants.FLOW_PARAM_CLUSTERSCHEDULER_DB, constants.ClusterSchedulerDatabaseName)
	csCC := ctx.FlowCC.ClusterSchedulerCC()
	return ctx.ConfigMap(
		secret,
		apps.CMWithName(constants.ClusterSchedulerConfigmapName),
		apps.CMAddData("conf.yaml", cs.getConfigForConf(ctx)),
		apps.CMAddData("flink-conf.yaml", ctx.FlinkConf(constants.ZooKeeperComponentReplicaNumber)),
		apps.CMAddData("log4j.properties", csCC.Flink111Log4jV2),
		// flink-1.13 k8s启动，log 的配置名字改为这个
		apps.CMAddData("log4j-console.properties", csCC.FlinkLog4jV2),
		apps.CMAddData("notify.properties", cs.getConfigForNotifyProperties(ctx)),
		apps.CMAddData("log4j-1.properties", csCC.Log4jV1),
		apps.CMAddData("application.conf", ctx.ConfUseCdbParams(dbName, ctx.FlowCC.ApplicationConf)),
		apps.CMAddData("config.properties", cs.configProperties(ctx)),
		apps.CMAddData("mybatis-config.xml", csCC.MybatisConf),
		apps.CMAddData("flink_restart_job.bpmn", csCC.FlinkRestartJobBPMN),
		apps.CMAddData("flink_run_job.bpmn", csCC.FlinkRunJobBPMN),
		apps.CMAddData("flink_stop_job.bpmn", csCC.FlinkStopJobBPMN))
	// ClusterAdmin 使用的 Log4j 1.x 配置文件 不能用 Common 下的 log4j.properties, 因为还需要写入日志到文件, 以便 CLS 采集

	// 去掉原来 1.10 集群 采用 Log4j的逻辑 新部署的集群，不会再是 1.10 的了
	// 去掉原来 on emr禁止日志输出到console，防止滚动日志失效
}

func (cs *DeployClusterSchedulerService) clusterSchedulerConfigMapForMerge(ctx *deploy.Context, secret *v1.Secret) apps.App {
	dbName, _ := ctx.GetParam(constants.FLOW_PARAM_CLUSTERSCHEDULER_DB, constants.ClusterSchedulerDatabaseName)
	csCC := ctx.FlowCC.ClusterSchedulerCC()
	return ctx.ConfigMap(
		secret,
		apps.CMWithName(constants.ClusterSchedulerConfigmapName),
		apps.CMAddData("conf.yaml", cs.getConfigForConfMerge(ctx)),
		apps.CMAddData("flink-conf.yaml", ctx.FlinkConf(constants.ZooKeeperComponentReplicaNumber)),
		apps.CMAddData("log4j.properties", csCC.Flink111Log4jV2),
		// flink-1.13 k8s启动，log 的配置名字改为这个
		apps.CMAddData("log4j-console.properties", csCC.FlinkLog4jV2),
		apps.CMAddData("notify.properties", cs.getConfigForNotifyProperties(ctx)),
		apps.CMAddData("log4j-1.properties", csCC.Log4jV1),
		apps.CMAddData("application.conf", ctx.ConfUseCdbParams(dbName, ctx.FlowCC.ApplicationConf)),
		apps.CMAddData("config.properties", cs.configProperties(ctx)),
		apps.CMAddData("mybatis-config.xml", ctx.ConfUseCdbParams(dbName, csCC.MergeMybatisConf)))
}

func (cs *DeployClusterSchedulerService) configProperties(ctx *deploy.Context) apps.CMDataValue {
	return func() (string, error) {
		return ctx.ConfUseCdbParams(constants.ClusterSchedulerDatabaseName,
			ctx.FlowCC.ClusterSchedulerCC().ConfProperties)()
	}
}

func (cs *DeployClusterSchedulerService) getConfigForNotifyProperties(ctx *deploy.Context) apps.CMDataValue {
	return func() (string, error) {
		url, err := ctx.FlowCC.NginxCC().Url()
		if err != nil {
			return "", err
		}
		// 替换模板参数
		params := struct {
			PublicServiceNginxUrl string
		}{
			PublicServiceNginxUrl: url,
		}
		return ctx.FlowCC.NotifyProperties(params)
	}
}

func (cs *DeployClusterSchedulerService) getConfigForConf(ctx *deploy.Context) apps.CMDataValue {
	return func() (string, error) {
		nginxUsername, nginxPassword, url, err := ctx.FlowCC.NginxCC().Info()
		if err != nil {
			return "", err
		}

		latestFlinkVersion := ctx.CC().DefaultFlinkVersion()

		regionId, err := region.GetRegionIdByName(ctx.ClusterGroup.Region)
		if err != nil {
			return "", err
		}

		params := struct {
			NginxUsername         string
			NginxPassword         string
			LatestFlinkVersion    string
			RegionId              int64
			ClusterId             int64
			PublicServiceNginxUrl string
			ClusterGroupId        int64

			// 下面三个参数已经不用, 为了保持历史模板的兼容性, 暂时保留
			ZooKeeperUrl string
			YarnRestUrl  string
			HdfsRestUrl  string
		}{
			NginxUsername:         nginxUsername,
			NginxPassword:         nginxPassword,
			LatestFlinkVersion:    latestFlinkVersion,
			RegionId:              int64(regionId),
			ClusterId:             ctx.Cluster.Id,
			PublicServiceNginxUrl: url,
			ClusterGroupId:        ctx.ClusterGroup.Id,
		}

		return ctx.FlowCC.ClusterSchedulerCC().Conf(params)
	}
}

func (cs *DeployClusterSchedulerService) getConfigForConfMerge(ctx *deploy.Context) apps.CMDataValue {
	return func() (string, error) {
		nginxUsername, nginxPassword, url, err := ctx.FlowCC.NginxCC().Info()
		if err != nil {
			return "", err
		}

		latestFlinkVersion := ctx.CC().DefaultFlinkVersion()

		regionId, err := region.GetRegionIdByName(ctx.ClusterGroup.Region)
		if err != nil {
			return "", err
		}
		dispatcherThreadPoolSize := 20
		clusterType := constants.CLUSTER_TYPE_EXCLUSIVE
		if ctx.ClusterGroup.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM {
			clusterType = constants.CLUSTER_TYPE_SHARED
			dispatcherThreadPoolSize = 120
		}
		isEks, err := clusterService.IsEks(ctx.ClusterGroup.Id)
		if err != nil {
			return "", err
		}
		params := struct {
			NginxUsername         string
			NginxPassword         string
			LatestFlinkVersion    string
			RegionId              int64
			ClusterId             int64
			PublicServiceNginxUrl string
			ClusterGroupId        int64

			ClusterSerialId      string
			ClusterGroupSerialId string
			CosBucket            string
			AppId                string
			Region               string
			SubnetId             string
			ClusterType          string
			IsEks                string
			DispatcherThreadPoolSize int
		}{
			NginxUsername:         nginxUsername,
			NginxPassword:         nginxPassword,
			LatestFlinkVersion:    latestFlinkVersion,
			RegionId:              int64(regionId),
			ClusterId:             ctx.Cluster.Id,
			PublicServiceNginxUrl: url,
			ClusterGroupId:        ctx.ClusterGroup.Id,

			ClusterSerialId:      ctx.Cluster.UniqClusterId,
			ClusterGroupSerialId: ctx.ClusterGroup.SerialId,
			CosBucket:            ctx.Cluster.DefaultCOSBucket,
			AppId:                strconv.FormatInt(int64(ctx.ClusterGroup.AppId), 10),
			Region:               ctx.ClusterGroup.Region,
			SubnetId:             ctx.Cluster.SubnetId,
			ClusterType:          clusterType,
			IsEks:                strconv.FormatBool(isEks),
			DispatcherThreadPoolSize: dispatcherThreadPoolSize,
		}

		return ctx.FlowCC.ClusterSchedulerCC().MergeConf(params)
	}
}

type clusterSchedulerSts struct {
	ctx *deploy.Context
	apps.StatefulSet
}

func (c *clusterSchedulerSts) Params() (interface{}, error) {
	return getClusterSchedulerParams(c.ctx, constants.K8S_KIND_STATEFULSET)
}

func (c *clusterSchedulerSts) Decode(params, into interface{}) (interface{}, error) {
	return c.ctx.FlowCC.AppsCC().ClusterScheduler(params, into)
}

type clusterSchedulerDep struct {
	apps.Deployment
	ctx *deploy.Context
}

func (c *clusterSchedulerDep) Params() (interface{}, error) {
	return getClusterSchedulerParams(c.ctx, constants.K8S_KIND_DEPLOYMENT)
}

func (c *clusterSchedulerDep) Decode(params, into interface{}) (interface{}, error) {
	return c.ctx.FlowCC.AppsCC().ClusterScheduler(params, into)
}

func getClusterSchedulerParams(ctx *deploy.Context, workLoadKind string) (*oceanus_controller.Base,
	error) {
	var err error
	var appContainerImage string
	var crossEni = true
	if ctx.ClusterGroup.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM {
		appContainerImage, err = ctx.CC().ImageRegistry().UniformClusterScheduler()
		crossEni = false
	} else {
		appContainerImage, err = ctx.CC().ImageRegistry().ClusterScheduler()
	}

	if err != nil {
		return nil, err
	}

	logContainerImage, err := ctx.CC().ImageRegistry().LogListener()
	if err != nil {
		return nil, err
	}

	base, err := ctx.ParamBase(workLoadKind,
		constants.OCEANUS_NAMESPACE,
		constants.ComponentClusterScheduler,
		crossEni)
	if err != nil {
		return nil, err
	}

	if ctx.Tke.ArchGeneration >= constants.TKE_ARCH_GENERATION_V5 &&
		ctx.ClusterType == constants.K8S_CLUSTER_TYPE_TKE &&
		ctx.ClusterGroup.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && ctx.ClusterGroup.AgentSerialId == "" { //TKE 集群
		for key := range base.NodeSelector {
			delete(base.NodeSelector, key)
		}

		base.NodeSelector[constants.TKE_CVM_WORKER_LABEL_KEY] = fmt.Sprintf("worker")
		base.PriorityClassName = constants.TKE_PRIORITY_CLASS_NAME
	}

	if ctx.Tke.ArchGeneration >= constants.TKE_ARCH_GENERATION_V6 {
		base.ClusterType = constants.CLUSTER_TYPE_EXCLUSIVE
		// 统一版本独享集群
		if ctx.ClusterGroup.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM {
			// 统一版本共享集群
			base.ClusterType = constants.CLUSTER_TYPE_SHARED
		}
	}
	err = fillTestParams(ctx, base)
	if err != nil {
		return nil, err
	}
	base.AppImage = appContainerImage
	base.SidecarImage = logContainerImage
	base.WorkLoadKind = workLoadKind
	base.ServiceAccountName = constants.SERVICE_ACCOUNT_OCEANUS

	return base, nil
}
