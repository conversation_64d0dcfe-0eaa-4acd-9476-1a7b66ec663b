package taskHandler

import (
	"encoding/json"
	"errors"
	"fmt"

	cvm "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/cvm/v20170312"
	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/dlocker"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cbs"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config/common_config"
	cvm3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cvm"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/tke"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
	tke2 "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke"
)

type oneStepActionService struct {
}

func (s *oneStepActionService) Apps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	cg := ctx.ClusterGroup
	action, exist := ctx.GetParam(constants.FLOW_PARAM_FLOW_ACTION, "")
	if !exist {
		return result, fmt.Errorf("action is empty, please check")
	}

	if constants.FLOW_PARAM_FLOW_ACTION_CREATE_CVM == action {
		if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM {
			result = append(result, &transformPoolWorkerToWorkerApp{ctx: ctx})
			result = append(result, &transformPooledCvmApp{ctx: ctx})
			result = append(result, &uniformClusterCreateCountPooledWorkerApp{ctx: ctx})
		}
		if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId == "" {
			result = append(result, &transformPooledCvmApp{ctx: ctx})
			result = append(result, &privateClusterCreateCountWorkerApp{ctx: ctx})
			result = append(result, &createNodeEniApp{ctx: ctx})
		}
		if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId != "" {
			result = append(result, &transformPoolWorkerToWorkerApp{ctx: ctx})
			result = append(result, &uniformClusterTransformWorkerToPrivateApp{ctx: ctx})
			result = append(result, &transformPooledCvmApp{ctx: ctx})
			result = append(result, &uniformClusterCreateCountPooledWorkerApp{ctx: ctx})
		}
	} else if constants.FLOW_PARAM_FLOW_ACTION_CREATE_NODE_ENI == action {
		result = append(result, &createNodeEniApp{ctx: ctx})
	}

	return result, nil
}

type oneStepEndpointService struct {
}

func (s *oneStepEndpointService) Apps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	action, exist := ctx.GetParam(constants.FLOW_PARAM_FLOW_ACTION, "")
	if !exist {
		return result, fmt.Errorf("action is empty, please check")
	}
	if constants.FLOW_PARAM_FLOW_ACTION_CREATE_CVM == action {
	}

	return result, nil
}

type transformPooledCvmApp struct {
	apps.ApplyApp
	ctx    *deploy.Context
	zone   string
	subnet string
	count  int64

	canTryMap            map[string]struct{}
	modifyVpcMap         map[string]struct{}
	addInstanceMap       map[string]struct{}
	completedInstanceMap map[string]struct{}
}

func (c *transformPooledCvmApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	cg := c.ctx.ClusterGroup
	if c.ctx.Tke.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		return
	}

	if cg.NetEnvironmentType == constants.NETWORK_ENV_INNER_VPC {
		return
	}

	if cg.CuMem != constants.CVM_DEFAULT_MEMRATIO {
		return
	}

	optId := cg.SerialId
	if cg.AgentSerialId != "" {
		optId = cg.AgentSerialId
	}
	c.zone, _ = c.ctx.GetParam(constants.FLOW_PARAM_ZONE, cg.Zone)
	if c.zone == "" {
		return nil, fmt.Errorf("zone is empty")
	}

	locker := dlocker.NewDlocker("transformPooledCvmApp", fmt.Sprintf("optId-%s-%s", optId, c.zone), 120)
	err = locker.Lock()
	if err != nil {
		return false, err
	}
	defer locker.UnLock()

	zoneSubnets, err := c.ctx.Cluster.GetSupportedZoneSubnets()
	if err != nil {
		return
	}
	c.subnet = zoneSubnets[c.zone]
	if c.subnet == "" {
		return nil, fmt.Errorf("subnet is empty, zone %s", c.zone)
	}
	count, exist, err := c.ctx.GetParamInt(constants.FlowParamNeedCvmCount, 0)
	if err != nil {
		return
	}

	h := &ApplyTKEHandler{}
	if !exist && cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId == "" {
		if tn, cn, wn, _, err := h.CalcClusterNodeCount(c.ctx); err != nil {
			return nil, err
		} else {
			count = int64(cn + wn - tn)
		}
	}
	c.count = count
	c.ctx.SetReturnParam(constants.FlowParamNeedCvmCount, fmt.Sprintf("%d", count))

	err = c.tryTransformPooledCvm()
	if err != nil {
		return nil, err
	}

	if allNodeRunning, err := h.TryMakeAllNodesRunning(c.ctx); err != nil {
		return nil, err
	} else if !allNodeRunning {
		return nil, errors.New("wait all node running")
	}

	return nil, nil
}

func (c *transformPooledCvmApp) tryTransformPooledCvm() (err error) {
	c.canTryMap = make(map[string]struct{})
	tryMapExist, err := c.ctx.GetParamUseJson(constants.FlowParamCanTryCvm, &c.canTryMap)
	if err != nil {
		return
	}

	c.modifyVpcMap = make(map[string]struct{})
	_, err = c.ctx.GetParamUseJson(constants.FlowParamModifyVpcMap, &c.modifyVpcMap)
	if err != nil {
		return
	}

	c.addInstanceMap = make(map[string]struct{})
	_, err = c.ctx.GetParamUseJson(constants.FlowParamAddInstanceMap, &c.addInstanceMap)
	if err != nil {
		return
	}

	c.completedInstanceMap = make(map[string]struct{})
	_, err = c.ctx.GetParamUseJson(constants.FlowParamCompletedInstanceMap, &c.completedInstanceMap)
	if err != nil {
		return
	}

	//1. 找到PooledCVM
	if !tryMapExist {
		if err := c.findCanTryMap(); err != nil {
			return err
		}
	}
	//2. 修改网络
	if err = c.modifyVpc(); err != nil {
		return
	}

	//3. 添加到TKE
	if err = c.addInstance(); err != nil {
		return
	}

	//4. 完成添加
	if err = c.completedInstance(); err != nil {
		return
	}
	return
}

func (c *transformPooledCvmApp) completedInstance() (err error) {
	cg := c.ctx.ClusterGroup

	tkeService := tke.GetTkeService()
	for i := range c.addInstanceMap {
		instanceId := i
		if _, ok := c.completedInstanceMap[instanceId]; ok {
			continue
		}
		instanceIdSet := make([]*string, 0)
		instanceIdSet = append(instanceIdSet, &instanceId)

		totalCount, instanceSet, err := tkeService.DescribeClusterInstanceForIdsWithScsAccountByNetEnvironmentType(
			cg.NetEnvironmentType, cg.Region, c.ctx.Tke.InstanceId, instanceIdSet)
		if err != nil {
			return err
		}
		if totalCount < 1 {
			return fmt.Errorf("can not find instance %s, in cluster %s", instanceId, c.ctx.Tke.InstanceId)
		}
		instance := instanceSet[0]
		if *instance.InstanceState != constants.TKE_INSTANCE_STATE_RUNNING {
			return fmt.Errorf("wait instance %s running", instanceId)
		}

		service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
			sql, args := dao.NewUpdateBuilder("update PooledCvm").
				Set("Status", constants.CVM_POOL_STATUS_HISTORY).
				WhereEq("Status", constants.CVM_POOL_STATUS_PENDING).
				WhereEq("InstanceId", instanceId).
				Build()

			service3.TExecute(tx, sql, args)

			return nil
		}).Close()

		c.completedInstanceMap[instanceId] = struct{}{}
		c.ctx.SetReturnParamUseJson(constants.FlowParamCompletedInstanceMap, c.completedInstanceMap)
	}
	b, _ := json.Marshal(c.completedInstanceMap)
	logger.Infof("%s: %s", constants.FlowParamCompletedInstanceMap, string(b))

	c.addInstanceMap = make(map[string]struct{})
	c.ctx.SetReturnParamUseJson(constants.FlowParamAddInstanceMap, c.addInstanceMap)
	return
}

func (c *transformPooledCvmApp) addInstance() (err error) {
	cg := c.ctx.ClusterGroup
	cl := c.ctx.Cluster

	cvmService := cvm3.GetCvmService()
	tkeService := tke.GetTkeService()
	for i := range c.modifyVpcMap {
		instanceId := i
		if _, ok := c.addInstanceMap[instanceId]; ok {
			continue
		}
		instanceIdSet := make([]*string, 0)
		instanceIdSet = append(instanceIdSet, &instanceId)

		instanceSet, err := cvmService.DescribeInstancesWithScsAccount(cg.NetEnvironmentType, cg.Region, instanceIdSet)
		if err != nil {
			return err
		}
		if len(instanceSet) <= 0 {
			return fmt.Errorf("can not find instance %s", instanceId)
		}
		instance := instanceSet[0]
		if *instance.VirtualPrivateCloud.VpcId != cl.VpcId || *instance.VirtualPrivateCloud.SubnetId != c.subnet {
			return fmt.Errorf("instance %s vpc %s subnet %s not match, waiting", instanceId, cl.VpcId, c.subnet)
		}

		totalCount, _, err := tkeService.DescribeClusterInstanceForIdsWithScsAccountByNetEnvironmentType(cg.NetEnvironmentType, cg.Region, c.ctx.Tke.InstanceId, instanceIdSet)
		if err != nil {
			return err
		}
		if totalCount < 1 {
			cc, err := c.ctx.FlowCC.TkeCC().ClusterConfig(cg)
			if err != nil {
				return err
			}
			requestBuilder := tkeService.NewAddClusterExistedInstancesRequestBuilder()
			logSettings := &tke2.LoginSettings{}
			if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId == "" {
				password, err := common_config.GetRainbowOceanusClusterPassword()
				if err != nil {
					return err
				}
				logSettings.Password = &password
			} else { //共享资源池使用Native节点
				sshKey, err := c.ctx.FlowCC.SshKey()
				if err != nil {
					return err
				}
				logSettings.KeyIds = []*string{&sshKey}
				requestBuilder.WithNodeType("Native")
			}
			h := &ApplyTKEHandler{}
			label := h.GetWorkerLabel(cg, c.zone)

			request := requestBuilder.WithClusterId(c.ctx.Tke.InstanceId).
				WithInstanceIds(instanceIdSet).
				WithInstanceAdvancedSettings(constants.TKE_DATA_DISK_AUTO_FORMAT_AND_MOUNT,
					constants.TKE_DATA_DISK_FILE_SYSTEM, constants.TKE_DATA_DISK_MOUNT_TARGET,
					constants.TKE_DATA_DISK_PARTITION, label, cc.UserScript).
				WithLoginSettings(logSettings).
				WithSecurityGroupIds([]*string{&cc.SecurityGroup}).
				Build()

			if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId == "" &&
				auth.IsInWhiteList(int64(cg.AppId), constants.WHITE_LIST_CPU_MANAGER_POLICY) {
				kubelet := make([]*string, 0)
				policy := "cpu-manager-policy=static"
				kubelet = append(kubelet, &policy)
				request.InstanceAdvancedSettings.ExtraArgs = &tke2.InstanceExtraArgs{
					Kubelet: kubelet,
				}
			}

			err = tkeService.AddClusterExistedInstancesWithScsAccountByNetworkEnvType(cg.NetEnvironmentType, cg.Region, request)
			if err != nil {
				return err
			}
		}

		c.addInstanceMap[instanceId] = struct{}{}
		c.ctx.SetReturnParamUseJson(constants.FlowParamAddInstanceMap, c.addInstanceMap)
	}
	b, _ := json.Marshal(c.addInstanceMap)
	logger.Infof("%s: %s", constants.FlowParamAddInstanceMap, string(b))

	c.modifyVpcMap = make(map[string]struct{})
	c.ctx.SetReturnParamUseJson(constants.FlowParamModifyVpcMap, c.modifyVpcMap)
	return
}

func (c *transformPooledCvmApp) modifyVpc() (err error) {
	cg := c.ctx.ClusterGroup
	cl := c.ctx.Cluster

	cvmService := cvm3.GetCvmService()
	for i := range c.canTryMap {
		instanceId := i
		if _, ok := c.modifyVpcMap[instanceId]; ok {
			continue
		}
		instanceIdSet := make([]*string, 0)
		instanceIdSet = append(instanceIdSet, &instanceId)

		instanceSet, err := cvmService.DescribeInstancesWithScsAccount(cg.NetEnvironmentType, cg.Region, instanceIdSet)
		if err != nil {
			return err
		}
		if len(instanceSet) <= 0 {
			return fmt.Errorf("can not find instance %s", instanceId)
		}
		instance := instanceSet[0]

		// 删除节点上的eni
		err = DeleteEniForNode(instanceId, c.ctx.ClusterGroup, c.ctx.ClusterGroupService)
		if err != nil {
			return err
		}

		if *instance.VirtualPrivateCloud.VpcId != cl.VpcId || *instance.VirtualPrivateCloud.SubnetId != c.subnet {
			err = cvmService.ModifyInstancesVpcAttributeWithScsAccount(cg.Region, instanceIdSet, cl.VpcId, c.subnet)
			if err != nil {
				return err
			}
		}

		err = cbs.GetCbsService(cg.NetEnvironmentType, cg.Region).CheckTerminateCbsFromCVM(instanceIdSet, func(disk *cvm.DataDisk) bool {
			return *disk.DiskSize == constants.TKE_WORKER_ZK_DISK_SIZE
		})
		if err != nil {
			return err
		}

		c.modifyVpcMap[instanceId] = struct{}{}
		c.ctx.SetReturnParamUseJson(constants.FlowParamModifyVpcMap, c.modifyVpcMap)
	}
	b, _ := json.Marshal(c.modifyVpcMap)
	logger.Infof("%s: %s", constants.FlowParamModifyVpcMap, string(b))

	c.canTryMap = make(map[string]struct{})
	c.ctx.SetReturnParamUseJson(constants.FlowParamCanTryCvm, c.canTryMap)
	return
}

func (c *transformPooledCvmApp) findCanTryMap() (err error) {
	cg := c.ctx.ClusterGroup
	if c.count <= 0 {
		return
	}
	pooledCvm, err := FindPooledCvmMap(cg, c.zone, int(c.count))
	if err != nil {
		return
	}

	for _, v := range pooledCvm {
		c.canTryMap[v.InstanceId] = struct{}{}
	}

	c.ctx.SetReturnParamUseJson(constants.FlowParamCanTryCvm, c.canTryMap)
	c.ctx.SetReturnParam(constants.FlowParamNeedCvmCount, fmt.Sprintf("%d", c.count-int64(len(pooledCvm))))

	b, _ := json.Marshal(c.canTryMap)
	logger.Infof("%s: %s", constants.FlowParamCanTryCvm, string(b))

	return
}

type privateClusterCreateCountWorkerApp struct {
	ctx *deploy.Context
	apps.ApplyApp
}

func (c *privateClusterCreateCountWorkerApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	cg := c.ctx.ClusterGroup
	if c.ctx.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		return nil, nil
	}

	h := &ApplyTKEHandler{}
	if allNodeRunning, err := h.TryMakeAllNodesRunning(c.ctx); err != nil {
		return nil, err
	} else if !allNodeRunning {
		return nil, errors.New("wait all node running")
	}

	count, exist, err := c.ctx.GetParamInt(constants.FlowParamNeedCvmCount, 0)
	if err != nil {
		return
	}

	if !exist && cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId == "" {
		if tn, cn, wn, _, err := h.CalcClusterNodeCount(c.ctx); err != nil {
			return nil, err
		} else {
			count = int64(cn + wn - tn)
		}
	}
	c.ctx.SetReturnParam(constants.FlowParamNeedCvmCount, fmt.Sprintf("%d", count))

	_, _, _, cvmConf, err := h.CalcClusterNodeCount(c.ctx)
	if err != nil {
		return nil, err
	}

	diskType, diskSize, err := cg.GetDiskInfo()
	if err != nil {
		return nil, err
	}
	changeConf, force, err := ChangeWorkSpec(c.ctx.ClusterGroup, cvmConf)
	if err != nil {
		return nil, err
	}

	label := h.GetBaseWorkerLabel(c.ctx.Region)
	if buyWorkerCount, err := h.CheckAndAddWorker(c.ctx,
		c.ctx.ClusterGroup.Zone,
		count,
		changeConf,
		force,
		label,
		diskSize,
		diskType,
		constants.TKE_WORKER_NODE_LABEL_VAL, nil, "", constants.TKE_NODE_SYSTEM_DISK_SIZE); err != nil {
		return nil, err
	} else if buyWorkerCount > 0 {
		c.ctx.SetReturnParam(constants.FlowParamNeedCvmCount, fmt.Sprintf("%d", count-buyWorkerCount))
		return nil, errors.New("wait worker running")
	}

	if allNodeRunning, err := h.TryMakeAllNodesRunning(c.ctx); err != nil {
		return nil, err
	} else if !allNodeRunning {
		return nil, errors.New("wait all node running")
	}
	return nil, nil
}

type uniformClusterCreateCountPooledWorkerApp struct {
	ctx *deploy.Context
	apps.ApplyApp

	zone string
}

func (c *uniformClusterCreateCountPooledWorkerApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	cg := c.ctx.ClusterGroup
	c.zone, _ = c.ctx.GetParam(constants.FLOW_PARAM_ZONE, cg.Zone)
	if c.zone == "" {
		return nil, fmt.Errorf("zone is empty")
	}

	if allNodeRunning, err := tryMakeAllPoolNodesRunning(c.ctx, c.zone); err != nil {
		return nil, err
	} else if !allNodeRunning {
		return nil, errors.New("wait all node running")
	}

	count, exist, err := c.ctx.GetParamInt(constants.FlowParamNeedCvmCount, 0)
	if err != nil {
		return
	}
	if !exist {
		return nil, fmt.Errorf("%s Param not exist", constants.FlowParamNeedCvmCount)
	}

	c.ctx.SetReturnParam(constants.FlowParamNeedCvmCount, fmt.Sprintf("%d", count))

	err = checkAndAddPoolWorker(c.ctx, c.zone, count)
	if err != nil {
		return
	}

	c.ctx.SetReturnParam(constants.FlowParamNeedCvmCount, fmt.Sprintf("%d", 0))

	if allNodeRunning, err := tryMakeAllPoolNodesRunning(c.ctx, c.zone); err != nil {
		return nil, err
	} else if !allNodeRunning {
		return nil, errors.New("wait all node running")
	}
	return nil, nil
}
