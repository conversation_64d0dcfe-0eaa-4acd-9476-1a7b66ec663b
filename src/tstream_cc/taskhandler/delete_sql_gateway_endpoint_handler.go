package taskHandler

import (
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	constants2 "tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	common "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	flow2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/flow"
)

type DeleteSqlGatewayEndpointHandler struct {
}

func (h *DeleteSqlGatewayEndpointHandler) CompleteTask(req *flow.TaskExecRequest) (resp *flow.TaskExecResponse) {
	flowService := flow2.GetFlowService()
	requestId, _, _ := flowService.GetFlowParamString(req.Params, constants2.FLOW_PARAM_REQUEST_ID, "")
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf("[%s] DeleteSqlGateway Endpoint task failed,error: %v", requestId, err)
			resp = flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1, fmt.Sprintf("%v", err), req.Params)
		}
	}()
	err := h.handle(req)
	if err != nil {
		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1, fmt.Sprintf("%v", err), req.Params)
	}
	return flow.NewTaskExecResponseWithParams(flow.TASK_SUCCESS, 1.0, "ok", req.Params)
}

func (h *DeleteSqlGatewayEndpointHandler) handle(req *flow.TaskExecRequest) (err error) {
	flowService := flow2.GetFlowService()
	requestId, _, err := flowService.GetFlowParamString(req.Params, constants2.FLOW_PARAM_REQUEST_ID, constants2.EMPTY)
	if err != nil {
		return err
	}
	gatewaySerialId, _, err := flowService.GetFlowParamString(req.Params, constants2.FLOW_PARAM_GATEWAY_SERIAL_ID, constants2.EMPTY)
	if err != nil {
		return err
	}
	sqlGatewayExists, gateway, err := service2.GetSqlGatewayBySerialId(gatewaySerialId)
	if err != nil {
		logger.Errorf("[%s] Failed to GetSqlGateway,error: %+v", requestId, err)
		return err
	}
	if sqlGatewayExists {
		err := service2.SwitchSqlGatewayStatus(gateway.SerialId, constants2.SqlGatewayStopped)
		if err != nil {
			logger.Errorf("[%s] Failed to SwitchSqlGatewayStatus,error: %+v", requestId, err)
			return err
		}
		err = common.UpdateSqlGatewayStopTime(gateway.Id, util.GetCurrentTime())
		if err != nil {
			logger.Errorf("[%s] Failed to UpdateSqlGatewayStopTime,error: %+v", requestId, err)
			panic(errorcode.NewStackError(errorcode.InternalErrorCode, "", err))
		}
	}
	err = service2.RecordEKSResource(gateway.ClusterId, int32(gateway.AppId), gateway.Region, gateway.OwnerUin)
	if err != nil {
		panic(errorcode.NewStackError(errorcode.InternalErrorCode, "RecordEKSResource failed", err))
	}

	return nil
}
