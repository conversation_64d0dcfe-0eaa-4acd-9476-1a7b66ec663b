package taskHandler

import (
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	constants2 "tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	common "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	flow2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/flow"
)

type DeploySqlGatewayPointHandler struct {
}

func (this *DeploySqlGatewayPointHandler) CompleteTask(request *flow.TaskExecRequest) (resp *flow.TaskExecResponse) {
	flowService := flow2.GetFlowService()
	params := request.Params
	requestId, _, _ := flowService.GetFlowParamString(params, constants2.FLOW_PARAM_REQUEST_ID, constants2.EMPTY)
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf("[%s] CreateSqlGateway task failed because: %v", requestId, err)
			resp = flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1, fmt.Sprintf("%v", err), params)
		}
	}()
	serialId, _, _ := flowService.GetFlowParamString(params, constants2.FLOW_PARAM_GATEWAY_SERIAL_ID, constants2.EMPTY)
	err := service.SwitchSqlGatewayStatus(serialId, constants2.SqlGatewayRunning)
	if err != nil {
		panic(errorcode.NewStackError(errorcode.InternalErrorCode, "", err))
	}
	_, gateway, err := service.GetSqlGatewayBySerialId(serialId)
	if err != nil {
		logger.Errorf("[%s] Failed to GetSqlGateway,error: %+v", requestId, err)
		panic(errorcode.NewStackError(errorcode.InternalErrorCode, "", err))
	}
	err = common.UpdateSqlGatewayStartTime(gateway.Id)
	if err != nil {
		logger.Errorf("[%s] Failed to UpdateSqlGatewayStartTime,error: %+v", requestId, err)
		panic(errorcode.NewStackError(errorcode.InternalErrorCode, "", err))
	}

	err = service.RecordEKSResource(gateway.ClusterId, int32(gateway.AppId), gateway.Region, gateway.OwnerUin)
	if err != nil {
		panic(errorcode.NewStackError(errorcode.InternalErrorCode, "RecordEKSResource failed", err))
	}
	return flow.NewTaskExecResponseWithParams(flow.TASK_SUCCESS, 1.0, "ok", request.Params)
}
