package taskHandler

import (
	sdkErrors "errors"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/dynamic"
	"sigs.k8s.io/yaml"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type RestartSetats struct {
}

func (c RestartSetats) Apps(ctx *deploy.Context) ([]apps.App, error) {

	return []apps.App{
		&restartSetats{ctx: ctx},
	}, nil
}

type restartSetats struct {
	ctx *deploy.Context
	apps.ApplyReadyApp
}

func (c *restartSetats) Ready(s apps.Client, v interface{}) (bool, error) {
	return true, nil
}

func (c *restartSetats) Params() (interface{}, error) {

	count, _setats, err := service.GetSetatsByClusterGroupSerialId(c.ctx.ClusterGroup.SerialId)
	if err != nil {
		logger.Errorf("Failed to get setats by ClusterGroupSerialId id:%s, with errors:%+v", c.ctx.ClusterGroup.SerialId, err)
		return nil, errorcode.FailedOperationCode.NewWithErr(err)
	}
	if count == 0 {
		logger.Errorf("Failed to get setats by ClusterGroupSerialId id:%s, with count 0", c.ctx.ClusterGroup.SerialId)
		return nil, sdkErrors.New("setats size is 0")
	}

	appContainerImage, err := c.ctx.CC().ImageRegistry().Setats()
	if err != nil {
		return nil, err
	}

	params := make(map[string]interface{})

	params["AppImage"] = appContainerImage
	params["MasterCpu"] = _setats.MasterCpu - 1
	params["WorkerCpu"] = _setats.WorkerCpu - 1
	params["Zookeeper"] = deploy.GetZooKeeperServers(constants.ZooKeeperComponentReplicaNumber)
	params["WorkerReplicas"] = _setats.WorkerDefaultParallelism
	params["WorkerStorageRequest"] = "140Gi"
	params["PvPath"] = "/var/lib/docker/oceanus/setats"
	params["PvStorageRequest"] = "140Gi"

	return params, nil
}

func (c *restartSetats) Apply(s apps.Client, v interface{}) (interface{}, error) {
	config, err := k8s.GetK8sService().NewConfig(c.ctx.KubeConfig())
	dynamicClient, err := dynamic.NewForConfig(config)
	if err != nil {
		logger.Errorf("apply setats new dynamic client error: %s", err.Error())
		return nil, err
	}

	params, err := c.Params()
	if err != nil {
		logger.Errorf("apply setats get params error: %s", err.Error())
		return nil, err
	}
	setatsYamlFile, err := c.ctx.FlowCC.SetatsCC().Setats(params)
	if err != nil {
		logger.Errorf("apply setats get yaml error: %s", err.Error())
		return nil, err
	}
	var obj unstructured.Unstructured
	if err := yaml.Unmarshal([]byte(setatsYamlFile), &obj); err != nil {
		logger.Errorf("apply setats unmarshal error: %s", err.Error())
		return nil, err
	}
	gvr := schema.GroupVersionResource{
		Group:    obj.GroupVersionKind().Group,
		Version:  obj.GroupVersionKind().Version,
		Resource: strings.ToLower(obj.GetKind()), // 复数化（如 Deployment → deployments）
	}
	namespace := obj.GetNamespace() // 从 YAML 中获取命名空间
	_, err = dynamicClient.Resource(gvr).Namespace(namespace).Create(&obj, metav1.CreateOptions{})
	if err != nil && !errors.IsAlreadyExists(err) {
		logger.Errorf("apply setats error: %s", err.Error())
		return nil, err
	}
	if errors.IsAlreadyExists(err) {
		// 若资源已存在，执行更新
		existingObj, _ := dynamicClient.Resource(gvr).Namespace(namespace).Get(obj.GetName(), metav1.GetOptions{})
		obj.SetResourceVersion(existingObj.GetResourceVersion())
		dynamicClient.Resource(gvr).Namespace(namespace).Update(&obj, metav1.UpdateOptions{})
	}
	logger.Infof("apply setats success")
	return nil, nil
}
