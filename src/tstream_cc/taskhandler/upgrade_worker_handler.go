package taskHandler

import (
	"encoding/json"
	"fmt"
	"sort"
	"strings"
	"sync"

	cvm2 "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/cvm/v20170312"
	tkesdk "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/tke/v20180525"
	v12 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/fields"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	table3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/Cvm"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cbs"
	clusterService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cvm"
	flow2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/flow"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/time_profile"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/tke"
)

type upgradeWorkerHandler struct {
}

func (h *upgradeWorkerHandler) HandleTaskStatusInit(requestId string, request *flow.TaskExecRequest) (resp *flow.TaskExecResponse) {
	flowService := flow2.GetFlowService()
	s, err := NewWorkerService(requestId, request)
	if err != nil {
		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_RUNNING, 0.1,
			fmt.Sprintf("%v", err), request.Params)
	}

	if err = s.Init(); err != nil {
		return s.RetryRsp(fmt.Sprintf("%v", err))
	}

	return s.upgrade()
}

func (h *upgradeWorkerHandler) HandleTaskStatusRunning(requestId string, request *flow.TaskExecRequest) (resp *flow.TaskExecResponse) {
	flowService := flow2.GetFlowService()
	s, err := NewWorkerService(requestId, request)
	if err != nil {
		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_RUNNING, 0.1,
			fmt.Sprintf("%v", err), request.Params)
	}

	if err = s.Init(); err != nil {
		return s.RetryRsp(fmt.Sprintf("%v", err))
	}

	return s.checkUpgraded()
}

type upgradeWorkerService struct {
	requestId string
	request   *flow.TaskExecRequest

	*flow2.ClusterFlowService

	*ApplyTKEHandler
}

func NewWorkerService(requestId string, request *flow.TaskExecRequest) (s *upgradeWorkerService, err error) {
	flowService, err := flow2.NewClusterFlowService(request)
	if err != nil {
		return nil, err
	}

	s = &upgradeWorkerService{requestId: requestId, request: request, ClusterFlowService: flowService,
		ApplyTKEHandler: &ApplyTKEHandler{}}

	if err != nil {
		return nil, err
	}
	if flowService.Tke == nil {
		return nil, errorcode.InternalErrorCode.NewWithMsg("No tke instances")
	}
	return
}

func (s *upgradeWorkerService) Init() (err error) {
	logger.Infof("init begin...")
	if err = s.getFromParam(); err != nil {
		return
	}

	running, err := IsClusterRunning(s.ClusterGroup, s.Cluster, s.Tke)
	if err != nil {
		return err
	}
	logger.Infof("cluster is running: %+v", running)
	if !running {
		return errorcode.InternalErrorCode.NewWithInfo(fmt.Sprintf("tke not running"), nil)
	}

	return nil
}

func (s *upgradeWorkerService) getFromParam() (err error) {

	return
}

func (s *upgradeWorkerService) upgrade() (rsp *flow.TaskExecResponse) {
	logger.Infof("upgrade: %v", s.request)
	_, exist := s.GetParam(constants.FLOW_PARAM_UPGRADE_WORKER_ADDED, "")
	if exist {
		return s.DoneRsp("upgrade worker, node added")
	}
	upgradeInstance, err := s.getWorkerToUpgrade()
	if err != nil {
		return s.RetryRspWithErr(err)
	}
	if upgradeInstance == nil {
		s.SetReturnParam(constants.FLOW_PARAM_UPGRADE_WORKER_FINISHED, "1")
		return s.DoneRsp("All Worker is upgraded or no free")
	}
	s.SetReturnParam(constants.FLOW_PARAM_UPGRADE_WORKER_INSTANCE, *upgradeInstance.InstanceId)
	logger.Infof("upgrade instance %+v", upgradeInstance)
	workerSpec, err := s.GetNewWorkerSpec()
	if err != nil {
		return s.RetryRspWithErr(err)
	}
	logger.Infof("upgrade worker spec: %s", workerSpec)
	shouldUpgrade, err := s.shouldUpgradeWorker(upgradeInstance, workerSpec)
	if err != nil {
		return s.RetryRspWithErr(err)
	}
	if !shouldUpgrade {
		s.SetReturnParam(constants.FLOW_PARAM_UPGRADE_WORKER_FINISHED, "1")
		return s.DoneRsp("no cheaper cvm upgraded")
	}
	err = s.AddNewWorker(workerSpec, 1, true)
	if err != nil {
		return s.RetryRspWithErr(err)
	}
	s.SetReturnParam(constants.FLOW_PARAM_UPGRADE_WORKER_ADDED, "1")
	return s.DoneRsp("upgrade worker, node added")
}

func (s *upgradeWorkerService) shouldUpgradeWorker(instance *cvm2.Instance, newWorkerSpec string) (shouldUpgrade bool, err error) {
	if constants.TKE_WORKER_NODE_INSTANCE_TYPE == newWorkerSpec {
		return true, nil
	}
	return false, nil
}

func (s *upgradeWorkerService) AddNewWorker(workerSpec string, workerNum int64, unschedule bool) (err error) {
	tkeService := tke.GetTkeService()
	period, err := s.CC().FlowCC().PrepaidPeriod()
	if err != nil {
		return errorcode.InternalErrorCode.NewWithErr(err)
	}
	cc, err := s.CC().FlowCC().TkeCC().ClusterConfig(s.ClusterGroup)
	if err != nil {
		return errorcode.InternalErrorCode.NewWithErr(err)
	}

	logger.Info("tke cc %v", cc)
	workerRequest := tkeService.NewDefaultWorkerNodeRunInstancesRequestBuilder().
		WithInstanceType(workerSpec).
		WithInstanceChargePrepaid(period, constants.TKE_CVM_RENEW_FLAG).
		WithPlacement(s.ClusterGroup.Zone, constants.TKE_PROJECT_ID).
		WithVirtualPrivateCloud(s.Cluster.VpcId, s.Cluster.SubnetId).
		WithInstanceCount(workerNum).
		WithSecurityGroup(cc.SecurityGroup).
		WithSystemDisk(constants.TKE_CVM_DISK_TYPE, constants.TKE_NODE_SYSTEM_DISK_SIZE).
		Build()

	isV1 := s.Tke.ArchGeneration >= constants.TKE_ARCH_GENERATION_V6
	mountTarget := constants.TKE_DATA_DISK_MOUNT_TARGET_FOR_CONTAINERD
	if !isV1 {
		mountTarget = constants.TKE_DATA_DISK_MOUNT_TARGET
	}
	requestBuilder := tkeService.NewDefaultCreateClusterInstancesRequestBuilder().
		WithClusterId(s.Tke.InstanceId).
		WithRunInstancePara(workerRequest).
		WithInstanceAdvancedSettings(constants.TKE_DATA_DISK_AUTO_FORMAT_AND_MOUNT,
			constants.TKE_DATA_DISK_FILE_SYSTEM, mountTarget, constants.TKE_CVM_DISK_TYPE,
			constants.TKE_WORKER_NODE_DISK_SIZE, map[string]string{constants.TKE_CVM_LABEL_KEY: constants.TKE_WORKER_NODE_LABEL_VAL}, cc.UserScript)

	if unschedule {
		requestBuilder.WithUnschedule()
	}

	request := requestBuilder.Build()

	err = tkeService.CreateClusterInstancesWithScsAccount(s.ClusterGroup.Region, request)
	if err != nil {
		return
	}
	return nil
}

func (s *upgradeWorkerService) GetNewWorkerSpec() (workerSpec string, err error) {
	cc, err := s.CC().FlowCC().TkeCC().ClusterConfig(s.ClusterGroup)
	if err != nil {
		return "", errorcode.InternalErrorCode.NewWithErr(err)
	}

	workerSpec = cvm.GetCvmService().GetWorkerSpec(s.Cluster, cc)

	cvmConfList, err := service3.GetTableService().ListCvmSaleConfByInstanceType(workerSpec, 0, 1)
	if err != nil {
		return "", errorcode.InternalErrorCode.NewWithErr(err)
	}
	if len(cvmConfList) == 0 {
		msg := fmt.Sprintf("cvm spec %s not found in table CvmSaleConf", workerSpec)
		return "", errorcode.InternalErrorCode.NewWithInfo(msg, nil)
	}
	instanceTypes := make(map[string]struct{}, 0)
	workerSpec, _, _, err = CheckCvmQuotaMaySwitchType(s.ClusterGroup.Zone, s.ClusterGroup, s.Cluster, cvmConfList[0], false, 1, true, instanceTypes, 0)
	if err != nil {
		return "", errorcode.InternalErrorCode.NewWithErr(err)
	}
	return workerSpec, nil
}

func (s *upgradeWorkerService) getFreeWorkers() (freeWorkers []string, err error) {
	k8sService := k8s.GetK8sService()
	kubConfig := []byte(s.Cluster.KubeConfig)
	client, err := k8sService.NewClient(kubConfig)
	if err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}

	timeProfile := time_profile.NewTimeProfile(s.Cluster.UniqClusterId)
	nodeList, err := k8sService.ListNode(client, metav1.ListOptions{
		LabelSelector: fmt.Sprintf("%s==%s", constants.TKE_CVM_LABEL_KEY, constants.TKE_WORKER_NODE_LABEL_VAL),
	})
	logger.Debugf("ListNode cost: %s", timeProfile.Cost())
	if err != nil {
		return
	}

	freeWorkers = make([]string, 0, len(nodeList.Items))
	const batchSize = 4
	namespace := clusterService.GetDefaultNamespace(s.ClusterGroup)
	wg := &sync.WaitGroup{}
	nodeInfoChn := make(chan interface{}, len(nodeList.Items))
	for i := 0; i < len(nodeList.Items); i += batchSize {
		end := i + batchSize
		if end > len(nodeList.Items) {
			end = len(nodeList.Items)
		}

		wg.Add(1)
		go func(i int) {
			timeProfile := time_profile.NewTimeProfile(s.Cluster.UniqClusterId)
			defer func() {
				wg.Done()
				logger.Debugf("ListPod & parse cost: %s", timeProfile.Cost())
			}()

			client, err := k8sService.NewClient(kubConfig)
			if err != nil {
				nodeInfoChn <- errorcode.InternalErrorCode.NewWithErr(err)
				return
			}

			for _, node := range nodeList.Items[i:end] {
				// TODO: 调整default ns
				podList, err := k8sService.ListPod(client, namespace, metav1.ListOptions{
					FieldSelector: fmt.Sprintf("spec.nodeName=%s", node.Name),
				})
				if err != nil {
					nodeInfoChn <- err
					break
				}
				nodeInfoChn <- s.parseNode(&node, podList)
			}
		}(i)
	}
	wg.Wait()
	close(nodeInfoChn)

	for nodeInfo := range nodeInfoChn {
		if err, yes := nodeInfo.(error); yes {
			return nil, err
		}
		nodeName := nodeInfo.(string)
		if !strings.HasPrefix(nodeName, "cql-") {
			freeWorkers = append(freeWorkers, nodeName)
		}
	}
	logger.Debugf("free workers: %+v", freeWorkers)
	return
}

func (s *upgradeWorkerService) parseNode(node *v12.Node, podList *v12.PodList) (nodeName string) {
	logger.Debugf("node: %s, podNum: %d", node.Name, len(podList.Items))

	for _, pod := range podList.Items {
		if strings.HasPrefix(pod.Name, "cql-") {
			return "cql-"
		}
	}
	return node.Name
}

func (s *upgradeWorkerService) ListUnschedulerWorker() (unschedulerIdSet []*string, err error) {
	k8sService := k8s.GetK8sService()
	kubConfig := []byte(s.Cluster.KubeConfig)
	client, err := k8sService.NewClient(kubConfig)
	if err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}
	fSet := fields.Set{"spec.unschedulable": "true"}
	nodeList, err := k8sService.ListNode(client,
		metav1.ListOptions{FieldSelector: fields.SelectorFromSet(fSet).String()})
	if err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}
	unschedulerIdSet = make([]*string, 0)
	for _, node := range nodeList.Items {
		labelMap := node.GetObjectMeta().GetLabels()
		if instanceId, ok := labelMap["cloud.tencent.com/node-instance-id"]; ok {
			unschedulerIdSet = append(unschedulerIdSet, &instanceId)
		} else {
			msg := fmt.Sprintf("not find instance id for %s", s.ClusterGroup.SerialId)
			return nil, errorcode.InternalErrorCode.NewWithMsg(msg)
		}
	}
	return unschedulerIdSet, nil
}

func (s *upgradeWorkerService) ComputeStandardWorkerNum() (needNum int64, unStandardCvmIdSet []*string, err error) {
	region := s.ClusterGroupService.GetClusterGroup().Region
	workerNodeNum, _, _, err := s.ComputeWorkerNodeAndCuNum(s.ClusterGroup.CuNum, nil)
	if err != nil {
		return 0, unStandardCvmIdSet, err
	}
	k8sService := k8s.GetK8sService()
	kubConfig := []byte(s.Cluster.KubeConfig)
	client, err := k8sService.NewClient(kubConfig)
	workerList, err := k8sService.ListNode(client, metav1.ListOptions{
		LabelSelector: fmt.Sprintf("%s==%s", constants.TKE_CVM_LABEL_KEY, constants.TKE_WORKER_NODE_LABEL_VAL),
	})
	if err != nil {
		return
	}
	instanceIdSet := make([]*string, 0)
	for _, node := range workerList.Items {
		labelMap := node.GetObjectMeta().GetLabels()
		if instanceId, ok := labelMap["cloud.tencent.com/node-instance-id"]; ok {
			instanceIdSet = append(instanceIdSet, &instanceId)
		} else {
			msg := fmt.Sprintf("not find instance id for %s", s.ClusterGroup.SerialId)
			return 0, unStandardCvmIdSet, errorcode.InternalErrorCode.NewWithMsg(msg)
		}
	}

	b, _ := json.Marshal(instanceIdSet)
	logger.Infof("ComputeStandardWorkerNum, all instance count :%d, instance id: %s", len(instanceIdSet), string(b))

	cvmService := cvm.GetCvmService()
	cvmInstanceSet, err := cvmService.DescribeInstancesWithScsAccount(s.ClusterGroup.NetEnvironmentType, region, instanceIdSet)
	if err != nil || len(cvmInstanceSet) != len(instanceIdSet) {
		msg := fmt.Sprintf("%s upgrade Worker DescribeInstancesWithScsAccount Error, expect %d, but %d",
			s.requestId, len(instanceIdSet), len(cvmInstanceSet))
		return 0, unStandardCvmIdSet, errorcode.InternalErrorCode.NewWithInfo(msg, err)
	}
	standardCvmCount := int64(0)
	unStandardCvmIdSet = make([]*string, 0)
	for _, cvmInstance := range cvmInstanceSet {
		diskSize := int64(0)
		for _, disk := range cvmInstance.DataDisks {
			if *disk.DiskSize > diskSize {
				diskSize = *disk.DiskSize
			}
		}
		newDiskSize := int64(constants.TKE_WORKER_NODE_DISK_SIZE)
		newInstanceType := constants.TKE_WORKER_NODE_INSTANCE_TYPE
		if newInstanceType == *cvmInstance.InstanceType && newDiskSize == diskSize {
			standardCvmCount += 1
		} else {
			unStandardCvmIdSet = append(unStandardCvmIdSet, cvmInstance.InstanceId)
		}
	}
	return int64(workerNodeNum) - standardCvmCount, unStandardCvmIdSet, nil
}

func (s *upgradeWorkerService) getWorkerToUpgrade() (instance *cvm2.Instance, err error) {
	instanceId, exist := s.GetParam(constants.FLOW_PARAM_UPGRADE_WORKER_INSTANCE, "")
	if exist {
		cvmService := cvm.GetCvmService()
		instanceIdSet := make([]*string, 0)
		instanceIdSet = append(instanceIdSet, &instanceId)
		cvmInstanceSet, err := cvmService.DescribeInstancesWithScsAccount(s.ClusterGroup.NetEnvironmentType, s.ClusterGroup.Region, instanceIdSet)
		if err != nil || len(cvmInstanceSet) != 1 {
			msg := fmt.Sprintf("cvm [%s] can not found, or err occur", instanceId)
			return nil, errorcode.InternalErrorCode.NewWithInfo(msg, err)
		}
		return cvmInstanceSet[0], nil
	}

	freeWorkers, err := s.getFreeWorkers()
	if err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}

	cg := s.ClusterGroupService.GetClusterGroup()
	region := s.ClusterGroupService.GetClusterGroup().Region
	tkeService := tke.GetTkeService()

	_, allInstanceSet, err := tkeService.DescribeClusterAllInstancesWithScsAccountByNetEnvironmentType(cg.NetEnvironmentType, region,
		s.Tke.InstanceId)
	if err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}

	ipInstanceMap := make(map[string]*string, 0)

	for _, instance := range allInstanceSet {
		for _, label := range instance.InstanceAdvancedSettings.Labels {
			if *label.Name == constants.TKE_CVM_LABEL_KEY && *label.Value == constants.TKE_WORKER_NODE_LABEL_VAL {
				ipInstanceMap[*instance.LanIP] = instance.InstanceId
			}
		}
	}
	instanceIdList := make([]*string, 0)
	for _, nodeIp := range freeWorkers {
		instanceId, ok := ipInstanceMap[nodeIp]
		if ok {
			instanceIdList = append(instanceIdList, instanceId)
		}
	}

	if len(instanceIdList) == 0 {
		return nil, nil
	}

	b, _ := json.Marshal(instanceIdList)
	logger.Infof("workerInstanceSet: %s", string(b))

	workerNodeNum, _, _, err := s.ComputeWorkerNodeAndCuNum(s.ClusterGroup.CuNum, nil)
	if err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}

	// worker 节点不匹配，手动介入
	if int(workerNodeNum) != len(ipInstanceMap) {
		msg := fmt.Sprintf("[%s] [%s] upgrade worker, "+
			"tke [%s] worker node expect %d, but has %d", s.requestId,
			s.ClusterGroup.SerialId, s.Tke.InstanceId, workerNodeNum, len(ipInstanceMap))
		return nil, errorcode.InternalErrorCode.NewWithInfo(msg, err)
	}

	instance, err = s.orderWorkerToUpgrade(s.ClusterGroupService.GetClusterGroup(), instanceIdList, true)
	if err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}
	if instance != nil {
		s.SetReturnParamUseJson(constants.FLOW_PARAM_UPGRADE_WORKER_ALL_INSTANCE, ipInstanceMap)
	}
	return instance, nil
}

func (s *upgradeWorkerService) orderWorkerToUpgrade(cg *table.ClusterGroup, instanceIdSet []*string, isWorker bool) (instance *cvm2.Instance, err error) {
	cvmService := cvm.GetCvmService()
	cvmInstanceSet, err := cvmService.DescribeInstancesWithScsAccount(cg.NetEnvironmentType, cg.Region, instanceIdSet)
	if err != nil || len(cvmInstanceSet) != len(instanceIdSet) {
		msg := fmt.Sprintf("%s upgrade Worker DescribeInstancesWithScsAccount Error, expect %d, but %d",
			s.requestId, len(instanceIdSet), len(cvmInstanceSet))
		return nil, errorcode.InternalErrorCode.NewWithInfo(msg, err)
	}
	cvmInstanceMap := make(map[string]*cvm2.Instance, len(instanceIdSet))
	cvmInstanceDiskMap := make(map[string]int64, len(instanceIdSet))
	for _, cvmInstance := range cvmInstanceSet {
		cvmInstanceMap[*cvmInstance.InstanceId] = cvmInstance
		for _, disk := range cvmInstance.DataDisks {
			diskSize := cvmInstanceDiskMap[*cvmInstance.InstanceId]
			if *disk.DiskSize > diskSize {
				cvmInstanceDiskMap[*cvmInstance.InstanceId] = *disk.DiskSize
			}
		}
	}

	cvmSaleConfList, err := service3.GetTableService().ListActiveCvmSaleConf()
	cvmConfMap := make(map[string]*table3.CvmSaleConfig, len(cvmSaleConfList))
	for _, config := range cvmSaleConfList {
		cvmConfMap[config.InstanceType] = config
	}

	tkeCC, err := s.CC().FlowCC().TkeCC().ClusterConfig(cg)
	if err != nil {
		return nil, errorcode.InternalErrorCode.NewWithInfo("scale own Get Cluster Config Error", err)
	}
	cvmConfList := make([]*table3.CvmSaleConfig, 0)
	newDiskSize := int64(constants.TKE_CONTROL_NODE_DISK_SIZE)
	newInstanceType := constants.TKE_CONTROL_NODE_INSTANCE_TYPE
	if isWorker {

		workerSpec := cvm.GetCvmService().GetWorkerSpec(s.Cluster, tkeCC)

		cvmConfList, err = service3.GetTableService().ListCvmSaleConfByInstanceType(workerSpec, 0, 1)
		newDiskSize = int64(constants.TKE_WORKER_NODE_DISK_SIZE)
		newInstanceType = constants.TKE_WORKER_NODE_INSTANCE_TYPE
	} else {
		cvmConfList, err = service3.GetTableService().ListCvmSaleConfByInstanceType(tkeCC.ControllerSpec, 0, 1)
	}
	if err != nil || len(cvmConfList) == 0 {
		return nil, errorcode.InternalErrorCode.NewWithInfo("scale down ListCvmSaleConfByInstanceType Error, length = 0", err)
	}
	newCvmConf := cvmConfList[0]

	sort.Slice(instanceIdSet, func(i, j int) bool {
		iInstanceId := instanceIdSet[i]
		jInstanceId := instanceIdSet[j]
		iInstance, ok := cvmInstanceMap[*iInstanceId]
		if !ok {
			return true
		}
		jInstance, ok := cvmInstanceMap[*jInstanceId]
		if !ok {
			return false
		}
		iDiskSize, ok := cvmInstanceDiskMap[*iInstanceId]
		if !ok {
			return true
		}
		jDiskSize, ok := cvmInstanceDiskMap[*jInstanceId]
		if !ok {
			return false
		}
		iConfig, ok := cvmConfMap[*iInstance.InstanceType]
		if !ok {
			return true
		}
		jConfig, ok := cvmConfMap[*jInstance.InstanceType]
		if !ok {
			return false
		}
		if iConfig.Cpu > newCvmConf.Cpu {
			return true
		} else if iConfig.Cpu < newCvmConf.Cpu {
			return false
		}
		if jConfig.Cpu > newCvmConf.Cpu {
			return false
		} else if jConfig.Cpu < newCvmConf.Cpu {
			return true
		}
		if iConfig.Memory > newCvmConf.Memory {
			return true
		} else if iConfig.Memory < newCvmConf.Memory {
			return false
		}
		if jConfig.Memory > newCvmConf.Memory {
			return false
		} else if jConfig.Memory < newCvmConf.Memory {
			return true
		}
		if iDiskSize > newDiskSize {
			return true
		} else if iDiskSize < newDiskSize {
			return false
		}
		if jDiskSize > newDiskSize {
			return false
		} else if jDiskSize < newDiskSize {
			return true
		}
		if iConfig.Price > jConfig.Price {
			return true
		} else if iConfig.Price < jConfig.Price {
			return false
		}
		if iConfig.Generation < jConfig.Generation {
			return true
		} else if iConfig.Generation > jConfig.Generation {
			return false
		}
		return true
	})

	b, _ := json.Marshal(instanceIdSet)
	logger.Infof("Sort instance: %s", string(b))

	upgradeInstanceId := instanceIdSet[0]
	upgradeCvmInstance := cvmInstanceMap[*upgradeInstanceId]

	if upgradeCvmInstance == nil {
		return nil, errorcode.InternalErrorCode.NewWithMsg("Can not get upgrade worker instance")
	}
	upgradeCvmDiskSize := cvmInstanceDiskMap[*upgradeInstanceId]

	if *upgradeCvmInstance.InstanceType == newInstanceType && upgradeCvmDiskSize == newDiskSize {
		return nil, nil
	}
	return cvmInstanceMap[*upgradeInstanceId], nil
}

func (s *upgradeWorkerService) checkUpgraded() (rsp *flow.TaskExecResponse) {
	logger.Infof("check upgrade: %v", s.request)
	_, exist := s.GetParam(constants.FLOW_PARAM_UPGRADE_WORKER_FINISHED, "")
	if exist {
		return s.DoneRsp("Stop upgrade worker")
	}
	_, exist = s.GetParam(constants.FLOW_PARAM_UPGRADE_WORKER_DELETE, "")
	logger.Infof("worker has deleted: %+v", exist)
	if !exist {
		running, err := s.isNewWorkerRunning()
		if err != nil {
			return s.RetryRspWithErr(err)
		}
		logger.Infof("new worker is running: %+v", running)
		if !running {
			return s.RetryRsp("new worker is not running")
		}
		instanceId, _ := s.GetParam(constants.FLOW_PARAM_UPGRADE_WORKER_INSTANCE, "")
		err = s.ScheduleWorker(instanceId, true)
		if err != nil {
			return s.RetryRspWithErr(err)
		}
		logger.Infof("unschedule worker: %s", instanceId)
		containJob, err := s.isWorkerContainJobs()
		if err != nil {
			return s.RetryRspWithErr(err)
		}
		logger.Infof("worker is contain jobs: %+v", containJob)
		deleteInstanceId := ""
		scheduleInstanceId := ""
		if containJob { //有job运行
			deleteInstanceId, _ = s.GetParam(constants.FLOW_PARAM_UPGRADE_WORKER_NEW_INSTANCE, "")
			scheduleInstanceId, _ = s.GetParam(constants.FLOW_PARAM_UPGRADE_WORKER_INSTANCE, "")
		} else {
			deleteInstanceId, _ = s.GetParam(constants.FLOW_PARAM_UPGRADE_WORKER_INSTANCE, "")
			scheduleInstanceId, _ = s.GetParam(constants.FLOW_PARAM_UPGRADE_WORKER_NEW_INSTANCE, "")
		}
		logger.Infof("begin to schedule instance: %s", scheduleInstanceId)
		err = s.ScheduleWorker(scheduleInstanceId, false)
		if err != nil {
			return s.RetryRspWithErr(err)
		}
		logger.Infof("begin to delete worker: %s", deleteInstanceId)
		err = s.deleteWorkerFromTke(deleteInstanceId)
		if err != nil {
			return s.RetryRspWithErr(err)
		} else {
			return s.RetryRsp("delete worker from tke")
		}
	}
	err := s.terminateWorker()
	if err != nil {
		return s.RetryRspWithErr(err)
	}

	return s.DoneRsp("check worker down")
}

func (s *upgradeWorkerService) isNewWorkerRunning() (running bool, err error) {
	allInstanceStr, _ := s.GetParam(constants.FLOW_PARAM_UPGRADE_WORKER_ALL_INSTANCE, "")
	beforeInstances := make(map[string]*string, 0)

	logger.Infof("before instance: %s", allInstanceStr)

	err = json.Unmarshal([]byte(allInstanceStr), &beforeInstances)
	if err != nil {
		return false, errorcode.InternalErrorCode.NewWithErr(err)
	}
	cg := s.ClusterGroupService.GetClusterGroup()
	region := s.ClusterGroupService.GetClusterGroup().Region
	tkeService := tke.GetTkeService()
	_, newInstances, err := tkeService.DescribeClusterAllInstancesWithScsAccountByNetEnvironmentType(cg.NetEnvironmentType, region,
		s.Tke.InstanceId)
	if err != nil {
		return false, errorcode.InternalErrorCode.NewWithErr(err)
	}
	workerInstanceMap := make(map[string]*tkesdk.Instance, 0)
	for _, instance := range newInstances {
		for _, label := range instance.InstanceAdvancedSettings.Labels {
			if *label.Name == constants.TKE_CVM_LABEL_KEY && *label.Value == constants.TKE_WORKER_NODE_LABEL_VAL {
				if *instance.InstanceState != constants.TKE_INSTANCE_STATE_RUNNING {
					return false, nil
				}
				workerInstanceMap[*instance.LanIP] = instance
			}
		}
	}

	if len(beforeInstances)+1 != len(workerInstanceMap) {
		return false, nil
	}
	for _, instance := range workerInstanceMap {
		_, ok := beforeInstances[*instance.LanIP]
		if !ok {
			if *instance.InstanceState == constants.TKE_INSTANCE_STATE_RUNNING {
				logger.Infof("upgrade worker new instance: %s", *instance.InstanceId)
				s.SetReturnParam(constants.FLOW_PARAM_UPGRADE_WORKER_NEW_INSTANCE, *instance.InstanceId)
				return true, nil
			}
		}
	}
	return false, nil
}

func (s *upgradeWorkerService) terminateWorker() (err error) {
	instanceId, exist := s.GetParam(constants.FLOW_PARAM_UPGRADE_WORKER_DELETE, "")
	if !exist {
		return errorcode.InternalErrorCode.NewWithMsg("delete worker is not exist")
	}

	instanceIdSet := make([]*string, 0)
	instanceIdSet = append(instanceIdSet, &instanceId)

	cvmService := cvm.GetCvmService()
	cvmInstanceSet, err := cvmService.DescribeInstancesWithScsAccount(s.ClusterGroup.NetEnvironmentType, s.ClusterGroup.Region, instanceIdSet)
	if err != nil {
		return errorcode.InternalErrorCode.NewWithErr(err)
	}

	if len(cvmInstanceSet) == 0 {
		return nil
	}

	for _, instance := range cvmInstanceSet {
		if *instance.InstanceState == "TERMINATING" {
			return nil
		}
	}

	err = cbs.GetCbsService(s.ClusterGroup.NetEnvironmentType, s.Region).TerminateCbsFromCVM(instanceIdSet)
	if err != nil {
		return err
	}

	return cvm.GetCvmService().TerminateInstancesWithScsAccount(s.Region, instanceIdSet)
}

func (s *upgradeWorkerService) isWorkerContainJobs() (containJob bool, err error) {
	instanceId, _ := s.GetParam(constants.FLOW_PARAM_UPGRADE_WORKER_INSTANCE, "")
	if len(instanceId) == 0 {
		return false, errorcode.InternalErrorCode.NewWithMsg("Can not find upgrade worker instance")
	}
	nodeName, err := s.getWorkerNodeName(instanceId)
	if err != nil {
		return false, errorcode.InternalErrorCode.NewWithErr(err)
	}
	if len(nodeName) == 0 {
		return false, nil
	}
	k8sService := k8s.GetK8sService()
	kubConfig := []byte(s.Cluster.KubeConfig)
	client, err := k8sService.NewClient(kubConfig)
	if err != nil {
		return false, errorcode.InternalErrorCode.NewWithErr(err)
	}
	fSet := fields.Set{"spec.nodeName": nodeName}
	// TODO: 调整default ns
	namespace := clusterService.GetDefaultNamespace(s.ClusterGroup)
	podList, err := k8sService.ListPod(client, namespace,
		metav1.ListOptions{FieldSelector: fields.SelectorFromSet(fSet).String()})
	if err != nil {
		return false, errorcode.InternalErrorCode.NewWithErr(err)
	}
	logger.Infof("pod %+v", podList)
	for _, pod := range podList.Items {
		if strings.HasPrefix(pod.Name, "cql-") {
			return true, nil
		}
	}
	return false, nil
}

func (s *upgradeWorkerService) DeleteWorkerFromTke(instanceIds []*string) (err error) {
	region := s.ClusterGroup.Region
	netEnv := s.ClusterGroup.NetEnvironmentType
	tkeService := tke.GetTkeService()
	totalCount, _, err := tkeService.DescribeClusterInstancesWithScsAccountByNetEnvironmentType(netEnv, region,
		tkeService.NewDefaultDescribeClusterInstancesRequestBuilder().
			WithClusterId(s.Tke.InstanceId).
			WithInstanceIds(instanceIds).
			Build())
	if err != nil {
		return errorcode.InternalErrorCode.NewWithErr(err)
	}
	if totalCount == 0 {
		return nil
	}

	rsp, err := tkeService.DeleteClusterInstancesForIdsByNetEnvironmentType(netEnv, region, s.Tke.InstanceId, instanceIds)
	if err != nil {
		return errorcode.InternalErrorCode.NewWithErr(err)
	}
	tmp, _ := json.Marshal(rsp)
	logger.Info("[%s]: DeleteClusterInstances return rsp: %s", string(tmp))
	return nil
}

func (s *upgradeWorkerService) deleteWorkerFromTke(instanceId string) (err error) {
	_, exist := s.GetParam(constants.FLOW_PARAM_UPGRADE_WORKER_DELETE, "")
	if exist {
		return nil
	}
	instanceIds := make([]*string, 0)
	instanceIds = append(instanceIds, &instanceId)

	s.DeleteWorkerFromTke(instanceIds)
	s.SetReturnParam(constants.FLOW_PARAM_UPGRADE_WORKER_DELETE, instanceId)
	return nil
}

func (s *upgradeWorkerService) ScheduleWorker(instanceId string, unschedule bool) (err error) {
	nodeName, err := s.getWorkerNodeName(instanceId)
	if err != nil {
		return errorcode.InternalErrorCode.NewWithErr(err)
	}
	if len(nodeName) == 0 {
		return nil
	}
	k8sService := k8s.GetK8sService()
	kubConfig := []byte(s.Cluster.KubeConfig)
	client, err := k8sService.NewClient(kubConfig)
	if err != nil {
		return errorcode.InternalErrorCode.NewWithErr(err)
	}
	_, err = k8sService.NodeSchedule(client, nodeName, unschedule)
	if err != nil {
		return errorcode.InternalErrorCode.NewWithErr(err)
	}
	return nil
}

func (s *upgradeWorkerService) getWorkerNodeName(instanceId string) (nodeName string, err error) {
	instanceIds := make([]*string, 0)
	instanceIds = append(instanceIds, &instanceId)

	region := s.ClusterGroupService.GetClusterGroup().Region
	tkeService := tke.GetTkeService()
	totalCount, instanceSet, err := tkeService.DescribeClusterInstancesWithScsAccountByNetEnvironmentType(
		s.ClusterGroupService.GetClusterGroup().NetEnvironmentType,
		region,
		tkeService.NewDefaultDescribeClusterInstancesRequestBuilder().
			WithClusterId(s.Tke.InstanceId).
			WithInstanceIds(instanceIds).
			Build())

	if err != nil {
		return "", errorcode.InternalErrorCode.NewWithErr(err)
	}
	if totalCount == 0 {
		return "", nil
	}
	return *instanceSet[0].LanIP, nil
}
