package taskHandler

import (
	"errors"
	"fmt"
	appsV1 "k8s.io/api/apps/v1"
	v1 "k8s.io/api/core/v1"
	"strconv"
	commandService "tencentcloud.com/tstream_galileo/src/common/command"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/oceanus_controller"
	clusterService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type DeployWatchdogService struct {
}

func (s *DeployWatchdogService) Apps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	cg := ctx.ClusterGroup

	// 共享集群 包年包月下的eks集群，不做任何处理
	if cg.Type == constants.CLUSTER_GROUP_TYPE_SUB_EKS {
		return result, nil
	}

	// 共享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId != "" {
		return result, nil
	}

	// 共享集群母集群，开新区
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone != "" {
		// TODO 开新区，可以增大replica
		return result, nil
	}

	// 版本统一的版本，不需要部署watchdog
	if ctx.Tke.ArchGeneration >= constants.TKE_ARCH_GENERATION_V6 {
		return result, nil
	}

	secret, err := ctx.GetOceanusIPSecret()
	if err != nil {
		return result, err
	}

	app := s.configMap(ctx, secret)
	// 创建共享集群母集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone == "" {
		app = s.configMapForUniform(ctx, secret)
	}

	// 独享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId == "" {

	}

	result = append(result,
		ctx.HadoopConfigMap(secret),
		ctx.LogListenerConfigMap(false, secret),
		ctx.LogListenerMetaConfigMap(constants.ComponentWatchdog, secret),
		app,
		ctx.Service(constants.ComponentWatchdog, constants.WatchdogPort, secret))

	if ctx.Tke.ArchGeneration <= constants.TKE_ARCH_GENERATION_V2 {
		result = append(result, &watchdogSts{ctx: ctx})
	}
	if ctx.Tke.ArchGeneration >= constants.TKE_ARCH_GENERATION_V3 {
		result = append(result, &watchdogDep{ctx: ctx})
	}

	return result, nil
}

type watchdogSts struct {
	ctx *deploy.Context
	apps.StatefulSet
}

func (w *watchdogSts) Params() (interface{}, error) {
	return getWatchdogParams(w.ctx, "StatefulSet")
}

func (w *watchdogSts) Decode(params, into interface{}) (interface{}, error) {
	return w.ctx.FlowCC.AppsCC().Watchdog(params, into)
}

func (w *watchdogSts) Transform(c apps.Client, v interface{}) (interface{}, error) {
	return transformSts(w.ctx, c, v)
}

func transformSts(ctx *deploy.Context, c apps.Client, v interface{}) (interface{}, error) {
	if ctx.Tke.ArchGeneration < constants.TKE_ARCH_GENERATION_V2 {
		return v, nil
	}
	if ctx.ClusterType != constants.K8S_CLUSTER_TYPE_TKE {
		return v, nil
	}
	vd := v.(*appsV1.StatefulSet)

	od, _ := c.K8sService().GetStatefulSetAppsV1(c.ClientSet(), vd.Namespace, vd.Name)
	if od == nil {
		return vd, nil
	}

	// retain node selector
	vd.Spec.Template.Spec.NodeSelector = od.Spec.Template.Spec.NodeSelector

	// retain app-container resource
	appContainer := "app-container"
	mapStsDepContainer(vd, appContainer, func(vc *v1.Container) {
		mapStsDepContainer(od, appContainer, func(oc *v1.Container) {
			vc.Resources = oc.Resources
		})
	})

	return vd, nil
}

func mapStsDepContainer(d *appsV1.StatefulSet, containerName string, f func(vc *v1.Container)) {
	containers := &d.Spec.Template.Spec.Containers
	for i := range *containers {
		c := *containers
		if c[i].Name == containerName {
			f(&c[i])
		}
	}
}

type watchdogDep struct {
	ctx *deploy.Context
	apps.Deployment
}

func (w *watchdogDep) Params() (interface{}, error) {
	return getWatchdogParams(w.ctx, "Deployment")
}

func (w *watchdogDep) Decode(params, into interface{}) (interface{}, error) {
	return w.ctx.FlowCC.AppsCC().Watchdog(params, into)
}

func (w *watchdogDep) Transform(c apps.Client, v interface{}) (interface{}, error) {
	return transformDep(w.ctx, c, v)
}

func transformDep(ctx *deploy.Context, c apps.Client, v interface{}) (interface{}, error) {
	if ctx.Tke.ArchGeneration < constants.TKE_ARCH_GENERATION_V2 {
		return v, nil
	}
	if ctx.ClusterType != constants.K8S_CLUSTER_TYPE_TKE {
		return v, nil
	}
	vd := v.(*appsV1.Deployment)

	od, _ := c.K8sService().GetDeploymentAppsV1(c.ClientSet(), vd.Namespace, vd.Name)
	if od == nil {
		return vd, nil
	}

	// retain node selector
	vd.Spec.Template.Spec.NodeSelector = od.Spec.Template.Spec.NodeSelector

	// retain app-container resource
	appContainer := "app-container"
	mapDepContainer(vd, appContainer, func(vc *v1.Container) {
		mapDepContainer(od, appContainer, func(oc *v1.Container) {
			vc.Resources = oc.Resources
		})
	})

	return vd, nil
}

func mapDepContainer(d *appsV1.Deployment, containerName string, f func(vc *v1.Container)) {
	containers := &d.Spec.Template.Spec.Containers
	for i := range *containers {
		c := *containers
		if c[i].Name == containerName {
			f(&c[i])
		}
	}
}

func getWatchdogParams(ctx *deploy.Context, workLoadKind string) (data *oceanus_controller.Base, err error) {
	appContainerImage, exists := ctx.GetParam(constants.FLOW_PARAM_WATCHDOG_IMAGE, "")
	if !exists {
		if ctx.ClusterGroup.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM {
			appContainerImage, err = ctx.CC().ImageRegistry().UniformWatchDog()
		} else {
			appContainerImage, err = ctx.CC().ImageRegistry().WatchDog()
		}

		if err != nil {
			return nil, err
		}
	}
	logContainerImage, err := ctx.CC().ImageRegistry().LogListener()
	if err != nil {
		return nil, err
	}

	base, err := ctx.ParamBase(workLoadKind,
		constants.OCEANUS_NAMESPACE,
		constants.ComponentWatchdog, false)
	if err != nil {
		return nil, err
	}

	err = fillTestParams(ctx, base)
	if err != nil {
		return nil, err
	}

	if ctx.ClusterGroup.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE &&
		ctx.Tke.ArchGeneration >= constants.TKE_ARCH_GENERATION_V2 &&
		ctx.ClusterType == constants.K8S_CLUSTER_TYPE_TKE { //TKE 集群
		for key := range base.NodeSelector {
			delete(base.NodeSelector, key)
		}
		if ctx.Tke.ArchGeneration >= constants.TKE_ARCH_GENERATION_V5 {
			base.NodeSelector[constants.TKE_CVM_WORKER_LABEL_KEY] = fmt.Sprintf("worker")
		} else {
			base.NodeSelector[constants.TKE_CVM_WORKER_NUM_LABEL_KEY] = fmt.Sprintf("worker%d", 2)
		}
		base.PriorityClassName = constants.TKE_PRIORITY_CLASS_NAME
	}
	base.AppImage = appContainerImage
	base.SidecarImage = logContainerImage
	base.ServiceAccountName = constants.SERVICE_ACCOUNT_OCEANUS

	return base, nil
}

func fillTestParams(ctx *deploy.Context, base *oceanus_controller.Base) error {
	if isDev := service.GetConfStringValue("scsDevEnv"); isDev == "true" {
		secretId, err := ctx.CC().Get("TestSecretId", "")
		if err != nil {
			return err
		}
		if len(secretId) == 0 {
			return errors.New("TestSecretId is empty")
		}
		secretKey, err := ctx.CC().Get("TestSecretKey", "")
		if err != nil {
			return err
		}
		if len(secretKey) == 0 {
			return errors.New("TestSecretKey is empty")
		}
		base.Env["SecretId"] = secretId
		base.Env["SecretKey"] = secretKey
		base.HostAliases = map[string]string{
			"*************": "oceanus.test.tencentcloudapi.com",
		}
	}
	return nil
}

func (s *DeployWatchdogService) configMap(ctx *deploy.Context, secret *v1.Secret) apps.App {
	dbName, _ := ctx.GetParam(constants.FLOW_PARAM_WATCHDOG_DB, constants.WatchdogDatabaseName)
	wdCC := ctx.FlowCC.WatchDogCC()
	return ctx.ConfigMap(
		secret,
		apps.CMWithName(constants.WatchdogConfigmapName),
		apps.CMAddData("flink-conf.yaml", ctx.FlinkConf(constants.ZooKeeperComponentReplicaNumber)),
		apps.CMAddData("logback.xml", wdCC.LogBack),
		apps.CMAddData("application.conf", ctx.ConfUseCdbParams(dbName, ctx.FlowCC.ApplicationConf)),
		apps.CMAddData("mybatis-config.xml", ctx.ConfUseCdbParams(dbName, wdCC.MybatisConfig)),
		apps.CMAddData("watchdog.properties", s.getConfigProperties(ctx)),
	)
}

func (s *DeployWatchdogService) configMapForUniform(ctx *deploy.Context, secret *v1.Secret) apps.App {
	dbName, _ := ctx.GetParam(constants.FLOW_PARAM_CLUSTERSCHEDULER_DB, constants.ClusterSchedulerDatabaseName)
	wdCC := ctx.FlowCC.WatchDogCC()
	return ctx.ConfigMap(
		secret,
		apps.CMWithName(constants.WatchdogConfigmapName),
		apps.CMAddData("flink-conf.yaml", ctx.FlinkConf(constants.ZooKeeperComponentReplicaNumber)),
		apps.CMAddData("logback.xml", wdCC.LogBack),
		apps.CMAddData("application.conf", ctx.ConfUseCdbParams(dbName, ctx.FlowCC.ApplicationConf)),
		apps.CMAddData("mybatis-config.xml", ctx.ConfUseCdbParams(dbName, wdCC.MybatisConfigForUniform)),
		apps.CMAddData("watchdog.properties", s.getConfigProperties(ctx)),
	)
}

func (s *DeployWatchdogService) getConfigProperties(ctx *deploy.Context) apps.CMDataValue {
	return func() (string, error) {
		// 渲染 ClusterAdminServiceUrl， 默认使用 CS的(创建集群)，如果集群有CA（更新集群），就使用CA的
		serviceUrl, err := commandService.GetCAOrCSUrl(ctx.Cluster)
		if err != nil {
			return "", err
		}
		logger.Infof("### cluster id %d watchdog ClusterAdminServiceUrl is %s", ctx.Cluster.Id, serviceUrl)

		isEks, _ := clusterService.IsEks(ctx.ClusterGroup.Id)
		nginxUsername, nginxPassword, url, err := ctx.FlowCC.NginxCC().Info()
		if err != nil {
			return "", err
		}
		// 替换模板参数
		params := struct {
			ClusterId              string
			ClusterSerialId        string
			ClusterGroupSerialId   string
			CosBucket              string
			AppId                  string
			Region                 string
			LoadbalanceId          string
			SubnetId               string
			ClusterAdminServiceUrl string
			NginxUsername          string
			NginxPassword          string
			PublicServiceNginxUrl  string
			IsEks                  string
		}{
			ClusterId:              strconv.FormatInt(ctx.Cluster.Id, 10),
			ClusterSerialId:        ctx.Cluster.UniqClusterId,
			ClusterGroupSerialId:   ctx.ClusterGroup.SerialId,
			CosBucket:              ctx.Cluster.DefaultCOSBucket,
			AppId:                  strconv.FormatInt(int64(ctx.ClusterGroup.AppId), 10),
			Region:                 ctx.ClusterGroup.Region,
			LoadbalanceId:          ctx.Cluster.LoadBalanceId,
			SubnetId:               ctx.Cluster.SubnetId,
			ClusterAdminServiceUrl: serviceUrl,
			NginxUsername:          nginxUsername,
			NginxPassword:          nginxPassword,
			PublicServiceNginxUrl:  url,
			IsEks:                  strconv.FormatBool(isEks),
		}
		return ctx.FlowCC.WatchDogCC().ConfProperties(params)
	}
}
