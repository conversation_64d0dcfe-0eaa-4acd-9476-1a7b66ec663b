package taskHandler

import (
	"fmt"
	v1 "k8s.io/api/apps/v1"
	v13 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	flow2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/flow"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/tke"
)

type DeleteSqlGatewayHandler struct {
}

func (d *DeleteSqlGatewayHandler) CompleteTask(request *flow.TaskExecRequest) (resp *flow.TaskExecResponse) {
	flowService := flow2.GetFlowService()
	params := request.Params
	defer func() {
		if errs := recover(); errs != nil {
			resp = flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1,
				fmt.Sprintf("%v", errs), params)
		}
	}()
	var err error
	resp, err = d.handle(request)
	if err != nil {
		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1, fmt.Sprintf("%v", err), params)
	}
	return flow.NewTaskExecResponseWithParams(flow.TASK_SUCCESS, 1.0, "ok", request.Params)
}

func (d *DeleteSqlGatewayHandler) handle(req *flow.TaskExecRequest) (resp *flow.TaskExecResponse, err error) {
	flowService := flow2.GetFlowService()
	gatewaySerialId, _, err := flowService.GetFlowParamString(req.Params, constants.FLOW_PARAM_GATEWAY_SERIAL_ID, constants.EMPTY)
	if err != nil {
		return nil, err
	}
	requestId, _, err := flowService.GetFlowParamString(req.Params, constants.FLOW_PARAM_REQUEST_ID, constants.EMPTY)
	if err != nil {
		return nil, err
	}
	exists, gateway, err := service2.GetSqlGatewayBySerialId(gatewaySerialId)
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, errorcode.InvalidParameterCode.ReplaceDesc(fmt.Sprintf("[%s] SqlGateway %s is not existed.", requestId, gatewaySerialId))
	}
	flinkVersionFormatted := service.GetNoDotLowerFlinkVersion(gateway.FlinkVersion)
	ClusterGroup, cluster, err := flowService.GetClusterGroupAndCluster(req.Params)
	if err != nil {
		panic(errorcode.NewStackError(errorcode.InternalErrorCode, "", err))
	}
	if err != nil {
		return nil, err
	}
	namespace := service2.GetDefaultNamespace(ClusterGroup)

	clientSet, err := tke.GetTkeService().KubernetesClientsetFromCluster(req.DocId, cluster)
	if err != nil {
		return nil, err
	}
	k8sService := k8s.GetK8sService()
	// 1. delete sql gateway deployment
	jmDeploymentName := fmt.Sprintf(SqlGatewayDeploymentNameTemplate, flinkVersionFormatted)
	// TODO: 调整default ns
	_, err = k8sService.DeleteDeployment(clientSet, &v1.Deployment{
		ObjectMeta: metav1.ObjectMeta{Namespace: namespace, Name: jmDeploymentName},
	})
	if err != nil {
		logger.Errorf("[%s] Failed to delete deployment, err %+v", requestId, err)
		return nil, err
	}
	// 2. delete sql gateway  service
	jmServiceName := fmt.Sprintf(SqlGatewayServiceNameTemplate, flinkVersionFormatted)
	_, err = k8sService.DeleteService(clientSet, &v13.Service{
		ObjectMeta: metav1.ObjectMeta{Namespace: namespace, Name: jmServiceName},
	})
	if err != nil {
		logger.Errorf("[%s] Failed to delete service, err %+v", requestId, err)
		return nil, err
	}
	// 3. delete flink-conf configmap
	flinkConfigMap := fmt.Sprintf(SqlGatewayFlinkConfigTemplate, flinkVersionFormatted)
	_, err = k8sService.DeleteConfigMap(clientSet, &v13.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{Namespace: namespace, Name: flinkConfigMap},
	})
	if err != nil {
		logger.Errorf("[%s] Failed to delete flink-conf configmap, err %+v", requestId, err)
		return nil, err
	}
	// 4. delete hadoop-conf configmap
	hadoopConfigMap := fmt.Sprintf(SqlGatewayHadoopConfigTemplate, flinkVersionFormatted)
	_, err = k8sService.DeleteConfigMap(clientSet, &v13.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{Namespace: namespace, Name: hadoopConfigMap},
	})
	if err != nil {
		logger.Errorf("[%s] Failed to delete hadoop-conf configmap err %+v", requestId, err)
		return nil, err
	}
	return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_SUCCESS, flow.TASK_STATUS_SUCCESS, 0.1, "ok",
		req.Params), nil
}
