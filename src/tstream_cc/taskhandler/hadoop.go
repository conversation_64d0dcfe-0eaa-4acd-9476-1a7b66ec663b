package taskHandler

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	v1 "k8s.io/api/apps/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	service4 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/helm"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	clusterService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/helm"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	subnetService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/subnet"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/tke"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type HadoopYarn struct {
	ctx *deploy.Context
}

func (i *HadoopYarn) AppType() interface{} {
	return &helm.InstallOptions{}
}

func (i *HadoopYarn) Params() (interface{}, error) {
	return nil, nil
}

func (i *HadoopYarn) Decode(params, into interface{}) (interface{}, error) {
	opt := into.(*helm.InstallOptions)
	appContainerImage, err := i.ctx.CC().ImageRegistry().HadoopYarn()
	if err != nil {
		return nil, err
	}
	annotations := make(map[string]string)
	annotations["tke.cloud.tencent.com/cross-tenant-eni-enable"] = "true"
	annotations["tke.cloud.tencent.com/networks"] = "tke-bridge,tke-direct-eni,tke-route"
	annotations["tke.cloud.tencent.com/vpc-ip-claim-delete-policy"] = "Never"
	annotations["tke.cloud.tencent.com/claim-expired-duration"] = "72h"

	hrCC := i.ctx.FlowCC.HadoopYarnCC()
	valueMapping, err := hrCC.ResourceValue()
	if err != nil {
		logger.Errorf("get rainbow ConfigureCenter.Flow.K8S.HadoopYarn.value_mappings error")
		valueMapping = map[string]interface{}{
			"hdfs.nameNode.pdbMinAvailable":              1,
			"yarn.resourceManager.memLimit":              40960,
			"yarn.resourceManager.cpuLimit":              20,
			"yarn.nodeManager.replicas":                  2,
			"yarn.nodeManager.resources.requests.memory": "2048Mi",
			"yarn.nodeManager.resources.limits.memory":   "6144Mi",
			"persistence.nameNode.storageClass":          "cbs",
			"persistence.dataNode.storageClass":          "cbs",
			"persistence.nameNode.size":                  "100Gi",
			"persistence.dataNode.size":                  "1000Gi",
			"yarn.resourceManager.resources.limits.cpu":  "1000m",
		}
	}

	valueMapping["image.repository"] = appContainerImage
	valueMapping["hdfs.podAnnotations"] = annotations

	logger.Infof("hadoop valueMapping: %+v", valueMapping)

	chartPath, err := i.ctx.FlowCC.TkeCC().HadoopChartPath()
	if err != nil {
		return nil, err
	}
	opt.ChartPath = chartPath
	opt.Name = service2.GetHelmService().HadoopYarnName(i.ctx.ClusterGroup)
	opt.ValuesMap = valueMapping
	return opt, nil
}

func (i *HadoopYarn) Transform(_ apps.Client, v interface{}) (interface{}, error) {
	return v, nil
}

func (i *HadoopYarn) Apply(s apps.Client, v interface{}) (interface{}, error) {
	// 需要开启 跨租户弹性网卡
	if i.ctx.Cluster.CrossTenantEniMode != constants.EnableStaticMode {
		logger.Infof("Before deploying hadoop, turn on cluster StaticMode to true.")
		return "", nil
	}
	helService := service2.GetHelmService()
	namespace := helService.HadoopNamespace(i.ctx.ClusterGroup)
	client, err := helService.NewClient(s.KubeConfig(), namespace)
	if err != nil {
		return "", err
	}

	res, err := helService.ListByName(client, namespace, helService.HadoopYarnName(i.ctx.ClusterGroup))
	if err != nil {
		return "", err
	}
	if len(res.Releases) == 1 {
		return res.Releases[0].Name, nil
	} else if len(res.Releases) > 1 {
		return "", errorcode.InternalErrorCode.New()
	}

	status, err := helService.Install(client, namespace, v.(*helm.InstallOptions))

	if err != nil {
		return "", err
	}
	return status.Name, nil
}

func (i *HadoopYarn) Ready(s apps.Client, v interface{}) (bool, error) {
	// 需要开启 跨租户弹性网卡
	if i.ctx.Cluster.CrossTenantEniMode != constants.EnableStaticMode {
		logger.Infof("Before deploying hadoop, turn on cluste StaticMode to true.")
		return true, nil
	}
	// 检查如下四个ss是否就绪
	// oceanus-hadoop-hadoop-hdfs-nn
	// oceanus-hadoop-hadoop-yarn-rm
	// oceanus-hadoop-hadoop-yarn-nm
	// oceanus-hadoop-hadoop-hdfs-dn

	k8sService := k8s.GetK8sService()
	helmServcie := service2.GetHelmService()
	namespace := helmServcie.HadoopNamespace(i.ctx.ClusterGroup)
	hadoopYarnName := helmServcie.HadoopYarnName(i.ctx.ClusterGroup)
	ss := &v1.StatefulSet{
		ObjectMeta: metav1.ObjectMeta{
			Namespace: namespace,
		},
	}
	k8s.GetK8sService()

	rmStsName := fmt.Sprintf("%s-hadoop-yarn-rm", hadoopYarnName)

	for _, name := range []string{
		rmStsName,
		fmt.Sprintf("%s-hadoop-hdfs-nn", hadoopYarnName),
		fmt.Sprintf("%s-hadoop-hdfs-dn", hadoopYarnName),
		fmt.Sprintf("%s-hadoop-yarn-nm", hadoopYarnName),
	} {
		logger.Infof("hadoop statefulset name:%s", name)
		ss.Name = name
		ready, err := k8sService.StatefulSetReady(s.ClientSet(), ss)
		if err != nil {
			return false, err
		}
		if !ready {
			return false, nil
		}
	}
	isEks, _ := clusterService.IsEks(i.ctx.ClusterGroup.Id)

	nnWebUiIp, _ := GetEniIp(i.ctx.Cluster, constants.HADOOP_APP_NAME, constants.NAMENODE_COMPONENT_NAME, namespace, isEks)
	nnParams := struct {
		WebIp string
	}{
		WebIp: nnWebUiIp,
	}

	rmWebUiIp, _ := GetEniIp(i.ctx.Cluster, constants.HADOOP_APP_NAME, constants.RESOURCE_MANAGER_COMPONENT_NAME, namespace, isEks)
	rmParams := struct {
		WebIp string
	}{
		WebIp: rmWebUiIp,
	}

	envMapping := map[string]string{
		"ProxyAddress": fmt.Sprintf("%s:8089", rmWebUiIp),
	}
	err := k8s.GetK8sService().UpdateStatefulSetAppsV1Env(s.ClientSet(), namespace, rmStsName, envMapping)
	if err != nil {
		logger.Errorf("Failed to UpdateStatefulSetAppsV1Env rm sts because %+v", err)
		return false, errorcode.InternalErrorCode.NewWithErr(err)
	}

	hrCC := i.ctx.FlowCC.HadoopYarnCC()
	coreSite, err := hrCC.HadoopCoreSiteYaml(nnParams)
	if err != nil {
		logger.Errorf("get  %s from rainbow ConfigureCenter.Flow.K8S.HadoopYarn err: %+v", constants.CORE_SITE_FILE_NAME, err)
		return false, errorcode.InternalErrorCode.NewWithErr(err)
	}
	hdfsSite, err := hrCC.HadoopHdfsSiteYaml()
	if err != nil {
		logger.Errorf("get  %s from rainbow ConfigureCenter.Flow.K8S.HadoopYarn err: %+v", constants.HDFS_SITE_FILE_NAME, err)
		return false, errorcode.InternalErrorCode.NewWithErr(err)
	}
	mapSite, err := hrCC.HadoopMapRedSiteYaml()
	if err != nil {
		logger.Errorf("get  %s from rainbow ConfigureCenter.Flow.K8S.HadoopYarn err: %+v", constants.MAPRED_SITE_FILE_NAME, err)
		return false, errorcode.InternalErrorCode.NewWithErr(err)
	}
	yarnSite, err := hrCC.HadoopYarnSiteYaml(rmParams)
	if err != nil {
		logger.Errorf("get  %s from rainbow ConfigureCenter.Flow.K8S.HadoopYarn err: %+v", constants.YARN_SITE_FILE_NAME, err)
		return false, errorcode.InternalErrorCode.NewWithErr(err)
	}

	streamParkConfig, err := hrCC.HadoopStreamparkConfigYaml(rmParams)
	if err != nil {
		logger.Errorf("get  %s from rainbow ConfigureCenter.Flow.K8S.HadoopYarn err: %+v", constants.STREAMPARK_CONFIG_FILE_NAME, err)
		return false, errorcode.InternalErrorCode.NewWithErr(err)
	}

	dinkyConfig, err := hrCC.HadoopDinkyConfigYaml(nnParams)
	if err != nil {
		logger.Errorf("get  %s from rainbow ConfigureCenter.Flow.K8S.HadoopYarn err: %+v", constants.DINKY_CONFIG_FILE_NAME, err)
		return false, errorcode.InternalErrorCode.NewWithErr(err)
	}

	dataNodeIp, _ := GetEniIp(i.ctx.Cluster, constants.HADOOP_APP_NAME, constants.DATANODE_COMPONENT_NAME, namespace, isEks)
	dnParams := struct {
		DataNodeIp string
	}{
		DataNodeIp: dataNodeIp,
	}

	hostsConfig, err := hrCC.HostsYaml(dnParams)
	if err != nil {
		logger.Errorf("get  %s from rainbow ConfigureCenter.Flow.K8S.HadoopYarn err: %+v", constants.HOSTS_CONF, err)
		return false, errorcode.InternalErrorCode.NewWithErr(err)
	}

	configMap := make(map[string]string)
	configMap[constants.CORE_SITE_FILE_NAME] = coreSite
	configMap[constants.HDFS_SITE_FILE_NAME] = hdfsSite
	configMap[constants.MAPRED_SITE_FILE_NAME] = mapSite
	configMap[constants.YARN_SITE_FILE_NAME] = yarnSite
	configMap[constants.STREAMPARK_CONFIG_FILE_NAME] = streamParkConfig
	configMap[constants.DINKY_CONFIG_FILE_NAME] = dinkyConfig
	configMap[constants.HOSTS_CONF] = hostsConfig

	configBytes, err := json.Marshal(configMap)
	if err != nil {
		logger.Errorf("configMap cannot Marshal to json")
		return false, err
	}

	service4.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.ExecuteSqlWithArgs("update ClusterHadoopYarn set NnWebUiIp=?, RmWebUiIp=?, Config=? where ClusterGroupSerialId=?",
			nnWebUiIp, rmWebUiIp, string(configBytes), i.ctx.ClusterGroup.SerialId)
		return nil
	}).Close()
	return true, nil
}

func GetEniIp(cluster *table.Cluster, app string, component string, namespace string, isEks bool) (string, error) {

	// 获取弹性网卡ip，设置webui地址.
	// 获取Deployment所关联的Pod信息
	k8sClient, err := tke.GetTkeService().KubernetesClientsetFromCluster("ModifySubnetService", cluster)
	if err != nil {
		errMsg := fmt.Sprintf("[GetEniIp] Failed to get k8s client %v", err)
		logger.Errorf(errMsg)
		return "", errorcode.NewStackError(errorcode.FailedOperationCode, errMsg, err)
	}
	pods, err := k8sClient.CoreV1().Pods(namespace).List(context.TODO(), metav1.ListOptions{
		LabelSelector: fmt.Sprintf("app=%s,component=%s", app, component),
	})
	if err != nil {
		return "", errorcode.FailedOperationCode.NewWithErr(err)
	}
	logger.Infof("### %s pods is %+v", app, pods)
	ip := ""
	for _, pod := range pods.Items {
		logger.Infof("Pod Name: %s\n", pod.Name)
		// 可以根据需要输出更多的Pod信息
		// 例如：pod.Status.Phase, pod.Status.StartTime, pod.Status.PodIP 等
		// 可以根据需要进一步操作Pod，例如获取日志、删除Pod等
		/**
		TKE tke.cloud.tencent.com/networks-status
		[{
			"name": "tke-bridge",
			"interface": "eth0",
			"ips": ["**********"],
			"mac": "fe:dd:b6:6b:f7:29",
			"default": true,
			"dns": {}
		}, {
			"name": "tke-direct-eni",
			"interface": "eth1",
			"ips": ["*************"],
			"mac": "20:90:6F:4F:ED:C5",
			"dns": {}
		}, {
			"name": "tke-route",
			"ips": ["*******"],
			"dns": {}
		}]

		EKS tke.cloud.tencent.com/attached-cross-tenant-eni

		{"eniID":"eni-dat2gtio","mac":"20:90:6F:40:07:82","primaryIP":"***********","subnetCIDR":"**********/24","attachedInsID":"eks-c2eqwv46"}
		*/
		if !strings.Contains(pod.Name, app) {
			continue
		}

		if !isEks {
			strNetworksStatus, exist := pod.Annotations["tke.cloud.tencent.com/networks-status"]
			if !exist {
				errMsg := fmt.Sprintf("Pod %s can't find tke.cloud.tencent.com/networks-status, please check.", pod.Name)
				logger.Errorf(errMsg)
				return "", errorcode.UnsupportedOperationCode.ReplaceDesc(errMsg)
			}
			logger.Infof("tke.cloud.tencent.com/networks-status info is %s", strNetworksStatus)
			ip, err = subnetService.ParseIp(strNetworksStatus)
			if err != nil {
				return "", errorcode.FailedOperationCode.NewWithErr(err)
			}
		} else {
			attachedCrossTenantEni, exist := pod.Annotations["tke.cloud.tencent.com/attached-cross-tenant-eni"]
			if !exist {
				errMsg := fmt.Sprintf("Pod %s can't find tke.cloud.tencent.com/attached-cross-tenant-eni, please check.", pod.Name)
				logger.Errorf(errMsg)
				return "", errorcode.UnsupportedOperationCode.ReplaceDesc(errMsg)
			}
			logger.Infof("tke.cloud.tencent.com/attached-cross-tenant-eni info is %s", attachedCrossTenantEni)
			ip, err = subnetService.ParseEksIp(attachedCrossTenantEni)
			if err != nil {
				return "", errorcode.FailedOperationCode.NewWithErr(err)
			}
		}
	}

	return ip, nil
}
