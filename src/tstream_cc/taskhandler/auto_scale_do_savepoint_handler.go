package taskHandler

import (
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	flow2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/flow"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/job_autoscale"
)

type doSavepointHandler struct {
}

func (this *doSavepointHandler) HandleTaskStatusInit(requestId string, request *flow.TaskExecRequest, task *job_autoscale.ScalingTask) (resp *flow.TaskExecResponse) {
	flowService := flow2.GetFlowService()
	params := request.Params
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf("AutoScale - doSavepointHandler HandleTaskStatusInit failed for Job %s, Action %s, because %+v",
				task.Action.JobSerialId, task.Action.SerialId, err)
			resp = flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1, fmt.Sprintf("%v", err), params)
		}
	}()
	// 直接成功
	if !task.CheckIfContinue(constants.SCALE_JOB_STATUS_READY) {
		return RunningRsp(params)
	}
	return RunningRsp(params)
}

func (this *doSavepointHandler) HandleTaskStatusRunning(requestId string, request *flow.TaskExecRequest, task *job_autoscale.ScalingTask) (resp *flow.TaskExecResponse) {
	flowService := flow2.GetFlowService()
	params := request.Params
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf("AutoScale - doSavepointHandler HandleTaskStatusRunning failed for Job %s, Action %s, because %+v",
				task.Action.JobSerialId, task.Action.SerialId, err)
			resp = flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1, fmt.Sprintf("%v", err), params)
		}
	}()
	// 直接成功
	task.SetJobStatus(constants.SCALE_JOB_STATUS_FINISH_SAVEPOINT)
	task.SaveStateToDB()
	return SuccessRsp(params)
}
