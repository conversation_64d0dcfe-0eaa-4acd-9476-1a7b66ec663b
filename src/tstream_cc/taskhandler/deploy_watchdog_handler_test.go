package taskHandler

import (
	"encoding/json"
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
	"testing"
	"time"
)

func TestDeployWatchdog(t *testing.T) {
	config.InitRainbowService(nil, nil, nil)

	handler := NewDeployHandler(&DeployWatchdogService{})
	request := &flow.TaskExecRequest{
		Retrycount: 0,
		Processkey: "test",
		Taskcode:   "test",
		DocId:      "1",
		FlowId:     "1",
		TaskId:     "1",
		Params: map[string]string{
			constants.FLOW_PARAM_CLUSTER_GROUP_ID: fmt.Sprintf("%d", 4),
			constants.FLOW_PARAM_CLUSTER_ID:       fmt.Sprintf("%d", 4),
			constants.FLOW_PARAM_REQUEST_ID:       "test_hahaha",
		},
	}

	rsp := handler.CompleteTask(request)

	b, _ := json.MarshalIndent(rsp, "", "")

	if rsp.RetCode != flow.TASK_IN_PROCESS {
		t.Fatal(string(b))
	}

	t.Log(string(b))

	for rsp.RetCode != flow.TASK_SUCCESS {
		request.Retrycount += 1
		for k, v := range rsp.Params {
			request.Params[k] = v
			request.Params["Error"] = ""
		}

		rsp = handler.CompleteTask(request)

		b, _ := json.MarshalIndent(rsp, "", "")
		t.Log(string(b))

		time.Sleep(time.Second * 3)
	}
}

func TestWatchdogConfigmap(t *testing.T) {
	url := "http://api.rainbow.oa.com:8080"
	appid := "97f4c581-649d-41ae-a21e-455bd3b4d5a5"
	envName := "Default"
	config.InitRainbowService(&url, &appid, &envName)
	request := &flow.TaskExecRequest{
		Retrycount: 0,
		Processkey: "test",
		Taskcode:   "test",
		DocId:      "1",
		FlowId:     "1",
		TaskId:     "1",
		Params: map[string]string{
			constants.FLOW_PARAM_CLUSTER_GROUP_ID: *fTestClusterGroupId,
			constants.FLOW_PARAM_CLUSTER_ID:       fmt.Sprintf("%d", *fTestClusterId),
			constants.FLOW_PARAM_REQUEST_ID:       "test_hahaha",
		},
	}
	ctx, err := deploy.New(request)
	if err != nil {
		t.Fatal(err)
	}
	secret, err := ctx.GetOceanusIPSecret()
	s := &DeployWatchdogService{}
	app := s.configMap(ctx, secret)
	t.Log(app)
	o := app.AppType()
	t.Log(app.Decode(nil, o))
}
