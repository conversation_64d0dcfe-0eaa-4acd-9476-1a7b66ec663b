package taskHandler

import (
	"encoding/json"
	"errors"
	"fmt"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common"
	es "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/es/v20180416"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/log"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config"
	flow2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/flow"
	service1 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/log"
)

type ApplyEsServerlessFilebeatEndpointHandler struct {
}

func (s *ApplyEsServerlessFilebeatEndpointHandler) CompleteTask(request *flow.TaskExecRequest) (resp *flow.TaskExecResponse) {
	logger.Infof("request: %+v", request)
	flowService := flow2.GetFlowService()
	params := request.Params
	defer func() {
		if errs := recover(); errs != nil {
			resp = flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1,
				fmt.Sprintf("%v", errs), params)
		}
	}()
	clusterFlowService, err := flow2.NewClusterFlowService(request)
	if err != nil {
		logger.Errorf("NewApplyEsServerlessPatchService  has return error %v", err)
		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1,
			fmt.Sprintf("%v", err), params)
	}
	clusterLogconfig := clusterFlowService.Cluster.LogConfig
	esServelessLogConf := &log.ClusterEsServelessLogConf{}
	err = json.Unmarshal([]byte(clusterLogconfig), esServelessLogConf)
	if err != nil {
		logger.Errorf("Cluster.LogConfig format error %s", clusterLogconfig)
		return clusterFlowService.RetryRspWithErr(err)
	}
	err = s.createEsIndexUser(request, clusterFlowService)
	if err != nil {
		logger.Errorf("createEsIndexUser with error %v", err)
		clusterFlowService.RetryRspWithErr(err)
	}
	esServelessLogConf.Status = log.ClusterLogconfDone
	err = updateClusterLogconf(clusterFlowService.Cluster.Id, esServelessLogConf)
	if err != nil {
		logger.Errorf("update cluster logconfig with error %v", err)
		clusterFlowService.RetryRspWithErr(err)
	}
	return flow.NewTaskExecResponseWithParams(flow.TASK_SUCCESS, 1.0, "ok", request.Params)
}

func (s *ApplyEsServerlessFilebeatEndpointHandler) createEsIndexUser(request *flow.TaskExecRequest, clusterFlowService *flow2.ClusterFlowService) error {
	indexId := request.Params[constants.FLOW_PARAM_ES_SERVERLESS_INDEXID]
	if indexId == "" {
		logger.Errorf("NewApplyEsServerlessEndpointService, indexId not valid")
		return errors.New("NewApplyEsServerlessEndpointService, indexId not valid")
	}

	csiur := es.NewCreateServerlessInstanceUserRequest()
	csiur.InstanceId = common.StringPtr(indexId)
	fixedType := int64(2)
	csiur.UserType = common.Int64Ptr(fixedType)
	userName, err := config.GetRainbowConfiguration("ConfigureCenter.Flow.Common", "esServerlessUser")
	if err != nil {
		logger.Warningf("cannot get config %s", "esServerlessUser")
		userName = "index-qcloud5"
	}
	csiur.Username = common.StringPtr(userName)
	csiur.Password = common.StringPtr(userName)
	secretId, secretKey, token, _, err := service.StsAssumeRole(clusterFlowService.ClusterGroup.OwnerUin, clusterFlowService.ClusterGroup.CreatorUin, clusterFlowService.Region)
	if err != nil {
		logger.Errorf("GetSecretIdAndKeyByNetworkEnvType with error %v", err)
		return err
	}
	esServerlessService := &service1.ApplyEsServerlessService{
		Region:    clusterFlowService.Region,
		SecretId:  secretId,
		SecretKey: secretKey,
		Token:     token,
	}
	_, err = esServerlessService.CreateServerlessInstanceUser(csiur)
	existMessage := fmt.Sprintf("user [%s] exists", userName)
	if err != nil && !strings.Contains(err.Error(), existMessage) {
		logger.Errorf("EsIndex:[%s] createServerlessInstanceUser API has return error %v", indexId, err)
		return err
	}
	return nil
}
