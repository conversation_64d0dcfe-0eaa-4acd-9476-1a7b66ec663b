package taskHandler

import (
	"encoding/json"
	"errors"

	tencentCloudErr "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/errors"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke"
)

type DeployClsService struct {
}

func (cs *DeployClsService) Apps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	cg := ctx.ClusterGroup

	// 共享集群 包年包月下的eks集群，不做任何处理
	if cg.Type == constants.CLUSTER_GROUP_TYPE_SUB_EKS {
		return result, nil
	}

	// 创建共享集群母集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone == "" {
		return result, nil
	}

	// 共享集群母集群，开新区
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone != "" {
		return result, nil
	}

	// 共享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId != "" {
		return result, nil
	}

	// 独享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId == "" {
	}

	if ctx.Tke.ClusterType == constants.K8S_CLUSTER_TYPE_TKE {
		supportFeature := make([]string, 0)
		err := json.Unmarshal([]byte(ctx.Cluster.SupportedFeatures), &supportFeature)
		if err != nil {
			logger.Errorf("cannot unmarshal Cluster.SupportedFeatures %s", ctx.Cluster.SupportedFeatures)
			panic(err)
		}
		applyClsComponents := true
		for _, item := range supportFeature {
			if item == constants.LOG2ES {
				applyClsComponents = false
			}
		}
		if applyClsComponents {
			result = append(result, ClsLogConfigApps(ctx.Region, ctx.Cluster.UniqClusterId, ctx)...)
		}
	} else {
		// EKS 集群，需要安装 cls-provisioner 和 logConfig crd
		result = append(result, &EksClsApp{ctx: ctx})
		result = append(result, ClsLogConfigAppsForEKS(ctx.Region, ctx.Cluster.UniqClusterId, ctx)...)
	}
	return result, nil
}

type EksClsApp struct {
	ctx *deploy.Context
	apps.ApplyApp
}

// Apply 部署对应的k8s资源
func (c *EksClsApp) Apply(s apps.Client, v interface{}) (interface{}, error) {
	uniqClusterId := c.ctx.Cluster.UniqClusterId
	logCollectReady, err := tke.EksLogCollectStatus(c.ctx.Region, uniqClusterId)
	if err != nil {
		var cloudSDKError *tencentCloudErr.TencentCloudSDKError
		ok := errors.As(err, &cloudSDKError)
		if ok && cloudSDKError.Code == "ResourceNotFound" {
			// 如果是ResourceNotFound，说明是第一次安装，需要调用enableEksLogCollect
			err, rsp := enableEksLogCollect(c.ctx.Region, uniqClusterId)
			logger.Infof("%s enableEksLogCollect result %v", uniqClusterId, rsp)
			if nil != err {
				logger.Errorf("%s ###ApplyenableEksLogCollect error. %+v", uniqClusterId, err)
				return nil, err
			}
			return nil, errors.New("enableEksLogCollect is not ready")
		} else {
			logger.Errorf("%s eksLogCollectStatus with error %+v", uniqClusterId, err)
			return nil, err
		}
	}
	if !logCollectReady {
		return nil, errors.New("enableEksLogCollect is not ready")
	} else {
		// 如果组件已经安装，则先删除API部署的，后续流程部署定制版本的 cls-provisioner
		deployment, err := s.ClientSet().AppsV1().Deployments("kube-system").
			Get("cls-provisioner", metav1.GetOptions{})
		if err != nil {
			logger.Errorf("%s get cls-provisioner return error %+v", uniqClusterId, err)
			if !k8sErrors.IsNotFound(err) {
				return nil, err
			}
		} else {
			logger.Infof("%s get cls-provisioner successfully, begin to delete origin cls-provisioner", uniqClusterId)
			err = s.ClientSet().AppsV1().Deployments(deployment.GetNamespace()).
				Delete(deployment.GetName(), &metav1.DeleteOptions{})
			if err != nil {
				logger.Errorf("%s delete cls-provisioner return error %+v", err, uniqClusterId)
				return nil, err
			}
			logger.Infof("%s delete origin cls-provisioner successfully.", uniqClusterId)
		}
	}
	return nil, nil
}
