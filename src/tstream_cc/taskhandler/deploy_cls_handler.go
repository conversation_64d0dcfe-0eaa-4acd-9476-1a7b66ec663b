package taskHandler

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	tencentCloudErr "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/errors"
	v1 "k8s.io/api/apps/v1"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/helm"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/helm"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke"
)

type DeployClsService struct {
}

func (cs *DeployClsService) Apps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	cg := ctx.ClusterGroup

	// 共享集群 包年包月下的eks集群，不做任何处理
	if cg.Type == constants.CLUSTER_GROUP_TYPE_SUB_EKS {
		return result, nil
	}

	// 创建共享集群母集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone == "" {
		return result, nil
	}

	// 共享集群母集群，开新区
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone != "" {
		return result, nil
	}

	// 共享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId != "" {
		return result, nil
	}

	// 独享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId == "" {
	}

	if ctx.Tke.ClusterType == constants.K8S_CLUSTER_TYPE_TKE {
		supportFeature := make([]string, 0)
		err := json.Unmarshal([]byte(ctx.Cluster.SupportedFeatures), &supportFeature)
		if err != nil {
			logger.Errorf("cannot unmarshal Cluster.SupportedFeatures %s", ctx.Cluster.SupportedFeatures)
			panic(err)
		}
		applyClsComponents := true
		for _, item := range supportFeature {
			if item == constants.LOG2ES {
				applyClsComponents = false
			}
		}
		if applyClsComponents {
			if ctx.Tke.ArchGeneration >= constants.TKE_ARCH_GENERATION_V6 {
				result = append(result, &ClsApp{ctx: ctx})
			} else {
				result = append(result, ClsLogConfigApps(ctx.Region, ctx.Cluster.UniqClusterId, ctx)...)
			}
		}
	} else {
		// EKS 集群，需要安装 cls-provisioner 和 logConfig crd
		result = append(result, &EksClsApp{ctx: ctx})
		result = append(result, ClsLogConfigAppsForEKS(ctx.Region, ctx.Cluster.UniqClusterId, ctx)...)
	}
	return result, nil
}

type EksClsApp struct {
	ctx *deploy.Context
	apps.ApplyApp
}

// Apply 部署对应的k8s资源
func (c *EksClsApp) Apply(s apps.Client, v interface{}) (interface{}, error) {
	uniqClusterId := c.ctx.Cluster.UniqClusterId
	logCollectReady, err := tke.EksLogCollectStatus(c.ctx.Region, uniqClusterId)
	if err != nil {
		var cloudSDKError *tencentCloudErr.TencentCloudSDKError
		ok := errors.As(err, &cloudSDKError)
		if ok && cloudSDKError.Code == "ResourceNotFound" {
			// 如果是ResourceNotFound，说明是第一次安装，需要调用enableEksLogCollect
			err, rsp := enableEksLogCollect(c.ctx.Region, uniqClusterId)
			logger.Infof("%s enableEksLogCollect result %v", uniqClusterId, rsp)
			if nil != err {
				logger.Errorf("%s ###ApplyenableEksLogCollect error. %+v", uniqClusterId, err)
				return nil, err
			}
			return nil, errors.New("enableEksLogCollect is not ready")
		} else {
			logger.Errorf("%s eksLogCollectStatus with error %+v", uniqClusterId, err)
			return nil, err
		}
	}
	if !logCollectReady {
		return nil, errors.New("enableEksLogCollect is not ready")
	} else {
		// 如果组件已经安装，则先删除API部署的，后续流程部署定制版本的 cls-provisioner
		deployment, err := s.ClientSet().AppsV1().Deployments("kube-system").
			Get(context.TODO(), "cls-provisioner", metav1.GetOptions{})
		if err != nil {
			logger.Errorf("%s get cls-provisioner return error %+v", uniqClusterId, err)
			if !k8sErrors.IsNotFound(err) {
				return nil, err
			}
		} else {
			logger.Infof("%s get cls-provisioner successfully, begin to delete origin cls-provisioner", uniqClusterId)
			err = s.ClientSet().AppsV1().Deployments(deployment.GetNamespace()).
				Delete(context.TODO(), deployment.GetName(), metav1.DeleteOptions{})
			if err != nil {
				logger.Errorf("%s delete cls-provisioner return error %+v", err, uniqClusterId)
				return nil, err
			}
			logger.Infof("%s delete origin cls-provisioner successfully.", uniqClusterId)
		}
	}
	return nil, nil
}

// ClsApp 使用Helm安装CLS组件
type ClsApp struct {
	ctx *deploy.Context
	apps.ApplyReadyApp
}

func (c *ClsApp) AppType() interface{} {
	return &helm.InstallOptions{}
}

func (c *ClsApp) Params() (interface{}, error) {
	return nil, nil
}

func (c *ClsApp) Decode(params, into interface{}) (interface{}, error) {
	opt := into.(*helm.InstallOptions)

	// 获取CLS的Chart路径
	chartPath, err := c.ctx.FlowCC.TkeCC().ClsChartPath()
	if err != nil {
		return nil, err
	}

	valuesMap := map[string]interface{}{
		"global.clsHost":   fmt.Sprintf("%s.cls.tencentyun.com", c.ctx.Region),
		"global.clusterID": c.ctx.Cluster.UniqClusterId,
	}

	opt.ChartPath = chartPath
	opt.Name = "cls-provisioner"
	opt.ValuesMap = valuesMap

	return opt, nil
}

func (c *ClsApp) Transform(_ apps.Client, v interface{}) (interface{}, error) {
	return v, nil
}

func (c *ClsApp) Apply(s apps.Client, v interface{}) (interface{}, error) {
	helmService := service2.GetHelmService()
	namespace := "kube-system"

	client, err := helmService.NewClient(s.KubeConfig(), namespace)
	if err != nil {
		logger.Errorf("ClsApp NewClient cluster %s error: %s", c.ctx.ClusterGroup.SerialId, err.Error())
		return "", err
	}

	// 检查是否已经安装
	res, err := helmService.ListByName(client, namespace, "cls-provisioner")
	if err != nil {
		logger.Errorf("ClsApp ListByName cluster %s error: %s", c.ctx.ClusterGroup.SerialId, err.Error())
		return "", err
	}

	if len(res.Releases) == 1 {
		return res.Releases[0].Name, nil
	} else if len(res.Releases) > 1 {
		return "", fmt.Errorf("found multiple cls releases")
	}

	// 安装CLS
	status, err := helmService.Install(client, namespace, v.(*helm.InstallOptions))
	if err != nil {
		logger.Errorf("ClsApp Install cluster %s error: %s", c.ctx.ClusterGroup.SerialId, err.Error())
		return "", err
	}

	return status.Name, nil
}

func (c *ClsApp) Ready(s apps.Client, v interface{}) (bool, error) {
	// 检查CLS相关组件是否就绪
	k8sService := k8s.GetK8sService()

	// 1. 检查cls-provisioner deployment是否就绪
	deployment := &v1.Deployment{
		ObjectMeta: metav1.ObjectMeta{
			Namespace: "kube-system",
			Name:      "cls-provisioner",
		},
	}

	deploymentReady, err := k8sService.DeploymentAppsV1Ready(s.ClientSet(), deployment)
	if err != nil {
		return false, err
	}

	if !deploymentReady {
		return false, nil
	}

	// 2. 检查tke-log-agent daemonset是否就绪
	daemonset := &v1.DaemonSet{
		ObjectMeta: metav1.ObjectMeta{
			Namespace: "kube-system",
			Name:      "tke-log-agent",
		},
	}

	daemonsetReady, err := k8sService.DaemonSetReady(s.ClientSet(), daemonset)
	if err != nil {
		return false, err
	}

	return daemonsetReady, nil
}
