package taskHandler

import (
	"tencentcloud.com/tstream_galileo/src/common/flow"
)

type failureRecoveryHandler struct {
}

func (this *failureRecoveryHandler) HandleTaskStatusInit(requestId string, request *flow.TaskExecRequest) (resp *flow.TaskExecResponse) {
	params := request.Params
	return RunningRsp(params)
}

func (this *failureRecoveryHandler) HandleTaskStatusRunning(requestId string, request *flow.TaskExecRequest) (resp *flow.TaskExecResponse) {
	params := request.Params
	return SuccessRsp(params)
}
