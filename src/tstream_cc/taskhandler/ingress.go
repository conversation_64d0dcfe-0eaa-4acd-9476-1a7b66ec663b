package taskHandler

import (
	"fmt"
	v1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	service4 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/helm"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/helm"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type NginxIngress struct {
	ctx *deploy.Context
}

func (i *NginxIngress) AppType() interface{} {
	return &helm.InstallOptions{}
}

func (i *NginxIngress) Params() (interface{}, error) {
	return nil, nil
}

func (i *NginxIngress) Decode(params, into interface{}) (interface{}, error) {
	opt := into.(*helm.InstallOptions)
	valuesMap := make(map[string]interface{})

	registryDomain, err := i.ctx.CC().ImageRegistry().TkeRegistry()
	if err != nil {
		return nil, err
	}
	valuesMap = map[string]interface{}{
		"controller.image.repository":     fmt.Sprintf("%s/tkeimages/nginx-ingress-controller", registryDomain),
		"defaultBackend.image.repository": fmt.Sprintf("%s/tkeimages/defaultbackend-amd64", registryDomain),
	}

	// 内网集群ingress的service自动创建CLB时指定为内网的clb,这里需要指定这个注解,否则默认会创建公网的clb导致内网集群的flinkui无法访问
	// 参考 https://cloud.tencent.com/document/product/457/45487#.E6.9C.8D.E5.8A.A1.E8.AE.BF.E9.97.AE.E6.96.B9.E5.BC.8F
	if i.ctx.ClusterGroup.NetEnvironmentType == constants.NETWORK_ENV_INNER_VPC {
		valuesMap["controller.service.annotations"] = map[string]string{
			"service.kubernetes.io/qcloud-loadbalancer-internal-subnetid": i.ctx.Cluster.SubnetId,
		}

	}
	// 上海自动驾驶云专区不支持外网CLB
	if i.ctx.ClusterGroup.Region == constants.AP_SHANGHAI_ADC {
		valuesMap["controller.service.type"] = corev1.ServiceTypeClusterIP

		valuesMap["controller.podAnnotations"] = map[string]string{
			"tke.cloud.tencent.com/cross-tenant-eni-enable": "true",
			"tke.cloud.tencent.com/networks":                "tke-bridge,tke-direct-eni,tke-route",
		}
	} else {
		if i.ctx.Cluster.CrossTenantEniMode == constants.EnableStaticMode {
			valuesMap["controller.service.type"] = corev1.ServiceTypeClusterIP
			flinkUiClb, err := service.GetFlinkUiCLB(i.ctx.ClusterGroup.Region)
			if err != nil {
				return nil, err
			}
			// 配置跨租户弹性网卡
			crossTenantEniConfig := fmt.Sprintf("{\"AppId\":%s,\"Uin\":\"%s\",\"UniqVpcId\":\"%s\",\"SubnetId\":\"%s\"}",
				i.ctx.CC().GetFlinkUiClbAppId(), i.ctx.CC().GetFlinkUiClbUin(), flinkUiClb.VpcId, flinkUiClb.SubnetId)
			logger.Infof("crossTenantEniConfig is %s", crossTenantEniConfig)
			valuesMap["controller.podAnnotations"] = map[string]string{
				"tke.cloud.tencent.com/cross-tenant-eni-config": crossTenantEniConfig,
				"tke.cloud.tencent.com/cross-tenant-eni-enable": "true",
				"tke.cloud.tencent.com/networks":                "tke-bridge,tke-direct-eni,tke-route",
			}
		}
	}

	chartPath, err := i.ctx.FlowCC.TkeCC().IngressChartPath()
	if err != nil {
		return nil, err
	}
	opt.ChartPath = chartPath
	opt.Name = service2.GetHelmService().IngressName(i.ctx.ClusterGroup)
	opt.ValuesMap = valuesMap
	return opt, nil
}

func (i *NginxIngress) Transform(_ apps.Client, v interface{}) (interface{}, error) {
	return v, nil
}

func (i *NginxIngress) Apply(s apps.Client, v interface{}) (interface{}, error) {
	helService := service2.GetHelmService()
	client, err := helService.NewClient(s.KubeConfig())
	if err != nil {
		return "", err
	}

	namespace := helService.IngressNamespace(i.ctx.ClusterGroup)

	res, err := helService.ListByName(client, namespace, helService.IngressName(i.ctx.ClusterGroup))
	if err != nil {
		return "", err
	}
	if len(res.Releases) == 1 {
		return res.Releases[0].Name, nil
	} else if len(res.Releases) > 1 {
		return "", errorcode.InternalErrorCode.New()
	}

	status, err := helService.Install(client, namespace, v.(*helm.InstallOptions))

	if err != nil {
		return "", err
	}
	return status.Name, nil
}

func (i *NginxIngress) Ready(s apps.Client, v interface{}) (bool, error) {
	// 检查如下两个deploy是否就绪
	// ingress-nginx-ingress-controller       1/1    1           1          2d3h
	// ingress-nginx-ingress-default-backend  1/1    1           1          2d3h

	k8sService := k8s.GetK8sService()
	helmServcie := service2.GetHelmService()
	namespace := helmServcie.IngressNamespace(i.ctx.ClusterGroup)
	ingressName := helmServcie.IngressName(i.ctx.ClusterGroup)
	controllerName := fmt.Sprintf("%s-nginx-ingress-controller", ingressName)

	deployment := &v1.Deployment{
		ObjectMeta: metav1.ObjectMeta{
			Namespace: namespace,
		},
	}

	for _, name := range []string{
		controllerName,
		fmt.Sprintf("%s-nginx-ingress-default-backend", ingressName),
	} {
		deployment.Name = name
		ready, err := k8sService.DeploymentAppsV1Ready(s.ClientSet(), deployment)
		if err != nil {
			return false, err
		}
		if !ready {
			return false, nil
		}
	}

	// 上海自动驾驶云从 弹性网卡获取地址，没有LoadBalancer
	if i.ctx.ClusterGroup.Region == constants.AP_SHANGHAI_ADC {
		describeWebUIWhiteListService := service.NewDescribeWebUIWhiteListService1(&model.DescribeWebUIWhiteListReq{
			ClusterId: i.ctx.ClusterGroup.SerialId,
			Type:      constants.PrivateType,
		}, s.ClientSet(), i.ctx.ClusterGroup, i.ctx.Cluster)
		result, err := describeWebUIWhiteListService.GetPrivate(i.ctx.ClusterGroup.Id)
		if err != nil {
			return false, errorcode.FailedOperationCode.NewWithErr(err)
		}
		return result, nil
	} else {
		if i.ctx.Cluster.CrossTenantEniMode == constants.EnableStaticMode {
			describeWebUIWhiteListService := service.NewDescribeWebUIWhiteListService1(&model.DescribeWebUIWhiteListReq{
				ClusterId: i.ctx.ClusterGroup.SerialId,
			}, s.ClientSet(), i.ctx.ClusterGroup, i.ctx.Cluster)
			result, err := describeWebUIWhiteListService.GetCrossTenantEni(i.ctx.ClusterGroup.Id)
			if err != nil {
				return false, errorcode.FailedOperationCode.NewWithErr(err)
			}
			return result, nil
		}
	}

	initKey := "ever_set_webui"

	_, exist := i.ctx.GetParam(initKey, "")
	if exist {
		return true, nil
	}

	// 设置WebUIPrefix为 https://${EXTERNAL-IP}/
	// NAME                                    TYPE           CLUSTER-IP      EXTERNAL-IP   PORT(S)                      AGE
	// ingress-nginx-ingress-controller        LoadBalancer   *************   ***********   80:31079/TCP,443:31060/TCP   18h
	ingService, err := s.ClientSet().CoreV1().Services(namespace).Get(controllerName, metav1.GetOptions{})
	if err != nil {
		return false, errorcode.InternalErrorCode.NewWithErr(err)
	}
	if len(ingService.Status.LoadBalancer.Ingress) == 0 {
		return false, errorcode.NewStackError(errorcode.InternalErrorCode,
			fmt.Sprintf("ingress %s LoadBalancer not found", controllerName), nil)
	}
	webUIPrefix := fmt.Sprintf("https://%s/", ingService.Status.LoadBalancer.Ingress[0].IP)

	service4.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.ExecuteSqlWithArgs("update Cluster set WebUIPrefix=? where Id=?", webUIPrefix, i.ctx.Cluster.Id)
		return nil
	}).Close()
	i.ctx.SetReturnParam(initKey, "")
	return true, nil
}

type DeleteIngressApp struct {
	apps.ApplyApp
	ctx *deploy.Context
}

func (i *DeleteIngressApp) Apply(s apps.Client, v interface{}) (interface{}, error) {
	helService := service2.GetHelmService()
	client, err := helService.NewClient(s.KubeConfig())
	if err != nil {
		return nil, err
	}

	namespace := helService.IngressNamespace(i.ctx.ClusterGroup)
	name := helService.IngressName(i.ctx.ClusterGroup)

	res, err := helService.ListByName(client, namespace, name)
	if err != nil {
		return nil, err
	}
	if len(res.Releases) == 0 {
		return nil, nil
	}

	err = helService.DeleteByName(client, name)
	return nil, err
}

type DeleteFlinkUiRuleApp struct {
	ctx *deploy.Context
	apps.ApplyApp
}

func (c *DeleteFlinkUiRuleApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	if c.ctx.Cluster.CrossTenantEniMode == constants.EnableStaticMode {
		logger.Infof("deleteCvmService-DeleteFlinkUiRuleApp %s", c.ctx.ClusterGroup.SerialId)
		flinkVersionService := service.NewFlinkUiClbService(c.ctx.ClientSet(), c.ctx.ClusterGroup, c.ctx.Cluster)
		err = flinkVersionService.DeleteFlinkUiRule()
		if err != nil {
			return false, err
		}
	}
	return true, nil
}
