package taskHandler

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"sync"
	"time"

	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"

	uuid2 "tencentcloud.com/tstream_galileo/src/common/uuid"

	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cvm"

	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"

	"encoding/base64"
	"text/template"

	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common"
	sdkTke "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/tke/v20180525"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config/configure_center"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/tke"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/vpc"
	tke2 "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke"
)

type InitEniService struct {
}

func (s *InitEniService) Apps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)

	cg := ctx.ClusterGroup

	// 共享集群 包年包月下的eks集群，不做任何处理i
	if cg.Type == constants.CLUSTER_GROUP_TYPE_SUB_EKS {
		return result, nil
	}

	ieApp := &conditionInitEniApp{iea: &initEniApp{ctx: ctx}}

	// 创建共享集群母集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone == "" {
		result = append(result, ieApp)
	}

	// 共享集群母集群，开新区
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone != "" {
	}

	// 独享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId == "" {
		result = append(result, ieApp)
		result = append(result, &createNodeEniApp{ctx: ctx})
	}

	// 共享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId != "" {
	}

	// TODO   处理CoreDNS
	return result, nil
}

type conditionInitEniApp struct {
	apps.ApplyReadyApp
	iea *initEniApp
}

func (i *conditionInitEniApp) Apply(c apps.Client, a interface{}) (_ interface{}, err error) {
	if i.iea.ctx.Cluster.SchedulerType != constants.CLUSTER_SCHEDULER_TYPE_TKE {
		return
	}
	// 内网VPC，无需初始化eni
	if i.iea.ctx.ClusterGroup.NetEnvironmentType == constants.NETWORK_ENV_INNER_VPC {
		return
	}
	// 非弹性网卡方案，不初始化eni
	if i.iea.ctx.ConnectionType != constants.CLUSTER_NET_CONNECTION_TYPE_ENI {
		return
	}
	return i.iea.Apply(c, a)
}

func (i *conditionInitEniApp) Ready(c apps.Client, a interface{}) (done bool, err error) {
	if i.iea.ctx.Cluster.SchedulerType != constants.CLUSTER_SCHEDULER_TYPE_TKE {
		return true, nil
	}
	// 内网VPC，无需初始化eni
	if i.iea.ctx.ClusterGroup.NetEnvironmentType == constants.NETWORK_ENV_INNER_VPC {
		return true, nil
	}
	// 非弹性网卡方案，不初始化eni
	if i.iea.ctx.ConnectionType != constants.CLUSTER_NET_CONNECTION_TYPE_ENI {
		return true, nil
	}
	return i.iea.Ready(c, a)
}

type initEniApp struct {
	ctx *deploy.Context
	apps.ApplyReadyApp
}

func (i *initEniApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {

	ever, err := i.everInit()
	if err != nil {
		return
	}
	if ever {
		return
	}

	err = i.init()
	return nil, err
}

func (i *initEniApp) Ready(_ apps.Client, _ interface{}) (done bool, err error) {
	done, err = i.checkInitProgress()
	return
}

func (i *initEniApp) everInit() (ever bool, err error) {
	if i.ctx.ClusterType == constants.K8S_CLUSTER_TYPE_TKE {
		ever, err = i.everInitTke()
	} else {
		ever, err = i.everInitEks()
	}
	return
}

func (i *initEniApp) everInitTke() (ever bool, err error) {
	req := tke.NewDefaultDescribeMetaFeatureProgressRequestBuilder().WithClusterId(i.ctx.Cluster.UniqClusterId).Build()

	ever, _, err = tke.GetTkeService().DescribeMetaFeatureProgressWithScsAccount(i.ctx.ClusterGroup.Region, req)
	if err != nil {
		return
	}
	return
}

func (i *initEniApp) everInitEks() (ever bool, err error) {
	req := tke.NewDefaultDescribeEksMetaFeatureProgressRequestBuilder().WithClusterId(i.ctx.Cluster.UniqClusterId).Build()

	ever, _, err = tke.GetTkeService().DescribeEksMetaFeatureProgressWithScsAccount(i.ctx.ClusterGroup.Region, req)
	if err != nil {
		return
	}
	return
}

func (i *initEniApp) init() (err error) {
	routeConfig, err := i.routeConfig()
	if err != nil {
		return
	}
	tenantParam, err := i.tenantParam()
	if err != nil {
		return
	}

	if i.ctx.ClusterType == constants.K8S_CLUSTER_TYPE_TKE {
		err = i.initTke(routeConfig, tenantParam, true)
	} else {
		err = i.initEks(routeConfig, tenantParam, true)
	}
	return
}

func (i *initEniApp) initTke(routeConfig []*tke2.RouteItem, tenantParam *tke2.TenantParam, staticMod bool) (err error) {
	req := tke.NewDefaultEnableMetaFeatureRequestBuilder().
		WithClusterId(i.ctx.Cluster.UniqClusterId).
		WithRouteConfig(routeConfig).
		WithTenantParam(tenantParam).
		WithStaticMode(staticMod).
		Build()

	logger.Infof("req: %s", req.ToJsonString())
	err = tke.GetTkeService().EnableMetaFeatureWithScsAccount(i.ctx.ClusterGroup.Region, req)
	return
}

func (i *initEniApp) initEks(routeConfig []*tke2.RouteItem, tenantParam *tke2.TenantParam, staticMod bool) (err error) {
	req := tke.NewDefaultEnableMetaFeatureForEksRequestBuilder().
		WithClusterId(i.ctx.Cluster.UniqClusterId).
		WithRouteConfig(routeConfig).
		WithTenantParam(tenantParam).
		Build()

	logger.Infof("initEks req: %s", req.ToJsonString())
	err = tke.GetTkeService().EnableMetaFeatureForEksWithScsAccount(i.ctx.ClusterGroup.Region, req)
	return
}

func (i *initEniApp) checkInitProgress() (done bool, err error) {
	if i.ctx.ClusterType == constants.K8S_CLUSTER_TYPE_TKE {
		done, err = i.checkTkeInitProgress()
	} else {
		done, err = i.checkEksInitProgress()
	}
	return
}

func (i *initEniApp) checkEksInitProgress() (done bool, err error) {
	req := tke.NewDefaultDescribeEksMetaFeatureProgressRequestBuilder().WithClusterId(i.ctx.Cluster.UniqClusterId).Build()

	found, rsp, err := tke.GetTkeService().DescribeEksMetaFeatureProgressWithScsAccount(i.ctx.ClusterGroup.Region, req)
	if err != nil {
		return
	}
	if !found {
		msg := fmt.Sprintf("tke %s EnableEksMetaFeature Process Not Found", i.ctx.Cluster.UniqClusterId)
		err = errorcode.InternalErrorCode.NewWithInfo(msg, nil)
	}

	logger.Infof("DescribeEksMetaFeatureProgress: %s", rsp.ToJsonString())
	done = *rsp.Response.Status == "succeeded"
	return
}

func (i *initEniApp) checkTkeInitProgress() (done bool, err error) {
	req := tke.NewDefaultDescribeMetaFeatureProgressRequestBuilder().WithClusterId(i.ctx.Cluster.UniqClusterId).Build()

	found, rsp, err := tke.GetTkeService().DescribeMetaFeatureProgressWithScsAccount(i.ctx.ClusterGroup.Region, req)
	if err != nil {
		return
	}
	if !found {
		msg := fmt.Sprintf("tke %s EnableMetaFeature Process Not Found", i.ctx.Cluster.UniqClusterId)
		err = errorcode.InternalErrorCode.NewWithInfo(msg, nil)
	}

	logger.Infof("DescribeMetaFeatureProgress: %s", rsp.ToJsonString())
	done = *rsp.Response.Status == "done"
	return
}

func (i *initEniApp) routeConfig() (routeConfig []*tke2.RouteItem, err error) {
	nodeCidr, podCidr, err := i.routeCidr()
	if err != nil {
		return
	}
	// http://rainbow.oa.com/console/c5772813-9438-4229-a777-ed02f76bfed2/Default/list?group_id=16453&group_name=Eni
	// cm-tke-cni-agent-conf.template-v2.yaml
	// 具体规则，可以再问tke的同学。
	routeConfig = make([]*tke2.RouteItem, 0)
	dev := "eth0"
	podType := "pod"
	if i.ctx.ClusterType == constants.K8S_CLUSTER_TYPE_TKE {
		routeConfig = append(routeConfig, &tke2.RouteItem{
			Subnet: nodeCidr,
			Type:   "node",
			Dev:    dev,
		})
	}
	routeConfig = append(routeConfig, &tke2.RouteItem{
		Subnet: nodeCidr,
		Type:   podType,
		Dev:    dev,
	})
	routeConfig = append(routeConfig, &tke2.RouteItem{
		Subnet: podCidr,
		Type:   podType,
		Dev:    dev,
	})
	routeConfig = append(routeConfig, &tke2.RouteItem{
		Subnet: "169.254.0.0/16",
		Type:   podType,
		Dev:    dev,
	})
	routeConfig = append(routeConfig, &tke2.RouteItem{
		Subnet: "169.254.0.0/17",
		Type:   podType,
		Dev:    "eth1",
	})
	if i.ctx.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		routeConfig = append(routeConfig, &tke2.RouteItem{
			Subnet: "0.0.0.0/0",
			Type:   podType,
			Dev:    "eth1",
		})
	}
	return
}

func (i *initEniApp) routeCidr() (nodeCidr, podCidr string, err error) {
	vpcService := vpc.GetVpcService()
	total, vpcSet, err := vpcService.DescribeVpcsWithScsAccount(i.ctx.ClusterGroup.Region,
		vpcService.NewDefaultDescribeVpcsRequestBuilder().
			WithVpcIds([]string{i.ctx.Cluster.VpcId}).
			Build())
	if err != nil {
		err = errorcode.InternalErrorCode.NewWithErr(err)
		return
	}
	if total == 0 {
		msg := fmt.Sprintf("none vpc found for cluster %s", i.ctx.ClusterGroup.SerialId)
		err = errorcode.InternalErrorCode.NewWithInfo(msg, nil)
		return
	}
	nodeCidr = *vpcSet[0].CidrBlock

	if i.ctx.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		podCidr, err = i.eksPodCidr()
	} else {
		podCidr, err = i.tkePodCidr()
	}
	return
}

func (i *initEniApp) eksPodCidr() (podCidr string, err error) {
	tkeService := tke.GetTkeService()
	describeEKSClustersRequest := sdkTke.NewDescribeEKSClustersRequest()
	describeEKSClustersRequest.Filters = []*sdkTke.Filter{
		{
			Name:   common.StringPtr("ClusterName"),
			Values: common.StringPtrs([]string{i.ctx.ClusterGroup.SerialId}),
		},
	}
	totalCount, clusters, err := tkeService.DescribeEKSClustersWithScsAccount(
		i.ctx.ClusterGroup.Region, describeEKSClustersRequest)
	if err != nil {
		err = errorcode.InternalErrorCode.NewWithErr(err)
		return
	}
	if totalCount == 0 {
		msg := fmt.Sprintf("eks %s not found", i.ctx.Cluster.UniqClusterId)
		err = errorcode.InternalErrorCode.NewWithInfo(msg, nil)
		return
	}
	podCidr = *clusters[0].ServiceSubnetId
	return
}

func (i *initEniApp) tkePodCidr() (podCidr string, err error) {
	tkeService := tke.GetTkeService()
	totalCount, clusters, err := tkeService.DescribeClustersWithScsAccount(i.ctx.ClusterGroup.Region,
		tkeService.NewDefaultDescribeClustersRequestBuilder().
			WithClusterIds(i.ctx.Cluster.UniqClusterId).
			Build())
	if err != nil {
		err = errorcode.InternalErrorCode.NewWithErr(err)
		return
	}
	if totalCount == 0 {
		msg := fmt.Sprintf("tke %s not found", i.ctx.Cluster.UniqClusterId)
		err = errorcode.InternalErrorCode.NewWithInfo(msg, nil)
		return
	}

	podCidr = *clusters[0].ClusterNetworkSettings.ClusterCIDR
	return
}

func (i *initEniApp) tenantParam() (param *tke2.TenantParam, err error) {
	// # KVM1.0机型列表如下，ENI配额 7 ：
	// # S1、S2、S2ne、M1、M2、I1、I2、C2、D1、IT2、DR1、GN2、GN8、GA2、GN6
	// # 其他机型均为KVM3.0，ENI配额 40
	// tke 那边 底层逻辑会针对每个Node节点单独查询VPC侧的最高限额的
	eniLimit := 40
	clusterGroupService, err := service.NewClusterGroupService(i.ctx.ClusterGroup.Id)
	if err != nil {
		logger.Errorf("Failed to call NewClusterGroupService because %+v", err)
		return nil, err
	}
	groupPeerVpc, err := clusterGroupService.GetGroupPeerVpc()
	if err != nil {
		logger.Errorf("Failed to call GetGroupPeerVpc because %+v", err)
		return nil, err
	}

	// groupPeerVpc.AppId = 0 表明， 是一个正常的用户， Oceanus集群和VPC都在用户账号下面
	// groupPeerVpc.AppId ！= 0 表明，就是wedata的用户， Oceanus集群托管到wedata的运营账号， VPC是用户的
	appId := groupPeerVpc.AppId
	ownerUin := groupPeerVpc.OwnerUin
	if appId == 0 || ownerUin == "" {
		appId = i.ctx.ClusterGroup.AppId
		ownerUin = i.ctx.ClusterGroup.OwnerUin
	}

	return &tke2.TenantParam{
		AppId:     uint64(appId),
		Uin:       ownerUin,
		UniqVpcId: groupPeerVpc.VpcId,
		SubnetId:  groupPeerVpc.SubnetId,
		ENILimit:  int64(eniLimit),
	}, nil
}

// 添加 ENI 配置结构体
type EniConfig struct {
	EniIP       string
	EniCIDR     string
	NodeEniType int8
	RouteCIDRs  []string
}

type createNodeEniApp struct {
	ctx *deploy.Context
	apps.ApplyApp
}

func (c *createNodeEniApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	var uuid = uuid2.New()
	if c.ctx.ClusterGroup.NetEniType == constants.CLUSTER_NET_ENI_NODE {
		defer func() {
			if errs := recover(); errs != nil {
				logger.Errorf("[initEniApp] recover error: %v", errs)
			}
		}()

		// 1. 获取集群节点信息并创建弹性网卡
		tkeService := tke.GetTkeService()
		_, instanceSet, err := tkeService.DescribeClusterAllInstancesWithScsAccountByNetEnvironmentType(
			c.ctx.ClusterGroup.NetEnvironmentType,
			c.ctx.ClusterGroup.Region,
			c.ctx.Cluster.UniqClusterId)
		if err != nil {
			return nil, err
		}
		logger.Debugf("step-1: Successfully got cluster instances, clusterId:%s,uuid:%s", c.ctx.ClusterGroup.SerialId, uuid)

		// 2. 为每个节点创建弹性网卡
		if c.ctx.ClusterGroup.NodeEniType != constants.CLUSTER_NODE_ENI_TKE {
			for _, instance := range instanceSet {
				if *instance.InstanceId == "" {
					continue
				}
				if err := c.createEniForNode(*instance.InstanceId); err != nil {
					return nil, err
				}
			}
			logger.Debugf("step-2: Successfully created ENIs for all nodes, clusterId:%s,uuid:%s", c.ctx.ClusterGroup.SerialId, uuid)
		}

		// 3. 获取脚本并准备执行命令
		var eniScript string
		if c.ctx.ClusterGroup.NodeEniType == constants.CLUSTER_NODE_ENI_TKE {
			eniScript, err = configure_center.CC(c.ctx.ClusterGroup.Region).FlowCC().TkeCC().ENIScript()
		} else {
			eniScript, err = configure_center.CC(c.ctx.ClusterGroup.Region).FlowCC().TkeCC().ENIAutoScript()
		}
		if err != nil {
			return nil, err
		}
		logger.Debugf("step-3: Successfully got ENI script, clusterId:%s,uuid:%s", c.ctx.ClusterGroup.SerialId, uuid)

		// 4. 并发执行命令
		maxConcurrent := 5
		sem := make(chan struct{}, maxConcurrent)
		var wg sync.WaitGroup
		errChan := make(chan error, len(instanceSet))

		for _, instance := range instanceSet {
			if *instance.InstanceId == "" {
				continue
			}

			wg.Add(1)
			sem <- struct{}{}

			go func(instanceId *string) {
				defer func() {
					<-sem
					wg.Done()
				}()

				var eniConfig *EniConfig
				if c.ctx.ClusterGroup.NodeEniType != constants.CLUSTER_NODE_ENI_TKE {
					// 获取网卡信息并处理脚本
					eniConfig, err = c.getEniInfo(*instanceId, 5)
					if err != nil {
						errChan <- err
						return
					}
				} else {
					routeConfig, err := c.ctx.Cluster.GetRouteConfig()
					if err != nil {
						errChan <- err
						return
					}
					eniConfig = &EniConfig{
						RouteCIDRs: routeConfig,
					}
				}

				tmpl, err := template.New("eni-script").Parse(eniScript)
				if err != nil {
					errChan <- fmt.Errorf("failed to parse template for instance %s: %v", *instanceId, err)
					return
				}

				var buf bytes.Buffer
				if err := tmpl.Execute(&buf, eniConfig); err != nil {
					errChan <- fmt.Errorf("failed to execute template for instance %s: %v", *instanceId, err)
					return
				}

				finalScript := base64.StdEncoding.EncodeToString(buf.Bytes())
				logger.Debugf("NodeENIScript: %s", finalScript)
				c.executeCommand(instanceId, finalScript, errChan, 5)
			}(instance.InstanceId)
		}

		go func() {
			wg.Wait()
			close(errChan)
		}()

		var errors []error
		for err := range errChan {
			errors = append(errors, err)
		}

		if len(errors) > 0 {
			errMsgs := make([]string, len(errors))
			for i, err := range errors {
				errMsgs[i] = err.Error()
			}
			return nil, fmt.Errorf("multiple errors occurred: %s", strings.Join(errMsgs, "; "))
		}

		logger.Debugf("step-4: Successfully executed ENI script for all nodes, clusterId:%s,uuid:%s", c.ctx.ClusterGroup.SerialId, uuid)
	}
	return nil, nil
}

func (c *createNodeEniApp) getEniInfo(instanceId string, retries ...int) (*EniConfig, error) {
	maxRetries := 0
	if len(retries) > 0 {
		maxRetries = retries[0]
	}

	// 获取 peerVpc 信息
	clusterGroupService, err := service.NewClusterGroupServiceBySerialId(c.ctx.ClusterGroup.SerialId)
	if err != nil {
		return nil, err
	}
	peerVpc, err := clusterGroupService.GetGroupPeerVpc()
	if err != nil {
		return nil, err
	}

	vpcEx := vpc.GetVpcServiceEx()
	b := vpcEx.NewDefaultDescribeNetworkInterfacesExBuilder()
	b.WithVpcAppId(int64(c.ctx.ClusterGroup.AppId))
	b.WithVpcUin(c.ctx.ClusterGroup.OwnerUin)
	b.WithOffset(0)
	b.WithLimit(100)
	b.WithFilters(peerVpc.VpcId, "", "", instanceId, "")

	var lastErr error
	for i := 0; i <= maxRetries; i++ {
		if i > 0 {
			logger.Debugf("Retrying get ENI info for instance %s, attempt %d/%d", instanceId, i, maxRetries)
			time.Sleep(5 * time.Second)
		}

		resp, err := vpcEx.DescribeNetworkInterfacesExWithScsAccount(c.ctx.ClusterGroup.Region, b.Build())
		if err != nil {
			lastErr = err
			logger.Warningf("Failed to describe ENIs for instance %s: %v", instanceId, err)
			continue
		}

		for _, eni := range resp.Response.NetworkInterfaceSet {
			if eni.Attachment != nil && *eni.Attachment.InstanceId == instanceId {
				if len(eni.PrivateIpAddressSet) > 0 {
					return &EniConfig{
						EniIP:       *eni.PrivateIpAddressSet[0].PrivateIpAddress,
						EniCIDR:     "17",
						NodeEniType: c.ctx.ClusterGroup.NodeEniType,
						RouteCIDRs:  []string{},
					}, nil
				}
			}
		}
		lastErr = fmt.Errorf("retry %d: no ENI found for instance %s", i, instanceId)
	}

	return nil, lastErr
}

func (c *createNodeEniApp) executeCommand(instanceId *string, script string, errChan chan<- error, statusRetries ...int) {
	if instanceId == nil {
		errChan <- fmt.Errorf("instance ID is nil")
		return
	}
	if script == "" {
		errChan <- fmt.Errorf("script is empty")
		return
	}

	cvmService := cvm.GetCvmService()
	commandRespSet, err := cvmService.RunCommandForIdsWithScsAccount(c.ctx.ClusterGroup.NetEnvironmentType,
		c.ctx.ClusterGroup.Region, []*string{instanceId}, script)
	if err != nil {
		errChan <- err
		return
	}
	if len(commandRespSet) == 0 {
		errChan <- fmt.Errorf("failed to run command for instance %s: empty response", *instanceId)
		return
	}

	if commandRespSet[0].InvocationId == nil {
		errChan <- fmt.Errorf("invocation ID is nil for instance %s", *instanceId)
		return
	}

	invocationId := *commandRespSet[0].InvocationId
	maxRetries := 0
	if len(statusRetries) > 0 {
		maxRetries = statusRetries[0]
	}
	time.Sleep(3 * time.Second)

	success := false
	for i := 0; i <= maxRetries; i++ {
		count, respSet, err := cvmService.DescribeInvocationsForIdsWithScsAccount(
			c.ctx.ClusterGroup.NetEnvironmentType,
			c.ctx.ClusterGroup.Region,
			[]*string{&invocationId})

		if err != nil || count == 0 {
			logger.Warningf("Attempt %d: Failed to check command status for instance %s: %v", i+1, *instanceId, err)
			time.Sleep(5 * time.Second)
			continue
		}

		if respSet[0].InvocationStatus == nil {
			logger.Warningf("Invocation status is nil for instance %s", *instanceId)
			time.Sleep(5 * time.Second)
			continue
		}

		status := *respSet[0].InvocationStatus
		if status == "SUCCESS" {
			success = true
			logger.Debugf("Command executed successfully for instance %s", *instanceId)
			break
		}

		if i < maxRetries {
			time.Sleep(5 * time.Second)
		}
	}

	if !success {
		errChan <- fmt.Errorf("failed to get successful command execution result for instance %s after %d attempts", *instanceId, maxRetries+1)
	}
}

func (c *createNodeEniApp) createEniForNode(instanceId string) error {
	peerVpc, err := c.ctx.ClusterGroupService.GetGroupPeerVpc()
	if err != nil {
		return err
	}

	// 1. 查询节点的弹性网卡
	vpcEx := vpc.GetVpcServiceEx()
	b := vpcEx.NewDefaultDescribeNetworkInterfacesExBuilder()
	b.WithVpcAppId(int64(c.ctx.ClusterGroup.AppId))
	b.WithVpcUin(c.ctx.ClusterGroup.OwnerUin)
	b.WithOffset(0)
	b.WithLimit(100)
	b.WithFilters(peerVpc.VpcId, "", "", instanceId, "")

	resp, err := vpcEx.DescribeNetworkInterfacesExWithScsAccount(c.ctx.ClusterGroup.Region, b.Build())
	if err != nil {
		logger.Warningf("Failed to describe ENIs for instance %s: %v", instanceId, err)
		return nil
	}

	// 2. 遍历找到eth1网卡并返回
	for _, eni := range resp.Response.NetworkInterfaceSet {
		if eni.Attachment != nil && *eni.Attachment.InstanceId == instanceId {
			return nil
		}
	}

	// 3. 没有找到eth1网卡，创建eth1网卡
	b2 := vpcEx.NewDefaultCreateNetworkInterfaceExBuilder()
	b2.WithVpcId(peerVpc.VpcId)
	b2.WithSubnetId(peerVpc.SubnetId)
	b2.WithInstanceId(instanceId)
	b2.WithVpcAppId(uint64(c.ctx.ClusterGroup.AppId))
	b2.WithVpcUin(c.ctx.ClusterGroup.OwnerUin)
	b2.WithNetworkInterfaceDescription("eth1")
	b2.WithNetworkInterfaceName("eth1")

	_, err = vpcEx.CreateNetworkInterfaceExWithScsAccount(c.ctx.ClusterGroup.Region, b2.Build())
	if err != nil {
		logger.Errorf("Failed to create network interface for node %s: %v", instanceId, err)
		return err
	}
	logger.Debugf("Successfully created network interface for node %s", instanceId)

	return nil
}

type scaleDownNodeEniApp struct {
	ctx *deploy.Context
	apps.ApplyApp
}

func (d *scaleDownNodeEniApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	if d.ctx.ClusterGroup.NetEniType == constants.CLUSTER_NET_ENI_NODE &&
		(d.ctx.ClusterGroup.NodeEniType == constants.CLUSTER_NODE_ENI_ETH1 ||
			d.ctx.ClusterGroup.NodeEniType == constants.CLUSTER_NODE_ENI_ETH0) {
		// 获取删除的实例ID
		var instanceSucc map[string]struct{}
		_, err = d.ctx.GetParamUseJson(constants.FlowParamDeleteTkeInstanceSucc, &instanceSucc)
		if err != nil {
			return nil, errorcode.InternalErrorCode.NewWithErr(err)
		}
		for id := range instanceSucc {
			if err = DeleteEniForNode(id, d.ctx.ClusterGroup, d.ctx.ClusterGroupService); err != nil {
				return err, nil
			}
		}
	}
	return
}

type deleteNodeEniApp struct {
	ctx *deploy.Context
	apps.ApplyApp
}

func (d *deleteNodeEniApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	if d.ctx.ClusterGroup.NetEniType == constants.CLUSTER_NET_ENI_NODE &&
		(d.ctx.ClusterGroup.NodeEniType == constants.CLUSTER_NODE_ENI_ETH1 ||
			d.ctx.ClusterGroup.NodeEniType == constants.CLUSTER_NODE_ENI_ETH0) {
		canDeleteIds, othersIds, err := getTkeCvm(d.ctx, cvm.GetCvmService().InstanceCanTerminate)
		if err != nil {
			return nil, err
		}
		if len(othersIds) > 0 {
			b, _ := json.Marshal(othersIds)
			return nil, errors.New(fmt.Sprintf("can not delete cvms: %s", string(b)))
		}
		if err = d.DeleteEniForCluster(canDeleteIds); err != nil {
			return nil, errorcode.InternalErrorCode.NewWithErr(err)
		}
	}
	return
}

func DeleteEniForNode(instanceId string, clusterGroup *table.ClusterGroup, clusterGroupService *service.ClusterGroupService) error {
	// 获取 peerVpc 信息
	peerVpc, err := clusterGroupService.GetGroupPeerVpc()
	if err != nil {
		return err
	}

	vpcEx := vpc.GetVpcServiceEx()
	b := vpcEx.NewDefaultDescribeNetworkInterfacesExBuilder()
	b.WithOffset(0)
	b.WithLimit(100)
	b.WithVpcAppId(int64(clusterGroup.AppId))
	b.WithVpcUin(clusterGroup.OwnerUin)
	b.WithFilters(peerVpc.VpcId, "", "", instanceId, "")

	resp, err := vpcEx.DescribeNetworkInterfacesExWithScsAccount(clusterGroup.Region, b.Build())
	if err != nil {
		logger.Warningf("Failed to describe ENIs for instance %s: %v", instanceId, err)
		return nil
	}

	// 2. 遍历找到eth1网卡并删除
	for _, eni := range resp.Response.NetworkInterfaceSet {
		if eni.Attachment != nil && *eni.Attachment.InstanceId == instanceId {
			// 直接删除网卡
			deleteBuilder := vpcEx.NewDefaultDeleteNetworkInterfaceExBuilder()
			deleteBuilder.WithNetworkInterfaceId(*eni.NetworkInterfaceId)
			deleteBuilder.WithVpcId(peerVpc.VpcId)
			deleteBuilder.WithVpcAppId(uint64(clusterGroup.AppId))
			deleteBuilder.WithVpcUin(clusterGroup.OwnerUin)
			_, err = vpcEx.DeleteNetworkInterfaceExWithScsAccount(clusterGroup.Region, deleteBuilder.Build())
			if err != nil {
				logger.Errorf("Failed to delete network interface %s: %v", *eni.NetworkInterfaceId, err)
				return err
			}
			return nil
		}
	}

	logger.Debugf("No ENI found for instance %s to delete", instanceId)
	return nil
}

func (d *deleteNodeEniApp) DeleteEniForCluster(instanceIds []*string) error {
	if len(instanceIds) == 0 {
		return nil
	}
	for _, instance := range instanceIds {
		if *instance == "" {
			continue
		}
		if err := DeleteEniForNode(*instance, d.ctx.ClusterGroup, d.ctx.ClusterGroupService); err != nil {
			return err
		}
	}

	logger.Debugf("Successfully deleted all ENIs for %d instances", len(instanceIds))
	return nil
}
