package taskHandler

import (
	"encoding/json"
	"errors"
	"fmt"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/dlocker"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	table3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/Cvm"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	service4 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/tke"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
	tke4 "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke/v20220501"
)

type CreateCvmService struct {
}

func (s *CreateCvmService) Apps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	cg := ctx.ClusterGroup

	// 共享集群 包年包月下的eks集群，不做任何处理
	if cg.Type == constants.CLUSTER_GROUP_TYPE_SUB_EKS {
		return result, nil
	}

	// 创建共享集群母集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone == "" {
		result = append(result, &createPoolApp{ctx: ctx,
			zone: cg.Zone,
		})
		//result = append(result, &transformPoolWorkerToControllerApp{ctx: ctx,
		//	zone: cg.Zone,
		//})
		//result = append(result, &createPoolWorkerForControllerApp{ctx: ctx,
		//	zone: cg.Zone,
		//})
		result = append(result, &uniformClusterCreateControllerApp{ctx: ctx,
			zone: cg.Zone,
		})

		result = append(result, &updateTkeStatusApp{ctx: ctx})
		result = append(result, &updateClusterCuApp{ctx: ctx})
	}

	// 共享集群母集群，开新区
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone != "" {
		result = append(result, &createPoolApp{ctx: ctx,
			zone: ctx.NewZone,
		})
		//result = append(result, &transformPoolWorkerToControllerApp{ctx: ctx,
		//	zone: ctx.NewZone,
		//})
		//result = append(result, &createPoolWorkerForControllerApp{ctx: ctx,
		//	zone: ctx.NewZone,
		//})
		result = append(result, &uniformClusterCreateControllerApp{ctx: ctx,
			zone: ctx.NewZone,
		})
	}

	// 独享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId == "" {
		result = append(result, &transformPooledCvmApp{ctx: ctx})
		result = append(result, &privateClusterCreateWorkerApp{ctx: ctx})
		result = append(result, &updateClusterCuApp{ctx: ctx})
		result = append(result, &addNodeLabelApp{ctx: ctx})
		result = append(result, &updateTkeStatusApp{ctx: ctx})
	}

	// 共享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId != "" {
		if cg.ResourceType == constants.RESOURCE_TYPE_PRIVATE {
			result = append(result, &transformPoolWorkerToWorkerApp{ctx: ctx})
			result = append(result, &uniformClusterTransformWorkerToPrivateApp{ctx: ctx})
			//result = append(result, &createPoolWorkerForWorkerApp{ctx: ctx})
			//result = append(result, &uniformClusterCreateWorkerApp{ctx: ctx})
		}

		result = append(result, &updateTkeStatusApp{ctx: ctx})
		result = append(result, &updateClusterCuApp{ctx: ctx})
	}

	return result, nil
}

func FindPooledCvmMap(cg *table.ClusterGroup, zone string, count int) (result []*table3.PooledCvm, err error) {
	if count <= 0 {
		return
	}
	if cg.CuMem != constants.CVM_DEFAULT_MEMRATIO {
		return
	}
	locker := dlocker.NewDlocker("FindPooledCvmMap", fmt.Sprintf("optId-%s", "all"), 120)
	err = locker.Lock()
	if err != nil {
		return
	}
	defer locker.UnLock()

	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		sql, args := dao.NewQueryBuilder("select * from PooledCvm").
			WhereEq("Status", constants.CVM_POOL_STATUS_VALID).
			WhereEq("Region", cg.Region).
			WhereEq("Zone", zone).
			OrderBy("PoolTime").
			Limit(count).
			Build()

		result, err = service3.TFetch[table3.PooledCvm](tx, sql, args)
		if err != nil {
			return err
		}
		if len(result) <= 0 {
			return nil
		}

		instanceIdSet := make([]string, 0)
		for _, v := range result {
			instanceIdSet = append(instanceIdSet, v.InstanceId)
		}

		sql, args = dao.NewUpdateBuilder("update PooledCvm").
			Set("UnPooledBy", cg.SerialId).
			Set("UnPoolTime", util.GetCurrentTime()).
			Set("Status", constants.CVM_POOL_STATUS_PENDING).
			WhereEq("Status", constants.CVM_POOL_STATUS_VALID).
			WhereIn("InstanceId", instanceIdSet).
			Build()

		service3.TExecute(tx, sql, args)

		return nil
	}).Close()
	return
}

type uniformClusterTransformWorkerToPrivateApp struct {
	apps.ApplyApp
	ctx    *deploy.Context
	needCU int16

	canTryMap       map[string]struct{}
	noJobMap        map[string]struct{}
	unSchedulerMap  map[string]struct{}
	confirmNoJobMap map[string]struct{}
	addLabelMap     map[string]struct{}
	schedulerMap    map[string]struct{}

	tmpNodeMap map[string]*v1.Node
}

func (c *uniformClusterTransformWorkerToPrivateApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	cg := c.ctx.ClusterGroup
	// lock for uniform cluster
	optId := cg.SerialId
	if cg.AgentSerialId != "" {
		optId = cg.AgentSerialId
	}
	locker := dlocker.NewDlocker("uniformClusterTransformWorkerToPrivateApp", fmt.Sprintf("optId-%s", optId), 120)
	err = locker.Lock()
	if err != nil {
		return false, err
	}
	defer locker.UnLock()

	h := &ApplyTKEHandler{}

	if c.needCU <= 0 {
		c.needCU = cg.CuNum
	}

	nodeNum, _, _, err := h.ComputeWorkerNodeAndCuNum(c.needCU, c.ctx)
	if err != nil {
		return
	}
	needCvmCount, _, err := c.ctx.GetParamInt(constants.FlowParamNeedCvmCount, int64(nodeNum))
	if err != nil {
		return
	}
	c.ctx.SetReturnParam(constants.FlowParamNeedCvmCount, fmt.Sprintf("%d", needCvmCount))

	if err = c.tryTransformWorkerToPrivate(int(needCvmCount)); err != nil {
		return nil, err
	}
	return nil, nil
}

func (c *uniformClusterTransformWorkerToPrivateApp) tryTransformWorkerToPrivate(count int) (err error) {
	c.canTryMap = make(map[string]struct{})
	c.tmpNodeMap = make(map[string]*v1.Node)
	tryMapExist, err := c.ctx.GetParamUseJson(constants.FlowParamCanTryCvm, &c.canTryMap)
	if err != nil {
		return
	}

	c.noJobMap = make(map[string]struct{})
	_, err = c.ctx.GetParamUseJson(constants.FlowParamNoJobCvm, &c.noJobMap)
	if err != nil {
		return
	}

	c.unSchedulerMap = make(map[string]struct{})
	_, err = c.ctx.GetParamUseJson(constants.FlowParamUnSchedulerCvm, &c.unSchedulerMap)
	if err != nil {
		return
	}

	c.confirmNoJobMap = make(map[string]struct{})
	_, err = c.ctx.GetParamUseJson(constants.FlowParamConfirmNoJobCvm, &c.confirmNoJobMap)
	if err != nil {
		return
	}

	c.addLabelMap = make(map[string]struct{})
	_, err = c.ctx.GetParamUseJson(constants.FlowParamAddLabelCvm, &c.addLabelMap)
	if err != nil {
		return
	}
	c.schedulerMap = make(map[string]struct{})
	_, err = c.ctx.GetParamUseJson(constants.FlowParamSchedulerCvm, &c.schedulerMap)
	if err != nil {
		return
	}

	k8sService := c.ctx.K8sService()

	cg := c.ctx.ClusterGroup

	shareLabel := GetShareWorkerLabel(cg.Zone)
	//1. 找到共享集群的CVM
	if !tryMapExist {
		workerList, err := k8sService.ListNode(c.ctx.ClientSet(), metav1.ListOptions{
			LabelSelector: labels.FormatLabels(shareLabel),
		})
		if err != nil {
			return err
		}
		for _, v := range workerList.Items {
			item := v
			labelMap := item.GetObjectMeta().GetLabels()
			instanceId, ok := labelMap["cloud.tencent.com/node-instance-id"]
			if !ok {
				msg := fmt.Sprintf("not find instance id for %s, name %s", cg.SerialId, item.Name)
				return errors.New(msg)
			}
			c.canTryMap[instanceId] = struct{}{}
			c.tmpNodeMap[instanceId] = &item
		}
		c.ctx.SetReturnParamUseJson(constants.FlowParamCanTryCvm, c.canTryMap)

		b, _ := json.Marshal(c.canTryMap)
		logger.Infof("%s: %s", constants.FlowParamCanTryCvm, string(b))
	}
	//2. 找到没有Job运行的CVM
	if err = c.findNoJobCvm(); err != nil {
		return
	}

	//3. 给CVM进行unscheduler
	if err = c.unSchedulerCVM(count); err != nil {
		return
	}

	//4. 确认没有Job运行的CVM
	if err = c.confirmNoJobCvm(); err != nil {
		return
	}
	//5. 打上独享worker标签
	if err = c.addCVMLabels(); err != nil {
		return
	}

	//6. 给CVM进行scheduler
	if err = c.schedulerCVM(); err != nil {
		return
	}

	return
}

func (c *uniformClusterTransformWorkerToPrivateApp) schedulerCVM() (err error) {
	k8sService := k8s.GetK8sService()
	for i := range c.unSchedulerMap {
		instanceId := i
		if _, ok := c.schedulerMap[instanceId]; ok {
			continue
		}
		node, err := getNodeById(c.ctx, instanceId, c.tmpNodeMap)
		if err != nil {
			return err
		}
		if _, err = k8sService.NodeSchedule(c.ctx.ClientSet(), node.Name, false); err != nil {
			return err
		}
		c.schedulerMap[instanceId] = struct{}{}
		c.ctx.SetReturnParamUseJson(constants.FlowParamSchedulerCvm, c.schedulerMap)
	}

	b, _ := json.Marshal(c.schedulerMap)
	logger.Infof("%s: %s", constants.FlowParamSchedulerCvm, string(b))
	return
}

func (c *uniformClusterTransformWorkerToPrivateApp) addCVMLabels() (err error) {
	for i := range c.confirmNoJobMap {
		instanceId := i
		if _, ok := c.addLabelMap[instanceId]; ok {
			continue
		}
		node, err := getNodeById(c.ctx, instanceId, c.tmpNodeMap)
		if err != nil {
			return err
		}

		nodes := make([]v1.Node, 0)
		nodes = append(nodes, *node)

		err = transformShareCvmToPrivate(c.ctx, nodes)
		if err != nil {
			return err
		}

		c.addLabelMap[instanceId] = struct{}{}
		c.ctx.SetReturnParamUseJson(constants.FlowParamAddLabelCvm, c.addLabelMap)
	}

	b, _ := json.Marshal(c.addLabelMap)
	logger.Infof("%s: %s", constants.FlowParamAddLabelCvm, string(b))
	return
}

func (c *uniformClusterTransformWorkerToPrivateApp) confirmNoJobCvm() (err error) {
	k8sService := k8s.GetK8sService()
	for i := range c.unSchedulerMap {
		instanceId := i
		if _, ok := c.confirmNoJobMap[instanceId]; ok {
			continue
		}
		node, err := getNodeById(c.ctx, instanceId, c.tmpNodeMap)
		if err != nil {
			return err
		}

		opts := metav1.ListOptions{
			FieldSelector: "spec.nodeName=" + node.Name,
			LabelSelector: "type=flink-native-kubernetes",
		}

		jobPodList, err := k8sService.ListPod(c.ctx.ClientSet(), "", opts)
		if err != nil {
			return err
		}

		if len(jobPodList.Items) != 0 {
			continue
		}

		c.confirmNoJobMap[instanceId] = struct{}{}
		c.ctx.SetReturnParamUseJson(constants.FlowParamConfirmNoJobCvm, c.confirmNoJobMap)
	}

	b, _ := json.Marshal(c.confirmNoJobMap)
	logger.Infof("%s: %s", constants.FlowParamConfirmNoJobCvm, string(b))
	return
}

func (c *uniformClusterTransformWorkerToPrivateApp) unSchedulerCVM(count int) (err error) {
	k8sService := k8s.GetK8sService()
	for i := range c.noJobMap {
		instanceId := i
		if _, ok := c.unSchedulerMap[instanceId]; ok {
			continue
		}

		if len(c.unSchedulerMap) >= count {
			break
		}

		node, err := getNodeById(c.ctx, instanceId, c.tmpNodeMap)
		if err != nil {
			return err
		}
		if _, err = k8sService.NodeSchedule(c.ctx.ClientSet(), node.Name, true); err != nil {
			return err
		}
		c.unSchedulerMap[instanceId] = struct{}{}
		c.ctx.SetReturnParamUseJson(constants.FlowParamUnSchedulerCvm, c.unSchedulerMap)
	}
	b, _ := json.Marshal(c.unSchedulerMap)
	logger.Infof("%s: %s", constants.FlowParamUnSchedulerCvm, string(b))

	c.noJobMap = make(map[string]struct{})
	c.ctx.SetReturnParamUseJson(constants.FlowParamNoJobCvm, c.noJobMap)
	return
}

func (c *uniformClusterTransformWorkerToPrivateApp) findNoJobCvm() (err error) {
	k8sService := k8s.GetK8sService()
	for i := range c.canTryMap {
		instanceId := i
		if _, ok := c.noJobMap[instanceId]; ok {
			continue
		}
		node, err := getNodeById(c.ctx, instanceId, c.tmpNodeMap)
		if err != nil {
			return err
		}

		opts := metav1.ListOptions{
			FieldSelector: "spec.nodeName=" + node.Name,
			LabelSelector: "type=flink-native-kubernetes",
		}

		jobPodList, err := k8sService.ListPod(c.ctx.ClientSet(), "", opts)
		if err != nil {
			return err
		}

		if len(jobPodList.Items) != 0 {
			continue
		}

		c.noJobMap[instanceId] = struct{}{}
		c.ctx.SetReturnParamUseJson(constants.FlowParamNoJobCvm, c.noJobMap)
	}
	b, _ := json.Marshal(c.noJobMap)
	logger.Infof("%s: %s", constants.FlowParamNoJobCvm, string(b))

	c.canTryMap = make(map[string]struct{})
	c.ctx.SetReturnParamUseJson(constants.FlowParamCanTryCvm, c.canTryMap)
	return
}

type transformPoolWorkerToWorkerApp struct {
	apps.ApplyApp
	ctx    *deploy.Context
	needCU int16

	zone string
}

func (c *transformPoolWorkerToWorkerApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	cg := c.ctx.ClusterGroup
	optId := cg.SerialId
	if cg.AgentSerialId != "" {
		optId = cg.AgentSerialId
	}
	locker := dlocker.NewDlocker("transformPoolWorkerApp", fmt.Sprintf("optId-%s", optId), 120)
	err = locker.Lock()
	if err != nil {
		return false, err
	}
	defer locker.UnLock()

	c.zone, _ = c.ctx.GetParam(constants.FLOW_PARAM_ZONE, cg.Zone)
	if c.zone == "" {
		return nil, fmt.Errorf("zone is empty")
	}

	h := &ApplyTKEHandler{}

	if c.needCU <= 0 {
		c.needCU = cg.CuNum
	}

	nodeNum, _, _, err := h.ComputeWorkerNodeAndCuNum(c.needCU, c.ctx)
	if err != nil {
		return
	}

	needCvmCount, _, err := c.ctx.GetParamInt(constants.FlowParamNeedCvmCount, int64(nodeNum))
	if err != nil {
		return
	}
	c.ctx.SetReturnParam(constants.FlowParamNeedCvmCount, fmt.Sprintf("%d", needCvmCount))

	noteLabel := h.GetWorkerLabel(cg, c.zone)
	err = tryTransformPoolWorker(c.ctx, c.zone, needCvmCount, noteLabel)
	if err != nil {
		return nil, err
	}
	return nil, nil
}

type transformPoolWorkerToControllerApp struct {
	apps.ApplyApp
	ctx  *deploy.Context
	zone string
}

func (c *transformPoolWorkerToControllerApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	cg := c.ctx.ClusterGroup
	optId := cg.SerialId
	if cg.AgentSerialId != "" {
		optId = cg.AgentSerialId
	}
	locker := dlocker.NewDlocker("transformPoolWorkerApp", fmt.Sprintf("optId-%s", optId), 120)
	err = locker.Lock()
	if err != nil {
		return false, err
	}
	defer locker.UnLock()

	h := &ApplyTKEHandler{}

	if allNodeRunning, err := tryMakeAllPoolNodesRunning(c.ctx, c.zone); err != nil {
		return nil, err
	} else if !allNodeRunning {
		return nil, errors.New("wait all pool node running")
	}

	label := h.GetControllerLabel(c.ctx)
	label[constants.TKE_CVM_NODE_ZONE] = c.zone
	label[constants.TKE_CVM_ZK_LABEL_KEY] = constants.TKE_ZK_NODE_LABEL_VAL

	nodeNum := constants.DefaultControllerNum(c.ctx.ArchGeneration, cg.Type)

	logger.Infof("arch: %d, type: %d, node num: %d", c.ctx.ArchGeneration, cg.Type, nodeNum)

	if nc, err := h.labelNodeCount(c.ctx, label); err != nil {
		return nil, err
	} else if nc >= int(nodeNum) {
		return nil, nil
	} else {
		err = tryTransformPoolWorker(c.ctx, c.zone, nodeNum-int64(nc), label)
		if err != nil {
			return nil, err
		}
	}
	return nil, nil
}

func tryTransformPoolWorker(ctx *deploy.Context, zone string, count int64, label map[string]string) (err error) {
	freeNodeMap, err := getPoolFreeNode(ctx, zone)
	if err != nil {
		return err
	}
	for _, node := range freeNodeMap {
		if count <= 0 {
			break
		}
		labelMap := node.GetObjectMeta().GetLabels()

		for k, v := range label {
			labelMap[k] = v
		}

		_, err = k8s.GetK8sService().AddNodeLabel(ctx.ClientSet(), node.Name, labelMap)
		if err != nil {
			return
		}
		count -= 1
		ctx.SetReturnParam(constants.FlowParamNeedCvmCount, fmt.Sprintf("%d", count))
	}
	return
}

type createPoolWorkerForWorkerApp struct {
	apps.ApplyApp
	ctx *deploy.Context
}

func (c *createPoolWorkerForWorkerApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	cg := c.ctx.ClusterGroup

	h := &ApplyTKEHandler{}

	if allNodeRunning, err := tryMakeAllPoolNodesRunning(c.ctx, cg.Zone); err != nil {
		return nil, err
	} else if !allNodeRunning {
		return nil, errors.New("wait all pool node running")
	}

	label := GetPrivateWorkerLabel(cg)

	nodeNum, _, _, err := h.ComputeWorkerNodeAndCuNum(cg.CuNum, c.ctx)
	if err != nil {
		return
	}

	if nc, err := h.labelNodeCount(c.ctx, label); err != nil {
		return nil, err
	} else if nc >= int(nodeNum) {
		return nil, nil
	} else {
		if fnm, err := getPoolFreeNode(c.ctx, cg.Zone); err != nil {
			return nil, err
		} else if len(fnm) >= int(nodeNum)-nc {
			return nil, nil
		}

		if err := checkAndAddPoolWorker(c.ctx,
			cg.Zone,
			int64(nodeNum)-int64(nc)); err != nil {
			return nil, err
		}
	}

	if allNodeRunning, err := tryMakeAllPoolNodesRunning(c.ctx, cg.Zone); err != nil {
		return nil, err
	} else if !allNodeRunning {
		return nil, errors.New("wait all node running")
	}
	return nil, nil
}

type createPoolWorkerForControllerApp struct {
	apps.ApplyApp
	ctx  *deploy.Context
	zone string
}

func (c *createPoolWorkerForControllerApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	cg := c.ctx.ClusterGroup

	h := &ApplyTKEHandler{}

	if allNodeRunning, err := tryMakeAllPoolNodesRunning(c.ctx, c.zone); err != nil {
		return nil, err
	} else if !allNodeRunning {
		return nil, errors.New("wait all pool node running")
	}

	label := h.GetControllerLabel(c.ctx)
	label[constants.TKE_CVM_NODE_ZONE] = c.zone
	label[constants.TKE_CVM_ZK_LABEL_KEY] = constants.TKE_ZK_NODE_LABEL_VAL

	nodeNum := constants.DefaultControllerNum(c.ctx.ArchGeneration, cg.Type)

	if nc, err := h.labelNodeCount(c.ctx, label); err != nil {
		return nil, err
	} else if nc >= int(nodeNum) {
		return nil, nil
	} else {
		if fnm, err := getPoolFreeNode(c.ctx, c.zone); err != nil {
			return nil, err
		} else if len(fnm) >= int(nodeNum)-nc {
			return nil, nil
		}

		if err := checkAndAddPoolWorker(c.ctx,
			c.zone,
			nodeNum-int64(nc)); err != nil {
			return nil, err
		}
	}

	if allNodeRunning, err := tryMakeAllPoolNodesRunning(c.ctx, c.zone); err != nil {
		return nil, err
	} else if !allNodeRunning {
		return nil, errors.New("wait all node running")
	}
	return nil, nil
}

func getPoolFreeNode(ctx *deploy.Context, zone string) (freeNode map[string]*v1.Node, err error) {
	freeNode = make(map[string]*v1.Node)

	pool, err := getNodePool(ctx, zone)
	if err != nil {
		return freeNode, err
	} else if pool == nil {
		return freeNode, fmt.Errorf("getPoolFreeNode: no pool %s found", zone)
	}

	label := make(map[string]string)
	for _, l := range pool.Labels {
		label[*l.Name] = *l.Value
	}
	label["node.tke.cloud.tencent.com/machineset"] = *pool.NodePoolId

	labelSelector := labels.FormatLabels(label)

	k8sService := k8s.GetK8sService()
	nodeList, err := k8sService.ListNode(ctx.ClientSet(), metav1.ListOptions{
		LabelSelector: labelSelector,
	})
	if err != nil {
		return
	}

	logger.Infof("Label: %s, count: %d", labelSelector, len(nodeList.Items))

	for _, n := range nodeList.Items {
		node := n
		labelMap := node.GetObjectMeta().GetLabels()
		if _, ok := labelMap[constants.TKE_CVM_LABEL_KEY]; ok {
			continue
		}
		nodeInstanceId, ok := labelMap["cloud.tencent.com/node-instance-id"]
		if !ok {
			msg := fmt.Sprintf("not find instance id for %s, name %s", ctx.ClusterGroup.SerialId, node.Name)
			return freeNode, errors.New(msg)
		}
		freeNode[nodeInstanceId] = &node
	}

	logger.Infof("Free node count: %d", len(freeNode))
	return freeNode, nil
}

func checkAndAddPoolWorker(ctx *deploy.Context, zone string, count int64) (err error) {
	clusterGroup := ctx.ClusterGroup
	k8sInstance := ctx.Tke

	if k8sInstance.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		return nil
	}

	optId := clusterGroup.SerialId
	if clusterGroup.AgentSerialId != "" {
		optId = clusterGroup.AgentSerialId
	}

	// lock for uniform cluster
	locker := dlocker.NewDlocker("checkAndAddPoolWorker", fmt.Sprintf("optId-%s-%s", optId, zone), 60)
	err = locker.Lock()
	if err != nil {
		return err
	}
	defer locker.UnLock()

	return addPoolNode(ctx, zone, count)
}

func tryMakeAllPoolNodesRunning(ctx *deploy.Context, zone string) (bool, error) {
	pool, err := getNodePool(ctx, zone)
	if err != nil {
		return false, err
	} else if pool == nil {
		return false, fmt.Errorf("tryMakeAllPoolNodesRunning: no pool %s found", zone)
	}
	if *pool.Native.Replicas > *pool.Native.ReadyReplicas {
		return false, nil
	}
	return true, nil
}

type createPoolApp struct {
	apps.ApplyApp
	ctx  *deploy.Context
	zone string
}

func (c *createPoolApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	if pool, err := getNodePool(c.ctx, c.zone); err != nil {
		return nil, err
	} else if pool != nil {
		return nil, nil
	}
	err = c.createNodePool()
	return nil, err
}

func (c *createPoolApp) createNodePool() (err error) {
	cg := c.ctx.ClusterGroup
	secretId, secretKey, err := service4.GetSecretIdAndKeyByNetworkEnvType(cg.NetEnvironmentType)
	if err != nil {
		return
	}

	cc, err := c.ctx.FlowCC.TkeCC().ClusterConfig(cg)
	if err != nil {
		return
	}
	prepaidPeriod, err := c.ctx.FlowCC.PooledPrepaidPeriod()
	if err != nil {
		return
	}

	sshKey, err := c.ctx.FlowCC.SshKey()
	if err != nil {
		return
	}

	cluster := c.ctx.Cluster

	zoneSubnets, err := cluster.GetSupportedZoneSubnets()
	if err != nil {
		return
	}
	subnet := zoneSubnets[c.zone]
	if subnet == "" {
		return fmt.Errorf("subnet is empty, zone %s", c.zone)
	}

	tkeService := tke.GetTkeService()

	req := tke.NewCreateNodePoolRequestBuilder().
		WithClusterId(c.ctx.Tke.InstanceId).
		WithName(c.zone).
		WithType("Native").
		WithLabel(constants.TKE_CVM_NODE_ZONE, c.zone).
		WithHealthCheckPolicyName(constants.TKE_HEALTH_CHECK_POLICY_NAME).
		WithSubnetId(subnet).
		WithInstanceTypes([]string{"SA2.2XLARGE32"}).
		WithKeyId(sshKey).
		WithSystemDisk(constants.TKE_CVM_DISK_TYPE, constants.TKE_NODE_SYSTEM_DISK_SIZE).
		WithRuntimeRootDir(constants.TKE_DATA_DISK_MOUNT_TARGET).
		WithDataDisks(constants.TKE_CVM_DISK_TYPE, constants.TKE_WORKER_NODE_DISK_SIZE, constants.TKE_DATA_DISK_MOUNT_TARGET).
		WithSecurityGroup(cc.SecurityGroup).
		WithInstanceCharge(constants.TKE_CVM_CHARGE_TYPE, uint64(prepaidPeriod), constants.TKE_CVM_RENEW_FLAG).
		Build()

	_, err = tkeService.CreateNodePool(secretId, secretKey, "", cg.Region, req)

	return
}

func getNodePool(ctx *deploy.Context, zone string) (pool *tke4.NodePool, err error) {
	cg := ctx.ClusterGroup
	secretId, secretKey, err := service4.GetSecretIdAndKeyByNetworkEnvType(cg.NetEnvironmentType)
	if err != nil {
		return
	}

	tkeService := tke.GetTkeService()

	req := tke.NewDescribeNodePoolsRequestBuilder().
		WithClusterId(ctx.Tke.InstanceId).
		WithFilter("NodePoolsName", []string{zone}).
		Build()

	poolSet, err := tkeService.DescribeNodePools(secretId, secretKey, "", cg.Region, req)
	if err != nil {
		return
	}

	for _, nodePool := range poolSet {
		if *nodePool.Name == zone {
			return nodePool, nil
		}
	}
	return
}

func addPoolNode(ctx *deploy.Context, zone string, count int64) (err error) {
	cg := ctx.ClusterGroup
	secretId, secretKey, err := service4.GetSecretIdAndKeyByNetworkEnvType(cg.NetEnvironmentType)
	if err != nil {
		return
	}

	pool, err := getNodePool(ctx, zone)
	if err != nil {
		return err
	} else if pool == nil {
		return fmt.Errorf("addPoolNode: no pool %s found", zone)
	}

	tkeService := tke.GetTkeService()

	req := tke.NewModifyNodePoolRequestBuilder().
		WithClusterId(ctx.Tke.InstanceId).
		WithNodePoolId(*pool.NodePoolId).
		WithReplicas(*pool.Native.Replicas + count).
		Build()

	err = tkeService.ModifyNodePool(secretId, secretKey, "", cg.Region, req)
	return err
}

type uniformClusterCreateControllerApp struct {
	apps.ApplyApp
	ctx  *deploy.Context
	zone string
}

func (c *uniformClusterCreateControllerApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	h := &ApplyTKEHandler{}

	if allNodeRunning, err := h.TryMakeAllNodesRunning(c.ctx); err != nil {
		return nil, err
	} else if !allNodeRunning {
		return nil, errors.New("wait all node running")
	}

	cg := c.ctx.ClusterGroup

	label := h.GetControllerLabel(c.ctx)
	label[constants.TKE_CVM_NODE_ZONE] = c.zone
	label[constants.TKE_CVM_ZK_LABEL_KEY] = constants.TKE_ZK_NODE_LABEL_VAL

	nodeNum := constants.DefaultControllerNum(c.ctx.ArchGeneration, cg.Type)
	cc, err := c.ctx.FlowCC.TkeCC().ClusterConfig(cg)
	if err != nil {
		return
	}
	cvmConfList, err := service3.GetTableService().ListCvmSaleConfByInstanceType(cc.WorkerSpec, 0, 1)
	if err != nil {
		return
	}
	if len(cvmConfList) <= 0 {
		err = fmt.Errorf("can not find cvm: %s", cc.WorkerSpec)
		return
	}

	if nc, err := h.labelNodeCount(c.ctx, label); err != nil {
		return nil, err
	} else if nc >= int(nodeNum) {
		return nil, nil
	} else {
		zoneId, err := GetZoneShortId(c.zone)
		if err != nil {
			return nil, err
		}
		name := fmt.Sprintf("%s-%d-", constants.TKE_CONTROL_NODE_LABEL_VAL, zoneId)
		if buyWorkerCount, err := h.CheckAndAddWorker(c.ctx,
			c.zone,
			nodeNum-int64(nc),
			cvmConfList[0],
			false,
			label,
			constants.TKE_CONTROL_NODE_DISK_SIZE_UNIFORM,
			constants.TKE_CVM_DISK_TYPE,
			name, nil, "", constants.TKE_NODE_SYSTEM_DISK_SIZE); err != nil {
			return nil, err
		} else if buyWorkerCount > 0 {
			return nil, errors.New("wait worker running")
		}
	}

	if allNodeRunning, err := h.TryMakeAllNodesRunning(c.ctx); err != nil {
		return nil, err
	} else if !allNodeRunning {
		return nil, errors.New("wait all node running")
	}

	if nc, err := h.labelNodeCount(c.ctx, label); err != nil {
		return nil, err
	} else if nc <= int(nodeNum) {
		return nil, errors.New("node count is not enough")
	}
	return nil, nil
}

type privateClusterCreateWorkerApp struct {
	ctx *deploy.Context
	apps.ApplyApp
}

func (c *privateClusterCreateWorkerApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	if c.ctx.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		return nil, nil
	}

	h := &ApplyTKEHandler{}

	if allNodeRunning, err := h.TryMakeAllNodesRunning(c.ctx); err != nil {
		return nil, err
	} else if !allNodeRunning {
		return nil, errors.New("wait all node running")
	}

	if tn, cn, wn, cvmConf, err := h.CalcClusterNodeCount(c.ctx); err != nil {
		return nil, err
	} else if tn >= cn+wn {
		return nil, nil
	} else {
		unallocatedWorkerNode := int64(cn + wn - tn)
		label := h.GetBaseWorkerLabel(c.ctx.Region)
		diskType, diskSize, err := c.ctx.ClusterGroup.GetDiskInfo()
		if err != nil {
			return nil, err
		}

		changeConf, force, err := ChangeWorkSpec(c.ctx.ClusterGroup, cvmConf)
		if err != nil {
			return nil, err
		}

		if buyWorkerCount, err := h.CheckAndAddWorker(c.ctx,
			c.ctx.ClusterGroup.Zone,
			unallocatedWorkerNode,
			changeConf,
			force,
			label,
			diskSize,
			diskType,
			constants.TKE_WORKER_NODE_LABEL_VAL, nil, "", constants.TKE_NODE_SYSTEM_DISK_SIZE); err != nil {
			return nil, err
		} else if buyWorkerCount > 0 {
			return nil, errors.New("wait worker running")
		}
	}

	if allNodeRunning, err := h.TryMakeAllNodesRunning(c.ctx); err != nil {
		return nil, err
	} else if !allNodeRunning {
		return nil, errors.New("wait all node running")
	}

	if tn, cn, wn, _, err := h.CalcClusterNodeCount(c.ctx); err != nil {
		return nil, err
	} else if tn < cn+wn {
		return nil, errors.New("node count is not enough")
	}

	return nil, nil
}

func ChangeWorkSpec(cg *table.ClusterGroup, workerConf *table3.CvmSaleConfig) (conf *table3.CvmSaleConfig, force bool, err error) {
	conf = workerConf
	spec, err := cg.GetWorkSpec()
	if err != nil {
		return
	}
	if spec == "" {
		return
	}
	cvmConfList, err := service3.GetTableService().ListCvmSaleConfByInstanceType(spec, 0, 1)
	if err != nil {
		return
	}
	if len(cvmConfList) == 0 {
		err = fmt.Errorf("cvm spec %s not found in table CvmSaleConf", spec)
		return
	}
	specConf := cvmConfList[0]
	if specConf.Cpu == workerConf.Cpu && specConf.Memory == workerConf.Memory {
		conf = specConf
		force = true
	}
	return conf, force, nil
}

type addNodeLabelApp struct {
	ctx *deploy.Context
	apps.ApplyApp
}

func (c *addNodeLabelApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	if c.ctx.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		return nil, nil
	}

	k8sInstance := c.ctx.Tke

	cc, err := c.ctx.CC().FlowCC().TkeCC().ClusterConfig(c.ctx.ClusterGroup)
	if err != nil {
		return nil, err
	}

	h := &ApplyTKEHandler{}

	targetWorkerCount := 2
	if k8sInstance.ArchGeneration >= constants.TKE_ARCH_GENERATION_V5 {
		targetWorkerCount = 3
	}

	err = h.AddNodeLabel(c.ctx.ClusterGroup, c.ctx.Cluster, k8sInstance, targetWorkerCount, cc, true)
	if err != nil {
		return nil, err
	}

	return nil, nil
}

type updateTkeStatusApp struct {
	apps.ApplyApp
	ctx *deploy.Context
}

func (c *updateTkeStatusApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.ExecuteSqlWithArgs("update Tke set status=?,WorkerStatus=? where ClusterId=? and InstanceId=?",
			constants.TKE_STATUS_RUNNING, constants.K8S_CLUSTER_WITH_WORKER, c.ctx.Cluster.Id, c.ctx.Tke.InstanceId)
		return nil
	}).Close()
	return nil, err
}

type updateClusterCuApp struct {
	apps.ApplyApp
	ctx *deploy.Context
}

func (c *updateClusterCuApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	updateClusterInfo(c.ctx.ClusterGroup, c.ctx.Cluster)
	return nil, err
}
