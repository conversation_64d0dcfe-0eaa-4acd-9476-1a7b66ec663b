package taskHandler

import (
	"encoding/json"
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config"
	"testing"
)

func TestStartSetatsWarehouse(t *testing.T) {
	config.InitRainbowService(nil, nil, nil)

	var cpu float64 = 8.0000
	fmt.Println(int64(cpu))
	fmt.Println(int64(cpu * constants.CVM_DEFAULT_MEMRATIO))

	handler := NewDeployHandler(&StartSetatsWarehouseBase{})
	request := &flow.TaskExecRequest{
		Retrycount: 0,
		Processkey: "test",
		Taskcode:   "test",
		DocId:      "1",
		FlowId:     "1",
		TaskId:     "1",
		Params: map[string]string{
			constants.FLOW_PARAM_CLUSTER_GROUP_ID: fmt.Sprintf("%d", 1),
			constants.FLOW_PARAM_CLUSTER_ID:       fmt.Sprintf("%d", 1),
			constants.FLOW_PARAM_REQUEST_ID:       "test_hahaha",
		},
	}

	rsp := handler.CompleteTask(request)

	b, _ := json.MarshalIndent(rsp, "", "")

	if rsp.RetCode != flow.TASK_IN_PROCESS {
		t.Fatal(string(b))
	}

	t.Log(string(b))
}
