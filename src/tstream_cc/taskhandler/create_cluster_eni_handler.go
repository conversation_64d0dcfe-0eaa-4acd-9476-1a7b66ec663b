package taskHandler

import (
	"fmt"

	"tencentcloud.com/tstream_galileo/src/common/flow"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	constants2 "tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	flow2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/flow"
)

type EnableEniIpReserveHandler struct {
}

func (c *EnableEniIpReserveHandler) CompleteTask(request *flow.TaskExecRequest) (resp *flow.TaskExecResponse) {
	logger.Infof("request: %+v", request)

	// 0. 公共基本逻辑
	flowService := flow2.GetFlowService()
	params := request.Params
	requestId, _, _ := flowService.GetFlowParamString(params, constants2.FLOW_PARAM_REQUEST_ID, "")

	defer func() {
		if err := recover(); err != nil {
			logger.Errorf("[%s] EnableEniIpReserve task failed because: %v", requestId, err)
			resp = flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1, fmt.Sprintf("%v", err), params)
		}
	}()

	handler := &EnableEniIpReserve{}
	resp = handler.enableEniIpReserve(requestId, request)
	return resp
}
