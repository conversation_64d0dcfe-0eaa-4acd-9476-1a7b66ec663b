package taskHandler

import (
	"fmt"
	commandService "tencentcloud.com/tstream_galileo/src/common/command"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type DeployDiagnosisDataUploaderService struct {
}

func (s *DeployDiagnosisDataUploaderService) Apps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	cg := ctx.ClusterGroup

	// 共享集群 包年包月下的eks集群，不做任何处理
	if cg.Type == constants.CLUSTER_GROUP_TYPE_SUB_EKS {
		return result, nil
	}

	// eks不部署
	if ctx.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		return result, nil
	}

	// 共享集群母集群，开新区
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone != "" {
		return result, nil
	}

	// 共享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId != "" {
		return result, nil
	}

	// 创建共享集群母集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone == "" {
		return result, nil
	}

	// 独享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId == "" {
		supportFeature, err := ctx.Cluster.GetSupportedFeatures()
		if err != nil {
			return result, err
		}
		// 如果独享集群支持 Log2cosByCli feature, 则无需部署 DiagnosisDataUploader
		for _, feature := range supportFeature {
			if feature == constants.Log2cosByCli {
				return result, nil
			}
		}
	}

	secret, err := ctx.GetOceanusIPSecret()
	if err != nil {
		return result, err
	}

	result = append(result, ctx.Service(constants.ComponentDiagnosisDataUploader, constants.ComponentDiagnosisDataUploaderPort, secret))
	result = append(result, &diagnosisDataUploader{ctx: ctx})

	return result, nil
}

type diagnosisDataUploader struct {
	ctx *deploy.Context
	apps.DaemonSet
}

func (d *diagnosisDataUploader) Params() (interface{}, error) {
	tkeClusterList, err := service2.GetTableService().ListTkeByClusterId(d.ctx.Cluster.Id)
	if err != nil {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	if len(tkeClusterList) == 0 {
		msg := fmt.Sprintf("Cluster %s tke not found", d.ctx.ClusterGroup.SerialId)
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, msg, nil)
	}

	image, err := d.ctx.CC().ImageRegistry().DiagnosisDataUploader()
	if err != nil {
		return nil, err
	}

	username, passwd, url, err := d.ctx.FlowCC.NginxCC().Info()
	if err != nil {
		return nil, err
	}

	base, err := d.ctx.ParamBase(constants.K8S_KIND_DAEMONSET,
		constants.OCEANUS_NAMESPACE,
		constants.ComponentDiagnosisDataUploader, false)
	if err != nil {
		return nil, err
	}
	base.NodeSelector[constants.TKE_CVM_LABEL_KEY] = constants.TKE_WORKER_NODE_LABEL_VAL

	// 渲染 ClusterAdminServiceUrl， 默认使用 CS的(创建集群)，如果集群有CA（更新集群），就使用CA的
	serviceUrl, err := commandService.GetCAOrCSUrl(d.ctx.Cluster)
	if err != nil {
		logger.Errorf("GetCAOrCSUrl return error %v", err)
		return nil, err
	}
	logger.Infof("### cluster id %d diagnosisDataUploader ClusterAdminServiceUrl is %s", d.ctx.Cluster.Id, serviceUrl)
	base.AppImage = image

	base.Env["PATH"] = "/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"
	base.Env["OCEANUS_CLUSTER_ID"] = d.ctx.ClusterGroup.SerialId
	base.Env["GALILEO_URL"] = fmt.Sprintf("%s/shark", url)
	base.Env["ClusterAdminURL"] = serviceUrl
	base.Env["NGINX_USERNAME"] = username
	base.Env["NGINX_PASSWORD"] = passwd
	bucket := d.ctx.Cluster.DefaultCOSBucket
	if len(d.ctx.Cluster.LogCOSBucket) > 0 {
		bucket = d.ctx.Cluster.LogCOSBucket
	}
	base.Env["COS_BUCKET"] = bucket

	return base, nil
}

func (d *diagnosisDataUploader) Decode(params, into interface{}) (interface{}, error) {
	return d.ctx.FlowCC.AppsCC().DiagnosisDataUploader(params, into)
}
