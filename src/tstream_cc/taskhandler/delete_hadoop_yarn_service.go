package taskHandler

import (
	"fmt"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/helm"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/tke"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type deleteHadoopYarnService struct {
}

func (s *deleteHadoopYarnService) Apps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	cg := ctx.ClusterGroup

	// 独享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId == "" && ctx.Cluster.CrossTenantEniMode == constants.EnableStaticMode {
		result = append(result, &DeleteHadoopYarnApp{ctx: ctx})
	}
	return result, nil
}

type DeleteHadoopYarnApp struct {
	apps.ApplyApp
	ctx *deploy.Context
}

func (i *DeleteHadoopYarnApp) Apply(s apps.Client, v interface{}) (interface{}, error) {
	helService := service2.GetHelmService()
	namespace := helService.HadoopNamespace(i.ctx.ClusterGroup)
	client, err := helService.NewClient(s.KubeConfig(), namespace)
	if err != nil {
		return nil, err
	}

	name := helService.HadoopYarnName(i.ctx.ClusterGroup)

	res, err := helService.ListByName(client, namespace, name)
	if err != nil {
		return nil, err
	}
	if len(res.Releases) == 0 {
		return nil, nil
	}

	err = helService.DeleteByName(client, name)
	if err != nil {
		return nil, err
	}

	// delete nodemanager pvc
	clientSet, err := tke.GetTkeService().KubernetesClientsetFromCluster(i.ctx.RequestId, i.ctx.Cluster)
	if err != nil {
		return nil, err
	}

	opts := metav1.ListOptions{
		LabelSelector: fmt.Sprintf("app=%s,component=%s", constants.HADOOP_APP_NAME, constants.NODE_MANAGER_COMPONENT_NAME),
	}
	k8sService := k8s.GetK8sService()

	pvcList, err := k8sService.ListPVC(clientSet, namespace, opts)

	if err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}
	for _, pvc := range pvcList.Items {
		_, err = k8sService.DeletePVC(clientSet, namespace, pvc.Name)
	}
	return nil, nil
}
