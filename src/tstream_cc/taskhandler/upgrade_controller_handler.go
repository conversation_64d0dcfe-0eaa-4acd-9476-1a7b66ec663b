package taskHandler

import (
	"encoding/json"
	"fmt"
	"sort"
	"strings"

	cvm2 "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/cvm/v20170312"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/fields"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	table3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/Cvm"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	service4 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cbs"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cvm"
	flow2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/flow"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/tke"
)

type upgradeControllerHandler struct {
}

func (h *upgradeControllerHandler) HandleTaskStatusInit(requestId string, request *flow.TaskExecRequest) (resp *flow.TaskExecResponse) {
	flowService := flow2.GetFlowService()
	s, err := NewUpgradeControllerService(requestId, request)
	if err != nil {
		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_RUNNING, 0.1,
			fmt.Sprintf("%v", err), request.Params)
	}

	if err = s.Init(); err != nil {
		return s.RetryRsp(fmt.Sprintf("%v", err))
	}

	if s.Tke.ArchGeneration == constants.TKE_ARCH_GENERATION_V2 {
		return s.upgradeV3()
	} else {
		return s.upgrade()
	}
}

func (h *upgradeControllerHandler) HandleTaskStatusRunning(requestId string, request *flow.TaskExecRequest) (resp *flow.TaskExecResponse) {
	flowService := flow2.GetFlowService()
	s, err := NewUpgradeControllerService(requestId, request)
	if err != nil {
		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_RUNNING, 0.1,
			fmt.Sprintf("%v", err), request.Params)
	}

	if err = s.Init(); err != nil {
		return s.RetryRsp(fmt.Sprintf("%v", err))
	}
	if s.Tke.ArchGeneration == constants.TKE_ARCH_GENERATION_V2 {
		return s.checkUpgradedV3()
	} else {
		return s.checkUpgraded()
	}
}

type upgradeControllerService struct {
	requestId string
	request   *flow.TaskExecRequest

	*flow2.ClusterFlowService

	*ApplyTKEHandler
}

func NewUpgradeControllerService(requestId string, request *flow.TaskExecRequest) (s *upgradeControllerService, err error) {
	flowService, err := flow2.NewClusterFlowService(request)
	if err != nil {
		return nil, err
	}

	s = &upgradeControllerService{requestId: requestId, request: request, ClusterFlowService: flowService,
		ApplyTKEHandler: &ApplyTKEHandler{}}

	if err != nil {
		return nil, err
	}
	if flowService.Tke == nil {
		return nil, errorcode.InternalErrorCode.NewWithMsg("No tke instances")
	}
	return
}

func (s *upgradeControllerService) Init() (err error) {
	if err = s.getFromParam(); err != nil {
		return
	}

	running, err := IsClusterRunning(s.ClusterGroup, s.Cluster, s.Tke)
	if err != nil {
		return err
	}
	if !running {
		return errorcode.InternalErrorCode.NewWithInfo(fmt.Sprintf("tke not running"), nil)
	}

	return nil
}

func (s *upgradeControllerService) getFromParam() (err error) {

	return
}
func (s *upgradeControllerService) upgradeV3() (rsp *flow.TaskExecResponse) {
	logger.Infof("%v", s.request)
	upgradeInstance, err := s.getControllerToUpgrade()
	if err != nil {
		return s.RetryRspWithErr(err)
	}
	if upgradeInstance == nil {
		s.SetReturnParam(constants.FLOW_PARAM_UPGRADE_CONTROLLER_FINISHED, "1")
		return s.DoneRsp("All controller is upgraded")
	}
	s.SetReturnParam(constants.FLOW_PARAM_UPGRADE_CONTROLLER_INSTANCE, *upgradeInstance.InstanceId)
	logger.Infof("upgrade instance %s", upgradeInstance.InstanceId)

	workNode, err := s.getUpgradeWorkNode()
	if err != nil {
		return s.RetryRspWithErr(err)
	}
	if workNode == "" {
		return s.RetryRsp("can not find work node for zookeeper")
	}
	s.SetReturnParam(constants.FLOW_PARAM_UPGRADE_CONTROLLER_WORK_NODE, workNode)
	logger.Infof("upgrade work node %s", workNode)

	return s.DoneRsp("upgrade controller, controller found")
}

func (s *upgradeControllerService) getUpgradeWorkNode() (instanceId string, err error) {
	instanceId, exist := s.GetParam(constants.FLOW_PARAM_UPGRADE_CONTROLLER_WORK_NODE, "")
	if exist {
		return instanceId, nil
	}
	k8sService := k8s.GetK8sService()
	kubConfig := []byte(s.Cluster.KubeConfig)
	client, err := k8sService.NewClient(kubConfig)
	if err != nil {
		return "", errorcode.InternalErrorCode.NewWithErr(err)
	}

	workerList, err := k8sService.ListNode(client, metav1.ListOptions{
		LabelSelector: fmt.Sprintf("%s==%s", constants.TKE_CVM_LABEL_KEY, constants.TKE_WORKER_NODE_LABEL_VAL),
	})
	if err != nil {
		return
	}
	zkWorkIdSet := make([]string, 0)
	for _, node := range workerList.Items {
		labelMap := node.GetObjectMeta().GetLabels()
		if _, ok := labelMap[constants.TKE_CVM_ZK_LABEL_KEY]; ok {
			continue
		}
		if _, ok := labelMap[constants.TKE_CVM_WORKER_LABEL_KEY]; ok {
			if instanceId, ok := labelMap["cloud.tencent.com/node-instance-id"]; ok {
				zkWorkIdSet = append(zkWorkIdSet, instanceId)
			} else {
				msg := fmt.Sprintf("not find instance id for %s, name %s", s.ClusterGroup.SerialId, node.Name)
				return "", errorcode.InternalErrorCode.NewWithMsg(msg)
			}
		}
	}
	if len(zkWorkIdSet) == 0 {
		return "", nil
	}
	instanceId = zkWorkIdSet[0]
	region := s.ClusterGroup.Region
	tkeService := tke.GetTkeService()

	instanceIds := make([]*string, 0)
	instanceIds = append(instanceIds, &instanceId)
	secretId, secretKey, err := service4.GetSecretIdAndKeyByNetworkEnvType(s.ClusterGroup.NetEnvironmentType)
	if err != nil {
		return
	}
	totalCount, instanceSet, err := tkeService.DescribeClusterInstances(secretId, secretKey, "", region,
		tkeService.NewDefaultDescribeClusterInstancesRequestBuilder().
			WithClusterId(s.Tke.InstanceId).
			WithInstanceIds(instanceIds).
			Build())

	if totalCount != 1 {
		msg := fmt.Sprintf("not find instance id for %s, instance %s", s.ClusterGroup.SerialId, instanceId)
		return "", errorcode.InternalErrorCode.NewWithMsg(msg)
	}

	workInstance := instanceSet[0]

	labelMap := make(map[string]string, 0)
	for _, label := range workInstance.InstanceAdvancedSettings.Labels {
		labelMap[*label.Name] = *label.Value
	}
	labelMap[constants.TKE_CVM_ZK_LABEL_KEY] = constants.TKE_ZK_NODE_LABEL_VAL

	nodeList, err := k8sService.ListNode(client, metav1.ListOptions{
		LabelSelector: fmt.Sprintf("%s==%s", "cloud.tencent.com/node-instance-id", *workInstance.InstanceId),
	})
	if err != nil {
		return "", errorcode.InternalErrorCode.NewWithErr(err)
	}
	if len(nodeList.Items) != 1 {
		msg := fmt.Sprintf("%s number is not one, but %d", *workInstance.InstanceId, len(nodeList.Items))
		return "", errorcode.InternalErrorCode.NewWithMsg(msg)
	}
	_, err = k8s.GetK8sService().AddNodeLabel(client, nodeList.Items[0].Name, labelMap)
	if err != nil {
		return "", errorcode.InternalErrorCode.NewWithErr(err)
	}
	return
}

func (s *upgradeControllerService) upgrade() (rsp *flow.TaskExecResponse) {
	logger.Infof("%v", s.request)
	_, exist := s.GetParam(constants.FLOW_PARAM_UPGRADE_CONTROLLER_ADDED, "")
	if exist {
		return s.DoneRsp("Node added, logic Error")
	}
	upgradeInstance, err := s.getControllerToUpgrade()
	if err != nil {
		return s.RetryRspWithErr(err)
	}
	if upgradeInstance == nil {
		service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
			tx.ExecuteSqlWithArgs("update Tke set ArchGeneration=? where ClusterId=? and InstanceId=?",
				1, s.Cluster.Id, s.Tke.InstanceId)
			return nil
		}).Close()
		s.SetReturnParam(constants.FLOW_PARAM_UPGRADE_CONTROLLER_FINISHED, "1")
		return s.DoneRsp("All controller is upgraded")
	}
	s.SetReturnParam(constants.FLOW_PARAM_UPGRADE_CONTROLLER_INSTANCE, *upgradeInstance.InstanceId)
	logger.Infof("upgrade instance %s", upgradeInstance.InstanceId)
	controllerSpec, err := s.GetNewControllerSpec()
	if err != nil {
		return s.RetryRspWithErr(err)
	}
	logger.Infof("upgrade controller spec: %s", controllerSpec)
	if controllerSpec != constants.TKE_CONTROL_NODE_INSTANCE_TYPE {
		msg := fmt.Sprintf("No [%s] controller spec, Stop upgrade", constants.TKE_CONTROL_NODE_INSTANCE_TYPE)
		s.SetReturnParam(constants.FLOW_PARAM_UPGRADE_CONTROLLER_FINISHED, "0")
		return s.DoneRsp(msg)
	}
	err = s.AddNewController(controllerSpec, 1)
	if err != nil {
		return s.RetryRspWithErr(err)
	}
	s.SetReturnParam(constants.FLOW_PARAM_UPGRADE_CONTROLLER_ADDED, "1")
	return s.DoneRsp("upgrade controller, node added")
}

func (s *upgradeControllerService) AddNewController(controllerSpec string, controllerNum int64) (err error) {
	tkeService := tke.GetTkeService()
	period, err := s.CC().FlowCC().PrepaidPeriod()
	if err != nil {
		return errorcode.InternalErrorCode.NewWithErr(err)
	}
	cc, err := s.CC().FlowCC().TkeCC().ClusterConfig(s.ClusterGroup)
	if err != nil {
		return errorcode.InternalErrorCode.NewWithErr(err)
	}

	logger.Info("tke cc %v", cc)
	controllerRequest := tkeService.NewDefaultControlNodeRunInstancesRequestBuilder().
		WithInstanceType(controllerSpec).
		WithInstanceChargePrepaid(period, constants.TKE_CVM_RENEW_FLAG).
		WithPlacement(s.ClusterGroup.Zone, constants.TKE_PROJECT_ID).
		WithVirtualPrivateCloud(s.Cluster.VpcId, s.Cluster.SubnetId).
		WithSecurityGroup(cc.SecurityGroup).
		WithInstanceCount(controllerNum).
		WithSystemDisk(constants.TKE_CVM_DISK_TYPE, constants.TKE_NODE_SYSTEM_DISK_SIZE).
		WithDataDisks(constants.TKE_CVM_DISK_TYPE, constants.TKE_CONTROL_NODE_DISK_SIZE).
		Build()

	isV1 := s.Tke.ArchGeneration >= constants.TKE_ARCH_GENERATION_V6
	mountTarget := constants.TKE_DATA_DISK_MOUNT_TARGET_FOR_CONTAINERD
	if !isV1 {
		mountTarget = constants.TKE_DATA_DISK_MOUNT_TARGET
	}

	request := tkeService.NewDefaultCreateClusterInstancesRequestBuilder().
		WithClusterId(s.Tke.InstanceId).
		WithRunInstancePara(controllerRequest).
		WithInstanceAdvancedSettings(constants.TKE_DATA_DISK_AUTO_FORMAT_AND_MOUNT,
			constants.TKE_DATA_DISK_FILE_SYSTEM, mountTarget, constants.TKE_CVM_DISK_TYPE,
			constants.TKE_CONTROL_NODE_DISK_SIZE, map[string]string{constants.TKE_CVM_LABEL_KEY: constants.TKE_CONTROL_NODE_LABEL_VAL}, cc.UserScript).
		Build()

	err = tkeService.CreateClusterInstancesWithScsAccount(s.ClusterGroup.Region, request)
	if err != nil {
		return errorcode.InternalErrorCode.NewWithErr(err)
	}
	return nil
}

func (s *upgradeControllerService) GetNewControllerSpec() (controllerSpec string, err error) {
	cc, err := s.CC().FlowCC().TkeCC().ClusterConfig(s.ClusterGroup)
	if err != nil {
		return "", errorcode.InternalErrorCode.NewWithErr(err)
	}
	cvmConfList, err := service3.GetTableService().ListCvmSaleConfByInstanceType(cc.ControllerSpec, 0, 1)
	if err != nil {
		return "", errorcode.InternalErrorCode.NewWithErr(err)
	}
	if len(cvmConfList) == 0 {
		msg := fmt.Sprintf("cvm spec %s not found in table CvmSaleConf", cc.ControllerSpec)
		return "", errorcode.InternalErrorCode.NewWithInfo(msg, nil)
	}
	instanceTypes := make(map[string]struct{}, 0)
	controllerSpec, _, _, err = CheckCvmQuotaMaySwitchType(s.ClusterGroup.Zone, s.ClusterGroup, s.Cluster, cvmConfList[0], false, 1, false, instanceTypes, 0)
	if err != nil {
		return "", errorcode.InternalErrorCode.NewWithErr(err)
	}
	return controllerSpec, nil
}

func (s *upgradeControllerService) getControllerToUpgrade() (instance *cvm2.Instance, err error) {
	instanceId, exist := s.GetParam(constants.FLOW_PARAM_UPGRADE_CONTROLLER_INSTANCE, "")
	secretId, secretKey, err := service4.GetSecretIdAndKeyByNetworkEnvType(s.ClusterGroup.NetEnvironmentType)
	if err != nil {
		return
	}
	if exist {
		cvmService := cvm.GetCvmService()
		instanceIdSet := make([]*string, 0)
		instanceIdSet = append(instanceIdSet, &instanceId)
		cvmInstanceSet, err := cvmService.DescribeInstancesForIds(secretId, secretKey, s.ClusterGroup.Region, instanceIdSet)
		if err != nil || len(cvmInstanceSet) != 1 {
			msg := fmt.Sprintf("cvm [%s] can not found, or err occur", instanceId)
			return nil, errorcode.InternalErrorCode.NewWithInfo(msg, err)
		}
		return cvmInstanceSet[0], nil
	}

	region := s.ClusterGroupService.GetClusterGroup().Region
	tkeService := tke.GetTkeService()

	_, allInstanceSet, err := tkeService.DescribeClusterAllInstances(secretId, secretKey, "", region, s.Tke.InstanceId)
	if err != nil {
		return
	}

	instanceIdSet := make([]*string, 0)

	for _, instance := range allInstanceSet {
		for _, label := range instance.InstanceAdvancedSettings.Labels {
			if *label.Name == constants.TKE_CVM_LABEL_KEY && *label.Value == constants.TKE_CONTROL_NODE_LABEL_VAL {
				instanceIdSet = append(instanceIdSet, instance.InstanceId)
			}
		}
	}

	logger.Infof("controllerInstanceSet %v", instanceIdSet)

	if s.Tke.ArchGeneration == constants.TKE_ARCH_GENERATION_V2 {
		if len(instanceIdSet) == 1 {
			return nil, nil
		}
	} else {
		// controller 节点不等于3，手动介入
		if constants.TKE_CONTROL_NODE_NUM != len(instanceIdSet) {
			msg := fmt.Sprintf("[%s] [%s] upgrade controller, "+
				"tke [%s] controller node expect %d, but has %d", s.requestId,
				s.ClusterGroup.SerialId, s.Tke.InstanceId, constants.TKE_CONTROL_NODE_NUM, len(instanceIdSet))
			return nil, errorcode.InternalErrorCode.NewWithInfo(msg, err)
		}
	}
	if s.ClusterGroup.NetEnvironmentType == constants.NETWORK_ENV_INNER_VPC {
		return s.orderControllerToUpgradeForInner(region, instanceIdSet, false)
	}

	return s.orderControllerToUpgrade(s.ClusterGroup.NetEnvironmentType, s.ClusterGroupService.GetClusterGroup(), instanceIdSet, false)
}

func (s *upgradeControllerService) orderControllerToUpgradeForInner(region string, instanceIdSet []*string, isWorker bool) (instance *cvm2.Instance, err error) {
	unschedulerNodeMap, err := s.listUnschedulerNode()
	if err != nil {
		return
	}
	upgradeIdSet := make([]*string, 0)
	for _, instanceId := range instanceIdSet {
		if _, ok := unschedulerNodeMap[*instanceId]; !ok {
			upgradeIdSet = append(upgradeIdSet, instanceId)
		}
	}
	b, _ := json.Marshal(upgradeIdSet)
	logger.Infof("upgradeIdSet: %s", string(b))
	if len(upgradeIdSet) <= 0 {
		msg := fmt.Sprintf("orderControllerToUpgradeForInner upgradeIdSet length less than 0")
		return nil, errorcode.InternalErrorCode.NewWithInfo(msg, err)
	}

	secretId, secretKey, err := service4.GetSecretIdAndKeyByNetworkEnvType(s.ClusterGroup.NetEnvironmentType)
	if err != nil {
		return
	}
	cvmService := cvm.GetCvmService()
	cvmInstanceSet, err := cvmService.DescribeInstancesForIds(secretId, secretKey, region, upgradeIdSet)
	if err != nil || len(cvmInstanceSet) != len(upgradeIdSet) {
		msg := fmt.Sprintf("%s upgrade Controller DescribeInstancesWithScsAccount Error, expect %d, but %d",
			s.requestId, len(upgradeIdSet), len(cvmInstanceSet))
		return nil, errorcode.InternalErrorCode.NewWithInfo(msg, err)
	}
	return cvmInstanceSet[0], nil
}

func (s *upgradeControllerService) listUnschedulerNode() (unschedulerNodeMap map[string]struct{}, err error) {
	k8sService := k8s.GetK8sService()
	kubConfig := []byte(s.Cluster.KubeConfig)
	client, err := k8sService.NewClient(kubConfig)
	if err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}
	fSet := fields.Set{"spec.unschedulable": "true"}
	nodeList, err := k8sService.ListNode(client,
		metav1.ListOptions{FieldSelector: fields.SelectorFromSet(fSet).String()})
	if err != nil {
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}
	unschedulerNodeMap = make(map[string]struct{}, 0)
	for _, node := range nodeList.Items {
		labelMap := node.GetObjectMeta().GetLabels()
		if instanceId, ok := labelMap["cloud.tencent.com/node-instance-id"]; ok {
			unschedulerNodeMap[instanceId] = struct{}{}
		} else {
			msg := fmt.Sprintf("not find instance id for %s, name %s", s.ClusterGroup.SerialId, node.Name)
			return nil, errorcode.InternalErrorCode.NewWithMsg(msg)
		}
	}
	return unschedulerNodeMap, nil
}

func (s *upgradeControllerService) orderControllerToUpgrade(netEnvironmentType int8, cg *table.ClusterGroup, instanceIdSet []*string, isWorker bool) (instance *cvm2.Instance, err error) {
	cvmService := cvm.GetCvmService()
	cvmInstanceSet, err := cvmService.DescribeInstancesWithScsAccount(netEnvironmentType, cg.Region, instanceIdSet)
	if err != nil || len(cvmInstanceSet) != len(instanceIdSet) {
		msg := fmt.Sprintf("%s upgrade Controller DescribeInstancesWithScsAccount Error, expect %d, but %d",
			s.requestId, len(instanceIdSet), len(cvmInstanceSet))
		return nil, errorcode.InternalErrorCode.NewWithInfo(msg, err)
	}
	cvmInstanceMap := make(map[string]*cvm2.Instance, len(instanceIdSet))
	cvmInstanceDiskMap := make(map[string]int64, len(instanceIdSet))
	for _, cvmInstance := range cvmInstanceSet {
		cvmInstanceMap[*cvmInstance.InstanceId] = cvmInstance
		for _, disk := range cvmInstance.DataDisks {
			diskSize := cvmInstanceDiskMap[*cvmInstance.InstanceId]
			if *disk.DiskSize > diskSize {
				cvmInstanceDiskMap[*cvmInstance.InstanceId] = *disk.DiskSize
			}
		}
	}

	cvmSaleConfList, err := service3.GetTableService().ListActiveCvmSaleConf()
	cvmConfMap := make(map[string]*table3.CvmSaleConfig, len(cvmSaleConfList))
	for _, config := range cvmSaleConfList {
		cvmConfMap[config.InstanceType] = config
	}

	tkeCC, err := s.CC().FlowCC().TkeCC().ClusterConfig(cg)
	if err != nil {
		return nil, errorcode.InternalErrorCode.NewWithInfo("scale own Get Cluster Config Error", err)
	}
	cvmConfList := make([]*table3.CvmSaleConfig, 0)
	newDiskSize := int64(constants.TKE_CONTROL_NODE_DISK_SIZE)
	newInstanceType := constants.TKE_CONTROL_NODE_INSTANCE_TYPE
	if isWorker {

		workerSpec := cvm.GetCvmService().GetWorkerSpec(s.Cluster, tkeCC)

		cvmConfList, err = service3.GetTableService().ListCvmSaleConfByInstanceType(workerSpec, 0, 1)
		newDiskSize = int64(constants.TKE_WORKER_NODE_DISK_SIZE)
		newInstanceType = constants.TKE_WORKER_NODE_INSTANCE_TYPE
	} else {
		cvmConfList, err = service3.GetTableService().ListCvmSaleConfByInstanceType(tkeCC.ControllerSpec, 0, 1)
	}
	if err != nil || len(cvmConfList) == 0 {
		return nil, errorcode.InternalErrorCode.NewWithInfo("scale down ListCvmSaleConfByInstanceType Error, length = 0", err)
	}
	newCvmConf := cvmConfList[0]

	sort.Slice(instanceIdSet, func(i, j int) bool {
		iInstanceId := instanceIdSet[i]
		jInstanceId := instanceIdSet[j]
		iInstance, ok := cvmInstanceMap[*iInstanceId]
		if !ok {
			return true
		}
		jInstance, ok := cvmInstanceMap[*jInstanceId]
		if !ok {
			return false
		}
		iDiskSize, ok := cvmInstanceDiskMap[*iInstanceId]
		if !ok {
			return true
		}
		jDiskSize, ok := cvmInstanceDiskMap[*jInstanceId]
		if !ok {
			return false
		}
		iConfig, ok := cvmConfMap[*iInstance.InstanceType]
		if !ok {
			return true
		}
		jConfig, ok := cvmConfMap[*jInstance.InstanceType]
		if !ok {
			return false
		}
		if iConfig.Cpu > newCvmConf.Cpu {
			return true
		} else if iConfig.Cpu < newCvmConf.Cpu {
			return false
		}
		if jConfig.Cpu > newCvmConf.Cpu {
			return false
		} else if jConfig.Cpu < newCvmConf.Cpu {
			return true
		}
		if iConfig.Memory > newCvmConf.Memory {
			return true
		} else if iConfig.Memory < newCvmConf.Memory {
			return false
		}
		if jConfig.Memory > newCvmConf.Memory {
			return false
		} else if jConfig.Memory < newCvmConf.Memory {
			return true
		}
		if iDiskSize > newDiskSize {
			return true
		} else if iDiskSize < newDiskSize {
			return false
		}
		if jDiskSize > newDiskSize {
			return false
		} else if jDiskSize < newDiskSize {
			return true
		}
		if iConfig.Price > jConfig.Price {
			return true
		} else if iConfig.Price < jConfig.Price {
			return false
		}
		if iConfig.Generation < jConfig.Generation {
			return true
		} else if iConfig.Generation > jConfig.Generation {
			return false
		}
		return true
	})

	b, _ := json.Marshal(instanceIdSet)
	logger.Infof("Sort instance: %s", string(b))

	upgradeInstanceId := instanceIdSet[0]
	upgradeCvmInstance := cvmInstanceMap[*upgradeInstanceId]

	if upgradeCvmInstance == nil {
		return nil, errorcode.InternalErrorCode.NewWithMsg("Can not get upgrade controller instance")
	}
	upgradeCvmDiskSize := cvmInstanceDiskMap[*upgradeInstanceId]

	if s.Tke.ArchGeneration == constants.TKE_ARCH_GENERATION_V2 {
		return cvmInstanceMap[*upgradeInstanceId], nil
	}

	if *upgradeCvmInstance.InstanceType == newInstanceType && upgradeCvmDiskSize == newDiskSize {
		return nil, nil
	}
	return cvmInstanceMap[*upgradeInstanceId], nil
}

func (s *upgradeControllerService) checkUpgradedV3() (rsp *flow.TaskExecResponse) {
	_, exist := s.GetParam(constants.FLOW_PARAM_UPGRADE_CONTROLLER_FINISHED, "")
	if exist {
		return s.DoneRsp("Stop upgrade controller")
	}
	err := s.unScheduleController()
	if err != nil {
		return s.RetryRspWithErr(err)
	}
	zkPod, err := s.getControllerZKPod(false)
	if err != nil {
		return s.RetryRspWithErr(err)
	}
	pvName, err := s.deleteZKPVC(zkPod)
	if err != nil {
		return s.RetryRspWithErr(err)
	}
	err = s.deleteZKPV(pvName)
	if err != nil {
		return s.RetryRspWithErr(err)
	}
	deleted, err := s.deleteZKPod(zkPod, false)
	if err != nil {
		return s.RetryRspWithErr(err)
	}
	if deleted {
		return s.RetryRsp("zk deleted")
	}

	forceGetZKPod, err := s.getControllerZKPod(true)
	if err != nil {
		return s.RetryRspWithErr(err)
	}
	if len(forceGetZKPod) != 0 {
		return s.RetryRsp("zk pod not deleted")
	}

	running, err := s.checkZKPodRunning(zkPod)
	if err != nil {
		return s.RetryRspWithErr(err)
	}
	if !running {
		return s.RetryRsp("zk pod are not restart")
	}

	deleted, err = s.deleteControllerPod()
	if err != nil {
		return s.RetryRspWithErr(err)
	}
	if deleted {
		return s.RetryRsp("controller deleted")
	}
	// 内网手动删除节点
	if s.ClusterGroup.NetEnvironmentType == constants.NETWORK_ENV_CLOUD_VPC {
		err = s.deleteControllerFromTke()
		if err != nil {
			return s.RetryRspWithErr(err)
		}
		err = s.terminateController()
		if err != nil {
			return s.RetryRspWithErr(err)
		}
	}
	running, err = s.checkAllPodRunning()
	if err != nil {
		return s.RetryRspWithErr(err)
	}
	if !running {
		return s.RetryRsp("Some pod are not restart")
	}
	return s.DoneRsp("check controller down")
}

func (s *upgradeControllerService) checkUpgraded() (rsp *flow.TaskExecResponse) {
	_, exist := s.GetParam(constants.FLOW_PARAM_UPGRADE_CONTROLLER_FINISHED, "")
	if exist {
		return s.DoneRsp("Stop upgrade controller")
	}
	_, exist = s.GetParam(constants.FLOW_PARAM_UPGRADE_CONTROLLER_DELETE, "")
	controllerDeleted := 0
	if exist {
		controllerDeleted = 1
	}
	running, err := IsAllControllersRunning(s.ClusterGroup, s.Cluster, s.Tke, int64(constants.TKE_CONTROL_NODE_NUM+1-controllerDeleted))
	if err != nil {
		return s.RetryRspWithErr(err)
	}
	if !running {
		return s.RetryRsp("New Controller is not running")
	}
	err = s.unScheduleController()
	if err != nil {
		return s.RetryRspWithErr(err)
	}
	zkPod, err := s.getControllerZKPod(false)
	if err != nil {
		return s.RetryRspWithErr(err)
	}
	pvName, err := s.deleteZKPVC(zkPod)
	if err != nil {
		return s.RetryRspWithErr(err)
	}
	err = s.deleteZKPV(pvName)
	if err != nil {
		return s.RetryRspWithErr(err)
	}
	deleted, err := s.deleteZKPod(zkPod, false)
	if err != nil {
		return s.RetryRspWithErr(err)
	}
	if deleted {
		return s.RetryRsp("zk deleted")
	}

	forceGetZKPod, err := s.getControllerZKPod(true)
	if err != nil {
		return s.RetryRspWithErr(err)
	}
	if len(forceGetZKPod) != 0 {
		return s.RetryRsp("zk pod not deleted")
	}

	running, err = s.checkZKPodRunning(zkPod)
	if err != nil {
		return s.RetryRspWithErr(err)
	}
	if !running {
		return s.RetryRsp("zk pod are not restart")
	}

	deleted, err = s.deleteControllerPod()
	if err != nil {
		return s.RetryRspWithErr(err)
	}
	if deleted {
		return s.RetryRsp("controller deleted")
	}
	err = s.deleteControllerFromTke()
	if err != nil {
		return s.RetryRspWithErr(err)
	}
	err = s.terminateController()
	if err != nil {
		return s.RetryRspWithErr(err)
	}
	running, err = s.checkAllPodRunning()
	if err != nil {
		return s.RetryRspWithErr(err)
	}
	if !running {
		return s.RetryRsp("Some pod are not restart")
	}
	return s.DoneRsp("check controller down")
}

func (s *upgradeControllerService) terminateController() (err error) {
	instanceId, _ := s.GetParam(constants.FLOW_PARAM_UPGRADE_CONTROLLER_INSTANCE, "")
	if len(instanceId) == 0 {
		return errorcode.InternalErrorCode.NewWithMsg("Can not find upgrade controller instance")
	}
	instanceIdSet := make([]*string, 0)
	instanceIdSet = append(instanceIdSet, &instanceId)
	secretId, secretKey, err := service4.GetSecretIdAndKeyByNetworkEnvType(s.ClusterGroup.NetEnvironmentType)
	if err != nil {
		return
	}

	cvmService := cvm.GetCvmService()
	cvmInstanceSet, err := cvmService.DescribeInstancesForIds(secretId, secretKey, s.ClusterGroup.Region, instanceIdSet)
	if err != nil {
		return errorcode.InternalErrorCode.NewWithErr(err)
	}

	if len(cvmInstanceSet) == 0 {
		return nil
	}

	for _, instance := range cvmInstanceSet {
		if *instance.InstanceState == "TERMINATING" {
			return nil
		}
	}

	err = cbs.GetCbsService(s.ClusterGroup.NetEnvironmentType, s.Region).TerminateCbsFromCVM(instanceIdSet)
	if err != nil {
		return errorcode.InternalErrorCode.NewWithErr(err)
	}

	return cvm.GetCvmService().TerminateInstancesWithScsAccount(s.Region, instanceIdSet)
}

func (s *upgradeControllerService) getControllerZKPod(force bool) (zkPod string, err error) {
	existPod, exist := s.GetParam(constants.FLOW_PARAM_UPGRADE_CONTROLLER_ZK_POD, "")
	if exist && !force {
		return existPod, nil
	}
	nodeName, err := s.getControllerNodeName()
	if err != nil {
		return "", errorcode.InternalErrorCode.NewWithErr(err)
	}
	if len(nodeName) == 0 {
		if force {
			return "", nil
		}
		return "", errorcode.InternalErrorCode.NewWithMsg("no node name found")
	}
	k8sService := k8s.GetK8sService()
	kubConfig := []byte(s.Cluster.KubeConfig)
	client, err := k8sService.NewClient(kubConfig)
	if err != nil {
		return "", errorcode.InternalErrorCode.NewWithErr(err)
	}
	//--field-selector spec.nodeName=9.165.0.11 --selector app.kubernetes.io/component=zookeeper
	fSet := fields.Set{"spec.nodeName": nodeName}

	podList, err := k8sService.ListPod(client, constants.OCEANUS_NAMESPACE,
		metav1.ListOptions{FieldSelector: fields.SelectorFromSet(fSet).String()})
	if err != nil {
		return "", errorcode.InternalErrorCode.NewWithErr(err)
	}
	for _, pod := range podList.Items {
		if strings.HasPrefix(pod.Name, "zookeeper") {
			zkPod = pod.Name
			break
		}
	}
	if force {
		return zkPod, nil
	}
	if len(zkPod) == 0 {
		msg := fmt.Sprintf("zk pod not found in %s", nodeName)
		return "", errorcode.InternalErrorCode.NewWithMsg(msg)
	}
	logger.Infof("zk pod name is %s", zkPod)
	s.SetReturnParam(constants.FLOW_PARAM_UPGRADE_CONTROLLER_ZK_POD, zkPod)
	return zkPod, nil
}

func (s *upgradeControllerService) deleteZKPVC(zkPod string) (pvName string, err error) {
	pvName, exist := s.GetParam(constants.FLOW_PARAM_UPGRADE_CONTROLLER_DELETE_PVC, "")
	if exist {
		return pvName, nil
	}
	k8sService := k8s.GetK8sService()
	kubConfig := []byte(s.Cluster.KubeConfig)
	client, err := k8sService.NewClient(kubConfig)
	if err != nil {
		return "", errorcode.InternalErrorCode.NewWithErr(err)
	}

	pvName, err = k8sService.DeletePVC(client, constants.OCEANUS_NAMESPACE, fmt.Sprintf("data-%s", zkPod))
	if err != nil {
		return "", errorcode.InternalErrorCode.NewWithErr(err)
	}

	if len(pvName) == 0 {
		msg := fmt.Sprintf("pv name not found in %s", pvName)
		return "", errorcode.InternalErrorCode.NewWithMsg(msg)
	}
	logger.Infof("zk pv name is %s", pvName)
	s.SetReturnParam(constants.FLOW_PARAM_UPGRADE_CONTROLLER_DELETE_PVC, pvName)
	return pvName, nil
}

func (s *upgradeControllerService) deleteZKPV(pvName string) (err error) {
	_, exist := s.GetParam(constants.FLOW_PARAM_UPGRADE_CONTROLLER_DELETE_PV, "")
	if exist {
		return nil
	}
	k8sService := k8s.GetK8sService()
	kubConfig := []byte(s.Cluster.KubeConfig)
	client, err := k8sService.NewClient(kubConfig)
	if err != nil {
		return errorcode.InternalErrorCode.NewWithErr(err)
	}

	err = k8sService.DeletePV(client, pvName)
	if err != nil {
		return errorcode.InternalErrorCode.NewWithErr(err)
	}
	logger.Infof("delete pv %s", pvName)
	s.SetReturnParam(constants.FLOW_PARAM_UPGRADE_CONTROLLER_DELETE_PV, pvName)
	return nil
}

func (s *upgradeControllerService) deleteZKPod(zkPod string, force bool) (delete bool, err error) {
	_, exist := s.GetParam(constants.FLOW_PARAM_UPGRADE_CONTROLLER_DELETE_ZK, "")
	if exist && !force {
		return false, nil
	}
	k8sService := k8s.GetK8sService()
	kubConfig := []byte(s.Cluster.KubeConfig)
	client, err := k8sService.NewClient(kubConfig)
	if err != nil {
		return false, errorcode.InternalErrorCode.NewWithErr(err)
	}

	err = k8sService.DeletePod(client, constants.OCEANUS_NAMESPACE, zkPod)
	if err != nil {
		return false, errorcode.InternalErrorCode.NewWithErr(err)
	}
	logger.Infof("delete zk pod %s", zkPod)
	s.SetReturnParam(constants.FLOW_PARAM_UPGRADE_CONTROLLER_DELETE_ZK, zkPod)
	return true, nil
}

func (s *upgradeControllerService) deleteControllerPod() (delete bool, err error) {
	_, exist := s.GetParam(constants.FLOW_PARAM_UPGRADE_CONTROLLER_DELETE_POD, "")
	if exist {
		return false, nil
	}
	nodeName, err := s.getControllerNodeName()
	if err != nil {
		return false, errorcode.InternalErrorCode.NewWithErr(err)
	}
	if len(nodeName) == 0 {
		return false, nil
	}
	k8sService := k8s.GetK8sService()
	kubConfig := []byte(s.Cluster.KubeConfig)
	client, err := k8sService.NewClient(kubConfig)
	if err != nil {
		return false, errorcode.InternalErrorCode.NewWithErr(err)
	}
	//--field-selector spec.nodeName=9.165.0.11 --selector app.kubernetes.io/component=zookeeper
	fSet := fields.Set{"spec.nodeName": nodeName}
	deleted, err := k8sService.DeletePods(client, constants.OCEANUS_NAMESPACE,
		metav1.ListOptions{FieldSelector: fields.SelectorFromSet(fSet).String()})
	if err != nil {
		return false, errorcode.InternalErrorCode.NewWithErr(err)
	}
	logger.Infof("delete pod %+v", deleted)
	statefulSetMap := make(map[string]struct{})
	for _, pod := range deleted {
		statefulSet := pod[0 : len(pod)-2]
		statefulSetMap[statefulSet] = struct{}{}
	}
	logger.Infof("delete statefulSet %+v", statefulSetMap)
	s.SetReturnParamUseJson(constants.FLOW_PARAM_UPGRADE_CONTROLLER_DELETE_POD, statefulSetMap)
	return true, nil
}

func (s *upgradeControllerService) deleteControllerFromTke() (err error) {
	_, exist := s.GetParam(constants.FLOW_PARAM_UPGRADE_CONTROLLER_DELETE, "")
	if exist {
		return nil
	}
	instanceId, _ := s.GetParam(constants.FLOW_PARAM_UPGRADE_CONTROLLER_INSTANCE, "")
	if len(instanceId) == 0 {
		return errorcode.InternalErrorCode.NewWithMsg("Can not find upgrade controller instance")
	}
	instanceIds := make([]*string, 0)
	instanceIds = append(instanceIds, &instanceId)

	region := s.ClusterGroupService.GetClusterGroup().Region
	tkeService := tke.GetTkeService()
	secretId, secretKey, err := service4.GetSecretIdAndKeyByNetworkEnvType(s.ClusterGroup.NetEnvironmentType)
	if err != nil {
		return
	}
	totalCount, _, err := tkeService.DescribeClusterInstances(secretId, secretKey, "", region,
		tkeService.NewDefaultDescribeClusterInstancesRequestBuilder().
			WithClusterId(s.Tke.InstanceId).
			WithInstanceIds(instanceIds).
			Build())
	if err != nil {
		return errorcode.InternalErrorCode.NewWithErr(err)
	}
	if totalCount == 0 {
		return nil
	}

	rsp, err := tkeService.DeleteClusterInstancesForIdsByNetEnvironmentType(s.ClusterGroup.NetEnvironmentType, region, s.Tke.InstanceId, instanceIds)
	if err != nil {
		return errorcode.InternalErrorCode.NewWithErr(err)
	}
	tmp, _ := json.Marshal(rsp)
	logger.Info("[%s]: DeleteClusterInstances return rsp: %s", string(tmp))
	s.SetReturnParam(constants.FLOW_PARAM_UPGRADE_CONTROLLER_DELETE, "1")
	return nil
}

func (s *upgradeControllerService) checkZKPodRunning(zkPod string) (running bool, err error) {
	k8sService := k8s.GetK8sService()
	kubConfig := []byte(s.Cluster.KubeConfig)
	client, err := k8sService.NewClient(kubConfig)
	if err != nil {
		return false, errorcode.InternalErrorCode.NewWithErr(err)
	}
	pod, err := k8sService.GetPod(client, constants.OCEANUS_NAMESPACE, zkPod)
	if err != nil {
		return false, errorcode.InternalErrorCode.NewWithErr(err)
	}
	deleteZKPod := false
	if pod.Status.Phase == v1.PodFailed {
		deleteZKPod = true
	}

	if pod.Status.Phase == v1.PodPending {
		for _, cond := range pod.Status.Conditions {
			if cond.Type == v1.PodScheduled && cond.Status == v1.ConditionFalse && cond.Reason == "Unschedulable" {
				deleteZKPod = true
				break
			}
		}
	}
	if deleteZKPod {
		_, err := s.deleteZKPod(zkPod, true)
		if err != nil {
			return false, errorcode.InternalErrorCode.NewWithErr(err)
		}
		return false, nil
	}

	ready, err := k8sService.StatefulSetAppsV1Ready(client, constants.OCEANUS_NAMESPACE, zkPod[0:len(zkPod)-2])
	if err != nil {
		return false, errorcode.InternalErrorCode.NewWithErr(err)
	}
	if !ready {
		//checkTimes, _, err := s.GetParamInt(constants.FLOW_PARAM_UPGRADE_CONTROLLER_ZK_CHECK, 0)
		//if err != nil {
		//	return false, errorcode.InternalErrorCode.NewWithErr(err)
		//}
		//checkTimes += 1
		//s.SetReturnParam(constants.FLOW_PARAM_UPGRADE_CONTROLLER_ZK_CHECK, fmt.Sprintf("%d", checkTimes))
		//if checkTimes%500 == 0 {
		//	_, err := s.deleteZKPod(zkPod, true)
		//	if err != nil {
		//		return false, errorcode.InternalErrorCode.NewWithErr(err)
		//	}
		//}
		return false, nil
	}
	return true, nil
}

func (s *upgradeControllerService) checkAllPodRunning() (running bool, err error) {
	statefulSetMap := make(map[string]struct{})
	_, err = s.GetParamUseJson(constants.FLOW_PARAM_UPGRADE_CONTROLLER_DELETE_POD, &statefulSetMap)
	if err != nil {
		return false, errorcode.InternalErrorCode.NewWithErr(err)
	}
	k8sService := k8s.GetK8sService()
	kubConfig := []byte(s.Cluster.KubeConfig)
	client, err := k8sService.NewClient(kubConfig)
	if err != nil {
		return false, errorcode.InternalErrorCode.NewWithErr(err)
	}
	for statefulSet := range statefulSetMap {
		ready, err := k8sService.StatefulSetAppsV1Ready(client, constants.OCEANUS_NAMESPACE, statefulSet)
		if err != nil {
			return false, errorcode.InternalErrorCode.NewWithErr(err)
		}
		if !ready {
			return false, nil
		}
	}
	return true, nil
}

func (s *upgradeControllerService) unScheduleController() (err error) {
	nodeName, err := s.getControllerNodeName()
	if err != nil {
		return errorcode.InternalErrorCode.NewWithErr(err)
	}
	if len(nodeName) == 0 {
		return nil
	}
	k8sService := k8s.GetK8sService()
	kubConfig := []byte(s.Cluster.KubeConfig)
	client, err := k8sService.NewClient(kubConfig)
	if err != nil {
		return errorcode.InternalErrorCode.NewWithErr(err)
	}
	_, err = k8sService.NodeSchedule(client, nodeName, true)
	if err != nil {
		return errorcode.InternalErrorCode.NewWithErr(err)
	}
	return nil
}

func (s *upgradeControllerService) getControllerNodeName() (nodeName string, err error) {
	instanceId, _ := s.GetParam(constants.FLOW_PARAM_UPGRADE_CONTROLLER_INSTANCE, "")
	if len(instanceId) == 0 {
		return "", errorcode.InternalErrorCode.NewWithMsg("instance length is 0")
	}

	k8sService := k8s.GetK8sService()
	kubConfig := []byte(s.Cluster.KubeConfig)
	client, err := k8sService.NewClient(kubConfig)
	if err != nil {
		return "", errorcode.InternalErrorCode.NewWithErr(err)
	}

	nodeList, err := k8sService.ListNode(client, metav1.ListOptions{
		LabelSelector: fmt.Sprintf("%s==%s", "cloud.tencent.com/node-instance-id", instanceId),
	})
	if err != nil {
		return "", errorcode.InternalErrorCode.NewWithErr(err)
	}
	if len(nodeList.Items) == 0 {
		return "", nil
	}

	if len(nodeList.Items) != 1 {
		msg := fmt.Sprintf("%s number is not one, but %d", instanceId, len(nodeList.Items))
		return "", errorcode.InternalErrorCode.NewWithMsg(msg)
	}

	return nodeList.Items[0].Name, nil
}
