package taskHandler

import (
	"encoding/json"
	"fmt"
	"math/rand"
	"sort"
	"strconv"
	"strings"

	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common"
	cvm "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/cvm/v20170312"
	sdkTke "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/tke/v20180525"
	v13 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cls"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"

	"tencentcloud.com/tstream_galileo/src/common/auth"

	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/dlocker"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	cvm2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cvm"
	table3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/Cvm"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/tke"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/yunti"
	service4 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service5 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cbs"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config/common_config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config/flowCC"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/configuration_center"
	cvm3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cvm"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/tke"
	tke2 "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke"
	v20180525 "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke"
)

type ApplyTKEHandler struct {
}

func (c *createTkeApp) createEKSCluster() (instanceId string, err error) {
	tkeService := tke.GetTkeService()
	cg := c.ctx.ClusterGroup
	cc, err := c.ctx.CC().FlowCC().TkeCC().ClusterConfig(cg)
	if err != nil {
		return "", err
	}

	logger.Info("eks cc %v", cc)
	describeEKSClustersRequest := sdkTke.NewDescribeEKSClustersRequest()
	describeEKSClustersRequest.Filters = []*sdkTke.Filter{
		{
			Name:   common.StringPtr("ClusterName"),
			Values: common.StringPtrs([]string{cg.SerialId}),
		},
	}

	totalCount, clusters, err := tkeService.DescribeEKSClustersWithScsAccount(
		cg.Region, describeEKSClustersRequest)
	if err != nil {
		return "", err
	}
	if totalCount == 1 {
		return *clusters[0].ClusterId, nil
	}
	if totalCount > 1 {
		msg := fmt.Sprintf("too many ekses with name %s, count is %d", cg.SerialId, totalCount)
		return "", errorcode.InternalErrorCode.NewWithInfo(msg, nil)
	}

	createEKSClusterRequest := sdkTke.NewCreateEKSClusterRequest()

	createEKSClusterRequest.K8SVersion = common.StringPtr(cc.EksVersion)
	createEKSClusterRequest.VpcId = common.StringPtr(c.ctx.Cluster.VpcId)
	createEKSClusterRequest.ClusterName = common.StringPtr(cg.SerialId)
	createEKSClusterRequest.SubnetIds = common.StringPtrs([]string{c.ctx.Cluster.SubnetId})
	createEKSClusterRequest.ServiceSubnetId = common.StringPtr(cc.Cidr)

	clusterId, err := tkeService.CreateEKSClusterWithScsAccount(cg.Region, createEKSClusterRequest)
	if err != nil {
		return "", err
	}
	return clusterId, nil
}

func (c *createTkeApp) createCluster(requestId string, archGeneration int) (instanceId string, err error) {
	tkeService := tke.GetTkeService()
	cg := c.ctx.ClusterGroup
	zone := cg.Zone
	cc, err := c.ctx.CC().FlowCC().TkeCC().ClusterConfig(cg)
	if err != nil {
		return "", err
	}

	logger.Info("tke cc %v", cc)
	// 根据网络环境查询对应环境(公有云和内网)下对应区域存不存在正在创建的该TKE集群
	totalCount, clusters, err := tkeService.DescribeClustersWithScsAccountByNetEnvironmentType(cg.NetEnvironmentType,
		cg.Region,
		tkeService.NewDefaultDescribeClustersRequestBuilder().
			WithFilterClusterName([]string{cg.SerialId}).
			Build())
	if err != nil {
		return "", err
	}
	if totalCount == 1 {
		return *clusters[0].ClusterId, nil
	}
	if totalCount > 1 {
		msg := fmt.Sprintf("too many tkes with name %s, count is %d", cg.SerialId, totalCount)
		return "", errorcode.InternalErrorCode.NewWithInfo(msg, nil)
	}

	instanceType := cc.ControllerSpec
	disSize := int64(constants.TKE_CONTROL_NODE_DISK_SIZE)
	// 共享资源池使用 worker 节点的配置
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM {
		instanceType = cc.WorkerSpec
		//isWorker = true
		disSize = constants.TKE_CONTROL_NODE_DISK_SIZE_UNIFORM
	}

	cvmConfList, err := service3.GetTableService().ListCvmSaleConfByInstanceType(instanceType, 0, 1)
	if err != nil {
		return
	}
	if len(cvmConfList) == 0 {
		msg := fmt.Sprintf("cvm spec %s not found in table CvmSaleConf", instanceType)
		err = errorcode.InternalErrorCode.NewWithInfo(msg, nil)
		return
	}

	label := map[string]string{constants.TKE_CVM_LABEL_KEY: constants.TKE_CONTROL_NODE_LABEL_VAL}
	if archGeneration >= constants.TKE_ARCH_GENERATION_V2 {
		label[constants.TKE_CVM_ZK_LABEL_KEY] = constants.TKE_ZK_NODE_LABEL_VAL
	}
	label[constants.TKE_CVM_NODE_ZONE] = c.ctx.ClusterGroup.Zone

	// 如果是内网的购买集群走另外一套逻辑(目前只能从云梯购买CVM然后再创建TKE集群)
	controllerNum := constants.DefaultControllerNum(archGeneration, cg.Type)
	if cg.NetEnvironmentType == constants.NETWORK_ENV_INNER_VPC {
		clusterId, err := c.CreateInnerTkeClusterWithController(zone, constants.CONTROLLER_CVM_ORDER_ID,
			label, tkeService, cc, requestId, cvmConfList[0],
			controllerNum, int(disSize))
		if err != nil {
			return constants.EMPTY, err
		}
		return clusterId, nil
	}

	version := cc.Version
	//containerRuntime := "docker"
	//runtimeVersion := "19.3"
	//
	//if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM {
	//	version = "1.28.3"
	//	containerRuntime = "containerd"
	//	runtimeVersion = "1.6.9"
	//}

	request := tkeService.NewDefaultCreateClusterRequestBuilder().
		WithClusterCIDRSettingsService(cc.MaxServiceNum, constants.TKE_SERVICE_CIDR).
		WithClusterCIDRSettingsBasic(cc.Cidr, false, cc.MaxNodePodNum).
		WithClusterBasicSettings(cc.OS, version, cg.SerialId, c.ctx.Cluster.VpcId,
			constants.TKE_PROJECT_ID, false).
		WithClusterAdvancedSettingsNetworkTypeAudit("", false, false, true).
		WithExtensionAddons().
		Build()

	instanceId, err = tkeService.CreateClusterWithScsAccountByNetEnvironmentType(cg.NetEnvironmentType, cg.Region, request)
	return
}

func (c *createTkeApp) CreateInnerTkeClusterWithController(zone string, orderIdKey string, nodeLabel map[string]string, tkeService *tke.TkeService, cc *flowCC.TkeClusterConfig, requestId string, prefer *table3.CvmSaleConfig, count int64, diskSize int) (string, error) {
	InternalClusterConfig, err := c.ctx.CC().FlowCC().TkeCC().InternalClusterConfig()
	logger.Info("InternalClusterConfig: %v", InternalClusterConfig)
	if err != nil {
		return constants.EMPTY, err
	}

	//_, exists := c.ctx.GetParam(orderIdKey, constants.EMPTY)
	//orderId, err := BuyCVMOrReturnOrderId(c.ctx, zone, InternalClusterConfig, orderIdKey, constants.TKE_CONTROL_NODE_LABEL_VAL, constants.TKE_CVM_DISK_TYPE, diskSize, requestId, prefer, count)
	//if err != nil {
	//	return constants.EMPTY, err
	//}
	//
	//msg := fmt.Sprintf("Controller Order create: %s", orderId)
	//logger.Infof(msg)
	//if !exists { //创建订单，先返回
	//	return constants.EMPTY, errorcode.InternalErrorCode.NewWithMsg(msg)
	//}

	cg := c.ctx.ClusterGroup

	//cvmInstanceIds, err := GetInnerCvmInstanceIds(c.ctx, requestId, orderId)
	//if err != nil {
	//	return constants.EMPTY, err
	//}

	//rainbowOceanusClusterPassword, getPasswordError := common_config.GetRainbowOceanusClusterPassword()
	//if getPasswordError != nil {
	//	logger.Errorf("Failed to get oceanus cluster password in Rainbow")
	//	return constants.EMPTY, getPasswordError
	//}

	// 设置已存在的CVM实例重装参数
	//existedInstancesPara := c.BuildExistedInstancesPara(InternalClusterConfig, cvmInstanceIds, rainbowOceanusClusterPassword)

	// 设置节点数据盘格式化信息和节点标签信息
	//instanceAdvancedSettingsOverride := c.BuildInstanceAdvancedSettings(InternalClusterConfig, nodeLabel)

	// 构建TKE集群的标签
	tagSpecifications := c.BuildTkeTagSpecification(InternalClusterConfig)

	networkType := InternalClusterConfig.NetworkType
	// serviceCIDR mask size must be in 17-27
	serviceCidr := InternalClusterConfig.ServiceCidr

	request := tkeService.NewDefaultCreateInnerClusterRequestBuilder().
		WithClusterBasicSettingsWithLabel(tagSpecifications, cc.OS, cc.Version, cg.SerialId, c.ctx.Cluster.VpcId,
			constants.TKE_PROJECT_ID, false).
		WithClusterCIDRSettings(constants.EMPTY, false, cc.MaxNodePodNum, serviceCidr, c.ctx.Cluster.SubnetId, true).
		WithClusterAdvancedSettingsNetworkTypeAudit(networkType, true, true, true).
		WithInstanceAdvancedSettings(nodeLabel, cc.UserScript).
		//WithExistedInstancesForNode(constants.TKE_NODE_ROLE, existedInstancesPara, instanceAdvancedSettingsOverride).
		WithExtensionAddons().
		Build()

	clusterId, err := tkeService.CreateClusterWithScsAccountByNetEnvironmentType(cg.NetEnvironmentType, cg.Region, request)
	if err != nil {
		return "", err
	}

	return clusterId, nil
}

func GetInnerCvmInstanceIds(c *deploy.Context, requestId string, orderId string) (cvmInstanceIds []*string, err error) {
	// 根据订单查询订单状态
	QueryOrdersRsp, err := QueryCreateInnerCVMOrder(c, requestId, orderId)
	if err != nil {
		return nil, err
	}
	// 判断有没有从云梯返回的error
	if QueryOrdersRsp.Error.Code != 0 {
		logger.Errorf("RequestId: %s queryOrders with error: %s", requestId, QueryOrdersRsp.Error.Message)
		return nil, errorcode.InternalErrorCode.NewWithMsg(QueryOrdersRsp.Error.Message)
	}

	if QueryOrdersRsp.Result.Data[0].IsOver != 1 {
		// 订单是非结单状态
		return nil, errorcode.InternalErrorCode.NewWithMsg("Order status is not over")
	}

	// 订单状态为结单了，查询生产的CVM实例状态是不是RUNNING的
	QueryCVMInstancesRsp, err := QueryCreateInnerCVMInstances(c, requestId, orderId)
	if err != nil {
		return nil, err
	}
	// 判断有没有从云梯返回的error
	if QueryCVMInstancesRsp.Error.Code != 0 {
		logger.Errorf("RequestId: %s QueryCVMInstances with error: %s", requestId, QueryCVMInstancesRsp.Error.Message)
		return nil, errorcode.InternalErrorCode.NewWithMsg(QueryCVMInstancesRsp.Error.Message)
	}

	cvmInstanceIds = make([]*string, 0)
	cvms := QueryCVMInstancesRsp.Result.Data
	for i := 0; i < len(cvms); i++ {
		if strings.ToUpper(cvms[i].InstanceStatus) != "RUNNING" {
			logger.Warningf("RequestId: %s, CVM instanceId: %s, status: %s is not RUNNING status", requestId, cvms[i].InstanceId, cvms[i].InstanceStatus)
			// 返回个错误让流程继续重试
			return nil, errorcode.InternalErrorCode.NewWithMsg("Not all cvm of the cvmOrder is running status")
		}
		cvmInstanceIds = append(cvmInstanceIds, &cvms[i].InstanceId)
	}
	return cvmInstanceIds, nil
}

func BuyCVMOrReturnOrderId(c *deploy.Context, zone string, InternalTkeClusterConfig *flowCC.InternalTkeClusterConfig, orderIdKey, instanceName, dataDiskType string, dataDiskSize int, requestId string, prefer *table3.CvmSaleConfig, count int64) (string, error) {
	// 检查购买CVM流程订单状态
	orderId, exists := c.GetParam(orderIdKey, constants.EMPTY)

	if exists {
		return orderId, nil
	}
	// 如果不存在说明是第一次请求还没有orderId则给云梯发请求申领CVM
	// 申领CVM之前先查询可以申领CVM的容量
	QueryApplyCapacityRsp, err := QueryApplyCapacityForInnerCVM(c, zone, InternalTkeClusterConfig, dataDiskType, requestId, prefer)
	if err != nil {
		logger.Errorf("RequestId %s: QueryApplyCapacityForInnerCVM with error %+v", requestId, err)
		return constants.EMPTY, err
	}
	if QueryApplyCapacityRsp.Error.Code != 0 {
		// 云梯接口有返回报错信息
		logger.Errorf("RequestId %s: QueryApplyCapacityForInnerCVM with error code: %d, msg: %s", requestId, QueryApplyCapacityRsp.Error.Code, QueryApplyCapacityRsp.Error.Message)
		return constants.EMPTY, err
	}
	if QueryApplyCapacityRsp.Result.MaxNum < count {
		logger.Errorf("RequestId %s, need %d cvm, but only can buy %d cvm", requestId, count, QueryApplyCapacityRsp.Result.MaxNum)
		return constants.EMPTY, errorcode.InternalErrorCode.NewWithMsg("cvm not enough")
	}
	// 加锁，避免多次申领CVM
	locker := dlocker.NewDlocker("oceanus-flow-CreateInnerCVM", fmt.Sprintf("optId-%s", c.Request.TaskId), 6000)
	err = locker.Lock()
	if err != nil {
		// 打印日志
		logger.Infof("Another oceanus-flow-CreateInnerCVM process for %s has lock but not finished yet", c.Request.TaskId)
		return constants.EMPTY, nil
	}
	defer locker.UnLock()
	// 购买CVM,按照配置购买CVM
	createCvmOrderRspStr, err := CreateInnerCVM(c, zone, InternalTkeClusterConfig, requestId, instanceName, dataDiskType, dataDiskSize, prefer, count)
	if err != nil {
		logger.Errorf("CreateInnerCVMwitherr: %s", err.Error())
		// 请求申领CVM出错了,不能贸然重试,此时要返回一个错误让流程不能重试否则可能导致申领了很多CVM
		panic(err)
	}
	CreateCvmOrderRsp := &yunti.CreateCvmOrderRsp{}
	err = service5.UnPackResp(createCvmOrderRspStr, CreateCvmOrderRsp)
	if err != nil {
		logger.Errorf("CreateInnerCVMwitherr UnPackResp with err: %s", err.Error())
		// 解包出错则说明云梯返回的结构可能变化了导致了报错,不能贸然重试,此时要返回一个错误让流程不能重试否则可能导致申领了很多CVM
		panic(err)
	}
	// 校验有没有从云梯返回的错误,不能贸然重试,此时要返回一个错误让流程不能重试否则可能导致申领了很多CVM
	if CreateCvmOrderRsp.Error.Code != 0 {
		logger.Errorf("CreateInnerCVMwitherr RequestId: %s createCvmOrder with error: %s", requestId, CreateCvmOrderRsp.Error.Message)
		err = fmt.Errorf("code: %d, error: %s", CreateCvmOrderRsp.Error.Code, CreateCvmOrderRsp.Error.Message)
		if strings.Contains(CreateCvmOrderRsp.Error.Message, "申领资源不足") { //可以重试
			return constants.EMPTY, err
		}
		panic(err)
	}
	// 获取订单Id
	orderId = CreateCvmOrderRsp.Result.OrderId
	logger.Infof("createCvmOrderRspStrSuccess orderId: %s", orderId)
	// 重试流程的时候参数添加上orderId用于查询订单状态和生产的CVM状态等
	c.SetReturnParam(orderIdKey, orderId)
	return orderId, nil
}

func (h *ApplyTKEHandler) CreateInnerTkeClusterWithWorker(
	c *deploy.Context,
	zone string,
	count int64,
	prefer *table3.CvmSaleConfig,
	nodeLabel map[string]string,
	diskSize int64,
	diskType string,
	name string) (err error) {

	cc, err := c.FlowCC.TkeCC().ClusterConfig(c.ClusterGroup)
	if err != nil {
		return
	}

	tkeService := tke.GetTkeService()
	InternalClusterConfig, err := c.CC().FlowCC().TkeCC().InternalClusterConfig()
	logger.Info("InternalClusterConfig: %v", InternalClusterConfig)
	if err != nil {
		return err
	}
	requestId := c.RequestId
	k8sInstance := c.Tke

	_, exists := c.GetParam(constants.WORKER_CVM_ORDER_ID, constants.EMPTY)

	orderId, err := BuyCVMOrReturnOrderId(c,
		zone,
		InternalClusterConfig,
		constants.WORKER_CVM_ORDER_ID,
		constants.TKE_WORKER_NODE_LABEL_VAL,
		diskType,
		int(diskSize),
		requestId, prefer, count)
	if err != nil {
		logger.Errorf("BuyCVMOrReturnOrderIdError with %+v", err)
		return err
	}
	msg := fmt.Sprintf("WorkerOrderIdCcreate: %s", orderId)
	logger.Infof(msg)
	if !exists { //创建订单，先返回
		return errorcode.InternalErrorCode.NewWithMsg(msg)
	}

	cvmInstanceIds, err := GetInnerCvmInstanceIds(c, requestId, orderId)
	if err != nil {
		return err
	}

	rainbowOceanusClusterPassword, err := common_config.GetRainbowOceanusClusterPassword()
	if err != nil {
		logger.Errorf("Failed to get oceanus cluster password in Rainbow")
		return err
	}

	logSettings := &tke2.LoginSettings{
		Password: &rainbowOceanusClusterPassword,
	}

	securityGroupId := InternalClusterConfig.SecurityGroup
	diskPartition := InternalClusterConfig.DiskPartition

	isV1 := c.Tke.ArchGeneration >= constants.TKE_ARCH_GENERATION_V6
	mountTarget := constants.TKE_DATA_DISK_MOUNT_TARGET_FOR_CONTAINERD
	if !isV1 {
		mountTarget = constants.TKE_DATA_DISK_MOUNT_TARGET
	}
	request := tkeService.NewAddClusterExistedInstancesRequestBuilder().
		WithSkipValidateOptions([]string{"VpcCniCIDRCheck"}).
		WithClusterId(k8sInstance.InstanceId).
		WithInstanceIds(cvmInstanceIds).
		WithInstanceAdvancedSettings(constants.TKE_DATA_DISK_AUTO_FORMAT_AND_MOUNT,
			constants.TKE_DATA_DISK_FILE_SYSTEM, mountTarget, diskPartition,
			nodeLabel, cc.UserScript).
		WithLoginSettings(logSettings).
		WithSecurityGroupIds([]*string{&securityGroupId}).
		Build()

	kubelet := make([]*string, 0)
	var rootDir string
	if isV1 {
		rootDir = fmt.Sprintf("%s=%s", constants.TKE_KUBELET_DIR, constants.TKE_DATA_DISK_MOUNT_TARGET_FOR_CONTAINERD)
	} else {
		rootDir = fmt.Sprintf("%s=%s", constants.TKE_KUBELET_DIR, constants.TKE_DATA_DISK_MOUNT_TARGET)
	}
	kubelet = append(kubelet, &rootDir)
	eviction := fmt.Sprintf("%s=%s", constants.TKE_EVICTION_HARD, constants.TKE_EVICTION_HARD_VALUE)
	kubelet = append(kubelet, &eviction)
	if auth.IsInWhiteList(int64(c.ClusterGroup.AppId), constants.WHITE_LIST_CPU_MANAGER_POLICY) {
		policy := "cpu-manager-policy=static"
		kubelet = append(kubelet, &policy)
	}
	request.InstanceAdvancedSettings.ExtraArgs = &v20180525.InstanceExtraArgs{
		Kubelet: kubelet,
	}

	logger.Infof("AddClusterExistedInstances request: %s", request.ToJsonString())
	err = tkeService.AddClusterExistedInstancesWithScsAccountByNetworkEnvType(c.ClusterGroup.NetEnvironmentType, c.ClusterGroup.Region, request)
	if err != nil {
		return err
	}

	return nil
}

func (c *createTkeApp) BuildTkeTagSpecification(InternalTkeClusterConfig *flowCC.InternalTkeClusterConfig) []*tke2.TagSpecification {
	tagKeyProduct := InternalTkeClusterConfig.TagKeyProduct
	tagValueProduct := InternalTkeClusterConfig.TagValueProduct
	tagKeyDepartment := InternalTkeClusterConfig.TagKeyDepartment
	tagValueDepartment := InternalTkeClusterConfig.TagValueDepartment
	tagBusiness1Key := InternalTkeClusterConfig.TagBusiness1Key
	tagBusiness1Value := InternalTkeClusterConfig.TagBusiness1Value
	tagBusiness2Key := InternalTkeClusterConfig.TagBusiness2Key
	tagBusiness2Value := InternalTkeClusterConfig.TagBusiness2Value
	tagPrincipalKey := InternalTkeClusterConfig.TagPrincipalKey
	tagPrincipalValue := InternalTkeClusterConfig.Operator
	tagResourceType := InternalTkeClusterConfig.TagResourceType

	tagSpecifications := make([]*tke2.TagSpecification, 0)
	tagSpecification := &tke2.TagSpecification{
		ResourceType: &tagResourceType,
		Tags: []*tke2.Tag{
			{
				Key:   &tagKeyProduct,
				Value: &tagValueProduct,
			},
			{
				Key:   &tagKeyDepartment,
				Value: &tagValueDepartment,
			},
			{
				Key:   &tagBusiness1Key,
				Value: &tagBusiness1Value,
			},
			{
				Key:   &tagBusiness2Key,
				Value: &tagBusiness2Value,
			},
			{
				Key:   &tagPrincipalKey,
				Value: &tagPrincipalValue,
			},
		},
	}

	tagSpecifications = append(tagSpecifications, tagSpecification)
	return tagSpecifications
}

func (c *createTkeApp) BuildExistedInstancesPara(
	InternalTkeClusterConfig *flowCC.InternalTkeClusterConfig,
	cvmInstanceIds []*string, rainbowOceanusClusterPassword string) *tke2.ExistedInstancesPara {
	securityGroupId := InternalTkeClusterConfig.SecurityGroup
	securityEnabled := true
	monitorEnabled := true

	// 已存在实例的重装参数
	existedInstancesPara := &tke2.ExistedInstancesPara{
		InstanceIds: cvmInstanceIds,
		LoginSettings: &tke2.LoginSettings{
			Password: &rainbowOceanusClusterPassword,
		},
		SecurityGroupIds: []*string{&securityGroupId},
		EnhancedService: &tke2.EnhancedService{
			SecurityService: &tke2.RunSecurityServiceEnabled{
				Enabled: &securityEnabled,
			},
			MonitorService: &tke2.RunMonitorServiceEnabled{
				Enabled: &monitorEnabled,
			},
		},
	}
	return existedInstancesPara
}

// 通过云梯接口查询生产的CVM状态
func QueryCreateInnerCVMInstances(c *deploy.Context, requestId, orderId string) (*yunti.QueryCVMInstancesRsp, error) {
	QueryCVMInstancesReq := &yunti.QueryCVMInstancesReq{
		Method: "QueryCVMInstances",
		Params: struct {
			Page struct {
				Start int `json:"start"`
				Size  int `json:"size"`
			} `json:"page"`
			OrderId []string `json:"orderId"`
		}{
			Page: struct {
				Start int `json:"start"`
				Size  int `json:"size"`
			}{
				Start: 0,
				Size:  200,
			},
			OrderId: []string{orderId},
		},
		Jsonrpc: "2.0",
		Id:      requestId,
	}
	QueryCVMInstancesRsp := &yunti.QueryCVMInstancesRsp{}
	QueryCVMInstancesRspStr, err := service5.SendRequest2YunTi("apply/api", "cvm", c.ClusterGroup.Region, QueryCVMInstancesReq)
	if err != nil {
		return nil, err
	}
	err = service5.UnPackResp(QueryCVMInstancesRspStr, QueryCVMInstancesRsp)
	if err != nil {
		return nil, err
	}
	return QueryCVMInstancesRsp, nil
}

// 通过云梯接口查询创建CVM的订单状态
func QueryCreateInnerCVMOrder(c *deploy.Context, requestId, orderId string) (*yunti.QueryOrdersRsp, error) {
	QueryOrdersReq := &yunti.QueryOrdersReq{
		Method: "queryOrders",
		Params: struct {
			Page struct {
				Start int `json:"start"`
				Size  int `json:"size"`
			} `json:"page"`
			OrderId []string `json:"orderId"`
		}{
			Page: struct {
				Start int `json:"start"`
				Size  int `json:"size"`
			}{
				Start: 0,
				Size:  200,
			},
			OrderId: []string{orderId},
		},
		Jsonrpc: "2.0",
		Id:      requestId,
	}
	QueryOrdersRsp := &yunti.QueryOrdersRsp{}
	QueryOrdersRspStr, err := service5.SendRequest2YunTi("apply/api", "cvm", c.ClusterGroup.Region, QueryOrdersReq)
	if err != nil {
		return nil, err
	}
	err = service5.UnPackResp(QueryOrdersRspStr, QueryOrdersRsp)
	if err != nil {
		return nil, err
	}
	return QueryOrdersRsp, nil
}

// 通过云梯接口购买CVM
func CreateInnerCVM(c *deploy.Context, zone string,
	InternalTkeClusterConfig *flowCC.InternalTkeClusterConfig,
	requestId, instanceName, dataDiskType string, dataDiskSize int,
	prefer *table3.CvmSaleConfig, count int64) (string, error) {

	zoneSubnets, err := c.Cluster.GetSupportedZoneSubnets()
	if err != nil {
		return "", err
	}
	subnet := zoneSubnets[zone]
	if subnet == "" {
		return "", fmt.Errorf("subnet is empty, zone %s", zone)
	}

	// 满足购买条件则请求接口去购买CVM
	CreateCvmOrderReq := &yunti.CreateCvmOrderReq{
		Method: "createCvmOrder",
		Params: struct {
			DeptId       int    `json:"deptId"`
			ProductId    int    `json:"productId"`
			OpDeptId     int    `json:"opDeptId"`
			Memo         string `json:"memo"`
			BakOperator  string `json:"bakOperator"`
			Business1Id  int    `json:"business1Id"`
			Business2Id  int    `json:"business2Id"`
			Business3Id  int    `json:"business3Id"`
			Operator     string `json:"operator"`
			ChargeType   string `json:"chargeType"`
			Region       string `json:"region"`
			StaticType   string `json:"staticType"`
			Zone         string `json:"zone"`
			VpcId        string `json:"vpcId"`
			SubnetId     string `json:"subnetId"`
			InstanceType string `json:"instanceType"`
			Image        struct {
				ImageId   string `json:"imageId"`
				ImageName string `json:"imageName"`
			} `json:"image"`
			SystemDiskType string `json:"systemDiskType"`
			SystemDiskSize int    `json:"systemDiskSize"`
			InstanceName   string `json:"instanceName"`
			DataDisk       []struct {
				DataDiskType string `json:"dataDiskType"`
				DataDiskSize int    `json:"dataDiskSize"`
			} `json:"dataDisk"`
			Security struct {
				SecurityGroupId string `json:"securityGroupId"`
			} `json:"security"`
			RenewFlag    string `json:"renewFlag"`
			ApplyNum     int    `json:"applyNum"`
			UseTime      string `json:"useTime"`
			ResourceType int    `json:"resourceType"`
		}{
			DeptId:       InternalTkeClusterConfig.DeptId,
			ProductId:    InternalTkeClusterConfig.ProductId,
			OpDeptId:     InternalTkeClusterConfig.OpDeptId,
			Memo:         InternalTkeClusterConfig.Memo,
			BakOperator:  InternalTkeClusterConfig.BakOperator,
			Business1Id:  InternalTkeClusterConfig.Business1Id,
			Business2Id:  InternalTkeClusterConfig.Business2Id,
			Business3Id:  InternalTkeClusterConfig.Business3Id,
			Operator:     InternalTkeClusterConfig.Operator,
			ChargeType:   constants.TKE_CVM_CHARGE_POSTPAID,
			Region:       c.ClusterGroup.Region,
			StaticType:   InternalTkeClusterConfig.StaticType,
			Zone:         zone,
			VpcId:        c.Cluster.VpcId,
			SubnetId:     subnet,
			InstanceType: service5.ResourceConfigSpec(prefer),
			Image: struct {
				ImageId   string `json:"imageId"`
				ImageName string `json:"imageName"`
			}{
				ImageId:   InternalTkeClusterConfig.ImageId,
				ImageName: InternalTkeClusterConfig.ImageName,
			},
			SystemDiskType: dataDiskType,
			SystemDiskSize: constants.TKE_NODE_SYSTEM_DISK_SIZE,
			InstanceName:   instanceName,
			DataDisk: []struct {
				DataDiskType string `json:"dataDiskType"`
				DataDiskSize int    `json:"dataDiskSize"`
			}{
				{
					DataDiskType: dataDiskType,
					DataDiskSize: dataDiskSize,
				},
			},
			Security: struct {
				SecurityGroupId string `json:"securityGroupId"`
			}{
				SecurityGroupId: InternalTkeClusterConfig.SecurityGroup,
			},
			RenewFlag:    constants.TKE_CVM_RENEW_FLAG,
			ApplyNum:     int(count),
			UseTime:      util.GetCurrentDate(),
			ResourceType: 7,
		},
		Jsonrpc: "2.0",
		Id:      requestId,
	}
	createCvmOrderRspStr, err := service5.SendRequest2YunTi("apply/api", "cvm", c.ClusterGroup.Region, CreateCvmOrderReq)
	logger.Debugf("CreateInnerCVMcreateCvmOrderRspStr是: %s", createCvmOrderRspStr)
	if err != nil {
		logger.Errorf("CreateInnerCVMcreateCvmOrderRspStrError failed err : %+v", err)
		return constants.EMPTY, err
	} else {
		logger.Infof("createCvmOrderRspStrSuccess, CreateInnerCVMcreateCvmOrderRspStr是: %s", createCvmOrderRspStr)
	}
	return createCvmOrderRspStr, nil
}

// 通过云梯接口查询要购买的CVM容量
func QueryApplyCapacityForInnerCVM(c *deploy.Context,
	zone string, InternalTkeClusterConfig *flowCC.InternalTkeClusterConfig,
	dataDiskType, requestId string, prefer *table3.CvmSaleConfig) (*yunti.QueryApplyCapacityRsp, error) {
	// 通过云梯接口查询最大可购买CVM容量
	QueryApplyCapacityReq := &yunti.QueryApplyCapacityReq{
		Method: "queryApplyCapacity",
		Params: struct {
			DeptId         int    `json:"deptId"`
			Business3Id    int    `json:"business3Id"`
			CloudCampus    string `json:"cloudCampus"`
			InstanceType   string `json:"instanceType"`
			ChargeType     string `json:"chargeType"`
			ImageId        string `json:"imageId"`
			VpcId          string `json:"vpcId"`
			SubnetId       string `json:"subnetId"`
			SystemDiskInfo struct {
				SystemDiskType string `json:"systemDiskType"`
				SystemDiskSize int    `json:"systemDiskSize"`
			} `json:"systemDiskInfo"`
			ResourceType int `json:"resourceType"`
		}{
			DeptId:       InternalTkeClusterConfig.DeptId,
			Business3Id:  InternalTkeClusterConfig.Business3Id,
			CloudCampus:  zone,
			InstanceType: service5.ResourceConfigSpec(prefer),
			ChargeType:   constants.TKE_CVM_CHARGE_POSTPAID,
			ImageId:      InternalTkeClusterConfig.ImageId,
			VpcId:        c.Cluster.VpcId,
			SubnetId:     c.Cluster.SubnetId,
			SystemDiskInfo: struct {
				SystemDiskType string `json:"systemDiskType"`
				SystemDiskSize int    `json:"systemDiskSize"`
			}{
				SystemDiskType: dataDiskType,
				SystemDiskSize: constants.TKE_NODE_SYSTEM_DISK_SIZE,
			},
			ResourceType: 7,
		},
		Jsonrpc: "2.0",
		Id:      requestId,
	}
	QueryApplyCapacityRsp := &yunti.QueryApplyCapacityRsp{}
	QueryApplyCapacityRspStr, err := service5.SendRequest2YunTi("capacity", "api", c.ClusterGroup.Region, QueryApplyCapacityReq)
	if err != nil {
		return nil, err
	}
	err = service5.UnPackResp(QueryApplyCapacityRspStr, QueryApplyCapacityRsp)
	if err != nil {
		return nil, err
	}
	return QueryApplyCapacityRsp, nil
}

func IsEKSClusterRunning(clusterGroup *table.ClusterGroup, cluster *table.Cluster,
	k8sInstance *table2.Tke) (running bool, err error) {

	describeEKSClustersRequest := sdkTke.NewDescribeEKSClustersRequest()
	describeEKSClustersRequest.Filters = []*sdkTke.Filter{
		{
			Name:   common.StringPtr("ClusterName"),
			Values: common.StringPtrs([]string{clusterGroup.SerialId}),
		},
	}
	secretId, secretKey, err := service4.GetSecretIdAndKeyByNetworkEnvType(clusterGroup.NetEnvironmentType)
	if err != nil {
		return false, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	instanceId := k8sInstance.InstanceId
	tkeService := tke.GetTkeService()

	totalCount, clusters, err := tkeService.DescribeEKSClusters(secretId, secretKey, clusterGroup.Region, describeEKSClustersRequest)

	if err != nil {
		return false, errorcode.InternalErrorCode.NewWithErr(err)
	}
	if totalCount != 1 {
		msg := fmt.Sprintf("tke instance count %d for instanceId %s", totalCount, instanceId)
		return false, errorcode.InternalErrorCode.NewWithInfo(msg, err)
	}

	if *clusters[0].Status != constants.TKE_STATUS_RUNNING_STR &&
		*clusters[0].Status != constants.EKS_STATUS_IDLING_STR {
		return false, nil
	}
	return true, nil
}

func IsClusterRunning(clusterGroup *table.ClusterGroup, cluster *table.Cluster,
	k8sInstance *table2.Tke) (running bool, err error) {
	// TODO: eks状态检测
	if k8sInstance.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		running, err := IsEKSClusterRunning(clusterGroup, cluster, k8sInstance)
		if err != nil {
			return false, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
		return running, nil
	}

	secretId, secretKey, err := service4.GetSecretIdAndKeyByNetworkEnvType(clusterGroup.NetEnvironmentType)
	if err != nil {
		return false, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	instanceId := k8sInstance.InstanceId
	tkeService := tke.GetTkeService()

	totalCount, clusters, err := tkeService.DescribeClusters(
		secretId, secretKey, "", clusterGroup.Region,
		tkeService.NewDefaultDescribeClustersRequestBuilder().WithClusterIds(instanceId).Build())
	if err != nil {
		return false, errorcode.InternalErrorCode.NewWithErr(err)
	}
	if totalCount != 1 {
		msg := fmt.Sprintf("tke instance count %d for instanceId %s", totalCount, instanceId)
		return false, errorcode.InternalErrorCode.NewWithInfo(msg, err)
	}
	if *clusters[0].ClusterStatus == constants.TKE_STATUS_ABNORMAL_STR {
		msg := fmt.Sprintf("tke cluster %s is in abnormal status", instanceId)
		return false, errorcode.InternalErrorCode.NewWithInfo(msg, err)
	}
	if *clusters[0].ClusterStatus != constants.TKE_STATUS_RUNNING_STR {
		return false, nil
	}
	return true, nil

}

func (h *ApplyTKEHandler) CheckAndAddWorker(ctx *deploy.Context,
	zone string,
	workNum int64,
	cvmConf *table3.CvmSaleConfig,
	forceSpec bool,
	nodeLabel map[string]string,
	diskSize int64,
	diskType string,
	name string,
	taints []*sdkTke.Taint,
	osImg string,
	systemDiskSize int64) (buyWorkerCount int64, err error) {
	params := ctx.Request.Params
	cg := ctx.ClusterGroup

	if workNum <= 0 {
		return 0, nil
	}

	logger.Infof("Cluster %s %d worker node need allocate", cg.SerialId, workNum)

	if ctx.Tke.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		return buyWorkerCount, nil
	}

	if _, exist, err := ctx.GetFlowParamString(params, constants.FLOW_PARAM_WORKER_STATUS, ""); err != nil {
		return buyWorkerCount, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	} else if exist {
		logger.Infof("Attempt add node, if needed, for %s", ctx.Tke.InstanceId)
		//内网只添加一次
		if cg.NetEnvironmentType == constants.NETWORK_ENV_INNER_VPC {
			return buyWorkerCount, nil
		}
	}
	buyWorkerCount = workNum
	if cg.NetEnvironmentType == constants.NETWORK_ENV_INNER_VPC {
		err = h.CreateInnerTkeClusterWithWorker(ctx, zone, workNum, cvmConf, nodeLabel, diskSize, diskType, name)
	} else {
		buyWorkerCount, err = h.CreateTkeWorker(ctx, zone, workNum, cvmConf, forceSpec, nodeLabel, diskSize, diskType, name, taints, osImg, systemDiskSize)
	}
	if err != nil {
		return
	}

	params[constants.FLOW_PARAM_WORKER_STATUS] = "ok"

	return buyWorkerCount, nil
}

func (h *ApplyTKEHandler) CreateNodeEniApp(ctx *deploy.Context) error {
	createEniApp := &createNodeEniApp{ctx: ctx}
	_, err := createEniApp.Apply(nil, nil)
	if err != nil {
		return err
	}
	return nil
}

func IsAllControllersRunning(clusterGroup *table.ClusterGroup, cluster *table.Cluster,
	k8sInstance *table2.Tke, expectRunningNum int64) (allRunning bool, err error) {

	if k8sInstance.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		return true, nil
	}
	secretId, secretKey, err := service4.GetSecretIdAndKeyByNetworkEnvType(clusterGroup.NetEnvironmentType)
	if err != nil {
		return
	}
	instanceId := k8sInstance.InstanceId
	tkeService := tke.GetTkeService()
	allInstanceMap := make(map[string]*sdkTke.Instance)
	if totalCount, instanceSet, err := tkeService.DescribeClusterAllInstances(secretId, secretKey, "",
		clusterGroup.Region, instanceId); err != nil {
		return false, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	} else if totalCount < uint64(expectRunningNum) {
		return false, errorcode.NewStackError(errorcode.InternalErrorCode,
			fmt.Sprintf("tke cluster %s should have more than %d nodes, but got %d", instanceId, expectRunningNum, totalCount), nil)
	} else {
		for _, instance := range instanceSet {
			allInstanceMap[*instance.InstanceId] = instance
		}
	}

	k8sService := k8s.GetK8sService()
	kubConfig := []byte(cluster.KubeConfig)
	client, err := k8sService.NewClient(kubConfig)
	if err != nil {
		return
	}

	controllerList, err := k8sService.ListNode(client, metav1.ListOptions{
		LabelSelector: fmt.Sprintf("%s==%s", constants.TKE_CVM_LABEL_KEY, constants.TKE_CONTROL_NODE_LABEL_VAL),
	})
	if err != nil {
		return
	}
	runningController := int64(0)
	for _, node := range controllerList.Items {
		labelMap := node.GetObjectMeta().GetLabels()
		instanceId, ok := labelMap["cloud.tencent.com/node-instance-id"]
		if !ok {
			continue
		}
		nodeInstance := allInstanceMap[instanceId]
		if *nodeInstance.InstanceState != constants.TKE_INSTANCE_STATE_RUNNING {
			continue
		}
		runningController += 1
	}

	return runningController >= expectRunningNum, nil
}

func (h *ApplyTKEHandler) TryMakeAllNodesRunning(ctx *deploy.Context) (allRunning bool, err error) {
	clusterGroup := ctx.ClusterGroup
	k8sInstance := ctx.Tke
	params := ctx.Request.Params

	if k8sInstance.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		return true, nil
	}

	optId := clusterGroup.SerialId
	if clusterGroup.AgentSerialId != "" {
		optId = clusterGroup.AgentSerialId
	}

	// lock for uniform cluster
	locker := dlocker.NewDlocker("uniformClusterCreateWorkerApp", fmt.Sprintf("optId-%s", optId), 60)
	err = locker.Lock()
	if err != nil {
		return false, err
	}
	defer locker.UnLock()

	secretId, secretKey, err := service4.GetSecretIdAndKeyByNetworkEnvType(clusterGroup.NetEnvironmentType)
	if err != nil {
		return false, err
	}

	instanceId := k8sInstance.InstanceId
	tkeService := tke.GetTkeService()
	totalCount, instanceSet, err := tkeService.DescribeClusterAllInstances(secretId, secretKey, "",
		clusterGroup.Region,
		instanceId)
	if err != nil {
		logger.Errorf("DescribeClusterAllInstances tke cluster %s, err: %+v", instanceId, err)
		return false, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	if totalCount != uint64(len(instanceSet)) {
		msg := fmt.Sprintf("tke cluster %s, total count %d, instance count %d", instanceId, uint64(len(instanceSet)), totalCount)
		logger.Errorf(msg)
		return false, errorcode.NewStackError(errorcode.InternalErrorCode, msg, nil)
	}

	hasInitInstance := false
	failedInstanceMap := make(map[string]struct{})
	instanceIdSet := make([]*string, 0)
	for _, inst := range instanceSet {
		if strings.HasPrefix(*inst.InstanceId, "ins-") { //except np-
			instanceIdSet = append(instanceIdSet, inst.InstanceId)
		}
		if *inst.InstanceState == constants.TKE_INSTANCE_STATE_FAILED {
			failedInstanceMap[*inst.InstanceId] = struct{}{}
		} else if *inst.InstanceState == constants.TKE_INSTANCE_STATE_SHUTDOWN {
			logger.Debugf("tke instance %s is shutdown, skip it", *inst.InstanceId)
			continue
		} else if *inst.InstanceState != constants.TKE_INSTANCE_STATE_RUNNING {
			logger.Errorf("tke cluster %s, instanceId %s, state: %s is not running", instanceId, *inst.InstanceId, *inst.InstanceState)
			hasInitInstance = true
		}
	}
	logger.Infof("tke failed maps: %+v", failedInstanceMap)

	//use cvm interface, double check
	cvmService := cvm3.GetCvmService()
	cvmInstanceSet, err := cvmService.DescribeInstancesForIds(secretId, secretKey, clusterGroup.Region, instanceIdSet)
	if err != nil {
		logger.Errorf("DescribeInstancesForIds tke cluster %s, err: %+v", instanceId, err)
		return false, errorcode.InternalErrorCode.NewWithErr(err)
	}
	failedInstanceTypeMap := make(map[string]struct{})
	for _, instance := range cvmInstanceSet {
		if *instance.InstanceState == "LAUNCH_FAILED" {
			failedInstanceMap[*instance.InstanceId] = struct{}{}
			failedInstanceTypeMap[*instance.InstanceType] = struct{}{}
		}
	}
	logger.Infof("cvm failed maps: %+v", failedInstanceMap)
	logger.Infof("cvm failed type maps: %+v", failedInstanceTypeMap)

	existFailedInstanceTypeMap := make(map[string]struct{})
	if failedInstanceTypes, ok := params[constants.FLOW_PARAM_WORKER_FAILED_TYPE]; ok && len(failedInstanceTypes) > 0 {
		err = json.Unmarshal([]byte(failedInstanceTypes), &existFailedInstanceTypeMap)
		if err != nil {
			logger.Errorf("Unmarshal to existFailedInstanceTypeMap, err: %+v", instanceId, err)
			return false, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
	}

	for instanceType, v := range existFailedInstanceTypeMap {
		failedInstanceTypeMap[instanceType] = v
	}

	v, _ := json.Marshal(failedInstanceTypeMap)
	params[constants.FLOW_PARAM_WORKER_FAILED_TYPE] = string(v)

	if len(failedInstanceMap) > 0 && clusterGroup.NetEnvironmentType == constants.NETWORK_ENV_CLOUD_VPC {
		// delete failed instance
		failedInstanceIds := toSliceStringPtr(failedInstanceMap)

		rsp, err := tkeService.DeleteClusterInstancesForIdsByNetEnvironmentType(clusterGroup.NetEnvironmentType, clusterGroup.Region, instanceId, failedInstanceIds)
		if err != nil {
			msg := fmt.Sprintf("tke cluster %s instance %+v with failed state", instanceId, failedInstanceIds)
			return false, errorcode.NewStackError(errorcode.InternalErrorCode, msg, err)
		}
		if len(rsp.Response.SuccInstanceIds) > 0 {
			err = cbs.GetCbsService(clusterGroup.NetEnvironmentType, clusterGroup.Region).TerminateCbsFromCVM(rsp.Response.SuccInstanceIds)
			if err != nil {
				logger.Errorf("terminate cbs err: %s", err.Error())
			}
			err = cvm3.GetCvmService().TerminateInstancesWithScsAccount(clusterGroup.Region, rsp.Response.SuccInstanceIds)
			if err != nil {
				logger.Errorf("terminate instance err: %s", err.Error())
			}
		}
		msg := fmt.Sprintf("tke cluster %s instance, try to remove failed instances %+v", instanceId, rsp.Response.SuccInstanceIds)
		return false, errorcode.NewStackError(errorcode.InternalErrorCode, msg, nil)
	}

	if hasInitInstance {
		return false, nil
	}
	return true, nil
}

func (h *ApplyTKEHandler) CheckAndOpenPolicyList(clusterGroup *table.ClusterGroup,
	k8sInstance *table2.Tke) (err error) {
	logger.Infof("CheckAndOpenPolicyList for cluster %s, type %v", k8sInstance.InstanceId, k8sInstance.ClusterType)

	var openPolicySet []*tke2.OpenPolicySwitch
	var replaceOpenPolicy bool
	value, err := config.GetRainbowConfiguration("ConfigureCenter.Flow.Common", "open_policy_switch.json")
	if err != nil {
		return
	}
	err = json.Unmarshal([]byte(value), &openPolicySet)
	if err != nil {
		return
	}
	value, err = config.GetRainbowConfiguration("ConfigureCenter.Flow.Common", "replaceOpenPolicy")
	if err != nil {
		return
	}
	replaceOpenPolicy, err = strconv.ParseBool(value)
	if err != nil {
		return
	}

	secretId, secretKey, err := service4.GetSecretIdAndKeyByNetworkEnvType(clusterGroup.NetEnvironmentType)
	if err != nil {
		return
	}

	instanceId := k8sInstance.InstanceId

	clusterType := "tke"
	if k8sInstance.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		clusterType = "TKEServerless"
	}

	describePolicyReq := tke.NewDescribeOpenPolicyListRequestBuilder().
		WithClusterType(clusterType).
		WithClusterId(instanceId).
		WithCategory("optional").
		Build()

	tkeService := tke.GetTkeService()
	policySet, err := tkeService.DescribeOpenPolicyList(secretId, secretKey, "", clusterGroup.Region, describePolicyReq)
	if err != nil {
		return err
	}
	policyMap := make(map[string]*tke2.OpenPolicyInfo)
	for _, s := range policySet {
		policyMap[*s.Kind] = s
	}

	needOpenPolicySet := make([]*tke2.OpenPolicySwitch, 0)

	for _, ps := range openPolicySet {
		p := policyMap[*ps.Kind]
		if p != nil && *p.EnabledStatus == "open" && !replaceOpenPolicy {
			continue
		}
		needOpenPolicySet = append(needOpenPolicySet, ps)
	}

	if len(needOpenPolicySet) == 0 {
		return nil
	}

	openPolicyReq := tke.NewModifyOpenPolicyListRequestBuilder().
		WithClusterType(clusterType).
		WithClusterId(instanceId).
		WithCategory("optional").
		WithOpenPolicySwitches(needOpenPolicySet).
		Build()
	err = tkeService.ModifyOpenPolicyList(secretId, secretKey, "", clusterGroup.Region, openPolicyReq)
	return err
}

func (h *ApplyTKEHandler) CheckAndEnableClusterAudit(clusterGroup *table.ClusterGroup,
	k8sInstance *table2.Tke) (err error) {

	logger.Infof("CheckAndEnableClusterAudit for cluster %s, type %v", k8sInstance.InstanceId, k8sInstance.ClusterType)

	secretId, secretKey, err := service4.GetSecretIdAndKeyByNetworkEnvType(clusterGroup.NetEnvironmentType)
	if err != nil {
		return
	}

	instanceId := k8sInstance.InstanceId
	var switchInfo = &v20180525.SwitchInfo{}

	clusterType := "tke"
	if k8sInstance.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		clusterType = "eks"
	}

	logSwitchesReq := tke.NewDescribeLogSwitchesRequestBuilder().
		WithClusterType(clusterType).
		WithClusterId(instanceId).
		Build()

	tkeService := tke.GetTkeService()
	switchSet, err := tkeService.DescribeLogSwitches(secretId, secretKey, "", clusterGroup.Region, logSwitchesReq)
	if err != nil {
		return err
	}
	for _, s := range switchSet {
		switchInfo = s.Audit
	}

	if *switchInfo.Enable {
		return nil
	}

	var logSetId string
	nameSuffix := "TKE-cls"
	if k8sInstance.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		nameSuffix = "eks-cls"
	}

	uin, err := service2.GetScsUserUin()
	if err != nil {
		return err
	}
	_, logSet, err := cls.DescribeAllLogsetsByName(uin, uin, secretId, secretKey, "", clusterGroup.Region, nameSuffix)
	if err != nil {
		return
	}
	if len(logSet) > 0 {
		ls := logSet[rand.Intn(len(logSet))]
		logSetId = *ls.LogsetId
	}

	if clusterGroup.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM {
		logSetId = ""
	}

	if k8sInstance.ClusterType == constants.K8S_CLUSTER_TYPE_TKE {
		err = tkeService.EnableClusterAuditForTke(secretId, secretKey, "", clusterGroup.Region, k8sInstance.InstanceId, logSetId)
	} else {
		err = tkeService.EnableClusterAuditForEks(secretId, secretKey, "", clusterGroup.Region, k8sInstance.InstanceId, logSetId)
	}

	return err
}

func (h *ApplyTKEHandler) CheckAndEnableEventPersistence(clusterGroup *table.ClusterGroup,
	k8sInstance *table2.Tke) (err error) {
	// Don't skip event persistence for EKS
	if clusterGroup.NetEnvironmentType == constants.NETWORK_ENV_INNER_VPC {
		logger.Info("Event Persistence is not supported for VPC")
		return nil
	}

	logger.Infof("CheckAndEnableEventPersistence for cluster %s, type %v", k8sInstance.InstanceId, k8sInstance.ClusterType)

	secretId, secretKey, err := service4.GetSecretIdAndKeyByNetworkEnvType(clusterGroup.NetEnvironmentType)
	if err != nil {
		return err
	}

	instanceId := k8sInstance.InstanceId

	tkeService := tke.GetTkeService()
	var switchInfo = &v20180525.SwitchInfo{}

	clusterType := "tke"
	if k8sInstance.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		clusterType = "eks"
	}

	logSwitchesReq := tke.NewDescribeLogSwitchesRequestBuilder().
		WithClusterType(clusterType).
		WithClusterId(instanceId).
		Build()

	switchSet, err := tkeService.DescribeLogSwitches(secretId, secretKey, "", clusterGroup.Region, logSwitchesReq)
	if err != nil {
		return err
	}
	for _, s := range switchSet {
		switchInfo = s.Event
	}

	if *switchInfo.Enable {
		return nil
	}

	eventTopic, err := service3.GetTableService().GetK8sEventTopicByRegion(clusterGroup.Region)
	if err != nil {
		return err
	}

	if len(eventTopic.LogSetId) == 0 || len(eventTopic.TopicId) == 0 {
		return errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("Not found LogSetId and TopicId for %s", clusterGroup.Region), err)
	}

	// 2.开启目标集群事件持久化功能
	if k8sInstance.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		req := tke.NewEnableEKSEventPersistenceRequestBuilder().
			WithClusterId(instanceId).
			WithLogSetId(eventTopic.LogSetId).
			WithTopicId(eventTopic.TopicId).
			Build()
		err = tkeService.EnableEKSEventPersistence(secretId, secretKey, "", clusterGroup.Region, req)
	} else {
		req := tke.NewEnableEventPersistenceRequestBuilder().
			WithClusterId(instanceId).
			WithLogSetId(eventTopic.LogSetId).
			WithTopicId(eventTopic.TopicId).
			Build()
		err = tkeService.EnableEventPersistence(secretId, secretKey, "", clusterGroup.Region, req)
	}

	return err
}

func updateClusterInfo(clusterGroup *table.ClusterGroup, cluster *table.Cluster) {
	// 所有节点都running之后，再更新ClusterGroup Cluster 的cuNUm
	var cuMem int8 = 4 // 每个Cu使用多少G内存
	if cuMem != clusterGroup.CuMem && clusterGroup.CuMem != 0 {
		cuMem = clusterGroup.CuMem
	}
	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.ExecuteSqlWithArgs("update ClusterGroup set CuNum=?, CuMem=? where Id=?", clusterGroup.CuNum, cuMem,
			clusterGroup.Id)
		tx.ExecuteSqlWithArgs("update Cluster set CuNum=? where Id=?", clusterGroup.CuNum, cluster.Id)
		return nil
	}).Close()
}

func updateClusterResourceType(clusterGroup *table.ClusterGroup) {
	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.ExecuteSqlWithArgs("update ClusterGroup set ResourceType=? where Id=?", clusterGroup.ResourceType, clusterGroup.Id)
		return nil
	}).Close()
}

func CheckCvmQuotaMaySwitchType(
	zone string,
	clusterGroup *table.ClusterGroup,
	cluster *table.Cluster,
	prefer *table3.CvmSaleConfig,
	forceSpec bool,
	count int64,
	isWorker bool,
	exceptInstanceTypes map[string]struct{},
	cvmGen int,
) (workerSpec string, workerCount int64, noInstanceType bool, err error) {
	minCvmGen := constants.CvmGenerationKvm10

	// the cvm kernel generation should meet the feature's requirement
	// controller does not have generation requirement
	if isWorker {
		supportedFeatures, err := cluster.GetSupportedFeatures()
		if err != nil {
			return "", 0, false, errorcode.InternalErrorCode.NewWithErr(err)
		}
		if service4.InSliceString(constants.FeatureFineGrainedResource, supportedFeatures) {
			minCvmGen = constants.CvmGenerationKvm30
		}
	}
	if cvmGen > 0 {
		minCvmGen = cvmGen
	}

	var cvmSaleConfList []*table3.CvmSaleConfig

	if forceSpec {
		cvmSaleConfList = []*table3.CvmSaleConfig{}
	} else {
		cvmSaleConfList, err = service3.GetTableService().ListActiveCvmSaleConfWithCpuMemory(prefer.Cpu, prefer.Memory)
		if err != nil {
			return
		}
	}
	cvmSaleConfMap := make(map[string]*table3.CvmSaleConfig)
	instanceTypes := make([]string, 0)
	for _, conf := range cvmSaleConfList {
		if conf.Generation >= minCvmGen {
			instanceTypes = append(instanceTypes, conf.InstanceType)
			cvmSaleConfMap[conf.InstanceType] = conf
		}
	}
	if prefer.Generation >= minCvmGen || forceSpec {
		instanceTypes = append(instanceTypes, prefer.InstanceType)
		cvmSaleConfMap[prefer.InstanceType] = prefer
	}
	if len(instanceTypes) == 0 {
		return "", 0, false, errorcode.ResourceUnavailableCode_Cvm.ReplaceDesc(
			fmt.Sprintf("No cvm resource generation >= %d alvailable", minCvmGen))
	}
	instanceTypes = service4.UniqueSliceString(instanceTypes)
	newInstanceTypes := make([]string, 0)
	for _, iType := range instanceTypes {
		if _, ok := exceptInstanceTypes[iType]; !ok {
			newInstanceTypes = append(newInstanceTypes, iType)
		}
	}

	if len(newInstanceTypes) == 0 {
		return "", 0, true, nil
	}

	appid, err := service2.GetScsUserAppId()
	if err != nil {
		return
	}
	uin, err := service2.GetScsUserUin()
	if err != nil {
		return
	}

	cvmService := cvm3.GetCvmService()
	cvmReq := cvmService.NewDefaultDescribeZoneInstanceConfigInfosRequestBuilder().
		WithFilter("instance-type", newInstanceTypes).
		WithFilter("zone", []string{zone}).
		WithRequestRole(cvm2.CvmInternApiRequestRole).
		WithInternalInfo(clusterGroup.Region, appid, uin, uin).
		BuildInternal()
	instanceTypeQuotaSet, err := cvmService.DescribeZoneInstanceConfigInfosInternal(clusterGroup.Region, cvmReq)
	if err != nil {
		return
	}
	// prefer higher cvm kernel generation and then more quota
	sort.Slice(instanceTypeQuotaSet, func(i, j int) bool {
		a := instanceTypeQuotaSet[i]
		b := instanceTypeQuotaSet[j]
		a1 := cvmSaleConfMap[*a.InstanceType]
		b1 := cvmSaleConfMap[*b.InstanceType]
		if a1.Price < b1.Price {
			return true
		} else if a1.Price > b1.Price {
			return false
		}

		if a1.Generation > b1.Generation {
			return true
		} else if a1.Generation < b1.Generation {
			return false
		}

		if *a.InstanceQuota > *b.InstanceQuota {
			return true
		}
		return false
	})
	b, _ := json.Marshal(instanceTypeQuotaSet)
	logger.Debug(string(b))

	// select cvm instance type
	needCount := count
	workerSpecNumMap := make(map[string]int64)
	for _, quota := range instanceTypeQuotaSet {
		if *quota.InstanceQuota <= 0 {
			continue
		}
		if isWorker {
			if workerSpec == "" {
				workerSpec = *quota.InstanceType
			}
			if *quota.InstanceQuota >= needCount {
				workerSpecNumMap[*quota.InstanceType] = needCount
				break
			} else {
				workerSpecNumMap[*quota.InstanceType] = *quota.InstanceQuota
				needCount -= *quota.InstanceQuota
			}
		} else {
			if *quota.InstanceQuota >= count {
				return *quota.InstanceType, count, false, nil
			}
		}
	}

	if !isWorker {
		return workerSpec, 0, false, errorcode.ResourceInsufficientCode_Cvm.ReplaceDesc(
			fmt.Sprintf("Insufficient resource for controller, need count %d", count))
	}

	haveCount := int64(0)
	for _, specNum := range workerSpecNumMap {
		haveCount += specNum
	}
	if haveCount == count {
		return workerSpec, workerSpecNumMap[workerSpec], false, nil
	}
	logger.Infof("Insufficient resource for worker, need count %d, have count %d", count, haveCount)
	return "", 0, true, nil
}

func (h *ApplyTKEHandler) CalcClusterNodeCount(ctx *deploy.Context) (totalNum, controllerNum, workerNodeNum uint64, workerConf *table3.CvmSaleConfig, err error) {
	tkeService := tke.GetTkeService()

	clusterGroup := ctx.ClusterGroup
	k8sInstance := ctx.Tke

	workerNodeNum, _, workerConf, err = h.ComputeWorkerNodeAndCuNum(clusterGroup.CuNum, ctx)
	if err != nil {
		return
	}

	totalNum, _, err = tkeService.DescribeClusterInstancesWithScsAccountByNetEnvironmentType(clusterGroup.NetEnvironmentType, clusterGroup.Region,
		tkeService.NewDefaultDescribeClusterInstancesRequestBuilder().
			WithClusterId(k8sInstance.InstanceId).
			Build())
	if err != nil {
		return
	}

	k8sService := k8s.GetK8sService()
	controllerList, err := k8sService.ListNode(ctx.ClientSet(), metav1.ListOptions{
		LabelSelector: fmt.Sprintf("%s==%s", constants.TKE_CVM_LABEL_KEY, constants.TKE_CONTROL_NODE_LABEL_VAL),
	})
	if err != nil {
		return
	}

	controllerNum = uint64(len(controllerList.Items))
	return
}

func (h *ApplyTKEHandler) labelNodeCount(ctx *deploy.Context, label map[string]string) (nodeCount int, err error) {
	k8sService := k8s.GetK8sService()
	controllerList, err := k8sService.ListNode(ctx.ClientSet(), metav1.ListOptions{
		LabelSelector: labels.FormatLabels(label),
	})
	if err != nil {
		return
	}

	nodeCount = len(controllerList.Items)

	b, _ := json.Marshal(label)
	logger.Infof("Label: %s, node count: %d", string(b), nodeCount)
	return
}

func GetPrivateWorkerLabel(zone string, appId int64) map[string]string {
	label := map[string]string{
		constants.TKE_CVM_NODE_ZONE:          zone,
		constants.TKE_CVM_NODE_APPID:         strconv.FormatInt(appId, 10),
		constants.TKE_CVM_NODE_RESOURCE_TYPE: constants.TKE_CVM_NODE_RESOURCE_TYPE_PRIVATE,
	}

	return label
}

func GetShareWorkerLabel(zone string) map[string]string {
	label := map[string]string{
		constants.TKE_CVM_NODE_ZONE:          zone,
		constants.TKE_CVM_NODE_RESOURCE_TYPE: constants.TKE_CVM_NODE_RESOURCE_TYPE_SHARE,
	}

	return label
}

func (h *ApplyTKEHandler) GetWorkerLabel(cg *table.ClusterGroup, zone string) (result map[string]string) {
	result = h.GetBaseWorkerLabel(cg.Region)

	var addLabel map[string]string

	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM {
		addLabel = GetShareWorkerLabel(zone)
	} else if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId == "" {
		addLabel = make(map[string]string)
	} else if cg.ResourceType == constants.RESOURCE_TYPE_PRIVATE {
		addLabel = GetPrivateWorkerLabel(cg.Zone, int64(cg.AppId))
	} else if cg.ResourceType == constants.RESOURCE_TYPE_SHARE {
		addLabel = GetShareWorkerLabel(zone)
	}

	for k, v := range addLabel {
		result[k] = v
	}
	return
}

func (h *ApplyTKEHandler) GetBaseWorkerLabel(region string) map[string]string {

	fcc := flowCC.New(region)

	label := map[string]string{constants.TKE_CVM_LABEL_KEY: constants.TKE_WORKER_NODE_LABEL_VAL}
	// 节点放大标签
	label[constants.EXPANSION_LABEL_KEY] = constants.EXPANSION_LABEL_KEY
	cpuExpansion, err := fcc.NodeExpansionControllerCC().Cpu()
	if err != nil {
		logger.Warningf("flowCC.NodeExpansionControllerCC().Cpu() error %+v", err)
	} else {
		label[constants.EXPANSION_CPU_LABEL_KEY] = cpuExpansion
	}
	memoryExpansion, err := fcc.NodeExpansionControllerCC().Memory()
	if err != nil {
		logger.Warningf("flowCC.NodeExpansionControllerCC().Memory() error %+v", err)
	} else {
		label[constants.EXPANSION_MEMORY_LABEL_KEY] = memoryExpansion
	}
	podsExpansion, err := fcc.NodeExpansionControllerCC().Pods()
	if err != nil {
		logger.Warningf("flowCC.NodeExpansionControllerCC().Pods() error %+v", err)
	} else {
		label[constants.EXPANSION_POD_LABEL_KEY] = podsExpansion
	}
	return label
}

func (h *ApplyTKEHandler) GetControllerLabel(ctx *deploy.Context) map[string]string {
	label := map[string]string{constants.TKE_CVM_LABEL_KEY: constants.TKE_CONTROL_NODE_LABEL_VAL}
	return label
}

func (h *ApplyTKEHandler) CreateTkeWorker(ctx *deploy.Context,
	zone string,
	count int64,
	cvmConf *table3.CvmSaleConfig,
	forceSpec bool,
	nodeLabel map[string]string,
	diskSize int64,
	diskType string,
	name string,
	taints []*sdkTke.Taint,
	osImg string,
	systemDiskSize int64) (buyWorkerCount int64, err error) {
	tkeService := tke.GetTkeService()

	params := ctx.Request.Params
	cg := ctx.ClusterGroup
	c := ctx.Cluster

	cc, err := ctx.FlowCC.TkeCC().ClusterConfig(cg)
	if err != nil {
		return
	}

	prepaidPeriod, err := ctx.FlowCC.PrepaidPeriod()
	if err != nil {
		return
	}

	zoneSubnets, err := c.GetSupportedZoneSubnets()
	if err != nil {
		return
	}
	subnet := zoneSubnets[zone]
	if subnet == "" {
		return buyWorkerCount, fmt.Errorf("subnet is empty, zone %s", zone)
	}

	existFailedInstanceTypeMap := make(map[string]struct{})
	failedInstanceTypes, existInstanceType := params[constants.FLOW_PARAM_WORKER_FAILED_TYPE]
	if existInstanceType && len(failedInstanceTypes) > 0 {
		err = json.Unmarshal([]byte(failedInstanceTypes), &existFailedInstanceTypeMap)
		if err != nil {
			return
		}
	}

	buyWorkerCount = count
	if workerSpec, workerCount, noInstanceType, err := CheckCvmQuotaMaySwitchType(zone, cg, c, cvmConf, forceSpec, count,
		true, existFailedInstanceTypeMap, 0); err != nil {
	} else if noInstanceType {
		logger.Errorf("instance type %s is out of stock", cvmConf.InstanceType)
		// 所有节点类型都尝试过一次，重新再尝试添加所有节点
		v, _ := json.Marshal(make(map[string]struct{}))
		params[constants.FLOW_PARAM_WORKER_FAILED_TYPE] = string(v)
		return buyWorkerCount, fmt.Errorf("instance type %s is out of stock, no instance type select, except types: %+v", cvmConf.InstanceType, existFailedInstanceTypeMap)
	} else if workerSpec == "" {
		logger.Errorf("instance type %s is not in CvmSaleConf", cvmConf.InstanceType)
		return buyWorkerCount, fmt.Errorf("instance type %s is not in CvmSaleConf, no instance type select, except types: %+v", cvmConf.InstanceType, existFailedInstanceTypeMap)
	} else {
		cc.WorkerSpec = workerSpec
		buyWorkerCount = workerCount
	}

	cc.WorkerSpec = cvm3.GetCvmService().GetWorkerSpec(c, cc)

	workerRequest := tkeService.NewDefaultWorkerNodeRunInstancesRequestBuilder().
		WithInstanceType(cc.WorkerSpec).
		WithInstanceChargePrepaid(prepaidPeriod, constants.TKE_CVM_RENEW_FLAG).
		WithPlacement(zone, constants.TKE_PROJECT_ID).
		WithVirtualPrivateCloud(c.VpcId, subnet).
		WithInstanceCount(buyWorkerCount).
		WithSecurityGroup(cc.SecurityGroup).
		WithSystemDisk(diskType, systemDiskSize).
		WithDataDisks(diskType, diskSize).
		WithInstanceName(name).
		Build()

	if osImg != "" {
		logger.Infof("CreateTkeWorker use os image %s", osImg)
		workerRequest.ImageId = &osImg
	}

	isV1 := ctx.Tke.ArchGeneration >= constants.TKE_ARCH_GENERATION_V6
	mountTarget := constants.TKE_DATA_DISK_MOUNT_TARGET_FOR_CONTAINERD
	if !isV1 {
		mountTarget = constants.TKE_DATA_DISK_MOUNT_TARGET
	}
	request := tkeService.NewDefaultCreateClusterInstancesRequestBuilder().
		WithClusterId(ctx.Tke.InstanceId).
		WithRunInstancePara(workerRequest).
		WithInstanceAdvancedSettings(constants.TKE_DATA_DISK_AUTO_FORMAT_AND_MOUNT,
			constants.TKE_DATA_DISK_FILE_SYSTEM, mountTarget, diskType,
			diskSize, nodeLabel, cc.UserScript).
		WithTaints(taints).
		Build()

	kubelet := make([]*string, 0)
	var rootDir string
	if isV1 {
		rootDir = fmt.Sprintf("%s=%s", constants.TKE_KUBELET_DIR, constants.TKE_DATA_DISK_MOUNT_TARGET_FOR_CONTAINERD)
	} else {
		rootDir = fmt.Sprintf("%s=%s", constants.TKE_KUBELET_DIR, constants.TKE_DATA_DISK_MOUNT_TARGET)
	}
	kubelet = append(kubelet, &rootDir)
	eviction := fmt.Sprintf("%s=%s", constants.TKE_EVICTION_HARD, constants.TKE_EVICTION_HARD_VALUE)
	kubelet = append(kubelet, &eviction)
	if auth.IsInWhiteList(int64(cg.AppId), constants.WHITE_LIST_CPU_MANAGER_POLICY) {
		policy := "cpu-manager-policy=static"
		kubelet = append(kubelet, &policy)
	}
	request.InstanceAdvancedSettings.ExtraArgs = &sdkTke.InstanceExtraArgs{
		Kubelet: kubelet,
	}

	err = tkeService.CreateClusterInstancesWithScsAccountByNetworkEnvType(cg.NetEnvironmentType, cg.Region, request)
	return buyWorkerCount, err
}

func (h *ApplyTKEHandler) ComputeWorkerNodeAndCuNum(clusterCuNum int16, ctx *deploy.Context) (
	count, cuNum uint64, cvmConf *table3.CvmSaleConfig, err error) {

	cg := ctx.ClusterGroup
	cc, err := ctx.FlowCC.TkeCC().ClusterConfig(cg)
	if err != nil {
		return
	}
	k8sInstance := ctx.Tke
	cluster := ctx.Cluster

	workerSpec := cvm3.GetCvmService().GetWorkerSpec(cluster, cc)

	cvmConfList, err := service3.GetTableService().ListCvmSaleConfByInstanceType(workerSpec, 0, 1)
	if err != nil {
		return
	}
	if len(cvmConfList) == 0 {
		err = fmt.Errorf("cvm spec %s not found in table CvmSaleConf", workerSpec)
		return
	}

	cvmCpu := cvmConfList[0].Cpu

	reservedCuPerNode := cc.ReservedCuPerNode
	if k8sInstance.ArchGeneration >= constants.TKE_ARCH_GENERATION_V2 {
		reservedCuPerNode = cc.ReservedCuPerNodeV3
	}

	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM || cg.AgentSerialId != "" {
		// 统一资源池不预留cu
	} else if ctx.Cluster.MemRatio == constants.CVM_MEMRATIO_2 {
		clusterCuNum = clusterCuNum + 7 //7个CU的管控服务占用
	} else if k8sInstance.ArchGeneration >= constants.TKE_ARCH_GENERATION_V5 {
		clusterCuNum = clusterCuNum + 5 //5个CU的管控服务占用
	} else if k8sInstance.ArchGeneration >= constants.TKE_ARCH_GENERATION_V2 {
		clusterCuNum = clusterCuNum + 2 //2个CU的管控服务占用
	}

	// 1:2 集群， 机型 每个节点只能提供 12.46 Gi，提供6CU
	if ctx.Cluster.MemRatio == constants.CVM_MEMRATIO_2 {
		reservedCuPerNode = cc.ReservedCuPerNode
	}

	if ok, w := auth.WhiteListValue(int64(cg.AppId), constants.WHITE_LIST_RESERVED_CU_PER_NODE); ok {
		cu, _ := w.GetIntVal()
		if cu > 0 {
			reservedCuPerNode = int64(cu)
		}
	}

	availableCvmCu := cvmCpu - reservedCuPerNode
	if availableCvmCu <= 0 {
		err = fmt.Errorf("cvmCpu %d should greater than reserved Cu %d", cvmCpu, cc.ReservedCuPerNode)
		return
	}

	cvmNum := (int64(clusterCuNum) + availableCvmCu - 1) / availableCvmCu
	if cvmNum == 0 {
		cvmNum = 1
	}

	cuNum = uint64(cvmNum * availableCvmCu)
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM || cg.AgentSerialId != "" {
		// 统一资源池不预留cu
	} else if ctx.Cluster.MemRatio == constants.CVM_MEMRATIO_2 {
		cuNum = cuNum - 7
	} else if k8sInstance.ArchGeneration >= constants.TKE_ARCH_GENERATION_V5 {
		cuNum = cuNum - 5
	} else if k8sInstance.ArchGeneration >= constants.TKE_ARCH_GENERATION_V2 {
		cuNum = cuNum - 2
	}

	return uint64(cvmNum), cuNum, cvmConfList[0], nil
}

func (h *ApplyTKEHandler) AddNodeLabel(clusterGroup *table.ClusterGroup, cluster *table.Cluster,
	k8sInstance *table2.Tke, targetWorkerCount int, tkeCC *flowCC.TkeClusterConfig, workerZK bool) (err error) {

	if k8sInstance.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		return
	}
	if k8sInstance.ArchGeneration < constants.TKE_ARCH_GENERATION_V2 {
		return
	}

	region := clusterGroup.Region
	tkeService := tke.GetTkeService()

	k8sService := k8s.GetK8sService()
	kubConfig := []byte(cluster.KubeConfig)
	client, err := k8sService.NewClient(kubConfig)
	if err != nil {
		return errorcode.InternalErrorCode.NewWithErr(err)
	}

	secretId, secretKey, err := service4.GetSecretIdAndKeyByNetworkEnvType(clusterGroup.NetEnvironmentType)
	if err != nil {
		return
	}

	_, allInstanceSet, err := tkeService.DescribeClusterAllInstances(secretId, secretKey, "", region,
		k8sInstance.InstanceId)
	if err != nil {
		return
	}
	allInstanceMap := make(map[string]*sdkTke.Instance)
	for _, instance := range allInstanceSet {
		allInstanceMap[*instance.InstanceId] = instance
	}

	instanceIdSet := make([]*string, 0)
	workerInstanceMap := make(map[string]*sdkTke.Instance)
	workerNodeMap := make(map[string]v13.Node)
	controllerInstanceMap := make(map[string]*sdkTke.Instance)
	controllerNodeMap := make(map[string]v13.Node)

	controllerList, err := k8sService.ListNode(client, metav1.ListOptions{
		LabelSelector: fmt.Sprintf("%s==%s", constants.TKE_CVM_LABEL_KEY, constants.TKE_CONTROL_NODE_LABEL_VAL),
	})
	if err != nil {
		return
	}
	for _, node := range controllerList.Items {
		labelMap := node.GetObjectMeta().GetLabels()
		instanceId, ok := labelMap["cloud.tencent.com/node-instance-id"]
		if !ok {
			continue
		}
		nodeInstance := allInstanceMap[instanceId]
		if *nodeInstance.InstanceState != constants.TKE_INSTANCE_STATE_RUNNING {
			continue
		}
		controllerInstanceMap[*nodeInstance.InstanceId] = nodeInstance
		controllerNodeMap[*nodeInstance.InstanceId] = node
	}

	workerList, err := k8sService.ListNode(client, metav1.ListOptions{
		LabelSelector: fmt.Sprintf("%s==%s", constants.TKE_CVM_LABEL_KEY, constants.TKE_WORKER_NODE_LABEL_VAL),
	})
	if err != nil {
		return
	}
	for _, node := range workerList.Items {
		labelMap := node.GetObjectMeta().GetLabels()
		instanceId, ok := labelMap["cloud.tencent.com/node-instance-id"]
		if !ok {
			continue
		}
		nodeInstance := allInstanceMap[instanceId]
		if *nodeInstance.InstanceState != constants.TKE_INSTANCE_STATE_RUNNING {
			continue
		}
		instanceIdSet = append(instanceIdSet, nodeInstance.InstanceId)
		workerInstanceMap[*nodeInstance.InstanceId] = nodeInstance
		workerNodeMap[*nodeInstance.InstanceId] = node
	}

	instanceBytes, _ := json.Marshal(instanceIdSet)
	logger.Infof("workerInstanceSet %s", string(instanceBytes))

	if targetWorkerCount > len(instanceIdSet) {
		msg := fmt.Sprintf("[%s] getTkeNodeToLabel targetWorkerCount %d, tkeworker node cnt %d not enough",
			clusterGroup.SerialId, targetWorkerCount, len(instanceIdSet))
		return errorcode.InternalErrorCode.NewWithInfo(msg, err)
	}

	// 目前内网集群购买的CVM都是直接根据CPU和Memory购买的，不支持按照实例类型购买，所以把这里屏蔽掉，只对公有云生效
	if clusterGroup.NetEnvironmentType == constants.NETWORK_ENV_CLOUD_VPC {
		workerSpec := cvm3.GetCvmService().GetWorkerSpec(cluster, tkeCC)
		cvmConfList, err := service3.GetTableService().ListCvmSaleConfByInstanceType(workerSpec, 0, 1)
		if err != nil || len(cvmConfList) == 0 {
			return errorcode.InternalErrorCode.NewWithInfo("scale down ListCvmSaleConfByInstanceType Error, length = 0", err)
		}
		cvmSaleConfList, err := service3.GetTableService().ListActiveCvmSaleConfWithCpuMemory(cvmConfList[0].Cpu, cvmConfList[0].Memory)
		if err != nil || len(cvmSaleConfList) == 0 {
			msg := fmt.Sprintf("getTkeNodeToLabel ListActiveCvmSaleConfWithCpuMemory Error, len %d", len(cvmSaleConfList))
			return errorcode.InternalErrorCode.NewWithInfo(msg, err)
		}
		cvmSaleConfMap := make(map[string]*table3.CvmSaleConfig, len(cvmSaleConfList))
		for _, config := range cvmSaleConfList {
			cvmSaleConfMap[config.InstanceType] = config
		}

		cvmService := cvm3.GetCvmService()
		cvmInstanceSet, err := cvmService.DescribeInstancesForIds(secretId, secretKey, region, instanceIdSet)
		if err != nil || len(cvmInstanceSet) == 0 {
			msg := fmt.Sprintf("getTkeNodeToLabel DescribeInstancesWithScsAccount Error, %d", len(cvmInstanceSet))
			return errorcode.InternalErrorCode.NewWithInfo(msg, err)
		}
		cvmInstanceMap := make(map[string]*cvm.Instance, len(instanceIdSet))
		for _, cvmInstance := range cvmInstanceSet {
			cvmInstanceMap[*cvmInstance.InstanceId] = cvmInstance
		}
		sort.Slice(instanceIdSet, func(i, j int) bool {
			iInstanceId := instanceIdSet[i]
			jInstanceId := instanceIdSet[j]
			iInstance, ok := cvmInstanceMap[*iInstanceId]
			if !ok {
				return false
			}
			jInstance, ok := cvmInstanceMap[*jInstanceId]
			if !ok {
				return true
			}
			iConfig, ok := cvmSaleConfMap[*iInstance.InstanceType]
			if !ok {
				return false
			}
			jConfig, ok := cvmSaleConfMap[*jInstance.InstanceType]
			if !ok {
				return true
			}
			if iConfig.Price > jConfig.Price {
				return false
			} else if iConfig.Price < jConfig.Price {
				return true
			}
			if iConfig.Generation < jConfig.Generation {
				return false
			} else if iConfig.Generation > jConfig.Generation {
				return true
			}
			// 确保每次选择出来的机器相同
			if *iInstance.InstanceId > *jInstance.InstanceId {
				return false
			} else if *iInstance.InstanceId < *jInstance.InstanceId {
				return true
			}
			return true
		})
	}

	specialWorkerSet := instanceIdSet[0:targetWorkerCount]
	nodeWorker := 1
	for _, instanceId := range specialWorkerSet {
		workNode := workerNodeMap[*instanceId]
		labelMap := workNode.GetObjectMeta().GetLabels()
		if workerZK {
			labelMap[constants.TKE_CVM_ZK_LABEL_KEY] = constants.TKE_ZK_NODE_LABEL_VAL
		}
		labelMap[constants.TKE_CVM_WORKER_LABEL_KEY] = fmt.Sprintf("worker")
		labelMap[constants.TKE_CVM_WORKER_NUM_LABEL_KEY] = fmt.Sprintf("worker%d", nodeWorker)
		nodeWorker += 1

		_, err = k8s.GetK8sService().AddNodeLabel(client, workNode.Name, labelMap)
		if err != nil {
			return errorcode.InternalErrorCode.NewWithErr(err)
		}
	}
	for _, instance := range controllerInstanceMap {
		controllerNode := controllerNodeMap[*instance.InstanceId]
		labelMap := controllerNode.GetObjectMeta().GetLabels()
		labelMap[constants.TKE_CVM_ZK_LABEL_KEY] = constants.TKE_ZK_NODE_LABEL_VAL

		_, err = k8s.GetK8sService().AddNodeLabel(client, controllerNode.Name, labelMap)
		if err != nil {
			return errorcode.InternalErrorCode.NewWithErr(err)
		}
	}
	return
}
