package taskHandler

import (
	"tencentcloud.com/tstream_galileo/src/common/dao"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/component"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/billing"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource_auth"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/tag"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
	"time"
)

type deleteClusterEndpointService struct {
}

func (s *deleteClusterEndpointService) Apps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	cg := ctx.ClusterGroup

	result = append(result, &deleteClusterApp{ctx: ctx})

	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM {

	}

	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId == "" {

	}

	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId != "" {

	}

	if cg.Type == constants.CLUSTER_GROUP_TYPE_SUB_EKS {

	}
	if ctx.Tke.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {

	}

	return result, nil
}

type deleteClusterApp struct {
	ctx *deploy.Context
	apps.ApplyApp
}

func (c *deleteClusterApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {

	clusterGroupService := c.ctx.ClusterGroupService

	err = clusterGroupService.MarkDeleted()
	if err != nil {
		return
	}

	if c.ctx.ClusterGroup.Type == constants.CLUSTER_GROUP_TYPE_SUB_EKS {
		// Stop EksSettle
		service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
			sql := "UPDATE EksSettle SET StopTime = ? WHERE ResourceId = ? and AppId = ? and StopTime = ?"
			tx.ExecuteSqlWithArgs(sql, util.GetCurrentTime(), c.ctx.ClusterGroup.SerialId, c.ctx.ClusterGroup.AppId, billing.NotIsolatedTimestamp)
			return nil
		}).Close()
		return nil, nil
	}

	err = c.UnbindTags(clusterGroupService.GetClusterGroup())
	if err != nil {
		return
	}
	return nil, nil
}

func (c *deleteClusterApp) UnbindTags(clusterGroup *table.ClusterGroup) (err error) {
	tagComponent := component.NewTagComponent()

	tags, err := tag.GetTagService().GetResourceTags(clusterGroup.OwnerUin, clusterGroup.Region, []string{clusterGroup.SerialId}, resource_auth.RESOURCE_PREFIX_CLUSTER)
	if err != nil {
		return err
	}

	for _, t := range tags {
		_, err = tagComponent.BatchDeleteResourcesTag(clusterGroup.OwnerUin, clusterGroup.OwnerUin,
			clusterGroup.Region, resource_auth.RESOURCE_SERVICE_TYPE, resource_auth.RESOURCE_PREFIX_CLUSTER,
			t.TagKey, t.TagValue, []string{clusterGroup.SerialId}, time.Now().Unix(), map[string]string{})
		if err != nil {
			return err
		}
	}

	// 解绑集群标签后解绑集群上关联作业的标签
	err = tag.JobUnBindToTagWithClusterGroupId(clusterGroup.SerialId+"_JobUnBindToTagWithClusterGroupId", clusterGroup.Id)
	if err != nil {
		logger.Errorf("Failed to JobUnBindToTagWithClusterGroupId of clusterGroup.SerialId, error: %+v", clusterGroup.SerialId, err)
	}
	return nil
}
