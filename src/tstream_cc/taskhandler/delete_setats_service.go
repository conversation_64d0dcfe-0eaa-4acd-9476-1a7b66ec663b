package taskHandler

import (
	"fmt"
	errors2 "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/dynamic"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/uuid"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type deleteSetatsService struct {
}

func (s *deleteSetatsService) Apps(ctx *deploy.Context) ([]apps.App, error) {
	requestId := fmt.Sprintf("deleteSetatsService-%s-%s", uuid.NewRandom().String(), ctx.ClusterGroup.SerialId)
	logger.Infof("deleteSetatsCvmService Apps, requestId:%s", requestId)
	result := make([]apps.App, 0)
	result = append(result, &deleteSetatsApp{ctx: ctx, requestId: requestId})

	return result, nil
}

type deleteSetatsApp struct {
	ctx *deploy.Context
	apps.ApplyApp
	requestId string
}

func (c *deleteSetatsApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	// 删除  setats-manager 和 setats-worker
	namespace := constants.DEFAULT_NAMESPACE
	name := "setats-manager"
	managerSts, err := c.ctx.K8sService().GetStatefulSetAppsV1(c.ctx.ClientSet(), namespace, name)
	if err != nil {
		logger.Errorf("%s deleteSetatsApp Ready GetStatefulSetAppsV1 error, %+v", c.requestId, err)
		return false, err
	}
	if managerSts != nil {
		logger.Infof("%s setats-manager sts is found, delete setats-manager", c.requestId)
		_, err = c.ctx.K8sService().DeleteStatefulSet(c.ctx.ClientSet(), managerSts)
		if err != nil {
			logger.Errorf("%s deleteSetatsApp delete setats-manager error, %+v", c.requestId, err)
			return nil, err
		}
	}

	managerDep, err := c.ctx.K8sService().GetDeploymentAppsV1(c.ctx.ClientSet(), namespace, name)
	if err != nil {
		logger.Errorf("%s managerDep deleteSetatsApp Ready GetStatefulSetAppsV1 error, %+v", c.requestId, err)
		return false, err
	}
	if managerSts != nil {
		logger.Infof("%s managerDep setats-manager deployment is found, delete setats-manager", c.requestId)
		_, err = c.ctx.K8sService().DeleteDeployment(c.ctx.ClientSet(), managerDep)
		if err != nil {
			logger.Errorf("%s managerDep deleteSetatsApp delete setats-manager error, %+v", c.requestId, err)
			return nil, err
		}
	}

	name = "setats-worker"
	workerSts, err := c.ctx.K8sService().GetStatefulSetAppsV1(c.ctx.ClientSet(), namespace, name)
	if err != nil {
		logger.Errorf("%s deleteSetatsApp Ready GetStatefulSetAppsV1 error, %+v", c.requestId, err)
		return nil, err
	}
	if workerSts != nil {
		logger.Infof("%s setats-worker sts is found, delete setats-worker", c.requestId)
		_, err = c.ctx.K8sService().DeleteStatefulSet(c.ctx.ClientSet(), workerSts)
		if err != nil {
			logger.Errorf("%s deleteSetatsApp delete setats-worker error, %+v", c.requestId, err)
			return nil, err
		}
	}

	count, _setats, err := service.GetSetatsByClusterGroupSerialId(c.ctx.ClusterGroup.SerialId)
	if err != nil {
		logger.Errorf("%s deleteSetatsApp Failed to get setats by ClusterGroupSerialId id:%s, with errors:%+v", c.requestId, c.ctx.ClusterGroup.SerialId, err)
		return nil, errorcode.FailedOperationCode.NewWithErr(err)
	}
	if count == 0 {
		logger.Errorf("%s deleteSetatsApp Failed to get setats by ClusterGroupSerialId id:%s, with count 0", c.requestId, c.ctx.ClusterGroup.SerialId)
		return nil, errorcode.FailedOperationCode.NewWithMsg(fmt.Sprintf("setats size is 0"))
	}

	// 删除operator
	operatorDep, err := c.ctx.K8sService().GetDeploymentAppsV1(c.ctx.ClientSet(), namespace, constants.ComponentSetatsOperator)
	if err != nil {
		logger.Errorf("%s deleteSetatsApp operatorDep error, %+v", c.requestId, err)
		return false, err
	}
	if operatorDep != nil {
		logger.Infof("%s deleteSetatsApp operatorDep is found, delete operator", c.requestId)
		_, err = c.ctx.K8sService().DeleteDeployment(c.ctx.ClientSet(), operatorDep)
		if err != nil {
			logger.Errorf("%s deleteSetatsApp delete setats-manager error, %+v", c.requestId, err)
			return nil, err
		}
	}

	// 删除PVC "worker-storage-setats-worker-1"  PV
	workerDefaultParallelism := _setats.WorkerDefaultParallelism
	for i := 0; i < workerDefaultParallelism; i++ {
		pvcName := fmt.Sprintf("worker-storage-setats-worker-%d", i)
		logger.Infof("deleteSetatsApp delete pvc %s", pvcName)
		pvName, err1 := c.ctx.K8sService().DeletePVC(c.ctx.ClientSet(), namespace, pvcName)
		if err1 != nil {
			logger.Errorf("%s deleteSetatsApp delete pvc %s error, %+v", c.requestId, pvcName, err1)
			return nil, err1
		}
		if pvName != "" {
			logger.Infof("deleteSetatsApp delete pv %s", pvName)
			err1 = c.ctx.K8sService().DeletePV(c.ctx.ClientSet(), pvName)
			if err1 != nil {
				logger.Errorf("%s deleteSetatsApp delete pv %s error, %+v", c.requestId, pvName, err1)
				return nil, err1
			}
		}
	}

	err = c.deleteSetats()
	if err != nil {
		logger.Errorf("%s deleteSetats error, %+v", c.requestId, err)
		return nil, err
	}

	// 删除hive metastore
	setatsHiveDep, err := c.ctx.K8sService().GetDeploymentAppsV1(c.ctx.ClientSet(), namespace, constants.ComponentSetatsHiveOperator)
	if err != nil {
		logger.Errorf("%s deleteSetatsApp setatsHiveDep error, %+v", c.requestId, err)
		return false, err
	}
	if setatsHiveDep != nil {
		logger.Infof("%s deleteSetatsApp operatorDep is found, delete operator", c.requestId)
		_, err = c.ctx.K8sService().DeleteDeployment(c.ctx.ClientSet(), setatsHiveDep)
		if err != nil {
			logger.Errorf("%s deleteSetatsApp delete setats hive meta error, %+v", c.requestId, err)
			return nil, err
		}
	}

	return nil, err
}

func (c *deleteSetatsApp) deleteSetats() error {
	config, err := k8s.GetK8sService().NewConfig(c.ctx.KubeConfig())
	dynamicClient, err := dynamic.NewForConfig(config)
	if err != nil {
		logger.Errorf("deleteSetats new dynamic client error: %s", err.Error())
		return err
	}

	gvr := schema.GroupVersionResource{
		Group:    "setats.oceanus.tencent.com",
		Version:  "v1",
		Resource: "setats",
	}
	err = dynamicClient.Resource(gvr).Namespace(DefaultNamespace(c.ctx.ClusterGroup)).Delete("setats", &metav1.DeleteOptions{})
	if err != nil {
		if errors2.IsNotFound(err) {
			logger.Warningf("deleteSetats setats is not found")
		} else {
			logger.Errorf("deleteSetats delete error: %s", err.Error())
			return err
		}
	}
	logger.Infof("deleteSetats success")
	return nil
}

func (c *deleteSetatsApp) SetatsReady(namespace string, name string) (bool, error) {
	managerSts, err := c.ctx.K8sService().GetStatefulSetAppsV1(c.ctx.ClientSet(), namespace, name)
	if err != nil {
		logger.Errorf("StartSetatsService Ready GetStatefulSetAppsV1 error, %+v", err)
		return false, err
	}
	ready, err := c.ctx.K8sService().StatefulSetReady(c.ctx.ClientSet(), managerSts)
	if err != nil {
		logger.Errorf("StartSetatsService Ready StatefulSetReady error, %+v", err)
		return false, err
	}
	if !ready {
		logger.Infof("StartSetatsService Ready StatefulSetReady not ready, %+v", err)
		return false, nil
	}
	return true, nil
}
