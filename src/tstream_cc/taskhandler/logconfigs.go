package taskHandler

import (
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common"
	tchttp "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/http"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/profile"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	service1 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config/configure_center"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

func ClsLogConfigApps(region, tkeInstanceId string, ctx *deploy.Context) []apps.App {
	result := make([]apps.App, 0)
	result = append(result, NewClsProvisioner(region, tkeInstanceId, ctx)...)
	result = append(result, NewLogAgent(region, tkeInstanceId, ctx)...)
	result = append(result, NewLogConfigCrd(ctx.CC())...)
	return result
}

func ClsLogConfigAppsForEKS(region, tkeInstanceId string, ctx *deploy.Context) []apps.App {
	result := make([]apps.App, 0)
	result = append(result, newEksConfig(ctx.CC())...)
	result = append(result, newEksClsProvisioner(region, tkeInstanceId, ctx)...)
	result = append(result, NewLogConfigCrd(ctx.CC())...)
	return result
}

type clsParams struct {
	region        string
	tkeInstanceId string
	cc            *configure_center.ConfigureCenter
}

func (p *clsParams) Params() (interface{}, error) {
	registryDomain, err := p.cc.ImageRegistry().TkeRegistry()
	if err != nil {
		return nil, err
	}
	data := &struct {
		Region         string
		ClusterID      string
		AuthHost       string
		RegistryDomain string
	}{
		Region:         p.region,
		ClusterID:      p.tkeInstanceId,
		AuthHost:       p.cc.OceanusDomain(),
		RegistryDomain: registryDomain,
	}
	return data, nil
}

type clsProvisioner struct {
	apps.Deployment
	clsParams
}

func NewClsProvisioner(region, tkeInstanceId string, ctx *deploy.Context) []apps.App {
	clsProvisionerCC := ctx.CC().FlowCC().TkeCC().ClsProvisionerCC()
	c := &clsProvisioner{
		clsParams: clsParams{
			region:        region,
			tkeInstanceId: tkeInstanceId,
			cc:            ctx.CC(),
		},
	}
	return []apps.App{
		&createServiceAccountApp{ctx: ctx, namespace: "kube-system", name: "cls-provisioner"},
		apps.NewClusterRoleBinding(clsProvisionerCC.ClusterRoleBinding),
		apps.NewClusterRole(clsProvisionerCC.ClusterRole),
		c,
	}
}

func (c *clsProvisioner) Decode(params, into interface{}) (interface{}, error) {
	return c.cc.FlowCC().TkeCC().ClsProvisionerCC().Deployment(params, into)
}

type eksClsProvisioner struct {
	apps.Deployment
	clsParams
}

func newEksClsProvisioner(region, tkeInstanceId string, ctx *deploy.Context) []apps.App {
	clsProvisionerCC := ctx.CC().FlowCC().TkeCC().ClsProvisionerCC()
	c := &eksClsProvisioner{
		clsParams: clsParams{
			region:        region,
			tkeInstanceId: tkeInstanceId,
			cc:            ctx.CC(),
		},
	}
	return []apps.App{
		&createServiceAccountApp{ctx: ctx, namespace: "kube-system", name: "cls-provisioner"},
		apps.NewClusterRoleBinding(clsProvisionerCC.ClusterRoleBinding),
		apps.NewClusterRole(clsProvisionerCC.ClusterRole),
		c,
	}
}

func (c *eksClsProvisioner) Decode(params, into interface{}) (interface{}, error) {
	return c.cc.FlowCC().TkeCC().ClsProvisionerCC().EksDeployment(params, into)
}

type logAgent struct {
	clsParams
	apps.DaemonSet
}

func NewLogAgent(region, tkeInstanceId string, ctx *deploy.Context) []apps.App {
	agentCC := ctx.CC().FlowCC().TkeCC().LogAgentCC()
	agent := &logAgent{
		clsParams: clsParams{
			region:        region,
			tkeInstanceId: tkeInstanceId,
			cc:            ctx.CC(),
		},
	}
	return []apps.App{
		&createServiceAccountApp{ctx: ctx, namespace: "kube-system", name: "tke-log-agent"},
		apps.NewClusterRole(agentCC.ClusterRole),
		apps.NewClusterRoleBinding(agentCC.ClusterRoleBinding),
		agent,
	}
}

func (l *logAgent) Decode(params, into interface{}) (interface{}, error) {
	return l.cc.FlowCC().TkeCC().LogAgentCC().DaemonSet(params, into)
}

func NewLogConfigCrd(cc *configure_center.ConfigureCenter) []apps.App {
	return []apps.App{&logConfigCrd{
		cc: cc,
	}}
}

type logConfigCrd struct {
	apps.CrdV1beta1
	cc *configure_center.ConfigureCenter
}

func (l *logConfigCrd) Params() (interface{}, error) {
	return nil, nil
}

func (l *logConfigCrd) Decode(params, into interface{}) (interface{}, error) {
	return l.cc.FlowCC().TkeCC().LogConfigCC().Crd(params, into)
}

// eks-config

func newEksConfig(cc *configure_center.ConfigureCenter) []apps.App {
	return []apps.App{&eksConfig{
		cc: cc,
	}}
}

type eksConfig struct {
	apps.ConfigMap
	cc *configure_center.ConfigureCenter
}

func (l *eksConfig) Params() (interface{}, error) {
	return nil, nil
}

func (l *eksConfig) Decode(params, into interface{}) (interface{}, error) {
	return l.cc.FlowCC().TkeCC().EksConfigCC().EksConfig(params, into)
}

type EnableEksLogRequest struct {
	*tchttp.BaseRequest
	ClusterId *string `json:"ClusterId"`
}

type EnableEksLogResponse struct {
	*tchttp.BaseResponse
	Response *struct {
		RequestId string `json:"RequestId"`
	}
}

func NewEnableEksLogRequest(clusterId *string) *EnableEksLogRequest {
	request := &EnableEksLogRequest{
		BaseRequest: &tchttp.BaseRequest{},
		ClusterId:   clusterId,
	}
	request.Init().WithApiInfo("tke", "2018-05-25", "InstallEksLogAgent")
	return request
}

func NewEnableEksLogResponse() *EnableEksLogResponse {
	response := &EnableEksLogResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return response
}

// enableEksLogCollect 开启EKS集群日志采集，调用云API
func enableEksLogCollect(region string, clusterId string) (err error, requestId string) {
	secretId, secretKey, err := service1.GetSecretIdAndKeyOfScs()
	if err != nil {
		err = errorcode.NewStackError(errorcode.InternalErrorCode, "GetScsSecretKey", err)
		return err, ""
	}
	prof := profile.NewClientProfile()
	if region == constants.AP_SHANGHAI_ADC {
		prof.HttpProfile.Endpoint = constants.TKE_API_DOMAIN_INTERNAL
	}
	client, err := common.NewClientWithSecretId(secretId, secretKey, region)
	client.WithProfile(prof)
	if err != nil {
		return err, ""
	}
	response := NewEnableEksLogResponse()
	err = client.Send(NewEnableEksLogRequest(&clusterId), response)
	if err != nil {
		logger.Errorf("error %+v", err)
		logger.Errorf("err response %+v", response)
		return err, ""
	}
	logger.Infof("enableEksLogCollect response : %v", response)
	return nil, response.Response.RequestId
}
