package taskHandler

import (
	v1 "k8s.io/api/core/v1"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/oceanus_controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/region"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type DeployClusterAdminService struct {
}

func (s *DeployClusterAdminService) Apps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	cg := ctx.ClusterGroup

	// 共享集群 包年包月下的eks集群，不做任何处理
	if cg.Type == constants.CLUSTER_GROUP_TYPE_SUB_EKS {
		return result, nil
	}

	// 创建共享集群母集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone == "" {
	}

	// 共享集群母集群，开新区
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone != "" {
	}

	// 独享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId == "" {
		if ctx.Tke.ArchGeneration < constants.TKE_ARCH_GENERATION_V2 {
			secret, err := ctx.GetOceanusIPSecret()
			if err != nil {
				return result, err
			}
			result = append(result,
				ctx.HadoopConfigMap(secret),
				ctx.LogListenerConfigMap(true, secret),
				ctx.LogListenerMetaConfigMap(constants.ComponentClusterAdmin, secret),
				s.clusterAdminConfigMap(ctx, secret),
				&clusterAdmin{ctx: ctx},
				ctx.Service(constants.ComponentClusterAdmin, constants.ClusterAdminPort, secret))
		}
	}

	// 共享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId != "" {
	}

	return result, nil
}

func (s *DeployClusterAdminService) clusterAdminConfigMap(ctx *deploy.Context, secret *v1.Secret) apps.App {
	dbName, _ := ctx.GetParam(constants.FLOW_PARAM_CLUSTERADMIN_DB, "cluster_admin")
	caCC := ctx.FlowCC.ClusterAdminCC()
	return ctx.ConfigMap(
		secret,
		apps.CMWithName(constants.ClusterAdminConfigmapName),
		apps.CMAddData("admin-conf.yaml", s.getConfigForAdminConf(ctx)),
		apps.CMAddData("flink-conf.yaml", ctx.FlinkConf(constants.ZooKeeperComponentReplicaNumber)),
		apps.CMAddData("log4j.properties", caCC.Flink111Log4jV2),
		// flink-1.13 k8s启动，log 的配置名字改为这个
		apps.CMAddData("log4j-console.properties", caCC.FlinkLog4jV2),
		apps.CMAddData("log4j-1.properties", caCC.Log4jV1),
		apps.CMAddData("application.conf", ctx.ConfUseCdbParams(dbName, ctx.FlowCC.ApplicationConf)))
	// ClusterAdmin 使用的 Log4j 1.x 配置文件 不能用 Common 下的 log4j.properties, 因为还需要写入日志到文件, 以便 CLS 采集

	// 去掉原来 1.10 集群 采用 Log4j的逻辑 新部署的集群，不会再是 1.10 的了
	// 去掉原来 on emr禁止日志输出到console，防止滚动日志失效
}

func (s *DeployClusterAdminService) getConfigForAdminConf(ctx *deploy.Context) apps.CMDataValue {
	return func() (string, error) {
		nginxUsername, nginxPassword, url, err := ctx.FlowCC.NginxCC().Info()
		if err != nil {
			return "", err
		}

		latestFlinkVersion := ctx.CC().DefaultFlinkVersion()

		regionId, err := region.GetRegionIdByName(ctx.ClusterGroup.Region)
		if err != nil {
			return "", err
		}

		params := struct {
			NginxUsername         string
			NginxPassword         string
			LatestFlinkVersion    string
			RegionId              int64
			ClusterId             int64
			PublicServiceNginxUrl string

			// 下面三个参数已经不用, 为了保持历史模板的兼容性, 暂时保留
			ZooKeeperUrl string
			YarnRestUrl  string
			HdfsRestUrl  string
		}{
			NginxUsername:         nginxUsername,
			NginxPassword:         nginxPassword,
			LatestFlinkVersion:    latestFlinkVersion,
			RegionId:              int64(regionId),
			ClusterId:             ctx.Cluster.Id,
			PublicServiceNginxUrl: url,
		}

		return ctx.FlowCC.ClusterAdminCC().AdminConf(params)
	}
}

type clusterAdmin struct {
	ctx *deploy.Context
	apps.StatefulSet
}

func (c *clusterAdmin) Params() (interface{}, error) {
	return c.getClusterAdminStatefulSetParams(c.ctx)
}

func (c *clusterAdmin) Decode(params, into interface{}) (interface{}, error) {
	return c.ctx.FlowCC.AppsCC().ClusterAdmin(params, into)
}

func (c *clusterAdmin) getClusterAdminStatefulSetParams(ctx *deploy.Context) (*oceanus_controller.Base, error) {
	var err error
	appContainerImage, exists := ctx.GetParam(constants.FLOW_PARAM_CLUSTERADMIN_IMAGE, "")
	if !exists {
		appContainerImage, err = ctx.CC().ImageRegistry().ClusterAdmin()
		if err != nil {
			return nil, err
		}
	}
	logContainerImage, err := ctx.CC().ImageRegistry().LogListener()
	if err != nil {
		return nil, err
	}

	if err != nil {
		return nil, err
	}
	base, err := ctx.ParamBase(constants.K8S_KIND_STATEFULSET,
		constants.OCEANUS_NAMESPACE,
		constants.ComponentClusterAdmin,
		true)
	if err != nil {
		return nil, err
	}

	base.AppImage = appContainerImage
	base.SidecarImage = logContainerImage
	return base, nil
}
