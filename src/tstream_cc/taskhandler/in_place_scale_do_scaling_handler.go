package taskHandler

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	flow2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/flow"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job"
	job_autoscale2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job_autoscale"
	"time"
)

type doInPlaceScalingHandler struct {
}

func (this *doInPlaceScalingHandler) HandleTaskStatusInit(requestId string, request *flow.TaskExecRequest, task *job_autoscale2.ScalingTask) (resp *flow.TaskExecResponse) {
	flowService := flow2.GetFlowService()
	params := request.Params
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf("AutoScale - %s - InPlace - doInPlaceScalingHandler HandleTaskStatusInit failed for Job %s, Action %s, because %+v", task.Action.SerialId,
				task.Action.JobSerialId, task.Action.SerialId, err)
			resp = flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1, fmt.Sprintf("%v", err), params)
		}
	}()
	if !task.CheckIfContinue(constants.SCALE_JOB_STATUS_READY) {
		return RunningRsp(params)
	}
	//url := task.WebUiUrlPrefix
	//logger.Infof("AutoScale - InPlace - doInPlaceScalingHandler get url %s for Job %s", url, task.Job.SerialId)
	getJobsIdUrl := "/jobs"
	suffix := fmt.Sprintf("%s/%s/rescaling", getJobsIdUrl, task.FlinkJobId)
	inPlaceScaleParam := &job_autoscale2.InPlaceScaleParam{
		Parallelism: task.TargetParallelism,
	}
	task.WebUiUrlSuffix = suffix
	if task.ScaleGranularity == constants.SCALEVERTEX {
		inPlaceScaleParam.SerializedJobGraph = base64.StdEncoding.EncodeToString([]byte(task.TargetJobGraph))
	}
	body, err := json.Marshal(inPlaceScaleParam)
	if err != nil {
		logger.Errorf("AutoScale - %s - InPlace - marshal body failed for Job %s, err: %s", task.Action.SerialId, task.Job.SerialId, err)
		task.ChangeToFail(constants.SCALE_EVENT_TYPE_INPLACE_JOB_FAILED)
		task.SaveStateToDB()
		return SuccessRsp(params)
	}
	logger.Debugf("AutoScale - %s - InPlace - send body %s", task.Action.SerialId, string(body))
	rsp1, err := task.CrdQuery(string(body))
	if err != nil {
		logger.Errorf("AutoScale - %s - InPlace - doInPlaceScalingHandler get job id from flink url %s failed for Job %s, err: %s", task.Action.SerialId, suffix, task.Job.SerialId, err)
		task.ChangeToFail(constants.SCALE_EVENT_TYPE_INPLACE_JOB_FAILED)
		task.SaveStateToDB()
		return SuccessRsp(params)
	}
	//rsp1, err := service3.SendHttpRequest(scaleJobUrl, "POST", string(body), nil, task.FlinkUsername, task.FlinkPassword)
	logger.Debugf("AutoScale - %s - InPlace - rsp %s", task.Action.SerialId, rsp1)
	if err != nil {
		logger.Errorf("AutoScale - %s - InPlace - doInPlaceScalingHandler get job id from flink url %s failed for Job %s, err: %s", task.Action.SerialId, suffix, task.Job.SerialId, err)
		if request.Retrycount > 3 {
			task.ChangeToFail(constants.SCALE_EVENT_TYPE_INPLACE_JOB_FAILED)
			task.SaveStateToDB()
			return SuccessRsp(params)
		}
		return RedoInitRsp(requestId, err, "", params)
	}
	rsp2 := &inPlaceScaleRsp{}
	err = json.Unmarshal([]byte(rsp1), &rsp2)
	if err != nil {
		logger.Errorf("AutoScale - %s - InPlace - unmarshal inPlaceScale rsp failed for Job %s, err: %s", task.Action.SerialId, task.Job.SerialId, err)
		task.ChangeToFail(constants.SCALE_EVENT_TYPE_INPLACE_JOB_FAILED)
		task.SaveStateToDB()
		return SuccessRsp(params)
	}
	if rsp2.RequestId == "" {
		logger.Errorf("AutoScale - %s - InPlace - %s - submit inplace scale failed  err: %s", task.Action.SerialId, task.Action.SerialId, rsp1)
		task.ChangeToFail(constants.SCALE_EVENT_TYPE_INPLACE_JOB_FAILED)
		task.SaveStateToDB()
		return SuccessRsp(params)
	}
	logger.Infof("AutoScale - %s - InPlace - do scale sucees requestId %s for Job %s", task.Action.SerialId, rsp2.RequestId, task.Job.SerialId)
	task.SetJobStatus(constants.SCALE_JOB_STATUS_SUBMIT_INPLACE_COMMAND)
	task.FlinkRequestId = rsp2.RequestId
	task.SaveStateToDB()
	return RunningRsp(params)
}

func (this *doInPlaceScalingHandler) HandleTaskStatusRunning(requestId string, request *flow.TaskExecRequest, task *job_autoscale2.ScalingTask) (resp *flow.TaskExecResponse) {
	flowService := flow2.GetFlowService()
	params := request.Params
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf("AutoScale - %s - inPlace - doInPlaceScalingHandler HandleTaskStatusRunning failed for Job %s, Action %s, because %+v", task.Action.SerialId,
				task.Action.JobSerialId, task.Action.SerialId, err)
			resp = flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1, fmt.Sprintf("%v", err), params)
		}
	}()
	// 这个地方用来处理线上多个机器的问题，不能放errorcode。（切记）
	if !task.CheckIfContinue(constants.SCALE_JOB_STATUS_SUBMIT_INPLACE_COMMAND) {
		logger.Warningf("AutoScale - %s - InPlace - Finish to do scaling for invalid flow status or error happened! Job %s, Action %s", task.Action.SerialId, task.Action.JobSerialId, task.Action.SerialId)
		return SuccessRsp(params)
	}

	if util.SubDate(task.StartTime, util.GetCurrentTime()) >= 600 {
		logger.Warningf("AutoScale - %s - restart job timeout! Job %s, Action %s", task.Action.SerialId, task.Action.JobSerialId, task.Action.SerialId)
		task.Alarm()
	}
	//
	//job, err := service.GetJobBySerialId(task.Job.SerialId)
	//if err != nil {
	//	logger.Errorf("AutoScale - InPlace - doInPlaceScalingHandler get job failed for Job %s, err: %s", task.Job.SerialId, err)
	//	task.EventErrorCode = constants.SCALE_EVENT_TYPE_INPLACE_JOB_FAILED
	//	return SuccessRsp(params)
	//}

	getJobsIdUrl := "/jobs"
	suffix := fmt.Sprintf("%s/%s/rescaling/%s", getJobsIdUrl, task.FlinkJobId, task.FlinkRequestId)
	logger.Infof("AutoScale - %s - InPlace - doInPlaceScalingHandler get result from url %s for Job %s", task.Action.SerialId, suffix, task.Job.SerialId)
	task.WebUiUrlSuffix = suffix
	//rsp1, err := service3.SendHttpRequest(scaleJobUrl, "GET", "", nil, task.FlinkUsername, task.FlinkPassword)
	rsp1, err := task.CrdQuery("")
	if err != nil {
		logger.Errorf("AutoScale - %s - InPlace - doInPlaceScalingHandler get job inplace result from  %s failed for Job %s, err: %s", task.Action.SerialId, suffix, task.Job.SerialId, err)
		task.ChangeToFail(constants.SCALE_EVENT_TYPE_INPLACE_JOB_FAILED)
		task.SaveStateToDB()
		return SuccessRsp(params)
	}
	scalingStatus := &inPlaceScalingRsp{}
	err = json.Unmarshal([]byte(rsp1), scalingStatus)
	if err != nil {
		logger.Errorf("AutoScale - %s - InPlace - unmarshal inPlaceScaling rsp failed for Job %s, err: %s", task.Action.SerialId, task.Job.SerialId, err)
		task.ChangeToFail(constants.SCALE_EVENT_TYPE_INPLACE_JOB_FAILED)
		task.SaveStateToDB()
		return SuccessRsp(params)
	}
	// akka超时，用资源判断
	if scalingStatus.Status.Id == "COMPLETED" && scalingStatus.Operation.FailureCause.Class != "" && scalingStatus.Operation.FailureCause.Class != "java.util.concurrent.TimeoutException" {
		logger.Errorf("AutoScale - %s - InPlace - SCALE failed err: %+v", task.Action.SerialId, scalingStatus.Operation.FailureCause)
		task.ChangeToFail(constants.SCALE_EVENT_TYPE_INPLACE_JOB_FAILED)
		task.SaveStateToDB()
		return SuccessRsp(params)
	} else if scalingStatus.Status.Id == "IN_PROGRESS" {
		logger.Debugf("AutoScale - %s - InPlace - scaling status :%s", task.Action.SerialId, scalingStatus.Status.Id)
		return RunningRsp(params)
	} else if scalingStatus.Status.Id == "COMPLETED" {
		// 超时则用资源判断
		if scalingStatus.Operation.FailureCause.Class == "java.util.concurrent.TimeoutException" {
			logger.Warningf("AutoScale - %s - InPlace - SCALE akka timeout reason: %+v", task.Action.SerialId, scalingStatus.Operation.FailureCause)
			job, err := service.GetJobBySerialId(task.Job.SerialId)
			if err != nil {
				logger.Errorf("AutoScale - %s - InPlace - doInPlaceScalingHandler get job failed for Job %s, err: %s", task.Action.SerialId, task.Job.SerialId, err)
				task.EventErrorCode = constants.SCALE_EVENT_TYPE_INPLACE_JOB_FAILED
				return SuccessRsp(params)
			}
			if task.TargetParallelism/task.Slot != int(job.TmRunningCuNum) {
				if util.SubDate(task.StartTime, util.GetCurrentTime()) >= 600 {
					logger.Errorf("AutoScale - %s - InPlace - restart job timeout! Job %s, Action %s", task.Action.SerialId, task.Action.JobSerialId, task.Action.SerialId)
					task.EventErrorCode = constants.SCALE_EVENT_TYPE_INPLACE_JOB_FAILED
					return SuccessRsp(params)
				}
				return RunningRsp(params)
			}
			// 如果资源相等则进入下一步
		}
		logger.Infof("AutoScale - %s - InPlace - scaling status :%s", task.Action.SerialId, scalingStatus)
		if err := task.UpdateJobConfig(task.TargetParallelism, task.TargetJmCUSpec, task.TargetTmCUSpec, task.TargetJmCPU, task.TargetJmMem, task.TargetTmCPU, task.TargetTmMem); err != nil {
			logger.Errorf("AutoScale - %s - InPlace - scale sucess,but update job config failed for Job %s, err: %s", task.Action.SerialId, task.Job.SerialId, err)
			task.EventErrorCode = constants.SCALE_EVENT_TYPE_INPLACE_MODIFY_CONFIG_FAILED
			task.SaveStateToDB()
			// 这里是galileo配置修改失败，也不好直接缩容回去，所以只能发送一个告警给用户
			task.AlarmConfigModifyFailed()
			return SuccessRsp(params)
		}
		if err = service.UpdateJobInstanceParallelism(task.Job.Id, task.TargetParallelism); err != nil {
			logger.Errorf("AutoScale - %s - InPlace - scale sucess,but update job config failed for Job %s, err: %s", task.Action.SerialId, task.Job.SerialId, err)
			task.EventErrorCode = constants.SCALE_EVENT_TYPE_INPLACE_MODIFY_CONFIG_FAILED
			task.SaveStateToDB()
			// 这里是galileo配置修改失败，也不好直接缩容回去，所以只能发送一个告警给用户
			task.AlarmConfigModifyFailed()
			return SuccessRsp(params)
		}
		// vertex的原地扩缩容还需要更新jobinstance的jobplan
		if task.ScaleGranularity == constants.SCALEVERTEX {
			err := service.UpdateJobInstanceJobPlan(task.Job.Id, task.TargetJobGraph)
			if err != nil {
				logger.Errorf("AutoScale - %s - InPlace - scale sucess,but update job instance job plan failed for Job %s, err: %s", task.Action.SerialId, task.Job.SerialId, err)
				task.EventErrorCode = constants.SCALE_EVENT_TYPE_INPLACE_MODIFY_CONFIG_FAILED
				task.SaveStateToDB()
				// 这里是galileo配置修改失败，也不好直接缩容回去，所以只能发送一个告警给用户
				task.AlarmConfigModifyFailed()
				return SuccessRsp(params)
			}
		}
		conf := &job_autoscale2.AutoScaleRuleConfig{}
		err = json.Unmarshal([]byte(task.Rule.Configuration), conf)
		if err != nil {
			logger.Errorf("AutoScale - %s - InPlace - unmarshal configuration failed for Job %s, err: %s", task.Action.SerialId, task.Job.SerialId, err)
			task.EventErrorCode = constants.SCALE_EVENT_TYPE_INPLACE_RESART_WD
			task.SaveStateToDB()
			return SuccessRsp(params)
		}
		conf.InPlaceTime = time.Now().String()
		config, err := json.Marshal(conf)
		if err != nil {
			logger.Errorf("AutoScale  %s - InPlace - unmarshal configuration failed for Job %s, err: %s", task.Action.SerialId, task.Job.SerialId, err)
			task.EventErrorCode = constants.SCALE_EVENT_TYPE_INPLACE_RESART_WD
			task.SaveStateToDB()
			return SuccessRsp(params)
		}
		if task.Action.ActionType != constants.SCALE_ACTION_TYPE_TASK_FLEXIBLE_TYPE {
			// 智能调优需要通过这个过期数据
			err = job_autoscale2.ModifyJobScaleConfiguration(string(config), task.Rule.RuleId)
			if err != nil {
				logger.Errorf("AutoScale - %s - InPlace - modify configuration failed for Job %s, err: %s", task.Action.SerialId, task.Job.SerialId, err)
				task.EventErrorCode = constants.SCALE_EVENT_TYPE_INPLACE_RESART_WD
				task.SaveStateToDB()
				return SuccessRsp(params)
			}
		}
		task.SetJobStatus(constants.SCALE_JOB_STATUS_FINISH_INPLACE_COMMAND)
		task.SaveStateToDB()
		return SuccessRsp(params)
	} else {
		logger.Errorf("AutoScale - %s - InPlace - unkown scaling status :%s", task.Action.SerialId, scalingStatus.Status.Id)
		task.ChangeToFail(constants.SCALE_EVENT_TYPE_INPLACE_JOB_FAILED)
		task.SaveStateToDB()
		return SuccessRsp(params)
	}
}

type jobsInfo struct {
	Jobs []struct {
		Id     string `json:"id"`
		Status string `json:"status"`
	} `json:"jobs"`
}

type inPlaceScaleRsp struct {
	RequestId string `json:"request-id"`
}
type inPlaceScalingRsp struct {
	Status struct {
		Id string `json:"id"`
	} `json:"status"`
	Operation struct {
		FailureCause struct {
			Class               string `json:"class"`
			StackTrace          string `json:"stack-trace"`
			SerializedThrowable string `json:"serialized-throwable"`
		} `json:"failure-cause"`
	} `json:"operation"`
}
