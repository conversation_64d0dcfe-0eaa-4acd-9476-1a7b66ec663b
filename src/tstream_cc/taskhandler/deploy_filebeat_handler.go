package taskHandler

import (
	"encoding/json"
	"fmt"

	v1 "k8s.io/api/core/v1"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/oceanus_controller"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/configuration_center"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/region"
	service1 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

const (
	FilebeatForSetats string = "filebeatForSetats"
)

type DeployFilebeatService struct {
	Type string
}

func (s *DeployFilebeatService) Apps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	cg := ctx.ClusterGroup
	logger.Infof("get filebeat apps")
	// 并非创建集群时部署
	if s.Type == FilebeatForSetats {
		return s.getAppsForSetats(ctx)
	}
	// 共享集群 包年包月下的eks集群，不做任何处理
	if cg.Type == constants.CLUSTER_GROUP_TYPE_SUB_EKS {
		return result, nil
	}

	// eks不部署
	if ctx.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		return result, nil
	}

	// 共享集群母集群，开新区
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone != "" {
		return result, nil
	}

	// 共享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId != "" {
		return result, nil
	}

	// 创建共享集群母集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone == "" {
	}

	// 独享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId == "" {
		return result, nil
	}

	return s.getApps(ctx)
}

func (s *DeployFilebeatService) getApps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	secret, err := ctx.GetNamespaceIPSecret(constants.KUBE_SYSTEM_NAMESPACE)
	if err != nil {
		logger.Errorf("%s: DeployFilebeatService with error %v", ctx.RequestId, err)
		return result, err
	}
	sNamespaceOption := apps.ServiceWithNamespace(constants.KUBE_SYSTEM_NAMESPACE)
	result = append(result, apps.NewClusterRole(ctx.FlowCC.FilebeatCC().ClusterRole))
	result = append(result, apps.NewClusterRoleBinding(ctx.FlowCC.FilebeatCC().ClusterRoleBinding))
	result = append(result, &filebeatSa{ctx: ctx})
	result = append(result, s.getFilebestCmApp(ctx, secret))
	result = append(result, ctx.ServiceBuilder(constants.ComponentFilebeat, constants.ComponentFilebeatPort, secret).
		WithOption(sNamespaceOption).Build())
	result = append(result, s.getFilebestApp(ctx))
	logger.Infof("get filebeat apps, result size : %d", len(result))

	return result, nil
}

func (s *DeployFilebeatService) getAppsForSetats(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	secret, err := ctx.GetNamespaceIPSecret(constants.KUBE_SYSTEM_NAMESPACE)
	if err != nil {
		logger.Errorf("%s: DeployFilebeatService with error %v", ctx.RequestId, err)
		return result, err
	}
	sNamespaceOption := apps.ServiceWithNamespace(constants.KUBE_SYSTEM_NAMESPACE)
	result = append(result, apps.NewClusterRole(ctx.FlowCC.FilebeatCC().ClusterRoleForSetats))
	result = append(result, apps.NewClusterRoleBinding(ctx.FlowCC.FilebeatCC().ClusterRoleBindingForSetats))
	result = append(result, &filebeatSaForSetats{ctx: ctx})
	result = append(result, s.getFilebestCmApp(ctx, secret))
	result = append(result, ctx.ServiceBuilder(constants.ComponentFilebeatForSetats, constants.ComponentFilebeatPort, secret).
		WithOption(sNamespaceOption).Build())
	result = append(result, s.getFilebestApp(ctx))
	logger.Infof("get filebeat apps, result size : %d", len(result))

	return result, nil
}

func (s *DeployFilebeatService) getFilebestCmApp(ctx *deploy.Context, secret *v1.Secret) apps.App {
	cmNamespaceOption := apps.CMWithNamespace(constants.KUBE_SYSTEM_NAMESPACE)
	configmapName := constants.FilebeatConfigMapName
	if s.Type == FilebeatForSetats {
		configmapName = constants.FilebeatConfigMapNameForSetats
	}
	app := ctx.ConfigMap(
		secret,
		cmNamespaceOption,
		apps.CMWithName(configmapName),
		apps.CMAddData("filebeat.yml", s.filebeatCm(ctx)))
	return app
}

func (s *DeployFilebeatService) getFilebestApp(ctx *deploy.Context) apps.App {
	filebeat := &filebeat{ctx: ctx}
	if s.Type == FilebeatForSetats {
		filebeat.NodeTypeValues = []string{
			constants.SETATS_MANAGER_LABEL,
			constants.SETATS_WORKER_LABEL,
		}
		filebeat.Name = constants.ComponentFilebeatForSetats
		filebeat.ServiceAccount = constants.ComponentFilebeatForSetats
		filebeat.ConfigMapName = constants.FilebeatConfigMapNameForSetats
	}
	return filebeat
}

type filebeatCmParam struct {
	Version         string
	Hosts           string
	UserName        string
	Password        string
	Topic           string
	RegionShortName string

	// 下面三个参数已经不用, 为了保持历史模板的兼容性, 暂时保留
	ZooKeeperUrl string
	YarnRestUrl  string
	HdfsRestUrl  string
}

func (s *DeployFilebeatService) filebeatCm(ctx *deploy.Context) apps.CMDataValue {
	if s.Type == FilebeatForSetats {
		return func() (string, error) {
			kafkaInfo, err := getLogCollectClsKafkaInfo(ctx)
			if err != nil {
				logger.Errorf("deploy filebeat CM with error %v", err)
				return "", err
			}
			params := filebeatCmParam{
				Version:         kafkaInfo.Version,
				Hosts:           kafkaInfo.Hosts,
				UserName:        kafkaInfo.UserName,
				Password:        kafkaInfo.Password,
				Topic:           kafkaInfo.Topic,
				RegionShortName: kafkaInfo.RegionShortName,
			}
			return ctx.FlowCC.FilebeatCC().FilebeatForSetatsYaml(params)
		}
	}
	return func() (string, error) {
		kafkaInfo, err := getLogCollectKafkaInfo()
		if err != nil {
			logger.Errorf("deploy filebeat CM with error %v", err)
			return "", err
		}
		params := filebeatCmParam{
			Version:  kafkaInfo.Version,
			Hosts:    kafkaInfo.Hosts,
			UserName: kafkaInfo.UserName,
			Password: kafkaInfo.Password,
			Topic:    kafkaInfo.Topic,
		}
		return ctx.FlowCC.FilebeatCC().FilebeatYaml(params)
	}
}

type filebeat struct {
	ctx *deploy.Context
	apps.DaemonSet
	NodeTypeValues []string
	Name           string
	ServiceAccount string
	ConfigMapName  string
}

func (d *filebeat) Params() (interface{}, error) {
	logger.Infof("apply filebeat ds")
	image, err := d.ctx.CC().ImageRegistry().Filebeat()
	if err != nil {
		logger.Errorf("%s: DeployFilebeatService get filebeat image with error %v", d.ctx.RequestId, err)
		return nil, err
	}
	base, err := d.ctx.ParamBase(constants.K8S_KIND_DAEMONSET,
		constants.KUBE_SYSTEM_NAMESPACE,
		constants.ComponentFilebeat, false)
	if err != nil {
		return nil, err
	}
	if d.NodeTypeValues != nil && len(d.NodeTypeValues) > 0 {
		base.NodeTypeValues = d.NodeTypeValues
	}
	if d.Name != "" {
		base.Name = d.Name
	} else {
		base.Name = constants.ComponentFilebeat
	}
	if d.ServiceAccount != "" {
		base.ServiceAccount = d.ServiceAccount
	} else {
		base.ServiceAccount = constants.ComponentFilebeat
	}
	if d.ConfigMapName != "" {
		base.ConfigMapName = d.ConfigMapName
	} else {
		base.ConfigMapName = constants.FilebeatConfigMapName
	}

	base.AppImage = image

	return base, nil
}

func (d *filebeat) Decode(params, into interface{}) (interface{}, error) {
	return d.ctx.FlowCC.AppsCC().Filebeat(params, into)
}

type filebeatSa struct {
	ctx *deploy.Context
	apps.ServiceAccount
}

func (c *filebeatSa) Params() (interface{}, error) {
	return &oceanus_controller.Base{
		Namespace: constants.KUBE_SYSTEM_NAMESPACE,
	}, nil
}

func (c *filebeatSa) Decode(params, into interface{}) (interface{}, error) {
	return c.ctx.FlowCC.FilebeatCC().ServiceAccount(into)
}

type filebeatSaForSetats struct {
	ctx *deploy.Context
	apps.ServiceAccount
}

func (c *filebeatSaForSetats) Params() (interface{}, error) {
	return &oceanus_controller.Base{
		Namespace: constants.KUBE_SYSTEM_NAMESPACE,
	}, nil
}

func (c *filebeatSaForSetats) Decode(params, into interface{}) (interface{}, error) {
	return c.ctx.FlowCC.FilebeatCC().ServiceAccountForSetats(into)
}

const (
	LogCollectKafkaInfoKey = "log.collect.kafka.info"
)

type LogCollectKafkaInfo struct {
	Version         string
	Hosts           string
	UserName        string
	Password        string
	Topic           string
	RegionShortName string
	Compression     string
}

func getLogCollectKafkaInfo() (*LogCollectKafkaInfo, error) {
	jsonStr, err := service.GetConfigurationValue(LogCollectKafkaInfoKey, "")
	if err != nil {
		logger.Errorf("GetConfigurationValueByKey for key %s, error: %+v", LogCollectKafkaInfoKey, err)
	}
	result := &LogCollectKafkaInfo{}
	err = json.Unmarshal([]byte(jsonStr), result)
	if err != nil {
		logger.Errorf("GetConfigurationValueByKey for key %s, parse to LogCollectKafkaInfo with error: %+v", LogCollectKafkaInfoKey, err)
	}
	return result, err
}

func getLogCollectClsKafkaInfo(ctx *deploy.Context) (*LogCollectKafkaInfo, error) {
	result := &LogCollectKafkaInfo{}
	regionShortName, err := region.GetRegionShortName(ctx.ClusterGroup.Region)
	if err != nil {
		logger.Errorf("cannot find regionShortName for region:%s, error:%v", ctx.ClusterGroup.Region, err)
		return result, err
	}
	logSetId, topicId, err := service1.GetTableService().GetSubmissionLogSetAndTopic(ctx.ClusterGroup.Region)
	if err != nil {
		logger.Errorf("GetSubmissionLogSetAndTopic err : %v", err)
		return result, err
	}
	result.RegionShortName = regionShortName
	result.Topic = topicId
	result.UserName = logSetId
	id, err := ctx.FlowCC.StartupLogListenerSecretId()
	if err != nil {
		logger.Errorf("cannot get StartupLogListenerSecretId, error %v", err)
		return result, err
	}
	key, err := ctx.FlowCC.StartupLogListenerSecretKey()
	if err != nil {
		logger.Errorf("cannot get StartupLogListenerSecretKey, error %v", err)
		return result, err
	}
	result.Password = fmt.Sprintf("%s#%s", id, key)
	return result, err
}
