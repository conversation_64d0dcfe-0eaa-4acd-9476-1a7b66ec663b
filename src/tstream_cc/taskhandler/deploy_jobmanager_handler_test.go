package taskHandler

import (
	"encoding/json"
	"fmt"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"testing"
)

func TestGenJmTmLabels(t *testing.T) {
	jmLabels := make([]string, 0)
	jmLabels = append(jmLabels, fmt.Sprintf(constants.ClusterLabelFormat1, constants.FLOW_PARAM_APPID, 1234567))
	jmLabels = append(jmLabels, fmt.Sprintf(constants.ClusterLabelFormat2, constants.FLOW_PARAM_OWNERUIN, "123"))
	jmLabels = append(jmLabels, fmt.Sprintf(constants.ClusterLabelFormat2, constants.FLOW_PARAM_CREATORUIN, "123"))
	jmLabels = append(jmLabels, fmt.Sprintf(constants.ClusterLabelFormat2, constants.FLOW_PARAM_TYPE, constants.ClusterSessionType))
	jmLabels = append(jmLabels, fmt.Sprintf(constants.ClusterLabelFormat2, constants.FLOW_PARAM_APP, fmt.Sprintf(constants.ClusterSessionAPPTemplate, "cluster-xxx", "flink-1-13")))

	tmLabels := make([]string, len(jmLabels))
	copy(tmLabels, jmLabels)
	jmLabels = append(jmLabels, fmt.Sprintf(constants.ClusterLabelFormat2, constants.ClusterLabelComponent, constants.ClusterJobmanager))
	tmLabels = append(tmLabels, fmt.Sprintf(constants.ClusterLabelFormat2, constants.ClusterLabelComponent, constants.ClusterTaskManager))

	jmLabelsMarshal, _ := json.Marshal(jmLabels)
	t.Log("=========jmLabels=========")
	t.Log(string(jmLabelsMarshal))
	t.Log("=========jmLabels=========")

	tmLabelsMarshal, _ := json.Marshal(tmLabels)
	t.Log("=========tmLabels=========")
	t.Log(string(tmLabelsMarshal))
	t.Log("=========tmLabels=========")
}
