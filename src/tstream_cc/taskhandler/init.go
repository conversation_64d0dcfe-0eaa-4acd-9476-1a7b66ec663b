package taskHandler

import (
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	constants2 "tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
)

var txManager *dao.DataSourceTransactionManager

func SetTxManager(txm *dao.DataSourceTransactionManager) {
	txManager = txm
}

func init() {
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_CREATE_CLUSTER, "createVPC", NewDeployHandler(&CreateVpcService{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_CREATE_CLUSTER, "createTKE", NewDeployHandler(&CreateTkeService{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_CREATE_CLUSTER, "createCDB", NewDeployHandler(&CreateCdbService{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_CREATE_CLUSTER, "createCVM", NewDeployHandler(&CreateCvmService{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_CREATE_CLUSTER, "initENI", NewDeployHandler(&InitEniService{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_CREATE_CLUSTER, "initTKE", NewDeployHandler(&InitTkeService{}))

	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_CREATE_CLUSTER, "deployZooKeeper", NewDeployHandler(&DeployZookeeperService{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_CREATE_CLUSTER, "deployIngress", NewDeployHandler(&DeployIngressService{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_CREATE_CLUSTER, "deployFilebeat", NewDeployHandler(&DeployFilebeatService{}))

	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_CREATE_CLUSTER, "deployClusterMaster", NewDeployHandler(&DeployClusterMasterService{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_CREATE_CLUSTER, "deployClusterAdmin", NewDeployHandler(&DeployClusterAdminService{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_CREATE_CLUSTER, "deployTaskCenter", NewDeployHandler(&DeployTaskCenterService{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_CREATE_CLUSTER, "deployCls", NewDeployHandler(&DeployClsService{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_CREATE_CLUSTER, "deployMetricPodAndService", NewDeployHandler(&DeployMetricProviderService{}))

	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_CREATE_CLUSTER, "deployCommandController", NewDeployHandler(&DeployCommandControllerService{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_CREATE_CLUSTER, "deployClusterScheduler", NewDeployHandler(&DeployClusterSchedulerService{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_CREATE_CLUSTER, "deployNodeExpansion", NewDeployHandler(&DeployNodeExpansionService{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_CREATE_CLUSTER, "deployWatchdog", NewDeployHandler(&DeployWatchdogService{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_CREATE_CLUSTER, "deployDiagnosisDataUploader", NewDeployHandler(&DeployDiagnosisDataUploaderService{}))

	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_CREATE_CLUSTER, "deployCredentialsProvider", NewDeployHandler(&DeployCredentialsService{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_CREATE_CLUSTER, "deployUserAgent", NewDeployHandler(&DeployUserAgentService{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_CREATE_CLUSTER, "deploySqlServer", NewDeployHandler(&DeploySqlServerService{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_CREATE_CLUSTER, "deployCoredns", NewDeployHandler(&DeployCorednsService{}))

	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_CREATE_CLUSTER, "endPoint", NewDeployHandler(&CreateClusterEndpointService{}))

	// 集群销毁
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_DELETE_CLUSTER, "DeleteCdb", NewDeployHandler(&deleteCdbService{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_DELETE_CLUSTER, "DeleteTkeCvm", NewDeployHandler(&deleteCvmService{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_DELETE_CLUSTER, "DeleteTke", NewDeployHandler(&deleteTkeService{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_DELETE_CLUSTER, "endPoint", NewDeployHandler(&deleteClusterEndpointService{}))

	// 集群扩容
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_SCALE_CLUSTER, "scaleTKECluster", NewDeployHandler(&ScaleClusterService{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_SCALE_CLUSTER, "endPoint", NewDeployHandler(&scaleClusterEndPointService{}))

	// 集群隔离
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_ISOLATE_CLUSTER, "stopClusterJob", NewDeployHandler(&isolateClusterService{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_ISOLATE_CLUSTER, "terminateCvms", NewDeployHandler(&isolateCvmService{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_ISOLATE_CLUSTER, "endPoint", NewDeployHandler(&isolateClusterEndPointService{}))

	// 集群续费
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_RENEW_CLUSTER, "renewCvms", NewDeployHandler(&renewCvmService{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_RENEW_CLUSTER, "endPoint", NewDeployHandler(&renewClusterEndPointService{}))

	//自动扩缩容
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_AUTO_SCALE_JOB, "doSavepoint", NewAutoScaleBaseHandler(&doSavepointHandler{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_AUTO_SCALE_JOB, "doScaling", NewAutoScaleBaseHandler(&doScalingHandler{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_AUTO_SCALE_JOB, "failureRecovery", NewCreateClusterBaseHandler(&failureRecoveryHandler{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_AUTO_SCALE_JOB, "endPoint", NewAutoScaleBaseHandler(&autoScaleEndpointHandler{}))

	//原地扩缩容
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_IN_PLACE_SCALE_JOB, "doScaling", NewAutoScaleBaseHandler(&doInPlaceScalingHandler{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_IN_PLACE_SCALE_JOB, "endPoint", NewAutoScaleBaseHandler(&inPlaceScaleEndpointHandler{}))
	//集群资源升级
	//flow.RegistTaskHandler(constants2.FLOW_OCEANUS_UPGRADE_CDB, "upgradeCdb", NewUpgradeHandler(&upgradeCDBHandler{}))
	//flow.RegistTaskHandler(constants2.FLOW_OCEANUS_UPGRADE_CDB, "endPoint", &upgradeCdbEndPointHandler{})

	//flow.RegistTaskHandler(constants2.FLOW_OCEANUS_UPGRADE_CONTROLLER, "upgradeController", NewDeployHandler(&oneStepActionService{}))
	//flow.RegistTaskHandler(constants2.FLOW_OCEANUS_UPGRADE_CONTROLLER, "endPoint", &upgradeControllerEndPointHandler{})

	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_UPGRADE_WORKER, "upgradeWorker", NewDeployHandler(&oneStepActionService{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_UPGRADE_WORKER, "endPoint", NewDeployHandler(&oneStepEndpointService{}))

	// 创建session集群
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_CREATE_SESSION_CLUSTER, "deployJobManager", NewDeployHandler(&DeployJobManagerService{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_CREATE_SESSION_CLUSTER, "deployTaskManager", NewDeployHandler(&DeployTaskManagerService{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_CREATE_SESSION_CLUSTER, "endPoint", &CreateSessionClusterEndPointHandler{})

	// 停止Session集群
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_STOP_SESSION_CLUSTER, "deleteJobManager", &DeleteJobManagerService{})
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_STOP_SESSION_CLUSTER, "deleteTaskManager", &DeleteTaskManagerService{})
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_STOP_SESSION_CLUSTER, "endPoint", &deleteClusterSessionEndpointHandler{})

	// 创建SqlGateway
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_CREATE_SQL_GATEWAY, "deploySqlGateway", NewDeployHandler(&DeploySqlGatewayHandler{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_CREATE_SQL_GATEWAY, "endPoint", &DeploySqlGatewayPointHandler{})

	// 停止SqlGateway
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_STOP_SQL_GATEWAY, "deleteSqlGateway", &DeleteSqlGatewayHandler{})
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_STOP_SQL_GATEWAY, "endPoint", &DeleteSqlGatewayEndpointHandler{})

	// 创建 esServerless 数据接入
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_APPLY_ES_SERVERLESS_COMPONENT, "createEsServerlessDi", NewCreateClusterBaseHandler(&ApplyEsServerlessFilebeatHandler{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_APPLY_ES_SERVERLESS_COMPONENT, "assignEniToFilebeat", NewCreateClusterBaseHandler(&ApplyEsServerlessFilebeatPatchHandler{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_APPLY_ES_SERVERLESS_COMPONENT, "endpoint", &ApplyEsServerlessFilebeatEndpointHandler{})

	// 创建固定ip所需的clb
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_CREATE_CLUSTER, "initClb", NewDeployHandler(&DeployInternalClbService{}))

	//setats
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_CREATE_SETATS, "addMasterApp", NewDeployHandler(&CreateSetatsMaster{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_CREATE_SETATS, "addWorkerApp", NewDeployHandler(&CreateSetatsWorker{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_CREATE_SETATS, "startSetatsWarehouseBase", NewDeployHandler(&StartSetatsWarehouseBase{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_CREATE_SETATS, "endPoint", NewDeployHandler(&CreateSetatsServiceEndpointHandler{}))

	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_RESTART_SETATS, "restartSetats", NewDeployHandler(&ReStartSetatsService{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_RESTART_SETATS, "endPoint", NewDeployHandler(&ReStartSetatsWarehouseEndpointHandler{}))

	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_START_SETATS_WAREHOUSE, "deploySetatsHiveMetastore", NewDeployHandler(&DeploySetatsHiveMetastoreHandler{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_START_SETATS_WAREHOUSE, "startSetatsService", NewDeployHandler(&StartSetatsService{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_START_SETATS_WAREHOUSE, "endPoint", NewDeployHandler(&StartSetatsWarehouseEndpointHandler{}))

	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_SCALE_SETATS, "scaleWorkerParallelism", NewDeployHandler(&ScaleSetatsWorkerParallelism{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_SCALE_SETATS, "scaleDisk", NewDeployHandler(&ScaleSetatsDisk{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_SCALE_SETATS, "modifySetatasInfoApp", NewDeployHandler(&ScaleSetatsModifyInfoHandler{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_SCALE_SETATS, "endPoint", NewDeployHandler(&ScaleSetatsEndpointHandler{}))

	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_DELETE_SETATS, "deleteSetatsCvm", NewDeployHandler(&deleteSetatsCvmService{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_DELETE_SETATS, "deleteSetatsTke", NewDeployHandler(&deleteSetatsService{}))
	flow.RegistTaskHandler(constants2.FLOW_OCEANUS_DELETE_SETATS, "endPoint", NewDeployHandler(&DeleteSetatsEndpointHandler{}))

}
