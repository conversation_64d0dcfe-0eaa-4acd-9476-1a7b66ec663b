package taskHandler

import (
	v1 "k8s.io/api/core/v1"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type DeployTaskCenterService struct {
}

func (s *DeployTaskCenterService) Apps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	cg := ctx.ClusterGroup

	// 共享集群 包年包月下的eks集群，不做任何处理
	if cg.Type == constants.CLUSTER_GROUP_TYPE_SUB_EKS {
		return result, nil
	}

	// 创建共享集群母集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone == "" {
	}

	// 共享集群母集群，开新区
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone != "" {
	}

	// 独享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId == "" {
		if ctx.Tke.ArchGeneration < constants.TKE_ARCH_GENERATION_V2 {
			secret, err := ctx.GetOceanusIPSecret()
			if err != nil {
				return result, err
			}
			result = append(result,
				ctx.LogListenerConfigMap(false, secret),
				ctx.LogListenerMetaConfigMap(constants.ComponentTaskcenter, secret),
				s.configMap(ctx, secret),
				&taskCenter{ctx: ctx},
				ctx.Service(constants.ComponentTaskcenter, constants.TaskcenterPort, secret))
		}
	}

	// 共享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId != "" {
	}

	return result, nil
}

type taskCenter struct {
	ctx *deploy.Context
	apps.StatefulSet
}

func (t *taskCenter) Params() (interface{}, error) {
	appContainerImage, err := t.ctx.CC().ImageRegistry().TaskCenter()
	if err != nil {
		return nil, err
	}
	logContainerImage, err := t.ctx.CC().ImageRegistry().LogListener()
	if err != nil {
		return nil, err
	}

	base, err := t.ctx.ParamBase(constants.K8S_KIND_STATEFULSET,
		constants.OCEANUS_NAMESPACE,
		constants.ComponentTaskcenter, false)
	if err != nil {
		return nil, err
	}

	base.AppImage = appContainerImage
	base.SidecarImage = logContainerImage

	return base, nil
}

func (t *taskCenter) Decode(params, into interface{}) (interface{}, error) {
	return t.ctx.FlowCC.AppsCC().TaskCenter(params, into)
}

func (s *DeployTaskCenterService) configMap(ctx *deploy.Context, secret *v1.Secret) apps.App {
	tcCC := ctx.FlowCC.TaskCenterCC()
	return ctx.ConfigMap(
		secret,
		apps.CMWithName(constants.TaskcenterConfigmapName),
		apps.CMAddData("config.properties", s.configProperties(ctx)),
		apps.CMAddData("mybatis-config.xml", tcCC.MybatisConf),
		apps.CMAddData("log4j.properties", ctx.FlowCC.CommonLog4j),
		apps.CMAddData("flink_restart_job.bpmn", tcCC.FlinkRestartJobBPMN),
		apps.CMAddData("flink_run_job.bpmn", tcCC.FlinkRunJobBPMN),
		apps.CMAddData("flink_stop_job.bpmn", tcCC.FlinkStopJobBPMN))
}

func (s *DeployTaskCenterService) configProperties(ctx *deploy.Context) apps.CMDataValue {
	return func() (string, error) {
		return ctx.ConfUseCdbParams(constants.TaskcenterDatabaseName,
			ctx.FlowCC.TaskCenterCC().ConfProperties)()
	}
}
