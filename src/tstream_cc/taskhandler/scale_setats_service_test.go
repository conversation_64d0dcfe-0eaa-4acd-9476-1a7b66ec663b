package taskHandler

import (
	"tencentcloud.com/tstream_galileo/src/common/flow"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	flow2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/flow"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
	"testing"
)

func TestScaleUpSetatsDisk(t *testing.T) {

	params := make(map[string]string, 0)
	params[constants.FLOW_PARAM_SETATS_WORKER_DISK_SIZE] = "100"
	ctx := &deploy.Context{

		ClusterFlowService: &flow2.ClusterFlowService{
			FlowService: flow2.FlowService{},
			RequestId:   "",
			Request: &flow.TaskExecRequest{
				Retrycount: 0,
				Processkey: "",
				Taskcode:   "",
				DocId:      "",
				FlowId:     "",
				TaskId:     "",
				Params:     params,
			},
			ClusterGroup: &table.ClusterGroup{
				SerialId:           "cluster-xxxx",
				NetEnvironmentType: 1,
				Region:             "ap-guangzhou",
			},
			Cluster: &table.Cluster{
				Id:                    0,
				UniqClusterId:         "",
				ClusterGroupId:        0,
				CreatorUin:            "",
				Zone:                  "",
				VpcId:                 "",
				SubnetId:              "",
				SupportedZoneSubnets:  "",
				VpcCIDR:               "",
				RoleType:              0,
				SchedulerType:         0,
				CuNum:                 0,
				UsedCuNum:             0,
				DefaultCOSBucket:      "",
				CreateTime:            "",
				UpdateTime:            "",
				StopTime:              "",
				Remark:                "",
				KubeConfig:            "",
				WebUIPrefix:           "",
				ClusterConfig:         "",
				FlinkConfig:           "",
				FlinkVersion:          "",
				ClsLogSet:             "",
				ClsTopicId:            "",
				LogConfig:             "",
				DefaultLogCollectConf: "",
				SupportedFeatures:     "",
				SupportedFlinkVersion: "",
				LogMode:               0,
				ClusterExtendConfig:   "",
				LoadBalanceId:         "",
				WebUIType:             0,
				MemRatio:              4,
				Cores:                 0,
				CrossTenantEniMode:    0,
				RouteConfig:           "",
			},
			ConnectionType:      0,
			Tke:                 nil,
			ClusterType:         0,
			ArchGeneration:      0,
			Cdb:                 nil,
			Region:              "ap-beijing",
			NewZone:             "",
			ClusterGroupService: nil,
			CurStatus:           0,
		},

		FlowCC: nil,
	}
	//s := &scaleDisk{ctx: ctx, requestId: "requestId1111111"}
	//invocationIds := make([]*string, 0)
	//i1 := "inv-1234"
	//invocationIds = append(invocationIds, &i1)
	//s.DescribeInvocations(invocationIds)
	//s.ScaleUpSetatsDisk()
	//handler := NewDeployHandler(&ScaleSetatsService{})
	//
	//request := &flow.TaskExecRequest{
	//	Retrycount: 0,
	//	Processkey: "test",
	//	Taskcode:   "test",
	//	DocId:      "1",
	//	FlowId:     "1",
	//	TaskId:     "1",
	//	Params: map[string]string{
	//		constants.FLOW_PARAM_CLUSTER_GROUP_ID:        fmt.Sprintf("%d", 1),
	//		constants.FLOW_PARAM_CLUSTER_ID:              fmt.Sprintf("%d", 1),
	//		constants.FLOW_PARAM_REQUEST_ID:              "test_hahaha",
	//		constants.FLOW_PARAM_SETATS_WORKER_DISK_SIZE: "100",
	//	},
	//}
	//
	//handler.CompleteTask(request)
	AddSetatsCvm("aaaa", ctx, 8, 32, "CLOUD_PREMIUM", 1, nil)
}
