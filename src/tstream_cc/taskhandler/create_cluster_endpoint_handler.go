package taskHandler

import (
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	service2 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	service4 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config/common_config"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/tke"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
	"time"
)

type CreateClusterEndpointService struct {
}

func (s *CreateClusterEndpointService) Apps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	cg := ctx.ClusterGroup

	// 共享集群母集群，开新区
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone != "" {
		result = append(result, &fillSupportNewZoneApp{ctx: ctx})
		return result, nil
	}

	result = append(result, &switchClusterGroupStatusApp{ctx: ctx, status: constants.CLUSTER_GROUP_STATUS_RUNNING})
	result = append(result, &updateStartTimeApp{ctx: ctx})

	// 共享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId != "" {
		return result, nil
	}

	// 共享集群 包年包月下的eks集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_SUB_EKS {
		result = append(result, &updateParentStatus{ctx: ctx})
		return result, nil
	}

	deployNodeExpansion := auth.IsInWhiteList(int64(cg.AppId), constants.WHITE_LIST_NODE_EXPANSION)
	if cg.NetEnvironmentType == constants.NETWORK_ENV_CLOUD_VPC && !deployNodeExpansion {
		result = append(result, &modifyClusterSchedulerApp{ctx: ctx})
	}

	//安全问题，处理tke创建过程中保留的secret
	result = append(result, &deleteSecretApp{ctx: ctx, namespace: "default", name: "qcloudregistrykey"})

	// 创建共享集群母集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone == "" {
	}

	// 独享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId == "" {
		if cg.NetEniType != constants.CLUSTER_NET_ENI_NODE {
			result = append(result, &enableEniIpReserveApp{ctx: ctx})
		}
	}

	//云梯环境下执行密码入库
	logger.Debugf("cg.NetEnvironmentType == constants.NETWORK_ENV_INNER_VPC: %+v CreateClusterEndpointService", cg.NetEnvironmentType == constants.NETWORK_ENV_INNER_VPC)
	if cg.NetEnvironmentType == constants.NETWORK_ENV_INNER_VPC {
		result = append(result, &updatePasswordApp{ctx: ctx})
	}

	return result, nil
}

type deleteSecretApp struct {
	apps.ApplyApp
	ctx       *deploy.Context
	namespace string
	name      string
}

func (c *deleteSecretApp) Apply(client apps.Client, _ interface{}) (_ interface{}, err error) {

	err = client.K8sService().DeleteSecrets(client.ClientSet(), c.namespace, c.name)

	return
}

type enableEniIpReserveApp struct {
	ctx *deploy.Context
	apps.ApplyApp
}

func (c *enableEniIpReserveApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	handler := &EnableEniIpReserve{}
	go handler.enableEniIpReserve(c.ctx.RequestId, c.ctx.Request)
	return
}

type modifyClusterSchedulerApp struct {
	ctx *deploy.Context
	apps.ApplyApp
}

func (c *modifyClusterSchedulerApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	go TryModifyClusterScheduler(c.ctx.ClusterGroup, "MostRequestedPriority")
	return
}

type sendCredentialApp struct {
	ctx *deploy.Context
	apps.ReadyApp
}

func (c *sendCredentialApp) Ready(_ apps.Client, _ interface{}) (bool, error) {

	serialIds := make([]string, 0)
	serialIds = append(serialIds, c.ctx.ClusterGroup.SerialId)

	successSerialIds, errorSerialIds, err := service.SendClusterCredential(serialIds)

	if err != nil {
		return false, err
	}

	if len(errorSerialIds) > 0 {
		return false, fmt.Errorf("send credential error: %+v", errorSerialIds)
	}

	logger.Infof("success send credential provider: %+v", successSerialIds)
	return true, nil
}

type updateStartTimeApp struct {
	ctx *deploy.Context
	apps.ApplyApp
}

func (c *updateStartTimeApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	err = service.UpdateStartTime(c.ctx.ClusterGroup.Id)
	return
}

func TryModifyClusterScheduler(cg *table.ClusterGroup, priority string) {
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf("TryModifyClusterScheduler failed because: %v", err)
		}
	}()

	groupService, err := service.NewClusterGroupServiceBySerialId(cg.SerialId)
	if err != nil {
		logger.Error("TryModifyClusterScheduler: can not get groupSerivice", err)
		return
	}

	tkeService := tke.NewTkeService()
	secretId, secretKey, err := service4.GetSecretIdAndKeyByNetworkEnvType(cg.NetEnvironmentType)
	if err != nil {
		logger.Error("TryModifyClusterScheduler: can not get secretId", err)
		return
	}

	tkeList, err := groupService.GetTkeList()
	if err != nil {
		logger.Error("TryModifyClusterScheduler: can not get tkeList", err)
		return
	}
	if len(tkeList) != 1 {
		logger.Error("TryModifyClusterScheduler: tke list length is not equal one")
		return
	}
	tkeInstance := tkeList[0]

	priorities := map[string]int64{
		"SelectorSpreadPriority":      1,
		"InterPodAffinityPriority":    1,
		"BalancedResourceAllocation":  1,
		"NodePreferAvoidPodsPriority": 10000,
		"NodeAffinityPriority":        1,
		"TaintTolerationPriority":     1,
		"ImageLocalityPriority":       1,
	}

	//第一次调用，去掉LeastRequestedPriority
	builder := tke.NewDefaultModifyClusterSchedulerPolicyBuilder().WithClusterId(tkeInstance.InstanceId)
	for k, v := range priorities {
		builder.WithPriority(k, v)
	}
	mReq := builder.Build()
	logger.Infof("ModifyClusterSchedulerPolicy Request: %s", mReq.ToJsonString())

	err = tkeService.ModifyClusterSchedulerPolicy(secretId, secretKey, "", cg.Region, mReq)
	if err != nil {
		logger.Error("TryModifyClusterScheduler: ModifyClusterSchedulerPolicy err", err)
		return
	}

	//第二次调用间隔3分钟
	time.Sleep(time.Duration(3) * time.Minute)

	//第二次调用，增加MostRequestedPriority/LeastRequestedPriority
	priorities[priority] = 1
	builder = tke.NewDefaultModifyClusterSchedulerPolicyBuilder().WithClusterId(tkeInstance.InstanceId)
	for k, v := range priorities {
		builder.WithPriority(k, v)
	}
	mReq = builder.Build()
	logger.Infof("ModifyClusterSchedulerPolicy Request2: %s", mReq.ToJsonString())

	err = tkeService.ModifyClusterSchedulerPolicy(secretId, secretKey, "", cg.Region, mReq)
	if err != nil {
		logger.Error("TryModifyClusterScheduler: ModifyClusterSchedulerPolicy err2", err)
		return
	}
	logger.Infof("ModifyClusterSchedulerPolicy Success")
}

type fillSupportNewZoneApp struct {
	apps.ApplyApp
	ctx *deploy.Context
}

func (c *fillSupportNewZoneApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	service2.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		cg := c.ctx.ClusterGroup
		if err := cg.AddSupportedZone(c.ctx.NewZone); err != nil {
			return err
		}
		tx.ExecuteSqlWithArgs("UPDATE ClusterGroup SET SupportedZones = ? WHERE id = ?", cg.SupportedZones, cg.Id)
		return nil
	}).Close()
	return
}

type updateParentStatus struct {
	apps.ApplyApp
	ctx *deploy.Context
}

func (c *updateParentStatus) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	service2.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		cg := c.ctx.ClusterGroup
		if cg.ParentSerialId != "" {
			tx.ExecuteSqlWithArgs("update ClusterGroup set Status=? where SerialId=?", constants.CLUSTER_GROUP_STATUS_RUNNING, cg.ParentSerialId)
		}
		return nil
	}).Close()
	return
}

type updatePasswordApp struct {
	apps.ApplyApp
	ctx *deploy.Context
}

func (c *updatePasswordApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	//查到所有的IP
	ts := tke.GetTkeService()
	_, instanceSet, err := ts.DescribeClusterAllInstancesWithScsAccountByNetEnvironmentType(constants.NETWORK_ENV_INNER_VPC, c.ctx.ClusterFlowService.Region, c.ctx.ClusterFlowService.Tke.InstanceId)
	if err != nil {
		logger.Errorf("DescribeClusterAllInstancesWithScsAccountByNetEnvironmentType err:%v", err)
		return
	}
	//查询集群下所有的机器内网ip
	lanIps := make([]string, 0)
	for _, instance := range instanceSet {
		if instance.LanIP != nil && *instance.LanIP != "" {
			lanIps = append(lanIps, *instance.LanIP)
		}
	}
	//获取集群region
	region := c.ctx.ClusterGroup.Region
	logger.Infof("ips:%v ,region: %v", lanIps, region)
	//查询节点是否入库
	ips2SavePwd := make([]string, 0)
	for _, lanIp := range lanIps {
		ret, err := service4.GetWhetherInitPwd(lanIp, region)
		//ret 为0为已入库 其他为未入库
		if err != nil {
			logger.Errorf("GetPasswordByIp2XingYun err:%v", err)
			return nil, err
		}
		if ret != 0 {
			logger.Debugf("update password for %s", lanIp)
			ips2SavePwd = append(ips2SavePwd, lanIp)
		}
	}
	//存储密码到星云
	//获取七彩石中配置的初始化密码
	rainbowOceanusClusterPassword, err := common_config.GetRainbowOceanusClusterPassword()
	if err != nil {
		logger.Errorf("get rainbowOceanusClusterPassword err:%v", err)
		return
	}
	logger.Infof("update password for %v rainbowOceanusClusterPassword is %v", ips2SavePwd, rainbowOceanusClusterPassword)
	_, err = service4.InitCvmPasswd2XingYun(ips2SavePwd, rainbowOceanusClusterPassword, region)
	if err != nil {
		logger.Errorf("InitCvmPasswd2XingYun err:%v", err)
		return
	}
	return
}
