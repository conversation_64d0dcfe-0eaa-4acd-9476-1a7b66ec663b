package taskHandler

import (
	"fmt"
	"k8s.io/apiextensions-apiserver/pkg/client/clientset/clientset"
	"k8s.io/client-go/kubernetes"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config/flowCC"
	flow2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/flow"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
)

type ApplyClsComponentService struct {
	*flow2.ClusterFlowService
	k8sService  *k8s.K8sService
	clientSet   *kubernetes.Clientset
	clientSetEx *clientset.Clientset
	tkeCC       *flowCC.TkeCC
	clusterType int

	clsLogApps []apps.App
}

func (s *ApplyClsComponentService) KubeConfig() []byte {
	return []byte(s.Cluster.KubeConfig)
}

func (s *ApplyClsComponentService) K8sService() *k8s.K8sService {
	return s.k8sService
}

func (s *ApplyClsComponentService) ClientSet() *kubernetes.Clientset {
	return s.clientSet
}

func (s *ApplyClsComponentService) ClientSetEx() *clientset.Clientset {
	return s.clientSetEx
}

// apply 会记录app是否apply过，如果apply过，下次调用不会调用该app，eksClsProvisioner 组件是个特例，需要跳过
func (s *ApplyClsComponentService) apply(stepFlag string, appList ...apps.App) error {
	var returnErr error
	for i, app := range appList {
		flag := fmt.Sprintf("cls_component_%s_app_%d", stepFlag, i)
		logger.Infof("try to apply %s %+v", i, flag)
		if err := s.applyApp(stepFlag, i, app); err != nil {
			logger.Errorf("!!! apply %s %+v err %v", flag, app, err)
			returnErr = err
		}
	}
	return returnErr
}

func (s *ApplyClsComponentService) applyApp(stepFlag string, i int, a apps.App) error {
	flag := fmt.Sprintf("cls_component_%s_app_%d", stepFlag, i)
	_, exist := s.GetParam(flag, "")
	if exist {
		return nil
	}
	params, err := a.Params()
	if err != nil {
		logger.Errorf("apply % Params with error %v", flag, err)
		return err
	}
	o := a.AppType()
	o, err = a.Decode(params, o)
	if err != nil {
		logger.Errorf("apply %s Decode with error %v", flag, err)
		return err
	}
	_, err = a.Apply(s, o)
	if err != nil {
		logger.Errorf("apply %s with error %v", flag, err)
		return err
	}
	s.SetReturnParam(flag, "")
	return nil
}

func (s *ApplyClsComponentService) ready(apps ...apps.App) (bool, error) {
	for i, app := range apps {
		ok, err := s.readyApp(app)
		if err != nil {
			logger.Errorf("check app %d ready with error %v", i, err)
			return false, err
		}
		if !ok {
			return false, nil
		}
	}
	return true, nil
}

func (s *ApplyClsComponentService) readyApp(a apps.App) (bool, error) {
	params, err := a.Params()
	if err != nil {
		logger.Errorf("check readyApp with error %v ", err)
		return false, err
	}
	o := a.AppType()
	o, err = a.Decode(params, o)
	if err != nil {
		logger.Errorf("check readyApp with error %v", err)
		return false, err
	}
	return a.Ready(s, o)
}

func (s *ApplyClsComponentService) EKS() bool {
	return s.clusterType == constants.K8S_CLUSTER_TYPE_EKS
}

func (s *ApplyClsComponentService) Apply() *flow.TaskExecResponse {
	if s.EKS() {
		return s.DoneRsp("eks cluster not support yet")
	}
	if err := s.apply("apply_cls_log", s.clsLogApps...); err != nil {
		return s.RetryRspWithErr(err)
	}
	logger.Infof("%s apply cls component successfully", s.Cluster.UniqClusterId)
	return s.DoneRsp("")
}

func (s *ApplyClsComponentService) Ready() *flow.TaskExecResponse {
	ok, err := s.ready(s.clsLogApps...)
	if err != nil {
		return s.RetryRspWithErr(err)
	}
	if !ok {
		return s.RetryRsp("cls component not ready")
	}
	return s.DoneRsp("")
}
