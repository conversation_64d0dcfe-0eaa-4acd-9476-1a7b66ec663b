package taskHandler

import (
	"fmt"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/oceanus_controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type DeployCorednsService struct {
}

func (coredns *DeployCorednsService) Apps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	cg := ctx.ClusterGroup

	// 共享集群 包年包月下的eks集群，不做任何处理
	if cg.Type == constants.CLUSTER_GROUP_TYPE_SUB_EKS {
		return result, nil
	}

	if ctx.Tke.ArchGeneration < constants.TKE_ARCH_GENERATION_V2 {
		return result, nil
	}

	// 共享集群母集群，开新区
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone != "" {
		// TODO 开新区，可以增大replica
		return result, nil
	}

	// 创建共享集群母集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone == "" {
	}

	// 独享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId == "" {
		// coredns默认设置弹性网卡
		if cg.NetEniType != constants.CLUSTER_NET_ENI_NODE {
			result = append(result, &corednsEniApp{ctx: ctx})
		}
	}

	// 共享集群, 每个集群ns下创建一个coredns
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId != "" {
		result = append(result,
			&corednsCrb{ctx: ctx},
			&corednsSa{ctx: ctx},
			&corednsCm{ctx: ctx},
			&corednsDep{ctx: ctx},
			&corednsService{ctx: ctx})

	}

	return result, nil
}

type corednsSa struct {
	ctx *deploy.Context
	apps.ServiceAccount
}

func (c *corednsSa) Params() (interface{}, error) {
	return &oceanus_controller.Base{
		Namespace: fmt.Sprintf("oceanus-%s", c.ctx.ClusterGroup.SerialId),
	}, nil
}

func (c *corednsSa) Decode(params, into interface{}) (interface{}, error) {
	return c.ctx.FlowCC.TkeCC().CorednsCC().ServiceAccount(params, into)
}

type corednsCm struct {
	ctx *deploy.Context
	apps.ConfigMap
}

func (c *corednsCm) Params() (interface{}, error) {
	return &oceanus_controller.Base{
		Namespace: fmt.Sprintf("oceanus-%s", c.ctx.ClusterGroup.SerialId),
	}, nil
}

func (c *corednsCm) Decode(params, into interface{}) (interface{}, error) {
	return c.ctx.FlowCC.TkeCC().CorednsCC().Configmap(params, into)
}

type corednsCrb struct {
	ctx *deploy.Context
	apps.ClusterRoleBinding
}

func (c *corednsCrb) Params() (interface{}, error) {
	return &oceanus_controller.Base{
		Namespace: fmt.Sprintf("oceanus-%s", c.ctx.ClusterGroup.SerialId),
	}, nil
}

func (c *corednsCrb) Decode(params, into interface{}) (interface{}, error) {
	return c.ctx.FlowCC.TkeCC().CorednsCC().ClusterRoleBinding(params, into)
}

type corednsService struct {
	ctx *deploy.Context
	apps.Service
}

func (c *corednsService) Params() (interface{}, error) {
	return &oceanus_controller.Base{
		Namespace: fmt.Sprintf("oceanus-%s", c.ctx.ClusterGroup.SerialId),
	}, nil
}

func (c *corednsService) Decode(params, into interface{}) (interface{}, error) {
	return c.ctx.FlowCC.TkeCC().CorednsCC().Service(params, into)
}

type corednsDep struct {
	ctx *deploy.Context
	apps.Deployment
}

func (c *corednsDep) Params() (interface{}, error) {
	annotations, err := c.ctx.GetPodAnnotations(true, true)
	if err != nil {
		logger.Errorf("corednsDep params error: %+v", err)
		return nil, err
	}
	registry, err := c.ctx.CC().ImageRegistry().TkeRegistry()
	if err != nil {
		logger.Errorf("corednsDep registry error: %+v", err)
		return nil, err
	}
	return struct {
		Namespace   string
		Annotations map[string]string
		Registry    string
	}{
		Namespace:   fmt.Sprintf("oceanus-%s", c.ctx.ClusterGroup.SerialId),
		Annotations: annotations,
		Registry:    registry,
	}, nil
}

func (c *corednsDep) Decode(params, into interface{}) (interface{}, error) {
	return c.ctx.FlowCC.TkeCC().CorednsCC().Deployment(params, into)
}
