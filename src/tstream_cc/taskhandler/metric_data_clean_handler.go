package taskHandler

import (
	"tencentcloud.com/tstream_galileo/src/common/configure"
	"tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/timer"
	"tencentcloud.com/tstream_galileo/src/common/util"
	cluster "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"time"
)

/**
 ** 每隔一个月清理前20天的数据
 */
type cleanMetricDataHandler struct {
	FirstClean   		bool
	BeginCleanTime 		string
	FixedDelay   		int64
	LastCleanTimestamp 	int64
}

func (this *cleanMetricDataHandler) Do() {
	threeDayTimestamp := int64(3*24*3600)
	tenDayTimestamp := int64(10*24*3600)
	timeLayout := "2006-01-02 15:04:05"
	loc, _ := time.LoadLocation("Local")
	tmp, _ := time.ParseInLocation(timeLayout, this.BeginCleanTime, loc)
	startTimestamp := tmp.Unix()
	var cutoffTime string
	currentTimeStamp := util.GetNowTimestamp() / 1000
	// 当前时间 减去 起始时间
	timestampMod := (currentTimeStamp - startTimestamp) % this.FixedDelay
	if timestampMod <= threeDayTimestamp && this.FirstClean == false{
		cutoffTimestamp := currentTimeStamp - timestampMod - tenDayTimestamp
		this.LastCleanTimestamp = cutoffTimestamp
		cutoffTime = time.Unix(cutoffTimestamp, 0).Format(timeLayout)
		err := cluster.CleanClusterMetrics(cutoffTime)
		if err != nil {
			logging.Errorf("Failed to clean cluster metrics because %+v", err)
		}
		this.FirstClean = true
	} else if timestampMod > threeDayTimestamp {
		logging.Debugf("The clean task is already run at:%d", this.LastCleanTimestamp)
		this.FirstClean = false
	} else {
		logging.Errorf("can not clean cluster metric data, the clean flag is true and timestampMod is bigger than %d, with start clean timestamp:%s", threeDayTimestamp, this.BeginCleanTime)
	}
}

func CleanMetricDataWithFixedDelay() {
	defaultTaskRunFixedDelay := 3600
	defaultCleanFixedDelay := 30*24*3600   //清理数据范围是60s以内的
	taskRunFixDelay := configure.GetConfInt64Value("component.properties","component.clusterMetric.cleanTask.runFixDelay",int64(defaultTaskRunFixedDelay))
	cleanFixDelay := configure.GetConfInt64Value("component.properties","component.clusterMetric.cleanTask.cleanFixDelay",int64(defaultCleanFixedDelay))
	beginCleanTime := configure.GetConfStringValueWithDefault("component.properties","component.clusterMetric.cleanTask.beginTime","2019-01-04 00:00:00")
	config := map[string]interface{}{}
	config["times"] = 0
	config["duration"] = taskRunFixDelay
	handler := &cleanMetricDataHandler{FirstClean: false,FixedDelay:cleanFixDelay,LastCleanTimestamp:-1,BeginCleanTime:beginCleanTime}
	task := timer.NewTask(config)
	task.Handler = handler
	task.Start()
}
