package taskHandler

import (
	"encoding/json"
	"fmt"
	"math"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service2 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	model3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	flow2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/flow"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job"
	job_autoscale2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job_autoscale"
	"time"
)

type doScalingHandler struct {
}

// 从 flow 的 clsParams 中获取 task context
func GetTaskContextFromParam(params map[string]string) (*job_autoscale2.AutoScaleContext, error) {
	flowService := flow2.GetFlowService()
	data, _, err := flowService.GetFlowParamString(params, constants.SCALE_PARAM_KEY_JOBS, "")
	if err != nil {
		return nil, err
	}
	context := &job_autoscale2.AutoScaleContext{}
	err = json.Unmarshal([]byte(data), context)
	if err != nil {
		return nil, err
	}
	return context, nil
}

func CreateOrGetStartTime(params map[string]string) (string, error) {
	flowService := flow2.GetFlowService()
	startTime, exists, err := flowService.GetFlowParamString(params, constants.SCALE_PARAM_START_TIME, util.GetCurrentTime())
	if err != nil {
		return "", err
	}
	if !exists {
		params[constants.SCALE_PARAM_START_TIME] = startTime
	}
	return startTime, nil
}

func (this *doScalingHandler) HandleTaskStatusInit(requestId string, request *flow.TaskExecRequest, task *job_autoscale2.ScalingTask) (resp *flow.TaskExecResponse) {
	flowService := flow2.GetFlowService()
	params := request.Params
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf("AutoScale - %s - doScalingHandler HandleTaskStatusInit failed for Job %s, Action %s, because %+v",
				task.Action.SerialId, task.Action.JobSerialId, task.Action.SerialId, err)
			resp = flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1, fmt.Sprintf("%v", err), params)
		}
	}()
	logger.Infof("AutoScale - %s - Begin to to restart Job %s, Action %s", task.Action.SerialId, task.Action.JobSerialId, task.Action.SerialId)
	if !task.CheckIfContinue(constants.SCALE_JOB_STATUS_FINISH_SAVEPOINT) {
		logger.Warningf("AutoScale - %s - Finish to do scaling for invalid flow status or error happened! Job %s, Action %s", task.Action.SerialId, task.Action.JobSerialId, task.Action.SerialId)
		return RunningRsp(params)
	}

	jobConfig, err := service2.GetJobConfigById(task.JobConfigId)
	if err != nil {
		logger.Errorf("AutoScale - %s - get jobconfig   failed,err %s", task.Action.SerialId, err)
		task.ChangeToFail(constants.SCALE_EVENT_TYPE_RESTART_JOB_FAILED)
		task.SaveStateToDB()
		return SuccessRsp(params)
	}
	properties := make([]*model3.Property, 0)
	err = json.Unmarshal([]byte(jobConfig.Properties), &properties)
	if err != nil {
		logger.Errorf("AutoScale - Unmarshal jobconfig failed,err %s", err)
		task.ChangeToFail(constants.SCALE_EVENT_TYPE_RESTART_JOB_FAILED)
		task.SaveStateToDB()
		return SuccessRsp(params)
	}
	flag1 := false
	flag2 := false
	for _, property := range properties {
		if property.Key == constants.YARN_NO_JOB_KILL_SEC {
			flag1 = true
		} else if property.Key == constants.JOB_MAX_PENDING_SECONDS {
			flag2 = true
		}
	}
	if !flag1 {
		properties = append(properties, &model3.Property{
			Key:   constants.YARN_NO_JOB_KILL_SEC,
			Value: strconv.Itoa(900),
		})
	}
	if !flag2 {
		properties = append(properties, &model3.Property{
			Key:   constants.JOB_MAX_PENDING_SECONDS,
			Value: strconv.Itoa(900),
		})
	}
	marshal, err := json.Marshal(properties)
	if err != nil {
		logger.Errorf("AutoScale - %s - marshal jobconfig failed,err %s", task.Action.SerialId, err)
		task.ChangeToFail(constants.SCALE_EVENT_TYPE_RESTART_JOB_FAILED)
		task.SaveStateToDB()
		return SuccessRsp(params)
	}
	jobConfig.Properties = string(marshal)
	txManager.GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		_, err := service.UpdateJobConfigProperties(0, 0, jobConfig.Id, jobConfig.Properties, tx)
		if err != nil {
			logger.Errorf("AutoScale - %s - update jobconfig properties failed, maybe restart over time ,err %s", task.Action.SerialId, err)
			return nil
		}
		return nil
	}).Close()
	if task.ScaleGranularity == constants.SCALEVERTEX {
		//graph, err := GetJobGraph(task)
		//if err != nil {
		//	logger.Errorf("AutoScale - get graph failed,err %s", err)
		//	if request.Retrycount > 3 {
		//		task.ChangeToFail(constants.SCALE_EVENT_TYPE_RESTART_JOB_FAILED)
		//		task.SaveStateToDB()
		//	}
		//	return RedoInitRsp(requestId, err, "", params)
		//}
		////s, _ := json.Marshal(graph)
		////baseOldGraph := base64.StdEncoding.EncodeToString(s)
		////if err != nil {
		////	logger.Errorf("AutoScale - encode failed,err %s", err)
		////	task.ChangeToFail(constants.SCALE_EVENT_TYPE_RESTART_JOB_FAILED)
		////	task.SaveStateToDB()
		////}
		//
		//scaleGraph, err := JobVertexScale(graph, float64(task.ActionDetail.Ratio), task.TargetParallelism, task.Action.ActionType)
		//if err != nil {
		//	logger.Errorf("AutoScale - scale vertex failed,err %s", err)
		//	task.ChangeToFail(constants.SCALE_EVENT_TYPE_RESTART_JOB_FAILED)
		//	task.SaveStateToDB()
		//	return SuccessRsp(params)
		//}
		//s1, _ := json.Marshal(scaleGraph)
		//logger.Debugf("AutoScale - scaled graph:%s", string(s1))
		err := service2.UpdateJobConfigJobGraph(task.JobConfigId, task.JobGraph, task.TargetJobGraph, task.Job.Id)
		if err != nil {
			logger.Errorf("AutoScale - %s - update jobconfig graph failed,err %s", task.Action.SerialId, err)
			task.ChangeToFail(constants.SCALE_EVENT_TYPE_RESTART_JOB_FAILED)
			task.SaveStateToDB()
			return SuccessRsp(params)
		}
		// 更新作业的资源配置
		// 这里更新jobconfig的TargetParallelism，会在finishCommand被覆盖
		if err := task.UpdateJobConfig(0, task.TargetJmCUSpec, task.TargetTmCUSpec, task.TargetJmCPU, task.TargetJmMem, task.TargetTmCPU, task.TargetTmMem); err != nil {
			logger.Errorf("AutoScale - %s - fail to update parallelism, cpu,mem for JobConfig %d! Job %s, Action %s, cause by %+v.", task.Action.SerialId, task.JobConfigId, task.Action.JobSerialId, task.Action.SerialId, err)
			task.ChangeToFail(constants.SCALE_EVENT_TYPE_MODIFY_RESOURCES_FAILED)
			task.SaveStateToDB()
			return SuccessRsp(params)
		}
		if err := task.DoRunJob(constants.JOB_RUN_TYPE_RESTART_WITH_SAVEPOINT, "", true); err != nil {
			//执行任务失败
			logger.Errorf("AutoScale -%s - failed to restart job! Job %s, Action %s, cause by %+v.", task.Action.SerialId, task.Action.JobSerialId, task.Action.SerialId, err)
			task.ChangeToFail(constants.SCALE_EVENT_TYPE_RESTART_JOB_FAILED)
			task.SaveStateToDB()
			return SuccessRsp(params)
		}
		task.SetJobStatus(constants.SCALE_JOB_STATUS_SUBMIT_RESTART_COMMAND)
		task.SaveStateToDB()
		return RunningRsp(params)
	} else {
		// todo else
		logger.Infof("AutoScale - %s - Begin to update job config, parallelism %d, JmCPU %f. JmMem %f,TmCPU %f, TmMem %f ! Job %s, Action %s, JobConfig %d.", task.Action.SerialId,
			task.TargetParallelism, task.TargetJmCPU, task.TargetJmMem, task.TargetTmCPU, task.TargetTmMem, task.Action.JobSerialId, task.Action.SerialId, task.JobConfigId)
		// 更新作业的资源配置
		if err := task.UpdateJobConfig(task.TargetParallelism, task.TargetJmCUSpec, task.TargetTmCUSpec, task.TargetJmCPU, task.TargetJmMem, task.TargetTmCPU, task.TargetTmMem); err != nil {
			logger.Errorf("AutoScale - %s - fail to update parallelism, cpu,mem for JobConfig %d! Job %s, Action %s, cause by %+v.", task.Action.SerialId, task.JobConfigId, task.Action.JobSerialId, task.Action.SerialId, err)
			task.ChangeToFail(constants.SCALE_EVENT_TYPE_MODIFY_RESOURCES_FAILED)
			task.SaveStateToDB()
			return SuccessRsp(params)
		}
		if jobConfig, err := task.GetCurrentJobConfig(); err != nil {
			logger.Errorf("AutoScale - %s - fail to get current job config for JobConfig %d! Job %s, Action %s, cause by %+v.", task.Action.SerialId, task.JobConfigId, task.Action.JobSerialId, task.Action.SerialId, err)
		} else {
			logger.Infof("AutoScale - %s - Updated parallelism %d, JmCuSpec %f, TmCuSpec %f ! Job %s, Action %s, JobConfig %d.", task.Action.SerialId,
				jobConfig.DefaultParallelism, jobConfig.JmCuSpec, jobConfig.TmCuSpec, task.Action.JobSerialId, task.Action.SerialId, task.JobConfigId)
		}

		logger.Infof("AutoScale - %s - Begin to to submit RUN_TYPE_RESTART_WITH_SAVEPOINT command for Job %s, Action %s", task.Action.SerialId, task.Action.JobSerialId, task.Action.SerialId)
		if err := task.DoRunJob(constants.JOB_RUN_TYPE_RESTART_WITH_SAVEPOINT, "", false); err != nil {
			//执行任务失败
			logger.Errorf("AutoScale - %s - failed to restart job! Job %s, Action %s, cause by %+v.", task.Action.SerialId, task.Action.JobSerialId, task.Action.SerialId, err)
			if request.Retrycount < 4 {
				// 尝试3次，因为前面的缩容不完成，这里的扩容可能DoRunJob一致失败，因为资源不足
				time.Sleep(60 * time.Second)
				return RedoInitRsp(requestId, err, "", params)
			}
			task.ChangeToFail(constants.SCALE_EVENT_TYPE_RESTART_JOB_FAILED)
			task.SaveStateToDB()
			// 启动作业失败，将jobconfig 还原。
			if err := task.UpdateJobConfig(int(task.DefaultParallelism), task.JmCuSpec, task.TmCuSpec, task.JmCPU, task.JmMem, task.TmCPU, task.TmMem); err != nil {
				// jar 作业并行度无法修改，所以之前这个 errcode 会将 rule 关闭
				logger.Errorf("AutoScale - %s - Fail to update parallelism, cpu ,mem to old version for JobConfig %d ! Job %s, Action %s, cause by %+v.", task.Action.SerialId, task.JobConfigId, task.Action.JobSerialId, task.Action.SerialId, err)
			}
			return SuccessRsp(params)
		}
		logger.Infof("AutoScale - %s - Succeeded to submit RUN_TYPE_RESTART_WITH_SAVEPOINT command! Job %s, Action %s", task.Action.SerialId, task.Action.JobSerialId, task.Action.SerialId)
		task.SetJobStatus(constants.SCALE_JOB_STATUS_SUBMIT_RESTART_COMMAND)
		task.SaveStateToDB()
		return RunningRsp(params)
	}
}

func (this *doScalingHandler) HandleTaskStatusRunning(requestId string, request *flow.TaskExecRequest, task *job_autoscale2.ScalingTask) (resp *flow.TaskExecResponse) {
	flowService := flow2.GetFlowService()
	params := request.Params
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf("AutoScale - %s - doScalingHandler HandleTaskStatusRunning failed for Job %s, Action %s, because %+v", task.Action.SerialId,
				task.Action.JobSerialId, task.Action.SerialId, err)
			resp = flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1, fmt.Sprintf("%v", err), params)
		}
	}()
	startTime, err := CreateOrGetStartTime(params)
	if err != nil {
		logger.Errorf("AutoScale - %s  -%s Fail to create start time cause by %+v", task.Action.SerialId, task.Action.JobSerialId, err)
		return RunningRsp(params)
	}

	if !task.CheckIfContinue(constants.SCALE_JOB_STATUS_SUBMIT_RESTART_COMMAND) {
		return SuccessRsp(params)
	}
	job, err := task.GetJobStatus()
	status := job.Status
	if err != nil {
		logger.Errorf("AutoScale - %s - Fail to get job in restart job! Job %s, Action %s, cause by %+v.", task.Action.SerialId, task.Action.JobSerialId, task.Action.SerialId, err)
		return RunningRsp(params)
	}
	_, isRecover := params["ERROR_CODE"]
	// 不需要在意jobConfig的并行度
	if task.ScaleGranularity == constants.SCALEVERTEX {
		if status == constants.JOB_STATUS_PROGRESS {
			if util.SubDate(startTime, util.GetCurrentTime()) >= 600 {
				logger.Warningf("AutoScale - %s - restart job timeout! Job %s, Action %s", task.Action.SerialId, task.Action.JobSerialId, task.Action.SerialId)
				task.Alarm()
			}
			logger.Infof("AutoScale  %s - waiting for job to restart! Job %s, Action %s", task.Action.SerialId, task.Action.JobSerialId, task.Action.SerialId)
			return RunningRsp(params)
		} else if status == constants.JOB_STATUS_RUNNING {
			//cluster, err := service4.GetClusterByClusterId(job.ClusterId)
			//if err != nil {
			//	task.EventErrorCode = constants.SCALE_EVENT_TYPE_RESTART_JOB_FAILED
			//	task.SaveStateToDB()
			//	return SuccessRsp(params)
			//}
			//url, err := service3.GetJobWebUIURLPrefix(job, cluster, task.ClusterGroup.SerialId)
			//logger.Debugf("AutoScale - restarted webui url:%s", url)
			//task.WebUiUrlPrefix = url
			jobInstance, err := service3.GetRunningJobInstanceByJobId(task.Job.Id)

			task.FlinkJobId = jobInstance.FlinkJobId
			if err != nil {
				task.EventErrorCode = constants.SCALE_EVENT_TYPE_RESTART_JOB_FAILED
				task.SaveStateToDB()
				return SuccessRsp(params)
			}
			runningGraph, err := GetJobGraph(task)
			if err != nil {
				logger.Errorf("AutoScale - %s - get graph failed,err %s", task.Action.SerialId, err)
				if request.Retrycount > 3 {
					task.EventErrorCode = constants.SCALE_EVENT_TYPE_RESTART_JOB_FAILED
					task.SaveStateToDB()
					return SuccessRsp(params)
				}
				return RunningRsp(params)
			}
			// 这里在FinishCommand已经设置了真正的并行度
			jobConfig, err := service2.GetJobConfigById(task.JobConfigId)
			if err != nil {
				logger.Errorf("AutoScale - %s - get jobconfig jobgraph failed,err %s", task.Action.SerialId, err)
				task.EventErrorCode = constants.SCALE_EVENT_TYPE_RESTART_JOB_FAILED
				task.SaveStateToDB()
				return SuccessRsp(params)
			}

			runningCPU, runningMem, _, err := task.GetJobRunningCPUAndMem()
			if err != nil {
				logger.Errorf("AutoScale - %s - get job running cpu and mem failed,err %s", task.Action.SerialId, err)
				task.EventErrorCode = constants.SCALE_EVENT_TYPE_RESTART_JOB_FAILED
				task.SaveStateToDB()
				return SuccessRsp(params)
			}
			// 用真正的并行度去算真正的cpu和mem
			targetRunningCpu := (float32)(int(jobConfig.DefaultParallelism)/task.Slot)*task.TargetTmCPU + task.TargetJmCPU
			targetRunningMem := (float32)(int(jobConfig.DefaultParallelism)/task.Slot)*task.TargetTmMem + task.TargetJmMem
			logger.Infof("AutoScale - %s - really target running cpu %f,mem %f ,running cpu %f,running mem %f", task.Action.SerialId, targetRunningCpu, targetRunningMem, runningCPU, runningMem)
			logger.Infof("AutoScale - %s - running job instance id %s", task.Action.SerialId, jobInstance.Id)
			// 还需要满足JobInstance不同
			if (service2.CompareJobGraph(runningGraph, jobConfig.AutoScaleJobGraph)) &&
				(service.AlmostEqual(runningCPU, targetRunningCpu)) &&
				(service.AlmostEqual(runningMem, targetRunningMem)) &&
				(jobInstance.Id != task.Action.JobInstanceId) {
				logger.Infof("AutoScale - %s - running job  graph %s equals job config graph %s  or jobinstance %d is equals %s", task.Action.SerialId, runningGraph, jobConfig.AutoScaleJobGraph, jobInstance.Id, task.Action.JobInstanceId)
				// 不需要更新
				// 成功启动
				task.SetJobStatus(constants.SCALE_JOB_STATUS_FINISH_RESTART_COMMAND)
				task.SaveStateToDB()
				return SuccessRsp(params)
			} else {
				// 需要还原JobConfigGraph
				logger.Warningf("AutoScale - %s - %s -  running job graph %s not equals job config graph %s", task.Action.SerialId, task.Job.SerialId, runningGraph, jobConfig.AutoScaleJobGraph)
				err := service2.UpdateJobConfigJobGraph(task.JobConfigId, runningGraph, runningGraph, task.Job.Id)
				if err != nil {
					logger.Errorf("AutoScale - recover job graph failed,err %s", err)
				}
				// 并行度设置在finishCommand
				if err := task.UpdateJobConfig(0, task.JmCuSpec, task.TmCuSpec, task.JmCPU, task.JmMem, task.TmCPU, task.TmMem); err != nil {
					logger.Errorf("AutoScale - %s - Fail to update parallelism, cuSpec to old version for JobConfig %d! Job %s, Action %s, cause by %+v.", task.Job.SerialId, task.JobConfigId, task.Action.JobSerialId, task.Action.SerialId, err)
				}
				task.EventErrorCode = constants.SCALE_EVENT_TYPE_MODIFY_RESOURCES_FAILED
				task.SaveStateToDB()
				return SuccessRsp(params)
			}
		} else if status == constants.JOB_STATUS_STOPPED || status == constants.JOB_STATUS_PAUSED || status == constants.JOB_STATUS_FINISHED {
			logger.Errorf("AutoScale - %s - Job status is stopped or paused, restart job now! Job %s, Action %s", task.Action.SerialId, task.Action.JobSerialId, task.Action.SerialId)

			if job.ExpectedStatus == constants.JOB_STATUS_STOPPED || job.ExpectedStatus == constants.JOB_STATUS_PAUSED || job.ExpectedStatus == constants.JOB_STATUS_FINISHED {
				logger.Infof("AutoScale - %s - Job expected status is stopped, skip restart job! Job %s, Action %s", task.Action.SerialId, task.Action.JobSerialId, task.Action.SerialId)
				task.EventErrorCode = constants.SCALE_EVENT_TYPE_RESTART_JOB_FAILED
				task.SaveStateToDB()
				return SuccessRsp(params)
			}
			// 一直拉
			if request.Retrycount < 3 {
				logger.Warningf("AutoScale - Job start failed,retry start job,retrycount:%d", request.Retrycount)
				err := task.DoRunJob(constants.JOB_RUN_TYPE_RUN_WITH_DEFAULT_SAVEPOINT, "", true)
				if err != nil {
					logger.Errorf("AutoScale - Job status is stopped or paused, But restart job failed! Job %s, Action %s, cause by %+v", task.Action.JobSerialId, task.Action.SerialId, err)
					return RunningRsp(params)
				}
				return RunningRsp(params)
			}
			// 按原配置重启
			logger.Infof("AutoScale - %s - recover job graph", task.Action.SerialId)
			err := service2.RecoverJobConfigJobGraph(task.JobConfigId, task.Job.Id)
			if err != nil {
				logger.Errorf("AutoScale - %s - recover job graph failed,err %s", task.Action.SerialId, err)
			}
			if err := task.UpdateJobConfig(0, task.JmCuSpec, task.TmCuSpec, task.JmCPU, task.JmMem, task.TmCPU, task.TmMem); err != nil {
				logger.Errorf("AutoScale - %s - Fail to update parallelism, cuSpec to old version for JobConfig %d! Job %s, Action %s, cause by %+v.", task.Action.SerialId, task.JobConfigId, task.Action.JobSerialId, task.Action.SerialId, err)
			}
			err = task.DoRunJob(constants.JOB_RUN_TYPE_RUN_WITH_DEFAULT_SAVEPOINT, "", true)
			if err != nil {
				logger.Errorf("AutoScale - %s - Job status is stopped or paused, But restart job failed! Job %s, Action %s, cause by %+v", task.Action.SerialId, task.Action.JobSerialId, task.Action.SerialId, err)
				return RunningRsp(params)
			}
			return RunningRsp(params)
		} else {
			logger.Errorf("AutoScale - %s - Unknown Job status %d. Job %s, Action %s", task.Action.SerialId, status, task.Action.JobSerialId, task.Action.SerialId)
			return RunningRsp(params)
		}
	} else {
		if status == constants.JOB_STATUS_PROGRESS {
			if util.SubDate(startTime, util.GetCurrentTime()) >= 600 {
				logger.Warningf("AutoScale- %s - restart job timeout! Job %s, Action %s", task.Action.SerialId, task.Action.JobSerialId, task.Action.SerialId)
				task.Alarm()
			}
			logger.Infof("AutoScale - %s - waiting for job to restart! Job %s, Action %s", task.Action.SerialId, task.Action.JobSerialId, task.Action.SerialId)
			return RunningRsp(params)
		} else if status == constants.JOB_STATUS_RUNNING && !isRecover {
			logger.Infof("AutoScale - %s - Job restarted! Job %s, Action %s", task.Action.SerialId, task.Action.JobSerialId, task.Action.SerialId)

			jobInstance, err := service3.GetRunningJobInstanceByJobId(task.Job.Id)
			logger.Infof("AutoScale - %s - running job instance id %s", task.Action.SerialId, jobInstance.Id)

			// 获取 cu 数，判断重启后作业的 cu 数是否与调优后的 cu 数相同
			runningCPU, runningMem, cuNum, err := task.GetJobRunningCPUAndMem()
			// 并行度/slot需要向上取整
			if err != nil || (!service.AlmostEqual(runningCPU, task.TargetRunningCPU) ||
				!service.AlmostEqual(runningMem, task.TargetRunningMem) ||
				cuNum != int16(math.Floor(float64(task.TargetParallelism)/float64(task.Slot))+0.5)) ||
				jobInstance.Id == task.Action.JobInstanceId {
				logger.Infof("AutoScale - %s - job:%s The real task resource config is not modified! Current running cpu is %f mem is %f, target running cpu is %f mem is %f，CuNum:%d, targetCuNum:%d", task.Action.SerialId, task.Job.SerialId, runningCPU, runningMem, task.TargetRunningCPU, task.TargetRunningMem, cuNum, int16(math.Floor(float64(task.TargetParallelism)/float64(task.Slot))+0.5))
				if util.SubDate(startTime, util.GetCurrentTime()) < 600 && jobInstance.Id != task.Action.JobInstanceId {
					logger.Debugf("AutoScale - %s - restart job done,but resource may be not renew in time! Job %s, Action %s", task.Action.SerialId, task.Action.JobSerialId, task.Action.SerialId)
					// 等待一下资源更新
					return RunningRsp(params)
				}
				logger.Errorf("AutoScale - %s - job:%s The real task resource config is maybe not modified, falling back to the previous resource config! Job %s, Action %s, because %+v", task.Action.SerialId, task.Job.SerialId, task.Action.JobSerialId, task.Action.SerialId, err)
				logger.Errorf("AutoScale - %s - restart job failed!maybe job %s jobinstance id not changed %s .", task.Action.SerialId, task.Job.SerialId, jobInstance.Id)
				if err := task.UpdateJobConfig(int(task.DefaultParallelism), task.JmCuSpec, task.TmCuSpec, task.JmCPU, task.JmMem, task.TmCPU, task.TmMem); err != nil {
					// jar 作业并行度无法修改，所以之前这个 errcode 会将 rule 关闭
					logger.Errorf("AutoScale - %s - Fail to update parallelism, cpu,mem to old version for JobConfig %d ! Job %s, Action %s, cause by %+v.", task.Action.SerialId, task.JobConfigId, task.Action.JobSerialId, task.Action.SerialId, err)
				}
				task.ChangeToFail(constants.SCALE_EVENT_TYPE_MODIFY_RESOURCES_FAILED)
				task.SaveStateToDB()
				return SuccessRsp(params)
			}
			logger.Infof("AutoScale - %s - Restart job succeeded! Job %s, Action %s", task.Action.SerialId, task.Action.JobSerialId, task.Action.SerialId)
			task.SetJobStatus(constants.SCALE_JOB_STATUS_FINISH_RESTART_COMMAND)
			task.SaveStateToDB()
			return SuccessRsp(params)
		} else if status == constants.JOB_STATUS_RUNNING && isRecover {
			// 任务重启失败，但是 使用 savepoint 恢复成功了
			logger.Infof("AutoScale - %s - Job restart failed but has been restored successfully! Job %s, Action %s", task.Action.SerialId, task.Action.JobSerialId, task.Action.SerialId)
			task.EventErrorCode = constants.SCALE_EVENT_TYPE_RESTART_JOB_FAILED
			task.SaveStateToDB()
			return SuccessRsp(params)
		} else if status == constants.JOB_STATUS_STOPPED || status == constants.JOB_STATUS_PAUSED || status == constants.JOB_STATUS_FINISHED {
			logger.Errorf("AutoScale - %s - Job status is stopped or paused, restart job now! Job %s, Action %s", task.Action.SerialId, task.Action.JobSerialId, task.Action.SerialId)

			if job.ExpectedStatus == constants.JOB_STATUS_STOPPED || job.ExpectedStatus == constants.JOB_STATUS_PAUSED || job.ExpectedStatus == constants.JOB_STATUS_FINISHED {
				logger.Infof("AutoScale - %s - Job expected status is stopped, skip restart job! Job %s, Action %s", task.Action.SerialId, task.Action.JobSerialId, task.Action.SerialId)
				task.EventErrorCode = constants.SCALE_EVENT_TYPE_RESTART_JOB_FAILED
				task.SaveStateToDB()
				return SuccessRsp(params)
			}

			// 尝试重启作业
			if request.Retrycount < 3 {
				//time.Sleep(60 * time.Second)
				logger.Warningf("AutoScale - Job start failed,retry start job,retrycount:%d", request.Retrycount)
				err := task.DoRunJob(constants.JOB_RUN_TYPE_RUN_WITH_DEFAULT_SAVEPOINT, "", false)
				if err != nil {
					logger.Errorf("AutoScale - %s - Job status is stopped or paused, But restart job failed! Job %s, Action %s, cause by %+v", task.Action.SerialId, task.Action.JobSerialId, task.Action.SerialId, err)
					return RunningRsp(params)
				}
				return RunningRsp(params)
			}
			logger.Errorf("AutoScale - %s - Job retry count max, recover job config and start! Job %s, Action %s.", task.Action.SerialId, task.Action.JobSerialId, task.Action.SerialId)
			if err := task.UpdateJobConfig(int(task.DefaultParallelism), task.JmCuSpec, task.TmCuSpec, task.JmCPU, task.JmMem, task.TmCPU, task.TmMem); err != nil {
				logger.Errorf("AutoScale - %s - Fail to update parallelism, cuSpec to old version for JobConfig %d! Job %s, Action %s, cause by %+v.", task.Action.SerialId, task.JobConfigId, task.Action.JobSerialId, task.Action.SerialId, err)
			}
			err := task.DoRunJob(constants.JOB_RUN_TYPE_RUN_WITH_DEFAULT_SAVEPOINT, "", false)
			if err != nil {
				logger.Errorf("AutoScale - %s - Job status is stopped or paused, But restart job failed! Job %s, Action %s, cause by %+v", task.Action.SerialId, task.Action.JobSerialId, task.Action.SerialId, err)
				return RunningRsp(params)
			}
			// 一直拉
			params["ERROR_CODE"] = constants.SCALE_EVENT_TYPE_RESTART_JOB_FAILED
			return RunningRsp(params)
		} else {
			logger.Errorf("AutoScale - %s - Unknown Job status %d. Job %s, Action %s", task.Action.SerialId, status, task.Action.JobSerialId, task.Action.SerialId)
			return RunningRsp(params)
		}
	}
}

//func JobVertexScale(oldJobGraph string, radio float64, parallelism int, actionType string) (*table.Job, error) {
//	oldFlinkJobVertice := &table.Job{}
//	err := json.Unmarshal([]byte(oldJobGraph), oldFlinkJobVertice)
//	if err != nil {
//		logger.Errorf("AutoScale - umarshal jobgraph failed,err %s", err)
//		return nil, err
//	}
//
//	sources, sinks := IdentifySourceAndSink(oldFlinkJobVertice)
//	sourceSinkIDs := make(map[string]bool)
//	logger.Debugf("AutoScale - job graph srouce vertex %+v,sink vertex %+v", sources, sinks)
//	// 标记 Source/Sink 节点
//	for _, n := range append(sources, sinks...) {
//		sourceSinkIDs[n.Id] = true
//	}
//	for i := range oldFlinkJobVertice.Nodes {
//		v := &oldFlinkJobVertice.Nodes[i]
//		if sourceSinkIDs[v.Id] {
//			logger.Debugf("AutoScale - is sink or source vertex %s", v.Id)
//			v.Parallelism = v.Parallelism
//		} else {
//			logger.Debugf("AutoScale - is not sink or source vertex %s", v.Id)
//			if actionType == constants.SCALE_ACTION_TYPE_TASK_FLEXIBLE_TYPE {
//				v.Parallelism = parallelism
//			} else {
//				v.Parallelism = int(math.Ceil(float64(v.Parallelism) * radio))
//			}
//		}
//	}
//	return oldFlinkJobVertice, nil
//}

func GetJobGraph(task *job_autoscale2.ScalingTask) (string, error) {
	instance, err := service3.GetRunningJobInstance(task.Job.Id)
	if err != nil {
		//logger.Errorf("AutoScale - get running jobinstance failed,err %s", err)
		return "", err
	}
	plan := instance.FlinkJobPlan
	return plan, nil
	//graph := &table.FlinkJobVertice{}
	//err = json.Unmarshal([]byte(plan), graph)
	//if err != nil {
	//	logger.Errorf("AutoScale - unmrshal graph failed,err %s", err)
	//	return nil, err
	//}
	//return graph, nil
}

//func IdentifySourceAndSink(job *table.Job) (sources, sinks []*table.Node) {
//	// 收集所有被引用的节点ID（用于识别 Sink）
//	referencedIDs := make(map[string]bool)
//	for _, node := range job.Nodes {
//		for _, input := range node.Inputs {
//			referencedIDs[input.ID] = true
//		}
//	}
//
//	// 遍历节点进行分类
//	for i := range job.Nodes {
//		node := &job.Nodes[i]
//		// Source: 无输入
//		if len(node.Inputs) == 0 {
//			sources = append(sources, node)
//		}
//		// Sink: 未被其他节点引用
//		if !referencedIDs[node.Id] {
//			sinks = append(sinks, node)
//		}
//	}
//	return
//}

//func compare(s1, s2 string) bool {
//	// 1. 处理空指针情况
//	if s1 == "" && s2 == "" {
//		return true
//	}
//	a := &table.Job{}
//	b := &table.Job{}
//	err := json.Unmarshal([]byte(s1), a)
//	if err != nil {
//		logger.Errorf("AutoScale unmarshal failed,%s", err)
//		return false
//	}
//	err = json.Unmarshal([]byte(s2), b)
//	if err != nil {
//		logger.Errorf("AutoScale unmarshal failed,%s", err)
//		return false
//	}
//
//	// 2. 比较 Nodes 数量
//	if len(a.Nodes) != len(b.Nodes) {
//		return false
//	}
//
//	// 3. 逐个 Node 对比指定字段
//	// 创建节点映射（ID → Node）
//	nodeMap := make(map[string]table.Node)
//	for _, node := range a.Nodes {
//		nodeMap[node.Id] = node
//	}
//
//	// 检查每个节点是否存在且匹配
//	for _, nodeB := range b.Nodes {
//		nodeA, exists := nodeMap[nodeB.Id]
//		if !exists ||
//			nodeA.Description != nodeB.Description ||
//			nodeA.Parallelism != nodeB.Parallelism {
//			return false
//		}
//	}
//
//	return true
//}
