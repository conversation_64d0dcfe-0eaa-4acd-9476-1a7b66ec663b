package taskHandler

import (
	"encoding/json"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"testing"
)

func TestDeleteEmrHandler_CompleteTask(t *testing.T) {
	request := &flow.TaskExecRequest{
		Retrycount: 0,
		Processkey: "test",
		Taskcode:   "test",
		DocId:      "1",
		FlowId:     "1",
		TaskId:     "1",
		Params: map[string]string{
			constants.FLOW_PARAM_CLUSTER_GROUP_ID: *fTestClusterGroupId,
			constants.FLOW_PARAM_REQUEST_ID:       "test_hahaha",
		},
	}

	handler := &deleteEmrHandler{}
	rsp := handler.CompleteTask(request)
	b, _ := json.MarshalIndent(rsp, "", "")
	t.Logf("response:\n%s", string(b))
}
