package taskHandler

import (
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource_auth"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type StartSetatsWarehouseBase struct {
}

func (c StartSetatsWarehouseBase) Apps(ctx *deploy.Context) ([]apps.App, error) {
	setatsCC := ctx.FlowCC.SetatsCC()
	result := []apps.App{
		&createServiceAccountApp{ctx: ctx, namespace: "default", name: "setats-sa"},
		apps.NewClusterRole(setatsCC.ClusterRole),
		apps.NewClusterRoleBinding(setatsCC.ClusterRoleBinding),
		&createSetatsStorageClassApp{ctx: ctx},
		&setatsOperator{ctx: ctx},
		&bindSetatsTagsApp{ctx: ctx},
	}
	deployFilebeatService := &DeployFilebeatService{Type: FilebeatForSetats}
	filebeatApps, err := deployFilebeatService.Apps(ctx)
	if err != nil {
		logger.Errorf("failed to get filebeatApps, error %v", err)
		return result, err
	}
	result = append(result, filebeatApps...)
	return result, nil
}

type bindSetatsTagsApp struct {
	ctx *deploy.Context
	apps.ApplyApp
}

func (c *bindSetatsTagsApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	err = bindTags(c.ctx.Request.Params, c.ctx.ClusterGroup, resource_auth.RESOURCE_PREFIX_SETATS)
	if err != nil {
		logger.Errorf("Failed to bind tags for setats, with errors:%+v", err)
	}
	return nil, err
}

type createSetatsStorageClassApp struct {
	apps.StorageClass
	ctx *deploy.Context
}

func (c *createSetatsStorageClassApp) Params() (interface{}, error) {
	return nil, nil
}

func (c *createSetatsStorageClassApp) Decode(params, into interface{}) (interface{}, error) {
	return c.ctx.FlowCC.SetatsCC().StorageClass(into)
}

type setatsOperator struct {
	ctx *deploy.Context
	apps.Deployment
}

func (c *setatsOperator) Params() (interface{}, error) {
	appContainerImage, err := c.ctx.CC().ImageRegistry().SetatsOperator()
	if err != nil {
		return nil, err
	}

	base, err := c.ctx.ParamBase(constants.K8S_KIND_DEPLOYMENT,
		constants.DEFAULT_NAMESPACE,
		constants.ComponentSetatsOperator, false)
	if err != nil {
		return nil, err
	}
	base.NodeSelector[constants.TKE_CVM_LABEL_KEY] = constants.SETATS_MANAGER_LABEL
	base.AppImage = appContainerImage
	return base, nil
}

func (c *setatsOperator) Decode(params, into interface{}) (interface{}, error) {
	return c.ctx.FlowCC.SetatsCC().SetatsOperator(params, into)
}
