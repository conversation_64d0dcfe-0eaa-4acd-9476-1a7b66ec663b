package taskHandler

import (
	"fmt"
	"runtime/debug"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	flow2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/flow"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/job_autoscale"
)

func RunningRsp(params map[string]string) *flow.TaskExecResponse {
	flowService := flow2.GetFlowService()
	return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_IN_PROCESS, flow.TASK_STATUS_RUNNING, 0.5, "OK", params)
}

func SuccessRsp(params map[string]string) *flow.TaskExecResponse {
	flowService := flow2.GetFlowService()
	logger.Debugf("return success rsp %+v", params)
	return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_SUCCESS, flow.TASK_STATUS_SUCCESS, 1.0, "ok", params)
}

func RedoInitRsp(requestId string, err error, msg string, params map[string]string) *flow.TaskExecResponse {
	flowService := flow2.GetFlowService()
	if err != nil {
		logger.Errorf("%s redo init task,cause by %+v", requestId, err)
		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_IN_PROCESS, flow.TASK_STATUS_INIT, 0.5, err.Error(), params)
	} else {
		logger.Errorf("%s redo init task ,msg: %s", msg)
		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_IN_PROCESS, flow.TASK_STATUS_INIT, 0.5, msg, params)
	}
}

func NewAutoScaleBaseHandler(handler autoScaleTaskStatusHandler) *autoScaleBaseHandler {
	return &autoScaleBaseHandler{taskStatusHandler: handler}
}

type autoScaleBaseHandler struct {
	taskStatusHandler autoScaleTaskStatusHandler
}

type autoScaleTaskStatusHandler interface {
	HandleTaskStatusInit(requestId string, request *flow.TaskExecRequest, task *job_autoscale.ScalingTask) (resp *flow.TaskExecResponse)
	HandleTaskStatusRunning(requestId string, request *flow.TaskExecRequest, task *job_autoscale.ScalingTask) (resp *flow.TaskExecResponse)
}

func (this *autoScaleBaseHandler) CompleteTask(request *flow.TaskExecRequest) (resp *flow.TaskExecResponse) {
	flowService := flow2.GetFlowService()

	params := request.Params
	requestId, _, _ := flowService.GetFlowParamString(params, constants.FLOW_PARAM_REQUEST_ID, "")

	defer func() {
		if err := recover(); err != nil {
			logger.Infof("AutoScale - [%s] Failed to complete task for err %v", requestId, debug.Stack())
			resp = flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1, fmt.Sprintf("%v", err), params)
		}
	}()

	taskStatusHandler := this.taskStatusHandler
	if taskStatusHandler == nil {
		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1, "taskStatusHandler is nil", params)
	}
	//1. 获取Task
	context, err := GetTaskContextFromParam(params)
	if err != nil {
		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1, "context is nil", params)
	}
	v := context.GetTask()
	if v == nil {
		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1, "task is nil", params)
	}
	//2. 获取状态
	v.GetStateFromDB()

	status, err := flowService.GetTaskStatus(request)
	if err != nil {
		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1, err.Error(), params)
	}

	switch status {
	case flow.TASK_STATUS_SUCCESS:
		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_SUCCESS, flow.TASK_STATUS_SUCCESS, 0.1, "ok", params)
	case flow.TASK_STATUS_RUNNING:
		return taskStatusHandler.HandleTaskStatusRunning(requestId, request, v)
	case flow.TASK_STATUS_INIT:
		return taskStatusHandler.HandleTaskStatusInit(requestId, request, v)
	case flow.TASK_STATUS_FAILED:
		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1, "", params)
	default:
		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1, "", params)
	}
}
