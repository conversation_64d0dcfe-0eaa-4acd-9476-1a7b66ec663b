package taskHandler

import (
	"encoding/base64"
	"encoding/json"
	errors2 "errors"
	"fmt"

	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	constants2 "tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	service4 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	flow2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/flow"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/tke"

	common "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/errors"
	profile "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/profile"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	tke_local "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke"
)

type EnableEniIpReserve struct {
}

const (
	EniMaxWarmTarget string = "5"
	EniMinWarmTarget string = "5"

	AddonName = "eniipamd"
	Succeeded = "Succeeded"

	vpcCni    = "vpcCni"
	directEni = "directEni"

	eniMaxWarmTarget string = "eniMaxWarmTarget"
	eniMinWarmTarget string = "eniMinWarmTarget"

	EndPoint = "tke.tencentcloudapi.com"

	EniReserveMaxNumParamName = "EniReserveMaxNum"
	EniReserveMinNumParamName = "EniReserveMinNum"
)

func (h *EnableEniIpReserve) enableEniIpReserve(requestId string, request *flow.TaskExecRequest) (resp *flow.TaskExecResponse) {
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf("[%s] EnableEniIpReserve task failed because: %v", requestId, err)
			resp = &flow.TaskExecResponse{
				RetCode:  flow.TASK_FAIL,
				Progress: flow.TASK_STATUS_FAILED,
				Err:      errors2.New(fmt.Sprintf("EnableEniIpReserve task panic with error %v", err)),
				Params:   request.Params,
			}
		}
	}()
	s, err := flow2.NewClusterFlowService(request)
	if err != nil {
		return s.RetryRspWithErr(err)
	}
	if s.ClusterType == constants2.K8S_CLUSTER_TYPE_EKS {
		return s.DoneRsp("eks is not support enable eni ip reserve")
	}

	if s.ClusterGroup.NetEnvironmentType == constants2.NETWORK_ENV_INNER_VPC {
		return s.DoneRsp("inner cluster is not support enable eni ip reserve")
	}
	secretID, secretKey, err := service4.GetSecretIdAndKeyByNetworkEnvType(s.ClusterGroup.NetEnvironmentType)
	if err != nil {
		return s.RetryRspWithErr(errorcode.NewStackError(errorcode.InternalErrorCode, "get secretID and secretKey failed", err))
	}

	// query tke status
	addonList, err := h.DescribeTkeAddons(secretID, secretKey, s.Region, s.Cluster.UniqClusterId, AddonName)
	if err != nil {
		logger.Errorf("requestId: %v, DescribeTkeAddons failed, err: %v", addonList.Response.RequestId, err)
		return s.RetryRsp("DescribeTkeAddons failed")
	}
	if addonList.Response == nil {
		return s.RetryRsp("DescribeTkeAddons response nil")
	}
	if len(addonList.Response.Addons) == 0 {
		return s.RetryRsp("DescribeTkeAddons AddonList is empty")
	}

	// retry if task is updating
	if updateDone := *addonList.Response.Addons[0].Phase == Succeeded; !updateDone {
		return s.RetryRsp("Task is updating eniMaxWarmTarget and eniMinWarmTargetInt")
	}

	// check if eniMaxWarmTarget and eniMinWarmTargetInt is excpetation
	response, err := h.DescribeTkeAddonsValues(s.Cluster.UniqClusterId, secretID, secretKey, s.Region)
	if err != nil {
		logger.Errorf("requestId: %v, DescribeTkeAddonsValues failed, err: %v", response.Response.RequestId, err)
		return s.RetryRsp("DescribeTkeAddonsValues failed")
	}

	valuesMap := make(map[string]interface{})
	err = json.Unmarshal([]byte(*response.Response.Values), &valuesMap)
	if err != nil {
		logger.Errorf("requestId: %v, values json Unmarshal failed, err: %v", response.Response.RequestId, err)
		return s.RetryRsp("values json Unmarshal failed")
	}

	var match bool
	var finalEniMaxNum = EniMaxWarmTarget
	var finalEniMinNum = EniMinWarmTarget
	if val, ok := request.Params[EniReserveMaxNumParamName]; ok && val != "" {
		finalEniMaxNum = val
	}
	if val, ok := request.Params[EniReserveMinNumParamName]; ok && val != "" {
		finalEniMinNum = val
	}
	if vpcCni, ok := valuesMap[vpcCni].(map[string]interface{}); ok {
		if directEni, ok := vpcCni[directEni].(map[string]interface{}); ok {
			maxCheck := directEni[eniMaxWarmTarget].(string) == finalEniMaxNum
			minCheck := directEni[eniMinWarmTarget].(string) == finalEniMinNum
			match = maxCheck && minCheck
		} else {
			logger.Errorf("requestId: %v, directEni json Unmarshal failed, err: %v", response.Response.RequestId, err)
			return s.RetryRsp("values json Unmarshal failed")
		}
	} else {
		logger.Errorf("requestId: %v, vpcCni json Unmarshal failed, err: %v", response.Response.RequestId, err)
		return s.RetryRsp("values json Unmarshal failed")
	}

	// update eniMaxWarmTarget and eniMinWarmTargetInt if not match
	if !match {
		if vpcCni, ok := valuesMap[vpcCni].(map[string]interface{}); ok {
			if directEni, ok := vpcCni[directEni].(map[string]interface{}); ok {
				directEni[eniMaxWarmTarget] = finalEniMaxNum
				directEni[eniMinWarmTarget] = finalEniMinNum
			}
		}

		jsonData, err := json.Marshal(valuesMap)
		if err != nil {
			logger.Errorf("requestId: %v, edited values json.Marshal failed, err: %v", response.Response.RequestId, err)
			return s.RetryRsp("edit values json.Marshal failed")
		}

		base64Data := base64.StdEncoding.EncodeToString(jsonData)
		resp, err := h.UpdateAddon(s.Cluster.UniqClusterId, AddonName, secretID, secretKey, s.Region, base64Data)
		if err != nil {
			logger.Errorf("requestId: %v, UpdateAddon failed, err: %v", resp.Response.RequestId, err)
			return s.RetryRsp("UpdateAddon failed")
		}

		logger.Infof("requestId: %v, UpdateAddon done", resp.Response.RequestId)
		return s.RetryRsp("UpdateAddon done")
	}

	return flow2.GetFlowService().NewTaskExecResponseWithParamsAndStatus(flow.TASK_SUCCESS, flow.TASK_STATUS_SUCCESS, 0.1, "in process", request.Params)
}

func (h *EnableEniIpReserve) IsTkeRunning(clusterGroup *table.ClusterGroup, tkeInstanceId string) (bool, error) {
	secretId, secretKey, err := service4.GetSecretIdAndKeyByNetworkEnvType(clusterGroup.NetEnvironmentType)
	if err != nil {
		return false, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	tkeService := tke.GetTkeService()
	totalCount, clusters, err := tkeService.DescribeClusters(
		secretId, secretKey, "", clusterGroup.Region,
		tkeService.NewDefaultDescribeClustersRequestBuilder().WithClusterIds(tkeInstanceId).Build())
	if err != nil {
		return false, errorcode.InternalErrorCode.NewWithErr(err)
	}
	if totalCount != 1 {
		msg := fmt.Sprintf("tke instance count %d for instanceId %s", totalCount, tkeInstanceId)
		return false, errorcode.InternalErrorCode.NewWithInfo(msg, err)
	}
	if *clusters[0].ClusterStatus == constants2.TKE_STATUS_ABNORMAL_STR {
		msg := fmt.Sprintf("tke cluster %s is in abnormal status", tkeInstanceId)
		return false, errorcode.InternalErrorCode.NewWithInfo(msg, err)
	}
	if *clusters[0].ClusterStatus != constants2.TKE_STATUS_RUNNING_STR {
		return false, nil
	}
	return true, nil
}

func (h *EnableEniIpReserve) DescribeTkeAddons(SecretId, SecretKey, region, clusterId, addonName string) (response *tke_local.DescribeAddonResponse, err error) {
	credential := common.NewCredential(
		SecretId, SecretKey,
	)
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = EndPoint
	client, _ := tke_local.NewClient(credential, region, cpf)

	request := tke_local.NewDescribeAddonRequest()
	request.ClusterId = common.StringPtr(clusterId)
	request.AddonName = common.StringPtr(addonName)

	resp, err := client.DescribeAddon(request)
	if _, ok := err.(*errors.TencentCloudSDKError); ok {
		logger.Errorf("An API error has returned: %s\n", err)
		return nil, err
	}
	return resp, nil
}

func (h *EnableEniIpReserve) DescribeTkeAddonsValues(clusterID, secretID, secretKey, region string) (response *tke_local.DescribeAddonValuesResponse, err error) {
	credential := common.NewCredential(
		secretID, secretKey,
	)
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = EndPoint
	client, _ := tke_local.NewClient(credential, region, cpf)

	request := tke_local.NewDescribeAddonValuesRequest()
	request.ClusterId = common.StringPtr(clusterID)
	request.AddonName = common.StringPtr(AddonName)

	resp, err := client.DescribeAddonValues(request)
	if _, ok := err.(*errors.TencentCloudSDKError); ok {
		logger.Errorf("An API error has returned: %s\n", err)
		return nil, err
	}
	return resp, nil
}

func (h *EnableEniIpReserve) UpdateAddon(clusterID, addonName, secretID, secretKey, region, rawValues string) (response *tke_local.UpdateAddonResponse, err error) {
	credential := common.NewCredential(
		secretID, secretKey,
	)
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = EndPoint
	client, _ := tke_local.NewClient(credential, region, cpf)

	request := tke_local.NewUpdateAddonRequest()
	request.ClusterId = common.StringPtr(clusterID)
	request.AddonName = common.StringPtr(addonName)
	request.RawValues = common.StringPtr(rawValues)

	resp, err := client.UpdateAddon(request)
	if _, ok := err.(*errors.TencentCloudSDKError); ok {
		logger.Errorf("An API error has returned: %s\n", err)
		return nil, err
	}

	return resp, nil
}
