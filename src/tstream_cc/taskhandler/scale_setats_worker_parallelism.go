package taskHandler

import (
	"fmt"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/uuid"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type ScaleSetatsWorkerParallelism struct {
}

func (c ScaleSetatsWorkerParallelism) Apps(ctx *deploy.Context) ([]apps.App, error) {
	requestId := fmt.Sprintf("ScaleSetatsWorkerParallelism-%s-%s", uuid.NewRandom().String(), ctx.ClusterGroup.SerialId)
	logger.Infof("ScaleSetatsWorkerParallelism Apps, requestId:%s", requestId)
	result := make([]apps.App, 0)
	if _, exists := ctx.GetParam(constants.FLOW_PARAM_SETATS_SCALE_WORKER, ""); exists {
		result = append(result, &scaleWorkerParallelism{ctx: ctx, requestId: requestId})
	}
	return result, nil
}
