package taskHandler

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/dynamic"
	"path/filepath"
	"sigs.k8s.io/yaml"
	"strconv"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/uuid"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	model1 "tencentcloud.com/tstream_galileo/src/tstream_cc/model"
	model "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/bucket"
	model5 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/job_config"
	model4 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/resource_config"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	setats2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/setats"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service_bucket "tencentcloud.com/tstream_galileo/src/tstream_cc/service/bucket"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/configuration_center"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cos"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
	"time"
)

type StartSetatsService struct {
}

func (c StartSetatsService) Apps(ctx *deploy.Context) ([]apps.App, error) {
	return []apps.App{
		&setats{ctx: ctx, needManagerUrl: false}, //第一次部署 没有弹性ip
		&setats{ctx: ctx, needManagerUrl: true},  // 第二次部署 获取弹性ip
	}, nil
}

type switchSetatsWarehouseStatusApp struct {
	apps.ApplyApp
	ctx    *deploy.Context
	status int8
}

func (c *switchSetatsWarehouseStatusApp) Apply(_ apps.Client, _ interface{}) (interface{}, error) {
	err := service.SwitchSetatsWarehouseStatusTo(c.ctx.ClusterGroup.SerialId, c.status)
	if err != nil {
		logger.Errorf("Failed to switch setats warehouse status to %d, with errors:%+v", c.status, err)
		return nil, err
	}
	return nil, nil
}

type setats struct {
	ctx *deploy.Context
	apps.ApplyReadyApp
	needManagerUrl bool
}

func DefaultNamespace(cg *table.ClusterGroup) string {
	result := constants.DEFAULT_NAMESPACE
	if cg.AgentSerialId != "" {
		result = fmt.Sprintf("oceanus-%s", cg.SerialId)
	}
	return result
}

func (c *setats) GetSetatsManagerEniIp() (string, error) {

	// 获取弹性网卡ip，设置webui地址.
	// 获取Deployment所关联的Pod信息
	namespace := DefaultNamespace(c.ctx.ClusterGroup)

	pods, err := c.ctx.ClientSet().CoreV1().Pods(namespace).List(metav1.ListOptions{
		LabelSelector: "app==setats,component==manager",
	})
	if err != nil {
		return "", errorcode.FailedOperationCode.NewWithErr(err)
	}
	logger.Infof("### pods is %+v", pods)
	ip := ""
	for _, pod := range pods.Items {
		logger.Infof("Pod Name: %s\n", pod.Name)
		// 可以根据需要输出更多的Pod信息
		// 例如：pod.Status.Phase, pod.Status.StartTime, pod.Status.PodIP 等
		// 可以根据需要进一步操作Pod，例如获取日志、删除Pod等
		/**
		TKE tke.cloud.tencent.com/networks-status
		[{
			"name": "tke-bridge",
			"interface": "eth0",
			"ips": ["**********"],
			"mac": "fe:dd:b6:6b:f7:29",
			"default": true,
			"dns": {}
		}, {
			"name": "tke-direct-eni",
			"interface": "eth1",
			"ips": ["*************"],
			"mac": "20:90:6F:4F:ED:C5",
			"dns": {}
		}, {
			"name": "tke-route",
			"ips": ["*******"],
			"dns": {}
		}]

		EKS tke.cloud.tencent.com/attached-cross-tenant-eni

		{"eniID":"eni-dat2gtio","mac":"20:90:6F:40:07:82","primaryIP":"***********","subnetCIDR":"**********/24","attachedInsID":"eks-c2eqwv46"}
		*/
		if !strings.Contains(pod.Name, constants.SetatsManager) {
			continue
		}

		strNetworksStatus, exist := pod.Annotations["tke.cloud.tencent.com/networks-status"]
		if !exist {
			errMsg := fmt.Sprintf("Pod %s can't find tke.cloud.tencent.com/networks-status, please check.", pod.Name)
			logger.Errorf(errMsg)
			return "", errorcode.UnsupportedOperationCode.ReplaceDesc(errMsg)
		}
		logger.Infof("tke.cloud.tencent.com/networks-status info is %s", strNetworksStatus)
		ip, err = ParseIp(strNetworksStatus)
		if err != nil {
			return "", errorcode.FailedOperationCode.NewWithErr(err)
		}
	}
	if ip == "" {
		return "", errorcode.UnsupportedOperationCode.ReplaceDesc("setats manager eni ip is empty")
	}
	return ip, nil
}

func ParseIp(strNetworksStatus string) (string, error) {
	ip := ""
	networksStatusList := make([]map[string]interface{}, 0, 0)
	err := json.Unmarshal([]byte(strNetworksStatus), &networksStatusList)
	logger.Debugf("after Unmarshal  %+v", networksStatusList)
	if err != nil {
		logger.Errorf("json.Unmarshal([]byte(%s), []map[string]interface{}) error, %+v", strNetworksStatus, err)
		return "", errorcode.FailedOperationCode.NewWithErr(err)
	}
	for _, networksStatus := range networksStatusList {
		logger.Debugf("networksStatus is %+v", networksStatus)
		if _, exist := networksStatus["name"]; !exist {
			continue
		}
		if networksStatus["name"] != "tke-direct-eni" {
			continue
		}
		if _, exist := networksStatus["ips"]; !exist {
			continue
		}
		ips := networksStatus["ips"]
		ipList, ok := ips.([]interface{})
		if !ok {
			continue
		}
		if len(ipList) < 1 {
			continue
		}
		ip = ipList[0].(string)
		break
	}
	return ip, nil
}

func (c *setats) Ready(s apps.Client, v interface{}) (bool, error) {
	// 检查 setats-manager 是否部署成功
	namespace := DefaultNamespace(c.ctx.ClusterGroup)
	ready, err := c.SetatsReady(namespace, constants.SetatsManager)
	if err != nil {
		logger.Errorf("StartSetatsService Ready SetatsManager error, %+v")
		return false, err
	}
	if !ready {
		return false, nil
	}
	ready, err = c.SetatsReady(namespace, constants.SetatsWorker)
	if err != nil {
		logger.Errorf("StartSetatsService Ready SetatsWorker error, %+v")
		return false, err
	}
	if !ready {
		return false, err
	}
	return ready, err
}

func (c *setats) SetatsReady(namespace string, name string) (bool, error) {
	managerSts, err := c.ctx.K8sService().GetStatefulSetAppsV1(c.ctx.ClientSet(), namespace, name)
	if err != nil {
		logger.Errorf("StartSetatsService Ready GetStatefulSetAppsV1 error, %+v", err)
		return false, err
	}
	if managerSts == nil {
		logger.Infof("StartSetatsService Ready GetStatefulSetAppsV1 %s not found", name)
		return false, nil
	}
	ready, err := c.ctx.K8sService().StatefulSetReady(c.ctx.ClientSet(), managerSts)
	if err != nil {
		logger.Errorf("StartSetatsService Ready StatefulSetReady error, %+v", err)
		return false, err
	}
	if !ready {
		logger.Infof("StartSetatsService Ready StatefulSetReady not ready, %+v", err)
		return false, nil
	}
	return true, nil
}

func (c *setats) Params() (interface{}, error) {

	count, _setats, err := service.GetSetatsByClusterGroupSerialId(c.ctx.ClusterGroup.SerialId)
	if err != nil {
		logger.Errorf("Failed to get setats by ClusterGroupSerialId id:%s, with errors:%+v", c.ctx.ClusterGroup.SerialId, err)
		return nil, errorcode.FailedOperationCode.NewWithErr(err)
	}
	if count == 0 {
		logger.Errorf("Failed to get setats by ClusterGroupSerialId id:%s, with count 0", c.ctx.ClusterGroup.SerialId)
		return nil, errorcode.FailedOperationCode.NewWithMsg(fmt.Sprintf("setats size is 0"))
	}

	appContainerImage, err := c.ctx.CC().ImageRegistry().Setats()
	if err != nil {
		return nil, err
	}

	managerReserveCu := float64(2)
	strManagerReserveCu, err := c.ctx.FlowCC.SetatsCC().ManagerReserveCu()
	logger.Infof("strManagerReserveCu: %s", strManagerReserveCu)
	if err != nil {
		logger.Errorf("Failed to get managerReserveCu, with errors:%+v", err)
	} else {
		managerReserveCu, _ = strconv.ParseFloat(strManagerReserveCu, 32)
	}

	workerReserveCu := float64(1.5)
	strWorkerReserveCu, err := c.ctx.FlowCC.SetatsCC().WorkerReserveCu()
	logger.Infof("strWorkerReserveCu: %s", strWorkerReserveCu)
	if err != nil {
		logger.Errorf("Failed to get workerReserveCu, with errors:%+v", err)
	} else {
		workerReserveCu, _ = strconv.ParseFloat(strWorkerReserveCu, 32)
	}

	workerReserveCu32 := float64(2.5)
	strWorkerReserveCu32, err := c.ctx.FlowCC.SetatsCC().WorkerReserveCu32()
	if err != nil {
		logger.Errorf("Failed to get workerReserveCu, with errors:%+v", err)
	} else {
		workerReserveCu32, _ = strconv.ParseFloat(strWorkerReserveCu32, 32)
	}
	logger.Infof("workerReserveCu32: %s", workerReserveCu32)

	masterCpu := _setats.MasterCpu - float32(managerReserveCu)
	workerCpu := _setats.WorkerCpu - float32(workerReserveCu)
	if _setats.WorkerCpu == float32(32) {
		workerCpu = _setats.WorkerCpu - float32(workerReserveCu32)
	}
	params := make(map[string]interface{})
	params["AppImage"] = appContainerImage
	params["MasterCpu"] = masterCpu
	params["WorkerCpu"] = workerCpu
	params["Zookeeper"] = deploy.GeneratePortConcatenatedIpString(deploy.GetZooKeeperServers(constants.ZooKeeperComponentReplicaNumber), strconv.Itoa(constants.ZooKeeperPort))
	params["WorkerReplicas"] = _setats.WorkerDefaultParallelism
	params["WorkerStorageRequest"] = fmt.Sprintf("%dGi", _setats.WorkerDiskSize-10)
	params["PvPath"] = "/var/lib/docker/oceanus/setats"
	params["PvStorageRequest"] = fmt.Sprintf("%dGi", _setats.WorkerDiskSize-10)

	count, setatsWarehouse, err := service.GetSetatsWarehouseBySerialId(_setats.SerialId)
	if err != nil {
		logger.Errorf("Failed to GetSetatsWarehouseBySerialId by setats serial id:%s, with errors:%+v", _setats.SerialId, err)
		return nil, err
	}
	if count == 0 {
		logger.Errorf("Failed to GetSetatsWarehouseBySerialId by setats serial id:%s, with count 0", _setats.SerialId)
		return nil, errorcode.FailedOperationCode.NewWithMsg(fmt.Sprintf("setats warehouse not found by setats serial id:%s", _setats.SerialId))
	}

	setatsParams, err := service.ListParamBySetatsSerialId(_setats.SerialId)
	if err != nil {
		logger.Errorf("Failed to list param by setats serial id:%s, with errors:%+v", _setats.SerialId, err)
		return nil, err
	}

	cosWriteOnlySecretKey, err2 := service3.GetSecretKeyOfCosWriteOnly()
	if err2 != nil {
		logger.Errorf("GetSecretKeyOfCosWriteOnly with err %v", err2)
	}
	cosWriteOnlySecretId, err2 := service3.GetSecretIdOfCosWriteOnly()
	if err2 != nil {
		logger.Errorf("GetSecretIdOfCosWriteOnly with error %v", err2)
	}

	logDumpBuckets := make(map[string]string)
	err2 = config.DecodeK8sObjectFromRainbowConfig(service.SetatsGroup, "log-dump-buckets", &logDumpBuckets)
	if err2 != nil {
		logger.Errorf("Failed to get setats params log-dump-buckets from rainbow config, with errors:%+v", err)
	}
	SetatsParams := make(map[string]string, 0)
	if setatsWarehouse.Uri != "" {
		SetatsParams["setats.hadoop.uri"] = setatsWarehouse.Uri
	}

	if setatsWarehouse.Properties != "" && setatsWarehouse.Properties != "[]" {
		properties := make([]model1.Property, 0, 0)
		err := json.Unmarshal([]byte(setatsWarehouse.Properties), &properties)
		if err != nil {
			logger.Errorf("Failed to unmarshal properties, with errors:%+v", err)
			return nil, err
		}
		for _, property := range properties {
			SetatsParams[property.Key] = property.Value
		}
	}
	if setatsWarehouse.Authentication == "kerberos" {
		SetatsParams["setats.hadoop.hadoop.security.authentication"] = setatsWarehouse.Authentication
	}

	if len(setatsParams) > 0 {
		for _, setatsParam := range setatsParams {
			SetatsParams[setatsParam.ParamName] = setatsParam.ParamValue
		}
		if _, ok := SetatsParams["setats.manager.memory.heap-size"]; !ok {
			SetatsParams["setats.manager.memory.heap-size"] = service2.GetMemoryByCuSpec(float64(masterCpu*0.8), "m", c.ctx.Tke.ArchGeneration, c.ctx.Cluster.MemRatio)
		}
		if _, ok := SetatsParams["setats.manager.memory.jvm-overhead-size"]; !ok {
			SetatsParams["setats.manager.memory.jvm-overhead-size"] = service2.GetMemoryByCuSpec(float64(masterCpu*0.2), "m", c.ctx.Tke.ArchGeneration, c.ctx.Cluster.MemRatio)
		}

		if _, ok := SetatsParams["setats.worker.memory.heap-size"]; !ok {
			SetatsParams["setats.worker.memory.heap-size"] = service2.GetMemoryByCuSpec(float64(workerCpu*0.8), "m", c.ctx.Tke.ArchGeneration, c.ctx.Cluster.MemRatio)
		}
		if _, ok := SetatsParams["setats.worker.memory.jvm-overhead-size"]; !ok {
			SetatsParams["setats.worker.memory.jvm-overhead-size"] = service2.GetMemoryByCuSpec(float64(workerCpu*0.2), "m", c.ctx.Tke.ArchGeneration, c.ctx.Cluster.MemRatio)
		}

		SetatsParams["setats.kubernetes.manager.labels"] = fmt.Sprintf("cluster_id:%s,setats_id:%s", c.ctx.ClusterGroup.SerialId, _setats.SerialId)
		SetatsParams["setats.kubernetes.worker.labels"] = fmt.Sprintf("cluster_id:%s,setats_id:%s", c.ctx.ClusterGroup.SerialId, _setats.SerialId)

		SetatsParams["setats.kubernetes.manager.env-vars"] = fmt.Sprintf("cluster_id:%s,setats_id:%s,region:%s,bucket:%s,coswo_secret_id:%s,coswo_secret_key:%s", c.ctx.ClusterGroup.SerialId, _setats.SerialId, c.ctx.Region, logDumpBuckets[c.ctx.Region], cosWriteOnlySecretId, cosWriteOnlySecretKey)
		SetatsParams["setats.kubernetes.worker.env-vars"] = fmt.Sprintf("cluster_id:%s,setats_id:%s,region:%s,bucket:%s,coswo_secret_id:%s,coswo_secret_key:%s", c.ctx.ClusterGroup.SerialId, _setats.SerialId, c.ctx.Region, logDumpBuckets[c.ctx.Region], cosWriteOnlySecretId, cosWriteOnlySecretKey)

		params["SetatsParams"] = SetatsParams

	} else {
		// 如果没有参数，从七彩石获取
		// 检查是否存在不合法的参数
		err = config.DecodeK8sObjectFromRainbowConfig(service.SetatsGroup, service.SetatsParamsKey, &setatsParams)
		if err != nil {
			logger.Errorf("Failed to get setats params from rainbow config, with errors:%+v", err)
			return nil, err
		}
		if len(setatsParams) > 0 {
			for _, setatsParam := range setatsParams {
				SetatsParams[setatsParam.ParamName] = setatsParam.ParamValue
			}
		}

		SetatsParams["setats.manager.memory.heap-size"] = service2.GetMemoryByCuSpec(float64(masterCpu*0.8), "m", c.ctx.Tke.ArchGeneration, c.ctx.Cluster.MemRatio)
		SetatsParams["setats.manager.memory.jvm-overhead-size"] = service2.GetMemoryByCuSpec(float64(masterCpu*0.2), "m", c.ctx.Tke.ArchGeneration, c.ctx.Cluster.MemRatio)

		SetatsParams["setats.worker.memory.heap-size"] = service2.GetMemoryByCuSpec(float64(workerCpu*0.8), "m", c.ctx.Tke.ArchGeneration, c.ctx.Cluster.MemRatio)
		SetatsParams["setats.worker.memory.jvm-overhead-size"] = service2.GetMemoryByCuSpec(float64(workerCpu*0.2), "m", c.ctx.Tke.ArchGeneration, c.ctx.Cluster.MemRatio)

		SetatsParams["setats.kubernetes.manager.labels"] = fmt.Sprintf("cluster_id:%s,setats_id:%s", c.ctx.ClusterGroup.SerialId, _setats.SerialId)
		SetatsParams["setats.kubernetes.worker.labels"] = fmt.Sprintf("cluster_id:%s,setats_id:%s", c.ctx.ClusterGroup.SerialId, _setats.SerialId)

		SetatsParams["setats.kubernetes.manager.env-vars"] = fmt.Sprintf("cluster_id:%s,setats_id:%s,region:%s,bucket:%s,coswo_secret_id:%s,coswo_secret_key:%s", c.ctx.ClusterGroup.SerialId, _setats.SerialId, c.ctx.Region, logDumpBuckets[c.ctx.Region], cosWriteOnlySecretId, cosWriteOnlySecretKey)
		SetatsParams["setats.kubernetes.worker.env-vars"] = fmt.Sprintf("cluster_id:%s,setats_id:%s,region:%s,bucket:%s,coswo_secret_id:%s,coswo_secret_key:%s", c.ctx.ClusterGroup.SerialId, _setats.SerialId, c.ctx.Region, logDumpBuckets[c.ctx.Region], cosWriteOnlySecretId, cosWriteOnlySecretKey)

		params["SetatsParams"] = SetatsParams
	}

	// setats refs
	setatsResourceInfoList, err := service.ListSetatsRefBySetatsSerialId(_setats.SerialId)
	if err != nil {
		logger.Errorf("Failed to ListSetatsRefBySetatsSerialId for %s because %+v", _setats.SerialId, err)
		return nil, err
	}

	var refs = ""
	if len(setatsResourceInfoList) > 0 {
		if rrefs, exists := c.ctx.GetParam(constants.FLOW_PARAM_SETATS_REFS, ""); exists {
			logger.Infof("Get Setats refs from Param SetatsRefs is %s", rrefs)
			refs = rrefs
		} else {
			refs, err = c.UploadJarToUserCos(_setats, setatsResourceInfoList)
			if err != nil {
				logger.Errorf("Failed to generate SetatsnRefs,error: %+v", err)
				return nil, err
			}
			if refs != "" {
				c.ctx.SetReturnParam(constants.FLOW_PARAM_SETATS_REFS, refs)
			}
		}
	}

	if refs != "" {
		params["Refs"] = refs
	}
	if c.needManagerUrl {
		ip, err := c.GetSetatsManagerEniIp()
		if err != nil {
			logger.Errorf("Failed to get setats manager eni ip, with errors:%+v", err)
			return nil, err
		}
		managerUrl := fmt.Sprintf("%s:%d", ip, constants.SetatsManagerPort)
		params["ManagerUrl"] = managerUrl
		err = service.ModifySetatasManagerUrl(c.ctx.ClusterGroup.SerialId, managerUrl)
		if err != nil {
			logger.Errorf("Failed to modify setats manager url, with errors:%+v", err)
			return nil, err
		}
	}

	if setatsWarehouse.WarehouseUrl != "" {
		params["WarehouseUrl"] = setatsWarehouse.WarehouseUrl
	}
	if setatsWarehouse.HiveUri != "" {
		params["Uri"] = setatsWarehouse.HiveUri
	}
	params["CatalogType"] = setatsWarehouse.CatalogType

	return params, nil
}

type SetatsResourceItem struct {
	Path     string `json:"path"`
	FileName string `json:"fileName"`
	Bucket   string `json:"bucket"`
	Region   string `json:"region"`
}

func (c *setats) UploadJarToUserCos(_setats *setats2.Setats, setatsResourceInfoList []*setats2.SetatsResourceInfo) (refs string, err error) {
	logger.Infof("uploadJarToUserCos start to upload jar to user cos")

	setatsResourceItems := make([]*SetatsResourceItem, 0)
	if len(setatsResourceInfoList) > 0 {
		oceanusSecretId, oceanusSecretKey, err := service2.GetSecretIdAndKeyOfCos()
		if err != nil {
			logger.Errorf("UploadJarToUserCos Failed to Get Oceanus SecretIdAndKeyOfCos,err: %+v", err)
			return "", errorcode.NewStackError(errorcode.InternalErrorCode, "uploadJarToUserCos Failed to get Oceanus GetSecretIdAndKeyOfCos", err)
		}

		logger.Infof("UploadJarToUserCos production")
		secretId, secretKey, token, pass, err := service2.StsAssumeRole(c.ctx.ClusterGroup.OwnerUin, c.ctx.ClusterGroup.CreatorUin, c.ctx.ClusterGroup.Region)
		if err != nil {
			logger.Errorf("UploadJarToUserCosFailed to Get user SecretIdAndKeyOfCos,err: %+v", err)
			return "", errorcode.NewStackError(errorcode.InternalErrorCode, "Failed to Get user  GetSecretIdAndKeyOfCos", err)
		}
		if !pass {
			logger.Errorf("UploadJarToUserCos No permission to access the user's bucket %s. ", c.ctx.Cluster.DefaultCOSBucket)
			return "", errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("No permission to access the user's bucket %s. ", c.ctx.Cluster.DefaultCOSBucket), err)
		}
		targetCosClient := cos.NewCosClient(secretId, secretKey, token, c.ctx.Region, c.ctx.Cluster.DefaultCOSBucket)

		bucketReq := model.GetResourceBucketReq{0, "", c.ctx.ClusterGroup.Region, "", ""}
		region, bucketName, err := service_bucket.GetResourceBucketName(&bucketReq)
		cosClient := cos.NewCosClient(oceanusSecretId, oceanusSecretKey, "", region, bucketName)
		strUUid := strings.ReplaceAll(uuid.New(), "-", "")
		for _, setatsResourceInfo := range setatsResourceInfoList {
			resourceConfig, err := service.GetResourceConfigByResourceRef(&model5.ResourceRefItem{
				ResourceId: setatsResourceInfo.ResourceSerialId,
				Type:       setatsResourceInfo.Type,
				Version:    setatsResourceInfo.Version,
			})
			if err != nil {
				logger.Errorf("UploadJarToUserCos RestartSetats Failed to GetResourceConfigByResourceRef,error: %+v", err)
				return "", err
			}

			loc := &model4.ResourceLocation{}
			err = json.Unmarshal([]byte(resourceConfig.ResourceLoc), loc)
			if err != nil {
				logger.Errorf("UploadJarToUserCos Failed to Unmarshal,error: %+v", err)
				return "", err
			}
			sourcePath := loc.Param.Path
			targetPath := fmt.Sprintf("setats/%d/%s/%s/%s",
				_setats.AppId, _setats.ClusterGroupSerialId, strUUid, filepath.Base(sourcePath))
			logger.Infof("UploadJarToUserCos source path %s, target path %s", sourcePath, targetPath)
			err = cosClient.CopyOnPutForTargetCos(sourcePath, targetPath, targetCosClient)
			if err != nil {
				logger.Errorf("UploadJarToUserCos Failed to CopyOnPutForTargetCos,err: %+v", err)
				return "", err
			}
			_, fileName := filepath.Split(targetPath)
			setatsResourceItems = append(setatsResourceItems, &SetatsResourceItem{
				Path:     targetPath,
				FileName: fileName,
				Bucket:   targetCosClient.Bucket,
				Region:   targetCosClient.Region,
			})
		}
		marshaled, err := json.Marshal(setatsResourceItems)
		if err != nil {
			logger.Errorf("UploadJarToUserCos Failed to Marshal,err: %+v", err)
			return "", err
		}
		logger.Infof("UploadJarToUserCos marshaled: %s", string(marshaled))
		encoded := base64.StdEncoding.EncodeToString(marshaled)
		return encoded, nil
	}
	return "", nil
}

func (c *setats) Apply(s apps.Client, v interface{}) (interface{}, error) {
	config, err := k8s.GetK8sService().NewConfig(c.ctx.KubeConfig())
	dynamicClient, err := dynamic.NewForConfig(config)
	if err != nil {
		logger.Errorf("apply setats new dynamic client error: %s", err.Error())
		return nil, err
	}

	params, err := c.Params()
	if err != nil {
		logger.Errorf("apply setats get params error: %s", err.Error())
		return nil, err
	}
	setatsYamlFile, err := c.ctx.FlowCC.SetatsCC().Setats(params)
	if err != nil {
		logger.Errorf("apply setats get yaml error: %s", err.Error())
		return nil, err
	}
	logger.Infof("apply setats yaml: %s", setatsYamlFile)
	var obj unstructured.Unstructured
	if err := yaml.Unmarshal([]byte(setatsYamlFile), &obj); err != nil {
		logger.Errorf("apply setats unmarshal error: %s", err.Error())
		return nil, err
	}
	logger.Infof("####applysetatsInfo Group: %s, Version: %s, Resource: %s, ", obj.GroupVersionKind().Group, obj.GroupVersionKind().Version, strings.ToLower(obj.GetKind()))
	gvr := schema.GroupVersionResource{
		Group:    obj.GroupVersionKind().Group,
		Version:  obj.GroupVersionKind().Version,
		Resource: strings.ToLower(obj.GetKind()),
	}
	namespace := obj.GetNamespace() // 从 YAML 中获取命名空间
	_, err = dynamicClient.Resource(gvr).Namespace(namespace).Create(&obj, metav1.CreateOptions{})
	if err != nil && !errors.IsAlreadyExists(err) {
		logger.Errorf("apply setats error: %s", err.Error())
		return nil, err
	}
	if errors.IsAlreadyExists(err) {
		logger.Infof("apply setats already exists")
		// 若资源已存在，执行更新
		existingObj, _ := dynamicClient.Resource(gvr).Namespace(namespace).Get(obj.GetName(), metav1.GetOptions{})
		obj.SetResourceVersion(existingObj.GetResourceVersion())
		_, err = dynamicClient.Resource(gvr).Namespace(namespace).Update(&obj, metav1.UpdateOptions{})
		if err != nil {
			logger.Errorf("apply setats update error: %s", err.Error())
			return nil, err
		}
	}
	logger.Infof("apply setats success")
	if !c.needManagerUrl {
		// 第一次部署，sleep ，需要获取弹性ip，不然会失败
		logger.Infof("sleep 30s for get elastic ip")
		time.Sleep(30 * time.Second)
	}
	return nil, nil
}
