package taskHandler

import (
	"context"
	"encoding/json"
	"fmt"
	"runtime/debug"

	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common"
	es "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/es/v20180416"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	serviceTx "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/log"
	commonService "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	flow2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/flow"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/log"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/tke"
)

type ApplyEsServerlessFilebeatHandler struct {
}

func (h *ApplyEsServerlessFilebeatHandler) HandleTaskStatusInit(requestId string, request *flow.TaskExecRequest) (resp *flow.TaskExecResponse) {
	flowService := flow2.GetFlowService()
	defer func() {
		if err := recover(); err != nil {
			logger.Infof("[%s] err %s", requestId, string(debug.Stack()))
			resp = flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_IN_PROCESS, 0.1, fmt.Sprintf("%v", err), request.Params)
		}
	}()
	applyEsServerlessService, err := NewApplyEsServerlessService(requestId, request)
	if err != nil {
		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_IN_PROCESS, 0.1,
			fmt.Sprintf("%v", err), request.Params)
	}
	return applyEsServerlessService.apply()
}

func (h *ApplyEsServerlessFilebeatHandler) HandleTaskStatusRunning(requestId string, request *flow.TaskExecRequest) (resp *flow.TaskExecResponse) {
	flowService := flow2.GetFlowService()
	defer func() {
		if err := recover(); err != nil {
			logger.Infof("[%s] err %s", requestId, string(debug.Stack()))
			resp = flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_IN_PROCESS, 0.1, fmt.Sprintf("%v", err), request.Params)
		}
	}()
	applyEsServerlessService, err := NewApplyEsServerlessService(requestId, request)
	if err != nil {
		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_IN_PROCESS, 0.1,
			fmt.Sprintf("%v", err), request.Params)
	}
	return applyEsServerlessService.checkReady()
}

type ApplyEsServerlessFilebeatService struct {
	requestId string
	request   *flow.TaskExecRequest
	*flow2.ClusterFlowService
}

func NewApplyEsServerlessService(reqId string, request *flow.TaskExecRequest) (*ApplyEsServerlessFilebeatService, error) {
	clusterFlowService, err := flow2.NewClusterFlowService(request)
	if err != nil {
		logger.Errorf("NewApplyEsServerlessService  has return error %v", err)
		return nil, err
	}
	result := &ApplyEsServerlessFilebeatService{
		requestId:          reqId,
		request:            request,
		ClusterFlowService: clusterFlowService,
	}
	return result, nil
}

func (s *ApplyEsServerlessFilebeatService) apply() (rsp *flow.TaskExecResponse) {
	esServerlessDiId := s.Request.Params[constants.FLOW_PARAM_ES_SERVERLESS_DIID]
	if esServerlessDiId != "" {
		msg := fmt.Sprintf("CreateServerlessDi is already called, diId: %s", esServerlessDiId)
		logger.Infof(msg)
		s.DoneRsp(msg)
	}
	indexId := s.request.Params[constants.FLOW_PARAM_ES_SERVERLESS_INDEXID]
	if indexId == "" {
		logger.Errorf("apply es serverless filebeat failed , indexId is not valid")
		s.RetryRsp("indexId not valid")
	}
	workspaceId := s.request.Params[constants.FLOW_PARAM_ES_SERVERLESS_WORKSPACE_ID]
	if workspaceId == "" {
		logger.Errorf("apply es serverless filebeat failed , workspaceId is not valid")
		s.RetryRsp("workspaceId not valid")
	}
	// 写入数据库
	clusterLogconf := &log.ClusterEsServelessLogConf{
		Status: log.ClusterLogconfOperating,
		EsServerlessInfo: append(make([]*log.EsServerlessInfoModel, 0), &log.EsServerlessInfoModel{
			IndexId:     indexId,
			WorkspaceId: workspaceId,
		}),
	}
	err := updateClusterLogconf(s.Cluster.Id, clusterLogconf)
	if err != nil {
		logger.Errorf("updateClusterLogconf return error %v", err)
		return s.RetryRspWithErr(err)
	}
	logger.Infof("update clusterLogconf successfully")

	// 创建数据接入
	secretId, secretKey, err := commonService.GetSecretIdAndKeyByNetworkEnvType(s.ClusterGroup.NetEnvironmentType)
	if err != nil {
		logger.Errorf("GetSecretIdAndKeyByNetworkEnvType with error %v", err)
		return s.RetryRspWithErr(err)
	}
	aess := &service.ApplyEsServerlessService{
		SecretId:  secretId,
		SecretKey: secretKey,
		Token:     "",
		Region:    s.Region,
	}
	csdr := es.NewCreateServerlessDiRequest()
	csdr.DiSinkServerless = &es.DiSinkServerless{
		ServerlessId: common.StringPtr(indexId),
	}
	innerProductType := constants.ES_SERVERLESS_INNER_PRODUCT_PUBLIC_CLOUD
	if s.ClusterGroup.NetEnvironmentType == constants.NETWORK_ENV_INNER_VPC {
		innerProductType = constants.ES_SERVERLESS_INNER_PRODUCT_YUNTI
	}
	csdr.DiSourceInfo = &es.DiSourceInfo{
		DiSourceId:       common.StringPtr(s.ClusterGroup.SerialId),
		InnerProductType: common.Uint64Ptr(innerProductType),
		ProductName:      common.StringPtr("oceanus"),
	}
	collectorTarget := &es.CollectorTarget{}
	collectorTarget.InputsProcessorIncludeFields = common.StringPtr("message,kubernetes.labels,kubernetes.pod.name")
	collectorTarget.TargetName = common.StringPtr("oceanus-" + s.ClusterGroup.SerialId)
	collectorTargets := make([]*es.CollectorTarget, 0)
	collectorTargets = append(collectorTargets, collectorTarget)
	csdr.DiSourceType = common.StringPtr("tke_collector")
	csdr.DiSourceTke = &es.DiSourceTke{
		VpcId:             common.StringPtr(s.Cluster.VpcId),
		TkeId:             common.StringPtr(s.Cluster.UniqClusterId),
		IncludeNamespaces: common.StringPtrs([]string{constants.FLOW_PARAM_ES_SERVERLESS_FILEBEAT_NAMESPACE}),
		ExcludeNamespaces: common.StringPtrs([]string{}),
		PodLabel: []*es.DiSourceTkePodLabel{{
			Key:   common.StringPtr("logCollect"),
			Value: common.StringPtr(indexId),
		}},
		CollectorTargets: collectorTargets,
	}
	logger.Infof("CreateServerlessDiRequest: %s", csdr.ToJsonString())
	result, err := aess.CreateServerlessDi(csdr)
	if err != nil {
		logger.Errorf("createServerlessDi has return error %v", err)
		return s.RetryRspWithErr(err)
	}
	logger.Infof("CreateServerlessDi result %s", result.ToJsonString())

	// DiId 设置到 param 中, 以供后续检测状态
	s.Request.Params[constants.FLOW_PARAM_ES_SERVERLESS_DIID] = *result.Response.DiId
	return s.DoneRsp("CreateServerlessDi successfully")
}

// updateClusterLogconf 更新 Cluster 表 LogConfig 字段
func updateClusterLogconf(clusterId int64, clusterLogconf *log.ClusterEsServelessLogConf) error {
	logconfStr, err := json.Marshal(clusterLogconf)
	if err != nil {
		logger.Errorf("ClusterEsServerlessLogconf format error %v", err)
		return err
	}
	sql := "UPDATE Cluster SET LogConfig = ? WHERE Id = ? limit 1"
	serviceTx.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.ExecuteSqlWithArgs(sql, logconfStr, clusterId)
		return nil
	}).Close()
	return nil
}

func (s *ApplyEsServerlessFilebeatService) checkReady() (rsp *flow.TaskExecResponse) {
	// 1. 检查数据接入是否创建成功
	diId := s.request.Params[constants.FLOW_PARAM_ES_SERVERLESS_DIID]
	if diId == "" {
		logger.Errorf("apply es serverless filebeat failed , DiId is not valid")
		s.RetryRsp("DiId not valid")
	}
	indexId := s.request.Params[constants.FLOW_PARAM_ES_SERVERLESS_INDEXID]
	if indexId == "" {
		logger.Errorf("apply es serverless filebeat failed , indexId is not valid")
		s.RetryRsp("indexId not valid")
	}
	// 查询数据接入改为用用户临时密钥查询
	secretId, secretKey, token, _, err := commonService.StsAssumeRole(s.ClusterGroup.OwnerUin, s.ClusterGroup.CreatorUin, s.Region)
	if err != nil {
		logger.Errorf("GetSecretIdAndKeyByNetworkEnvType with error %v", err)
		return s.RetryRspWithErr(err)
	}
	aess := &service.ApplyEsServerlessService{
		SecretId:  secretId,
		SecretKey: secretKey,
		Token:     token,
		Region:    s.Region,
	}
	dsdr := es.NewDescribeServerlessDiRequest()
	dsdr.DiIds = append(make([]*string, 0), common.StringPtr(diId))
	dsdr.ServerlessId = common.StringPtr(indexId)
	result, err := aess.DescribeServerlessDi(dsdr)
	if err != nil {
		logger.Errorf("describeServerlessDi with error %v", err)
		s.RetryRspWithErr(err)
	}
	logger.Infof("describeServerlessDi result %s", result.ToJsonString())
	if result == nil || result.Response == nil ||
		result.Response.DiDataList == nil || len(result.Response.DiDataList) <= 0 {
		errMsg := fmt.Sprintf("describeServerlessDi result not valid %s", result.ToJsonString())
		logger.Errorf(errMsg)
		s.RetryRsp(errMsg)
	}
	filebeatNames := make([]string, 0)
	for _, dd := range result.Response.DiDataList {
		if *dd.Status != 1 {
			return s.RetryRsp(fmt.Sprintf("DiId %s not ready", *dd.DiId))
		}
		filebeatNames = append(filebeatNames, *dd.DiDataSourceTke.CollectorId+"-filebeat")
	}

	// 2. 检查 filebeat 是否创建成功
	clientSet, err := tke.GetTkeService().KubernetesClientsetFromCluster(s.request.DocId, s.Cluster)
	if err != nil {
		logger.Errorf("ApplyEsServerlessFilebeatService new kubernetes client has return error %v", err)
		return s.FailRspWithErr(err)
	}
	for _, v := range filebeatNames {
		ds, err := clientSet.AppsV1().DaemonSets(constants.FLOW_PARAM_ES_SERVERLESS_FILEBEAT_NAMESPACE).Get(context.TODO(), v, v1.GetOptions{})
		if err != nil {
			logger.Errorf("cannot get daemonset by name %s", v)
			return s.RetryRspWithErr(err)
		}
		// 这里只需要检测 daemonset 是否存在, 此时网络未打通, pod 无法启动
		if ds != nil {
			logger.Infof("filebeat %s apply successfully", v)
		}
	}
	if len(filebeatNames) <= 0 {
		logger.Errorf("cannot find valid filebeat %s", result.ToJsonString())
		return s.RetryRsp("cannot find valid filebeat ")
	}

	// 3. 把 filebeat 设置到 Cluster.LogConfig 中
	logConfigStr := s.Cluster.LogConfig
	esServelessLogConf := &log.ClusterEsServelessLogConf{}
	err = json.Unmarshal([]byte(logConfigStr), esServelessLogConf)
	if err != nil {
		logger.Errorf("Cluster.LogConfig format error %s", logConfigStr)
		s.RetryRspWithErr(err)
	}
	for _, model := range esServelessLogConf.EsServerlessInfo {
		if model.IndexId == indexId {
			model.DiDataCollectorIds = filebeatNames
		}
	}
	updateClusterLogconf(s.Cluster.Id, esServelessLogConf)

	return s.DoneRsp("filebeat is ready")
}
