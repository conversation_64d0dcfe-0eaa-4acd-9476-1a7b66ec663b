package taskHandler

import (
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/emr"
	flow2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/flow"
)

type deleteEmrHandler struct {
}

func (d *deleteEmrHandler) CompleteTask(request *flow.TaskExecRequest) (resp *flow.TaskExecResponse) {
	flowService := flow2.GetFlowService()
	params := request.Params

	defer func() {
		if errs := recover(); errs != nil {
			resp = flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1,
				fmt.Sprintf("%v", errs), params)
		}
	}()

	var err error
	resp, err = d.handle(request)
	if err != nil {
		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1, fmt.Sprintf("%v", err), params)
	}
	return
}

func (d *deleteEmrHandler) handle(request *flow.TaskExecRequest) (resp *flow.TaskExecResponse, err error) {
	flowService := flow2.GetFlowService()
	params := request.Params
	clusterGroupId, exists, err := flowService.GetFlowParamInt(params, constants.FLOW_PARAM_CLUSTER_GROUP_ID, 0)
	if err != nil {
		return
	}
	if !exists {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "Cluster Group Id Not Found", nil)
	}

	clusterType, exists1, err1 := flowService.GetFlowParamInt(params, constants.FLOW_PARAM_CLUSTER_TYPE, 0)
	if err1 != nil {
		return
	}
	if !exists1 {
		return nil, errorcode.NewStackError(errorcode.InternalErrorCode, "clusterType Not Found", nil)
	}

	clusterGroupService, err := service.NewClusterGroupService(clusterGroupId)
	if err != nil {
		return
	}

	// 如果是内网(云梯)环境TKE集群直接返回
	clusterGroup := clusterGroupService.GetClusterGroup()
	if clusterGroup.NetEnvironmentType == constants.NETWORK_ENV_INNER_VPC && clusterType == constants.K8S_CLUSTER_TYPE_TKE {
		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_SUCCESS, flow.TASK_STATUS_SUCCESS, 0.1, "ok",
			params), nil
	}

	emrService := emr.GetEmrService()
	emrList, err := clusterGroupService.GetEmrList()
	if err != nil {
		return
	}
	for _, instance := range emrList {
		req := emrService.NewDefaultTerminateInstanceRequestBuilder().
			WithInstanceId(instance.InstanceId).Build()
		// 对同一个集群重复调用这个接口
		// 1. 集群正在销毁中， 会报错
		// 2. 集群销毁完成， 不会报错。 所以这里直接调用接口删除，不管是否重复调用
		_, err = emrService.TerminateInstanceWithScsAccount(clusterGroupService.GetClusterGroup().Region, req)
		if err != nil {
			return
		}
	}

	return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_SUCCESS, flow.TASK_STATUS_SUCCESS, 0.1, "ok",
		params), nil
}
