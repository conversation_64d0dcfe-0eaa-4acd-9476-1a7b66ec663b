package taskHandler

import (
	"encoding/json"
	"errors"
	"fmt"
	cvm "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/cvm/v20170312"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/dlocker"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cbs"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	cvm3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cvm"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/tke"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type ScaleSetatsService struct {
}

type scaleWorkerParallelism struct {
	ctx *deploy.Context
	apps.ApplyReadyApp
	requestId string
}

func (swp scaleWorkerParallelism) Apply(s apps.Client, v interface{}) (interface{}, error) {
	msg := swp.ScaleUpWorkerParallelism()
	if msg != "" {
		logger.Errorf("%s scaleWorkerParallelism Apply, with errors:%s", swp.requestId, msg)
		return nil, errors.New(msg)
	}
	return nil, nil
}

func (swp scaleWorkerParallelism) Ready(s apps.Client, v interface{}) (bool, error) {
	h := &ApplyTKEHandler{}
	if allNodeRunning, err := h.TryMakeAllNodesRunning(swp.ctx); err != nil {
		msg := fmt.Sprintf("%s scaleWorkerParallelism TryMakeAllNodesRunning Error, err %+v", swp.requestId, err)
		logger.Errorf(msg)
		return false, err
	} else if !allNodeRunning {
		msg := fmt.Sprintf("%s scaleWorkerParallelism allNodeRunning is false", swp.requestId)
		logger.Errorf(msg)
		return false, errors.New(msg)
	}
	return true, nil
}

func (swp scaleWorkerParallelism) ScaleUpWorkerParallelism() (errMsg string) {
	defer func() {
		if errs := recover(); errs != nil {
			errMsg = fmt.Sprintf("%s ScaleUpWorkerParallelism, recover error:%+v", swp.requestId, errs)
		}
	}()
	count, _setats, err := service.GetSetatsByClusterGroupSerialId(swp.ctx.ClusterGroup.SerialId)
	if err != nil {
		msg := fmt.Sprintf("%s Failed to get setats by ClusterGroupSerialId id:%s, with errors:%+v", swp.requestId, swp.ctx.ClusterGroup.SerialId, err)
		logger.Errorf(msg)
		return msg
	}

	if count == 0 {
		msg := fmt.Sprintf("%s Failed to get setats by ClusterGroupSerialId id:%s, with count 0", swp.requestId, swp.ctx.ClusterGroup.SerialId)
		logger.Errorf(msg)
		return msg
	}

	setatsSerialId := _setats.SerialId
	locker := dlocker.NewDlocker(fmt.Sprintf("ScaleUpWorkerParallelism_%s", setatsSerialId), setatsSerialId, 8000)
	err = locker.Lock()
	if err != nil {
		msg := fmt.Sprintf("%s Another ScaleUpWorkerParallelism process for %s has lock but not finished yet", swp.requestId, setatsSerialId)
		logger.Errorf(msg)
		return msg
	}
	defer locker.UnLock()

	strWorkerDiskSize, _ := swp.ctx.GetParam(constants.FLOW_PARAM_SETATS_WORKER_DISK_SIZE, "0")
	workerDiskSize, _ := strconv.Atoi(strWorkerDiskSize)

	if workerDiskSize > 0 && workerDiskSize > _setats.WorkerDiskSize {
		_setats.WorkerDiskSize = workerDiskSize
	}

	strWorkerParallelism, _ := swp.ctx.GetParam(constants.FLOW_PARAM_SETATS_WORKERDEFAULTPARALLELISM, "0")
	workerParallelism, _ := strconv.Atoi(strWorkerParallelism)
	if workerParallelism <= _setats.WorkerDefaultParallelism {
		msg := fmt.Sprintf("%s workerParallelism:%d <= _setats.WorkerDefaultParallelism:%d", swp.requestId, workerParallelism, _setats.WorkerDefaultParallelism)
		logger.Infof(msg)
		return ""
	}
	addWorkerNum := workerParallelism - _setats.WorkerDefaultParallelism

	labels := make(map[string]string)
	labels[constants.TKE_CVM_LABEL_KEY] = constants.SETATS_WORKER_LABEL

	logger.Infof("%s scaleWorkerParallelism, addWorkerNum:%d", swp.requestId, addWorkerNum)
	err = AddSetatsCvm("scaleWorkerApp", swp.ctx, float64(_setats.WorkerCpu), int64(_setats.WorkerDiskSize), _setats.WorkerDiskType, int64(addWorkerNum), labels, workerParallelism)
	if err != nil {
		msg := fmt.Sprintf("%s scaleWorkerParallelism addMasterApp addSetatsCvm Error, err %v", swp.requestId, err)
		logger.Errorf(msg)
		return msg
	}
	return ""
}

type scaleDisk struct {
	ctx *deploy.Context
	apps.ApplyReadyApp
	requestId string
}

func (c *scaleDisk) DescribeInvocations(invocationIds []*string) (errMsg string) {
	defer func() {
		if errs := recover(); errs != nil {
			msg := fmt.Sprintf("DescribeInvocations, errors:%+v", errs)
			errMsg = msg
			return
		}
	}()
	locker := dlocker.NewDlocker("setats-DescribeInvocations-%s", c.ctx.ClusterGroup.SerialId, 8000)
	err := locker.Lock()
	if err != nil {
		msg := fmt.Sprintf("%s Another setats-DescribeInvocations-%s process has lock but not finished yet", c.requestId, c.ctx.ClusterGroup.SerialId)
		logger.Errorf(msg)
		return msg
	}
	defer locker.UnLock()

	cvmService := cvm3.GetCvmService()
	_, invocationRst, err := cvmService.DescribeInvocationsForIdsWithScsAccount(c.ctx.ClusterGroup.NetEnvironmentType, c.ctx.ClusterGroup.Region, invocationIds)
	if err != nil {
		msg := fmt.Sprintf("%s DescribeInvocations, with errors:%+v", c.requestId, err)
		logger.Errorf("%s DescribeInvocations, with errors:%+v", c.requestId, err)
		return msg
	}
	for _, invocation := range invocationRst {
		if invocation == nil {
			logger.Errorf("%s DescribeInvocations, invocation is nil", c.requestId)
			continue
		}
		invocationTaskBasicInfoSet := invocation.InvocationTaskBasicInfoSet
		if len(invocationTaskBasicInfoSet) == 0 {
			logger.Errorf("%s DescribeInvocations, invocationTaskBasicInfoSet is empty", c.requestId)
			continue
		}
		i, _ := json.Marshal(invocation)
		logger.Infof("%s DescribeInvocations, invocation:%s", c.requestId, string(i))
		for _, invocationTaskBasicInfo := range invocationTaskBasicInfoSet {
			if *invocationTaskBasicInfo.TaskStatus == "SUCCESS" {
				logger.Infof("%s DescribeInvocations, invocationTaskBasicInfo.TaskStatus is SUCCESS, instanceId %s", c.requestId, invocationTaskBasicInfo.InstanceId)
				continue
			} else {
				return fmt.Sprintf("%s DescribeInvocations, instanceId %s invocationTaskBasicInfo.TaskStatus is not SUCCESS", c.requestId, *invocationTaskBasicInfo.InstanceId)
			}
		}
	}
	return ""
}

func (c *scaleDisk) Ready(s apps.Client, v interface{}) (bool, error) {
	invocationIds := make([]string, 0)
	_, err := c.ctx.GetParamUseJson(constants.FLOW_RETURN_PARAM_SETATS_SCALE_DISK_INVOCATION_IDS, &invocationIds)
	if err != nil {
		logger.Errorf("%s ScaleUpSetatsDisk Ready, with errors:%s", c.requestId, err.Error())
		return false, err
	}
	logger.Infof("%s ScaleUpSetatsDisk Ready, invocationIds:%+v", c.requestId, invocationIds)
	if len(invocationIds) == 0 {
		logger.Infof("%s ScaleUpSetatsDisk Ready, invocationIds is empty", c.requestId)
		return true, nil
	}

	msg := c.DescribeInvocations(util.ConvertToPtrSlice(invocationIds))
	if msg != "" {
		return false, errors.New(msg)
	}
	return true, nil
}

func (c *scaleDisk) Apply(s apps.Client, v interface{}) (interface{}, error) {
	msg := c.ScaleUpSetatsDisk()
	if msg != "" {
		logger.Errorf("%s ScaleUpSetatsDisk Apply, with errors:%s", c.requestId, msg)
		return nil, errors.New(msg)
	}
	return nil, nil
}

func (c *scaleDisk) GetWorker(label map[string]string) (labelInstancesMap map[string][]string, err error) {

	labelSelectSet := make([]*string, 0)
	for k, v := range label {
		kv := fmt.Sprintf("%s=%s", k, v)
		labelSelectSet = append(labelSelectSet, &kv)
	}

	workerList, err := c.ctx.K8sService().ListNode(c.ctx.ClientSet(), metav1.ListOptions{
		LabelSelector: labels.FormatLabels(label),
	})
	if err != nil {
		logger.Errorf("%s scale disk, Failed to list node, with errors:%+v", c.requestId, err)
		return
	}
	labelInstancesMap = make(map[string][]string, 0)
	for _, node := range workerList.Items {
		labelMap := node.GetObjectMeta().GetLabels()
		if instanceId, ok := labelMap["cloud.tencent.com/node-instance-id"]; ok {
			if labelValue, ok := labelMap[constants.TKE_CVM_LABEL_KEY]; ok {
				if labelValue == constants.SETATS_MANAGER_LABEL || labelValue == constants.SETATS_WORKER_LABEL {
					logger.Infof("%s scale disk, get instance id:%s, label:%s", c.requestId, instanceId, labelValue)
					if _, exists := labelInstancesMap[labelValue]; !exists {
						instanceIds := make([]string, 0)
						instanceIds = append(instanceIds, instanceId)
						labelInstancesMap[labelValue] = instanceIds
					} else {
						labelInstancesMap[labelValue] = append(labelInstancesMap[labelValue], instanceId)
					}
				}
			}
		} else {
			err = fmt.Errorf("%s not find instance id for %s, %+v", c.requestId, node.Name, node)
			return
		}
	}
	return
}

type ScaleDiskInfo struct {
	InstanceId     string
	Label          string
	TargetDiskSize int
}

func (c *scaleDisk) ScaleUpSetatsDisk() (errMsg string) {
	defer func() {
		if errs := recover(); errs != nil {
			msg := fmt.Sprintf("%s ScaleUpSetatsDisk, recover error:%+v", c.requestId, errs)
			errMsg = msg
		}
	}()
	count, _setats, err := service.GetSetatsByClusterGroupSerialId(c.ctx.ClusterGroup.SerialId)
	if err != nil {
		msg := fmt.Sprintf("%s Failed to get setats by ClusterGroupSerialId id:%s, with errors:%+v", c.requestId, c.ctx.ClusterGroup.SerialId, err)
		logger.Errorf(msg)
		return msg
	}

	if count == 0 {
		msg := fmt.Sprintf("%s Failed to get setats by ClusterGroupSerialId id:%s, with count 0", c.requestId, c.ctx.ClusterGroup.SerialId)
		logger.Errorf(msg)
		return msg
	}

	setatsSerialId := _setats.SerialId
	locker := dlocker.NewDlocker(fmt.Sprintf("ScaleUpSetatsDisk_%s", setatsSerialId), setatsSerialId, 8000)
	err = locker.Lock()
	if err != nil {
		msg := fmt.Sprintf("%s Another ScaleUpSetatsDisk process for %s has lock but not finished yet", c.requestId, setatsSerialId)
		logger.Infof(msg)
		return msg
	}
	defer locker.UnLock()

	strMasterDiskSize, _ := c.ctx.GetParam(constants.FLOW_PARAM_SETATS_MASTER_DISK_SIZE, "0")
	masterDiskSize, _ := strconv.Atoi(strMasterDiskSize)

	strWorkerDiskSize, _ := c.ctx.GetParam(constants.FLOW_PARAM_SETATS_WORKER_DISK_SIZE, "0")
	workerDiskSize, _ := strconv.Atoi(strWorkerDiskSize)

	labelDiskMap := make(map[string]int)
	masterLabels := make(map[string]string)
	workerLabels := make(map[string]string)
	if masterDiskSize > 0 && masterDiskSize > _setats.MasterDiskSize {
		logger.Infof("%s ScaleUpSetatsDisk setatsSerialId %s, masterDiskSize %d, _setats.MasterDiskSize %d", c.requestId, setatsSerialId, masterDiskSize, _setats.MasterDiskSize)
		masterLabels[constants.TKE_CVM_LABEL_KEY] = constants.SETATS_MANAGER_LABEL
		labelDiskMap[constants.SETATS_MANAGER_LABEL] = masterDiskSize
	}
	if workerDiskSize > 0 && workerDiskSize > _setats.WorkerDiskSize {
		logger.Infof("%s ScaleUpSetatsDisk setatsSerialId %s, target workerDiskSize %d, source WorkerDiskSize %d", c.requestId, setatsSerialId, workerDiskSize, _setats.WorkerDiskSize)
		workerLabels[constants.TKE_CVM_LABEL_KEY] = constants.SETATS_WORKER_LABEL
		labelDiskMap[constants.SETATS_WORKER_LABEL] = workerDiskSize
	}

	allLabelInstancesMap := make(map[string][]string, 0)
	if len(masterLabels) > 0 {
		labelInstancesMap, err := c.GetWorker(masterLabels)
		if err != nil {
			msg := fmt.Sprintf("%s ScaleUpSetatsDisk setatsSerialId %s, get worker error:%+v", c.requestId, setatsSerialId, err)
			logger.Errorf(msg)
			return msg
		}
		for key, value := range labelInstancesMap {
			allLabelInstancesMap[key] = value
		}
	}
	if len(workerLabels) > 0 {
		labelInstancesMap, err := c.GetWorker(workerLabels)
		if err != nil {
			msg := fmt.Sprintf("%s ScaleUpSetatsDisk setatsSerialId %s, get worker error:%+v", c.requestId, setatsSerialId, err)
			logger.Errorf(msg)
			return msg
		}
		for key, value := range labelInstancesMap {
			allLabelInstancesMap[key] = value
		}
	}

	c.ctx.SetReturnParamUseJson(constants.FLOW_RETURN_PARAM_SETATS_SCALE_DISK_INSTANCE_IDS, allLabelInstancesMap)
	if len(allLabelInstancesMap) == 0 {
		msg := fmt.Sprintf("%s ScaleUpSetatsDisk setatsSerialId %s, no worker to scale", c.requestId, setatsSerialId)
		logger.Warningf(msg)
		return ""
	}
	b, _ := json.Marshal(allLabelInstancesMap)
	logger.Infof("%s ScaleUpSetatsDisk -> labelInstanceMap is %s", c.requestId, string(b))

	scaleDiskInfos := make([]*ScaleDiskInfo, 0)
	instances := make([]*string, 0)
	for label, instanceList := range allLabelInstancesMap {
		targetDiskSize, exits := labelDiskMap[label]
		if !exits {
			logger.Warningf("%s ScaleUpSetatsDisk -> label %s, not in labelDiskMap", c.requestId, label)
			continue
		}
		for _, s := range instanceList {
			tmp := s
			instances = append(instances, &tmp)
			scaleDiskInfos = append(scaleDiskInfos, &ScaleDiskInfo{
				InstanceId:     s,
				Label:          label,
				TargetDiskSize: targetDiskSize,
			})
		}

	}
	b, _ = json.Marshal(scaleDiskInfos)
	logger.Infof("%s ScaleUpSetatsDisk -> scaleDiskInfo is %s", c.requestId, string(b))
	b, _ = json.Marshal(instances)
	logger.Infof("%s ScaleUpSetatsDisk -> instances is %+v", c.requestId, string(b))
	if len(scaleDiskInfos) < 1 {
		msg := fmt.Sprintf("%s ScaleUpSetatsDisk -> scaleDiskInfo is empty", c.requestId)
		logger.Errorf(msg)
		return msg
	}

	cvmService := cvm3.GetCvmService()
	cvmSet, err := cvmService.DescribeInstancesWithScsAccount(c.ctx.ClusterGroup.NetEnvironmentType, c.ctx.Region, instances)
	if err != nil {
		msg := fmt.Sprintf("%s ScaleUpSetatsDisk -> DescribeInstancesWithScsAccount error:%+v", c.requestId, err)
		logger.Errorf(msg)
		return msg
	}
	if len(cvmSet) == 0 {
		msg := fmt.Sprintf("%s ScaleUpSetatsDisk -> DescribeInstancesWithScsAccount error:%+v", c.requestId, err)
		logger.Errorf(msg)
		return msg
	}
	cvmMap := make(map[string]*cvm.Instance)
	for _, instance := range cvmSet {
		if *instance.InstanceState != "RUNNING" {
			logger.Warningf("%s ScaleUpSetatsDisk -> instance %s, state %s, skip", c.requestId, *instance.InstanceId, *instance.InstanceState)
			continue
		}
		cvmMap[*instance.InstanceId] = instance
	}

	tkeService := tke.GetTkeService()
	_, instanceSet, err := tkeService.DescribeClusterInstanceForIdsWithScsAccountByNetEnvironmentTypeV2022(
		c.ctx.ClusterGroup.NetEnvironmentType, c.ctx.Region, c.ctx.Tke.InstanceId, instances)
	if err != nil {
		msg := fmt.Sprintf("%s ScaleUpSetatsDisk -> DescribeClusterInstanceForIdsWithScsAccountByNetEnvironmentTypeV2022 error:%+v", c.requestId, err)
		logger.Errorf(msg)
		return msg
	}
	logger.Infof("%s ScaleUpSetatsDisk -> DescribeClusterInstanceForIdsWithScsAccountByNetEnvironmentTypeV2022 instanceSet:%+v", c.requestId, instanceSet)

	scaleDiskInfoMap := make(map[string]*ScaleDiskInfo)
	for _, scaleDiskInfo := range scaleDiskInfos {
		scaleDiskInfoMap[scaleDiskInfo.InstanceId] = scaleDiskInfo
	}

	cbsService := cbs.GetCbsService(c.ctx.ClusterGroup.NetEnvironmentType, c.ctx.Region)
	for _, instance := range instanceSet {
		for _, disk := range instance.Regular.InstanceAdvancedSettings.DataDisks {
			scaleDiskInfo, exits := scaleDiskInfoMap[*instance.InstanceId]
			if !exits {
				logger.Errorf("%s ScaleUpSetatsDisk -> instance %s, not in scaleDiskInfoMap", c.requestId, *instance.InstanceId)
				continue
			}
			if *disk.MountTarget != "/var/lib/docker" {
				continue
			}

			if *disk.DiskSize >= int64(scaleDiskInfo.TargetDiskSize) {
				logger.Warningf("%s ScaleUpSetatsDisk -> instance %s, disk size %d, target disk size %d skip", c.requestId, *instance.InstanceId, *disk.DiskSize, scaleDiskInfo.TargetDiskSize)
				continue
			}

			diskId := *disk.DiskId
			if diskId == "" {
				cvmInstance := cvmMap[*instance.InstanceId]
				for _, dataDisk := range cvmInstance.DataDisks {
					if *dataDisk.DiskSize == *disk.DiskSize {
						diskId = *dataDisk.DiskId
					}
				}
			}
			logger.Infof("%s ScaleUpSetatsDisk -> ResizeDiskForId instance %s, diskId %s, disk size %d, target disk size %d", c.requestId, *instance.InstanceId, diskId, *disk.DiskSize, scaleDiskInfo.TargetDiskSize)
			_, err = cbsService.CloudApi.ResizeDiskForId(c.ctx.ClusterGroup.NetEnvironmentType, c.ctx.ClusterGroup.Region, *disk.DiskId, uint64(scaleDiskInfo.TargetDiskSize))
			if err != nil {
				logger.Errorf("%s cbsservice ResizeDiskForId error:%+v", c.requestId, err)
				return fmt.Sprintf("%s cbsservice ResizeDiskForId error:%+v", c.requestId, err)
			}
		}
	}
	logger.Infof("%s start to run command for  %+v", c.requestId, instances)
	// run command
	command := "cmVzaXplMmZzIC9kZXYvdmRiICYmIGRmIC1USHwgZ3JlcCAnL2Rldi92ZGIn"
	commandRespSet, err := cvmService.RunCommandForIdsWithScsAccount(c.ctx.ClusterGroup.NetEnvironmentType, c.ctx.ClusterGroup.Region, instances, command)
	if err != nil {
		logger.Errorf("%s ScaleUpSetatsDisk -> RunCommandForIdsWithScsAccount error:%+v", c.requestId, err)
		return fmt.Sprintf("%s ScaleUpSetatsDisk -> RunCommandForIdsWithScsAccount error:%+v", c.requestId, err)
	}
	invocationIds := make([]string, 0)
	for _, commandResp := range commandRespSet {
		if commandResp != nil {
			logger.Infof("%s ScaleUpSetatsDisk -> RunCommandForIdsWithScsAccount commandResp %+v", c.requestId, commandResp)
			invocationIds = append(invocationIds, *commandResp.InvocationId)
		}
	}
	c.ctx.SetReturnParamUseJson(constants.FLOW_RETURN_PARAM_SETATS_SCALE_DISK_INVOCATION_IDS, invocationIds)
	return ""
}
