package taskHandler

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"runtime/debug"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/component"
	constants2 "tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/tag"
	flow2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/flow"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource_auth"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
	"time"
)

func NewDeployHandler(c deploy.Component) flow.TaskHandler {
	return NewCreateClusterBaseHandler(deploy.NewHandler(c))
}

type createClusterBaseHandler struct {
	taskStatusHandler taskStatusHandler
}

type taskStatusHandler interface {
	HandleTaskStatusInit(requestId string, request *flow.TaskExecRequest) (resp *flow.TaskExecResponse)
	HandleTaskStatusRunning(requestId string, request *flow.TaskExecRequest) (resp *flow.TaskExecResponse)
}

func (this *createClusterBaseHandler) CompleteTask(request *flow.TaskExecRequest) (resp *flow.TaskExecResponse) {
	flowService := flow2.GetFlowService()

	params := request.Params
	requestId, _, _ := flowService.GetFlowParamString(params, constants2.FLOW_PARAM_REQUEST_ID, "")

	defer func() {
		if err := recover(); err != nil {
			logger.Infof("[%s] err %v", requestId, string(debug.Stack()))
			resp = flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1, fmt.Sprintf("%v", err), params)
		}
	}()

	taskStatusHandler := this.taskStatusHandler
	if taskStatusHandler == nil {
		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1, "taskStatusHandler is nil", params)
	}

	status, err := flowService.GetTaskStatus(request)
	if err != nil {
		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1, err.Error(), params)
	}

	switch status {
	case flow.TASK_STATUS_SUCCESS:
		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_SUCCESS, flow.TASK_STATUS_SUCCESS, 0.1, "ok", params)
	case flow.TASK_STATUS_RUNNING:
		return taskStatusHandler.HandleTaskStatusRunning(requestId, request)
	case flow.TASK_STATUS_INIT:
		return taskStatusHandler.HandleTaskStatusInit(requestId, request)
	case flow.TASK_STATUS_FAILED:
		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1, "", params)
	default:
		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1, "", params)
	}
}

func NewCreateClusterBaseHandler(handler taskStatusHandler) *createClusterBaseHandler {
	return &createClusterBaseHandler{taskStatusHandler: handler}
}

func bindTags(params map[string]string, clusterGroup *table.ClusterGroup, resourceRrefix string) (err error) {
	flowService := flow2.GetFlowService()

	tagsStr, exist, err := flowService.GetFlowParamString(params, constants2.FLOW_PARAM_TAGS, "")
	if err != nil {
		return errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	if !exist || len(tagsStr) == 0 {
		return nil
	}

	tagBytes, err := base64.StdEncoding.DecodeString(tagsStr)
	if err != nil {
		return errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	tags := make([]*tag.Tag, 0)
	if err := json.Unmarshal(tagBytes, &tags); err != nil {
		return errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	for _, t := range tags {
		// createUin传ownerUin，避免子账户无权限绑定标签，基本假设：客户有创建集群权限，就能绑定标签
		tagComponent := component.NewTagComponent()
		_, err := tagComponent.BatchAddResourcesTag(clusterGroup.OwnerUin, clusterGroup.OwnerUin,
			clusterGroup.Region, resource_auth.RESOURCE_SERVICE_TYPE, resourceRrefix,
			t.TagKey, t.TagValue, []string{clusterGroup.SerialId}, time.Now().Unix(), map[string]string{})
		if err != nil {
			// 忽略标签已关联的错误
			if tagComponent.GetReturnCode() == component.ResourceInUse_TagKeyAttached {
				logger.Infof("Ignore attached tag: (%s, %s) %v", t.TagKey, t.TagValue, err)
				continue
			}
			return err
		}
	}

	return nil
}
