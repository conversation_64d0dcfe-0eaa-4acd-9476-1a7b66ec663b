package taskHandler

import (
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"testing"
)

func Test_InitCDBHandler_CompleteTask(t *testing.T) {
	request := &flow.TaskExecRequest{
		Retrycount: 0,
		Processkey: "",
		Taskcode:   "",
		DocId:      "",
		FlowId:     "",
		TaskId:     "",
		Params: map[string]string{
			constants.FLOW_PARAM_CLUSTER_GROUP_ID: *fTestClusterGroupId,
			constants.FLOW_PARAM_CLUSTER_ID:       fmt.Sprintf("%d", *fTestClusterId),
			constants.FLOW_PARAM_CLUSTER_TYPE:       "1",
		},
	}

	handler := &InitCDBHandler{}
	rsp := handler.CompleteTask(request)
	t.Log(rsp)
}
