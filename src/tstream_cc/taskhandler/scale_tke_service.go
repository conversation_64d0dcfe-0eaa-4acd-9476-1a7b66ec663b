package taskHandler

import (
	"encoding/json"
	"errors"
	"fmt"
	cvm2 "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/cvm/v20170312"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/fields"
	"k8s.io/apimachinery/pkg/labels"
	"sort"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/auth"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	table3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/Cvm"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cbs"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cdb"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cvm"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/tke"
	yuntiService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/yunti"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type ScaleClusterService struct {
}

func (s *ScaleClusterService) Apps(ctx *deploy.Context) (result []apps.App, err error) {
	result = make([]apps.App, 0)
	targetCuNum, exist, err := ctx.GetFlowParamInt(ctx.Request.Params, constants.FLOW_PARAM_SCALE_CLUSTER_TARGET_CU_NUM, 0)
	if err != nil {
		return
	}
	if !exist {
		return result, errors.New("can not find ScaleTargetCuNum")
	}

	cg := ctx.ClusterGroup
	currentCu := cg.CuNum
	cg.CuNum = int16(targetCuNum)

	if int16(targetCuNum) == currentCu {
		return result, nil
	}

	if ctx.Cdb == nil {
		return result, errors.New("can not find Cdb")
	}

	// 共享集群母集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM {

	}
	// 独享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId == "" {
		if int16(targetCuNum) > currentCu && !auth.IsInWhiteList(int64(cg.AppId), constants.WHITE_LIST_SCALE_UP_PROTECTED) {
			result = append(result, &transformPooledCvmApp{ctx: ctx})
			result = append(result, &privateClusterCreateWorkerApp{ctx: ctx})
			result = append(result, &createNodeEniApp{ctx: ctx})
		} else if !auth.IsInWhiteList(int64(cg.AppId), constants.WHITE_LIST_SCALE_DOWN_PROTECTED) {
			result = append(result, &poolCvmApp{ctx: ctx, pcProvider: &scaleDownClusterPoolCvmProvider{}})
			result = append(result, &scaleDownCvmApp{ctx: ctx})
			result = append(result, &scaleDownNodeEniApp{ctx: ctx})
		}
		result = append(result, &upgradeCdbApp{ctx: ctx})
	}
	// 共享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId != "" {
		if cg.ResourceType == constants.RESOURCE_TYPE_PRIVATE {
			if int16(targetCuNum) > currentCu && !auth.IsInWhiteList(int64(cg.AppId), constants.WHITE_LIST_SCALE_UP_PROTECTED) {
				result = append(result, &transformPoolWorkerToWorkerApp{ctx: ctx, needCU: int16(targetCuNum) - currentCu})
				result = append(result, &uniformClusterTransformWorkerToPrivateApp{ctx: ctx, needCU: int16(targetCuNum) - currentCu})
				//result = append(result, &createPoolWorkerForWorkerApp{ctx: ctx})
			} else {
				//result = append(result, &uniformScaleDownCvmApp{ctx: ctx})
			}
		}
		// 处理 resource quota
		//result = append(result, &CreateResourceQuotaApp{ctx: ctx})
	}

	result = append(result, &updateClusterCuApp{ctx: ctx})

	return result, nil
}

type upgradeCdbApp struct {
	apps.ApplyReadyApp
	ctx *deploy.Context
}

func (c *upgradeCdbApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	cg := c.ctx.ClusterGroup

	memory, err := c.ctx.FlowCC.CdbMemoryMB(int64(cg.CuNum))
	if err != nil {
		return
	}
	volume, err := c.ctx.FlowCC.CdbVolumeGB(int64(cg.CuNum))
	if err != nil {
		return
	}

	if c.ctx.Cdb.Memory > memory {
		memory = c.ctx.Cdb.Memory
	}

	if c.ctx.Cdb.Volume > volume {
		volume = c.ctx.Cdb.Volume
	}

	if c.ctx.Cdb.Memory != memory || c.ctx.Cdb.Volume != volume {
		cdbService := cdb.GetCdbService()
		err = cdbService.UpgradeDBInstanceResourceByNetworkEnvType(cg.NetEnvironmentType, cg.Region, c.ctx.Cdb.InstanceId, memory, volume)
		if err != nil {
			return nil, err
		}

		cdbService.UpdateInstanceInfo(c.ctx.Cdb.Id, memory, volume)
	}
	return nil, nil
}

func (c *upgradeCdbApp) Ready(_ apps.Client, _ interface{}) (_ bool, err error) {
	cg := c.ctx.ClusterGroup
	cdbService := cdb.GetCdbService()

	cdbInstance, err := cdbService.DescribeDBInstanceByNetworkEnvType(cg.NetEnvironmentType, cg.Region, c.ctx.Cdb.InstanceId)
	if err != nil {
		return
	}
	if !cdbService.IsInstanceRunning(cdbInstance) {
		return false, errors.New("wait cdb running")
	}
	return true, nil
}

type uniformScaleDownCvmApp struct {
	apps.ApplyApp
	ctx *deploy.Context

	needToDeleteMap map[string]struct{}
	unSchedulerMap  map[string]struct{}
	driveJobMap     map[string]struct{}
	addLabelMap     map[string]struct{}
	schedulerMap    map[string]struct{}

	tmpNodeMap map[string]*v1.Node
}

func (c *uniformScaleDownCvmApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	c.needToDeleteMap = make(map[string]struct{})
	c.tmpNodeMap = make(map[string]*v1.Node)
	_, err = c.ctx.GetParamUseJson(constants.FlowParamNeedToDeleteCvm, &c.needToDeleteMap)
	if err != nil {
		return
	}

	c.unSchedulerMap = make(map[string]struct{})
	_, err = c.ctx.GetParamUseJson(constants.FlowParamUnSchedulerCvm, &c.unSchedulerMap)
	if err != nil {
		return
	}

	c.driveJobMap = make(map[string]struct{})
	_, err = c.ctx.GetParamUseJson(constants.FlowParamDriveJobCvm, &c.driveJobMap)
	if err != nil {
		return
	}

	c.addLabelMap = make(map[string]struct{})
	_, err = c.ctx.GetParamUseJson(constants.FlowParamAddLabelCvm, &c.addLabelMap)
	if err != nil {
		return
	}
	c.schedulerMap = make(map[string]struct{})
	_, err = c.ctx.GetParamUseJson(constants.FlowParamSchedulerCvm, &c.schedulerMap)
	if err != nil {
		return
	}

	targetWorkerCount, err := ComputeWorkerNodeNum(c.ctx)
	if err != nil {
		return
	}

	logger.Infof("targetWorkerCount %v", targetWorkerCount)
	//1. 通过标签找到需要下架的CVM
	tkeInstanceSet, err := GetTkeNodeToDelete(c.ctx, int(targetWorkerCount))
	if err != nil {
		return
	}

	for _, ins := range tkeInstanceSet {
		c.needToDeleteMap[*ins] = struct{}{}
	}
	c.ctx.SetReturnParamUseJson(constants.FlowParamNeedToDeleteCvm, c.needToDeleteMap)

	b, _ := json.Marshal(c.needToDeleteMap)
	logger.Infof("%s: %s", constants.FlowParamNeedToDeleteCvm, string(b))

	//2. 给CVM进行unscheduler
	if err = c.unSchedulerCVM(); err != nil {
		return
	}
	//3. 驱逐CVM上存在的作业
	if err = c.driveCVMJobs(); err != nil {
		return
	}
	//4. 打上共享worker标签
	if err = c.addCVMLabels(); err != nil {
		return
	}

	//5. 给CVM进行scheduler
	if err = c.schedulerCVM(); err != nil {
		return
	}

	return
}

func (c *uniformScaleDownCvmApp) unSchedulerCVM() (err error) {
	k8sService := k8s.GetK8sService()
	for i := range c.needToDeleteMap {
		instanceId := i
		if _, ok := c.unSchedulerMap[instanceId]; ok {
			continue
		}
		node, err := getNodeById(c.ctx, instanceId, c.tmpNodeMap)
		if err != nil {
			return err
		}
		if _, err = k8sService.NodeSchedule(c.ctx.ClientSet(), node.Name, true); err != nil {
			return err
		}
		c.unSchedulerMap[instanceId] = struct{}{}
		c.ctx.SetReturnParamUseJson(constants.FlowParamUnSchedulerCvm, c.unSchedulerMap)
	}

	b, _ := json.Marshal(c.unSchedulerMap)
	logger.Infof("%s: %s", constants.FlowParamUnSchedulerCvm, string(b))
	return
}

func (c *uniformScaleDownCvmApp) schedulerCVM() (err error) {
	k8sService := k8s.GetK8sService()
	for i := range c.addLabelMap {
		instanceId := i
		if _, ok := c.schedulerMap[instanceId]; ok {
			continue
		}
		node, err := getNodeById(c.ctx, instanceId, c.tmpNodeMap)
		if err != nil {
			return err
		}
		if _, err = k8sService.NodeSchedule(c.ctx.ClientSet(), node.Name, false); err != nil {
			return err
		}
		c.schedulerMap[instanceId] = struct{}{}
		c.ctx.SetReturnParamUseJson(constants.FlowParamSchedulerCvm, c.schedulerMap)
	}

	b, _ := json.Marshal(c.schedulerMap)
	logger.Infof("%s: %s", constants.FlowParamSchedulerCvm, string(b))
	return
}

func (c *uniformScaleDownCvmApp) driveCVMJobs() (err error) {
	k8sService := k8s.GetK8sService()
	for i := range c.unSchedulerMap {
		instanceId := i
		if _, ok := c.driveJobMap[instanceId]; ok {
			continue
		}
		node, err := getNodeById(c.ctx, instanceId, c.tmpNodeMap)
		if err != nil {
			return err
		}

		fSet := fields.Set{"spec.nodeName": node.Name}
		deleted, err := k8sService.DeletePods(c.ctx.ClientSet(), c.ctx.ClusterGroup.SerialId,
			metav1.ListOptions{FieldSelector: fields.SelectorFromSet(fSet).String(),
				LabelSelector: fmt.Sprintf("type=flink-native-kubernetes")})
		if err != nil {
			return err
		}
		logger.Infof("Node %s delete all Job Pod %s", instanceId, strings.Join(deleted, ","))

		c.driveJobMap[instanceId] = struct{}{}
		c.ctx.SetReturnParamUseJson(constants.FlowParamDriveJobCvm, c.driveJobMap)
	}

	b, _ := json.Marshal(c.driveJobMap)
	logger.Infof("%s: %s", constants.FlowParamDriveJobCvm, string(b))
	return
}

func (c *uniformScaleDownCvmApp) addCVMLabels() (err error) {
	for i := range c.driveJobMap {
		instanceId := i
		if _, ok := c.addLabelMap[instanceId]; ok {
			continue
		}
		node, err := getNodeById(c.ctx, instanceId, c.tmpNodeMap)
		if err != nil {
			return err
		}

		nodes := make([]v1.Node, 0)
		nodes = append(nodes, *node)

		err = transformPrivateCvmToShare(c.ctx, nodes)
		if err != nil {
			return err
		}

		c.addLabelMap[instanceId] = struct{}{}
		c.ctx.SetReturnParamUseJson(constants.FlowParamAddLabelCvm, c.addLabelMap)
	}

	b, _ := json.Marshal(c.addLabelMap)
	logger.Infof("%s: %s", constants.FlowParamAddLabelCvm, string(b))
	return
}

func getNodeById(ctx *deploy.Context, instanceId string, tmpNodeMap map[string]*v1.Node) (node *v1.Node, err error) {
	node, ok := tmpNodeMap[instanceId]
	if ok {
		return node, nil
	}

	k8sService := k8s.GetK8sService()
	nodeList, err := k8sService.ListNode(ctx.ClientSet(), metav1.ListOptions{
		LabelSelector: fmt.Sprintf("%s==%s", "cloud.tencent.com/node-instance-id", instanceId),
	})
	if err != nil {
		return
	}
	if len(nodeList.Items) == 0 {
		return nil, fmt.Errorf("can not find node %s", instanceId)
	}

	if len(nodeList.Items) != 1 {
		return nil, fmt.Errorf("%s number is not one, but %d", instanceId, len(nodeList.Items))
	}

	return &nodeList.Items[0], nil
}

type scaleDownClusterPoolCvmProvider struct {
}

func (c *scaleDownClusterPoolCvmProvider) TryPoolCvmIds(ctx *deploy.Context) ([]*string, error) {
	targetWorkerCount, err := ComputeWorkerNodeNum(ctx)
	if err != nil {
		return nil, err
	}

	logger.Infof("targetWorkerCount %v", targetWorkerCount)

	tkeInstanceSet, err := GetTkeNodeToDelete(ctx, int(targetWorkerCount))
	return tkeInstanceSet, err
}

type scaleDownCvmApp struct {
	apps.ApplyApp
	ctx            *deploy.Context
	instanceSucc   map[string]struct{}
	instanceFailed map[string]struct{}
}

func (c *scaleDownCvmApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {

	c.instanceSucc = make(map[string]struct{})
	c.instanceFailed = make(map[string]struct{})
	_, err = c.ctx.GetParamUseJson(constants.FlowParamDeleteTkeInstanceSucc, &c.instanceSucc)
	if err != nil {
		return
	}
	_, err = c.ctx.GetParamUseJson(constants.FlowParamDeleteTkeInstanceFailed, &c.instanceFailed)
	if err != nil {
		return
	}

	targetWorkerCount, err := ComputeWorkerNodeNum(c.ctx)
	if err != nil {
		return
	}

	logger.Infof("targetWorkerCount %v", targetWorkerCount)

	tkeInstanceSet, err := GetTkeNodeToDelete(c.ctx, int(targetWorkerCount))
	if err != nil {
		return
	}

	if len(tkeInstanceSet) != 0 {
		instanceBytes, _ := json.Marshal(tkeInstanceSet)
		logger.Infof("removeInstanceFromTke %s", string(instanceBytes))

		err = c.removeInstanceFromTke(tkeInstanceSet)
		if err != nil {
			return
		}
		// 不管成功失败， 都先直接返回，cvm的销毁等下一次 task回调再来处理
		return nil, errors.New("remove node from tke")
	}

	err = c.terminateCVM()
	return
}

func ComputeWorkerNodeNum(ctx *deploy.Context) (count uint64, err error) {
	cg := ctx.ClusterGroup

	h := &ApplyTKEHandler{}

	count, _, _, err = h.ComputeWorkerNodeAndCuNum(cg.CuNum, ctx)
	return
}

func GetTkeNodeToDelete(ctx *deploy.Context, expectWorkerCount int) (instanceSet []*string, err error) {
	instanceSet = make([]*string, 0)

	cg := ctx.ClusterGroup
	if cg.Type == constants.CLUSTER_GROUP_TYPE_SUB_EKS {
		return
	}
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId != "" {
		return
	}

	privateLabel := map[string]string{
		constants.TKE_CVM_LABEL_KEY: constants.TKE_WORKER_NODE_LABEL_VAL,
	}

	k8sService := ctx.K8sService()
	workerList, err := k8sService.ListNode(ctx.ClientSet(), metav1.ListOptions{
		LabelSelector: labels.FormatLabels(privateLabel),
	})
	if err != nil {
		return
	}
	workerCount := len(workerList.Items)
	if expectWorkerCount >= workerCount {
		logger.Infof("expectWorkerCount %d, worker node cnt %d", expectWorkerCount, workerCount)
		return nil, nil
	}

	canDeleteWorkIdSet := make([]*string, 0)
	for _, node := range workerList.Items {
		labelMap := node.GetObjectMeta().GetLabels()
		if _, ok := labelMap[constants.TKE_CVM_WORKER_LABEL_KEY]; ok {
			continue
		}
		if _, ok := labelMap[constants.TKE_CVM_WORKER_MANAGE_KEY]; ok {
			continue
		}
		if val, ok := labelMap[constants.TKE_CVM_LABEL_KEY]; ok && val == constants.TKE_WORKER_NODE_LABEL_VAL {
			if instanceId, ok := labelMap["cloud.tencent.com/node-instance-id"]; ok {
				canDeleteWorkIdSet = append(canDeleteWorkIdSet, &instanceId)
			} else {
				msg := fmt.Sprintf("not find instance id for %s, name %s", cg.SerialId, node.Name)
				return nil, errorcode.InternalErrorCode.NewWithMsg(msg)
			}
		}
	}

	instanceBytes, _ := json.Marshal(canDeleteWorkIdSet)
	logger.Infof("canDeleteWorkIdSet %s", string(instanceBytes))

	if len(canDeleteWorkIdSet) <= 0 {
		return
	}

	// 公有云可以按实例排序，内网账号的集群购买的CVM都是按照配置购买的，实例类型并不一定是一样，所以如果不是内网账号这里才执行这段逻辑
	if cg.NetEnvironmentType == constants.NETWORK_ENV_CLOUD_VPC {
		region := cg.Region
		tkeCC, err := ctx.FlowCC.TkeCC().ClusterConfig(cg)
		if err != nil {
			return nil, errorcode.InternalErrorCode.NewWithInfo("scale own Get Cluster Config Error", err)
		}

		workerSpec := cvm.GetCvmService().GetWorkerSpec(ctx.Cluster, tkeCC)

		cvmConfList, err := service3.GetTableService().ListCvmSaleConfByInstanceType(workerSpec, 0, 1)
		if err != nil || len(cvmConfList) == 0 {
			return nil, errorcode.InternalErrorCode.NewWithInfo("scale down ListCvmSaleConfByInstanceType Error, length = 0", err)
		}
		cvmSaleConfList, err := service3.GetTableService().ListActiveCvmSaleConfWithCpuMemory(cvmConfList[0].Cpu, cvmConfList[0].Memory)
		if err != nil || len(cvmSaleConfList) == 0 {
			msg := fmt.Sprintf("scale down ListActiveCvmSaleConfWithCpuMemory Error, len %d", len(cvmSaleConfList))
			return nil, errorcode.InternalErrorCode.NewWithInfo(msg, err)
		}
		cvmSaleConfMap := make(map[string]*table3.CvmSaleConfig, len(cvmSaleConfList))
		for _, config := range cvmSaleConfList {
			cvmSaleConfMap[config.InstanceType] = config
		}

		cvmService := cvm.GetCvmService()
		cvmInstanceSet, err := cvmService.DescribeInstancesWithScsAccount(cg.NetEnvironmentType, region, canDeleteWorkIdSet)
		if err != nil || len(cvmInstanceSet) == 0 {
			msg := fmt.Sprintf("scale down DescribeInstancesWithScsAccount Error, %d", len(cvmInstanceSet))
			return nil, errorcode.InternalErrorCode.NewWithInfo(msg, err)
		}

		cvmInstanceMap := make(map[string]*cvm2.Instance, len(canDeleteWorkIdSet))
		for _, cvmInstance := range cvmInstanceSet {
			cvmInstanceMap[*cvmInstance.InstanceId] = cvmInstance
		}
		sort.Slice(canDeleteWorkIdSet, func(i, j int) bool {
			iInstanceId := canDeleteWorkIdSet[i]
			jInstanceId := canDeleteWorkIdSet[j]
			iInstance, ok := cvmInstanceMap[*iInstanceId]
			if !ok {
				return true
			}
			jInstance, ok := cvmInstanceMap[*jInstanceId]
			if !ok {
				return false
			}
			iConfig, ok := cvmSaleConfMap[*iInstance.InstanceType]
			if !ok {
				return true
			}
			jConfig, ok := cvmSaleConfMap[*jInstance.InstanceType]
			if !ok {
				return false
			}
			if iConfig.Price > jConfig.Price {
				return true
			} else if iConfig.Price < jConfig.Price {
				return false
			}
			if iConfig.Generation < jConfig.Generation {
				return true
			} else if iConfig.Generation > jConfig.Generation {
				return false
			}
			// 确保每次选择出来的机器相同
			if *iInstance.InstanceId > *jInstance.InstanceId {
				return false
			} else if *iInstance.InstanceId < *jInstance.InstanceId {
				return true
			}
			return true
		})
	}

	needDeleteCount := workerCount - expectWorkerCount
	if needDeleteCount >= len(canDeleteWorkIdSet) {
		instanceSet = canDeleteWorkIdSet
	} else {
		instanceSet = canDeleteWorkIdSet[0:needDeleteCount]
	}
	return
}

func (c *scaleDownCvmApp) removeInstanceFromTke(instanceId []*string) (err error) {
	cg := c.ctx.ClusterGroup
	region := cg.Region
	tkeService := tke.GetTkeService()

	rsp, err := tkeService.DeleteClusterInstancesForIdsByNetEnvironmentType(cg.NetEnvironmentType, region, c.ctx.Tke.InstanceId, instanceId)
	if err != nil {
		return
	}

	tmp, _ := json.Marshal(rsp)
	logger.Info("[%s]: DeleteClusterInstances return rsp: %s", string(tmp))

	for _, ins := range rsp.Response.SuccInstanceIds {
		c.instanceSucc[*ins] = struct{}{}
	}
	c.ctx.SetReturnParamUseJson(constants.FlowParamDeleteTkeInstanceSucc, c.instanceSucc)

	for _, ins := range rsp.Response.FailedInstanceIds {
		c.instanceFailed[*ins] = struct{}{}
	}
	c.ctx.SetReturnParamUseJson(constants.FlowParamDeleteTkeInstanceFailed, c.instanceFailed)

	return
}

func toSliceStringPtr(m map[string]struct{}) []*string {
	r := make([]*string, 0, len(m))
	for k, _ := range m {
		kk := k
		r = append(r, &kk)
	}
	return r
}

func toSliceString(m map[string]struct{}) []string {
	r := make([]string, 0, len(m))
	for k, _ := range m {
		r = append(r, k)
	}
	return r
}

func (c *scaleDownCvmApp) terminateCVM() (err error) {
	if len(c.instanceSucc) == 0 {
		return
	}

	cg := c.ctx.ClusterGroup

	cvmInstanceIds := toSliceStringPtr(c.instanceSucc)

	cvmService := cvm.GetCvmService()
	cvmInstanceSet, err := cvmService.DescribeInstancesWithScsAccount(cg.NetEnvironmentType, cg.Region, cvmInstanceIds)
	if err != nil {
		return errorcode.InternalErrorCode.NewWithErr(err)
	}

	if len(cvmInstanceSet) == 0 {
		return nil
	}

	newCvmInstanceIds := make([]*string, 0)

	for _, instance := range cvmInstanceSet {
		if *instance.InstanceState != "TERMINATING" {
			newCvmInstanceIds = append(newCvmInstanceIds, instance.InstanceId)
		}
	}

	if len(newCvmInstanceIds) == 0 {
		return nil
	}

	// 如果是内网账号的集群则销毁cvm要走云梯的接口
	if cg.NetEnvironmentType == constants.NETWORK_ENV_INNER_VPC {
		serialId := cg.SerialId
		return yuntiService.TerminateInstances(fmt.Sprintf("CSIG流计算Oceanus集群%s缩容", serialId), newCvmInstanceIds, cg.Region, serialId)
	}

	err = cbs.GetCbsService(cg.NetEnvironmentType, cg.Region).TerminateCbsFromCVM(cvmInstanceIds)
	if err != nil {
		return err
	}

	return cvm.GetCvmService().TerminateInstancesWithScsAccount(cg.Region, newCvmInstanceIds)
}
