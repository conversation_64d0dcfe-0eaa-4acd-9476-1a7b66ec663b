package taskHandler

import (
	"encoding/json"
	"fmt"
	cvm "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/cvm/v20170312"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	service2 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	table3 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/Cvm"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cbs"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config/flowCC"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/configuration_center"
	cvm2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cvm"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/tke"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type isolateCvmService struct {
}

func (s *isolateCvmService) Apps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	cg := ctx.ClusterGroup

	isCluster, exists := ctx.GetParam(constants.FLOW_PARAM_SETATS_IS_CLUSTER, "1")
	if exists {
		if isCluster == "0" {
			return result, nil
		}
	}

	// 共享集群母集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM {
	}

	// 独享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId == "" {

	}

	// 共享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId != "" {
		if cg.ResourceType == constants.RESOURCE_TYPE_PRIVATE {
			//result = append(result, &uniformClusterIsolateCvmApp{ctx: ctx})
		}
	}

	return result, nil
}

type uniformClusterIsolateCvmApp struct {
	apps.ApplyApp
	ctx *deploy.Context
}

func (c *uniformClusterIsolateCvmApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	if c.ctx.Tke.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		return
	}

	cg := c.ctx.ClusterGroup

	privateLabel := GetPrivateWorkerLabel(cg.Zone, int64(cg.AppId))

	k8sService := k8s.GetK8sService()
	nodeList, err := k8sService.ListNode(c.ctx.ClientSet(), metav1.ListOptions{
		LabelSelector: labels.FormatLabels(privateLabel),
	})
	if err != nil {
		return
	}
	err = transformPrivateCvmToShare(c.ctx, nodeList.Items)
	return
}

func transformPrivateCvmToShare(ctx *deploy.Context, nodes []v1.Node) (err error) {
	privateLabel := GetPrivateWorkerLabel(ctx.ClusterGroup.Zone, int64(ctx.ClusterGroup.AppId))
	for _, node := range nodes {
		labelMap := node.GetObjectMeta().GetLabels()
		for k := range privateLabel {
			if constants.TKE_CVM_NODE_ZONE == k {
				continue
			}
			delete(labelMap, k)
		}

		labelMap[constants.TKE_CVM_NODE_RESOURCE_TYPE] = constants.TKE_CVM_NODE_RESOURCE_TYPE_SHARE

		_, err = k8s.GetK8sService().AddNodeLabel(ctx.ClientSet(), node.Name, labelMap)
		if err != nil {
			return
		}
	}
	return
}

func transformShareCvmToPrivate(ctx *deploy.Context, nodes []v1.Node) (err error) {
	privateLabel := GetPrivateWorkerLabel(ctx.ClusterGroup.Zone, int64(ctx.ClusterGroup.AppId))
	for _, node := range nodes {
		labelMap := node.GetObjectMeta().GetLabels()

		for k, v := range privateLabel {
			labelMap[k] = v
		}

		_, err = k8s.GetK8sService().AddNodeLabel(ctx.ClientSet(), node.Name, labelMap)
		if err != nil {
			return
		}

		needCvmCount, _, err := ctx.GetParamInt(constants.FlowParamNeedCvmCount, 0)
		if err != nil {
			return err
		}
		ctx.SetReturnParam(constants.FlowParamNeedCvmCount, fmt.Sprintf("%d", needCvmCount-1))
	}
	return
}

type isolateCvmApp struct {
	apps.ApplyApp
	ctx *deploy.Context
}

func (c *isolateCvmApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	if c.ctx.Tke.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		return
	}

	if c.ctx.ClusterGroup.NetEnvironmentType == constants.NETWORK_ENV_INNER_VPC {
		return
	}

	/**
	 * 隔离回收cvm，只回收 running 状态的， 不然如果重复执行， terminate 多次 cvm就直接被销毁了，后面就恢复不回来了、
	 * https://cloud.tencent.com/document/api/213/15723
	 * 包年包月实例首次调用本接口，实例将被移至回收站，再次调用本接口，实例将被销毁，且不可恢复。按量计费实例调用本接口将被直接销毁。
	 */
	runningIds, otherIds, err := getTkeCvm(c.ctx, cvm2.GetCvmService().IsInstanceRunning)
	if err != nil {
		return
	}

	if len(otherIds) > 0 {
		b, _ := json.Marshal(otherIds)
		logger.Infof("No need to isolate cvms: %s", string(b))
	}

	err = c.deleteCvm(runningIds)
	return
}

func (c *isolateCvmApp) deleteCvm(cvmInstances []*string) (err error) {
	if len(cvmInstances) == 0 {
		return
	}
	b, _ := json.Marshal(cvmInstances)
	logger.Infof("delete cvms: %s", string(b))

	region := c.ctx.Region
	cvmService := cvm2.GetCvmService()
	return cvmService.TerminateInstancesWithScsAccount(region, cvmInstances)
}

type poolCvmProvider interface {
	TryPoolCvmIds(ctx *deploy.Context) ([]*string, error)
}

type deleteClusterPoolCvmProvider struct {
}

func (c *deleteClusterPoolCvmProvider) TryPoolCvmIds(ctx *deploy.Context) ([]*string, error) {
	poolCvmIds, otherIds, err := getTkeCvm(ctx, cvm2.GetCvmService().InstanceCanTerminate)
	if err != nil {
		return nil, err
	}

	if len(otherIds) > 0 {
		b, _ := json.Marshal(otherIds)
		logger.Infof("No need to pool cvms: %s", string(b))
	}

	return poolCvmIds, nil
}

type poolCvmApp struct {
	apps.ApplyApp
	ctx            *deploy.Context
	instanceSucc   map[string]struct{}
	instanceFailed map[string]struct{}
	pcProvider     poolCvmProvider
}

func (c *poolCvmApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	cg := c.ctx.ClusterGroup

	if c.ctx.Tke.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		return
	}

	if cg.NetEnvironmentType == constants.NETWORK_ENV_INNER_VPC {
		return
	}

	c.instanceSucc = make(map[string]struct{})
	c.instanceFailed = make(map[string]struct{})
	_, err = c.ctx.GetParamUseJson(constants.FlowParamPoolTkeInstanceSucc, &c.instanceSucc)
	if err != nil {
		return
	}
	_, err = c.ctx.GetParamUseJson(constants.FlowParamPoolTkeInstanceFailed, &c.instanceFailed)
	if err != nil {
		return
	}

	needPooledIds, err := c.pcProvider.TryPoolCvmIds(c.ctx)
	if err != nil {
		return
	}

	canPooledIds, err := FindCanPooledCvm(cg, needPooledIds)
	if err != nil {
		return
	}

	if len(canPooledIds) == 0 {
		return
	}

	err = c.removeInstanceFromTke(canPooledIds)
	if err != nil {
		return
	}
	err = c.poolCVM()
	return
}

func FindCanPooledCvm(cg *table.ClusterGroup, tryPooledIds []*string) (canPooledIds []*string, err error) {
	if len(tryPooledIds) == 0 {
		return
	}

	cvmService := cvm2.GetCvmService()
	var cvmInstanceSet = make([]*cvm.Instance, 0)

	cvmInstanceSet, err = cvmService.DescribeInstancesWithScsAccount(cg.NetEnvironmentType, cg.Region, tryPooledIds)
	if err != nil {
		return
	}

	pooledCvmSet, err := flowCC.New(cg.Region).PooledCvmType()
	if err != nil {
		return
	}
	overCount, _ := service.GetConfigurationIntValue(constants.CONF_KEY_POOLED_CVM_LIMIT, 0)

	sql, args := dao.NewQueryBuilder("SELECT COUNT(*) AS Count FROM PooledCvm ").
		WhereEq("Status", constants.CVM_POOL_STATUS_VALID).
		Build()
	hasCount, err := service3.FetchInt64(sql, args)
	if err != nil {
		return
	}

	canPooledIds = make([]*string, 0)
	for _, instance := range cvmInstanceSet {
		if hasCount >= overCount {
			continue
		}
		if !cvm2.GetCvmService().InstanceCanTerminate(instance) {
			continue
		}
		if !contains(pooledCvmSet, *instance.InstanceType) {
			continue
		}
		canPooledIds = append(canPooledIds, instance.InstanceId)
		hasCount += 1
	}

	return canPooledIds, nil
}

func (c *poolCvmApp) removeInstanceFromTke(instanceSet []*string) (err error) {
	cg := c.ctx.ClusterGroup
	region := cg.Region
	tkeService := tke.GetTkeService()

	b, _ := json.Marshal(instanceSet)
	logger.Infof("removeInstanceFromTke %s", string(b))

	rsp, err := tkeService.DeleteClusterInstancesForIdsByNetEnvironmentType(cg.NetEnvironmentType, region, c.ctx.Tke.InstanceId, instanceSet)
	if err != nil {
		logger.Errorf("DeleteClusterInstances err: %+v", err)
		return
	}

	b, _ = json.Marshal(rsp)
	logger.Infof("DeleteClusterInstances return rsp: %s", string(b))

	for _, ins := range rsp.Response.SuccInstanceIds {
		c.instanceSucc[*ins] = struct{}{}
	}
	c.ctx.SetReturnParamUseJson(constants.FlowParamPoolTkeInstanceSucc, c.instanceSucc)

	for _, ins := range rsp.Response.FailedInstanceIds {
		c.instanceFailed[*ins] = struct{}{}
	}
	c.ctx.SetReturnParamUseJson(constants.FlowParamPoolTkeInstanceFailed, c.instanceFailed)
	return
}

func (c *poolCvmApp) poolCVM() (err error) {
	if len(c.instanceSucc) == 0 {
		return
	}
	cg := c.ctx.ClusterGroup

	cvmInstanceIds := toSliceStringPtr(c.instanceSucc)

	b, _ := json.Marshal(c.instanceSucc)
	logger.Infof("%s instanceSucc cvm %s", cg.SerialId, string(b))

	PoolCVM(cg, cvmInstanceIds)
	return
}

func PoolCVM(cg *table.ClusterGroup, cvmInstanceIds []*string) {
	if len(cvmInstanceIds) == 0 {
		return
	}

	cvmService := cvm2.GetCvmService()
	cvmInstanceSet, err := cvmService.DescribeInstancesWithScsAccount(cg.NetEnvironmentType, cg.Region, cvmInstanceIds)
	if err != nil || len(cvmInstanceSet) == 0 {
		return
	}

	cvmInstanceMap := make(map[string]*cvm.Instance)
	for _, instance := range cvmInstanceSet {
		cvmInstanceMap[*instance.InstanceId] = instance
	}

	service2.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		r := make([]string, 0, len(cvmInstanceIds))
		for _, v := range cvmInstanceIds {
			r = append(r, *v)
		}
		sql, args := dao.NewQueryBuilder("select * from PooledCvm").
			WhereEq("Status", constants.CVM_POOL_STATUS_VALID).
			WhereIn("InstanceId", r).
			Build()

		cvmPool, err := service3.TFetch[table3.PooledCvm](tx, sql, args)
		if err != nil {
			return err
		}
		for _, c := range cvmPool {
			delete(cvmInstanceMap, c.InstanceId)
		}
		cvmInstanceIdSet := make([]*string, 0)
		for _, instance := range cvmInstanceMap {
			cvmInstanceIdSet = append(cvmInstanceIdSet, instance.InstanceId)
			pv := buildPooledCvm(cg, instance)
			pvId := tx.SaveObject(pv, "PooledCvm")
			pv.Id = pvId
			logger.Infof("pooled cvm :%+v", pv)
		}
		err = cbs.GetCbsService(cg.NetEnvironmentType, cg.Region).CheckTerminateCbsFromCVM(cvmInstanceIdSet, func(disk *cvm.DataDisk) bool {
			return *disk.DiskSize == constants.TKE_WORKER_ZK_DISK_SIZE
		})
		if err != nil {
			return err
		}
		return nil
	}).Close()
}

func buildPooledCvm(cg *table.ClusterGroup, instance *cvm.Instance) *table3.PooledCvm {
	pc := &table3.PooledCvm{}
	pc.InstanceId = *instance.InstanceId

	pc.PoolTime = util.GetCurrentTime()
	pc.PooledBy = cg.SerialId

	pc.NetEnvironmentType = cg.NetEnvironmentType
	pc.Region = cg.Region
	pc.Zone = cg.Zone

	pc.InstanceType = *instance.InstanceType
	pc.Cpu = *instance.CPU
	pc.Memory = *instance.Memory

	pc.Status = constants.CVM_POOL_STATUS_VALID
	return pc
}

func contains(ss []string, s string) bool {
	for _, v := range ss {
		if v == s {
			return true
		}
	}
	return false
}
