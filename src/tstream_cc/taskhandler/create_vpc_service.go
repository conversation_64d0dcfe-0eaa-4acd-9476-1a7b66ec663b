package taskHandler

import (
	"errors"
	"fmt"
	"strconv"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	constants2 "tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource_auth"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/vpc"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type CreateVpcService struct {
}

func (s *CreateVpcService) Apps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	cg := ctx.ClusterGroup

	// 共享集群 包年包月下的eks集群
	if cg.Type == constants2.CLUSTER_GROUP_TYPE_SUB_EKS {
		result = append(result, &updateVpcAndSubnetApp{ctx: ctx})
		return result, nil
	}

	ssApp := switchClusterGroupStatusApp{ctx: ctx, status: constants2.CLUSTER_GROUP_STATUS_INIT_PROGRESS}
	btApp := bindTagsApp{ctx: ctx}
	cvApp := createVpcApp{ctx: ctx}
	csApp := createSubnetApp{ctx: ctx}

	//纵向区分有集群类型、集群版本，横向区分有 内外网、调度类型
	//功能包括：创建、升级(幂等创建)，不包括扩缩容

	// 创建共享集群母集群
	if cg.Type == constants2.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone == "" {
		result = append(result, &ssApp)
		result = append(result, &btApp)
		result = append(result, &cvApp)
		result = append(result, &csApp)
	}

	// 共享集群母集群，开新区
	if cg.Type == constants2.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone != "" {
		result = append(result, &csApp)
	}

	// 独享集群
	if cg.Type == constants2.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId == "" {
		result = append(result, &ssApp)
		result = append(result, &btApp)
		result = append(result, &cvApp)
		result = append(result, &csApp)
	}

	// 共享集群
	if cg.Type == constants2.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId != "" {
		result = append(result, &ssApp)
		result = append(result, &btApp)
		result = append(result, &updateVpcAndSubnetApp{ctx: ctx})
		//区有可能没有开，但是需要确保子网存在
		result = append(result, &csApp)
	}

	return result, nil
}

type switchClusterGroupStatusApp struct {
	apps.ApplyApp
	ctx    *deploy.Context
	status int
}

func (c *switchClusterGroupStatusApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	err = service2.SwitchClusterGroupStatusTo(c.ctx.ClusterGroup.Id, c.status)
	return
}

type bindTagsApp struct {
	ctx *deploy.Context
	apps.ApplyApp
}

func (c *bindTagsApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	err = bindTags(c.ctx.Request.Params, c.ctx.ClusterGroup, resource_auth.RESOURCE_PREFIX_CLUSTER)
	return nil, err
}

type createVpcApp struct {
	ctx *deploy.Context
	apps.ApplyApp
}

func (c *createVpcApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	params := c.ctx.Request.Params

	if c.ctx.Cluster.VpcId != constants2.DOUBLE_BAR {
		params[constants2.FLOW_PARAM_VPC_ID] = c.ctx.Cluster.VpcId
		return nil, err
	}

	// 内网VPC无需创建，但需提供
	if c.ctx.ClusterGroup.NetEnvironmentType == constants2.NETWORK_ENV_INNER_VPC {
		if c.ctx.Cluster.VpcId == constants2.DOUBLE_BAR {
			return nil, errors.New("should provide VpcId in inner vpc env, fill in Cluster table")
		}
		return nil, err
	}

	vpcId, exist, err := c.ctx.GetFlowParamString(c.ctx.Request.Params, constants2.FLOW_PARAM_VPC_ID, "")
	if err != nil {
		return nil, err
	}
	if !exist {
		vpcId, err = createVpc(c.ctx)
		if err != nil {
			return nil, err
		}
		params[constants2.FLOW_PARAM_VPC_ID] = vpcId
	}

	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.ExecuteSqlWithArgs("update Cluster set VpcId=?,SchedulerType=? where Id=?",
			vpcId, constants2.CLUSTER_SCHEDULER_TYPE_TKE, c.ctx.Cluster.Id)
		c.ctx.Cluster.VpcId = vpcId
		return nil
	}).Close()
	return nil, err
}

type createSubnetApp struct {
	ctx *deploy.Context
	apps.ApplyApp
}

func (c *createSubnetApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	zone := c.ctx.NewZone
	if zone == "" {
		zone = c.ctx.Cluster.Zone
	} else {
		var zoneSubnets map[string]string
		zoneSubnets, err = c.ctx.Cluster.GetSupportedZoneSubnets()
		if err != nil {
			return
		}
		if _, ok := zoneSubnets[zone]; ok {
			return
		}
	}

	params := c.ctx.Request.Params

	// 内网VPC无需创建，但需提供
	if c.ctx.ClusterGroup.NetEnvironmentType == constants2.NETWORK_ENV_INNER_VPC {
		//TODO 处理共享集群母集群开新区
		if c.ctx.Cluster.SubnetId != constants2.DOUBLE_BAR {
			params[constants2.FLOW_PARAM_SUBNET_ID] = c.ctx.Cluster.SubnetId
			return nil, err
		}
		return nil, errors.New("should provide SubnetId in inner vpc env, fill in Cluster table")
	}

	vpcId := c.ctx.Cluster.VpcId
	if vpcId == "" {
		return nil, errors.New("VpcId not exist")
	}
	subNetId, exist, err := c.ctx.GetFlowParamString(params, constants2.FLOW_PARAM_SUBNET_ID, "")
	if err != nil {
		return nil, err
	}
	if !exist {
		subNetId, err = createSubnetId(c.ctx, vpcId, zone)
		if err != nil {
			return nil, err
		}
		params[constants2.FLOW_PARAM_SUBNET_ID] = subNetId
	}
	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		if c.ctx.NewZone == "" {
			c.ctx.Cluster.SubnetId = subNetId
			tx.ExecuteSqlWithArgs("update Cluster set SubnetId=?,SchedulerType=? where Id=?", subNetId, constants2.CLUSTER_SCHEDULER_TYPE_TKE, c.ctx.Cluster.Id)
		} else {
			err = c.ctx.Cluster.AddSupportedZoneSubnets(zone, subNetId)
			if err != nil {
				return err
			}
			tx.ExecuteSqlWithArgs("update Cluster set SupportedZoneSubnets=?,SchedulerType=? where Id=?", c.ctx.Cluster.SupportedZoneSubnets, constants2.CLUSTER_SCHEDULER_TYPE_TKE, c.ctx.Cluster.Id)
		}
		return nil
	}).Close()
	return nil, err
}

func createVpc(s *deploy.Context) (vpcId string, err error) {
	vpcService := vpc.GetVpcService()
	cidr, err := s.CC().FlowCC().VPCCidr()
	if err != nil {
		return "", errorcode.InternalErrorCode.NewWithErr(err)
	}

	total, vpcSet, err := vpcService.DescribeVpcsWithScsAccount(
		s.ClusterGroup.Region,
		vpcService.NewDefaultDescribeVpcsRequestBuilder().
			WithFilterVpcName([]string{s.ClusterGroup.SerialId}).
			WithFilterCidrBlock([]string{cidr}).
			Build())
	if err != nil {
		return "", errorcode.InternalErrorCode.NewWithErr(err)
	}
	if total == 1 {
		return *vpcSet[0].VpcId, nil
	}
	if total > 1 {
		msg := fmt.Sprintf("to many vpcs with name %s and cidr %s, count is %d", s.ClusterGroup.SerialId, cidr, total)
		return "", errorcode.InternalErrorCode.NewWithInfo(msg, nil)
	}

	resp, err := vpcService.CreateVpcWithScsAccount(
		s.ClusterGroup.Region,
		vpcService.NewDefaultCreateVpcRequest(s.ClusterGroup.SerialId, cidr))
	if err != nil {
		return "", errorcode.InternalErrorCode.NewWithErr(err)
	}

	return *resp.Response.Vpc.VpcId, nil
}

func createSubnetId(s *deploy.Context, vpcId string, zone string) (subNetId string, err error) {
	vpcService := vpc.GetVpcService()
	cidr, err := s.CC().FlowCC().SubnetCidr()
	if err != nil {
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	cidrTimes, err := s.CC().FlowCC().SubnetCidrTimes()
	if err != nil {
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	zoneShortId, err := GetZoneShortId(zone)
	if err != nil {
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	if zoneShortId < 1 || zoneShortId > 256/int(cidrTimes) {
		err = fmt.Errorf("zone short id not in [1 ~ %d], %d, %s", 256/int(cidrTimes), zoneShortId, zone)
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	logger.Debugf("cidr=%s, zoneId=%d, times=%d", cidr, zoneShortId, cidrTimes)
	cidr, err = calcZoneCidr(cidr, zoneShortId, int(cidrTimes))
	if err != nil {
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}

	name := s.ClusterGroup.SerialId
	if s.ClusterGroup.AgentSerialId != "" {
		name = s.ClusterGroup.AgentSerialId
	}

	total, subnetSet, err := vpcService.DescribeSubnetsWithScsAccount(
		s.ClusterGroup.Region,
		vpcService.NewDefaultDescribeSubnetsRequestBuilder().
			WithFilterVpcId([]string{vpcId}).
			WithFilterSubnetName([]string{name}).
			WithFilterCidrBlock([]string{cidr}).
			WithFilterZone([]string{zone}).
			Build())
	if err != nil {
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	if total == 1 {
		return *subnetSet[0].SubnetId, nil
	} else if total > 1 {
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, fmt.Sprintf("to many subnets with vpcid %s name %s and cidr %s, count is %d", vpcId, s.ClusterGroup.SerialId, cidr, total), nil)
	}

	resp, err := vpcService.CreateSubnetScsAccount(
		s.ClusterGroup.Region,
		vpcService.NewDefaultCreateSubnetRequest(vpcId, name, cidr, zone))
	if err != nil {
		return "", errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
	}
	return *resp.Response.Subnet.SubnetId, nil
}

func GetZoneShortId(zone string) (zoneShortId int, err error) {
	zones := strings.Split(zone, "-")
	if len(zones) <= 0 {
		return 0, fmt.Errorf("zone split length less zero, %s", zone)
	}
	strZoneId := zones[len(zones)-1]
	zoneShortId, err = strconv.Atoi(strZoneId)
	return
}

func calcZoneCidr(defaultCidr string, zoneShortId int, times int) (cidr string, err error) {
	splitCidr := strings.Split(defaultCidr, ".")
	if len(splitCidr) != 4 {
		return cidr, fmt.Errorf("cidr split length not 4, %s", defaultCidr)
	}
	splitCidr[2] = strconv.Itoa((zoneShortId - 1) * times)
	return strings.Join(splitCidr, "."), nil
}

type updateVpcAndSubnetApp struct {
	ctx *deploy.Context
	apps.ApplyApp
}

func (c *updateVpcAndSubnetApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	cg := c.ctx.ClusterGroup
	cluster := c.ctx.Cluster

	if cluster.VpcId != constants2.DOUBLE_BAR {
		return true, nil
	}

	if cg.AgentSerialId == "" && cg.ParentSerialId == "" {
		return false, fmt.Errorf("%s is not uniform cluster", cg.SerialId)
	}

	serialId := cg.AgentSerialId
	if serialId == "" {
		serialId = cg.ParentSerialId
	}
	acs, err := service2.NewClusterGroupServiceBySerialId(serialId)
	if err != nil {
		return nil, err
	}

	ac, err := acs.GetActiveCluster()
	if err != nil {
		return nil, err
	}

	supportedZoneSubnets, err := ac.GetSupportedZoneSubnets()
	if err != nil {
		return nil, err
	}

	cluster.VpcId = ac.VpcId
	cluster.SubnetId = supportedZoneSubnets[cg.Zone]

	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.ExecuteSqlWithArgs("update Cluster set VpcId=?,SubnetId=? where Id=?", cluster.VpcId, cluster.SubnetId, cluster.Id)
		return nil
	}).Close()

	return nil, nil
}
