package taskHandler

import (
	"errors"
	"fmt"
	"strconv"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type CreateSetatsWorker struct {
}

func (c CreateSetatsWorker) Apps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	result = append(result, &addWorkerApp{ctx: ctx})
	return result, nil
}

type addWorkerApp struct {
	ctx *deploy.Context
	apps.ApplyReadyApp
}

func (c *addWorkerApp) Ready(s apps.Client, v interface{}) (bool, error) {
	h := &ApplyTKEHandler{}
	if allNodeRunning, err := h.TryMakeAllNodesRunning(c.ctx); err != nil {
		msg := fmt.Sprintf("addWorkerApp TryMakeAllNodesRunning Error, err %v", err)
		logger.Errorf(msg)
		return false, err
	} else if !allNodeRunning {
		msg := fmt.Sprintf("addWorkerApp allNodeRunning is false")
		return false, errors.New(msg)
	}
	return true, nil
}

func (c *addWorkerApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	workerDiskType, _ := c.ctx.GetParam(constants.FLOW_PARAM_SETATS_WORKER_DISK_TYPE, constants.TKE_CVM_DISK_TYPE)
	strWorkerrDiskSize, _ := c.ctx.GetParam(constants.FLOW_PARAM_SETATS_WORKER_DISK_SIZE, fmt.Sprintf("%d", constants.TKE_WORKER_NODE_DISK_SIZE))
	strWorkerCpu, _ := c.ctx.GetParam(constants.FLOW_PARAM_SETATS_WORKERCPU, "8")
	strParallelism, _ := c.ctx.GetParam(constants.FLOW_PARAM_SETATS_WORKERDEFAULTPARALLELISM, "1")

	parallelism, _ := strconv.Atoi(strParallelism)
	workerDiskSize, _ := strconv.Atoi(strWorkerrDiskSize)
	workerCpu, _ := strconv.ParseFloat(strWorkerCpu, 32)
	labels := make(map[string]string)
	labels[constants.TKE_CVM_LABEL_KEY] = constants.SETATS_WORKER_LABEL
	err = AddSetatsCvm("addWorkerApp", c.ctx, workerCpu, int64(workerDiskSize), workerDiskType, int64(parallelism), labels, parallelism)
	if err != nil {
		logger.Errorf("addMasterApp addSetatsCvm Error, err %v", err)
		return nil, err
	}
	return nil, nil
}
