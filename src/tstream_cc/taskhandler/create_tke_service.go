package taskHandler

import (
	"errors"
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	constants2 "tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	service4 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/tke"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type CreateTkeService struct {
}

func (s *CreateTkeService) Apps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	cg := ctx.ClusterGroup

	ctApp := &createTkeApp{ctx: ctx}
	ckApp := &checkAndSaveUniqClusterIdAndKubeConfigApp{ctx: ctx}
	crApp := &checkTkeRunningApp{ctx: ctx}

	// 共享集群 包年包月下的eks集群
	if cg.Type == constants2.CLUSTER_GROUP_TYPE_SUB_EKS {
		result = append(result, &insertTkeApp{ctx: ctx, aSerialId: cg.ParentSerialId})
		result = append(result, ckApp)
		return result, nil
	}

	// 创建共享集群母集群
	if cg.Type == constants2.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone == "" {
		result = append(result, ctApp)
		result = append(result, ckApp)
		result = append(result, crApp)
	}

	// 共享集群母集群，开新区
	if cg.Type == constants2.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone != "" {
	}

	// 独享集群
	if cg.Type == constants2.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId == "" {
		result = append(result, ctApp)
		result = append(result, ckApp)
		result = append(result, crApp)
	}

	// 共享集群
	if cg.Type == constants2.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId != "" {
		result = append(result, &insertTkeApp{ctx: ctx, aSerialId: cg.AgentSerialId})
		result = append(result, ckApp)
	}

	return result, nil
}

type createTkeApp struct {
	ctx *deploy.Context
	apps.ApplyApp
}

func (c *createTkeApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	params := c.ctx.Request.Params
	if c.ctx.Tke != nil {
		params[constants2.FLOW_PARAM_TKE_INSTANCE_ID] = c.ctx.Tke.InstanceId
		return
	}

	instanceId, exist, err := c.ctx.GetFlowParamString(params, constants2.FLOW_PARAM_TKE_INSTANCE_ID, "")
	if err != nil {
		return nil, err
	}

	if !exist { // create
		if c.ctx.ClusterType == constants2.K8S_CLUSTER_TYPE_TKE {
			instanceId, err = c.createCluster(c.ctx.RequestId, c.ctx.ArchGeneration)
		} else {
			// 暂不支持使用内网账号创建EKS集群
			if c.ctx.ClusterGroup.NetEnvironmentType == constants2.NETWORK_ENV_INNER_VPC {
				return nil, fmt.Errorf("should provide eks instance in InnerVpc")
			}
			instanceId, err = c.createEKSCluster()
		}
		if err != nil {
			return nil, err
		}
		params[constants2.FLOW_PARAM_TKE_INSTANCE_ID] = instanceId
	}

	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		sql := "insert into Tke(`ClusterId`,`InstanceId`,`InstanceName`,`ClusterType`,`Status`,`ArchGeneration`) values(?,?,?,?,?,?) on duplicate key update `InstanceName`=?"
		tx.ExecuteSqlWithArgs(sql, c.ctx.Cluster.Id, instanceId, c.ctx.ClusterGroup.SerialId, c.ctx.ClusterType, constants2.TKE_STATUS_CREATING, c.ctx.ArchGeneration, c.ctx.ClusterGroup.SerialId)
		return nil
	}).Close()
	return nil, nil
}

type checkTkeRunningApp struct {
	apps.ApplyApp
	ctx *deploy.Context
}

func (c *checkTkeRunningApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	if c.ctx.Tke == nil {
		return nil, errors.New("there is no tke instance find")
	}
	k8sInstance := c.ctx.Tke

	cg := c.ctx.ClusterGroup

	running, err := IsClusterRunning(cg, c.ctx.Cluster, k8sInstance)
	if err != nil {
		return nil, err
	}
	if !running {
		return nil, errors.New("tke is not running")
	}
	return nil, nil
}

type insertTkeApp struct {
	apps.ApplyApp
	ctx *deploy.Context

	aSerialId string
}

func (c *insertTkeApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	if c.ctx.Tke != nil {
		return nil, nil
	}

	acg, err := service2.NewClusterGroupServiceBySerialId(c.aSerialId)
	if err != nil {
		return nil, err
	}
	tkeTable, err := acg.GetTke()
	if err != nil {
		return nil, err
	}
	tkeTable.Id = 0
	tkeTable.ClusterId = c.ctx.Cluster.Id
	tkeTable.InstanceName = c.ctx.ClusterGroup.SerialId
	tkeTable.ArchGeneration = c.ctx.ArchGeneration

	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.SaveObject(tkeTable, "Tke")
		return nil
	}).Close()
	return nil, nil
}

type checkAndSaveUniqClusterIdAndKubeConfigApp struct {
	apps.ApplyApp
	ctx *deploy.Context
}

func (c *checkAndSaveUniqClusterIdAndKubeConfigApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	cg := c.ctx.ClusterGroup
	cluster := c.ctx.Cluster
	k8sInstance := c.ctx.Tke

	if cluster.KubeConfig != "" {
		return nil, nil
	}

	instanceId := k8sInstance.InstanceId
	tkeService := tke.GetTkeService()

	secretId, secretKey, err := service4.GetSecretIdAndKeyByNetworkEnvType(cg.NetEnvironmentType)
	if err != nil {
		return nil, err
	}

	var kubeConfig string
	if k8sInstance.ClusterType == constants2.K8S_CLUSTER_TYPE_TKE {
		kubeConfig, err = tkeService.GetKubeConfig(secretId, secretKey, "", cg.Region, instanceId)
	} else {
		kubeConfig, err = tkeService.GetEksKubeConfig(secretId, secretKey, "", cg.Region, instanceId)
	}
	if err != nil {
		return nil, err
	}
	cluster.UniqClusterId = instanceId
	cluster.KubeConfig = kubeConfig

	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.ExecuteSqlWithArgs("update Cluster set UniqClusterId=?,KubeConfig=? where Id=?", instanceId, kubeConfig, cluster.Id)
		return nil
	}).Close()
	return nil, nil
}
