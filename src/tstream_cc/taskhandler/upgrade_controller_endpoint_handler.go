package taskHandler

import (
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	flow2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/flow"
)

type upgradeControllerEndPointHandler struct {
}

func (this *upgradeControllerEndPointHandler) CompleteTask(request *flow.TaskExecRequest) (resp *flow.TaskExecResponse) {
	logger.Infof("request: %+v", request)

	flowService := flow2.GetFlowService()
	params := request.Params

	defer func() {
		if errs := recover(); errs != nil {
			resp = flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1,
				fmt.Sprintf("%v", errs), params)
		}
	}()

	clusterGroup, _, err := flowService.GetClusterGroupAndCluster(params)
	if err != nil {
		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1, fmt.Sprintf("%v", err), params)
	}

	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		clusterGroup.Status = constants.CLUSTER_GROUP_STATUS_RUNNING
		tx.UpdateObject(clusterGroup, clusterGroup.Id, "ClusterGroup")
		return nil
	}).Close()

	return flow.NewTaskExecResponseWithParams(flow.TASK_SUCCESS, 1.0, "ok", request.Params)
}
