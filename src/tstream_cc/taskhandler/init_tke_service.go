package taskHandler

import (
	"encoding/json"
	"fmt"
	"strconv"

	clb "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/errors"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	service4 "tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	k8s2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/k8s"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/password"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/qcloud"
	service5 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/tke"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type InitTkeService struct {
}

func (s *InitTkeService) Apps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	cg := ctx.ClusterGroup

	// 共享集群 包年包月下的eks集群，不做任何处理
	if cg.Type == constants.CLUSTER_GROUP_TYPE_SUB_EKS {
		//result = append(result, &CreateResourceQuotaApp{ctx: ctx})
		return result, nil
	}

	//network policy, security policy

	// 创建共享集群母集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone == "" {
		oceanus := ctx.FlowCC.TkeCC().OceanusCC()
		result = append(result, &createStorageClassApp{ctx: ctx, volumeBindingMode: "WaitForFirstConsumer"})
		result = append(result, &createNamespaceApp{ctx: ctx, name: "oceanus"})
		result = append(result, &createServiceAccountApp{ctx: ctx, namespace: "oceanus", name: "oceanus"})
		result = append(result, &createClusterRoleApp{ctx: ctx, name: "oceanus-cluster-role", key: "oceanus-cluster-role.yaml"})
		result = append(result, &createClusterRoleBindingApp{ctx: ctx, name: "oceanus-cluster-role-binding", roleName: "oceanus-cluster-role", serviceAccount: "oceanus", serviceAccountNamespace: "oceanus"})
		result = append(result, &CreateImagePullSecretApp{ctx: ctx, namespace: constants.OCEANUS_NAMESPACE})
		result = append(result, apps.NewPriorityClassApp(oceanus.PriorityClassHigh))
		result = append(result, &EnableEventPersistenceApp{ctx: ctx})
		result = append(result, &EnableClusterAuditApp{ctx: ctx})
		result = append(result, &OpenPolicyListApp{ctx: ctx})
	}

	// 共享集群母集群，开新区
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone != "" {
	}

	// 独享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId == "" {

		result = append(result, &createNamespaceApp{ctx: ctx, name: "oceanus"})
		result = append(result, &createServiceAccountApp{ctx: ctx, namespace: "oceanus", name: "oceanus"})
		result = append(result, &createClusterRoleApp{ctx: ctx, name: "oceanus-cluster-role", key: "oceanus-cluster-role.yaml"})
		result = append(result, &createClusterRoleBindingApp{ctx: ctx, name: "oceanus-cluster-role-binding", roleName: "oceanus-cluster-role", serviceAccount: "oceanus", serviceAccountNamespace: "oceanus"})

		result = append(result, &CreateImagePullSecretApp{ctx: ctx, namespace: constants.DEFAULT_NAMESPACE})
		result = append(result, &CreateImagePullSecretApp{ctx: ctx, namespace: constants.OCEANUS_NAMESPACE})

		result = append(result, &appendClusterConfigApp{ctx: ctx})

		oceanus := ctx.FlowCC.TkeCC().OceanusCC()
		result = append(result, apps.NewPriorityClassApp(oceanus.PriorityClassHigh))
		if ctx.Tke.ClusterType == constants.K8S_CLUSTER_TYPE_TKE {
			result = append(result,
				&createFlinkNetworkPolicyApp{ctx: ctx},
				&createSqlServerNetworkPolicyApp{ctx: ctx})
		}
		result = append(result, &createRoleApp{ctx: ctx, namespace: constants.DEFAULT_NAMESPACE, name: "oceanus-role", key: "oceanus-role.yaml"})
		result = append(result, &createRoleBindingApp{ctx: ctx, namespace: constants.DEFAULT_NAMESPACE, name: "oceanus-role-binding", roleName: "oceanus-role", serviceAccount: "oceanus", serviceAccountNamespace: constants.OCEANUS_NAMESPACE})

		result = append(result, &createRoleApp{ctx: ctx, namespace: constants.OCEANUS_NAMESPACE, name: "oceanus-role", key: constants.OCEANUS_NAMESPACE_OCEANUS_ROLE})
		result = append(result, &createRoleBindingApp{ctx: ctx, namespace: constants.OCEANUS_NAMESPACE, name: "oceanus-role-binding", roleName: "oceanus-role", serviceAccount: "oceanus", serviceAccountNamespace: constants.OCEANUS_NAMESPACE})

		result = append(result, &createServiceAccountApp{ctx: ctx, namespace: "default", name: "flink"})
		result = append(result, &createRoleApp{ctx: ctx, namespace: "default", name: "flink-role-flink", key: "flink-role.yaml"})
		result = append(result, &createRoleBindingApp{ctx: ctx, namespace: "default", name: "flink-role-binding-flink", roleName: "flink-role-flink", serviceAccount: "flink"})
		result = append(result, &EnableEventPersistenceApp{ctx: ctx})
		result = append(result, &EnableClusterAuditApp{ctx: ctx})
		result = append(result, &OpenPolicyListApp{ctx: ctx})
	}

	// 共享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId != "" {
		flinkNamespace := ctx.ClusterGroup.SerialId
		oceanusNamespace := fmt.Sprintf("oceanus-%s", flinkNamespace)
		result = append(result, &createNamespaceApp{ctx: ctx, name: oceanusNamespace})
		result = append(result, &createNamespaceApp{ctx: ctx, name: flinkNamespace})

		result = append(result, &createServiceAccountApp{ctx: ctx, namespace: oceanusNamespace, name: "oceanus"})
		result = append(result, &createRoleApp{ctx: ctx, namespace: flinkNamespace, name: "oceanus-role", key: "oceanus-role.yaml"})
		result = append(result, &createRoleApp{ctx: ctx, namespace: oceanusNamespace, name: "oceanus-role", key: constants.OCEANUS_NAMESPACE_OCEANUS_ROLE})

		result = append(result, &createRoleBindingApp{ctx: ctx, namespace: flinkNamespace, name: "oceanus-role-binding", roleName: "oceanus-role", serviceAccount: "oceanus", serviceAccountNamespace: oceanusNamespace})
		result = append(result, &createRoleBindingApp{ctx: ctx, namespace: flinkNamespace, name: "oceanus-role-binding-oceanus", roleName: "oceanus-role", serviceAccount: "oceanus", serviceAccountNamespace: "oceanus"})

		result = append(result, &createRoleBindingApp{ctx: ctx, namespace: oceanusNamespace, name: "oceanus-role-binding-oceanus", roleName: "oceanus-role", serviceAccount: "oceanus", serviceAccountNamespace: "oceanus"})

		result = append(result, &CreateImagePullSecretApp{ctx: ctx, namespace: flinkNamespace})
		result = append(result, &CreateImagePullSecretApp{ctx: ctx, namespace: oceanusNamespace})

		result = append(result, &createServiceAccountApp{ctx: ctx, namespace: flinkNamespace, name: "flink"})
		result = append(result, &createRoleApp{ctx: ctx, namespace: flinkNamespace, name: "flink-role-flink", key: "flink-role.yaml"})
		result = append(result, &createRoleBindingApp{ctx: ctx, namespace: flinkNamespace, name: "flink-role-binding-flink", roleName: "flink-role-flink", serviceAccount: "flink"})

		result = append(result, &appendClusterConfigApp{ctx: ctx})

		//result = append(result, &CreateResourceQuotaApp{ctx: ctx})

		result = append(result, &createNetworkPolicyApp{ctx: ctx})

		result = append(result, &createOceanusNetworkPolicyApp{ctx: ctx})

		if ctx.Tke.ClusterType == constants.K8S_CLUSTER_TYPE_TKE {
			result = append(result,
				&createFlinkNetworkPolicyApp{ctx: ctx},
				&createSqlServerNetworkPolicyApp{ctx: ctx})
		}

	}
	return result, nil
}

type EnableEventPersistenceApp struct {
	apps.ApplyApp
	ctx *deploy.Context
}

func (c *EnableEventPersistenceApp) Apply(client apps.Client, _ interface{}) (_ interface{}, err error) {

	h := &ApplyTKEHandler{}
	err = h.CheckAndEnableEventPersistence(c.ctx.ClusterGroup, c.ctx.Tke)

	return
}

type EnableClusterAuditApp struct {
	apps.ApplyApp
	ctx *deploy.Context
}

func (c *EnableClusterAuditApp) Apply(client apps.Client, _ interface{}) (_ interface{}, err error) {

	h := &ApplyTKEHandler{}
	err = h.CheckAndEnableClusterAudit(c.ctx.ClusterGroup, c.ctx.Tke)

	return
}

type OpenPolicyListApp struct {
	apps.ApplyApp
	ctx *deploy.Context
}

func (c *OpenPolicyListApp) Apply(client apps.Client, _ interface{}) (_ interface{}, err error) {

	h := &ApplyTKEHandler{}
	err = h.CheckAndOpenPolicyList(c.ctx.ClusterGroup, c.ctx.Tke)
	return
}

type CreateImagePullSecretApp struct {
	apps.ApplyApp
	ctx       *deploy.Context
	namespace string
}

func (c *CreateImagePullSecretApp) Apply(client apps.Client, _ interface{}) (_ interface{}, err error) {

	_, _, err = tke.GetTkeService().GetOrCreateImagePullSecretsWithNamespace(c.ctx.ClusterGroup.Region, c.namespace, client.ClientSet())
	return nil, err
}

type setIngressPasswordApp struct {
	ctx *deploy.Context
	apps.ReadyApp
}

func (c *setIngressPasswordApp) Ready(_ apps.Client, _ interface{}) (_ bool, err error) {
	initKey := "init_set_ingress_password"
	_, exists := c.ctx.GetParam(initKey, "")
	if exists {
		return true, nil
	}

	authInfo, err := service5.GetTableService().ListFlinkUiAuthInfo(c.ctx.ClusterGroup.Id)
	if err != nil {
		return false, err
	}

	if authInfo == nil {
		authInfo = &table2.FlinkUiAuthInfo{
			Id:             0,
			ClusterGroupId: c.ctx.ClusterGroup.Id,
			User:           k8s2.K8S_FLINK_WEBUI_PREFIX_USER,
			Password:       k8s2.K8S_FLINK_WEBUI_PREFIX_PASSWD,
		}
	}

	err = authInfo.DecodePassword()
	if err != nil {
		return false, err
	}

	_, err = c.ctx.K8sService().IngressService.ApplyIngressAll(c.ctx.ClusterGroup.Region, c.ctx.ClientSet(), authInfo.User,
		authInfo.Password, c.ctx.ClusterGroup, c.ctx.Cluster, c.ctx.Tke)
	if err != nil {
		return false, err
	}

	c.ctx.SetReturnParam(initKey, "")
	return true, nil
}

type createLoadBalancerService struct {
	ctx *deploy.Context
	apps.ReadyApp
}

func (c *createLoadBalancerService) Ready(_ apps.Client, _ interface{}) (_ bool, err error) {
	initKey := "createLoadBalancerService"
	_, exists := c.ctx.GetParam(initKey, "")
	if exists {
		return true, nil
	}

	if c.ctx.Cluster != nil && len(c.ctx.Cluster.LoadBalanceId) > 0 {
		return true, nil
	}

	secretId, secretKey, err := service3.GetSecretIdAndKeyOfScs()
	if err != nil {
		logger.Errorf("createLoadBalancerService Failed to GetSecretIdAndKeyOfScs ,err: %v", err)
		return false, err
	}
	prof := qcloud.NewClientProfile()
	credential := common.NewTokenCredential(secretId, secretKey, "")
	clbClient, err := clb.NewClient(credential, c.ctx.ClusterGroup.Region, prof)
	if err != nil {
		logger.Errorf("createLoadBalancerService Failed to NewClient[clb] ,err: %v", err.Error())
		return false, errorcode.FailedOperationCode.NewWithErr(err)
	}
	createLoadBalancersReq := clb.NewCreateLoadBalancerRequest()
	lbType := "INTERNAL"
	lbName := c.ctx.ClusterGroup.SerialId + "flinkwebuiInternal"
	slaType := "clb.c4.small"
	createLoadBalancersReq.LoadBalancerType = &lbType
	createLoadBalancersReq.LoadBalancerName = &lbName
	createLoadBalancersReq.VpcId = &c.ctx.Cluster.VpcId
	createLoadBalancersReq.SubnetId = &c.ctx.Cluster.SubnetId
	createLoadBalancersReq.SlaType = &slaType
	internetChargeType := "TRAFFIC_POSTPAID_BY_HOUR"
	internetMaxBandwidthOut := int64(10240)
	createLoadBalancersReq.InternetAccessible = &clb.InternetAccessible{
		InternetChargeType:      &internetChargeType,
		InternetMaxBandwidthOut: &internetMaxBandwidthOut,
	}
	logger.Infof("#CreateLoadBalancer, createLoadBalancersReq: {}", createLoadBalancersReq.ToJsonString())
	response, err := clbClient.CreateLoadBalancer(createLoadBalancersReq)
	if _, ok := err.(*errors.TencentCloudSDKError); ok {
		logger.Errorf("An API: CreateLoadBalancer error has returned: %v", err)
		return false, err
	}

	if err != nil {
		logger.Errorf("An API: CreateLoadBalancer error has returned: %v", err)
		return false, err
	}

	// 输出json格式的字符串回包
	logger.Infof("#CreateLoadBalancer, response: {}", response.ToJsonString())
	service4.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.ExecuteSqlWithArgs("update Cluster set LoadBalanceId=? where Id=?", *response.Response.LoadBalancerIds[0], c.ctx.Cluster.Id)
		return nil
	}).Close()
	c.ctx.Request.Params[constants.FLOW_PARAM_LOAD_BALANCE_ID] = *response.Response.LoadBalancerIds[0]
	c.ctx.SetReturnParam(initKey, "")
	return true, nil
}

type bindFlinkUiClbApp struct {
	ctx *deploy.Context
	apps.ReadyApp
}

func (c *bindFlinkUiClbApp) Ready(_ apps.Client, _ interface{}) (_ bool, err error) {
	if c.ctx.Cluster.CrossTenantEniMode == constants.EnableStaticMode {
		flinkVersionService := service2.NewFlinkUiClbService(c.ctx.ClientSet(), c.ctx.ClusterGroup, c.ctx.Cluster)
		err = flinkVersionService.BindFlinkUiClb()
		if err != nil {
			return false, err
		}
	}
	return true, nil
}

type corednsEniApp struct {
	ctx *deploy.Context
	apps.ReadyApp
}

func (c *corednsEniApp) Ready(_ apps.Client, _ interface{}) (_ bool, err error) {
	if c.ctx.ClusterGroup.NetEnvironmentType != constants.NETWORK_ENV_CLOUD_VPC {
		return true, nil
	}
	if c.ctx.ClusterGroup.NetEniType != constants.CLUSTER_NET_ENI_POD {
		return true, nil
	}
	logger.Infof("init core dns eni with %s", c.ctx.ClusterGroup.SerialId)
	coredns, err := c.ctx.K8sService().GetDeploymentAppsV1(c.ctx.ClientSet(), "kube-system", "coredns")
	if err != nil {
		logger.Warningf("kube-system coredns not exists")
		return true, nil
	}
	if coredns == nil {
		logger.Warningf("Deployment coredns not installed.")
		return true, nil
	}
	annotations := coredns.Spec.Template.Annotations
	exist := func(key string) (exist bool) {
		_, exist = annotations[key]
		return
	}
	if exist("tke.cloud.tencent.com/cross-tenant-eni-enable") && exist("tke.cloud.tencent.com/networks") {
		return true, nil
	}

	if len(annotations) == 0 {
		coredns.Spec.Template.Annotations = make(map[string]string)
		annotations = coredns.Spec.Template.Annotations
	}
	eniAnnotation := tke.GetTkeService().GenerateTkeEniAnnotation()

	for k, v := range eniAnnotation {
		annotations[k] = v
	}
	_, err = c.ctx.K8sService().ApplyDeploymentAppsV1(c.ctx.ClientSet(), coredns)
	return true, nil
}

type appendClusterConfigApp struct {
	ctx *deploy.Context
	apps.ReadyApp
}

func (c *appendClusterConfigApp) Ready(_ apps.Client, _ interface{}) (_ bool, err error) {
	clusterConfig := make(map[string]string)
	if len(c.ctx.Cluster.ClusterConfig) > 0 {
		if err := json.Unmarshal([]byte(c.ctx.Cluster.ClusterConfig), &clusterConfig); err != nil {
			return false, err
		} else {
			return true, nil
		}
	}
	if c.ctx.ClusterGroup.NetEnvironmentType == constants.NETWORK_ENV_INNER_VPC {
		// 解决云监控集群init-container oom 问题
		clusterConfig[constants.KUBERNETES_INITCONTAINER_MB_KEY] = constants.KUBERNETES_INITCONTAINER_MB_1024
	}

	if c.ctx.ConnectionType != constants.CLUSTER_NET_CONNECTION_TYPE_ENI {
		clusterConfig[constants.KUBERNETES_HOSTNETWORK_ENABLED] = strconv.FormatBool(true)
	} else {
		eniAnno, err := c.ctx.GetPodAnnotations(true, false)
		if err != nil {
			return false, err
		}

		annotations, err := json.Marshal(eniAnno)
		if err != nil {
			logger.Errorf("init_tke_service[Ready] Cluster.annotations format error %s, error %v", eniAnno, err)
			return false, err
		}

		clusterConfig[constants.KUBERNETES_JOBMANAGER_ANNOTATIONS] = string(annotations)
		clusterConfig[constants.KUBERNETES_TASKMANAGER_ANNOTATIONS] = string(annotations)
	}

	strConf, err := json.Marshal(clusterConfig)
	if err != nil {
		return false, err
	}

	service4.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		tx.ExecuteSqlWithArgs("update Cluster set ClusterConfig=? where Id=?", string(strConf), c.ctx.Cluster.Id)
		return nil
	}).Close()

	return true, nil
}

type createFlinkNetworkPolicyApp struct {
	apps.NetworkPolicy
	ctx *deploy.Context
}

func (c *createFlinkNetworkPolicyApp) Params() (interface{}, error) {

	cg := c.ctx.ClusterGroup
	namespace := constants.DEFAULT_NAMESPACE
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId != "" {
		namespace = cg.SerialId
	}
	return struct {
		Namespace string
	}{namespace}, nil
}

func (c *createFlinkNetworkPolicyApp) Decode(params, into interface{}) (interface{}, error) {
	return c.ctx.FlowCC.TkeCC().OceanusCC().FlinkNamespaceNetworkPolicy(params, into)
}

type createSqlServerNetworkPolicyApp struct {
	apps.NetworkPolicy
	ctx *deploy.Context
}

func (c *createSqlServerNetworkPolicyApp) Params() (interface{}, error) {

	cg := c.ctx.ClusterGroup
	namespace := constants.OCEANUS_NAMESPACE
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId != "" {
		namespace = fmt.Sprintf("%s-%s", constants.OCEANUS_NAMESPACE, cg.SerialId)
	}
	return struct {
		Namespace string
	}{namespace}, nil
}

func (c *createSqlServerNetworkPolicyApp) Decode(params, into interface{}) (interface{}, error) {
	return c.ctx.FlowCC.TkeCC().OceanusCC().SqlServerNetworkPolicy(params, into)
}

type createOceanusNetworkPolicyApp struct {
	apps.NetworkPolicy
	ctx *deploy.Context
}

func (c *createOceanusNetworkPolicyApp) Params() (interface{}, error) {

	flinkNamespace := c.ctx.ClusterGroup.SerialId
	oceanusNamespace := fmt.Sprintf("oceanus-%s", flinkNamespace)
	fromNamespaces := make([]string, 0, 0)
	fromNamespaces = append(fromNamespaces, oceanusNamespace)
	fromNamespaces = append(fromNamespaces, flinkNamespace)
	fromNamespaces = append(fromNamespaces, constants.OCEANUS_NAMESPACE)
	return struct {
		ToNamespace    string
		FromNamespaces []string
	}{oceanusNamespace,
		fromNamespaces,
	}, nil
}

func (c *createOceanusNetworkPolicyApp) Decode(params, into interface{}) (interface{}, error) {
	return c.ctx.FlowCC.TkeCC().NetworkPolicyCC().OceanusNetworkPolicy(params, into)
}

type createNetworkPolicyApp struct {
	apps.NetworkPolicy
	ctx *deploy.Context
}

func (c *createNetworkPolicyApp) Params() (interface{}, error) {

	flinkNamespace := c.ctx.ClusterGroup.SerialId
	oceanusNamespace := fmt.Sprintf("oceanus-%s", flinkNamespace)
	fromNamespaces := make([]string, 0, 0)
	fromNamespaces = append(fromNamespaces, oceanusNamespace)
	fromNamespaces = append(fromNamespaces, flinkNamespace)
	fromNamespaces = append(fromNamespaces, constants.OCEANUS_NAMESPACE)
	return struct {
		ToNamespace    string
		FromNamespaces []string
	}{flinkNamespace,
		fromNamespaces,
	}, nil
}

func (c *createNetworkPolicyApp) Decode(params, into interface{}) (interface{}, error) {
	return c.ctx.FlowCC.TkeCC().NetworkPolicyCC().NetworkPolicy(params, into)
}

type CreateResourceQuotaApp struct {
	apps.ResourceQuota
	ctx *deploy.Context
}

func (c *CreateResourceQuotaApp) Params() (interface{}, error) {

	clusterGroup := c.ctx.ClusterGroup
	cluster := c.ctx.Cluster
	serialId := clusterGroup.SerialId
	cuNum := clusterGroup.CuNum
	// 包年包月 + 按量付费子集群
	if clusterGroup.Type == constants.CLUSTER_GROUP_TYPE_SUB_EKS {
		acs, err := service2.NewClusterGroupServiceBySerialId(clusterGroup.ParentSerialId)
		if err != nil {
			return nil, err
		}
		cuNum += acs.GetClusterGroup().CuNum
		serialId = acs.GetClusterGroup().SerialId
	} else {
		// 是否有sub eks cluster
		count, subEksClusterGroup, err := service2.ListClusterGroupByParentSerialId(clusterGroup.SerialId)
		if err == nil && count == 1 && subEksClusterGroup.Type == constants.CLUSTER_GROUP_TYPE_SUB_EKS {
			cuNum += subEksClusterGroup.CuNum
		}
	}

	memRatio := constants.CVM_DEFAULT_MEMRATIO
	if cluster.MemRatio != constants.CVM_DEFAULT_MEMRATIO && cluster.MemRatio != 0 {
		memRatio = int(cluster.MemRatio)
	}

	memory := cuNum * int16(memRatio)
	mem := fmt.Sprintf("%dGi", memory)
	cpu := fmt.Sprintf("%d", cuNum)
	limitCpu := cuNum * 4 // limit cpu 放大 4倍，因为细粒度加速会放大cpu limit
	return struct {
		Name           string
		Namespace      string
		RequestsCpu    string
		RequestsMemory string
		LimitsCpu      string
		LimitsMemory   string
	}{serialId,
		serialId,
		cpu,
		mem,
		fmt.Sprintf("%d", limitCpu),
		mem}, nil
}

func (c *CreateResourceQuotaApp) Decode(params, into interface{}) (interface{}, error) {
	return c.ctx.FlowCC.TkeCC().ResourceQuotaCC().ResourceQuota(params, into)
}

type createNamespaceApp struct {
	apps.Namespace
	ctx  *deploy.Context
	name string
}

func (c *createNamespaceApp) Params() (interface{}, error) {
	return struct {
		Name string
	}{c.name}, nil
}

func (c *createNamespaceApp) Decode(params, into interface{}) (interface{}, error) {
	return c.ctx.FlowCC.TkeCC().OceanusCC().Namespace(params, into)
}

type createServiceAccountApp struct {
	apps.ServiceAccount
	ctx       *deploy.Context
	namespace string
	name      string
}

func (c *createServiceAccountApp) Params() (interface{}, error) {
	return struct {
		Namespace string
		Name      string
	}{c.namespace, c.name}, nil
}

func (c *createServiceAccountApp) Decode(params, into interface{}) (interface{}, error) {
	return c.ctx.FlowCC.TkeCC().OceanusCC().ServiceAccount(params, into)
}

type createRoleApp struct {
	apps.Role
	ctx       *deploy.Context
	namespace string
	name      string
	key       string // role 七彩石的配置
}

func (c *createRoleApp) Params() (interface{}, error) {
	return struct {
		Namespace string
		Name      string
	}{c.namespace, c.name}, nil
}

func (c *createRoleApp) Decode(params, into interface{}) (interface{}, error) {
	return c.ctx.FlowCC.TkeCC().OceanusCC().Role(c.key, params, into)
}

type createRoleBindingApp struct {
	apps.RoleBinding
	ctx                     *deploy.Context
	namespace               string
	name                    string
	roleName                string
	serviceAccount          string
	serviceAccountNamespace string
}

func (c *createRoleBindingApp) Params() (interface{}, error) {

	if c.serviceAccountNamespace == "" {
		c.serviceAccountNamespace = c.namespace
	}

	return struct {
		Namespace               string
		Name                    string
		RoleName                string
		ServiceAccount          string
		ServiceAccountNamespace string
	}{c.namespace, c.name, c.roleName, c.serviceAccount, c.serviceAccountNamespace}, nil
}

func (c *createRoleBindingApp) Decode(params, into interface{}) (interface{}, error) {
	return c.ctx.FlowCC.TkeCC().OceanusCC().RoleBinding(params, into)
}

type createClusterRoleApp struct {
	apps.ClusterRole
	ctx  *deploy.Context
	name string
	key  string // role 七彩石的配置
}

func (c *createClusterRoleApp) Params() (interface{}, error) {
	return struct {
		Name string
	}{c.name}, nil
}

func (c *createClusterRoleApp) Decode(params, into interface{}) (interface{}, error) {
	return c.ctx.FlowCC.TkeCC().OceanusCC().ClusterRole(c.key, params, into)
}

type createClusterRoleBindingApp struct {
	apps.ClusterRoleBinding
	ctx                     *deploy.Context
	name                    string
	roleName                string
	serviceAccount          string
	serviceAccountNamespace string
}

func (c *createClusterRoleBindingApp) Params() (interface{}, error) {

	return struct {
		Name                    string
		RoleName                string
		ServiceAccount          string
		ServiceAccountNamespace string
	}{c.name, c.roleName, c.serviceAccount, c.serviceAccountNamespace}, nil
}

func (c *createClusterRoleBindingApp) Decode(params, into interface{}) (interface{}, error) {
	return c.ctx.FlowCC.TkeCC().OceanusCC().ClusterRoleBinding(params, into)
}
