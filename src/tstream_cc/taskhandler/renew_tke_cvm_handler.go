package taskHandler

import (
	"encoding/json"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	cvm2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cvm"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type renewCvmService struct {
}

func (s *renewCvmService) Apps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	cg := ctx.ClusterGroup
	k8sInstance := ctx.Tke

	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM {

	}

	isCluster, exists := ctx.GetParam(constants.FLOW_PARAM_SETATS_IS_CLUSTER, "1")
	if exists {
		if isCluster == "0" {
			return result, nil
		}
	}

	//独享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId == "" {
		if k8sInstance.ClusterType == constants.K8S_CLUSTER_TYPE_TKE &&
			cg.NetEnvironmentType == constants.NETWORK_ENV_CLOUD_VPC {

			result = append(result, &renewCvmApp{ctx: ctx})
			result = append(result, &checkCvmRunningApp{ctx: ctx})
		}
	}

	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId != "" {
		if cg.ResourceType == constants.RESOURCE_TYPE_PRIVATE {
			result = append(result, &transformPoolWorkerToWorkerApp{ctx: ctx})
			result = append(result, &uniformClusterTransformWorkerToPrivateApp{ctx: ctx})
			//result = append(result, &createPoolWorkerForWorkerApp{ctx: ctx})
		}
	}

	return result, nil
}

type renewCvmApp struct {
	apps.ApplyApp
	ctx *deploy.Context
}

func (c *renewCvmApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	if c.ctx.Tke.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		return
	}

	if c.ctx.ClusterGroup.NetEnvironmentType == constants.NETWORK_ENV_INNER_VPC {
		return
	}

	if c.ctx.ClusterGroup.Status == constants.CLUSTER_GROUP_STATUS_RUNNING {
		return
	}

	/**
	 * 续费 隔离期间 被回收的cvm
	 * https://cloud.tencent.com/document/api/213/15740
	 * 实例操作结果可以通过调用 DescribeInstances 接口查询，如果实例的最新操作状态(LatestOperationState)为“SUCCESS”，则代表操作成功。
	 */
	shutdownIds, _, err := getTkeCvm(c.ctx, cvm2.GetCvmService().IsInstanceSHUTDOWN)
	if err != nil {
		return
	}

	err = c.renewCvm(shutdownIds)
	return
}

type checkCvmRunningApp struct {
	apps.ReadyApp
	ctx *deploy.Context
}

func (c *checkCvmRunningApp) Ready(_ apps.Client, _ interface{}) (ready bool, err error) {
	if c.ctx.Tke.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		return true, nil
	}

	handler := ApplyTKEHandler{}
	ready, err = handler.TryMakeAllNodesRunning(c.ctx)
	if err != nil {
		return false, err
	}
	tn, cn, wn, _, err := handler.CalcClusterNodeCount(c.ctx)
	if err != nil {
		return false, err
	}
	if tn < cn+wn {
		logger.Errorf("node count is not enough, need %d, has %d", tn, cn+wn)
	}
	return
}

func (c *renewCvmApp) renewCvm(cvmInstances []*string) (err error) {
	if len(cvmInstances) == 0 {
		return nil
	}
	b, _ := json.Marshal(cvmInstances)
	logger.Infof("renew cvms: %s", string(b))

	prepaidPeriod, err := c.ctx.FlowCC.PrepaidPeriod()
	if err != nil {
		return
	}

	region := c.ctx.ClusterGroup.Region
	cvmService := cvm2.GetCvmService()
	return cvmService.RenewInstancesWithScsAccount(region, cvmInstances, prepaidPeriod)
}
