package taskHandler

import (
	"fmt"
	v20170320 "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/cdb/v20170320"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	constants2 "tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	table2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cdb"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cdb"
	flow2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/flow"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
)

const (
	ControllerSqlTemplateName = "template-new.sql"
)

var (
	NetworkTypeSupportEnvironment int64 = 4
)

type InitCDBHandler struct {
}

func (this InitCDBHandler) CompleteTask(request *flow.TaskExecRequest) (resp *flow.TaskExecResponse) {
	// 0. 公共基本逻辑
	flowService := flow2.GetFlowService()
	params := request.Params
	requestId, _, _ := flowService.GetFlowParamString(params, constants2.FLOW_PARAM_REQUEST_ID, "")

	defer func() {
		if err := recover(); err != nil {
			logger.Errorf("[%s] Init CDB task failed because: %v", requestId, err)
			resp = flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_INIT, 0.1, fmt.Sprintf("%v",
				err), params)
		}
	}()

	s, err := flow2.NewClusterFlowService(request)
	if err != nil {
		return s.RetryRspWithErr(err)
	}

	// 1. 获取 CDB 实例信息: 地域, 实例 ID, SQL 模板文件名, 用户名
	cdbInstances, err := s.ClusterGroupService.GetCdbList()
	if err != nil {
		s.RetryRspWithErr(err)
	}
	if len(cdbInstances) != 1 {
		msg := fmt.Sprintf("[%s] Currently only 1 CDB instance is supported, provided %d", requestId, len(cdbInstances))
		s.FailRsp(msg)
	}

	// 4. 返回成功
	logger.Infof("[%s] Finished initiating CDB instance", requestId)
	return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_SUCCESS, flow.TASK_STATUS_SUCCESS, 0.1, "ok", params)
}

func (o *InitCDBHandler) getCdbAccessAddress(requestId string, clusterGroup *table.ClusterGroup, cluster *table.Cluster, cdbInstance *table2.Cdb) (vip string, vport int, err error) {
	if clusterGroup.NetEnvironmentType == constants2.NETWORK_ENV_INNER_VPC {
		return cdbInstance.Vip, cdbInstance.Vport, nil
	}

	// 现网 调用 CDB 云 API DescribeDBRoutes 获取支撑环境访问地址
	scsSecretId, scsSecretKey, err := service3.GetSecretIdAndKeyOfScs()
	if err != nil {
		logger.Errorf("[%s] Failed to get scs SecretId: %+v", requestId, err)
		panic(errorcode.NewStackError(errorcode.InternalErrorCode, "", err))
	}
	cdbRoutesResponse, err := cdb.GetCdbService().DescribeDBRoutes(scsSecretId, scsSecretKey, clusterGroup.Region, cdbInstance.InstanceId)
	if err != nil {
		logger.Errorf("[%s] Failed to call DescribeDBRoutes because %+v", requestId, err)
		panic(errorcode.NewStackError(errorcode.InternalErrorCode, "", err))
	}
	logger.Infof("[%s] Retrieved CDB routes for instance %s: %s", requestId, cdbInstance.InstanceId, cdbRoutesResponse.ToJsonString())
	routers := cdbRoutesResponse.Response.Routers
	routersInSupportEnvironment := make([]*v20170320.Route, 0) // 支撑环境下的路由地址
	for _, router := range routers {
		if *router.VipType == NetworkTypeSupportEnvironment {
			routersInSupportEnvironment = append(routersInSupportEnvironment, router)
		}
	}
	if len(routersInSupportEnvironment) == 0 {
		logger.Errorf("[%s] Failed to find any routes in support environment for CDB instance %s", requestId, cdbInstance.InstanceId)
		panic(errorcode.NewStackError(errorcode.InternalErrorCode, "no routes in support environment", nil))
	}
	vips := routersInSupportEnvironment[0].VipList
	if len(vips) == 0 {
		logger.Errorf("[%s] Failed to get IPs in support environment because VipList", requestId)
		panic(errorcode.NewStackError(errorcode.InternalErrorCode, "Failed to get IPs in support environment because VipList", nil))
	}

	return *vips[0].Vip, int(*vips[0].Vport), nil
}

type runSql struct {
	user     string
	password string
	ip       string
	port     int
}

func (r *runSql) run(sql, requestId string) {
	logger.Info("%v run sql %s", *r, sql)
	service.GetTableService().MustRunSqlInBatch(sql, r.user, r.password, r.ip, r.port, requestId)
}
