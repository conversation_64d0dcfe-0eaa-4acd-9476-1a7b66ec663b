package taskHandler

import (
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type DeployHadoopYarnService struct {
}

func (s *DeployHadoopYarnService) Apps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	clusterGroup := ctx.ClusterGroup

	// 独享集群, 且 tke/eks 开启跨租户弹性网卡
	if clusterGroup.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && clusterGroup.AgentSerialId == "" && ctx.Cluster.CrossTenantEniMode == constants.EnableStaticMode {
		result = append(result, &HadoopYarn{ctx: ctx})
	}

	return result, nil
}
