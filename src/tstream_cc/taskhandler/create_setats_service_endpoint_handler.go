package taskHandler

import (
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/component"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	setats2 "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/setats"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/resource_auth"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/tag"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
	"time"
)

type CreateSetatsServiceEndpointHandler struct {
}

func (c CreateSetatsServiceEndpointHandler) Apps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	result = append(result, &switchSetatsStatusApp{ctx: ctx, status: constants.SETATS_RUNNING})
	return result, nil
}

type switchSetatsStatusApp struct {
	apps.ApplyApp
	ctx    *deploy.Context
	status int8
}

func (c *switchSetatsStatusApp) Apply(_ apps.Client, _ interface{}) (interface{}, error) {
	count, _setats, err := service.GetSetatsByClusterGroupSerialId(c.ctx.ClusterGroup.SerialId)
	if err != nil {
		logger.Errorf("Failed to get setats by ClusterGroupSerialId id:%s, with errors:%+v", c.ctx.ClusterGroup.SerialId, err)
		return nil, errorcode.FailedOperationCode.NewWithErr(err)
	}
	if count == 0 {
		logger.Errorf("Failed to get setats by ClusterGroupSerialId id:%s, with count 0", c.ctx.ClusterGroup.SerialId)
		return nil, errorcode.FailedOperationCode.NewWithMsg(fmt.Sprintf("setats size is 0"))
	}
	if c.status == constants.SETATS_DELETED {
		err = c.UnbindSetatsTags(_setats)
		if err != nil {
			logger.Warningf("Failed to unbind setats tags, with errors:%+v", err)
		}
	}
	err = service2.SwitchSetatasStatusTo(c.ctx.ClusterGroup.SerialId, c.status)
	if err != nil {
		logger.Errorf("Failed to switch setats status to %d, with errors:%+v", c.status, err)
		return nil, err
	}
	return nil, nil
}

func (c *switchSetatsStatusApp) UnbindSetatsTags(_setats *setats2.Setats) (err error) {

	tagComponent := component.NewTagComponent()
	tags, err := tag.GetTagService().GetResourceTags(_setats.OwnerUin, _setats.Region, []string{_setats.SerialId}, resource_auth.RESOURCE_PREFIX_SETATS)
	if err != nil {
		return err
	}
	for _, t := range tags {
		_, err = tagComponent.BatchDeleteResourcesTag(_setats.OwnerUin, _setats.OwnerUin,
			_setats.Region, resource_auth.RESOURCE_SERVICE_TYPE, resource_auth.RESOURCE_PREFIX_SETATS,
			t.TagKey, t.TagValue, []string{_setats.SerialId}, time.Now().Unix(), map[string]string{})
		if err != nil {
			return err
		}
	}
	return nil
}
