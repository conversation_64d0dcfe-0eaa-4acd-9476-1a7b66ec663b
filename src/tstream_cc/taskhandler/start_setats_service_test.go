package taskHandler

import (
	"tencentcloud.com/tstream_galileo/src/common/flow"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	table "tencentcloud.com/tstream_galileo/src/tstream_cc/model/table/cluster"
	flow2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/flow"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
	"testing"
)

func TestCopyOnPutForTargetCos(t *testing.T) {

	params := make(map[string]string, 0)
	params[constants.FLOW_PARAM_SETATS_WORKER_DISK_SIZE] = "100"
	ctx := &deploy.Context{

		ClusterFlowService: &flow2.ClusterFlowService{
			FlowService: flow2.FlowService{},
			RequestId:   "",
			Request: &flow.TaskExecRequest{
				Retrycount: 0,
				Processkey: "",
				Taskcode:   "",
				DocId:      "",
				FlowId:     "",
				TaskId:     "",
				Params:     params,
			},
			ClusterGroup: &table.ClusterGroup{
				SerialId:           "cluster-pgel2z70",
				NetEnvironmentType: 1,
				Region:             "ap-chengdu",
			},
			Cluster: &table.Cluster{
				Id:                    0,
				UniqClusterId:         "",
				ClusterGroupId:        0,
				CreatorUin:            "",
				Zone:                  "",
				VpcId:                 "",
				SubnetId:              "",
				SupportedZoneSubnets:  "",
				VpcCIDR:               "",
				RoleType:              0,
				SchedulerType:         0,
				CuNum:                 0,
				UsedCuNum:             0,
				DefaultCOSBucket:      "autotest-cos-cd-1257058945",
				CreateTime:            "",
				UpdateTime:            "",
				StopTime:              "",
				Remark:                "",
				KubeConfig:            "",
				WebUIPrefix:           "",
				ClusterConfig:         "",
				FlinkConfig:           "",
				FlinkVersion:          "",
				ClsLogSet:             "",
				ClsTopicId:            "",
				LogConfig:             "",
				DefaultLogCollectConf: "",
				SupportedFeatures:     "",
				SupportedFlinkVersion: "",
				LogMode:               0,
				ClusterExtendConfig:   "",
				LoadBalanceId:         "",
				WebUIType:             0,
				MemRatio:              4,
				Cores:                 0,
				CrossTenantEniMode:    0,
				RouteConfig:           "",
			},
			ConnectionType:      0,
			Tke:                 nil,
			ClusterType:         0,
			ArchGeneration:      0,
			Cdb:                 nil,
			Region:              "ap-chengdu",
			NewZone:             "",
			ClusterGroupService: nil,
			CurStatus:           0,
		},

		FlowCC: nil,
	}

	ss := &setats{
		ctx:            ctx,
		needManagerUrl: false,
	}
	refs, err := ss.UploadJarToUserCos(nil, nil)
	logger.Errorf("UploadJarToUserCos err: %s", refs)
	logger.Errorf("UploadJarToUserCos err: %+v", err)

	//oceanusSecretId := "CosSecretId"
	//oceanusSecretKey := "CosSecretKey"
	//targetCosClient := cos.NewCosClient(oceanusSecretId, oceanusSecretKey, "", "ap-chengdu", "autotest-cd-1257058945")
	//
	//cosClient := cos.NewCosClient(oceanusSecretId, oceanusSecretKey, "", "ap-chengdu", "autotest-cd-1257058945")
	//sourcePath := "1257058945/100006386216/upload/20250522155858/emr-6nt908ea-krb.jar"
	//targetPath := fmt.Sprintf("setats/%d/%s/%s/%s",
	//	123, "setats-xxx", strings.ReplaceAll(uuid.New(), "-", ""), filepath.Base(sourcePath))
	//logger.Infof("CopyOnPutForTargetCos source path %s, target path %s", sourcePath, targetPath)
	//err := cosClient.CopyOnPutForTargetCos(sourcePath, targetPath, targetCosClient)
	//if err != nil {
	//	logger.Errorf("Failed to CopyOnPutForTargetCos,err: %+v", err)
	//}

}
