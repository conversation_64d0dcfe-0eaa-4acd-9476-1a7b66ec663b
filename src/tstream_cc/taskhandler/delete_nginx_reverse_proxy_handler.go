package taskHandler

import (
	"fmt"
	v1 "k8s.io/api/apps/v1"
	v13 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	constants1 "tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	flow2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/flow"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/tke"
)

type DeleteNginxReverseProxyService struct {
}

func (d *DeleteNginxReverseProxyService) CompleteTask(request *flow.TaskExecRequest) (resp *flow.TaskExecResponse) {
	flowService := flow2.GetFlowService()
	params := request.Params

	defer func() {
		if errs := recover(); errs != nil {
			resp = flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1,
				fmt.Sprintf("%v", errs), params)
		}
	}()

	var err error
	resp, err = d.handle(request)
	if err != nil {
		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1, fmt.Sprintf("%v", err), params)
	}
	return
}

func (d *DeleteNginxReverseProxyService) handle(request *flow.TaskExecRequest) (resp *flow.TaskExecResponse, err error) {
	flowService := flow2.GetFlowService()

	params := request.Params
	clusterGroup, _, err := flowService.GetClusterGroupAndCluster(request.Params)
	if err != nil {
		return nil, err
	}
	cluster, err := service2.GetClusterByClusterGroupId(clusterGroup.Id)
	if err != nil {
		return nil, err
	}
	namespace := service2.GetDefaultNamespace(clusterGroup)

	clientSet, err := tke.GetTkeService().KubernetesClientsetFromCluster(request.DocId, cluster)
	if err != nil {
		return nil, err
	}

	k8sService := k8s.GetK8sService()
	// 1. 删除nginx-reverse-proxy
	nginxReverseName := constants1.ComponentNginxReverse
	// TODO: 调整default ns
	_, err = k8sService.DeleteStatefulSet(clientSet, &v1.StatefulSet{
		ObjectMeta: metav1.ObjectMeta{Namespace: namespace, Name: nginxReverseName},
	})
	if err != nil {
		logger.Errorf("Failed to delete StatefulSet: %s, err %+v", nginxReverseName, err)
		return nil, err
	}
	// 2. nginx-reverse-proxy service
	nginxReverseServiceName := constants1.NginxReverseServiceName
	_, err = k8sService.DeleteService(clientSet, &v13.Service{
		ObjectMeta: metav1.ObjectMeta{Namespace: namespace, Name: nginxReverseServiceName},
	})
	if err != nil {
		logger.Errorf("Failed to delete service: %s, err %+v", nginxReverseServiceName, err)
		return nil, err
	}
	// 3. delete  configmap default.conf
	nginxReverseConfigMap := constants1.NginxReverseDefaultConfigmapName
	_, err = k8sService.DeleteConfigMap(clientSet, &v13.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{Namespace: namespace, Name: nginxReverseConfigMap},
	})
	if err != nil {
		logger.Errorf("Failed to delete configmap: %s, err %+v", nginxReverseConfigMap, err)
		return nil, err
	}

	// 4. delete  configmap nginx.conf
	nginxReverseNginxConfig := constants1.NginxReverseConfigName
	_, err = k8sService.DeleteConfigMap(clientSet, &v13.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{Namespace: namespace, Name: nginxReverseNginxConfig},
	})
	if err != nil {
		logger.Errorf("Failed to delete configmap: %s, err %+v", nginxReverseNginxConfig, err)
		return nil, err
	}

	return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_SUCCESS, flow.TASK_STATUS_SUCCESS, 0.1, "ok",
		params), nil
}
