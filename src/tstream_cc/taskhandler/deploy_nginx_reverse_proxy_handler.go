package taskHandler

import (
	"fmt"
	appsV1 "k8s.io/api/apps/v1"
	v1 "k8s.io/api/core/v1"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/oceanus_controller"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/tke"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type DeployNginxReverseProxyService struct {
}

func (s *DeployNginxReverseProxyService) Apps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	cg := ctx.ClusterGroup

	// 独享集群, 且 tke/eks 开启跨租户弹性网卡
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId == "" && ctx.Cluster.CrossTenantEniMode == constants.EnableStaticMode {
		secret, err := ctx.GetOceanusIPSecret()
		if err != nil {
			return result, err
		}
		namespace := service2.GetDefaultNamespace(cg)
		app := s.configMap(ctx, secret, namespace)
		result = append(result,
			app,
			s.configMap2(ctx, secret, namespace),
			s.service(ctx, constants.ComponentNginxReverse, constants.NginxReversePort, namespace, secret))

		result = append(result, &nginxReverseSts{ctx: ctx})
	}
	return result, nil
}

type nginxReverseSts struct {
	ctx *deploy.Context
	apps.StatefulSet
}

func (w *nginxReverseSts) Params() (interface{}, error) {
	return getNginxReverseParams(w.ctx, "StatefulSet")
}

func (w *nginxReverseSts) Decode(params, into interface{}) (interface{}, error) {
	return w.ctx.FlowCC.AppsCC().NginxReverse(params, into)
}

func (w *nginxReverseSts) Transform(c apps.Client, v interface{}) (interface{}, error) {
	return transformDeploy(w.ctx, c, v)
}

func transformDeploy(ctx *deploy.Context, c apps.Client, v interface{}) (interface{}, error) {
	if ctx.Tke.ArchGeneration < constants.TKE_ARCH_GENERATION_V2 {
		return v, nil
	}
	if ctx.ClusterType != constants.K8S_CLUSTER_TYPE_TKE {
		return v, nil
	}
	vd := v.(*appsV1.StatefulSet)

	od, _ := c.K8sService().GetStatefulSetAppsV1(c.ClientSet(), vd.Namespace, vd.Name)
	if od == nil {
		return vd, nil
	}

	// retain node selector
	vd.Spec.Template.Spec.NodeSelector = od.Spec.Template.Spec.NodeSelector

	// retain app-container resource
	appContainer := "app-container"
	mapMainContainer(vd, appContainer, func(vc *v1.Container) {
		mapMainContainer(od, appContainer, func(oc *v1.Container) {
			vc.Resources = oc.Resources
		})
	})

	return vd, nil
}

func mapMainContainer(d *appsV1.StatefulSet, containerName string, f func(vc *v1.Container)) {
	containers := &d.Spec.Template.Spec.Containers
	for i := range *containers {
		c := *containers
		if c[i].Name == containerName {
			f(&c[i])
		}
	}
}

func getNginxReverseParams(ctx *deploy.Context, workLoadKind string) (data *oceanus_controller.Base, err error) {
	appContainerImage, err := ctx.CC().ImageRegistry().NginxReverse()
	if err != nil {
		return nil, err
	}

	base, err := ctx.ParamBase(workLoadKind,
		constants.DEFAULT_NAMESPACE,
		constants.ComponentNginxReverse, false)
	if err != nil {
		return nil, err
	}

	if ctx.ClusterGroup.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE &&
		ctx.Tke.ArchGeneration >= constants.TKE_ARCH_GENERATION_V2 &&
		ctx.ClusterType == constants.K8S_CLUSTER_TYPE_TKE { //TKE 集群
		for key := range base.NodeSelector {
			delete(base.NodeSelector, key)
		}
		if ctx.Tke.ArchGeneration >= constants.TKE_ARCH_GENERATION_V5 {
			base.NodeSelector[constants.TKE_CVM_WORKER_LABEL_KEY] = fmt.Sprintf("worker")
		} else {
			base.NodeSelector[constants.TKE_CVM_WORKER_NUM_LABEL_KEY] = fmt.Sprintf("worker%d", 2)
		}
		base.PriorityClassName = constants.TKE_PRIORITY_CLASS_NAME
	}
	annotations := make(map[string]string)
	annotations["tke.cloud.tencent.com/cross-tenant-eni-enable"] = "true"
	annotations["tke.cloud.tencent.com/networks"] = "tke-bridge,tke-direct-eni,tke-route"
	annotations["tke.cloud.tencent.com/vpc-ip-claim-delete-policy"] = "Never"
	annotations["tke.cloud.tencent.com/claim-expired-duration"] = "72h"
	base.Annotations = annotations
	base.AppImage = appContainerImage
	base.Env = map[string]string{
		"CONFIGMAP_NAME": constants.NginxReverseDefaultConfigmapName,
	}
	base.ServiceAccountName = "flink"

	return base, nil
}

func (s *DeployNginxReverseProxyService) configMap(ctx *deploy.Context, secret *v1.Secret, namespace string) apps.App {
	hrCC := ctx.FlowCC.HadoopYarnCC()
	return ctx.ConfigMap(
		secret,
		apps.CMWithName(constants.NginxReverseDefaultConfigmapName),
		apps.CMWithNamespace(namespace),
		apps.CMAddData(constants.NginxReverseDefaultConf, hrCC.NginxDefaultConf),
	)
}

func (s *DeployNginxReverseProxyService) configMap2(ctx *deploy.Context, secret *v1.Secret, namespace string) apps.App {
	hrCC := ctx.FlowCC.HadoopYarnCC()
	return ctx.ConfigMap(
		secret,
		apps.CMWithName(constants.NginxReverseConfigName),
		apps.CMWithNamespace(namespace),
		apps.CMAddData(constants.NginxReverseNginxConf, hrCC.NginxConf),
	)
}

func (s *DeployNginxReverseProxyService) service(ctx *deploy.Context, componentName string, port int32, namespace string, secret *v1.Secret) apps.App {
	labels := tke.GetTkeService().ComponentLabels(componentName, ctx.ClusterGroup, ctx.Cluster)
	return apps.NewService(
		apps.ServiceWithName(componentName),
		apps.ServiceWithNamespace(namespace),
		apps.ServiceWithPort(port),
		apps.ServiceWithLabels(labels),
		apps.ServiceWithSelector(map[string]string{
			"app": componentName,
		}),
		apps.ServiceWithOwnerReference(tke.GetTkeService().SecretAsOwnerReference(secret)),
	)
}
