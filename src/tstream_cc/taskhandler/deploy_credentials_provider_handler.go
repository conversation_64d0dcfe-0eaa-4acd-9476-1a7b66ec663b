package taskHandler

import (
	"fmt"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/oceanus_controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type DeployCredentialsService struct {
}

func (s *DeployCredentialsService) Apps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	cg := ctx.ClusterGroup

	// 共享集群 包年包月下的eks集群，不做任何处理
	if cg.Type == constants.CLUSTER_GROUP_TYPE_SUB_EKS {
		return result, nil
	}

	// 创建共享集群母集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone == "" {
	}

	// 共享集群母集群，开新区
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone != "" {
		return result, nil
	}

	deployNamespace := constants.OCEANUS_NAMESPACE
	secret, err := ctx.GetOceanusIPSecret()
	if err != nil {
		return result, err
	}

	// 共享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId != "" {
		deployNamespace = fmt.Sprintf("oceanus-%s", ctx.ClusterGroup.SerialId)
		secret, err = ctx.GetNamespaceIPSecret(deployNamespace)
		if err != nil {
			return result, err
		}
	}
	// 独享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId == "" {
	}

	result = append(result, ctx.ServiceBuilder(constants.ComponentCredentialsProvider, constants.CredentialsProviderPort, secret).
		WithOption(apps.ServiceWithNamespace(deployNamespace)).
		Build())

	if ctx.Tke.ArchGeneration <= constants.TKE_ARCH_GENERATION_V2 {
		result = append(result, &credentialsProviderSts{ctx: ctx, namespace: deployNamespace})
	}
	if ctx.Tke.ArchGeneration >= constants.TKE_ARCH_GENERATION_V3 {
		result = append(result, &credentialsProviderDep{ctx: ctx, namespace: deployNamespace})
	}

	return result, nil
}

type credentialsProviderSts struct {
	apps.StatefulSet
	ctx       *deploy.Context
	namespace string
}

func (c *credentialsProviderSts) Params() (interface{}, error) {
	return getCredentialsProviderParams(c.ctx, constants.K8S_KIND_STATEFULSET, c.namespace)
}

func (c *credentialsProviderSts) Decode(params, into interface{}) (interface{}, error) {
	return c.ctx.FlowCC.AppsCC().CredentialsProvider(params, into)
}

func (c *credentialsProviderSts) Transform(cc apps.Client, v interface{}) (interface{}, error) {
	return transformSts(c.ctx, cc, v)
}

type credentialsProviderDep struct {
	apps.Deployment
	ctx       *deploy.Context
	namespace string
}

func (c *credentialsProviderDep) Params() (interface{}, error) {
	return getCredentialsProviderParams(c.ctx, constants.K8S_KIND_DEPLOYMENT, c.namespace)
}

func (c *credentialsProviderDep) Decode(params, into interface{}) (interface{}, error) {
	return c.ctx.FlowCC.AppsCC().CredentialsProvider(params, into)
}

func (c *credentialsProviderDep) Transform(cc apps.Client, v interface{}) (interface{}, error) {
	return transformDep(c.ctx, cc, v)
}

func getCredentialsProviderParams(ctx *deploy.Context, workLoadKind, namespace string) (*oceanus_controller.CredentialProvider, error) {
	image, err := ctx.CC().ImageRegistry().CredentialsProvider()
	if err != nil {
		return nil, err
	}

	base, err := ctx.ParamBase(workLoadKind, namespace, constants.ComponentCredentialsProvider, false)
	if err != nil {
		return nil, err
	}

	if ctx.Tke.ArchGeneration <= constants.TKE_ARCH_GENERATION_V2 {
		base.HostNetwork = true
	}

	if ctx.Tke.ArchGeneration >= constants.TKE_ARCH_GENERATION_V2 &&
		ctx.ClusterType == constants.K8S_CLUSTER_TYPE_TKE { //TKE 集群

		if ctx.ClusterGroup.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && ctx.ClusterGroup.AgentSerialId == "" {
			for key := range base.NodeSelector {
				delete(base.NodeSelector, key)
			}
			base.NodeSelector[constants.TKE_CVM_WORKER_LABEL_KEY] = fmt.Sprintf("worker")
		}
		base.PriorityClassName = constants.TKE_PRIORITY_CLASS_NAME
	}
	// eks增加role type
	if ctx.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		eksRoleName, err := ctx.CC().FlowCC().EksCredentialsProviderRoleName()
		if err != nil {
			return nil, err
		}
		base.Annotations[constants.EKS_ROLE_NAME] = eksRoleName
	}
	username, passwd, url, err := ctx.FlowCC.NginxCC().Info()
	if err != nil {
		return nil, err
	}

	base.Env["PATH"] = "/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"
	base.Env["GALILEO_URL"] = fmt.Sprintf("%s/shark", url)
	base.Env["NGINX_USERNAME"] = username
	base.Env["NGINX_PASSWORD"] = passwd
	base.Env["TKE_CLUSTER_ID"] = ctx.Tke.InstanceId
	base.Env["CLS_HOST"] = "ap-guangzhou.cls.tencentyun.com"
	if ctx.ClusterType == constants.K8S_CLUSTER_TYPE_EKS {
		base.Env["EKS_CLUSTER_ID"] = ctx.Tke.InstanceId
	}

	err = fillTestParams(ctx, base)
	if err != nil {
		return nil, err
	}

	base.AppImage = image
	base.ListenPort = constants.CredentialsProviderPort
	base.Replicas = 2

	data := &oceanus_controller.CredentialProvider{
		Base:            base,
		OceanusAuthHost: ctx.CC().OceanusDomain(),
	}
	return data, nil
}
