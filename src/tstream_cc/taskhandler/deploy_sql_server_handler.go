package taskHandler

import (
	"fmt"
	v1 "k8s.io/api/core/v1"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/oceanus_controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type DeploySqlServerService struct {
}

func (s *DeploySqlServerService) Apps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	cg := ctx.ClusterGroup

	// 共享集群 包年包月下的eks集群，不做任何处理
	if cg.Type == constants.CLUSTER_GROUP_TYPE_SUB_EKS {
		return result, nil
	}

	// 创建共享集群母集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone == "" {
		return result, nil
	}

	// 共享集群母集群，开新区
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone != "" {
		return result, nil
	}

	deployNamespace := constants.OCEANUS_NAMESPACE
	ssRoleNamespace := deployNamespace
	secret, err := ctx.GetOceanusIPSecret()
	flinkNamespace := constants.DEFAULT_NAMESPACE
	if err != nil {
		return result, err
	}
	// 共享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId != "" {
		deployNamespace = fmt.Sprintf("oceanus-%s", ctx.ClusterGroup.SerialId)
		ssRoleNamespace = ctx.ClusterGroup.SerialId
		flinkNamespace = ssRoleNamespace
		secret, err = ctx.GetNamespaceIPSecret(deployNamespace)
		if err != nil {
			return result, err
		}
	}

	// 独享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId == "" {
	}

	cmNamespaceOption := apps.CMWithNamespace(deployNamespace)
	sNamespaceOption := apps.ServiceWithNamespace(deployNamespace)
	result = append(result,
		ctx.LogListenerConfigMapBuilder(false, secret).WithOption(cmNamespaceOption).Build(),
		ctx.LogListenerMetaConfigMapBuilder(constants.ComponentSqlServer, secret).WithOption(cmNamespaceOption).Build(),
		ctx.HadoopConfigMapBuilder(secret).WithOption(cmNamespaceOption).Build(),
		&createServiceAccountApp{ctx: ctx, namespace: deployNamespace, name: "sqlserver"},
		&createRoleApp{ctx: ctx, namespace: flinkNamespace, name: "sqlserver", key: "sqlserver-role.yaml"},
		&createRoleBindingApp{ctx: ctx, namespace: flinkNamespace, name: "sqlserver-role-binding", roleName: "sqlserver", serviceAccount: "sqlserver", serviceAccountNamespace: deployNamespace},

		s.configMap(ctx, cmNamespaceOption, secret),
		ctx.ServiceBuilder(constants.ComponentSqlServer, constants.SqlServerPort, secret).WithOption(sNamespaceOption).Build())

	if ctx.Tke.ArchGeneration <= constants.TKE_ARCH_GENERATION_V2 {
		result = append(result, &sqlServerSts{ctx: ctx, deployNamespace: deployNamespace})
	}
	if ctx.Tke.ArchGeneration >= constants.TKE_ARCH_GENERATION_V3 {
		result = append(result, &sqlServerDep{ctx: ctx, deployNamespace: deployNamespace})
	}

	return result, nil
}

func (s *DeploySqlServerService) configMap(ctx *deploy.Context, nOption apps.AssignCMOption, secret *v1.Secret) apps.App {
	sqlCC := ctx.FlowCC.SqlServerCC()
	return ctx.ConfigMap(
		secret,
		nOption,
		apps.CMWithName(constants.SqlServerConfigmapName),
		apps.CMAddData("application.properties", sqlCC.ApplicationProperties),
		apps.CMAddData("connector.json", sqlCC.ConnectorJson),
		apps.CMAddData("libs.json", sqlCC.LibsJson),
		apps.CMAddData("logback.xml", sqlCC.LogBackXml))

}

type sqlServerSts struct {
	apps.StatefulSet
	ctx             *deploy.Context
	deployNamespace string
	flinkNamespace  string
}

func (c *sqlServerSts) Params() (interface{}, error) {
	return getSqlServerParams(c.ctx, constants.K8S_KIND_STATEFULSET, c.deployNamespace)
}

func (c *sqlServerSts) Decode(params, into interface{}) (interface{}, error) {
	return c.ctx.FlowCC.AppsCC().SqlServer(params, into)
}

func (c *sqlServerSts) Transform(cc apps.Client, v interface{}) (interface{}, error) {
	return transformSts(c.ctx, cc, v)
}

type sqlServerDep struct {
	apps.Deployment
	ctx             *deploy.Context
	deployNamespace string
	flinkNamespace  string
}

func (c *sqlServerDep) Params() (interface{}, error) {
	return getSqlServerParams(c.ctx, constants.K8S_KIND_DEPLOYMENT, c.deployNamespace)
}

func (c *sqlServerDep) Decode(params, into interface{}) (interface{}, error) {
	return c.ctx.FlowCC.AppsCC().SqlServer(params, into)
}

func (c *sqlServerDep) Transform(cc apps.Client, v interface{}) (interface{}, error) {
	return transformDep(c.ctx, cc, v)
}

func getSqlServerParams(ctx *deploy.Context, workLoadKind, deployNamespace string) (*oceanus_controller.Base, error) {
	appContainerImage, err := ctx.CC().ImageRegistry().SqlServer()
	if err != nil {
		return nil, err
	}

	logContainerImage, err := ctx.CC().ImageRegistry().LogListener()
	if err != nil {
		return nil, err
	}

	base, err := ctx.ParamBase(workLoadKind, deployNamespace, constants.ComponentSqlServer, true)
	if err != nil {
		return nil, err
	}

	if ctx.Tke.ArchGeneration >= constants.TKE_ARCH_GENERATION_V2 &&
		ctx.ClusterType == constants.K8S_CLUSTER_TYPE_TKE &&
		ctx.ClusterGroup.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && ctx.ClusterGroup.AgentSerialId == "" { //TKE 集群
		for key := range base.NodeSelector {
			delete(base.NodeSelector, key)
		}

		if ctx.Tke.ArchGeneration >= constants.TKE_ARCH_GENERATION_V5 {
			base.NodeSelector[constants.TKE_CVM_WORKER_LABEL_KEY] = fmt.Sprintf("worker")
		} else {
			base.NodeSelector[constants.TKE_CVM_WORKER_NUM_LABEL_KEY] = fmt.Sprintf("worker%d", 1)
		}
		base.PriorityClassName = constants.TKE_PRIORITY_CLASS_NAME
	}

	base.AppImage = appContainerImage
	base.SidecarImage = logContainerImage
	base.ListenPort = constants.SqlServerPort
	base.ServiceAccountName = constants.SERVICE_ACCOUNT_SQLSERVER
	base.Env["NAMESPACE"] = deployNamespace
	base.DnsPolicy = constants.DEFAULT_DNS_POLICY
	// 共享集群走自己的coredns https://tapd.woa.com/********/prong/stories/view/10********116032631
	if ctx.ClusterGroup.AgentSerialId != "" {
		base.DnsPolicy = constants.NONE_DNS_POLICY
		ns := fmt.Sprintf("oceanus-%s", ctx.ClusterGroup.SerialId)
		service, err := ctx.K8sService().GetService(ctx.ClientSet(), ns, "kube-dns")
		if err != nil {
			logger.Errorf("get core dns service from %s error: %+v", ns, err)
			return nil, err
		}
		base.Nameserver = service.Spec.ClusterIP
	}

	return base, nil
}
