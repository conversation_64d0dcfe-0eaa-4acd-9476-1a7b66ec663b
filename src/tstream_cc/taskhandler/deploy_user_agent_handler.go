package taskHandler

import (
	"fmt"
	v1 "k8s.io/api/core/v1"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/oceanus_controller"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/region"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type DeployUserAgentService struct {
}

func (s *DeployUserAgentService) Apps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	cg := ctx.ClusterGroup

	// 共享集群 包年包月下的eks集群，不做任何处理
	if cg.Type == constants.CLUSTER_GROUP_TYPE_SUB_EKS {
		return result, nil
	}

	if ctx.Tke.ArchGeneration < constants.TKE_ARCH_GENERATION_V4 {
		return result, nil
	}

	// 共享集群母集群，开新区
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone != "" {
		return result, nil
	}

	// 创建共享集群母集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone == "" {
		return result, nil
	}

	// 独享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId == "" {
		return result, nil
	}

	// 共享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId != "" {
		deployNamespace := fmt.Sprintf("oceanus-%s", ctx.ClusterGroup.SerialId)
		secret, err := ctx.GetNamespaceIPSecret(deployNamespace)
		if err != nil {
			return result, err
		}
		cmNamespaceOption := apps.CMWithNamespace(deployNamespace)
		sNamespaceOption := apps.ServiceWithNamespace(deployNamespace)
		result = append(result,
			ctx.HadoopConfigMapBuilder(secret).WithOption(cmNamespaceOption).Build(),
			ctx.LogListenerConfigMapBuilder(true, secret).WithOption(cmNamespaceOption).Build(),
			ctx.LogListenerMetaConfigMapBuilder(constants.ComponentUserAgent, secret).WithOption(cmNamespaceOption).Build(),
			s.userAgentConfigMap(ctx, cmNamespaceOption, secret),
			&userAgent{ctx: ctx, namespace: deployNamespace},
			ctx.ServiceBuilder(constants.ComponentUserAgent, constants.UserAgentPort, secret).WithOption(sNamespaceOption).Build())
	}
	return result, nil
}

func (s *DeployUserAgentService) userAgentConfigMap(ctx *deploy.Context, nOption apps.AssignCMOption, secret *v1.Secret) apps.App {
	uaCC := ctx.FlowCC.UserAgentCC()
	return ctx.ConfigMap(
		secret,
		nOption,
		apps.CMWithName(constants.UserAgentConfigmapName),
		apps.CMAddData("conf.yaml", s.getConfigForConf(ctx)),
		// from ConfigureCenter.Flow.K8S.ClusterAdmin flink-conf-cos.yaml
		apps.CMAddData("flink-conf.yaml", ctx.FlinkConfForUa(constants.ZooKeeperComponentReplicaNumber)),
		apps.CMAddData("log4j.properties", uaCC.Flink111Log4jV2),
		// flink-1.13 k8s启动，log 的配置名字改为这个
		apps.CMAddData("log4j-console.properties", uaCC.FlinkLog4jV2),
		apps.CMAddData("log4j-1.properties", uaCC.Log4jV1))
	// ClusterAdmin 使用的 Log4j 1.x 配置文件 不能用 Common 下的 log4j.properties, 因为还需要写入日志到文件, 以便 CLS 采集

	// 去掉原来 1.10 集群 采用 Log4j的逻辑 新部署的集群，不会再是 1.10 的了
	// 去掉原来 on emr禁止日志输出到console，防止滚动日志失效
}

func (s *DeployUserAgentService) getConfigForConf(ctx *deploy.Context) apps.CMDataValue {
	return func() (string, error) {
		nginxUsername, nginxPassword, url, err := ctx.FlowCC.NginxCC().Info()
		if err != nil {
			return "", err
		}

		latestFlinkVersion := ctx.CC().DefaultFlinkVersion()

		regionId, err := region.GetRegionIdByName(ctx.ClusterGroup.Region)
		if err != nil {
			return "", err
		}

		params := struct {
			NginxUsername         string
			NginxPassword         string
			LatestFlinkVersion    string
			RegionId              int64
			ClusterId             int64
			PublicServiceNginxUrl string

			// 下面三个参数已经不用, 为了保持历史模板的兼容性, 暂时保留
			ZooKeeperUrl string
			YarnRestUrl  string
			HdfsRestUrl  string
		}{
			NginxUsername:         nginxUsername,
			NginxPassword:         nginxPassword,
			LatestFlinkVersion:    latestFlinkVersion,
			RegionId:              int64(regionId),
			ClusterId:             ctx.Cluster.Id,
			PublicServiceNginxUrl: url,
		}

		return ctx.FlowCC.UserAgentCC().Conf(params)
	}
}

type userAgent struct {
	apps.Deployment
	ctx       *deploy.Context
	namespace string
}

func (c *userAgent) Params() (interface{}, error) {
	return c.getUserAgentParams(c.ctx)
}

func (c *userAgent) Decode(params, into interface{}) (interface{}, error) {
	return c.ctx.FlowCC.AppsCC().UserAgent(params, into)
}

func (c *userAgent) getUserAgentParams(ctx *deploy.Context) (*oceanus_controller.Base,
	error) {
	var err error
	appContainerImage, exists := ctx.GetParam(constants.FLOW_PARAM_USERAGENT_IMAGE, "")
	if !exists {
		appContainerImage, err = ctx.CC().ImageRegistry().UserAgent()
		if err != nil {
			return nil, err
		}
	}
	logContainerImage, err := ctx.CC().ImageRegistry().LogListener()
	if err != nil {
		return nil, err
	}

	base, err := ctx.ParamBase(constants.K8S_KIND_DEPLOYMENT,
		c.namespace,
		constants.ComponentUserAgent,
		true)
	if err != nil {
		return nil, err
	}

	base.AppImage = appContainerImage
	base.SidecarImage = logContainerImage
	base.ListenPort = constants.UserAgentPort

	// 识别 flink pod ns
	base.Env["NAMESPACE"] = c.namespace

	// 识别 credential NAMESPACE
	base.Env["CREDENTIAL_NAMESPACE"] = c.namespace
	base.ServiceAccountName = constants.SERVICE_ACCOUNT_OCEANUS

	base.DnsPolicy = constants.DEFAULT_DNS_POLICY
	// 共享集群走自己的coredns https://tapd.woa.com/********/prong/stories/view/10********116032631
	if ctx.ClusterGroup.AgentSerialId != "" {
		base.DnsPolicy = constants.NONE_DNS_POLICY
		ns := fmt.Sprintf("oceanus-%s", ctx.ClusterGroup.SerialId)
		service, err := ctx.K8sService().GetService(ctx.ClientSet(), ns, "kube-dns")
		if err != nil {
			logger.Errorf("get core dns service from %s error: %+v", ns, err)
			return nil, err
		}
		base.Nameserver = service.Spec.ClusterIP
	}
	logger.Errorf("@@@@@@@@ base is %+v", base)

	return base, nil
}
