package taskHandler

import (
	"encoding/json"
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/cloud_api_protocol/log"
	flow2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/flow"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/log"
)

type ApplyClsLogConfigHandler struct {
}

func (h *ApplyClsLogConfigHandler) CompleteTask(request *flow.TaskExecRequest) (resp *flow.TaskExecResponse) {
	flowService := flow2.GetFlowService()
	defer func() {
		if err := recover(); err != nil {
			resp = flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_INIT, 0.1, fmt.Sprintf("%v",
				err), request.Params)
		}
	}()
	applyClsLogConfigService, err := NewApplyClsLogConfigService(request)
	if err != nil {
		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_RUNNING, 0.1,
			fmt.Sprintf("%v", err), request.Params)
	}
	return applyClsLogConfigService.apply()
}

type ApplyClsLogConfigService struct {
	*flow2.ClusterFlowService
}

func NewApplyClsLogConfigService(request *flow.TaskExecRequest) (*ApplyClsLogConfigService, error) {
	clusterFlowService, err := flow2.NewClusterFlowService(request)
	if err != nil {
		logger.Errorf("NewApplyClsLogConfigService NewClusterFlowService has return error %v", err)
		return nil, err
	}
	result := &ApplyClsLogConfigService{
		ClusterFlowService: clusterFlowService,
	}
	return result, nil
}

func (s *ApplyClsLogConfigService) apply() (rsp *flow.TaskExecResponse) {
	uin := ""
	if uin, ok := s.Request.Params["Uin"]; !ok || uin == "" {
		return s.RetryRsp("TaskExecRequest clsParams not valid, uin not set")
	}
	subAccountUin := ""
	if subAccountUin, ok := s.Request.Params["SubAccountUin"]; !ok || subAccountUin == "" {
		return s.RetryRsp("TaskExecRequest clsParams not valid, subAccountUin not set")
	}
	Region := ""
	if Region, ok := s.Request.Params["Region"]; !ok || Region == "" {
		return s.RetryRsp("TaskExecRequest clsParams not valid, Region not set")
	}
	kubeConfig := ""
	if kubeConfig, ok := s.Request.Params["kubeConfig"]; !ok || kubeConfig == "" {
		return s.RetryRsp("TaskExecRequest clsParams not valid, kubeConfig not set")
	}
	clsInfo := make([]*log.ClusterBundleClsInfo, 0)
	clsInfoStr := ""
	if clsInfoStr, ok := s.Request.Params["clsInfoStr"]; !ok || clsInfoStr == "" {
		return s.RetryRsp("TaskExecRequest clsParams not valid, clsInfoStr not set")
	}
	err := json.Unmarshal([]byte(clsInfoStr), &clsInfo)
	if err != nil {
		return s.RetryRsp("cls info not valid ")
	}
	// 初始化新的logConfig
	if clsInfo != nil && len(clsInfo) > 0 {
		for _, v := range clsInfo {
			err := (&service.ApplyLogConfigsCrdInstanceService{}).ApplyLogConfigsCrdInstance(uin, subAccountUin, Region, kubeConfig, v.ClsLogTopicId)
			if err != nil {
				logger.Errorf("init logConfig failed with error %v", err)
				return s.RetryRspWithErr(err)
			}
		}
	} else {
		logger.Warningf("req.ClsInfo is empty, cannot ApplyLogConfigsCrdInstance")
	}
	return s.DoneRsp("ApplyLogConfigsCrdInstance successfully")
}
