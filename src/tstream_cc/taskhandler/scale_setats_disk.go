package taskHandler

import (
	"fmt"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/uuid"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type ScaleSetatsDisk struct {
}

func (c ScaleSetatsDisk) Apps(ctx *deploy.Context) ([]apps.App, error) {
	requestId := fmt.Sprintf("ScaleSetatsService-%s-%s", uuid.NewRandom().String(), ctx.ClusterGroup.SerialId)
	logger.Infof("ScaleSetatsService Apps, requestId:%s", requestId)
	result := make([]apps.App, 0)
	if _, exists := ctx.GetParam(constants.FLOW_PARAM_SETATS_SCALE_DISK, ""); exists {
		result = append(result, &scaleDisk{ctx: ctx, requestId: requestId})
	}
	return result, nil
}
