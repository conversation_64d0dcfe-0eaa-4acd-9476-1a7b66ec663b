package taskHandler

import (
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/auth"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type DeployNodeExpansionService struct {
}

func (s *DeployNodeExpansionService) Apps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	cg := ctx.ClusterGroup

	InWhiteList := auth.IsInWhiteList(int64(cg.AppId), constants.WHITE_LIST_NODE_EXPANSION)
	if !InWhiteList {
		logger.Infof("AppID %d does not have permission to create node expansion", cg.AppId)
		return result, nil
	}

	// 共享集群 包年包月下的eks集群，不做任何处理
	if cg.Type == constants.CLUSTER_GROUP_TYPE_SUB_EKS {
		return result, nil
	}

	if ctx.Tke.ArchGeneration < constants.TKE_ARCH_GENERATION_V2 {
		return result, nil
	}

	// 共享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId != "" {
		return result, nil
	}

	// 共享集群母集群，开新区
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone != "" {
		return result, nil
	}

	// 创建共享集群母集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM && ctx.NewZone == "" {
	}

	// 独享集群
	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId == "" {

	}
	// TODO 副本数量

	secret, err := ctx.GetOceanusIPSecret()
	if err != nil {
		return result, err
	}

	go TryModifyClusterScheduler(cg, "LeastRequestedPriority")

	neCC := ctx.FlowCC.NodeExpansionControllerCC()
	return []apps.App{
		&createServiceAccountApp{ctx: ctx, namespace: "oceanus", name: "node-expansion"},
		apps.NewClusterRole(neCC.ClusterRole),
		apps.NewClusterRoleBinding(neCC.ClusterRoleBinding),
		&nodeExpansion{ctx: ctx},
		ctx.Service(constants.ComponentNodeExpansion, constants.NodeExpansionPort, secret),
		apps.NewSecret(neCC.Secret),
		&nodeExpansionCrd{ctx: ctx},
	}, nil
}

type nodeExpansionCrd struct {
	ctx *deploy.Context
	apps.AdmissionCrd
}

func (c *nodeExpansionCrd) Params() (interface{}, error) {
	return nil, nil
}

func (c *nodeExpansionCrd) Decode(params, into interface{}) (interface{}, error) {
	return c.ctx.FlowCC.NodeExpansionControllerCC().WebHookCrd(params, into)
}

type nodeExpansion struct {
	ctx *deploy.Context
	apps.Deployment
}

func (c *nodeExpansion) Params() (interface{}, error) {
	appContainerImage, err := c.ctx.CC().ImageRegistry().NodeExpansionController()
	if err != nil {
		return nil, err
	}

	base, err := c.ctx.ParamBase(constants.K8S_KIND_DEPLOYMENT,
		constants.OCEANUS_NAMESPACE,
		constants.ComponentNodeExpansion, false)
	if err != nil {
		return nil, err
	}

	if c.ctx.Tke.ArchGeneration >= constants.TKE_ARCH_GENERATION_V5 &&
		c.ctx.ClusterType == constants.K8S_CLUSTER_TYPE_TKE &&
		c.ctx.ClusterGroup.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && c.ctx.ClusterGroup.AgentSerialId == "" { //TKE 集群
		for key := range base.NodeSelector {
			delete(base.NodeSelector, key)
		}

		base.NodeSelector[constants.TKE_CVM_WORKER_LABEL_KEY] = fmt.Sprintf("worker")
		base.PriorityClassName = constants.TKE_PRIORITY_CLASS_NAME
	}
	base.AppImage = appContainerImage
	base.ServiceAccountName = "node-expansion"

	return base, nil
}

func (c *nodeExpansion) Decode(params, into interface{}) (interface{}, error) {
	return c.ctx.FlowCC.NodeExpansionControllerCC().NodeExpansionController(params, into)
}
