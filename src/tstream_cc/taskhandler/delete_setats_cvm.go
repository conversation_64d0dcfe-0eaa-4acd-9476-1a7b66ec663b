package taskHandler

import (
	"encoding/json"
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/uuid"

	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/cbs"
	cvm2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cvm"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	yuntiService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/yunti"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type deleteSetatsCvmService struct {
}

func (s *deleteSetatsCvmService) Apps(ctx *deploy.Context) ([]apps.App, error) {
	requestId := fmt.Sprintf("deleteSetatsCvmService-%s-%s", uuid.NewRandom().String(), ctx.ClusterGroup.SerialId)
	logger.Infof("deleteSetatsCvmService Apps, requestId:%s", requestId)
	result := make([]apps.App, 0)
	result = append(result, &deleteSetatsCvmApp{ctx: ctx, requestId: requestId})
	return result, nil
}

type deleteSetatsCvmApp struct {
	ctx *deploy.Context
	apps.ApplyApp
	requestId string
}

func (c *deleteSetatsCvmApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	canDeleteIds, err := c.getSetasCvm()
	if err != nil {
		logger.Errorf("%s get setats cvm failed: %v", c.requestId, err)
		return
	}

	err = c.deleteCvm(canDeleteIds)
	if err != nil {
		logger.Errorf("%s delete cvm failed: %v", c.requestId, err)
		return nil, err
	}
	p := &poolCvmApp{ctx: c.ctx}
	err = p.removeInstanceFromTke(canDeleteIds)
	if err != nil {
		logger.Errorf("deleteSetatsCvmApp delete cvm failed: %v", err)
		return
	}
	return
}

func (c *deleteSetatsCvmApp) getSetasCvm() ([]*string, error) {
	instances := make([]*string, 0)

	scaleDiskFun := &scaleDisk{
		ctx:       c.ctx,
		requestId: c.requestId,
	}

	labelInstancesMap := make(map[string][]string, 0)
	masterLabels := make(map[string]string)
	masterLabels[constants.TKE_CVM_LABEL_KEY] = constants.SETATS_MANAGER_LABEL
	masterLabelInstancesMap, err := scaleDiskFun.GetWorker(masterLabels)
	if err != nil {
		logger.Errorf("%s get setats master cvm failed: %v", c.requestId, err)
		return instances, err
	}
	for masterLabel, masterInstances := range masterLabelInstancesMap {
		labelInstancesMap[masterLabel] = masterInstances
	}

	workerLabels := make(map[string]string)
	workerLabels[constants.TKE_CVM_LABEL_KEY] = constants.SETATS_WORKER_LABEL
	workerLabelInstancesMap, err := scaleDiskFun.GetWorker(workerLabels)
	if err != nil {
		logger.Errorf("%s get setats worker cvm failed: %v", c.requestId, err)
		return instances, err
	}
	for workerLabel, workerInstances := range workerLabelInstancesMap {
		labelInstancesMap[workerLabel] = workerInstances
	}
	logger.Infof("%s labelInstancesMap: %+v", c.requestId, labelInstancesMap)

	for _, instanceList := range labelInstancesMap {
		for _, s := range instanceList {
			tmp := s
			instances = append(instances, &tmp)
		}
	}
	return instances, nil
}

func (c *deleteSetatsCvmApp) deleteCvm(cvmInstances []*string) (err error) {
	if len(cvmInstances) == 0 {
		return nil
	}

	b, _ := json.Marshal(cvmInstances)
	logger.Infof("%s deleteSetatsCvmApp: %s", c.requestId, string(b))

	cg := c.ctx.ClusterGroup
	region := cg.Region

	if cg.NetEnvironmentType == constants.NETWORK_ENV_INNER_VPC {
		return yuntiService.TerminateInstances(fmt.Sprintf("CSIG流计算Oceanus集群%s销毁", cg.SerialId), cvmInstances, region, cg.SerialId)
	}

	err = cbs.GetCbsService(cg.NetEnvironmentType, region).TerminateCbsFromCVM(cvmInstances)
	if err != nil {
		return err
	}
	cvmService := cvm2.GetCvmService()
	return cvmService.TerminateInstancesWithScsAccount(region, cvmInstances)
}
