package taskHandler

import (
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"testing"
)

func Test_createVPCHandler_HandleTaskStatusInit(t *testing.T) {
	request := &flow.TaskExecRequest{
		Retrycount: 0,
		Processkey: "",
		Taskcode:   "",
		DocId:      "",
		FlowId:     "",
		TaskId:     "",
		Params: map[string]string{
			constants.FLOW_PARAM_CLUSTER_GROUP_ID: *fTestClusterGroupId,
			constants.FLOW_PARAM_CLUSTER_ID:       fmt.Sprintf("%d", *fTestClusterId),
			constants.FLOW_PARAM_CLUSTER_TYPE:       "1",
		},
	}

	handler := &CreateVPCHandler{}
	rsp := handler.HandleTaskStatusInit("1", request)
	t.Log(rsp)
}

func Test_createVPCHandler_HandleTaskStatusRunning(t *testing.T) {
	request := &flow.TaskExecRequest{
		Retrycount: 0,
		Processkey: "",
		Taskcode:   "",
		DocId:      "",
		FlowId:     "",
		TaskId:     "",
		Params: map[string]string{
			constants.FLOW_PARAM_CLUSTER_GROUP_ID: *fTestClusterGroupId,
			constants.FLOW_PARAM_CLUSTER_ID:       fmt.Sprintf("%d", *fTestClusterId),
			constants.FLOW_PARAM_CLUSTER_TYPE:       "1",
		},
	}

	handler := &CreateVPCHandler{}
	rsp := handler.HandleTaskStatusRunning("1", request)
	t.Log(rsp)
}
