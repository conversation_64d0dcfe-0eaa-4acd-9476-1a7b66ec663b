package taskHandler

import (
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type DeploySetatsHiveMetastoreHandler struct {
}

const (
	SetatsHiveMetastoreConfigTemplate = "setats-hive-metastore-conf"
	SetatsHiveMetastoreComponentName  = "setats-hive-metastore"
	SetatsHiveMetastoreServicePort    = 9083
)

func (s *DeploySetatsHiveMetastoreHandler) Apps(ctx *deploy.Context) ([]apps.App, error) {

	secret, err := ctx.GetNamespaceIPSecret(constants.DEFAULT_NAMESPACE)
	if err != nil {
		logger.Errorf("DeploySetatsHiveMetastoreHandler Apps Failed to get namespace ip secret:%s, with errors:%+v", constants.DEFAULT_NAMESPACE, err)
		return nil, err
	}
	sNamespaceOption := apps.ServiceWithNamespace(constants.DEFAULT_NAMESPACE)
	apps := []apps.App{
		s.hiveMetastoreConfMap(ctx),
		&setatsHiveMetastore{ctx: ctx},
		ctx.ServiceBuilder(SetatsHiveMetastoreComponentName, SetatsHiveMetastoreServicePort, secret).WithOption(sNamespaceOption).Build(),
		&recordHiveMetaInfoApp{ctx: ctx},
	}
	return apps, nil
}

type recordHiveMetaInfoApp struct {
	apps.ApplyApp
	ctx *deploy.Context
}

func (c *recordHiveMetaInfoApp) Apply(_ apps.Client, _ interface{}) (interface{}, error) {
	count, _setats, err := service.GetSetatsByClusterGroupSerialId(c.ctx.ClusterGroup.SerialId)
	if err != nil {
		logger.Errorf("Failed to get setats by ClusterGroupSerialId id:%s, with errors:%+v", c.ctx.ClusterGroup.SerialId, err)
		return nil, errorcode.FailedOperationCode.NewWithErr(err)
	}
	if count == 0 {
		logger.Errorf("Failed to get setats by ClusterGroupSerialId id:%s, with count 0", c.ctx.ClusterGroup.SerialId)
		return nil, errorcode.FailedOperationCode.NewWithMsg(fmt.Sprintf("setats size is 0"))
	}
	err = service2.RecordHiveMetaInfoApp(_setats.SerialId)
	if err != nil {
		logger.Errorf("Failed to recordHiveMetaInfo, with errors:%+v", err)
		return nil, err
	}
	return nil, nil
}

func (s *DeploySetatsHiveMetastoreHandler) hiveMetastoreConfMap(ctx *deploy.Context) apps.App {
	return apps.NewConfigMap(
		apps.CMWithName(SetatsHiveMetastoreConfigTemplate),
		apps.CMWithNamespace(constants.DEFAULT_NAMESPACE),
		apps.CMAddData("hive-site.xml", func() (string, error) {
			return ctx.FlowCC.SetatsCC().HiveSite(struct {
				DATABASE_HOST     string
				DATABASE_PORT     string
				DATABASE_USER     string
				DATABASE_PASSWORD string
			}{
				DATABASE_HOST:     ctx.Cdb.Vip,
				DATABASE_PORT:     fmt.Sprintf("%d", ctx.Cdb.Vport),
				DATABASE_USER:     ctx.Cdb.User,
				DATABASE_PASSWORD: ctx.Cdb.Password,
			})
		}),
	)
}

type setatsHiveMetastore struct {
	ctx *deploy.Context
	apps.StatefulSet
}

func (c *setatsHiveMetastore) Params() (interface{}, error) {
	appContainerImage, err := c.ctx.CC().ImageRegistry().SetatsHiveMetastore()
	if err != nil {
		logger.Errorf("DeploySetatsHiveMetastoreHandler Apps Failed to get setats hive metastore image:%s, with errors:%+v", constants.DEFAULT_NAMESPACE, err)
		return nil, err
	}
	base, err := c.ctx.ParamBase(constants.K8S_KIND_DEPLOYMENT, constants.DEFAULT_NAMESPACE, SetatsHiveMetastoreComponentName, true)
	if err != nil {
		logger.Errorf("DeploySetatsHiveMetastoreHandler Apps Failed to get param base:%s, with errors:%+v", constants.DEFAULT_NAMESPACE, err)
		return nil, err
	}
	// sts 弹性网卡不变
	base.Annotations["tke.cloud.tencent.com/vpc-ip-claim-delete-policy"] = "Never"
	base.AppImage = appContainerImage
	base.ConfigMapName = SetatsHiveMetastoreConfigTemplate
	hiveMetastoreUser, err := c.ctx.FlowCC.SetatsCC().HiveMetastoreUser()
	if err != nil {
		logger.Errorf("setatsHiveMetastore Failed to get hiveMetastoreUser, with errors:%+v", err)
		return nil, err
	}
	hiveMetastorePass, err := c.ctx.FlowCC.SetatsCC().HiveMetastorePass()
	if err != nil {
		logger.Errorf("setatsHiveMetastore Failed to get hiveMetastorePass, with errors:%+v", err)
		return nil, err
	}

	if base.Env == nil {
		base.Env = make(map[string]string)
	}
	base.Env["hiveMetastoreUser"] = hiveMetastoreUser
	base.Env["hiveMetastorePass"] = hiveMetastorePass
	base.Env["CREDENTIAL_NAMESPACE"] = service2.GetDefaultCredentialNamespace(c.ctx.ClusterGroup)
	base.Env["bucket"] = c.ctx.Cluster.DefaultCOSBucket
	base.Env["region"] = c.ctx.ClusterGroup.Region

	count, _setats, err := service.GetSetatsByClusterGroupSerialId(c.ctx.ClusterGroup.SerialId)
	if err != nil {
		logger.Errorf("Faicled to get setats by ClusterGroupSerialId id:%s, with errors:%+v", c.ctx.ClusterGroup.SerialId, err)
		return nil, errorcode.FailedOperationCode.NewWithErr(err)
	}
	if count == 0 {
		logger.Errorf("Failed to get setats by ClusterGroupSerialId id:%s, with count 0", c.ctx.ClusterGroup.SerialId)
		return nil, errorcode.FailedOperationCode.NewWithMsg(fmt.Sprintf("setats size is 0"))
	}

	// setats refs
	setatsResourceInfoList, err := service2.ListSetatsRefBySetatsSerialId(_setats.SerialId)
	if err != nil {
		logger.Errorf("Failed to ListSetatsRefBySetatsSerialId for %s because %+v", _setats.SerialId, err)
		return nil, err
	}

	var refs = ""
	if len(setatsResourceInfoList) > 0 {
		if rrefs, exists := c.ctx.GetParam(constants.FLOW_PARAM_SETATS_REFS, ""); exists {
			logger.Infof("Get Setats refs from Param SetatsRefs is %s", rrefs)
			refs = rrefs
		} else {
			refs, err = UploadJarToUserCos(c.ctx.Cluster, c.ctx.ClusterGroup, _setats, setatsResourceInfoList)
			if err != nil {
				logger.Errorf("Failed to generate SetatsnRefs,error: %+v", err)
				return nil, err
			}
			if refs != "" {
				c.ctx.SetReturnParam(constants.FLOW_PARAM_SETATS_REFS, refs)
			}
		}
	}
	if refs != "" {
		base.Env["setatsJar"] = refs
	}
	return base, nil
}

func (c *setatsHiveMetastore) Decode(params, info interface{}) (interface{}, error) {
	return c.ctx.FlowCC.SetatsCC().HiveMetastore(params, info)
}
