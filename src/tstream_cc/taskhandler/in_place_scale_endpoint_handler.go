package taskHandler

import (
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	flow2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/flow"
	job_autoscale2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job_autoscale"
)

type inPlaceScaleEndpointHandler struct {
}

func (this *inPlaceScaleEndpointHandler) HandleTaskStatusInit(requestId string, request *flow.TaskExecRequest, task *job_autoscale2.ScalingTask) (resp *flow.TaskExecResponse) {
	flowService := flow2.GetFlowService()
	params := request.Params
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf("AutoScale - inPlace - endpointHandler HandleTaskStatusInit failed for Job %s, Action %s, because %+v",
				task.Action.JobSerialId, task.Action.SerialId, err)
			resp = flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1, fmt.Sprintf("%v", err), params)
		}
	}()
	context, err := GetTaskContextFromParam(params)
	if err != nil {
		logger.Errorf("AutoScale - inPlace - Fail to get context for job %s, Action %s, cause by %+v.", task.Action.JobSerialId, task.Action.SerialId, err)
		return SuccessRsp(params)
	}
	// 创建扩缩容事件，更新 Action 的状态
	if _, err = task.MaybeSaveScaleEvent(context); err != nil {
		logger.Errorf("AutoScale - inPlace - Failed to save event for job %s, cause by %+v", task.Action.JobSerialId, err)
	}
	if err = task.UpdateLastScaleTime(); err != nil {
		logger.Errorf("AutoScale - inPlace - Failed to update last scale time for job %s, cause by %+v", task.Action.JobSerialId, err)
	}
	if task.EventErrorCode != "" {
		logger.Warningf("AutoScale - inPlace - disable inPlaceScale for job %s ", task.Job.SerialId)
		task.DisableInPlaceScale()
	}
	return SuccessRsp(params)
}

func (this *inPlaceScaleEndpointHandler) HandleTaskStatusRunning(requestId string, request *flow.TaskExecRequest, task *job_autoscale2.ScalingTask) (resp *flow.TaskExecResponse) {
	params := request.Params
	return SuccessRsp(params)
}
