package taskHandler

import (
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	constants2 "tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	clusterService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	flow2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/flow"
)

type DeleteHadoopYarnEndpointHandler struct {
}

func (h *DeleteHadoopYarnEndpointHandler) CompleteTask(req *flow.TaskExecRequest) (resp *flow.TaskExecResponse) {
	flowService := flow2.GetFlowService()
	requestId, _, _ := flowService.GetFlowParamString(req.Params, constants2.FLOW_PARAM_REQUEST_ID, "")
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf("[%s] DeleteEmrYarn Endpoint task failed,error: %v", requestId, err)
			resp = flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1, fmt.Sprintf("%v", err), req.Params)
		}
	}()
	err := h.handle(req)
	if err != nil {
		return flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1, fmt.Sprintf("%v", err), req.Params)
	}
	return flow.NewTaskExecResponseWithParams(flow.TASK_SUCCESS, 1.0, "ok", req.Params)
}

func (h *DeleteHadoopYarnEndpointHandler) handle(req *flow.TaskExecRequest) (err error) {
	flowService := flow2.GetFlowService()
	requestId, _, err := flowService.GetFlowParamString(req.Params, constants2.FLOW_PARAM_REQUEST_ID, constants2.EMPTY)
	if err != nil {
		return err
	}
	ClusterGroup, Cluster, err := flowService.GetClusterGroupAndCluster(req.Params)
	if ClusterGroup == nil {
		panic(errorcode.NewStackError(errorcode.InternalErrorCode, "DeleteHadoopYarnEndpointHandler ClusterGroup is null", err))
	}

	clusterGroupSerialId := ClusterGroup.SerialId
	clusterHadoopYarn, err := service2.GetClusterHadoopYarnBySerialId(clusterGroupSerialId)
	if err != nil {
		panic(errorcode.NewStackError(errorcode.InternalErrorCode, "GetClusterHadoopYarnBySerialId error", err))
	}
	if clusterHadoopYarn != nil {
		err := service2.SwitchClusterHadoopYarnStatusTo(clusterHadoopYarn.Id, constants2.ClusterHadoopYarnStopped)
		if err != nil {
			logger.Errorf("[%s] Failed to SwitchClusterHadoopYarnStatus, error: %+v", requestId, err)
			return err
		}
		err = service2.UpdateClusterHadoopYarnStopTime(clusterHadoopYarn.Id, util.GetCurrentTime())
		if err != nil {
			logger.Errorf("[%s] Failed to UpdateHadoopYarnStopTime,error: %+v", requestId, err)
			panic(errorcode.NewStackError(errorcode.InternalErrorCode, "", err))
		}
	}
	err = clusterService.RecordEKSResource(Cluster.Id, ClusterGroup.AppId, ClusterGroup.Region, ClusterGroup.OwnerUin)
	if err != nil {
		panic(errorcode.NewStackError(errorcode.InternalErrorCode, "RecordEKSResource failed", err))
	}

	return nil
}
