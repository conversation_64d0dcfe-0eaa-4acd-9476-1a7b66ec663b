package taskHandler

import (
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	v1 "k8s.io/api/apps/v1"
	v13 "k8s.io/api/core/v1"
	networkingV1 "k8s.io/api/networking/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"tencentcloud.com/tstream_galileo/src/common/dao"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	log "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"tencentcloud.com/tstream_galileo/src/galileo_cc/service"
	logger "tencentcloud.com/tstream_galileo/src/main/credentials_provider/common/log"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	service3 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	clusterService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	service5 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/helm"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/job"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	service4 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/table"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type isolateClusterService struct {
}

func (s *isolateClusterService) Apps(ctx *deploy.Context) ([]apps.App, error) {
	result := make([]apps.App, 0)
	cg := ctx.ClusterGroup

	isCluster, exists := ctx.GetParam(constants.FLOW_PARAM_SETATS_IS_CLUSTER, "1")
	if exists {
		if isCluster == "0" {
			return result, nil
		}
	}

	jobNamespace := "default"

	if cg.Type == constants.CLUSTER_GROUP_TYPE_UNIFORM {

	}

	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId == "" {

	}

	if cg.Type == constants.CLUSTER_GROUP_TYPE_PRIVATE && cg.AgentSerialId != "" {
		jobNamespace = ctx.ClusterGroup.SerialId
	}

	result = append(result, &stopJobsApp{ctx: ctx, stopType: constants.JOB_STOP_TYPE_PAUSE, namespace: jobNamespace})
	result = append(result, &stopSessionApp{ctx: ctx})
	result = append(result, &stopSqlGatewayApp{ctx: ctx})
	result = append(result, &stopHadoopYarnApp{ctx: ctx})

	return result, nil
}

type stopJobsApp struct {
	apps.ApplyApp
	ctx       *deploy.Context
	stopType  int8
	namespace string
}

func (c *stopJobsApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	params := c.ctx.Request.Params
	key := fmt.Sprintf("%s-%d", constants.FLOW_PARAM_ISOLATE_JOB_IDS, c.stopType)
	timeKey := fmt.Sprintf("%s-%d", constants.FLOW_PARAM_ISOLATE_JOB_TIME, c.stopType)
	idsStr, exist, err := c.ctx.GetFlowParamString(params, key, "")
	if err != nil {
		return
	}
	if !exist {
		ids, err := stopClusterJobs(c.ctx, c.stopType)
		if err != nil {
			return nil, err
		}

		b, err := json.Marshal(ids)
		if err != nil {
			return nil, err
		}
		params[key] = string(b)
		params[timeKey] = util.GetCurrentTime()
		return nil, errors.New("stop cluster jobs")
	}
	if timeOut, err := checkTimeout(params[timeKey]); err != nil {
		return nil, err
	} else if timeOut {
		log.Infof("stop job timeout, force stop")
		return nil, c.forceStopClusterJobs()
	}

	ids := make([]int64, 0)
	if err = json.Unmarshal([]byte(idsStr), &ids); err != nil {
		return
	}

	if done, err := checkClusterAllCommandDone(ids); err != nil {
		return nil, err
	} else if !done {
		return nil, errors.New("wait job stopped")
	}
	return nil, c.forceStopClusterJobs()
}

func checkTimeout(start string) (bool, error) {
	loc, _ := time.LoadLocation("Local")
	startTime, err := time.ParseInLocation("2006-01-02 15:04:05", start, loc)
	if err != nil {
		return false, err
	}
	elapsedDuration := time.Now().Sub(startTime)
	return elapsedDuration > time.Duration(20)*time.Minute, nil
}

type stopHadoopYarnApp struct {
	apps.ApplyApp
	ctx *deploy.Context
}

func (c *stopHadoopYarnApp) Apply(s apps.Client, _ interface{}) (_ interface{}, err error) {
	err = c.StopHadoopYarnCluster(s)
	return
}

func (c *stopHadoopYarnApp) StopHadoopYarnCluster(s apps.Client) (err error) {
	cg := c.ctx.ClusterGroup
	k8sService := c.ctx.K8sService()
	clientSet := c.ctx.ClientSet()

	sgs, err := service3.GetHadoopYarnsByGroupSerialId(cg.SerialId)
	if err != nil {
		return err
	}
	helService := service5.GetHelmService()
	namespace := helService.HadoopNamespace(c.ctx.ClusterGroup)
	name := helService.HadoopYarnName(c.ctx.ClusterGroup)
	//
	for i := 0; i < len(sgs); i++ {
		sg := sgs[i]
		if sg.Status == constants.ClusterHadoopYarnRunning || sg.Status == constants.ClusterHadoopYarnCreating ||
			sg.Status == constants.ClusterHadoopYarnStopping {

			// delete hadoop Yarn helm
			client, err := helService.NewClient(s.KubeConfig(), namespace)
			if err != nil {
				return err
			}
			res, err := helService.ListByName(client, namespace, name)
			if err != nil {
				return err
			}
			if len(res.Releases) == 0 {
				return nil
			}

			err = helService.DeleteByName(client, name)
			if err != nil {
				return err
			}

			opts := metav1.ListOptions{
				LabelSelector: fmt.Sprintf("app=%s,component=%s", constants.HADOOP_APP_NAME, constants.NODE_MANAGER_COMPONENT_NAME),
			}

			pvcList, err := k8sService.ListPVC(clientSet, namespace, opts)

			if err != nil {
				return errorcode.InternalErrorCode.NewWithErr(err)
			}
			for _, pvc := range pvcList.Items {
				_, err = k8sService.DeletePVC(clientSet, namespace, pvc.Name)
			}

			// 删除 nginx-reverse
			// 1. 删除nginx-reverse-proxy
			nginxReverseName := constants.ComponentNginxReverse
			// TODO: 调整default ns
			_, err = k8sService.DeleteStatefulSet(clientSet, &v1.StatefulSet{
				ObjectMeta: metav1.ObjectMeta{Namespace: namespace, Name: nginxReverseName},
			})
			if err != nil {
				logger.Errorf("Failed to delete StatefulSet: %s, err %+v", nginxReverseName, err)
				return err
			}
			// 2. nginx-reverse-proxy service
			nginxReverseServiceName := constants.NginxReverseServiceName
			_, err = k8sService.DeleteService(clientSet, &v13.Service{
				ObjectMeta: metav1.ObjectMeta{Namespace: namespace, Name: nginxReverseServiceName},
			})
			if err != nil {
				logger.Errorf("Failed to delete service: %s, err %+v", nginxReverseServiceName, err)
				return err
			}
			// 3. delete  configmap default.conf
			nginxReverseConfigMap := constants.NginxReverseDefaultConfigmapName
			_, err = k8sService.DeleteConfigMap(clientSet, &v13.ConfigMap{
				ObjectMeta: metav1.ObjectMeta{Namespace: namespace, Name: nginxReverseConfigMap},
			})
			if err != nil {
				logger.Errorf("Failed to delete configmap: %s, err %+v", nginxReverseConfigMap, err)
				return err
			}

			// 4. delete  configmap nginx.conf
			nginxReverseNginxConfig := constants.NginxReverseConfigName
			_, err = k8sService.DeleteConfigMap(clientSet, &v13.ConfigMap{
				ObjectMeta: metav1.ObjectMeta{Namespace: namespace, Name: nginxReverseNginxConfig},
			})
			if err != nil {
				logger.Errorf("Failed to delete configmap: %s, err %+v", nginxReverseNginxConfig, err)
				return err
			}

		}

		// 更新对应HadoopYarn集群的状态
		err := clusterService.SwitchHadoopYarnStatus(sg.ClusterGroupSerialId, constants.ClusterHadoopYarnStopped)
		if err != nil {
			log.Errorf("StopHadoopYarnCluster Failed to SwitchHadoopYarnStatus because %+v", err)
			return err
		}

		log.Infof("[%s] Successfully switched cluster hadoopYarn %s to Stopped status, cluster stop completed.",
			c.ctx.RequestId, sg.ClusterGroupSerialId)
	}
	return nil
}

type stopSqlGatewayApp struct {
	apps.ApplyApp
	ctx *deploy.Context
}

func (c *stopSqlGatewayApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	err = c.StopSqlGatewayCluster()
	return
}

func (c *stopSqlGatewayApp) StopSqlGatewayCluster() (err error) {

	cg := c.ctx.ClusterGroup

	k8sService := c.ctx.K8sService()
	clientSet := c.ctx.ClientSet()

	sgs, err := service3.GetSqlGatewaysByGroupSerialId(cg.SerialId)
	if err != nil {
		return err
	}
	// 后续可能会有多个Session集群，每个Flink版本可以启动一个
	namespace := clusterService.GetDefaultNamespace(cg)
	for i := 0; i < len(sgs); i++ {
		sg := sgs[i]

		if sg.Status == constants.SqlGatewayRunning || sg.Status == constants.SqlGatewayCreating || sg.Status == constants.SqlGatewayStopping {
			flinkVersionFormatted := service3.GetNoDotLowerFlinkVersion(sg.FlinkVersion)
			// 1. delete sql gateway deployment
			jmDeploymentName := fmt.Sprintf(SqlGatewayDeploymentNameTemplate, flinkVersionFormatted)
			// TODO: 调整default ns
			_, err = k8sService.DeleteDeployment(clientSet, &v1.Deployment{
				ObjectMeta: metav1.ObjectMeta{Namespace: namespace, Name: jmDeploymentName},
			})
			if err != nil {
				logger.Errorf("stopSqlGatewayApp Failed to delete deployment, err %+v", err)
				return err
			}
			// 2. delete sql gateway  service
			jmServiceName := fmt.Sprintf(SqlGatewayServiceNameTemplate, flinkVersionFormatted)
			_, err = k8sService.DeleteService(clientSet, &v13.Service{
				ObjectMeta: metav1.ObjectMeta{Namespace: namespace, Name: jmServiceName},
			})
			if err != nil {
				logger.Errorf("stopSqlGatewayApp Failed to delete service, err %+v", err)
				return err
			}
			// 3. delete flink-conf configmap
			flinkConfigMap := fmt.Sprintf(SqlGatewayFlinkConfigTemplate, flinkVersionFormatted)
			_, err = k8sService.DeleteConfigMap(clientSet, &v13.ConfigMap{
				ObjectMeta: metav1.ObjectMeta{Namespace: namespace, Name: flinkConfigMap},
			})
			if err != nil {
				logger.Errorf("stopSqlGatewayApp Failed to delete flink-conf configmap, err %+v", err)
				return err
			}
			// 4. delete hadoop-conf configmap
			hadoopConfigMap := fmt.Sprintf(SqlGatewayHadoopConfigTemplate, flinkVersionFormatted)
			_, err = k8sService.DeleteConfigMap(clientSet, &v13.ConfigMap{
				ObjectMeta: metav1.ObjectMeta{Namespace: namespace, Name: hadoopConfigMap},
			})
			if err != nil {
				logger.Errorf("stopSqlGatewayApp Failed to delete hadoop-conf configmap err %+v", err)
				return err
			}
		}

		// 更新对应Session集群的状态
		err := clusterService.SwitchSqlGatewayStatus(sg.SerialId, constants.SqlGatewayStopped)
		if err != nil {
			log.Errorf("stopSqlGatewayApp Failed to SwitchClusterSessionStatus because %+v", err)
			return err
		}

		log.Infof("[%s] Successfully switched cluster SqlGateway %s to Stopped status, cluster stop completed.",
			c.ctx.RequestId, sg.SerialId)
	}
	return nil
}

type stopSessionApp struct {
	apps.ApplyApp
	ctx *deploy.Context
}

func (c *stopSessionApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	err = c.StopSessionCluster()
	return
}

func (c *stopJobsApp) forceStopClusterJobs() (err error) {
	clusterGroup := c.ctx.ClusterGroup
	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {

		sql, args := dao.NewUpdateBuilder("UPDATE Job").
			Set("Status", constants.JOB_STATUS_STOPPED).
			Set("ExpectedStatus", constants.JOB_STATUS_STOPPED).
			Set("LastOpResult", "isolate force stop").
			WhereEq("ClusterGroupId", clusterGroup.Id).
			WhereIn("Status", []int{constants.JOB_STATUS_PROGRESS, constants.JOB_STATUS_RUNNING}).
			Build()

		service4.TExecute(tx, sql, args)

		sql, args = dao.NewUpdateBuilder("UPDATE Command").
			Set("Status", constants.CLUSTER_MASTER_COMMAND_FINISH).
			WhereEq("ClusterGroupId", clusterGroup.Id).
			Build()
		service4.TExecute(tx, sql, args)

		sql = "update JobInstance ji join Job j on ji.JobId = j.Id set ji.Status = ? where j.ClusterGroupId = ?"
		tx.ExecuteSqlWithArgs(sql, constants.JOB_INSTANCE_STATUS_HISTORY, clusterGroup.Id)

		return nil
	}).Close()

	k8sService := k8s.GetK8sService()
	dList, err := k8sService.ListDeployment(c.ctx.ClientSet(), c.namespace, metav1.ListOptions{})
	for _, d := range dList.Items {
		if !strings.HasPrefix(d.Name, "cql-") {
			continue
		}
		log.Infof("force stop job: %s", d.Name)
		if _, err = k8sService.DeleteDeployment(c.ctx.ClientSet(), &d); err != nil {
			return err
		}
	}
	return
}

func stopClusterJobs(ctx *deploy.Context, stopType int8) (ids []int64, err error) {

	ids = make([]int64, 0)

	requestId := ctx.RequestId
	clusterGroup := ctx.ClusterGroup
	service.GetTxManager().GetTransactionTemplate().DoTransaction(func(tx *dao.Transaction) error {
		jobSet, err := service2.GetJobByClusterGroupId(clusterGroup.Id, constants.JOB_STATUS_RUNNING)
		if err != nil {
			return err
		}

		for _, job := range jobSet {
			jobInstance, err := service2.GetRunningJobInstanceByJobId(job.Id)
			if err != nil {
				log.Errorf("%s: Failed to getRunningJobInstanceByJobId for job id %d because %+v", requestId, job.SerialId, err)
				return err
			}

			command, err := service2.BuildStopCommandForClusterMaster(job, stopType, requestId, jobInstance.RunningOrderId)
			if err != nil {
				return err
			}
			if stopType == constants.JOB_STOP_TYPE_PAUSE {
				_ = service2.UpdateSavepointBeforeCancelWithSavepoint(tx, job, "")
			}

			sql := "UPDATE Job SET Status=?, ProgressDesc=?, LastOpResult=? WHERE Id = ?"
			result := tx.ExecuteSqlWithArgs(sql, constants.JOB_STATUS_PROGRESS, constants.STOP_JOB_PROGRESS_DESC, constants.OK_OP_RESULT_FOR_JOB, job.Id)
			rowsAffected, err := result.RowsAffected()
			if err != nil {
				err = errorcode.NewStackError(errorcode.InternalErrorCode, "update job status err", err)
				return err
			}
			if rowsAffected != 1 {
				msg := fmt.Sprintf("%s: Failed to Update Job[%s] to `Delete` Status, rowsAffected != 1, actual: %d",
					requestId, job.SerialId, rowsAffected)
				err = errorcode.NewStackError(errorcode.InternalErrorCode, msg, nil)
				return err
			}
			log.Infof("gen command for job %s %d type %d", job.SerialId, job.Id, stopType)
			id := tx.SaveObject(command, "Command")
			ids = append(ids, id)
		}
		return nil
	}).Close()

	return ids, nil
}

func checkClusterAllCommandDone(ids []int64) (allDone bool, err error) {

	if len(ids) < 1 {
		return true, nil
	}
	sql := "SELECT COUNT(*) AS Count FROM Command"

	cond := dao.NewCondition()
	cond.Eq("Status", constants.CLUSTER_MASTER_COMMAND_FINISH)
	cond.In("Id", ids)

	where, args := cond.GetWhere()
	sql += where
	_, data, err := service.GetTxManager().GetQueryTemplate().DoQuery(sql, args)
	if err != nil {
		return false, err
	}

	v, _ := data[0]["Count"]
	count, err := strconv.Atoi(string(v))
	if err != nil {
		return false, err
	}

	if count != len(ids) {
		return false, fmt.Errorf("command not done: cnt %d", count)
	}

	return true, nil
}

func (c *stopSessionApp) StopSessionCluster() (err error) {

	cg := c.ctx.ClusterGroup

	k8sService := c.ctx.K8sService()
	clientset := c.ctx.ClientSet()

	ss, err := service3.GetClusterSessionsBySerialId(cg.SerialId)
	if err != nil {
		return err
	}
	// 后续可能会有多个Session集群，每个Flink版本可以启动一个
	namespace := clusterService.GetDefaultNamespace(cg)
	for i := 0; i < len(ss); i++ {
		cs := ss[i]

		if cs.Status == constants.ClusterSessionRunning || cs.Status == constants.ClusterSessionCreating {
			fv := service3.GetNoDotLowerFlinkVersion(cs.FlinkVersion)

			// 1.1 删除TM
			tmDeploymentName := fmt.Sprintf(constants.ClusterSessionTMTemplate, cg.SerialId, fv)
			_, err = k8s.GetK8sService().DeleteDeployment(clientset, &v1.Deployment{
				ObjectMeta: metav1.ObjectMeta{Namespace: namespace, Name: tmDeploymentName}})
			if err != nil {
				log.Errorf("Failed to DeleteDeployment because %+v", err)
				return err
			}

			// 1.2 删除JM
			jmDeploymentName := fmt.Sprintf(constants.ClusterSessionJMTemplate, cg.SerialId, fv)
			_, err = k8sService.DeleteDeployment(clientset, &v1.Deployment{
				ObjectMeta: metav1.ObjectMeta{Namespace: namespace, Name: jmDeploymentName},
			})
			if err != nil {
				log.Errorf("Failed to DeleteDeployment because %+v", err)
				return err
			}

			// 2. 删除Jm的service和其对应的Ingress规则
			// 2.1 删除JM对应的svc
			jmServiceName := fmt.Sprintf(constants.ClusterSessionAPPTemplate, cg.SerialId, fv) + "-rest"
			_, err = k8sService.DeleteService(clientset, &v13.Service{
				ObjectMeta: metav1.ObjectMeta{Namespace: namespace, Name: jmServiceName},
			})
			if err != nil {
				log.Errorf("Failed to DeleteService because %+v", err)
				return err
			}
			// 2.2 删除svc对应的Ingress
			appName := fmt.Sprintf(constants.ClusterSessionAPPTemplate, cg.SerialId, fv)
			err = k8s.GetK8sService().IngressService.DeleteIngressRule(clientset, &networkingV1.Ingress{
				ObjectMeta: metav1.ObjectMeta{Namespace: namespace, Name: "oceanus-flink-webui"},
			}, appName+"-rest", 8081, c.ctx.Cluster.Id)
			if err != nil {
				log.Errorf("DeleteIngressRule err, IngressName: oceanus-flink-webui, Path: %s", appName)
				panic(errorcode.NewStackError(errorcode.InternalErrorCode, "", err))
			}

			// 3. 删除flink configmap和hadoop configmap
			// 3.1 删除flink configmap
			flinkConfigMap := fmt.Sprintf(constants.ClusterSessionFlinkConfigTemplate, cg.SerialId, fv)
			_, err = k8sService.DeleteConfigMap(clientset, &v13.ConfigMap{
				ObjectMeta: metav1.ObjectMeta{Namespace: namespace, Name: flinkConfigMap},
			})
			if err != nil {
				log.Errorf("Failed to DeleteFlinkConfigMap because %+v", err)
				return err
			}

			// 3.2 删除hadoop configmap
			hadoopConfigMap := fmt.Sprintf(constants.ClusterSessionHadoopConfigTemplate, cg.SerialId, fv)
			_, err = k8sService.DeleteConfigMap(clientset, &v13.ConfigMap{
				ObjectMeta: metav1.ObjectMeta{Namespace: namespace, Name: hadoopConfigMap},
			})
			if err != nil {
				log.Errorf("Failed to DeleteHadoopConfigMap because %+v", err)
				return err
			}
		}

		// 更新对应Session集群的状态
		err = service3.SwitchClusterSessionStatusTo(cs.Id, constants.ClusterSessionStopped)
		if err != nil {
			log.Errorf("Failed to SwitchClusterSessionStatus because %+v", err)
			return err
		}

		log.Infof("[%s] Successfully switched cluster session %s to Stopped status, cluster stop completed.",
			c.ctx.RequestId, cs.ClusterGroupSerialId)
	}
	return nil
}
