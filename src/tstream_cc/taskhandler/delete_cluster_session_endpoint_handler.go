package taskHandler

import (
	"fmt"

	networkingV1 "k8s.io/api/networking/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/util"
	constants2 "tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	clusterService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/config/configure_center"
	flow2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/flow"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/tke"
)

type deleteClusterSessionEndpointHandler struct {
}

func (d *deleteClusterSessionEndpointHandler) CompleteTask(request *flow.TaskExecRequest) (resp *flow.TaskExecResponse) {
	flowService := flow2.GetFlowService()
	params := request.Params
	requestId, _, _ := flowService.GetFlowParamString(params, constants2.FLOW_PARAM_REQUEST_ID, constants2.EMPTY)

	defer func() {
		if err := recover(); err != nil {
			logger.Errorf("[%s] CreateClusterEndpoint task failed because: %v", requestId, err)
			resp = flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1, fmt.Sprintf("%v", err), params)
		}
	}()

	Region, _, _ := flowService.GetFlowParamString(params, constants2.FLOW_PARAM_REGION, "ap-guangzhou")
	flinkVersion, _, _ := flowService.GetFlowParamString(params, constants2.FLOW_PARAM_FLINK_VERSION, configure_center.CC(Region).DefaultFlinkVersion())
	serialId, _, _ := flowService.GetFlowParamString(params, constants2.FLOW_PARAM_CLUSTER_GROUP_SERIALID, constants2.EMPTY)

	ClusterGroup, Cluster, err := flowService.GetClusterGroupAndCluster(request.Params)
	if err != nil {
		panic(errorcode.NewStackError(errorcode.InternalErrorCode, "", err))
	}
	// 删除ingress
	noDotLowerFlinkVersion := service.GetNoDotLowerFlinkVersion(flinkVersion)
	appName := fmt.Sprintf(constants2.ClusterSessionAPPTemplate, serialId, noDotLowerFlinkVersion)
	clientset, err := tke.GetTkeService().KubernetesClientsetFromCluster(request.DocId, Cluster)
	if err != nil {
		panic(errorcode.NewStackError(errorcode.InternalErrorCode, "", err))
	}
	namespace := clusterService.GetDefaultNamespace(ClusterGroup)
	err = k8s.GetK8sService().IngressService.DeleteIngressRule(clientset, &networkingV1.Ingress{
		ObjectMeta: metav1.ObjectMeta{Namespace: namespace, Name: "oceanus-flink-webui"},
	}, appName+"-rest", 8081, Cluster.Id)
	if err != nil {
		logger.Errorf("DeleteIngressRule err, IngressName: oceanus-flink-webui, Path: %s", appName)
		panic(errorcode.NewStackError(errorcode.InternalErrorCode, "", err))
	}

	clusterSession, err := service.GetClusterSessionBySerialIdAndFlinkVersion(serialId, flinkVersion)
	if err != nil {
		panic(errorcode.NewStackError(errorcode.InternalErrorCode, "", err))
	}
	err = service.SwitchClusterSessionStatusTo(clusterSession.Id, constants2.ClusterSessionStopped)
	if err != nil {
		panic(errorcode.NewStackError(errorcode.InternalErrorCode, "", err))
	}

	err = service.UpdateClusterSessionStopTime(clusterSession.Id, util.GetCurrentTime())
	if err != nil {
		panic(errorcode.NewStackError(errorcode.InternalErrorCode, "", err))
	}

	err = clusterService.RecordEKSResource(Cluster.Id, ClusterGroup.AppId, ClusterGroup.Region, ClusterGroup.OwnerUin)
	if err != nil {
		panic(errorcode.NewStackError(errorcode.InternalErrorCode, "RecordEKSResource failed", err))
	}

	logger.Infof("[%s] Successfully switched cluster session %s (%d) to Stopped status, cluster stop completed.",
		requestId, serialId)

	return flow.NewTaskExecResponseWithParams(flow.TASK_SUCCESS, 1.0, "ok", request.Params)
}
