package taskHandler

import (
	"fmt"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	"tencentcloud.com/tstream_galileo/src/common/flow"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	constants2 "tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/model/billing"
	service2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
	clusterService "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	flow2 "tencentcloud.com/tstream_galileo/src/tstream_cc/service/flow"
)

type CreateHadoopYarnEndPointHandler struct {
}

func (this *CreateHadoopYarnEndPointHandler) CompleteTask(request *flow.TaskExecRequest) (resp *flow.TaskExecResponse) {
	flowService := flow2.GetFlowService()
	params := request.Params
	requestId, _, _ := flowService.GetFlowParamString(params, constants2.FLOW_PARAM_REQUEST_ID, constants2.EMPTY)

	defer func() {
		if err := recover(); err != nil {
			logger.Errorf("[%s] CreateHadoopYarnEndPoint task failed because: %v", requestId, err)
			resp = flowService.NewTaskExecResponseWithParamsAndStatus(flow.TASK_FAIL, flow.TASK_STATUS_FAILED, 0.1, fmt.Sprintf("%v", err), params)
		}
	}()
	ClusterGroup, Cluster, err := flowService.GetClusterGroupAndCluster(request.Params)

	clusterGroupSerialId := ClusterGroup.SerialId
	clusterHadoopYarn, err := service2.GetClusterHadoopYarnBySerialId(clusterGroupSerialId)
	if err != nil {
		panic(errorcode.NewStackError(errorcode.InternalErrorCode, "", err))
	}
	err = service2.SwitchClusterHadoopYarnStatusTo(clusterHadoopYarn.Id, constants2.ClusterHadoopYarnRunning)
	if err != nil {
		panic(errorcode.NewStackError(errorcode.InternalErrorCode, "", err))
	}
	// 更新 StartTime，计费用
	err = service2.UpdateClusterHadoopYarnStartTime(clusterHadoopYarn.Id)
	if err != nil {
		panic(errorcode.NewStackError(errorcode.InternalErrorCode, "", err))
	}
	// 清空 StopTime，因为Hadoop yarn集群反复开启，是在同一条记录上反复操作的
	err = service2.UpdateClusterHadoopYarnStopTime(clusterHadoopYarn.Id, billing.NotIsolatedTimestamp)
	if err != nil {
		panic(errorcode.NewStackError(errorcode.InternalErrorCode, "", err))
	}

	err = clusterService.RecordEKSResource(Cluster.Id, ClusterGroup.AppId, ClusterGroup.Region, ClusterGroup.OwnerUin)
	if err != nil {
		panic(errorcode.NewStackError(errorcode.InternalErrorCode, "RecordEKSResource failed", err))
	}

	logger.Infof("[%s] Successfully switched cluster Hadoop %s (%d) to RUNNING status, cluster creation completed.",
		requestId, clusterGroupSerialId)

	return flow.NewTaskExecResponseWithParams(flow.TASK_SUCCESS, 1.0, "ok", request.Params)
}
