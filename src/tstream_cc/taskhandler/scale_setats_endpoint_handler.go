package taskHandler

import (
	"errors"
	"fmt"
	"strconv"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/uuid"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"
	service "tencentcloud.com/tstream_galileo/src/tstream_cc/service/cluster"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/service/k8s/apps"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/taskhandler/deploy"
)

type ScaleSetatsEndpointHandler struct {
}

func (c ScaleSetatsEndpointHandler) Apps(ctx *deploy.Context) ([]apps.App, error) {
	requestId := fmt.Sprintf("ScaleSetatsEndpointHandler-%s-%s", uuid.NewRandom().String(), ctx.ClusterGroup.SerialId)
	logger.Infof("ScaleSetatsEndpointHandler Apps, requestId:%s", requestId)
	result := make([]apps.App, 0)
	result = append(result, &switchSetatsStatusApp{ctx: ctx, status: constants.SETATS_RUNNING})
	return result, nil
}

type modifySetatasInfoApp struct {
	apps.ApplyApp
	ctx       *deploy.Context
	requestId string
}

func (c *modifySetatasInfoApp) Apply(_ apps.Client, _ interface{}) (_ interface{}, err error) {
	count, _setats, err := service.GetSetatsByClusterGroupSerialId(c.ctx.ClusterGroup.SerialId)
	if err != nil {
		msg := fmt.Sprintf("%s Failed to get setats by ClusterGroupSerialId id:%s, with errors:%+v", c.requestId, c.ctx.ClusterGroup.SerialId, err)
		logger.Errorf(msg)
		return nil, err
	}

	if count == 0 {
		msg := fmt.Sprintf("%s Failed to get setats by ClusterGroupSerialId id:%s, with count 0", c.requestId, c.ctx.ClusterGroup.SerialId)
		logger.Errorf(msg)
		return nil, errors.New(msg)
	}

	strMasterDiskSize, _ := c.ctx.GetParam(constants.FLOW_PARAM_SETATS_MASTER_DISK_SIZE, "0")
	targetMasterDiskSize, _ := strconv.Atoi(strMasterDiskSize)

	strWorkerDiskSize, _ := c.ctx.GetParam(constants.FLOW_PARAM_SETATS_WORKER_DISK_SIZE, "0")
	targetWorkerDiskSize, _ := strconv.Atoi(strWorkerDiskSize)

	if targetMasterDiskSize == 0 {
		targetMasterDiskSize = _setats.MasterDiskSize
	}
	if targetWorkerDiskSize == 0 {
		targetWorkerDiskSize = _setats.WorkerDiskSize
	}

	strWorkerParallelism, _ := c.ctx.GetParam(constants.FLOW_PARAM_SETATS_WORKERDEFAULTPARALLELISM, "0")
	workerParallelism, _ := strconv.Atoi(strWorkerParallelism)
	if workerParallelism > _setats.WorkerDefaultParallelism {
		_setats.WorkerDefaultParallelism = workerParallelism
	}

	logger.Infof("ScaleSetatsEndpointHandler modifySetatasInfoApp, requestId:%s, targetMasterDiskSize:%d, targetWorkerDiskSize:%d", c.requestId, targetMasterDiskSize, targetWorkerDiskSize)
	err = service.ModifySetatasInfo(c.ctx.ClusterGroup.SerialId, _setats.WorkerDefaultParallelism, targetWorkerDiskSize, targetMasterDiskSize)
	if err != nil {
		msg := fmt.Sprintf("%s Failed to modify setats info, with errors:%+v", c.requestId, err)
		logger.Errorf(msg)
		return nil, err
	}
	return nil, nil
}
