// Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package v20190422

import (
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common"
	tchttp "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/http"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/profile"
)

const APIVersion = "2019-04-22"

type Client struct {
	common.Client
}

// Deprecated
func NewClientWithSecretId(secretId, secretKey, region string) (client *Client, err error) {
	cpf := profile.NewClientProfile()
	client = &Client{}
	client.Init(region).WithSecretId(secretId, secretKey).WithProfile(cpf)
	return
}

func NewClient(credential *common.Credential, region string, clientProfile *profile.ClientProfile) (client *Client, err error) {
	client = &Client{}
	client.Init(region).
		WithCredential(credential).
		WithProfile(clientProfile)
	return
}

func NewDescribeRegionClusterCountRequest() (request *DescribeRegionClusterCountRequest) {
	request = &DescribeRegionClusterCountRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("oceanus", APIVersion, "DescribeRegionClusterCount")

	return
}

func NewDescribeRegionClusterCountResponse() (response *DescribeRegionClusterCountResponse) {
	response = &DescribeRegionClusterCountResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// DescribeRegionClusterCount
// 描述用户所在地域集群数量
func (c *Client) DescribeRegionClusterCount(request *DescribeRegionClusterCountRequest) (response *DescribeRegionClusterCountResponse, err error) {
	if request == nil {
		request = NewDescribeRegionClusterCountRequest()
	}
	response = NewDescribeRegionClusterCountResponse()
	err = c.Send(request, response)
	return
}
