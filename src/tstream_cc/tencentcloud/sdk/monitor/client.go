// Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package v20180724

import (
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common"
	tchttp "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/http"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/profile"
)

const APIVersion = "2018-07-24"

type Client struct {
	common.Client
}

// Deprecated
func NewClientWithSecretId(secretId, secretKey, region string) (client *Client, err error) {
	cpf := profile.NewClientProfile()
	client = &Client{}
	client.Init(region).WithSecretId(secretId, secretKey).WithProfile(cpf)
	return
}

func NewClient(credential *common.Credential, region string, clientProfile *profile.ClientProfile) (client *Client, err error) {
	client = &Client{}
	client.Init(region).
		WithCredential(credential).
		WithProfile(clientProfile)
	return
}

func NewAddMonitorAlarmConfirmRequest() (request *AddMonitorAlarmConfirmRequest) {
	request = &AddMonitorAlarmConfirmRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "AddMonitorAlarmConfirm")
	return
}

func NewAddMonitorAlarmConfirmResponse() (response *AddMonitorAlarmConfirmResponse) {
	response = &AddMonitorAlarmConfirmResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 添加告警确认
func (c *Client) AddMonitorAlarmConfirm(request *AddMonitorAlarmConfirmRequest) (response *AddMonitorAlarmConfirmResponse, err error) {
	if request == nil {
		request = NewAddMonitorAlarmConfirmRequest()
	}
	response = NewAddMonitorAlarmConfirmResponse()
	err = c.Send(request, response)
	return
}

func NewAddMonitorAlarmStatementRequest() (request *AddMonitorAlarmStatementRequest) {
	request = &AddMonitorAlarmStatementRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "AddMonitorAlarmStatement")
	return
}

func NewAddMonitorAlarmStatementResponse() (response *AddMonitorAlarmStatementResponse) {
	response = &AddMonitorAlarmStatementResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 添加告警评论
func (c *Client) AddMonitorAlarmStatement(request *AddMonitorAlarmStatementRequest) (response *AddMonitorAlarmStatementResponse, err error) {
	if request == nil {
		request = NewAddMonitorAlarmStatementRequest()
	}
	response = NewAddMonitorAlarmStatementResponse()
	err = c.Send(request, response)
	return
}

func NewAmpFrontTunnelRequest() (request *AmpFrontTunnelRequest) {
	request = &AmpFrontTunnelRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "AmpFrontTunnel")
	return
}

func NewAmpFrontTunnelResponse() (response *AmpFrontTunnelResponse) {
	response = &AmpFrontTunnelResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// Amp系统前端统一接入API
func (c *Client) AmpFrontTunnel(request *AmpFrontTunnelRequest) (response *AmpFrontTunnelResponse, err error) {
	if request == nil {
		request = NewAmpFrontTunnelRequest()
	}
	response = NewAmpFrontTunnelResponse()
	err = c.Send(request, response)
	return
}

func NewArgusFrontTunnelRequest() (request *ArgusFrontTunnelRequest) {
	request = &ArgusFrontTunnelRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "ArgusFrontTunnel")
	return
}

func NewArgusFrontTunnelResponse() (response *ArgusFrontTunnelResponse) {
	response = &ArgusFrontTunnelResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// Argus系统前端统一接入API
func (c *Client) ArgusFrontTunnel(request *ArgusFrontTunnelRequest) (response *ArgusFrontTunnelResponse, err error) {
	if request == nil {
		request = NewArgusFrontTunnelRequest()
	}
	response = NewArgusFrontTunnelResponse()
	err = c.Send(request, response)
	return
}

func NewArgusReportRequest() (request *ArgusReportRequest) {
	request = &ArgusReportRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "ArgusReport")
	return
}

func NewArgusReportResponse() (response *ArgusReportResponse) {
	response = &ArgusReportResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// argus数据Http上报接口
func (c *Client) ArgusReport(request *ArgusReportRequest) (response *ArgusReportResponse, err error) {
	if request == nil {
		request = NewArgusReportRequest()
	}
	response = NewArgusReportResponse()
	err = c.Send(request, response)
	return
}

func NewBindingPolicyObjectRequest() (request *BindingPolicyObjectRequest) {
	request = &BindingPolicyObjectRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "BindingPolicyObject")
	return
}

func NewBindingPolicyObjectResponse() (response *BindingPolicyObjectResponse) {
	response = &BindingPolicyObjectResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 将告警策略绑定到特定对象
func (c *Client) BindingPolicyObject(request *BindingPolicyObjectRequest) (response *BindingPolicyObjectResponse, err error) {
	if request == nil {
		request = NewBindingPolicyObjectRequest()
	}
	response = NewBindingPolicyObjectResponse()
	err = c.Send(request, response)
	return
}

func NewCLMDescribeMetricSetsRequest() (request *CLMDescribeMetricSetsRequest) {
	request = &CLMDescribeMetricSetsRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "CLMDescribeMetricSets")
	return
}

func NewCLMDescribeMetricSetsResponse() (response *CLMDescribeMetricSetsResponse) {
	response = &CLMDescribeMetricSetsResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 拉取指标集列表
func (c *Client) CLMDescribeMetricSets(request *CLMDescribeMetricSetsRequest) (response *CLMDescribeMetricSetsResponse, err error) {
	if request == nil {
		request = NewCLMDescribeMetricSetsRequest()
	}
	response = NewCLMDescribeMetricSetsResponse()
	err = c.Send(request, response)
	return
}

func NewCancelStarMonitorDashboardRequest() (request *CancelStarMonitorDashboardRequest) {
	request = &CancelStarMonitorDashboardRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "CancelStarMonitorDashboard")
	return
}

func NewCancelStarMonitorDashboardResponse() (response *CancelStarMonitorDashboardResponse) {
	response = &CancelStarMonitorDashboardResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 取消收藏dashboard
func (c *Client) CancelStarMonitorDashboard(request *CancelStarMonitorDashboardRequest) (response *CancelStarMonitorDashboardResponse, err error) {
	if request == nil {
		request = NewCancelStarMonitorDashboardRequest()
	}
	response = NewCancelStarMonitorDashboardResponse()
	err = c.Send(request, response)
	return
}

func NewClonePolicyGroupRequest() (request *ClonePolicyGroupRequest) {
	request = &ClonePolicyGroupRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "ClonePolicyGroup")
	return
}

func NewClonePolicyGroupResponse() (response *ClonePolicyGroupResponse) {
	response = &ClonePolicyGroupResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 复制基础告警策略组
func (c *Client) ClonePolicyGroup(request *ClonePolicyGroupRequest) (response *ClonePolicyGroupResponse, err error) {
	if request == nil {
		request = NewClonePolicyGroupRequest()
	}
	response = NewClonePolicyGroupResponse()
	err = c.Send(request, response)
	return
}

func NewCopyConditionsTemplateRequest() (request *CopyConditionsTemplateRequest) {
	request = &CopyConditionsTemplateRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "CopyConditionsTemplate")
	return
}

func NewCopyConditionsTemplateResponse() (response *CopyConditionsTemplateResponse) {
	response = &CopyConditionsTemplateResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 复制触发条件模板
func (c *Client) CopyConditionsTemplate(request *CopyConditionsTemplateRequest) (response *CopyConditionsTemplateResponse, err error) {
	if request == nil {
		request = NewCopyConditionsTemplateRequest()
	}
	response = NewCopyConditionsTemplateResponse()
	err = c.Send(request, response)
	return
}

func NewCopyInstanceGroupRequest() (request *CopyInstanceGroupRequest) {
	request = &CopyInstanceGroupRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "CopyInstanceGroup")
	return
}

func NewCopyInstanceGroupResponse() (response *CopyInstanceGroupResponse) {
	response = &CopyInstanceGroupResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 复制实例分组
func (c *Client) CopyInstanceGroup(request *CopyInstanceGroupRequest) (response *CopyInstanceGroupResponse, err error) {
	if request == nil {
		request = NewCopyInstanceGroupRequest()
	}
	response = NewCopyInstanceGroupResponse()
	err = c.Send(request, response)
	return
}

func NewCreateAlertPolicyRequest() (request *CreateAlertPolicyRequest) {
	request = &CreateAlertPolicyRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "CreateAlertPolicy")
	return
}

func NewCreateAlertPolicyResponse() (response *CreateAlertPolicyResponse) {
	response = &CreateAlertPolicyResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 新增告警策略返回对应ID
func (c *Client) CreateAlertPolicy(request *CreateAlertPolicyRequest) (response *CreateAlertPolicyResponse, err error) {
	if request == nil {
		request = NewCreateAlertPolicyRequest()
	}
	response = NewCreateAlertPolicyResponse()
	err = c.Send(request, response)
	return
}

func NewCreateAttributesRequest() (request *CreateAttributesRequest) {
	request = &CreateAttributesRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "CreateAttributes")
	return
}

func NewCreateAttributesResponse() (response *CreateAttributesResponse) {
	response = &CreateAttributesResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 批量创建指标
func (c *Client) CreateAttributes(request *CreateAttributesRequest) (response *CreateAttributesResponse, err error) {
	if request == nil {
		request = NewCreateAttributesRequest()
	}
	response = NewCreateAttributesResponse()
	err = c.Send(request, response)
	return
}

func NewCreateCCMChartRequest() (request *CreateCCMChartRequest) {
	request = &CreateCCMChartRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "CreateCCMChart")
	return
}

func NewCreateCCMChartResponse() (response *CreateCCMChartResponse) {
	response = &CreateCCMChartResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 创建分组视图自定义监控图表
func (c *Client) CreateCCMChart(request *CreateCCMChartRequest) (response *CreateCCMChartResponse, err error) {
	if request == nil {
		request = NewCreateCCMChartRequest()
	}
	response = NewCreateCCMChartResponse()
	err = c.Send(request, response)
	return
}

func NewCreateCCMDashboardRequest() (request *CreateCCMDashboardRequest) {
	request = &CreateCCMDashboardRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "CreateCCMDashboard")
	return
}

func NewCreateCCMDashboardResponse() (response *CreateCCMDashboardResponse) {
	response = &CreateCCMDashboardResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 创建自定义监控图表
func (c *Client) CreateCCMDashboard(request *CreateCCMDashboardRequest) (response *CreateCCMDashboardResponse, err error) {
	if request == nil {
		request = NewCreateCCMDashboardRequest()
	}
	response = NewCreateCCMDashboardResponse()
	err = c.Send(request, response)
	return
}

func NewCreateClmAlertPolicyRequest() (request *CreateClmAlertPolicyRequest) {
	request = &CreateClmAlertPolicyRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "CreateClmAlertPolicy")
	return
}

func NewCreateClmAlertPolicyResponse() (response *CreateClmAlertPolicyResponse) {
	response = &CreateClmAlertPolicyResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 创建告警策略
func (c *Client) CreateClmAlertPolicy(request *CreateClmAlertPolicyRequest) (response *CreateClmAlertPolicyResponse, err error) {
	if request == nil {
		request = NewCreateClmAlertPolicyRequest()
	}
	response = NewCreateClmAlertPolicyResponse()
	err = c.Send(request, response)
	return
}

func NewCreateClmMetricSetRequest() (request *CreateClmMetricSetRequest) {
	request = &CreateClmMetricSetRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "CreateClmMetricSet")
	return
}

func NewCreateClmMetricSetResponse() (response *CreateClmMetricSetResponse) {
	response = &CreateClmMetricSetResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 创建指标集
func (c *Client) CreateClmMetricSet(request *CreateClmMetricSetRequest) (response *CreateClmMetricSetResponse, err error) {
	if request == nil {
		request = NewCreateClmMetricSetRequest()
	}
	response = NewCreateClmMetricSetResponse()
	err = c.Send(request, response)
	return
}

func NewCreateConditionsTemplateRequest() (request *CreateConditionsTemplateRequest) {
	request = &CreateConditionsTemplateRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "CreateConditionsTemplate")
	return
}

func NewCreateConditionsTemplateResponse() (response *CreateConditionsTemplateResponse) {
	response = &CreateConditionsTemplateResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 创建告警条件模板
func (c *Client) CreateConditionsTemplate(request *CreateConditionsTemplateRequest) (response *CreateConditionsTemplateResponse, err error) {
	if request == nil {
		request = NewCreateConditionsTemplateRequest()
	}
	response = NewCreateConditionsTemplateResponse()
	err = c.Send(request, response)
	return
}

func NewCreateDashboardRequest() (request *CreateDashboardRequest) {
	request = &CreateDashboardRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "CreateDashboard")
	return
}

func NewCreateDashboardResponse() (response *CreateDashboardResponse) {
	response = &CreateDashboardResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 新增监控面板
func (c *Client) CreateDashboard(request *CreateDashboardRequest) (response *CreateDashboardResponse, err error) {
	if request == nil {
		request = NewCreateDashboardRequest()
	}
	response = NewCreateDashboardResponse()
	err = c.Send(request, response)
	return
}

func NewCreateDashboardViewRequest() (request *CreateDashboardViewRequest) {
	request = &CreateDashboardViewRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "CreateDashboardView")
	return
}

func NewCreateDashboardViewResponse() (response *CreateDashboardViewResponse) {
	response = &CreateDashboardViewResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 创建监控面板视图
func (c *Client) CreateDashboardView(request *CreateDashboardViewRequest) (response *CreateDashboardViewResponse, err error) {
	if request == nil {
		request = NewCreateDashboardViewRequest()
	}
	response = NewCreateDashboardViewResponse()
	err = c.Send(request, response)
	return
}

func NewCreateDefaultViewRequest() (request *CreateDefaultViewRequest) {
	request = &CreateDefaultViewRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "CreateDefaultView")
	return
}

func NewCreateDefaultViewResponse() (response *CreateDefaultViewResponse) {
	response = &CreateDefaultViewResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 创建默认视图
func (c *Client) CreateDefaultView(request *CreateDefaultViewRequest) (response *CreateDefaultViewResponse, err error) {
	if request == nil {
		request = NewCreateDefaultViewRequest()
	}
	response = NewCreateDefaultViewResponse()
	err = c.Send(request, response)
	return
}

func NewCreateGroupRequest() (request *CreateGroupRequest) {
	request = &CreateGroupRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "CreateGroup")
	return
}

func NewCreateGroupResponse() (response *CreateGroupResponse) {
	response = &CreateGroupResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 创建子分组
func (c *Client) CreateGroup(request *CreateGroupRequest) (response *CreateGroupResponse, err error) {
	if request == nil {
		request = NewCreateGroupRequest()
	}
	response = NewCreateGroupResponse()
	err = c.Send(request, response)
	return
}

func NewCreateInstanceGroupRequest() (request *CreateInstanceGroupRequest) {
	request = &CreateInstanceGroupRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "CreateInstanceGroup")
	return
}

func NewCreateInstanceGroupResponse() (response *CreateInstanceGroupResponse) {
	response = &CreateInstanceGroupResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 创建实例分组
func (c *Client) CreateInstanceGroup(request *CreateInstanceGroupRequest) (response *CreateInstanceGroupResponse, err error) {
	if request == nil {
		request = NewCreateInstanceGroupRequest()
	}
	response = NewCreateInstanceGroupResponse()
	err = c.Send(request, response)
	return
}

func NewCreateInstancesRequest() (request *CreateInstancesRequest) {
	request = &CreateInstancesRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "CreateInstances")
	return
}

func NewCreateInstancesResponse() (response *CreateInstancesResponse) {
	response = &CreateInstancesResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 批量添加实例列表
func (c *Client) CreateInstances(request *CreateInstancesRequest) (response *CreateInstancesResponse, err error) {
	if request == nil {
		request = NewCreateInstancesRequest()
	}
	response = NewCreateInstancesResponse()
	err = c.Send(request, response)
	return
}

func NewCreateMetricSetRequest() (request *CreateMetricSetRequest) {
	request = &CreateMetricSetRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "CreateMetricSet")
	return
}

func NewCreateMetricSetResponse() (response *CreateMetricSetResponse) {
	response = &CreateMetricSetResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 创建指标集
func (c *Client) CreateMetricSet(request *CreateMetricSetRequest) (response *CreateMetricSetResponse, err error) {
	if request == nil {
		request = NewCreateMetricSetRequest()
	}
	response = NewCreateMetricSetResponse()
	err = c.Send(request, response)
	return
}

func NewCreateModuleRequest() (request *CreateModuleRequest) {
	request = &CreateModuleRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "CreateModule")
	return
}

func NewCreateModuleResponse() (response *CreateModuleResponse) {
	response = &CreateModuleResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 创建叶子分组节点
func (c *Client) CreateModule(request *CreateModuleRequest) (response *CreateModuleResponse, err error) {
	if request == nil {
		request = NewCreateModuleRequest()
	}
	response = NewCreateModuleResponse()
	err = c.Send(request, response)
	return
}

func NewCreateMonitorAlarmPolicyRequest() (request *CreateMonitorAlarmPolicyRequest) {
	request = &CreateMonitorAlarmPolicyRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "CreateMonitorAlarmPolicy")
	return
}

func NewCreateMonitorAlarmPolicyResponse() (response *CreateMonitorAlarmPolicyResponse) {
	response = &CreateMonitorAlarmPolicyResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 创建告警策略
func (c *Client) CreateMonitorAlarmPolicy(request *CreateMonitorAlarmPolicyRequest) (response *CreateMonitorAlarmPolicyResponse, err error) {
	if request == nil {
		request = NewCreateMonitorAlarmPolicyRequest()
	}
	response = NewCreateMonitorAlarmPolicyResponse()
	err = c.Send(request, response)
	return
}

func NewCreateMonitorDashboardRequest() (request *CreateMonitorDashboardRequest) {
	request = &CreateMonitorDashboardRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "CreateMonitorDashboard")
	return
}

func NewCreateMonitorDashboardResponse() (response *CreateMonitorDashboardResponse) {
	response = &CreateMonitorDashboardResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 新建dashboard
func (c *Client) CreateMonitorDashboard(request *CreateMonitorDashboardRequest) (response *CreateMonitorDashboardResponse, err error) {
	if request == nil {
		request = NewCreateMonitorDashboardRequest()
	}
	response = NewCreateMonitorDashboardResponse()
	err = c.Send(request, response)
	return
}

func NewCreateMonitorNoticeRuleRequest() (request *CreateMonitorNoticeRuleRequest) {
	request = &CreateMonitorNoticeRuleRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "CreateMonitorNoticeRule")
	return
}

func NewCreateMonitorNoticeRuleResponse() (response *CreateMonitorNoticeRuleResponse) {
	response = &CreateMonitorNoticeRuleResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 创建一个告警通知规则
func (c *Client) CreateMonitorNoticeRule(request *CreateMonitorNoticeRuleRequest) (response *CreateMonitorNoticeRuleResponse, err error) {
	if request == nil {
		request = NewCreateMonitorNoticeRuleRequest()
	}
	response = NewCreateMonitorNoticeRuleResponse()
	err = c.Send(request, response)
	return
}

func NewCreateMsgPolicyRequest() (request *CreateMsgPolicyRequest) {
	request = &CreateMsgPolicyRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "CreateMsgPolicy")
	return
}

func NewCreateMsgPolicyResponse() (response *CreateMsgPolicyResponse) {
	response = &CreateMsgPolicyResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 创建自定义消息策略
func (c *Client) CreateMsgPolicy(request *CreateMsgPolicyRequest) (response *CreateMsgPolicyResponse, err error) {
	if request == nil {
		request = NewCreateMsgPolicyRequest()
	}
	response = NewCreateMsgPolicyResponse()
	err = c.Send(request, response)
	return
}

func NewCreatePolicyGroupRequest() (request *CreatePolicyGroupRequest) {
	request = &CreatePolicyGroupRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "CreatePolicyGroup")
	return
}

func NewCreatePolicyGroupResponse() (response *CreatePolicyGroupResponse) {
	response = &CreatePolicyGroupResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 增加策略组
func (c *Client) CreatePolicyGroup(request *CreatePolicyGroupRequest) (response *CreatePolicyGroupResponse, err error) {
	if request == nil {
		request = NewCreatePolicyGroupRequest()
	}
	response = NewCreatePolicyGroupResponse()
	err = c.Send(request, response)
	return
}

func NewCreateServiceRequest() (request *CreateServiceRequest) {
	request = &CreateServiceRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "CreateService")
	return
}

func NewCreateServiceResponse() (response *CreateServiceResponse) {
	response = &CreateServiceResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 开通服务
func (c *Client) CreateService(request *CreateServiceRequest) (response *CreateServiceResponse, err error) {
	if request == nil {
		request = NewCreateServiceRequest()
	}
	response = NewCreateServiceResponse()
	err = c.Send(request, response)
	return
}

func NewCreateStrategyRequest() (request *CreateStrategyRequest) {
	request = &CreateStrategyRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "CreateStrategy")
	return
}

func NewCreateStrategyResponse() (response *CreateStrategyResponse) {
	response = &CreateStrategyResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 创建告警策略
func (c *Client) CreateStrategy(request *CreateStrategyRequest) (response *CreateStrategyResponse, err error) {
	if request == nil {
		request = NewCreateStrategyRequest()
	}
	response = NewCreateStrategyResponse()
	err = c.Send(request, response)
	return
}

func NewDeleteAlertPolicyRequest() (request *DeleteAlertPolicyRequest) {
	request = &DeleteAlertPolicyRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DeleteAlertPolicy")
	return
}

func NewDeleteAlertPolicyResponse() (response *DeleteAlertPolicyResponse) {
	response = &DeleteAlertPolicyResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// DeleteAlertPolicy删除告警策略
func (c *Client) DeleteAlertPolicy(request *DeleteAlertPolicyRequest) (response *DeleteAlertPolicyResponse, err error) {
	if request == nil {
		request = NewDeleteAlertPolicyRequest()
	}
	response = NewDeleteAlertPolicyResponse()
	err = c.Send(request, response)
	return
}

func NewDeleteAttributeRequest() (request *DeleteAttributeRequest) {
	request = &DeleteAttributeRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DeleteAttribute")
	return
}

func NewDeleteAttributeResponse() (response *DeleteAttributeResponse) {
	response = &DeleteAttributeResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 删除指标
func (c *Client) DeleteAttribute(request *DeleteAttributeRequest) (response *DeleteAttributeResponse, err error) {
	if request == nil {
		request = NewDeleteAttributeRequest()
	}
	response = NewDeleteAttributeResponse()
	err = c.Send(request, response)
	return
}

func NewDeleteAttributesRequest() (request *DeleteAttributesRequest) {
	request = &DeleteAttributesRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DeleteAttributes")
	return
}

func NewDeleteAttributesResponse() (response *DeleteAttributesResponse) {
	response = &DeleteAttributesResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 批量删除指标
func (c *Client) DeleteAttributes(request *DeleteAttributesRequest) (response *DeleteAttributesResponse, err error) {
	if request == nil {
		request = NewDeleteAttributesRequest()
	}
	response = NewDeleteAttributesResponse()
	err = c.Send(request, response)
	return
}

func NewDeleteAutoScalingPolicyRequest() (request *DeleteAutoScalingPolicyRequest) {
	request = &DeleteAutoScalingPolicyRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DeleteAutoScalingPolicy")
	return
}

func NewDeleteAutoScalingPolicyResponse() (response *DeleteAutoScalingPolicyResponse) {
	response = &DeleteAutoScalingPolicyResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 删除告警策略下绑定的弹性伸缩策略
func (c *Client) DeleteAutoScalingPolicy(request *DeleteAutoScalingPolicyRequest) (response *DeleteAutoScalingPolicyResponse, err error) {
	if request == nil {
		request = NewDeleteAutoScalingPolicyRequest()
	}
	response = NewDeleteAutoScalingPolicyResponse()
	err = c.Send(request, response)
	return
}

func NewDeleteCCMChartsRequest() (request *DeleteCCMChartsRequest) {
	request = &DeleteCCMChartsRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DeleteCCMCharts")
	return
}

func NewDeleteCCMChartsResponse() (response *DeleteCCMChartsResponse) {
	response = &DeleteCCMChartsResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 删除分组视图自定义监控图表
func (c *Client) DeleteCCMCharts(request *DeleteCCMChartsRequest) (response *DeleteCCMChartsResponse, err error) {
	if request == nil {
		request = NewDeleteCCMChartsRequest()
	}
	response = NewDeleteCCMChartsResponse()
	err = c.Send(request, response)
	return
}

func NewDeleteCCMDashboardsRequest() (request *DeleteCCMDashboardsRequest) {
	request = &DeleteCCMDashboardsRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DeleteCCMDashboards")
	return
}

func NewDeleteCCMDashboardsResponse() (response *DeleteCCMDashboardsResponse) {
	response = &DeleteCCMDashboardsResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 批量删除自定义监控图表
func (c *Client) DeleteCCMDashboards(request *DeleteCCMDashboardsRequest) (response *DeleteCCMDashboardsResponse, err error) {
	if request == nil {
		request = NewDeleteCCMDashboardsRequest()
	}
	response = NewDeleteCCMDashboardsResponse()
	err = c.Send(request, response)
	return
}

func NewDeleteClmAlertPolicyRequest() (request *DeleteClmAlertPolicyRequest) {
	request = &DeleteClmAlertPolicyRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DeleteClmAlertPolicy")
	return
}

func NewDeleteClmAlertPolicyResponse() (response *DeleteClmAlertPolicyResponse) {
	response = &DeleteClmAlertPolicyResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 删除告警策略
func (c *Client) DeleteClmAlertPolicy(request *DeleteClmAlertPolicyRequest) (response *DeleteClmAlertPolicyResponse, err error) {
	if request == nil {
		request = NewDeleteClmAlertPolicyRequest()
	}
	response = NewDeleteClmAlertPolicyResponse()
	err = c.Send(request, response)
	return
}

func NewDeleteClmMetricSetRequest() (request *DeleteClmMetricSetRequest) {
	request = &DeleteClmMetricSetRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DeleteClmMetricSet")
	return
}

func NewDeleteClmMetricSetResponse() (response *DeleteClmMetricSetResponse) {
	response = &DeleteClmMetricSetResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 删除指标集
func (c *Client) DeleteClmMetricSet(request *DeleteClmMetricSetRequest) (response *DeleteClmMetricSetResponse, err error) {
	if request == nil {
		request = NewDeleteClmMetricSetRequest()
	}
	response = NewDeleteClmMetricSetResponse()
	err = c.Send(request, response)
	return
}

func NewDeleteConditionsTemplateRequest() (request *DeleteConditionsTemplateRequest) {
	request = &DeleteConditionsTemplateRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DeleteConditionsTemplate")
	return
}

func NewDeleteConditionsTemplateResponse() (response *DeleteConditionsTemplateResponse) {
	response = &DeleteConditionsTemplateResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 删除触发条件模板
func (c *Client) DeleteConditionsTemplate(request *DeleteConditionsTemplateRequest) (response *DeleteConditionsTemplateResponse, err error) {
	if request == nil {
		request = NewDeleteConditionsTemplateRequest()
	}
	response = NewDeleteConditionsTemplateResponse()
	err = c.Send(request, response)
	return
}

func NewDeleteDashboardRequest() (request *DeleteDashboardRequest) {
	request = &DeleteDashboardRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DeleteDashboard")
	return
}

func NewDeleteDashboardResponse() (response *DeleteDashboardResponse) {
	response = &DeleteDashboardResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 删除监控面板
func (c *Client) DeleteDashboard(request *DeleteDashboardRequest) (response *DeleteDashboardResponse, err error) {
	if request == nil {
		request = NewDeleteDashboardRequest()
	}
	response = NewDeleteDashboardResponse()
	err = c.Send(request, response)
	return
}

func NewDeleteDashboardViewRequest() (request *DeleteDashboardViewRequest) {
	request = &DeleteDashboardViewRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DeleteDashboardView")
	return
}

func NewDeleteDashboardViewResponse() (response *DeleteDashboardViewResponse) {
	response = &DeleteDashboardViewResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 删除监控面板视图
func (c *Client) DeleteDashboardView(request *DeleteDashboardViewRequest) (response *DeleteDashboardViewResponse, err error) {
	if request == nil {
		request = NewDeleteDashboardViewRequest()
	}
	response = NewDeleteDashboardViewResponse()
	err = c.Send(request, response)
	return
}

func NewDeleteGroupRequest() (request *DeleteGroupRequest) {
	request = &DeleteGroupRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DeleteGroup")
	return
}

func NewDeleteGroupResponse() (response *DeleteGroupResponse) {
	response = &DeleteGroupResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 删除分组节点
func (c *Client) DeleteGroup(request *DeleteGroupRequest) (response *DeleteGroupResponse, err error) {
	if request == nil {
		request = NewDeleteGroupRequest()
	}
	response = NewDeleteGroupResponse()
	err = c.Send(request, response)
	return
}

func NewDeleteInstanceGroupRequest() (request *DeleteInstanceGroupRequest) {
	request = &DeleteInstanceGroupRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DeleteInstanceGroup")
	return
}

func NewDeleteInstanceGroupResponse() (response *DeleteInstanceGroupResponse) {
	response = &DeleteInstanceGroupResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 删除实例分组
func (c *Client) DeleteInstanceGroup(request *DeleteInstanceGroupRequest) (response *DeleteInstanceGroupResponse, err error) {
	if request == nil {
		request = NewDeleteInstanceGroupRequest()
	}
	response = NewDeleteInstanceGroupResponse()
	err = c.Send(request, response)
	return
}

func NewDeleteInstancesRequest() (request *DeleteInstancesRequest) {
	request = &DeleteInstancesRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DeleteInstances")
	return
}

func NewDeleteInstancesResponse() (response *DeleteInstancesResponse) {
	response = &DeleteInstancesResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 批量删除分组列表
func (c *Client) DeleteInstances(request *DeleteInstancesRequest) (response *DeleteInstancesResponse, err error) {
	if request == nil {
		request = NewDeleteInstancesRequest()
	}
	response = NewDeleteInstancesResponse()
	err = c.Send(request, response)
	return
}

func NewDeleteInstancesInInstanceGroupRequest() (request *DeleteInstancesInInstanceGroupRequest) {
	request = &DeleteInstancesInInstanceGroupRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DeleteInstancesInInstanceGroup")
	return
}

func NewDeleteInstancesInInstanceGroupResponse() (response *DeleteInstancesInInstanceGroupResponse) {
	response = &DeleteInstancesInInstanceGroupResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 删除实例分组中实例对象
func (c *Client) DeleteInstancesInInstanceGroup(request *DeleteInstancesInInstanceGroupRequest) (response *DeleteInstancesInInstanceGroupResponse, err error) {
	if request == nil {
		request = NewDeleteInstancesInInstanceGroupRequest()
	}
	response = NewDeleteInstancesInInstanceGroupResponse()
	err = c.Send(request, response)
	return
}

func NewDeleteMetricSetRequest() (request *DeleteMetricSetRequest) {
	request = &DeleteMetricSetRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DeleteMetricSet")
	return
}

func NewDeleteMetricSetResponse() (response *DeleteMetricSetResponse) {
	response = &DeleteMetricSetResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// DeleteMetricSet删除指标集
func (c *Client) DeleteMetricSet(request *DeleteMetricSetRequest) (response *DeleteMetricSetResponse, err error) {
	if request == nil {
		request = NewDeleteMetricSetRequest()
	}
	response = NewDeleteMetricSetResponse()
	err = c.Send(request, response)
	return
}

func NewDeleteModuleRequest() (request *DeleteModuleRequest) {
	request = &DeleteModuleRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DeleteModule")
	return
}

func NewDeleteModuleResponse() (response *DeleteModuleResponse) {
	response = &DeleteModuleResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 删除叶子分组节点
func (c *Client) DeleteModule(request *DeleteModuleRequest) (response *DeleteModuleResponse, err error) {
	if request == nil {
		request = NewDeleteModuleRequest()
	}
	response = NewDeleteModuleResponse()
	err = c.Send(request, response)
	return
}

func NewDeleteMonitorAlarmPolicyRequest() (request *DeleteMonitorAlarmPolicyRequest) {
	request = &DeleteMonitorAlarmPolicyRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DeleteMonitorAlarmPolicy")
	return
}

func NewDeleteMonitorAlarmPolicyResponse() (response *DeleteMonitorAlarmPolicyResponse) {
	response = &DeleteMonitorAlarmPolicyResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 删除告警策略
func (c *Client) DeleteMonitorAlarmPolicy(request *DeleteMonitorAlarmPolicyRequest) (response *DeleteMonitorAlarmPolicyResponse, err error) {
	if request == nil {
		request = NewDeleteMonitorAlarmPolicyRequest()
	}
	response = NewDeleteMonitorAlarmPolicyResponse()
	err = c.Send(request, response)
	return
}

func NewDeleteMonitorDashboardsRequest() (request *DeleteMonitorDashboardsRequest) {
	request = &DeleteMonitorDashboardsRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DeleteMonitorDashboards")
	return
}

func NewDeleteMonitorDashboardsResponse() (response *DeleteMonitorDashboardsResponse) {
	response = &DeleteMonitorDashboardsResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 删除dashboard
func (c *Client) DeleteMonitorDashboards(request *DeleteMonitorDashboardsRequest) (response *DeleteMonitorDashboardsResponse, err error) {
	if request == nil {
		request = NewDeleteMonitorDashboardsRequest()
	}
	response = NewDeleteMonitorDashboardsResponse()
	err = c.Send(request, response)
	return
}

func NewDeleteMonitorNoticeRulesRequest() (request *DeleteMonitorNoticeRulesRequest) {
	request = &DeleteMonitorNoticeRulesRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DeleteMonitorNoticeRules")
	return
}

func NewDeleteMonitorNoticeRulesResponse() (response *DeleteMonitorNoticeRulesResponse) {
	response = &DeleteMonitorNoticeRulesResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 删除告警通知规则
func (c *Client) DeleteMonitorNoticeRules(request *DeleteMonitorNoticeRulesRequest) (response *DeleteMonitorNoticeRulesResponse, err error) {
	if request == nil {
		request = NewDeleteMonitorNoticeRulesRequest()
	}
	response = NewDeleteMonitorNoticeRulesResponse()
	err = c.Send(request, response)
	return
}

func NewDeleteMsgPolicyRequest() (request *DeleteMsgPolicyRequest) {
	request = &DeleteMsgPolicyRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DeleteMsgPolicy")
	return
}

func NewDeleteMsgPolicyResponse() (response *DeleteMsgPolicyResponse) {
	response = &DeleteMsgPolicyResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 删除自定义消息策略
func (c *Client) DeleteMsgPolicy(request *DeleteMsgPolicyRequest) (response *DeleteMsgPolicyResponse, err error) {
	if request == nil {
		request = NewDeleteMsgPolicyRequest()
	}
	response = NewDeleteMsgPolicyResponse()
	err = c.Send(request, response)
	return
}

func NewDeletePolicyGroupRequest() (request *DeletePolicyGroupRequest) {
	request = &DeletePolicyGroupRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DeletePolicyGroup")
	return
}

func NewDeletePolicyGroupResponse() (response *DeletePolicyGroupResponse) {
	response = &DeletePolicyGroupResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 删除告警策略组
func (c *Client) DeletePolicyGroup(request *DeletePolicyGroupRequest) (response *DeletePolicyGroupResponse, err error) {
	if request == nil {
		request = NewDeletePolicyGroupRequest()
	}
	response = NewDeletePolicyGroupResponse()
	err = c.Send(request, response)
	return
}

func NewDeleteStrategysRequest() (request *DeleteStrategysRequest) {
	request = &DeleteStrategysRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DeleteStrategys")
	return
}

func NewDeleteStrategysResponse() (response *DeleteStrategysResponse) {
	response = &DeleteStrategysResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 批量删除告警策略
func (c *Client) DeleteStrategys(request *DeleteStrategysRequest) (response *DeleteStrategysResponse, err error) {
	if request == nil {
		request = NewDeleteStrategysRequest()
	}
	response = NewDeleteStrategysResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeAbnormalObjectsRequest() (request *DescribeAbnormalObjectsRequest) {
	request = &DescribeAbnormalObjectsRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeAbnormalObjects")
	return
}

func NewDescribeAbnormalObjectsResponse() (response *DescribeAbnormalObjectsResponse) {
	response = &DescribeAbnormalObjectsResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 拉取近二十四小时发生异常的业务实例（告警维度）
func (c *Client) DescribeAbnormalObjects(request *DescribeAbnormalObjectsRequest) (response *DescribeAbnormalObjectsResponse, err error) {
	if request == nil {
		request = NewDescribeAbnormalObjectsRequest()
	}
	response = NewDescribeAbnormalObjectsResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeAccidentConfigRequest() (request *DescribeAccidentConfigRequest) {
	request = &DescribeAccidentConfigRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeAccidentConfig")
	return
}

func NewDescribeAccidentConfigResponse() (response *DescribeAccidentConfigResponse) {
	response = &DescribeAccidentConfigResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取服务健康看板事件配置
func (c *Client) DescribeAccidentConfig(request *DescribeAccidentConfigRequest) (response *DescribeAccidentConfigResponse, err error) {
	if request == nil {
		request = NewDescribeAccidentConfigRequest()
	}
	response = NewDescribeAccidentConfigResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeAccidentEventListRequest() (request *DescribeAccidentEventListRequest) {
	request = &DescribeAccidentEventListRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeAccidentEventList")
	return
}

func NewDescribeAccidentEventListResponse() (response *DescribeAccidentEventListResponse) {
	response = &DescribeAccidentEventListResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取平台事件列表
func (c *Client) DescribeAccidentEventList(request *DescribeAccidentEventListRequest) (response *DescribeAccidentEventListResponse, err error) {
	if request == nil {
		request = NewDescribeAccidentEventListRequest()
	}
	response = NewDescribeAccidentEventListResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeAgentStatusHistoryRequest() (request *DescribeAgentStatusHistoryRequest) {
	request = &DescribeAgentStatusHistoryRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeAgentStatusHistory")
	return
}

func NewDescribeAgentStatusHistoryResponse() (response *DescribeAgentStatusHistoryResponse) {
	response = &DescribeAgentStatusHistoryResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取子机历史状态
func (c *Client) DescribeAgentStatusHistory(request *DescribeAgentStatusHistoryRequest) (response *DescribeAgentStatusHistoryResponse, err error) {
	if request == nil {
		request = NewDescribeAgentStatusHistoryRequest()
	}
	response = NewDescribeAgentStatusHistoryResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeAlarmBindingInstanceListRequest() (request *DescribeAlarmBindingInstanceListRequest) {
	request = &DescribeAlarmBindingInstanceListRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeAlarmBindingInstanceList")
	return
}

func NewDescribeAlarmBindingInstanceListResponse() (response *DescribeAlarmBindingInstanceListResponse) {
	response = &DescribeAlarmBindingInstanceListResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取告警策略绑定实例列表
func (c *Client) DescribeAlarmBindingInstanceList(request *DescribeAlarmBindingInstanceListRequest) (response *DescribeAlarmBindingInstanceListResponse, err error) {
	if request == nil {
		request = NewDescribeAlarmBindingInstanceListRequest()
	}
	response = NewDescribeAlarmBindingInstanceListResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeAlarmCallbackHistoryRequest() (request *DescribeAlarmCallbackHistoryRequest) {
	request = &DescribeAlarmCallbackHistoryRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeAlarmCallbackHistory")
	return
}

func NewDescribeAlarmCallbackHistoryResponse() (response *DescribeAlarmCallbackHistoryResponse) {
	response = &DescribeAlarmCallbackHistoryResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取告警回调接口历史记录列表
func (c *Client) DescribeAlarmCallbackHistory(request *DescribeAlarmCallbackHistoryRequest) (response *DescribeAlarmCallbackHistoryResponse, err error) {
	if request == nil {
		request = NewDescribeAlarmCallbackHistoryRequest()
	}
	response = NewDescribeAlarmCallbackHistoryResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeAlarmCallbackVerifyCodeRequest() (request *DescribeAlarmCallbackVerifyCodeRequest) {
	request = &DescribeAlarmCallbackVerifyCodeRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeAlarmCallbackVerifyCode")
	return
}

func NewDescribeAlarmCallbackVerifyCodeResponse() (response *DescribeAlarmCallbackVerifyCodeResponse) {
	response = &DescribeAlarmCallbackVerifyCodeResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取告警回调验证码接口
func (c *Client) DescribeAlarmCallbackVerifyCode(request *DescribeAlarmCallbackVerifyCodeRequest) (response *DescribeAlarmCallbackVerifyCodeResponse, err error) {
	if request == nil {
		request = NewDescribeAlarmCallbackVerifyCodeRequest()
	}
	response = NewDescribeAlarmCallbackVerifyCodeResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeAlarmHistoryByAlarmIdRequest() (request *DescribeAlarmHistoryByAlarmIdRequest) {
	request = &DescribeAlarmHistoryByAlarmIdRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeAlarmHistoryByAlarmId")
	return
}

func NewDescribeAlarmHistoryByAlarmIdResponse() (response *DescribeAlarmHistoryByAlarmIdResponse) {
	response = &DescribeAlarmHistoryByAlarmIdResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 根据alarmId获取历史告警数据
func (c *Client) DescribeAlarmHistoryByAlarmId(request *DescribeAlarmHistoryByAlarmIdRequest) (response *DescribeAlarmHistoryByAlarmIdResponse, err error) {
	if request == nil {
		request = NewDescribeAlarmHistoryByAlarmIdRequest()
	}
	response = NewDescribeAlarmHistoryByAlarmIdResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeAlarmInfoRequest() (request *DescribeAlarmInfoRequest) {
	request = &DescribeAlarmInfoRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeAlarmInfo")
	return
}

func NewDescribeAlarmInfoResponse() (response *DescribeAlarmInfoResponse) {
	response = &DescribeAlarmInfoResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取告警详情
func (c *Client) DescribeAlarmInfo(request *DescribeAlarmInfoRequest) (response *DescribeAlarmInfoResponse, err error) {
	if request == nil {
		request = NewDescribeAlarmInfoRequest()
	}
	response = NewDescribeAlarmInfoResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeAlarmSmsQuotaRequest() (request *DescribeAlarmSmsQuotaRequest) {
	request = &DescribeAlarmSmsQuotaRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeAlarmSmsQuota")
	return
}

func NewDescribeAlarmSmsQuotaResponse() (response *DescribeAlarmSmsQuotaResponse) {
	response = &DescribeAlarmSmsQuotaResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取告警短信配额
func (c *Client) DescribeAlarmSmsQuota(request *DescribeAlarmSmsQuotaRequest) (response *DescribeAlarmSmsQuotaResponse, err error) {
	if request == nil {
		request = NewDescribeAlarmSmsQuotaRequest()
	}
	response = NewDescribeAlarmSmsQuotaResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeAlertPoliciesRequest() (request *DescribeAlertPoliciesRequest) {
	request = &DescribeAlertPoliciesRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeAlertPolicies")
	return
}

func NewDescribeAlertPoliciesResponse() (response *DescribeAlertPoliciesResponse) {
	response = &DescribeAlertPoliciesResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 查询告警策略列表
func (c *Client) DescribeAlertPolicies(request *DescribeAlertPoliciesRequest) (response *DescribeAlertPoliciesResponse, err error) {
	if request == nil {
		request = NewDescribeAlertPoliciesRequest()
	}
	response = NewDescribeAlertPoliciesResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeAlertPolicyRequest() (request *DescribeAlertPolicyRequest) {
	request = &DescribeAlertPolicyRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeAlertPolicy")
	return
}

func NewDescribeAlertPolicyResponse() (response *DescribeAlertPolicyResponse) {
	response = &DescribeAlertPolicyResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// DescribeAlertPolicyCLM查询告警策略详情
func (c *Client) DescribeAlertPolicy(request *DescribeAlertPolicyRequest) (response *DescribeAlertPolicyResponse, err error) {
	if request == nil {
		request = NewDescribeAlertPolicyRequest()
	}
	response = NewDescribeAlertPolicyResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeAppFlowRequest() (request *DescribeAppFlowRequest) {
	request = &DescribeAppFlowRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeAppFlow")
	return
}

func NewDescribeAppFlowResponse() (response *DescribeAppFlowResponse) {
	response = &DescribeAppFlowResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取实例月粒度数据
func (c *Client) DescribeAppFlow(request *DescribeAppFlowRequest) (response *DescribeAppFlowResponse, err error) {
	if request == nil {
		request = NewDescribeAppFlowRequest()
	}
	response = NewDescribeAppFlowResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeAttributeAggregateDataRequest() (request *DescribeAttributeAggregateDataRequest) {
	request = &DescribeAttributeAggregateDataRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeAttributeAggregateData")
	return
}

func NewDescribeAttributeAggregateDataResponse() (response *DescribeAttributeAggregateDataResponse) {
	response = &DescribeAttributeAggregateDataResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 查询指标聚合数据
func (c *Client) DescribeAttributeAggregateData(request *DescribeAttributeAggregateDataRequest) (response *DescribeAttributeAggregateDataResponse, err error) {
	if request == nil {
		request = NewDescribeAttributeAggregateDataRequest()
	}
	response = NewDescribeAttributeAggregateDataResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeAttributeAllServerRequest() (request *DescribeAttributeAllServerRequest) {
	request = &DescribeAttributeAllServerRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeAttributeAllServer")
	return
}

func NewDescribeAttributeAllServerResponse() (response *DescribeAttributeAllServerResponse) {
	response = &DescribeAttributeAllServerResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 查询指标关联服务器信息
func (c *Client) DescribeAttributeAllServer(request *DescribeAttributeAllServerRequest) (response *DescribeAttributeAllServerResponse, err error) {
	if request == nil {
		request = NewDescribeAttributeAllServerRequest()
	}
	response = NewDescribeAttributeAllServerResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeAttributeUnitsRequest() (request *DescribeAttributeUnitsRequest) {
	request = &DescribeAttributeUnitsRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeAttributeUnits")
	return
}

func NewDescribeAttributeUnitsResponse() (response *DescribeAttributeUnitsResponse) {
	response = &DescribeAttributeUnitsResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 查询指标单位
func (c *Client) DescribeAttributeUnits(request *DescribeAttributeUnitsRequest) (response *DescribeAttributeUnitsResponse, err error) {
	if request == nil {
		request = NewDescribeAttributeUnitsRequest()
	}
	response = NewDescribeAttributeUnitsResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeAttributesRequest() (request *DescribeAttributesRequest) {
	request = &DescribeAttributesRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeAttributes")
	return
}

func NewDescribeAttributesResponse() (response *DescribeAttributesResponse) {
	response = &DescribeAttributesResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 查询属性信息
func (c *Client) DescribeAttributes(request *DescribeAttributesRequest) (response *DescribeAttributesResponse, err error) {
	if request == nil {
		request = NewDescribeAttributesRequest()
	}
	response = NewDescribeAttributesResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeAutoScalingPolicyRequest() (request *DescribeAutoScalingPolicyRequest) {
	request = &DescribeAutoScalingPolicyRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeAutoScalingPolicy")
	return
}

func NewDescribeAutoScalingPolicyResponse() (response *DescribeAutoScalingPolicyResponse) {
	response = &DescribeAutoScalingPolicyResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 查询某个告警策略下绑定的弹性伸缩策略
func (c *Client) DescribeAutoScalingPolicy(request *DescribeAutoScalingPolicyRequest) (response *DescribeAutoScalingPolicyResponse, err error) {
	if request == nil {
		request = NewDescribeAutoScalingPolicyRequest()
	}
	response = NewDescribeAutoScalingPolicyResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeBaseMetricsRequest() (request *DescribeBaseMetricsRequest) {
	request = &DescribeBaseMetricsRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeBaseMetrics")
	return
}

func NewDescribeBaseMetricsResponse() (response *DescribeBaseMetricsResponse) {
	response = &DescribeBaseMetricsResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取基础指标详情
func (c *Client) DescribeBaseMetrics(request *DescribeBaseMetricsRequest) (response *DescribeBaseMetricsResponse, err error) {
	if request == nil {
		request = NewDescribeBaseMetricsRequest()
	}
	response = NewDescribeBaseMetricsResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeBaseMetricsForConsoleFontEndRequest() (request *DescribeBaseMetricsForConsoleFontEndRequest) {
	request = &DescribeBaseMetricsForConsoleFontEndRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeBaseMetricsForConsoleFontEnd")
	return
}

func NewDescribeBaseMetricsForConsoleFontEndResponse() (response *DescribeBaseMetricsForConsoleFontEndResponse) {
	response = &DescribeBaseMetricsForConsoleFontEndResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 控制台前端调用获取基础指标
func (c *Client) DescribeBaseMetricsForConsoleFontEnd(request *DescribeBaseMetricsForConsoleFontEndRequest) (response *DescribeBaseMetricsForConsoleFontEndResponse, err error) {
	if request == nil {
		request = NewDescribeBaseMetricsForConsoleFontEndRequest()
	}
	response = NewDescribeBaseMetricsForConsoleFontEndResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeBasicAlarmListRequest() (request *DescribeBasicAlarmListRequest) {
	request = &DescribeBasicAlarmListRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeBasicAlarmList")
	return
}

func NewDescribeBasicAlarmListResponse() (response *DescribeBasicAlarmListResponse) {
	response = &DescribeBasicAlarmListResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取基础告警列表
func (c *Client) DescribeBasicAlarmList(request *DescribeBasicAlarmListRequest) (response *DescribeBasicAlarmListResponse, err error) {
	if request == nil {
		request = NewDescribeBasicAlarmListRequest()
	}
	response = NewDescribeBasicAlarmListResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeBindingPolicyObjectListRequest() (request *DescribeBindingPolicyObjectListRequest) {
	request = &DescribeBindingPolicyObjectListRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeBindingPolicyObjectList")
	return
}

func NewDescribeBindingPolicyObjectListResponse() (response *DescribeBindingPolicyObjectListResponse) {
	response = &DescribeBindingPolicyObjectListResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取已绑定对象列表
func (c *Client) DescribeBindingPolicyObjectList(request *DescribeBindingPolicyObjectListRequest) (response *DescribeBindingPolicyObjectListResponse, err error) {
	if request == nil {
		request = NewDescribeBindingPolicyObjectListRequest()
	}
	response = NewDescribeBindingPolicyObjectListResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeCCMChartsRequest() (request *DescribeCCMChartsRequest) {
	request = &DescribeCCMChartsRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeCCMCharts")
	return
}

func NewDescribeCCMChartsResponse() (response *DescribeCCMChartsResponse) {
	response = &DescribeCCMChartsResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 查询分组视图自定义监控图表
func (c *Client) DescribeCCMCharts(request *DescribeCCMChartsRequest) (response *DescribeCCMChartsResponse, err error) {
	if request == nil {
		request = NewDescribeCCMChartsRequest()
	}
	response = NewDescribeCCMChartsResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeCCMDashboardsRequest() (request *DescribeCCMDashboardsRequest) {
	request = &DescribeCCMDashboardsRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeCCMDashboards")
	return
}

func NewDescribeCCMDashboardsResponse() (response *DescribeCCMDashboardsResponse) {
	response = &DescribeCCMDashboardsResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 查询自定义监控图表信息
func (c *Client) DescribeCCMDashboards(request *DescribeCCMDashboardsRequest) (response *DescribeCCMDashboardsResponse, err error) {
	if request == nil {
		request = NewDescribeCCMDashboardsRequest()
	}
	response = NewDescribeCCMDashboardsResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeCCMGroupViewRequest() (request *DescribeCCMGroupViewRequest) {
	request = &DescribeCCMGroupViewRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeCCMGroupView")
	return
}

func NewDescribeCCMGroupViewResponse() (response *DescribeCCMGroupViewResponse) {
	response = &DescribeCCMGroupViewResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 查询分组视图
func (c *Client) DescribeCCMGroupView(request *DescribeCCMGroupViewRequest) (response *DescribeCCMGroupViewResponse, err error) {
	if request == nil {
		request = NewDescribeCCMGroupViewRequest()
	}
	response = NewDescribeCCMGroupViewResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeCCMGroupViewAttributeRequest() (request *DescribeCCMGroupViewAttributeRequest) {
	request = &DescribeCCMGroupViewAttributeRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeCCMGroupViewAttribute")
	return
}

func NewDescribeCCMGroupViewAttributeResponse() (response *DescribeCCMGroupViewAttributeResponse) {
	response = &DescribeCCMGroupViewAttributeResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 查询分组视图关联指标
func (c *Client) DescribeCCMGroupViewAttribute(request *DescribeCCMGroupViewAttributeRequest) (response *DescribeCCMGroupViewAttributeResponse, err error) {
	if request == nil {
		request = NewDescribeCCMGroupViewAttributeRequest()
	}
	response = NewDescribeCCMGroupViewAttributeResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeCCMGroupViewStrategyRequest() (request *DescribeCCMGroupViewStrategyRequest) {
	request = &DescribeCCMGroupViewStrategyRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeCCMGroupViewStrategy")
	return
}

func NewDescribeCCMGroupViewStrategyResponse() (response *DescribeCCMGroupViewStrategyResponse) {
	response = &DescribeCCMGroupViewStrategyResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 查询分组视图关联告警策略
func (c *Client) DescribeCCMGroupViewStrategy(request *DescribeCCMGroupViewStrategyRequest) (response *DescribeCCMGroupViewStrategyResponse, err error) {
	if request == nil {
		request = NewDescribeCCMGroupViewStrategyRequest()
	}
	response = NewDescribeCCMGroupViewStrategyResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeCCMInstanceDatasRequest() (request *DescribeCCMInstanceDatasRequest) {
	request = &DescribeCCMInstanceDatasRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeCCMInstanceDatas")
	return
}

func NewDescribeCCMInstanceDatasResponse() (response *DescribeCCMInstanceDatasResponse) {
	response = &DescribeCCMInstanceDatasResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 批量查询实例指标数据
func (c *Client) DescribeCCMInstanceDatas(request *DescribeCCMInstanceDatasRequest) (response *DescribeCCMInstanceDatasResponse, err error) {
	if request == nil {
		request = NewDescribeCCMInstanceDatasRequest()
	}
	response = NewDescribeCCMInstanceDatasResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeClmAlertPoliciesRequest() (request *DescribeClmAlertPoliciesRequest) {
	request = &DescribeClmAlertPoliciesRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeClmAlertPolicies")
	return
}

func NewDescribeClmAlertPoliciesResponse() (response *DescribeClmAlertPoliciesResponse) {
	response = &DescribeClmAlertPoliciesResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// CLM查询告警策略列表
func (c *Client) DescribeClmAlertPolicies(request *DescribeClmAlertPoliciesRequest) (response *DescribeClmAlertPoliciesResponse, err error) {
	if request == nil {
		request = NewDescribeClmAlertPoliciesRequest()
	}
	response = NewDescribeClmAlertPoliciesResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeClmAlertPolicyRequest() (request *DescribeClmAlertPolicyRequest) {
	request = &DescribeClmAlertPolicyRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeClmAlertPolicy")
	return
}

func NewDescribeClmAlertPolicyResponse() (response *DescribeClmAlertPolicyResponse) {
	response = &DescribeClmAlertPolicyResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 查询告警策略详情
func (c *Client) DescribeClmAlertPolicy(request *DescribeClmAlertPolicyRequest) (response *DescribeClmAlertPolicyResponse, err error) {
	if request == nil {
		request = NewDescribeClmAlertPolicyRequest()
	}
	response = NewDescribeClmAlertPolicyResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeClmDemoDimensionValueRequest() (request *DescribeClmDemoDimensionValueRequest) {
	request = &DescribeClmDemoDimensionValueRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeClmDemoDimensionValue")
	return
}

func NewDescribeClmDemoDimensionValueResponse() (response *DescribeClmDemoDimensionValueResponse) {
	response = &DescribeClmDemoDimensionValueResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取demo维度值
func (c *Client) DescribeClmDemoDimensionValue(request *DescribeClmDemoDimensionValueRequest) (response *DescribeClmDemoDimensionValueResponse, err error) {
	if request == nil {
		request = NewDescribeClmDemoDimensionValueRequest()
	}
	response = NewDescribeClmDemoDimensionValueResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeClmDemoLogRequest() (request *DescribeClmDemoLogRequest) {
	request = &DescribeClmDemoLogRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeClmDemoLog")
	return
}

func NewDescribeClmDemoLogResponse() (response *DescribeClmDemoLogResponse) {
	response = &DescribeClmDemoLogResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// CLM搜索Demo日志主题日志
func (c *Client) DescribeClmDemoLog(request *DescribeClmDemoLogRequest) (response *DescribeClmDemoLogResponse, err error) {
	if request == nil {
		request = NewDescribeClmDemoLogRequest()
	}
	response = NewDescribeClmDemoLogResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeClmDemoMetricSetRequest() (request *DescribeClmDemoMetricSetRequest) {
	request = &DescribeClmDemoMetricSetRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeClmDemoMetricSet")
	return
}

func NewDescribeClmDemoMetricSetResponse() (response *DescribeClmDemoMetricSetResponse) {
	response = &DescribeClmDemoMetricSetResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// CLM获取demo指标集详情
func (c *Client) DescribeClmDemoMetricSet(request *DescribeClmDemoMetricSetRequest) (response *DescribeClmDemoMetricSetResponse, err error) {
	if request == nil {
		request = NewDescribeClmDemoMetricSetRequest()
	}
	response = NewDescribeClmDemoMetricSetResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeClmDimensionAnalysisDataRequest() (request *DescribeClmDimensionAnalysisDataRequest) {
	request = &DescribeClmDimensionAnalysisDataRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeClmDimensionAnalysisData")
	return
}

func NewDescribeClmDimensionAnalysisDataResponse() (response *DescribeClmDimensionAnalysisDataResponse) {
	response = &DescribeClmDimensionAnalysisDataResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 拉取维度分析数据
func (c *Client) DescribeClmDimensionAnalysisData(request *DescribeClmDimensionAnalysisDataRequest) (response *DescribeClmDimensionAnalysisDataResponse, err error) {
	if request == nil {
		request = NewDescribeClmDimensionAnalysisDataRequest()
	}
	response = NewDescribeClmDimensionAnalysisDataResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeClmMetricAnalysisDataRequest() (request *DescribeClmMetricAnalysisDataRequest) {
	request = &DescribeClmMetricAnalysisDataRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeClmMetricAnalysisData")
	return
}

func NewDescribeClmMetricAnalysisDataResponse() (response *DescribeClmMetricAnalysisDataResponse) {
	response = &DescribeClmMetricAnalysisDataResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 查询指标分析数据
func (c *Client) DescribeClmMetricAnalysisData(request *DescribeClmMetricAnalysisDataRequest) (response *DescribeClmMetricAnalysisDataResponse, err error) {
	if request == nil {
		request = NewDescribeClmMetricAnalysisDataRequest()
	}
	response = NewDescribeClmMetricAnalysisDataResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeClmMetricSetRequest() (request *DescribeClmMetricSetRequest) {
	request = &DescribeClmMetricSetRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeClmMetricSet")
	return
}

func NewDescribeClmMetricSetResponse() (response *DescribeClmMetricSetResponse) {
	response = &DescribeClmMetricSetResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取指标集详情
func (c *Client) DescribeClmMetricSet(request *DescribeClmMetricSetRequest) (response *DescribeClmMetricSetResponse, err error) {
	if request == nil {
		request = NewDescribeClmMetricSetRequest()
	}
	response = NewDescribeClmMetricSetResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeClmMetricSetsRequest() (request *DescribeClmMetricSetsRequest) {
	request = &DescribeClmMetricSetsRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeClmMetricSets")
	return
}

func NewDescribeClmMetricSetsResponse() (response *DescribeClmMetricSetsResponse) {
	response = &DescribeClmMetricSetsResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 拉取指标集列表
func (c *Client) DescribeClmMetricSets(request *DescribeClmMetricSetsRequest) (response *DescribeClmMetricSetsResponse, err error) {
	if request == nil {
		request = NewDescribeClmMetricSetsRequest()
	}
	response = NewDescribeClmMetricSetsResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeConditionsTemplateListRequest() (request *DescribeConditionsTemplateListRequest) {
	request = &DescribeConditionsTemplateListRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeConditionsTemplateList")
	return
}

func NewDescribeConditionsTemplateListResponse() (response *DescribeConditionsTemplateListResponse) {
	response = &DescribeConditionsTemplateListResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取条件模板列表
func (c *Client) DescribeConditionsTemplateList(request *DescribeConditionsTemplateListRequest) (response *DescribeConditionsTemplateListResponse, err error) {
	if request == nil {
		request = NewDescribeConditionsTemplateListRequest()
	}
	response = NewDescribeConditionsTemplateListResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeContactListRequest() (request *DescribeContactListRequest) {
	request = &DescribeContactListRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeContactList")
	return
}

func NewDescribeContactListResponse() (response *DescribeContactListResponse) {
	response = &DescribeContactListResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取联系人列表
func (c *Client) DescribeContactList(request *DescribeContactListRequest) (response *DescribeContactListResponse, err error) {
	if request == nil {
		request = NewDescribeContactListRequest()
	}
	response = NewDescribeContactListResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeCurrentTimestampRequest() (request *DescribeCurrentTimestampRequest) {
	request = &DescribeCurrentTimestampRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeCurrentTimestamp")
	return
}

func NewDescribeCurrentTimestampResponse() (response *DescribeCurrentTimestampResponse) {
	response = &DescribeCurrentTimestampResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取服务器当前时间戳
func (c *Client) DescribeCurrentTimestamp(request *DescribeCurrentTimestampRequest) (response *DescribeCurrentTimestampResponse, err error) {
	if request == nil {
		request = NewDescribeCurrentTimestampRequest()
	}
	response = NewDescribeCurrentTimestampResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeCustomAlarmListRequest() (request *DescribeCustomAlarmListRequest) {
	request = &DescribeCustomAlarmListRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeCustomAlarmList")
	return
}

func NewDescribeCustomAlarmListResponse() (response *DescribeCustomAlarmListResponse) {
	response = &DescribeCustomAlarmListResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取自定义消息列表
func (c *Client) DescribeCustomAlarmList(request *DescribeCustomAlarmListRequest) (response *DescribeCustomAlarmListResponse, err error) {
	if request == nil {
		request = NewDescribeCustomAlarmListRequest()
	}
	response = NewDescribeCustomAlarmListResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeCustomMetisAbnormalRequest() (request *DescribeCustomMetisAbnormalRequest) {
	request = &DescribeCustomMetisAbnormalRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeCustomMetisAbnormal")
	return
}

func NewDescribeCustomMetisAbnormalResponse() (response *DescribeCustomMetisAbnormalResponse) {
	response = &DescribeCustomMetisAbnormalResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 自定义监控智能检测查询接口
func (c *Client) DescribeCustomMetisAbnormal(request *DescribeCustomMetisAbnormalRequest) (response *DescribeCustomMetisAbnormalResponse, err error) {
	if request == nil {
		request = NewDescribeCustomMetisAbnormalRequest()
	}
	response = NewDescribeCustomMetisAbnormalResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeCvmAgentStatusRequest() (request *DescribeCvmAgentStatusRequest) {
	request = &DescribeCvmAgentStatusRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeCvmAgentStatus")
	return
}

func NewDescribeCvmAgentStatusResponse() (response *DescribeCvmAgentStatusResponse) {
	response = &DescribeCvmAgentStatusResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取cvm主机agent的状态（是否安装）
func (c *Client) DescribeCvmAgentStatus(request *DescribeCvmAgentStatusRequest) (response *DescribeCvmAgentStatusResponse, err error) {
	if request == nil {
		request = NewDescribeCvmAgentStatusRequest()
	}
	response = NewDescribeCvmAgentStatusResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeDashboardViewsRequest() (request *DescribeDashboardViewsRequest) {
	request = &DescribeDashboardViewsRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeDashboardViews")
	return
}

func NewDescribeDashboardViewsResponse() (response *DescribeDashboardViewsResponse) {
	response = &DescribeDashboardViewsResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取监控面板视图
func (c *Client) DescribeDashboardViews(request *DescribeDashboardViewsRequest) (response *DescribeDashboardViewsResponse, err error) {
	if request == nil {
		request = NewDescribeDashboardViewsRequest()
	}
	response = NewDescribeDashboardViewsResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeDashboardsRequest() (request *DescribeDashboardsRequest) {
	request = &DescribeDashboardsRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeDashboards")
	return
}

func NewDescribeDashboardsResponse() (response *DescribeDashboardsResponse) {
	response = &DescribeDashboardsResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取Dashboard列表
func (c *Client) DescribeDashboards(request *DescribeDashboardsRequest) (response *DescribeDashboardsResponse, err error) {
	if request == nil {
		request = NewDescribeDashboardsRequest()
	}
	response = NewDescribeDashboardsResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeDataRequest() (request *DescribeDataRequest) {
	request = &DescribeDataRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeData")
	return
}

func NewDescribeDataResponse() (response *DescribeDataResponse) {
	response = &DescribeDataResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 查询监控数据和measurement的tag，field名，tag值
func (c *Client) DescribeData(request *DescribeDataRequest) (response *DescribeDataResponse, err error) {
	if request == nil {
		request = NewDescribeDataRequest()
	}
	response = NewDescribeDataResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeDefaultViewRequest() (request *DescribeDefaultViewRequest) {
	request = &DescribeDefaultViewRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeDefaultView")
	return
}

func NewDescribeDefaultViewResponse() (response *DescribeDefaultViewResponse) {
	response = &DescribeDefaultViewResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 查询默认视图
func (c *Client) DescribeDefaultView(request *DescribeDefaultViewRequest) (response *DescribeDefaultViewResponse, err error) {
	if request == nil {
		request = NewDescribeDefaultViewRequest()
	}
	response = NewDescribeDefaultViewResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeDimensionAnalysisDataRequest() (request *DescribeDimensionAnalysisDataRequest) {
	request = &DescribeDimensionAnalysisDataRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeDimensionAnalysisData")
	return
}

func NewDescribeDimensionAnalysisDataResponse() (response *DescribeDimensionAnalysisDataResponse) {
	response = &DescribeDimensionAnalysisDataResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 拉取维度分析数据
func (c *Client) DescribeDimensionAnalysisData(request *DescribeDimensionAnalysisDataRequest) (response *DescribeDimensionAnalysisDataResponse, err error) {
	if request == nil {
		request = NewDescribeDimensionAnalysisDataRequest()
	}
	response = NewDescribeDimensionAnalysisDataResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeEventPolicyConfigRequest() (request *DescribeEventPolicyConfigRequest) {
	request = &DescribeEventPolicyConfigRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeEventPolicyConfig")
	return
}

func NewDescribeEventPolicyConfigResponse() (response *DescribeEventPolicyConfigResponse) {
	response = &DescribeEventPolicyConfigResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取事件策略配置列表
func (c *Client) DescribeEventPolicyConfig(request *DescribeEventPolicyConfigRequest) (response *DescribeEventPolicyConfigResponse, err error) {
	if request == nil {
		request = NewDescribeEventPolicyConfigRequest()
	}
	response = NewDescribeEventPolicyConfigResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeGlobalTagsRequest() (request *DescribeGlobalTagsRequest) {
	request = &DescribeGlobalTagsRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeGlobalTags")
	return
}

func NewDescribeGlobalTagsResponse() (response *DescribeGlobalTagsResponse) {
	response = &DescribeGlobalTagsResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 查询globalTags
func (c *Client) DescribeGlobalTags(request *DescribeGlobalTagsRequest) (response *DescribeGlobalTagsResponse, err error) {
	if request == nil {
		request = NewDescribeGlobalTagsRequest()
	}
	response = NewDescribeGlobalTagsResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeGraphDataRequest() (request *DescribeGraphDataRequest) {
	request = &DescribeGraphDataRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeGraphData")
	return
}

func NewDescribeGraphDataResponse() (response *DescribeGraphDataResponse) {
	response = &DescribeGraphDataResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取硬盘分区使用图
func (c *Client) DescribeGraphData(request *DescribeGraphDataRequest) (response *DescribeGraphDataResponse, err error) {
	if request == nil {
		request = NewDescribeGraphDataRequest()
	}
	response = NewDescribeGraphDataResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeGroupRequest() (request *DescribeGroupRequest) {
	request = &DescribeGroupRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeGroup")
	return
}

func NewDescribeGroupResponse() (response *DescribeGroupResponse) {
	response = &DescribeGroupResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取分组树
func (c *Client) DescribeGroup(request *DescribeGroupRequest) (response *DescribeGroupResponse, err error) {
	if request == nil {
		request = NewDescribeGroupRequest()
	}
	response = NewDescribeGroupResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeIdcServerRequest() (request *DescribeIdcServerRequest) {
	request = &DescribeIdcServerRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeIdcServer")
	return
}

func NewDescribeIdcServerResponse() (response *DescribeIdcServerResponse) {
	response = &DescribeIdcServerResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 查询地域下的所有服务器列表
func (c *Client) DescribeIdcServer(request *DescribeIdcServerRequest) (response *DescribeIdcServerResponse, err error) {
	if request == nil {
		request = NewDescribeIdcServerRequest()
	}
	response = NewDescribeIdcServerResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeIdcServerCountRequest() (request *DescribeIdcServerCountRequest) {
	request = &DescribeIdcServerCountRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeIdcServerCount")
	return
}

func NewDescribeIdcServerCountResponse() (response *DescribeIdcServerCountResponse) {
	response = &DescribeIdcServerCountResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 查询地域实例统计信息
func (c *Client) DescribeIdcServerCount(request *DescribeIdcServerCountRequest) (response *DescribeIdcServerCountResponse, err error) {
	if request == nil {
		request = NewDescribeIdcServerCountRequest()
	}
	response = NewDescribeIdcServerCountResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeIdcTreeRequest() (request *DescribeIdcTreeRequest) {
	request = &DescribeIdcTreeRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeIdcTree")
	return
}

func NewDescribeIdcTreeResponse() (response *DescribeIdcTreeResponse) {
	response = &DescribeIdcTreeResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 查询IDC树
func (c *Client) DescribeIdcTree(request *DescribeIdcTreeRequest) (response *DescribeIdcTreeResponse, err error) {
	if request == nil {
		request = NewDescribeIdcTreeRequest()
	}
	response = NewDescribeIdcTreeResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeInstanceRequest() (request *DescribeInstanceRequest) {
	request = &DescribeInstanceRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeInstance")
	return
}

func NewDescribeInstanceResponse() (response *DescribeInstanceResponse) {
	response = &DescribeInstanceResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 查询实例列表
func (c *Client) DescribeInstance(request *DescribeInstanceRequest) (response *DescribeInstanceResponse, err error) {
	if request == nil {
		request = NewDescribeInstanceRequest()
	}
	response = NewDescribeInstanceResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeInstanceGroupRequest() (request *DescribeInstanceGroupRequest) {
	request = &DescribeInstanceGroupRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeInstanceGroup")
	return
}

func NewDescribeInstanceGroupResponse() (response *DescribeInstanceGroupResponse) {
	response = &DescribeInstanceGroupResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取实例分组详情
func (c *Client) DescribeInstanceGroup(request *DescribeInstanceGroupRequest) (response *DescribeInstanceGroupResponse, err error) {
	if request == nil {
		request = NewDescribeInstanceGroupRequest()
	}
	response = NewDescribeInstanceGroupResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeInstanceGroupListRequest() (request *DescribeInstanceGroupListRequest) {
	request = &DescribeInstanceGroupListRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeInstanceGroupList")
	return
}

func NewDescribeInstanceGroupListResponse() (response *DescribeInstanceGroupListResponse) {
	response = &DescribeInstanceGroupListResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取实例分组列表
func (c *Client) DescribeInstanceGroupList(request *DescribeInstanceGroupListRequest) (response *DescribeInstanceGroupListResponse, err error) {
	if request == nil {
		request = NewDescribeInstanceGroupListRequest()
	}
	response = NewDescribeInstanceGroupListResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeLogSetsRequest() (request *DescribeLogSetsRequest) {
	request = &DescribeLogSetsRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeLogSets")
	return
}

func NewDescribeLogSetsResponse() (response *DescribeLogSetsResponse) {
	response = &DescribeLogSetsResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 拉取CLS中的日志集列表
func (c *Client) DescribeLogSets(request *DescribeLogSetsRequest) (response *DescribeLogSetsResponse, err error) {
	if request == nil {
		request = NewDescribeLogSetsRequest()
	}
	response = NewDescribeLogSetsResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeLogTopicIndexRequest() (request *DescribeLogTopicIndexRequest) {
	request = &DescribeLogTopicIndexRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeLogTopicIndex")
	return
}

func NewDescribeLogTopicIndexResponse() (response *DescribeLogTopicIndexResponse) {
	response = &DescribeLogTopicIndexResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 拉取CLS日志主题索引信息
func (c *Client) DescribeLogTopicIndex(request *DescribeLogTopicIndexRequest) (response *DescribeLogTopicIndexResponse, err error) {
	if request == nil {
		request = NewDescribeLogTopicIndexRequest()
	}
	response = NewDescribeLogTopicIndexResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeLogTopicsRequest() (request *DescribeLogTopicsRequest) {
	request = &DescribeLogTopicsRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeLogTopics")
	return
}

func NewDescribeLogTopicsResponse() (response *DescribeLogTopicsResponse) {
	response = &DescribeLogTopicsResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 拉取CLS日志主题
func (c *Client) DescribeLogTopics(request *DescribeLogTopicsRequest) (response *DescribeLogTopicsResponse, err error) {
	if request == nil {
		request = NewDescribeLogTopicsRequest()
	}
	response = NewDescribeLogTopicsResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMeasurementsRequest() (request *DescribeMeasurementsRequest) {
	request = &DescribeMeasurementsRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeMeasurements")
	return
}

func NewDescribeMeasurementsResponse() (response *DescribeMeasurementsResponse) {
	response = &DescribeMeasurementsResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 查询measurement列表
func (c *Client) DescribeMeasurements(request *DescribeMeasurementsRequest) (response *DescribeMeasurementsResponse, err error) {
	if request == nil {
		request = NewDescribeMeasurementsRequest()
	}
	response = NewDescribeMeasurementsResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMetricAnalysisDataRequest() (request *DescribeMetricAnalysisDataRequest) {
	request = &DescribeMetricAnalysisDataRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeMetricAnalysisData")
	return
}

func NewDescribeMetricAnalysisDataResponse() (response *DescribeMetricAnalysisDataResponse) {
	response = &DescribeMetricAnalysisDataResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 查询指标分析数据
func (c *Client) DescribeMetricAnalysisData(request *DescribeMetricAnalysisDataRequest) (response *DescribeMetricAnalysisDataResponse, err error) {
	if request == nil {
		request = NewDescribeMetricAnalysisDataRequest()
	}
	response = NewDescribeMetricAnalysisDataResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMetricSetRequest() (request *DescribeMetricSetRequest) {
	request = &DescribeMetricSetRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeMetricSet")
	return
}

func NewDescribeMetricSetResponse() (response *DescribeMetricSetResponse) {
	response = &DescribeMetricSetResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取指标集详情
func (c *Client) DescribeMetricSet(request *DescribeMetricSetRequest) (response *DescribeMetricSetResponse, err error) {
	if request == nil {
		request = NewDescribeMetricSetRequest()
	}
	response = NewDescribeMetricSetResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMetricSetsRequest() (request *DescribeMetricSetsRequest) {
	request = &DescribeMetricSetsRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeMetricSets")
	return
}

func NewDescribeMetricSetsResponse() (response *DescribeMetricSetsResponse) {
	response = &DescribeMetricSetsResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 拉取指标集列表DescribeMetricSets
func (c *Client) DescribeMetricSets(request *DescribeMetricSetsRequest) (response *DescribeMetricSetsResponse, err error) {
	if request == nil {
		request = NewDescribeMetricSetsRequest()
	}
	response = NewDescribeMetricSetsResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeModuleRequest() (request *DescribeModuleRequest) {
	request = &DescribeModuleRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeModule")
	return
}

func NewDescribeModuleResponse() (response *DescribeModuleResponse) {
	response = &DescribeModuleResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取叶子分组节点
func (c *Client) DescribeModule(request *DescribeModuleRequest) (response *DescribeModuleResponse, err error) {
	if request == nil {
		request = NewDescribeModuleRequest()
	}
	response = NewDescribeModuleResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMonitorAbnormalDimensionRequest() (request *DescribeMonitorAbnormalDimensionRequest) {
	request = &DescribeMonitorAbnormalDimensionRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeMonitorAbnormalDimension")
	return
}

func NewDescribeMonitorAbnormalDimensionResponse() (response *DescribeMonitorAbnormalDimensionResponse) {
	response = &DescribeMonitorAbnormalDimensionResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取异常维度下钻分析数据
func (c *Client) DescribeMonitorAbnormalDimension(request *DescribeMonitorAbnormalDimensionRequest) (response *DescribeMonitorAbnormalDimensionResponse, err error) {
	if request == nil {
		request = NewDescribeMonitorAbnormalDimensionRequest()
	}
	response = NewDescribeMonitorAbnormalDimensionResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMonitorAggregateFunctionsRequest() (request *DescribeMonitorAggregateFunctionsRequest) {
	request = &DescribeMonitorAggregateFunctionsRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeMonitorAggregateFunctions")
	return
}

func NewDescribeMonitorAggregateFunctionsResponse() (response *DescribeMonitorAggregateFunctionsResponse) {
	response = &DescribeMonitorAggregateFunctionsResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取统计方式
func (c *Client) DescribeMonitorAggregateFunctions(request *DescribeMonitorAggregateFunctionsRequest) (response *DescribeMonitorAggregateFunctionsResponse, err error) {
	if request == nil {
		request = NewDescribeMonitorAggregateFunctionsRequest()
	}
	response = NewDescribeMonitorAggregateFunctionsResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMonitorAggregatePeriodRequest() (request *DescribeMonitorAggregatePeriodRequest) {
	request = &DescribeMonitorAggregatePeriodRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeMonitorAggregatePeriod")
	return
}

func NewDescribeMonitorAggregatePeriodResponse() (response *DescribeMonitorAggregatePeriodResponse) {
	response = &DescribeMonitorAggregatePeriodResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取统计周期
func (c *Client) DescribeMonitorAggregatePeriod(request *DescribeMonitorAggregatePeriodRequest) (response *DescribeMonitorAggregatePeriodResponse, err error) {
	if request == nil {
		request = NewDescribeMonitorAggregatePeriodRequest()
	}
	response = NewDescribeMonitorAggregatePeriodResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMonitorAlarmCallbackURLsRequest() (request *DescribeMonitorAlarmCallbackURLsRequest) {
	request = &DescribeMonitorAlarmCallbackURLsRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeMonitorAlarmCallbackURLs")
	return
}

func NewDescribeMonitorAlarmCallbackURLsResponse() (response *DescribeMonitorAlarmCallbackURLsResponse) {
	response = &DescribeMonitorAlarmCallbackURLsResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 根据 SpaceUUID 获取空间下所有已创建的告警的回调URL
func (c *Client) DescribeMonitorAlarmCallbackURLs(request *DescribeMonitorAlarmCallbackURLsRequest) (response *DescribeMonitorAlarmCallbackURLsResponse, err error) {
	if request == nil {
		request = NewDescribeMonitorAlarmCallbackURLsRequest()
	}
	response = NewDescribeMonitorAlarmCallbackURLsResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMonitorAlarmConfirmRequest() (request *DescribeMonitorAlarmConfirmRequest) {
	request = &DescribeMonitorAlarmConfirmRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeMonitorAlarmConfirm")
	return
}

func NewDescribeMonitorAlarmConfirmResponse() (response *DescribeMonitorAlarmConfirmResponse) {
	response = &DescribeMonitorAlarmConfirmResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取告警确认信息
func (c *Client) DescribeMonitorAlarmConfirm(request *DescribeMonitorAlarmConfirmRequest) (response *DescribeMonitorAlarmConfirmResponse, err error) {
	if request == nil {
		request = NewDescribeMonitorAlarmConfirmRequest()
	}
	response = NewDescribeMonitorAlarmConfirmResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMonitorAlarmDetailRequest() (request *DescribeMonitorAlarmDetailRequest) {
	request = &DescribeMonitorAlarmDetailRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeMonitorAlarmDetail")
	return
}

func NewDescribeMonitorAlarmDetailResponse() (response *DescribeMonitorAlarmDetailResponse) {
	response = &DescribeMonitorAlarmDetailResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取告警历史详情
func (c *Client) DescribeMonitorAlarmDetail(request *DescribeMonitorAlarmDetailRequest) (response *DescribeMonitorAlarmDetailResponse, err error) {
	if request == nil {
		request = NewDescribeMonitorAlarmDetailRequest()
	}
	response = NewDescribeMonitorAlarmDetailResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMonitorAlarmHistoryRequest() (request *DescribeMonitorAlarmHistoryRequest) {
	request = &DescribeMonitorAlarmHistoryRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeMonitorAlarmHistory")
	return
}

func NewDescribeMonitorAlarmHistoryResponse() (response *DescribeMonitorAlarmHistoryResponse) {
	response = &DescribeMonitorAlarmHistoryResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取云监控告警历史列表
func (c *Client) DescribeMonitorAlarmHistory(request *DescribeMonitorAlarmHistoryRequest) (response *DescribeMonitorAlarmHistoryResponse, err error) {
	if request == nil {
		request = NewDescribeMonitorAlarmHistoryRequest()
	}
	response = NewDescribeMonitorAlarmHistoryResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMonitorAlarmLogRequest() (request *DescribeMonitorAlarmLogRequest) {
	request = &DescribeMonitorAlarmLogRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeMonitorAlarmLog")
	return
}

func NewDescribeMonitorAlarmLogResponse() (response *DescribeMonitorAlarmLogResponse) {
	response = &DescribeMonitorAlarmLogResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取告警日志
func (c *Client) DescribeMonitorAlarmLog(request *DescribeMonitorAlarmLogRequest) (response *DescribeMonitorAlarmLogResponse, err error) {
	if request == nil {
		request = NewDescribeMonitorAlarmLogRequest()
	}
	response = NewDescribeMonitorAlarmLogResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMonitorAlarmPoliciesOfNoticeRuleRequest() (request *DescribeMonitorAlarmPoliciesOfNoticeRuleRequest) {
	request = &DescribeMonitorAlarmPoliciesOfNoticeRuleRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeMonitorAlarmPoliciesOfNoticeRule")
	return
}

func NewDescribeMonitorAlarmPoliciesOfNoticeRuleResponse() (response *DescribeMonitorAlarmPoliciesOfNoticeRuleResponse) {
	response = &DescribeMonitorAlarmPoliciesOfNoticeRuleResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 通过通知规则uuid查询它关联的告警策略
func (c *Client) DescribeMonitorAlarmPoliciesOfNoticeRule(request *DescribeMonitorAlarmPoliciesOfNoticeRuleRequest) (response *DescribeMonitorAlarmPoliciesOfNoticeRuleResponse, err error) {
	if request == nil {
		request = NewDescribeMonitorAlarmPoliciesOfNoticeRuleRequest()
	}
	response = NewDescribeMonitorAlarmPoliciesOfNoticeRuleResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMonitorAlarmPolicyDetailRequest() (request *DescribeMonitorAlarmPolicyDetailRequest) {
	request = &DescribeMonitorAlarmPolicyDetailRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeMonitorAlarmPolicyDetail")
	return
}

func NewDescribeMonitorAlarmPolicyDetailResponse() (response *DescribeMonitorAlarmPolicyDetailResponse) {
	response = &DescribeMonitorAlarmPolicyDetailResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 告警策略详情
func (c *Client) DescribeMonitorAlarmPolicyDetail(request *DescribeMonitorAlarmPolicyDetailRequest) (response *DescribeMonitorAlarmPolicyDetailResponse, err error) {
	if request == nil {
		request = NewDescribeMonitorAlarmPolicyDetailRequest()
	}
	response = NewDescribeMonitorAlarmPolicyDetailResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMonitorAlarmPolicyListRequest() (request *DescribeMonitorAlarmPolicyListRequest) {
	request = &DescribeMonitorAlarmPolicyListRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeMonitorAlarmPolicyList")
	return
}

func NewDescribeMonitorAlarmPolicyListResponse() (response *DescribeMonitorAlarmPolicyListResponse) {
	response = &DescribeMonitorAlarmPolicyListResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 根据告警策略ID查询列表
func (c *Client) DescribeMonitorAlarmPolicyList(request *DescribeMonitorAlarmPolicyListRequest) (response *DescribeMonitorAlarmPolicyListResponse, err error) {
	if request == nil {
		request = NewDescribeMonitorAlarmPolicyListRequest()
	}
	response = NewDescribeMonitorAlarmPolicyListResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMonitorAlarmPolicyRemainingCountRequest() (request *DescribeMonitorAlarmPolicyRemainingCountRequest) {
	request = &DescribeMonitorAlarmPolicyRemainingCountRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeMonitorAlarmPolicyRemainingCount")
	return
}

func NewDescribeMonitorAlarmPolicyRemainingCountResponse() (response *DescribeMonitorAlarmPolicyRemainingCountResponse) {
	response = &DescribeMonitorAlarmPolicyRemainingCountResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 告警策略（应用空间下）剩余条数
func (c *Client) DescribeMonitorAlarmPolicyRemainingCount(request *DescribeMonitorAlarmPolicyRemainingCountRequest) (response *DescribeMonitorAlarmPolicyRemainingCountResponse, err error) {
	if request == nil {
		request = NewDescribeMonitorAlarmPolicyRemainingCountRequest()
	}
	response = NewDescribeMonitorAlarmPolicyRemainingCountResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMonitorAlarmSmsQuotaRequest() (request *DescribeMonitorAlarmSmsQuotaRequest) {
	request = &DescribeMonitorAlarmSmsQuotaRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeMonitorAlarmSmsQuota")
	return
}

func NewDescribeMonitorAlarmSmsQuotaResponse() (response *DescribeMonitorAlarmSmsQuotaResponse) {
	response = &DescribeMonitorAlarmSmsQuotaResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取云监控告警短信配额
func (c *Client) DescribeMonitorAlarmSmsQuota(request *DescribeMonitorAlarmSmsQuotaRequest) (response *DescribeMonitorAlarmSmsQuotaResponse, err error) {
	if request == nil {
		request = NewDescribeMonitorAlarmSmsQuotaRequest()
	}
	response = NewDescribeMonitorAlarmSmsQuotaResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMonitorAlarmStatementRequest() (request *DescribeMonitorAlarmStatementRequest) {
	request = &DescribeMonitorAlarmStatementRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeMonitorAlarmStatement")
	return
}

func NewDescribeMonitorAlarmStatementResponse() (response *DescribeMonitorAlarmStatementResponse) {
	response = &DescribeMonitorAlarmStatementResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取告警评论列表
func (c *Client) DescribeMonitorAlarmStatement(request *DescribeMonitorAlarmStatementRequest) (response *DescribeMonitorAlarmStatementResponse, err error) {
	if request == nil {
		request = NewDescribeMonitorAlarmStatementRequest()
	}
	response = NewDescribeMonitorAlarmStatementResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMonitorComparedTimeRangesRequest() (request *DescribeMonitorComparedTimeRangesRequest) {
	request = &DescribeMonitorComparedTimeRangesRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeMonitorComparedTimeRanges")
	return
}

func NewDescribeMonitorComparedTimeRangesResponse() (response *DescribeMonitorComparedTimeRangesResponse) {
	response = &DescribeMonitorComparedTimeRangesResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取时间对比选项
func (c *Client) DescribeMonitorComparedTimeRanges(request *DescribeMonitorComparedTimeRangesRequest) (response *DescribeMonitorComparedTimeRangesResponse, err error) {
	if request == nil {
		request = NewDescribeMonitorComparedTimeRangesRequest()
	}
	response = NewDescribeMonitorComparedTimeRangesResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMonitorConditionsRequest() (request *DescribeMonitorConditionsRequest) {
	request = &DescribeMonitorConditionsRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeMonitorConditions")
	return
}

func NewDescribeMonitorConditionsResponse() (response *DescribeMonitorConditionsResponse) {
	response = &DescribeMonitorConditionsResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 拉取指标下的条件key（展开可用）
func (c *Client) DescribeMonitorConditions(request *DescribeMonitorConditionsRequest) (response *DescribeMonitorConditionsResponse, err error) {
	if request == nil {
		request = NewDescribeMonitorConditionsRequest()
	}
	response = NewDescribeMonitorConditionsResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMonitorDashboardRequest() (request *DescribeMonitorDashboardRequest) {
	request = &DescribeMonitorDashboardRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeMonitorDashboard")
	return
}

func NewDescribeMonitorDashboardResponse() (response *DescribeMonitorDashboardResponse) {
	response = &DescribeMonitorDashboardResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取dashboard详情。返回格式同更新dashboard的入参格式
func (c *Client) DescribeMonitorDashboard(request *DescribeMonitorDashboardRequest) (response *DescribeMonitorDashboardResponse, err error) {
	if request == nil {
		request = NewDescribeMonitorDashboardRequest()
	}
	response = NewDescribeMonitorDashboardResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMonitorDashboardDimensionKeysRequest() (request *DescribeMonitorDashboardDimensionKeysRequest) {
	request = &DescribeMonitorDashboardDimensionKeysRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeMonitorDashboardDimensionKeys")
	return
}

func NewDescribeMonitorDashboardDimensionKeysResponse() (response *DescribeMonitorDashboardDimensionKeysResponse) {
	response = &DescribeMonitorDashboardDimensionKeysResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 根据DashboardUUID查询维度Key
func (c *Client) DescribeMonitorDashboardDimensionKeys(request *DescribeMonitorDashboardDimensionKeysRequest) (response *DescribeMonitorDashboardDimensionKeysResponse, err error) {
	if request == nil {
		request = NewDescribeMonitorDashboardDimensionKeysRequest()
	}
	response = NewDescribeMonitorDashboardDimensionKeysResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMonitorDashboardDimensionValuesRequest() (request *DescribeMonitorDashboardDimensionValuesRequest) {
	request = &DescribeMonitorDashboardDimensionValuesRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeMonitorDashboardDimensionValues")
	return
}

func NewDescribeMonitorDashboardDimensionValuesResponse() (response *DescribeMonitorDashboardDimensionValuesResponse) {
	response = &DescribeMonitorDashboardDimensionValuesResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 根据DashboardUUID查询维度Value
func (c *Client) DescribeMonitorDashboardDimensionValues(request *DescribeMonitorDashboardDimensionValuesRequest) (response *DescribeMonitorDashboardDimensionValuesResponse, err error) {
	if request == nil {
		request = NewDescribeMonitorDashboardDimensionValuesRequest()
	}
	response = NewDescribeMonitorDashboardDimensionValuesResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMonitorDashboardsRequest() (request *DescribeMonitorDashboardsRequest) {
	request = &DescribeMonitorDashboardsRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeMonitorDashboards")
	return
}

func NewDescribeMonitorDashboardsResponse() (response *DescribeMonitorDashboardsResponse) {
	response = &DescribeMonitorDashboardsResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取dashboard列表&搜索
func (c *Client) DescribeMonitorDashboards(request *DescribeMonitorDashboardsRequest) (response *DescribeMonitorDashboardsResponse, err error) {
	if request == nil {
		request = NewDescribeMonitorDashboardsRequest()
	}
	response = NewDescribeMonitorDashboardsResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMonitorDataRequest() (request *DescribeMonitorDataRequest) {
	request = &DescribeMonitorDataRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeMonitorData")
	return
}

func NewDescribeMonitorDataResponse() (response *DescribeMonitorDataResponse) {
	response = &DescribeMonitorDataResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 无
func (c *Client) DescribeMonitorData(request *DescribeMonitorDataRequest) (response *DescribeMonitorDataResponse, err error) {
	if request == nil {
		request = NewDescribeMonitorDataRequest()
	}
	response = NewDescribeMonitorDataResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMonitorDataByAlarmIDRequest() (request *DescribeMonitorDataByAlarmIDRequest) {
	request = &DescribeMonitorDataByAlarmIDRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeMonitorDataByAlarmID")
	return
}

func NewDescribeMonitorDataByAlarmIDResponse() (response *DescribeMonitorDataByAlarmIDResponse) {
	response = &DescribeMonitorDataByAlarmIDResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 根据告警ID获取监控数据
func (c *Client) DescribeMonitorDataByAlarmID(request *DescribeMonitorDataByAlarmIDRequest) (response *DescribeMonitorDataByAlarmIDResponse, err error) {
	if request == nil {
		request = NewDescribeMonitorDataByAlarmIDRequest()
	}
	response = NewDescribeMonitorDataByAlarmIDResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMonitorDimensionKeysRequest() (request *DescribeMonitorDimensionKeysRequest) {
	request = &DescribeMonitorDimensionKeysRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeMonitorDimensionKeys")
	return
}

func NewDescribeMonitorDimensionKeysResponse() (response *DescribeMonitorDimensionKeysResponse) {
	response = &DescribeMonitorDimensionKeysResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 拉取中台维度Key列表
func (c *Client) DescribeMonitorDimensionKeys(request *DescribeMonitorDimensionKeysRequest) (response *DescribeMonitorDimensionKeysResponse, err error) {
	if request == nil {
		request = NewDescribeMonitorDimensionKeysRequest()
	}
	response = NewDescribeMonitorDimensionKeysResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMonitorDimensionValuesRequest() (request *DescribeMonitorDimensionValuesRequest) {
	request = &DescribeMonitorDimensionValuesRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeMonitorDimensionValues")
	return
}

func NewDescribeMonitorDimensionValuesResponse() (response *DescribeMonitorDimensionValuesResponse) {
	response = &DescribeMonitorDimensionValuesResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 拉去中台维度Value列表
func (c *Client) DescribeMonitorDimensionValues(request *DescribeMonitorDimensionValuesRequest) (response *DescribeMonitorDimensionValuesResponse, err error) {
	if request == nil {
		request = NewDescribeMonitorDimensionValuesRequest()
	}
	response = NewDescribeMonitorDimensionValuesResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMonitorMetricDataRequest() (request *DescribeMonitorMetricDataRequest) {
	request = &DescribeMonitorMetricDataRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeMonitorMetricData")
	return
}

func NewDescribeMonitorMetricDataResponse() (response *DescribeMonitorMetricDataResponse) {
	response = &DescribeMonitorMetricDataResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 此接口不用，拉dashboard视图数据和拉指标数据统一使用DescribeMonitorViewData
func (c *Client) DescribeMonitorMetricData(request *DescribeMonitorMetricDataRequest) (response *DescribeMonitorMetricDataResponse, err error) {
	if request == nil {
		request = NewDescribeMonitorMetricDataRequest()
	}
	response = NewDescribeMonitorMetricDataResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMonitorNamespaceMetricsRequest() (request *DescribeMonitorNamespaceMetricsRequest) {
	request = &DescribeMonitorNamespaceMetricsRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeMonitorNamespaceMetrics")
	return
}

func NewDescribeMonitorNamespaceMetricsResponse() (response *DescribeMonitorNamespaceMetricsResponse) {
	response = &DescribeMonitorNamespaceMetricsResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 拉取自定义上报命名空间下的指标
func (c *Client) DescribeMonitorNamespaceMetrics(request *DescribeMonitorNamespaceMetricsRequest) (response *DescribeMonitorNamespaceMetricsResponse, err error) {
	if request == nil {
		request = NewDescribeMonitorNamespaceMetricsRequest()
	}
	response = NewDescribeMonitorNamespaceMetricsResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMonitorNamespacesRequest() (request *DescribeMonitorNamespacesRequest) {
	request = &DescribeMonitorNamespacesRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeMonitorNamespaces")
	return
}

func NewDescribeMonitorNamespacesResponse() (response *DescribeMonitorNamespacesResponse) {
	response = &DescribeMonitorNamespacesResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 拉取自定义上报的命名空间
func (c *Client) DescribeMonitorNamespaces(request *DescribeMonitorNamespacesRequest) (response *DescribeMonitorNamespacesResponse, err error) {
	if request == nil {
		request = NewDescribeMonitorNamespacesRequest()
	}
	response = NewDescribeMonitorNamespacesResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMonitorNoticeRuleRequest() (request *DescribeMonitorNoticeRuleRequest) {
	request = &DescribeMonitorNoticeRuleRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeMonitorNoticeRule")
	return
}

func NewDescribeMonitorNoticeRuleResponse() (response *DescribeMonitorNoticeRuleResponse) {
	response = &DescribeMonitorNoticeRuleResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 根据通知规则 uuid 获取详情
func (c *Client) DescribeMonitorNoticeRule(request *DescribeMonitorNoticeRuleRequest) (response *DescribeMonitorNoticeRuleResponse, err error) {
	if request == nil {
		request = NewDescribeMonitorNoticeRuleRequest()
	}
	response = NewDescribeMonitorNoticeRuleResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMonitorNoticeRulesRequest() (request *DescribeMonitorNoticeRulesRequest) {
	request = &DescribeMonitorNoticeRulesRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeMonitorNoticeRules")
	return
}

func NewDescribeMonitorNoticeRulesResponse() (response *DescribeMonitorNoticeRulesResponse) {
	response = &DescribeMonitorNoticeRulesResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 通过查询条件、分页参数查询通知规则列表
func (c *Client) DescribeMonitorNoticeRules(request *DescribeMonitorNoticeRulesRequest) (response *DescribeMonitorNoticeRulesResponse, err error) {
	if request == nil {
		request = NewDescribeMonitorNoticeRulesRequest()
	}
	response = NewDescribeMonitorNoticeRulesResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMonitorOperatorsRequest() (request *DescribeMonitorOperatorsRequest) {
	request = &DescribeMonitorOperatorsRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeMonitorOperators")
	return
}

func NewDescribeMonitorOperatorsResponse() (response *DescribeMonitorOperatorsResponse) {
	response = &DescribeMonitorOperatorsResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取操作符
func (c *Client) DescribeMonitorOperators(request *DescribeMonitorOperatorsRequest) (response *DescribeMonitorOperatorsResponse, err error) {
	if request == nil {
		request = NewDescribeMonitorOperatorsRequest()
	}
	response = NewDescribeMonitorOperatorsResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMonitorProductByIdsRequest() (request *DescribeMonitorProductByIdsRequest) {
	request = &DescribeMonitorProductByIdsRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeMonitorProductByIds")
	return
}

func NewDescribeMonitorProductByIdsResponse() (response *DescribeMonitorProductByIdsResponse) {
	response = &DescribeMonitorProductByIdsResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 按照Id查询监控产品列表
func (c *Client) DescribeMonitorProductByIds(request *DescribeMonitorProductByIdsRequest) (response *DescribeMonitorProductByIdsResponse, err error) {
	if request == nil {
		request = NewDescribeMonitorProductByIdsRequest()
	}
	response = NewDescribeMonitorProductByIdsResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMonitorProductsRequest() (request *DescribeMonitorProductsRequest) {
	request = &DescribeMonitorProductsRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeMonitorProducts")
	return
}

func NewDescribeMonitorProductsResponse() (response *DescribeMonitorProductsResponse) {
	response = &DescribeMonitorProductsResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 查询监控产品列表
func (c *Client) DescribeMonitorProducts(request *DescribeMonitorProductsRequest) (response *DescribeMonitorProductsResponse, err error) {
	if request == nil {
		request = NewDescribeMonitorProductsRequest()
	}
	response = NewDescribeMonitorProductsResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMonitorRefreshIntervalsRequest() (request *DescribeMonitorRefreshIntervalsRequest) {
	request = &DescribeMonitorRefreshIntervalsRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeMonitorRefreshIntervals")
	return
}

func NewDescribeMonitorRefreshIntervalsResponse() (response *DescribeMonitorRefreshIntervalsResponse) {
	response = &DescribeMonitorRefreshIntervalsResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取刷新间隔
func (c *Client) DescribeMonitorRefreshIntervals(request *DescribeMonitorRefreshIntervalsRequest) (response *DescribeMonitorRefreshIntervalsResponse, err error) {
	if request == nil {
		request = NewDescribeMonitorRefreshIntervalsRequest()
	}
	response = NewDescribeMonitorRefreshIntervalsResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMonitorRegionsRequest() (request *DescribeMonitorRegionsRequest) {
	request = &DescribeMonitorRegionsRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeMonitorRegions")
	return
}

func NewDescribeMonitorRegionsResponse() (response *DescribeMonitorRegionsResponse) {
	response = &DescribeMonitorRegionsResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 拉取地域列表
func (c *Client) DescribeMonitorRegions(request *DescribeMonitorRegionsRequest) (response *DescribeMonitorRegionsResponse, err error) {
	if request == nil {
		request = NewDescribeMonitorRegionsRequest()
	}
	response = NewDescribeMonitorRegionsResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMonitorSpacesRequest() (request *DescribeMonitorSpacesRequest) {
	request = &DescribeMonitorSpacesRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeMonitorSpaces")
	return
}

func NewDescribeMonitorSpacesResponse() (response *DescribeMonitorSpacesResponse) {
	response = &DescribeMonitorSpacesResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 根据 AppId 拉取应用空间列表
func (c *Client) DescribeMonitorSpaces(request *DescribeMonitorSpacesRequest) (response *DescribeMonitorSpacesResponse, err error) {
	if request == nil {
		request = NewDescribeMonitorSpacesRequest()
	}
	response = NewDescribeMonitorSpacesResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMonitorTimeRangesRequest() (request *DescribeMonitorTimeRangesRequest) {
	request = &DescribeMonitorTimeRangesRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeMonitorTimeRanges")
	return
}

func NewDescribeMonitorTimeRangesResponse() (response *DescribeMonitorTimeRangesResponse) {
	response = &DescribeMonitorTimeRangesResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取时间范围选项
func (c *Client) DescribeMonitorTimeRanges(request *DescribeMonitorTimeRangesRequest) (response *DescribeMonitorTimeRangesResponse, err error) {
	if request == nil {
		request = NewDescribeMonitorTimeRangesRequest()
	}
	response = NewDescribeMonitorTimeRangesResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMonitorViewDataRequest() (request *DescribeMonitorViewDataRequest) {
	request = &DescribeMonitorViewDataRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeMonitorViewData")
	return
}

func NewDescribeMonitorViewDataResponse() (response *DescribeMonitorViewDataResponse) {
	response = &DescribeMonitorViewDataResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取视图数据。指标分析可调用此接口
func (c *Client) DescribeMonitorViewData(request *DescribeMonitorViewDataRequest) (response *DescribeMonitorViewDataResponse, err error) {
	if request == nil {
		request = NewDescribeMonitorViewDataRequest()
	}
	response = NewDescribeMonitorViewDataResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMsgPolicyInfoRequest() (request *DescribeMsgPolicyInfoRequest) {
	request = &DescribeMsgPolicyInfoRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeMsgPolicyInfo")
	return
}

func NewDescribeMsgPolicyInfoResponse() (response *DescribeMsgPolicyInfoResponse) {
	response = &DescribeMsgPolicyInfoResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取自定义消息策略信息
func (c *Client) DescribeMsgPolicyInfo(request *DescribeMsgPolicyInfoRequest) (response *DescribeMsgPolicyInfoResponse, err error) {
	if request == nil {
		request = NewDescribeMsgPolicyInfoRequest()
	}
	response = NewDescribeMsgPolicyInfoResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMsgPolicyListRequest() (request *DescribeMsgPolicyListRequest) {
	request = &DescribeMsgPolicyListRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeMsgPolicyList")
	return
}

func NewDescribeMsgPolicyListResponse() (response *DescribeMsgPolicyListResponse) {
	response = &DescribeMsgPolicyListResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取自定义消息列表
func (c *Client) DescribeMsgPolicyList(request *DescribeMsgPolicyListRequest) (response *DescribeMsgPolicyListResponse, err error) {
	if request == nil {
		request = NewDescribeMsgPolicyListRequest()
	}
	response = NewDescribeMsgPolicyListResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeNamespaceListRequest() (request *DescribeNamespaceListRequest) {
	request = &DescribeNamespaceListRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeNamespaceList")
	return
}

func NewDescribeNamespaceListResponse() (response *DescribeNamespaceListResponse) {
	response = &DescribeNamespaceListResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 查询namespace列表
func (c *Client) DescribeNamespaceList(request *DescribeNamespaceListRequest) (response *DescribeNamespaceListResponse, err error) {
	if request == nil {
		request = NewDescribeNamespaceListRequest()
	}
	response = NewDescribeNamespaceListResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeNamespacesRequest() (request *DescribeNamespacesRequest) {
	request = &DescribeNamespacesRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeNamespaces")
	return
}

func NewDescribeNamespacesResponse() (response *DescribeNamespacesResponse) {
	response = &DescribeNamespacesResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 查询namespace列表
func (c *Client) DescribeNamespaces(request *DescribeNamespacesRequest) (response *DescribeNamespacesResponse, err error) {
	if request == nil {
		request = NewDescribeNamespacesRequest()
	}
	response = NewDescribeNamespacesResponse()
	err = c.Send(request, response)
	return
}

func NewDescribePolicyConditionListRequest() (request *DescribePolicyConditionListRequest) {
	request = &DescribePolicyConditionListRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribePolicyConditionList")
	return
}

func NewDescribePolicyConditionListResponse() (response *DescribePolicyConditionListResponse) {
	response = &DescribePolicyConditionListResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取基础告警策略条件
func (c *Client) DescribePolicyConditionList(request *DescribePolicyConditionListRequest) (response *DescribePolicyConditionListResponse, err error) {
	if request == nil {
		request = NewDescribePolicyConditionListRequest()
	}
	response = NewDescribePolicyConditionListResponse()
	err = c.Send(request, response)
	return
}

func NewDescribePolicyGroupCountRequest() (request *DescribePolicyGroupCountRequest) {
	request = &DescribePolicyGroupCountRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribePolicyGroupCount")
	return
}

func NewDescribePolicyGroupCountResponse() (response *DescribePolicyGroupCountResponse) {
	response = &DescribePolicyGroupCountResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 查询对象列表绑定的告警策略组个数
func (c *Client) DescribePolicyGroupCount(request *DescribePolicyGroupCountRequest) (response *DescribePolicyGroupCountResponse, err error) {
	if request == nil {
		request = NewDescribePolicyGroupCountRequest()
	}
	response = NewDescribePolicyGroupCountResponse()
	err = c.Send(request, response)
	return
}

func NewDescribePolicyGroupInfoRequest() (request *DescribePolicyGroupInfoRequest) {
	request = &DescribePolicyGroupInfoRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribePolicyGroupInfo")
	return
}

func NewDescribePolicyGroupInfoResponse() (response *DescribePolicyGroupInfoResponse) {
	response = &DescribePolicyGroupInfoResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取基础策略组详情
func (c *Client) DescribePolicyGroupInfo(request *DescribePolicyGroupInfoRequest) (response *DescribePolicyGroupInfoResponse, err error) {
	if request == nil {
		request = NewDescribePolicyGroupInfoRequest()
	}
	response = NewDescribePolicyGroupInfoResponse()
	err = c.Send(request, response)
	return
}

func NewDescribePolicyGroupListRequest() (request *DescribePolicyGroupListRequest) {
	request = &DescribePolicyGroupListRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribePolicyGroupList")
	return
}

func NewDescribePolicyGroupListResponse() (response *DescribePolicyGroupListResponse) {
	response = &DescribePolicyGroupListResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取基础策略告警组列表
func (c *Client) DescribePolicyGroupList(request *DescribePolicyGroupListRequest) (response *DescribePolicyGroupListResponse, err error) {
	if request == nil {
		request = NewDescribePolicyGroupListRequest()
	}
	response = NewDescribePolicyGroupListResponse()
	err = c.Send(request, response)
	return
}

func NewDescribePolicyInfoByInstanceRequest() (request *DescribePolicyInfoByInstanceRequest) {
	request = &DescribePolicyInfoByInstanceRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribePolicyInfoByInstance")
	return
}

func NewDescribePolicyInfoByInstanceResponse() (response *DescribePolicyInfoByInstanceResponse) {
	response = &DescribePolicyInfoByInstanceResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 通过实例获取策略信息
func (c *Client) DescribePolicyInfoByInstance(request *DescribePolicyInfoByInstanceRequest) (response *DescribePolicyInfoByInstanceResponse, err error) {
	if request == nil {
		request = NewDescribePolicyInfoByInstanceRequest()
	}
	response = NewDescribePolicyInfoByInstanceResponse()
	err = c.Send(request, response)
	return
}

func NewDescribePolicyObjectCountRequest() (request *DescribePolicyObjectCountRequest) {
	request = &DescribePolicyObjectCountRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribePolicyObjectCount")
	return
}

func NewDescribePolicyObjectCountResponse() (response *DescribePolicyObjectCountResponse) {
	response = &DescribePolicyObjectCountResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 查询策略组在每个地域下面绑定的对象数统计
func (c *Client) DescribePolicyObjectCount(request *DescribePolicyObjectCountRequest) (response *DescribePolicyObjectCountResponse, err error) {
	if request == nil {
		request = NewDescribePolicyObjectCountRequest()
	}
	response = NewDescribePolicyObjectCountResponse()
	err = c.Send(request, response)
	return
}

func NewDescribePolicyObjectCountTestRequest() (request *DescribePolicyObjectCountTestRequest) {
	request = &DescribePolicyObjectCountTestRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribePolicyObjectCountTest")
	return
}

func NewDescribePolicyObjectCountTestResponse() (response *DescribePolicyObjectCountTestResponse) {
	response = &DescribePolicyObjectCountTestResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取各地区告警策略个数
func (c *Client) DescribePolicyObjectCountTest(request *DescribePolicyObjectCountTestRequest) (response *DescribePolicyObjectCountTestResponse, err error) {
	if request == nil {
		request = NewDescribePolicyObjectCountTestRequest()
	}
	response = NewDescribePolicyObjectCountTestResponse()
	err = c.Send(request, response)
	return
}

func NewDescribePolicyQuotaRequest() (request *DescribePolicyQuotaRequest) {
	request = &DescribePolicyQuotaRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribePolicyQuota")
	return
}

func NewDescribePolicyQuotaResponse() (response *DescribePolicyQuotaResponse) {
	response = &DescribePolicyQuotaResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取策略配额
func (c *Client) DescribePolicyQuota(request *DescribePolicyQuotaRequest) (response *DescribePolicyQuotaResponse, err error) {
	if request == nil {
		request = NewDescribePolicyQuotaRequest()
	}
	response = NewDescribePolicyQuotaResponse()
	err = c.Send(request, response)
	return
}

func NewDescribePolicySituationRequest() (request *DescribePolicySituationRequest) {
	request = &DescribePolicySituationRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribePolicySituation")
	return
}

func NewDescribePolicySituationResponse() (response *DescribePolicySituationResponse) {
	response = &DescribePolicySituationResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 拉取告警策略统计情况
func (c *Client) DescribePolicySituation(request *DescribePolicySituationRequest) (response *DescribePolicySituationResponse, err error) {
	if request == nil {
		request = NewDescribePolicySituationRequest()
	}
	response = NewDescribePolicySituationResponse()
	err = c.Send(request, response)
	return
}

func NewDescribePolicyUseListRequest() (request *DescribePolicyUseListRequest) {
	request = &DescribePolicyUseListRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribePolicyUseList")
	return
}

func NewDescribePolicyUseListResponse() (response *DescribePolicyUseListResponse) {
	response = &DescribePolicyUseListResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取已关联对象列表
func (c *Client) DescribePolicyUseList(request *DescribePolicyUseListRequest) (response *DescribePolicyUseListResponse, err error) {
	if request == nil {
		request = NewDescribePolicyUseListRequest()
	}
	response = NewDescribePolicyUseListResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeProductEventListRequest() (request *DescribeProductEventListRequest) {
	request = &DescribeProductEventListRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeProductEventList")
	return
}

func NewDescribeProductEventListResponse() (response *DescribeProductEventListResponse) {
	response = &DescribeProductEventListResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 分页获取产品事件的列表
func (c *Client) DescribeProductEventList(request *DescribeProductEventListRequest) (response *DescribeProductEventListResponse, err error) {
	if request == nil {
		request = NewDescribeProductEventListRequest()
	}
	response = NewDescribeProductEventListResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeProductHealthStatusListRequest() (request *DescribeProductHealthStatusListRequest) {
	request = &DescribeProductHealthStatusListRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeProductHealthStatusList")
	return
}

func NewDescribeProductHealthStatusListResponse() (response *DescribeProductHealthStatusListResponse) {
	response = &DescribeProductHealthStatusListResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 拉取每个业务近24小时健康情况统计
func (c *Client) DescribeProductHealthStatusList(request *DescribeProductHealthStatusListRequest) (response *DescribeProductHealthStatusListResponse, err error) {
	if request == nil {
		request = NewDescribeProductHealthStatusListRequest()
	}
	response = NewDescribeProductHealthStatusListResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeProductListRequest() (request *DescribeProductListRequest) {
	request = &DescribeProductListRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeProductList")
	return
}

func NewDescribeProductListResponse() (response *DescribeProductListResponse) {
	response = &DescribeProductListResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 查询云监控产品列表
func (c *Client) DescribeProductList(request *DescribeProductListRequest) (response *DescribeProductListResponse, err error) {
	if request == nil {
		request = NewDescribeProductListRequest()
	}
	response = NewDescribeProductListResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeProjectsListRequest() (request *DescribeProjectsListRequest) {
	request = &DescribeProjectsListRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeProjectsList")
	return
}

func NewDescribeProjectsListResponse() (response *DescribeProjectsListResponse) {
	response = &DescribeProjectsListResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获得项目列表
func (c *Client) DescribeProjectsList(request *DescribeProjectsListRequest) (response *DescribeProjectsListResponse, err error) {
	if request == nil {
		request = NewDescribeProjectsListRequest()
	}
	response = NewDescribeProjectsListResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeRecommendedTemplateRequest() (request *DescribeRecommendedTemplateRequest) {
	request = &DescribeRecommendedTemplateRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeRecommendedTemplate")
	return
}

func NewDescribeRecommendedTemplateResponse() (response *DescribeRecommendedTemplateResponse) {
	response = &DescribeRecommendedTemplateResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取默认推荐告警策略模板列表
func (c *Client) DescribeRecommendedTemplate(request *DescribeRecommendedTemplateRequest) (response *DescribeRecommendedTemplateResponse, err error) {
	if request == nil {
		request = NewDescribeRecommendedTemplateRequest()
	}
	response = NewDescribeRecommendedTemplateResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeRegionsRequest() (request *DescribeRegionsRequest) {
	request = &DescribeRegionsRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeRegions")
	return
}

func NewDescribeRegionsResponse() (response *DescribeRegionsResponse) {
	response = &DescribeRegionsResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 查询有数据的区域
func (c *Client) DescribeRegions(request *DescribeRegionsRequest) (response *DescribeRegionsResponse, err error) {
	if request == nil {
		request = NewDescribeRegionsRequest()
	}
	response = NewDescribeRegionsResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeServerAttributesRequest() (request *DescribeServerAttributesRequest) {
	request = &DescribeServerAttributesRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeServerAttributes")
	return
}

func NewDescribeServerAttributesResponse() (response *DescribeServerAttributesResponse) {
	response = &DescribeServerAttributesResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 批量查询服务器上报的指标
func (c *Client) DescribeServerAttributes(request *DescribeServerAttributesRequest) (response *DescribeServerAttributesResponse, err error) {
	if request == nil {
		request = NewDescribeServerAttributesRequest()
	}
	response = NewDescribeServerAttributesResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeServerDataRequest() (request *DescribeServerDataRequest) {
	request = &DescribeServerDataRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeServerData")
	return
}

func NewDescribeServerDataResponse() (response *DescribeServerDataResponse) {
	response = &DescribeServerDataResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 查询单机数据
func (c *Client) DescribeServerData(request *DescribeServerDataRequest) (response *DescribeServerDataResponse, err error) {
	if request == nil {
		request = NewDescribeServerDataRequest()
	}
	response = NewDescribeServerDataResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeServerDatasRequest() (request *DescribeServerDatasRequest) {
	request = &DescribeServerDatasRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeServerDatas")
	return
}

func NewDescribeServerDatasResponse() (response *DescribeServerDatasResponse) {
	response = &DescribeServerDatasResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 批量查询单机数据
func (c *Client) DescribeServerDatas(request *DescribeServerDatasRequest) (response *DescribeServerDatasResponse, err error) {
	if request == nil {
		request = NewDescribeServerDatasRequest()
	}
	response = NewDescribeServerDatasResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeServersRequest() (request *DescribeServersRequest) {
	request = &DescribeServersRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeServers")
	return
}

func NewDescribeServersResponse() (response *DescribeServersResponse) {
	response = &DescribeServersResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 查询服务器
func (c *Client) DescribeServers(request *DescribeServersRequest) (response *DescribeServersResponse, err error) {
	if request == nil {
		request = NewDescribeServersRequest()
	}
	response = NewDescribeServersResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeServiceRequest() (request *DescribeServiceRequest) {
	request = &DescribeServiceRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeService")
	return
}

func NewDescribeServiceResponse() (response *DescribeServiceResponse) {
	response = &DescribeServiceResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 查询服务信息
func (c *Client) DescribeService(request *DescribeServiceRequest) (response *DescribeServiceResponse, err error) {
	if request == nil {
		request = NewDescribeServiceRequest()
	}
	response = NewDescribeServiceResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeSortObjectListRequest() (request *DescribeSortObjectListRequest) {
	request = &DescribeSortObjectListRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeSortObjectList")
	return
}

func NewDescribeSortObjectListResponse() (response *DescribeSortObjectListResponse) {
	response = &DescribeSortObjectListResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取按topN排序的某个viewname下面的对象列表
func (c *Client) DescribeSortObjectList(request *DescribeSortObjectListRequest) (response *DescribeSortObjectListResponse, err error) {
	if request == nil {
		request = NewDescribeSortObjectListRequest()
	}
	response = NewDescribeSortObjectListResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeStorageDurationRequest() (request *DescribeStorageDurationRequest) {
	request = &DescribeStorageDurationRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeStorageDuration")
	return
}

func NewDescribeStorageDurationResponse() (response *DescribeStorageDurationResponse) {
	response = &DescribeStorageDurationResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取存储周期及持续时间列表
func (c *Client) DescribeStorageDuration(request *DescribeStorageDurationRequest) (response *DescribeStorageDurationResponse, err error) {
	if request == nil {
		request = NewDescribeStorageDurationRequest()
	}
	response = NewDescribeStorageDurationResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeStrategysRequest() (request *DescribeStrategysRequest) {
	request = &DescribeStrategysRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeStrategys")
	return
}

func NewDescribeStrategysResponse() (response *DescribeStrategysResponse) {
	response = &DescribeStrategysResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 批量查询告警策略
func (c *Client) DescribeStrategys(request *DescribeStrategysRequest) (response *DescribeStrategysResponse, err error) {
	if request == nil {
		request = NewDescribeStrategysRequest()
	}
	response = NewDescribeStrategysResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeSubscribeInfoRequest() (request *DescribeSubscribeInfoRequest) {
	request = &DescribeSubscribeInfoRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeSubscribeInfo")
	return
}

func NewDescribeSubscribeInfoResponse() (response *DescribeSubscribeInfoResponse) {
	response = &DescribeSubscribeInfoResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 查询订阅告警列表
func (c *Client) DescribeSubscribeInfo(request *DescribeSubscribeInfoRequest) (response *DescribeSubscribeInfoResponse, err error) {
	if request == nil {
		request = NewDescribeSubscribeInfoRequest()
	}
	response = NewDescribeSubscribeInfoResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeTransLogRequest() (request *DescribeTransLogRequest) {
	request = &DescribeTransLogRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeTransLog")
	return
}

func NewDescribeTransLogResponse() (response *DescribeTransLogResponse) {
	response = &DescribeTransLogResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取实例组变更日志
func (c *Client) DescribeTransLog(request *DescribeTransLogRequest) (response *DescribeTransLogResponse, err error) {
	if request == nil {
		request = NewDescribeTransLogRequest()
	}
	response = NewDescribeTransLogResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeUserDashboardOptionRequest() (request *DescribeUserDashboardOptionRequest) {
	request = &DescribeUserDashboardOptionRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeUserDashboardOption")
	return
}

func NewDescribeUserDashboardOptionResponse() (response *DescribeUserDashboardOptionResponse) {
	response = &DescribeUserDashboardOptionResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取用户Dashboard行为
func (c *Client) DescribeUserDashboardOption(request *DescribeUserDashboardOptionRequest) (response *DescribeUserDashboardOptionResponse, err error) {
	if request == nil {
		request = NewDescribeUserDashboardOptionRequest()
	}
	response = NewDescribeUserDashboardOptionResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeUserInfoRequest() (request *DescribeUserInfoRequest) {
	request = &DescribeUserInfoRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeUserInfo")
	return
}

func NewDescribeUserInfoResponse() (response *DescribeUserInfoResponse) {
	response = &DescribeUserInfoResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取用户账号信息
func (c *Client) DescribeUserInfo(request *DescribeUserInfoRequest) (response *DescribeUserInfoResponse, err error) {
	if request == nil {
		request = NewDescribeUserInfoRequest()
	}
	response = NewDescribeUserInfoResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeViewDataRequest() (request *DescribeViewDataRequest) {
	request = &DescribeViewDataRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "DescribeViewData")
	return
}

func NewDescribeViewDataResponse() (response *DescribeViewDataResponse) {
	response = &DescribeViewDataResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 查询视图数据
func (c *Client) DescribeViewData(request *DescribeViewDataRequest) (response *DescribeViewDataResponse, err error) {
	if request == nil {
		request = NewDescribeViewDataRequest()
	}
	response = NewDescribeViewDataResponse()
	err = c.Send(request, response)
	return
}

func NewGetArgusMonitorDataRequest() (request *GetArgusMonitorDataRequest) {
	request = &GetArgusMonitorDataRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "GetArgusMonitorData")
	return
}

func NewGetArgusMonitorDataResponse() (response *GetArgusMonitorDataResponse) {
	response = &GetArgusMonitorDataResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取Argus监控数据
func (c *Client) GetArgusMonitorData(request *GetArgusMonitorDataRequest) (response *GetArgusMonitorDataResponse, err error) {
	if request == nil {
		request = NewGetArgusMonitorDataRequest()
	}
	response = NewGetArgusMonitorDataResponse()
	err = c.Send(request, response)
	return
}

func NewGetMonitorDataRequest() (request *GetMonitorDataRequest) {
	request = &GetMonitorDataRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "GetMonitorData")
	return
}

func NewGetMonitorDataResponse() (response *GetMonitorDataResponse) {
	response = &GetMonitorDataResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取云产品的监控数据。传入产品的命名空间、对象维度描述和监控指标即可获得相应的监控数据。
// 接口调用频率限制为：20次/秒，1200次/分钟。
// 若您需要调用的指标、对象较多，可能存在因限频出现拉取失败的情况，建议尽量将请求按时间维度均摊。
func (c *Client) GetMonitorData(request *GetMonitorDataRequest) (response *GetMonitorDataResponse, err error) {
	if request == nil {
		request = NewGetMonitorDataRequest()
	}
	response = NewGetMonitorDataResponse()
	err = c.Send(request, response)
	return
}

func NewGetTkeDataRequest() (request *GetTkeDataRequest) {
	request = &GetTkeDataRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "GetTkeData")
	return
}

func NewGetTkeDataResponse() (response *GetTkeDataResponse) {
	response = &GetTkeDataResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 拉取TKE监控数据
func (c *Client) GetTkeData(request *GetTkeDataRequest) (response *GetTkeDataResponse, err error) {
	if request == nil {
		request = NewGetTkeDataRequest()
	}
	response = NewGetTkeDataResponse()
	err = c.Send(request, response)
	return
}

func NewHelloWorldRequest() (request *HelloWorldRequest) {
	request = &HelloWorldRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "HelloWorld")
	return
}

func NewHelloWorldResponse() (response *HelloWorldResponse) {
	response = &HelloWorldResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// HelloWorld
func (c *Client) HelloWorld(request *HelloWorldRequest) (response *HelloWorldResponse, err error) {
	if request == nil {
		request = NewHelloWorldRequest()
	}
	response = NewHelloWorldResponse()
	err = c.Send(request, response)
	return
}

func NewInitGroupRequest() (request *InitGroupRequest) {
	request = &InitGroupRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "InitGroup")
	return
}

func NewInitGroupResponse() (response *InitGroupResponse) {
	response = &InitGroupResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 初始化一颗分组树
func (c *Client) InitGroup(request *InitGroupRequest) (response *InitGroupResponse, err error) {
	if request == nil {
		request = NewInitGroupRequest()
	}
	response = NewInitGroupResponse()
	err = c.Send(request, response)
	return
}

func NewModifyAlarmCallbackRequest() (request *ModifyAlarmCallbackRequest) {
	request = &ModifyAlarmCallbackRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "ModifyAlarmCallback")
	return
}

func NewModifyAlarmCallbackResponse() (response *ModifyAlarmCallbackResponse) {
	response = &ModifyAlarmCallbackResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 更新告警回调地址
func (c *Client) ModifyAlarmCallback(request *ModifyAlarmCallbackRequest) (response *ModifyAlarmCallbackResponse, err error) {
	if request == nil {
		request = NewModifyAlarmCallbackRequest()
	}
	response = NewModifyAlarmCallbackResponse()
	err = c.Send(request, response)
	return
}

func NewModifyAlarmReceiversRequest() (request *ModifyAlarmReceiversRequest) {
	request = &ModifyAlarmReceiversRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "ModifyAlarmReceivers")
	return
}

func NewModifyAlarmReceiversResponse() (response *ModifyAlarmReceiversResponse) {
	response = &ModifyAlarmReceiversResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 修改告警接收人
func (c *Client) ModifyAlarmReceivers(request *ModifyAlarmReceiversRequest) (response *ModifyAlarmReceiversResponse, err error) {
	if request == nil {
		request = NewModifyAlarmReceiversRequest()
	}
	response = NewModifyAlarmReceiversResponse()
	err = c.Send(request, response)
	return
}

func NewModifyAlertPolicyRequest() (request *ModifyAlertPolicyRequest) {
	request = &ModifyAlertPolicyRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "ModifyAlertPolicy")
	return
}

func NewModifyAlertPolicyResponse() (response *ModifyAlertPolicyResponse) {
	response = &ModifyAlertPolicyResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// ModifyAlertPolicy修改告警策略
func (c *Client) ModifyAlertPolicy(request *ModifyAlertPolicyRequest) (response *ModifyAlertPolicyResponse, err error) {
	if request == nil {
		request = NewModifyAlertPolicyRequest()
	}
	response = NewModifyAlertPolicyResponse()
	err = c.Send(request, response)
	return
}

func NewModifyAlertPolicyStatusRequest() (request *ModifyAlertPolicyStatusRequest) {
	request = &ModifyAlertPolicyStatusRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "ModifyAlertPolicyStatus")
	return
}

func NewModifyAlertPolicyStatusResponse() (response *ModifyAlertPolicyStatusResponse) {
	response = &ModifyAlertPolicyStatusResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// ModifyAlertPolicyStatus 更新告警策略状态
func (c *Client) ModifyAlertPolicyStatus(request *ModifyAlertPolicyStatusRequest) (response *ModifyAlertPolicyStatusResponse, err error) {
	if request == nil {
		request = NewModifyAlertPolicyStatusRequest()
	}
	response = NewModifyAlertPolicyStatusResponse()
	err = c.Send(request, response)
	return
}

func NewModifyAttributeRequest() (request *ModifyAttributeRequest) {
	request = &ModifyAttributeRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "ModifyAttribute")
	return
}

func NewModifyAttributeResponse() (response *ModifyAttributeResponse) {
	response = &ModifyAttributeResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 修改指标
func (c *Client) ModifyAttribute(request *ModifyAttributeRequest) (response *ModifyAttributeResponse, err error) {
	if request == nil {
		request = NewModifyAttributeRequest()
	}
	response = NewModifyAttributeResponse()
	err = c.Send(request, response)
	return
}

func NewModifyAutoScalingPolicyRequest() (request *ModifyAutoScalingPolicyRequest) {
	request = &ModifyAutoScalingPolicyRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "ModifyAutoScalingPolicy")
	return
}

func NewModifyAutoScalingPolicyResponse() (response *ModifyAutoScalingPolicyResponse) {
	response = &ModifyAutoScalingPolicyResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 绑定弹性伸缩策略
func (c *Client) ModifyAutoScalingPolicy(request *ModifyAutoScalingPolicyRequest) (response *ModifyAutoScalingPolicyResponse, err error) {
	if request == nil {
		request = NewModifyAutoScalingPolicyRequest()
	}
	response = NewModifyAutoScalingPolicyResponse()
	err = c.Send(request, response)
	return
}

func NewModifyCCMChartRequest() (request *ModifyCCMChartRequest) {
	request = &ModifyCCMChartRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "ModifyCCMChart")
	return
}

func NewModifyCCMChartResponse() (response *ModifyCCMChartResponse) {
	response = &ModifyCCMChartResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 修改分组视图自定义监控图表
func (c *Client) ModifyCCMChart(request *ModifyCCMChartRequest) (response *ModifyCCMChartResponse, err error) {
	if request == nil {
		request = NewModifyCCMChartRequest()
	}
	response = NewModifyCCMChartResponse()
	err = c.Send(request, response)
	return
}

func NewModifyCCMDashboardRequest() (request *ModifyCCMDashboardRequest) {
	request = &ModifyCCMDashboardRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "ModifyCCMDashboard")
	return
}

func NewModifyCCMDashboardResponse() (response *ModifyCCMDashboardResponse) {
	response = &ModifyCCMDashboardResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 修改自定义监控图表
func (c *Client) ModifyCCMDashboard(request *ModifyCCMDashboardRequest) (response *ModifyCCMDashboardResponse, err error) {
	if request == nil {
		request = NewModifyCCMDashboardRequest()
	}
	response = NewModifyCCMDashboardResponse()
	err = c.Send(request, response)
	return
}

func NewModifyCCMGroupStrategyRequest() (request *ModifyCCMGroupStrategyRequest) {
	request = &ModifyCCMGroupStrategyRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "ModifyCCMGroupStrategy")
	return
}

func NewModifyCCMGroupStrategyResponse() (response *ModifyCCMGroupStrategyResponse) {
	response = &ModifyCCMGroupStrategyResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 修改实例分组部分实例告警历史信息置为失效状态
func (c *Client) ModifyCCMGroupStrategy(request *ModifyCCMGroupStrategyRequest) (response *ModifyCCMGroupStrategyResponse, err error) {
	if request == nil {
		request = NewModifyCCMGroupStrategyRequest()
	}
	response = NewModifyCCMGroupStrategyResponse()
	err = c.Send(request, response)
	return
}

func NewModifyCCMGroupViewRequest() (request *ModifyCCMGroupViewRequest) {
	request = &ModifyCCMGroupViewRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "ModifyCCMGroupView")
	return
}

func NewModifyCCMGroupViewResponse() (response *ModifyCCMGroupViewResponse) {
	response = &ModifyCCMGroupViewResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 修改分组视图
func (c *Client) ModifyCCMGroupView(request *ModifyCCMGroupViewRequest) (response *ModifyCCMGroupViewResponse, err error) {
	if request == nil {
		request = NewModifyCCMGroupViewRequest()
	}
	response = NewModifyCCMGroupViewResponse()
	err = c.Send(request, response)
	return
}

func NewModifyClmAlertPolicyRequest() (request *ModifyClmAlertPolicyRequest) {
	request = &ModifyClmAlertPolicyRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "ModifyClmAlertPolicy")
	return
}

func NewModifyClmAlertPolicyResponse() (response *ModifyClmAlertPolicyResponse) {
	response = &ModifyClmAlertPolicyResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 修改告警策略
func (c *Client) ModifyClmAlertPolicy(request *ModifyClmAlertPolicyRequest) (response *ModifyClmAlertPolicyResponse, err error) {
	if request == nil {
		request = NewModifyClmAlertPolicyRequest()
	}
	response = NewModifyClmAlertPolicyResponse()
	err = c.Send(request, response)
	return
}

func NewModifyClmAlertPolicyStatusRequest() (request *ModifyClmAlertPolicyStatusRequest) {
	request = &ModifyClmAlertPolicyStatusRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "ModifyClmAlertPolicyStatus")
	return
}

func NewModifyClmAlertPolicyStatusResponse() (response *ModifyClmAlertPolicyStatusResponse) {
	response = &ModifyClmAlertPolicyStatusResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 更新告警策略状态
func (c *Client) ModifyClmAlertPolicyStatus(request *ModifyClmAlertPolicyStatusRequest) (response *ModifyClmAlertPolicyStatusResponse, err error) {
	if request == nil {
		request = NewModifyClmAlertPolicyStatusRequest()
	}
	response = NewModifyClmAlertPolicyStatusResponse()
	err = c.Send(request, response)
	return
}

func NewModifyClmMetricSetRequest() (request *ModifyClmMetricSetRequest) {
	request = &ModifyClmMetricSetRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "ModifyClmMetricSet")
	return
}

func NewModifyClmMetricSetResponse() (response *ModifyClmMetricSetResponse) {
	response = &ModifyClmMetricSetResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 修改指标集配置
func (c *Client) ModifyClmMetricSet(request *ModifyClmMetricSetRequest) (response *ModifyClmMetricSetResponse, err error) {
	if request == nil {
		request = NewModifyClmMetricSetRequest()
	}
	response = NewModifyClmMetricSetResponse()
	err = c.Send(request, response)
	return
}

func NewModifyConditionsTemplateRequest() (request *ModifyConditionsTemplateRequest) {
	request = &ModifyConditionsTemplateRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "ModifyConditionsTemplate")
	return
}

func NewModifyConditionsTemplateResponse() (response *ModifyConditionsTemplateResponse) {
	response = &ModifyConditionsTemplateResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 修改告警策略模板
func (c *Client) ModifyConditionsTemplate(request *ModifyConditionsTemplateRequest) (response *ModifyConditionsTemplateResponse, err error) {
	if request == nil {
		request = NewModifyConditionsTemplateRequest()
	}
	response = NewModifyConditionsTemplateResponse()
	err = c.Send(request, response)
	return
}

func NewModifyDashboardRequest() (request *ModifyDashboardRequest) {
	request = &ModifyDashboardRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "ModifyDashboard")
	return
}

func NewModifyDashboardResponse() (response *ModifyDashboardResponse) {
	response = &ModifyDashboardResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 修改dashboard配置
func (c *Client) ModifyDashboard(request *ModifyDashboardRequest) (response *ModifyDashboardResponse, err error) {
	if request == nil {
		request = NewModifyDashboardRequest()
	}
	response = NewModifyDashboardResponse()
	err = c.Send(request, response)
	return
}

func NewModifyDashboardViewRequest() (request *ModifyDashboardViewRequest) {
	request = &ModifyDashboardViewRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "ModifyDashboardView")
	return
}

func NewModifyDashboardViewResponse() (response *ModifyDashboardViewResponse) {
	response = &ModifyDashboardViewResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 修改监控面板视图
func (c *Client) ModifyDashboardView(request *ModifyDashboardViewRequest) (response *ModifyDashboardViewResponse, err error) {
	if request == nil {
		request = NewModifyDashboardViewRequest()
	}
	response = NewModifyDashboardViewResponse()
	err = c.Send(request, response)
	return
}

func NewModifyDashboardViewBatchRequest() (request *ModifyDashboardViewBatchRequest) {
	request = &ModifyDashboardViewBatchRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "ModifyDashboardViewBatch")
	return
}

func NewModifyDashboardViewBatchResponse() (response *ModifyDashboardViewBatchResponse) {
	response = &ModifyDashboardViewBatchResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 批量更新视图位置
func (c *Client) ModifyDashboardViewBatch(request *ModifyDashboardViewBatchRequest) (response *ModifyDashboardViewBatchResponse, err error) {
	if request == nil {
		request = NewModifyDashboardViewBatchRequest()
	}
	response = NewModifyDashboardViewBatchResponse()
	err = c.Send(request, response)
	return
}

func NewModifyGroupRequest() (request *ModifyGroupRequest) {
	request = &ModifyGroupRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "ModifyGroup")
	return
}

func NewModifyGroupResponse() (response *ModifyGroupResponse) {
	response = &ModifyGroupResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 更新分组节点
func (c *Client) ModifyGroup(request *ModifyGroupRequest) (response *ModifyGroupResponse, err error) {
	if request == nil {
		request = NewModifyGroupRequest()
	}
	response = NewModifyGroupResponse()
	err = c.Send(request, response)
	return
}

func NewModifyInstanceGroupRequest() (request *ModifyInstanceGroupRequest) {
	request = &ModifyInstanceGroupRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "ModifyInstanceGroup")
	return
}

func NewModifyInstanceGroupResponse() (response *ModifyInstanceGroupResponse) {
	response = &ModifyInstanceGroupResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 更新实例分组
func (c *Client) ModifyInstanceGroup(request *ModifyInstanceGroupRequest) (response *ModifyInstanceGroupResponse, err error) {
	if request == nil {
		request = NewModifyInstanceGroupRequest()
	}
	response = NewModifyInstanceGroupResponse()
	err = c.Send(request, response)
	return
}

func NewModifyMetricSetRequest() (request *ModifyMetricSetRequest) {
	request = &ModifyMetricSetRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "ModifyMetricSet")
	return
}

func NewModifyMetricSetResponse() (response *ModifyMetricSetResponse) {
	response = &ModifyMetricSetResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// ModifyMetricSet 修改指标集配置
func (c *Client) ModifyMetricSet(request *ModifyMetricSetRequest) (response *ModifyMetricSetResponse, err error) {
	if request == nil {
		request = NewModifyMetricSetRequest()
	}
	response = NewModifyMetricSetResponse()
	err = c.Send(request, response)
	return
}

func NewModifyModuleRequest() (request *ModifyModuleRequest) {
	request = &ModifyModuleRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "ModifyModule")
	return
}

func NewModifyModuleResponse() (response *ModifyModuleResponse) {
	response = &ModifyModuleResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 更新叶子分组节点
func (c *Client) ModifyModule(request *ModifyModuleRequest) (response *ModifyModuleResponse, err error) {
	if request == nil {
		request = NewModifyModuleRequest()
	}
	response = NewModifyModuleResponse()
	err = c.Send(request, response)
	return
}

func NewModifyMonitorAlarmPolicyRequest() (request *ModifyMonitorAlarmPolicyRequest) {
	request = &ModifyMonitorAlarmPolicyRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "ModifyMonitorAlarmPolicy")
	return
}

func NewModifyMonitorAlarmPolicyResponse() (response *ModifyMonitorAlarmPolicyResponse) {
	response = &ModifyMonitorAlarmPolicyResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 更新告警策略
func (c *Client) ModifyMonitorAlarmPolicy(request *ModifyMonitorAlarmPolicyRequest) (response *ModifyMonitorAlarmPolicyResponse, err error) {
	if request == nil {
		request = NewModifyMonitorAlarmPolicyRequest()
	}
	response = NewModifyMonitorAlarmPolicyResponse()
	err = c.Send(request, response)
	return
}

func NewModifyMonitorDashboardRequest() (request *ModifyMonitorDashboardRequest) {
	request = &ModifyMonitorDashboardRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "ModifyMonitorDashboard")
	return
}

func NewModifyMonitorDashboardResponse() (response *ModifyMonitorDashboardResponse) {
	response = &ModifyMonitorDashboardResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 更新dashboard(包括设置全局过滤条件、更新时间刷新间隔等。必须发送全量数据)
func (c *Client) ModifyMonitorDashboard(request *ModifyMonitorDashboardRequest) (response *ModifyMonitorDashboardResponse, err error) {
	if request == nil {
		request = NewModifyMonitorDashboardRequest()
	}
	response = NewModifyMonitorDashboardResponse()
	err = c.Send(request, response)
	return
}

func NewModifyMonitorNoticeRuleRequest() (request *ModifyMonitorNoticeRuleRequest) {
	request = &ModifyMonitorNoticeRuleRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "ModifyMonitorNoticeRule")
	return
}

func NewModifyMonitorNoticeRuleResponse() (response *ModifyMonitorNoticeRuleResponse) {
	response = &ModifyMonitorNoticeRuleResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 编辑告警的通知规则
func (c *Client) ModifyMonitorNoticeRule(request *ModifyMonitorNoticeRuleRequest) (response *ModifyMonitorNoticeRuleResponse, err error) {
	if request == nil {
		request = NewModifyMonitorNoticeRuleRequest()
	}
	response = NewModifyMonitorNoticeRuleResponse()
	err = c.Send(request, response)
	return
}

func NewModifyMonitorPolicyStatusRequest() (request *ModifyMonitorPolicyStatusRequest) {
	request = &ModifyMonitorPolicyStatusRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "ModifyMonitorPolicyStatus")
	return
}

func NewModifyMonitorPolicyStatusResponse() (response *ModifyMonitorPolicyStatusResponse) {
	response = &ModifyMonitorPolicyStatusResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 更新告警策略状态
func (c *Client) ModifyMonitorPolicyStatus(request *ModifyMonitorPolicyStatusRequest) (response *ModifyMonitorPolicyStatusResponse, err error) {
	if request == nil {
		request = NewModifyMonitorPolicyStatusRequest()
	}
	response = NewModifyMonitorPolicyStatusResponse()
	err = c.Send(request, response)
	return
}

func NewModifyMsgPolicyRequest() (request *ModifyMsgPolicyRequest) {
	request = &ModifyMsgPolicyRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "ModifyMsgPolicy")
	return
}

func NewModifyMsgPolicyResponse() (response *ModifyMsgPolicyResponse) {
	response = &ModifyMsgPolicyResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 更改自定义消息策略
func (c *Client) ModifyMsgPolicy(request *ModifyMsgPolicyRequest) (response *ModifyMsgPolicyResponse, err error) {
	if request == nil {
		request = NewModifyMsgPolicyRequest()
	}
	response = NewModifyMsgPolicyResponse()
	err = c.Send(request, response)
	return
}

func NewModifyNotifyBatchRequest() (request *ModifyNotifyBatchRequest) {
	request = &ModifyNotifyBatchRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "ModifyNotifyBatch")
	return
}

func NewModifyNotifyBatchResponse() (response *ModifyNotifyBatchResponse) {
	response = &ModifyNotifyBatchResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 批量更新告警渠道
func (c *Client) ModifyNotifyBatch(request *ModifyNotifyBatchRequest) (response *ModifyNotifyBatchResponse, err error) {
	if request == nil {
		request = NewModifyNotifyBatchRequest()
	}
	response = NewModifyNotifyBatchResponse()
	err = c.Send(request, response)
	return
}

func NewModifyPolicyGroupRequest() (request *ModifyPolicyGroupRequest) {
	request = &ModifyPolicyGroupRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "ModifyPolicyGroup")
	return
}

func NewModifyPolicyGroupResponse() (response *ModifyPolicyGroupResponse) {
	response = &ModifyPolicyGroupResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 更新策略组
func (c *Client) ModifyPolicyGroup(request *ModifyPolicyGroupRequest) (response *ModifyPolicyGroupResponse, err error) {
	if request == nil {
		request = NewModifyPolicyGroupRequest()
	}
	response = NewModifyPolicyGroupResponse()
	err = c.Send(request, response)
	return
}

func NewModifyPolicyGroupInfoRequest() (request *ModifyPolicyGroupInfoRequest) {
	request = &ModifyPolicyGroupInfoRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "ModifyPolicyGroupInfo")
	return
}

func NewModifyPolicyGroupInfoResponse() (response *ModifyPolicyGroupInfoResponse) {
	response = &ModifyPolicyGroupInfoResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 更新策略组详情
func (c *Client) ModifyPolicyGroupInfo(request *ModifyPolicyGroupInfoRequest) (response *ModifyPolicyGroupInfoResponse, err error) {
	if request == nil {
		request = NewModifyPolicyGroupInfoRequest()
	}
	response = NewModifyPolicyGroupInfoResponse()
	err = c.Send(request, response)
	return
}

func NewModifyRecoverNotifyBatchRequest() (request *ModifyRecoverNotifyBatchRequest) {
	request = &ModifyRecoverNotifyBatchRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "ModifyRecoverNotifyBatch")
	return
}

func NewModifyRecoverNotifyBatchResponse() (response *ModifyRecoverNotifyBatchResponse) {
	response = &ModifyRecoverNotifyBatchResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 批量编辑恢复告警渠道
func (c *Client) ModifyRecoverNotifyBatch(request *ModifyRecoverNotifyBatchRequest) (response *ModifyRecoverNotifyBatchResponse, err error) {
	if request == nil {
		request = NewModifyRecoverNotifyBatchRequest()
	}
	response = NewModifyRecoverNotifyBatchResponse()
	err = c.Send(request, response)
	return
}

func NewModifyStrategyRequest() (request *ModifyStrategyRequest) {
	request = &ModifyStrategyRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "ModifyStrategy")
	return
}

func NewModifyStrategyResponse() (response *ModifyStrategyResponse) {
	response = &ModifyStrategyResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 修改告警策略
func (c *Client) ModifyStrategy(request *ModifyStrategyRequest) (response *ModifyStrategyResponse, err error) {
	if request == nil {
		request = NewModifyStrategyRequest()
	}
	response = NewModifyStrategyResponse()
	err = c.Send(request, response)
	return
}

func NewModifyStrategyChannelsRequest() (request *ModifyStrategyChannelsRequest) {
	request = &ModifyStrategyChannelsRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "ModifyStrategyChannels")
	return
}

func NewModifyStrategyChannelsResponse() (response *ModifyStrategyChannelsResponse) {
	response = &ModifyStrategyChannelsResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 批量修改告警策略告警渠道
func (c *Client) ModifyStrategyChannels(request *ModifyStrategyChannelsRequest) (response *ModifyStrategyChannelsResponse, err error) {
	if request == nil {
		request = NewModifyStrategyChannelsRequest()
	}
	response = NewModifyStrategyChannelsResponse()
	err = c.Send(request, response)
	return
}

func NewModifyStrategyStatesRequest() (request *ModifyStrategyStatesRequest) {
	request = &ModifyStrategyStatesRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "ModifyStrategyStates")
	return
}

func NewModifyStrategyStatesResponse() (response *ModifyStrategyStatesResponse) {
	response = &ModifyStrategyStatesResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 批量修改告警启停状态
func (c *Client) ModifyStrategyStates(request *ModifyStrategyStatesRequest) (response *ModifyStrategyStatesResponse, err error) {
	if request == nil {
		request = NewModifyStrategyStatesRequest()
	}
	response = NewModifyStrategyStatesResponse()
	err = c.Send(request, response)
	return
}

func NewModifyUserDashboardOptionRequest() (request *ModifyUserDashboardOptionRequest) {
	request = &ModifyUserDashboardOptionRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "ModifyUserDashboardOption")
	return
}

func NewModifyUserDashboardOptionResponse() (response *ModifyUserDashboardOptionResponse) {
	response = &ModifyUserDashboardOptionResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 修改用户Dashboard行为
func (c *Client) ModifyUserDashboardOption(request *ModifyUserDashboardOptionRequest) (response *ModifyUserDashboardOptionResponse, err error) {
	if request == nil {
		request = NewModifyUserDashboardOptionRequest()
	}
	response = NewModifyUserDashboardOptionResponse()
	err = c.Send(request, response)
	return
}

func NewModifyViewSaveAsRequest() (request *ModifyViewSaveAsRequest) {
	request = &ModifyViewSaveAsRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "ModifyViewSaveAs")
	return
}

func NewModifyViewSaveAsResponse() (response *ModifyViewSaveAsResponse) {
	response = &ModifyViewSaveAsResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 批量迁移图表
func (c *Client) ModifyViewSaveAs(request *ModifyViewSaveAsRequest) (response *ModifyViewSaveAsResponse, err error) {
	if request == nil {
		request = NewModifyViewSaveAsRequest()
	}
	response = NewModifyViewSaveAsResponse()
	err = c.Send(request, response)
	return
}

func NewPutMonitorDataRequest() (request *PutMonitorDataRequest) {
	request = &PutMonitorDataRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "PutMonitorData")
	return
}

func NewPutMonitorDataResponse() (response *PutMonitorDataResponse) {
	response = &PutMonitorDataResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 默认接口请求频率限制：50次/秒。
// 默认单租户指标上限：100个。
// 单次上报最多 30 个指标/值对，请求返回错误时，请求中所有的指标/值均不会被保存。
//
// 上报的时间戳为期望保存的时间戳，建议构造整数分钟时刻的时间戳。
// 时间戳时间范围必须为当前时间到 300 秒前之间。
// 同一 IP 指标对的数据需按分钟先后顺序上报。
func (c *Client) PutMonitorData(request *PutMonitorDataRequest) (response *PutMonitorDataResponse, err error) {
	if request == nil {
		request = NewPutMonitorDataRequest()
	}
	response = NewPutMonitorDataResponse()
	err = c.Send(request, response)
	return
}

func NewQueryDataRequest() (request *QueryDataRequest) {
	request = &QueryDataRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "QueryData")
	return
}

func NewQueryDataResponse() (response *QueryDataResponse) {
	response = &QueryDataResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 查询数据
func (c *Client) QueryData(request *QueryDataRequest) (response *QueryDataResponse, err error) {
	if request == nil {
		request = NewQueryDataRequest()
	}
	response = NewQueryDataResponse()
	err = c.Send(request, response)
	return
}

func NewReportRequest() (request *ReportRequest) {
	request = &ReportRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "Report")
	return
}

func NewReportResponse() (response *ReportResponse) {
	response = &ReportResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 自定义数据上报，目前对上报数据量和上报数据条数无限制。
func (c *Client) Report(request *ReportRequest) (response *ReportResponse, err error) {
	if request == nil {
		request = NewReportRequest()
	}
	response = NewReportResponse()
	err = c.Send(request, response)
	return
}

func NewReportMetricRequest() (request *ReportMetricRequest) {
	request = &ReportMetricRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "ReportMetric")
	return
}

func NewReportMetricResponse() (response *ReportMetricResponse) {
	response = &ReportMetricResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 自定义数据上报，目前对上报数据量和上报数据条数无限制。
func (c *Client) ReportMetric(request *ReportMetricRequest) (response *ReportMetricResponse, err error) {
	if request == nil {
		request = NewReportMetricRequest()
	}
	response = NewReportMetricResponse()
	err = c.Send(request, response)
	return
}

func NewSearchClmDimensionValueRequest() (request *SearchClmDimensionValueRequest) {
	request = &SearchClmDimensionValueRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "SearchClmDimensionValue")
	return
}

func NewSearchClmDimensionValueResponse() (response *SearchClmDimensionValueResponse) {
	response = &SearchClmDimensionValueResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 搜索维度值
func (c *Client) SearchClmDimensionValue(request *SearchClmDimensionValueRequest) (response *SearchClmDimensionValueResponse, err error) {
	if request == nil {
		request = NewSearchClmDimensionValueRequest()
	}
	response = NewSearchClmDimensionValueResponse()
	err = c.Send(request, response)
	return
}

func NewSearchDimensionValueRequest() (request *SearchDimensionValueRequest) {
	request = &SearchDimensionValueRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "SearchDimensionValue")
	return
}

func NewSearchDimensionValueResponse() (response *SearchDimensionValueResponse) {
	response = &SearchDimensionValueResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 搜索维度值
func (c *Client) SearchDimensionValue(request *SearchDimensionValueRequest) (response *SearchDimensionValueResponse, err error) {
	if request == nil {
		request = NewSearchDimensionValueRequest()
	}
	response = NewSearchDimensionValueResponse()
	err = c.Send(request, response)
	return
}

func NewSearchLogRequest() (request *SearchLogRequest) {
	request = &SearchLogRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "SearchLog")
	return
}

func NewSearchLogResponse() (response *SearchLogResponse) {
	response = &SearchLogResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 搜索CLS日志主题日志
func (c *Client) SearchLog(request *SearchLogRequest) (response *SearchLogResponse, err error) {
	if request == nil {
		request = NewSearchLogRequest()
	}
	response = NewSearchLogResponse()
	err = c.Send(request, response)
	return
}

func NewSearchMonitorConditionsValuesRequest() (request *SearchMonitorConditionsValuesRequest) {
	request = &SearchMonitorConditionsValuesRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "SearchMonitorConditionsValues")
	return
}

func NewSearchMonitorConditionsValuesResponse() (response *SearchMonitorConditionsValuesResponse) {
	response = &SearchMonitorConditionsValuesResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 搜索条件key下的value
func (c *Client) SearchMonitorConditionsValues(request *SearchMonitorConditionsValuesRequest) (response *SearchMonitorConditionsValuesResponse, err error) {
	if request == nil {
		request = NewSearchMonitorConditionsValuesRequest()
	}
	response = NewSearchMonitorConditionsValuesResponse()
	err = c.Send(request, response)
	return
}

func NewSearchMonitorDimensionValuesRequest() (request *SearchMonitorDimensionValuesRequest) {
	request = &SearchMonitorDimensionValuesRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "SearchMonitorDimensionValues")
	return
}

func NewSearchMonitorDimensionValuesResponse() (response *SearchMonitorDimensionValuesResponse) {
	response = &SearchMonitorDimensionValuesResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 搜索指标下维度的value
func (c *Client) SearchMonitorDimensionValues(request *SearchMonitorDimensionValuesRequest) (response *SearchMonitorDimensionValuesResponse, err error) {
	if request == nil {
		request = NewSearchMonitorDimensionValuesRequest()
	}
	response = NewSearchMonitorDimensionValuesResponse()
	err = c.Send(request, response)
	return
}

func NewSendCustomAlarmMsgRequest() (request *SendCustomAlarmMsgRequest) {
	request = &SendCustomAlarmMsgRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "SendCustomAlarmMsg")
	return
}

func NewSendCustomAlarmMsgResponse() (response *SendCustomAlarmMsgResponse) {
	response = &SendCustomAlarmMsgResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 发送自定义消息告警
func (c *Client) SendCustomAlarmMsg(request *SendCustomAlarmMsgRequest) (response *SendCustomAlarmMsgResponse, err error) {
	if request == nil {
		request = NewSendCustomAlarmMsgRequest()
	}
	response = NewSendCustomAlarmMsgResponse()
	err = c.Send(request, response)
	return
}

func NewSetDefaultPolicyGroupRequest() (request *SetDefaultPolicyGroupRequest) {
	request = &SetDefaultPolicyGroupRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "SetDefaultPolicyGroup")
	return
}

func NewSetDefaultPolicyGroupResponse() (response *SetDefaultPolicyGroupResponse) {
	response = &SetDefaultPolicyGroupResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 设置默认策略
func (c *Client) SetDefaultPolicyGroup(request *SetDefaultPolicyGroupRequest) (response *SetDefaultPolicyGroupResponse, err error) {
	if request == nil {
		request = NewSetDefaultPolicyGroupRequest()
	}
	response = NewSetDefaultPolicyGroupResponse()
	err = c.Send(request, response)
	return
}

func NewShieldPolicyAlarmRequest() (request *ShieldPolicyAlarmRequest) {
	request = &ShieldPolicyAlarmRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "ShieldPolicyAlarm")
	return
}

func NewShieldPolicyAlarmResponse() (response *ShieldPolicyAlarmResponse) {
	response = &ShieldPolicyAlarmResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 设置告警屏蔽状态
func (c *Client) ShieldPolicyAlarm(request *ShieldPolicyAlarmRequest) (response *ShieldPolicyAlarmResponse, err error) {
	if request == nil {
		request = NewShieldPolicyAlarmRequest()
	}
	response = NewShieldPolicyAlarmResponse()
	err = c.Send(request, response)
	return
}

func NewStarMonitorDashboardRequest() (request *StarMonitorDashboardRequest) {
	request = &StarMonitorDashboardRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "StarMonitorDashboard")
	return
}

func NewStarMonitorDashboardResponse() (response *StarMonitorDashboardResponse) {
	response = &StarMonitorDashboardResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 收藏dashboard
func (c *Client) StarMonitorDashboard(request *StarMonitorDashboardRequest) (response *StarMonitorDashboardResponse, err error) {
	if request == nil {
		request = NewStarMonitorDashboardRequest()
	}
	response = NewStarMonitorDashboardResponse()
	err = c.Send(request, response)
	return
}

func NewSubscribeEventRequest() (request *SubscribeEventRequest) {
	request = &SubscribeEventRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "SubscribeEvent")
	return
}

func NewSubscribeEventResponse() (response *SubscribeEventResponse) {
	response = &SubscribeEventResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 订阅平台事件告警
func (c *Client) SubscribeEvent(request *SubscribeEventRequest) (response *SubscribeEventResponse, err error) {
	if request == nil {
		request = NewSubscribeEventRequest()
	}
	response = NewSubscribeEventResponse()
	err = c.Send(request, response)
	return
}

func NewUnBindingAllPolicyObjectRequest() (request *UnBindingAllPolicyObjectRequest) {
	request = &UnBindingAllPolicyObjectRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "UnBindingAllPolicyObject")
	return
}

func NewUnBindingAllPolicyObjectResponse() (response *UnBindingAllPolicyObjectResponse) {
	response = &UnBindingAllPolicyObjectResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 删除全部的关联对象
func (c *Client) UnBindingAllPolicyObject(request *UnBindingAllPolicyObjectRequest) (response *UnBindingAllPolicyObjectResponse, err error) {
	if request == nil {
		request = NewUnBindingAllPolicyObjectRequest()
	}
	response = NewUnBindingAllPolicyObjectResponse()
	err = c.Send(request, response)
	return
}

func NewUnBindingInstanceGroupRequest() (request *UnBindingInstanceGroupRequest) {
	request = &UnBindingInstanceGroupRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "UnBindingInstanceGroup")
	return
}

func NewUnBindingInstanceGroupResponse() (response *UnBindingInstanceGroupResponse) {
	response = &UnBindingInstanceGroupResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 解绑实例分组的告警策略
func (c *Client) UnBindingInstanceGroup(request *UnBindingInstanceGroupRequest) (response *UnBindingInstanceGroupResponse, err error) {
	if request == nil {
		request = NewUnBindingInstanceGroupRequest()
	}
	response = NewUnBindingInstanceGroupResponse()
	err = c.Send(request, response)
	return
}

func NewUnBindingPolicyObjectRequest() (request *UnBindingPolicyObjectRequest) {
	request = &UnBindingPolicyObjectRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "UnBindingPolicyObject")
	return
}

func NewUnBindingPolicyObjectResponse() (response *UnBindingPolicyObjectResponse) {
	response = &UnBindingPolicyObjectResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 删除策略的关联对象
func (c *Client) UnBindingPolicyObject(request *UnBindingPolicyObjectRequest) (response *UnBindingPolicyObjectResponse, err error) {
	if request == nil {
		request = NewUnBindingPolicyObjectRequest()
	}
	response = NewUnBindingPolicyObjectResponse()
	err = c.Send(request, response)
	return
}

func NewUnsubscribeEventRequest() (request *UnsubscribeEventRequest) {
	request = &UnsubscribeEventRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "UnsubscribeEvent")
	return
}

func NewUnsubscribeEventResponse() (response *UnsubscribeEventResponse) {
	response = &UnsubscribeEventResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 取消订阅平台事件告警
func (c *Client) UnsubscribeEvent(request *UnsubscribeEventRequest) (response *UnsubscribeEventResponse, err error) {
	if request == nil {
		request = NewUnsubscribeEventRequest()
	}
	response = NewUnsubscribeEventResponse()
	err = c.Send(request, response)
	return
}

func NewVerifyAlarmCallbackRequest() (request *VerifyAlarmCallbackRequest) {
	request = &VerifyAlarmCallbackRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("monitor", APIVersion, "VerifyAlarmCallback")
	return
}

func NewVerifyAlarmCallbackResponse() (response *VerifyAlarmCallbackResponse) {
	response = &VerifyAlarmCallbackResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 验证用户的回调Url
func (c *Client) VerifyAlarmCallback(request *VerifyAlarmCallbackRequest) (response *VerifyAlarmCallbackResponse, err error) {
	if request == nil {
		request = NewVerifyAlarmCallbackRequest()
	}
	response = NewVerifyAlarmCallbackResponse()
	err = c.Send(request, response)
	return
}
