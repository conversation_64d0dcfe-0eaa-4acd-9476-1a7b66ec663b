package cls

import (
	"context"

	common "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common"
	tchttp "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/http"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/profile"
	tkeApi "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/tke/v20180525"
)

const APIVersion = "2020-10-16"

type Client struct {
	tkeApi.Client
}

func NewClient(credential *common.Credential, region string, clientProfile *profile.ClientProfile) (client *Client,
	err error) {
	client = &Client{}
	client.Init(region).
		WithCredential(credential).
		WithProfile(clientProfile)
	return
}

func NewSearchLogRequest() (request *SearchLogRequestv1) {
	request = &SearchLogRequestv1{
		BaseRequest: &tchttp.BaseRequest{},
	}

	request.Init().WithApiInfo("cls", APIVersion, "SearchLog")

	return
}

func NewSearchLogResponse() (response *SearchLogResponsev1) {
	response = &SearchLogResponsev1{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

func (c *Client) SearchLog(request *SearchLogRequestv1) (response *SearchLogResponsev1, err error) {
	return c.SearchLogWithContext(context.Background(), request)
}

func (c *Client) SearchLogWithContext(ctx context.Context, request *SearchLogRequestv1) (response *SearchLogResponsev1, err error) {
	if request == nil {
		request = NewSearchLogRequest()
	}
	

	response = NewSearchLogResponse()
	err = c.Send(request, response)
	return
}
