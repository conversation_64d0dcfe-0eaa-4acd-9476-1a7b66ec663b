package tke

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"tencentcloud.com/tstream_galileo/src/tstream_cc/constants"

	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common"
	tchttp "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/http"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/profile"
	tkeApi "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/tke/v20180525"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	logger "tencentcloud.com/tstream_galileo/src/common/logger"
	service1 "tencentcloud.com/tstream_galileo/src/tstream_cc/service"
)

const APIVersion = "2018-05-25"

type Client struct {
	tkeApi.Client
}

func NewClient(credential *common.Credential, region string, clientProfile *profile.ClientProfile) (client *Client,
	err error) {
	client = &Client{}
	client.Init(region).
		WithCredential(credential).
		WithProfile(clientProfile)
	return
}

func NewUpdateMetaFeatureRequest() (request *UpdateMetaFeatureRequest) {
	request = &UpdateMetaFeatureRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("tke", APIVersion, "UpdateMetaFeature")
	return
}

func NewUpdateMetaFeatureResponse() (response *UpdateMetaFeatureResponse) {
	response = &UpdateMetaFeatureResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// UpdateMetaFeature
// TKE集群更新跨租户弹性网卡全局配置接口
//
// 可能返回的错误码:
//
//	FAILEDOPERATION = "FailedOperation"
//	FAILEDOPERATION_CLUSTERUPDATEMETAFEATUREFAILED = "FailedOperation.ClusterUpdateMetaFeatureFailed"
//	FAILEDOPERATION_DB = "FailedOperation.Db"
//	FAILEDOPERATION_UNEXPECTEDERROR = "FailedOperation.UnexpectedError"
//	INTERNALERROR = "InternalError"
//	INTERNALERROR_UNEXCEPTEDINTERNAL = "InternalError.UnexceptedInternal"
//	INTERNALERROR_UNEXPECTEDINTERNAL = "InternalError.UnexpectedInternal"
//	INVALIDPARAMETER_PARAM = "InvalidParameter.Param"
func (c *Client) UpdateMetaFeature(request *UpdateMetaFeatureRequest) (response *UpdateMetaFeatureResponse, err error) {
	if request == nil {
		request = NewUpdateMetaFeatureRequest()
	}

	response = NewUpdateMetaFeatureResponse()
	err = c.Send(request, response)
	return
}

func NewUpdateMetaFeatureForEksRequest() (request *UpdateMetaFeatureForEksRequest) {
	request = &UpdateMetaFeatureForEksRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}

	request.Init().WithApiInfo("tke", APIVersion, "UpdateMetaFeatureForEks")

	return
}

func NewUpdateMetaFeatureForEksResponse() (response *UpdateMetaFeatureForEksResponse) {
	response = &UpdateMetaFeatureForEksResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// UpdateMetaFeatureForEks
// EKS集群更新跨租户弹性网卡配置接口
//
// 可能返回的错误码:
//
//	FAILEDOPERATION = "FailedOperation"
//	INTERNALERROR = "InternalError"
//	INVALIDPARAMETER = "InvalidParameter"
//	RESOURCENOTFOUND = "ResourceNotFound"
//	RESOURCEUNAVAILABLE = "ResourceUnavailable"
func (c *Client) UpdateMetaFeatureForEks(request *UpdateMetaFeatureForEksRequest) (response *UpdateMetaFeatureForEksResponse, err error) {
	if request == nil {
		request = NewUpdateMetaFeatureForEksRequest()
	}

	response = NewUpdateMetaFeatureForEksResponse()
	err = c.Send(request, response)
	return
}

func NewEnableMetaFeatureForEksRequest() (request *EnableMetaFeatureForEksRequest) {
	request = &EnableMetaFeatureForEksRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("tke", tkeApi.APIVersion, "EnableMetaFeatureForEks")
	return
}

func NewEnableMetaFeatureRequest() (request *EnableMetaFeatureRequest) {
	request = &EnableMetaFeatureRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("tke", tkeApi.APIVersion, "EnableMetaFeature")
	return
}

func NewEnableMetaFeatureResponse() (response *EnableMetaFeatureResponse) {
	response = &EnableMetaFeatureResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

func (c *Client) EnableMetaFeature(request *EnableMetaFeatureRequest) (response *EnableMetaFeatureResponse, err error) {
	if request == nil {
		request = NewEnableMetaFeatureRequest()
	}
	response = NewEnableMetaFeatureResponse()
	err = c.Send(request, response)
	return
}

func NewModifyClusterSchedulerPolicyRequest() (request *ModifyClusterSchedulerPolicyRequest) {
	request = &ModifyClusterSchedulerPolicyRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("tke", tkeApi.APIVersion, "ModifyClusterSchedulerPolicy")
	return
}

func NewModifyClusterSchedulerPolicyResponse() (response *ModifyClusterSchedulerPolicyResponse) {
	response = &ModifyClusterSchedulerPolicyResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

func (c *Client) ModifyClusterSchedulerPolicy(request *ModifyClusterSchedulerPolicyRequest) (response *ModifyClusterSchedulerPolicyResponse, err error) {
	if request == nil {
		request = NewModifyClusterSchedulerPolicyRequest()
	}
	response = NewModifyClusterSchedulerPolicyResponse()
	err = c.Send(request, response)
	return
}

func (c *Client) EnableMetaFeatureForEks(request *EnableMetaFeatureForEksRequest) (response *EnableMetaFeatureResponse, err error) {
	if request == nil {
		request = NewEnableMetaFeatureForEksRequest()
	}
	response = NewEnableMetaFeatureResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeMetaFeatureProgressRequest() (request *DescribeMetaFeatureProgressRequest) {
	request = &DescribeMetaFeatureProgressRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("tke", tkeApi.APIVersion, "DescribeMetaFeatureProgress")
	return
}

func NewDescribeMetaFeatureProgressResponse() (response *DescribeMetaFeatureProgressResponse) {
	response = &DescribeMetaFeatureProgressResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

func (c *Client) DescribeMetaFeatureProgress(request *DescribeMetaFeatureProgressRequest) (
	response *DescribeMetaFeatureProgressResponse, err error) {
	if request == nil {
		request = NewDescribeMetaFeatureProgressRequest()
	}
	response = NewDescribeMetaFeatureProgressResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeEksMetaFeatureProgressRequest() (request *DescribeEksMetaFeatureProgressRequest) {
	request = &DescribeEksMetaFeatureProgressRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("tke", tkeApi.APIVersion, "DescribeEksMetaFeatureProgress")
	return
}

func NewDescribeEksMetaFeatureProgressResponse() (response *DescribeEksMetaFeatureProgressResponse) {
	response = &DescribeEksMetaFeatureProgressResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

func (c *Client) DescribeEksMetaFeatureProgress(request *DescribeEksMetaFeatureProgressRequest) (
	response *DescribeEksMetaFeatureProgressResponse, err error) {
	if request == nil {
		request = NewDescribeEksMetaFeatureProgressRequest()
	}
	response = NewDescribeEksMetaFeatureProgressResponse()
	err = c.Send(request, response)
	return
}

// EksLogCollectStatus 判断eks集群日志采集组件安装状态
func EksLogCollectStatus(region string, clusterUniqueId string) (ready bool, err error) {
	if region == "" || clusterUniqueId == "" {
		logger.Errorf("invalid param region[%s] or clusterUniqueId[%s] is empty ", region, clusterUniqueId)
		return false, errorcode.NewStackError(errorcode.InvalidParameterCode, "", err)
	}
	secretId, secretKey, err := service1.GetSecretIdAndKeyOfScs()
	if err != nil {
		err = errorcode.NewStackError(errorcode.InternalErrorCode, "GetScsSecretKey", err)
		return false, err
	}
	client, err := common.NewClientWithSecretId(secretId, secretKey, region)
	if err != nil {
		return false, err
	}
	prof := profile.NewClientProfile()
	if region == constants.AP_SHANGHAI_ADC {
		prof.HttpProfile.Endpoint = constants.TKE_API_DOMAIN_INTERNAL
	}
	client.WithProfile(prof)
	response := NewForwardEKSApplicationRequestV3Response()
	err = client.Send(NewForwardEKSApplicationRequestV3Request(common.StringPtr(clusterUniqueId)), response)
	if err != nil {
		logger.Errorf("ForwardEKSApplicationRequestV3 with error %+v", err)
		err.Error()
		return false, err
	}
	appStatus := &ForwardEKSApp{}
	err = json.Unmarshal([]byte(*response.Response.ResponseBody), appStatus)
	if err != nil {
		logger.Errorf("ForwardEKSApplicationRequestV3 parse with error %+v", err)
		return false, err
	}
	return appStatus.Status.Phase == "Succeeded", nil
}

func NewForwardEKSApplicationRequestV3Request(clusterId *string) *ForwardEKSApplicationRequestV3Request {
	request := &ForwardEKSApplicationRequestV3Request{
		BaseRequest: &tchttp.BaseRequest{},
		ClusterName: clusterId,
		Method:      common.StringPtr("GET"),
		Path: common.StringPtr(
			fmt.Sprintf("/apis/application.tkestack.io/v1/namespaces/%s/apps/tke-log-agent", *clusterId)),
	}
	request.Init().WithApiInfo("tke", "2018-05-25", "ForwardEKSApplicationRequestV3")
	return request
}

func NewForwardEKSApplicationRequestV3Response() *ForwardEKSApplicationRequestV3Response {
	response := &ForwardEKSApplicationRequestV3Response{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return response
}

/****************************************tke创建集群接口****************************************/
func NewCreateClusterRequest() (request *CreateClusterRequest) {
	request = &CreateClusterRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}

	request.Init().WithApiInfo("tke", APIVersion, "CreateCluster")

	return
}

func (c *Client) CreateCluster(request *CreateClusterRequest) (response *CreateClusterResponse, err error) {
	if request == nil {
		request = NewCreateClusterRequest()
	}
	response = NewCreateClusterResponse()
	err = c.Send(request, response)
	return

}

func NewCreateClusterResponse() (response *CreateClusterResponse) {
	response = &CreateClusterResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

func NewAddExistedInstancesRequest() (request *AddExistedInstancesRequest) {
	request = &AddExistedInstancesRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}

	request.Init().WithApiInfo("tke", APIVersion, "AddExistedInstances")

	return
}

func (c *Client) AddExistedInstances(request *AddExistedInstancesRequest) (response *AddExistedInstancesResponse, err error) {
	if request == nil {
		request = NewAddExistedInstancesRequest()
	}

	response = NewAddExistedInstancesResponse()
	err = c.Send(request, response)
	return
}

func NewAddExistedInstancesResponse() (response *AddExistedInstancesResponse) {
	response = &AddExistedInstancesResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

func NewDescribeAddonRequest() (request *DescribeAddonRequest) {
	request = &DescribeAddonRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}

	request.Init().WithApiInfo("tke", APIVersion, "DescribeAddon")

	return
}

func NewDescribeAddonResponse() (response *DescribeAddonResponse) {
	response = &DescribeAddonResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

func (c *Client) DescribeAddon(request *DescribeAddonRequest) (response *DescribeAddonResponse, err error) {
	return c.DescribeAddonWithContext(context.Background(), request)
}
func (c *Client) DescribeAddonWithContext(ctx context.Context, request *DescribeAddonRequest) (response *DescribeAddonResponse, err error) {
	if request == nil {
		request = NewDescribeAddonRequest()
	}

	request.context = ctx

	response = NewDescribeAddonResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeAddonValuesRequest() (request *DescribeAddonValuesRequest) {
	request = &DescribeAddonValuesRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}

	request.Init().WithApiInfo("tke", APIVersion, "DescribeAddonValues")

	return
}

func NewUpdateAddonRequest() (request *UpdateAddonRequest) {
	request = &UpdateAddonRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}

	request.Init().WithApiInfo("tke", APIVersion, "UpdateAddon")

	return
}

// DescribeAddonValues
// 获取一个addon的参数
//
// 可能返回的错误码:
//
//	RESOURCEUNAVAILABLE = "ResourceUnavailable"
func (c *Client) DescribeAddonValues(request *DescribeAddonValuesRequest) (response *DescribeAddonValuesResponse, err error) {
	return c.DescribeAddonValuesWithContext(context.Background(), request)
}

// DescribeAddonValues
// 获取一个addon的参数
//
// 可能返回的错误码:
//
//	RESOURCEUNAVAILABLE = "ResourceUnavailable"
func (c *Client) DescribeAddonValuesWithContext(ctx context.Context, request *DescribeAddonValuesRequest) (response *DescribeAddonValuesResponse, err error) {
	if request == nil {
		request = NewDescribeAddonValuesRequest()
	}

	request.context = ctx

	response = NewDescribeAddonValuesResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeAddonValuesResponse() (response *DescribeAddonValuesResponse) {
	response = &DescribeAddonValuesResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

func (c *Client) UpdateAddon(request *UpdateAddonRequest) (response *UpdateAddonResponse, err error) {
	return c.UpdateAddonWithContext(context.Background(), request)
}

func (c *Client) UpdateAddonWithContext(ctx context.Context, request *UpdateAddonRequest) (response *UpdateAddonResponse, err error) {
	if request == nil {
		request = NewUpdateAddonRequest()
	}

	request.context = ctx

	response = NewUpdateAddonResponse()
	err = c.Send(request, response)
	return
}
func NewUpdateAddonResponse() (response *UpdateAddonResponse) {
	response = &UpdateAddonResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

func NewDescribeLogSwitchesRequest() (request *DescribeLogSwitchesRequest) {
	request = &DescribeLogSwitchesRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("tke", APIVersion, "DescribeLogSwitches")
	return
}

func NewDescribeLogSwitchesResponse() (response *DescribeLogSwitchesResponse) {
	response = &DescribeLogSwitchesResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 查询集群日志（审计、事件、普通日志）开关列表
func (c *Client) DescribeLogSwitches(request *DescribeLogSwitchesRequest) (response *DescribeLogSwitchesResponse, err error) {
	if request == nil {
		request = NewDescribeLogSwitchesRequest()
	}
	response = NewDescribeLogSwitchesResponse()
	err = c.Send(request, response)
	return
}

func NewEnableEksEventPersistenceRequest() (request *EnableEksEventPersistenceRequest) {
	request = &EnableEksEventPersistenceRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("tke", APIVersion, "EnableEksEventPersistence")
	return
}

func NewEnableEksEventPersistenceResponse() (response *EnableEksEventPersistenceResponse) {
	response = &EnableEksEventPersistenceResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// Eks集群开启事件持久化功能
func (c *Client) EnableEksEventPersistence(request *EnableEksEventPersistenceRequest) (response *EnableEksEventPersistenceResponse, err error) {
	if request == nil {
		request = NewEnableEksEventPersistenceRequest()
	}
	response = NewEnableEksEventPersistenceResponse()
	err = c.Send(request, response)
	return
}

func NewEnableEventPersistenceRequest() (request *EnableEventPersistenceRequest) {
	request = &EnableEventPersistenceRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("tke", APIVersion, "EnableEventPersistence")
	return
}

func NewEnableEventPersistenceResponse() (response *EnableEventPersistenceResponse) {
	response = &EnableEventPersistenceResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 开启事件持久化功能
func (c *Client) EnableEventPersistence(request *EnableEventPersistenceRequest) (response *EnableEventPersistenceResponse, err error) {
	if request == nil {
		request = NewEnableEventPersistenceRequest()
	}
	response = NewEnableEventPersistenceResponse()
	err = c.Send(request, response)
	return
}

func NewCreateClusterReleaseRequest() (request *CreateClusterReleaseRequest) {
	request = &CreateClusterReleaseRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("tke", APIVersion, "CreateClusterRelease")
	return
}

func NewCreateClusterReleaseResponse() (response *CreateClusterReleaseResponse) {
	response = &CreateClusterReleaseResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 在应用市场中集群创建应用
func (c *Client) CreateClusterRelease(request *CreateClusterReleaseRequest) (response *CreateClusterReleaseResponse, err error) {
	if request == nil {
		request = NewCreateClusterReleaseRequest()
	}
	response = NewCreateClusterReleaseResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeClusterSecurityRequest() (request *DescribeClusterSecurityRequest) {
	request = &DescribeClusterSecurityRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("tke", APIVersion, "DescribeClusterSecurity")
	return
}

func NewDescribeClusterSecurityResponse() (response *DescribeClusterSecurityResponse) {
	response = &DescribeClusterSecurityResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 集群的密钥信息
func (c *Client) DescribeClusterSecurity(request *DescribeClusterSecurityRequest) (response *DescribeClusterSecurityResponse, err error) {
	if request == nil {
		request = NewDescribeClusterSecurityRequest()
	}
	response = NewDescribeClusterSecurityResponse()
	err = c.Send(request, response)
	return
}

func NewUpgradeClusterReleaseRequest() (request *UpgradeClusterReleaseRequest) {
	request = &UpgradeClusterReleaseRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("tke", APIVersion, "UpgradeClusterRelease")
	return
}

func NewUpgradeClusterReleaseResponse() (response *UpgradeClusterReleaseResponse) {
	response = &UpgradeClusterReleaseResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 在应用市场中集群升级应用
func (c *Client) UpgradeClusterRelease(request *UpgradeClusterReleaseRequest) (response *UpgradeClusterReleaseResponse, err error) {
	if request == nil {
		request = NewUpgradeClusterReleaseRequest()
	}
	response = NewUpgradeClusterReleaseResponse()
	err = c.Send(request, response)
	return
}

func NewGetEKSClusterResourcesRequest() (request *GetEKSClusterResourcesRequest) {
	request = &GetEKSClusterResourcesRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("tke", APIVersion, "GetEKSClusterResources")
	return
}

func NewGetEKSClusterResourcesResponse() (response *GetEKSClusterResourcesResponse) {
	response = &GetEKSClusterResourcesResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 查询EKS集群资源资源信息
func (c *Client) GetEKSClusterResources(request *GetEKSClusterResourcesRequest) (response *GetEKSClusterResourcesResponse, err error) {
	if request == nil {
		request = NewGetEKSClusterResourcesRequest()
	}
	response = NewGetEKSClusterResourcesResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeOpenPolicyListRequest() (request *DescribeOpenPolicyListRequest) {
	request = &DescribeOpenPolicyListRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}

	request.Init().WithApiInfo("tke", APIVersion, "DescribeOpenPolicyList")

	return
}

func NewDescribeOpenPolicyListResponse() (response *DescribeOpenPolicyListResponse) {
	response = &DescribeOpenPolicyListResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return

}

// DescribeOpenPolicyList
// 查询opa策略列表
func (c *Client) DescribeOpenPolicyList(request *DescribeOpenPolicyListRequest) (response *DescribeOpenPolicyListResponse, err error) {
	return c.DescribeOpenPolicyListWithContext(context.Background(), request)
}

// DescribeOpenPolicyList
// 查询opa策略列表
func (c *Client) DescribeOpenPolicyListWithContext(ctx context.Context, request *DescribeOpenPolicyListRequest) (response *DescribeOpenPolicyListResponse, err error) {
	if request == nil {
		request = NewDescribeOpenPolicyListRequest()
	}

	if c.GetCredential() == nil {
		return nil, errors.New("DescribeOpenPolicyList require credential")
	}

	request.SetContext(ctx)

	response = NewDescribeOpenPolicyListResponse()
	err = c.Send(request, response)
	return
}

func NewEnableEksAuditRequest() (request *EnableEksAuditRequest) {
	request = &EnableEksAuditRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}

	request.Init().WithApiInfo("tke", APIVersion, "EnableEksAudit")

	return
}

func NewEnableEksAuditResponse() (response *EnableEksAuditResponse) {
	response = &EnableEksAuditResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return

}

// EnableEksAudit
// 开启弹性集群审计
//
// 可能返回的错误码:
//
//	FAILEDOPERATION_CREATECLSCLIENT = "FailedOperation.CreateClsClient"
//	FAILEDOPERATION_CREATECLSINDEX = "FailedOperation.CreateClsIndex"
//	FAILEDOPERATION_CREATECLSMACHINEGROUP = "FailedOperation.CreateClsMachineGroup"
//	FAILEDOPERATION_CREATECLSTOPIC = "FailedOperation.CreateClsTopic"
//	FAILEDOPERATION_DB = "FailedOperation.Db"
//	FAILEDOPERATION_DBRECORDNOTFOUND = "FailedOperation.DbRecordNotFound"
//	FAILEDOPERATION_GETCLSINDEX = "FailedOperation.GetClsIndex"
//	FAILEDOPERATION_GETCLSMACHINEGROUP = "FailedOperation.GetClsMachineGroup"
//	FAILEDOPERATION_KUBERNETESDELETEOPERATIONERROR = "FailedOperation.KubernetesDeleteOperationError"
//	FAILEDOPERATION_KUBERNETESPATCHOPERATIONERROR = "FailedOperation.KubernetesPatchOperationError"
//	FAILEDOPERATION_MODIFYCLSINDEX = "FailedOperation.ModifyClsIndex"
//	INTERNALERROR = "InternalError"
//	INVALIDPARAMETER = "InvalidParameter"
//	LIMITEXCEEDED = "LimitExceeded"
//	RESOURCENOTFOUND = "ResourceNotFound"
//	RESOURCEUNAVAILABLE = "ResourceUnavailable"
func (c *Client) EnableEksAudit(request *EnableEksAuditRequest) (response *EnableEksAuditResponse, err error) {
	return c.EnableEksAuditWithContext(context.Background(), request)
}

// EnableEksAudit
// 开启弹性集群审计
//
// 可能返回的错误码:
//
//	FAILEDOPERATION_CREATECLSCLIENT = "FailedOperation.CreateClsClient"
//	FAILEDOPERATION_CREATECLSINDEX = "FailedOperation.CreateClsIndex"
//	FAILEDOPERATION_CREATECLSMACHINEGROUP = "FailedOperation.CreateClsMachineGroup"
//	FAILEDOPERATION_CREATECLSTOPIC = "FailedOperation.CreateClsTopic"
//	FAILEDOPERATION_DB = "FailedOperation.Db"
//	FAILEDOPERATION_DBRECORDNOTFOUND = "FailedOperation.DbRecordNotFound"
//	FAILEDOPERATION_GETCLSINDEX = "FailedOperation.GetClsIndex"
//	FAILEDOPERATION_GETCLSMACHINEGROUP = "FailedOperation.GetClsMachineGroup"
//	FAILEDOPERATION_KUBERNETESDELETEOPERATIONERROR = "FailedOperation.KubernetesDeleteOperationError"
//	FAILEDOPERATION_KUBERNETESPATCHOPERATIONERROR = "FailedOperation.KubernetesPatchOperationError"
//	FAILEDOPERATION_MODIFYCLSINDEX = "FailedOperation.ModifyClsIndex"
//	INTERNALERROR = "InternalError"
//	INVALIDPARAMETER = "InvalidParameter"
//	LIMITEXCEEDED = "LimitExceeded"
//	RESOURCENOTFOUND = "ResourceNotFound"
//	RESOURCEUNAVAILABLE = "ResourceUnavailable"
func (c *Client) EnableEksAuditWithContext(ctx context.Context, request *EnableEksAuditRequest) (response *EnableEksAuditResponse, err error) {
	if request == nil {
		request = NewEnableEksAuditRequest()
	}

	if c.GetCredential() == nil {
		return nil, errors.New("EnableEksAudit require credential")
	}

	request.SetContext(ctx)

	response = NewEnableEksAuditResponse()
	err = c.Send(request, response)
	return
}

func NewModifyOpenPolicyListRequest() (request *ModifyOpenPolicyListRequest) {
	request = &ModifyOpenPolicyListRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}

	request.Init().WithApiInfo("tke", APIVersion, "ModifyOpenPolicyList")

	return
}

func NewModifyOpenPolicyListResponse() (response *ModifyOpenPolicyListResponse) {
	response = &ModifyOpenPolicyListResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return

}

// ModifyOpenPolicyList
// 批量修改opa策略
func (c *Client) ModifyOpenPolicyList(request *ModifyOpenPolicyListRequest) (response *ModifyOpenPolicyListResponse, err error) {
	return c.ModifyOpenPolicyListWithContext(context.Background(), request)
}

// ModifyOpenPolicyList
// 批量修改opa策略
func (c *Client) ModifyOpenPolicyListWithContext(ctx context.Context, request *ModifyOpenPolicyListRequest) (response *ModifyOpenPolicyListResponse, err error) {
	if request == nil {
		request = NewModifyOpenPolicyListRequest()
	}

	if c.GetCredential() == nil {
		return nil, errors.New("ModifyOpenPolicyList require credential")
	}

	request.SetContext(ctx)

	response = NewModifyOpenPolicyListResponse()
	err = c.Send(request, response)
	return
}
