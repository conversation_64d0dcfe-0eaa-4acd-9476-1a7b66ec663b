package tke

import (
	"context"
	"encoding/json"

	tcerr "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/errors"
	tchttp "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/http"
)

// Predefined struct for user
type UpdateMetaFeatureRequestParams struct {
	// 集群ID
	ClusterId *string `json:"ClusterId,omitempty" name:"ClusterId"`

	// 更新后的跨租户网卡配置，注意需填写完整的信息，暂不支持更新部分字段
	TenantParam *TenantParam `json:"TenantParam,omitempty" name:"TenantParam"`
}

type UpdateMetaFeatureRequest struct {
	*tchttp.BaseRequest

	// 集群ID
	ClusterId *string `json:"ClusterId,omitempty" name:"ClusterId"`

	// 更新后的跨租户网卡配置，注意需填写完整的信息，暂不支持更新部分字段
	TenantParam *TenantParam `json:"TenantParam,omitempty" name:"TenantParam"`
}

func (r *UpdateMetaFeatureRequest) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// FromJsonString It is highly **NOT** recommended to use this function
// because it has no param check, nor strict type check
func (r *UpdateMetaFeatureRequest) FromJsonString(s string) error {
	f := make(map[string]interface{})
	if err := json.Unmarshal([]byte(s), &f); err != nil {
		return err
	}
	delete(f, "ClusterId")
	delete(f, "TenantParam")
	if len(f) > 0 {
		return tcerr.NewTencentCloudSDKError("ClientError.BuildRequestError", "UpdateMetaFeatureRequest has unknown keys!", "")
	}
	return json.Unmarshal([]byte(s), &r)
}

// Predefined struct for user
type UpdateMetaFeatureResponseParams struct {
	// 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
	RequestId *string `json:"RequestId,omitempty" name:"RequestId"`
}

type UpdateMetaFeatureResponse struct {
	*tchttp.BaseResponse
	Response *UpdateMetaFeatureResponseParams `json:"Response"`
}

func (r *UpdateMetaFeatureResponse) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// FromJsonString It is highly **NOT** recommended to use this function
// because it has no param check, nor strict type check
func (r *UpdateMetaFeatureResponse) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

// Predefined struct for user
type UpdateMetaFeatureForEksResponseParams struct {
	// 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
	RequestId *string `json:"RequestId,omitempty" name:"RequestId"`
}

type UpdateMetaFeatureForEksResponse struct {
	*tchttp.BaseResponse
	Response *UpdateMetaFeatureForEksResponseParams `json:"Response"`
}

func (r *UpdateMetaFeatureForEksResponse) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// FromJsonString It is highly **NOT** recommended to use this function
// because it has no param check, nor strict type check
func (r *UpdateMetaFeatureForEksResponse) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

// Predefined struct for user
type UpdateMetaFeatureForEksRequestParams struct {
	// 集群Id
	ClusterId *string `json:"ClusterId,omitempty" name:"ClusterId"`

	// 目前仅支持填写crossTenant
	FeatureType *string `json:"FeatureType,omitempty" name:"FeatureType"`

	// 租户信息
	TenantParam *TenantParam `json:"TenantParam,omitempty" name:"TenantParam"`

	// 业务名称
	Business *string `json:"Business,omitempty" name:"Business"`
}

type UpdateMetaFeatureForEksRequest struct {
	*tchttp.BaseRequest

	// 集群Id
	ClusterId *string `json:"ClusterId,omitempty" name:"ClusterId"`

	// 目前仅支持填写crossTenant
	FeatureType *string `json:"FeatureType,omitempty" name:"FeatureType"`

	// 租户信息
	TenantParam *TenantParam `json:"TenantParam,omitempty" name:"TenantParam"`

	// 业务名称
	Business *string `json:"Business,omitempty" name:"Business"`
}

func (r *UpdateMetaFeatureForEksRequest) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// FromJsonString It is highly **NOT** recommended to use this function
// because it has no param check, nor strict type check
func (r *UpdateMetaFeatureForEksRequest) FromJsonString(s string) error {
	f := make(map[string]interface{})
	if err := json.Unmarshal([]byte(s), &f); err != nil {
		return err
	}
	delete(f, "ClusterId")
	delete(f, "FeatureType")
	delete(f, "TenantParam")
	delete(f, "Business")
	if len(f) > 0 {
		return tcerr.NewTencentCloudSDKError("ClientError.BuildRequestError", "UpdateMetaFeatureForEksRequest has unknown keys!", "")
	}
	return json.Unmarshal([]byte(s), &r)
}

type EnableMetaFeatureForEksRequest struct {
	*tchttp.BaseRequest
	// 集群ID
	ClusterId   *string      `json:"ClusterId,omitempty" name:"ClusterId"`
	FeatureType *string      `json:"FeatureType,omitempty" name:"FeatureType"`
	Business    *string      `json:"Business,omitempty" name:"Business"`
	RouteConfig []*RouteItem `json:"RouteConfig,omitempty" name:"RouteConfig"`
	TenantParam *TenantParam `json:"TenantParam,omitempty" name:"TenantParam"`
}

func (e *EnableMetaFeatureForEksRequest) ToJsonString() string {
	b, _ := json.Marshal(e)
	return string(b)
}

type EnableMetaFeatureRequest struct {
	*tchttp.BaseRequest
	// 集群ID
	ClusterId   *string      `json:"ClusterId,omitempty" name:"ClusterId"`
	FeatureType *string      `json:"FeatureType,omitempty" name:"FeatureType"`
	NeedVpcLb   *bool        `json:"NeedVpcLb,omitempty" name:"NeedVpcLb"`
	RouteConfig []*RouteItem `json:"RouteConfig,omitempty" name:"RouteConfig"`
	TenantParam *TenantParam `json:"TenantParam,omitempty" name:"TenantParam"`
	StaticMode  *bool        `json:"StaticMode,omitempty" name:"StaticMode"`
}

func (e *EnableMetaFeatureRequest) ToJsonString() string {
	b, _ := json.Marshal(e)
	return string(b)
}

type EnableMetaFeatureResponse struct {
	*tchttp.BaseResponse
	Response *struct {
		// 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
		RequestId *string `json:"RequestId,omitempty" name:"RequestId"`
	} `json:"Response"`
}

func (d *EnableMetaFeatureResponse) ToJsonString() string {
	b, _ := json.Marshal(d)
	return string(b)
}

type ModifyClusterSchedulerPolicyRequest struct {
	*tchttp.BaseRequest
	// 集群ID
	ClusterId  *string                    `json:"ClusterId,omitempty" name:"ClusterId"`
	Priorities []*SchedulerPolicyPriority `json:"Priorities,omitempty" name:"Priorities"`
}

func (e *ModifyClusterSchedulerPolicyRequest) ToJsonString() string {
	b, _ := json.Marshal(e)
	return string(b)
}

type ModifyClusterSchedulerPolicyResponse struct {
	*tchttp.BaseResponse
	Response *struct {
		// 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
		RequestId *string `json:"RequestId,omitempty" name:"RequestId"`
	} `json:"Response"`
}

func (d *ModifyClusterSchedulerPolicyResponse) ToJsonString() string {
	b, _ := json.Marshal(d)
	return string(b)
}

type DescribeEksMetaFeatureProgressRequest struct {
	*tchttp.BaseRequest

	// 集群ID
	ClusterId *string `json:"ClusterId,omitempty" name:"ClusterId"`
}

type DescribeMetaFeatureProgressRequest struct {
	*tchttp.BaseRequest

	// 集群ID
	ClusterId *string `json:"ClusterId,omitempty" name:"ClusterId"`
}

type DescribeEksMetaFeatureProgressResponse struct {
	*tchttp.BaseResponse

	Response *struct {
		Status *string
		// 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
		RequestId *string `json:"RequestId,omitempty" name:"RequestId"`
	} `json:"Response"`
}

func (d *DescribeEksMetaFeatureProgressResponse) ToJsonString() string {
	b, _ := json.Marshal(d)
	return string(b)
}

type DescribeMetaFeatureProgressResponse struct {
	*tchttp.BaseResponse

	Response *struct {
		Status   *string
		Progress []*Step
		// 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
		RequestId *string `json:"RequestId,omitempty" name:"RequestId"`
	} `json:"Response"`
}

func (d *DescribeMetaFeatureProgressResponse) ToJsonString() string {
	b, _ := json.Marshal(d)
	return string(b)
}

type RouteItem struct {
	Subnet string `json:"Subnet,omitempty" name:"Subnet"`
	Type   string `json:"Type,omitempty" name:"Type"`
	Dev    string `json:"Dev,omitempty" name:"Dev"`
}

type TenantParam struct {
	AppId     uint64 `json:"AppId,omitempty" name:"AppId"`
	Uin       string `json:"Uin,omitempty" name:"Uin"`
	UniqVpcId string `json:"UniqVpcId,omitempty" name:"UniqVpcId"`
	SubnetId  string `json:"SubnetId,omitempty" name:"SubnetId"`
	ENILimit  int64  `json:"ENILimit,omitempty" name:"ENILimit"`
}

type Step struct {
	Name    string `json:"Name,omitempty" name:"Name"`
	Status  string `json:"Status,omitempty" name:"Status"`
	Message string `json:"Message,omitempty" name:"Message"`
	StartAt string `json:"StartAt,omitempty" name:"StartAt"`
	EndAt   string `json:"EndAt,omitempty" name:"EndAt"`
}

type SchedulerPolicyPriority struct {
	Name   string `json:"Name,omitempty" name:"Name"`
	Weight int64  `json:"Weight,omitempty" name:"Weight"`
}

type ForwardEKSApplicationRequestV3Request struct {
	*tchttp.BaseRequest
	// 请求集群addon的访问
	Method *string `json:"Method,omitempty" name:"Method"`
	// 请求集群addon的路径
	Path *string `json:"Path,omitempty" name:"Path"`
	// 集群名称
	ClusterName *string `json:"ClusterName,omitempty" name:"ClusterName"`
}

type ForwardEKSApplicationRequestV3Response struct {
	*tchttp.BaseResponse
	Response *struct {
		// 请求集群addon后返回的数据
		ResponseBody *string `json:"ResponseBody,omitempty" name:"ResponseBody"`
		// 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
		RequestId *string `json:"RequestId,omitempty" name:"RequestId"`
	} `json:"Response"`
}

type ForwardEKSApp struct {
	Status *ForwardEKSAppStatus `json:"status"`
}

type ForwardEKSAppStatus struct {
	Phase string `json:"phase"`
}

/****************************************tke创建集群接口****************************************/
type CreateClusterRequest struct {
	*tchttp.BaseRequest

	// 集群容器网络配置信息
	ClusterCIDRSettings *ClusterCIDRSettings `json:"ClusterCIDRSettings,omitempty" name:"ClusterCIDRSettings"`

	// 集群类型，托管集群：MANAGED_CLUSTER，独立集群：INDEPENDENT_CLUSTER。
	ClusterType *string `json:"ClusterType,omitempty" name:"ClusterType"`

	// CVM创建透传参数，json化字符串格式，详见[CVM创建实例](https://cloud.tencent.com/document/product/213/15730)接口。总机型(包括地域)数量不超过10个，相同机型(地域)购买多台机器可以通过设置参数中RunInstances中InstanceCount来实现。
	RunInstancesForNode []*RunInstancesForNode `json:"RunInstancesForNode,omitempty" name:"RunInstancesForNode"`

	// 集群的基本配置信息
	ClusterBasicSettings *ClusterBasicSettings `json:"ClusterBasicSettings,omitempty" name:"ClusterBasicSettings"`

	// 集群高级配置信息
	ClusterAdvancedSettings *ClusterAdvancedSettings `json:"ClusterAdvancedSettings,omitempty" name:"ClusterAdvancedSettings"`

	// 节点高级配置信息
	InstanceAdvancedSettings *InstanceAdvancedSettings `json:"InstanceAdvancedSettings,omitempty" name:"InstanceAdvancedSettings"`

	// 已存在实例的配置信息。所有实例必须在同一个VPC中，最大数量不超过100，不支持添加竞价实例。
	ExistedInstancesForNode []*ExistedInstancesForNode `json:"ExistedInstancesForNode,omitempty" name:"ExistedInstancesForNode"`

	// CVM类型和其对应的数据盘挂载配置信息
	InstanceDataDiskMountSettings []*InstanceDataDiskMountSetting `json:"InstanceDataDiskMountSettings,omitempty" name:"InstanceDataDiskMountSettings"`

	// 需要安装的扩展组件信息
	ExtensionAddons []*ExtensionAddon `json:"ExtensionAddons,omitempty" name:"ExtensionAddons"`
}

func (r *CreateClusterRequest) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

type ExtensionAddon struct {
	// 扩展组件名称
	AddonName *string `json:"AddonName,omitempty" name:"AddonName"`

	// 扩展组件信息(扩展组件资源对象的json字符串描述)
	AddonParam *string `json:"AddonParam,omitempty" name:"AddonParam"`
}

type InstanceDataDiskMountSetting struct {
	// CVM实例类型
	InstanceType *string `json:"InstanceType,omitempty" name:"InstanceType"`

	// 数据盘挂载信息
	DataDisks []*DataDisk `json:"DataDisks,omitempty" name:"DataDisks"`

	// CVM实例所属可用区
	Zone *string `json:"Zone,omitempty" name:"Zone"`
}

type ExistedInstancesForNode struct {
	// 节点角色，取值:MASTER_ETCD, WORKER。MASTER_ETCD只有在创建 INDEPENDENT_CLUSTER 独立集群时需要指定。MASTER_ETCD节点数量为3～7，建议为奇数。MASTER_ETCD最小配置为4C8G。
	NodeRole *string `json:"NodeRole,omitempty" name:"NodeRole"`

	// 已存在实例的重装参数
	ExistedInstancesPara *ExistedInstancesPara `json:"ExistedInstancesPara,omitempty" name:"ExistedInstancesPara"`

	// 节点高级设置，会覆盖集群级别设置的InstanceAdvancedSettings（当前只对节点自定义参数ExtraArgs生效）
	InstanceAdvancedSettingsOverride *InstanceAdvancedSettings `json:"InstanceAdvancedSettingsOverride,omitempty" name:"InstanceAdvancedSettingsOverride"`

	// 自定义模式集群，可指定每个节点的pod数量
	DesiredPodNumbers []*int64 `json:"DesiredPodNumbers,omitempty" name:"DesiredPodNumbers"`
}

type ExistedInstancesPara struct {
	// 集群ID
	InstanceIds []*string `json:"InstanceIds,omitempty" name:"InstanceIds"`

	// 实例额外需要设置参数信息
	InstanceAdvancedSettings *InstanceAdvancedSettings `json:"InstanceAdvancedSettings,omitempty" name:"InstanceAdvancedSettings"`

	// 增强服务。通过该参数可以指定是否开启云安全、云监控等服务。若不指定该参数，则默认开启云监控、云安全服务。
	EnhancedService *EnhancedService `json:"EnhancedService,omitempty" name:"EnhancedService"`

	// 节点登录信息（目前仅支持使用Password或者单个KeyIds）
	LoginSettings *LoginSettings `json:"LoginSettings,omitempty" name:"LoginSettings"`

	// 实例所属安全组。该参数可以通过调用 DescribeSecurityGroups 的返回值中的sgId字段来获取。若不指定该参数，则绑定默认安全组。
	SecurityGroupIds []*string `json:"SecurityGroupIds,omitempty" name:"SecurityGroupIds"`

	// 重装系统时，可以指定修改实例的HostName(集群为HostName模式时，此参数必传，规则名称除不支持大写字符外与[CVM创建实例](https://cloud.tencent.com/document/product/213/15730)接口HostName一致)
	HostName *string `json:"HostName,omitempty" name:"HostName"`
}

type LoginSettings struct {
	// 实例登录密码。不同操作系统类型密码复杂度限制不一样，具体如下：<br><li>Linux实例密码必须8到30位，至少包括两项[a-z]，[A-Z]、[0-9] 和 [( ) \` ~ ! @ # $ % ^ & *  - + = | { } [ ] : ; ' , . ? / ]中的特殊符号。<br><li>Windows实例密码必须12到30位，至少包括三项[a-z]，[A-Z]，[0-9] 和 [( ) \` ~ ! @ # $ % ^ & * - + = | { } [ ] : ; ' , . ? /]中的特殊符号。<br><br>若不指定该参数，则由系统随机生成密码，并通过站内信方式通知到用户。
	// 注意：此字段可能返回 null，表示取不到有效值。
	Password *string `json:"Password,omitempty" name:"Password"`

	// 密钥ID列表。关联密钥后，就可以通过对应的私钥来访问实例；KeyId可通过接口[DescribeKeyPairs](https://cloud.tencent.com/document/api/213/15699)获取，密钥与密码不能同时指定，同时Windows操作系统不支持指定密钥。
	// 注意：此字段可能返回 null，表示取不到有效值。
	KeyIds []*string `json:"KeyIds,omitempty" name:"KeyIds"`

	// 保持镜像的原始设置。该参数与Password或KeyIds.N不能同时指定。只有使用自定义镜像、共享镜像或外部导入镜像创建实例时才能指定该参数为TRUE。取值范围：<br><li>TRUE：表示保持镜像的登录设置<br><li>FALSE：表示不保持镜像的登录设置<br><br>默认取值：FALSE。
	// 注意：此字段可能返回 null，表示取不到有效值。
	KeepImageLogin *string `json:"KeepImageLogin,omitempty" name:"KeepImageLogin"`
}

type EnhancedService struct {
	// 开启云安全服务。若不指定该参数，则默认开启云安全服务。
	SecurityService *RunSecurityServiceEnabled `json:"SecurityService,omitempty" name:"SecurityService"`

	// 开启云监控服务。若不指定该参数，则默认开启云监控服务。
	MonitorService *RunMonitorServiceEnabled `json:"MonitorService,omitempty" name:"MonitorService"`

	// 开启云自动化助手服务（TencentCloud Automation Tools，TAT）。若不指定该参数，则公共镜像默认开启云自动化助手服务，其他镜像默认不开启云自动化助手服务。
	AutomationService *RunAutomationServiceEnabled `json:"AutomationService,omitempty" name:"AutomationService"`
}

type RunSecurityServiceEnabled struct {
	// 是否开启[云安全](/document/product/296)服务。取值范围：<br><li>TRUE：表示开启云安全服务<br><li>FALSE：表示不开启云安全服务<br><br>默认取值：TRUE。
	Enabled *bool `json:"Enabled,omitempty" name:"Enabled"`
}

type RunMonitorServiceEnabled struct {
	// 是否开启[云监控](/document/product/248)服务。取值范围：<br><li>TRUE：表示开启云监控服务<br><li>FALSE：表示不开启云监控服务<br><br>默认取值：TRUE。
	Enabled *bool `json:"Enabled,omitempty" name:"Enabled"`
}

type RunAutomationServiceEnabled struct {
	// 是否开启云自动化助手。取值范围：<br><li>TRUE：表示开启云自动化助手服务<br><li>FALSE：表示不开启云自动化助手服务<br><br>默认取值：FALSE。
	Enabled *bool `json:"Enabled,omitempty" name:"Enabled"`
}

type ClusterAdvancedSettings struct {
	// 是否启用IPVS
	IPVS *bool `json:"IPVS,omitempty" name:"IPVS"`

	// 是否启用集群节点自动扩缩容(创建集群流程不支持开启此功能)
	AsEnabled *bool `json:"AsEnabled,omitempty" name:"AsEnabled"`

	// 集群使用的runtime类型，包括"docker"和"containerd"两种类型，默认为"docker"
	ContainerRuntime *string `json:"ContainerRuntime,omitempty" name:"ContainerRuntime"`

	// 集群中节点NodeName类型（包括 hostname,lan-ip两种形式，默认为lan-ip。如果开启了hostname模式，创建节点时需要设置HostName参数，并且InstanceName需要和HostName一致）
	NodeNameType *string `json:"NodeNameType,omitempty" name:"NodeNameType"`

	// 集群自定义参数
	ExtraArgs *ClusterExtraArgs `json:"ExtraArgs,omitempty" name:"ExtraArgs"`

	// 集群网络类型（包括GR(全局路由)和VPC-CNI两种模式，默认为GR。
	NetworkType *string `json:"NetworkType,omitempty" name:"NetworkType"`

	// 集群VPC-CNI模式是否为非固定IP，默认: FALSE 固定IP。
	IsNonStaticIpMode *bool `json:"IsNonStaticIpMode,omitempty" name:"IsNonStaticIpMode"`

	// 是否启用集群删除保护
	DeletionProtection *bool `json:"DeletionProtection,omitempty" name:"DeletionProtection"`

	// 集群的网络代理模型，目前tke集群支持的网络代理模式有三种：iptables,ipvs,ipvs-bpf，此参数仅在使用ipvs-bpf模式时使用，三种网络模式的参数设置关系如下：
	// iptables模式：IPVS和KubeProxyMode都不设置
	// ipvs模式: 设置IPVS为true, KubeProxyMode不设置
	// ipvs-bpf模式: 设置KubeProxyMode为kube-proxy-bpf
	// 使用ipvs-bpf的网络模式需要满足以下条件：
	// 1. 集群版本必须为1.14及以上；
	// 2. 系统镜像必须是: Tencent Linux 2.4；
	KubeProxyMode *string `json:"KubeProxyMode,omitempty" name:"KubeProxyMode"`

	// 是否开启审计开关
	AuditEnabled *bool `json:"AuditEnabled,omitempty" name:"AuditEnabled"`

	// 审计日志上传到的logset日志集
	AuditLogsetId *string `json:"AuditLogsetId,omitempty" name:"AuditLogsetId"`

	// 审计日志上传到的topic
	AuditLogTopicId *string `json:"AuditLogTopicId,omitempty" name:"AuditLogTopicId"`

	// 区分共享网卡多IP模式和独立网卡模式，共享网卡多 IP 模式填写"tke-route-eni"，独立网卡模式填写"tke-direct-eni"，默认为共享网卡模式
	VpcCniType *string `json:"VpcCniType,omitempty" name:"VpcCniType"`

	// 运行时版本
	RuntimeVersion *string `json:"RuntimeVersion,omitempty" name:"RuntimeVersion"`

	// 是否开节点podCIDR大小的自定义模式
	EnableCustomizedPodCIDR *bool `json:"EnableCustomizedPodCIDR,omitempty" name:"EnableCustomizedPodCIDR"`

	// 自定义模式下的基础pod数量
	BasePodNumber *int64 `json:"BasePodNumber,omitempty" name:"BasePodNumber"`

	// 启用 CiliumMode 的模式，空值表示不启用，“clusterIP” 表示启用 Cilium 支持 ClusterIP
	CiliumMode *string `json:"CiliumMode,omitempty" name:"CiliumMode"`

	// 集群VPC-CNI模式下是否是双栈集群，默认false，表明非双栈集群。
	IsDualStack *bool `json:"IsDualStack,omitempty" name:"IsDualStack"`

	// 是否开启QGPU共享
	QGPUShareEnable *bool `json:"QGPUShareEnable,omitempty" name:"QGPUShareEnable"`
}

type ClusterExtraArgs struct {
	// kube-apiserver自定义参数，参数格式为["k1=v1", "k1=v2"]， 例如["max-requests-inflight=500","feature-gates=PodShareProcessNamespace=true,DynamicKubeletConfig=true"]
	// 注意：此字段可能返回 null，表示取不到有效值。
	KubeAPIServer []*string `json:"KubeAPIServer,omitempty" name:"KubeAPIServer"`

	// kube-controller-manager自定义参数
	// 注意：此字段可能返回 null，表示取不到有效值。
	KubeControllerManager []*string `json:"KubeControllerManager,omitempty" name:"KubeControllerManager"`

	// kube-scheduler自定义参数
	// 注意：此字段可能返回 null，表示取不到有效值。
	KubeScheduler []*string `json:"KubeScheduler,omitempty" name:"KubeScheduler"`

	// etcd自定义参数，只支持独立集群
	// 注意：此字段可能返回 null，表示取不到有效值。
	Etcd []*string `json:"Etcd,omitempty" name:"Etcd"`
}

type ClusterBasicSettings struct {
	// 集群操作系统，支持设置公共镜像(字段传相应镜像Name)和自定义镜像(字段传相应镜像ID)，详情参考：https://cloud.tencent.com/document/product/457/68289
	ClusterOs *string `json:"ClusterOs,omitempty" name:"ClusterOs"`

	// 集群版本,默认值为1.10.5
	ClusterVersion *string `json:"ClusterVersion,omitempty" name:"ClusterVersion"`

	// 集群名称
	ClusterName *string `json:"ClusterName,omitempty" name:"ClusterName"`

	// 集群描述
	ClusterDescription *string `json:"ClusterDescription,omitempty" name:"ClusterDescription"`

	// 私有网络ID，形如vpc-xxx。创建托管空集群时必传。
	VpcId *string `json:"VpcId,omitempty" name:"VpcId"`

	// 集群内新增资源所属项目ID。
	ProjectId *int64 `json:"ProjectId,omitempty" name:"ProjectId"`

	// 标签描述列表。通过指定该参数可以同时绑定标签到相应的资源实例，当前仅支持绑定标签到集群实例。
	TagSpecification []*TagSpecification `json:"TagSpecification,omitempty" name:"TagSpecification"`

	// 容器的镜像版本，"DOCKER_CUSTOMIZE"(容器定制版),"GENERAL"(普通版本，默认值)
	OsCustomizeType *string `json:"OsCustomizeType,omitempty" name:"OsCustomizeType"`

	// 是否开启节点的默认安全组(默认: 否，Alpha特性)
	NeedWorkSecurityGroup *bool `json:"NeedWorkSecurityGroup,omitempty" name:"NeedWorkSecurityGroup"`

	// 当选择Cilium Overlay网络插件时，TKE会从该子网获取2个IP用来创建内网负载均衡
	SubnetId *string `json:"SubnetId,omitempty" name:"SubnetId"`

	// 集群等级，针对托管集群生效
	ClusterLevel *string `json:"ClusterLevel,omitempty" name:"ClusterLevel"`

	// 自动变配集群等级，针对托管集群生效
	AutoUpgradeClusterLevel *AutoUpgradeClusterLevel `json:"AutoUpgradeClusterLevel,omitempty" name:"AutoUpgradeClusterLevel"`
}

type AutoUpgradeClusterLevel struct {
	// 是否开启自动变配集群等级
	IsAutoUpgrade *bool `json:"IsAutoUpgrade,omitempty" name:"IsAutoUpgrade"`
}

type Tag struct {
	// 标签键
	Key *string `json:"Key,omitempty" name:"Key"`

	// 标签值
	Value *string `json:"Value,omitempty" name:"Value"`
}

type TagSpecification struct {
	// 标签绑定的资源类型，当前支持类型："cluster"
	// 注意：此字段可能返回 null，表示取不到有效值。
	ResourceType *string `json:"ResourceType,omitempty" name:"ResourceType"`

	// 标签对列表
	// 注意：此字段可能返回 null，表示取不到有效值。
	Tags []*Tag `json:"Tags,omitempty" name:"Tags"`
}

type ClusterCIDRSettings struct {

	// 用于分配集群容器和服务 IP 的 CIDR，不得与 VPC CIDR 冲突，也不得与同 VPC 内其他集群 CIDR 冲突。且网段范围必须在内网网段内，例如:********/14, ***********/18,**********/16。
	ClusterCIDR *string `json:"ClusterCIDR,omitempty" name:"ClusterCIDR"`

	// 是否忽略 ClusterCIDR 冲突错误, 默认不忽略
	IgnoreClusterCIDRConflict *bool `json:"IgnoreClusterCIDRConflict,omitempty" name:"IgnoreClusterCIDRConflict"`

	// 集群中每个Node上最大的Pod数量。取值范围4～256。不为2的幂值时会向上取最接近的2的幂值。
	MaxNodePodNum *uint64 `json:"MaxNodePodNum,omitempty" name:"MaxNodePodNum"`

	// 集群最大的service数量。取值范围32～32768，不为2的幂值时会向上取最接近的2的幂值。
	MaxClusterServiceNum *uint64 `json:"MaxClusterServiceNum,omitempty" name:"MaxClusterServiceNum"`

	// 用于分配集群服务 IP 的 CIDR，不得与 VPC CIDR 冲突，也不得与同 VPC 内其他集群 CIDR 冲突。且网段范围必须在内网网段内，例如:********/14, ***********/18,**********/16。
	ServiceCIDR *string `json:"ServiceCIDR,omitempty" name:"ServiceCIDR"`

	// VPC-CNI网络模式下，弹性网卡的子网Id。
	EniSubnetIds []*string `json:"EniSubnetIds,omitempty" name:"EniSubnetIds" list`

	// VPC-CNI网络模式下，弹性网卡IP的回收时间，取值范围[300,15768000)
	ClaimExpiredSeconds *int64 `json:"ClaimExpiredSeconds,omitempty" name:"ClaimExpiredSeconds"`

	// 是否忽略 ServiceCIDR 冲突错误, 仅在 VPC-CNI 模式生效，默认不忽略
	IgnoreServiceCIDRConflict *bool `json:"IgnoreServiceCIDRConflict,omitempty" name:"IgnoreServiceCIDRConflict"`
}

type RunInstancesForNode struct {
	// 节点角色，取值:MASTER_ETCD, WORKER。MASTER_ETCD只有在创建 INDEPENDENT_CLUSTER 独立集群时需要指定。MASTER_ETCD节点数量为3～7，建议为奇数。MASTER_ETCD节点最小配置为4C8G。
	NodeRole *string `json:"NodeRole,omitempty" name:"NodeRole"`

	// CVM创建透传参数，json化字符串格式，详见[CVM创建实例](https://cloud.tencent.com/document/product/213/15730)接口，传入公共参数外的其他参数即可，其中ImageId会替换为TKE集群OS对应的镜像。
	RunInstancesPara []*string `json:"RunInstancesPara,omitempty" name:"RunInstancesPara"`

	// 节点高级设置，该参数会覆盖集群级别设置的InstanceAdvancedSettings，和上边的RunInstancesPara按照顺序一一对应（当前只对节点自定义参数ExtraArgs生效）。
	InstanceAdvancedSettingsOverrides []*InstanceAdvancedSettings `json:"InstanceAdvancedSettingsOverrides,omitempty" name:"InstanceAdvancedSettingsOverrides"`
}

type InstanceAdvancedSettings struct {
	// 该节点属于podCIDR大小自定义模式时，可指定节点上运行的pod数量上限
	// 注意：此字段可能返回 null，表示取不到有效值。
	DesiredPodNumber *int64 `json:"DesiredPodNumber,omitempty" name:"DesiredPodNumber"`

	// GPU驱动相关参数
	// 注意：此字段可能返回 null，表示取不到有效值。
	GPUArgs *GPUArgs `json:"GPUArgs,omitempty" name:"GPUArgs"`

	// base64 编码的用户脚本，在初始化节点之前执行，目前只对添加已有节点生效
	// 注意：此字段可能返回 null，表示取不到有效值。
	PreStartUserScript *string `json:"PreStartUserScript,omitempty" name:"PreStartUserScript"`

	// 节点污点
	// 注意：此字段可能返回 null，表示取不到有效值。
	Taints []*Taint `json:"Taints,omitempty" name:"Taints"`

	// 数据盘挂载点, 默认不挂载数据盘. 已格式化的 ext3，ext4，xfs 文件系统的数据盘将直接挂载，其他文件系统或未格式化的数据盘将自动格式化为ext4 (tlinux系统格式化成xfs)并挂载，请注意备份数据! 无数据盘或有多块数据盘的云主机此设置不生效。
	// 注意，注意，多盘场景请使用下方的DataDisks数据结构，设置对应的云盘类型、云盘大小、挂载路径、是否格式化等信息。
	// 注意：此字段可能返回 null，表示取不到有效值。
	MountTarget *string `json:"MountTarget,omitempty" name:"MountTarget"`

	// dockerd --graph 指定值, 默认为 /var/lib/docker
	// 注意：此字段可能返回 null，表示取不到有效值。
	DockerGraphPath *string `json:"DockerGraphPath,omitempty" name:"DockerGraphPath"`

	// base64 编码的用户脚本, 此脚本会在 k8s 组件运行后执行, 需要用户保证脚本的可重入及重试逻辑, 脚本及其生成的日志文件可在节点的 /data/ccs_userscript/ 路径查看, 如果要求节点需要在进行初始化完成后才可加入调度, 可配合 unschedulable 参数使用, 在 userScript 最后初始化完成后, 添加 kubectl uncordon nodename --kubeconfig=/root/.kube/config 命令使节点加入调度
	// 注意：此字段可能返回 null，表示取不到有效值。
	UserScript *string `json:"UserScript,omitempty" name:"UserScript"`

	// 设置加入的节点是否参与调度，默认值为0，表示参与调度；非0表示不参与调度, 待节点初始化完成之后, 可执行kubectl uncordon nodename使node加入调度.
	Unschedulable *int64 `json:"Unschedulable,omitempty" name:"Unschedulable"`

	// 节点Label数组
	// 注意：此字段可能返回 null，表示取不到有效值。
	Labels []*Label `json:"Labels,omitempty" name:"Labels"`

	// 多盘数据盘挂载信息：新建节点时请确保购买CVM的参数传递了购买多个数据盘的信息，如CreateClusterInstances API的RunInstancesPara下的DataDisks也需要设置购买多个数据盘, 具体可以参考CreateClusterInstances接口的添加集群节点(多块数据盘)样例；添加已有节点时，请确保填写的分区信息在节点上真实存在
	// 注意：此字段可能返回 null，表示取不到有效值。
	DataDisks []*DataDisk `json:"DataDisks,omitempty" name:"DataDisks"`

	// 节点相关的自定义参数信息
	// 注意：此字段可能返回 null，表示取不到有效值。
	ExtraArgs *InstanceExtraArgs `json:"ExtraArgs,omitempty" name:"ExtraArgs"`
}

type InstanceExtraArgs struct {
	// kubelet自定义参数，参数格式为["k1=v1", "k1=v2"]， 例如["root-dir=/var/lib/kubelet","feature-gates=PodShareProcessNamespace=true,DynamicKubeletConfig=true"]
	// 注意：此字段可能返回 null，表示取不到有效值。
	Kubelet []*string `json:"Kubelet,omitempty" name:"Kubelet"`
}

type DataDisk struct {
	// 云盘类型
	// 注意：此字段可能返回 null，表示取不到有效值。
	DiskType *string `json:"DiskType,omitempty" name:"DiskType"`

	// 文件系统(ext3/ext4/xfs)
	// 注意：此字段可能返回 null，表示取不到有效值。
	FileSystem *string `json:"FileSystem,omitempty" name:"FileSystem"`

	// 云盘大小(G）
	// 注意：此字段可能返回 null，表示取不到有效值。
	DiskSize *int64 `json:"DiskSize,omitempty" name:"DiskSize"`

	// 是否自动化格式盘并挂载
	// 注意：此字段可能返回 null，表示取不到有效值。
	AutoFormatAndMount *bool `json:"AutoFormatAndMount,omitempty" name:"AutoFormatAndMount"`

	// 挂载目录
	// 注意：此字段可能返回 null，表示取不到有效值。
	MountTarget *string `json:"MountTarget,omitempty" name:"MountTarget"`

	// 挂载设备名或分区名，当且仅当添加已有节点时需要
	// 注意：此字段可能返回 null，表示取不到有效值。
	DiskPartition *string `json:"DiskPartition,omitempty" name:"DiskPartition"`
}

type Label struct {
	// map表中的Name
	Name *string `json:"Name,omitempty" name:"Name"`

	// map表中的Value
	Value *string `json:"Value,omitempty" name:"Value"`
}

type Taint struct {
	// Key
	Key *string `json:"Key,omitempty" name:"Key"`

	// Value
	Value *string `json:"Value,omitempty" name:"Value"`

	// Effect
	Effect *string `json:"Effect,omitempty" name:"Effect"`
}

type GPUArgs struct {
	// 是否启用MIG特性
	// 注意：此字段可能返回 null，表示取不到有效值。
	MIGEnable *bool `json:"MIGEnable,omitempty" name:"MIGEnable"`

	// GPU驱动版本信息
	Driver *DriverVersion `json:"Driver,omitempty" name:"Driver"`

	// CUDA版本信息
	// 注意：此字段可能返回 null，表示取不到有效值。
	CUDA *DriverVersion `json:"CUDA,omitempty" name:"CUDA"`

	// cuDNN版本信息
	// 注意：此字段可能返回 null，表示取不到有效值。
	CUDNN *CUDNN `json:"CUDNN,omitempty" name:"CUDNN"`

	// 自定义GPU驱动信息
	// 注意：此字段可能返回 null，表示取不到有效值。
	CustomDriver *CustomDriver `json:"CustomDriver,omitempty" name:"CustomDriver"`
}

type DriverVersion struct {
	// GPU驱动或者CUDA的版本
	Version *string `json:"Version,omitempty" name:"Version"`

	// GPU驱动或者CUDA的名字
	Name *string `json:"Name,omitempty" name:"Name"`
}

type CUDNN struct {
	// cuDNN的版本
	Version *string `json:"Version,omitempty" name:"Version"`

	// cuDNN的名字
	Name *string `json:"Name,omitempty" name:"Name"`

	// cuDNN的Doc名字
	DocName *string `json:"DocName,omitempty" name:"DocName"`

	// cuDNN的Dev名字
	DevName *string `json:"DevName,omitempty" name:"DevName"`
}

type CustomDriver struct {
	// 自定义GPU驱动地址链接
	// 注意：此字段可能返回 null，表示取不到有效值。
	Address *string `json:"Address,omitempty" name:"Address"`
}

type CreateClusterResponseParams struct {
	// 集群ID
	ClusterId *string `json:"ClusterId,omitempty" name:"ClusterId"`

	// 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
	RequestId *string `json:"RequestId,omitempty" name:"RequestId"`
}

type CreateClusterResponse struct {
	*tchttp.BaseResponse
	Response *CreateClusterResponseParams `json:"Response"`
}

func (r *CreateClusterResponse) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

type AddExistedInstancesRequest struct {
	*tchttp.BaseRequest

	// 集群ID
	ClusterId *string `json:"ClusterId,omitempty" name:"ClusterId"`

	// 实例列表，不支持竞价实例
	InstanceIds []*string `json:"InstanceIds,omitempty" name:"InstanceIds"`

	// 实例额外需要设置参数信息(默认值)
	InstanceAdvancedSettings *InstanceAdvancedSettings `json:"InstanceAdvancedSettings,omitempty" name:"InstanceAdvancedSettings"`

	// 增强服务。通过该参数可以指定是否开启云安全、云监控等服务。若不指定该参数，则默认开启云监控、云安全服务。
	EnhancedService *EnhancedService `json:"EnhancedService,omitempty" name:"EnhancedService"`

	// 节点登录信息（目前仅支持使用Password或者单个KeyIds）
	LoginSettings *LoginSettings `json:"LoginSettings,omitempty" name:"LoginSettings"`

	// 重装系统时，可以指定修改实例的HostName(集群为HostName模式时，此参数必传，规则名称除不支持大写字符外与[CVM创建实例](https://cloud.tencent.com/document/product/213/15730)接口HostName一致)
	HostName *string `json:"HostName,omitempty" name:"HostName"`

	// 实例所属安全组。该参数可以通过调用 DescribeSecurityGroups 的返回值中的sgId字段来获取。若不指定该参数，则绑定默认安全组。（目前仅支持设置单个sgId）
	SecurityGroupIds []*string `json:"SecurityGroupIds,omitempty" name:"SecurityGroupIds"`

	// 节点池选项
	NodePool *NodePoolOption `json:"NodePool,omitempty" name:"NodePool"`

	// 校验规则相关选项，可配置跳过某些校验规则。目前支持GlobalRouteCIDRCheck（跳过GlobalRouter的相关校验），VpcCniCIDRCheck（跳过VpcCni相关校验）
	SkipValidateOptions []*string `json:"SkipValidateOptions,omitempty" name:"SkipValidateOptions"`

	// 参数InstanceAdvancedSettingsOverride数组用于定制化地配置各台instance，与InstanceIds顺序对应。当传入InstanceAdvancedSettingsOverrides数组时，将覆盖默认参数InstanceAdvancedSettings；当没有传入参数InstanceAdvancedSettingsOverrides时，InstanceAdvancedSettings参数对每台instance生效。
	//
	// 参数InstanceAdvancedSettingsOverride数组的长度应与InstanceIds数组一致；当长度大于InstanceIds数组长度时将报错；当长度小于InstanceIds数组时，没有对应配置的instace将使用默认配置。
	InstanceAdvancedSettingsOverrides []*InstanceAdvancedSettings `json:"InstanceAdvancedSettingsOverrides,omitempty" name:"InstanceAdvancedSettingsOverrides"`

	// 节点镜像
	ImageId *string `json:"ImageId,omitempty" name:"ImageId"`

	NodeType *string `json:"NodeType,omitempty" name:"NodeType"`
}

type NodePoolOption struct {
	// 是否加入节点池
	AddToNodePool *bool `json:"AddToNodePool,omitempty" name:"AddToNodePool"`

	// 节点池id
	NodePoolId *string `json:"NodePoolId,omitempty" name:"NodePoolId"`

	// 是否继承节点池相关配置
	InheritConfigurationFromNodePool *bool `json:"InheritConfigurationFromNodePool,omitempty" name:"InheritConfigurationFromNodePool"`
}

func (r *AddExistedInstancesRequest) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// Predefined struct for user
type AddExistedInstancesResponseParams struct {
	// 失败的节点ID
	// 注意：此字段可能返回 null，表示取不到有效值。
	FailedInstanceIds []*string `json:"FailedInstanceIds,omitempty" name:"FailedInstanceIds"`

	// 成功的节点ID
	// 注意：此字段可能返回 null，表示取不到有效值。
	SuccInstanceIds []*string `json:"SuccInstanceIds,omitempty" name:"SuccInstanceIds"`

	// 超时未返回出来节点的ID(可能失败，也可能成功)
	// 注意：此字段可能返回 null，表示取不到有效值。
	TimeoutInstanceIds []*string `json:"TimeoutInstanceIds,omitempty" name:"TimeoutInstanceIds"`

	// 失败的节点的失败原因
	// 注意：此字段可能返回 null，表示取不到有效值。
	FailedReasons []*string `json:"FailedReasons,omitempty" name:"FailedReasons"`

	// 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
	RequestId *string `json:"RequestId,omitempty" name:"RequestId"`
}

type AddExistedInstancesResponse struct {
	*tchttp.BaseResponse
	Response *AddExistedInstancesResponseParams `json:"Response"`
}

func (r *AddExistedInstancesResponse) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// DescribeAddon relevant codes....

type Addon struct {
	// addon名称
	AddonName *string `json:"AddonName,omitempty" name:"AddonName"`

	// addon的版本
	AddonVersion *string `json:"AddonVersion,omitempty" name:"AddonVersion"`

	// addon的参数，是一个json格式的base64转码后的字符串
	// 注意：此字段可能返回 null，表示取不到有效值。
	RawValues *string `json:"RawValues,omitempty" name:"RawValues"`

	// addon的状态
	// 注意：此字段可能返回 null，表示取不到有效值。
	Phase *string `json:"Phase,omitempty" name:"Phase"`

	// addon失败的原因
	// 注意：此字段可能返回 null，表示取不到有效值。
	Reason *string `json:"Reason,omitempty" name:"Reason"`
}

// Predefined struct for user
type DescribeAddonRequestParams struct {
	// 集群ID
	ClusterId *string `json:"ClusterId,omitempty" name:"ClusterId"`

	// addon名称（不传时会返回集群下全部的addon）
	AddonName *string `json:"AddonName,omitempty" name:"AddonName"`
}

type DescribeAddonRequest struct {
	*tchttp.BaseRequest

	context context.Context

	// 集群ID
	ClusterId *string `json:"ClusterId,omitempty" name:"ClusterId"`

	// addon名称（不传时会返回集群下全部的addon）
	AddonName *string `json:"AddonName,omitempty" name:"AddonName"`
}

func (r *DescribeAddonRequest) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// FromJsonString It is highly **NOT** recommended to use this function
// because it has no param check, nor strict type check
func (r *DescribeAddonRequest) FromJsonString(s string) error {
	f := make(map[string]interface{})
	if err := json.Unmarshal([]byte(s), &f); err != nil {
		return err
	}
	delete(f, "ClusterId")
	delete(f, "AddonName")
	if len(f) > 0 {
		return tcerr.NewTencentCloudSDKError("ClientError.BuildRequestError", "DescribeAddonRequest has unknown keys!", "")
	}
	return json.Unmarshal([]byte(s), &r)
}

// Predefined struct for user
type DescribeAddonResponseParams struct {
	// addon列表
	Addons []*Addon `json:"Addons,omitempty" name:"Addons"`
	// 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
	RequestId *string `json:"RequestId,omitempty" name:"RequestId"`
}

type DescribeAddonResponse struct {
	*tchttp.BaseResponse
	Response *DescribeAddonResponseParams `json:"Response"`
}

func (r *DescribeAddonResponse) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// FromJsonString It is highly **NOT** recommended to use this function
// because it has no param check, nor strict type check
func (r *DescribeAddonResponse) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

// Predefined struct for user
type DescribeAddonValuesRequestParams struct {
	// 集群ID
	ClusterId *string `json:"ClusterId,omitempty" name:"ClusterId"`

	// addon名称
	AddonName *string `json:"AddonName,omitempty" name:"AddonName"`
}

type DescribeAddonValuesRequest struct {
	*tchttp.BaseRequest

	context context.Context

	// 集群ID
	ClusterId *string `json:"ClusterId,omitempty" name:"ClusterId"`

	// addon名称
	AddonName *string `json:"AddonName,omitempty" name:"AddonName"`
}

func (r *DescribeAddonValuesRequest) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// FromJsonString It is highly **NOT** recommended to use this function
// because it has no param check, nor strict type check
func (r *DescribeAddonValuesRequest) FromJsonString(s string) error {
	f := make(map[string]interface{})
	if err := json.Unmarshal([]byte(s), &f); err != nil {
		return err
	}
	delete(f, "ClusterId")
	delete(f, "AddonName")
	if len(f) > 0 {
		return tcerr.NewTencentCloudSDKError("ClientError.BuildRequestError", "DescribeAddonValuesRequest has unknown keys!", "")
	}
	return json.Unmarshal([]byte(s), &r)
}

// Predefined struct for user
type DescribeAddonValuesResponseParams struct {
	// 参数列表，如果addon已安装，会使用已设置的的参数做渲染，是一个json格式的字符串
	Values *string `json:"Values,omitempty" name:"Values"`

	// addon支持的参数列表，使用默认值，是一个json格式的字符串
	DefaultValues *string `json:"DefaultValues,omitempty" name:"DefaultValues"`

	// 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
	RequestId *string `json:"RequestId,omitempty" name:"RequestId"`
}

type DescribeAddonValuesResponse struct {
	*tchttp.BaseResponse
	Response *DescribeAddonValuesResponseParams `json:"Response"`
}

func (r *DescribeAddonValuesResponse) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// FromJsonString It is highly **NOT** recommended to use this function
// because it has no param check, nor strict type check
func (r *DescribeAddonValuesResponse) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

// Update Addon ...
// Predefined struct for user
type UpdateAddonRequestParams struct {
	// 集群ID
	ClusterId *string `json:"ClusterId,omitempty" name:"ClusterId"`

	// addon名称
	AddonName *string `json:"AddonName,omitempty" name:"AddonName"`

	// addon版本（不传默认不更新）
	AddonVersion *string `json:"AddonVersion,omitempty" name:"AddonVersion"`

	// addon的参数，是一个json格式的base64转码后的字符串（addon参数由DescribeAddonValues获取）
	RawValues *string `json:"RawValues,omitempty" name:"RawValues"`
}

type UpdateAddonRequest struct {
	*tchttp.BaseRequest

	context context.Context

	// 集群ID
	ClusterId *string `json:"ClusterId,omitempty" name:"ClusterId"`

	// addon名称
	AddonName *string `json:"AddonName,omitempty" name:"AddonName"`

	// addon版本（不传默认不更新）
	AddonVersion *string `json:"AddonVersion,omitempty" name:"AddonVersion"`

	// addon的参数，是一个json格式的base64转码后的字符串（addon参数由DescribeAddonValues获取）
	RawValues *string `json:"RawValues,omitempty" name:"RawValues"`
}

func (r *UpdateAddonRequest) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// FromJsonString It is highly **NOT** recommended to use this function
// because it has no param check, nor strict type check
func (r *UpdateAddonRequest) FromJsonString(s string) error {
	f := make(map[string]interface{})
	if err := json.Unmarshal([]byte(s), &f); err != nil {
		return err
	}
	delete(f, "ClusterId")
	delete(f, "AddonName")
	delete(f, "AddonVersion")
	delete(f, "RawValues")
	if len(f) > 0 {
		return tcerr.NewTencentCloudSDKError("ClientError.BuildRequestError", "UpdateAddonRequest has unknown keys!", "")
	}
	return json.Unmarshal([]byte(s), &r)
}

// Predefined struct for user
type UpdateAddonResponseParams struct {
	// 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
	RequestId *string `json:"RequestId,omitempty" name:"RequestId"`
}

type UpdateAddonResponse struct {
	*tchttp.BaseResponse
	Response *UpdateAddonResponseParams `json:"Response"`
}

func (r *UpdateAddonResponse) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// FromJsonString It is highly **NOT** recommended to use this function
// because it has no param check, nor strict type check
func (r *UpdateAddonResponse) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

type Switch struct {

	// 集群ID
	ClusterId *string `json:"ClusterId,omitempty" name:"ClusterId"`

	// 审计开关的详细信息
	// 注意：此字段可能返回 null，表示取不到有效值。
	Audit *SwitchInfo `json:"Audit,omitempty" name:"Audit"`

	// 事件开关的详细信息
	// 注意：此字段可能返回 null，表示取不到有效值。
	Event *SwitchInfo `json:"Event,omitempty" name:"Event"`

	// 普通日志的详细信息
	// 注意：此字段可能返回 null，表示取不到有效值。
	Log *SwitchInfo `json:"Log,omitempty" name:"Log"`
}

type SwitchInfo struct {

	// 开启标识符 true代表开启
	// 注意：此字段可能返回 null，表示取不到有效值。
	Enable *bool `json:"Enable,omitempty" name:"Enable"`

	// CLS日志集ID
	// 注意：此字段可能返回 null，表示取不到有效值。
	LogsetId *string `json:"LogsetId,omitempty" name:"LogsetId"`

	// CLS日志主题ID
	// 注意：此字段可能返回 null，表示取不到有效值。
	TopicId *string `json:"TopicId,omitempty" name:"TopicId"`
}

type DescribeLogSwitchesRequest struct {
	*tchttp.BaseRequest
	// 集群类型
	ClusterType *string `json:"ClusterType,omitempty" name:"ClusterType"`
	// 集群ID列表
	ClusterIds []*string `json:"ClusterIds,omitempty" name:"ClusterIds" list`
}

func (r *DescribeLogSwitchesRequest) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

func (r *DescribeLogSwitchesRequest) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

type DescribeLogSwitchesResponse struct {
	*tchttp.BaseResponse
	Response *struct {

		// 集群日志开关集合
		// 注意：此字段可能返回 null，表示取不到有效值。
		SwitchSet []*Switch `json:"SwitchSet,omitempty" name:"SwitchSet" list`

		// 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
		RequestId *string `json:"RequestId,omitempty" name:"RequestId"`
	} `json:"Response"`
}

func (r *DescribeLogSwitchesResponse) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

func (r *DescribeLogSwitchesResponse) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

type EnableEksEventPersistenceRequest struct {
	*tchttp.BaseRequest

	// 集群ID
	ClusterId *string `json:"ClusterId,omitempty" name:"ClusterId"`

	// cls服务的logsetID
	LogsetId *string `json:"LogsetId,omitempty" name:"LogsetId"`

	// cls服务的topicID
	TopicId *string `json:"TopicId,omitempty" name:"TopicId"`
}

func (r *EnableEksEventPersistenceRequest) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

func (r *EnableEksEventPersistenceRequest) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

type EnableEksEventPersistenceResponse struct {
	*tchttp.BaseResponse
	Response *struct {

		// 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
		RequestId *string `json:"RequestId,omitempty" name:"RequestId"`
	} `json:"Response"`
}

func (r *EnableEksEventPersistenceResponse) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

func (r *EnableEksEventPersistenceResponse) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

type EnableEventPersistenceRequest struct {
	*tchttp.BaseRequest

	// 集群ID
	ClusterId *string `json:"ClusterId,omitempty" name:"ClusterId"`

	// cls服务的logsetID
	LogsetId *string `json:"LogsetId,omitempty" name:"LogsetId"`

	// cls服务的topicID
	TopicId *string `json:"TopicId,omitempty" name:"TopicId"`
}

func (r *EnableEventPersistenceRequest) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

func (r *EnableEventPersistenceRequest) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

type EnableEventPersistenceResponse struct {
	*tchttp.BaseResponse
	Response *struct {

		// 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
		RequestId *string `json:"RequestId,omitempty" name:"RequestId"`
	} `json:"Response"`
}

func (r *EnableEventPersistenceResponse) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

func (r *EnableEventPersistenceResponse) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

type CreateClusterReleaseRequest struct {
	*tchttp.BaseRequest

	// 集群ID
	ClusterId *string `json:"ClusterId,omitempty" name:"ClusterId"`

	// 应用名称
	Name *string `json:"Name,omitempty" name:"Name"`

	// 应用命名空间
	Namespace *string `json:"Namespace,omitempty" name:"Namespace"`

	// 制品名称或制品压缩包下载地址
	Chart *string `json:"Chart,omitempty" name:"Chart"`

	// 自定义参数
	Values *ReleaseValues `json:"Values,omitempty" name:"Values"`

	// 制品来源，范围：tke-market/tcr/other
	ChartFrom *string `json:"ChartFrom,omitempty" name:"ChartFrom"`

	// 制品版本
	ChartVersion *string `json:"ChartVersion,omitempty" name:"ChartVersion"`

	// 制品仓库URL地址
	ChartRepoURL *string `json:"ChartRepoURL,omitempty" name:"ChartRepoURL"`

	// 制品访问用户名
	Username *string `json:"Username,omitempty" name:"Username"`

	// 制品访问密码
	Password *string `json:"Password,omitempty" name:"Password"`

	// 制品命名空间
	ChartNamespace *string `json:"ChartNamespace,omitempty" name:"ChartNamespace"`

	// 容器镜像服务实例地域
	ChartRegionName *string `json:"ChartRegionName,omitempty" name:"ChartRegionName"`

	// 容器镜像服务实例ID
	ChartInstanceId *string `json:"ChartInstanceId,omitempty" name:"ChartInstanceId"`

	// 集群类型
	ClusterType *string `json:"ClusterType,omitempty" name:"ClusterType"`
}

func (r *CreateClusterReleaseRequest) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

func (r *CreateClusterReleaseRequest) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

type CreateClusterReleaseResponse struct {
	*tchttp.BaseResponse
	Response *struct {

		// 应用详情
		// 注意：此字段可能返回 null，表示取不到有效值。
		Release *PendingRelease `json:"Release,omitempty" name:"Release"`

		// 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
		RequestId *string `json:"RequestId,omitempty" name:"RequestId"`
	} `json:"Response"`
}

func (r *CreateClusterReleaseResponse) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

func (r *CreateClusterReleaseResponse) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

type ReleaseValues struct {

	// 自定义参数原始值
	RawOriginal *string `json:"RawOriginal,omitempty" name:"RawOriginal"`

	// 自定义参数值类型
	ValuesType *string `json:"ValuesType,omitempty" name:"ValuesType"`
}

type PendingRelease struct {

	// 应用状态详情
	// 注意：此字段可能返回 null，表示取不到有效值。
	Condition *string `json:"Condition,omitempty" name:"Condition"`

	// 创建时间
	// 注意：此字段可能返回 null，表示取不到有效值。
	CreatedTime *string `json:"CreatedTime,omitempty" name:"CreatedTime"`

	// 应用ID
	// 注意：此字段可能返回 null，表示取不到有效值。
	ID *string `json:"ID,omitempty" name:"ID"`

	// 应用名称
	// 注意：此字段可能返回 null，表示取不到有效值。
	Name *string `json:"Name,omitempty" name:"Name"`

	// 应用命名空间
	// 注意：此字段可能返回 null，表示取不到有效值。
	Namespace *string `json:"Namespace,omitempty" name:"Namespace"`

	// 应用状态
	// 注意：此字段可能返回 null，表示取不到有效值。
	Status *string `json:"Status,omitempty" name:"Status"`

	// 更新时间
	// 注意：此字段可能返回 null，表示取不到有效值。
	UpdatedTime *string `json:"UpdatedTime,omitempty" name:"UpdatedTime"`
}

type DescribeClusterSecurityRequest struct {
	*tchttp.BaseRequest

	// 集群 ID，请填写 查询集群列表 接口中返回的 clusterId 字段
	ClusterId *string `json:"ClusterId,omitempty" name:"ClusterId"`

	// 是否返回内网地址(默认: FALSE)
	JnsGwEndpointEnable *bool `json:"JnsGwEndpointEnable,omitempty" name:"JnsGwEndpointEnable"`
}

func (r *DescribeClusterSecurityRequest) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

func (r *DescribeClusterSecurityRequest) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

type DescribeClusterSecurityResponse struct {
	*tchttp.BaseResponse
	Response *struct {

		// 集群的账号名称
		UserName *string `json:"UserName,omitempty" name:"UserName"`

		// 集群的访问密码
		Password *string `json:"Password,omitempty" name:"Password"`

		// 集群访问CA证书
		CertificationAuthority *string `json:"CertificationAuthority,omitempty" name:"CertificationAuthority"`

		// 集群访问的地址
		ClusterExternalEndpoint *string `json:"ClusterExternalEndpoint,omitempty" name:"ClusterExternalEndpoint"`

		// 集群访问的域名
		Domain *string `json:"Domain,omitempty" name:"Domain"`

		// 集群Endpoint地址
		PgwEndpoint *string `json:"PgwEndpoint,omitempty" name:"PgwEndpoint"`

		// 集群访问策略组
		// 注意：此字段可能返回 null，表示取不到有效值。
		SecurityPolicy []*string `json:"SecurityPolicy,omitempty" name:"SecurityPolicy" list`

		// 集群Kubeconfig文件
		// 注意：此字段可能返回 null，表示取不到有效值。
		Kubeconfig *string `json:"Kubeconfig,omitempty" name:"Kubeconfig"`

		// 集群JnsGw的访问地址
		// 注意：此字段可能返回 null，表示取不到有效值。
		JnsGwEndpoint *string `json:"JnsGwEndpoint,omitempty" name:"JnsGwEndpoint"`

		// 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
		RequestId *string `json:"RequestId,omitempty" name:"RequestId"`
	} `json:"Response"`
}

func (r *DescribeClusterSecurityResponse) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

func (r *DescribeClusterSecurityResponse) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

type UpgradeClusterReleaseRequest struct {
	*tchttp.BaseRequest

	// 集群ID
	ClusterId *string `json:"ClusterId,omitempty" name:"ClusterId"`

	// 应用名称
	Name *string `json:"Name,omitempty" name:"Name"`

	// 应用命名空间
	Namespace *string `json:"Namespace,omitempty" name:"Namespace"`

	// 制品名称或制品压缩包下载地址
	Chart *string `json:"Chart,omitempty" name:"Chart"`

	// 自定义参数
	Values *ReleaseValues `json:"Values,omitempty" name:"Values"`

	// 制品来源，范围：tke-market/tcr/other
	ChartFrom *string `json:"ChartFrom,omitempty" name:"ChartFrom"`

	// 制品版本
	ChartVersion *string `json:"ChartVersion,omitempty" name:"ChartVersion"`

	// 制品仓库URL地址
	ChartRepoURL *string `json:"ChartRepoURL,omitempty" name:"ChartRepoURL"`

	// 制品访问用户名
	Username *string `json:"Username,omitempty" name:"Username"`

	// 制品访问密码
	Password *string `json:"Password,omitempty" name:"Password"`

	// 制品命名空间
	ChartNamespace *string `json:"ChartNamespace,omitempty" name:"ChartNamespace"`

	// 容器镜像服务实例地域
	ChartRegionName *string `json:"ChartRegionName,omitempty" name:"ChartRegionName"`

	// 容器镜像服务实例ID
	ChartInstanceId *string `json:"ChartInstanceId,omitempty" name:"ChartInstanceId"`

	// 集群类型
	ClusterType *string `json:"ClusterType,omitempty" name:"ClusterType"`
}

func (r *UpgradeClusterReleaseRequest) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

func (r *UpgradeClusterReleaseRequest) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

type UpgradeClusterReleaseResponse struct {
	*tchttp.BaseResponse
	Response *struct {

		// 应用详情
		// 注意：此字段可能返回 null，表示取不到有效值。
		Release *PendingRelease `json:"Release,omitempty" name:"Release"`

		// 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
		RequestId *string `json:"RequestId,omitempty" name:"RequestId"`
	} `json:"Response"`
}

func (r *UpgradeClusterReleaseResponse) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

func (r *UpgradeClusterReleaseResponse) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

type GetEKSClusterResourcesRequest struct {
	*tchttp.BaseRequest

	// 集群ID
	ClusterIds []*string `json:"ClusterIds,omitempty" name:"ClusterIds" list`
}

func (r *GetEKSClusterResourcesRequest) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

func (r *GetEKSClusterResourcesRequest) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

type GetEKSClusterResourcesResponse struct {
	*tchttp.BaseResponse
	Response *struct {

		// 集群资源信息
		// 注意：此字段可能返回 null，表示取不到有效值。
		ClusterResources []*ClusterResource `json:"ClusterResources,omitempty" name:"ClusterResources" list`

		// 集群数量配额
		// 注意：此字段可能返回 null，表示取不到有效值。
		ClusterNumQuota *int64 `json:"ClusterNumQuota,omitempty" name:"ClusterNumQuota"`

		// 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
		RequestId *string `json:"RequestId,omitempty" name:"RequestId"`
	} `json:"Response"`
}

func (r *GetEKSClusterResourcesResponse) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

func (r *GetEKSClusterResourcesResponse) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

type ClusterResource struct {

	// 集群ID
	ClusterId *string `json:"ClusterId,omitempty" name:"ClusterId"`

	// 子网资源列表
	SubnetResources []*SubnetResource `json:"SubnetResources,omitempty" name:"SubnetResources" list`

	// 集群内Pod数量配额
	ClusterPodQuota *int64 `json:"ClusterPodQuota,omitempty" name:"ClusterPodQuota"`

	// 集群内已创建的Pod总数量
	ClusterPodNum *int64 `json:"ClusterPodNum,omitempty" name:"ClusterPodNum"`
}

type SubnetResource struct {

	// cpu
	CPU *float64 `json:"CPU,omitempty" name:"CPU"`

	// mem
	Memory *float64 `json:"Memory,omitempty" name:"Memory"`

	// pod数量
	PodNum *int64 `json:"PodNum,omitempty" name:"PodNum"`

	// 子网ID
	SubnetId *string `json:"SubnetId,omitempty" name:"SubnetId"`
}

// Predefined struct for user
type DescribeOpenPolicyListRequestParams struct {
	// 集群ID
	ClusterId *string `json:"ClusterId,omitnil,omitempty" name:"ClusterId"`

	// 集群类型
	ClusterType *string `json:"ClusterType,omitnil,omitempty" name:"ClusterType"`

	// 策略分类 基线：baseline 优选：priority 可选：optional
	Category *string `json:"Category,omitnil,omitempty" name:"Category"`
}

type DescribeOpenPolicyListRequest struct {
	*tchttp.BaseRequest

	// 集群ID
	ClusterId *string `json:"ClusterId,omitnil,omitempty" name:"ClusterId"`

	// 集群类型
	ClusterType *string `json:"ClusterType,omitnil,omitempty" name:"ClusterType"`

	// 策略分类 基线：baseline 优选：priority 可选：optional
	Category *string `json:"Category,omitnil,omitempty" name:"Category"`
}

func (r *DescribeOpenPolicyListRequest) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// FromJsonString It is highly **NOT** recommended to use this function
// because it has no param check, nor strict type check
func (r *DescribeOpenPolicyListRequest) FromJsonString(s string) error {
	f := make(map[string]interface{})
	if err := json.Unmarshal([]byte(s), &f); err != nil {
		return err
	}
	delete(f, "ClusterId")
	delete(f, "ClusterType")
	delete(f, "Category")
	if len(f) > 0 {
		return tcerr.NewTencentCloudSDKError("ClientError.BuildRequestError", "DescribeOpenPolicyListRequest has unknown keys!", "")
	}
	return json.Unmarshal([]byte(s), &r)
}

// Predefined struct for user
type DescribeOpenPolicyListResponseParams struct {
	// 策略信息列表
	// 注意：此字段可能返回 null，表示取不到有效值。
	OpenPolicyInfoList []*OpenPolicyInfo `json:"OpenPolicyInfoList,omitnil,omitempty" name:"OpenPolicyInfoList"`

	// 唯一请求 ID，由服务端生成，每次请求都会返回（若请求因其他原因未能抵达服务端，则该次请求不会获得 RequestId）。定位问题时需要提供该次请求的 RequestId。
	RequestId *string `json:"RequestId,omitnil,omitempty" name:"RequestId"`
}

type DescribeOpenPolicyListResponse struct {
	*tchttp.BaseResponse
	Response *DescribeOpenPolicyListResponseParams `json:"Response"`
}

func (r *DescribeOpenPolicyListResponse) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// FromJsonString It is highly **NOT** recommended to use this function
// because it has no param check, nor strict type check
func (r *DescribeOpenPolicyListResponse) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

type OpenConstraintInfo struct {
	// 策略实例名称
	// 注意：此字段可能返回 null，表示取不到有效值。
	Name *string `json:"Name,omitnil,omitempty" name:"Name"`

	// 策略实例关联事件数
	// 注意：此字段可能返回 null，表示取不到有效值。
	EventNums *uint64 `json:"EventNums,omitnil,omitempty" name:"EventNums"`

	// 实例yaml详情base64编码
	// 注意：此字段可能返回 null，表示取不到有效值。
	YamlDetail *string `json:"YamlDetail,omitnil,omitempty" name:"YamlDetail"`
}

type OpenPolicyInfo struct {
	// 策略分类：cluster集群策略、node节点策略、namespace命名空间策略、configuration配置相关策略、compute计算资源策略、storage存储资源策略、network网络资源策略
	// 注意：此字段可能返回 null，表示取不到有效值。
	PolicyCategory *string `json:"PolicyCategory,omitnil,omitempty" name:"PolicyCategory"`

	// 策略中文名称
	// 注意：此字段可能返回 null，表示取不到有效值。
	PolicyName *string `json:"PolicyName,omitnil,omitempty" name:"PolicyName"`

	// 策略描述
	// 注意：此字段可能返回 null，表示取不到有效值。
	PolicyDesc *string `json:"PolicyDesc,omitnil,omitempty" name:"PolicyDesc"`

	// 策略运行模式：dryrun空跑不生效，deny拦截生效
	// 注意：此字段可能返回 null，表示取不到有效值。
	EnforcementAction *string `json:"EnforcementAction,omitnil,omitempty" name:"EnforcementAction"`

	// 关联的事件数量(最近7d)
	// 注意：此字段可能返回 null，表示取不到有效值。
	EventNums *uint64 `json:"EventNums,omitnil,omitempty" name:"EventNums"`

	// 策略英文名称
	// 注意：此字段可能返回 null，表示取不到有效值。
	Name *string `json:"Name,omitnil,omitempty" name:"Name"`

	// 策略模版类型
	// 注意：此字段可能返回 null，表示取不到有效值。
	Kind *string `json:"Kind,omitnil,omitempty" name:"Kind"`

	// 排除的命名空间名称列表（该字段暂未支持）
	// 注意：此字段可能返回 null，表示取不到有效值。
	ExcludeNsNames []*string `json:"ExcludeNsNames,omitnil,omitempty" name:"ExcludeNsNames"`

	// 排除的命名空间标签列表（该字段暂未支持）
	// 注意：此字段可能返回 null，表示取不到有效值。
	ExcludeNsLabels []*string `json:"ExcludeNsLabels,omitnil,omitempty" name:"ExcludeNsLabels"`

	// 策略开关状态：open打开，close关闭
	// 注意：此字段可能返回 null，表示取不到有效值。
	EnabledStatus *string `json:"EnabledStatus,omitnil,omitempty" name:"EnabledStatus"`

	// 策略的实例的yaml示例base64编码
	// 注意：此字段可能返回 null，表示取不到有效值。
	ConstraintYamlExample *string `json:"ConstraintYamlExample,omitnil,omitempty" name:"ConstraintYamlExample"`

	// 策略关联的实例列表
	// 注意：此字段可能返回 null，表示取不到有效值。
	OpenConstraintInfoList []*OpenConstraintInfo `json:"OpenConstraintInfoList,omitnil,omitempty" name:"OpenConstraintInfoList"`
}

// Predefined struct for user
type EnableEksAuditRequestParams struct {
	// 弹性集群ID
	ClusterId *string `json:"ClusterId,omitnil,omitempty" name:"ClusterId"`

	// CLS日志集ID
	LogsetId *string `json:"LogsetId,omitnil,omitempty" name:"LogsetId"`

	// CLS日志主题ID
	TopicId *string `json:"TopicId,omitnil,omitempty" name:"TopicId"`

	// topic所在region，默认为当前集群
	TopicRegion *string `json:"TopicRegion,omitnil,omitempty" name:"TopicRegion"`
}

type EnableEksAuditRequest struct {
	*tchttp.BaseRequest

	// 弹性集群ID
	ClusterId *string `json:"ClusterId,omitnil,omitempty" name:"ClusterId"`

	// CLS日志集ID
	LogsetId *string `json:"LogsetId,omitnil,omitempty" name:"LogsetId"`

	// CLS日志主题ID
	TopicId *string `json:"TopicId,omitnil,omitempty" name:"TopicId"`

	// topic所在region，默认为当前集群
	TopicRegion *string `json:"TopicRegion,omitnil,omitempty" name:"TopicRegion"`
}

func (r *EnableEksAuditRequest) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// FromJsonString It is highly **NOT** recommended to use this function
// because it has no param check, nor strict type check
func (r *EnableEksAuditRequest) FromJsonString(s string) error {
	f := make(map[string]interface{})
	if err := json.Unmarshal([]byte(s), &f); err != nil {
		return err
	}
	delete(f, "ClusterId")
	delete(f, "LogsetId")
	delete(f, "TopicId")
	delete(f, "TopicRegion")
	if len(f) > 0 {
		return tcerr.NewTencentCloudSDKError("ClientError.BuildRequestError", "EnableEksAuditRequest has unknown keys!", "")
	}
	return json.Unmarshal([]byte(s), &r)
}

// Predefined struct for user
type EnableEksAuditResponseParams struct {
	// 唯一请求 ID，由服务端生成，每次请求都会返回（若请求因其他原因未能抵达服务端，则该次请求不会获得 RequestId）。定位问题时需要提供该次请求的 RequestId。
	RequestId *string `json:"RequestId,omitnil,omitempty" name:"RequestId"`
}

type EnableEksAuditResponse struct {
	*tchttp.BaseResponse
	Response *EnableEksAuditResponseParams `json:"Response"`
}

func (r *EnableEksAuditResponse) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// FromJsonString It is highly **NOT** recommended to use this function
// because it has no param check, nor strict type check
func (r *EnableEksAuditResponse) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

// Predefined struct for user
type ModifyOpenPolicyListRequestParams struct {
	// 集群ID
	ClusterId *string `json:"ClusterId,omitnil,omitempty" name:"ClusterId"`

	// 修改的策略列表，目前仅支持修改EnforcementAction字段
	OpenPolicyInfoList []*OpenPolicySwitch `json:"OpenPolicyInfoList,omitnil,omitempty" name:"OpenPolicyInfoList"`

	// 集群类型
	ClusterType *string `json:"ClusterType,omitnil,omitempty" name:"ClusterType"`

	// 策略分类 基线：baseline 优选：priority 可选：optional
	Category *string `json:"Category,omitnil,omitempty" name:"Category"`
}

type ModifyOpenPolicyListRequest struct {
	*tchttp.BaseRequest

	// 集群ID
	ClusterId *string `json:"ClusterId,omitnil,omitempty" name:"ClusterId"`

	// 修改的策略列表，目前仅支持修改EnforcementAction字段
	OpenPolicyInfoList []*OpenPolicySwitch `json:"OpenPolicyInfoList,omitnil,omitempty" name:"OpenPolicyInfoList"`

	// 集群类型
	ClusterType *string `json:"ClusterType,omitnil,omitempty" name:"ClusterType"`

	// 策略分类 基线：baseline 优选：priority 可选：optional
	Category *string `json:"Category,omitnil,omitempty" name:"Category"`
}

func (r *ModifyOpenPolicyListRequest) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// FromJsonString It is highly **NOT** recommended to use this function
// because it has no param check, nor strict type check
func (r *ModifyOpenPolicyListRequest) FromJsonString(s string) error {
	f := make(map[string]interface{})
	if err := json.Unmarshal([]byte(s), &f); err != nil {
		return err
	}
	delete(f, "ClusterId")
	delete(f, "OpenPolicyInfoList")
	delete(f, "ClusterType")
	delete(f, "Category")
	if len(f) > 0 {
		return tcerr.NewTencentCloudSDKError("ClientError.BuildRequestError", "ModifyOpenPolicyListRequest has unknown keys!", "")
	}
	return json.Unmarshal([]byte(s), &r)
}

// Predefined struct for user
type ModifyOpenPolicyListResponseParams struct {
	// 唯一请求 ID，由服务端生成，每次请求都会返回（若请求因其他原因未能抵达服务端，则该次请求不会获得 RequestId）。定位问题时需要提供该次请求的 RequestId。
	RequestId *string `json:"RequestId,omitnil,omitempty" name:"RequestId"`
}

type ModifyOpenPolicyListResponse struct {
	*tchttp.BaseResponse
	Response *ModifyOpenPolicyListResponseParams `json:"Response"`
}

func (r *ModifyOpenPolicyListResponse) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// FromJsonString It is highly **NOT** recommended to use this function
// because it has no param check, nor strict type check
func (r *ModifyOpenPolicyListResponse) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

type OpenPolicySwitch struct {
	// 策略运行模式：dryrun空跑不生效，deny拦截生效
	EnforcementAction *string `json:"EnforcementAction,omitnil,omitempty" name:"EnforcementAction"`

	// 策略英文名称
	Name *string `json:"Name,omitnil,omitempty" name:"Name"`

	// 策略模版类型
	Kind *string `json:"Kind,omitnil,omitempty" name:"Kind"`

	// 策略开关状态：open打开，close关闭
	EnabledStatus *string `json:"EnabledStatus,omitnil,omitempty" name:"EnabledStatus"`

	// 策略关联的实例列表
	OpenConstraintInfoList []*OpenConstraintInfo `json:"OpenConstraintInfoList,omitnil,omitempty" name:"OpenConstraintInfoList"`
}
