package sts

import (
	tcerr "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/errors"
	tchttp "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/http"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/json"
)

type AssumeRoleRequest struct {
	*tchttp.BaseRequest

	// 角色的资源描述，可在[访问管理](https://console.cloud.tencent.com/cam/role)，点击角色名获取。
	// 普通角色：
	// qcs::cam::uin/12345678:role/4611686018427397919、qcs::cam::uin/12345678:roleName/testRoleName
	// 服务角色：
	// qcs::cam::uin/12345678:role/tencentcloudServiceRole/4611686018427397920、qcs::cam::uin/12345678:role/tencentcloudServiceRoleName/testServiceRoleName
	RoleArn *string `json:"RoleArn,omitnil" name:"RoleArn"`

	// 临时会话名称，由用户自定义名称。
	// 长度在2到128之间，可包含大小写字符，数字以及特殊字符：=,.@_-。 正则为：[\w+=,.@_-]*
	RoleSessionName *string `json:"RoleSessionName,omitnil" name:"RoleSessionName"`

	// 指定临时访问凭证的有效期，单位：秒，默认 7200 秒，最长可设定有效期为 43200 秒
	DurationSeconds *uint64 `json:"DurationSeconds,omitnil" name:"DurationSeconds"`

	// 策略描述
	// 注意：
	// 1、policy 需要做 urlencode（如果通过 GET 方法请求云 API，发送请求前，所有参数都需要按照[云 API 规范](https://cloud.tencent.com/document/api/598/33159#1.-.E6.8B.BC.E6.8E.A5.E8.A7.84.E8.8C.83.E8.AF.B7.E6.B1.82.E4.B8.B2)再 urlencode 一次）。
	// 2、策略语法参照[ CAM 策略语法](https://cloud.tencent.com/document/product/598/10603)。
	// 3、策略中不能包含 principal 元素。
	Policy *string `json:"Policy,omitnil" name:"Policy"`

	// 角色外部ID，可在[访问管理](https://console.cloud.tencent.com/cam/role)，点击角色名获取。
	// 长度在2到128之间，可包含大小写字符，数字以及特殊字符：=,.@:/-。 正则为：[\w+=,.@:\/-]*
	ExternalId *string `json:"ExternalId,omitnil" name:"ExternalId"`

	// 会话标签列表。最多可以传递 50 个会话标签，不支持包含相同标签键。
	Tags []*Tag `json:"Tags,omitnil" name:"Tags"`

	// 调用者身份uin
	SourceIdentity *string `json:"SourceIdentity,omitnil" name:"SourceIdentity"`

	UserUin *uint64 `json:"UserUin,omitempty" name:"UserUin"`
}

func (r *AssumeRoleRequest) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// FromJsonString It is highly **NOT** recommended to use this function
// because it has no param check, nor strict type check
func (r *AssumeRoleRequest) FromJsonString(s string) error {
	f := make(map[string]interface{})
	if err := json.Unmarshal([]byte(s), &f); err != nil {
		return err
	}
	delete(f, "RoleArn")
	delete(f, "RoleSessionName")
	delete(f, "DurationSeconds")
	delete(f, "Policy")
	delete(f, "ExternalId")
	delete(f, "Tags")
	delete(f, "SourceIdentity")
	if len(f) > 0 {
		return tcerr.NewTencentCloudSDKError("ClientError.BuildRequestError", "AssumeRoleRequest has unknown keys!", "")
	}
	return json.Unmarshal([]byte(s), &r)
}
func (r *AssumeRoleRequest) IsInternalServiceAction() bool {
	return false
}

func (r *AssumeRoleRequest) NeedUserRoleToken() bool {
	return false
}

// Predefined struct for user
type AssumeRoleResponseParams struct {
	// 临时访问凭证
	Credentials *Credentials `json:"Credentials,omitnil" name:"Credentials"`

	// 临时访问凭证的过期时间，返回 Unix 时间戳，精确到秒
	ExpiredTime *int64 `json:"ExpiredTime,omitnil" name:"ExpiredTime"`

	// 临时访问凭证的过期时间，以 iso8601 格式的 UTC 时间表示
	Expiration *string `json:"Expiration,omitnil" name:"Expiration"`

	// 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
	RequestId *string `json:"RequestId,omitnil" name:"RequestId"`
}

type AssumeRoleResponse struct {
	*tchttp.BaseResponse
	Response *AssumeRoleResponseParams `json:"Response"`
}

func (r *AssumeRoleResponse) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// FromJsonString It is highly **NOT** recommended to use this function
// because it has no param check, nor strict type check
func (r *AssumeRoleResponse) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

// Predefined struct for user
type AssumeRoleWithSAMLRequestParams struct {
	// base64 编码的 SAML 断言信息
	SAMLAssertion *string `json:"SAMLAssertion,omitnil" name:"SAMLAssertion"`

	// 扮演者访问描述名
	PrincipalArn *string `json:"PrincipalArn,omitnil" name:"PrincipalArn"`

	// 角色访问描述名
	RoleArn *string `json:"RoleArn,omitnil" name:"RoleArn"`

	// 会话名称
	RoleSessionName *string `json:"RoleSessionName,omitnil" name:"RoleSessionName"`

	// 指定临时访问凭证的有效期，单位：秒，默认 7200 秒，最长可设定有效期为 43200 秒
	DurationSeconds *uint64 `json:"DurationSeconds,omitnil" name:"DurationSeconds"`
}

type Tag struct {
	// 标签键，最长128个字符，区分大小写。
	Key *string `json:"Key,omitnil" name:"Key"`

	// 标签值，最长256个字符，区分大小写。
	Value *string `json:"Value,omitnil" name:"Value"`
}

type Credentials struct {
	// token。token长度和绑定的策略有关，最长不超过4096字节。
	Token *string `json:"Token,omitnil" name:"Token"`

	// 临时证书密钥ID。最长不超过1024字节。
	TmpSecretId *string `json:"TmpSecretId,omitnil" name:"TmpSecretId"`

	// 临时证书密钥Key。最长不超过1024字节。
	TmpSecretKey *string `json:"TmpSecretKey,omitnil" name:"TmpSecretKey"`
}

type CredentialPolicyStatement struct {
	Action    []string                          `json:"action,omitempty"`
	Effect    string                            `json:"effect,omitempty"`
	Resource  []string                          `json:"resource,omitempty"`
	Condition map[string]map[string]interface{} `json:"condition,omitempty"`
}

type CredentialPolicy struct {
	Version   string                      `json:"version,omitempty"`
	Statement []CredentialPolicyStatement `json:"statement,omitempty"`
}

type CredentialOptions struct {
	Policy          *CredentialPolicy
	Region          string
	DurationSeconds int64
	RoleArn         string
	RoleSessionName string
	ExternalId      string
}

type NewCredentials struct {
	TmpSecretID  string `json:"TmpSecretId,omitempty"`
	TmpSecretKey string `json:"TmpSecretKey,omitempty"`
	SessionToken string `json:"Token,omitempty"`
}
