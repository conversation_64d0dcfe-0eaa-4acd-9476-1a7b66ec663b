package sts

import (
	"context"
	"errors"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common"
	tchttp "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/http"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/profile"
)

const APIVersion = "2018-08-13"

type Client struct {
	common.Client
}

func NewClient(credential common.CredentialIface, region string, clientProfile *profile.ClientProfile) (client *Client, err error) {
	client = &Client{}
	client.Init(region).
		WithCredential(credential).
		WithProfile(clientProfile)
	return
}

func NewAssumeRoleRequest() (request *AssumeRoleRequest) {
	request = &AssumeRoleRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}

	request.Init().WithApiInfo("sts", APIVersion, "AssumeRole")

	return
}

func NewAssumeRoleResponse() (response *AssumeRoleResponse) {
	response = &AssumeRoleResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return

}

// AssumeRole
// 申请扮演角色临时访问凭证。
//
// 可能返回的错误码:
//
//	INTERNALERROR_DBERROR = "InternalError.DbError"
//	INTERNALERROR_ENCRYPTERROR = "InternalError.EncryptError"
//	INTERNALERROR_GETAPPIDERROR = "InternalError.GetAppIdError"
//	INTERNALERROR_GETROLEERROR = "InternalError.GetRoleError"
//	INTERNALERROR_GETSEEDTOKENERROR = "InternalError.GetSeedTokenError"
//	INTERNALERROR_ILLEGALROLE = "InternalError.IllegalRole"
//	INTERNALERROR_PBSERIALIZEERROR = "InternalError.PbSerializeError"
//	INTERNALERROR_SYSTEMERROR = "InternalError.SystemError"
//	INTERNALERROR_UNKNOWNERROR = "InternalError.UnknownError"
//	INVALIDPARAMETER_ACCOUNTNOTAVALIABLE = "InvalidParameter.AccountNotAvaliable"
//	INVALIDPARAMETER_EXTENDSTRATEGYOVERSIZE = "InvalidParameter.ExtendStrategyOverSize"
//	INVALIDPARAMETER_GRANTOTHERRESOURCE = "InvalidParameter.GrantOtherResource"
//	INVALIDPARAMETER_OVERLIMIT = "InvalidParameter.OverLimit"
//	INVALIDPARAMETER_OVERTIMEERROR = "InvalidParameter.OverTimeError"
//	INVALIDPARAMETER_PARAMERROR = "InvalidParameter.ParamError"
//	INVALIDPARAMETER_POLICYTOOLONG = "InvalidParameter.PolicyTooLong"
//	INVALIDPARAMETER_RESOUCEERROR = "InvalidParameter.ResouceError"
//	INVALIDPARAMETER_STRATEGYFORMATERROR = "InvalidParameter.StrategyFormatError"
//	INVALIDPARAMETER_STRATEGYINVALID = "InvalidParameter.StrategyInvalid"
//	INVALIDPARAMETER_TEMPCODENOTAVALIABLE = "InvalidParameter.TempCodeNotAvaliable"
//	RESOURCENOTFOUND_ROLENOTFOUND = "ResourceNotFound.RoleNotFound"
//	UNAUTHORIZEDOPERATION = "UnauthorizedOperation"
//	UNSUPPORTEDOPERATION = "UnsupportedOperation"
func (c *Client) AssumeRole(request *AssumeRoleRequest) (response *AssumeRoleResponse, err error) {
	return c.AssumeRoleWithContext(context.Background(), request)
}

// AssumeRole
// 申请扮演角色临时访问凭证。
//
// 可能返回的错误码:
//
//	INTERNALERROR_DBERROR = "InternalError.DbError"
//	INTERNALERROR_ENCRYPTERROR = "InternalError.EncryptError"
//	INTERNALERROR_GETAPPIDERROR = "InternalError.GetAppIdError"
//	INTERNALERROR_GETROLEERROR = "InternalError.GetRoleError"
//	INTERNALERROR_GETSEEDTOKENERROR = "InternalError.GetSeedTokenError"
//	INTERNALERROR_ILLEGALROLE = "InternalError.IllegalRole"
//	INTERNALERROR_PBSERIALIZEERROR = "InternalError.PbSerializeError"
//	INTERNALERROR_SYSTEMERROR = "InternalError.SystemError"
//	INTERNALERROR_UNKNOWNERROR = "InternalError.UnknownError"
//	INVALIDPARAMETER_ACCOUNTNOTAVALIABLE = "InvalidParameter.AccountNotAvaliable"
//	INVALIDPARAMETER_EXTENDSTRATEGYOVERSIZE = "InvalidParameter.ExtendStrategyOverSize"
//	INVALIDPARAMETER_GRANTOTHERRESOURCE = "InvalidParameter.GrantOtherResource"
//	INVALIDPARAMETER_OVERLIMIT = "InvalidParameter.OverLimit"
//	INVALIDPARAMETER_OVERTIMEERROR = "InvalidParameter.OverTimeError"
//	INVALIDPARAMETER_PARAMERROR = "InvalidParameter.ParamError"
//	INVALIDPARAMETER_POLICYTOOLONG = "InvalidParameter.PolicyTooLong"
//	INVALIDPARAMETER_RESOUCEERROR = "InvalidParameter.ResouceError"
//	INVALIDPARAMETER_STRATEGYFORMATERROR = "InvalidParameter.StrategyFormatError"
//	INVALIDPARAMETER_STRATEGYINVALID = "InvalidParameter.StrategyInvalid"
//	INVALIDPARAMETER_TEMPCODENOTAVALIABLE = "InvalidParameter.TempCodeNotAvaliable"
//	RESOURCENOTFOUND_ROLENOTFOUND = "ResourceNotFound.RoleNotFound"
//	UNAUTHORIZEDOPERATION = "UnauthorizedOperation"
//	UNSUPPORTEDOPERATION = "UnsupportedOperation"
func (c *Client) AssumeRoleWithContext(ctx context.Context, request *AssumeRoleRequest) (response *AssumeRoleResponse, err error) {
	if request == nil {
		request = NewAssumeRoleRequest()
	}

	if c.GetCredential() == nil {
		return nil, errors.New("AssumeRole require credential")
	}

	request.SetContext(ctx)

	response = NewAssumeRoleResponse()
	err = c.Send(request, response)
	return
}
