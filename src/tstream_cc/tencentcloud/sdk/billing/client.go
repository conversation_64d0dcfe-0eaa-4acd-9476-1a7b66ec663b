package billing

import (
	"context"
	"errors"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common"
	tchttp "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/http"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/profile"
)

const APIVersion = "2018-07-09"

func NewClient(credential common.CredentialIface, region string, clientProfile *profile.ClientProfile) (client *Client, err error) {
	client = &Client{}
	client.Init(region).
		WithCredential(credential).
		WithProfile(clientProfile)
	return
}

type Client struct {
	common.Client
}

func NewCreateOrdersAndPayRequest() (request *CreateOrdersAndPayRequest) {
	request = &CreateOrdersAndPayRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}

	request.Init().WithApiInfo("billing", APIVersion, "CreateOrdersAndPay")

	return
}

func NewCreateOrdersAndPayResponse() (response *CreateOrdersAndPayResponse) {
	response = &CreateOrdersAndPayResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return

}

// CreateOrdersAndPay
// 国内站：预付费新购、续费、变配、后付费开通、后付费变配、退款、退带宽
//
// 国际站：预付费新购、续费、变配、退费
//
// 可能返回的错误码:
//
//	FAILEDOPERATION = "FailedOperation"
//	FAILEDOPERATION_AGENTPAYDEALCANNOTDOWN = "FailedOperation.AgentPayDealCannotDown"
//	FAILEDOPERATION_BALANCEINSUFFICIENT = "FailedOperation.BalanceInsufficient"
//	FAILEDOPERATION_BUSINESSCHECKERRCODE = "FailedOperation.BusinessCheckErrCode"
//	FAILEDOPERATION_GETAPPIDERROR = "FailedOperation.GetAppIdError"
//	FAILEDOPERATION_GETPRICEERROR = "FailedOperation.GetPriceError"
//	FAILEDOPERATION_GETPRICEPARAMERROR = "FailedOperation.GetPriceParamError"
//	FAILEDOPERATION_INTLWHITELISTLIMITFAILD = "FailedOperation.IntlWhiteListLimitFaild"
//	FAILEDOPERATION_INVALIDAPPID = "FailedOperation.InvalidAppId"
//	FAILEDOPERATION_INVALIDDEAL = "FailedOperation.InvalidDeal"
//	FAILEDOPERATION_INVALIDRESOURCEID = "FailedOperation.InvalidResourceId"
//	FAILEDOPERATION_INVALIDVOUCHER = "FailedOperation.InvalidVoucher"
//	FAILEDOPERATION_NEEDPAYTOGETER = "FailedOperation.NeedPayTogeter"
//	FAILEDOPERATION_NOTALLOWTOREFUND = "FailedOperation.NotAllowToRefund"
//	FAILEDOPERATION_NUMLIMITERROR = "FailedOperation.NumLimitError"
//	FAILEDOPERATION_PAYPRICEERROR = "FailedOperation.PayPriceError"
//	FAILEDOPERATION_RETURNCOMMONERROR = "FailedOperation.ReturnCommonError"
//	FAILEDOPERATION_USEVOUCHERTIMEOUT = "FailedOperation.UseVoucherTimeOut"
//	INTERNALERROR = "InternalError"
//	INTERNALERROR_GATEWAYERROR = "InternalError.GatewayError"
//	INTERNALERROR_SYSTEMERROR = "InternalError.SystemError"
//	INTERNALERROR_UNKNOWNERROR = "InternalError.UnknownError"
//	INVALIDPARAMETER = "InvalidParameter"
//	INVALIDPARAMETER_CLIENTUINERROR = "InvalidParameter.ClientUinError"
//	INVALIDPARAMETER_NOTALLOWOPERATEACTIVITYDEAL = "InvalidParameter.NotAllowOperateActivityDeal"
//	INVALIDPARAMETER_RESOURCELOCKED = "InvalidParameter.ResourceLocked"
//	UNAUTHORIZEDOPERATION_CAMNOAUTH = "UnauthorizedOperation.CamNoAuth"
//	UNAUTHORIZEDOPERATION_CERTIFICATIONNEEDUPGRADE = "UnauthorizedOperation.CertificationNeedUpgrade"
//	UNAUTHORIZEDOPERATION_NOTCERTIFICATION = "UnauthorizedOperation.NotCertification"
func (c *Client) CreateOrdersAndPay(request *CreateOrdersAndPayRequest) (response *CreateOrdersAndPayResponse, err error) {
	return c.CreateOrdersAndPayWithContext(context.Background(), request)
}

func (c *Client) CreateOrdersAndPayWithContext(ctx context.Context, request *CreateOrdersAndPayRequest) (response *CreateOrdersAndPayResponse, err error) {
	if request == nil {
		request = NewCreateOrdersAndPayRequest()
	}

	if c.GetCredential() == nil {
		return nil, errors.New("CreateOrdersAndPay require credential")
	}

	request.SetContext(ctx)

	response = NewCreateOrdersAndPayResponse()
	err = c.Send(request, response)
	return
}
