package billing

import (
	tcerr "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/errors"
	tchttp "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/http"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/json"
)

type CreateAuth struct {
	// 版本号，目前 2.0
	// 注意：此字段可能返回 null，表示取不到有效值。
	Version *string `json:"Version,omitnil,omitempty" name:"Version"`

	// 鉴权参数
	// 注意：此字段可能返回 null，表示取不到有效值。
	SecretId *string `json:"SecretId,omitnil,omitempty" name:"SecretId"`

	// 鉴权参数，时间戳
	// 注意：此字段可能返回 null，表示取不到有效值。
	Timestamp *string `json:"Timestamp,omitnil,omitempty" name:"Timestamp"`

	// 鉴权参数，签名
	// 注意：此字段可能返回 null，表示取不到有效值。
	Signature *string `json:"Signature,omitnil,omitempty" name:"Signature"`

	// 每次签名随机字符串
	// 注意：此字段可能返回 null，表示取不到有效值。
	Nonce *string `json:"Nonce,omitnil,omitempty" name:"Nonce"`

	// 标记字段，签名时需原样带回
	// 注意：此字段可能返回 null，表示取不到有效值。
	Flag *string `json:"Flag,omitnil,omitempty" name:"Flag"`
}

// Predefined struct for user
type CreateOrdersAndPayRequestParams struct {
	// 下单类型，create开通或新购，renew续费，modify变配，refund退款，returnFitting退带宽
	GenerateType *string `json:"GenerateType,omitnil,omitempty" name:"GenerateType"`

	// 商品信息
	Goods []*GoodsInfo `json:"Goods,omitnil,omitempty" name:"Goods"`

	// 是否自动选择代金券 默认为false
	AutoVoucher *bool `json:"AutoVoucher,omitnil,omitempty" name:"AutoVoucher"`

	// 代金券ID列表，目前仅支持指定一张代金券
	VoucherIds []*string `json:"VoucherIds,omitnil,omitempty" name:"VoucherIds"`

	// 是否立即发货
	AsyncDeliver *bool `json:"AsyncDeliver,omitnil,omitempty" name:"AsyncDeliver"`

	// 推荐者uin
	CpsUin *string `json:"CpsUin,omitnil,omitempty" name:"CpsUin"`

	// 已废弃，请勿使用
	AgentPay *bool `json:"AgentPay,omitnil,omitempty" name:"AgentPay"`

	// 已废弃，请勿使用
	Payer *string `json:"Payer,omitnil,omitempty" name:"Payer"`

	// 废弃字段，请使用CreateAuth
	Auth *string `json:"Auth,omitnil,omitempty" name:"Auth"`

	// 鉴权参数，创建官网活动订单时需传鉴权参数
	CreateAuth *CreateAuth `json:"CreateAuth,omitnil,omitempty" name:"CreateAuth"`
}

type CreateOrdersAndPayRequest struct {
	*tchttp.BaseRequest

	// 下单类型，create开通或新购，renew续费，modify变配，refund退款，returnFitting退带宽
	GenerateType *string `json:"GenerateType,omitnil,omitempty" name:"GenerateType"`

	// 商品信息
	Goods []*GoodsInfo `json:"Goods,omitnil,omitempty" name:"Goods"`

	// 是否自动选择代金券 默认为false
	AutoVoucher *bool `json:"AutoVoucher,omitnil,omitempty" name:"AutoVoucher"`

	// 代金券ID列表，目前仅支持指定一张代金券
	VoucherIds []*string `json:"VoucherIds,omitnil,omitempty" name:"VoucherIds"`

	// 是否立即发货
	AsyncDeliver *bool `json:"AsyncDeliver,omitnil,omitempty" name:"AsyncDeliver"`

	// 推荐者uin
	CpsUin *string `json:"CpsUin,omitnil,omitempty" name:"CpsUin"`

	// 已废弃，请勿使用
	AgentPay *bool `json:"AgentPay,omitnil,omitempty" name:"AgentPay"`

	// 已废弃，请勿使用
	Payer *string `json:"Payer,omitnil,omitempty" name:"Payer"`

	// 废弃字段，请使用CreateAuth
	Auth *string `json:"Auth,omitnil,omitempty" name:"Auth"`

	// 鉴权参数，创建官网活动订单时需传鉴权参数
	CreateAuth *CreateAuth `json:"CreateAuth,omitnil,omitempty" name:"CreateAuth"`
}

func (r *CreateOrdersAndPayRequest) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// FromJsonString It is highly **NOT** recommended to use this function
// because it has no param check, nor strict type check
func (r *CreateOrdersAndPayRequest) FromJsonString(s string) error {
	f := make(map[string]interface{})
	if err := json.Unmarshal([]byte(s), &f); err != nil {
		return err
	}
	delete(f, "GenerateType")
	delete(f, "Goods")
	delete(f, "AutoVoucher")
	delete(f, "VoucherIds")
	delete(f, "AsyncDeliver")
	delete(f, "CpsUin")
	delete(f, "AgentPay")
	delete(f, "Payer")
	delete(f, "Auth")
	delete(f, "CreateAuth")
	if len(f) > 0 {
		return tcerr.NewTencentCloudSDKError("ClientError.BuildRequestError", "CreateOrdersAndPayRequest has unknown keys!", "")
	}
	return json.Unmarshal([]byte(s), &r)
}

// Predefined struct for user
type CreateOrdersAndPayResponseParams struct {
	// 此次操作成功的大订单号
	BigOrderId *string `json:"BigOrderId,omitnil,omitempty" name:"BigOrderId"`

	// 后付费冻结流水ID
	BillId *string `json:"BillId,omitnil,omitempty" name:"BillId"`

	// 此次操作成功的订单详情
	OrderDetails []*OrderDetails `json:"OrderDetails,omitnil,omitempty" name:"OrderDetails"`

	// 唯一请求 ID，由服务端生成，每次请求都会返回（若请求因其他原因未能抵达服务端，则该次请求不会获得 RequestId）。定位问题时需要提供该次请求的 RequestId。
	RequestId *string `json:"RequestId,omitnil,omitempty" name:"RequestId"`
}

type CreateOrdersAndPayResponse struct {
	*tchttp.BaseResponse
	Response *CreateOrdersAndPayResponseParams `json:"Response"`
}

func (r *CreateOrdersAndPayResponse) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// FromJsonString It is highly **NOT** recommended to use this function
// because it has no param check, nor strict type check
func (r *CreateOrdersAndPayResponse) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

type GoodsInfo struct {
	// 新购时表示商品数量，续费与配置变更时固定传1
	GoodsNum *int64 `json:"GoodsNum,omitnil,omitempty" name:"GoodsNum"`

	// 商品详情，timeSpan表示购买时间长度，timeUnit表示购买时间单位，pid表示定价公式ID，instances表示资源id，bandwidth表示带宽
	GoodsDetail *string `json:"GoodsDetail,omitnil,omitempty" name:"GoodsDetail"`

	// 订单类型ID，接入计费时由计费后台分配，下单时必传
	GoodsCategoryId *int64 `json:"GoodsCategoryId,omitnil,omitempty" name:"GoodsCategoryId"`

	// 地域字母缩写（ap code）
	Region *string `json:"Region,omitnil,omitempty" name:"Region"`

	// 区域全拼（ap code
	Zone *string `json:"Zone,omitnil,omitempty" name:"Zone"`

	// 项目ID，没有项目概念则传0
	ProjectId *int64 `json:"ProjectId,omitnil,omitempty" name:"ProjectId"`

	// 付费模式，postPay后付费/prePay预付费/riPay预留实例，下单、续费、变配时必传
	PayMode *string `json:"PayMode,omitnil,omitempty" name:"PayMode"`

	// 服务类型，一般是服务的简称。例如云服务器传cvm，数据库传cdb，云盘传cbs等。后付费开通、变配，退带宽时必传
	Business *string `json:"Business,omitnil,omitempty" name:"Business"`

	// 发货模式，2表示经销商进货，3表示经销商发货
	DeliverMode *int64 `json:"DeliverMode,omitnil,omitempty" name:"DeliverMode"`

	// 询价用途.purchase:新购、renew:续费、modify:变配、return:退费、autoRenew：自动续费
	Purpose *string `json:"Purpose,omitnil,omitempty" name:"Purpose"`

	// 子appId
	SubAppId *string `json:"SubAppId,omitnil,omitempty" name:"SubAppId"`

	// 对应midas处申请的offerId
	OfferId *string `json:"OfferId,omitnil,omitempty" name:"OfferId"`

	// 活动ID，传入活动id需要鉴权
	ActivityId *string `json:"ActivityId,omitnil,omitempty" name:"ActivityId"`

	// sku场景下没GoodsCategoryId， sku 场景下必选。
	// 询价的action,取值范围为
	// purchase,renew,modify.
	// purchase 新购询价
	// renew  续费询价
	// modify  变配询价
	Action *string `json:"Action,omitnil,omitempty" name:"Action"`
}

type OrderDetails struct {
	// 订单号
	OrderId *string `json:"OrderId,omitnil,omitempty" name:"OrderId"`

	// 后付费发货资源ID，每个订单号对应的发货资源id列表
	ResourceIds []*string `json:"ResourceIds,omitnil,omitempty" name:"ResourceIds"`
}
