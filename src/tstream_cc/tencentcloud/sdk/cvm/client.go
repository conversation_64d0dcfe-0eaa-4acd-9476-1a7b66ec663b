package cvm

import (
	"context"
	"errors"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common"
	tchttp "git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/http"
	"git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common/profile"
)

const APIVersion = "2017-03-12"

type Client struct {
	common.Client
}

func NewClient(credential common.CredentialIface, region string, clientProfile *profile.ClientProfile) (client *Client, err error) {
	client = &Client{}
	client.Init(region).
		WithCredential(credential).
		WithProfile(clientProfile)
	return
}

func NewDescribeZoneInstanceConfigInfosRequest() (request *DescribeZoneInstanceConfigInfosRequest) {
	request = &DescribeZoneInstanceConfigInfosRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}

	request.Init().WithApiInfo("cvm", APIVersion, "DescribeZoneInstanceConfigInfos")

	return
}

func NewDescribeZoneInstanceConfigInfosResponse() (response *DescribeZoneInstanceConfigInfosResponse) {
	response = &DescribeZoneInstanceConfigInfosResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return

}

// DescribeZoneInstanceConfigInfos
// 本接口(DescribeZoneInstanceConfigInfos) 获取可用区的机型信息。
//
// 可能返回的错误码:
//
//	INVALIDFILTER = "InvalidFilter"
//	INVALIDFILTERVALUE_LIMITEXCEEDED = "InvalidFilterValue.LimitExceeded"
//	INVALIDINSTANCEID_MALFORMED = "InvalidInstanceId.Malformed"
//	INVALIDINSTANCEID_NOTFOUND = "InvalidInstanceId.NotFound"
//	INVALIDINSTANCETYPE_MALFORMED = "InvalidInstanceType.Malformed"
//	INVALIDPARAMETERVALUE_INSTANCEIDMALFORMED = "InvalidParameterValue.InstanceIdMalformed"
//	INVALIDPARAMETERVALUE_INVALIDAPPIDFORMAT = "InvalidParameterValue.InvalidAppIdFormat"
//	INVALIDPARAMETERVALUE_LIMITEXCEEDED = "InvalidParameterValue.LimitExceeded"
//	INVALIDPARAMETERVALUE_ZONENOTSUPPORTED = "InvalidParameterValue.ZoneNotSupported"
//	INVALIDREGION_NOTFOUND = "InvalidRegion.NotFound"
//	INVALIDZONE_MISMATCHREGION = "InvalidZone.MismatchRegion"
//	RESOURCEINSUFFICIENT_AVAILABILITYZONESOLDOUT = "ResourceInsufficient.AvailabilityZoneSoldOut"
//	UNSUPPORTEDOPERATION = "UnsupportedOperation"
func (c *Client) DescribeZoneInstanceConfigInfos(request *DescribeZoneInstanceConfigInfosRequest) (response *DescribeZoneInstanceConfigInfosResponse, err error) {
	return c.DescribeZoneInstanceConfigInfosWithContext(context.Background(), request)
}

// DescribeZoneInstanceConfigInfos
// 本接口(DescribeZoneInstanceConfigInfos) 获取可用区的机型信息。
//
// 可能返回的错误码:
//
//	INVALIDFILTER = "InvalidFilter"
//	INVALIDFILTERVALUE_LIMITEXCEEDED = "InvalidFilterValue.LimitExceeded"
//	INVALIDINSTANCEID_MALFORMED = "InvalidInstanceId.Malformed"
//	INVALIDINSTANCEID_NOTFOUND = "InvalidInstanceId.NotFound"
//	INVALIDINSTANCETYPE_MALFORMED = "InvalidInstanceType.Malformed"
//	INVALIDPARAMETERVALUE_INSTANCEIDMALFORMED = "InvalidParameterValue.InstanceIdMalformed"
//	INVALIDPARAMETERVALUE_INVALIDAPPIDFORMAT = "InvalidParameterValue.InvalidAppIdFormat"
//	INVALIDPARAMETERVALUE_LIMITEXCEEDED = "InvalidParameterValue.LimitExceeded"
//	INVALIDPARAMETERVALUE_ZONENOTSUPPORTED = "InvalidParameterValue.ZoneNotSupported"
//	INVALIDREGION_NOTFOUND = "InvalidRegion.NotFound"
//	INVALIDZONE_MISMATCHREGION = "InvalidZone.MismatchRegion"
//	RESOURCEINSUFFICIENT_AVAILABILITYZONESOLDOUT = "ResourceInsufficient.AvailabilityZoneSoldOut"
//	UNSUPPORTEDOPERATION = "UnsupportedOperation"
func (c *Client) DescribeZoneInstanceConfigInfosWithContext(ctx context.Context, request *DescribeZoneInstanceConfigInfosRequest) (response *DescribeZoneInstanceConfigInfosResponse, err error) {
	if request == nil {
		request = NewDescribeZoneInstanceConfigInfosRequest()
	}

	if c.GetCredential() == nil {
		return nil, errors.New("DescribeZoneInstanceConfigInfos require credential")
	}

	request.SetContext(ctx)

	response = NewDescribeZoneInstanceConfigInfosResponse()
	err = c.Send(request, response)
	return
}
