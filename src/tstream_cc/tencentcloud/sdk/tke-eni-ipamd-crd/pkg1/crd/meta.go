/*
Copyright 2018 The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package crd

import (
	apiextensionsv1beta1 "k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1beta1"
	"k8s.io/kube-openapi/pkg/common"

	"tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking"
)

// CRDMeta contains version, validation and API information for a CRD.
type CRDMeta struct {
	groupName  string
	version    string
	kind       string
	listKind   string
	singular   string
	plural     string
	shortName  string
	typeSource string
	fn         common.GetOpenAPIDefinitions
	scope      apiextensionsv1beta1.ResourceScope
}

// NewCRDMeta creates a CRDMeta type which can be passed to a CRDHandler in
// order to create/ensure a CRD.
func NewCRDMeta(groupName, version, kind, listKind, singular, plural, shortName string, scope apiextensionsv1beta1.ResourceScope) *CRDMeta {
	return &CRDMeta{
		groupName: groupName,
		version:   version,
		kind:      kind,
		listKind:  listKind,
		singular:  singular,
		plural:    plural,
		shortName: shortName,
		scope:     scope,
	}
}

// AddValidationInfo adds information that is needed to ensure validation is
// properly added to a CRD when CRDHandler.EnsureCRD is called.
func (m *CRDMeta) AddValidationInfo(typeSource string, fn common.GetOpenAPIDefinitions) {
	m.typeSource = typeSource
	m.fn = fn
}

func NodeENIConfigCRDMeta() *CRDMeta {
	meta := NewCRDMeta(
		networking.GroupName,
		"v1",
		"NodeENIConfig",
		"NodeENIConfigList",
		"nodeeniconfig",
		"nodeeniconfigs",
		"nec",
		apiextensionsv1beta1.ClusterScoped,
	)
	return meta
}

func VpcIPClaimCRDMeta() *CRDMeta {
	meta := NewCRDMeta(
		networking.GroupName,
		"v1",
		"VpcIPClaim",
		"VpcIPClaimList",
		"vpcipclaim",
		"vpcipclaims",
		"vipc",
		apiextensionsv1beta1.NamespaceScoped,
	)
	return meta
}

func VpcIPCRDMeta() *CRDMeta {
	meta := NewCRDMeta(
		networking.GroupName,
		"v1",
		"VpcIP",
		"VpcIPList",
		"vpcip",
		"vpcips",
		"vip",
		apiextensionsv1beta1.ClusterScoped,
	)
	return meta
}

func EIPClaimCRDMeta() *CRDMeta {
	meta := NewCRDMeta(
		networking.GroupName,
		"v1",
		"EIPClaim",
		"EIPClaimList",
		"eipclaim",
		"eipclaims",
		"eipc",
		apiextensionsv1beta1.NamespaceScoped,
	)
	return meta
}

func VpcENICRDMeta() *CRDMeta {
	meta := NewCRDMeta(
		networking.GroupName,
		"v1",
		"VpcENI",
		"VpcENIList",
		"vpceni",
		"vpcenis",
		"veni",
		apiextensionsv1beta1.ClusterScoped,
	)
	return meta
}
