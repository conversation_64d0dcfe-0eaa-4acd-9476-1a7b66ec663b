/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	networkingv1 "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeNodeENIConfigs implements NodeENIConfigInterface
type FakeNodeENIConfigs struct {
	Fake *FakeNetworkingV1
}

var nodeeniconfigsResource = schema.GroupVersionResource{Group: "networking.tke.cloud.tencent.com", Version: "v1", Resource: "nodeeniconfigs"}

var nodeeniconfigsKind = schema.GroupVersionKind{Group: "networking.tke.cloud.tencent.com", Version: "v1", Kind: "NodeENIConfig"}

// Get takes name of the nodeENIConfig, and returns the corresponding nodeENIConfig object, and an error if there is any.
func (c *FakeNodeENIConfigs) Get(name string, options v1.GetOptions) (result *networkingv1.NodeENIConfig, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootGetAction(nodeeniconfigsResource, name), &networkingv1.NodeENIConfig{})
	if obj == nil {
		return nil, err
	}
	return obj.(*networkingv1.NodeENIConfig), err
}

// List takes label and field selectors, and returns the list of NodeENIConfigs that match those selectors.
func (c *FakeNodeENIConfigs) List(opts v1.ListOptions) (result *networkingv1.NodeENIConfigList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootListAction(nodeeniconfigsResource, nodeeniconfigsKind, opts), &networkingv1.NodeENIConfigList{})
	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &networkingv1.NodeENIConfigList{ListMeta: obj.(*networkingv1.NodeENIConfigList).ListMeta}
	for _, item := range obj.(*networkingv1.NodeENIConfigList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested nodeENIConfigs.
func (c *FakeNodeENIConfigs) Watch(opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewRootWatchAction(nodeeniconfigsResource, opts))
}

// Create takes the representation of a nodeENIConfig and creates it.  Returns the server's representation of the nodeENIConfig, and an error, if there is any.
func (c *FakeNodeENIConfigs) Create(nodeENIConfig *networkingv1.NodeENIConfig) (result *networkingv1.NodeENIConfig, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootCreateAction(nodeeniconfigsResource, nodeENIConfig), &networkingv1.NodeENIConfig{})
	if obj == nil {
		return nil, err
	}
	return obj.(*networkingv1.NodeENIConfig), err
}

// Update takes the representation of a nodeENIConfig and updates it. Returns the server's representation of the nodeENIConfig, and an error, if there is any.
func (c *FakeNodeENIConfigs) Update(nodeENIConfig *networkingv1.NodeENIConfig) (result *networkingv1.NodeENIConfig, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateAction(nodeeniconfigsResource, nodeENIConfig), &networkingv1.NodeENIConfig{})
	if obj == nil {
		return nil, err
	}
	return obj.(*networkingv1.NodeENIConfig), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeNodeENIConfigs) UpdateStatus(nodeENIConfig *networkingv1.NodeENIConfig) (*networkingv1.NodeENIConfig, error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateSubresourceAction(nodeeniconfigsResource, "status", nodeENIConfig), &networkingv1.NodeENIConfig{})
	if obj == nil {
		return nil, err
	}
	return obj.(*networkingv1.NodeENIConfig), err
}

// Delete takes name of the nodeENIConfig and deletes it. Returns an error if one occurs.
func (c *FakeNodeENIConfigs) Delete(name string, options *v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewRootDeleteAction(nodeeniconfigsResource, name), &networkingv1.NodeENIConfig{})
	return err
}

// DeleteCollection deletes a collection of objects.
func (c *FakeNodeENIConfigs) DeleteCollection(options *v1.DeleteOptions, listOptions v1.ListOptions) error {
	action := testing.NewRootDeleteCollectionAction(nodeeniconfigsResource, listOptions)

	_, err := c.Fake.Invokes(action, &networkingv1.NodeENIConfigList{})
	return err
}

// Patch applies the patch and returns the patched nodeENIConfig.
func (c *FakeNodeENIConfigs) Patch(name string, pt types.PatchType, data []byte, subresources ...string) (result *networkingv1.NodeENIConfig, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootPatchSubresourceAction(nodeeniconfigsResource, name, pt, data, subresources...), &networkingv1.NodeENIConfig{})
	if obj == nil {
		return nil, err
	}
	return obj.(*networkingv1.NodeENIConfig), err
}
