/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by lister-gen. DO NOT EDIT.

package v1

import (
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/tools/cache"
	v1 "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1"
)

// VpcIPClaimLister helps list VpcIPClaims.
type VpcIPClaimLister interface {
	// List lists all VpcIPClaims in the indexer.
	List(selector labels.Selector) (ret []*v1.VpcIPClaim, err error)
	// VpcIPClaims returns an object that can list and get VpcIPClaims.
	VpcIPClaims(namespace string) VpcIPClaimNamespaceLister
	VpcIPClaimListerExpansion
}

// vpcIPClaimLister implements the VpcIPClaimLister interface.
type vpcIPClaimLister struct {
	indexer cache.Indexer
}

// NewVpcIPClaimLister returns a new VpcIPClaimLister.
func NewVpcIPClaimLister(indexer cache.Indexer) VpcIPClaimLister {
	return &vpcIPClaimLister{indexer: indexer}
}

// List lists all VpcIPClaims in the indexer.
func (s *vpcIPClaimLister) List(selector labels.Selector) (ret []*v1.VpcIPClaim, err error) {
	err = cache.ListAll(s.indexer, selector, func(m interface{}) {
		ret = append(ret, m.(*v1.VpcIPClaim))
	})
	return ret, err
}

// VpcIPClaims returns an object that can list and get VpcIPClaims.
func (s *vpcIPClaimLister) VpcIPClaims(namespace string) VpcIPClaimNamespaceLister {
	return vpcIPClaimNamespaceLister{indexer: s.indexer, namespace: namespace}
}

// VpcIPClaimNamespaceLister helps list and get VpcIPClaims.
type VpcIPClaimNamespaceLister interface {
	// List lists all VpcIPClaims in the indexer for a given namespace.
	List(selector labels.Selector) (ret []*v1.VpcIPClaim, err error)
	// Get retrieves the VpcIPClaim from the indexer for a given namespace and name.
	Get(name string) (*v1.VpcIPClaim, error)
	VpcIPClaimNamespaceListerExpansion
}

// vpcIPClaimNamespaceLister implements the VpcIPClaimNamespaceLister
// interface.
type vpcIPClaimNamespaceLister struct {
	indexer   cache.Indexer
	namespace string
}

// List lists all VpcIPClaims in the indexer for a given namespace.
func (s vpcIPClaimNamespaceLister) List(selector labels.Selector) (ret []*v1.VpcIPClaim, err error) {
	err = cache.ListAllByNamespace(s.indexer, s.namespace, selector, func(m interface{}) {
		ret = append(ret, m.(*v1.VpcIPClaim))
	})
	return ret, err
}

// Get retrieves the VpcIPClaim from the indexer for a given namespace and name.
func (s vpcIPClaimNamespaceLister) Get(name string) (*v1.VpcIPClaim, error) {
	obj, exists, err := s.indexer.GetByKey(s.namespace + "/" + name)
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, errors.NewNotFound(v1.Resource("vpcipclaim"), name)
	}
	return obj.(*v1.VpcIPClaim), nil
}
