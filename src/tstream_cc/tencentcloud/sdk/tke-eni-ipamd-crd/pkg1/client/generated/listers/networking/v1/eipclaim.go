/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by lister-gen. DO NOT EDIT.

package v1

import (
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/tools/cache"
	v1 "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1"
)

// EIPClaimLister helps list EIPClaims.
type EIPClaimLister interface {
	// List lists all EIPClaims in the indexer.
	List(selector labels.Selector) (ret []*v1.EIPClaim, err error)
	// EIPClaims returns an object that can list and get EIPClaims.
	EIPClaims(namespace string) EIPClaimNamespaceLister
	EIPClaimListerExpansion
}

// eIPClaimLister implements the EIPClaimLister interface.
type eIPClaimLister struct {
	indexer cache.Indexer
}

// NewEIPClaimLister returns a new EIPClaimLister.
func NewEIPClaimLister(indexer cache.Indexer) EIPClaimLister {
	return &eIPClaimLister{indexer: indexer}
}

// List lists all EIPClaims in the indexer.
func (s *eIPClaimLister) List(selector labels.Selector) (ret []*v1.EIPClaim, err error) {
	err = cache.ListAll(s.indexer, selector, func(m interface{}) {
		ret = append(ret, m.(*v1.EIPClaim))
	})
	return ret, err
}

// EIPClaims returns an object that can list and get EIPClaims.
func (s *eIPClaimLister) EIPClaims(namespace string) EIPClaimNamespaceLister {
	return eIPClaimNamespaceLister{indexer: s.indexer, namespace: namespace}
}

// EIPClaimNamespaceLister helps list and get EIPClaims.
type EIPClaimNamespaceLister interface {
	// List lists all EIPClaims in the indexer for a given namespace.
	List(selector labels.Selector) (ret []*v1.EIPClaim, err error)
	// Get retrieves the EIPClaim from the indexer for a given namespace and name.
	Get(name string) (*v1.EIPClaim, error)
	EIPClaimNamespaceListerExpansion
}

// eIPClaimNamespaceLister implements the EIPClaimNamespaceLister
// interface.
type eIPClaimNamespaceLister struct {
	indexer   cache.Indexer
	namespace string
}

// List lists all EIPClaims in the indexer for a given namespace.
func (s eIPClaimNamespaceLister) List(selector labels.Selector) (ret []*v1.EIPClaim, err error) {
	err = cache.ListAllByNamespace(s.indexer, s.namespace, selector, func(m interface{}) {
		ret = append(ret, m.(*v1.EIPClaim))
	})
	return ret, err
}

// Get retrieves the EIPClaim from the indexer for a given namespace and name.
func (s eIPClaimNamespaceLister) Get(name string) (*v1.EIPClaim, error) {
	obj, exists, err := s.indexer.GetByKey(s.namespace + "/" + name)
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, errors.NewNotFound(v1.Resource("eipclaim"), name)
	}
	return obj.(*v1.EIPClaim), nil
}
