/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by lister-gen. DO NOT EDIT.

package v1

import (
	v1 "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/tools/cache"
)

// VpcIPLister helps list VpcIPs.
type VpcIPLister interface {
	// List lists all VpcIPs in the indexer.
	List(selector labels.Selector) (ret []*v1.VpcIP, err error)
	// Get retrieves the VpcIP from the index for a given name.
	Get(name string) (*v1.VpcIP, error)
	VpcIPListerExpansion
}

// vpcIPLister implements the VpcIPLister interface.
type vpcIPLister struct {
	indexer cache.Indexer
}

// NewVpcIPLister returns a new VpcIPLister.
func NewVpcIPLister(indexer cache.Indexer) VpcIPLister {
	return &vpcIPLister{indexer: indexer}
}

// List lists all VpcIPs in the indexer.
func (s *vpcIPLister) List(selector labels.Selector) (ret []*v1.VpcIP, err error) {
	err = cache.ListAll(s.indexer, selector, func(m interface{}) {
		ret = append(ret, m.(*v1.VpcIP))
	})
	return ret, err
}

// Get retrieves the VpcIP from the index for a given name.
func (s *vpcIPLister) Get(name string) (*v1.VpcIP, error) {
	obj, exists, err := s.indexer.GetByKey(name)
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, errors.NewNotFound(v1.Resource("vpcip"), name)
	}
	return obj.(*v1.VpcIP), nil
}
