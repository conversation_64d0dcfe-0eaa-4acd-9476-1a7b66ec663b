/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	networkingv1 "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeEIPClaims implements EIPClaimInterface
type FakeEIPClaims struct {
	Fake *FakeNetworkingV1
	ns   string
}

var eipclaimsResource = schema.GroupVersionResource{Group: "networking.tke.cloud.tencent.com", Version: "v1", Resource: "eipclaims"}

var eipclaimsKind = schema.GroupVersionKind{Group: "networking.tke.cloud.tencent.com", Version: "v1", Kind: "EIPClaim"}

// Get takes name of the eIPClaim, and returns the corresponding eIPClaim object, and an error if there is any.
func (c *FakeEIPClaims) Get(name string, options v1.GetOptions) (result *networkingv1.EIPClaim, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewGetAction(eipclaimsResource, c.ns, name), &networkingv1.EIPClaim{})

	if obj == nil {
		return nil, err
	}
	return obj.(*networkingv1.EIPClaim), err
}

// List takes label and field selectors, and returns the list of EIPClaims that match those selectors.
func (c *FakeEIPClaims) List(opts v1.ListOptions) (result *networkingv1.EIPClaimList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewListAction(eipclaimsResource, eipclaimsKind, c.ns, opts), &networkingv1.EIPClaimList{})

	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &networkingv1.EIPClaimList{ListMeta: obj.(*networkingv1.EIPClaimList).ListMeta}
	for _, item := range obj.(*networkingv1.EIPClaimList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested eIPClaims.
func (c *FakeEIPClaims) Watch(opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewWatchAction(eipclaimsResource, c.ns, opts))

}

// Create takes the representation of a eIPClaim and creates it.  Returns the server's representation of the eIPClaim, and an error, if there is any.
func (c *FakeEIPClaims) Create(eIPClaim *networkingv1.EIPClaim) (result *networkingv1.EIPClaim, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewCreateAction(eipclaimsResource, c.ns, eIPClaim), &networkingv1.EIPClaim{})

	if obj == nil {
		return nil, err
	}
	return obj.(*networkingv1.EIPClaim), err
}

// Update takes the representation of a eIPClaim and updates it. Returns the server's representation of the eIPClaim, and an error, if there is any.
func (c *FakeEIPClaims) Update(eIPClaim *networkingv1.EIPClaim) (result *networkingv1.EIPClaim, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewUpdateAction(eipclaimsResource, c.ns, eIPClaim), &networkingv1.EIPClaim{})

	if obj == nil {
		return nil, err
	}
	return obj.(*networkingv1.EIPClaim), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeEIPClaims) UpdateStatus(eIPClaim *networkingv1.EIPClaim) (*networkingv1.EIPClaim, error) {
	obj, err := c.Fake.
		Invokes(testing.NewUpdateSubresourceAction(eipclaimsResource, "status", c.ns, eIPClaim), &networkingv1.EIPClaim{})

	if obj == nil {
		return nil, err
	}
	return obj.(*networkingv1.EIPClaim), err
}

// Delete takes name of the eIPClaim and deletes it. Returns an error if one occurs.
func (c *FakeEIPClaims) Delete(name string, options *v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewDeleteAction(eipclaimsResource, c.ns, name), &networkingv1.EIPClaim{})

	return err
}

// DeleteCollection deletes a collection of objects.
func (c *FakeEIPClaims) DeleteCollection(options *v1.DeleteOptions, listOptions v1.ListOptions) error {
	action := testing.NewDeleteCollectionAction(eipclaimsResource, c.ns, listOptions)

	_, err := c.Fake.Invokes(action, &networkingv1.EIPClaimList{})
	return err
}

// Patch applies the patch and returns the patched eIPClaim.
func (c *FakeEIPClaims) Patch(name string, pt types.PatchType, data []byte, subresources ...string) (result *networkingv1.EIPClaim, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewPatchSubresourceAction(eipclaimsResource, c.ns, name, pt, data, subresources...), &networkingv1.EIPClaim{})

	if obj == nil {
		return nil, err
	}
	return obj.(*networkingv1.EIPClaim), err
}
