/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	networkingv1 "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeVpcENIs implements VpcENIInterface
type FakeVpcENIs struct {
	Fake *FakeNetworkingV1
}

var vpcenisResource = schema.GroupVersionResource{Group: "networking.tke.cloud.tencent.com", Version: "v1", Resource: "vpcenis"}

var vpcenisKind = schema.GroupVersionKind{Group: "networking.tke.cloud.tencent.com", Version: "v1", Kind: "VpcENI"}

// Get takes name of the vpcENI, and returns the corresponding vpcENI object, and an error if there is any.
func (c *FakeVpcENIs) Get(name string, options v1.GetOptions) (result *networkingv1.VpcENI, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootGetAction(vpcenisResource, name), &networkingv1.VpcENI{})
	if obj == nil {
		return nil, err
	}
	return obj.(*networkingv1.VpcENI), err
}

// List takes label and field selectors, and returns the list of VpcENIs that match those selectors.
func (c *FakeVpcENIs) List(opts v1.ListOptions) (result *networkingv1.VpcENIList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootListAction(vpcenisResource, vpcenisKind, opts), &networkingv1.VpcENIList{})
	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &networkingv1.VpcENIList{ListMeta: obj.(*networkingv1.VpcENIList).ListMeta}
	for _, item := range obj.(*networkingv1.VpcENIList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested vpcENIs.
func (c *FakeVpcENIs) Watch(opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewRootWatchAction(vpcenisResource, opts))
}

// Create takes the representation of a vpcENI and creates it.  Returns the server's representation of the vpcENI, and an error, if there is any.
func (c *FakeVpcENIs) Create(vpcENI *networkingv1.VpcENI) (result *networkingv1.VpcENI, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootCreateAction(vpcenisResource, vpcENI), &networkingv1.VpcENI{})
	if obj == nil {
		return nil, err
	}
	return obj.(*networkingv1.VpcENI), err
}

// Update takes the representation of a vpcENI and updates it. Returns the server's representation of the vpcENI, and an error, if there is any.
func (c *FakeVpcENIs) Update(vpcENI *networkingv1.VpcENI) (result *networkingv1.VpcENI, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateAction(vpcenisResource, vpcENI), &networkingv1.VpcENI{})
	if obj == nil {
		return nil, err
	}
	return obj.(*networkingv1.VpcENI), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeVpcENIs) UpdateStatus(vpcENI *networkingv1.VpcENI) (*networkingv1.VpcENI, error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateSubresourceAction(vpcenisResource, "status", vpcENI), &networkingv1.VpcENI{})
	if obj == nil {
		return nil, err
	}
	return obj.(*networkingv1.VpcENI), err
}

// Delete takes name of the vpcENI and deletes it. Returns an error if one occurs.
func (c *FakeVpcENIs) Delete(name string, options *v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewRootDeleteAction(vpcenisResource, name), &networkingv1.VpcENI{})
	return err
}

// DeleteCollection deletes a collection of objects.
func (c *FakeVpcENIs) DeleteCollection(options *v1.DeleteOptions, listOptions v1.ListOptions) error {
	action := testing.NewRootDeleteCollectionAction(vpcenisResource, listOptions)

	_, err := c.Fake.Invokes(action, &networkingv1.VpcENIList{})
	return err
}

// Patch applies the patch and returns the patched vpcENI.
func (c *FakeVpcENIs) Patch(name string, pt types.PatchType, data []byte, subresources ...string) (result *networkingv1.VpcENI, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootPatchSubresourceAction(vpcenisResource, name, pt, data, subresources...), &networkingv1.VpcENI{})
	if obj == nil {
		return nil, err
	}
	return obj.(*networkingv1.VpcENI), err
}
