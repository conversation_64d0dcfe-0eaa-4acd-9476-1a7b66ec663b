/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	networkingv1 "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeVpcIPClaims implements VpcIPClaimInterface
type FakeVpcIPClaims struct {
	Fake *FakeNetworkingV1
	ns   string
}

var vpcipclaimsResource = schema.GroupVersionResource{Group: "networking.tke.cloud.tencent.com", Version: "v1", Resource: "vpcipclaims"}

var vpcipclaimsKind = schema.GroupVersionKind{Group: "networking.tke.cloud.tencent.com", Version: "v1", Kind: "VpcIPClaim"}

// Get takes name of the vpcIPClaim, and returns the corresponding vpcIPClaim object, and an error if there is any.
func (c *FakeVpcIPClaims) Get(name string, options v1.GetOptions) (result *networkingv1.VpcIPClaim, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewGetAction(vpcipclaimsResource, c.ns, name), &networkingv1.VpcIPClaim{})

	if obj == nil {
		return nil, err
	}
	return obj.(*networkingv1.VpcIPClaim), err
}

// List takes label and field selectors, and returns the list of VpcIPClaims that match those selectors.
func (c *FakeVpcIPClaims) List(opts v1.ListOptions) (result *networkingv1.VpcIPClaimList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewListAction(vpcipclaimsResource, vpcipclaimsKind, c.ns, opts), &networkingv1.VpcIPClaimList{})

	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &networkingv1.VpcIPClaimList{ListMeta: obj.(*networkingv1.VpcIPClaimList).ListMeta}
	for _, item := range obj.(*networkingv1.VpcIPClaimList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested vpcIPClaims.
func (c *FakeVpcIPClaims) Watch(opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewWatchAction(vpcipclaimsResource, c.ns, opts))

}

// Create takes the representation of a vpcIPClaim and creates it.  Returns the server's representation of the vpcIPClaim, and an error, if there is any.
func (c *FakeVpcIPClaims) Create(vpcIPClaim *networkingv1.VpcIPClaim) (result *networkingv1.VpcIPClaim, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewCreateAction(vpcipclaimsResource, c.ns, vpcIPClaim), &networkingv1.VpcIPClaim{})

	if obj == nil {
		return nil, err
	}
	return obj.(*networkingv1.VpcIPClaim), err
}

// Update takes the representation of a vpcIPClaim and updates it. Returns the server's representation of the vpcIPClaim, and an error, if there is any.
func (c *FakeVpcIPClaims) Update(vpcIPClaim *networkingv1.VpcIPClaim) (result *networkingv1.VpcIPClaim, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewUpdateAction(vpcipclaimsResource, c.ns, vpcIPClaim), &networkingv1.VpcIPClaim{})

	if obj == nil {
		return nil, err
	}
	return obj.(*networkingv1.VpcIPClaim), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeVpcIPClaims) UpdateStatus(vpcIPClaim *networkingv1.VpcIPClaim) (*networkingv1.VpcIPClaim, error) {
	obj, err := c.Fake.
		Invokes(testing.NewUpdateSubresourceAction(vpcipclaimsResource, "status", c.ns, vpcIPClaim), &networkingv1.VpcIPClaim{})

	if obj == nil {
		return nil, err
	}
	return obj.(*networkingv1.VpcIPClaim), err
}

// Delete takes name of the vpcIPClaim and deletes it. Returns an error if one occurs.
func (c *FakeVpcIPClaims) Delete(name string, options *v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewDeleteAction(vpcipclaimsResource, c.ns, name), &networkingv1.VpcIPClaim{})

	return err
}

// DeleteCollection deletes a collection of objects.
func (c *FakeVpcIPClaims) DeleteCollection(options *v1.DeleteOptions, listOptions v1.ListOptions) error {
	action := testing.NewDeleteCollectionAction(vpcipclaimsResource, c.ns, listOptions)

	_, err := c.Fake.Invokes(action, &networkingv1.VpcIPClaimList{})
	return err
}

// Patch applies the patch and returns the patched vpcIPClaim.
func (c *FakeVpcIPClaims) Patch(name string, pt types.PatchType, data []byte, subresources ...string) (result *networkingv1.VpcIPClaim, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewPatchSubresourceAction(vpcipclaimsResource, c.ns, name, pt, data, subresources...), &networkingv1.VpcIPClaim{})

	if obj == nil {
		return nil, err
	}
	return obj.(*networkingv1.VpcIPClaim), err
}
