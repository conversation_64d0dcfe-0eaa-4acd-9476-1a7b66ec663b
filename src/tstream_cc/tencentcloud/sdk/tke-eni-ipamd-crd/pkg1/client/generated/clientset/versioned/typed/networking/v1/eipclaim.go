/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package v1

import (
	"context"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	rest "k8s.io/client-go/rest"
	v1 "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1"
	scheme "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/client/generated/clientset/versioned/scheme"
)

// EIPClaimsGetter has a method to return a EIPClaimInterface.
// A group's client should implement this interface.
type EIPClaimsGetter interface {
	EIPClaims(namespace string) EIPClaimInterface
}

// EIPClaimInterface has methods to work with EIPClaim resources.
type EIPClaimInterface interface {
	Create(*v1.EIPClaim) (*v1.EIPClaim, error)
	Update(*v1.EIPClaim) (*v1.EIPClaim, error)
	UpdateStatus(*v1.EIPClaim) (*v1.EIPClaim, error)
	Delete(name string, options *metav1.DeleteOptions) error
	DeleteCollection(options *metav1.DeleteOptions, listOptions metav1.ListOptions) error
	Get(name string, options metav1.GetOptions) (*v1.EIPClaim, error)
	List(opts metav1.ListOptions) (*v1.EIPClaimList, error)
	Watch(opts metav1.ListOptions) (watch.Interface, error)
	Patch(name string, pt types.PatchType, data []byte, subresources ...string) (result *v1.EIPClaim, err error)
	EIPClaimExpansion
}

// eIPClaims implements EIPClaimInterface
type eIPClaims struct {
	client rest.Interface
	ns     string
}

// newEIPClaims returns a EIPClaims
func newEIPClaims(c *NetworkingV1Client, namespace string) *eIPClaims {
	return &eIPClaims{
		client: c.RESTClient(),
		ns:     namespace,
	}
}

// Get takes name of the eIPClaim, and returns the corresponding eIPClaim object, and an error if there is any.
func (c *eIPClaims) Get(name string, options metav1.GetOptions) (result *v1.EIPClaim, err error) {
	result = &v1.EIPClaim{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("eipclaims").
		Name(name).
		VersionedParams(&options, scheme.ParameterCodec).
		Do(context.TODO()).
		Into(result)
	return
}

// List takes label and field selectors, and returns the list of EIPClaims that match those selectors.
func (c *eIPClaims) List(opts metav1.ListOptions) (result *v1.EIPClaimList, err error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	result = &v1.EIPClaimList{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("eipclaims").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(context.TODO()).
		Into(result)
	return
}

// Watch returns a watch.Interface that watches the requested eIPClaims.
func (c *eIPClaims) Watch(opts metav1.ListOptions) (watch.Interface, error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	opts.Watch = true
	return c.client.Get().
		Namespace(c.ns).
		Resource("eipclaims").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Watch(context.TODO())
}

// Create takes the representation of a eIPClaim and creates it.  Returns the server's representation of the eIPClaim, and an error, if there is any.
func (c *eIPClaims) Create(eIPClaim *v1.EIPClaim) (result *v1.EIPClaim, err error) {
	result = &v1.EIPClaim{}
	err = c.client.Post().
		Namespace(c.ns).
		Resource("eipclaims").
		Body(eIPClaim).
		Do(context.TODO()).
		Into(result)
	return
}

// Update takes the representation of a eIPClaim and updates it. Returns the server's representation of the eIPClaim, and an error, if there is any.
func (c *eIPClaims) Update(eIPClaim *v1.EIPClaim) (result *v1.EIPClaim, err error) {
	result = &v1.EIPClaim{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("eipclaims").
		Name(eIPClaim.Name).
		Body(eIPClaim).
		Do(context.TODO()).
		Into(result)
	return
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().

func (c *eIPClaims) UpdateStatus(eIPClaim *v1.EIPClaim) (result *v1.EIPClaim, err error) {
	result = &v1.EIPClaim{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("eipclaims").
		Name(eIPClaim.Name).
		SubResource("status").
		Body(eIPClaim).
		Do(context.TODO()).
		Into(result)
	return
}

// Delete takes name of the eIPClaim and deletes it. Returns an error if one occurs.
func (c *eIPClaims) Delete(name string, options *metav1.DeleteOptions) error {
	return c.client.Delete().
		Namespace(c.ns).
		Resource("eipclaims").
		Name(name).
		Body(options).
		Do(context.TODO()).
		Error()
}

// DeleteCollection deletes a collection of objects.
func (c *eIPClaims) DeleteCollection(options *metav1.DeleteOptions, listOptions metav1.ListOptions) error {
	var timeout time.Duration
	if listOptions.TimeoutSeconds != nil {
		timeout = time.Duration(*listOptions.TimeoutSeconds) * time.Second
	}
	return c.client.Delete().
		Namespace(c.ns).
		Resource("eipclaims").
		VersionedParams(&listOptions, scheme.ParameterCodec).
		Timeout(timeout).
		Body(options).
		Do(context.TODO()).
		Error()
}

// Patch applies the patch and returns the patched eIPClaim.
func (c *eIPClaims) Patch(name string, pt types.PatchType, data []byte, subresources ...string) (result *v1.EIPClaim, err error) {
	result = &v1.EIPClaim{}
	err = c.client.Patch(pt).
		Namespace(c.ns).
		Resource("eipclaims").
		SubResource(subresources...).
		Name(name).
		Body(data).
		Do(context.TODO()).
		Into(result)
	return
}
