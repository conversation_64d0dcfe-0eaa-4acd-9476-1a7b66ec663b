/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by lister-gen. DO NOT EDIT.

package v1

import (
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/tools/cache"
	v1 "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1"
)

// VpcENILister helps list VpcENIs.
type VpcENILister interface {
	// List lists all VpcENIs in the indexer.
	List(selector labels.Selector) (ret []*v1.VpcENI, err error)
	// Get retrieves the VpcENI from the index for a given name.
	Get(name string) (*v1.VpcENI, error)
	VpcENIListerExpansion
}

// vpcENILister implements the VpcENILister interface.
type vpcENILister struct {
	indexer cache.Indexer
}

// NewVpcENILister returns a new VpcENILister.
func NewVpcENILister(indexer cache.Indexer) VpcENILister {
	return &vpcENILister{indexer: indexer}
}

// List lists all VpcENIs in the indexer.
func (s *vpcENILister) List(selector labels.Selector) (ret []*v1.VpcENI, err error) {
	err = cache.ListAll(s.indexer, selector, func(m interface{}) {
		ret = append(ret, m.(*v1.VpcENI))
	})
	return ret, err
}

// Get retrieves the VpcENI from the index for a given name.
func (s *vpcENILister) Get(name string) (*v1.VpcENI, error) {
	obj, exists, err := s.indexer.GetByKey(name)
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, errors.NewNotFound(v1.Resource("vpceni"), name)
	}
	return obj.(*v1.VpcENI), nil
}
