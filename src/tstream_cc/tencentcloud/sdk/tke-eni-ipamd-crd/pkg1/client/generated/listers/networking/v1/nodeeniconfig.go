/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by lister-gen. DO NOT EDIT.

package v1

import (
	v1 "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/tools/cache"
)

// NodeENIConfigLister helps list NodeENIConfigs.
type NodeENIConfigLister interface {
	// List lists all NodeENIConfigs in the indexer.
	List(selector labels.Selector) (ret []*v1.NodeENIConfig, err error)
	// Get retrieves the NodeENIConfig from the index for a given name.
	Get(name string) (*v1.NodeENIConfig, error)
	NodeENIConfigListerExpansion
}

// nodeENIConfigLister implements the NodeENIConfigLister interface.
type nodeENIConfigLister struct {
	indexer cache.Indexer
}

// NewNodeENIConfigLister returns a new NodeENIConfigLister.
func NewNodeENIConfigLister(indexer cache.Indexer) NodeENIConfigLister {
	return &nodeENIConfigLister{indexer: indexer}
}

// List lists all NodeENIConfigs in the indexer.
func (s *nodeENIConfigLister) List(selector labels.Selector) (ret []*v1.NodeENIConfig, err error) {
	err = cache.ListAll(s.indexer, selector, func(m interface{}) {
		ret = append(ret, m.(*v1.NodeENIConfig))
	})
	return ret, err
}

// Get retrieves the NodeENIConfig from the index for a given name.
func (s *nodeENIConfigLister) Get(name string) (*v1.NodeENIConfig, error) {
	obj, exists, err := s.indexer.GetByKey(name)
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, errors.NewNotFound(v1.Resource("nodeeniconfig"), name)
	}
	return obj.(*v1.NodeENIConfig), nil
}
