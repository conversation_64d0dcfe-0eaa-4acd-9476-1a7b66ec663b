/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package v1

import (
	"context"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	rest "k8s.io/client-go/rest"
	v1 "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1"
	scheme "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/client/generated/clientset/versioned/scheme"
)

// NodeENIConfigsGetter has a method to return a NodeENIConfigInterface.
// A group's client should implement this interface.
type NodeENIConfigsGetter interface {
	NodeENIConfigs() NodeENIConfigInterface
}

// NodeENIConfigInterface has methods to work with NodeENIConfig resources.
type NodeENIConfigInterface interface {
	Create(*v1.NodeENIConfig) (*v1.NodeENIConfig, error)
	Update(*v1.NodeENIConfig) (*v1.NodeENIConfig, error)
	UpdateStatus(*v1.NodeENIConfig) (*v1.NodeENIConfig, error)
	Delete(name string, options *metav1.DeleteOptions) error
	DeleteCollection(options *metav1.DeleteOptions, listOptions metav1.ListOptions) error
	Get(name string, options metav1.GetOptions) (*v1.NodeENIConfig, error)
	List(opts metav1.ListOptions) (*v1.NodeENIConfigList, error)
	Watch(opts metav1.ListOptions) (watch.Interface, error)
	Patch(name string, pt types.PatchType, data []byte, subresources ...string) (result *v1.NodeENIConfig, err error)
	NodeENIConfigExpansion
}

// nodeENIConfigs implements NodeENIConfigInterface
type nodeENIConfigs struct {
	client rest.Interface
}

// newNodeENIConfigs returns a NodeENIConfigs
func newNodeENIConfigs(c *NetworkingV1Client) *nodeENIConfigs {
	return &nodeENIConfigs{
		client: c.RESTClient(),
	}
}

// Get takes name of the nodeENIConfig, and returns the corresponding nodeENIConfig object, and an error if there is any.
func (c *nodeENIConfigs) Get(name string, options metav1.GetOptions) (result *v1.NodeENIConfig, err error) {
	result = &v1.NodeENIConfig{}
	err = c.client.Get().
		Resource("nodeeniconfigs").
		Name(name).
		VersionedParams(&options, scheme.ParameterCodec).
		Do(context.TODO()).
		Into(result)
	return
}

// List takes label and field selectors, and returns the list of NodeENIConfigs that match those selectors.
func (c *nodeENIConfigs) List(opts metav1.ListOptions) (result *v1.NodeENIConfigList, err error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	result = &v1.NodeENIConfigList{}
	err = c.client.Get().
		Resource("nodeeniconfigs").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(context.TODO()).
		Into(result)
	return
}

// Watch returns a watch.Interface that watches the requested nodeENIConfigs.
func (c *nodeENIConfigs) Watch(opts metav1.ListOptions) (watch.Interface, error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	opts.Watch = true
	return c.client.Get().
		Resource("nodeeniconfigs").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Watch(context.TODO())
}

// Create takes the representation of a nodeENIConfig and creates it.  Returns the server's representation of the nodeENIConfig, and an error, if there is any.
func (c *nodeENIConfigs) Create(nodeENIConfig *v1.NodeENIConfig) (result *v1.NodeENIConfig, err error) {
	result = &v1.NodeENIConfig{}
	err = c.client.Post().
		Resource("nodeeniconfigs").
		Body(nodeENIConfig).
		Do(context.TODO()).
		Into(result)
	return
}

// Update takes the representation of a nodeENIConfig and updates it. Returns the server's representation of the nodeENIConfig, and an error, if there is any.
func (c *nodeENIConfigs) Update(nodeENIConfig *v1.NodeENIConfig) (result *v1.NodeENIConfig, err error) {
	result = &v1.NodeENIConfig{}
	err = c.client.Put().
		Resource("nodeeniconfigs").
		Name(nodeENIConfig.Name).
		Body(nodeENIConfig).
		Do(context.TODO()).
		Into(result)
	return
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().

func (c *nodeENIConfigs) UpdateStatus(nodeENIConfig *v1.NodeENIConfig) (result *v1.NodeENIConfig, err error) {
	result = &v1.NodeENIConfig{}
	err = c.client.Put().
		Resource("nodeeniconfigs").
		Name(nodeENIConfig.Name).
		SubResource("status").
		Body(nodeENIConfig).
		Do(context.TODO()).
		Into(result)
	return
}

// Delete takes name of the nodeENIConfig and deletes it. Returns an error if one occurs.
func (c *nodeENIConfigs) Delete(name string, options *metav1.DeleteOptions) error {
	return c.client.Delete().
		Resource("nodeeniconfigs").
		Name(name).
		Body(options).
		Do(context.TODO()).
		Error()
}

// DeleteCollection deletes a collection of objects.
func (c *nodeENIConfigs) DeleteCollection(options *metav1.DeleteOptions, listOptions metav1.ListOptions) error {
	var timeout time.Duration
	if listOptions.TimeoutSeconds != nil {
		timeout = time.Duration(*listOptions.TimeoutSeconds) * time.Second
	}
	return c.client.Delete().
		Resource("nodeeniconfigs").
		VersionedParams(&listOptions, scheme.ParameterCodec).
		Timeout(timeout).
		Body(options).
		Do(context.TODO()).
		Error()
}

// Patch applies the patch and returns the patched nodeENIConfig.
func (c *nodeENIConfigs) Patch(name string, pt types.PatchType, data []byte, subresources ...string) (result *v1.NodeENIConfig, err error) {
	result = &v1.NodeENIConfig{}
	err = c.client.Patch(pt).
		Resource("nodeeniconfigs").
		SubResource(subresources...).
		Name(name).
		Body(data).
		Do(context.TODO()).
		Into(result)
	return
}
