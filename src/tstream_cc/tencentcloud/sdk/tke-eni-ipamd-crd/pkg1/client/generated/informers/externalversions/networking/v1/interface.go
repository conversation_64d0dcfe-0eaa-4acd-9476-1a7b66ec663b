/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by informer-gen. DO NOT EDIT.

package v1

import (
	internalinterfaces "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/client/generated/informers/externalversions/internalinterfaces"
)

// Interface provides access to all the informers in this group version.
type Interface interface {
	// EIPClaims returns a EIPClaimInformer.
	EIPClaims() EIPClaimInformer
	// NodeENIConfigs returns a NodeENIConfigInformer.
	NodeENIConfigs() NodeENIConfigInformer
	// VpcENIs returns a VpcENIInformer.
	VpcENIs() VpcENIInformer
	// VpcIPs returns a VpcIPInformer.
	VpcIPs() VpcIPInformer
	// VpcIPClaims returns a VpcIPClaimInformer.
	VpcIPClaims() VpcIPClaimInformer
}

type version struct {
	factory          internalinterfaces.SharedInformerFactory
	namespace        string
	tweakListOptions internalinterfaces.TweakListOptionsFunc
}

// New returns a new Interface.
func New(f internalinterfaces.SharedInformerFactory, namespace string, tweakListOptions internalinterfaces.TweakListOptionsFunc) Interface {
	return &version{factory: f, namespace: namespace, tweakListOptions: tweakListOptions}
}

// EIPClaims returns a EIPClaimInformer.
func (v *version) EIPClaims() EIPClaimInformer {
	return &eIPClaimInformer{factory: v.factory, namespace: v.namespace, tweakListOptions: v.tweakListOptions}
}

// NodeENIConfigs returns a NodeENIConfigInformer.
func (v *version) NodeENIConfigs() NodeENIConfigInformer {
	return &nodeENIConfigInformer{factory: v.factory, tweakListOptions: v.tweakListOptions}
}

// VpcENIs returns a VpcENIInformer.
func (v *version) VpcENIs() VpcENIInformer {
	return &vpcENIInformer{factory: v.factory, tweakListOptions: v.tweakListOptions}
}

// VpcIPs returns a VpcIPInformer.
func (v *version) VpcIPs() VpcIPInformer {
	return &vpcIPInformer{factory: v.factory, tweakListOptions: v.tweakListOptions}
}

// VpcIPClaims returns a VpcIPClaimInformer.
func (v *version) VpcIPClaims() VpcIPClaimInformer {
	return &vpcIPClaimInformer{factory: v.factory, namespace: v.namespace, tweakListOptions: v.tweakListOptions}
}
