/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package v1

import (
	"context"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	rest "k8s.io/client-go/rest"
	v1 "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1"
	scheme "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/client/generated/clientset/versioned/scheme"
)

// VpcENIsGetter has a method to return a VpcENIInterface.
// A group's client should implement this interface.
type VpcENIsGetter interface {
	VpcENIs() VpcENIInterface
}

// VpcENIInterface has methods to work with VpcENI resources.
type VpcENIInterface interface {
	Create(*v1.VpcENI) (*v1.VpcENI, error)
	Update(*v1.VpcENI) (*v1.VpcENI, error)
	UpdateStatus(*v1.VpcENI) (*v1.VpcENI, error)
	Delete(name string, options *metav1.DeleteOptions) error
	DeleteCollection(options *metav1.DeleteOptions, listOptions metav1.ListOptions) error
	Get(name string, options metav1.GetOptions) (*v1.VpcENI, error)
	List(opts metav1.ListOptions) (*v1.VpcENIList, error)
	Watch(opts metav1.ListOptions) (watch.Interface, error)
	Patch(name string, pt types.PatchType, data []byte, subresources ...string) (result *v1.VpcENI, err error)
	VpcENIExpansion
}

// vpcENIs implements VpcENIInterface
type vpcENIs struct {
	client rest.Interface
}

// newVpcENIs returns a VpcENIs
func newVpcENIs(c *NetworkingV1Client) *vpcENIs {
	return &vpcENIs{
		client: c.RESTClient(),
	}
}

// Get takes name of the vpcENI, and returns the corresponding vpcENI object, and an error if there is any.
func (c *vpcENIs) Get(name string, options metav1.GetOptions) (result *v1.VpcENI, err error) {
	result = &v1.VpcENI{}
	err = c.client.Get().
		Resource("vpcenis").
		Name(name).
		VersionedParams(&options, scheme.ParameterCodec).
		Do(context.TODO()).
		Into(result)
	return
}

// List takes label and field selectors, and returns the list of VpcENIs that match those selectors.
func (c *vpcENIs) List(opts metav1.ListOptions) (result *v1.VpcENIList, err error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	result = &v1.VpcENIList{}
	err = c.client.Get().
		Resource("vpcenis").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(context.TODO()).
		Into(result)
	return
}

// Watch returns a watch.Interface that watches the requested vpcENIs.
func (c *vpcENIs) Watch(opts metav1.ListOptions) (watch.Interface, error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	opts.Watch = true
	return c.client.Get().
		Resource("vpcenis").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Watch(context.TODO())
}

// Create takes the representation of a vpcENI and creates it.  Returns the server's representation of the vpcENI, and an error, if there is any.
func (c *vpcENIs) Create(vpcENI *v1.VpcENI) (result *v1.VpcENI, err error) {
	result = &v1.VpcENI{}
	err = c.client.Post().
		Resource("vpcenis").
		Body(vpcENI).
		Do(context.TODO()).
		Into(result)
	return
}

// Update takes the representation of a vpcENI and updates it. Returns the server's representation of the vpcENI, and an error, if there is any.
func (c *vpcENIs) Update(vpcENI *v1.VpcENI) (result *v1.VpcENI, err error) {
	result = &v1.VpcENI{}
	err = c.client.Put().
		Resource("vpcenis").
		Name(vpcENI.Name).
		Body(vpcENI).
		Do(context.TODO()).
		Into(result)
	return
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().

func (c *vpcENIs) UpdateStatus(vpcENI *v1.VpcENI) (result *v1.VpcENI, err error) {
	result = &v1.VpcENI{}
	err = c.client.Put().
		Resource("vpcenis").
		Name(vpcENI.Name).
		SubResource("status").
		Body(vpcENI).
		Do(context.TODO()).
		Into(result)
	return
}

// Delete takes name of the vpcENI and deletes it. Returns an error if one occurs.
func (c *vpcENIs) Delete(name string, options *metav1.DeleteOptions) error {
	return c.client.Delete().
		Resource("vpcenis").
		Name(name).
		Body(options).
		Do(context.TODO()).
		Error()
}

// DeleteCollection deletes a collection of objects.
func (c *vpcENIs) DeleteCollection(options *metav1.DeleteOptions, listOptions metav1.ListOptions) error {
	var timeout time.Duration
	if listOptions.TimeoutSeconds != nil {
		timeout = time.Duration(*listOptions.TimeoutSeconds) * time.Second
	}
	return c.client.Delete().
		Resource("vpcenis").
		VersionedParams(&listOptions, scheme.ParameterCodec).
		Timeout(timeout).
		Body(options).
		Do(context.TODO()).
		Error()
}

// Patch applies the patch and returns the patched vpcENI.
func (c *vpcENIs) Patch(name string, pt types.PatchType, data []byte, subresources ...string) (result *v1.VpcENI, err error) {
	result = &v1.VpcENI{}
	err = c.client.Patch(pt).
		Resource("vpcenis").
		SubResource(subresources...).
		Name(name).
		Body(data).
		Do(context.TODO()).
		Into(result)
	return
}
