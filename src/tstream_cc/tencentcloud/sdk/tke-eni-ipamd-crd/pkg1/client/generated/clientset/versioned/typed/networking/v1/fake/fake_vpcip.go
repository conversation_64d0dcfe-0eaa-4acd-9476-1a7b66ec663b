/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	networkingv1 "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeVpcIPs implements VpcIPInterface
type FakeVpcIPs struct {
	Fake *FakeNetworkingV1
}

var vpcipsResource = schema.GroupVersionResource{Group: "networking.tke.cloud.tencent.com", Version: "v1", Resource: "vpcips"}

var vpcipsKind = schema.GroupVersionKind{Group: "networking.tke.cloud.tencent.com", Version: "v1", Kind: "VpcIP"}

// Get takes name of the vpcIP, and returns the corresponding vpcIP object, and an error if there is any.
func (c *FakeVpcIPs) Get(name string, options v1.GetOptions) (result *networkingv1.VpcIP, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootGetAction(vpcipsResource, name), &networkingv1.VpcIP{})
	if obj == nil {
		return nil, err
	}
	return obj.(*networkingv1.VpcIP), err
}

// List takes label and field selectors, and returns the list of VpcIPs that match those selectors.
func (c *FakeVpcIPs) List(opts v1.ListOptions) (result *networkingv1.VpcIPList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootListAction(vpcipsResource, vpcipsKind, opts), &networkingv1.VpcIPList{})
	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &networkingv1.VpcIPList{ListMeta: obj.(*networkingv1.VpcIPList).ListMeta}
	for _, item := range obj.(*networkingv1.VpcIPList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested vpcIPs.
func (c *FakeVpcIPs) Watch(opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewRootWatchAction(vpcipsResource, opts))
}

// Create takes the representation of a vpcIP and creates it.  Returns the server's representation of the vpcIP, and an error, if there is any.
func (c *FakeVpcIPs) Create(vpcIP *networkingv1.VpcIP) (result *networkingv1.VpcIP, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootCreateAction(vpcipsResource, vpcIP), &networkingv1.VpcIP{})
	if obj == nil {
		return nil, err
	}
	return obj.(*networkingv1.VpcIP), err
}

// Update takes the representation of a vpcIP and updates it. Returns the server's representation of the vpcIP, and an error, if there is any.
func (c *FakeVpcIPs) Update(vpcIP *networkingv1.VpcIP) (result *networkingv1.VpcIP, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateAction(vpcipsResource, vpcIP), &networkingv1.VpcIP{})
	if obj == nil {
		return nil, err
	}
	return obj.(*networkingv1.VpcIP), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeVpcIPs) UpdateStatus(vpcIP *networkingv1.VpcIP) (*networkingv1.VpcIP, error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateSubresourceAction(vpcipsResource, "status", vpcIP), &networkingv1.VpcIP{})
	if obj == nil {
		return nil, err
	}
	return obj.(*networkingv1.VpcIP), err
}

// Delete takes name of the vpcIP and deletes it. Returns an error if one occurs.
func (c *FakeVpcIPs) Delete(name string, options *v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewRootDeleteAction(vpcipsResource, name), &networkingv1.VpcIP{})
	return err
}

// DeleteCollection deletes a collection of objects.
func (c *FakeVpcIPs) DeleteCollection(options *v1.DeleteOptions, listOptions v1.ListOptions) error {
	action := testing.NewRootDeleteCollectionAction(vpcipsResource, listOptions)

	_, err := c.Fake.Invokes(action, &networkingv1.VpcIPList{})
	return err
}

// Patch applies the patch and returns the patched vpcIP.
func (c *FakeVpcIPs) Patch(name string, pt types.PatchType, data []byte, subresources ...string) (result *networkingv1.VpcIP, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootPatchSubresourceAction(vpcipsResource, name, pt, data, subresources...), &networkingv1.VpcIP{})
	if obj == nil {
		return nil, err
	}
	return obj.(*networkingv1.VpcIP), err
}
