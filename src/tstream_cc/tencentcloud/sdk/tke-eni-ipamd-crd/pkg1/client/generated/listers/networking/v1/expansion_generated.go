/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by lister-gen. DO NOT EDIT.

package v1

// EIPClaimListerExpansion allows custom methods to be added to
// EIPClaimLister.
type EIPClaimListerExpansion interface{}

// EIPClaimNamespaceListerExpansion allows custom methods to be added to
// EIPClaimNamespaceLister.
type EIPClaimNamespaceListerExpansion interface{}

// NodeENIConfigListerExpansion allows custom methods to be added to
// NodeENIConfigLister.
type NodeENIConfigListerExpansion interface{}

// VpcENIListerExpansion allows custom methods to be added to
// VpcENILister.
type VpcENIListerExpansion interface{}

// VpcIPListerExpansion allows custom methods to be added to
// VpcIPLister.
type VpcIPListerExpansion interface{}

// VpcIPClaimListerExpansion allows custom methods to be added to
// VpcIPClaimLister.
type VpcIPClaimListerExpansion interface{}

// VpcIPClaimNamespaceListerExpansion allows custom methods to be added to
// VpcIPClaimNamespaceLister.
type VpcIPClaimNamespaceListerExpansion interface{}
