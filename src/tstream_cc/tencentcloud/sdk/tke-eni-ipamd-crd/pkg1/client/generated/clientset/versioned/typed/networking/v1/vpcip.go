/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package v1

import (
	"context"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	rest "k8s.io/client-go/rest"
	v1 "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1"
	scheme "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/client/generated/clientset/versioned/scheme"
)

// VpcIPsGetter has a method to return a VpcIPInterface.
// A group's client should implement this interface.
type VpcIPsGetter interface {
	VpcIPs() VpcIPInterface
}

// VpcIPInterface has methods to work with VpcIP resources.
type VpcIPInterface interface {
	Create(*v1.VpcIP) (*v1.VpcIP, error)
	Update(*v1.VpcIP) (*v1.VpcIP, error)
	UpdateStatus(*v1.VpcIP) (*v1.VpcIP, error)
	Delete(name string, options *metav1.DeleteOptions) error
	DeleteCollection(options *metav1.DeleteOptions, listOptions metav1.ListOptions) error
	Get(name string, options metav1.GetOptions) (*v1.VpcIP, error)
	List(opts metav1.ListOptions) (*v1.VpcIPList, error)
	Watch(opts metav1.ListOptions) (watch.Interface, error)
	Patch(name string, pt types.PatchType, data []byte, subresources ...string) (result *v1.VpcIP, err error)
	VpcIPExpansion
}

// vpcIPs implements VpcIPInterface
type vpcIPs struct {
	client rest.Interface
}

// newVpcIPs returns a VpcIPs
func newVpcIPs(c *NetworkingV1Client) *vpcIPs {
	return &vpcIPs{
		client: c.RESTClient(),
	}
}

// Get takes name of the vpcIP, and returns the corresponding vpcIP object, and an error if there is any.
func (c *vpcIPs) Get(name string, options metav1.GetOptions) (result *v1.VpcIP, err error) {
	result = &v1.VpcIP{}
	err = c.client.Get().
		Resource("vpcips").
		Name(name).
		VersionedParams(&options, scheme.ParameterCodec).
		Do(context.TODO()).
		Into(result)
	return
}

// List takes label and field selectors, and returns the list of VpcIPs that match those selectors.
func (c *vpcIPs) List(opts metav1.ListOptions) (result *v1.VpcIPList, err error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	result = &v1.VpcIPList{}
	err = c.client.Get().
		Resource("vpcips").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(context.TODO()).
		Into(result)
	return
}

// Watch returns a watch.Interface that watches the requested vpcIPs.
func (c *vpcIPs) Watch(opts metav1.ListOptions) (watch.Interface, error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	opts.Watch = true
	return c.client.Get().
		Resource("vpcips").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Watch(context.TODO())
}

// Create takes the representation of a vpcIP and creates it.  Returns the server's representation of the vpcIP, and an error, if there is any.
func (c *vpcIPs) Create(vpcIP *v1.VpcIP) (result *v1.VpcIP, err error) {
	result = &v1.VpcIP{}
	err = c.client.Post().
		Resource("vpcips").
		Body(vpcIP).
		Do(context.TODO()).
		Into(result)
	return
}

// Update takes the representation of a vpcIP and updates it. Returns the server's representation of the vpcIP, and an error, if there is any.
func (c *vpcIPs) Update(vpcIP *v1.VpcIP) (result *v1.VpcIP, err error) {
	result = &v1.VpcIP{}
	err = c.client.Put().
		Resource("vpcips").
		Name(vpcIP.Name).
		Body(vpcIP).
		Do(context.TODO()).
		Into(result)
	return
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().

func (c *vpcIPs) UpdateStatus(vpcIP *v1.VpcIP) (result *v1.VpcIP, err error) {
	result = &v1.VpcIP{}
	err = c.client.Put().
		Resource("vpcips").
		Name(vpcIP.Name).
		SubResource("status").
		Body(vpcIP).
		Do(context.TODO()).
		Into(result)
	return
}

// Delete takes name of the vpcIP and deletes it. Returns an error if one occurs.
func (c *vpcIPs) Delete(name string, options *metav1.DeleteOptions) error {
	return c.client.Delete().
		Resource("vpcips").
		Name(name).
		Body(options).
		Do(context.TODO()).
		Error()
}

// DeleteCollection deletes a collection of objects.
func (c *vpcIPs) DeleteCollection(options *metav1.DeleteOptions, listOptions metav1.ListOptions) error {
	var timeout time.Duration
	if listOptions.TimeoutSeconds != nil {
		timeout = time.Duration(*listOptions.TimeoutSeconds) * time.Second
	}
	return c.client.Delete().
		Resource("vpcips").
		VersionedParams(&listOptions, scheme.ParameterCodec).
		Timeout(timeout).
		Body(options).
		Do(context.TODO()).
		Error()
}

// Patch applies the patch and returns the patched vpcIP.
func (c *vpcIPs) Patch(name string, pt types.PatchType, data []byte, subresources ...string) (result *v1.VpcIP, err error) {
	result = &v1.VpcIP{}
	err = c.client.Patch(pt).
		Resource("vpcips").
		SubResource(subresources...).
		Name(name).
		Body(data).
		Do(context.TODO()).
		Into(result)
	return
}
