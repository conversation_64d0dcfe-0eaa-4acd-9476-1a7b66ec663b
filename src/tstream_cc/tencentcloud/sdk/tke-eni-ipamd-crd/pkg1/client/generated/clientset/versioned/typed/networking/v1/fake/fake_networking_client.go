/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	v1 "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/client/generated/clientset/versioned/typed/networking/v1"
	rest "k8s.io/client-go/rest"
	testing "k8s.io/client-go/testing"
)

type FakeNetworkingV1 struct {
	*testing.Fake
}

func (c *FakeNetworkingV1) EIPClaims(namespace string) v1.EIPClaimInterface {
	return &FakeEIPClaims{c, namespace}
}

func (c *FakeNetworkingV1) NodeENIConfigs() v1.NodeENIConfigInterface {
	return &FakeNodeENIConfigs{c}
}

func (c *FakeNetworkingV1) VpcENIs() v1.VpcENIInterface {
	return &FakeVpcENIs{c}
}

func (c *FakeNetworkingV1) VpcIPs() v1.VpcIPInterface {
	return &FakeVpcIPs{c}
}

func (c *FakeNetworkingV1) VpcIPClaims(namespace string) v1.VpcIPClaimInterface {
	return &FakeVpcIPClaims{c, namespace}
}

// RESTClient returns a RESTClient that is used to communicate
// with API server by this client implementation.
func (c *FakeNetworkingV1) RESTClient() rest.Interface {
	var ret *rest.RESTClient
	return ret
}
