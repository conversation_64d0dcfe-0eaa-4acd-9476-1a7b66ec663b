/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package v1

import (
	"context"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	rest "k8s.io/client-go/rest"
	v1 "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1"
	scheme "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/client/generated/clientset/versioned/scheme"
)

// VpcIPClaimsGetter has a method to return a VpcIPClaimInterface.
// A group's client should implement this interface.
type VpcIPClaimsGetter interface {
	VpcIPClaims(namespace string) VpcIPClaimInterface
}

// VpcIPClaimInterface has methods to work with VpcIPClaim resources.
type VpcIPClaimInterface interface {
	Create(*v1.VpcIPClaim) (*v1.VpcIPClaim, error)
	Update(*v1.VpcIPClaim) (*v1.VpcIPClaim, error)
	UpdateStatus(*v1.VpcIPClaim) (*v1.VpcIPClaim, error)
	Delete(name string, options *metav1.DeleteOptions) error
	DeleteCollection(options *metav1.DeleteOptions, listOptions metav1.ListOptions) error
	Get(name string, options metav1.GetOptions) (*v1.VpcIPClaim, error)
	List(opts metav1.ListOptions) (*v1.VpcIPClaimList, error)
	Watch(opts metav1.ListOptions) (watch.Interface, error)
	Patch(name string, pt types.PatchType, data []byte, subresources ...string) (result *v1.VpcIPClaim, err error)
	VpcIPClaimExpansion
}

// vpcIPClaims implements VpcIPClaimInterface
type vpcIPClaims struct {
	client rest.Interface
	ns     string
}

// newVpcIPClaims returns a VpcIPClaims
func newVpcIPClaims(c *NetworkingV1Client, namespace string) *vpcIPClaims {
	return &vpcIPClaims{
		client: c.RESTClient(),
		ns:     namespace,
	}
}

// Get takes name of the vpcIPClaim, and returns the corresponding vpcIPClaim object, and an error if there is any.
func (c *vpcIPClaims) Get(name string, options metav1.GetOptions) (result *v1.VpcIPClaim, err error) {
	result = &v1.VpcIPClaim{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("vpcipclaims").
		Name(name).
		VersionedParams(&options, scheme.ParameterCodec).
		Do(context.TODO()).
		Into(result)
	return
}

// List takes label and field selectors, and returns the list of VpcIPClaims that match those selectors.
func (c *vpcIPClaims) List(opts metav1.ListOptions) (result *v1.VpcIPClaimList, err error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	result = &v1.VpcIPClaimList{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("vpcipclaims").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(context.TODO()).
		Into(result)
	return
}

// Watch returns a watch.Interface that watches the requested vpcIPClaims.
func (c *vpcIPClaims) Watch(opts metav1.ListOptions) (watch.Interface, error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	opts.Watch = true
	return c.client.Get().
		Namespace(c.ns).
		Resource("vpcipclaims").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Watch(context.TODO())
}

// Create takes the representation of a vpcIPClaim and creates it.  Returns the server's representation of the vpcIPClaim, and an error, if there is any.
func (c *vpcIPClaims) Create(vpcIPClaim *v1.VpcIPClaim) (result *v1.VpcIPClaim, err error) {
	result = &v1.VpcIPClaim{}
	err = c.client.Post().
		Namespace(c.ns).
		Resource("vpcipclaims").
		Body(vpcIPClaim).
		Do(context.TODO()).
		Into(result)
	return
}

// Update takes the representation of a vpcIPClaim and updates it. Returns the server's representation of the vpcIPClaim, and an error, if there is any.
func (c *vpcIPClaims) Update(vpcIPClaim *v1.VpcIPClaim) (result *v1.VpcIPClaim, err error) {
	result = &v1.VpcIPClaim{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("vpcipclaims").
		Name(vpcIPClaim.Name).
		Body(vpcIPClaim).
		Do(context.TODO()).
		Into(result)
	return
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().

func (c *vpcIPClaims) UpdateStatus(vpcIPClaim *v1.VpcIPClaim) (result *v1.VpcIPClaim, err error) {
	result = &v1.VpcIPClaim{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("vpcipclaims").
		Name(vpcIPClaim.Name).
		SubResource("status").
		Body(vpcIPClaim).
		Do(context.TODO()).
		Into(result)
	return
}

// Delete takes name of the vpcIPClaim and deletes it. Returns an error if one occurs.
func (c *vpcIPClaims) Delete(name string, options *metav1.DeleteOptions) error {
	return c.client.Delete().
		Namespace(c.ns).
		Resource("vpcipclaims").
		Name(name).
		Body(options).
		Do(context.TODO()).
		Error()
}

// DeleteCollection deletes a collection of objects.
func (c *vpcIPClaims) DeleteCollection(options *metav1.DeleteOptions, listOptions metav1.ListOptions) error {
	var timeout time.Duration
	if listOptions.TimeoutSeconds != nil {
		timeout = time.Duration(*listOptions.TimeoutSeconds) * time.Second
	}
	return c.client.Delete().
		Namespace(c.ns).
		Resource("vpcipclaims").
		VersionedParams(&listOptions, scheme.ParameterCodec).
		Timeout(timeout).
		Body(options).
		Do(context.TODO()).
		Error()
}

// Patch applies the patch and returns the patched vpcIPClaim.
func (c *vpcIPClaims) Patch(name string, pt types.PatchType, data []byte, subresources ...string) (result *v1.VpcIPClaim, err error) {
	result = &v1.VpcIPClaim{}
	err = c.client.Patch(pt).
		Namespace(c.ns).
		Resource("vpcipclaims").
		SubResource(subresources...).
		Name(name).
		Body(data).
		Do(context.TODO()).
		Into(result)
	return
}
