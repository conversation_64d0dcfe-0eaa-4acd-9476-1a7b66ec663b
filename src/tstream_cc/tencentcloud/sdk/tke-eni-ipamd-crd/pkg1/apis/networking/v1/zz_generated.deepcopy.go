// +build !ignore_autogenerated

/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by deepcopy-gen. DO NOT EDIT.

package v1

import (
	corev1 "k8s.io/api/core/v1"
	runtime "k8s.io/apimachinery/pkg/runtime"
)

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *CrossTenantSpec) DeepCopyInto(out *CrossTenantSpec) {
	*out = *in
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CrossTenantSpec.
func (in *CrossTenantSpec) DeepCopy() *CrossTenantSpec {
	if in == nil {
		return nil
	}
	out := new(CrossTenantSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EIPAttachment) DeepCopyInto(out *EIPAttachment) {
	*out = *in
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EIPAttachment.
func (in *EIPAttachment) DeepCopy() *EIPAttachment {
	if in == nil {
		return nil
	}
	out := new(EIPAttachment)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EIPClaim) DeepCopyInto(out *EIPClaim) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	out.Spec = in.Spec
	in.Status.DeepCopyInto(&out.Status)
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EIPClaim.
func (in *EIPClaim) DeepCopy() *EIPClaim {
	if in == nil {
		return nil
	}
	out := new(EIPClaim)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *EIPClaim) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EIPClaimList) DeepCopyInto(out *EIPClaimList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	out.ListMeta = in.ListMeta
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]EIPClaim, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EIPClaimList.
func (in *EIPClaimList) DeepCopy() *EIPClaimList {
	if in == nil {
		return nil
	}
	out := new(EIPClaimList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *EIPClaimList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EIPClaimSpec) DeepCopyInto(out *EIPClaimSpec) {
	*out = *in
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EIPClaimSpec.
func (in *EIPClaimSpec) DeepCopy() *EIPClaimSpec {
	if in == nil {
		return nil
	}
	out := new(EIPClaimSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EIPClaimStatus) DeepCopyInto(out *EIPClaimStatus) {
	*out = *in
	if in.Attachment != nil {
		in, out := &in.Attachment, &out.Attachment
		*out = new(EIPAttachment)
		**out = **in
	}
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]EIPCondition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EIPClaimStatus.
func (in *EIPClaimStatus) DeepCopy() *EIPClaimStatus {
	if in == nil {
		return nil
	}
	out := new(EIPClaimStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EIPCondition) DeepCopyInto(out *EIPCondition) {
	*out = *in
	in.LastProbeTime.DeepCopyInto(&out.LastProbeTime)
	in.LastTransitionTime.DeepCopyInto(&out.LastTransitionTime)
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EIPCondition.
func (in *EIPCondition) DeepCopy() *EIPCondition {
	if in == nil {
		return nil
	}
	out := new(EIPCondition)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ENIInfo) DeepCopyInto(out *ENIInfo) {
	*out = *in
	if in.SecondaryIPs != nil {
		in, out := &in.SecondaryIPs, &out.SecondaryIPs
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.SecurityGroups != nil {
		in, out := &in.SecurityGroups, &out.SecurityGroups
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ENIInfo.
func (in *ENIInfo) DeepCopy() *ENIInfo {
	if in == nil {
		return nil
	}
	out := new(ENIInfo)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *NodeENIConfig) DeepCopyInto(out *NodeENIConfig) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new NodeENIConfig.
func (in *NodeENIConfig) DeepCopy() *NodeENIConfig {
	if in == nil {
		return nil
	}
	out := new(NodeENIConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *NodeENIConfig) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *NodeENIConfigCondition) DeepCopyInto(out *NodeENIConfigCondition) {
	*out = *in
	in.LastProbeTime.DeepCopyInto(&out.LastProbeTime)
	in.LastTransitionTime.DeepCopyInto(&out.LastTransitionTime)
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new NodeENIConfigCondition.
func (in *NodeENIConfigCondition) DeepCopy() *NodeENIConfigCondition {
	if in == nil {
		return nil
	}
	out := new(NodeENIConfigCondition)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *NodeENIConfigList) DeepCopyInto(out *NodeENIConfigList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	out.ListMeta = in.ListMeta
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]NodeENIConfig, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new NodeENIConfigList.
func (in *NodeENIConfigList) DeepCopy() *NodeENIConfigList {
	if in == nil {
		return nil
	}
	out := new(NodeENIConfigList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *NodeENIConfigList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *NodeENIConfigSpec) DeepCopyInto(out *NodeENIConfigSpec) {
	*out = *in
	if in.MaxENI != nil {
		in, out := &in.MaxENI, &out.MaxENI
		*out = new(int32)
		**out = **in
	}
	if in.MaxIPPerENI != nil {
		in, out := &in.MaxIPPerENI, &out.MaxIPPerENI
		*out = new(int32)
		**out = **in
	}
	if in.MaxEIP != nil {
		in, out := &in.MaxEIP, &out.MaxEIP
		*out = new(int32)
		**out = **in
	}
	if in.MaxDirectENI != nil {
		in, out := &in.MaxDirectENI, &out.MaxDirectENI
		*out = new(int32)
		**out = **in
	}
	if in.DesiredDirectENI != nil {
		in, out := &in.DesiredDirectENI, &out.DesiredDirectENI
		*out = new(int32)
		**out = **in
	}
	if in.SecurityGroups != nil {
		in, out := &in.SecurityGroups, &out.SecurityGroups
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.MaxRouteENI != nil {
		in, out := &in.MaxRouteENI, &out.MaxRouteENI
		*out = new(int32)
		**out = **in
	}
	if in.DesiredRouteENI != nil {
		in, out := &in.DesiredRouteENI, &out.DesiredRouteENI
		*out = new(int32)
		**out = **in
	}
	if in.DesiredRouteENIIP != nil {
		in, out := &in.DesiredRouteENIIP, &out.DesiredRouteENIIP
		*out = new(int32)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new NodeENIConfigSpec.
func (in *NodeENIConfigSpec) DeepCopy() *NodeENIConfigSpec {
	if in == nil {
		return nil
	}
	out := new(NodeENIConfigSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *NodeENIConfigStatus) DeepCopyInto(out *NodeENIConfigStatus) {
	*out = *in
	if in.ENIInfos != nil {
		in, out := &in.ENIInfos, &out.ENIInfos
		*out = make([]ENIInfo, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.DynamicReserv != nil {
		in, out := &in.DynamicReserv, &out.DynamicReserv
		*out = new(int32)
		**out = **in
	}
	if in.PodCIDRs != nil {
		in, out := &in.PodCIDRs, &out.PodCIDRs
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]NodeENIConfigCondition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new NodeENIConfigStatus.
func (in *NodeENIConfigStatus) DeepCopy() *NodeENIConfigStatus {
	if in == nil {
		return nil
	}
	out := new(NodeENIConfigStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *VpcENI) DeepCopyInto(out *VpcENI) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new VpcENI.
func (in *VpcENI) DeepCopy() *VpcENI {
	if in == nil {
		return nil
	}
	out := new(VpcENI)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *VpcENI) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *VpcENICondition) DeepCopyInto(out *VpcENICondition) {
	*out = *in
	in.LastProbeTime.DeepCopyInto(&out.LastProbeTime)
	in.LastTransitionTime.DeepCopyInto(&out.LastTransitionTime)
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new VpcENICondition.
func (in *VpcENICondition) DeepCopy() *VpcENICondition {
	if in == nil {
		return nil
	}
	out := new(VpcENICondition)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *VpcENIList) DeepCopyInto(out *VpcENIList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	out.ListMeta = in.ListMeta
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]VpcENI, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new VpcENIList.
func (in *VpcENIList) DeepCopy() *VpcENIList {
	if in == nil {
		return nil
	}
	out := new(VpcENIList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *VpcENIList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *VpcENISpec) DeepCopyInto(out *VpcENISpec) {
	*out = *in
	if in.MaxSecondaryIP != nil {
		in, out := &in.MaxSecondaryIP, &out.MaxSecondaryIP
		*out = new(int32)
		**out = **in
	}
	if in.ClaimRef != nil {
		in, out := &in.ClaimRef, &out.ClaimRef
		*out = new(corev1.ObjectReference)
		**out = **in
	}
	out.CrossTenant = in.CrossTenant
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new VpcENISpec.
func (in *VpcENISpec) DeepCopy() *VpcENISpec {
	if in == nil {
		return nil
	}
	out := new(VpcENISpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *VpcENIStatus) DeepCopyInto(out *VpcENIStatus) {
	*out = *in
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]VpcENICondition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.ENI != nil {
		in, out := &in.ENI, &out.ENI
		*out = new(ENIInfo)
		(*in).DeepCopyInto(*out)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new VpcENIStatus.
func (in *VpcENIStatus) DeepCopy() *VpcENIStatus {
	if in == nil {
		return nil
	}
	out := new(VpcENIStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *VpcIP) DeepCopyInto(out *VpcIP) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new VpcIP.
func (in *VpcIP) DeepCopy() *VpcIP {
	if in == nil {
		return nil
	}
	out := new(VpcIP)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *VpcIP) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *VpcIPClaim) DeepCopyInto(out *VpcIPClaim) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	out.Status = in.Status
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new VpcIPClaim.
func (in *VpcIPClaim) DeepCopy() *VpcIPClaim {
	if in == nil {
		return nil
	}
	out := new(VpcIPClaim)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *VpcIPClaim) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *VpcIPClaimList) DeepCopyInto(out *VpcIPClaimList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	out.ListMeta = in.ListMeta
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]VpcIPClaim, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new VpcIPClaimList.
func (in *VpcIPClaimList) DeepCopy() *VpcIPClaimList {
	if in == nil {
		return nil
	}
	out := new(VpcIPClaimList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *VpcIPClaimList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *VpcIPClaimSpec) DeepCopyInto(out *VpcIPClaimSpec) {
	*out = *in
	if in.Zone != nil {
		in, out := &in.Zone, &out.Zone
		*out = new(string)
		**out = **in
	}
	if in.CNIType != nil {
		in, out := &in.CNIType, &out.CNIType
		*out = new(VpcIPCNIType)
		**out = **in
	}
	if in.SecurityGroups != nil {
		in, out := &in.SecurityGroups, &out.SecurityGroups
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	out.CrossTenant = in.CrossTenant
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new VpcIPClaimSpec.
func (in *VpcIPClaimSpec) DeepCopy() *VpcIPClaimSpec {
	if in == nil {
		return nil
	}
	out := new(VpcIPClaimSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *VpcIPClaimStatus) DeepCopyInto(out *VpcIPClaimStatus) {
	*out = *in
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new VpcIPClaimStatus.
func (in *VpcIPClaimStatus) DeepCopy() *VpcIPClaimStatus {
	if in == nil {
		return nil
	}
	out := new(VpcIPClaimStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *VpcIPCondition) DeepCopyInto(out *VpcIPCondition) {
	*out = *in
	in.LastProbeTime.DeepCopyInto(&out.LastProbeTime)
	in.LastTransitionTime.DeepCopyInto(&out.LastTransitionTime)
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new VpcIPCondition.
func (in *VpcIPCondition) DeepCopy() *VpcIPCondition {
	if in == nil {
		return nil
	}
	out := new(VpcIPCondition)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *VpcIPList) DeepCopyInto(out *VpcIPList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	out.ListMeta = in.ListMeta
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]VpcIP, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new VpcIPList.
func (in *VpcIPList) DeepCopy() *VpcIPList {
	if in == nil {
		return nil
	}
	out := new(VpcIPList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *VpcIPList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *VpcIPSpec) DeepCopyInto(out *VpcIPSpec) {
	*out = *in
	if in.ClaimRef != nil {
		in, out := &in.ClaimRef, &out.ClaimRef
		*out = new(corev1.ObjectReference)
		**out = **in
	}
	if in.NECRef != nil {
		in, out := &in.NECRef, &out.NECRef
		*out = new(corev1.ObjectReference)
		**out = **in
	}
	if in.CNIType != nil {
		in, out := &in.CNIType, &out.CNIType
		*out = new(VpcIPCNIType)
		**out = **in
	}
	if in.DirectENISecurityGroups != nil {
		in, out := &in.DirectENISecurityGroups, &out.DirectENISecurityGroups
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.SecondaryNECRef != nil {
		in, out := &in.SecondaryNECRef, &out.SecondaryNECRef
		*out = new(corev1.ObjectReference)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new VpcIPSpec.
func (in *VpcIPSpec) DeepCopy() *VpcIPSpec {
	if in == nil {
		return nil
	}
	out := new(VpcIPSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *VpcIPStatus) DeepCopyInto(out *VpcIPStatus) {
	*out = *in
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]VpcIPCondition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.DirectENI != nil {
		in, out := &in.DirectENI, &out.DirectENI
		*out = new(ENIInfo)
		(*in).DeepCopyInto(*out)
	}
	if in.RouteENIID != nil {
		in, out := &in.RouteENIID, &out.RouteENIID
		*out = new(string)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new VpcIPStatus.
func (in *VpcIPStatus) DeepCopy() *VpcIPStatus {
	if in == nil {
		return nil
	}
	out := new(VpcIPStatus)
	in.DeepCopyInto(out)
	return out
}
