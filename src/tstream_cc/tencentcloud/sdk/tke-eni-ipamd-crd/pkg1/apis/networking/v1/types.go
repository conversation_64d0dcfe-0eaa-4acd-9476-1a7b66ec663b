/*
Copyright 2018 The TKE Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1

import (
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

type AllocPolicyType string

const (
	CentralAllocPolicy    AllocPolicyType = "central"
	DistributeAllocPolicy AllocPolicyType = "distribute"
)

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// NodeENIConfigList is a list of NodeENIConfig resources
type NodeENIConfigList struct {
	metav1.TypeMeta `json:",inline"`
	// Standard list metadata.
	// More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
	// +optional
	metav1.ListMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// Items is the list of NodeENIConfig objects in the list.
	Items []NodeENIConfig `json:"items" protobuf:"bytes,2,rep,name=items"`
}

const (
	AnnMaxSecondaryIP    = "tke.cloud.tencent.com/max-secondary-ip"
	FinalizerNEC         = "tke.cloud.tencent.com/nec"
	AnnDENIMaxAttach     = "tke.cloud.tencent.com/direct-eni-max-attach"
	AnnDENIMinWarmTarget = "tke.cloud.tencent.com/direct-eni-min-warm-target"
	AnnDENIMaxWarmTarget = "tke.cloud.tencent.com/direct-eni-max-warm-target"
	AnnRENIMaxAttach     = "tke.cloud.tencent.com/route-eni-max-attach"
	AnnRENIIPMaxAttach   = "tke.cloud.tencent.com/max-ip-per-route-eni"
	AnnRENIIPMinAttach   = "tke.cloud.tencent.com/min-ip-per-route-eni"
	AnnRENIMinWarmTarget = "tke.cloud.tencent.com/route-eni-ip-min-warm-target"
	AnnRENIMaxWarmTarget = "tke.cloud.tencent.com/route-eni-ip-max-warm-target"
	AnnRENINameSuffix    = "tke.cloud.tencent.com/next-route-eni-name-suffix"
)

// +genclient
// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// NodeENIConfig is a specification for a NodeENIConfig resource
// +k8s:openapi-gen=true
type NodeENIConfig struct {
	metav1.TypeMeta `json:",inline"`
	// Standard object's metadata.
	// More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
	// +optional
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// Specification of the desired behavior of the nodeENIConfig.
	// More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
	// +optional
	Spec NodeENIConfigSpec `json:"spec,omitempty" protobuf:"bytes,2,opt,name=spec"`

	// Most recently observed status of the nodeENIConfig.
	// This data may not be up to date.
	// Populated by the system.
	// Read-only.
	// More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
	// +optional
	Status NodeENIConfigStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
}

// NodeENIConfigSpec is a description for a NodeENIConfig
// +k8s:openapi-gen=true
type NodeENIConfigSpec struct {
	// zone of the node
	Zone string `json:"zone" protobuf:"bytes,1,opt,name=zone"`

	// ID of the node assigned by ENI provider
	// +optional
	ProviderID string `json:"providerID,omitempty" protobuf:"bytes,2,opt,name=providerID"`

	// max ENIs the node can attach
	// +optional
	MaxENI *int32 `json:"maxENI,omitempty" protobuf:"varint,3,opt,name=maxENI"`

	// max IP per eni can assign
	// +optional
	MaxIPPerENI *int32 `json:"maxIPPerENI,omitempty" protobuf:"varint,4,opt,name=maxIPPerENI"`

	// max EIPs the node can associate
	// +optional
	MaxEIP *int32 `json:"maxEIP,omitempty" protobuf:"varint,5,opt,name=maxEIP"`

	// whether disable route eni of the node
	// +optional
	DisableRouteENI bool `json:"disableRouteENI,omitempty" protobuf:"varint,6,opt,name=disableRouteENI"`

	// max direct ENIS the node can attach
	// +optional
	MaxDirectENI *int32 `json:"maxDirectENI,omitempty" protobuf:"varint,7,opt,name=maxDirectENI"`

	// number of enis needs to attach to the node
	// +optional
	DesiredDirectENI *int32 `json:"desiredDirectENI,omitempty" protobuf:"varint,8,opt,name=desiredDirectENI"`

	// SecurityGroups is the security groups expected to be associated with the node enis
	// +optional
	// +listType=atomic
	SecurityGroups []string `json:"securityGroups,omitempty" protobuf:"bytes,9,opt,name=securityGroups"`

	// max route ENIS the node can attach
	// +optional
	MaxRouteENI *int32 `json:"maxRouteENI,omitempty" protobuf:"varint,10,opt,name=maxRouteENI"`

	// number of route enis needs to attach to the node
	// +optional
	DesiredRouteENI *int32 `json:"desiredRouteENI,omitempty" protobuf:"varint,11,opt,name=desiredRouteENI"`

	// number of eni ip needs to attach to the node
	// +optional
	DesiredRouteENIIP *int32 `json:"desiredRouteENIIP,omitempty" protobuf:"varint,12,opt,name=desiredRouteENIIP"`
}

type ENIInfo struct {
	// ENI ID
	ENIID string `json:"eniID" protobuf:"bytes,1,opt,name=eniID"`

	// ENI mac address
	MAC string `json:"mac" protobuf:"bytes,2,opt,name=mac"`

	// ENI primary ip
	PrimaryIP string `json:"primaryIP" protobuf:"bytes,3,opt,name=primaryIP"`

	// Subnet CIDR of the eni
	SubnetCIDR string `json:"subnetCIDR" protobuf:"bytes,4,opt,name=subnetCIDR"`

	// Primary eni of the node
	// +optional
	Primary bool `json:"primary,omitempty" protobuf:"varint,5,opt,name=primary"`

	// SecondaryIPs assigned by the eni
	// +optional
	// +listType=atomic
	SecondaryIPs []string `json:"secondaryIPs,omitempty" protobuf:"bytes,6,opt,name=secondaryIPs"`

	// SecurityGroups is the security groups associated with the node enis
	// +optional
	// +listType=atomic
	SecurityGroups []string `json:"securityGroups,omitempty" protobuf:"bytes,7,opt,name=securityGroups"`
}

// +k8s:openapi-gen=true
type NodeENIConfigStatus struct {
	// ENIs is a list of enis attached by the node
	// +optional
	// +listType=atmoic
	ENIInfos []ENIInfo `json:"eniInfos,omitempty" protobuf:"bytes,1,opt,name=eniInfos"`
	// number of dynamic reservation ip resource for scheduler pod to node
	// +optional
	DynamicReserv *int32 `json:"dynamicReserv,omitempty" protobuf:"varint,2,opt,name=dynamicReserv"`
	// list of podCIDR allocated to this node
	// +optional
	// +listType=atomic
	PodCIDRs []string `json:"podCIDRs,omitempty" protobuf:"bytes,3,opt,name=podCIDRs"`
	// Current Condition of nec.
	// +optional
	// +listType=map
	// +listMapKey=type
	Conditions []NodeENIConfigCondition `json:"conditions,omitempty" protobuf:"bytes,4,rep,name=conditions"`
}

// NodeENIConfigConditionType is a valid value of NodeENIConfigCondition.Type
type NodeENIConfigConditionType string

const (
	// ConditionNECAllocatedIPs - the vpc eni is created
	ConditionNECENIIPAllocated NodeENIConfigConditionType = "RouteENIIPsAllocated"
	// ConditionNECRouteENIAttacheds - the vpc eni is deleted
	ConditionNECRouteENIAttached NodeENIConfigConditionType = "RouteENIAttached"
)

// VpcENICondition contails details about state of vpceni
type NodeENIConfigCondition struct {
	Type   NodeENIConfigConditionType `json:"type" protobuf:"bytes,1,opt,name=type,casttype=PersistentVolumeClaimConditionType"`
	Status corev1.ConditionStatus     `json:"status" protobuf:"bytes,2,opt,name=status,casttype=ConditionStatus"`
	// Last time we probed the condition.
	// +optional
	LastProbeTime metav1.Time `json:"lastProbeTime,omitempty" protobuf:"bytes,3,opt,name=lastProbeTime"`
	// Last time the condition transitioned from one status to another.
	// +optional
	LastTransitionTime metav1.Time `json:"lastTransitionTime,omitempty" protobuf:"bytes,4,opt,name=lastTransitionTime"`
	// Unique, one-word, CamelCase reason for the condition's last transition.
	// +optional
	Reason string `json:"reason,omitempty" protobuf:"bytes,5,opt,name=reason"`
	// Human-readable message indicating details about last transition.
	// +optional
	Message string `json:"message,omitempty" protobuf:"bytes,6,opt,name=message"`
	// The number of attempts for this condition.
	// +optional
	Attempts int32 `json:"attempts,omitempty" protobuf:"varint,7,opt,name=attempts"`
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// VpcENIList is a list of VpcENI resources
type VpcENIList struct {
	metav1.TypeMeta `json:",inline"`
	// Standard list metadata.
	// More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
	// +optional
	metav1.ListMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// Items is the list of VpcENI objects in the list.
	Items []VpcENI `json:"items" protobuf:"bytes,2,rep,name=items"`
}

const (
	FinalizerVpcENI       = "tke.cloud.tencent.com/final-vpceni"
	ENIModeDirectENI      = "tke-direct-eni"
	ENIModeRouteENI       = "tke-route-eni"
	ENIModeCrossTenantENI = "cross-tenant-eni"
)

// +genclient
// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// VpcENI is a specification for a VpcENI resource
// +k8s:openapi-gen=true
type VpcENI struct {
	metav1.TypeMeta `json:",inline"`
	// Standard object's metadata.
	// More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
	// +optional
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// Specification of the desired behavior of the nodeENIConfig.
	// More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
	// +optional
	Spec VpcENISpec `json:"spec,omitempty" protobuf:"bytes,2,opt,name=spec"`

	// Most recently observed status of the nodeENIConfig.
	// This data may not be up to date.
	// Populated by the system.
	// Read-only.
	// More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
	// +optional
	Status VpcENIStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
}

// VpcENISpec is a description for a VpcENI
// +k8s:openapi-gen=true
type VpcENISpec struct {
	// zone of the eni
	Zone string `json:"zone" protobuf:"bytes,1,opt,name=zone"`

	// ID of the attached node assigned by ENI provider
	// +optional
	ProviderID string `json:"providerID,omitempty" protobuf:"bytes,2,opt,name=providerID"`

	// max secondary IPs the eni can attach
	// +optional
	MaxSecondaryIP *int32 `json:"maxSecondaryIP,omitempty" protobuf:"varint,3,opt,name=maxSecondaryIP"`

	// Mode of the eni: route-eni, direct-eni, cross
	// +optional
	Mode string `json:"mode,omitempty" protobuf:"bytes,4,opt,name=mode"`

	// VpcENI of the claim
	// +optional
	ClaimRef *corev1.ObjectReference `json:"claimRef,omitempty" protobuf:"bytes,5,opt,name=claimRef"`

	// CrossTenant cross tenant param that identify the tenant account
	// +optional
	CrossTenant CrossTenantSpec `json:"crossTenant,omitempty" protobuf:"bytes,6,opt,name=crossTenant"`
}

// +k8s:openapi-gen=true
type VpcENIStatus struct {
	// Phase represents the current phase of VpcENI.
	// +optional
	Phase VpcENIPhase `json:"phase,omitempty" protobuf:"bytes,1,opt,name=phase"`

	// Current Condition of vpc ip.
	// +optional
	// +listType=map
	// +listMapKey=type
	Conditions []VpcENICondition `json:"conditions,omitempty" protobuf:"bytes,2,rep,name=conditions"`

	// information of eni attached to the node
	// +optional
	ENI *ENIInfo `json:"eni,omitempty" protobuf:"bytes,3,opt,name=eni"`
}

type VpcENIPhase string

const (
	// used for VpcENI being created.
	VpcENICreating VpcENIPhase = "Creating"
	// used for VpcENI being deleted.
	VpcENIDeleting VpcENIPhase = "Deleting"
	// used for VpcENI that is deleted.
	VpcENIDeleted VpcENIPhase = "Deleted"
	// used for VpcENI that are not available.
	VpcENIPending VpcENIPhase = "Pending"
	// used for VpcENI related eni is being Attached.
	// used for eni being Attached.
	VpcENIAttaching VpcENIPhase = "Attaching"
	// used for eni that successfully attached to node.
	VpcENIAttached VpcENIPhase = "Attached"
	// used for eni being unAttaching.
	VpcENIUnAttaching VpcENIPhase = "UnAttaching"
	// used for eni that is successfully unAttached.
	VpcENIUnAttached VpcENIPhase = "UnAttached"
)

// VpcENIConditionType is a valid value of VpcENICondition.Type
type VpcENIConditionType string

const (
	// ConditionVpcENICreated - the vpc eni is created
	ConditionVpcENICreated VpcENIConditionType = "VpcENICreated"
	// ConditionVpcENIDeleted - the vpc eni is deleted
	ConditionVpcENIDeleted VpcENIConditionType = "VpcENIDeleted"
	// ConditionVpcENIAttached - the eni is attached
	ConditionVpcENIAttached VpcENIConditionType = "VpcENIAttached"
	// ConditionVpcENIUnAttached - the eni is unAttached
	ConditionVpcENIUnAttached VpcENIConditionType = "VpcENIUnAttached"
)

// VpcENICondition contails details about state of vpceni
type VpcENICondition struct {
	Type   VpcENIConditionType    `json:"type" protobuf:"bytes,1,opt,name=type,casttype=PersistentVolumeClaimConditionType"`
	Status corev1.ConditionStatus `json:"status" protobuf:"bytes,2,opt,name=status,casttype=ConditionStatus"`
	// Last time we probed the condition.
	// +optional
	LastProbeTime metav1.Time `json:"lastProbeTime,omitempty" protobuf:"bytes,3,opt,name=lastProbeTime"`
	// Last time the condition transitioned from one status to another.
	// +optional
	LastTransitionTime metav1.Time `json:"lastTransitionTime,omitempty" protobuf:"bytes,4,opt,name=lastTransitionTime"`
	// Unique, one-word, CamelCase reason for the condition's last transition.
	// +optional
	Reason string `json:"reason,omitempty" protobuf:"bytes,5,opt,name=reason"`
	// Human-readable message indicating details about last transition.
	// +optional
	Message string `json:"message,omitempty" protobuf:"bytes,6,opt,name=message"`
	// The number of attempts for this condition.
	// +optional
	Attempts int32 `json:"attempts,omitempty" protobuf:"varint,7,opt,name=attempts"`
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// VpcIPClaimList is a list of VpcIPClaim resources
type VpcIPClaimList struct {
	metav1.TypeMeta `json:",inline"`
	// Standard list metadata.
	// More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
	// +optional
	metav1.ListMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// Items is the list of VpcIPClaim objects in the list.
	Items []VpcIPClaim `json:"items" protobuf:"bytes,2,rep,name=items"`
}

const (
	AnnNominatedVpcIP     = "tke.cloud.tencent.com/nominated-vpc-ip"
	AnnNominatedENISubnet = "tke.cloud.tencent.com/nominated-eni-subnet"
)

// +genclient
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// VpcIPClaim is a specification for a VpcIPClaim resource
// +k8s:openapi-gen=true
type VpcIPClaim struct {
	metav1.TypeMeta `json:",inline"`
	// Standard object's metadata.
	// More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
	// +optional
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// Specification of the desired behavior of the vpcIPClaim.
	// More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
	// +optional
	Spec VpcIPClaimSpec `json:"spec,omitempty" protobuf:"bytes,2,opt,name=spec"`

	// Most recently observed status of the vpcIPClaim.
	// This data may not be up to date.
	// Populated by the system.
	// Read-only.
	// More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
	// +optional
	Status VpcIPClaimStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
}

// VpcIPClaimSpec is a description for a VpcIPClaim
// +k8s:openapi-gen=true
type VpcIPClaimSpec struct {
	// VpcIPClaimDeletePolicy
	VpcIPClaimDeletePolicy VpcIPClaimDeletePolicy `json:"vpcIPClaimDeletePolicy" protobuf:"bytes,1,opt,name=vpcIPClaimDeletePolicy"`

	// Subnet CIDR of the vpcIP
	SubnetCIDR string `json:"subnetCIDR" protobuf:"bytes,2,opt,name=subnetCIDR"`

	// vpcIP of the claim
	// +optional
	VpcIP string `json:"vpcIP,omitempty" protobuf:"bytes,3,opt,name=vpcIP"`

	// UID of the pod
	// +optional
	PodUID string `json:"podUID,omitempty" protobuf:"bytes,4,opt,name=podUID"`

	// Zone of the vpcIP
	// +optional
	Zone *string `json:"zone,omitempty" protobuf:"bytes,5,opt,name=zone"`

	// CNIType of the vpcip, TKEDirectENI or TKERouteENI, default is TKERouteENI
	// +optional
	CNIType *VpcIPCNIType `json:"cniType,omitempty" protobuf:"bytes,6,opt,name=cniType"`

	// SecurityGroup represents the securitygroup of vpcipclaim
	// +optional
	// +listType=atomic
	SecurityGroups []string `json:"securityGroups,omitempty" protobuf:"bytes,7,opt,name=securityGroups"`

	// CrossTenant cross tenant param that identify the tenant account
	// +optional
	CrossTenant CrossTenantSpec `json:"crossTenant,omitempty" protobuf:"bytes,8,opt,name=crossTenant"`

	// vpcENI of the claim
	// +optional
	VpcENI string `json:"vpcENI,omitempty" protobuf:"bytes,9,opt,name=vpcENI"`
}

// CrossTenantSpec is a description for a cross tenant eni param
// +k8s:openapi-gen=true
type CrossTenantSpec struct {
	// AppId peerAppId
	// +optional
	AppId uint64 `json:"appId,omitempty" protobuf:"varint,1,opt,name=appId"`

	// Uin peerUin
	// +optional
	Uin string `json:"uin,omitempty" protobuf:"bytes,2,opt,name=uin"`

	// uniqVpcId peer uniqVpcId
	// +optional
	UniqVpcId string `json:"uniqVpcId,omitempty" protobuf:"bytes,3,opt,name=uniqVpcId"`

	// SubnetId, peer subnetId, only if wan to use user's real ip
	// +optional
	SubnetId string `json:"subnetId,omitempty" protobuf:"bytes,4,opt,name=subnetId"`

	// ENILimit cross tenant eni num limit on one node
	// +optional
	ENILimit int `json:"eniLimit,omitempty" protobuf:"varint,5,opt,name=eniLimit"`
}

// VpcIPClaimDeletePolicy describes a policy for end-of-life maintenance of the pod which the claim cares.
type VpcIPClaimDeletePolicy string

const (
	// VpcIPClaimDeleteImmediate means the claim will be delete immediate on release from its pod.
	VpcIPClaimDeleteImmediate VpcIPClaimDeletePolicy = "Immediate"
	// VpcIPClaimDeleteNever means the claim never be deleted on release from its pod.
	VpcIPClaimDeleteNever VpcIPClaimDeletePolicy = "Never"
)

// VpcIPCNIType describes the cni type of a vpc-ip or vpc-ip-claim
type VpcIPCNIType string

const (
	// TKECrossTenantENI means the claim or vpc-ip will use tke-direct-eni to create network
	TKECrossTenantENI VpcIPCNIType = "tke-crosstenant-eni"
	// TKEDirectENI means the claim or vpc-ip will use tke-direct-eni to create network
	TKEDirectENI VpcIPCNIType = "tke-direct-eni"
	// TKERouteENI means the claim or vpc-ip will use tke-route-eni to create network
	TKERouteENI VpcIPCNIType = "tke-route-eni"
)

// +k8s:openapi-gen=true
type VpcIPClaimStatus struct {
	// Phase represents the current phase of VpcIPClaim.
	// +optional
	Phase VpcIPClaimPhase `json:"phase,omitempty" protobuf:"bytes,1,opt,name=phase"`
}

type VpcIPClaimPhase string

const (
	// used for VpcIPClaims that are not bound
	ClaimPending VpcIPClaimPhase = "Pending"
	// used for VpcIPClaims that are bound
	ClaimBound VpcIPClaimPhase = "Bound"
	// used for VpcIPClaims that lost their underlying Pod.
	ClaimLost VpcIPClaimPhase = "Lost"
)

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// VpcIPList is a list of VpcIP resources
type VpcIPList struct {
	metav1.TypeMeta `json:",inline"`
	// Standard list metadata.
	// More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
	// +optional
	metav1.ListMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// Items is the list of VpcIP objects in the list.
	Items []VpcIP `json:"items" protobuf:"bytes,2,rep,name=items"`
}

const (
	FinalizerVpcIP           = "tke.cloud.tencent.com/vpcip"
	LabelCreatedByIpamd      = "tke.cloud.tencent.com/created-by-ipamd"
	LabelNodeName            = "tke.cloud.tencent.com/node-name"
	LabelENIID               = "tke.cloud.tencent.com/eni-id"
	LabelPodName             = "tke.cloud.tencent.com/pod-name"
	AnnAssignedByIpamdBefore = "tke.cloud.tencent.com/assigned-by-ipamd-before"
	LabelPrimaryVpcIP        = "tke.cloud.tencent.com/primary-vpc-ip"
	VpcIPTypeNode            = "Node"
	VpcIPTypePod             = "Pod"
)

// +genclient
// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// VpcIP is a specification for a VpcIP resource
// +k8s:openapi-gen=true
type VpcIP struct {
	metav1.TypeMeta `json:",inline"`
	// Standard object's metadata.
	// More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
	// +optional
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// Specification of the desired behavior of the vpcIP.
	// More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
	// +optional
	Spec VpcIPSpec `json:"spec,omitempty" protobuf:"bytes,2,opt,name=spec"`

	// Most recently observed status of the vpcIP.
	// This data may not be up to date.
	// Populated by the system.
	// Read-only.
	// More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
	// +optional
	Status VpcIPStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
}

// VpcIPSpec is a description for a VpcIP
// +k8s:openapi-gen=true
type VpcIPSpec struct {
	// VpcIP of the claim
	// +optional
	ClaimRef *corev1.ObjectReference `json:"claimRef,omitempty" protobuf:"bytes,1,opt,name=claimRef"`

	// VpcIP of the NodeENIConfig
	// +optional
	NECRef *corev1.ObjectReference `json:"necRef,omitempty" protobuf:"bytes,2,opt,name=necRef"`

	// Type of the VpcIP(Pod, Node, CVM, CTSDB, CFS, CLB)
	// +optional
	Type string `json:"type,omitempty" protobuf:"bytes,3,opt,name=type"`

	// ID of the resource allocating the VpcIP(CVM, CTSDB, CFS, CLB)
	// +optional
	ResourceID string `json:"resourceID,omitempty" protobuf:"bytes,4,opt,name=resourceID"`

	// CNIType of the vpcip, TKEDirectENI or TKERouteENI, default is TKERouteENI
	// +optional
	CNIType *VpcIPCNIType `json:"cniType,omitempty" protobuf:"bytes,6,opt,name=cniType"`

	//  store the direct eni security group info for DirectENISecurityGroups
	// +optional
	// +listType=atomic
	DirectENISecurityGroups []string `json:"directENISecurityGroups,omitempty" protobuf:"bytes,7,opt,name=directENISecurityGroups"`

	// VpcIP of the NodeENIConfig, only for non-static-ip mode
	// +optional
	SecondaryNECRef *corev1.ObjectReference `json:"secondaryNECRef,omitempty" protobuf:"bytes,8,opt,name=secondaryNECRef"`
}

// +k8s:openapi-gen=true
type VpcIPStatus struct {
	// Phase represents the current phase of VpcIP.
	// +optional
	Phase VpcIPPhase `json:"phase,omitempty" protobuf:"bytes,1,opt,name=phase"`

	// Current Condition of vpc ip.
	// +optional
	// +listType=map
	// +listMapKey=type
	Conditions []VpcIPCondition `json:"conditions,omitempty" protobuf:"bytes,2,rep,name=conditions"`

	// store the direct eni info for tke-direct-eni mode
	// +optional
	DirectENI *ENIInfo `json:"directENI,omitempty" protobuf:"bytes,3,opt,name=directENI"`

	// store the route eni ID for tke-route-eni mode
	// +optional
	RouteENIID *string `json:"routeENIID,omitempty" protobuf:"bytes,4,opt,name=routeENIID"`
}

type VpcIPPhase string

const (
	// used for VpcIP being created.
	VpcIPCreating VpcIPPhase = "Creating"
	// used for VpcIP being deleted.
	VpcIPDeleting VpcIPPhase = "Deleting"
	// used for VpcIP that is deleted.
	VpcIPDeleted VpcIPPhase = "Deleted"
	// used for VpcIP that are not available.
	VpcIPPending VpcIPPhase = "Pending"
	// used for VpcIP is being Assigned.
	VpcIPAssigning VpcIPPhase = "Assigning"
	// used for VpcIP that successfully assigned to node.
	VpcIPAssigned VpcIPPhase = "Assigned"
	// used for VpcIP is being unAssigned.
	VpcIPUnAssigning VpcIPPhase = "UnAssigning"
	// used for VpcIP that successfully unassigned to node.
	VpcIPUnAssigned VpcIPPhase = "UnAssigned"
	// used for VpcIP related eni is being Attached.
	VpcIPAttaching VpcIPPhase = "Attaching"
	// used for VpcIP related eni that successfully attached to node.
	VpcIPAttached VpcIPPhase = "Attached"
	// used for VpcIP related is being unAttaching.
	VpcIPUnAttaching VpcIPPhase = "UnAttaching"
	// used for VpcIP related eni that successfully unAttached.
	VpcIPUnAttached VpcIPPhase = "UnAttached"
	// used for VpcIP that is reserving
	VpcIPReserving VpcIPPhase = "Reserving"
	// used for VpcIP reserved.
	VpcIPReserved VpcIPPhase = "Reserved"
)

// VpcIPConditionType is a valid value of VpcIPCondition.Type
type VpcIPConditionType string

const (
	// ConditionVpcIPCreated - the vpc ip is created
	ConditionVpcIPCreated VpcIPConditionType = "VpcIPCreated"
	// ConditionVpcIPDeleted - the vpc ip is deleted
	ConditionVpcIPDeleted VpcIPConditionType = "VpcIPDeleted"
	// ConditionVpcIPAssigning - the vpc ip is assigned
	ConditionVpcIPAssigned VpcIPConditionType = "VpcIPAssigned"
	// ConditionVpcIPUnAssigning - the vpc ip is unAssigned
	ConditionVpcIPUnAssigned VpcIPConditionType = "VpcIPUnAssigned"
	// ConditionVpcIPAttached - the vpc ip related eni is attached
	ConditionVpcIPAttached VpcIPConditionType = "VpcIPAttached"
	// ConditionVpcIPUnAttached - the vpc ip is unAttached
	ConditionVpcIPUnAttached VpcIPConditionType = "VpcIPUnAttached"
)

// VpcIPCondition contails details about state of vip
type VpcIPCondition struct {
	Type   VpcIPConditionType     `json:"type" protobuf:"bytes,1,opt,name=type,casttype=PersistentVolumeClaimConditionType"`
	Status corev1.ConditionStatus `json:"status" protobuf:"bytes,2,opt,name=status,casttype=ConditionStatus"`
	// Last time we probed the condition.
	// +optional
	LastProbeTime metav1.Time `json:"lastProbeTime,omitempty" protobuf:"bytes,3,opt,name=lastProbeTime"`
	// Last time the condition transitioned from one status to another.
	// +optional
	LastTransitionTime metav1.Time `json:"lastTransitionTime,omitempty" protobuf:"bytes,4,opt,name=lastTransitionTime"`
	// Unique, one-word, CamelCase reason for the condition's last transition.
	// +optional
	Reason string `json:"reason,omitempty" protobuf:"bytes,5,opt,name=reason"`
	// Human-readable message indicating details about last transition.
	// +optional
	Message string `json:"message,omitempty" protobuf:"bytes,6,opt,name=message"`
	// The number of attempts for this condition.
	// +optional
	Attempts int32 `json:"attempts,omitempty" protobuf:"varint,7,opt,name=attempts"`
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// EIPClaimList is a list of EIPClaim resources
type EIPClaimList struct {
	metav1.TypeMeta `json:",inline"`
	// Standard list metadata.
	// More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
	// +optional
	metav1.ListMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// Items is the list of EIPClaim objects in the list.
	Items []EIPClaim `json:"items" protobuf:"bytes,2,rep,name=items"`
}

const (
	FinalizerEIP = "tke.cloud.tencent.com/eip"
)

// +genclient
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

// EIPClaim is a specification for a EIPClaim resource
// +k8s:openapi-gen=true
type EIPClaim struct {
	metav1.TypeMeta `json:",inline"`
	// Standard object's metadata.
	// More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
	// +optional
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// Specification of the desired behavior of the EIPClaim.
	// More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
	// +optional
	Spec EIPClaimSpec `json:"spec,omitempty" protobuf:"bytes,2,opt,name=spec"`

	// Most recently observed status of the EIPClaim.
	// This data may not be up to date.
	// Populated by the system.
	// Read-only.
	// More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
	// +optional
	Status EIPClaimStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
}

// EIPClaimSpec is a description for a EIPClaim
// +k8s:openapi-gen=true
type EIPClaimSpec struct {
	// VpcIPClaimDeletePolicy
	EIPClaimDeletePolicy EIPClaimDeletePolicy `json:"eipClaimDeletePolicy" protobuf:"bytes,1,opt,name=eipClaimDeletePolicy"`

	// ID of the eip
	// +optional
	EIPID string `json:"eipID,omitempty" protobuf:"bytes,2,opt,name=eipID"`

	// PublicIP of the eip
	// +optional
	PublicIP string `json:"publicIP,omitempty" protobuf:"bytes,3,opt,name=publicIP"`

	// UID of the pod
	// +optional
	PodUID string `json:"podUID,omitempty" protobuf:"bytes,4,opt,name=podUID"`

	// ISP of the eipo
	// +optional
	ISP string `json:"isp,omitempty" protobuf:"bytes,5,opt,name=isp"`
}

// EIPClaimDeletePolicy describes a policy for end-of-life maintenance of the pod which the claim cares.
type EIPClaimDeletePolicy string

const (
	// EIPClaimDeleteImmediate means the claim will be delete immediate on release from its pod.
	EIPClaimDeleteImmediate EIPClaimDeletePolicy = "Immediate"
	// EIPClaimDeleteNever means the claim never be deleted on release from its pod.
	EIPClaimDeleteNever EIPClaimDeletePolicy = "Never"
)

// +k8s:openapi-gen=true
type EIPClaimStatus struct {
	// Phase represents the current phase of EIPClaim.
	// +optional
	Phase EIPPhase `json:"phase,omitempty" protobuf:"bytes,1,opt,name=phase"`

	// Attachment represents the related eni and vpcIP
	// +optional
	Attachment *EIPAttachment `json:"attachment,omitempty" protobuf:"bytes,2,opt,name=attachment"`

	// Current Condition of eip.
	// +optional
	// +listType=map
	// +listMapKey=type
	Conditions []EIPCondition `json:"conditions,omitempty" protobuf:"bytes,3,rep,name=conditions"`
}

type EIPAttachment struct {
	// Name of the node
	// +optional
	NodeName string `json:"nodeName,omitempty" protobuf:"bytes,1,opt,name=nodeName"`

	// ID of the eni
	// +optional
	ENIID string `json:"eniID,omitempty" protobuf:"bytes,2,opt,name=eniID"`

	// VpcIP of the attachment
	// +optional
	VpcIP string `json:"vpcIP,omitempty" protobuf:"bytes,3,opt,name=vpcIP"`
}

type EIPPhase string

const (
	// used for EIP that are not available.
	EIPPending EIPPhase = "Pending"
	// used for EIP is already allocated.
	EIPAllocated EIPPhase = "Allocated"
	// used for EIP is being associated with vpcIP.
	EIPAssociating EIPPhase = "Associating"
	// used for EIP that are associated with vpcIP.
	EIPAssociated EIPPhase = "Associated"
	// used for EIP is being disassociating with vpcIP.
	EIPDisassociating EIPPhase = "Disassociating"
	// used for EIP that are disassociated with vpcIP.
	EIPDisassociated EIPPhase = "Disassociated"
	// used for EIP is being deleted.
	EIPDeleting EIPPhase = "Deleting"
)

// EIPConditionType is a valid value of EIPCondition.Type
type EIPConditionType string

const (
	// ConditionEIPAllocated - the eip is allocated
	ConditionEIPAllocated EIPConditionType = "EIPAllocated"
	// ConditionEIPAssociated - the eip is associated with eni vpcIP
	ConditionEIPAssociated EIPConditionType = "EIPAssociated"
	// ConditionEIPDisassociated - the eip is disassociated with eni vpcIP
	ConditionEIPDisassociated EIPConditionType = "EIPDisassociated"
	// ConditionEIPDeleted - the eip is deleted
	ConditionEIPDeleted EIPConditionType = "EIPDeleted"
)

// EIPCondition contails details about state of eip
type EIPCondition struct {
	Type   EIPConditionType       `json:"type" protobuf:"bytes,1,opt,name=type,casttype=PersistentVolumeClaimConditionType"`
	Status corev1.ConditionStatus `json:"status" protobuf:"bytes,2,opt,name=status,casttype=ConditionStatus"`
	// Last time we probed the condition.
	// +optional
	LastProbeTime metav1.Time `json:"lastProbeTime,omitempty" protobuf:"bytes,3,opt,name=lastProbeTime"`
	// Last time the condition transitioned from one status to another.
	// +optional
	LastTransitionTime metav1.Time `json:"lastTransitionTime,omitempty" protobuf:"bytes,4,opt,name=lastTransitionTime"`
	// Unique, one-word, CamelCase reason for the condition's last transition.
	// +optional
	Reason string `json:"reason,omitempty" protobuf:"bytes,5,opt,name=reason"`
	// Human-readable message indicating details about last transition.
	// +optional
	Message string `json:"message,omitempty" protobuf:"bytes,6,opt,name=message"`
	// The number of attempts for this condition.
	// +optional
	Attempts int32 `json:"attempts,omitempty" protobuf:"varint,7,opt,name=attempts"`
}
