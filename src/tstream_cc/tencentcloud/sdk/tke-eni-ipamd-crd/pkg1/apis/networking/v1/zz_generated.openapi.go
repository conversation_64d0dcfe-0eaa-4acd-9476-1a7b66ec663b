//go:build !ignore_autogenerated
// +build !ignore_autogenerated

/*
Copyright 2021 TKE Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by openapi-gen. DO NOT EDIT.

// This file was autogenerated by openapi-gen. Do not edit it manually!

package v1

import (
	common "k8s.io/kube-openapi/pkg/common"
	"k8s.io/kube-openapi/pkg/validation/spec"
)

func GetOpenAPIDefinitions(ref common.ReferenceCallback) map[string]common.OpenAPIDefinition {
	return map[string]common.OpenAPIDefinition{
		"tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.CrossTenantSpec":     schema_pkg_apis_networking_v1_CrossTenantSpec(ref),
		"tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.EIPClaim":            schema_pkg_apis_networking_v1_EIPClaim(ref),
		"tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.EIPClaimSpec":        schema_pkg_apis_networking_v1_EIPClaimSpec(ref),
		"tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.EIPClaimStatus":      schema_pkg_apis_networking_v1_EIPClaimStatus(ref),
		"tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.NodeENIConfig":       schema_pkg_apis_networking_v1_NodeENIConfig(ref),
		"tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.NodeENIConfigSpec":   schema_pkg_apis_networking_v1_NodeENIConfigSpec(ref),
		"tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.NodeENIConfigStatus": schema_pkg_apis_networking_v1_NodeENIConfigStatus(ref),
		"tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.VpcENI":              schema_pkg_apis_networking_v1_VpcENI(ref),
		"tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.VpcENISpec":          schema_pkg_apis_networking_v1_VpcENISpec(ref),
		"tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.VpcENIStatus":        schema_pkg_apis_networking_v1_VpcENIStatus(ref),
		"tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.VpcIP":               schema_pkg_apis_networking_v1_VpcIP(ref),
		"tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.VpcIPClaim":          schema_pkg_apis_networking_v1_VpcIPClaim(ref),
		"tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.VpcIPClaimSpec":      schema_pkg_apis_networking_v1_VpcIPClaimSpec(ref),
		"tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.VpcIPClaimStatus":    schema_pkg_apis_networking_v1_VpcIPClaimStatus(ref),
		"tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.VpcIPSpec":           schema_pkg_apis_networking_v1_VpcIPSpec(ref),
		"tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.VpcIPStatus":         schema_pkg_apis_networking_v1_VpcIPStatus(ref),
	}
}

func schema_pkg_apis_networking_v1_CrossTenantSpec(ref common.ReferenceCallback) common.OpenAPIDefinition {
	return common.OpenAPIDefinition{
		Schema: spec.Schema{
			SchemaProps: spec.SchemaProps{
				Description: "CrossTenantSpec is a description for a cross tenant eni param",
				Type:        []string{"object"},
				Properties: map[string]spec.Schema{
					"appId": {
						SchemaProps: spec.SchemaProps{
							Description: "AppId peerAppId",
							Type:        []string{"integer"},
							Format:      "int64",
						},
					},
					"uin": {
						SchemaProps: spec.SchemaProps{
							Description: "Uin peerUin",
							Type:        []string{"string"},
							Format:      "",
						},
					},
					"uniqVpcId": {
						SchemaProps: spec.SchemaProps{
							Description: "uniqVpcId peer uniqVpcId",
							Type:        []string{"string"},
							Format:      "",
						},
					},
					"subnetId": {
						SchemaProps: spec.SchemaProps{
							Description: "SubnetId, peer subnetId, only if wan to use user's real ip",
							Type:        []string{"string"},
							Format:      "",
						},
					},
					"eniLimit": {
						SchemaProps: spec.SchemaProps{
							Description: "ENILimit cross tenant eni num limit on one node",
							Type:        []string{"integer"},
							Format:      "int32",
						},
					},
				},
			},
		},
	}
}

func schema_pkg_apis_networking_v1_EIPClaim(ref common.ReferenceCallback) common.OpenAPIDefinition {
	return common.OpenAPIDefinition{
		Schema: spec.Schema{
			SchemaProps: spec.SchemaProps{
				Description: "EIPClaim is a specification for a EIPClaim resource",
				Type:        []string{"object"},
				Properties: map[string]spec.Schema{
					"kind": {
						SchemaProps: spec.SchemaProps{
							Description: "Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/api-conventions.md#types-kinds",
							Type:        []string{"string"},
							Format:      "",
						},
					},
					"apiVersion": {
						SchemaProps: spec.SchemaProps{
							Description: "APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/api-conventions.md#resources",
							Type:        []string{"string"},
							Format:      "",
						},
					},
					"metadata": {
						SchemaProps: spec.SchemaProps{
							Description: "Standard object's metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata",
							Ref:         ref("k8s.io/apimachinery/pkg/apis/meta/v1.ObjectMeta"),
						},
					},
					"spec": {
						SchemaProps: spec.SchemaProps{
							Description: "Specification of the desired behavior of the EIPClaim. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status",
							Ref:         ref("tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.EIPClaimSpec"),
						},
					},
					"status": {
						SchemaProps: spec.SchemaProps{
							Description: "Most recently observed status of the EIPClaim. This data may not be up to date. Populated by the system. Read-only. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status",
							Ref:         ref("tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.EIPClaimStatus"),
						},
					},
				},
			},
		},
		Dependencies: []string{
			"tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.EIPClaimSpec", "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.EIPClaimStatus", "k8s.io/apimachinery/pkg/apis/meta/v1.ObjectMeta"},
	}
}

func schema_pkg_apis_networking_v1_EIPClaimSpec(ref common.ReferenceCallback) common.OpenAPIDefinition {
	return common.OpenAPIDefinition{
		Schema: spec.Schema{
			SchemaProps: spec.SchemaProps{
				Description: "EIPClaimSpec is a description for a EIPClaim",
				Type:        []string{"object"},
				Properties: map[string]spec.Schema{
					"eipClaimDeletePolicy": {
						SchemaProps: spec.SchemaProps{
							Description: "VpcIPClaimDeletePolicy",
							Type:        []string{"string"},
							Format:      "",
						},
					},
					"eipID": {
						SchemaProps: spec.SchemaProps{
							Description: "ID of the eip",
							Type:        []string{"string"},
							Format:      "",
						},
					},
					"publicIP": {
						SchemaProps: spec.SchemaProps{
							Description: "PublicIP of the eip",
							Type:        []string{"string"},
							Format:      "",
						},
					},
					"podUID": {
						SchemaProps: spec.SchemaProps{
							Description: "UID of the pod",
							Type:        []string{"string"},
							Format:      "",
						},
					},
					"isp": {
						SchemaProps: spec.SchemaProps{
							Description: "ISP of the eipo",
							Type:        []string{"string"},
							Format:      "",
						},
					},
				},
				Required: []string{"eipClaimDeletePolicy"},
			},
		},
	}
}

func schema_pkg_apis_networking_v1_EIPClaimStatus(ref common.ReferenceCallback) common.OpenAPIDefinition {
	return common.OpenAPIDefinition{
		Schema: spec.Schema{
			SchemaProps: spec.SchemaProps{
				Type: []string{"object"},
				Properties: map[string]spec.Schema{
					"phase": {
						SchemaProps: spec.SchemaProps{
							Description: "Phase represents the current phase of EIPClaim.",
							Type:        []string{"string"},
							Format:      "",
						},
					},
					"attachment": {
						SchemaProps: spec.SchemaProps{
							Description: "Attachment represents the related eni and vpcIP",
							Ref:         ref("tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.EIPAttachment"),
						},
					},
					"conditions": {
						VendorExtensible: spec.VendorExtensible{
							Extensions: spec.Extensions{
								"x-kubernetes-list-map-keys": "type",
								"x-kubernetes-list-type":     "map",
							},
						},
						SchemaProps: spec.SchemaProps{
							Description: "Current Condition of eip.",
							Type:        []string{"array"},
							Items: &spec.SchemaOrArray{
								Schema: &spec.Schema{
									SchemaProps: spec.SchemaProps{
										Ref: ref("tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.EIPCondition"),
									},
								},
							},
						},
					},
				},
			},
		},
		Dependencies: []string{
			"tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.EIPAttachment", "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.EIPCondition"},
	}
}

func schema_pkg_apis_networking_v1_NodeENIConfig(ref common.ReferenceCallback) common.OpenAPIDefinition {
	return common.OpenAPIDefinition{
		Schema: spec.Schema{
			SchemaProps: spec.SchemaProps{
				Description: "NodeENIConfig is a specification for a NodeENIConfig resource",
				Type:        []string{"object"},
				Properties: map[string]spec.Schema{
					"kind": {
						SchemaProps: spec.SchemaProps{
							Description: "Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/api-conventions.md#types-kinds",
							Type:        []string{"string"},
							Format:      "",
						},
					},
					"apiVersion": {
						SchemaProps: spec.SchemaProps{
							Description: "APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/api-conventions.md#resources",
							Type:        []string{"string"},
							Format:      "",
						},
					},
					"metadata": {
						SchemaProps: spec.SchemaProps{
							Description: "Standard object's metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata",
							Ref:         ref("k8s.io/apimachinery/pkg/apis/meta/v1.ObjectMeta"),
						},
					},
					"spec": {
						SchemaProps: spec.SchemaProps{
							Description: "Specification of the desired behavior of the nodeENIConfig. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status",
							Ref:         ref("tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.NodeENIConfigSpec"),
						},
					},
					"status": {
						SchemaProps: spec.SchemaProps{
							Description: "Most recently observed status of the nodeENIConfig. This data may not be up to date. Populated by the system. Read-only. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status",
							Ref:         ref("tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.NodeENIConfigStatus"),
						},
					},
				},
			},
		},
		Dependencies: []string{
			"tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.NodeENIConfigSpec", "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.NodeENIConfigStatus", "k8s.io/apimachinery/pkg/apis/meta/v1.ObjectMeta"},
	}
}

func schema_pkg_apis_networking_v1_NodeENIConfigSpec(ref common.ReferenceCallback) common.OpenAPIDefinition {
	return common.OpenAPIDefinition{
		Schema: spec.Schema{
			SchemaProps: spec.SchemaProps{
				Description: "NodeENIConfigSpec is a description for a NodeENIConfig",
				Type:        []string{"object"},
				Properties: map[string]spec.Schema{
					"zone": {
						SchemaProps: spec.SchemaProps{
							Description: "zone of the node",
							Type:        []string{"string"},
							Format:      "",
						},
					},
					"providerID": {
						SchemaProps: spec.SchemaProps{
							Description: "ID of the node assigned by ENI provider",
							Type:        []string{"string"},
							Format:      "",
						},
					},
					"maxENI": {
						SchemaProps: spec.SchemaProps{
							Description: "max ENIs the node can attach",
							Type:        []string{"integer"},
							Format:      "int32",
						},
					},
					"maxIPPerENI": {
						SchemaProps: spec.SchemaProps{
							Description: "max IP per eni can assign",
							Type:        []string{"integer"},
							Format:      "int32",
						},
					},
					"maxEIP": {
						SchemaProps: spec.SchemaProps{
							Description: "max EIPs the node can associate",
							Type:        []string{"integer"},
							Format:      "int32",
						},
					},
					"disableRouteENI": {
						SchemaProps: spec.SchemaProps{
							Description: "whether disable route eni of the node",
							Type:        []string{"boolean"},
							Format:      "",
						},
					},
					"maxDirectENI": {
						SchemaProps: spec.SchemaProps{
							Description: "max direct ENIS the node can attach",
							Type:        []string{"integer"},
							Format:      "int32",
						},
					},
					"desiredDirectENI": {
						SchemaProps: spec.SchemaProps{
							Description: "number of enis needs to attach to the node",
							Type:        []string{"integer"},
							Format:      "int32",
						},
					},
					"securityGroups": {
						VendorExtensible: spec.VendorExtensible{
							Extensions: spec.Extensions{
								"x-kubernetes-list-type": "atomic",
							},
						},
						SchemaProps: spec.SchemaProps{
							Description: "SecurityGroups is the security groups expected to be associated with the node enis",
							Type:        []string{"array"},
							Items: &spec.SchemaOrArray{
								Schema: &spec.Schema{
									SchemaProps: spec.SchemaProps{
										Type:   []string{"string"},
										Format: "",
									},
								},
							},
						},
					},
					"maxRouteENI": {
						SchemaProps: spec.SchemaProps{
							Description: "max route ENIS the node can attach",
							Type:        []string{"integer"},
							Format:      "int32",
						},
					},
					"desiredRouteENI": {
						SchemaProps: spec.SchemaProps{
							Description: "number of route enis needs to attach to the node",
							Type:        []string{"integer"},
							Format:      "int32",
						},
					},
					"desiredRouteENIIP": {
						SchemaProps: spec.SchemaProps{
							Description: "number of eni ip needs to attach to the node",
							Type:        []string{"integer"},
							Format:      "int32",
						},
					},
				},
				Required: []string{"zone"},
			},
		},
	}
}

func schema_pkg_apis_networking_v1_NodeENIConfigStatus(ref common.ReferenceCallback) common.OpenAPIDefinition {
	return common.OpenAPIDefinition{
		Schema: spec.Schema{
			SchemaProps: spec.SchemaProps{
				Type: []string{"object"},
				Properties: map[string]spec.Schema{
					"eniInfos": {
						VendorExtensible: spec.VendorExtensible{
							Extensions: spec.Extensions{
								"x-kubernetes-list-type": "atmoic",
							},
						},
						SchemaProps: spec.SchemaProps{
							Description: "ENIs is a list of enis attached by the node",
							Type:        []string{"array"},
							Items: &spec.SchemaOrArray{
								Schema: &spec.Schema{
									SchemaProps: spec.SchemaProps{
										Ref: ref("tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.ENIInfo"),
									},
								},
							},
						},
					},
					"dynamicReserv": {
						SchemaProps: spec.SchemaProps{
							Description: "number of dynamic reservation ip resource for scheduler pod to node",
							Type:        []string{"integer"},
							Format:      "int32",
						},
					},
					"podCIDRs": {
						VendorExtensible: spec.VendorExtensible{
							Extensions: spec.Extensions{
								"x-kubernetes-list-type": "atomic",
							},
						},
						SchemaProps: spec.SchemaProps{
							Description: "list of podCIDR allocated to this node",
							Type:        []string{"array"},
							Items: &spec.SchemaOrArray{
								Schema: &spec.Schema{
									SchemaProps: spec.SchemaProps{
										Type:   []string{"string"},
										Format: "",
									},
								},
							},
						},
					},
					"conditions": {
						VendorExtensible: spec.VendorExtensible{
							Extensions: spec.Extensions{
								"x-kubernetes-list-map-keys": "type",
								"x-kubernetes-list-type":     "map",
							},
						},
						SchemaProps: spec.SchemaProps{
							Description: "Current Condition of nec.",
							Type:        []string{"array"},
							Items: &spec.SchemaOrArray{
								Schema: &spec.Schema{
									SchemaProps: spec.SchemaProps{
										Ref: ref("tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.NodeENIConfigCondition"),
									},
								},
							},
						},
					},
				},
			},
		},
		Dependencies: []string{
			"tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.ENIInfo", "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.NodeENIConfigCondition"},
	}
}

func schema_pkg_apis_networking_v1_VpcENI(ref common.ReferenceCallback) common.OpenAPIDefinition {
	return common.OpenAPIDefinition{
		Schema: spec.Schema{
			SchemaProps: spec.SchemaProps{
				Description: "VpcENI is a specification for a VpcENI resource",
				Type:        []string{"object"},
				Properties: map[string]spec.Schema{
					"kind": {
						SchemaProps: spec.SchemaProps{
							Description: "Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/api-conventions.md#types-kinds",
							Type:        []string{"string"},
							Format:      "",
						},
					},
					"apiVersion": {
						SchemaProps: spec.SchemaProps{
							Description: "APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/api-conventions.md#resources",
							Type:        []string{"string"},
							Format:      "",
						},
					},
					"metadata": {
						SchemaProps: spec.SchemaProps{
							Description: "Standard object's metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata",
							Ref:         ref("k8s.io/apimachinery/pkg/apis/meta/v1.ObjectMeta"),
						},
					},
					"spec": {
						SchemaProps: spec.SchemaProps{
							Description: "Specification of the desired behavior of the nodeENIConfig. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status",
							Ref:         ref("tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.VpcENISpec"),
						},
					},
					"status": {
						SchemaProps: spec.SchemaProps{
							Description: "Most recently observed status of the nodeENIConfig. This data may not be up to date. Populated by the system. Read-only. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status",
							Ref:         ref("tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.VpcENIStatus"),
						},
					},
				},
			},
		},
		Dependencies: []string{
			"tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.VpcENISpec", "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.VpcENIStatus", "k8s.io/apimachinery/pkg/apis/meta/v1.ObjectMeta"},
	}
}

func schema_pkg_apis_networking_v1_VpcENISpec(ref common.ReferenceCallback) common.OpenAPIDefinition {
	return common.OpenAPIDefinition{
		Schema: spec.Schema{
			SchemaProps: spec.SchemaProps{
				Description: "VpcENISpec is a description for a VpcENI",
				Type:        []string{"object"},
				Properties: map[string]spec.Schema{
					"zone": {
						SchemaProps: spec.SchemaProps{
							Description: "zone of the eni",
							Type:        []string{"string"},
							Format:      "",
						},
					},
					"providerID": {
						SchemaProps: spec.SchemaProps{
							Description: "ID of the attached node assigned by ENI provider",
							Type:        []string{"string"},
							Format:      "",
						},
					},
					"maxSecondaryIP": {
						SchemaProps: spec.SchemaProps{
							Description: "max secondary IPs the eni can attach",
							Type:        []string{"integer"},
							Format:      "int32",
						},
					},
					"mode": {
						SchemaProps: spec.SchemaProps{
							Description: "Mode of the eni: route-eni, direct-eni, cross",
							Type:        []string{"string"},
							Format:      "",
						},
					},
					"claimRef": {
						SchemaProps: spec.SchemaProps{
							Description: "VpcENI of the claim",
							Ref:         ref("k8s.io/api/core/v1.ObjectReference"),
						},
					},
					"crossTenant": {
						SchemaProps: spec.SchemaProps{
							Description: "CrossTenant cross tenant param that identify the tenant account",
							Ref:         ref("tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.CrossTenantSpec"),
						},
					},
				},
				Required: []string{"zone"},
			},
		},
		Dependencies: []string{
			"tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.CrossTenantSpec", "k8s.io/api/core/v1.ObjectReference"},
	}
}

func schema_pkg_apis_networking_v1_VpcENIStatus(ref common.ReferenceCallback) common.OpenAPIDefinition {
	return common.OpenAPIDefinition{
		Schema: spec.Schema{
			SchemaProps: spec.SchemaProps{
				Type: []string{"object"},
				Properties: map[string]spec.Schema{
					"phase": {
						SchemaProps: spec.SchemaProps{
							Description: "Phase represents the current phase of VpcENI.",
							Type:        []string{"string"},
							Format:      "",
						},
					},
					"conditions": {
						VendorExtensible: spec.VendorExtensible{
							Extensions: spec.Extensions{
								"x-kubernetes-list-map-keys": "type",
								"x-kubernetes-list-type":     "map",
							},
						},
						SchemaProps: spec.SchemaProps{
							Description: "Current Condition of vpc ip.",
							Type:        []string{"array"},
							Items: &spec.SchemaOrArray{
								Schema: &spec.Schema{
									SchemaProps: spec.SchemaProps{
										Ref: ref("tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.VpcENICondition"),
									},
								},
							},
						},
					},
					"eni": {
						SchemaProps: spec.SchemaProps{
							Description: "information of eni attached to the node",
							Ref:         ref("tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.ENIInfo"),
						},
					},
				},
			},
		},
		Dependencies: []string{
			"tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.ENIInfo", "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.VpcENICondition"},
	}
}

func schema_pkg_apis_networking_v1_VpcIP(ref common.ReferenceCallback) common.OpenAPIDefinition {
	return common.OpenAPIDefinition{
		Schema: spec.Schema{
			SchemaProps: spec.SchemaProps{
				Description: "VpcIP is a specification for a VpcIP resource",
				Type:        []string{"object"},
				Properties: map[string]spec.Schema{
					"kind": {
						SchemaProps: spec.SchemaProps{
							Description: "Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/api-conventions.md#types-kinds",
							Type:        []string{"string"},
							Format:      "",
						},
					},
					"apiVersion": {
						SchemaProps: spec.SchemaProps{
							Description: "APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/api-conventions.md#resources",
							Type:        []string{"string"},
							Format:      "",
						},
					},
					"metadata": {
						SchemaProps: spec.SchemaProps{
							Description: "Standard object's metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata",
							Ref:         ref("k8s.io/apimachinery/pkg/apis/meta/v1.ObjectMeta"),
						},
					},
					"spec": {
						SchemaProps: spec.SchemaProps{
							Description: "Specification of the desired behavior of the vpcIP. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status",
							Ref:         ref("tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.VpcIPSpec"),
						},
					},
					"status": {
						SchemaProps: spec.SchemaProps{
							Description: "Most recently observed status of the vpcIP. This data may not be up to date. Populated by the system. Read-only. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status",
							Ref:         ref("tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.VpcIPStatus"),
						},
					},
				},
			},
		},
		Dependencies: []string{
			"tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.VpcIPSpec", "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.VpcIPStatus", "k8s.io/apimachinery/pkg/apis/meta/v1.ObjectMeta"},
	}
}

func schema_pkg_apis_networking_v1_VpcIPClaim(ref common.ReferenceCallback) common.OpenAPIDefinition {
	return common.OpenAPIDefinition{
		Schema: spec.Schema{
			SchemaProps: spec.SchemaProps{
				Description: "VpcIPClaim is a specification for a VpcIPClaim resource",
				Type:        []string{"object"},
				Properties: map[string]spec.Schema{
					"kind": {
						SchemaProps: spec.SchemaProps{
							Description: "Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/api-conventions.md#types-kinds",
							Type:        []string{"string"},
							Format:      "",
						},
					},
					"apiVersion": {
						SchemaProps: spec.SchemaProps{
							Description: "APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/api-conventions.md#resources",
							Type:        []string{"string"},
							Format:      "",
						},
					},
					"metadata": {
						SchemaProps: spec.SchemaProps{
							Description: "Standard object's metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata",
							Ref:         ref("k8s.io/apimachinery/pkg/apis/meta/v1.ObjectMeta"),
						},
					},
					"spec": {
						SchemaProps: spec.SchemaProps{
							Description: "Specification of the desired behavior of the vpcIPClaim. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status",
							Ref:         ref("tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.VpcIPClaimSpec"),
						},
					},
					"status": {
						SchemaProps: spec.SchemaProps{
							Description: "Most recently observed status of the vpcIPClaim. This data may not be up to date. Populated by the system. Read-only. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status",
							Ref:         ref("tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.VpcIPClaimStatus"),
						},
					},
				},
			},
		},
		Dependencies: []string{
			"tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.VpcIPClaimSpec", "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.VpcIPClaimStatus", "k8s.io/apimachinery/pkg/apis/meta/v1.ObjectMeta"},
	}
}

func schema_pkg_apis_networking_v1_VpcIPClaimSpec(ref common.ReferenceCallback) common.OpenAPIDefinition {
	return common.OpenAPIDefinition{
		Schema: spec.Schema{
			SchemaProps: spec.SchemaProps{
				Description: "VpcIPClaimSpec is a description for a VpcIPClaim",
				Type:        []string{"object"},
				Properties: map[string]spec.Schema{
					"vpcIPClaimDeletePolicy": {
						SchemaProps: spec.SchemaProps{
							Description: "VpcIPClaimDeletePolicy",
							Type:        []string{"string"},
							Format:      "",
						},
					},
					"subnetCIDR": {
						SchemaProps: spec.SchemaProps{
							Description: "Subnet CIDR of the vpcIP",
							Type:        []string{"string"},
							Format:      "",
						},
					},
					"vpcIP": {
						SchemaProps: spec.SchemaProps{
							Description: "vpcIP of the claim",
							Type:        []string{"string"},
							Format:      "",
						},
					},
					"podUID": {
						SchemaProps: spec.SchemaProps{
							Description: "UID of the pod",
							Type:        []string{"string"},
							Format:      "",
						},
					},
					"zone": {
						SchemaProps: spec.SchemaProps{
							Description: "Zone of the vpcIP",
							Type:        []string{"string"},
							Format:      "",
						},
					},
					"cniType": {
						SchemaProps: spec.SchemaProps{
							Description: "CNIType of the vpcip, TKEDirectENI or TKERouteENI, default is TKERouteENI",
							Type:        []string{"string"},
							Format:      "",
						},
					},
					"securityGroups": {
						VendorExtensible: spec.VendorExtensible{
							Extensions: spec.Extensions{
								"x-kubernetes-list-type": "atomic",
							},
						},
						SchemaProps: spec.SchemaProps{
							Description: "SecurityGroup represents the securitygroup of vpcipclaim",
							Type:        []string{"array"},
							Items: &spec.SchemaOrArray{
								Schema: &spec.Schema{
									SchemaProps: spec.SchemaProps{
										Type:   []string{"string"},
										Format: "",
									},
								},
							},
						},
					},
					"crossTenant": {
						SchemaProps: spec.SchemaProps{
							Description: "CrossTenant cross tenant param that identify the tenant account",
							Ref:         ref("tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.CrossTenantSpec"),
						},
					},
					"vpcENI": {
						SchemaProps: spec.SchemaProps{
							Description: "vpcENI of the claim",
							Type:        []string{"string"},
							Format:      "",
						},
					},
				},
				Required: []string{"vpcIPClaimDeletePolicy", "subnetCIDR"},
			},
		},
		Dependencies: []string{
			"tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.CrossTenantSpec"},
	}
}

func schema_pkg_apis_networking_v1_VpcIPClaimStatus(ref common.ReferenceCallback) common.OpenAPIDefinition {
	return common.OpenAPIDefinition{
		Schema: spec.Schema{
			SchemaProps: spec.SchemaProps{
				Type: []string{"object"},
				Properties: map[string]spec.Schema{
					"phase": {
						SchemaProps: spec.SchemaProps{
							Description: "Phase represents the current phase of VpcIPClaim.",
							Type:        []string{"string"},
							Format:      "",
						},
					},
				},
			},
		},
	}
}

func schema_pkg_apis_networking_v1_VpcIPSpec(ref common.ReferenceCallback) common.OpenAPIDefinition {
	return common.OpenAPIDefinition{
		Schema: spec.Schema{
			SchemaProps: spec.SchemaProps{
				Description: "VpcIPSpec is a description for a VpcIP",
				Type:        []string{"object"},
				Properties: map[string]spec.Schema{
					"claimRef": {
						SchemaProps: spec.SchemaProps{
							Description: "VpcIP of the claim",
							Ref:         ref("k8s.io/api/core/v1.ObjectReference"),
						},
					},
					"necRef": {
						SchemaProps: spec.SchemaProps{
							Description: "VpcIP of the NodeENIConfig",
							Ref:         ref("k8s.io/api/core/v1.ObjectReference"),
						},
					},
					"type": {
						SchemaProps: spec.SchemaProps{
							Description: "Type of the VpcIP(Pod, Node, CVM, CTSDB, CFS, CLB)",
							Type:        []string{"string"},
							Format:      "",
						},
					},
					"resourceID": {
						SchemaProps: spec.SchemaProps{
							Description: "ID of the resource allocating the VpcIP(CVM, CTSDB, CFS, CLB)",
							Type:        []string{"string"},
							Format:      "",
						},
					},
					"cniType": {
						SchemaProps: spec.SchemaProps{
							Description: "CNIType of the vpcip, TKEDirectENI or TKERouteENI, default is TKERouteENI",
							Type:        []string{"string"},
							Format:      "",
						},
					},
					"directENISecurityGroups": {
						VendorExtensible: spec.VendorExtensible{
							Extensions: spec.Extensions{
								"x-kubernetes-list-type": "atomic",
							},
						},
						SchemaProps: spec.SchemaProps{
							Description: "\n store the direct eni security group info for DirectENISecurityGroups",
							Type:        []string{"array"},
							Items: &spec.SchemaOrArray{
								Schema: &spec.Schema{
									SchemaProps: spec.SchemaProps{
										Type:   []string{"string"},
										Format: "",
									},
								},
							},
						},
					},
					"secondaryNECRef": {
						SchemaProps: spec.SchemaProps{
							Description: "VpcIP of the NodeENIConfig, only for non-static-ip mode",
							Ref:         ref("k8s.io/api/core/v1.ObjectReference"),
						},
					},
				},
			},
		},
		Dependencies: []string{
			"k8s.io/api/core/v1.ObjectReference"},
	}
}

func schema_pkg_apis_networking_v1_VpcIPStatus(ref common.ReferenceCallback) common.OpenAPIDefinition {
	return common.OpenAPIDefinition{
		Schema: spec.Schema{
			SchemaProps: spec.SchemaProps{
				Type: []string{"object"},
				Properties: map[string]spec.Schema{
					"phase": {
						SchemaProps: spec.SchemaProps{
							Description: "Phase represents the current phase of VpcIP.",
							Type:        []string{"string"},
							Format:      "",
						},
					},
					"conditions": {
						VendorExtensible: spec.VendorExtensible{
							Extensions: spec.Extensions{
								"x-kubernetes-list-map-keys": "type",
								"x-kubernetes-list-type":     "map",
							},
						},
						SchemaProps: spec.SchemaProps{
							Description: "Current Condition of vpc ip.",
							Type:        []string{"array"},
							Items: &spec.SchemaOrArray{
								Schema: &spec.Schema{
									SchemaProps: spec.SchemaProps{
										Ref: ref("tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.VpcIPCondition"),
									},
								},
							},
						},
					},
					"directENI": {
						SchemaProps: spec.SchemaProps{
							Description: "store the direct eni info for tke-direct-eni mode",
							Ref:         ref("tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.ENIInfo"),
						},
					},
					"routeENIID": {
						SchemaProps: spec.SchemaProps{
							Description: "store the route eni ID for tke-route-eni mode",
							Type:        []string{"string"},
							Format:      "",
						},
					},
				},
			},
		},
		Dependencies: []string{
			"tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.ENIInfo", "tencentcloud.com/tstream_galileo/src/tstream_cc/tencentcloud/sdk/tke-eni-ipamd-crd/pkg1/apis/networking/v1.VpcIPCondition"},
	}
}
