// Code generated by protoc-gen-go.
// source: ActiveRMInfoProto.proto
// DO NOT EDIT!

/*
Package ActiveRMInfoProto is a generated protocol buffer package.

It is generated from these files:
    ActiveRMInfoProto.proto

It has these top-level messages:
    ActiveRMInfoProto
*/
package proto

import proto "github.com/golang/protobuf/proto"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = math.Inf

type ActiveRMInfoProto struct {
	ClusterId        *string `protobuf:"bytes,1,opt,name=clusterId" json:"clusterId,omitempty"`
	RmId             *string `protobuf:"bytes,2,opt,name=rmId" json:"rmId,omitempty"`
	XXX_unrecognized []byte  `json:"-"`
}

func (m *ActiveRMInfoProto) Reset()         { *m = ActiveRMInfoProto{} }
func (m *ActiveRMInfoProto) String() string { return proto.CompactTextString(m) }
func (*ActiveRMInfoProto) ProtoMessage()    {}

func (m *ActiveRMInfoProto) GetClusterId() string {
	if m != nil && m.ClusterId != nil {
		return *m.ClusterId
	}
	return ""
}

func (m *ActiveRMInfoProto) GetRmId() string {
	if m != nil && m.RmId != nil {
		return *m.RmId
	}
	return ""
}

func init() {
}
