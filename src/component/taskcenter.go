package component

import (
	"encoding/json"
	"fmt"
	"math/rand"
	"strconv"
	log "tencentcloud.com/tstream_galileo/src/common/logger"

	"github.com/juju/errors"
)

type taskcenter struct {
	component
}

var g_taskcenter *taskcenter

func init() {
	g_taskcenter = &taskcenter{}
	g_taskcenter.componentName = "taskcenter"
	g_taskcenter.keyRetcode = "returnCode"
	g_taskcenter.keyRetmsg = "returnMsg"
	g_taskcenter.keyData = "data"
}

func (this *taskcenter) CreateProcessInstance(processKey, docId string, params map[string]string) (processInstanceId int64, err error) {
	defer func() {
		if errs := recover(); errs != nil {
			log.Error("[taskcenter]CreateProcessInstance error:", errs)
			err = errors.Cause(fmt.Errorf("%+v", errs))
			return
		}
	}()

	reqdata := make(map[string]interface{})
	evevntId := strconv.Itoa(rand.Intn(10000000))
	reqdata["processKey"] = processKey
	reqdata["docId"] = docId
	reqdata["params"] = params
	retdata, err := this.sendRequest("qcloud.darwintaskcenter.createprocess", evevntId, reqdata)
	if err != nil {
		return 0, err
	}
	jsondata, err := json.Marshal(retdata)
	this.checkErr(err)
	retmap := map[string]string{}
	err = json.Unmarshal(jsondata, &retmap)
	this.checkErr(err)
	if len(retmap) == 0 {
		return 0, errors.New("task center return empty")
	}
	fid, ok := retmap["flowId"]
	if !ok {
		return 0, errors.New("task center return empty")
	}
	processInstanceId, err = strconv.ParseInt(fid, 10, 64)
	return processInstanceId, err
}

func NewComponentTaskCenter(rid int) *taskcenter {
	g_taskcenter.regionId = rid
	return g_taskcenter
}

func (this *taskcenter) SuspendProcessInstance(regionId int, flowId int64) (err error) {
	defer func() {
		if errs := recover(); errs != nil {
			log.Error("[taskcenter]SuspendProcessInstance error:", errs)
			err = errors.Cause(fmt.Errorf("%+v", errs))
			return
		}
	}()

	reqdata := make(map[string]interface{})
	evevntId := strconv.Itoa(rand.Intn(10000000))
	reqdata["regionId"] = regionId
	reqdata["flowId"] = fmt.Sprintf("%d", flowId)
	retdata, err := this.sendRequest("qcloud.darwintaskcenter.suspendprocess", evevntId, reqdata)
	if err != nil {
		return err
	}
	jsondata, err := json.Marshal(retdata)
	this.checkErr(err)
	retmap := map[string]string{}
	err = json.Unmarshal(jsondata, &retmap)
	this.checkErr(err)

	return nil
}

func (this *taskcenter) CancelProcessInstance(regionId int, flowId int64) (err error) {
	defer func() {
		if errs := recover(); errs != nil {
			log.Error("[taskcenter]CancelProcessInstance error:", errs)
			err = errors.Cause(fmt.Errorf("%+v", errs))
			return
		}
	}()

	reqdata := make(map[string]interface{})
	evevntId := strconv.Itoa(rand.Intn(10000000))
	reqdata["regionId"] = regionId
	reqdata["flowId"] = fmt.Sprintf("%d", flowId)
	retdata, err := this.sendRequest("qcloud.darwintaskcenter.cancelprocess", evevntId, reqdata)
	if err != nil {
		return err
	}
	jsondata, err := json.Marshal(retdata)
	this.checkErr(err)
	retmap := map[string]string{}
	err = json.Unmarshal(jsondata, &retmap)
	this.checkErr(err)

	return nil
}
