package component_test
import (
	"testing"
	"tencentcloud.com/tstream_galileo/src/component"
)
////go test -v cvm_cgw_test.go -o ~/emrccCode/bin/t
func TestCvmCgwService(t *testing.T){


	cvmCgwService:=component.NewCvmCgwService(4)
	paraMap:= make(map[string]interface{})
	//paraMap["regionId"]=4
	paraMap["appId"]=1253534036
	paraMap["zoneIdList"] =[1]int{200002}
	paraMap["cvmPayMode"]	= 1 //1包年包月，2按量计费


	ret,err:=cvmCgwService.GetVmConfigQuota(paraMap,1253444701)

	if nil != err{
		t.Error("err:",err)
	}

	t.Log("ret:",ret,"\n","err:",err)
}