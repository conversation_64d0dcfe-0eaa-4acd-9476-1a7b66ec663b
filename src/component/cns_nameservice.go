/**
* 与CNS交互的外部接口
 */
package component

import (
	"encoding/json"
	"errors"
	log "tencentcloud.com/tstream_galileo/src/common/logger"
	"strings"
	"tencentcloud.com/tstream_galileo/src/common/util"
)

type CnsNameService struct {
	component
}

var gCnsNameService *CnsNameService

func init() {
	gCnsNameService = new(CnsNameService)
	gCnsNameService.componentName = "cns_nameservice"				// nolint
	gCnsNameService.keyRetcode = "code"								// nolint
	gCnsNameService.keyRetmsg = "msg"								// nolint
	gCnsNameService.keyData = "data"								// nolint
	gCnsNameService.makePackFunc = gCnsNameService.makePack
}

func NewCnsNameService(rid int) *CnsNameService {
	gCnsNameService.regionId=rid
	return gCnsNameService
}

/**
* 重写makePack， 定义cns的方法
 */
func (this *CnsNameService) makePack(interfaceName string, eventId string, params map[string]interface{}) (req string, err error) {

	reqmap := make(map[string]interface{})

	reqmap["interfaceName"] = interfaceName
	reqmap["data"] = params
	reqmap["password"] = this.getPwd()
	reqmap["username"] = this.getUserName()

	data, err := json.Marshal(reqmap)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

/**
* 查询路由
* {
*	"interfaceName":"Cns.QueryRoute",
* 	"data":
* 	{
* 		"serviceName": "tcp:*******:9090",
* 	}
* }
*
* Ret:
* "recordList":["1|tcp:************:9090","1|tcp:************:9090"]
*
 */
func (this *CnsNameService) QueryRoute(eventId string, serviceName *IpAddr) (e error, recodeList []*IpAddr) {
	reqData := make(map[string]interface{})

	reqData["serviceName"] = serviceName.ToString()

	ifName := "Cns.QueryRoute"

	retData, e := this.sendRequest(ifName, eventId, reqData)
	if e != nil {
		log.Error(eventId, " Failed to QueryRoute by: ",
			serviceName.ToString(), ", Errors: ", e.Error())
		return e, nil
	}

	retMap, ok := retData.(map[string]interface{})
	if !ok {
		log.Error(eventId, " Ret data is not map[string]interface{}")
		return errors.New("Return data is not map[string]interface{}"), nil
	}

	// get recordList
	e, recodeListConf := util.ParseStringArrayFromRetData(retMap, "recordList")

	if e != nil {
		log.Error(eventId, " Cns.QueryRoute return recordList error: "+e.Error())
		return e, nil
	}

	recodeList = make([]*IpAddr, 0)

	for _, record := range recodeListConf {
		// trim first part separate by |
		fields := strings.Split(record, "|")

		if len(fields) != 2 {
			log.Error(eventId, " Cns.QueryRoute return recordList sep by |, len != 2, data:"+record)
			return errors.New("return data format error"), nil
		}

		addr := &IpAddr{}

		// use |tcp:************:9090
		if addr.FromString(fields[1], ":") != nil {
			log.Error(eventId, " Cns.QueryRoute return recordList parse ip add data:"+fields[1])
			return errors.New("return data format error"), nil
		}

		recodeList = append(recodeList, addr)
	}

	return nil, recodeList
}

/**
* 添加路由
*
* {
* 	"interfaceName":"Cns.DeleteRoute",
* 	"data":
*	 {
* 		"serviceName": "tcp:*******:9090",
* 		"recordList": ["1|tcp:************:9090"," 1|tcp:************:9090"]
* 	}
* }
*
* Ret:
* {
* 	"code":0,
* 	"msg":"success",
* 	"data":{"tips":"name_not_exist"}
* }
*
 */
func (this *CnsNameService) AddRoute(eventId string, serviceName *IpAddr, recodeList []IpAddr) (e error) {
	reqData := make(map[string]interface{})

	reqData["serviceName"] = serviceName.ToString()

	var hostList []string = make([]string, 0)
	for _, recode := range recodeList {
		hostList = append(hostList, "1|"+recode.ToString()) // 协议规则
	}
	reqData["recordList"] = hostList

	ifName := "Cns.AddRoute"
	_, e = this.sendRequest(ifName, eventId, reqData)
	if e != nil {
		log.Error(eventId, " Failed to AddRoute for: ",
			serviceName.ToString(), ", Errors: ", e.Error())
		return e
	}

	return nil
}

/**
* 删除路由
* {
* 	"interfaceName":"Cns.DeleteRoute",
* 	"data":
*	 {
* 		"serviceName": "tcp:*******:9090",
* 		"recordList": ["1|tcp:************:9090"," 1|tcp:************:9090"]
* 	}
* }
*
* Ret:
* {
* 	"code":0,
* 	"msg":"success",
* 	"data":{"tips":"name_not_exist"}
* }
*
 */
func (this *CnsNameService) DeleteRoute(eventId string, serviceName *IpAddr, recodeList []IpAddr) (e error) {
	reqData := make(map[string]interface{})

	reqData["serviceName"] = serviceName.ToString()

	var hostList []string = make([]string, 0)
	for _, recode := range recodeList {
		hostList = append(hostList, "1|"+recode.ToString()) // 协议规则
	}
	reqData["recordList"] = hostList

	ifName := "Cns.DeleteRoute"
	_, e = this.sendRequest(ifName, eventId, reqData)
	if e != nil {
		log.Error(eventId, " Failed to DeleteRoute for: ",
			serviceName.ToString(), ", Errors: ", e.Error())
		return e
	}

	return nil
}

/**
* 更新路由
* 将路由更新为recordelist里面的值
* {
* 	"interfaceName":"Cns.UpdateRoute",
* 	"data":
*	 {
* 		"serviceName": "tcp:*******:9090",
* 		"recordList": ["1|tcp:************:9090"," 1|tcp:************:9090"]
* 	}
* }
*
* Ret:
* {
* 	"code":0,
* 	"msg":"success",
* 	"data":{"tips":"name_not_exist"}
* }
*
 */
func (this *CnsNameService) UpdateRoute(eventId string, serviceName *IpAddr, recodeList []IpAddr) (e error) {
	reqData := make(map[string]interface{})

	reqData["serviceName"] = serviceName.ToString()

	var hostList []string = make([]string, 0)
	for _, recode := range recodeList {
		hostList = append(hostList, "1|"+recode.ToString()) // 协议规则
	}
	reqData["recordList"] = hostList

	ifName := "Cns.UpdateRoute"
	_, e = this.sendRequest(ifName, eventId, reqData)
	if e != nil {
		log.Error(eventId, " Failed to UpdateRoute for: ",
			serviceName.ToString(), ", Errors: ", e.Error())
		return e
	}

	return nil
}

/**
* 删除名字服务
* {
* 	"interfaceName":"Cns.DeleteName",
* 	"data":
*	 {
* 		"serviceName": "tcp:*******:9090"
* 	}
* }
*
* Ret:
* {
* 	"code":0,
* 	"msg":"success",
* 	"data":{"tips":"name_not_exist"}
* }
*
 */
func (this *CnsNameService) DeleteName(eventId string, serviceName *IpAddr) (e error) {
	reqData := make(map[string]interface{})

	reqData["serviceName"] = serviceName.ToString()

	ifName := "Cns.DeleteName"
	_, e = this.sendRequest(ifName, eventId, reqData)
	if e != nil {
		log.Error(eventId, " Failed to DeleteName for: ",
			serviceName.ToString(), ", Errors: ", e.Error())
		return e
	}

	return nil
}
