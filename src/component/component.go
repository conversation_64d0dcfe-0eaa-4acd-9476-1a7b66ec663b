package component

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"reflect"
	"strconv"
	"strings"
	"time"

	conf "tencentcloud.com/tstream_galileo/src/common/configure"
	"tencentcloud.com/tstream_galileo/src/common/errorcode"
	log "tencentcloud.com/tstream_galileo/src/common/logger"
	"tencentcloud.com/tstream_galileo/src/common/monitor"
	"tencentcloud.com/tstream_galileo/src/common/rpcctx"
	"tencentcloud.com/tstream_galileo/src/common/timecost"
	"tencentcloud.com/tstream_galileo/src/common/util"
)

const conffile = "component.properties"

// 返回码成功标示
const succ = 0

const (
	HTTP_GET_METHOD    = "GET"
	HTTP_POST_METHOD   = "POST"
	HTTP_DELETE_METHOD = "DELETE"
	HTTP_PUT_METHOD    = "PUT"
)

type component struct {
	componentName string
	keyRetcode    string //返回码key 每个组件可能不一样
	keyRetmsg     string //返回消息key
	keyData       string //返回数据key
	regionId      int    //地域ID
	makePackFunc  func(string, string, map[string]interface{}) (string, error)
	getUrlFunc    func() (string, error)

	// 是否支持scsDevEnv环境的mock操作
	disableMock bool
}

// 占位对象, 用来做外部 MC POST 调用, 以复用现有代码
type ExternalComponent struct {
	component
}

func (this *component) checkErr(err error) {
	if err != nil {
		panic(err.Error())
	}
}

func (this *component) checkBool(ok bool, errmsg string) {
	if !ok {
		panic(errmsg)
	}
}

// 获取请求的URL
func (this *component) getUrl() (url string, err error) {
	urlkey := fmt.Sprintf("component.url.%s.%d", this.componentName, this.regionId)
	url = conf.GetConfStringValueWithDefault(conffile, urlkey, "")
	if url == "" {
		url = conf.GetConfStringValue(conffile, fmt.Sprintf("component.url.%s", this.componentName))
		if len(url) == 0 {
			return "", errors.New(urlkey + " not set")
		}
	}
	return url, nil
}

// 获取超时时间
func (this *component) getTimeOut() (timeout int) {
	timeout = conf.GetConfIntValue(conffile, "component.timeout."+this.componentName, 15)
	return timeout
}

// 获取callee
func (this *component) getCallee() (callee string) {
	callee = conf.GetConfStringValueWithDefault(conffile, "component.callee."+this.componentName, "MC")
	return callee
}

// 获取callee
func (this *component) getCaller() (caller string) {
	caller = conf.GetConfStringValueWithDefault(conffile, "component.caller."+this.componentName, "Oceanus")
	return caller
}

// 获取callee
func (this *component) getUserName() (caller string) {
	return conf.GetConfStringValueWithDefault(conffile, "component.username."+this.componentName, "oceanus")
}

// 获取password
func (this *component) getPwd() (pwd string) {
	pwd = conf.GetConfStringValueWithDefault(conffile, "component.password."+this.componentName, "******")
	return pwd
}

func (this *component) sendRequestV2(interfaceName string, eventId string, reqdata map[string]interface{}) (ReturnCode int, ReturnMessage string, ReturnData interface{}, err error) {
	defer timecost.TimeCost("sendRequestV2", interfaceName, eventId, reqdata)()

	//打包请求
	var senddata string
	if this.makePackFunc != nil {
		senddata, err = this.makePackFunc(interfaceName, eventId, reqdata)
	} else {
		senddata, err = this.makePack(interfaceName, eventId, reqdata)
	}

	if err != nil {
		return -1, err.Error(), nil, err
	}
	//获取请求URL
	var url string
	if this.getUrlFunc != nil {
		url, err = this.getUrlFunc()
	} else {
		url, err = this.getUrl()
	}
	if err != nil {
		return -1, err.Error(), nil, err
	}
	//获取超时时间
	timeout := this.getTimeOut()
	if timeout == 0 {
		timeout = 15 //默认15秒超时
	}
	// 时间戳
	startTimeStamp := util.GetNowTimestamp()
	//发送请求
	req, err := http.NewRequest("POST", url, strings.NewReader(senddata))
	if err != nil {
		return -1, err.Error(), nil, err
	}

	// 上报监控
	msg, ctx := rpcctx.CreateClientMessage(context.Background(),
		rpcctx.WithCallerServerName("galileo"),
		rpcctx.WithCalleeServerName("http-server"),
		rpcctx.WithCalleeRPCName(getRPCName(req, interfaceName)))
	defer rpcctx.PutBackMessage(msg)
	ctx, holder := monitor.StartHTTPClient(ctx, req)
	defer func() {
		// 注意不能简化成defer holder.Report(err)
		holder.Report(ctx, err)
	}()

	devenv := conf.GetConfStringValue("galileo_cc.properties", "scsDevEnv")
	if devenv == "true" {
		log.Debug("CallCvmInternalApi in DevEnv mode!")
		//devUin := configure.GetConfStringValue("emrcc.properties", "devUin")
		//req.Header.Set("X-QCloud-User-ID", devUin)
	}
	http.DefaultClient.Timeout = time.Duration(timeout) * time.Second
	resp, err := http.DefaultClient.Do(req)
	defer func() {
		if resp != nil {
			resp.Body.Close()
		}
	}()
	// 耗时
	timeDelta := util.GetNowTimestamp() - startTimeStamp

	if err != nil {
		log.Error("[component] sendRequest request:", senddata, "\t err:", err)
		return -1, err.Error(), nil, err
	}
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.Error("[component] sendRequest  url:", url, ",request:", senddata, "\t Header:", req.Header, "\t read response err:", err)
		return -1, err.Error(), nil, err
	}
	ret := string(body)
	//ret := "{\"retCode\": 0,\"msg\": \"\"}"
	log.Debug("[component] sendRequest  url:", url, ",request:", senddata, "\t Header:", req.Header, "\t response is: \t", ret, " time delta:", timeDelta)

	data, err := this.unPackResp(ret)
	if err != nil {
		log.Error("[component] sendRequest  request:", senddata, "\t unpack err:", err)
		return -1, err.Error(), nil, err
	}
	ReturnMessage = this.getRetMsg(data)
	ReturnCode, err = this.getRetCode(data)
	if err != nil {
		return -1, ReturnMessage, nil, err
	}
	ReturnData, err = this.getRetData(data)
	if err != nil {
		return ReturnCode, ReturnMessage, nil, err
	}
	return ReturnCode, ReturnMessage, ReturnData, nil
}

// 发送请求
func (this *component) sendRequest(interfaceName string, eventId string, reqdata map[string]interface{}) (retdata interface{}, err error) {
	// 获取请求URL
	url, err := this.getUrl()
	if err != nil {
		return nil, err
	}

	data, err := this.SendRequestByURL(url, interfaceName, eventId, reqdata)
	if err != nil {
		return nil, err
	}

	issucc, err := this.isSucc(data)
	if err != nil {
		return nil, err
	}

	// 业务调用不成功
	if !issucc {
		errmsg := this.getRetMsg(data)
		return nil, errors.New(errmsg)
	}
	retdata, err = this.getRetData(data)
	if err != nil {
		return nil, err
	}
	return retdata, nil
}

func (this *component) SendRequestByURL(url string, interfaceName string, eventId string, reqdata map[string]interface{}) (retdata map[string]interface{}, err error) {
	// 打包请求
	var senddata string
	if this.makePackFunc != nil {
		senddata, err = this.makePackFunc(interfaceName, eventId, reqdata)
	} else {
		senddata, err = this.makePack(interfaceName, eventId, reqdata)
	}

	if err != nil {
		return nil, err
	}

	// 获取超时时间
	timeout := this.getTimeOut()
	if timeout == 0 {
		timeout = 15 // 默认15秒超时
	}

	// 发送请求
	req, err := http.NewRequest("POST", url, strings.NewReader(senddata))
	if err != nil {
		return nil, err
	}
	// Set IP to User-Agent
	req.Header.Set("User-Agent", fmt.Sprintf("Galileo/1.0 IP:%s", util.GetLocalAddressMust()))
	http.DefaultClient.Timeout = time.Duration(time.Duration(timeout) * time.Second)

	// 上报监控
	msg, ctx := rpcctx.CreateClientMessage(context.Background(),
		rpcctx.WithCallerServerName("galileo"),
		rpcctx.WithCalleeServerName("http-server"),
		rpcctx.WithCalleeRPCName(getRPCName(req, interfaceName)))
	defer rpcctx.PutBackMessage(msg)
	ctx, holder := monitor.StartHTTPClient(ctx, req)
	defer func() {
		// 注意不能简化成defer holder.Report(err)
		holder.Report(ctx, err)
	}()

	resp, err := http.DefaultClient.Do(req)
	defer func() {
		if resp != nil {
			resp.Body.Close()
		}
	}()
	if err != nil {
		log.Error("[component] sendRequest request:", senddata, "\t err:", err)
		return nil, err
	}
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.Error("[component] sendRequest  url:", url, ",request:", senddata, "\t read response err:", err)
		return nil, err
	}
	ret := string(body)

	log.Debug("[component] sendRequest  url:", url, ",request:", senddata, "\t response is: \t", ret)
	data, err := this.unPackResp(ret)
	if err != nil {
		log.Error("[component] sendRequest request:", senddata, "\t unpack err:", err)
		return nil, err
	}
	return data, nil
}

// 根据自定义url发送请求
func (this *component) sendRequestByUrl(urlSuffix, interfaceName string, eventId string, reqdata map[string]interface{}, reqMethod string) (retdata interface{}, err error) {
	return this.sendRequestByUrlWithContext(context.Background(), urlSuffix, interfaceName, eventId, reqdata, reqMethod)
}

func (this *component) sendRequestByUrlWithContext(ctx context.Context, urlSuffix, interfaceName string,
	eventId string, reqdata map[string]interface{}, reqMethod string) (retdata interface{}, err error) {

	//打包请求
	var senddata string
	if this.makePackFunc != nil {
		senddata, err = this.makePackFunc(interfaceName, eventId, reqdata)
	} else {
		senddata, err = this.makePack(interfaceName, eventId, reqdata)
	}

	if err != nil {
		return nil, err
	}
	var url string
	if strings.HasPrefix(urlSuffix, "http://") {
		url = urlSuffix
	} else {
		//获取请求URL
		url, err = this.getUrl()
		if err != nil {
			return nil, err
		}
		url = url + urlSuffix
	}
	//获取超时时间
	timeout := 30

	var req *http.Request
	if reqMethod == "" || strings.ToUpper(reqMethod) == "POST" {
		// 发送 POST 请求
		req, err = http.NewRequest("POST", url, strings.NewReader(senddata))
		if err != nil {
			return nil, errorcode.InternalErrorCode_NET.NewWithErr(err)
		}
	} else { // GET, HEAD, etc.
		req, err = http.NewRequest(strings.ToUpper(reqMethod), url, strings.NewReader(""))
		if err != nil {
			return nil, errorcode.InternalErrorCode_NET.NewWithErr(err)
		}
	}
	req.Header.Set("User-Agent", fmt.Sprintf("Galileo/1.0 IP:%s", util.GetLocalAddressMust()))
	req.Header.Add("content-type", "application/json")

	// 上报监控
	msg, ctx := rpcctx.CreateClientMessage(ctx,
		rpcctx.WithCallerServerName("galileo"),
		rpcctx.WithCalleeServerName("http-server"),
		rpcctx.WithCalleeRPCName(getRPCName(req, interfaceName)))
	defer rpcctx.PutBackMessage(msg)
	ctx, holder := monitor.StartHTTPClient(ctx, req)
	defer func() {
		// 注意不能简化成defer holder.Report(err)
		holder.Report(ctx, err)
	}()

	http.DefaultClient.Timeout = time.Duration(time.Duration(timeout) * time.Second)
	resp, err := http.DefaultClient.Do(req)
	defer func() {
		if resp != nil {
			resp.Body.Close()
		}
	}()
	if err != nil {
		log.Error("[component] sendRequest request:", senddata, "\t err:", err)
		return nil, errorcode.InternalErrorCode_NET.NewWithErr(err)
	}
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.Error("[component] sendRequest  url:", url, ",request:", senddata, "\t read response err:", err)
		return nil, errorcode.InternalErrorCode_NET.NewWithErr(err)
	}
	ret := string(body)

	log.Debug("[component] sendRequest  url:", url, ",request:", senddata, "\t response is: \t", ret)
	data, err := this.unPackResp(ret)
	if err != nil {
		log.Error("[component] sendRequest request:", senddata, "\t unpack err:", err)
		return nil, err
	}
	issucc, err := this.isSucc(data)
	if err != nil {
		return nil, err
	}
	//业务调用不成功
	if !issucc {
		errmsg := this.getRetMsg(data)
		return nil, errorcode.InternalErrorCode_Component.NewWithInfo(errmsg, nil)
	}
	retdata, err = this.getRetData(data)
	if err != nil {
		return nil, err
	}
	return retdata, nil
}

func (this *component) isSucc(response map[string]interface{}) (ok bool, err error) {
	retcode, err := this.getRetCode(response)
	if err != nil {
		return false, nil
	}
	if retcode == succ {
		return true, nil
	}
	return false, nil
}

// 获取返回码
func (this *component) getRetCode(response map[string]interface{}) (retcode int, err error) {
	code, ok := response[this.keyRetcode]
	if !ok {
		return -1, errorcode.InternalErrorCode_Component.NewWithInfo(this.keyRetcode+" not exist in map", nil)
	}
	v, err := util.GetValueFromEle(reflect.ValueOf(code))
	if err != nil {
		return -1, errorcode.InternalErrorCode_Component.NewWithErr(err)
	}
	// 這裡這樣處理， 主要是因為go在將Json轉化為Map的時候，會將Int定義為最寬的Float型
	index := strings.Index(v, ".")
	if index > 0 {
		v = v[0:index]
	}
	retcode, err = strconv.Atoi(v)
	if err != nil {
		return -1, errorcode.InternalErrorCode_Component.NewWithErr(err)
	}
	return retcode, nil
}

// 获取返值
func (this *component) getRetMsg(response map[string]interface{}) (retmsg string) {
	msg, ok := response[this.keyRetmsg]
	if !ok {
		return ""
	}
	return msg.(string)
}

// 获取返回数据
func (this *component) getRetData(response map[string]interface{}) (data interface{}, err error) {
	data, ok := response[this.keyData]
	if !ok {
		return "", errorcode.InternalErrorCode_Component.NewWithInfo(this.keyData+" not exist in map", nil)
	}
	return data, nil
}

// 解包
func (this *component) unPackResp(resp string) (response map[string]interface{}, err error) {
	if len(resp) == 0 {
		return nil, errorcode.InternalErrorCode.NewWithInfo("resp msg is empty", nil)
	}

	d := json.NewDecoder(strings.NewReader(resp))
	d.UseNumber()
	if err := d.Decode(&response); err != nil {
		log.Error("unPackResp Decode err:", err)
		return nil, errorcode.InternalErrorCode.NewWithErr(err)
	}

	return response, nil
}

// 封包
func (this *component) makePack(interfaceName string, eventId string, params map[string]interface{}) (req string, err error) {

	reqmap := make(map[string]interface{})
	imap := make(map[string]interface{})

	imap["interfaceName"] = interfaceName
	imap["para"] = params

	reqmap["version"] = "4.0" // nolint
	reqmap["caller"] = this.getCaller()
	reqmap["password"] = this.getPwd()
	reqmap["callee"] = this.getCallee()
	reqmap["username"] = this.getUserName()
	reqmap["eventId"] = eventId
	reqmap["timestamp"] = strconv.FormatInt(util.GetNowTimestamp(), 10)
	reqmap["interface"] = imap

	data, err := json.Marshal(reqmap)
	if err != nil {
		return "", errorcode.InternalErrorCode.NewWithErr(err)
	}
	return string(data), nil
}

func (o *component) MockForTestEnvIfScsDevEnv(appId int64, uin, subAccountUin string) (appIdMocked int64, uinMocked, subAccountUinMocked string, err error) {
	appIdMocked, uinMocked, subAccountUinMocked = appId, uin, subAccountUin

	if o.disableMock {
		return
	}

	isDev := conf.GetConfStringValue("galileo_cc.properties", "scsDevEnv")
	if isDev != "true" {
		return
	}

	appIdStr := conf.GetConfStringValue("galileo_cc.properties", "scsDevEnvTestAppId")
	if appIdStr != "" {
		appId, err := strconv.ParseInt(appIdStr, 10, 32)
		if err != nil {
			return appIdMocked, uinMocked, subAccountUinMocked, errorcode.NewStackError(errorcode.InternalErrorCode, "", err)
		}
		appIdMocked = appId
	}

	uin = conf.GetConfStringValue("galileo_cc.properties", "scsDevEnvTestUin")
	if uin != "" {
		uinMocked = uin //替换AppId
	}

	subAccountUin = conf.GetConfStringValue("galileo_cc.properties", "scsDevEnvTestSubAccountUin")
	if subAccountUin != "" {
		subAccountUinMocked = subAccountUin
	}
	return
}

func getRPCName(req *http.Request, interfaceName string) string {
	if interfaceName != "" {
		return interfaceName
	}
	if req == nil || req.URL == nil {
		return "httpCall"
	}
	if req.URL.Path != "" {
		return req.URL.Path
	}
	return "httpCall"
}
