package component

import (
	log "tencentcloud.com/tstream_galileo/src/common/logger"
	//"tencentcloud.com/tstream_galileo/src/common/util"
	"encoding/json"
	"fmt"
	"github.com/juju/errors"
	"math/rand"
	//"reflect"
	"strconv"
)

/**
访问CGW
*/
type cgateway struct {
	component
}

var g_cgateway *cgateway

func init() {
	g_cgateway = &cgateway{}
	g_cgateway.componentName = "cgateway"
	g_cgateway.keyRetcode = "returnCode"
	g_cgateway.keyRetmsg = "returnMessage"
	g_cgateway.keyData = "data"
}



func NewComponentCgateway(rid int) *cgateway {
	g_cgateway.regionId = rid
	return g_cgateway
}


type VPCSubnetInfo struct {
	SubnetName     string `json:"subnetName"`
	SubnetId       int64  `json:"subnetId"`
	UniqSubnetId   string `json:"uniqSubnetId"`
	VpcId          int64  `json:"vpcId"`
	UniqVpcId      string `json:"uniqVpcId"`
	SubnetCIDR     string `json:"subnetCIDR"`
	RtbName        string `json:"rtbName"`
	RtbId          int64  `json:"rtbId"`
	UniqRtbId      string `json:"uniqRtbId"`
	VpcCIDR        string `json:"vpcCIDR"`
	VpcName        string `json:"vpcName"`
	ZoneId         int64  `json:"zoneId"`
	Broadcast      int    `json:"broadcast"`
	CreateTime     string `json:"createTime"`
	TotalIPNum     int    `json:"totalIPNum"`
	AvailableIPNum int    `json:"availbaleIPNum"`
	VpcDevices     int    `json:"vpcDevices"`
}

type VPCSubnetList struct {
	TotalNum int              `json:"totalNum"`
	Detail   []*VPCSubnetInfo `json:"detail"`
}

func (this *cgateway) GetVPCListByAppId(uin, vpcid, subnetid string, appid int64) (vpcinfo []*VPCSubnetInfo, err error) {
	if uin == "" || appid == 0 {
		return nil, errors.New(fmt.Sprintf("uin or appid is NULL"))
	}

	if vpcid == "" && subnetid == "" {
		return nil, errors.New(fmt.Sprintf("vpcid or subnetid is NULL"))
	}

	reqData := make(map[string]interface{})
	reqData["appId"] = appid
	reqData["uin"] = uin
	if vpcid != "" {
		reqData["vpcId"] = vpcid
	}
	if subnetid != "" {
		reqData["subnetId"] = subnetid
	}

	log.Debug(fmt.Sprintf("query vpc subnet list, appid: %d, uin: %s, vpcid: %s, subnetid: %s", appid, uin, vpcid, subnetid))
	ifName := "qcloud.QVPC.getVPCSubnetListByAppId"
	eventId := strconv.Itoa(rand.Intn(10000000))
	retData, err := this.sendRequest(ifName, eventId, reqData)
	if err != nil {
		log.Error(eventId, " call: ", ifName, ", errors:", err.Error())
		return nil, err
	}

	if strdata, err := json.Marshal(retData); err == nil {
		var sblist VPCSubnetList
		err = json.Unmarshal([]byte(strdata), &sblist)
		if err != nil {
			return nil, err
		}
		vpcinfo = sblist.Detail
	} else {
		return nil, err
	}

	return vpcinfo, nil

}
