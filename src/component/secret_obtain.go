package component
import (
	"strconv"
	"tencentcloud.com/tstream_galileo/src/common/util"
	"encoding/json"
	log "tencentcloud.com/tstream_galileo/src/common/logger"
"errors"
)

/**
平台获得secretId和secretKey的接口
*/

type SecretObtainService struct {
	component
}

var gSecretObtainService *SecretObtainService

func init() {
	gSecretObtainService = new(SecretObtainService)
	gSecretObtainService.componentName = "cgateway"
	gSecretObtainService.keyRetcode = "returnCode"
	gSecretObtainService.keyRetmsg = "returnMessage"
	gSecretObtainService.keyData = "data"
	gSecretObtainService.makePackFunc = gSecretObtainService.makePack
}

func NewSecretObtainService() *SecretObtainService {
	gSecretObtainService.regionId=0 ////cgw使用1
	return gSecretObtainService
}

//eventId转换成int64发送
func (this *SecretObtainService) makePack (interfaceName string, eventId string, params map[string]interface{}) (req string, err error){
	reqmap := make(map[string]interface{})
	imap := make(map[string]interface{})

	imap["interfaceName"] = interfaceName
	imap["para"] = params

	reqmap["version"] = "1.0"
	reqmap["caller"] = "emr_darwin"
	reqmap["password"] = this.getPwd()
	reqmap["callee"] = "api"
	reqmap["username"] = this.getUserName()
	eventIdInt,err := strconv.ParseInt(eventId, 10, 64)
	if err != nil {
		return "", err
	}
	reqmap["eventId"] =eventIdInt
	reqmap["timestamp"] = util.GetNowTimestamp()
	reqmap["interface"] = imap

	data, err := json.Marshal(reqmap)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

func (this *SecretObtainService) QueryKey(uin string,appId int64)(secretId,secretKey string,err error){
	if len(uin)<=0 || appId <=0 {
		log.Error("[SecretObtain->QueryKey] uin len or appId len <=0")
		err =errors.New("[SecretObtain->QueryKey] uin len or  appId len <=0")
		return "","",err
	}

	//构造发送数据
	reqData := make(map[string]interface{})
	reqData["uin"]=uin
	reqData["secretProjectId"]=appId
	//固定参数
	reqData["platform"]=1
	reqData["system"]=10

	eventId := util.GetNowTimestamp()
	interfaceName := "qcloud.Qsecret.queryKey"

	retData, err := this.sendRequest(interfaceName, strconv.FormatInt(eventId, 10), reqData)
	if err != nil {
		log.Error("[SecretObtain->QueryKey] eventId:", eventId, " call: ", interfaceName, ", errors:", err.Error())
		return "","",err
	}

	jsondata, err := json.Marshal(retData)
	this.checkErr(err)
	rsp :=&QueryKeyResp{}
	err = json.Unmarshal(jsondata, &rsp)
	this.checkErr(err)

	keys:=rsp.IdKeys
	if len(keys) ==0 {
		log.Error("[SecretObtain->QueryKey]  return IdKeys is empty")
		err =errors.New("[SecretObtain->QueryKey]  return IdKeys is empty")
		return "","",err
	}

	for _,key := range keys{
		if key.Status ==2 {//只有status是2时才表示secretId 和secretKey 可用
			return key.SecretId,key.SecretKey,nil
		}
	}

	log.Error("[SecretObtain->QueryKey]  No secret Id and Key can be used (not key status ==2) ")
	err =errors.New("[SecretObtain->QueryKey]  No secret Id and Key can be used (not key status ==2) ")
	return "","", err
}

type QueryKeyResp struct {
	Uin				string
	SecretProjectId int64
	IdKeys 			[]KeyRsp
}

type KeyRsp struct {
	SecretId 	string
	SecretKey	string
	CreateTime	int64
	Status		int
	Source		int
}
