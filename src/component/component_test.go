package component

import (
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func Test_getRPCName(t *testing.T) {
	{
		rpcName := getRPCName(nil, "")
		assert.Equal(t, "httpCall", rpcName)
	}
	{
		rpcName := getRPCName(httptest.NewRequest("POST", "/doLike", nil), "doLike")
		assert.Equal(t, "doLike", rpcName)
	}
	{
		rpcName := getRPCName(httptest.NewRequest("POST", "http://127.0.0.1/publish", nil), "")
		assert.Equal(t, "/publish", rpcName)
	}
	{
		rpcName := getRPCName(httptest.NewRequest("POST", "https://127.0.0.1/uncare", nil), "")
		assert.Equal(t, "/uncare", rpcName)
	}
	{
		rpcName := getRPCName(httptest.NewRequest("POST", "http://www.woa.com/care", nil), "")
		assert.Equal(t, "/care", rpcName)
	}
	{
		rpcName := getRPCName(httptest.NewRequest("POST", "http://127.0.0.1:8090/run?foo=bar", nil), "")
		assert.Equal(t, "/run", rpcName)
	}
}
