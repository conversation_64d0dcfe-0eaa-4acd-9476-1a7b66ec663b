package component_test

import (
	"math/rand"
	"tencentcloud.com/tstream_galileo/src/component"
	"testing"
	"time"
	//"strconv"
)

func TestSendApiHttps(t *testing.T) {
	apiService := component.GetApiService()
	appId := int64(1251828434)
	uin := "2173089919"

	//domain:="https://cvm.api.qcloud.com/v2/index.php"
	//domain:="https://vpc.api.qcloud.com/v2/index.php"
	//domain:="https://cdb.api.qcloud.com/v2/index.php"
	domain := "https://cdb.api.tencentyun.com/v2/index.php"

	action := "ModifyCdbInstanceName"

	//构造业务数据
	//必传参数
	apiParaMap := make(map[string]string)
	apiParaMap["Region"] = component.API_REGION_MAP[1]
	apiParaMap["cdbInstanceId"] = "cdb-r22bjb1b"
	apiParaMap["cdbInstanceName"] = "中文测试"

	r1 := rand.New(rand.NewSource(time.Now().UnixNano()))
	envenId := r1.Intn(65533) + 1
	rsp, err := apiService.SendApiHttps(appId, uin, domain, action, envenId, apiParaMap)

	if nil != err {
		t.Error("rsp err:", err)
		return
	}
	t.Log("rsp:", rsp)
}
