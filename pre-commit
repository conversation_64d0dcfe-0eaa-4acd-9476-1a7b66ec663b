#!/bin/bash
set -e

#install=1,uninstall=2,temporaryClose=3
CMD=1

USAGE="Usage: `basename $0` [--install|--uninstall|--temporaryClose]"
while true; do
    case $1 in
        --install)
            CMD=1
            ;;
        --uninstall)
            CMD=2
            ;;
        --temporaryClose)
            CMD=3
            ;;
        *)
            break
            ;;
    esac
    shift
done

HOME=`cd $(dirname $0) && pwd`
cd $HOME

pre_commit=pre-commit.template
git_pre_commit=.git/hooks/pre-commit

if [ $CMD -eq '1' ]; then
    chmod +x $pre_commit 
    ln -s ../../$pre_commit $git_pre_commit
elif [ $CMD -eq '2' ]; then
    rm $git_pre_commit
else
    touch .git/hooks/pre-commit.temporary.close
fi
