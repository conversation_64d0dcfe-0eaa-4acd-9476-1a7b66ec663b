CREATE TABLE if not exists `Role` (
  `Id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '角色编码,对应 RoleAuth的permission',
  `SerialId` varchar(25) CHARACTER SET utf8mb4 NOT NULL,
  `Name` varchar(255) NOT NULL,
  `AppId` bigint NOT NULL,
  `OwnerUin` varchar(25) NOT NULL,
  `CreatorUin` varchar(25) NOT NULL,
  `Status` tinyint NOT NULL DEFAULT '1' COMMENT '-2 已删除；1 生效',
  `Type` tinyint NOT NULL DEFAULT '0' COMMENT '0 内置 1 非内置',
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `SerialId` (`SerialId`),
  KEY `AppId` (`AppId`,`Status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='角色表';

CREATE TABLE if not exists `Permission` (
  `Id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `Name` varchar(255) NOT NULL,
  `ControllerNameList` text NOT NULL COMMENT 'galileo里的Controller名称列表，逗号隔开',
  `Status` tinyint NOT NULL DEFAULT '1' COMMENT '1 生效 -2 删除',
  `Type` varchar(100) NOT NULL COMMENT '权限归类, Job/作业管理 ',
`Selectable` tinyint NOT NULL DEFAULT '1' COMMENT '是否可选',
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='权限表';

CREATE TABLE if not exists `RolePermission` (
  `Id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `RoleId` int NOT NULL COMMENT 'Role表的id',
  `PermissionId` int NOT NULL COMMENT 'Permission的id',
  `Status` tinyint NOT NULL DEFAULT '1' COMMENT '1 生效 -2 删除',
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

alter table SynVersion add column TableName VARCHAR(255) not null default 'Administrator' comment '同步表名';

alter table RoleAuth modify `Permission` bigint(20) NOT NULL COMMENT '操作权限: 关联role表的 id';

-- 初始化内置角色  id 为0 插入不进去， 需要手动修改
INSERT INTO `Role` (`Id`, `SerialId`, `Name`, `AppId`, `OwnerUin`, `CreatorUin`, `Status`, `Type`)
VALUES
	(4, "role-kyt347et", '超级管理员', 0, '0', '0', 1, 0),
	(1, "role-02us6d86", '空间管理员', 0, '0', '0', 1, 0),
	(2, "role-e07v5ok2", '开发者', 0, '0', '0', 1, 0),
	(3, "role-ioidtr4n", '访客', 0, '0', '0', 1, 0);
update Role set Id = 0 where Id = 4;

-- 初始化 权限
INSERT INTO `Permission` (`Id`, `Name`, `ControllerNameList`, `Status`, `Type`, `Selectable`)
VALUES
	(1, '新建作业', 'CreateJob', 1, 'Job/作业管理', 1),
	(2, '删除作业', 'DeleteJobs,DeleteJobConfigs', 1, 'Job/作业管理', 1),
	(3, '运行作业', 'RunJobs', 1, 'Job/作业管理', 1),
	(4, '停止作业', 'StopJobs', 1, 'Job/作业管理', 1),
	(5, '开发/调试作业', 'ModifyJob,CreateDebugJob,CheckSqlGrammar,CreateTableConfig,DeleteTableConfig,ModifyDraftConfig,CreateJobConfig,TriggerJobSavepoint,CheckSavepoint,CreateJobScaleRule,ModifyJobScaleRule,DeleteJobScaleRule,CreateEtlPreCheck,CheckConnectivity,DescribeFlinkSql,DescribeConnections,CheckEtlTypeMapping,CreateConnection,CheckConnectionName,DeleteConnections,ModifyConnection', 1, 'Job/作业管理', 1),
	(6, '查看作业', 'DescribeJobConfigs,DescribeJobs,DescribeTreeJobs,DescribeJobCustomMetricEnabled,DescribeJobSavepoint,DescribeJobScaleRules,DescribeJobScaleEvents,DescribeConnSubItemSchema,DescribeEtlPreCheckStatus,DescribeConnectionSubItems', 1, 'Job/作业管理', 1),
	(7, '监控告警', 'DescribeJobEvents,DescribeJobJobManagerMetric,DescribeJobTaskManagerMetric,DescribeJobTaskMetric,DescribeJobCheckpointMetric', 1, 'Job/作业管理', 1),
	(8, '新建集群', 'CreateCluster,CreateClusterInstance', 1, 'Cluster/计算资源', 0),
	(9, '销毁集群', 'DeleteCluster,DeleteClusterInstance', 1, 'Cluster/计算资源', 0),
	(10, '修改集群信息', 'ModifyLoginSettings,ModifyResourceTags,ModifyCluster,ModifyClusterDNS', 1, 'Cluster/计算资源', 0),
	(11, '变配/续费', 'ModifyClusterInstance,ModifyClusterCls,CreateProperClsLogset,CreateProperClsTopic,ModifySubnet,RenewClusterInstance', 1, 'Cluster/计算资源', 0),
	(12, '查看集群', 'DescribeClusters', 1, 'Cluster/计算资源', 1),
	(13, '创建依赖资源', 'CreateResource,CreateResourceConfig', 1, 'Resource/依赖管理', 1),
	(14, '删除依赖资源', 'DeleteResources,DeleteResourceConfigs', 1, 'Resource/依赖管理', 1),
	(15, '编辑依赖资源', 'ModifyResource', 1, 'Resource/依赖管理', 1),
	(16, '查看依赖资源', 'DescribeResourceConfigs,DescribeResources,DescribeResourceRelatedJobs,DescribeTreeResources', 1, 'Resource/依赖管理', 1),
	(17, '创建/删除元数据库', 'CreateMetaDatabase,DeleteMetaDatabases', 1, 'Meta/元数据管理', 1),
	(18, '创建/删除全局变量', 'CreateVariable,DeleteVariables', 1, 'Meta/元数据管理', 1),
	(19, '查看元数据', 'DescribeMetaTable,DescribeMetaReferences,DescribeMetaDatabases,DescribeMetaCatalogs,DescribeVariables,DescribeExternalMetaTables,DescribeExternalMetaDatabases,DescribeCatalogs,DescribeExternalMetaCatalog,DescribeCatalogRelatedJobs,DescribeSupportCatalogs', 1, 'Meta/元数据管理', 1),
	(20, '增加/删除空间成员', 'CreateWorkSpaceUser,DeleteWorkSpaceUser', 1, 'MemberManagement/成员管理', 1),
	(21, '修改空间成员角色', 'ModifyWorkSpaceUser', 1, 'MemberManagement/成员管理', 1),
	(22, '增加/删除空间', 'CreateWorkSpace,DeleteWorkSpace', 1, 'WorkSpace/工作空间', 0),
	(23, '修改空间属性', 'ModifyWorkSpace', 1, 'WorkSpace/工作空间', 0),
	(24, '集群和空间关联/解除关联', 'CreateCorrelation,DeleteCorrelation', 1, 'WorkSpace/工作空间', 0),
	(25, '编辑超级管理员', 'ModifyAdministrator', 1, 'Administer/角色权限', 0),
	(26, '操作目录', 'CreateFolder,ModifyFolder,DeleteFolders', 1, 'Fold/目录管理', 1),
	(28, '复制作业', 'CopyJobs', 1, 'Job/作业管理', 1),
	(29, '编辑元数据', 'ModifyExternalMetaCatalog', 1, 'Meta/元数据管理', 1),
	(30, '创建/删除元数据表', 'CreateMetaTable,DeleteMetaTables', 1, 'Meta/元数据管理', 1),
	(31, '创建/删除元数据', 'DeleteExternalMetaCatalogs', 1, 'Meta/元数据管理', 1),
	(32, '创建角色', 'CreateRole', 1, 'RoleAuth/角色权限', 0),
	(33, '删除角色', 'DeleteRole', 1, 'RoleAuth/角色权限', 0),
	(34, '修改角色', 'ModifyRole,ModifyRoleName', 1, 'RoleAuth/角色权限', 0);



-- 初始化 RolePermission
INSERT INTO `RolePermission` (`Id`, `RoleId`, `PermissionId`, `Status`)
VALUES
	(1, 0, 30, 1),
	(2, 0, 31, 1),
	(3, 0, 1, 1),
	(4, 0, 2, 1),
	(5, 0, 3, 1),
	(6, 0, 4, 1),
	(7, 0, 5, 1),
	(8, 0, 6, 1),
	(9, 0, 7, 1),
	(10, 0, 8, 1),
	(11, 0, 9, 1),
	(12, 0, 10, 1),
	(13, 0, 11, 1),
	(14, 0, 12, 1),
	(15, 0, 13, 1),
	(16, 0, 14, 1),
	(17, 0, 15, 1),
	(18, 0, 16, 1),
	(19, 0, 17, 1),
	(20, 0, 18, 1),
	(21, 0, 19, 1),
	(22, 0, 20, 1),
	(23, 0, 21, 1),
	(24, 0, 22, 1),
	(25, 0, 23, 1),
	(26, 0, 24, 1),
	(27, 0, 25, 1),
	(28, 0, 26, 1),
	(29, 0, 28, 1),
	(30, 0, 29, 1),
	(31, 1, 12, 1),
	(32, 1, 1, 1),
	(33, 1, 2, 1),
	(34, 1, 3, 1),
	(35, 1, 4, 1),
	(36, 1, 5, 1),
	(37, 1, 6, 1),
	(38, 1, 7, 1),
	(39, 1, 28, 1),
	(40, 1, 13, 1),
	(41, 1, 14, 1),
	(42, 1, 15, 1),
	(43, 1, 16, 1),
	(44, 1, 20, 1),
	(45, 1, 21, 1),
	(46, 1, 26, 1),
	(47, 1, 17, 1),
	(48, 1, 18, 1),
	(49, 1, 19, 1),
	(50, 1, 29, 1),
	(51, 1, 30, 1),
	(52, 1, 31, 1),
	(53, 2, 12, 1),
	(54, 2, 1, 1),
	(55, 2, 2, 1),
	(56, 2, 3, 1),
	(57, 2, 4, 1),
	(58, 2, 5, 1),
	(59, 2, 6, 1),
	(60, 2, 7, 1),
	(61, 2, 28, 1),
	(62, 2, 13, 1),
	(63, 2, 14, 1),
	(64, 2, 15, 1),
	(65, 2, 16, 1),
	(66, 2, 26, 1),
	(67, 2, 17, 1),
	(68, 2, 18, 1),
	(69, 2, 19, 1),
	(70, 2, 29, 1),
	(71, 2, 30, 1),
	(72, 2, 31, 1),
	(73, 3, 12, 1),
	(74, 3, 6, 1),
	(75, 3, 7, 1),
	(76, 3, 16, 1),
	(77, 3, 19, 1),
	(78, 0, 32, 1),
	(79, 0, 33, 1),
	(80, 0, 34, 1);

UPDATE ConfigurationCenter SET ConfigurationValue = REPLACE(ConfigurationValue, '5.2.0', '6.6.0') WHERE ConfigurationKey= 'oceanus.debug.kubernetes.container.image';

update `ConfigurationCenter` set `ConfigurationValue`
	= '%java%  -Duser.timezone=GMT+08 -Dlog.level=INFO %classpath% %jvmmem% %jvmopts% %logging% %class% %args%'
	where `ConfigurationKey` = 'kubernetes.container-start-command-template' limit 1;

update `ConfigurationCenter` set `ConfigurationValue` = '-Duser.timezone=GMT+08  -Dlog.level=INFO'
	where `ConfigurationKey` = 'containerized.taskmanager.env.JVM_ARGS' limit 1;

update `ConfigurationCenter` set `ConfigurationValue` = '-Duser.timezone=GMT+08 -Dlog.level=INFO'
	where `ConfigurationKey` = 'containerized.master.env.JVM_ARGS' limit 1;

alter table JobConfig ADD COLUMN `LogLevel` varchar(8) NOT NULL comment '作业运行日志级别';

alter table JobConfig ADD COLUMN `AutoRecover` int(9) NOT NULL default 1 comment '作业失败自动恢复';