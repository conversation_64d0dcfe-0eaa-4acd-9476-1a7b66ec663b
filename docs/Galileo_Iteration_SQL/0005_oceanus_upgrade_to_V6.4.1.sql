-- 血缘
CREATE TABLE `MetaTableIdentifierRef` (
  `Id` bigint NOT NULL AUTO_INCREMENT,
  `IdentifierId` varchar(100) DEFAULT NULL,
  `TableId` bigint DEFAULT NULL,
  `Status` int NOT NULL DEFAULT '1' COMMENT '1：正常，-2：删除',
  `CreateTime` datetime DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;


CREATE TABLE `MetaTableIdentifier` (
  `Id` bigint NOT NULL AUTO_INCREMENT,
  `SerialId` varchar(20) DEFAULT NULL,
  `StableOptions` varchar(500) DEFAULT NULL,
  `UnstableOptions` varchar(500) DEFAULT NULL,
  `Status` int NOT NULL DEFAULT '1' COMMENT '1：正常，-2：删除',
  `Type` varchar(100) DEFAULT NULL,
  `Region` varchar(20) DEFAULT NULL COMMENT '地域',
  `Uin` varchar(20) DEFAULT NULL,
  `SubUin` varchar(20) DEFAULT NULL,
  `AppId` bigint(20) DEFAULT NULL,
  `ItemSpaceId` bigint(20) NOT NULL DEFAULT '0' COMMENT '项目空间ID',
  `CreateTime` datetime DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;


CREATE TABLE `MetaTableLineage` (
  `Id` bigint NOT NULL AUTO_INCREMENT,
  `JobConfigId` bigint DEFAULT NULL,
  `SourceId` bigint DEFAULT NULL,
  `SinkId` bigint DEFAULT NULL,
  `SourceRef` varchar(100) DEFAULT NULL,
  `SinkRef` varchar(100) DEFAULT NULL,
  `Status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '1：正常，-2：删除',
  `SourceType` tinyint(4) NOT NULL DEFAULT '1' COMMENT '1: 流表，2：维表',
  `Region` varchar(20) DEFAULT NULL COMMENT '地域',
  `Uin` varchar(20) DEFAULT NULL,
  `SubUin` varchar(20) DEFAULT NULL,
  `AppId` bigint(20) DEFAULT NULL,
  `CreateTime` datetime DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;


ALTER TABLE MetastoreTable add column SerialId varchar(20);
ALTER TABLE MetastoreTable Add column Status tinyint(4) DEFAULT 1 COMMENT '1：正常，-2：删除';

ALTER TABLE SystemConnector add column StableOptions varchar(100);
ALTER TABLE SystemConnector add column UnstableOptions varchar(100);
ALTER TABLE SystemConnector Add column IdentExtractType tinyint(4) DEFAULT 2 COMMENT '1：Mapping，2：Unmapping';

-- 表初始化
update SystemConnector a  set a.StableOptions ='["topic","properties.bootstrap.servers"]' , a.IdentExtractType=1 where`Options`  like '%kafka%' ;
update SystemConnector a  set a.StableOptions ='["hosts","queue"]', a.IdentExtractType=1 where `Options` like '%cmq%' ;
update SystemConnector a  set a.StableOptions ='["host","port","queue"]', a.IdentExtractType=1 where `Options` like '%rabbitmq%' ; -- t
update SystemConnector a  set a.StableOptions ='["hostname","port","database-name","table-name"]', a.IdentExtractType=1 where `Options` like '%mysql-cdc%' ;
update SystemConnector a  set a.StableOptions ='["topic","properties.bootstrap.servers"]', a.IdentExtractType=1 where `Options` like '%tdsql-subscribe%' ; -- t
update SystemConnector a  set a.StableOptions ='["nodes","database"]', a.IdentExtractType=1 where `Options` like '%redis%' ;
update SystemConnector a  set a.StableOptions ='["hosts","database","collection"]', a.IdentExtractType=1 where `Options` like '%mongodb-cdc%' ;
update SystemConnector a  set a.StableOptions ='["uri","database","collection"]', a.IdentExtractType=1 where `Options` like '%mongodb%' and `Options` not like '%mongodb-cdc%';
update SystemConnector a  set a.StableOptions ='["hostname","port","database-name","table-name"]', a.IdentExtractType=1 where `Options` like '%postgres-cdc%' ;
update SystemConnector a  set a.StableOptions ='["table-name","zookeeper.quorum"]', a.IdentExtractType=1 where `Options` like '%hbase-1.4%' ;
update SystemConnector a  set a.StableOptions ='["table-name","zookeeper.quorum"]', a.IdentExtractType=1 where `Options` like '%hbase-2.2%' ;
update SystemConnector a  set a.StableOptions ='["fenodes","table.identifier"]', a.IdentExtractType=1 where `Options` like '%doris%' ;
update SystemConnector a  set a.StableOptions ='["hive-version","hive-database"]', a.IdentExtractType=1 where `Options` like '%hive%' ;
update SystemConnector a  set a.StableOptions ='["url","database-name","table-name"]', a.IdentExtractType=1 where `Options` like '%clickhouse%' ;
update SystemConnector a  set a.StableOptions ='["kudu.masters","kudu.table"]', a.IdentExtractType=1 where `Options` like '%kudu%' ;
update SystemConnector a  set a.StableOptions ='["url","table-name"]', a.IdentExtractType=1 where `Options` like '%jdbcPG%' ;
update SystemConnector a  set a.StableOptions ='["hosts","index","document-type"]', a.IdentExtractType=1 where `Options` like '%elasticsearch-6%' ;
update SystemConnector a  set a.StableOptions ='["hosts","index"]', a.IdentExtractType=1 where `Options` like '%elasticsearch-7%' ;
update SystemConnector a  set a.StableOptions ='["hosts","index"]', a.IdentExtractType=1 where `Options` like '%elasticsearch%' and `Options` like "%7%" and `Options` not like '%elasticsearch-%' ;
update SystemConnector a  set a.StableOptions ='["hosts","index"]', a.IdentExtractType=1 where `Options` like '%elasticsearch%' and `Options` like "%6%" and `Options` not like '%elasticsearch-%' ;
update SystemConnector a  set a.StableOptions ='["url","table-name"]', a.IdentExtractType=1 where `Options` like '%jdbc%' ;
update SystemConnector a  set a.StableOptions ='["path"]', a.IdentExtractType=1 where `Options` like '%filesystem%' ;
update SystemConnector a  set a.StableOptions ='["table-name","zookeeper.quorum"]' , a.IdentExtractType=1 where a.`Options` like '%hbase%' and a.Status=1; -- 旧版本的hbase options格式
update SystemConnector a  set a.StableOptions ='["path"]',a.IdentExtractType=1 where `Options` like '%hudi%' and Status=1;  -- 6.0新增的connector

-- Flink-1.14支持写入hdfs，但是不支持写入cos，所以需要添加如下的配置
INSERT INTO `SystemConnector` ( `ResourceId`, `VersionId`, `FlinkVersion`, `Options`, `Status`, `Region`, `StableOptions`, `IdentExtractType`) VALUES ('',-2,'Flink-1.14','[{\"Options\":[{\"Key\":\"connector/connector.type\",\"Value\":\"filesystem\"}]}]',1,'ap-guangzhou','[\"path\"]',1);

-- 自动扩缩容
Alter table JobScaleRule add column `Configuration` mediumtext default '' comment '规则配置字段';

CREATE TABLE JobTuningAction (
    Id bigint(20) NOT NULL AUTO_INCREMENT,
    SerialId varchar(30) COLLATE utf8mb4_bin NOT NULL COMMENT '例如 action-ck9scadk',
    JobSerialId varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '作业 SerialId，例如 cql-bu8nydsy',
    JobInstanceId bigint(20) NOT NULL,
    ActionType varchar(50) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '调优操作类型',
    ActionDetail mediumtext COLLATE utf8mb4_bin COMMENT '调优操作详细信息，json 格式',
    CreateTime timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    UpdateTime timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '结束时间',
    `Status` tinyint(4) NOT NULL COMMENT '状态，0: 初始化，1：进行中，2：成功，3：失败',
    Diagnosis mediumtext COLLATE utf8mb4_bin COMMENT '作业诊断信息，json 格式',
    Properties mediumtext COLLATE utf8mb4_bin COMMENT '扩缩容信息，json 格式',
    Configuration text COLLATE utf8mb4_bin COMMENT '扩缩容规则配置信息',
    PRIMARY KEY (Id) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=udtf8mb4_bin ROW_FORMAT=COMPACT;

alter table JobScaleEvent ADD COLUMN `ScalingActionConfig` text default '' comment '扩缩容行为信息';

-- 看情况是否需要删除
-- delete from JobScaleEvent where id > 1;
-- delete from JobScaleRule where id > 1;