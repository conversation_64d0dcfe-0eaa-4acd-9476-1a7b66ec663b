CREATE TABLE IF NOT EXISTS `ConnectionResource` (
    `Id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `SerialId` varchar(12) NOT NULL DEFAULT '' COMMENT '数据连接资源Id 例如: con-xxxxx',
    `AppId` bigint(20) NOT NULL DEFAULT -1 COMMENT '数据连接所属AppId',
    `OwnerUin` varchar(20) NOT NULL DEFAULT '' COMMENT '数据连接所属Uin',
    `CreatorUin` varchar(20)  NOT NULL DEFAULT '' COMMENT '创建数据连接的Uin',
    `Region` varchar(20) NOT NULL DEFAULT '' COMMENT '数据连接所属区域',
    `ItemSpaceId` bigint(20) NOT NULL DEFAULT 0 COMMENT '数据连接所属空间',
    `Name` varchar(350) NOT NULL DEFAULT '' COMMENT '数据连接名称',
    `Type` int(30) NOT NULL DEFAULT 1 COMMENT '数据连接类型 1-Mysql 2-PostgreSQL 3-ClickHouse 4-Elasticsearch6 5-Elasticsearch7 6-Oracle 7-Sqlserver 8-Kafka 9-MongoDB',
    `Origin` int(10) NOT NULL DEFAULT 1 COMMENT '数据连接来源 1-腾讯云 2-IP连接',
    `InstanceId` varchar(100) NOT NULL DEFAULT '' COMMENT '连接实例Id',
    `Properties` TEXT NULL DEFAULT NULL COMMENT '数据连接属性',
    `Username` varchar(64) NOT NULL DEFAULT '' COMMENT '用户名',
    `Password` varchar(64) NOT NULL DEFAULT '' COMMENT '加密后的密码',
    `Status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '-2-已删除；1-运行中',
    `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`Id`),
    UNIQUE KEY `SerialIdUinque` (`SerialId`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8 COMMENT '数据连接资源表';


CREATE TABLE IF NOT EXISTS `AsyncJobInfo` (
    `Id`              bigint(20) NOT NULL AUTO_INCREMENT,
    `SerialId`        varchar(100) COLLATE utf8mb4_bin NOT NULL ,
    `Type`            tinyint(4) DEFAULT NULL COMMENT '类型 Etl预检测:1',
    `Status`          tinyint(4) DEFAULT NULL COMMENT '状态 1：等待  2:进行中  3:成功 4:失败',
    `Context`         mediumtext  COLLATE utf8mb4_bin NOT NULL COMMENT '任务上下文',
    `Params`          mediumtext  COLLATE utf8mb4_bin NOT NULL COMMENT '参数',
    `Result`			mediumtext  COLLATE utf8mb4_bin NOT NULL COMMENT '结果',
    `Handler`         varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '处理器code',
    `CreatorUin`      varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '创建人',
    `OwnerUin`        varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '子用户',
    `AppId`           bigint(20) NOT NULL COMMENT 'Appid',
    `Region`          varchar(30) COLLATE utf8mb4_bin NOT NULL COMMENT '地区',
    `CreateTime`      timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`      timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `FinishTime`      timestamp NULL DEFAULT '0000-00-00 00:00:00',
    `StartTime`       timestamp NULL DEFAULT '0000-00-00 00:00:00',
    PRIMARY KEY (`Id`) USING BTREE,
    INDEX `SerialIdIndex` (`SerialId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT;