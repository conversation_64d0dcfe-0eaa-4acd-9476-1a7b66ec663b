CREATE TABLE IF NOT EXISTS `FlinkUiCLB`
(
    `Id`             bigint(20)   NOT NULL AUTO_INCREMENT,
    `Region`         varchar(50)  NOT NULL COMMENT '地域',
    `LoadBalancerId` varchar(20)  NOT NULL COMMENT '负载均衡实例 ID',
    `ListenerId`     varchar(20)  NOT NULL COMMENT '监听器 ID',
    `PublicIp`       varchar(100) NOT NULL COMMENT '公网 IP 地址',
    `VpcId`          varchar(20)  NOT NULL COMMENT 'VPC ID',
    `SubnetId`       varchar(20)  NOT NULL COMMENT '子网 ID',
    `CreateTime`     timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`     timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`Id`) USING BTREE,
    UNIQUE KEY `uk_Region` (`Region`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin
  ROW_FORMAT = DYNAMIC;

# 测试环境
INSERT INTO FlinkUiCLB (Region, LoadBalancerId, ListenerId, PublicIp, VpcId, SubnetId) VALUES
('ap-shanghai', 'lb-aiv6dgzl', 'lbl-r797fg0z', '**************', 'vpc-875gmoby', 'subnet-cotznfjr'),
('ap-guangzhou', 'lb-ifsytlvw', 'lbl-2bevyc4q', '**************', 'vpc-e7yiz1z5', 'subnet-h5vx7uzg'),
('ap-beijing', 'lb-et24hm33', 'lbl-jbmaumm9', '**************', 'vpc-lsi2fcm8', 'subnet-6bz3ppx3'),
('ap-chengdu', 'lb-4q7q2git', 'lbl-f0n9ej1r', '*************', 'vpc-nrs5gw7k', 'subnet-klnfv8sj'),
('ap-hongkong', 'lb-qj2diqjk', 'lbl-jfpcv2q0', '**************', 'vpc-7yrjni72', 'subnet-4t5riow3');

# 生产环境

ALTER TABLE Cluster ADD  COLUMN `CrossTenantEniMode` INT(8) NOT NULL DEFAULT 0 COMMENT '是否开启跨租户ENI模式';