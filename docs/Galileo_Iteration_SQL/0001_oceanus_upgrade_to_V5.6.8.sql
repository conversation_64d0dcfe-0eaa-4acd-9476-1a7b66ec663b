-- MySQL dump 10.13  Distrib 5.7.35, for Linux (x86_64)
--
-- Host: ************    Database: online_galileo
-- ------------------------------------------------------
-- Server version	5.7.18-txsql-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
SET @MYSQLDUMP_TEMP_LOG_BIN = @@SESSION.SQL_LOG_BIN;
SET @@SESSION.SQL_LOG_BIN= 0;

--
-- GTID state at the beginning of the backup 
--

SET @@GLOBAL.GTID_PURGED='ae077fab-95bf-11ea-a2cf-a4ae11f94cd0:1-********';

--
-- Table structure for table `Administrator`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `Administrator` (
  `AppId` bigint(20) NOT NULL,
  `OwnerUin` varchar(20) COLLATE utf8mb4_bin NOT NULL,
  `CreatorUin` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '记录创建子账号',
  `AuthSubAccountUin` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '授权子用户',
  `UpdateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `Version` bigint(25) NOT NULL,
  `Status` tinyint(4) NOT NULL COMMENT '状态: 2 启用 1 停用',
  PRIMARY KEY (`AppId`,`AuthSubAccountUin`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `BillingOrder`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `BillingOrder` (
  `Id` int(11) NOT NULL AUTO_INCREMENT,
  `AppId` bigint(20) NOT NULL,
  `CategoryId` int(11) NOT NULL,
  `DealName` varchar(255) NOT NULL,
  `Uin` varchar(255) NOT NULL,
  `OperateUin` varchar(255) NOT NULL,
  `Region` int(11) NOT NULL,
  `ZoneId` int(11) NOT NULL,
  `ClusterId` int(11) DEFAULT NULL,
  `ClusterGroupId` int(11) DEFAULT NULL,
  `ResourceId` varchar(255) DEFAULT NULL,
  `PayMode` tinyint(4) DEFAULT NULL,
  `TimeUnit` varchar(5) NOT NULL,
  `TimeSpan` tinyint(4) NOT NULL,
  `AutoRenewFlag` tinyint(4) NOT NULL,
  `SubProductCode` varchar(255) NOT NULL,
  `ComputeCu` int(11) NOT NULL,
  `FlowId` bigint(20) NOT NULL,
  `Status` tinyint(4) NOT NULL DEFAULT '1',
  `CreateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE KEY `DealName_2` (`DealName`) USING BTREE,
  KEY `DealName` (`DealName`) USING BTREE,
  KEY `FlowId` (`FlowId`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=75 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `BillingResource`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `BillingResource` (
  `Id` int(11) NOT NULL AUTO_INCREMENT,
  `ResourceId` varchar(255) DEFAULT NULL,
  `AppId` bigint(20) NOT NULL,
  `Uin` varchar(255) NOT NULL,
  `FlowId` bigint(20) DEFAULT NULL,
  `OperateUin` varchar(255) NOT NULL,
  `Region` int(11) NOT NULL,
  `ZoneId` int(11) NOT NULL,
  `PayMode` tinyint(4) DEFAULT NULL,
  `ComputeCu` int(11) NOT NULL,
  `SubProductCode` varchar(255) NOT NULL,
  `TimeUnit` varchar(5) NOT NULL,
  `TimeSpan` tinyint(4) NOT NULL,
  `AutoRenewFlag` tinyint(4) NOT NULL,
  `Status` tinyint(4) NOT NULL DEFAULT '1',
  `CreateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `IsolatedTimestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `ExpireTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `GoodsDetail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin,
  `International` tinyint(4) DEFAULT '0',
  PRIMARY KEY (`Id`) USING BTREE,
  KEY `ResourceId` (`ResourceId`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=28 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `Bucket`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `Bucket` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `Region` varchar(45) DEFAULT NULL,
  `Status` tinyint(4) DEFAULT NULL,
  `MaxNum` bigint(20) DEFAULT NULL,
  `CurNum` bigint(20) DEFAULT NULL,
  `CurMaxQPS` bigint(20) DEFAULT '0',
  `Type` tinyint(4) DEFAULT NULL COMMENT 'Bucketçš„ç±»åž‹ï¼Œä¾‹å¦‚ï¼Œå…±äº«é›†ç¾¤ï¼Œç‹¬å é›†ç¾¤ï¼Œæä¾›ä½œä¸šå­˜å‚¨jaråŒ…bucket',
  `BucketName` varchar(200) DEFAULT NULL,
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB AUTO_INCREMENT=35 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `BucketClusterRef`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `BucketClusterRef` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ClusterGroupId` bigint(20) DEFAULT NULL,
  `ClusterId` bigint(20) DEFAULT NULL,
  `BucketName` varchar(200) DEFAULT NULL,
  `Status` tinyint(4) DEFAULT NULL,
  `CreatorUin` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `BucketJobRef`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `BucketJobRef` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `BucketName` varchar(200) DEFAULT NULL,
  `JobId` varchar(45) DEFAULT NULL,
  `Status` tinyint(4) DEFAULT NULL,
  `Weight` int(10) DEFAULT NULL,
  `UseHdfs` tinyint(4) DEFAULT NULL,
  `CreatorUin` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `Cdb`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `Cdb` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `ClusterId` bigint(20) NOT NULL COMMENT '所属的集群id',
  `InstanceId` varchar(16) NOT NULL COMMENT '实例id',
  `InstanceName` varchar(32) NOT NULL COMMENT '实例名字',
  `Vip` varchar(32) DEFAULT NULL,
  `Vport` int(11) DEFAULT '0',
  `User` varchar(32) DEFAULT NULL,
  `Password` varchar(64) DEFAULT NULL COMMENT '加密后的密码',
  `Memory` bigint(20) NOT NULL COMMENT '内存大小，单位mb',
  `Volume` bigint(20) NOT NULL COMMENT '磁盘大小，单位gb',
  `Status` int(11) NOT NULL DEFAULT '0' COMMENT '0-创建中；1-运行中；4-隔离中；5-已隔离',
  PRIMARY KEY (`Id`),
  UNIQUE KEY `ClusterIdInstanceId` (`ClusterId`,`InstanceId`)
) ENGINE=InnoDB AUTO_INCREMENT=62 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `Cluster`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `Cluster` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `UniqClusterId` varchar(16) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'EMR/TKEé›†ç¾¤å·',
  `ClusterGroupId` bigint(20) NOT NULL,
  `CreatorUin` varchar(20) COLLATE utf8mb4_bin NOT NULL,
  `Zone` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou-3',
  `VpcId` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'vpc-xxx',
  `SubnetId` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'subnet-xxx',
  `VpcCIDR` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '10.0.0.1/16',
  `RoleType` tinyint(4) NOT NULL COMMENT '1：Active，2：Standby, -1：Decommissioning，-2：Decommissioned',
  `SchedulerType` tinyint(4) NOT NULL COMMENT '调度器类型，1：EMR，2：TKE',
  `CuNum` smallint(20) NOT NULL COMMENT 'CU数量',
  `UsedCuNum` int(20) NOT NULL COMMENT '已使用的CU数量',
  `DefaultCOSBucket` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '默认的 COS 存储桶（作业可覆盖）',
  `Remark` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `KubeConfig` text COLLATE utf8mb4_bin COMMENT 'tkeé›†ç¾¤kubeé…ç½®',
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `StopTime` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  `FlinkVersion` varchar(200) COLLATE utf8mb4_bin DEFAULT NULL,
  `WebUIPrefix` varchar(256) COLLATE utf8mb4_bin DEFAULT '',
  `ClusterConfig` text COLLATE utf8mb4_bin COMMENT '集群特定配置',
  `FlinkConfig` text COLLATE utf8mb4_bin,
  `ClsLogSet` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `ClsTopicId` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `SupportedFeatures` text COLLATE utf8mb4_bin,
  `SupportedFlinkVersion` varchar(255) COLLATE utf8mb4_bin DEFAULT '[]',
  `LogMode` tinyint(2) NOT NULL DEFAULT '0',
  `LogConfig` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '集群日志配置',
  `DefaultLogCollectConf` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '集群默认采集配置',
  `ClusterExtendConfig` text COLLATE utf8mb4_bin,
  PRIMARY KEY (`Id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ClusterGroup`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ClusterGroup` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `SerialId` varchar(16) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '例如 cluster-bu8nydsy',
  `Name` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '集群名',
  `AppId` bigint(20) NOT NULL,
  `OwnerUin` varchar(20) COLLATE utf8mb4_bin NOT NULL,
  `CreatorUin` varchar(20) COLLATE utf8mb4_bin NOT NULL,
  `Region` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou',
  `Zone` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou-3',
  `Type` tinyint(4) NOT NULL COMMENT '模式，1：共享集群，2：独占集群',
  `Status` tinyint(4) NOT NULL COMMENT '集群状态，1：创建中，2：运行中',
  `CuNum` int(20) NOT NULL COMMENT 'CU数量',
  `UsedCuNum` int(20) NOT NULL COMMENT '已使用的CU数量',
  `CuMem` tinyint(4) NOT NULL COMMENT 'CU内存规格类型，单位GB，只支持2,4,,8,16',
  `Remark` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `StopTime` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  `NetEnvironmentType` tinyint(4) NOT NULL COMMENT 'ç½‘ç»œçŽ¯å¢ƒï¼Œ1ï¼šVPCï¼Œ2ï¼šäº‘æ”¯æ’‘ 3ï¼šVPCX',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE KEY `SerialId` (`SerialId`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ClusterGroupPeerVpc`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ClusterGroupPeerVpc` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ClusterGroupId` bigint(20) NOT NULL,
  `VpcId` varchar(20) COLLATE utf8mb4_bin NOT NULL,
  `SubnetId` varchar(20) COLLATE utf8mb4_bin NOT NULL,
  `CcnId` varchar(20) COLLATE utf8mb4_bin NOT NULL,
  `Status` tinyint(4) NOT NULL COMMENT 'VPC状态，1：OK，-1：DELETE',
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `AppId` bigint(20) NOT NULL DEFAULT '0' COMMENT 'vpcæ‰€å±žAppId',
  `OwnerUin` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'vpcæ‰€å±žOwnerUin',
  PRIMARY KEY (`Id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ClusterMetric`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ClusterMetric` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `clusterId` bigint(20) NOT NULL,
  `vcoreUsed` int(4) DEFAULT NULL,
  `vcoreTotal` int(4) DEFAULT NULL,
  `memoryUsed` int(4) DEFAULT NULL,
  `memoryTotal` int(4) DEFAULT NULL,
  `diskCapacity` bigint(20) DEFAULT NULL,
  `diskUsed` bigint(20) DEFAULT NULL,
  `createTime` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=235681 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ClusterVersion`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ClusterVersion` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `ClusterId` bigint(20) NOT NULL COMMENT 'id in Cluster',
  `Version` varchar(255) NOT NULL COMMENT 'v1 v2 v3 ... v1000',
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`Id`),
  KEY `ClusterId` (`ClusterId`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ClustersAllRegionCount`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ClustersAllRegionCount` (
  `Id` int(11) NOT NULL AUTO_INCREMENT,
  `AppId` bigint(20) NOT NULL,
  `Num` bigint(20) NOT NULL,
  `Region` varchar(20) NOT NULL,
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `Command`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `Command` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ClusterGroupId` bigint(20) NOT NULL,
  `ClusterId` bigint(20) NOT NULL,
  `AppId` bigint(20) NOT NULL,
  `OwnerUin` varchar(20) COLLATE utf8mb4_bin NOT NULL,
  `CreatorUin` varchar(20) COLLATE utf8mb4_bin NOT NULL,
  `Region` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou',
  `Zone` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou-3',
  `JobId` bigint(20) NOT NULL,
  `JobName` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL,
  `JobSerialId` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL,
  `JobConfigId` bigint(20) unsigned DEFAULT NULL,
  `RunningOrderId` int(11) DEFAULT NULL,
  `Action` tinyint(4) NOT NULL,
  `JobType` tinyint(4) NOT NULL COMMENT '作业类型，1：SQL，2：JAR',
  `JobRegressStatus` tinyint(4) NOT NULL COMMENT '作业回退状态',
  `JobNextStatus` tinyint(4) NOT NULL COMMENT '作业下一个状态',
  `Params` mediumtext COLLATE utf8mb4_bin COMMENT '命令参数',
  `Status` tinyint(4) NOT NULL COMMENT '作业状态，0：未初始化，1：未发布，2：操作中，3：运行中，4：停止，5：暂停，-1：故障，-2：删除',
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `FetchTime` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  `AckTime` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  `FinishTime` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  PRIMARY KEY (`Id`) USING BTREE,
  KEY `index_command_jobId_status` (`JobId`,`Status`),
  KEY `index_0` (`ClusterGroupId`,`Status`,`CreateTime`)
) ENGINE=InnoDB AUTO_INCREMENT=2051 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ConfigurationCenter`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ConfigurationCenter` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ConfigurationKey` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '配置key',
  `ConfigurationValue` text COLLATE utf8mb4_bin NOT NULL COMMENT '配置Value',
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `Type` tinyint(4) DEFAULT '0',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE KEY `unique_key` (`ConfigurationKey`)
) ENGINE=InnoDB AUTO_INCREMENT=904 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ConfigurationCenter_18`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ConfigurationCenter_18` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ConfigurationKey` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '配置key',
  `ConfigurationValue` text COLLATE utf8mb4_bin NOT NULL COMMENT '配置Value',
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `Type` tinyint(4) DEFAULT '0',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE KEY `unique_key` (`ConfigurationKey`)
) ENGINE=InnoDB AUTO_INCREMENT=840 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `CuMemWhiteList`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `CuMemWhiteList` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `AppId` bigint(20) NOT NULL,
  `SubAccountUin` varchar(20) COLLATE utf8mb4_bin NOT NULL,
  PRIMARY KEY (`Id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=753 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `CvmSaleConf`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `CvmSaleConf` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `Region` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou',
  `Zone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou-3',
  `InstanceType` varchar(128) NOT NULL DEFAULT '' COMMENT '实例规格',
  `Cpu` int(32) NOT NULL DEFAULT '0' COMMENT 'cpu',
  `Memory` int(32) NOT NULL DEFAULT '0' COMMENT '内存：GB',
  `Status` int(11) NOT NULL DEFAULT '1' COMMENT '0-不开放，1-开放',
  `Generation` int(11) DEFAULT '1' COMMENT 'cvm内核版本,1:kvm1.0,3:kvm3.0',
  PRIMARY KEY (`Id`),
  UNIQUE KEY `ZoneInstanceType` (`Zone`,`InstanceType`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `DebugConfig`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `DebugConfig` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `createTime` datetime DEFAULT NULL,
  `jobSerialId` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `sqlStatement` varchar(5120) COLLATE utf8mb4_bin DEFAULT NULL,
  `updateTime` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `DebugJob`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `DebugJob` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `attemptId` int(11) NOT NULL,
  `configId` bigint(20) DEFAULT NULL,
  `createTime` datetime DEFAULT NULL,
  `returnCode` int(11) NOT NULL,
  `returnMsg` mediumtext COLLATE utf8mb4_bin,
  `status` int(11) NOT NULL,
  `updateTime` datetime DEFAULT NULL,
  `isShare` int(11) DEFAULT '1',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `DebugResourceRef`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `DebugResourceRef` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ResourceId` varchar(45) DEFAULT NULL,
  `JobConfigId` bigint(20) DEFAULT NULL,
  `VersionId` bigint(20) DEFAULT NULL,
  `Status` int(11) DEFAULT NULL,
  `UsageType` int(11) DEFAULT NULL,
  `CreateTime` datetime DEFAULT NULL,
  `UpdateTime` datetime DEFAULT NULL,
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB AUTO_INCREMENT=116 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `DebugSink`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `DebugSink` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `debugJobId` bigint(20) DEFAULT NULL,
  `tableName` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `url` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `DebugSource`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `DebugSource` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `configId` bigint(20) DEFAULT NULL,
  `content` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `fieldDelimiter` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `inputType` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `fileName` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `tableName` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `updateTime` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `DependencyFolderTree`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `DependencyFolderTree` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ItemSpaceId` bigint(20) NOT NULL DEFAULT '0' COMMENT '项目空间ID',
  `FolderId` varchar(15) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '例如 folder-bu8nydsy',
  `FolderName` varchar(50) CHARACTER SET utf8 NOT NULL COMMENT '文件夹名',
  `ParentId` varchar(15) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '例如 folder-bu8nyd75',
  `AppId` bigint(20) NOT NULL,
  `Region` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou',
  `CreatorUin` varchar(20) COLLATE utf8mb4_bin NOT NULL,
  `OwnerUin` varchar(20) COLLATE utf8mb4_bin NOT NULL,
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE KEY `FolderId` (`FolderId`) USING BTREE,
  UNIQUE KEY `FolderName_ParentId` (`FolderName`,`ParentId`,`AppId`,`Region`,`ItemSpaceId`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `Emr`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `Emr` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `ClusterId` bigint(20) NOT NULL COMMENT '所属的集群id',
  `InstanceId` varchar(16) NOT NULL COMMENT '实例id',
  `InstanceName` varchar(32) NOT NULL COMMENT '实例名字',
  `CoreDiskSize` bigint(20) NOT NULL COMMENT 'core磁盘大小，单位gb',
  `Config` text COMMENT 'emr配置信息',
  `Status` int(11) NOT NULL DEFAULT '0' COMMENT '2-运行中；3-创建中',
  PRIMARY KEY (`Id`),
  UNIQUE KEY `ClusterId` (`ClusterId`)
) ENGINE=InnoDB AUTO_INCREMENT=50 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `EventAlert`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `EventAlert` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `appId` int(11) NOT NULL COMMENT '用户的 AppId',
  `region` varchar(30) DEFAULT NULL,
  `name` varchar(100) DEFAULT NULL COMMENT '云监控事件告警中录入的事件名',
  `type` varchar(45) DEFAULT NULL COMMENT '事件类型. 1 启动停止 2 运行失败 3 快照失败 4 作业异常 (有各种子类型)',
  `runningOrderIdOnTrigger` int(11) DEFAULT NULL COMMENT '触发异常事件时的运行实例 RunningOrderId',
  `runningOrderIdOnRecovery` int(11) DEFAULT NULL COMMENT '异常事件恢复时的运行实例 RunningOrderId',
  `status` int(11) DEFAULT '1' COMMENT '废弃字段, 与 isAlertOn 含义相同',
  `message` varchar(2000) DEFAULT NULL,
  `clusterId` int(11) NOT NULL COMMENT '作业的集群ID',
  `jobId` varchar(20) NOT NULL COMMENT '作业的名字',
  `jobName` varchar(1000) NOT NULL COMMENT '作业的序列号',
  `isAlertOn` int(11) NOT NULL COMMENT '0 已恢复，1 未恢复',
  `createTime` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  `recoverTime` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `jobId_isAlertOn` (`jobId`,`isAlertOn`),
  KEY `clusterId_isAlertOn` (`clusterId`,`isAlertOn`)
) ENGINE=InnoDB AUTO_INCREMENT=1471 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `FlinkUiAuthInfo`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `FlinkUiAuthInfo` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `ClusterGroupId` bigint(20) NOT NULL COMMENT '所属的集群id',
  `User` varchar(32) NOT NULL DEFAULT 'admin',
  `Password` varchar(256) NOT NULL DEFAULT '',
  PRIMARY KEY (`Id`),
  UNIQUE KEY `ClusterGroupId` (`ClusterGroupId`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `InnerUserTransition`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `InnerUserTransition` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `AppId` bigint(20) NOT NULL,
  `OwnerAccountUin` varchar(20) COLLATE utf8mb4_bin NOT NULL,
  `SubAccountUin` varchar(20) COLLATE utf8mb4_bin NOT NULL,
  `JarFilePath` varchar(500) COLLATE utf8mb4_bin NOT NULL COMMENT 'JAR作业路径',
  `ShipFiles` varchar(500) COLLATE utf8mb4_bin NOT NULL COMMENT '依赖JAR包路径',
  `EntrypointClass` varchar(500) COLLATE utf8mb4_bin NOT NULL COMMENT '主类',
  `Status` tinyint(4) NOT NULL COMMENT '状态，1：active，-1: inactive',
  PRIMARY KEY (`Id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=756 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ItemSpace`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ItemSpace` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `SerialId` varchar(60) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '例如 spc-XXX',
  `AppId` bigint(20) NOT NULL,
  `OwnerUin` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '创建空间的主账号uin',
  `CreatorUin` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '创建空间的子账号uin(如果是主账号创建,就是主账号uin)',
  `ItemSpaceName` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '项目空间名称',
  `Region` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '地域',
  `UpdateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `Status` tinyint(4) NOT NULL COMMENT '空间状态 1 未初始化 2 可用  -1 已删除',
  `Description` varchar(256) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '空间描述',
  PRIMARY KEY (`Id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1334 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ItemSpacesClusters`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ItemSpacesClusters` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `AppId` bigint(20) NOT NULL,
  `ItemSpaceId` bigint(20) NOT NULL,
  `Region` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '地域',
  `ClusterGroupId` bigint(20) NOT NULL COMMENT '集群组ID',
  `UpdateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `Status` tinyint(4) NOT NULL COMMENT '状态: 2 启用 1 停用',
  PRIMARY KEY (`Id`) USING BTREE,
  KEY `ItemSpaceId` (`ItemSpaceId`)
) ENGINE=InnoDB AUTO_INCREMENT=1328 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `Job`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `Job` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `SerialId` varchar(12) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '例如 cql-bu8nydsy',
  `AppId` bigint(20) NOT NULL,
  `OwnerUin` varchar(20) COLLATE utf8mb4_bin NOT NULL,
  `CreatorUin` varchar(20) COLLATE utf8mb4_bin NOT NULL,
  `Region` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou',
  `Zone` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou-3',
  `Name` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL,
  `Status` tinyint(4) NOT NULL COMMENT '作业状态，0：未初始化，1：未发布，2：操作中，3：运行中，4：停止，5：暂停，-1：故障，-2：删除',
  `Type` tinyint(4) NOT NULL COMMENT '作业类型，0：SQL，1：JAR',
  `ClusterGroupId` smallint(20) NOT NULL,
  `ClusterId` smallint(20) NOT NULL,
  `ItemSpaceId` bigint(20) NOT NULL DEFAULT '0' COMMENT '项目空间ID',
  `TmRunningCuNum` int(20) DEFAULT NULL,
  `CuMem` tinyint(4) NOT NULL COMMENT 'CU内存规格类型，单位GB，只支持2,4,,8,16',
  `LastOpResult` mediumtext COLLATE utf8mb4_bin COMMENT '作业调度结果',
  `Remark` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `StartTime` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  `StopTime` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  `TotalRunMillis` bigint(20) DEFAULT '0' COMMENT '作业累计运行时间',
  `PublishedJobConfigId` bigint(20) DEFAULT '0',
  `LatestJobConfigId` bigint(20) DEFAULT '0',
  `LatestJobConfigVersionId` bigint(20) DEFAULT '0',
  `JmRunningCuNum` int(11) DEFAULT '0',
  `LastPublishedJobConfigId` bigint(20) DEFAULT '-1',
  `JobRunningOrderId` bigint(20) DEFAULT '0' COMMENT '该作业总共运行了多少次，包含重启，此值等于JobInstance的数量',
  `FolderId` varchar(15) COLLATE utf8mb4_bin DEFAULT 'root' COMMENT '例如 folder-bu8nydsy',
  `FlinkVersion` varchar(255) COLLATE utf8mb4_bin DEFAULT '',
  `LastScaleTime` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '上一次完成扩缩容的时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE KEY `SerialId` (`SerialId`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=243 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `JobConfig`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `JobConfig` (
  `Id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `CreatorUin` varchar(20) COLLATE utf8mb4_bin NOT NULL,
  `JobId` bigint(20) NOT NULL COMMENT '流计算 Job 表的 ID, 不是 Flink 的 JobId',
  `VersionId` bigint(20) NOT NULL,
  `EntrypointClass` varchar(500) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'JAR 包的主类名',
  `ProgramArgs` mediumtext COLLATE utf8mb4_bin COMMENT '作业的参数 以空格分隔',
  `CheckpointInterval` bigint(20) NOT NULL DEFAULT '-1' COMMENT 'Checkpoint间隔',
  `SqlCode` mediumtext COLLATE utf8mb4_bin COMMENT 'SQL代码',
  `Status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0: 使用中，-1：已删除',
  `Remark` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'JobConfig 的注释',
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `DefaultParallelism` int(11) DEFAULT '1',
  `Properties` mediumtext COLLATE utf8mb4_bin,
  `MaxParallelism` int(11) DEFAULT '128',
  `DeletorUin` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL,
  `LogCollect` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否开启日志采集',
  `UpdateTime` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  `COSBucket` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '作业关联的 COS Bucket 信息',
  `JmCuSpec` float DEFAULT '1',
  `TmCuSpec` float DEFAULT '1',
  `ClsLogsetId` varchar(64) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'CLS日志集',
  `ClsTopicId` varchar(64) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'CLS日志主题',
  `PythonVersion` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '',
  PRIMARY KEY (`Id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=701 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `JobInstance`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `JobInstance` (
  `Id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '相当于 JobRuntimeID, 标识着Galileo一个唯一的运行时实例',
  `JobId` bigint(20) NOT NULL COMMENT '流计算 Job 表的 ID, 不是 Flink 的 JobId',
  `JobConfigId` bigint(20) NOT NULL COMMENT '表明由哪个 jobConfig 启动的',
  `CuMem` tinyint(4) NOT NULL COMMENT 'CU内存规格，单位GB 2,4,8,16',
  `TmRunningCuNum` int(20) DEFAULT NULL,
  `DefaultParallelism` smallint(20) NOT NULL COMMENT '默认的 Parallelism',
  `FlinkJobId` varchar(32) COLLATE utf8mb4_bin DEFAULT '' COMMENT 'Flink 的 32 位作业 ID',
  `ApplicationId` varchar(100) COLLATE utf8mb4_bin DEFAULT '' COMMENT 'YARN 等 ApplicationId',
  `FlinkJobPlan` mediumtext COLLATE utf8mb4_bin,
  `Status` tinyint(4) NOT NULL,
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `StartTime` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  `UpdateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `StopTime` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  `JmRunningCuNum` int(11) DEFAULT '0',
  `RunningOrderId` bigint(20) NOT NULL DEFAULT '0',
  PRIMARY KEY (`Id`) USING BTREE,
  KEY `index_jobId_status` (`JobId`,`Status`)
) ENGINE=InnoDB AUTO_INCREMENT=1774 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `JobK8sEventTopic`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `JobK8sEventTopic` (
  `Id` bigint(20) NOT NULL,
  `Region` varchar(50) NOT NULL,
  `LogSetId` varchar(50) NOT NULL,
  `TopicId` varchar(50) NOT NULL,
  `Status` tinyint(4) DEFAULT '0' COMMENT '1-开启，0-关闭',
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `JobMetaTableRef`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `JobMetaTableRef` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `JobId` bigint(20) NOT NULL COMMENT '流计算 Job 表的 ID, refer Job',
  `JobConfigId` bigint(20) NOT NULL COMMENT '流作业配置表的 自增ID, refer JobConfig',
  `MetaTableId` bigint(20) NOT NULL COMMENT '元数据表自增ID, refer MetastoreTable',
  `Status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0: 使用中，-1：已删除',
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `MetaCatalogId` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `JobRunningLogTopic`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `JobRunningLogTopic` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `OwnerUin` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `Region` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou',
  `LogSetId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `LogTopicId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `Status` tinyint(4) DEFAULT '0',
  `Remark` text,
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `Instance` (`OwnerUin`,`Region`,`LogSetId`,`LogTopicId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `JobRunningLogTopic_18`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `JobRunningLogTopic_18` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `OwnerUin` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `Region` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou',
  `LogSetId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `LogTopicId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `Status` tinyint(4) DEFAULT '0',
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`Id`),
  UNIQUE KEY `Instance` (`OwnerUin`,`Region`,`LogSetId`,`LogTopicId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `JobScaleEvent`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `JobScaleEvent` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `JobId` varchar(12) COLLATE utf8mb4_bin NOT NULL COMMENT '例如 cql-bu8nydsy',
  `RuleItem` mediumtext COLLATE utf8mb4_bin COMMENT '扩缩容信息，json格式',
  `StartTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
  `EndTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '结束时间',
  `Status` tinyint(4) NOT NULL COMMENT '状态，1：成功，2：失败，-1：删除',
  `ErrorCode` varchar(50) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '错误编码',
  `AppId` bigint(20) NOT NULL,
  PRIMARY KEY (`Id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `JobScaleRule`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `JobScaleRule` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `SerialId` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '例如 rule-ck9scadk',
  `JobId` varchar(12) COLLATE utf8mb4_bin NOT NULL COMMENT '例如 cql-bu8nydsy',
  `AppId` bigint(20) NOT NULL,
  `OwnerUin` varchar(20) COLLATE utf8mb4_bin NOT NULL,
  `CreatorUin` varchar(20) COLLATE utf8mb4_bin NOT NULL,
  `Region` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou',
  `Zone` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou-3',
  `RuleName` varchar(50) CHARACTER SET utf8 NOT NULL COMMENT '规则名',
  `Status` tinyint(4) NOT NULL COMMENT '规则状态，1：启用，-1：停用，-2：删除',
  `ConditionRatio` smallint(10) DEFAULT NULL COMMENT '0-100取值百分比，如果tm中高于阈值CPU或低于阈值cpu占比高于这个值，就会触发扩缩容',
  `Threshold` bigint(20) NOT NULL COMMENT '阈值，如果是cpu 则是一个0-100的值 表示使用率，如果是kafka offset 则是offset之间的差值',
  `DurationTime` smallint(10) NOT NULL COMMENT '持续多久时间才进行扩缩容，单位分钟',
  `Step` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '如果结尾是%，则是按当前并行度的百分数进行扩缩容，否则按固定数字进行扩缩容',
  `ReachLimit` smallint(10) NOT NULL COMMENT '扩容最高的并行度',
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `Properties` mediumtext COLLATE utf8mb4_bin,
  PRIMARY KEY (`Id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `JobSubmissionLogTopic`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `JobSubmissionLogTopic` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `Region` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou',
  `LogSetId` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `LogTopicId` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `Status` tinyint(4) DEFAULT '0' COMMENT '0-开启，1-关闭',
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE KEY `Instance` (`Region`,`LogSetId`,`LogTopicId`)
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `JobSubmissionLogTopic_18`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `JobSubmissionLogTopic_18` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `Region` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou',
  `LogSetId` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `LogTopicId` varchar(50) COLLATE utf8mb4_bin NOT NULL,
  `Status` tinyint(4) DEFAULT '0' COMMENT '0-开启，1-关闭',
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE KEY `Instance` (`Region`,`LogSetId`,`LogTopicId`)
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `MetaCatalogConnector`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `MetaCatalogConnector` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `CatalogType` tinyint(4) NOT NULL,
  `CatalogVersion` varchar(20) DEFAULT NULL,
  `ResourceId` bigint(20) DEFAULT NULL COMMENT '资源表的ID',
  `VersionId` bigint(20) DEFAULT '-1' COMMENT '资源版本,默认-1 代表该资源引用最新的资源',
  `Status` tinyint(4) DEFAULT NULL COMMENT '状态,ACTIVE:1,DELETE:-2',
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `FlinkVersion` varchar(100) DEFAULT 'Flink-1.13' COMMENT '内核版本',
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `MetaCatalogResourceRef`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `MetaCatalogResourceRef` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ResourceId` bigint(20) DEFAULT NULL COMMENT '资源表的ID',
  `VersionId` bigint(20) DEFAULT '-1' COMMENT '资源版本,默认-1 代表该资源引用最新的资源',
  `CatalogId` bigint(20) DEFAULT NULL,
  `Status` tinyint(4) DEFAULT NULL COMMENT '状态,ACTIVE:1,DELETE:-2',
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `FlinkVersion` varchar(100) DEFAULT 'Flink-1.13' COMMENT '内核版本',
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `MetaTableResourceRef`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `MetaTableResourceRef` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `MetaTableId` bigint(20) DEFAULT NULL COMMENT '元数据表的ID',
  `ResourceId` bigint(20) DEFAULT NULL COMMENT '资源表的ID,-2表示不引用资源',
  `VersionId` bigint(20) DEFAULT '-1' COMMENT '配置版本,默认-1代表该资源引用最新的资源',
  `Status` tinyint(4) DEFAULT '1' COMMENT '资源配置的状态',
  `Type` tinyint(4) DEFAULT '1' COMMENT '引用类型 1:系统资源 2:用户资源',
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `FlinkVersion` varchar(100) DEFAULT 'Flink-1.11' COMMENT 'Flink版本',
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `MetastoreCatalog`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `MetastoreCatalog` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `Type` tinyint(4) DEFAULT NULL COMMENT '0:defalut,1:jdbc:2:hive',
  `Name` varchar(100) DEFAULT NULL,
  `ItemSpaceId` bigint(20) NOT NULL DEFAULT '0' COMMENT '项目空间ID',
  `Properties` varchar(1000) DEFAULT NULL,
  `Comment` varchar(200) DEFAULT NULL,
  `Region` varchar(20) DEFAULT NULL,
  `Uin` varchar(20) DEFAULT NULL,
  `SubUin` varchar(20) DEFAULT NULL,
  `AppId` bigint(20) DEFAULT NULL,
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `DefaultDatabase` varchar(100) DEFAULT NULL COMMENT '默认database',
  `SerialId` varchar(20) DEFAULT NULL,
  `Status` int(11) DEFAULT '1' COMMENT '1表示active,-2表示deleted',
  `CatalogVersion` varchar(20) DEFAULT NULL,
  `FlinkVersion` varchar(20) DEFAULT NULL COMMENT '内核版本',
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `MetastoreDatabase`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `MetastoreDatabase` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `Name` varchar(100) DEFAULT NULL,
  `CatalogId` bigint(20) DEFAULT NULL,
  `Properties` varchar(1000) DEFAULT NULL,
  `Comment` varchar(200) DEFAULT NULL,
  `Region` varchar(20) DEFAULT NULL,
  `ItemSpaceId` bigint(20) NOT NULL DEFAULT '0' COMMENT '项目空间ID',
  `Uin` varchar(200) DEFAULT NULL,
  `SubUin` varchar(20) DEFAULT NULL,
  `AppId` bigint(20) DEFAULT NULL,
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `MetastoreTable`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `MetastoreTable` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `Name` varchar(100) NOT NULL,
  `DatabaseId` bigint(20) NOT NULL,
  `TableSchema` mediumtext,
  `Properties` varchar(4000) NOT NULL,
  `Type` tinyint(4) DEFAULT NULL COMMENT '0：table,1:view,2:function',
  `TableType` varchar(100) DEFAULT NULL,
  `Script` mediumtext,
  `Comment` varchar(200) DEFAULT NULL,
  `Region` varchar(20) DEFAULT NULL COMMENT '地域',
  `Uin` varchar(20) DEFAULT NULL,
  `SubUin` varchar(20) DEFAULT NULL,
  `AppId` bigint(20) DEFAULT NULL,
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `Description` varchar(1000) DEFAULT NULL COMMENT '描述信息',
  PRIMARY KEY (`Id`),
  UNIQUE KEY `MetastoreTable_UN` (`DatabaseId`,`Name`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `MetastoreTableProperty`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `MetastoreTableProperty` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `IfSource` tinyint(4) DEFAULT NULL COMMENT '0：it can not be used as source table,1:it can be used as source table',
  `IfSink` tinyint(4) DEFAULT NULL COMMENT '0：it can not be used as tagert table",1:it can be used as target table',
  `IfDim` tinyint(4) DEFAULT NULL COMMENT '0：it can not be used as dimentional table",1:it can be used as dimentional table',
  `Dependency` varchar(4000) DEFAULT NULL,
  `Type` varchar(20) DEFAULT NULL,
  `Formats` varchar(200) DEFAULT NULL,
  `Comment` varchar(200) DEFAULT NULL,
  `resourceId` bigint(20) DEFAULT NULL,
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `Quota`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `Quota` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `AppId` bigint(20) DEFAULT NULL,
  `ConfigurationKey` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '配置key',
  `ConfigurationValue` bigint(20) DEFAULT '0' COMMENT '配置值',
  PRIMARY KEY (`Id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=120 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `Region`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `Region` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增',
  `region` varchar(30) DEFAULT NULL,
  `regionName` varchar(30) DEFAULT NULL,
  `state` int(11) DEFAULT NULL COMMENT '地域是否可达',
  `netEnvType` int(11) DEFAULT NULL,
  `regionId` int(11) NOT NULL,
  `ShortName` varchar(10) NOT NULL DEFAULT '',
  `supportSharedCluster` tinyint(4) DEFAULT '0',
  `supportInternational` tinyint(4) DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=72 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='region元数据';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ResGroup`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ResGroup` (
  `ResGroupId` varchar(45) DEFAULT NULL COMMENT '资源组ID',
  `ResourceId` varchar(45) DEFAULT NULL COMMENT '资源ID',
  `Status` tinyint(4) DEFAULT NULL,
  `Version` bigint(20) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `Resource`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `Resource` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ResourceName` varchar(200) DEFAULT NULL COMMENT 'èµ„æºåç§°',
  `ResourceType` tinyint(4) DEFAULT NULL COMMENT 'èµ„æºç±»åž‹ï¼Œjarä¸º1',
  `Remark` varchar(200) DEFAULT NULL COMMENT 'èµ„æºæè¿°',
  `ResourceFrom` tinyint(4) DEFAULT NULL COMMENT 'èµ„æºæ¥è‡ªç”¨æˆ·è¿˜æ˜¯è‡ªå·±',
  `Status` tinyint(4) DEFAULT NULL COMMENT 'èµ„æºçŠ¶æ€',
  `CreateTime` varchar(20) DEFAULT NULL,
  `UpdateTime` varchar(20) DEFAULT NULL,
  `AppId` bigint(20) DEFAULT NULL,
  `OwnerUin` varchar(20) DEFAULT NULL,
  `CreatorUin` varchar(20) DEFAULT NULL,
  `Region` varchar(45) DEFAULT NULL,
  `ItemSpaceId` bigint(20) NOT NULL DEFAULT '0' COMMENT '项目空间ID',
  `ResourceId` varchar(30) NOT NULL,
  `IsResGroup` tinyint(4) DEFAULT '0' COMMENT '1 代表改资源是资源组',
  `SystemProvide` tinyint(4) DEFAULT '0' COMMENT '0 代表用户提供资源 1代表系统提供资源',
  `FileName` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '资源文件名',
  `FolderId` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT 'root' COMMENT '例如 folder-bu8nydsy',
  PRIMARY KEY (`Id`),
  UNIQUE KEY `unique_key` (`ResourceId`)
) ENGINE=InnoDB AUTO_INCREMENT=279 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ResourceConfig`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ResourceConfig` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ResourceId` varchar(20) DEFAULT NULL,
  `VersionId` bigint(20) DEFAULT NULL COMMENT 'èµ„æºé…ç½®çš„ç‰ˆæœ¬',
  `CreateTime` varchar(20) DEFAULT NULL,
  `ResourceLoc` varchar(400) DEFAULT NULL COMMENT 'èµ„æºä½ç½®ï¼Œjsonå­—ç¬¦ä¸²',
  `Status` tinyint(4) DEFAULT NULL COMMENT 'èµ„æºé…ç½®çŠ¶æ€',
  `ReqResourceLoc` varchar(400) DEFAULT NULL COMMENT '资源位置，请求里的json字符串',
  `CreatorUin` varchar(20) DEFAULT NULL,
  `DeletorUin` varchar(20) DEFAULT NULL,
  `Remark` varchar(200) DEFAULT NULL COMMENT '资源版本描述',
  PRIMARY KEY (`Id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=498 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ResourceRef`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ResourceRef` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ResourceId` bigint(20) DEFAULT NULL COMMENT 'ç®¡ç†èµ„æºè¡¨çš„ID',
  `JobConfigId` bigint(20) DEFAULT NULL COMMENT 'ç®¡ç†ä½œä¸šé…ç½®è¡¨çš„ID',
  `VersionId` bigint(20) DEFAULT '-1' COMMENT 'é…ç½®ç‰ˆæœ¬,é»˜è®¤-1ä»£è¡¨è¯¥èµ„æºå¼•ç”¨æœ€æ–°çš„èµ„æº',
  `Status` tinyint(4) DEFAULT NULL COMMENT 'èµ„æºé…ç½®çš„çŠ¶æ€',
  `UsageType` tinyint(4) DEFAULT NULL COMMENT 'èµ„æºç±»åž‹ï¼Œä¸»èµ„æº1',
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`Id`),
  KEY `jobconfigid_status` (`JobConfigId`,`Status`),
  KEY `rsjv` (`ResourceId`,`Status`,`JobConfigId`,`VersionId`)
) ENGINE=InnoDB AUTO_INCREMENT=466 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ResourceWhiteList`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ResourceWhiteList` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `AppId` bigint(20) DEFAULT NULL,
  `MaxLimit` bigint(255) DEFAULT NULL,
  `MaxDependencyLimit` bigint(255) NOT NULL DEFAULT '2' COMMENT '外部依赖资源上传配额',
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB AUTO_INCREMENT=36 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `RoleAuth`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `RoleAuth` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `AppId` bigint(20) NOT NULL,
  `ItemSpaceId` bigint(20) NOT NULL,
  `ItemSpaceSerialId` varchar(60) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'ItemSpace表 SerialId',
  `OwnerUin` varchar(20) COLLATE utf8mb4_bin NOT NULL,
  `CreatorUin` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '记录创建子账号',
  `AuthSubAccountUin` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '授权子用户',
  `Permission` tinyint(4) NOT NULL COMMENT '操作权限: 0.超级管理员 1.空间管理员 2.开发者 3.访客',
  `UpdateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `Status` tinyint(4) NOT NULL COMMENT '状态: 2 启用 1 停用',
  PRIMARY KEY (`Id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1334 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `Savepoint`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `Savepoint` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ClusterId` bigint(20) DEFAULT NULL COMMENT 'ClusterId',
  `SerialId` varchar(12) DEFAULT NULL,
  `JobId` bigint(20) DEFAULT NULL,
  `JobRuntimeId` bigint(20) DEFAULT NULL,
  `ItemSpaceId` bigint(20) NOT NULL DEFAULT '0' COMMENT '项目空间ID',
  `Status` int(11) DEFAULT NULL,
  `Path` varchar(500) DEFAULT NULL,
  `CreateTime` datetime NOT NULL,
  `UpdateTime` datetime DEFAULT NULL,
  `RecordType` int(4) DEFAULT NULL COMMENT 'savepoint è¡¨ä¸­è®°å½•çš„ç±»åž‹, 1ä»£è¡¨ savepoint, 2ä»£è¡¨ checkpoint',
  `Size` bigint(20) DEFAULT NULL COMMENT 'savepoint å’Œ checkpoint çš„å¤§å°',
  `Timeout` int(8) DEFAULT NULL COMMENT 'savepoint è¶…æ—¶æ—¶é—´',
  `Description` varchar(1024) DEFAULT NULL COMMENT 'savepoint æè¿°ä¿¡æ¯',
  PRIMARY KEY (`Id`),
  KEY `savepoint_job_record_status` (`JobId`,`RecordType`,`Status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `SqlKeyword`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `SqlKeyword` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `keyword` varchar(20) COLLATE utf8mb4_bin NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1107 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `SqlTemplate`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `SqlTemplate` (
  `Id` int(10) NOT NULL AUTO_INCREMENT,
  `Name` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '模板名称，显示在前端的，例如ckafka->mysql',
  `Type` tinyint(4) NOT NULL COMMENT '模板类型，1:共享集群SQL模板，2:独享集群SQL模板',
  `Code` varchar(5000) COLLATE utf8mb4_bin NOT NULL COMMENT '模板代码',
  `Status` tinyint(4) DEFAULT '1' COMMENT '模板状态，1：使用中，-2：已删除',
  `FlinkVersion` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`Id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `SqlTemplate_18`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `SqlTemplate_18` (
  `Id` int(10) NOT NULL AUTO_INCREMENT,
  `Name` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '模板名称，显示在前端的，例如ckafka->mysql',
  `Type` tinyint(4) NOT NULL COMMENT '模板类型，1:共享集群SQL模板，2:独享集群SQL模板',
  `Code` varchar(5000) COLLATE utf8mb4_bin NOT NULL COMMENT '模板代码',
  `Status` tinyint(4) DEFAULT '1' COMMENT '模板状态，1：使用中，-2：已删除',
  PRIMARY KEY (`Id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `SynVersion`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `SynVersion` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `Region` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '地域信息',
  `UpdateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `Status` tinyint(4) NOT NULL COMMENT '状态: 2 启用 1 停用',
  `LastSyncTime` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  PRIMARY KEY (`Id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1329 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `SystemConnector`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `SystemConnector` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ResourceId` varchar(20) DEFAULT NULL COMMENT '资源表的ID,NULL 表示不引用资源',
  `VersionId` bigint(20) DEFAULT '-1' COMMENT '配置版本,默认-1代表该资源引用最新的资源,-2表示不引用资源',
  `FlinkVersion` varchar(20) DEFAULT NULL COMMENT '元数据表的ID',
  `Options` varchar(1000) DEFAULT NULL,
  `Status` tinyint(4) DEFAULT NULL COMMENT '状态,ACTIVE:1,DELETE:-2',
  `Region` varchar(100) DEFAULT NULL,
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB AUTO_INCREMENT=57 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `SystemResourceRef`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `SystemResourceRef` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `FlinkVersion` varchar(200) DEFAULT NULL,
  `ResourceId` varchar(200) DEFAULT NULL,
  `Remark` varchar(200) DEFAULT NULL,
  `Status` tinyint(4) DEFAULT NULL,
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB AUTO_INCREMENT=55 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `Tke`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `Tke` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `ClusterId` bigint(20) NOT NULL COMMENT '所属的集群id',
  `InstanceId` varchar(16) NOT NULL COMMENT '实例id',
  `InstanceName` varchar(32) NOT NULL COMMENT '实例名字',
  `ClusterType` int(11) DEFAULT '0' COMMENT '0:tke,1:eks',
  `WorkerStatus` int(11) DEFAULT '0' COMMENT '0:未添加,1:已添加',
  `Status` int(11) NOT NULL DEFAULT '0' COMMENT '0-创建中；1-运行中；2-异常',
  PRIMARY KEY (`Id`),
  UNIQUE KEY `ClusterIdInstanceId` (`ClusterId`,`InstanceId`)
) ENGINE=InnoDB AUTO_INCREMENT=52 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `TkeVersion`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `TkeVersion` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ClusterId` bigint(20) NOT NULL,
  `ResourceName` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT 'cluster-admin',
  `ContainerName` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT 'app-container',
  `ContainerVersion` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '1.8',
  `LastImageVersion` varchar(200) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'ccr.ccs.tencentyun.com/oceanus-release/cluster-admin:1.8',
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`Id`) USING BTREE,
  KEY `tv_ClusterId_ResourceName_ContainerName` (`ClusterId`,`ResourceName`,`ContainerName`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `Tree`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `Tree` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ItemSpaceId` bigint(20) NOT NULL DEFAULT '0' COMMENT '项目空间ID',
  `FolderId` varchar(15) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '例如 folder-bu8nydsy',
  `FolderName` varchar(50) CHARACTER SET utf8 NOT NULL COMMENT '文件夹名',
  `ParentId` varchar(15) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '例如 folder-bu8nyd75',
  `AppId` bigint(20) NOT NULL,
  `Region` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou',
  `CreatorUin` varchar(20) COLLATE utf8mb4_bin NOT NULL,
  `OwnerUin` varchar(20) COLLATE utf8mb4_bin NOT NULL,
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE KEY `FolderId` (`FolderId`) USING BTREE,
  UNIQUE KEY `FolderName_ParentId` (`FolderName`,`ParentId`,`AppId`,`Region`,`ItemSpaceId`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `UserUinMap`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `UserUinMap` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `AppId` bigint(20) DEFAULT NULL,
  `AccessUin` varchar(45) DEFAULT NULL,
  `CustomUin` varchar(45) DEFAULT NULL,
  `Status` tinyint(4) DEFAULT NULL,
  `Extension` varchar(200) DEFAULT NULL,
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `Variable`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `Variable` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `Name` varchar(200) NOT NULL COMMENT 'å˜é‡å',
  `ItemSpaceId` bigint(20) NOT NULL DEFAULT '0' COMMENT '项目空间ID',
  `Value` varchar(500) NOT NULL COMMENT 'å˜é‡å€¼',
  `Type` tinyint(4) NOT NULL COMMENT '1: æ˜¾ç¤º 2ï¼šéšè—',
  `Status` tinyint(4) NOT NULL COMMENT 'çŠ¶æ€ï¼šACTIVE:1,DELETE:-2',
  `Remark` varchar(100) DEFAULT NULL COMMENT 'å˜é‡æè¿°',
  `SerialId` varchar(32) NOT NULL COMMENT 'å˜é‡id',
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'å˜é‡åˆ›å»ºæ—¶é—´',
  `UpdateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'å˜é‡å€¼æ›´æ–°æ—¶é—´',
  `CreatorUin` varchar(100) NOT NULL COMMENT 'åˆ›å»ºäºº',
  `OwnerUin` varchar(100) NOT NULL COMMENT 'uin',
  `AppId` bigint(20) NOT NULL COMMENT 'Appid',
  `Region` varchar(30) NOT NULL COMMENT 'åœ°åŒº',
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='å˜é‡è¡¨';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `VariableReference`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `VariableReference` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `VariableId` bigint(20) NOT NULL COMMENT 'å˜é‡Id',
  `ComponentId` bigint(20) NOT NULL COMMENT 'å¼•ç”¨æ¨¡å—Id',
  `ComponentType` tinyint(4) NOT NULL COMMENT 'å¼•ç”¨æ¨¡å—ç±»åž‹ 1: Job',
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'åˆ›å»ºæ—¶é—´',
  `UpdateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'æ›´æ–°æ—¶é—´',
  `Status` tinyint(4) NOT NULL COMMENT 'çŠ¶æ€ï¼šACTIVE:1,DELETE:-2',
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='å˜é‡å¼•ç”¨è¡¨';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `WhiteList`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `WhiteList` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT,
  `AppId` bigint(20) NOT NULL,
  `Type` tinyint(4) NOT NULL COMMENT 'ç™½åå•ç±»åž‹ 1.è§’è‰²æƒé™ 2.0.25 CU',
  `UpdateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE KEY `AppId_Type` (`AppId`,`Type`)
) ENGINE=InnoDB AUTO_INCREMENT=1356 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `Zone`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `Zone` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `region` varchar(30) DEFAULT NULL,
  `zoneId` bigint(20) DEFAULT NULL,
  `zone` varchar(30) DEFAULT NULL,
  `zoneName` varchar(50) DEFAULT NULL,
  `State` int(11) NOT NULL COMMENT '0 å”®ç½„ï¼Œ1 èµ„æºå……è¶³',
  `netEnvType` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=102 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `Zone_18`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `Zone_18` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `region` varchar(30) DEFAULT NULL,
  `zoneId` bigint(20) DEFAULT NULL,
  `zone` varchar(30) DEFAULT NULL,
  `zoneName` varchar(50) DEFAULT NULL,
  `State` int(11) NOT NULL COMMENT '0 å”®ç½„ï¼Œ1 èµ„æºå……è¶³',
  `netEnvType` int(11) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=88 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dlocker`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `dlocker` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ä¸»é”®ID',
  `bizType` char(100) NOT NULL COMMENT 'ä¸šåŠ¡ç±»åž‹',
  `bizId` char(100) NOT NULL COMMENT 'ä¸šåŠ¡ç›¸å…³æ ‡è¯†',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `bizType` (`bizType`,`bizId`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4656284 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='ä»»åŠ¡é”';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dlocker_detail`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `dlocker_detail` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ä¸»é”®ID',
  `bizType` char(100) NOT NULL COMMENT 'ä¸šåŠ¡ç±»åž‹',
  `bizId` char(100) NOT NULL COMMENT 'ä¸šåŠ¡ç›¸å…³æ ‡è¯†',
  `lockerIp` char(20) NOT NULL COMMENT 'é”å®šè€…IP',
  `lockerId` bigint(20) NOT NULL COMMENT 'dlocker id',
  `status` int(4) unsigned NOT NULL DEFAULT '0' COMMENT 'è®°å½•çŠ¶æ€ï¼Œ 0 æ­£å¸¸ï¼Œ 1 åˆ é™¤',
  `createTime` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT 'åˆ›å»ºæ—¶é—´',
  `updateTime` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT 'æ›´æ–°æ—¶é—´',
  `expireTime` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT 'è¿‡æœŸæ—¶é—´',
  `unlockTime` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT 'è§£é”æ—¶é—´',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4086132 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='ä»»åŠ¡é”è¯¦æƒ…';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `instanceCuCtrl`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `instanceCuCtrl` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `appId` bigint(20) unsigned NOT NULL COMMENT 'Ã§â€Â¨Ã¦Ë†Â·appId',
  `maxCu` int(4) NOT NULL COMMENT 'Ã¦Å“â‚¬Ã¥Â¤Â§CUÃ©â„¢ÂÃ¥Ë†Â¶',
  `minCu` int(4) NOT NULL COMMENT 'Ã¦Å“â‚¬Ã¥Â°ÂCUÃ©â„¢ÂÃ¥Ë†Â¶',
  `maxInstances` int(4) NOT NULL COMMENT 'Ã¦Å“â‚¬Ã¥Â¤Â§instanceÃ¤Â¸ÂªÃ¦â€¢Â°Ã©â„¢ÂÃ¥Ë†Â¶',
  `cuStepsize` int(4) NOT NULL COMMENT 'CUÃ¨Â°Æ’Ã¦â€¢Â´Ã¦Â­Â¥Ã©â€¢Â¿',
  `regionId` int(4) NOT NULL,
  `maxTotalCu` bigint(20) DEFAULT '6',
  `type` int(11) DEFAULT '1',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2014 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='Ã§â€Â¨Ã¦Ë†Â·CUÃ¦Å½Â§Ã¥Ë†Â¶Ã¨Â¡Â¨';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `instanceCuCtrl_18`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `instanceCuCtrl_18` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `appId` bigint(20) unsigned NOT NULL COMMENT 'Ã§â€Â¨Ã¦Ë†Â·appId',
  `maxCu` int(4) NOT NULL COMMENT 'Ã¦Å“â‚¬Ã¥Â¤Â§CUÃ©â„¢ÂÃ¥Ë†Â¶',
  `minCu` int(4) NOT NULL COMMENT 'Ã¦Å“â‚¬Ã¥Â°ÂCUÃ©â„¢ÂÃ¥Ë†Â¶',
  `maxInstances` int(4) NOT NULL COMMENT 'Ã¦Å“â‚¬Ã¥Â¤Â§instanceÃ¤Â¸ÂªÃ¦â€¢Â°Ã©â„¢ÂÃ¥Ë†Â¶',
  `cuStepsize` int(4) NOT NULL COMMENT 'CUÃ¨Â°Æ’Ã¦â€¢Â´Ã¦Â­Â¥Ã©â€¢Â¿',
  `regionId` int(4) NOT NULL,
  `maxTotalCu` bigint(20) DEFAULT '6',
  `type` int(11) DEFAULT '1',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1993 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='Ã§â€Â¨Ã¦Ë†Â·CUÃ¦Å½Â§Ã¥Ë†Â¶Ã¨Â¡Â¨';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `taskflow`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `taskflow` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '流程ID',
  `processname` varchar(120) NOT NULL DEFAULT '' COMMENT '流程code',
  `regionId` bigint(20) DEFAULT NULL COMMENT '地域',
  `progress` float(15,3) DEFAULT '0.000' COMMENT '进度',
  `flowId` varchar(64) NOT NULL DEFAULT '' COMMENT '流程ID,由流程引擎创建',
  `docId` varchar(64) NOT NULL COMMENT '数据ID',
  `status` int(4) NOT NULL COMMENT '流程状态: 0:未发，1：流程已发起，2：流程结束,-1 流程删除 -2 流程挂起',
  `locked` int(4) NOT NULL COMMENT '流程锁定状态: 0：未锁定，1任务已锁定',
  `addtime` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '流程创建时间',
  `starttime` bigint(20) DEFAULT '0' COMMENT '流程执行开始时间戳',
  `endtime` bigint(20) DEFAULT '0' COMMENT '流程执行结束时间戳',
  `flowparam` mediumtext,
  PRIMARY KEY (`id`),
  KEY `idx_doc` (`docId`),
  KEY `flowId_index` (`flowId`)
) ENGINE=InnoDB AUTO_INCREMENT=932721 DEFAULT CHARSET=utf8 COMMENT='流程信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `taskflow_status`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `taskflow_status` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `docId` varchar(32) NOT NULL,
  `flowId` bigint(20) NOT NULL,
  `status` int(4) NOT NULL,
  `processKey` varchar(64) NOT NULL,
  `taskCode` varchar(32) NOT NULL,
  `starttime` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '开始时间',
  `endtime` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '结束时间',
  UNIQUE KEY `flowId` (`flowId`,`processKey`,`taskCode`),
  KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=37729 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `taskflowparams`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `taskflowparams` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `flowid` bigint(20) unsigned NOT NULL COMMENT '流程ID',
  `paramkey` varchar(32) DEFAULT NULL COMMENT '参数名',
  `paramvalue` text COMMENT '参数值',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3420889 DEFAULT CHARSET=utf8 COMMENT='流程表';
/*!40101 SET character_set_client = @saved_cs_client */;
SET @@SESSION.SQL_LOG_BIN = @MYSQLDUMP_TEMP_LOG_BIN;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2022-04-15 11:31:51
