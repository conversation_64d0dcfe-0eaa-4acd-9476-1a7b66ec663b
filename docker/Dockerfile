FROM debian:stable

WORKDIR /usr/local/services/galileo

ENV TZ=Asia/Shanghai \
    DEBIAN_FRONTEND=noninteractive \
    GOLANG_PROTOBUF_REGISTRATION_CONFLICT=warn

RUN ln -fs /usr/share/zoneinfo/${TZ} /etc/localtime \
    && echo ${TZ} > /etc/timezone \
    && dpkg-reconfigure --frontend noninteractive tzdata \
    && rm -rf /var/lib/apt/lists/*

RUN set -ex \
    && apt-get -qq update \
    && apt-get -qq install -y --no-install-recommends vim ca-certificates less \
       wget curl traceroute tcpdump iproute2 net-tools inetutils-ping iptables \
       telnet mtr ethtool netcat-openbsd iftop dnsutils \
    && apt-get clean \
    && apt-get autoclean \
    && mkdir -p bin etc/conf logs \
    && touch logs/galileo_cc.log \
    && chmod -R 777 logs/galileo_cc.log logs

ADD galileo_cc bin/

ENTRYPOINT ["./bin/galileo_cc"]

EXPOSE 5021