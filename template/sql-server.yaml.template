apiVersion: apps/v1
kind: "{{.WorkLoadKind}}"
metadata:
  namespace: "{{.Namespace}}"
  name: "{{.Name}}"
  {{- if .Labels}}
  labels:
    {{- range $k, $v := .Labels}}
    {{print $k ": " $v}}
    {{- end}}
  {{- end}}
  ownerReferences:
  - apiVersion: v1
    kind: Secret
    name: {{.OwnerRef.Name}}
    uid: {{.OwnerRef.Uid}}
spec:
  replicas: {{.Replicas}}
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 1
    type: RollingUpdate
  {{- if .Labels}}
  selector:
    matchLabels:
      {{- range $k, $v := .Labels}}
      {{print $k ": " $v}}
      {{- end}}
  {{- end}}
  {{- if .ServiceName}}
  serviceName: "{{.ServiceName}}"
  {{- end}}
  template:
    metadata:
      {{- if .Annotations}}
      annotations:
        {{- range $k, $v := .Annotations}}
        {{print $k ": " $v}}
        {{- end}}
      {{- end}}
      {{- if .Labels}}
      labels:
        {{- range $k, $v := .Labels}}
        {{print $k ": " $v}}
        {{- end}}
      {{- end}}
      ownerReferences:
      - apiVersion: v1
        kind: Secret
        name: {{.OwnerRef.Name}}
        uid: {{.OwnerRef.Uid}}
    spec:
      serviceAccountName: {{.ServiceAccountName}}
      imagePullSecrets:
      - name: qcloud
      {{- if .PriorityClassName}}
      priorityClassName: {{.PriorityClassName}}
      {{- end}}
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              {{- if .Labels}}
              matchLabels:
                {{- range $k, $v := .Labels}}
                {{print $k ": " $v}}
                {{- end}}
              {{- end}}
            topologyKey: kubernetes.io/hostname
      {{- if .NodeSelector}}
      nodeSelector:
        {{- range $k, $v := .NodeSelector}}
        {{print $k ": " $v}}
        {{- end}}
      {{- end}}
      {{- if .HostAliases}}
      hostAliases:
        {{- range $k, $v := .HostAliases}}
        {{print "- ip: " $k}}
        {{print "  hostnames:"}}
        {{print "  - " $v}}
        {{- end}}
      {{- end}}
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      containers:
      - name: app-container
        image: {{.AppImage}}
        imagePullPolicy: Always
        resources:
          limits:
            cpu: 400m
            memory: 1250Mi
          requests:
            cpu: 400m
            memory: 1250Mi
        env:
        {{- if .Env}}
        {{- range $k, $v := .Env}}
        {{print "- name: " $k}}
        {{print "  value: " $v}}
        {{- end}}
        {{- end}}
        - name: POD_IP
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: status.podIP
        - name: NODE_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: status.podIP
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        ports:
        - containerPort: 8081
          protocol: TCP
        livenessProbe:
          failureThreshold: 10
          httpGet:
            path: /Readiness/check
            port: 8081
            scheme: HTTP
          initialDelaySeconds: 30
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 5
        readinessProbe:
          failureThreshold: 3
          httpGet:
            path: /Readiness/check
            port: 8081
            scheme: HTTP
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 5
        securityContext:
          runAsUser: 1000
        volumeMounts:
          - mountPath: /opt/sql-server/conf
            name: sql-server-config-volume
          - mountPath: /data/logs/cluster_admin
            name: sql-server-log-volume
          - mountPath: /opt/sql-server/hadoop
            name: cluster-admin-hadoop-config-volume
      initContainers:
      - name: initializer
        command:
          - /bin/bash
          - -c
          - cp /tmp/conf/* /opt/conf && cp /tmp/logs/.controller.log.metadata /data/logs/cluster_admin && echo "{}" >/data/logs/cluster_admin/.ca.log.metadata {{- if .IsEKs}} && iptables -C OUTPUT -m conntrack --ctstate NEW -d ************/32 -p tcp -j DROP >/dev/null 2>&1 || iptables -t filter -I OUTPUT -m conntrack --ctstate NEW -d ************/32 -p tcp -j DROP && iptables -C OUTPUT -m conntrack --ctstate NEW -d ************/32 -p tcp -j DROP >/dev/null 2>&1 || iptables -t filter -I OUTPUT -m conntrack --ctstate NEW -d ************/32 -p tcp -j DROP {{- end}}
        image: {{.AppImage}}
        imagePullPolicy: Always
        name: initializer
        securityContext:
          privileged: true
          runAsUser: 0
        resources: {}
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
          - mountPath: /tmp/conf
            name: sql-server-configmap-volume
          - mountPath: /opt/conf
            name: sql-server-config-volume
          - mountPath: /tmp/logs
            name: sql-server-log-metadata-volume
          - mountPath: /data/logs/cluster_admin
            name: sql-server-log-volume
      volumes:
      - configMap:
          defaultMode: 420
          name: sql-server-config
        name: sql-server-configmap-volume
      - configMap:
          defaultMode: 420
          name: cluster-admin-hadoop-config
        name: cluster-admin-hadoop-config-volume
      - configMap:
          defaultMode: 420
          name: sql-server-log-metadata
        name: sql-server-log-metadata-volume
      - emptyDir: {}
        name: sql-server-config-volume
      - emptyDir: {}
        name: sql-server-log-volume