apiVersion: apps/v1
kind: "{{.WorkLoadKind}}"
metadata:
  namespace: "{{.Namespace}}"
  name: "{{.Name}}"
  {{- if .Labels}}
  labels:
    {{- range $k, $v := .Labels}}
    {{print $k ": " $v}}
    {{- end}}
  {{- end}}
  ownerReferences:
  - apiVersion: v1
    kind: Secret
    name: {{.OwnerRef.Name}}
    uid: {{.OwnerRef.Uid}}
spec:
  replicas: {{.Replicas}}
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 1
    type: RollingUpdate
  {{- if .Labels}}
  selector:
    matchLabels:
      {{- range $k, $v := .Labels}}
      {{print $k ": " $v}}
      {{- end}}
  {{- end}}
  {{- if .ServiceName}}
  serviceName: "{{.ServiceName}}"
  {{- end}}
  template:
    metadata:
      {{- if .Annotations}}
      annotations:
        {{- range $k, $v := .Annotations}}
        {{print $k ": " $v}}
        {{- end}}
      {{- end}}
      {{- if .Labels}}
      labels:
        {{- range $k, $v := .Labels}}
        {{print $k ": " $v}}
        {{- end}}
      {{- end}}
      ownerReferences:
      - apiVersion: v1
        kind: Secret
        name: {{.OwnerRef.Name}}
        uid: {{.OwnerRef.Uid}}
    spec:
      serviceAccountName: {{.ServiceAccountName}}
      imagePullSecrets:
      - name: qcloud
      {{- if .PriorityClassName}}
      priorityClassName: {{.PriorityClassName}}
      {{- end}}
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              {{- if .Labels}}
              matchLabels:
                {{- range $k, $v := .Labels}}
                {{print $k ": " $v}}
                {{- end}}
              {{- end}}
            topologyKey: kubernetes.io/hostname
      {{- if .NodeSelector}}
      nodeSelector:
        {{- range $k, $v := .NodeSelector}}
        {{print $k ": " $v}}
        {{- end}}
      {{- end}}
      {{- if .HostAliases}}
      hostAliases:
        {{- range $k, $v := .HostAliases}}
        {{print "- ip: " $k}}
        {{print "  hostnames:"}}
        {{print "  - " $v}}
        {{- end}}
      {{- end}}
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      containers:
      - name: app-container
        image: {{.AppImage}}
        imagePullPolicy: Always
        resources:
          limits:
            cpu: 600m
            memory: 2048Mi
          requests:
            cpu: 500m
            memory: 1536Mi
        env:
        {{- if .Env}}
        {{- range $k, $v := .Env}}
        {{print "- name: " $k}}
        {{print "  value: " $v}}
        {{- end}}
        {{- end}}
        - name: POD_IP
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: status.podIP
        - name: NODE_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: status.podIP
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        ports:
        - containerPort: 9020
          protocol: TCP
        livenessProbe:
          failureThreshold: 10
          httpGet:
            path: /
            port: 9020
            scheme: HTTP
          initialDelaySeconds: 30
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 5
        readinessProbe:
          failureThreshold: 3
          httpGet:
            path: /
            port: 9020
            scheme: HTTP
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 5
        securityContext:
          runAsUser: 1000
        volumeMounts:
        - mountPath: /opt/cluster-scheduler/conf
          name: cluster-scheduler-config-volume
        - mountPath: /opt/cluster-scheduler/hadoop
          name: cluster-scheduler-hadoop-config-volume
        - mountPath: /data/logs/cluster_admin
          name: cluster-scheduler-log-volume
      - name: loglistener
        image: {{.SidecarImage}}
        imagePullPolicy: Always
        resources:
          limits:
            cpu: 20m
            memory: 100Mi
          requests:
            cpu: 20m
            memory: 100Mi
        env:
        {{- if .Env}}
        {{- range $k, $v := .Env}}
        {{print "- name: " $k}}
        {{print "  value: " $v}}
        {{- end}}
        {{- end}}
        - name: _POD_IP_ADDRESS
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: status.podIP
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /usr/local/loglistener-2.3.2/etc
          name: loglistener-configmap-volume
        - mountPath: /data/logs/cluster_admin
          name: cluster-scheduler-log-volume
          readOnly: true
      initContainers:
      - name: initializer
        command:
        - /bin/sh
        - -c
        - cp /tmp/conf/* /opt/conf && mkdir -p /opt/conf/flow && mv /opt/conf/*.bpmn /opt/conf/flow/ && cp /tmp/logs/.controller.log.metadata /data/logs/cluster_admin && ln -snf /data/logs/cluster_admin/ca.log /data/logs/cluster_admin/controller.log && echo "{}" >/data/logs/cluster_admin/.ca.log.metadata
        image: {{.AppImage}}
        imagePullPolicy: Always
        resources: {}
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /tmp/conf
          name: cluster-scheduler-configmap-volume
        - mountPath: /opt/conf
          name: cluster-scheduler-config-volume
        - mountPath: /tmp/logs
          name: cluster-scheduler-log-metadata-volume
        - mountPath: /data/logs/cluster_admin
          name: cluster-scheduler-log-volume
      volumes:
      - configMap:
          defaultMode: 420
          name: cluster-scheduler-config
        name: cluster-scheduler-configmap-volume
      - configMap:
          defaultMode: 420
          name: cluster-admin-hadoop-config
        name: cluster-scheduler-hadoop-config-volume
      - configMap:
          defaultMode: 420
          name: loglistener-config-startuplog
        name: loglistener-configmap-volume
      - configMap:
          defaultMode: 420
          name: cluster-scheduler-log-metadata
        name: cluster-scheduler-log-metadata-volume
      - emptyDir: {}
        name: cluster-scheduler-config-volume
      - emptyDir: {}
        name: cluster-scheduler-log-volume