apiVersion: apps/v1
kind: "{{.WorkLoadKind}}"
metadata:
  namespace: "{{.Namespace}}"
  name: "{{.Name}}"
  {{- if .Labels}}
  labels:
    {{- range $k, $v := .Labels}}
    {{print $k ": " $v}}
    {{- end}}
  {{- end}}
  ownerReferences:
  - apiVersion: v1
    kind: Secret
    name: {{.OwnerRef.Name}}
    uid: {{.OwnerRef.Uid}}
spec:
  replicas: {{.Replicas}}
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 1
    type: RollingUpdate
  {{- if .Labels}}
  selector:
    matchLabels:
      {{- range $k, $v := .Labels}}
      {{print $k ": " $v}}
      {{- end}}
  {{- end}}
  {{- if .ServiceName}}
  serviceName: "{{.ServiceName}}"
  {{- end}}
  template:
    metadata:
      {{- if .Annotations}}
      annotations:
        {{- range $k, $v := .Annotations}}
        {{print $k ": " $v}}
        {{- end}}
      {{- end}}
      {{- if .Labels}}
      labels:
        {{- range $k, $v := .Labels}}
        {{print $k ": " $v}}
        {{- end}}
      {{- end}}
      ownerReferences:
      - apiVersion: v1
        kind: Secret
        name: {{.OwnerRef.Name}}
        uid: {{.OwnerRef.Uid}}
    spec:
      serviceAccountName: {{.ServiceAccountName}}
      imagePullSecrets:
      - name: qcloud
      {{- if .PriorityClassName}}
      priorityClassName: {{.PriorityClassName}}
      {{- end}}
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              {{- if .Labels}}
              matchLabels:
                {{- range $k, $v := .Labels}}
                {{print $k ": " $v}}
                {{- end}}
              {{- end}}
            topologyKey: kubernetes.io/hostname
      {{- if .NodeSelector}}
      nodeSelector:
        {{- range $k, $v := .NodeSelector}}
        {{print $k ": " $v}}
        {{- end}}
      {{- end}}
      {{- if .HostAliases}}
      hostAliases:
        {{- range $k, $v := .HostAliases}}
        {{print "- ip: " $k}}
        {{print "  hostnames:"}}
        {{print "  - " $v}}
        {{- end}}
      {{- end}}
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      containers:
      - name: app-container
        image: {{.AppImage}}
        imagePullPolicy: Always
        resources:
          limits:
            cpu: 400m
            memory: 1250Mi
          requests:
            cpu: 400m
            memory: 1250Mi
        env:
        {{- if .Env}}
        {{- range $k, $v := .Env}}
        {{print "- name: " $k}}
        {{print "  value: " $v}}
        {{- end}}
        {{- end}}
        - name: POD_IP
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: status.podIP
        - name: NODE_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: status.podIP
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        ports:
        - containerPort: 8600
          protocol: TCP
        livenessProbe:
          failureThreshold: 10
          httpGet:
            path: /
            port: 8600
            scheme: HTTP
          initialDelaySeconds: 30
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 5
        readinessProbe:
          failureThreshold: 3
          httpGet:
            path: /
            port: 8600
            scheme: HTTP
          periodSeconds: 1
          successThreshold: 1
          timeoutSeconds: 1
        securityContext:
          runAsUser: 1000
        volumeMounts:
        - mountPath: /opt/watchdog/conf
          name: watchdog-config-volume
        - mountPath: /opt/watchdog/hadoop
          name: watchdog-hadoop-config-volume
        - mountPath: /data/logs/cluster_admin
          name: watchdog-log-volume
      initContainers:
      - name: initializer
        command:
        - /bin/sh
        - -c
        - cp /tmp/conf/* /opt/conf && cp /tmp/logs/.controller.log.metadata /data/logs/cluster_admin && ln -snf /data/logs/cluster_admin/ca.log /data/logs/cluster_admin/controller.log
        image: busybox
        imagePullPolicy: Always
        resources: {}
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /tmp/conf
          name: watchdog-configmap-volume
        - mountPath: /opt/conf
          name: watchdog-config-volume
        - mountPath: /tmp/logs
          name: watchdog-log-metadata-volume
        - mountPath: /data/logs/cluster_admin
          name: watchdog-log-volume
      volumes:
      - configMap:
          defaultMode: 420
          name: watchdog-config
        name: watchdog-configmap-volume
      - configMap:
          defaultMode: 420
          name: cluster-admin-hadoop-config
        name: watchdog-hadoop-config-volume
      - configMap:
          defaultMode: 420
          name: watchdog-log-metadata
        name: watchdog-log-metadata-volume
      - emptyDir: {}
        name: watchdog-config-volume
      - emptyDir: {}
        name: watchdog-log-volume