apiVersion: apps/v1
kind: "{{.WorkLoadKind}}"
metadata:
  namespace: "{{.Namespace}}"
  name: "{{.Name}}"
  {{- if .Labels}}
  labels:
    {{- range $k, $v := .Labels}}
    {{print $k ": " $v}}
    {{- end}}
  {{- end}}
  ownerReferences:
  - apiVersion: v1
    kind: Secret
    name: {{.OwnerRef.Name}}
    uid: {{.OwnerRef.Uid}}
spec:
  replicas: {{.Replicas}}
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 1
    type: RollingUpdate
  {{- if .Labels}}
  selector:
    matchLabels:
      {{- range $k, $v := .Labels}}
      {{print $k ": " $v}}
      {{- end}}
  {{- end}}
  {{- if .ServiceName}}
  serviceName: "{{.ServiceName}}"
  {{- end}}
  template:
    metadata:
      {{- if .Annotations}}
      annotations:
        {{- range $k, $v := .Annotations}}
        {{print $k ": " $v}}
        {{- end}}
      {{- end}}
      {{- if .Labels}}
      labels:
        {{- range $k, $v := .Labels}}
        {{print $k ": " $v}}
        {{- end}}
      {{- end}}
      ownerReferences:
      - apiVersion: v1
        kind: Secret
        name: {{.OwnerRef.Name}}
        uid: {{.OwnerRef.Uid}}
    spec:
      serviceAccountName: {{.ServiceAccountName}}
      imagePullSecrets:
      - name: qcloud
      {{- if .PriorityClassName}}
      priorityClassName: {{.PriorityClassName}}
      {{- end}}
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              {{- if .Labels}}
              matchLabels:
                {{- range $k, $v := .Labels}}
                {{print $k ": " $v}}
                {{- end}}
              {{- end}}
            topologyKey: kubernetes.io/hostname
      {{- if .NodeSelector}}
      nodeSelector:
        {{- range $k, $v := .NodeSelector}}
        {{print $k ": " $v}}
        {{- end}}
      {{- end}}
      {{- if .HostAliases}}
      hostAliases:
        {{- range $k, $v := .HostAliases}}
        {{print "- ip: " $k}}
        {{print "  hostnames:"}}
        {{print "  - " $v}}
        {{- end}}
      {{- end}}
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      containers:
      - name: app-container
        image: {{.AppImage}}
        imagePullPolicy: Always
        resources:
          limits:
            cpu: 100m
            memory: 100Mi
          requests:
            cpu: 100m
            memory: 100Mi
        env:
        {{- if .Env}}
        {{- range $k, $v := .Env}}
        {{print "- name: " $k}}
        {{print "  value: " $v}}
        {{- end}}
        {{- end}}
        - name: POD_IP
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: status.podIP
        - name: NODE_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: status.podIP
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /opt/logs
          name: command-controller-log-volume
      volumes:
      - emptyDir: {}
        name: command-controller-log-volume