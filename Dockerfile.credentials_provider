FROM golang:1.15 as builder

RUN mkdir -p /go/src /go/bin && chmod -R 777 /go
ENV GOROOT /usr/local/go
ENV GOPATH /go
ENV PATH /go/bin:$PATH
ADD . /go/credentials_provider
WORKDIR /go/credentials_provider

RUN GO111MODULE=on GOOS=linux GOARCH=amd64 go build -mod=vendor -o credentials_provider src/main/credentials_provider/main.go

FROM debian:stable-slim


COPY --from=builder /go/credentials_provider/credentials_provider /credentials_provider
COPY --from=builder /go/credentials_provider/src/main/credentials_provider/start.sh /start.sh
RUN chmod +x /start.sh
