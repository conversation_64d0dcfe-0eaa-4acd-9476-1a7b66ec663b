create database if not exists online_galileo default character set utf8;
use online_galileo;
CREATE TABLE if not exists `Administrator`
(
    `AppId`             bigint(20)                      NOT NULL,
    `OwnerUin`          varchar(20) COLLATE utf8mb4_bin NOT NULL,
    `CreatorUin`        varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '记录创建子账号',
    `AuthSubAccountUin` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '授权子用户',
    `UpdateTime`        timestamp                       NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `CreateTime`        timestamp                       NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `Version`           bigint(25)                      NOT NULL,
    `Status`            tinyint(4)                      NOT NULL COMMENT '状态: 2 启用 1 停用',
    PRIMARY KEY (`AppId`, `AuthSubAccountUin`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin
  ROW_FORMAT = DYNAMIC;--
-- Table structure for table `AsyncJobInfo`
--

CREATE TABLE if not exists `AsyncJobInfo`
(
    `Id`         bigint(20)                       NOT NULL AUTO_INCREMENT,
    `SerialId`   varchar(100) COLLATE utf8mb4_bin NOT NULL,
    `Type`       tinyint(4)                                DEFAULT NULL COMMENT '类型 Etl预检测:1',
    `Status`     tinyint(4)                                DEFAULT NULL COMMENT '状态 1：等待  2:进行中  3:成功 4:失败',
    `Context`    mediumtext COLLATE utf8mb4_bin   NOT NULL COMMENT '任务上下文',
    `Params`     mediumtext COLLATE utf8mb4_bin   NOT NULL COMMENT '参数',
    `Result`     mediumtext COLLATE utf8mb4_bin   NOT NULL COMMENT '结果',
    `Handler`    varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '处理器code',
    `CreatorUin` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '创建人',
    `OwnerUin`   varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '子用户',
    `AppId`      bigint(20)                       NOT NULL COMMENT 'Appid',
    `Region`     varchar(30) COLLATE utf8mb4_bin  NOT NULL COMMENT '地区',
    `CreateTime` timestamp                        NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime` timestamp                        NULL     DEFAULT CURRENT_TIMESTAMP,
    `FinishTime` timestamp                        NULL     DEFAULT '0000-00-00 00:00:00',
    `StartTime`  timestamp                        NULL     DEFAULT '0000-00-00 00:00:00',
    PRIMARY KEY (`Id`) USING BTREE,
    KEY `SerialIdIndex` (`SerialId`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin
  ROW_FORMAT = DYNAMIC;--
-- Table structure for table `BillingOrder`
--

CREATE TABLE if not exists `BillingOrder`
(
    `Id`             int(11)      NOT NULL AUTO_INCREMENT,
    `AppId`          bigint(20)   NOT NULL,
    `CategoryId`     int(11)      NOT NULL,
    `DealName`       varchar(255) NOT NULL,
    `Uin`            varchar(255) NOT NULL,
    `OperateUin`     varchar(255) NOT NULL,
    `Region`         int(11)      NOT NULL,
    `ZoneId`         int(11)      NOT NULL,
    `ClusterId`      int(11)               DEFAULT NULL,
    `ClusterGroupId` int(11)               DEFAULT NULL,
    `ResourceId`     varchar(255)          DEFAULT NULL,
    `PayMode`        tinyint(4)            DEFAULT NULL,
    `TimeUnit`       varchar(5)   NOT NULL,
    `TimeSpan`       int(9)                DEFAULT NULL,
    `AutoRenewFlag`  tinyint(4)   NOT NULL,
    `SubProductCode` varchar(255) NOT NULL,
    `ComputeCu`      int(11)      NOT NULL,
    `FlowId`         bigint(20)   NOT NULL,
    `Status`         tinyint(4)   NOT NULL DEFAULT '1',
    `CreateTime`     datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`     datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`Id`) USING BTREE,
    UNIQUE KEY `DealName_2` (`DealName`) USING BTREE,
    KEY `DealName` (`DealName`) USING BTREE,
    KEY `FlowId` (`FlowId`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 277
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC;--
-- Table structure for table `BillingResource`
--

CREATE TABLE if not exists `BillingResource`
(
    `Id`                     int(11)      NOT NULL AUTO_INCREMENT,
    `ResourceId`             varchar(255)          DEFAULT NULL,
    `AppId`                  bigint(20)   NOT NULL,
    `Uin`                    varchar(255) NOT NULL,
    `FlowId`                 bigint(20)            DEFAULT NULL,
    `OperateUin`             varchar(255) NOT NULL,
    `Region`                 int(11)      NOT NULL,
    `ZoneId`                 int(11)      NOT NULL,
    `PayMode`                tinyint(4)            DEFAULT NULL,
    `ComputeCu`              int(11)      NOT NULL,
    `SubProductCode`         varchar(255) NOT NULL,
    `TimeUnit`               varchar(5)   NOT NULL,
    `TimeSpan`               int(9)                DEFAULT NULL,
    `AutoRenewFlag`          tinyint(4)   NOT NULL,
    `Status`                 tinyint(4)   NOT NULL DEFAULT '1',
    `CreateTime`             datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `IsolatedTimestamp`      datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `ExpireTime`             datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `GoodsDetail`            text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin,
    `International`          tinyint(4)            DEFAULT '0',
    `IsNeedManageNode`       tinyint(4)            DEFAULT '0' COMMENT 'is need extra 2CU ',
    `BillingResourceMode`    varchar(255)          DEFAULT '',
    `Duration`               varchar(255)          DEFAULT '',
    `ExclusiveSaleStartTime` datetime              DEFAULT '0000-00-00 00:00:00',
    PRIMARY KEY (`Id`) USING BTREE,
    KEY `ResourceId` (`ResourceId`) USING BTREE,
    KEY `index_0` (`AppId`, `Status`, `Region`, `ExpireTime`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 179
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC;--
-- Table structure for table `Bucket`
--

CREATE TABLE if not exists `Bucket`
(
    `Id`         bigint(20) NOT NULL AUTO_INCREMENT,
    `Region`     varchar(45)  DEFAULT NULL,
    `Status`     tinyint(4)   DEFAULT NULL,
    `MaxNum`     bigint(20)   DEFAULT NULL,
    `CurNum`     bigint(20)   DEFAULT NULL,
    `CurMaxQPS`  bigint(20)   DEFAULT '0',
    `Type`       tinyint(4)   DEFAULT NULL COMMENT 'Bucket 1-共享集群Flink状态存储 2-独占集群Flink状态存储 3-资源数据状态存储 4-集群日志状态存储',
    `BucketName` varchar(200) DEFAULT NULL,
    PRIMARY KEY (`Id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 10
  DEFAULT CHARSET = utf8;--
-- Table structure for table `BucketClusterRef`
--

CREATE TABLE if not exists `BucketClusterRef`
(
    `Id`             bigint(20) NOT NULL AUTO_INCREMENT,
    `ClusterGroupId` bigint(20)   DEFAULT NULL,
    `ClusterId`      bigint(20)   DEFAULT NULL,
    `BucketName`     varchar(200) DEFAULT NULL,
    `Status`         tinyint(4)   DEFAULT NULL,
    `CreatorUin`     varchar(20)  DEFAULT NULL,
    PRIMARY KEY (`Id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8;--
-- Table structure for table `BucketJobRef`
--

CREATE TABLE if not exists `BucketJobRef`
(
    `Id`         bigint(20) NOT NULL AUTO_INCREMENT,
    `BucketName` varchar(200) DEFAULT NULL,
    `JobId`      varchar(45)  DEFAULT NULL,
    `Status`     tinyint(4)   DEFAULT NULL,
    `Weight`     int(10)      DEFAULT NULL,
    `UseHdfs`    tinyint(4)   DEFAULT NULL,
    `CreatorUin` varchar(20)  DEFAULT NULL,
    PRIMARY KEY (`Id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8;--
-- Table structure for table `Cdb`
--

CREATE TABLE if not exists `Cdb`
(
    `Id`           bigint(20)  NOT NULL AUTO_INCREMENT,
    `CreateTime`   timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`   timestamp   NULL     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `ClusterId`    bigint(20)  NOT NULL COMMENT '所属的集群id',
    `InstanceId`   varchar(16) NOT NULL COMMENT '实例id',
    `InstanceName` varchar(32) NOT NULL COMMENT '实例名字',
    `Vip`          varchar(32)          DEFAULT NULL,
    `Vport`        int(11)              DEFAULT '0',
    `User`         varchar(32)          DEFAULT NULL,
    `Password`     varchar(64)          DEFAULT NULL COMMENT '加密后的密码',
    `Memory`       bigint(20)  NOT NULL COMMENT '内存大小，单位mb',
    `Volume`       bigint(20)  NOT NULL COMMENT '磁盘大小，单位gb',
    `Status`       int(11)     NOT NULL DEFAULT '0' COMMENT '0-创建中；1-运行中；4-隔离中；5-已隔离',
    PRIMARY KEY (`Id`),
    UNIQUE KEY `ClusterIdInstanceId` (`ClusterId`, `InstanceId`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 170
  DEFAULT CHARSET = utf8;--
-- Table structure for table `Cluster`
--

CREATE TABLE if not exists `Cluster`
(
    `Id`                    bigint(20)                      NOT NULL AUTO_INCREMENT,
    `UniqClusterId`         varchar(16) COLLATE utf8mb4_bin          DEFAULT NULL COMMENT 'EMR/TKE集群id',
    `ClusterGroupId`        bigint(20)                      NOT NULL,
    `CreatorUin`            varchar(20) COLLATE utf8mb4_bin NOT NULL,
    `Zone`                  varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou-3',
    `VpcId`                 varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'vpc-xxx',
    `SubnetId`              varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'subnet-xxx',
    `VpcCIDR`               varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '10.0.0.1/16',
    `RoleType`              tinyint(4)                      NOT NULL COMMENT '1：Active，2：Standby, -1：Decommissioning，-2：Decommissioned',
    `SchedulerType`         tinyint(4)                      NOT NULL COMMENT '调度器类型，1：EMR，2：TKE，3：TCS',
    `CuNum`                 smallint(20)                    NOT NULL COMMENT 'CU数量',
    `UsedCuNum`             int(20)                         NOT NULL COMMENT '已使用的CU数量',
    `DefaultCOSBucket`      varchar(255) COLLATE utf8mb4_bin         DEFAULT NULL COMMENT '默认的 COS 存储桶（作业可覆盖）',
    `Remark`                varchar(100) COLLATE utf8mb4_bin         DEFAULT NULL COMMENT '备注',
    `KubeConfig`            text COLLATE utf8mb4_bin COMMENT 'tke集群的kubeconfig',
    `CreateTime`            timestamp                       NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`            timestamp                       NULL     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `StopTime`              timestamp                       NOT NULL DEFAULT '0000-00-00 00:00:00',
    `WebUIPrefix`           varchar(256) COLLATE utf8mb4_bin         DEFAULT '',
    `ClusterConfig`         text COLLATE utf8mb4_bin COMMENT '集群特定配置',
    `FlinkConfig`           text COLLATE utf8mb4_bin,
    `FlinkVersion`          varchar(200) COLLATE utf8mb4_bin         DEFAULT NULL,
    `ClsLogSet`             varchar(255) COLLATE utf8mb4_bin         DEFAULT NULL,
    `ClsTopicId`            varchar(255) COLLATE utf8mb4_bin         DEFAULT NULL,
    `SupportedFeatures`     text COLLATE utf8mb4_bin,
    `SupportedFlinkVersion` varchar(255) COLLATE utf8mb4_bin         DEFAULT '[]',
    `LogMode`               tinyint(2)                      NOT NULL DEFAULT '0',
    `LogConfig`             varchar(512) COLLATE utf8mb4_bin         DEFAULT NULL COMMENT '集群日志配置',
    `DefaultLogCollectConf` varchar(255) COLLATE utf8mb4_bin         DEFAULT NULL COMMENT '集群默认采集配置',
    `ClusterExtendConfig`   text COLLATE utf8mb4_bin,
    `WebUIType`             int(11)                                  DEFAULT '0' COMMENT '0 公网访问，1 内网访问',
    `MemRatio`              int(11)                                  DEFAULT '4' COMMENT 'CU内存比例',
    `Cores`                 int(11)                                  DEFAULT '8' COMMENT '机型Cpu个数',
    `SupportedZoneSubnets`  varchar(1024) COLLATE utf8mb4_bin        DEFAULT '' COMMENT '共享集群支持区的子网',
    `CrossTenantEniMode`    int(8)                          NOT NULL DEFAULT '0' COMMENT '是否开启跨租户ENI模式',
    `LoadBalanceId`         varchar(20) COLLATE utf8mb4_bin          DEFAULT '',
    PRIMARY KEY (`Id`) USING BTREE,
    KEY `index_0` (`ClusterGroupId`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 189
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin
  ROW_FORMAT = DYNAMIC;--
-- Table structure for table `ClusterEvent`
--

CREATE TABLE if not exists `ClusterEvent`
(
    `Id`         bigint(20) unsigned              NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `ClusterId`  bigint(20)                       NOT NULL,
    `EventName`  varchar(200) COLLATE utf8mb4_bin NOT NULL,
    `EventValue` text COLLATE utf8mb4_bin,
    `OldValue`   text COLLATE utf8mb4_bin,
    `CreateTime` timestamp                        NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime` timestamp                        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`Id`),
    KEY `ClusterEvent` (`ClusterId`, `EventName`(191))
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='集群事件表';--
-- Table structure for table `ClusterGroup`
--

CREATE TABLE if not exists `ClusterGroup`
(
    `Id`                 bigint(20)                      NOT NULL AUTO_INCREMENT,
    `SerialId`           varchar(16) COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '例如 cluster-bu8nydsy',
    `Name`               varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '集群名',
    `AppId`              bigint(20)                      NOT NULL,
    `OwnerUin`           varchar(20) COLLATE utf8mb4_bin NOT NULL,
    `CreatorUin`         varchar(20) COLLATE utf8mb4_bin NOT NULL,
    `Region`             varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou',
    `Zone`               varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou-3',
    `Type`               tinyint(4)                      NOT NULL COMMENT '模式，1：共享集群，2：独占集群',
    `Status`             tinyint(4)                      NOT NULL COMMENT '集群状态，1：创建中，2：运行中',
    `CuNum`              int(20)                         NOT NULL COMMENT 'CU数量',
    `UsedCuNum`          int(20)                         NOT NULL COMMENT '已使用的CU数量',
    `CuMem`              tinyint(4)                      NOT NULL COMMENT 'CU内存规格类型，单位GB，只支持2,4,,8,16',
    `Remark`             varchar(100) COLLATE utf8mb4_bin         DEFAULT NULL COMMENT '备注333',
    `CreateTime`         timestamp                       NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`         timestamp                       NULL     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `StopTime`           timestamp                       NOT NULL DEFAULT '0000-00-00 00:00:00',
    `NetEnvironmentType` tinyint(4)                      NOT NULL COMMENT '网络环境，1：VPC，2：云支撑 3：VPCX 4: 私有云',
    `StartTime`          timestamp                       NOT NULL DEFAULT '0000-00-00 00:00:00',
    `NetEniType`         tinyint(4)                               DEFAULT '0' COMMENT '弹性网卡方案，0：POD弹性网卡，1：Node弹性网卡',
    `AgentSerialId`      varchar(16) COLLATE utf8mb4_bin          DEFAULT '' COMMENT '共享集群母集群 cluster-bu8nydsy',
    `SupportedZones`     varchar(1024) COLLATE utf8mb4_bin        DEFAULT '' COMMENT '共享集群支持的区',
    `CreateParam`        varchar(2048) COLLATE utf8mb4_bin        DEFAULT '' COMMENT '集群创建流程参数',
    `ResourceType`       tinyint(4)                               DEFAULT '0' COMMENT '0:独立使用资源,1:共享使用资源',
    `SendCredentialTime` timestamp                       NOT NULL DEFAULT '0000-00-00 00:00:00',
    `ZkType`             int(11)                                  DEFAULT '0' COMMENT '0 no auth, 1 Digest',
    `ParentSerialId`     varchar(45) COLLATE utf8mb4_bin          DEFAULT '' COMMENT '混合计费模式下按量付费集群所属的包年包月集群',
    `ZkRootPath`         varchar(255) COLLATE utf8mb4_bin         DEFAULT '' COMMENT '共享集群zk path隔离',
    `ZkUser`             varchar(255) COLLATE utf8mb4_bin         DEFAULT '',
    `ZkPass`             varchar(255) COLLATE utf8mb4_bin         DEFAULT '',
    `UniformConfig`      text COLLATE utf8mb4_bin COMMENT '共享集群的配置',
    `UniformCalcInfo`    text COLLATE utf8mb4_bin COMMENT '共享集群计算的结果',
    `UniformCalcTime`    timestamp                       NOT NULL DEFAULT '0000-00-00 00:00:00',
    PRIMARY KEY (`Id`) USING BTREE,
    UNIQUE KEY `SerialId` (`SerialId`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 190
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin
  ROW_FORMAT = DYNAMIC;--
-- Table structure for table `ClusterGroupPeerVpc`
--

CREATE TABLE if not exists `ClusterGroupPeerVpc`
(
    `Id`             bigint(20)                      NOT NULL AUTO_INCREMENT,
    `ClusterGroupId` bigint(20)                      NOT NULL,
    `VpcId`          varchar(20) COLLATE utf8mb4_bin NOT NULL,
    `SubnetId`       varchar(20) COLLATE utf8mb4_bin NOT NULL,
    `CcnId`          varchar(20) COLLATE utf8mb4_bin NOT NULL,
    `Status`         tinyint(4)                      NOT NULL COMMENT 'VPC状态，1：OK，-1：DELETE',
    `CreateTime`     timestamp                       NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`     timestamp                       NULL     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `AppId`          bigint(20)                      NOT NULL DEFAULT '0' COMMENT 'vpc所属AppId',
    `OwnerUin`       varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'vpc所属OwnerUin',
    PRIMARY KEY (`Id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 189
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin
  ROW_FORMAT = DYNAMIC;--
-- Table structure for table `ClusterMetric`
--

CREATE TABLE if not exists `ClusterMetric`
(
    `id`           bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `clusterId`    bigint(20) NOT NULL,
    `vcoreUsed`    int(4)              DEFAULT NULL,
    `vcoreTotal`   int(4)              DEFAULT NULL,
    `memoryUsed`   int(4)              DEFAULT NULL,
    `memoryTotal`  int(4)              DEFAULT NULL,
    `diskCapacity` bigint(20)          DEFAULT NULL,
    `diskUsed`     bigint(20)          DEFAULT NULL,
    `createTime`   timestamp  NOT NULL DEFAULT '0000-00-00 00:00:00',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC;--
-- Table structure for table `ClusterSession`
--

CREATE TABLE if not exists `ClusterSession`
(
    `Id`                   bigint(20)                      NOT NULL AUTO_INCREMENT,
    `ClusterGroupSerialId` varchar(16) COLLATE utf8mb4_bin          DEFAULT NULL COMMENT 'cluster-bu8nydsy',
    `AppId`                bigint(20)                      NOT NULL,
    `OwnerUin`             varchar(20) COLLATE utf8mb4_bin NOT NULL,
    `CreatorUin`           varchar(20) COLLATE utf8mb4_bin NOT NULL,
    `Region`               varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou',
    `Zone`                 varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou-3',
    `Status`               tinyint(4)                               DEFAULT NULL COMMENT 'session集群状态，1.停止 2. 开启中 3. 开启 4. 开启失败 5. 停止中 -2. 已删除',
    `CuNum`                float                           NOT NULL DEFAULT '0' COMMENT 'CU数量',
    `FlinkVersion`         varchar(50) COLLATE utf8mb4_bin          DEFAULT 'Flink-1.13',
    `Properties`           mediumtext COLLATE utf8mb4_bin COMMENT '高级参数',
    `JobManagerCuSpec`     float                                    DEFAULT '1',
    `TaskManagerCuSpec`    float                                    DEFAULT '1',
    `TaskManagerNum`       int(20)                                  DEFAULT NULL COMMENT 'Session集群可建TM数量',
    `CreateTime`           timestamp                       NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`           timestamp                       NULL     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `StartTime`            timestamp                       NOT NULL DEFAULT '0000-00-00 00:00:00',
    `StopTime`             timestamp                       NOT NULL DEFAULT '0000-00-00 00:00:00',
    `JobManagerCpu`        float                                    DEFAULT NULL,
    `JobManagerMem`        float                                    DEFAULT NULL,
    `TaskManagerCpu`       float                                    DEFAULT NULL,
    `TaskManagerMem`       float                                    DEFAULT NULL,
    PRIMARY KEY (`Id`) USING BTREE,
    UNIQUE KEY `SerialId` (`ClusterGroupSerialId`, `FlinkVersion`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 64
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin
  ROW_FORMAT = DYNAMIC;--
-- Table structure for table `ClusterSessionRef`
--

CREATE TABLE if not exists `ClusterSessionRef`
(
    `Id`               bigint(20) NOT NULL AUTO_INCREMENT,
    `SessionClusterId` bigint(20)          DEFAULT NULL COMMENT 'SessionCluster表ID',
    `ResourceId`       bigint(20)          DEFAULT NULL COMMENT '资源表的ID',
    `VersionId`        bigint(20)          DEFAULT '-1' COMMENT '资源版本,默认-1代表引用最新的资源',
    `Status`           tinyint(4)          DEFAULT '1' COMMENT '资源配置的状态 1:正常，-2:删除',
    `Type`             tinyint(4)          DEFAULT '1' COMMENT '引用类型 1:系统资源 2:用户资源',
    `CreateTime`       timestamp  NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`       timestamp  NULL     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`Id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 20
  DEFAULT CHARSET = utf8;--
-- Table structure for table `ClusterVersion`
--

CREATE TABLE if not exists `ClusterVersion`
(
    `Id`         bigint(20)   NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `ClusterId`  bigint(20)   NOT NULL COMMENT 'id in Cluster',
    `Version`    varchar(255) NOT NULL COMMENT 'v1 v2 v3 ... v1000',
    `CreateTime` timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime` timestamp    NULL     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`Id`),
    KEY `ClusterId` (`ClusterId`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 188
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC;--
-- Table structure for table `ClustersAllRegionCount`
--

CREATE TABLE if not exists `ClustersAllRegionCount`
(
    `Id`     int(11)     NOT NULL AUTO_INCREMENT,
    `AppId`  bigint(20)  NOT NULL,
    `Num`    bigint(20)  NOT NULL,
    `Region` varchar(20) NOT NULL,
    PRIMARY KEY (`Id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 671
  DEFAULT CHARSET = utf8;--
-- Table structure for table `Command`
--

CREATE TABLE if not exists `Command`
(
    `Id`               bigint(20)                      NOT NULL AUTO_INCREMENT,
    `ClusterGroupId`   bigint(20)                      NOT NULL,
    `ClusterId`        bigint(20)                      NOT NULL,
    `AppId`            bigint(20)                      NOT NULL,
    `OwnerUin`         varchar(20) COLLATE utf8mb4_bin NOT NULL,
    `CreatorUin`       varchar(20) COLLATE utf8mb4_bin NOT NULL,
    `Region`           varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou',
    `Zone`             varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou-3',
    `JobId`            bigint(20)                      NOT NULL,
    `JobName`          varchar(50) COLLATE utf8mb4_bin          DEFAULT NULL,
    `JobSerialId`      varchar(20) COLLATE utf8mb4_bin          DEFAULT NULL,
    `JobConfigId`      bigint(20) unsigned                      DEFAULT NULL,
    `RunningOrderId`   int(11)                                  DEFAULT NULL,
    `Action`           tinyint(4)                      NOT NULL,
    `JobType`          tinyint(4)                      NOT NULL COMMENT '作业类型，1：SQL，2：JAR',
    `JobRegressStatus` tinyint(4)                      NOT NULL COMMENT '作业回退状态',
    `JobNextStatus`    tinyint(4)                      NOT NULL COMMENT '作业下一个状态',
    `Params`           mediumtext COLLATE utf8mb4_bin COMMENT '命令参数',
    `Status`           tinyint(4)                      NOT NULL COMMENT '作业状态，0：未初始化，1：未发布，2：操作中，3：运行中，4：停止，5：暂停，-1：故障，-2：删除',
    `CreateTime`       timestamp                       NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`       timestamp                       NULL     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `FetchTime`        timestamp                       NOT NULL DEFAULT '0000-00-00 00:00:00',
    `AckTime`          timestamp                       NOT NULL DEFAULT '0000-00-00 00:00:00',
    `FinishTime`       timestamp                       NOT NULL DEFAULT '0000-00-00 00:00:00',
    PRIMARY KEY (`Id`) USING BTREE,
    KEY `indx1` (`JobId`, `Status`),
    KEY `index_0` (`ClusterGroupId`, `Status`, `CreateTime`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 53636
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin
  ROW_FORMAT = DYNAMIC;--
-- Table structure for table `ConfigurationCenter`
--

CREATE TABLE if not exists `ConfigurationCenter`
(
    `Id`                 bigint(20)                       NOT NULL AUTO_INCREMENT,
    `ConfigurationKey`   varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '配置key',
    `ConfigurationValue` text COLLATE utf8mb4_bin         NOT NULL COMMENT '配置Value',
    `CreateTime`         timestamp                        NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`         timestamp                        NULL     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `Type`               tinyint(4)                                DEFAULT '0',
    PRIMARY KEY (`Id`) USING BTREE,
    UNIQUE KEY `unique_key` (`ConfigurationKey`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 908
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin
  ROW_FORMAT = DYNAMIC;--
-- Table structure for table `ConfigurationCenter_18`
--

CREATE TABLE if not exists `ConfigurationCenter_18`
(
    `Id`                 bigint(20)                       NOT NULL AUTO_INCREMENT,
    `ConfigurationKey`   varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '配置key',
    `ConfigurationValue` text COLLATE utf8mb4_bin         NOT NULL COMMENT '配置Value',
    `CreateTime`         timestamp                        NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`         timestamp                        NULL     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `Type`               tinyint(4)                                DEFAULT '0',
    PRIMARY KEY (`Id`) USING BTREE,
    UNIQUE KEY `unique_key` (`ConfigurationKey`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin
  ROW_FORMAT = DYNAMIC;--
-- Table structure for table `ConnectionResource`
--

CREATE TABLE if not exists `ConnectionResource`
(
    `Id`          bigint(20)   NOT NULL AUTO_INCREMENT COMMENT '主键',
    `SerialId`    varchar(12)  NOT NULL DEFAULT '' COMMENT '数据连接资源Id 例如: con-xxxxx',
    `AppId`       bigint(20)   NOT NULL DEFAULT '-1' COMMENT '数据连接所属AppId',
    `OwnerUin`    varchar(20)  NOT NULL DEFAULT '' COMMENT '数据连接所属Uin',
    `CreatorUin`  varchar(20)  NOT NULL DEFAULT '' COMMENT '创建数据连接的Uin',
    `Region`      varchar(20)  NOT NULL DEFAULT '' COMMENT '数据连接所属区域',
    `ItemSpaceId` bigint(20)   NOT NULL DEFAULT '0' COMMENT '数据连接所属空间',
    `Name`        varchar(350) NOT NULL DEFAULT '' COMMENT '数据连接名称',
    `Type`        int(30)      NOT NULL DEFAULT '1' COMMENT '数据连接类型 1-Mysql 2-PostgreSQL 3-ClickHouse 4-Elasticsearch6 5-Elasticsearch7 6-Oracle 7-Sqlserver 8-Kafka 9-MongoDB',
    `Origin`      int(10)      NOT NULL DEFAULT '1' COMMENT '数据连接来源 1-腾讯云 2-IP连接',
    `InstanceId`  varchar(100) NOT NULL DEFAULT '' COMMENT '连接实例Id',
    `Properties`  text COMMENT '数据连接属性',
    `Username`    varchar(64)  NOT NULL DEFAULT '' COMMENT '用户名',
    `Password`    varchar(64)  NOT NULL DEFAULT '' COMMENT '加密后的密码',
    `Status`      tinyint(1)   NOT NULL DEFAULT '1' COMMENT '-2-已删除；1-运行中',
    `CreateTime`  timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`  timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`Id`),
    UNIQUE KEY `SerialIdUinque` (`SerialId`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 4
  DEFAULT CHARSET = utf8 COMMENT ='数据连接资源表';--
-- Table structure for table `CuMemWhiteList`
--

CREATE TABLE if not exists `CuMemWhiteList`
(
    `Id`            bigint(20)                      NOT NULL AUTO_INCREMENT,
    `AppId`         bigint(20)                      NOT NULL,
    `SubAccountUin` varchar(20) COLLATE utf8mb4_bin NOT NULL,
    PRIMARY KEY (`Id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin
  ROW_FORMAT = DYNAMIC;--
-- Table structure for table `CvmSaleConf`
--

CREATE TABLE if not exists `CvmSaleConf`
(
    `Id`           bigint(20)                                            NOT NULL AUTO_INCREMENT,
    `CreateTime`   timestamp                                             NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`   timestamp                                             NULL     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `Region`       varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou',
    `Zone`         varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou-3',
    `InstanceType` varchar(128)                                          NOT NULL DEFAULT '' COMMENT '实例规格',
    `Cpu`          int(32)                                               NOT NULL DEFAULT '0' COMMENT 'cpu',
    `Memory`       int(32)                                               NOT NULL DEFAULT '0' COMMENT '内存：GB',
    `Status`       int(11)                                               NOT NULL DEFAULT '1' COMMENT '0-不开放，1-开放',
    `Generation`   int(11)                                                        DEFAULT '1' COMMENT 'cvm内核版本,1:kvm1.0,3:kvm3.0',
    `Price`        int(11)                                               NOT NULL DEFAULT '0' COMMENT '机器的价格',
    PRIMARY KEY (`Id`),
    UNIQUE KEY `ZoneInstanceType` (`Zone`, `InstanceType`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 58
  DEFAULT CHARSET = utf8;--
-- Table structure for table `DebugConfig`
--

CREATE TABLE if not exists `DebugConfig`
(
    `id`           bigint(20) NOT NULL AUTO_INCREMENT,
    `createTime`   datetime                          DEFAULT NULL,
    `jobSerialId`  varchar(255) COLLATE utf8mb4_bin  DEFAULT NULL,
    `sqlStatement` varchar(5120) COLLATE utf8mb4_bin DEFAULT NULL,
    `updateTime`   datetime                          DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1261
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin
  ROW_FORMAT = DYNAMIC;--
-- Table structure for table `DebugJob`
--

CREATE TABLE if not exists `DebugJob`
(
    `id`         bigint(20) NOT NULL AUTO_INCREMENT,
    `attemptId`  int(11)    NOT NULL,
    `configId`   bigint(20) DEFAULT NULL,
    `createTime` datetime   DEFAULT NULL,
    `returnCode` int(11)    NOT NULL,
    `returnMsg`  mediumtext COLLATE utf8mb4_bin,
    `status`     int(11)    NOT NULL,
    `updateTime` datetime   DEFAULT NULL,
    `isShare`    int(11)    DEFAULT '1',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `index_0` (`status`, `isShare`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1261
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin
  ROW_FORMAT = DYNAMIC;--
-- Table structure for table `DebugResourceRef`
--

CREATE TABLE if not exists `DebugResourceRef`
(
    `Id`          bigint(20) NOT NULL AUTO_INCREMENT,
    `ResourceId`  varchar(45) DEFAULT NULL,
    `JobConfigId` bigint(20)  DEFAULT NULL,
    `VersionId`   bigint(20)  DEFAULT NULL,
    `Status`      int(11)     DEFAULT NULL,
    `UsageType`   int(11)     DEFAULT NULL,
    `CreateTime`  datetime    DEFAULT NULL,
    `UpdateTime`  datetime    DEFAULT NULL,
    PRIMARY KEY (`Id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 4
  DEFAULT CHARSET = utf8;--
-- Table structure for table `DebugSink`
--

CREATE TABLE if not exists `DebugSink`
(
    `id`         bigint(20) NOT NULL AUTO_INCREMENT,
    `debugJobId` bigint(20)                       DEFAULT NULL,
    `tableName`  varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
    `url`        varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 44
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin
  ROW_FORMAT = DYNAMIC;--
-- Table structure for table `DebugSource`
--

CREATE TABLE if not exists `DebugSource`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT,
    `configId`       bigint(20)                       DEFAULT NULL,
    `content`        varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
    `fieldDelimiter` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
    `inputType`      varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
    `fileName`       varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
    `tableName`      varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
    `updateTime`     datetime                         DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 40
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin
  ROW_FORMAT = DYNAMIC;--
-- Table structure for table `DependencyFolderTree`
--

CREATE TABLE if not exists `DependencyFolderTree`
(
    `Id`          bigint(20)                      NOT NULL AUTO_INCREMENT,
    `ItemSpaceId` bigint(20)                      NOT NULL DEFAULT '0' COMMENT '项目空间ID',
    `FolderId`    varchar(15) COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '例如 folder-bu8nydsy',
    `FolderName`  varchar(50) CHARACTER SET utf8  NOT NULL COMMENT '文件夹名',
    `ParentId`    varchar(15) COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '例如 folder-bu8nyd75',
    `AppId`       bigint(20)                      NOT NULL,
    `Region`      varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou',
    `CreatorUin`  varchar(20) COLLATE utf8mb4_bin NOT NULL,
    `OwnerUin`    varchar(20) COLLATE utf8mb4_bin NOT NULL,
    `CreateTime`  timestamp                       NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`  timestamp                       NULL     DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`Id`) USING BTREE,
    UNIQUE KEY `FolderId` (`FolderId`) USING BTREE,
    UNIQUE KEY `FolderName_ParentId` (`FolderName`, `ParentId`, `AppId`, `Region`, `ItemSpaceId`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 323
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin
  ROW_FORMAT = DYNAMIC;--
-- Table structure for table `EksResource`
--

CREATE TABLE if not exists `EksResource`
(
    `Id`           int(11) unsigned NOT NULL AUTO_INCREMENT,
    `SerialId`     varchar(100)     NOT NULL,
    `ActualCpu`    float                 DEFAULT NULL,
    `ActualMem`    float                 DEFAULT NULL,
    `ActualPodNum` int(5)                DEFAULT NULL,
    `CreateTime`   timestamp        NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`   timestamp        NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `UsedCpu`      float                 DEFAULT NULL,
    `UsedMem`      float                 DEFAULT NULL,
    PRIMARY KEY (`Id`),
    UNIQUE KEY `SerialId` (`SerialId`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;--
-- Table structure for table `EksSettle`
--

CREATE TABLE if not exists `EksSettle`
(
    `Id`           bigint(20) unsigned             NOT NULL AUTO_INCREMENT,
    `ResourceId`   varchar(50) COLLATE utf8mb4_bin NOT NULL,
    `StartTime`    timestamp                       NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `StopTime`     timestamp                       NOT NULL DEFAULT '0000-00-00 00:00:00',
    `CreateTime`   timestamp                       NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`   timestamp                       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `RunningCuNum` float                                    DEFAULT '0',
    `AppId`        bigint(20)                      NOT NULL,
    `Uid`          varchar(55) COLLATE utf8mb4_bin NOT NULL,
    PRIMARY KEY (`Id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1011
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='统一资源池混合计费，记录按量付费流量';--
-- Table structure for table `Emr`
--

CREATE TABLE if not exists `Emr`
(
    `Id`           bigint(20)  NOT NULL AUTO_INCREMENT,
    `CreateTime`   timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`   timestamp   NULL     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `ClusterId`    bigint(20)  NOT NULL COMMENT '所属的集群id',
    `InstanceId`   varchar(16) NOT NULL COMMENT '实例id',
    `InstanceName` varchar(32) NOT NULL COMMENT '实例名字',
    `CoreDiskSize` bigint(20)  NOT NULL COMMENT 'core磁盘大小，单位gb',
    `Config`       text COMMENT 'emr配置信息',
    `Status`       int(11)     NOT NULL DEFAULT '0' COMMENT '2-运行中；3-创建中',
    PRIMARY KEY (`Id`),
    UNIQUE KEY `ClusterId` (`ClusterId`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8;--
-- Table structure for table `EventAlert`
--

CREATE TABLE if not exists `EventAlert`
(
    `id`                           bigint(20)    NOT NULL AUTO_INCREMENT,
    `appId`                        int(11)       NOT NULL COMMENT '用户的 AppId',
    `region`                       varchar(30)            DEFAULT NULL,
    `name`                         varchar(100)           DEFAULT NULL COMMENT '云监控事件告警中录入的事件名',
    `type`                         varchar(45)            DEFAULT NULL COMMENT '事件类型. 1 启动停止 2 运行失败 3 快照失败 4 作业异常 (有各种子类型)',
    `runningOrderIdOnTrigger`      int(11)                DEFAULT NULL COMMENT '触发异常事件时的运行实例 RunningOrderId',
    `runningOrderIdOnRecovery`     int(11)                DEFAULT NULL COMMENT '异常事件恢复时的运行实例 RunningOrderId',
    `status`                       int(11)                DEFAULT '1' COMMENT '废弃字段, 与 isAlertOn 含义相同',
    `message`                      text,
    `clusterId`                    int(11)       NOT NULL COMMENT '作业的集群ID',
    `jobId`                        varchar(20)   NOT NULL COMMENT '作业的名字',
    `jobName`                      varchar(1000) NOT NULL COMMENT '作业的序列号',
    `isAlertOn`                    int(11)       NOT NULL COMMENT '0 已恢复，1 未恢复',
    `createTime`                   timestamp     NOT NULL DEFAULT '0000-00-00 00:00:00',
    `recoverTime`                  timestamp     NOT NULL DEFAULT '0000-00-00 00:00:00',
    `syncStatus`                   int(11)                DEFAULT '0' COMMENT '是否已同步到inlong，0-未同步，1-已同步',
    `jobIdRunningOrderIdOnTrigger` varchar(255)           DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    KEY `jobId_isAlertOn` (`jobId`, `isAlertOn`),
    KEY `clusterId_isAlertOn` (`clusterId`, `isAlertOn`),
    KEY `index_jobId_onTrigger_type` (`jobId`, `runningOrderIdOnTrigger`, `type`),
    KEY `index_jobIdRunningOrderIdOnTrigger_time` (`jobIdRunningOrderIdOnTrigger`, `type`, `createTime`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 798416
  DEFAULT CHARSET = utf8;--
-- Table structure for table `FlinkUiAuthInfo`
--

CREATE TABLE if not exists `FlinkUiAuthInfo`
(
    `Id`             bigint(20)   NOT NULL AUTO_INCREMENT,
    `CreateTime`     timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`     timestamp    NULL     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `ClusterGroupId` bigint(20)   NOT NULL COMMENT '所属的集群id',
    `User`           varchar(32)  NOT NULL DEFAULT 'admin',
    `Password`       varchar(256) NOT NULL DEFAULT '',
    PRIMARY KEY (`Id`),
    UNIQUE KEY `ClusterGroupId` (`ClusterGroupId`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 3132
  DEFAULT CHARSET = utf8;--
-- Table structure for table `FlinkUiCLB`
--

CREATE TABLE if not exists `FlinkUiCLB`
(
    `Id`             bigint(20)                       NOT NULL AUTO_INCREMENT,
    `Region`         varchar(50) COLLATE utf8mb4_bin  NOT NULL COMMENT '地域',
    `LoadBalancerId` varchar(20) COLLATE utf8mb4_bin  NOT NULL COMMENT '负载均衡实例 ID',
    `ListenerId`     varchar(20) COLLATE utf8mb4_bin  NOT NULL COMMENT '监听器 ID',
    `PublicIp`       varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '公网 IP 地址',
    `VpcId`          varchar(20) COLLATE utf8mb4_bin  NOT NULL COMMENT 'VPC ID',
    `SubnetId`       varchar(20) COLLATE utf8mb4_bin  NOT NULL COMMENT '子网 ID',
    `CreateTime`     timestamp                        NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`     timestamp                        NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`Id`) USING BTREE,
    UNIQUE KEY `uk_Region` (`Region`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 6
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin
  ROW_FORMAT = DYNAMIC;--
-- Table structure for table `InnerUserTransition`
--

CREATE TABLE if not exists `InnerUserTransition`
(
    `Id`              bigint(20)                       NOT NULL AUTO_INCREMENT,
    `AppId`           bigint(20)                       NOT NULL,
    `OwnerAccountUin` varchar(20) COLLATE utf8mb4_bin  NOT NULL,
    `SubAccountUin`   varchar(20) COLLATE utf8mb4_bin  NOT NULL,
    `JarFilePath`     varchar(500) COLLATE utf8mb4_bin NOT NULL COMMENT 'JAR作业路径',
    `ShipFiles`       varchar(500) COLLATE utf8mb4_bin NOT NULL COMMENT '依赖JAR包路径',
    `EntrypointClass` varchar(500) COLLATE utf8mb4_bin NOT NULL COMMENT '主类',
    `Status`          tinyint(4)                       NOT NULL COMMENT '状态，1：active，-1: inactive',
    PRIMARY KEY (`Id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin
  ROW_FORMAT = DYNAMIC;--
-- Table structure for table `ItemSpace`
--

CREATE TABLE if not exists `ItemSpace`
(
    `Id`            bigint(20)                      NOT NULL AUTO_INCREMENT,
    `SerialId`      varchar(60) COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '例如 spc-XXX',
    `AppId`         bigint(20)                      NOT NULL,
    `OwnerUin`      varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '创建空间的主账号uin',
    `CreatorUin`    varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '创建空间的子账号uin(如果是主账号创建,就是主账号uin)',
    `ItemSpaceName` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '项目空间名称',
    `Region`        varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '地域',
    `UpdateTime`    timestamp                       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `CreateTime`    timestamp                       NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `Status`        tinyint(4)                      NOT NULL COMMENT '空间状态 1 未初始化 2 可用  -1 已删除',
    `Description`   varchar(256) COLLATE utf8mb4_bin         DEFAULT NULL COMMENT '空间描述',
    `ProjectId`     bigint(20)                      NOT NULL DEFAULT '0' COMMENT 'Wedata ProjectId',
    PRIMARY KEY (`Id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 30
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin
  ROW_FORMAT = DYNAMIC;--
-- Table structure for table `ItemSpacesClusters`
--

CREATE TABLE if not exists `ItemSpacesClusters`
(
    `Id`             bigint(20)                      NOT NULL AUTO_INCREMENT,
    `AppId`          bigint(20)                      NOT NULL,
    `ItemSpaceId`    bigint(20)                      NOT NULL,
    `Region`         varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '地域',
    `ClusterGroupId` bigint(20)                      NOT NULL COMMENT '集群组ID',
    `UpdateTime`     timestamp                       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `CreateTime`     timestamp                       NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `Status`         tinyint(4)                      NOT NULL COMMENT '状态: 2 启用 1 停用',
    PRIMARY KEY (`Id`) USING BTREE,
    KEY `ItemSpaceId` (`ItemSpaceId`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 329
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin
  ROW_FORMAT = DYNAMIC;--
-- Table structure for table `Job`
--

CREATE TABLE if not exists `Job`
(
    `Id`                       bigint(20)                      NOT NULL AUTO_INCREMENT,
    `SerialId`                 varchar(12) COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '例如 cql-bu8nydsy',
    `AppId`                    bigint(20)                      NOT NULL,
    `OwnerUin`                 varchar(20) COLLATE utf8mb4_bin NOT NULL,
    `CreatorUin`               varchar(20) COLLATE utf8mb4_bin NOT NULL,
    `Region`                   varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou',
    `Zone`                     varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou-3',
    `Name`                     varchar(100) COLLATE utf8mb4_bin         DEFAULT NULL,
    `Status`                   tinyint(4)                      NOT NULL COMMENT '作业状态，0：未初始化，1：未发布，2：操作中，3：运行中，4：停止，5：暂停，-1：故障，-2：删除',
    `Type`                     tinyint(4)                      NOT NULL COMMENT '作业类型，0：SQL，1：JAR',
    `ClusterGroupId`           smallint(20)                    NOT NULL,
    `ClusterId`                smallint(20)                    NOT NULL,
    `ItemSpaceId`              bigint(20)                      NOT NULL DEFAULT '0' COMMENT '项目空间ID',
    `TmRunningCuNum`           int(20)                                  DEFAULT NULL,
    `CuMem`                    tinyint(4)                      NOT NULL COMMENT 'CU内存规格类型，单位GB，只支持2,4,,8,16',
    `LastOpResult`             mediumtext COLLATE utf8mb4_bin COMMENT '作业调度结果',
    `Remark`                   varchar(100) COLLATE utf8mb4_bin         DEFAULT NULL COMMENT '备注',
    `CreateTime`               timestamp                       NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`               timestamp                       NULL     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `StartTime`                timestamp                       NOT NULL DEFAULT '0000-00-00 00:00:00',
    `StopTime`                 timestamp                       NOT NULL DEFAULT '0000-00-00 00:00:00',
    `TotalRunMillis`           bigint(20)                               DEFAULT '0' COMMENT '作业累计运行时间',
    `PublishedJobConfigId`     bigint(20)                               DEFAULT '0',
    `LatestJobConfigId`        bigint(20)                               DEFAULT '0',
    `LatestJobConfigVersionId` bigint(20)                               DEFAULT '0',
    `JmRunningCuNum`           int(11)                                  DEFAULT '0',
    `LastPublishedJobConfigId` bigint(20)                               DEFAULT '-1',
    `JobRunningOrderId`        bigint(20)                               DEFAULT '0' COMMENT '该作业总共运行了多少次，包含重启，此值等于JobInstance的数量',
    `FolderId`                 varchar(15) COLLATE utf8mb4_bin          DEFAULT 'root' COMMENT '例如 folder-bu8nydsy',
    `FlinkVersion`             varchar(255) COLLATE utf8mb4_bin         DEFAULT '',
    `LastScaleTime`            timestamp                       NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '上一次完成扩缩容的时间',
    `Description`              varchar(200) COLLATE utf8mb4_bin         DEFAULT NULL COMMENT '作业描述',
    `ManageType`               varchar(20) COLLATE utf8mb4_bin          DEFAULT 'internal',
    `RunningCu`                float                                    DEFAULT '0',
    `FlinkJobType`             int(11)                                  DEFAULT '0',
    `RunningCpu`               float                                    DEFAULT NULL,
    `RunningMem`               float                                    DEFAULT NULL,
    `ProgressDesc`             varchar(255) COLLATE utf8mb4_bin         DEFAULT '' COMMENT '作业在操作中的时候，提示用户是启动中，还是停止中，引导用户',
    PRIMARY KEY (`Id`) USING BTREE,
    UNIQUE KEY `SerialId` (`SerialId`) USING BTREE,
    KEY `index_0` (`AppId`, `Status`),
    KEY `index_1` (`Status`, `ClusterId`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1404
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin
  ROW_FORMAT = DYNAMIC;--
-- Table structure for table `JobConfig`
--

CREATE TABLE if not exists `JobConfig`
(
    `Id`                      bigint(20) unsigned             NOT NULL AUTO_INCREMENT,
    `CreatorUin`              varchar(20) COLLATE utf8mb4_bin NOT NULL,
    `JobId`                   bigint(20)                      NOT NULL COMMENT '流计算 Job 表的 ID, 不是 Flink 的 JobId',
    `VersionId`               bigint(20)                      NOT NULL,
    `EntrypointClass`         varchar(500) COLLATE utf8mb4_bin         DEFAULT NULL COMMENT 'JAR 包的主类名',
    `ProgramArgs`             mediumtext COLLATE utf8mb4_bin COMMENT '作业的参数 以空格分隔',
    `CheckpointInterval`      bigint(20)                      NOT NULL DEFAULT '-1' COMMENT 'Checkpoint间隔',
    `SqlCode`                 mediumtext COLLATE utf8mb4_bin COMMENT 'SQL代码',
    `Status`                  tinyint(4)                      NOT NULL DEFAULT '0' COMMENT '0: 使用中，-1：已删除',
    `Remark`                  varchar(100) COLLATE utf8mb4_bin         DEFAULT NULL COMMENT 'JobConfig 的注释',
    `CreateTime`              timestamp                       NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `DefaultParallelism`      int(11)                                  DEFAULT '1',
    `Properties`              mediumtext COLLATE utf8mb4_bin,
    `MaxParallelism`          int(11)                                  DEFAULT '128',
    `DeletorUin`              varchar(20) COLLATE utf8mb4_bin          DEFAULT NULL,
    `LogCollect`              tinyint(4)                      NOT NULL DEFAULT '0' COMMENT '是否开启日志采集',
    `UpdateTime`              timestamp                       NOT NULL DEFAULT '0000-00-00 00:00:00',
    `COSBucket`               varchar(255) COLLATE utf8mb4_bin         DEFAULT NULL COMMENT '作业关联的 COS Bucket 信息',
    `JmCuSpec`                float                                    DEFAULT '1',
    `TmCuSpec`                float                                    DEFAULT '1',
    `ClsLogsetId`             varchar(64) COLLATE utf8mb4_bin          DEFAULT NULL COMMENT 'CLS日志集',
    `ClsTopicId`              varchar(64) COLLATE utf8mb4_bin          DEFAULT NULL COMMENT 'CLS日志主题',
    `PythonVersion`           varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '',
    `LogLevel`                varchar(8) COLLATE utf8mb4_bin  NOT NULL COMMENT '作业运行日志级别',
    `AutoRecover`             int(9)                          NOT NULL DEFAULT '1' COMMENT '作业失败自动恢复',
    `UseOldSysConnector`      int(9)                          NOT NULL DEFAULT '0' COMMENT '作业使用历史connector',
    `TraceModeConfiguration`  varchar(2000) COLLATE utf8mb4_bin        DEFAULT '',
    `CheckpointRetainedNum`   int(11)                                  DEFAULT '0',
    `JobGraph`                text COLLATE utf8mb4_bin,
    `ClazzLevels`             text COLLATE utf8mb4_bin,
    `ExpertModeConfiguration` longtext COLLATE utf8mb4_bin,
    `EsServerlessIndex`       varchar(64) COLLATE utf8mb4_bin          DEFAULT NULL COMMENT 'ES索引Id',
    `EsServerlessSpace`       varchar(64) COLLATE utf8mb4_bin          DEFAULT NULL COMMENT 'ES空间Id',
    `FlinkVersion`            varchar(50) COLLATE utf8mb4_bin          DEFAULT '' COMMENT 'flink 版本',
    `JobManagerCpu`           float                                    DEFAULT NULL,
    `JobManagerMem`           float                                    DEFAULT NULL,
    `TaskManagerCpu`          float                                    DEFAULT NULL,
    `TaskManagerMem`          float                                    DEFAULT NULL,
    `LibConfig`               text COLLATE utf8mb4_bin COMMENT 'yarn接口对接的依赖配置',
    PRIMARY KEY (`Id`) USING BTREE,
    KEY `index_0` (`Status`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 5581
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin
  ROW_FORMAT = DYNAMIC;--
-- Table structure for table `JobInsMetricMeta`
--

CREATE TABLE if not exists `JobInsMetricMeta`
(
    `Id`            bigint(20)   NOT NULL AUTO_INCREMENT,
    `JobSerialId`   varchar(16)  NOT NULL,
    `JobInstanceId` bigint(20)   NOT NULL,
    `MetricName`    varchar(128) NOT NULL,
    `Metas`         text,
    `CreateTime`    timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `UpdateTime`    timestamp    NOT NULL DEFAULT '0000-00-00 00:00:00',
    PRIMARY KEY (`Id`),
    KEY `idx_job_metric` (`JobSerialId`, `MetricName`, `CreateTime`),
    KEY `idx_time` (`CreateTime`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 23
  DEFAULT CHARSET = utf8;--
-- Table structure for table `JobInstance`
--

CREATE TABLE if not exists `JobInstance`
(
    `Id`                 bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '相当于 JobRuntimeID, 标识着Galileo一个唯一的运行时实例',
    `JobId`              bigint(20)          NOT NULL COMMENT '流计算 Job 表的 ID, 不是 Flink 的 JobId',
    `JobConfigId`        bigint(20)          NOT NULL COMMENT '表明由哪个 jobConfig 启动的',
    `CuMem`              tinyint(4)          NOT NULL COMMENT 'CU内存规格，单位GB 2,4,8,16',
    `TmRunningCuNum`     int(20)                          DEFAULT NULL,
    `DefaultParallelism` smallint(20)        NOT NULL COMMENT '默认的 Parallelism',
    `FlinkJobId`         varchar(32) COLLATE utf8mb4_bin  DEFAULT '' COMMENT 'Flink 的 32 位作业 ID',
    `ApplicationId`      varchar(100) COLLATE utf8mb4_bin DEFAULT '' COMMENT 'YARN 等 ApplicationId',
    `FlinkJobPlan`       mediumtext COLLATE utf8mb4_bin,
    `Status`             tinyint(4)          NOT NULL,
    `CreateTime`         timestamp           NOT NULL     DEFAULT CURRENT_TIMESTAMP,
    `StartTime`          timestamp           NOT NULL     DEFAULT '0000-00-00 00:00:00',
    `UpdateTime`         timestamp           NULL         DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `StopTime`           timestamp           NOT NULL     DEFAULT '0000-00-00 00:00:00',
    `JmRunningCuNum`     int(11)                          DEFAULT '0',
    `RunningOrderId`     bigint(20)          NOT NULL     DEFAULT '0',
    `Zone`               varchar(50) COLLATE utf8mb4_bin  DEFAULT '' COMMENT 'ap-guangzhou-3',
    `TmTotalCpu`         float                            DEFAULT NULL,
    `TmTotalMem`         float                            DEFAULT NULL,
    PRIMARY KEY (`Id`) USING BTREE,
    KEY `indx1` (`JobId`, `Status`),
    KEY `index_0` (`JobId`, `Status`),
    KEY `status_index` (`Status`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 41842
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin
  ROW_FORMAT = DYNAMIC;--
-- Table structure for table `JobK8sEventTopic`
--

CREATE TABLE if not exists `JobK8sEventTopic`
(
    `Id`         bigint(20)  NOT NULL,
    `Region`     varchar(50) NOT NULL,
    `LogSetId`   varchar(50) NOT NULL,
    `TopicId`    varchar(50) NOT NULL,
    `Status`     tinyint(4)           DEFAULT '0' COMMENT '1-开启，0-关闭',
    `CreateTime` timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime` timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`Id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8;--
-- Table structure for table `JobMetaTableRef`
--

CREATE TABLE if not exists `JobMetaTableRef`
(
    `Id`            bigint(20) NOT NULL AUTO_INCREMENT,
    `JobId`         bigint(20) NOT NULL COMMENT '流计算 Job 表的 ID, refer Job',
    `JobConfigId`   bigint(20) NOT NULL COMMENT '流作业配置表的 自增ID, refer JobConfig',
    `MetaTableId`   bigint(20) NOT NULL COMMENT '元数据表自增ID, refer MetastoreTable',
    `Status`        tinyint(4) NOT NULL DEFAULT '0' COMMENT '0: 使用中，-1：已删除',
    `CreateTime`    timestamp  NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`    timestamp  NULL     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `MetaCatalogId` bigint(20)          DEFAULT NULL,
    PRIMARY KEY (`Id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 398
  DEFAULT CHARSET = utf8;--
-- Table structure for table `JobRunningLogTopic`
--

CREATE TABLE if not exists `JobRunningLogTopic`
(
    `Id`         bigint(20)                                            NOT NULL AUTO_INCREMENT,
    `OwnerUin`   varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
    `Region`     varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou',
    `LogSetId`   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
    `LogTopicId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
    `Status`     tinyint(4)                                                     DEFAULT '0',
    `Remark`     text,
    `CreateTime` timestamp                                             NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime` timestamp                                             NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`Id`),
    UNIQUE KEY `Instance` (`OwnerUin`, `Region`, `LogSetId`, `LogTopicId`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8;--
-- Table structure for table `JobRunningLogTopic_18`
--

CREATE TABLE if not exists `JobRunningLogTopic_18`
(
    `Id`         bigint(20)                                            NOT NULL AUTO_INCREMENT,
    `OwnerUin`   varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
    `Region`     varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou',
    `LogSetId`   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
    `LogTopicId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
    `Status`     tinyint(4)                                                     DEFAULT '0',
    `CreateTime` timestamp                                             NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime` timestamp                                             NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`Id`),
    UNIQUE KEY `Instance` (`OwnerUin`, `Region`, `LogSetId`, `LogTopicId`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8;--
-- Table structure for table `JobScaleEvent`
--

CREATE TABLE if not exists `JobScaleEvent`
(
    `Id`                  bigint(20)                      NOT NULL AUTO_INCREMENT,
    `JobId`               varchar(12) COLLATE utf8mb4_bin NOT NULL COMMENT '例如 cql-bu8nydsy',
    `RuleItem`            mediumtext COLLATE utf8mb4_bin COMMENT '扩缩容信息，json格式',
    `StartTime`           timestamp                       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    `EndTime`             timestamp                       NULL     DEFAULT CURRENT_TIMESTAMP COMMENT '结束时间',
    `Status`              tinyint(4)                      NOT NULL COMMENT '状态，1：成功，2：失败，-1：删除',
    `ErrorCode`           varchar(50) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '错误编码',
    `AppId`               bigint(20)                      NOT NULL,
    `ScalingActionConfig` text COLLATE utf8mb4_bin COMMENT '扩缩容行为信息',
    `PlanName`            varchar(200) COLLATE utf8mb4_bin         DEFAULT NULL COMMENT '定时计划名字',
    `ScaleType`           tinyint(4)                               DEFAULT NULL COMMENT '定时计划事件类型，1代表定时调优，2代表智能调优',
    PRIMARY KEY (`Id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 7858
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin
  ROW_FORMAT = DYNAMIC;--
-- Table structure for table `JobScalePlan`
--

CREATE TABLE if not exists `JobScalePlan`
(
    `Id`          bigint(20)                      NOT NULL AUTO_INCREMENT,
    `SerialId`    varchar(20) COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '例如 plan-ck9scadk',
    `JobSerialId` varchar(12) COLLATE utf8mb4_bin NOT NULL COMMENT '例如 cql-bu8nydsy',
    `AppId`       bigint(20)                      NOT NULL,
    `OwnerUin`    varchar(20) COLLATE utf8mb4_bin NOT NULL,
    `CreatorUin`  varchar(20) COLLATE utf8mb4_bin NOT NULL,
    `Region`      varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou',
    `Zone`        varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou-3',
    `Name`        varchar(200) CHARACTER SET utf8 NOT NULL COMMENT '计划名',
    `Status`      tinyint(4)                      NOT NULL COMMENT '规则状态，1：启用，2：停用，-1：删除',
    `CreateTime`  timestamp                       NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`  timestamp                       NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `Modifier`    varchar(150) CHARACTER SET utf8          DEFAULT NULL COMMENT '最近的修改人名字',
    PRIMARY KEY (`Id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 107
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin
  ROW_FORMAT = DYNAMIC;--
-- Table structure for table `JobScaleRule`
--

CREATE TABLE if not exists `JobScaleRule`
(
    `Id`             bigint(20)                      NOT NULL AUTO_INCREMENT,
    `SerialId`       varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '例如 rule-ck9scadk',
    `JobId`          varchar(12) COLLATE utf8mb4_bin NOT NULL COMMENT '例如 cql-bu8nydsy',
    `AppId`          bigint(20)                      NOT NULL,
    `OwnerUin`       varchar(20) COLLATE utf8mb4_bin NOT NULL,
    `CreatorUin`     varchar(20) COLLATE utf8mb4_bin NOT NULL,
    `Region`         varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou',
    `Zone`           varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou-3',
    `RuleName`       varchar(50) CHARACTER SET utf8  NOT NULL COMMENT '规则名',
    `Status`         tinyint(4)                      NOT NULL COMMENT '规则状态，1：启用，-1：停用，-2：删除',
    `ConditionRatio` smallint(10)                             DEFAULT NULL COMMENT '0-100取值百分比，如果tm中高于阈值CPU或低于阈值cpu占比高于这个值，就会触发扩缩容',
    `Threshold`      bigint(20)                      NOT NULL COMMENT '阈值，如果是cpu 则是一个0-100的值 表示使用率，如果是kafka offset 则是offset之间的差值',
    `DurationTime`   smallint(10)                    NOT NULL COMMENT '持续多久时间才进行扩缩容，单位分钟',
    `Step`           varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '如果结尾是%，则是按当前并行度的百分数进行扩缩容，否则按固定数字进行扩缩容',
    `ReachLimit`     smallint(10)                    NOT NULL COMMENT '扩容最高的并行度',
    `CreateTime`     timestamp                       NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`     timestamp                       NULL     DEFAULT CURRENT_TIMESTAMP,
    `Properties`     mediumtext COLLATE utf8mb4_bin,
    `Configuration`  mediumtext COLLATE utf8mb4_bin COMMENT '规则配置字段',
    `PlanSerialId`   varchar(20) COLLATE utf8mb4_bin          DEFAULT '' COMMENT '定时计划id',
    PRIMARY KEY (`Id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 3428
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin
  ROW_FORMAT = DYNAMIC;--
-- Table structure for table `JobSubmissionLogTopic`
--

CREATE TABLE if not exists `JobSubmissionLogTopic`
(
    `Id`         bigint(20)                      NOT NULL AUTO_INCREMENT,
    `Region`     varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou',
    `LogSetId`   varchar(50) COLLATE utf8mb4_bin NOT NULL,
    `LogTopicId` varchar(50) COLLATE utf8mb4_bin NOT NULL,
    `Status`     tinyint(4)                               DEFAULT '0' COMMENT '0-开启，1-关闭',
    `CreateTime` timestamp                       NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime` timestamp                       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`Id`) USING BTREE,
    UNIQUE KEY `Instance` (`Region`, `LogSetId`, `LogTopicId`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 18
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin
  ROW_FORMAT = DYNAMIC;--
-- Table structure for table `JobTuningAction`
--

CREATE TABLE if not exists `JobTuningAction`
(
    `Id`            bigint(20)                      NOT NULL AUTO_INCREMENT,
    `SerialId`      varchar(30) COLLATE utf8mb4_bin NOT NULL COMMENT '例如 action-ck9scadk',
    `JobSerialId`   varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '作业 SerialId，例如 cql-bu8nydsy',
    `JobInstanceId` bigint(20)                      NOT NULL,
    `ActionType`    varchar(50) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '调优操作类型',
    `ActionDetail`  mediumtext COLLATE utf8mb4_bin COMMENT '调优操作详细信息，json 格式',
    `CreateTime`    timestamp                       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    `UpdateTime`    timestamp                       NULL     DEFAULT CURRENT_TIMESTAMP COMMENT '结束时间',
    `Status`        tinyint(4)                      NOT NULL COMMENT '状态，0: 初始化，1：进行中，2：成功，3：失败',
    `Diagnosis`     mediumtext COLLATE utf8mb4_bin COMMENT '作业诊断信息，json 格式',
    `Properties`    mediumtext COLLATE utf8mb4_bin COMMENT '扩缩容信息，json 格式',
    `Configuration` text COLLATE utf8mb4_bin COMMENT '扩缩容规则配置信息',
    `RuleSerialId`  varchar(20) COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '调优规则id',
    `ExecuteTime`   timestamp                       NULL     DEFAULT NULL COMMENT 'action执行时间',
    PRIMARY KEY (`Id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 8496
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin
  ROW_FORMAT = DYNAMIC;--
-- Table structure for table `MetaCatalogConnector`
--

CREATE TABLE if not exists `MetaCatalogConnector`
(
    `Id`             bigint(20) NOT NULL AUTO_INCREMENT,
    `CatalogType`    tinyint(4) NOT NULL,
    `CatalogVersion` varchar(20)         DEFAULT NULL,
    `ResourceId`     bigint(20)          DEFAULT NULL COMMENT '资源表的ID',
    `VersionId`      bigint(20)          DEFAULT '-1' COMMENT '资源版本,默认-1 代表该资源引用最新的资源',
    `Status`         tinyint(4)          DEFAULT NULL COMMENT '状态,ACTIVE:1,DELETE:-2',
    `CreateTime`     timestamp  NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`     timestamp  NULL     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `FlinkVersion`   varchar(100)        DEFAULT 'Flink-1.13' COMMENT '内核版本',
    PRIMARY KEY (`Id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 44
  DEFAULT CHARSET = utf8;--
-- Table structure for table `MetaCatalogResourceRef`
--

CREATE TABLE if not exists `MetaCatalogResourceRef`
(
    `Id`           bigint(20) NOT NULL AUTO_INCREMENT,
    `ResourceId`   bigint(20)          DEFAULT NULL COMMENT '资源表的ID',
    `VersionId`    bigint(20)          DEFAULT '-1' COMMENT '资源版本,默认-1 代表该资源引用最新的资源',
    `CatalogId`    bigint(20)          DEFAULT NULL,
    `Status`       tinyint(4)          DEFAULT NULL COMMENT '状态,ACTIVE:1,DELETE:-2',
    `CreateTime`   timestamp  NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`   timestamp  NULL     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `FlinkVersion` varchar(100)        DEFAULT 'Flink-1.13' COMMENT '内核版本',
    PRIMARY KEY (`Id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 164
  DEFAULT CHARSET = utf8;--
-- Table structure for table `MetaTableChangeLog`
--

CREATE TABLE if not exists `MetaTableChangeLog`
(
    `Id`          bigint(20)    NOT NULL AUTO_INCREMENT,
    `TableId`     bigint(20)    NOT NULL,
    `Version`     bigint(20)    NOT NULL,
    `TableSchema` mediumtext,
    `Properties`  varchar(4000) NOT NULL,
    `Script`      mediumtext,
    `Status`      tinyint(4)             DEFAULT '1' COMMENT '1：正常，-2：删除',
    `OperatorUin` varchar(20)            DEFAULT NULL COMMENT '操作人',
    `Remark`      varchar(200)           DEFAULT NULL COMMENT '备注',
    `CreateTime`  timestamp     NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`  timestamp     NULL     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`Id`),
    UNIQUE KEY `MetaTableChangeLog_UN` (`TableId`, `Version`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 32
  DEFAULT CHARSET = utf8;--
-- Table structure for table `MetaTableIdentifier`
--

CREATE TABLE if not exists `MetaTableIdentifier`
(
    `Id`              bigint(20) NOT NULL AUTO_INCREMENT,
    `SerialId`        varchar(20)         DEFAULT NULL,
    `StableOptions`   varchar(500)        DEFAULT NULL,
    `UnstableOptions` varchar(500)        DEFAULT NULL,
    `Status`          int(11)    NOT NULL DEFAULT '1' COMMENT '1：正常，-2：删除',
    `Type`            varchar(100)        DEFAULT NULL,
    `Region`          varchar(20)         DEFAULT NULL COMMENT '地域',
    `Uin`             varchar(20)         DEFAULT NULL,
    `SubUin`          varchar(20)         DEFAULT NULL,
    `AppId`           bigint(20)          DEFAULT NULL,
    `ItemSpaceId`     bigint(20) NOT NULL DEFAULT '0' COMMENT '项目空间ID',
    `CreateTime`      datetime            DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`      datetime            DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`Id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 113
  DEFAULT CHARSET = utf8;--
-- Table structure for table `MetaTableIdentifierRef`
--

CREATE TABLE if not exists `MetaTableIdentifierRef`
(
    `Id`           bigint(20) NOT NULL AUTO_INCREMENT,
    `IdentifierId` varchar(100)        DEFAULT NULL,
    `TableId`      bigint(20)          DEFAULT NULL,
    `Status`       int(11)    NOT NULL DEFAULT '1' COMMENT '1：正常，-2：删除',
    `CreateTime`   datetime            DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`   datetime            DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`Id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 113
  DEFAULT CHARSET = utf8;--
-- Table structure for table `MetaTableLineage`
--

CREATE TABLE if not exists `MetaTableLineage`
(
    `Id`          bigint(20) NOT NULL AUTO_INCREMENT,
    `JobConfigId` bigint(20)          DEFAULT NULL,
    `SourceId`    bigint(20)          DEFAULT NULL,
    `SinkId`      bigint(20)          DEFAULT NULL,
    `SourceRef`   varchar(100)        DEFAULT NULL,
    `SinkRef`     varchar(100)        DEFAULT NULL,
    `Status`      tinyint(4) NOT NULL DEFAULT '1' COMMENT '1：正常，-2：删除',
    `SourceType`  tinyint(4) NOT NULL DEFAULT '1' COMMENT '1: 流表，2：维表',
    `Region`      varchar(20)         DEFAULT NULL COMMENT '地域',
    `Uin`         varchar(20)         DEFAULT NULL,
    `SubUin`      varchar(20)         DEFAULT NULL,
    `AppId`       bigint(20)          DEFAULT NULL,
    `CreateTime`  datetime            DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`  datetime            DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`Id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 71
  DEFAULT CHARSET = utf8;--
-- Table structure for table `MetaTableResourceRef`
--

CREATE TABLE if not exists `MetaTableResourceRef`
(
    `Id`           bigint(20) NOT NULL AUTO_INCREMENT,
    `MetaTableId`  bigint(20)          DEFAULT NULL COMMENT '元数据表的ID',
    `ResourceId`   bigint(20)          DEFAULT NULL COMMENT '资源表的ID,-2表示不引用资源',
    `VersionId`    bigint(20)          DEFAULT '-1' COMMENT '配置版本,默认-1代表该资源引用最新的资源',
    `Status`       tinyint(4)          DEFAULT '1' COMMENT '资源配置的状态',
    `Type`         tinyint(4)          DEFAULT '1' COMMENT '引用类型 1:系统资源 2:用户资源',
    `CreateTime`   timestamp  NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`   timestamp  NULL     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `FlinkVersion` varchar(100)        DEFAULT 'Flink-1.11' COMMENT 'Flink版本',
    PRIMARY KEY (`Id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 300
  DEFAULT CHARSET = utf8;--
-- Table structure for table `MetastoreCatalog`
--

CREATE TABLE if not exists `MetastoreCatalog`
(
    `Id`              bigint(20) NOT NULL AUTO_INCREMENT,
    `Type`            tinyint(4)          DEFAULT NULL COMMENT '0:defalut,1:jdbc:2:hive',
    `Name`            varchar(100)        DEFAULT NULL,
    `ItemSpaceId`     bigint(20) NOT NULL DEFAULT '0' COMMENT '项目空间ID',
    `Properties`      varchar(1000)       DEFAULT NULL,
    `Comment`         varchar(200)        DEFAULT NULL,
    `Region`          varchar(20)         DEFAULT NULL,
    `Uin`             varchar(20)         DEFAULT NULL,
    `SubUin`          varchar(20)         DEFAULT NULL,
    `AppId`           bigint(20)          DEFAULT NULL,
    `CreateTime`      timestamp  NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`      timestamp  NULL     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `DefaultDatabase` varchar(100)        DEFAULT NULL COMMENT '默认database',
    `SerialId`        varchar(20)         DEFAULT NULL,
    `Status`          int(11)             DEFAULT '1' COMMENT '1表示active,-2表示deleted',
    `CatalogVersion`  varchar(20)         DEFAULT NULL,
    `FlinkVersion`    varchar(20)         DEFAULT NULL COMMENT '内核版本',
    PRIMARY KEY (`Id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 52
  DEFAULT CHARSET = utf8;--
-- Table structure for table `MetastoreDatabase`
--

CREATE TABLE if not exists `MetastoreDatabase`
(
    `Id`          bigint(20) NOT NULL AUTO_INCREMENT,
    `Name`        varchar(100)        DEFAULT NULL,
    `CatalogId`   bigint(20)          DEFAULT NULL,
    `Properties`  varchar(1000)       DEFAULT NULL,
    `Comment`     varchar(200)        DEFAULT NULL,
    `Region`      varchar(20)         DEFAULT NULL,
    `ItemSpaceId` bigint(20) NOT NULL DEFAULT '0' COMMENT '项目空间ID',
    `Uin`         varchar(200)        DEFAULT NULL,
    `SubUin`      varchar(20)         DEFAULT NULL,
    `AppId`       bigint(20)          DEFAULT NULL,
    `CreateTime`  timestamp  NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`  timestamp  NULL     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `Status`      int(11)             DEFAULT '1' COMMENT '1：正常，-2：删除',
    PRIMARY KEY (`Id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 38
  DEFAULT CHARSET = utf8;--
-- Table structure for table `MetastoreTable`
--

CREATE TABLE if not exists `MetastoreTable`
(
    `Id`          bigint(20)    NOT NULL AUTO_INCREMENT,
    `Name`        varchar(100)  NOT NULL,
    `DatabaseId`  bigint(20)    NOT NULL,
    `TableSchema` mediumtext,
    `Properties`  varchar(4000) NOT NULL,
    `Type`        tinyint(4)             DEFAULT NULL COMMENT '0：table,1:view,2:function',
    `TableType`   varchar(100)           DEFAULT NULL,
    `Script`      mediumtext,
    `Comment`     varchar(200)           DEFAULT NULL,
    `Region`      varchar(20)            DEFAULT NULL COMMENT '地域',
    `Uin`         varchar(20)            DEFAULT NULL,
    `SubUin`      varchar(20)            DEFAULT NULL,
    `AppId`       bigint(20)             DEFAULT NULL,
    `CreateTime`  timestamp     NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`  timestamp     NULL     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `Description` varchar(1000)          DEFAULT NULL COMMENT '描述信息',
    `SerialId`    varchar(20)            DEFAULT NULL,
    `Status`      tinyint(4)             DEFAULT '1' COMMENT '1：正常，-2：删除',
    `Version`     bigint(20)    NOT NULL DEFAULT '1',
    PRIMARY KEY (`Id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 113
  DEFAULT CHARSET = utf8;--
-- Table structure for table `MetastoreTableProperty`
--

CREATE TABLE if not exists `MetastoreTableProperty`
(
    `Id`         bigint(20) NOT NULL AUTO_INCREMENT,
    `IfSource`   tinyint(4)          DEFAULT NULL COMMENT '0：it can not be used as source table,1:it can be used as source table',
    `IfSink`     tinyint(4)          DEFAULT NULL COMMENT '0：it can not be used as tagert table",1:it can be used as target table',
    `IfDim`      tinyint(4)          DEFAULT NULL COMMENT '0：it can not be used as dimentional table",1:it can be used as dimentional table',
    `Dependency` varchar(4000)       DEFAULT NULL,
    `Type`       varchar(20)         DEFAULT NULL,
    `Formats`    varchar(200)        DEFAULT NULL,
    `Comment`    varchar(200)        DEFAULT NULL,
    `resourceId` bigint(20)          DEFAULT NULL,
    `CreateTime` timestamp  NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime` timestamp  NULL     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`Id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8;--
-- Table structure for table `Operation`
--

CREATE TABLE if not exists `Operation`
(
    `Id`         bigint(20) NOT NULL AUTO_INCREMENT,
    `AppId`      bigint(20)   DEFAULT NULL,
    `OwnerUin`   varchar(255) DEFAULT NULL,
    `OperateUin` varchar(255) DEFAULT NULL,
    `Action`     varchar(255) DEFAULT NULL,
    `CreateTime` datetime     DEFAULT CURRENT_TIMESTAMP,
    `RequestId`  varchar(255) DEFAULT NULL,
    `ResultCode` varchar(255) DEFAULT NULL,
    `Request`    text,
    `Response`   text,
    `ErrorMsg`   text,
    PRIMARY KEY (`Id`) USING BTREE,
    KEY `Operation_RequestId_IDX` (`RequestId`) USING BTREE,
    KEY `Operation_C_IDX` (`CreateTime`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 7307044
  DEFAULT CHARSET = utf8;--
-- Table structure for table `Permission`
--

CREATE TABLE if not exists `Permission`
(
    `Id`                 bigint(20) unsigned              NOT NULL AUTO_INCREMENT,
    `Name`               varchar(255) COLLATE utf8mb4_bin NOT NULL,
    `ControllerNameList` text COLLATE utf8mb4_bin         NOT NULL COMMENT 'galileo里的Controller名称列表，逗号隔开',
    `Status`             tinyint(4)                       NOT NULL DEFAULT '1' COMMENT '1 生效 -2 删除',
    `Type`               varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '权限归类, Job/作业管理 ',
    `Selectable`         tinyint(4)                       NOT NULL DEFAULT '1' COMMENT '是否可选',
    `CreateTime`         timestamp                        NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`         timestamp                        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`Id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 41
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='权限表';--
-- Table structure for table `PooledCvm`
--

CREATE TABLE if not exists `PooledCvm`
(
    `Id`                 bigint(20)  NOT NULL AUTO_INCREMENT,
    `InstanceId`         varchar(16) NOT NULL COMMENT 'ä¾‹å¦‚ ins-bu8nydsy',
    `Status`             tinyint(4)  NOT NULL COMMENT '1 æœ‰æ•ˆ -1æ— æ•ˆ',
    `PoolTime`           timestamp   NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT 'poolæ—¶é—´',
    `PooledBy`           varchar(16) NOT NULL COMMENT 'ä¾‹å¦‚ cluster-bu8nydsy',
    `UnPoolTime`         timestamp   NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT 'unpoolæ—¶é—´',
    `UnPooledBy`         varchar(16) NOT NULL COMMENT 'ä¾‹å¦‚ cluster-bu8nydsy',
    `NetEnvironmentType` tinyint(4)  NOT NULL COMMENT 'ç½‘ç»œç±»åž‹',
    `Region`             varchar(30) NOT NULL COMMENT 'ap-guangzhou',
    `Zone`               varchar(30) NOT NULL COMMENT 'ap-guangzhou-3',
    `InstanceType`       varchar(30) NOT NULL COMMENT 'ä¾‹å¦‚ SA2.2XLARGE32',
    `Cpu`                int(20)     NOT NULL COMMENT 'CUæ ¸å¿ƒæ•°é‡',
    `Memory`             int(20)     NOT NULL COMMENT 'å†…å­˜å¤§å°',
    PRIMARY KEY (`Id`) USING BTREE,
    KEY `InstanceId_Status` (`InstanceId`, `Status`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 176
  DEFAULT CHARSET = utf8;--
-- Table structure for table `Quota`
--

CREATE TABLE if not exists `Quota`
(
    `Id`                 bigint(20)                                             NOT NULL AUTO_INCREMENT,
    `AppId`              bigint(20) DEFAULT NULL,
    `ConfigurationKey`   varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '配置key',
    `ConfigurationValue` bigint(20) DEFAULT '0' COMMENT '配置值',
    PRIMARY KEY (`Id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 122
  DEFAULT CHARSET = utf8;--
-- Table structure for table `Region`
--

CREATE TABLE if not exists `Region`
(
    `id`                   bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增',
    `region`               varchar(30)                  DEFAULT NULL,
    `regionName`           varchar(30)                  DEFAULT NULL,
    `state`                int(11)                      DEFAULT NULL COMMENT '地域是否可达',
    `netEnvType`           int(11)                      DEFAULT NULL,
    `regionId`             int(11)             NOT NULL,
    `ShortName`            varchar(10)         NOT NULL DEFAULT '',
    `supportSharedCluster` tinyint(4)                   DEFAULT '0',
    `supportInternational` tinyint(4)                   DEFAULT '0',
    `ClusterType`          int(11)                      DEFAULT '0' COMMENT '0:tke,1:eks',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 89
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='region元数据';--
-- Table structure for table `ResGroup`
--

CREATE TABLE if not exists `ResGroup`
(
    `ResGroupId` varchar(45) DEFAULT NULL COMMENT '资源组ID',
    `ResourceId` varchar(45) DEFAULT NULL COMMENT '资源ID',
    `Status`     tinyint(4)  DEFAULT NULL,
    `Version`    bigint(20)  DEFAULT NULL
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8;--
-- Table structure for table `Resource`
--

CREATE TABLE if not exists `Resource`
(
    `Id`                bigint(20)                       NOT NULL AUTO_INCREMENT,
    `ResourceName`      varchar(200) CHARACTER SET utf8                               DEFAULT NULL COMMENT '资源名称',
    `ResourceType`      tinyint(4)                                                    DEFAULT NULL COMMENT '资源类型，jar为1',
    `Remark`            varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `ResourceFrom`      tinyint(4)                                                    DEFAULT NULL COMMENT '资源来自用户还是自己',
    `Status`            tinyint(4)                                                    DEFAULT NULL COMMENT '资源状态',
    `CreateTime`        varchar(20) CHARACTER SET latin1                              DEFAULT NULL,
    `UpdateTime`        varchar(20) CHARACTER SET latin1                              DEFAULT NULL,
    `AppId`             bigint(20)                                                    DEFAULT NULL,
    `OwnerUin`          varchar(20) CHARACTER SET latin1                              DEFAULT NULL,
    `CreatorUin`        varchar(20) CHARACTER SET latin1                              DEFAULT NULL,
    `Region`            varchar(45) CHARACTER SET latin1                              DEFAULT NULL,
    `ItemSpaceId`       bigint(20)                       NOT NULL                     DEFAULT '0' COMMENT '项目空间ID',
    `ResourceId`        varchar(30) CHARACTER SET latin1 NOT NULL,
    `IsResGroup`        tinyint(4)                                                    DEFAULT '0' COMMENT '1 代表改资源是集群组',
    `SystemProvide`     tinyint(4)                                                    DEFAULT '0' COMMENT '0 代表用户提供资源 1代表系统提供资源',
    `FolderId`          varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin         DEFAULT 'root' COMMENT '例如 folder-bu8nydsy',
    `FileName`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin        DEFAULT '' COMMENT '资源文件名',
    `Connector`         varchar(128)                                                  DEFAULT '' COMMENT '连接器',
    `ConnectionMethod`  varchar(16)                                                   DEFAULT '' COMMENT '连接方式',
    `ConnectorVersion`  varchar(64)                                                   DEFAULT '' COMMENT '连接器版本',
    `RelatedResourceId` varchar(30)                                                   DEFAULT '' COMMENT 'Connector关联的资源id',
    PRIMARY KEY (`Id`),
    UNIQUE KEY `unique_key` (`ResourceId`),
    KEY `index_0` (`Region`, `Status`, `AppId`, `IsResGroup`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 4128
  DEFAULT CHARSET = utf8mb4;--
-- Table structure for table `ResourceConfig`
--

CREATE TABLE if not exists `ResourceConfig`
(
    `Id`             bigint(20)                                            NOT NULL AUTO_INCREMENT,
    `ResourceId`     varchar(20)  DEFAULT NULL,
    `VersionId`      bigint(20)   DEFAULT NULL COMMENT '资源配置的版本',
    `CreateTime`     varchar(20)  DEFAULT NULL,
    `ResourceLoc`    varchar(400) DEFAULT NULL COMMENT '资源位置，json字符串',
    `Status`         tinyint(4)   DEFAULT NULL COMMENT '资源配置状态',
    `ReqResourceLoc` varchar(400) DEFAULT NULL COMMENT '资源位置，请求里的json字符串',
    `CreatorUin`     varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
    `DeletorUin`     varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
    `Remark`         varchar(200) DEFAULT NULL COMMENT '资源版本描述',
    PRIMARY KEY (`Id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 4399
  DEFAULT CHARSET = utf8
  ROW_FORMAT = COMPACT;--
-- Table structure for table `ResourceRef`
--

CREATE TABLE if not exists `ResourceRef`
(
    `Id`          bigint(20) NOT NULL AUTO_INCREMENT,
    `ResourceId`  bigint(20)          DEFAULT NULL COMMENT 'ç®¡ç†èµ„æºè¡¨çš„ID',
    `JobConfigId` bigint(20)          DEFAULT NULL COMMENT 'ç®¡ç†ä½œä¸šé…ç½®è¡¨çš„ID',
    `VersionId`   bigint(20)          DEFAULT '-1' COMMENT 'é…ç½®ç‰ˆæœ¬,é»˜è®¤-1ä»£è¡¨è¯¥èµ„æºå¼•ç”¨æœ€æ–°çš„èµ„æº',
    `Status`      tinyint(4)          DEFAULT NULL COMMENT 'èµ„æºé…ç½®çš„çŠ¶æ€',
    `UsageType`   tinyint(4)          DEFAULT NULL COMMENT 'èµ„æºç±»åž‹ï¼Œä¸»èµ„æº1',
    `CreateTime`  timestamp  NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`  timestamp  NULL     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`Id`),
    KEY `index_0` (`Status`),
    KEY `jobconfigid_status` (`JobConfigId`, `Status`),
    KEY `rsjv` (`ResourceId`, `Status`, `JobConfigId`, `VersionId`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 17246
  DEFAULT CHARSET = utf8;--
-- Table structure for table `ResourceWhiteList`
--

CREATE TABLE if not exists `ResourceWhiteList`
(
    `Id`                 bigint(20)  NOT NULL AUTO_INCREMENT,
    `AppId`              bigint(20)           DEFAULT NULL,
    `MaxLimit`           bigint(255)          DEFAULT NULL,
    `MaxDependencyLimit` bigint(255) NOT NULL DEFAULT '2' COMMENT '外部依赖资源上传配额',
    PRIMARY KEY (`Id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 23
  DEFAULT CHARSET = utf8;--
-- Table structure for table `Role`
--

CREATE TABLE if not exists `Role`
(
    `Id`         bigint(20) unsigned               NOT NULL AUTO_INCREMENT COMMENT '角色编码,对应 RoleAuth的permission',
    `SerialId`   varchar(25) CHARACTER SET utf8mb4 NOT NULL,
    `Name`       varchar(255) COLLATE utf8mb4_bin  NOT NULL,
    `AppId`      bigint(20)                        NOT NULL,
    `OwnerUin`   varchar(25) COLLATE utf8mb4_bin   NOT NULL,
    `CreatorUin` varchar(25) COLLATE utf8mb4_bin   NOT NULL,
    `Status`     tinyint(4)                        NOT NULL DEFAULT '1' COMMENT '-2 已删除；1 生效',
    `Type`       tinyint(4)                        NOT NULL DEFAULT '0' COMMENT '0 内置 1 非内置',
    `CreateTime` timestamp                         NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime` timestamp                         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`Id`),
    UNIQUE KEY `SerialId` (`SerialId`),
    KEY `AppId` (`AppId`, `Status`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 26
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin COMMENT ='角色表';--
-- Table structure for table `RoleAuth`
--

CREATE TABLE if not exists `RoleAuth`
(
    `Id`                bigint(20)                      NOT NULL AUTO_INCREMENT,
    `AppId`             bigint(20)                      NOT NULL,
    `ItemSpaceId`       bigint(20)                      NOT NULL,
    `ItemSpaceSerialId` varchar(60) COLLATE utf8mb4_bin          DEFAULT NULL COMMENT 'ItemSpace表 SerialId',
    `OwnerUin`          varchar(20) COLLATE utf8mb4_bin NOT NULL,
    `CreatorUin`        varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '记录创建子账号',
    `AuthSubAccountUin` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '授权子用户',
    `Permission`        bigint(20)                      NOT NULL COMMENT '操作权限: 关联role表的 id',
    `UpdateTime`        timestamp                       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `CreateTime`        timestamp                       NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `Status`            tinyint(4)                      NOT NULL COMMENT '状态: 2 启用 1 停用',
    PRIMARY KEY (`Id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 46
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin
  ROW_FORMAT = DYNAMIC;--
-- Table structure for table `RolePermission`
--

CREATE TABLE if not exists `RolePermission`
(
    `Id`           bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `RoleId`       int(11)             NOT NULL COMMENT 'Role表的id',
    `PermissionId` int(11)             NOT NULL COMMENT 'Permission的id',
    `Status`       tinyint(4)          NOT NULL DEFAULT '1' COMMENT '1 生效 -2 删除',
    `CreateTime`   timestamp           NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`   timestamp           NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`Id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 397
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin;--
-- Table structure for table `Savepoint`
--

CREATE TABLE if not exists `Savepoint`
(
    `Id`           bigint(20) NOT NULL AUTO_INCREMENT,
    `ClusterId`    bigint(20)          DEFAULT NULL COMMENT 'ClusterId',
    `SerialId`     varchar(12)         DEFAULT NULL,
    `JobId`        bigint(20)          DEFAULT NULL,
    `JobRuntimeId` bigint(20)          DEFAULT NULL,
    `ItemSpaceId`  bigint(20) NOT NULL DEFAULT '0' COMMENT '项目空间ID',
    `Status`       int(11)             DEFAULT NULL,
    `Path`         varchar(500)        DEFAULT NULL,
    `CreateTime`   datetime   NOT NULL,
    `UpdateTime`   datetime            DEFAULT NULL,
    `RecordType`   int(4)              DEFAULT NULL COMMENT 'savepoint 表中记录的类型, 1代表 savepoint, 2代表 checkpoint',
    `Size`         bigint(20)          DEFAULT NULL COMMENT 'savepoint 和 checkpoint 的大小',
    `Timeout`      int(8)              DEFAULT NULL COMMENT 'savepoint 超时时间',
    `Description`  varchar(1024)       DEFAULT NULL COMMENT 'savepoint 描述信息',
    PRIMARY KEY (`Id`),
    KEY `savepoint_job_record_status` (`JobId`, `RecordType`, `Status`),
    KEY `idx_jobId_recordType_updateTime` (`JobId`, `RecordType`, `UpdateTime`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 5136447
  DEFAULT CHARSET = utf8;--
-- Table structure for table `Settle`
--

CREATE TABLE if not exists `Settle`
(
    `id`            int(11) unsigned NOT NULL AUTO_INCREMENT,
    `CalcDate`      varchar(50)      NOT NULL,
    `ResourceCount` int(11)          NOT NULL DEFAULT '0',
    `Status`        int(1)           NOT NULL DEFAULT '0' COMMENT '0 未处理 1处理中 2已处理',
    `createTime`    timestamp        NULL     DEFAULT CURRENT_TIMESTAMP,
    `updateTime`    timestamp        NULL     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `errMsg`        text,
    PRIMARY KEY (`id`),
    UNIQUE KEY `CalcDate` (`CalcDate`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 19673
  DEFAULT CHARSET = utf8mb4 COMMENT ='后付费推量结算汇总表，用于对账，以及推量问题的报警';--
-- Table structure for table `SettleDetail`
--

CREATE TABLE if not exists `SettleDetail`
(
    `id`         int(11) unsigned NOT NULL AUTO_INCREMENT,
    `ResourceId` varchar(50)      NOT NULL,
    `AppId`      bigint(20)       NOT NULL,
    `Uid`        varchar(50)      NOT NULL,
    `Count`      decimal(28, 8)   NOT NULL,
    `createTime` timestamp        NULL DEFAULT CURRENT_TIMESTAMP,
    `updateTime` timestamp        NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `CalcDate`   varchar(50)      NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `ResourceId` (`ResourceId`, `AppId`, `CalcDate`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 17045
  DEFAULT CHARSET = utf8mb4;--
-- Table structure for table `SqlGateway`
--

CREATE TABLE if not exists `SqlGateway`
(
    `Id`           bigint(20)                      NOT NULL AUTO_INCREMENT,
    `SerialId`     varchar(16) COLLATE utf8mb4_bin          DEFAULT NULL COMMENT 'SerialId',
    `ClusterId`    smallint(20)                    NOT NULL,
    `FlinkVersion` varchar(50) COLLATE utf8mb4_bin          DEFAULT 'Flink-1.17',
    `Properties`   mediumtext COLLATE utf8mb4_bin COMMENT '高级参数',
    `CuSpec`       float                                    DEFAULT '1' COMMENT 'CU规格',
    `Status`       tinyint(4)                               DEFAULT NULL COMMENT '状态，1.停止 2. 开启中 3. 开启 4. 开启失败 5. 停止中 -2. 已删除',
    `AppId`        bigint(20)                      NOT NULL,
    `OwnerUin`     varchar(20) COLLATE utf8mb4_bin NOT NULL,
    `CreatorUin`   varchar(20) COLLATE utf8mb4_bin NOT NULL,
    `Region`       varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou',
    `CreateTime`   timestamp                       NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`   timestamp                       NULL     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `Cpu`          float                                    DEFAULT NULL,
    `Mem`          float                                    DEFAULT NULL,
    `StartTime`    timestamp                       NOT NULL DEFAULT '0000-00-00 00:00:00',
    `StopTime`     timestamp                       NOT NULL DEFAULT '0000-00-00 00:00:00',
    PRIMARY KEY (`Id`) USING BTREE,
    UNIQUE KEY `SerialId` (`ClusterId`, `FlinkVersion`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 26
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin
  ROW_FORMAT = DYNAMIC;--
-- Table structure for table `SqlGatewayRef`
--

CREATE TABLE if not exists `SqlGatewayRef`
(
    `Id`           bigint(20) NOT NULL AUTO_INCREMENT,
    `SqlGatewayId` bigint(20)          DEFAULT NULL COMMENT 'Gateway表ID',
    `ResourceId`   bigint(20)          DEFAULT NULL COMMENT '资源表的ID',
    `VersionId`    bigint(20)          DEFAULT '-1' COMMENT '资源版本,默认-1代表引用最新的资源',
    `Status`       tinyint(4)          DEFAULT '1' COMMENT '资源配置的状态 1:正常，-2:删除',
    `Type`         tinyint(4)          DEFAULT '1' COMMENT '引用类型 1:系统资源 2:用户资源',
    `CreateTime`   timestamp  NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`   timestamp  NULL     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`Id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8;--
-- Table structure for table `SqlKeyword`
--

CREATE TABLE if not exists `SqlKeyword`
(
    `id`      bigint(20)                      NOT NULL AUTO_INCREMENT,
    `keyword` varchar(20) COLLATE utf8mb4_bin NOT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin
  ROW_FORMAT = DYNAMIC;--
-- Table structure for table `SqlTemplate`
--

CREATE TABLE if not exists `SqlTemplate`
(
    `Id`           int(10)                           NOT NULL AUTO_INCREMENT,
    `Name`         varchar(50) COLLATE utf8mb4_bin   NOT NULL COMMENT '模板名称，显示在前端的，例如ckafka->mysql',
    `Type`         tinyint(4)                        NOT NULL COMMENT '模板类型，1:共享集群SQL模板，2:独享集群SQL模板',
    `Code`         varchar(5000) COLLATE utf8mb4_bin NOT NULL COMMENT '模板代码',
    `Status`       tinyint(4)                      DEFAULT '1' COMMENT '模板状态，1：使用中，-2：已删除',
    `FlinkVersion` varchar(32) COLLATE utf8mb4_bin DEFAULT 'Flink-1.11',
    PRIMARY KEY (`Id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1150
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin
  ROW_FORMAT = DYNAMIC;--
-- Table structure for table `SqlTemplate_18`
--

CREATE TABLE if not exists `SqlTemplate_18`
(
    `Id`     int(10)                           NOT NULL AUTO_INCREMENT,
    `Name`   varchar(50) COLLATE utf8mb4_bin   NOT NULL COMMENT '模板名称，显示在前端的，例如ckafka->mysql',
    `Type`   tinyint(4)                        NOT NULL COMMENT '模板类型，1:共享集群SQL模板，2:独享集群SQL模板',
    `Code`   varchar(5000) COLLATE utf8mb4_bin NOT NULL COMMENT '模板代码',
    `Status` tinyint(4) DEFAULT '1' COMMENT '模板状态，1：使用中，-2：已删除',
    PRIMARY KEY (`Id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin
  ROW_FORMAT = DYNAMIC;--
-- Table structure for table `SynVersion`
--

CREATE TABLE if not exists `SynVersion`
(
    `Id`           bigint(20)                       NOT NULL AUTO_INCREMENT,
    `Region`       varchar(20) COLLATE utf8mb4_bin  NOT NULL COMMENT '地域信息',
    `UpdateTime`   timestamp                        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `CreateTime`   timestamp                        NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `Status`       tinyint(4)                       NOT NULL COMMENT '状态: 2 启用 1 停用',
    `LastSyncTime` timestamp                        NOT NULL DEFAULT '0000-00-00 00:00:00',
    `TableName`    varchar(255) COLLATE utf8mb4_bin NOT NULL DEFAULT 'Administrator' COMMENT '同步表名',
    PRIMARY KEY (`Id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 4
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin
  ROW_FORMAT = DYNAMIC;--
-- Table structure for table `SystemConnector`
--

CREATE TABLE if not exists `SystemConnector`
(
    `Id`               bigint(20) NOT NULL AUTO_INCREMENT,
    `ResourceId`       varchar(20)         DEFAULT NULL COMMENT '资源表的ID,NULL 表示不引用资源',
    `VersionId`        bigint(20)          DEFAULT '-1' COMMENT '配置版本,默认-1代表该资源引用最新的资源',
    `FlinkVersion`     varchar(20)         DEFAULT NULL COMMENT '元数据表的ID',
    `Options`          text,
    `Status`           tinyint(4)          DEFAULT NULL COMMENT '状态,ACTIVE:1,DELETE:-2',
    `Region`           varchar(100)        DEFAULT NULL,
    `CreateTime`       timestamp  NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`       timestamp  NULL     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `StableOptions`    varchar(100)        DEFAULT NULL,
    `UnstableOptions`  varchar(100)        DEFAULT NULL,
    `IdentExtractType` tinyint(4)          DEFAULT '2' COMMENT '1：Mapping，2：Unmapping',
    PRIMARY KEY (`Id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 114
  DEFAULT CHARSET = utf8;--
-- Table structure for table `SystemResourceRef`
--

CREATE TABLE if not exists `SystemResourceRef`
(
    `Id`           bigint(20) NOT NULL AUTO_INCREMENT,
    `FlinkVersion` varchar(200) DEFAULT NULL,
    `ResourceId`   varchar(200) DEFAULT NULL,
    `Remark`       varchar(200) DEFAULT NULL,
    `Status`       tinyint(4)   DEFAULT NULL,
    PRIMARY KEY (`Id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 105
  DEFAULT CHARSET = utf8;--
-- Table structure for table `TemporarilyDisableCluster`
--

CREATE TABLE if not exists `TemporarilyDisableCluster`
(
    `Id`             int(9)     NOT NULL AUTO_INCREMENT,
    `ClusterGroupId` bigint(20) NOT NULL,
    `EndTime`        timestamp  NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`Id`),
    UNIQUE KEY `ClusterId` (`ClusterGroupId`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8;--
-- Table structure for table `Tke`
--

CREATE TABLE if not exists `Tke`
(
    `Id`             bigint(20)  NOT NULL AUTO_INCREMENT,
    `CreateTime`     timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`     timestamp   NULL     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `ClusterId`      bigint(20)  NOT NULL COMMENT '所属的集群id',
    `InstanceId`     varchar(16) NOT NULL COMMENT '实例id',
    `InstanceName`   varchar(32) NOT NULL COMMENT '实例名字',
    `ClusterType`    int(11)              DEFAULT '0' COMMENT '0:tke,1:eks,2:tcs',
    `WorkerStatus`   int(11)              DEFAULT '0' COMMENT '0:未添加,1:已添加',
    `Status`         int(11)     NOT NULL DEFAULT '0' COMMENT '0-创建中；1-运行中；2-异常',
    `ArchGeneration` int(11)     NOT NULL DEFAULT '0' COMMENT '架构版本: 0老架构, 1版本，2版本',
    PRIMARY KEY (`Id`),
    UNIQUE KEY `ClusterIdInstanceId` (`ClusterId`, `InstanceId`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 176
  DEFAULT CHARSET = utf8;--
-- Table structure for table `TkeVersion`
--

CREATE TABLE if not exists `TkeVersion`
(
    `Id`               bigint(20)                      NOT NULL AUTO_INCREMENT,
    `ClusterId`        bigint(20)                      NOT NULL,
    `ResourceName`     varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT 'cluster-admin',
    `ContainerName`    varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT 'app-container',
    `ContainerVersion` varchar(80) COLLATE utf8mb4_bin NOT NULL COMMENT '1.8',
    `LastImageVersion` varchar(200) COLLATE utf8mb4_bin         DEFAULT NULL COMMENT 'ccr.ccs.tencentyun.com/oceanus-release/cluster-admin:1.8',
    `CreateTime`       timestamp                       NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`       timestamp                       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`Id`) USING BTREE,
    KEY `tv_ClusterId_ResourceName_ContainerName` (`ClusterId`, `ResourceName`, `ContainerName`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1566
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin
  ROW_FORMAT = DYNAMIC;--
-- Table structure for table `Tree`
--

CREATE TABLE if not exists `Tree`
(
    `Id`          bigint(20)                      NOT NULL AUTO_INCREMENT,
    `ItemSpaceId` bigint(20)                      NOT NULL DEFAULT '0' COMMENT '项目空间ID',
    `FolderId`    varchar(15) COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '例如 folder-bu8nydsy',
    `FolderName`  varchar(50) CHARACTER SET utf8  NOT NULL COMMENT '文件夹名',
    `ParentId`    varchar(15) COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '例如 folder-bu8nyd75',
    `AppId`       bigint(20)                      NOT NULL,
    `Region`      varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT 'ap-guangzhou',
    `CreatorUin`  varchar(20) COLLATE utf8mb4_bin NOT NULL,
    `OwnerUin`    varchar(20) COLLATE utf8mb4_bin NOT NULL,
    `CreateTime`  timestamp                       NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`  timestamp                       NULL     DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`Id`) USING BTREE,
    UNIQUE KEY `FolderId` (`FolderId`) USING BTREE,
    UNIQUE KEY `FolderName_ParentId` (`FolderName`, `ParentId`, `AppId`, `Region`, `ItemSpaceId`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 127
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin
  ROW_FORMAT = DYNAMIC;--
-- Table structure for table `UserUinMap`
--

CREATE TABLE if not exists `UserUinMap`
(
    `Id`        bigint(20) NOT NULL AUTO_INCREMENT,
    `AppId`     bigint(20)   DEFAULT NULL,
    `AccessUin` varchar(45)  DEFAULT NULL,
    `CustomUin` varchar(45)  DEFAULT NULL,
    `Status`    tinyint(4)   DEFAULT NULL,
    `Extension` varchar(200) DEFAULT NULL,
    PRIMARY KEY (`Id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8;--
-- Table structure for table `Variable`
--

CREATE TABLE if not exists `Variable`
(
    `Id`          bigint(20)   NOT NULL AUTO_INCREMENT,
    `Name`        varchar(300)          DEFAULT NULL,
    `ItemSpaceId` bigint(20)   NOT NULL DEFAULT '0' COMMENT '项目空间ID',
    `Value`       varchar(500) NOT NULL COMMENT 'å˜é‡å€¼',
    `Type`        tinyint(4)   NOT NULL COMMENT '1: æ˜¾ç¤º 2ï¼šéšè—',
    `Status`      tinyint(4)   NOT NULL COMMENT 'çŠ¶æ€ï¼šACTIVE:1,DELETE:-2',
    `Remark`      varchar(100)          DEFAULT NULL COMMENT 'å˜é‡æè¿°',
    `SerialId`    varchar(32)  NOT NULL COMMENT 'å˜é‡id',
    `CreateTime`  timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'å˜é‡åˆ›å»ºæ—¶é—´',
    `UpdateTime`  timestamp    NULL     DEFAULT CURRENT_TIMESTAMP COMMENT 'å˜é‡å€¼æ›´æ–°æ—¶é—´',
    `CreatorUin`  varchar(100) NOT NULL COMMENT 'åˆ›å»ºäºº',
    `OwnerUin`    varchar(100) NOT NULL COMMENT 'uin',
    `AppId`       bigint(20)   NOT NULL COMMENT 'Appid',
    `Region`      varchar(30)  NOT NULL COMMENT 'åœ°åŒº',
    PRIMARY KEY (`Id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 326
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='å˜é‡è¡¨';--
-- Table structure for table `VariableReference`
--

CREATE TABLE if not exists `VariableReference`
(
    `Id`            bigint(20) NOT NULL AUTO_INCREMENT,
    `VariableId`    bigint(20) NOT NULL COMMENT 'å˜é‡Id',
    `ComponentId`   bigint(20) NOT NULL COMMENT 'å¼•ç”¨æ¨¡å—Id',
    `ComponentType` tinyint(4) NOT NULL COMMENT 'å¼•ç”¨æ¨¡å—ç±»åž‹ 1: Job',
    `CreateTime`    timestamp  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'åˆ›å»ºæ—¶é—´',
    `UpdateTime`    timestamp  NULL     DEFAULT CURRENT_TIMESTAMP COMMENT 'æ›´æ–°æ—¶é—´',
    `Status`        tinyint(4) NOT NULL COMMENT 'çŠ¶æ€ï¼šACTIVE:1,DELETE:-2',
    PRIMARY KEY (`Id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 148
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='å˜é‡å¼•ç”¨è¡¨';--
-- Table structure for table `WatchdogEvent`
--

CREATE TABLE if not exists `WatchdogEvent`
(
    `Id`            bigint(20)                      NOT NULL AUTO_INCREMENT,
    `Uid`           varchar(40) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '事件唯一标识uuid',
    `JobSerialId`   varchar(12) COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '例如 cql-bu8nydsy',
    `JobInstanceId` bigint(20)                               DEFAULT NULL,
    `CreateTime`    datetime                        NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`    datetime                        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `InsertTime`    datetime                        NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `Path`          varchar(100) COLLATE utf8mb4_bin         DEFAULT NULL COMMENT 'api path',
    `Body`          text COLLATE utf8mb4_bin COMMENT 'api request body',
    `Status`        tinyint(4)                               DEFAULT '0' COMMENT '事件状态：0-未发送，1-已发送',
    `Message`       varchar(300) COLLATE utf8mb4_bin         DEFAULT NULL COMMENT 'message',
    PRIMARY KEY (`Id`),
    UNIQUE KEY `Uid` (`Uid`),
    KEY `watchdog_event_status_createTime` (`Status`, `CreateTime`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 5873354
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin
  ROW_FORMAT = DYNAMIC;--
-- Table structure for table `WhiteList`
--

CREATE TABLE if not exists `WhiteList`
(
    `Id`         bigint(20) NOT NULL AUTO_INCREMENT,
    `AppId`      bigint(20) NOT NULL,
    `Type`       tinyint(4) NOT NULL COMMENT 'ç™½åå•ç±»åž‹ 1.è§’è‰²æƒé™ 2.0.25 CU',
    `UpdateTime` timestamp  NOT NULL               DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `CreateTime` timestamp  NOT NULL               DEFAULT CURRENT_TIMESTAMP,
    `Param`      varchar(2048) COLLATE utf8mb4_bin DEFAULT '' COMMENT '白名单参数',
    PRIMARY KEY (`Id`) USING BTREE,
    UNIQUE KEY `AppId_Type` (`AppId`, `Type`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 79
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin
  ROW_FORMAT = DYNAMIC;--
-- Table structure for table `Zone`
--

CREATE TABLE if not exists `Zone`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT,
    `region`      varchar(30) DEFAULT NULL,
    `zoneId`      bigint(20)  DEFAULT NULL,
    `zone`        varchar(30) DEFAULT NULL,
    `zoneName`    varchar(50) DEFAULT NULL,
    `State`       int(11)    NOT NULL COMMENT '0 å”®ç½„ï¼Œ1 èµ„æºå……è¶³',
    `netEnvType`  int(11)    NOT NULL,
    `ClusterType` int(11)     DEFAULT '0' COMMENT '0:tke,1:eks',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 167
  DEFAULT CHARSET = utf8;--
-- Table structure for table `Zone_18`
--


CREATE TABLE if not exists `deepsqlcheck`
(
    `Id`                   bigint(20) unsigned              NOT NULL AUTO_INCREMENT,
    `SqlCheckId`           varchar(200) COLLATE utf8mb4_bin NOT NULL COMMENT '语法检查ID，前端生成',
    `CreateTime`           timestamp                        NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`           timestamp                        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `Status`               tinyint(4)                                DEFAULT NULL COMMENT '语法检查的状态 0: 等待执行 1: 提交, 2:语法检查未完成 doing  3:语法检查完毕 done 4:超时 5: 失败',
    `Result`               varchar(300) COLLATE utf8mb4_bin          DEFAULT NULL,
    `Param`                mediumtext COLLATE utf8mb4_bin,
    `ClusterGroupSerialId` varchar(30) COLLATE utf8mb4_bin  NOT NULL COMMENT '地区',
    `SqlServerHost`        varchar(30) COLLATE utf8mb4_bin           DEFAULT NULL,
    PRIMARY KEY (`Id`) USING BTREE,
    KEY `index_1` (`Status`, `ClusterGroupSerialId`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin;--
-- Table structure for table `deploy_records`
--

CREATE TABLE if not exists `deploy_records`
(
    `id`          int(11) NOT NULL AUTO_INCREMENT,
    `sql_file`    varchar(128) DEFAULT NULL COMMENT '文件路径',
    `create_time` datetime     DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 10
  DEFAULT CHARSET = utf8mb4;--
-- Table structure for table `dlocker`
--

CREATE TABLE if not exists `dlocker`
(
    `id`      bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ä¸»é”®ID',
    `bizType` char(100)           NOT NULL COMMENT 'ä¸šåŠ¡ç±»åž‹',
    `bizId`   char(100)           NOT NULL COMMENT 'ä¸šåŠ¡ç›¸å…³æ ‡è¯†',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `bizType` (`bizType`, `bizId`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 209202995
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='ä»»åŠ¡é”';--
-- Table structure for table `dlocker_detail`
--

CREATE TABLE if not exists `dlocker_detail`
(
    `id`         bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ä¸»é”®ID',
    `bizType`    char(100)           NOT NULL COMMENT 'ä¸šåŠ¡ç±»åž‹',
    `bizId`      char(100)           NOT NULL COMMENT 'ä¸šåŠ¡ç›¸å…³æ ‡è¯†',
    `lockerIp`   char(20)            NOT NULL COMMENT 'é”å®šè€…IP',
    `lockerId`   bigint(20)          NOT NULL COMMENT 'dlocker id',
    `status`     int(4) unsigned     NOT NULL DEFAULT '0' COMMENT 'è®°å½•çŠ¶æ€ï¼Œ 0 æ­£å¸¸ï¼Œ 1 åˆ é™¤',
    `createTime` timestamp           NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT 'åˆ›å»ºæ—¶é—´',
    `updateTime` timestamp           NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT 'æ›´æ–°æ—¶é—´',
    `expireTime` timestamp           NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT 'è¿‡æœŸæ—¶é—´',
    `unlockTime` timestamp           NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT 'è§£é”æ—¶é—´',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `index_0` (`status`),
    KEY `index_1` (`lockerId`, `lockerIp`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 210048019
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='ä»»åŠ¡é”è¯¦æƒ…';--
-- Table structure for table `instanceCuCtrl`
--


CREATE TABLE if not exists `sqlserver`
(
    `Id`                   bigint(20) unsigned             NOT NULL AUTO_INCREMENT,
    `CreateTime`           timestamp                       NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime`           timestamp                       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `TicketTime`           timestamp                       NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `Status`               tinyint(4)                               DEFAULT NULL COMMENT 'sql-server的状态 1.停止 2. 开启中 3. 开启 4. 开启失败 5. 停止中 -2. 已删除',
    `Name`                 varchar(30) COLLATE utf8mb4_bin NOT NULL,
    `ClusterGroupSerialId` varchar(30) COLLATE utf8mb4_bin NOT NULL,
    PRIMARY KEY (`Id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin;--
-- Table structure for table `taskflow`
--

CREATE TABLE if not exists `taskflow`
(
    `id`          bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '流程ID',
    `processname` varchar(120)        NOT NULL DEFAULT '' COMMENT '流程code',
    `regionId`    bigint(20)                   DEFAULT NULL COMMENT '地域',
    `progress`    float(15, 3)                 DEFAULT '0.000' COMMENT '进度',
    `flowId`      varchar(64)         NOT NULL DEFAULT '' COMMENT '流程ID,由流程引擎创建',
    `docId`       varchar(64)         NOT NULL COMMENT '数据ID',
    `status`      int(4)              NOT NULL COMMENT '流程状态: 0:未发，1：流程已发起，2：流程结束,-1 流程删除 -2 流程挂起',
    `locked`      int(4)              NOT NULL COMMENT '流程锁定状态: 0：未锁定，1任务已锁定',
    `addtime`     timestamp           NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '流程创建时间',
    `starttime`   bigint(20)                   DEFAULT '0' COMMENT '流程执行开始时间戳',
    `endtime`     bigint(20)                   DEFAULT '0' COMMENT '流程执行结束时间戳',
    `flowparam`   mediumtext,
    `isGroup`     int(1)                       DEFAULT '0' COMMENT '是不是组合了流程，后付费接入，欠费需要隔离所有资源，只能返回一个flowid',
    PRIMARY KEY (`id`),
    KEY `idx_doc` (`docId`),
    KEY `flowId_index` (`flowId`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 29654
  DEFAULT CHARSET = utf8 COMMENT ='流程信息表';--
-- Table structure for table `taskflow_relation`
--

CREATE TABLE if not exists `taskflow_relation`
(
    `id`          bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `parentId`    bigint(20)          NOT NULL,
    `childId`     bigint(20)          NOT NULL,
    `isFinished`  int(1)                   DEFAULT '0',
    `createTime`  timestamp           NULL DEFAULT CURRENT_TIMESTAMP,
    `updateTime`  timestamp           NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `processname` varchar(120)        NOT NULL,
    `resourceId`  varchar(255)        NOT NULL COMMENT '冲正只恢复隔离的',
    `AppId`       bigint(20)          NOT NULL,
    `Region`      int(11)             NOT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='后付费接入，欠费需要隔离所有资源，只能返回一个flowid，要组合taskflow，所以需要一个关系表，只维护关系，实际走的还是taskflow';--
-- Table structure for table `taskflow_status`
--

CREATE TABLE if not exists `taskflow_status`
(
    `id`         bigint(20)  NOT NULL AUTO_INCREMENT,
    `docId`      varchar(32) NOT NULL,
    `flowId`     bigint(20)  NOT NULL,
    `status`     int(4)      NOT NULL,
    `processKey` varchar(64) NOT NULL,
    `taskCode`   varchar(32) NOT NULL,
    `starttime`  timestamp   NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '开始时间',
    `endtime`    timestamp   NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '结束时间',
    UNIQUE KEY `flowId` (`flowId`, `processKey`, `taskCode`),
    KEY `id` (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 13632
  DEFAULT CHARSET = utf8;--
-- Table structure for table `taskflowparams`
--

CREATE TABLE if not exists `taskflowparams`
(
    `id`         bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `flowid`     bigint(20) unsigned NOT NULL COMMENT '流程ID',
    `paramkey`   varchar(32) DEFAULT NULL COMMENT '参数名',
    `paramvalue` text COMMENT '参数值',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 288664
  DEFAULT CHARSET = utf8 COMMENT ='流程表';